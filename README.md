## 运行

```bash
    # 安装依赖
    npm install

    # 本地开发 开启服务
    npm run dev

    # 构建生成环境
    npm run build
    # 构建带cdn的生成环境
    npm run cdn
```

## 目录结构

```shell
├── vue.config.js              // vue构建相关
├── src                        // 项目源码
│   ├── service                // 向服务端的请求
│   ├── assets                 // 静态资源(图片，字体等)
│   ├── components             // 全局公用组件
│   ├── directive              // 全局指令
│   ├── filtres                // 全局filter
│   ├── router                 // 路由
│   ├── store                  // 全局store管理
│   ├── styles                 // 全局样式
│   ├── utils                  // 全局公用方法 路径配置等
│   ├── view                   // 页面目录
│   ├── pages                  // 入口页面
├── public                     // 不需要webpack处理的文件
└── package.json               // package.json
```

## 使用说明

### 环境说明

灰度和线上环境打包时会在 js、css 资源前添加 cdn 地址 当灰度接口正常使用时左上角 logo 移动上去会有相应 title 提示

### 面包屑机制、vue2.x 版本跳转 1.x 的兼容性处理

详细配置见 router 目录下的相关参数说明及 layout 目录下 sidebar 和 breadCrumb 组件中的相关设置

### ajax 的 loading 遮罩框状态

使用 vuex 的 loading 全局控制，不需要启动时只需要在 axios 的 config 配置中设置 loading 为 false(默认值为 true，推荐在点击按钮提交 ajax 时使用给予用户反馈，同时可以防止重复提交)

```javascript
//axios.get(url[, config])  axios.post(url[, data[, config]])
//下面示例为post
this.$service.post(url, postData, { loading: false }).then(response => {});
```

### 对话框，询问框

为统一体验，在页面中弹出带输入等操作的 iview Modal 对话框时候加上 :mask-closable="false"禁止点击遮罩关闭 带确定和取消操作的询问框统一使用 this.$Modal.confirm(config)

### 页面状态缓存 当跳转到其它页面后返回该页面会继续保存上一次的状态（如页码等）

将路由中的 meta 设置 keepAlive 属性，该页面就会缓存，此时若跳转到其它页面后需要在返回到该页面后更新数据 可在 activated 钩子中进行(原来在 created 钩子中进行的一些初始化也可放置在其中) 详见 vue 关于 keep-alive 的说明

解释：
vue2.1.0 keep-alive 新增的 include 和 exclude 属性允许组件有条件地缓存 但在内部使用 router-view 的时候有些 bug 故使用 meta 来判断

```javascript
//代码示例 路由
{
  path: 'class',
  alias: '/Web/ClassMark/pc_class_mark_list',
  name: '课程预约',
  component: resolve => require(['views/reservation/class'], resolve),
  meta: {keepAlive: true}
}
//页面
export default {
  name: 'class',
  activated() {
    //初始化和更新表格数据
    this.getClass();
  },
  ...
}
```

## z-index 规范
 - modal: 1000
 - sidebar: 999
 - headerTop: 998


## 全局组件 (src/components/global)

### FaIcon.vue

fontAwesome 图标 <https://fontawesome.com/v4.7.0/icons/>

```javascript
/**
 * name: 名称
 * size: 字号
 * color: 颜色
 */
<FaIcon name="angle-left" size="14" color="#666" />

// 以下是 iview 自带的 ionic icon 用法
<Icon type="arrow-left" size="14" color="#666" />
```

## src/components

### components/card/cardList.vue
会员卡列表
``` javascript
props: {
      value: {},
      // 只显示在售的卡
      onlySale: {
        type: Boolean,
        default: false
      },
      // 刷新卡列表
      refresh: {
        type: Boolean,
        default: false
      },
      // 将整个 card 返回
      returnCardInfo: {
        type: Boolean,
        default: false
      }
}
```

### components/form/Editor.vue

富文本编辑器

```javascript
/**
 * 用法
 */
<Editor v-model="form.description" />
```

### components/membership/salesSelect.vue

会籍/教练/私教/泳教 选择

```javascript
  props: {
    value: {
      type: [String, Number]
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    //如果 labelInValue 为 true, @on-change 里返回 { value: saleId, label: saleName }
    labelInValue: {
      type: Boolean,
      default: false
    },
    // 所有教练
    isCoach: {
      type: Boolean,
      default: false
    },
    // 私教教练
    isPtCoach: {
      type: Boolean,
      default: false
    },
    // 泳教教练
    isSwimCoach: {
      type: Boolean,
      default: false
    },
    isMembership: {
      type: Boolean,
      default: true
    }
  }
  // 用法
<SalesSelect v-model="saleId" />
```

components/pager.vue

```javascript
  props: {
    total: {
      type: [String, Number],
      required: true,
      default: 0
    },
    history: {
      type: Boolean,
      default: true
    },
    // 只有在有 tabs 多表格的页面需要
    name: {
      type: String
    },
    postData: {
      type: Object,
      default: () => {
        return {
          page_no: 1,
          page_size: 10
        };
      }
    }
  },
// 用法
<Pager :total="listCount" :history="false" @on-change="pageChange">

// Pager 会在 created 里触发一次 pageChange, 返回 postData = {page_no: 1, page_size: 10}
// 如果不需要保存历史记录, 传入 history = false 即可, 同时 created 不会触发 pageChange
// pager 通过 $route.name 来区分页面, 如果相同 $route.name 里的 tabs 里有 5 张表格, 则至少其中 4 张的 pager 要传入不重复的 name 值, 否则无法区分
<Pager name="goodsList" :total="total" :postData="postData" @on-change="pageChange" />

// on-change 返回页面存储的 postData, 需要手动还原
pageChange(postData) {
  const {s_date, e_date} = postData
  this.dateRange = [s_date, e_date]

  // this.postData = { search: '张三', page_no: 1, page_size: 10 }, postData = { page_no: 2, page_size: 10 }
  // 不能写成: this.postData = postData, 会丢失 search 参数
  this.postData = {...this.postData, ...postData}

  // important!!!: 如果 this.postData 里一开始有参数, 比如进入页面 search = 一年卡, 而 pageChange 里面返回的 postData.search = 次卡, 上面的写法 search 会被覆盖导致传参不生效, 正确写法为:
  this.postData = {...postData, ...{ search: this.postData.search } }

  // 嗯, 然后就可以请求列表了
  this.getList()
}
```

### components/Export.vue

表格导出组件

```javascript
<Export ref="export" />;

// columns: 同 iview
const columns = [
  {
    title: '用户名',
    key: 'username'
  },
  {
    title: '会员卡',
    key: 'card_name'
  }
  // ...
];
const data = [
  {
    username: '张三', // 自动计算 rowspan = card_name.length
    card_name: ['一年卡', '私教卡', '两年卡'] // 多行数据用数组
  }
];
const filename = '用户列表导出(2018-01-01~2018-02-01)';
this.$refs.export.export({columns, data, filename});
```

// 导出结构为

| 用户名 | 会员卡 |
| ------ | ------ |
|        | 一年卡 |
| 张三   | 私教卡 |
|        | 两年卡 |

---
### components/form/cropperPlus (https://github.com/Agontuk/vue-cropperjs)
基础用法
``` html
  <cropper v-model="img" />
```
自定义界面和上传函数, 参见小程序自定义页面 icon-picker.vue
``` html
   <cropper v-model="img" :beforeUpload="beforeUpload" :options="option" width="300px" height="200px">
         <div slot="result" slot-scope="{ onCropping }" v-if="!onCropping">
           <img v-if="!icons.includes(selected)" :src="selected">
         </div>
         <Button slot="picker" v-if="!slotProps.imgSelected" type="success" slot-scope="slotProps">
           <label style="cursor: pointer">
             上传图标
             <input type="file" style="display: none" @change="slotProps.onChange" />
           </label>
         </Button>
         <div slot="save" slot-scope="{ save, cancel }" style="padding: 15px 0; display: flex; justify-content:space-around; width: 100%">
           <Button type="success" @click="save">上传图标</Button> <!-- save 上传成功后返回一个 promise -->
           <Button type="ghost" @click="cancel">取消</Button>
         </div>
    </cropper>
```
``` javascript
   import cropper from 'components/form/cropperPlus'

   // 自定义上传函数, cropData: 裁剪后图片的 base64 数据
   beforeUpload(cropData) {
        const url = '/Admin/Public/upload_image';
        let postData = {
          image_data: cropData,
          _type: 'platform',
          userid: this.userId
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.status === 1) {
              resolve(res.data.path);
            } else {
              this.$Message.error(res.data.info);
              reject()
            }
          })
          .catch(err => {
            console.error(err);
          });
   }
```

## 关于 table.less 使用
``` javascript
<div class="table-wrap">
  <!--header 里的元素会自动垂直居中以及 margin-right: 15px, 两边会有 35px 边距, flex-wrap-->
  <header>Input, Select, Button...</header>
  <Table />
  <!--footer 样式同 header, 不同之处在于: Button type=success 会有自定义颜色, 最后的 Pager 会自动居右-->
  <footer>Button, Dropdown, Pager</footer>
  <!--头像放大等请参考会员签到-->
</div>
```

## 不在菜单中的页面
- #/paraset/pointClear 积分清零
- #/paraset/padComment 平板评论
