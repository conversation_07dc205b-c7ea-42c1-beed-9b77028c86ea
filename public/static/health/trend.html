<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>体测曲线走势</title>
  <style>
    /* root */
    html,
    body {
      margin: 0;
      padding: 0;
      font-size: 10px;
      background-color: #f5f5f5;
    }

    /* box */
    .box {
      height: 100%;
      width: 100%;
      background-color: #f5f5f5;
    }
    .box .box-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 3.7rem;
      width: 100%;
    }
    .box .box-title .title-date {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 1rem;
    }
    .box .box-title .title-trend {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 1rem;
    }
    .box .box-title .title-date img {
      height: 1.3rem;
      width: 1.3rem;
      margin-right: .9rem;
    }
    #dateOfTest {
      border: none;
      outline: none;
      appearance:none;
      -moz-appearance:none;
      -webkit-appearance:none;
      background-color: #f5f5f5;
      font-size: 1.3rem;
      color: #262626;
    }
    .box .box-title .title-trend p {
      font-size: 1.3rem;
      font-weight: bold;
      color: #0b78e3;
    }
    .box .box-title .title-trend img {
      height: 1rem;
      width: .55rem;
      margin-left: .9rem;
    }

    /* item */
    .item {
      background-color: white;
      height: 21.5rem;
      width: calc(100%-2rem);
      margin: .6rem 1rem;
    }

    .item-head {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      height: 3.4rem;
      margin: 0 1.5rem;
    }

    .item-head .title {
      font-size: 1.3rem;
      color: #353b63;
      margin-top: 1.8rem;
    }

    .item-body {
      height: 14.7rem;
      width: 100%;
    }

    .item-body canvas {
      height: 14.7rem;
      width: 100%;
    }

    .item-foot {
      height: 3.4rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      border-top: .1rem solid #f3f6fa;
      font-size: 1.2rem;
      color: #353b63;
      margin: 0 1.5rem;
    }
  </style>
</head>

<body>

  <div class="box">
    <div class="box-title">
      <div class="title-date">
        <img src="../img/date.png" alt="date">
        <p id="dateOfTest">2012.12.12 10:00</p>
      </div>
      <div class="title-trend">
        <p>查看单次体测明细</p>
        <img src="../img/next.png" alt="next">
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">体重</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyWeight" canvas-id="checkBodyWeight"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">体脂</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyFat" canvas-id="checkBodyFat"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">BMI</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyBMI" canvas-id="checkBodyBMI"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">胸围</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyChest" canvas-id="checkBodyChest"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">腰围</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyAbs" canvas-id="checkBodyAbs"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

    <div class="item">
      <div class="item-head">
        <text class="title">臀围</text>
      </div>
      <div class="item-body">
        <canvas id="checkBodyRump" canvas-id="checkBodyRump"></canvas>
      </div>
      <div class="item-foot">
        <!-- <div wx:if="{{weightTrendFlag=='+'}}">比上次 ý {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='='}}">和上次持平 ± {{weightTrend}}kg
        </div>
        <div wx:if="{{weightTrendFlag=='-'}}">比上次 ñ {{weightTrend}}kg
        </div> -->
      </div>
    </div>

  </div>

  <script src="../body_three_d_js/wxcharts.js"></script>
  <script src="../body_three_d_js/jquery.min.js"></script>
  <script>
    $(document).ready(function(){
      $.extend({
        getBaseUrl() {
          let baseUrl,
            host = window.location.host,
            subDomain = host.split('.')[0];
          if (subDomain === 'vip-sim') {
            baseUrl = 'https://sim.rocketbird.cn';
          } else if (subDomain === 'vip') {
            baseUrl = 'https://wx.rocketbird.cn';
          } else {
            baseUrl = 'https://beta.rocketbird.cn';
          }
          return baseUrl;
        },
        drawTrendCanvas: function (canvasId, xField, yField, maxKG, minKG, yFormat, seriesName = '体重') {
          return new Charts({
            canvasId: canvasId,
            type: 'line',
            categories: xField,
            animation: true,
            legend: true,
            series: [{
              name: seriesName,
              color: '#4ad0c5',
              data: yField,
              format: function (val) {
                return val.toFixed(1);
              }
            }],
            xAxis: {
              disableGrid: true,
              gridColor: 'white',
              fontColor: '#b0bbdc'
            },
            yAxis: {
              disableGrid: true,
              gridColor: 'white',
              fontColor: '#b0bbdc',
              min: minKG,
              max: maxKG,
              format: yFormat
            },
            width: 250,
            height: 140,
            background: 'white',
            dataLabel: true,
            dataPointShape: true,
            enableScroll: true,
            extra: {
              lineStyle: 'curve'
            }
          });
        }
      });

      $('.title-trend:first').click(function(){
        window.history.back();
      });

      const query = decodeURIComponent(window.location.search);
      if (query.length !== 0) {
        const val = query.substr(5);
        $.post(`${$.getBaseUrl()}/Coach/InBodyData/getStaminaTendency`, {
          uid: val
        }, res=>{
          if (res.errorcode == 0) {
            const resData = res.data;
            // const resData = { "weight": [{ "ideal_value": "13.3", "value": "45.0", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "ideal_value": "63.0", "value": "64.0", "date": "10.31", "compare_to_last": 19, "trend": "+" }, { "ideal_value": "63.0", "value": "65.0", "date": "11.06", "compare_to_last": 1, "trend": "+" }], "body_fat": [{ "ideal_value": "14%~20%", "value": "41", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "ideal_value": "14%~20%", "value": "17", "date": "10.31", "compare_to_last": 24, "trend": "-" }, { "ideal_value": "14%~20%", "value": "18", "date": "11.06", "compare_to_last": 1, "trend": "+" }], "BMI": [{ "ideal_value": "18.5~23.9", "value": "27.9", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "ideal_value": "18.5~23.9", "value": "22.1", "date": "10.31", "compare_to_last": 23.8, "trend": "-" }, { "ideal_value": "18.5~23.9", "value": "22.5", "date": "11.06", "compare_to_last": 0.4, "trend": "+" }], "bust": [{ "value": "75.0", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "value": "95.0", "date": "10.31", "compare_to_last": 20, "trend": "+" }, { "value": "95.0", "date": "11.06", "compare_to_last": 0, "trend": "=" }], "waist": [{ "value": "66.0", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "value": "79.0", "date": "10.31", "compare_to_last": 13, "trend": "+" }, { "value": "79.0", "date": "11.06", "compare_to_last": 0, "trend": "=" }], "hipline": [{ "value": "77.0", "date": "10.30", "compare_to_last": 0, "trend": "=" }, { "value": "95.0", "date": "10.31", "compare_to_last": 18, "trend": "+" }, { "value": "96.0", "date": "11.06", "compare_to_last": 1, "trend": "+" }], "weight_ideal_value": "63.0kg", "fat_ideal_value": "14%~20%", "bmi_ideal_value": "18.5~23.9" };
            let weightChart = null,
                fatChart = null,
                bmiChart = null,
                chestChart = null,
                absChart = null,
                rumpChart = null;

            // 体重
            let xField = [];
            let yField = [];
            let maxKilogram = 0;
            let minKilogram = 200;
            if (Array.isArray(resData.weight)) {
              if (resData.weight.length === 0) {
                maxKilogram = 105;
                minKilogram = 37;
              }
              resData.weight.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
                if (maxKilogram < val) {
                  maxKilogram = val;
                }
                if (minKilogram > val) {
                  minKilogram = val;
                }
              });
            }
            maxKilogram += 5;
            minKilogram -= 5;
            weightChart = $.drawTrendCanvas('checkBodyWeight', xField, yField, maxKilogram, minKilogram, val => (val + 'kg'));

            // 体脂
            xField = [];
            yField = [];
            if (Array.isArray(resData.body_fat)) {
              resData.body_fat.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
              });
            }
            fatChart = $.drawTrendCanvas('checkBodyFat', xField, yField, 100, 0, val => (val + '%'), '体脂');

            // BMI
            xField = [];
            yField = [];
            if (Array.isArray(resData.BMI)) {
              resData.BMI.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
              });
            }
            bmiChart = $.drawTrendCanvas('checkBodyBMI', xField, yField, 50, 0, val => (val), 'BMI');

            // 胸围
            xField = [];
            yField = [];
            if (Array.isArray(resData.bust)) {
              resData.bust.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
              });
            }
            chestChart = $.drawTrendCanvas('checkBodyChest', xField, yField, 150, 50, val => (val + 'cm'), '胸围');

            // 腰围
            xField = [];
            yField = [];
            if (Array.isArray(resData.waist)) {
              resData.waist.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
              });
            }
            absChart = $.drawTrendCanvas('checkBodyAbs', xField, yField, 150, 50, val => (val + 'cm'), '腰围');

            // 臀围
            xField = [];
            yField = [];
            if (Array.isArray(resData.hipline)) {
              resData.hipline.forEach((item) => {
                const val = parseFloat(item.value);
                xField.push(item.test_time);
                yField.push(val);
              });
            }
            rumpChart = $.drawTrendCanvas('checkBodyRump', xField, yField, 150, 50, val => (val + 'cm'), '臀围');

            $('#checkBodyWeight').on('touchstart', function(e) {
              weightChart.scrollStart(e);
            }).on('touchmove', function(e){
              weightChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              weightChart.scrollEnd(e);
              weightChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.weight.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        weightIdeal: item.ideal_value,
                        weightTrend: item.compare_to_last,
                        weightTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });

            $('#checkBodyFat').on('touchstart', function(e) {
              fatChart.scrollStart(e);
            }).on('touchmove', function(e){
              fatChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              fatChart.scrollEnd(e);
              fatChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.fat.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        fatIdeal: item.ideal_value,
                        fatTrend: item.compare_to_last,
                        fatTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });

            $('#checkBodyBMI').on('touchstart', function(e) {
              fatChart.scrollStart(e);
            }).on('touchmove', function(e){
              fatChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              bmiChart.scrollEnd(e);
              bmiChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.bmi.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        bmiIdeal: item.ideal_value,
                        bmiTrend: item.compare_to_last,
                        bmiTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });

            $('#checkBodyChest').on('touchstart', function(e) {
              fatChart.scrollStart(e);
            }).on('touchmove', function(e){
              fatChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              chestChart.scrollEnd(e);
              chestChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.chest.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        chestTrend: item.compare_to_last,
                        chestTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });

            $('#touchEndHandlerAbs').on('touchstart', function(e) {
              fatChart.scrollStart(e);
            }).on('touchmove', function(e){
              fatChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              absChart.scrollEnd(e);
              absChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.abs.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        absTrend: item.compare_to_last,
                        absTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });

            $('#touchEndHandlerAbs').on('touchstart', function(e) {
              fatChart.scrollStart(e);
            }).on('touchmove', function(e){
              fatChart.scroll(e);
            }).on('touchend', function(e){
              const self = this;
              rumpChart.scrollEnd(e);
              rumpChart.showToolTip(e, {
                format: function (item, category) {
                  self.data.rump.forEach((item) => {
                    if (item.test_time == category) {
                      self.setData({
                        rumpTrend: item.compare_to_last,
                        rumpTrendFlag: item.trend
                      });
                    }
                  });
                  return category + ' ' + item.name + ':' + item.data
                }
              });
            });
          } else {
            console.log(res.errorcode);
          }
        });
      }
    });
  </script>

</body>

</html>
