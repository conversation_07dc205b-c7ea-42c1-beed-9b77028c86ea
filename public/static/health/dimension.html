<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>体测情况</title>
  <style>
    #btn {
      width: 8rem;
      margin-left: 1rem;
    }

    #visWrapper {
      width: 100%;
      height: calc(100vh - 37px);
      overflow: hidden;
    }
    /* root */
    html,
    body {
      margin: 0;
      padding: 0;
      font-size: 10px;
      background-color: #f5f5f5;
      overflow: hidden;
    }
    /* head */
    .head-tabs {
      background-color: #10111b;
      font-size: 1.4rem;
      color: #a1a4b0;
      height: 3.5rem;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }
    .head-tabs .tab {
      height: 3.5rem;
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .head-tabs .line {
      height: 2.4rem;
      width: .1rem;
      border-left: .1rem solid #272a3e;
    }
    .head-tabs .selected {
      border-bottom: .2rem solid #e22c87;
    }
    /* area */
    .area {
      height: 100%;
      width: 100%;
      background-color: #f5f5f5;
    }
    .area .area-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 3.7rem;
      width: 100%;
    }
    .area .area-title .title-date {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 1rem;
    }
    .area .area-title .title-trend {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 1rem;
    }
    .area .area-title .title-date img {
      height: 1.3rem;
      width: 1.3rem;
      margin-right: .9rem;
    }
    #dateOfTest {
      border: none;
      outline: none;
      appearance: none;
      -moz-appearance: none;
      -webkit-appearance: none;
      background-color: #f5f5f5;
      font-size: 1.3rem;
      color: #262626;
    }
    #dateOfTest:focus option:checked {
      border: none;
      outline: none;
    }
    .area .area-title .title-trend p {
      font-size: 1.3rem;
      font-weight: bold;
      color: #0b78e3;
    }
    .area .area-title .title-trend img {
      height: 1rem;
      width: .55rem;
      margin-left: .9rem;
    }
    /* card of area */
    .area-card {
      background-color: white;
      border-radius: .2rem;
      transition: all .2s ease-in-out;
      margin: 0 1rem 1rem 1rem;
      box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
      border-color: #eee;
    }
    .area-card .card-title {
      font-size: 1.2rem;
      color: #686b75;
      height: 3.1rem;
      display: flex;
      align-items: center;
      margin-left: 1rem;
    }
    .area-card .card-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }
    /* item of card box */
    .card-item {
      height: 8.5rem;
      width: 42%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-top: .1rem solid #f5f5f5;
      margin: 0 1rem;
    }
    .card-item .item-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .card-item .item-right {
      display: flex;
      align-items: center;
    }
    .card-item .item-right img {
      height: 2.8rem;
      width: 2.4rem;
    }
    .card-item .item-value {
      font-size: 2.6rem;
      font-weight: bold;
      color: #282c41;
    }
    .card-item .item-name {
      font-size: 1.2rem;
      color: #686572;
    }
    /* li of card box */
    .card-li {
      height: 7.7rem;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-top: .1rem solid #f5f5f5;
      margin: 0 1rem;
    }
    .card-li .li-left {
      display: flex;
      flex-direction: column;
      margin-left: 1.5rem;
    }
    .card-li .li-left .li-name {
      font-size: 1.3rem;
      font-weight: bold;
      color: #282c41;
      margin-left: .4rem;
    }
    .card-li .li-left .li-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 1.2rem;
      color: #a2a4b0;
      margin-left: .4rem;
      margin-top: .8rem;
    }
    .card-li .li-left .li-value label {
      margin: 0 .3rem;
    }
    .card-li .li-left .li-value p {
      color: #e60012;
    }
    .card-li .li-right {
      font-size: 1.2rem;
      color: #a2a4b0;
      margin-right: 2.1rem;
    }
  </style>
</head>

<body>

  <!-- <div class="head-tabs">
    <div class="tab selected">围度信息</div>
    <div class="line"></div>
    <div class="tab">身体成分</div>
  </div> -->

  <div class="area">
    <div class="area-title">
      <div class="title-date">
        <img src="../img/date.png" alt="date">
        <select id="dateOfTest"></select>
      </div>
      <div class="title-trend">
        <p>查看体测曲线走势</p>
        <img src="../img/next.png" alt="next">
      </div>
    </div>
    
  </div>

  <!-- <button id="btn">3D 模型图</button> -->

  <script src="../body_three_d_js/jquery.min.js"></script>
  <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.0.js"></script>
  <script type="text/javascript">
    $(document).ready(function () {
      let USER_ID = '', userId = '', busId = '';

      $.extend({
        getBaseUrl() {
          let baseUrl,
            host = window.location.host,
            subDomain = host.split('.')[0];
          if (subDomain === 'vip-sim') {
            baseUrl = 'https://sim.rocketbird.cn';
          } else if (subDomain === 'vip') {
            baseUrl = 'https://wx.rocketbird.cn';
          } else {
            baseUrl = 'https://beta.rocketbird.cn';
          }
          return baseUrl;
        },
        initData() {
          $('#leftArm').text(0);
          $('#rightArm').text(0);
          $('#chest').text(0);
          $('#abs').text(0);
          $('#rump').text(0);
          $('#leftLeg').text(0);
          $('#rightLeg').text(0);
          $('#leftLowerLeg').text(0);
          $('#rightLowerLeg').text(0);

          $('#weight').text(0);
          $('#bodyFat').text(0);
          $('#water').text(0);
        },
        getBodyInfo(val) {
          $.initData();
          const vArr = val.split('_');
          if (Array.isArray(vArr) && vArr.length === 3) {
            const id = vArr[0],
                  type = vArr[1];
            if (type === 'vis' || type === 'zk') {
              $.post(`${$.getBaseUrl()}/Coach/InBodyData/getStaminaInfo`, {
                id: id,
                device: type
              }, res => {
                if (res.errorcode == 0) {
                  $('.area').append(`<iframe id="visWrapper" src="${res.data.url}" frameborder="0"></iframe>`);
                  // TODO ios下点击小程序返回按钮会导致iframe页面重新加载 由于体测仪的展示页面与父页面域名不一致 不好判断页面是否重新加载了以及内部具体的url地址 外部iframeEle.src会一直不变
                  // let hasload = false;
                  // const iframeEle = document.getElementById("visWrapper");
                  // iframeEle.addEventListener("load", function(e) {
                  //   console.log('iframeEle.src', iframeEle.src);
                  //   console.log('iframeEle.contentWindow', iframeEle.contentWindow); // 报错 Blocked a frame with origin
                  //   if(!hasload) {
                  //     hasload = true;
                  //   } else {
                  //   }
                  // });
                  
                } else {
                  console.log(res.errormsg);
                }
              });
            } else if (type === 'inbody' || type === 'youjian' || type === 'ivep' || type === 'lw' || type === 'ts') {
              const param = `body=${val}&busId=${busId}&userId=${userId}`;
              window.location.replace(`./inbody.html?${encodeURIComponent(param)}`);
            }
          }
        }
      });
     
      $('.tab').click(function () {
        if(!$(this).hasClass('selected')) {
          $('.tab').removeClass('selected');
          $(this).addClass('selected');
          $('.area-card').slideToggle();
        }
      });
      $('.title-trend:first').click(function () {
        wx.miniProgram.navigateTo({url: '/pages/physicalExamination/physicalExamination?uid=' + USER_ID});
      });
      // $('#btn').click(function(){
      //   const val = $('#dateOfTest').val();
      //   window.location.href = `../bodyThreeD.html?body=${USER_ID}&busId=${busId}&userId=${USER_ID}&sence=${val.split('_')[0]}`;
      // });

      const query = decodeURIComponent(window.location.search);
      if (query.length !== 0) {
        const paramArr = query.split('&');
        if (paramArr.length === 3) {
          busId = paramArr[1].split('=')[1];
          userId = paramArr[2].split('=')[1];
        }
        const vArr = paramArr[0].substr(6).split('_');
				if (Array.isArray(vArr) && vArr.length === 3) {
          USER_ID = vArr[2];
        }
      }
     
      $.post(`${$.getBaseUrl()}/Coach/InBodyData/getDropDownList`, {
        uid: USER_ID
      }, res => {
        if (res.errorcode == 0) {
          $('#dateOfTest').empty();
          res.data.forEach(item => {
            $('#dateOfTest').append(`<option value="${item.id}_${item.device}_${USER_ID}">${item.test_time}</option>`);
          });

          const query = decodeURIComponent(window.location.search);
          if (query.length === 0) {
            $.getBodyInfo($('#dateOfTest').val());
          } else {
            const paramArr = query.split('&');
            const val = paramArr[0].substr(6);
            const vArr = val.split('_');
            if (vArr[0] == -1) {
              $.getBodyInfo($('#dateOfTest').val());
            } else {
              $('#dateOfTest').val(val);
              $.getBodyInfo(val);
            }
          }

          $('#dateOfTest').change(function () {
            $.getBodyInfo($(this).val());
          });
        } else {
          console.log(res.errormsg);
        }
      });
      window.addEventListener("popstate", function(e) {
        wx.miniProgram.navigateBack({
            delta: 1
        });
      }, false);

    });
  </script>

</body>

</html>
