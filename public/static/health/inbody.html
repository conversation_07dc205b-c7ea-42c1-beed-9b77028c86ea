<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>体测情况</title>
  <style>
    /* root */
    html,
    body {
      margin: 0;
      padding: 0;
      font-size: 10px;
      background-color: #f5f5f5;
    }

    /* head */
    .head-tabs {
      background-color: #10111b;
      font-size: 1.4rem;
      color: #a1a4b0;
      height: 3.5rem;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    .head-tabs .tab {
      height: 3.5rem;
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .head-tabs .line {
      height: 2.4rem;
      width: .1rem;
      border-left: .1rem solid #272a3e;
    }

    .head-tabs .selected {
      border-bottom: .2rem solid #e22c87;
    }

    /* area */
    .area {
      height: 100%;
      width: 100%;
      background-color: #f5f5f5;
    }

    .area .area-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 3.7rem;
      width: 100%;
    }

    .area .area-title .title-date {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 1rem;
    }

    .area .area-title .title-trend {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 1rem;
    }

    .area .area-title .title-date img {
      height: 1.3rem;
      width: 1.3rem;
      margin-right: .9rem;
    }

    #dateOfTest {
      border: none;
      outline: none;
      appearance: none;
      -moz-appearance: none;
      -webkit-appearance: none;
      background-color: #f5f5f5;
      font-size: 1.3rem;
      color: #262626;
    }

    .area .area-title .title-trend p {
      font-size: 1.3rem;
      font-weight: bold;
      color: #0b78e3;
    }

    .area .area-title .title-trend img {
      height: 1rem;
      width: .55rem;
      margin-left: .9rem;
    }

    /* card of area */
    .area-card {
      background-color: white;
      border-radius: .2rem;
      transition: all .2s ease-in-out;
      margin: 0 1rem 1rem 1rem;
      box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
      border-color: #eee;
    }

    .area-card .card-title {
      font-size: 1.2rem;
      color: #686b75;
      height: 3.1rem;
      display: flex;
      align-items: center;
      margin-left: 1rem;
    }

    .area-card .card-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
    }

    /* li of card box */
    .card-li {
      height: 7.7rem;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-top: .1rem solid #f5f5f5;
      margin: 0 1rem;
    }

    .card-li .li-left {
      display: flex;
      flex-direction: column;
      margin-left: 1.5rem;
    }

    .card-li .li-left .li-name {
      font-size: 1.3rem;
      font-weight: bold;
      color: #282c41;
      margin-left: .4rem;
    }

    .card-li .li-left .li-value {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 1.2rem;
      color: #a2a4b0;
      margin-left: .4rem;
      /* margin-top: .8rem; */
    }

    .card-li .li-left .li-value label {
      margin: 0 .3rem;
    }

    .card-li .li-right {
      font-size: 1.2rem;
      color: #a2a4b0;
      margin-right: 2.1rem;
    }

    /* item of card */
    .card-item {
      width: 42%;
      display: flex;
      /* flex-direction: row; */
      /* justify-content: space-between; */
      border-top: .1rem solid #f5f5f5;
      margin: 0 1rem;
    }

    .heng {
      height: 8.5rem;
      flex-direction: row;
    }

    .heng .item-left {
      margin-left: 1.2rem;
    }

    .shu {
      height: 10rem;
      flex-direction: column;
      justify-content: space-around;
    }

    .card-item .item-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .card-item .item-right {
      display: flex;
      align-items: center;
    }

    .card-item .item-right img {
      height: 2.8rem;
      width: 2.4rem;
    }

    .card-item .item-value {
      font-size: 1.3rem;
      font-weight: bold;
      color: #282c41;
    }

    .card-item .item-name {
      font-size: 1.2rem;
      color: #686572;
      margin-top: .8rem;
    }

    .body-pic {
      zoom: 0.32;
      width: 1042px;
      height: 865px;
      border: 1px solid rgba(229, 229, 229, 1);
      border-radius: 20px;
      background: url('../img/man-pic.png') no-repeat center center rgba(242, 247, 252, 1);
      background-size: 795px 729px;
      position: relative;
      margin: 0 auto 15px;
    }

    .body-pic div {
      position: absolute;
      width: 170px;
      height: 50px;
      text-align: center;
      font-size: 40px;
      font-weight: bold;
      color: rgba(27, 27, 27, 1);
      line-height: 50px;
    }

    .pic-women {
      background: url('../img/women-pic.png') no-repeat center center rgba(242, 247, 252, 1);
      background-size: 795px 729px;
    }

    .pic-part1 {
      left: 141px;
      top: 128px;
    }

    .pic-part2 {
      left: 141px;
      top: 277px;
    }

    .pic-part3 {
      left: 141px;
      top: 422px;
    }

    .pic-part4 {
      left: 141px;
      top: 568px;
    }

    .pic-part5 {
      left: 730px;
      top: 128px;
    }

    .pic-part6 {
      left: 730px;
      top: 344px;
    }

    .pic-part7 {
      left: 730px;
      top: 568px;
    }
  </style>
</head>

<body>

  <div class="head-tabs">
    <div class="tab selected">身体成分</div>
    <div class="line"></div>
    <div class="tab">肥胖分析</div>
  </div>

  <div class="area">
    <div class="area-title">
      <div class="title-date">
        <img src="../img/date.png" alt="date">
        <select id="dateOfTest"></select>
      </div>
      <div class="title-trend">
        <p>查看体测曲线走势</p>
        <img src="../img/next.png" alt="next">
      </div>
    </div>
    <div class="area-card">
      <div class="card-title">身体成分(kg)</div>
      <div class="card-box">
        <div class="card-li">
          <div class="li-left">
            <div class="li-name">体重</div>
            <div class="li-value">当前值 <label id="weight">0</label>
              <p id="weight-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="weight-down">0</label>kg-<label id="weight-up">0</label>kg</div>
        </div>
        <div class="card-li" id="heightTag">
          <div class="li-left">
            <div class="li-name">身高</div>
            <div class="li-value">当前值 <label id="height">0</label>
              <p id="height-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="height-down">0</label>cm-<label id="height-up">0</label>cm</div>
        </div>
        <div class="card-li" id="skeletonTag">
          <div class="li-left">
            <div class="li-name">骨骼肌</div>
            <div class="li-value">当前值 <label id="bone">0</label>
              <p id="bone-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="bone-down">0</label>kg-<label id="bone-up">0</label>kg</div>
        </div>
        <div class="card-li">
          <div class="li-left">
            <div class="li-name">体脂肪</div>
            <div class="li-value">当前值 <label id="bodyFat">0</label>
              <p id="bodyFat-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="bodyFat-down">0</label>kg-<label id="bodyFat-up">0</label>kg</div>
        </div>
        <div class="card-li" id="noFatTag">
          <div class="li-left">
            <div class="li-name">去脂体重</div>
            <div class="li-value">当前值 <label id="leanWeight">0</label>
              <p id="leanWeight-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="leanWeight-down">0</label>kg-<label id="leanWeight-up">0</label>kg</div>
        </div>
        <div class="card-li">
          <div class="li-left">
            <div class="li-name">身体水分含量</div>
            <div class="li-value">当前值 <label id="water">0</label>
              <p id="water-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="water-down">0</label>kg-<label id="water-up">0</label>kg</div>
        </div>
        <div class="card-li" id="proteinTag">
          <div class="li-left">
            <div class="li-name">蛋白质</div>
            <div class="li-value">当前值 <label id="protein">0</label>
              <p id="protein-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="protein-down">0</label>kg-<label id="protein-up">0</label>kg</div>
        </div>
        <div class="card-li" id="saltTag">
          <div class="li-left">
            <div class="li-name">无机盐</div>
            <div class="li-value">当前值 <label id="mineral_salt">0</label>
              <p id="mineral_salt-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="mineral_salt-down">0</label>kg-<label id="mineral_salt-up">0</label>kg
          </div>
        </div>
        <div class="card-li" id="heartTag">
          <div class="li-left">
            <div class="li-name">安静心率</div>
            <div class="li-value">当前值 <label id="peace_heart">0</label>
              <p id="peace_heart-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="peace_heart-down">0</label>bmp-<label id="peace_heart-up">0</label>bmp
          </div>
        </div>
        <div class="card-li" id="inPressureTag">
          <div class="li-left">
            <div class="li-name">收缩压</div>
            <div class="li-value">当前值 <label id="systolic">0</label>
              <p id="systolic-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="systolic-down">0</label>mhg-<label id="systolic-up">0</label>mhg</div>
        </div>
        <div class="card-li" id="outPressureTag">
          <div class="li-left">
            <div class="li-name">舒张压</div>
            <div class="li-value">当前值 <label id="diastolic">0</label>
              <p id="diastolic-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="diastolic-down">0</label>mhg-<label id="diastolic-up">0</label>mhg</div>
        </div>
        <div class="card-li" id="reflectTag">
          <div class="li-left">
            <div class="li-name">反应时</div>
            <div class="li-value">当前值 <label id="reaction_time">0</label>
              <p id="reaction_time-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="reaction_time-down">0</label>-<label id="reaction_time-up">0</label>
          </div>
        </div>
        <div class="card-li" id="gripTag">
          <div class="li-left">
            <div class="li-name">握力(kg)</div>
            <div class="li-value">当前值 <label id="grip_power">0</label>
              <p id="grip_power-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="grip_power-down">0</label>kg-<label id="grip_power-up">0</label>kg</div>
        </div>
        <div class="card-li" id="strengthTag">
          <div class="li-left">
            <div class="li-name">柔韧性(s)</div>
            <div class="li-value">当前值 <label id="sit_and_reach">0</label>
              <p id="sit_and_reach-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="sit_and_reach-down">0</label>s-<label id="sit_and_reach-up">0</label>s
          </div>
        </div>
        <div class="card-li" id="oneFootTag">
          <div class="li-left">
            <div class="li-name">闭眼单脚站立(s)</div>
            <div class="li-value">当前值 <label id="single_stand">0</label>
              <p id="single_stand-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="single_stand-down">0</label>s-<label id="single_stand-up">0</label>s
          </div>
        </div>
        <div class="card-li" id="pushUpTag">
          <div class="li-left">
            <div class="li-name">俯卧撑/仰卧起坐(个)</div>
            <div class="li-value">当前值 <label id="up_and_down">0</label>
              <p id="up_and_down-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="up_and_down-down">0</label>个-<label id="up_and_down-up">0</label>个
          </div>
        </div>
        <div class="card-li" id="breathTag">
          <div class="li-left">
            <div class="li-name">肺活量(ml)</div>
            <div class="li-value">当前值 <label id="vital_capacity">0</label>
              <p id="vital_capacity-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="vital_capacity-down">0</label>ml-<label
              id="vital_capacity-up">0</label>ml</div>
        </div>
        <div class="card-li" id="stairTag">
          <div class="li-left">
            <div class="li-name">台阶实验(s)</div>
            <div class="li-value">当前值 <label id="step">0</label>
              <p id="step-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="step-down">0</label>s-<label id="step-up">0</label>s</div>
        </div>
      </div>
    </div>
    <div class="area-card" style="display: none;">
      <div class="card-title">肥胖分析(kg)</div>
      <div class="card-box">
        <div class="card-li">
          <div class="li-left">
            <div class="li-name">身体质量指数</div>
            <div class="li-value">当前值 <label id="energy">0</label>
              <p id="energy-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="energy-down">0</label>kg/㎡-<label id="energy-up">0</label>kg/㎡</div>
        </div>
        <div class="card-li" id="basicConsumeTag">
          <div class="li-left">
            <div class="li-name">基础代谢</div>
            <div class="li-value">当前值 <label id="matebolism">0</label>
              <p id="matebolism-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="matebolism-down">0</label>kcal-<label id="matebolism-up">0</label>kcal
          </div>
        </div>
        <div class="card-li">
          <div class="li-left">
            <div class="li-name">体脂率</div>
            <div class="li-value">当前值 <label id="bodyFat2">0</label>
              <p id="bodyFat2-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="bodyFat2-down">0</label>%-<label id="bodyFat2-up">0</label>%</div>
        </div>
        <div class="card-li inbody-type" id="coreSizeTag">
          <div class="li-left">
            <div class="li-name">腰臀比</div>
            <div class="li-value">当前值 <label id="coreSize"></label>
              <p id="coreSize-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="coreSize-down"></label>-<label id="coreSize-up"></label></div>
        </div>
        <div class="card-li" id="bodyFatTag">
          <div class="li-left">
            <div class="li-name">体脂肪</div>
            <div class="li-value">当前值 <label id="body_fat">0</label>
              <p id="body_fat-state">(正常)</p>
            </div>
          </div>
          <div class="li-right">理想值 <label id="body_fat-down">0</label>kg-<label id="body_fat-up">0</label>kg</div>
        </div>
      </div>
    </div>
    <div class="area-card" id="ivepDataWrap" style="display: none;margin-top: .5rem;">
      <div class="card-title">身体维度</div>
      <div class="card-box" style="margin-top: .5rem;">
        <div class="body-pic" id="ivepData">
          <div class="pic-part1">0</div>
          <div class="pic-part2">0</div>
          <div class="pic-part3">0</div>
          <div class="pic-part4">0</div>
          <div class="pic-part5">0</div>
          <div class="pic-part6">0</div>
          <div class="pic-part7">0</div>
        </div>
      </div>
    </div>
  </div>
  <div class="area-card" id="jieduan" style="display: none;margin-top: .5rem;">
    <div class="card-title">节段肌肉</div>
    <div class="card-box" style="margin-top: .5rem;">
      <div class="card-item heng">
        <div class="item-right">
          <img src="../img/1.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">左上臂围</div>
          <div class="item-name" id="leftArmMuscle">0kg</div>
        </div>
      </div>
      <div class="card-item heng">
        <div class="item-right">
          <img src="../img/2.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">右上臂围</div>
          <div class="item-name" id="rightArmMuscle">0kg</div>
        </div>
      </div>
      <div class="card-item heng">
        <div class="item-right">
          <img src="../img/5.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">左大腿围</div>
          <div class="item-name" id="leftLegMuscle">0kg</div>
        </div>
      </div>
      <div class="card-item heng">
        <div class="item-right">
          <img src="../img/6.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">右大腿围</div>
          <div class="item-name" id="rightLegMuscle">0kg</div>
        </div>
      </div>
      <div class="card-item heng">
        <div class="item-right">
          <img src="../img/4.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">躯干</div>
          <div class="item-name" id="absMuscle">0kg</div>
        </div>
      </div>
    </div>
  </div>
  <div class="area-card" id="jieduanzhi" style="display: none;margin-top: .5rem;">
    <div class="card-title">节段脂肪</div>
    <div class="card-box" style="margin-top: .5rem;">
      <div class="card-item shu">
        <div class="item-right">
          <img src="../img/1.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">左上臂围</div>
          <div class="item-name" id="leftArmFat">0kg/0%</div>
        </div>
      </div>
      <div class="card-item shu">
        <div class="item-right">
          <img src="../img/2.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">右上臂围</div>
          <div class="item-name" id="rightArmFat">0kg/0%</div>
        </div>
      </div>
      <div class="card-item shu">
        <div class="item-right">
          <img src="../img/5.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">左大腿围</div>
          <div class="item-name" id="leftLegFat">0kg/0%</div>
        </div>
      </div>
      <div class="card-item shu">
        <div class="item-right">
          <img src="../img/6.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">右大腿围</div>
          <div class="item-name" id="rightLegFat">0kg/0%</div>
        </div>
      </div>
      <div class="card-item shu">
        <div class="item-right">
          <img src="../img/4.png" alt="human">
        </div>
        <div class="item-left">
          <div class="item-value">躯干</div>
          <div class="item-name" id="absFat">0kg/0%</div>
        </div>
      </div>
    </div>
  </div>
  </div>

  <script src="../body_three_d_js/jquery.min.js"></script>
  <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.0.js"></script>
  <script type="text/javascript">
    $(document).ready(function () {
      let USER_ID = '', busId = '', userId = '';

      $.extend({
        getBaseUrl() {
          let baseUrl,
            host = window.location.host,
            subDomain = host.split('.')[0];
          if (subDomain === 'vip-sim') {
            baseUrl = 'https://sim.rocketbird.cn';
          } else if (subDomain === 'vip') {
            baseUrl = 'https://wx.rocketbird.cn';
          } else {
            baseUrl = 'https://beta.rocketbird.cn';
          }
          return baseUrl;
        },
        initData() {
          $('#weight').add('#weight-down').add('#weight-up')
            .add('#bone').add('#bone-down').add('#bone-up')
            .add('#bodyFat').add('#bodyFat-down').add('#bodyFat-up')
            .add('#leanWeight').add('#leanWeight-down').add('#leanWeight-up')
            .add('#water').add('#water-down').add('#water-up')
            .add('#energy').add('#energy-down').add('#energy-up')
            .add('#matebolism').add('#matebolism-down').add('#matebolism-up')
            .add('#bodyFat2').add('#bodyFat2-down').add('#bodyFat2-up')
            .add('#coreSize').add('#coreSize-down').add('#coreSize-up').text(0);

          $('#weight-state').add('#bone-state').add('#bodyFat-state').add('#leanWeight-state').add('#water-state')
            .add('#energy-state').add('#matebolism-state').add('#bodyFat2-state').add('#coreSize-state')
            .text('正常').css('color', '#0b78e3');

          $('#leftArmMuscle').add('#rightArmMuscle').add('#leftLegMuscle').add('#rightLegMuscle').add('#absMuscle').text('0kg');

          $('#leftArmFat').add('#rightArmFat').add('#leftLegFat').add('#rightLegFat').add('#absFat').text('0kg/0%');
        },
        packageItem(label, info, status) {
          $('#' + label).text(info[label]);
          $('#' + label + '-down').text(info[label + '_min']);
          $('#' + label + '-up').text(info[label + '_max']);

          if (status) {
            $('#' + label + '-state').text('(' + status + ')');
            if (status == '正常') {
              $('#' + label + '-state').css('color', '#0b78e3');
            } else {
              $('#' + label + '-state').css('color', '#e60012');
            }
          } else {
            $('#' + label + '-state').text('(' + info[label + '_evaluating'] + ')');
            if (info[label + '_evaluating'] == '正常') {
              $('#' + label + '-state').css('color', '#0b78e3');
            } else {
              $('#' + label + '-state').css('color', '#e60012');
            }
          }
        },
        getBodyInfo(val) {
          $.initData();
          const bodyArr = val.split('_');
          if (Array.isArray(bodyArr) && bodyArr.length === 3) {
            const id = bodyArr[0],
              type = bodyArr[1];
            if (type === 'vis' || type === 'zk') {
              const param = `body=${val}&busId=${busId}&userId=${userId}`;
              // 防止多次回退
              window.location.replace(`./dimension.html?${encodeURIComponent(param)}`);
            } else if (type === 'inbody' || type === 'youjian' || type === 'ivep' || type === 'lw' || type === 'ts') {
              $.post(`${$.getBaseUrl()}/Coach/InBodyData/getStaminaInfo`, {
                id: id,
                device: type
              }, res => {
                if (res.errorcode == 0) {
                  var pageData = res.data
                  
                  if (pageData.ivep_CHEST || pageData.ivep_LA || pageData.ivep_CHEST || pageData.ivep_LT || pageData.ivep_RA || pageData.ivep_WAIST || pageData.ivep_RT) {
                    $('#ivepData').html('<div class="pic-part1">' + pageData.ivep_LA + '</div><div class="pic-part2">' + pageData.ivep_CHEST + '</div><div class="pic-part3">' + pageData.ivep_HIP + '</div><div class="pic-part4">' + pageData.ivep_LT + '</div><div class="pic-part5">' + pageData.ivep_RA + '</div><div class="pic-part6">' + pageData.ivep_WAIST + '</div><div class="pic-part7">' + pageData.ivep_RT + '</div>')
                  } else {
                    $('#ivepDataWrap').addClass('shouldhide').hide()
                  }

                  if (pageData.SEX == 2) {
                    $('#ivepData').addClass('pic-women')
                  }
                  $('#weight').text(res.data.weight);
                  $('#weight-down').text(res.data.weight_min);
                  $('#weight-up').text(res.data.weight_max);
                  $('#weight-state').text('(' + res.data.wt_evaluating + ')');
                  if (res.data.wt_evaluating == '正常') {
                    $('#weight-state').css('color', '#0b78e3');
                  } else {
                    $('#weight-state').css('color', '#e60012');
                  }

                  $('#bone').text(res.data.skeletal_muscle);
                  $('#bone-down').text(res.data.skeletal_muscle_min);
                  $('#bone-up').text(res.data.skeletal_muscle_max);
                  $('#bone-state').text('(' + res.data.smm_evaluating + ')');
                  if (res.data.smm_evaluating == '正常') {
                    $('#bone-state').css('color', '#0b78e3');
                  } else {
                    $('#bone-state').css('color', '#e60012');
                  }

                  $('#bodyFat').text(res.data.body_fat);
                  $('#bodyFat-down').text(res.data.body_fat_min);
                  $('#bodyFat-up').text(res.data.body_fat_max);
                  if (type !== 'ivep') {
                    $('#bodyFat-state').text('(' + res.data.bfm_evaluating + ')');
                    if (res.data.bfm_evaluating == '正常') {
                      $('#bodyFat-state').css('color', '#0b78e3');
                    } else {
                      $('#bodyFat-state').css('color', '#e60012');
                    }
                  } else {
                    $('#bodyFat-state').hide()
                  }

                  
                  $('#leanWeight').text(res.data.weight_without_fat);
                  $('#leanWeight-down').text(res.data.weight_without_fat_min);
                  $('#leanWeight-up').text(res.data.weight_without_fat_max);
                  $('#leanWeight-state').text('(' + res.data.ffm_evaluating + ')');
                  if (res.data.ffm_evaluating == '正常') {
                    $('#leanWeight-state').css('color', '#0b78e3');
                  } else {
                    $('#leanWeight-state').css('color', '#e60012');
                  }

                  $('#water').text(res.data.body_contain_water);
                  $('#water-down').text(res.data.body_contain_water_min);
                  $('#water-up').text(res.data.body_contain_water_max);
                  $('#water-state').text('(' + res.data.tbw_evaluating + ')');
                  if (res.data.tbw_evaluating == '正常') {
                    $('#water-state').css('color', '#0b78e3');
                  } else {
                    $('#water-state').css('color', '#e60012');
                  }

                  $('#energy').text(res.data.body_quality);
                  $('#energy-down').text(res.data.body_quality_min);
                  $('#energy-up').text(res.data.body_quality_max);
                  $('#energy-state').text('(' + res.data.bmi_evaluating + ')');
                  if (res.data.bmi_evaluating == '正常') {
                    $('#energy-state').css('color', '#0b78e3');
                  } else {
                    $('#energy-state').css('color', '#e60012');
                  }

                  $('#matebolism').text(res.data.basal_metabolism);
                  $('#matebolism-down').text(res.data.basal_metabolism_min);
                  $('#matebolism-up').text(res.data.basal_metabolism_max);
                  $('#matebolism-state').text('(' + res.data.bmr_evaluating + ')');
                  if (res.data.bmr_evaluating == '正常') {
                    $('#matebolism-state').css('color', '#0b78e3');
                  } else {
                    $('#matebolism-state').css('color', '#e60012');
                  }

                  $('#bodyFat2').text(res.data.body_fat_rate);
                  $('#bodyFat2-down').text(res.data.body_fat_rate_min);
                  $('#bodyFat2-up').text(res.data.body_fat_rate_max);
                  $('#bodyFat2-state').text('(' + res.data.pbf_evaluating + ')');
                  if (res.data.pbf_evaluating == '正常') {
                    $('#bodyFat2-state').css('color', '#0b78e3');
                  } else {
                    $('#bodyFat2-state').css('color', '#e60012');
                  }

                  $('#coreSize').text(res.data.waist_hip_ratio);
                  $('#coreSize-down').text(res.data.waist_hip_ratio_min);
                  $('#coreSize-up').text(res.data.waist_hip_ratio_max);
                  $('#coreSize-state').text('(' + res.data.whr_evaluating + ')');
                  if (res.data.whr_evaluating == '正常') {
                    $('#coreSize-state').css('color', '#0b78e3');
                  } else {
                    $('#coreSize-state').css('color', '#e60012');
                  }
                  if (type !== 'ivep') {
                    $('#leftArmMuscle').text(res.data.lla + 'kg');
                    $('#rightArmMuscle').text(res.data.lra + 'kg');
                    $('#leftLegMuscle').text(res.data.lll + 'kg');
                    $('#rightLegMuscle').text(res.data.lrl + 'kg');
                    $('#absMuscle').text(res.data.lt + 'kg');
                  }

                  if (type == 'inbody' || type === 'lw') {
                    // $('.inbody-type').css('visibility', 'visible');
                    $('.inbody-type').show();
                    if (type !== 'ivep') {
                      $('#leftArmFat').text(res.data.fla + 'kg/' + res.data.pbfla + '%');
                      $('#rightArmFat').text(res.data.fra + 'kg/' + res.data.pbfra + '%');
                      $('#leftLegFat').text(res.data.fll + 'kg/' + res.data.pbfll + '%');
                      $('#rightLegFat').text(res.data.frl + 'kg/' + res.data.pbfrl + '%');
                      $('#absFat').text(res.data.ft + 'kg/' + res.data.pbft + '%');
                    }
                  } else if (type == 'youjian') {
                    // $('.inbody-type').css('visibility', 'hidden');
                    $('.inbody-type').hide();
                    if (type !== 'ivep') {
                      $('#leftArmFat').text(res.data.fla + 'kg');
                      $('#rightArmFat').text(res.data.fra + 'kg');
                      $('#leftLegFat').text(res.data.fll + 'kg');
                      $('#rightLegFat').text(res.data.frl + 'kg');
                      $('#absFat').text(res.data.ft + 'kg');
                    }
                  } else if (type === 'ts') {
                    // TODO: 泰山体测仪
                    $('.inbody-type').show();
                    if (type !== 'ivep') {
                      $('#leftArmFat').text(res.data.fla + 'kg/' + res.data.pbfla + '%');
                      $('#rightArmFat').text(res.data.fra + 'kg/' + res.data.pbfra + '%');
                      $('#leftLegFat').text(res.data.fll + 'kg/' + res.data.pbfll + '%');
                      $('#rightLegFat').text(res.data.frl + 'kg/' + res.data.pbfrl + '%');
                      $('#absFat').text(res.data.ft + 'kg/' + res.data.pbft + '%');
                    }
                    $.packageItem('height', res.data);
                    $.packageItem('protein', res.data);
                    $.packageItem('mineral_salt', res.data);
                    $.packageItem('peace_heart', res.data);
                    $.packageItem('diastolic', res.data);
                    $.packageItem('systolic', res.data);
                    $.packageItem('reaction_time', res.data);
                    $.packageItem('grip_power', res.data);
                    $.packageItem('sit_and_reach', res.data);
                    $.packageItem('single_stand', res.data);
                    $.packageItem('up_and_down', res.data);
                    $.packageItem('vital_capacity', res.data);
                    $.packageItem('step', res.data);
                    $.packageItem('body_fat', res.data, res.data.bfm_evaluating);
                  }

                  // FIXME: 泰山体测仪
                  const inbodyTagList = ['skeletonTag', 'noFatTag', 'basicConsumeTag', 'coreSizeTag'];
                  const tsTagList = ['heightTag', 'proteinTag', 'saltTag', 'heartTag', 'inPressureTag', 'outPressureTag', 'reflectTag', 'gripTag', 'strengthTag', 'oneFootTag', 'pushUpTag', 'breathTag', 'stairTag', 'bodyFatTag'];
                  if (type === 'ts') {
                    for (let i = 0; i < inbodyTagList.length; i++) {
                      $('#' + inbodyTagList[i]).css('display', 'none');
                    }
                  } else {
                    for (let i = 0; i < tsTagList.length; i++) {
                      $('#' + tsTagList[i]).css('display', 'none');
                    }
                  }
                  if(pageData.is_jump) {
                    const { jump_url, wx_appid, wx_version } = pageData
                    const info = {
                      jump_url,
                      wx_appid,
                      wx_version
                    };
                    wx.miniProgram.navigateTo({
                      url: '/packageMy/jump/jump?&info=' + encodeURIComponent(JSON.stringify(info)),
                    })
                    return;
                  }
                } else {
                  console.log(res.errormsg);
                }
              });
            }
          }
        }
      });
      const query = decodeURIComponent(window.location.search);
      let paramArr = '';
      let vArr = '';
      if (query.length !== 0) {
        paramArr = query.split('&');
        if (paramArr.length === 3) {
          busId = paramArr[1].split('=')[1];
          userId = paramArr[2].split('=')[1];
        }
        vArr = paramArr[0].substr(6).split('_');
        if (Array.isArray(vArr) && vArr.length === 3) {
          USER_ID = vArr[2];
          $('.title-trend:first').click(function () {
            wx.miniProgram.navigateTo({url: '/pages/physicalExamination/physicalExamination?uid=' + USER_ID});
          });
        }
      }
      $('.tab').click(function () {
        if (!$(this).hasClass('selected')) {
          $('.tab').removeClass('selected');
          $(this).addClass('selected');
          $('.area-card').toggle();
          $('.shouldhide').hide();
          if (vArr && vArr[1] && vArr[1] === 'ivep') {
            $('#jieduan').hide()
            $('#jieduanzhi').hide()
          }
        }
      });



      $.post(`${$.getBaseUrl()}/Coach/InBodyData/getDropDownList`, {
        uid: USER_ID
      }, res => {
        if (res.errorcode == 0) {
          $('#dateOfTest').empty();
          res.data.forEach((item, index) => {
            $('#dateOfTest').append(`<option ${index === 0 ? 'selected' : ''} value="${item.id}_${item.device}_${USER_ID}">${item.test_time}</option>`);
          });

          const query = decodeURIComponent(window.location.search);
          if (query.length === 0) {
            $.getBodyInfo($('#dateOfTest').val());
          } else {
            val = paramArr[0].substr(6)
            if (vArr[0] == -1) {
              $.getBodyInfo($('#dateOfTest').val());
            } else {
              $('#dateOfTest').val(val);
              $.getBodyInfo(val);
            }
          }

          $('#dateOfTest').change(function () {
            $.getBodyInfo($(this).val());
          });
        } else {
          console.log(res.errormsg);
        }
      });
      window.addEventListener("popstate", function(e) {
        wx.miniProgram.navigateBack({
            delta: 1
        });
      }, false);
    });
  </script>

</body>

</html>