<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>等待页面</title>
  <style>
    /* root */
    html, body {
      margin: 0;
      padding: 0;
      font-size: 10px;
    }

    .box {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .h-txt {
      font-size: 1.4rem;
    }

    .h-or {
      font-size: 4rem;
    }

    button {
      font-size: 2rem;
      color: black;
      background-color: #fff;
      border: .1rem solid gray;
      border-radius: .5rem;
      height: 4rem;
      width: 20rem;
    }
  </style>
</head>
<body>
  <div class="box">
    <p class="h-txt">正在分析您的体测结果，大约需要30秒，请等待！</p>
    <p class="h-or">or</p>
    <p class="h-txt">不想等待？点击下方按钮后，即可关闭此页面</p>
    <button>用微信消息通知我</button>
  </div>
</body>
</html>