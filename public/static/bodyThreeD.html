<!DOCTYPE html>
<html>
<head>
	<title>体测详情</title>
	<meta charset="utf-8">
	<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport" />
	<style type="text/css">
		html,body{
			padding: 0;
			margin: 0;
			background: #f5f5f5;
			overflow: hidden;
			font-size: 10px;
		}

		html,body,#model{
			position: relative;
			width: 100%;
			height: 100%;
			text-align: center;
		}

		.model-title {
			position: absolute;
			top: 5.4rem;
			left: 2.2rem;
			z-index: 100;
			display:block;
			font-size: 1.2rem;
			color: #a1a4b0;
		}

		.model-name {
			position: absolute;
			top: 13.7rem;
			left: 4rem;
			z-index: 100;
			display:block;
			font-size: 1.3rem;
			font-weight: bold;
			color: #262626;
			display: flex;
			flex-direction: row;
			align-items: center;
		}

		.model-name-circle {
			height: 1.1rem;
			width: 1.1rem;
			border: .25rem solid #faa81c;
			box-sizing: border-box;
			overflow: hidden;
			border-radius: 50%;
			margin-right: .6rem;
		}

		.model-value {
			position: absolute;
			right: 2.9rem;
			bottom: 17.8rem;
			z-index: 100;
			display:block;
			font-size: 1.5rem;
			font-weight: bold;
			color: #262626;
			height: 7.7rem;
			width: 7.7rem;
			border: .2rem solid #faa81c;
			border-radius: 50%;
			overflow: hidden;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.view-head {
			height: 3.7rem;
			width: 100%;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
		}

		.view-foot {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-top: .4rem;
		}

		.view-foot a {
			margin: 0 .6rem 0 1rem;
			font-size: 1.3rem;
			font-weight: bold;
			color: #0b78e3;
			text-decoration: none;
		}

		.view-foot img {
			height: 1rem;
			width: .55rem;
		}

		.physical-examination-date {
			font-size: 1.3rem;
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-left: 1rem;
		}

		#dateOfTest {
      border: none;
      outline: none;
      appearance:none;
      -moz-appearance:none;
      -webkit-appearance:none;
      background-color: #f5f5f5;
      font-size: 1.3rem;
      color: #262626;
    }

		.physical-examination-trend {
			font-size: 1.3rem;
			font-weight: bold;
			color: #0b78e3;
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-right: 1rem;
		}

		.physical-examination-date img {
			height: 1.3rem;
			width: 1.3rem;
			margin-right: .9rem;
		}

		.physical-examination-trend img {
			height: 1rem;
			width: .55rem;
			margin-left: .9rem;
		}
	</style>
</head>
<body>
	<div class="view-head">
		<div class="physical-examination-date">
			<img src="./img/date.png">
			<select id="dateOfTest"></select>
		</div>
		<div class="physical-examination-trend">
			<p>体测曲线走势</p>
			<img src="./img/next.png">
		</div>
	</div>

	<div id="model"></div>

	<div class="model-title"></div>
	<div class="model-name">
		<div class="model-name-circle"></div>
		<label id="modelName"></label>
	</div>
	<div class="model-value">
		<div id="modelValue"></div>
	</div>

	<script id="vs" type="x-shader/x-vertex">
		#define PHYSICAL

		uniform float uVertexOffset;
		varying vec2 vUv;

		vec3 defaultRimColor = vec3(0.4);
		varying vec3 vRimColor;

		varying vec3 vViewPosition;
		#ifndef FLAT_SHADED
			varying vec3 vNormal;
		#endif

		#include <common>
		#include <uv_pars_vertex>
		#include <uv2_pars_vertex>
		#include <displacementmap_pars_vertex>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <morphtarget_pars_vertex>
		#include <skinning_pars_vertex>
		#include <shadowmap_pars_vertex>
		#include <specularmap_pars_fragment>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		void main() {
			#include <uv_vertex>
			#include <uv2_vertex>
			#include <color_vertex>
			#include <beginnormal_vertex>
			#include <morphnormal_vertex>
			#include <skinbase_vertex>
			#include <skinnormal_vertex>
			#include <defaultnormal_vertex>

		#ifndef FLAT_SHADED // Normal computed with derivatives when FLAT_SHADED
			vNormal = normalize( transformedNormal );
		#endif
			#include <begin_vertex>
			#include <displacementmap_vertex>
			#include <morphtarget_vertex>
			#include <skinning_vertex>


		#ifdef USE_MEASURE
		    transformed += normalize(objectNormal) * uVertexOffset;
		#endif
		    vUv = uv;


			#include <project_vertex>



			vec4 mvNormal =  normalize(modelViewMatrix * vec4(objectNormal, 0.0));
		    float rim = 1.0 - saturate(dot(-normalize(mvPosition.xyz / mvPosition.w), mvNormal.xyz));
		    vRimColor = defaultRimColor * pow(rim, 4.0);

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			vViewPosition = - mvPosition.xyz;
			#include <worldpos_vertex>
			#include <shadowmap_vertex>
			#include <fog_vertex>
		}

	</script>
	<script id="fs" type="x-shader/x-fragment">
		#define PHYSICAL

		varying vec3 vRimColor;
		varying vec2 vUv;
		vec3 powThreshold = vec3(0.001);
		uniform sampler2D uFullTextureMapTex;
		uniform float uTextureMapSwitch;

		uniform sampler2D uRulerTexSelected;
		uniform float uRulerUVScale;
		uniform vec3 uRulerControl;

		uniform vec3 diffuse;
		uniform vec3 emissive;
		uniform float roughness;
		uniform float metalness;
		uniform float opacity;

		#ifndef STANDARD
			uniform float clearCoat;
			uniform float clearCoatRoughness;
		#endif

		varying vec3 vViewPosition;

		#ifndef FLAT_SHADED
			varying vec3 vNormal;
		#endif

		#include <common>
		#include <packing>
		#include <color_pars_fragment>
		#include <uv_pars_fragment>
		#include <uv2_pars_fragment>
		#include <map_pars_fragment>
		#include <alphamap_pars_fragment>
		#include <aomap_pars_fragment>
		#include <lightmap_pars_fragment>
		#include <emissivemap_pars_fragment>
		#include <envmap_pars_fragment>
		#include <fog_pars_fragment>
		#include <bsdfs>
		#include <cube_uv_reflection_fragment>
		#include <lights_pars>
		#include <lights_physical_pars_fragment>
		#include <shadowmap_pars_fragment>
		#include <bumpmap_pars_fragment>
		#include <normalmap_pars_fragment>
		#include <roughnessmap_pars_fragment>
		#include <metalnessmap_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		void main() {
			#include <clipping_planes_fragment>

			vec4 diffuseColor = vec4( diffuse, opacity );
			ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );
			vec3 totalEmissiveRadiance = emissive;

			#include <logdepthbuf_fragment>
			#include <map_fragment>
			#include <color_fragment>
			#include <alphamap_fragment>
			#include <alphatest_fragment>
			#include <specularmap_fragment>
			#include <roughnessmap_fragment>
			#include <metalnessmap_fragment>
			#include <normal_flip>
			#include <normal_fragment>
			#include <emissivemap_fragment>

			// accumulation
			#include <lights_physical_fragment>
			#include <lights_template>

			// modulation
			#include <aomap_fragment>

			vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;
		    vec3 c = outgoingLight + vRimColor;
		    float alpha = diffuseColor.a;

		#ifdef USE_TEXTUREMAP
		    vec3 col = texture2D(uFullTextureMapTex, vUv).xyz;
		    c *= pow(clamp(col, vec3(0.0001), vec3(1.0)), vec3(uTextureMapSwitch));
		#endif

		    //sampling girth ruler texture
		#ifdef USE_MEASURE
			vec3 selectedRulerCol = texture2D(uRulerTexSelected, vec2(-vUv.x * uRulerUVScale, -vUv.y)).xyz;
		    // vec3 selectedRulerCol = texture2D(uRulerTexSelected, vec2(vUv.x * uRulerUVScale, vUv.y)).xyz;
		    c =  selectedRulerCol * uRulerControl + (vec3(1.0) - uRulerControl);
		    alpha = clamp(uRulerControl.x, 0.2, 0.6);
		#endif

			gl_FragColor = vec4( c, alpha);
			#include <premultiplied_alpha_fragment>
			#include <tonemapping_fragment>
			#include <encodings_fragment>
			#include <fog_fragment>
		}
	</script>
	<script src="body_three_d_js/jquery.min.js"></script>
	<script src="body_three_d_js/three.min.js"></script>
	<script src="body_three_d_js/OBJLoader.js"></script>
	<script src="body_three_d_js/OrbitControls.js"></script>
	<script src="body_three_d_js/model.js"></script>
	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.0.js"></script>
	<script type="text/javascript">
		//模型及围度路径
		var jsonPath = '';
		var modelPath = '';
		var bodyTypeUrl = '';

		//模型加载完成回调
    function loadOBJOver(){
      console.log('模型加载完毕');
      initMeasureCurveFromJSON(jsonPath);
    }

    //围度加载完成回调
    function drawOver(){
      console.log('围度加载完毕');
      //切换环境 显示围度
      selectScene(Scene.Measure);
    }

    //选择围度 数据回调
    function selectCurveValue(name,value,girth){
			$('#modelName').text(name);
			$('#modelValue').text(value+'cm');
		}

		let busId = '', shitUserId = '', sceneId = '';

		$.extend({
			getBaseUrl() {
				let baseUrl,
					host = window.location.host,
					subDomain = host.split('.')[0];
				if (subDomain === 'vip-sim') {
					baseUrl = 'https://sim.rocketbird.cn';
				} else if (subDomain === 'vip') {
					baseUrl = 'https://wx.rocketbird.cn';
				} else {
					baseUrl = 'https://beta.rocketbird.cn';
				}
				return baseUrl;
			},
			initData() {
				console.log('假装初始化！');
			},
			getBodyInfo(val) {
				$.initData();
				const vArr = val.split('_');
				if (Array.isArray(vArr) && vArr.length === 3) {
					const id = vArr[0],
								type = vArr[1],
								userId = vArr[2],
								param = `body=${val}&busId=${busId}&userId=${userId}`;
					if (type === 'vis') {
            bodyTypeUrl = './health/dimension.html?' + encodeURIComponent(param);
						$('.model-title:first').text('数据信息来自维塑');
					} else if (type === 'inbody') {
            bodyTypeUrl = './health/inbody.html?' + encodeURIComponent(param);
						$('.model-title:first').text('数据信息来自inbody');
					}
					$.post(`${$.getBaseUrl()}/Mobile/Vis/getVisBodyPoint`, {
						scene: sceneId,
						bus_id: busId,
						user_id: shitUserId
					}, res => {
						if (res.errorcode == 0) {
							jsonPath = res.data.json_url;
							modelPath = res.data.model_url;
							//加载模型
      				loadOBJ(modelPath);
						} else {
							console.log(res.errormsg);
						}
					});
				}
			}
		});

		$(document).ready(function(){

			const query = decodeURIComponent(window.location.search);
			if (query.length !== 0) {
				const paramArr = query.split('&');
				if (paramArr.length > 1) {
					busId = paramArr[1].split('=')[1];
					shitUserId = paramArr[2].split('=')[1];
					sceneId = paramArr[3].split('=')[1];
				}
				const val = paramArr[0].substr(6);
				$.post(`${$.getBaseUrl()}/Coach/InBodyData/getDropDownList`, {
					uid: val,
					type: 'vis'
				}, res => {
					if (res.errorcode == 0) {
						$('#dateOfTest').empty();
						res.data.forEach(item => {
							$('#dateOfTest').append(`<option value="${item.id}_${item.device}_${val}">${item.test_time}</option>`);
						});

						$.getBodyInfo($('#dateOfTest').val());

						setTimeout(() => {
							$('#dateOfTest').val(sceneId+'_vis_'+shitUserId);
						}, 666);

						$('#dateOfTest').change(function () {
							$.getBodyInfo($(this).val());
						});
					} else {
						console.log(res.errormsg);
					}
				});
				$('.physical-examination-trend:first').click(function(){
					wx.miniProgram.navigateTo({url: '/pages/physicalExamination/physicalExamination?uid=' + val});
				});
				$('.view-foot:first').click(function(){
					window.location.href = bodyTypeUrl;
				});
			}
		});
	</script>
</body>
</html>