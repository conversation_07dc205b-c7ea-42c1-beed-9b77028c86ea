//Create a mock object for testing on a desktop browser
var mockPrecorSignIn = {
  signIntoPreva: function (token) {
    var succeeded = true;
    this.onSignIntoPrevaCompleted(succeeded);
    console.log('Signing in with token ' + token)
  },
  onShown: function () {
    console.log('onShown')
  },
  complete: function () {
    console.log('Complete')
  },
  getProviderConfig: function () {
      return ''
  },
  getFitnessEquipmentInfo: function () {
      return '{"serialNumber":""}'
  }
};
var setTime = ''
var precorSignin = precorSignin != undefined ? precorSignin : mockPrecorSignIn;
var precorTxt = document.getElementById('precorTxt');
var precorQrcode = document.getElementById('qrcodeImg');
var precoAjax = {
  init: function (type) {
    var xhr = false;
    if (window.XMLHttpRequest) { 
      xhr = new XMLHttpRequest();
      if (xhr.overrideMimeType) {
        if (type == "text") {
          xhr.overrideMimeType("text/plain");
        } else if (type == "xml") {
          xhr.overrideMimeType("text/xml");
        }
      }
    } else if (window.ActiveXObject) { //IE 浏览器
      try {
        xhr = new ActiveXObject("Msxml2.XMLHTTP");
      } catch (e) {
        try {
          xhr = new ActiveXObject("Microsoft.XMLHTTP");
        } catch (e) {}
      }
    }
    return xhr;
  },
  respone: function (callBack, type, xhr) {
    if (xhr.readyState == 4 && xhr.status == 200 || xhr.status == 304) {
      if (type == "text") {
        return callBack(xhr.responseText, xhr);
      } else if (type == "xml") {
        return callBack(xhr.responseXML, xhr);
      }
    }
  },
  get: function (url, async, callBack, type) {
    var xhr = precoAjax.init(type);
    async = async ||true;
    data = data || null;
    callBack = callBack || function () {};
    type = type || "text";
    type = type.toLowerCase();

    xhr.open("GET", url, async);
    xhr.onreadystatechange = function () {
      return precoAjax.respone(callBack, type, xhr);
    };
    xhr.send();
  },
  post: function (url, data, async, callBack, type) {
    var xhr = precoAjax.init(type);
    async = async ||true;
    data = data || null;
    callBack = callBack || function () {};
    type = type || "text";
    type = type.toLowerCase();

    xhr.open("POST", url, async);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function () {
      return precoAjax.respone(callBack, type, xhr);
    };
    xhr.send(data);
  },
}
var precorPostData = 'bus_id=' + precorSignin.getProviderConfig() + '&device_id=' + JSON.parse(precorSignin.getFitnessEquipmentInfo()).serialNumber;
precorSignin.onShown = function () {
  precorTxt.innerText = '微信扫码 · 开启运动';
  precoAjax.post(getBaseUrl() + '/Web/Precor/get_qrcode', precorPostData, true, function (res, obj) {
    res = JSON.parse(res)
    if (res.errorcode == 0) {
      precorQrcode.src = res.data;
      doSignIn()
      setTimeout(function () {
        clearTimeout(setTime);
        precorSignin.complete();
      }, 90*1000);
    } else {
      precorTxt.innerText = res.errormsg;
    }
  });
};

//Set the onSignInToPrevaCompleted callback
//In a real implementation, complete should only be called if 'succeeded' is true
precorSignin.onSignIntoPrevaCompleted = function (succeeded) {
  if (succeeded) {
    precorTxt.innerText = '进入系统成功';
    precorSignin.complete();
  } else {
    precorTxt.innerText = '进入系统失败，稍后再试';
    setTimeout(function(){
      precorSignin.complete();
    }, 2000);
  }
  
};
function precorBack() {
  clearTimeout(setTime);
  precorSignin.complete();
}
function getBaseUrl() {
  var baseUrl,
    host = window.location.host,
    subDomain = host.split('.')[0];
  if (subDomain === 'vip-sim') {
    baseUrl = 'https://sim.rocketbird.cn';
  } else if (subDomain === 'vip') {
    baseUrl = 'https://wx.rocketbird.cn';
  } else {
    baseUrl = 'https://beta.rocketbird.cn';
  }
  return baseUrl;
};
//Use the inputted data to sign in
//Call prevent default to prevent page from reloading on submit
function doSignIn() {
  precoAjax.post(getBaseUrl() + '/Web/Precor/get_device_token', precorPostData, true, function (res, obj) {
    res = JSON.parse(res)
    if (res.errorcode == 0) {
      precorTxt.innerText = '登录成功，进入系统中';
      precorSignin.signIntoPreva(res.data.token);
    } else {
     setTime = setTimeout(function () {
        doSignIn()
      }, 3000);
    }
  });
}