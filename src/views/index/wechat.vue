<template>
  <div class="container">
    <Card style="margin-top: 20px;">
      <Alert type="warning" show-icon>如果没有打开新的微信月付页面，请点击按钮打开; 或者复制页面地址, 然后在浏览器中打开</Alert>
      <Button type="primary" @click="handleOpenWindow">打开微信月付页面</Button>
      <Button style="margin-left: 20px;" @click="handleCopyUrl">复制页面地址</Button>
      <Input ref="urlInput" readonly style="margin-top: 20px;" v-model="url" />
    </Card>
  </div>
</template>

<script setup>
import { ref, defineComponent } from 'vue';

defineComponent({ name: 'WeChat' })

const url = ref()
const urlInput = ref()
if (window.location.hostname.indexOf('test') === -1) {
  url.value = 'https://paycontract.rocketbird.cn/'
} else {
  url.value = 'https://paycontract-dev.rocketbird.cn/'
}

const handleOpenWindow = () => {
  window.open(url.value, '_blank')
}
const handleCopyUrl = () => {
  urlInput.value.$refs.input.select()
  urlInput.value.$refs.input.setSelectionRange(0, 99999)
  document.execCommand('copy')
}
</script>

<style lang="less" scoped></style>