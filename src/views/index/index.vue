<template>
  <div class="wall">
    <div class="box">
      <div class="box-main">
        <div class="main-head">
          <div class="main-flow card">
            <HelpCircle class="help-tips" color="#fff">
              <template>
                <p>训练：仅包含自主训练 和 参加团操课人数</p>
                <p>私教：私教课消课实际人数包含体验课</p>
                <p v-if="showSwimming">泳教：泳教课消课实际人数包含体验课</p>
                <p v-if="showBooking">票务：散场票和订场人数</p>
                <p>到访：预约到店实际人数</p>
              </template>
            </HelpCircle>
            <p class="main-primary">今日客流
              <strong>{{todayFlowData.all_class}}</strong>人</p>
            <p class="main-secondary">
              <span class="min-span">训练
                <strong class="min-strong">{{todayFlowData.other_class}}</strong>人</span>
              <span class="min-span">私教
                <strong class="min-strong">{{todayFlowData.private_class}}</strong>人</span>
              <span v-if="showSwimming" class="min-span">泳教
                <strong class="min-strong">{{todayFlowData.swim_class}}</strong>人</span>
              <span v-if="showBooking" class="min-span">票务
                <strong class="min-strong">{{todayFlowData.san_log_data}}</strong>人</span>
              <span class="min-span">到访
                <strong class="min-strong">{{todayFlowData.to_visit_list}}</strong>人</span>
            </p>
          </div>
          <div class="main-sale card">
            <div class="main-secondary sale-info">
              <div>今日新售卡
                <strong>{{daysSaleData.today.card_count}}</strong>张</div>
              <div>本月新售卡
                <strong>{{daysSaleData.all.card_count}}</strong>张</div>
            </div>
            <div class="main-secondary sale-info">
              <div>今日新售私教
                <strong>{{daysSaleData.today.private_count}}</strong>节</div>
              <div>本月新售私教
                <strong>{{daysSaleData.all.private_count}}</strong>节</div>
            </div>
            <div v-if="showSwimming" class="main-secondary sale-info">
              <div>今日新售泳教
                <strong>{{daysSaleData.today.swim_count}}</strong>节</div>
              <div>本月新售泳教
                <strong>{{daysSaleData.all.swim_count}}</strong>节</div>
            </div>
          </div>
        </div>
        <div class="main-body">
          <div class="flow-stat card">
            <!--<img v-if="todayFlowData.count == 0" src="../../assets/img/stat_null_2.png" alt="null" />-->
            <div id="flowStat" style="height:100%;width:100%;"></div>
          </div>
        </div>
        <div class="main-foot">
          <router-link to="/member?curMenu=会籍会员&fastMenu=有效会籍会员">
            <div class="main-tip card tip-effective">
              <p>{{tipData.availability}}</p>
              <p>有效会籍会员</p>
            </div>
          </router-link>
          <router-link to="/member?curMenu=会籍会员&fastMenu=会籍潜客">
            <div class="main-tip card tip-potential">
              <p>{{tipData.not_buy_card}}</p>
              <p>会籍潜客</p>
            </div>
          </router-link>
          <router-link to="/member?curMenu=会籍会员&fastMenu=会籍过期会员">
            <div class="main-tip card tip-overdue">
              <p>{{tipData.overdue}}</p>
              <p>会籍过期会员</p>
            </div>
          </router-link>
          <router-link to="/member?curMenu=私教会员&fastMenu=有效私教会员">
            <div class="main-tip card tip-normal">
              <p>{{tipData.private_card}}</p>
              <p>有效私教会员</p>
            </div>
          </router-link>
          <router-link v-if="showSwimming" to="/member?curMenu=泳教会员&fastMenu=有效泳教会员">
            <div class="main-tip card tip-coach">
              <p>{{tipData.swim_card}}</p>
              <p>有效泳教会员</p>
            </div>
          </router-link>
        </div>
      </div>
      <div class="box-sub">
        <div class="sub-business card">
          <div class="business-head">
            <div class="head-title">
              <p v-if="bizStatVal == 'today'">
                今日流水
                <Icon type="social-yen"></Icon>
                <strong>{{daysBusinessData.day}}</strong>
              </p>
              <p v-else-if="bizStatVal == 'month'">
                本月流水
                <Icon type="social-yen"></Icon>
                <strong>{{daysBusinessData.month}}</strong>
              </p>
            </div>
            <div class="change-biz-stat">
              <Select v-model="bizStatVal" size="small" @on-change="handleBizStatChange">
                <Option value="today">今日</Option>
                <Option value="month">本月</Option>
              </Select>
            </div>
          </div>
          <!-- <div class="business-body">
            <div class="business-stat">
              <div class="zen-stat">
                <div id="businessStat" style="height:100%;width:400px;"></div>
                <div class="biz-legend">
                  <ul>
                    <li>
                      <p id="buy_card">0</p>
                    </li>
                    <li>
                      <p id="renew_card">0</p>
                    </li>
                    <li>
                      <p id="pt_buy_card">0</p>
                    </li>
                    <li>
                      <p id="pt_renew_card">0</p>
                    </li>
                    <li>
                      <p id="change_card">0</p>
                    </li>
                    <li>
                      <p id="deliver_card">0</p>
                    </li>
                    <li>
                      <p id="goods_total">0</p>
                    </li>
                    <li>
                      <p id="rent_cupboard">0</p>
                    </li>
                    <li>
                      <p id="cancellation_card">0</p>
                    </li>
                    <li>
                      <p id="front_money">0</p>
                    </li>
                    <li>
                      <p id="cash_pledge">0</p>
                    </li>
                    <li>
                      <p id="cross_buy_card">0</p>
                    </li>
                    <li>
                      <p id="space_amount">0</p>
                    </li>
                    <li>
                      <p id="other">0</p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div> -->
          <mini-table :detail="this.miniTableDB" :show-swimming="showSwimming" :show-booking="showBooking" />
        </div>
        <div class="sub-membership card">
          <div class="membership-head">
            <div class="head-title">
              <p>今日课程</p>
            </div>
          </div>
          <div class="membership-body">
            <div class="membership-stat">
              <div class="stat-null" v-if="courseData.length == 0" @click="handleCourseNull">
                <img src="../../assets/img/stat_null_2.png" alt="null" />
                <p>添加课程</p>
              </div>
              <Timeline v-else class="item-timeline">
                <Timeline-item :color="item.goodjob?'orange':'gray'" v-for="(item, index) in courseData" :key="index" :class="{'selected-timeline': item.goodjob}">
                  <div style="display:flex;flex-direction:row;">
                    <div class="time">{{item.beg_time}}
                      <span>{{item.class_name}}</span>
                    </div>
                    <div class="content">
                      <p class="person">
                        <img v-if="!item.goodjob" src="../../assets/img/sd_03.png" alt="person">
                        <img v-else src="../../assets/img/sd_25.png" alt="person">
                        <label>{{item.coach_name}}</label>
                      </p>
                      <p class="home">
                        <img v-if="!item.goodjob && item.classroom_name.length > 0" src="../../assets/img/sd_05.png" alt="home" />
                        <img v-else-if="item.goodjob && item.classroom_name.length > 0" src="../../assets/img/sd_26.png" alt="home" />
                        <label>{{item.classroom_name}}</label>
                      </p>
                      <p v-if="item.class_category == 0" class="subscription">
                        <a href="javascript:void(0);" @click="handleSubscription(item.class_id)">{{item.already_number}}/{{item.reserve_number}}</a>
                      </p>
                    </div>
                  </div>
                </Timeline-item>
              </Timeline>
            </div>
          </div>
        </div>
      </div>
      <div class="box-item item-s item-s-auto-height card">
        <div class="item-head">
          <div class="head-title">
            <p>会员购卡</p>
          </div>
        </div>
        <div class="item-body item-body-l item-ul">
          <div class="stat-null" v-if="membershipData.length == 0" @click="handleMembershipNull">
            <img src="../../assets/img/stat_null.png" alt="null" />
            <p>暂无数据</p>
          </div>
          <ul v-else class="membership-list">
            <li v-for="(member, index) in membershipData" :key="index">
              <p class="card-name">{{member.cardname}}</p>
              <p class="card-price">{{member.amount}}</p>
              <p class="card-person">
                <img src="../../assets/img/sd_13.png" alt="person">{{member.username}}</p>
              <p class="card-date">
                <img src="../../assets/img/sd_16.png" alt="time">{{member.buy_time}}</p>
            </li>
          </ul>
        </div>
      </div>
      <div class="box-item card item-l">
        <div class="item-head">
          <div class="head-title">
            <p>来源分析</p>
          </div>
          <div class="change-coach-stat">
            <Select v-model="coachStatVal" size="small" @on-change="handleCoachStatChange">
              <Option value="whereiscust">获得客户来源</Option>
              <Option value="membership">会籍成交方式</Option>
              <Option value="private">私教成交方式</Option>
              <!-- TODO: bp-> 获客方式 套餐包 唐伟说不要了 -->
              <!-- <Option value="package">套餐包成交方式</Option> -->
            </Select>
          </div>
        </div>
        <div class="item-body item-body-s">
          <div id="coachBizStat" style="height:100%;width:100%;"></div>
        </div>
      </div>
      <div class="box-item item-s card item-l">
        <div class="item-head">
          <div class="head-title">
            <p>本月业绩排行榜</p>
          </div>
          <div class="change-coach-stat">
            <Select v-model="saleTopSel" size="small" @on-change="handleSaleTopChange">
              <Option value="ms">会籍</Option>
              <Option value="coach">教练</Option>
            </Select>
          </div>
        </div>
        <!-- <div class="item-body item-body-s">
          <div id="coachBizStat" style="height:100%;width:100%;"></div>
        </div> -->
        <div class="item-body item-body-s">
          <!-- <div class="stat-null" v-if="marketNull" @click="handleMarketNull">
            <img src="../../assets/img/stat_null_2.png" alt="null" />
            <p>添加营销活动</p>
          </div> -->
          <!-- <div v-else class="market">
            <div class="market-img">
              <img :src="marketData.thumb" alt="营销活动" />
            </div>
            <div class="market-title" @click="handleMarketDetail(marketData.id)">{{marketData.name}}</div>
            <div class="market-subtitle">
              <p>报名截止时间：{{marketData.cutoffDate}}</p>
              <p>活动时间：{{marketData.beginDate}} - {{marketData.endDate}}</p>
            </div>
            <div class="market-info">
              <span>可报名
                <strong>{{marketData.about_number}}</strong>
              </span>
              <span>已报名
                <strong>{{marketData.sign_num}}</strong>
              </span>
              <span>今日报名
                <strong>{{marketData.curr_sign_num}}</strong>
              </span>
            </div>
            <div class="market-more" @click="handleMarketMore">
              查看全部活动
            </div>
          </div> -->
          <div class="sale-top">
            <div class="salesperson" v-for="(item, index) in saleList" :key="index">
              <div class="seq">No.{{index+1}}</div>
              <div class="avatar"><img :src="item.avatar"></div>
              <div class="name">{{item.name}}</div>
              <div class="amount">¥ {{item.amount}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-foot">
        <p class="company">Copyright ©  2016-{{currentYear}} 重庆勤鸟圈科技有限公司 All Rights Reserved. </p>
      </div>
    </div>
    <div class="right-side">
      <Poptip trigger="hover" class="item" placement="left">
        <span @click="$router.push('/help')">
        <Icon class="md-help" type="md-help" />
        <div>
          使用帮助
        </div>
        </span>
        <div slot="content" class="item-con" @click="$router.push('/help')">
           <div class="con-des">
             查看使用帮助
           </div>
        </div>
      </Poptip>
      <Poptip trigger="hover" class="item" placement="left">
        <img class="con-ico" src="../../assets/img/ico-phone.png" alt="售后电话" />
        <div>
          售后电话
        </div>
        <div slot="content" class="item-con">
          <div class="con-top">
           <Icon type="ios-call" :size="16" color="#666666"/> 023-67397805
          </div>
           <div class="con-des">
             接听时间：09:00-22:00
           </div>
        </div>
      </Poptip>
      <Poptip trigger="hover" class="item" placement="left">
        <img class="con-ico" src="../../assets/img/ico-code.png" alt="二维码" />
        <div>
          售后服务号
        </div>
        <div slot="content" class="item-con">
          <div class="con-top">
           <img src="../../assets/img/code.jpg" alt="二维码" />
          </div>
           <div class="con-des">
             关注我们 <br />
             咨询时间：09:00-22:00
           </div>
        </div>
      </Poptip>
      <div class="item" placement="left" v-if="pageToSupply" @click="handleRouteTo">
        <img class="con-ico" src="../../assets/img/ico-code.png" alt="二维码" />
        <div>
          企服核销
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  var echarts = require('echarts');
  import { formatDate } from '../../utils/index';
  // import { getNewHost } from '../../utils/config';

  const legendData = [];
  const seriesData = [];

  import MiniTable from '../statistics/components/MiniTable'
  import HelpCircle from 'components/form/helpCircle';

  export default {
    components: {
      MiniTable,
      HelpCircle
    },
    data() {
      return {
        flowChart: null,
        currentYear: new Date().getFullYear(),
        todayFlowData: {
          all_class: 0,
          other_class: 0,
          private_class: 0,
          swim_class: 0,
          to_visit_list: 0
        },
        flowOption: {
          title: [
            {
              text: '',
              left: 20,
              top: 10,
              textStyle: {
                fontSize: 14,
                color: '#999'
              }
            },
            {
              text: '',
              left: 82,
              top: 10,
              textStyle: {
                fontSize: 14,
                color: '#ff696a'
              }
            }
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          color: ['#ffbd55', '#ff696a', '#5eeeaf', '#52a4ea', '#ff81c2', '#94bfff'],
          legend: {
            data: legendData,
          },
          toolbox: {
            show: true,
            feature: {
              saveAsImage: { show: true }
            },
            right: 20
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: seriesData,
        },
        daysSaleData: {
          today: {
            card_count: 0,
            private_count: 0,
            swim_count: 0
          },
          all: {
            card_count: 0,
            private_count: 0,
            swim_count: 0
          }
        },
        miniTableDB: {},
        daysBusinessData: {
          day: 0,
          month: 0,
          current: 0
        },
        // businessOption: {
        //   tooltip: {
        //     trigger: 'item',
        //     formatter: '{a} <br/>{b}: {c} ({d}%)'
        //   },
        //   legend: {
        //     orient: 'vertical',
        //     top: 0,
        //     right: 25,
        //     formatter: '{name}',
        //     data: ['购卡', '续卡', '购私教', '续私教', '升卡', '转卡', '商品销售', '租柜', '销卡', '定金', '押金', '跨店购卡', '订场', '其它'],
        //     itemHeight: 12,
        //     itemWidth: 12
        //   },
        //   color: [
        //     '#1abbde',
        //     '#a76de8',
        //     '#ff6969',
        //     '#1bd4c9',
        //     '#b5db4f',
        //     '#ff7b44',
        //     '#80df89',
        //     '#f4ed28',
        //     '#ff85b3',
        //     'gray',
        //     'blue',
        //     'blueviolet'
        //   ],
        //   series: [
        //     {
        //       name: '业绩统计',
        //       type: 'pie',
        //       radius: ['50%', '70%'],
        //       center: ['37%', '50%'],
        //       avoidLabelOverlap: false,
        //       label: {
        //         normal: {
        //           show: false,
        //           position: 'center'
        //         },
        //         emphasis: {
        //           show: true,
        //           textStyle: {
        //             fontSize: '30',
        //             fontWeight: 'bold'
        //           }
        //         }
        //       },
        //       labelLine: {
        //         normal: {
        //           show: false
        //         }
        //       },
        //       data: [
        //         { value: 0, name: '购卡', code: 'buy_card' },
        //         // { value: 0, name: '私教', code: 'private_card' },
        //         { value: 0, name: '续卡', code: 'renew_card' },
        //         { value: 0, name: '购私教', code: 'pt_buy_card' },
        //         { value: 0, name: '续私教', code: 'pt_renew_card' },
        //         { value: 0, name: '升卡', code: 'change_card' },
        //         { value: 0, name: '转卡', code: 'deliver_card' },
        //         // { value: 0, name: '请假', code: 'leave_card' },
        //         { value: 0, name: '商品销售', code: 'goods_total' },
        //         { value: 0, name: '租柜', code: 'rent_cupboard' },
        //         { value: 0, name: '销卡', code: 'cancellation_card' },
        //         { value: 0, name: '定金', code: 'front_money' },
        //         { value: 0, name: '押金', code: 'cash_pledge' },
        //         { value: 0, name: '跨店购卡', code: 'cross_buy_card' },
        //         { value: 0, name: '订场', code: 'space_amount' },
        //         // { value: 0, name: '补卡', code: 'make_up_card' },
        //         // { value: 0, name: '付费活动', code: 'activity_sign' },
        //         { value: 0, name: '其它', code: 'other' }
        //       ]
        //     }
        //   ]
        // },
        coachBizOption: {
          title: {
            top: 10,
            left: 163,
            text: '进30天获取客户资源37人'
          },
          color: ['#3398DB'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              name: '来源分析',
              type: 'bar',
              barWidth: '60%',
              data: [10, 52, 200, 334, 390, 330, 220]
            }
          ]
        },
        tipData: {
          availability: 0,
          overdue: 0,
          not_buy_card: 0,
          normal: 0,
          private_card: 0,
          swim_card: 0
        },
        courseData: [],
        membershipData: [],
        bizStatVal: 'today',
        coachStatVal: 'whereiscust',
        saleTopSel: 'ms',
        saleList: [],
        // coachStatNull: true,
        marketNull: true,
        marketData: {},
        // 跳转企服核销页面
        pageToSupply: '',
        showSwimming: false,
        showBooking: false,
      };
    },
    async created() {
      legendData.length = 0;
      seriesData.length = 0;
      legendData.push(...['客流', '到店体验', '训练', '私教']);
      seriesData.push(...[
        {
          name: '客流',
          type: 'line',
          lineStyle: {
            normal: {
              color: '#ffbd55',
              opacity: 0
            }
          },
          areaStyle: {
            normal: {
              color: '#ffbd55',
              opacity: 0.7
            }
          },
          data: [0, 0, 0, 0, 0, 0, 0]
        },
        {
          name: '到店体验',
          type: 'line',
          lineStyle: {
            normal: {
              color: '#ff696a',
              opacity: 0
            }
          },
          areaStyle: {
            normal: {
              color: '#ff696a',
              opacity: 0.7
            }
          },
          data: [0, 0, 0, 0, 0, 0, 0]
        },
        {
          name: '训练',
          type: 'line',
          lineStyle: {
            normal: {
              color: '#5eeeaf',
              opacity: 0
            }
          },
          areaStyle: {
            normal: {
              color: '#5eeeaf',
              opacity: 0.7
            }
          },
          data: [0, 0, 0, 0, 0, 0, 0]
        },
        {
          name: '私教',
          type: 'line',
          lineStyle: {
            normal: {
              color: '#52a4ea',
              opacity: 0
            }
          },
          areaStyle: {
            normal: {
              color: '#52a4ea',
              opacity: 0.7
            }
          },
          data: [0, 0, 0, 0, 0, 0, 0]
        },
      ]);
      // 数据获取
      this.getSaleDB();
      this.getCustomerOriginDB();
      this.getCourseDB();
      this.getMarketDB();
      this.getSaleTop();
      this.getSupplyUrl();

      await this.getTipDB();
      this.getBusinessDB();
      await this.getNumberInBus();
      await this.getFlowDB();

      this.$nextTick(() => {
        this.flowChart.setOption(this.flowOption);
      });
    },
    mounted() {
      this.flowChart = echarts.init(document.querySelector('#flowStat'));
    },
    methods: {
      handleRouteTo() {
        if(!this.pageToSupply) {
          return false;
        }
        window.open(this.pageToSupply)
      },
      getSupplyUrl() {
        const url = 'Web/SupplyChainBus/get_supply_bus_token';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.pageToSupply = res.data.data
            } else {
              this.pageToSupply = ''
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });

      },
      getNumberInBus() {
        const url = 'Web/Business/get_in_bus_num';
        return this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (!data.is_display) {
                this.flowOption.title[0].text = this.flowOption.title[1].text = '';
              } else {
                this.flowOption.title[0].text = `场内实时: `;
                this.flowOption.title[1].text = `${data.in_bus_num}人`;
              }
              // this.$nextTick(() => {
              //   this.flowChart.setOption(this.flowOption);
              // });
            } else {
              this.$Message.error(res.data.errormsg);
            }

            return res;
          })
          .catch(err => {
            console.error(err);
          });
      },
      getFlowDB() {
        return this.$service.post('/Web/Index/get_flow_list').then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              const flowData = response.data.data;

              // 今日客流量显示
              this.todayFlowData = flowData.today;

              // 近30天客流量统计图
              let dateList = [];
              let inStoreList = [];
              let trainList = [];
              let coachList = [];
              let swimList = [];
              let sanLogList = [];
              let allList = [];
              for (const key in flowData) {
                if (key === 'today') continue;
                dateList.push(key);
                if (flowData.hasOwnProperty(key)) {
                  inStoreList.push(flowData[key].to_visit_list);
                  trainList.push(flowData[key].other_class);
                  coachList.push(flowData[key].private_class);
                  swimList.push(flowData[key].swim_class);
                  allList.push(flowData[key].all_class);
                  sanLogList.push(flowData[key].san_log_data)
                }
              }
              this.flowOption.xAxis[0].data = dateList;
              this.flowOption.series[0].data = allList;
              this.flowOption.series[1].data = inStoreList;
              this.flowOption.series[2].data = trainList;
              this.flowOption.series[3].data = coachList;

              if (this.showSwimming) {
                this.flowOption.series[4].data = swimList;
              }
              if (this.showBooking && this.showSwimming) {
                this.flowOption.series[5].data = sanLogList;
              } else if (this.showBooking && !this.showSwimming) {
                this.flowOption.series[4].data = sanLogList;
              }

              // this.flowChart.setOption(this.flowOption);
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }

          return response;
        });
      },
      getSaleDB() {
        this.$service.post('/Web/Index/new_card').then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              const saleData = response.data.data;

              // 勒两天的销售情况
              this.daysSaleData = saleData.total;

              // 会员购卡数据
              this.membershipData = saleData.list;
              // saleData.list.forEach(currentItem => {
              //   currentItem.amount = '￥' + currentItem.amount;
              //   this.membershipData.push(currentItem);
              // });
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      },
      getBusinessDB() {
        this.$service
          .post(
            '/Web/Index/achievement',
            {
              type: this.bizStatVal
            },
            { loading: false }
          )
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                const businessData = response.data.data;
                // 勒两天的业绩
                this.daysBusinessData = {
                  day: businessData.day,
                  month: businessData.month,
                  current: this.bizStatVal === 'today' ? businessData.day : businessData.month
                };

                this.miniTableDB = businessData.list

                // 不需要画图了
                // if (this.daysBusinessData.current === 0) return;

                // 业绩饼图
                // for (const key in businessData.list) {
                //   if (businessData.list.hasOwnProperty(key)) {
                //     this.businessOption.series[0].data.forEach(function(item) {
                //       if (item.code == key) {
                //         if (item.code !== 'cancellation_card') {
                //           item.value = businessData.list[key];
                //         }
                //         setTimeout(function() {
                //           document.querySelector('#' + key).innerHTML = businessData.list[key];
                //         }, 800);
                //       }
                //     });
                //   }
                // }

                // const self = this;
                // setTimeout(function() {
                //   const businessChart = echarts.init(document.querySelector('#businessStat'));
                //   businessChart.setOption(self.businessOption);
                // }, 800);
              } else {
                console.log(response.data.errormsg);
              }
            } else {
              console.log('服务器扑街！');
            }
          });
      },
      handleBizStatChange(val) {
        if (val === 'today') {
          this.daysBusinessData.current = this.daysBusinessData.day;
        } else if (val === 'month') {
          this.daysBusinessData.current = this.daysBusinessData.month;
        }
        this.getBusinessDB();
      },
      getCustomerOriginDB() {
        this.$service
          .post('/Web/Index/source', {
            type: this.coachStatVal
          })
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                const coachBizData = response.data.data;
                const total = coachBizData.total;

                // if (total == 0) {
                //   this.coachStatNull = true;
                // } else {
                //   this.coachStatNull = false;
                // }

                // if (this.coachStatNull) return;

                if (this.coachStatVal === 'whereiscust') {
                  this.coachBizOption.title.text = `近30天获取客户资源${total}人`;
                } else if (this.coachStatVal === 'membership') {
                  this.coachBizOption.title.text = `近30天会籍新成单${total}次`;
                } else if (this.coachStatVal === 'private') {
                  this.coachBizOption.title.text = `近30天私教新成单${total}次`;
                }

                let methodList = [];
                let valueList = [];
                coachBizData.data.forEach(function(item) {
                  methodList.push(item.name);
                  valueList.push(item.count);
                });
                this.coachBizOption.xAxis[0].data = methodList;
                this.coachBizOption.series[0].data = valueList;

                const self = this;
                setTimeout(function() {
                  const coachBizStat = echarts.init(document.querySelector('#coachBizStat'));
                  coachBizStat.setOption(self.coachBizOption);
                }, 800);
              } else {
                console.log(response.data.errormsg);
              }
            } else {
              console.log('服务器扑街！');
            }
          });
      },
      handleCoachStatChange(val) {
        this.getCustomerOriginDB();
      },
      getTipDB() {
        return this.$service.post('/Web/Index/user_statistics').then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              this.tipData = response.data.data;

              const showData = response.data.data.statistics_display_setting;
              this.showSwimming = Number(showData?.swim_data || 0) === 1;
              this.showBooking = Number(showData?.booking_data || 0) === 1;

              if (this.showSwimming) {
                legendData.push('泳教');
                seriesData.push({
                  name: '泳教',
                  type: 'line',
                  lineStyle: {
                    normal: {
                      color: '#ff81c2',
                      opacity: 0
                    }
                  },
                  areaStyle: {
                    normal: {
                      color: '#ff81c2',
                      opacity: 0.7
                    }
                  },
                  data: [0, 0, 0, 0, 0, 0, 0]
                });
              }
              if (this.showBooking) {
                legendData.push('票务');
                seriesData.push({
                  name: '票务',
                  type: 'line',
                  lineStyle: {
                    normal: {
                      color: '#94bfff',
                      opacity: 0
                    }
                  },
                  areaStyle: {
                    normal: {
                      color: '#94bfff',
                      opacity: 0.7
                    }
                  },
                  data: [0, 0, 0, 0, 0, 0, 0]
                });
              }
            } else if (response.data.errorcode === 40005) {
              this.$Message.error(response.data.errormsg);
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }

          return response;
        });
      },
      getCourseDB() {
        this.$service.post('/Web/Index/course_schedule').then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              // 找出时间最近的，加一个标识
              let list = response.data.data;
              const now = new Date().getTime();
              let disparityList = [];
              list.forEach((item, index) => {
                item.disparity = parseInt(item.b_time) * 1000 - now;
                if (item.disparity > 0) {
                  disparityList.push(item.disparity);
                }
              });
              let idx = 0;
              let idxFlag = true;
              if (disparityList.length > 0) {
                const min = Math.min.apply(null, disparityList);
                if (min)
                  list
                    .filter((item, index) => {
                      if (item.disparity === min) {
                        if (idxFlag) {
                          idx = index;
                          idxFlag = false;
                        }
                        return true;
                      } else {
                        return false;
                      }
                    })
                    .forEach(item => {
                      item.goodjob = true;
                    });
              }
              this.courseData = list;

              // 滑动条定位
              setTimeout(function() {
                const $timeline = document.querySelector('.item-timeline');
                if ($timeline) {
                  const sh = $timeline.scrollHeight;
                  $timeline.scrollTop = idx / list.length * sh;
                }
              }, 800);
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      },
      getMarketDB() {
        this.$service.post('/Web/Index/indexActivity').then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              let mkt = response.data.data;
              if (mkt.hasOwnProperty('secret_id') && !mkt.secret_id) {
                return false;
              }
              this.marketNull = false;
              mkt.beginDate = formatDate(new Date(mkt.beg_time * 1000), 'yyyy年MM月dd日');
              mkt.endDate = formatDate(new Date(mkt.end_time * 1000), 'yyyy年MM月dd日');
              mkt.cutoffDate = formatDate(new Date(mkt.cutoff_time * 1000), 'yyyy年MM月dd日 HH:mm');
              this.marketData = mkt;
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      },
      handleSubscription(class_id) {
        window.history.replaceState({}, '', `/class/open-reser?classId=${class_id}&from=index`);
      },
      handleMarketMore() {
        const url = '/activity/list';
        // window.location.href = url;
        this.$router.replace(url)
      },
      handleMarketDetail(id) {
        const url = `/activity/list/detail?id=${id}`;
        // window.location.href = url;
        this.$router.replace(url)
      },
      handleMembershipNull() {
        window.history.replaceState({}, '', `/admin/card-list`);
      },
      handleCourseNull() {
        window.history.replaceState({}, '', `/class/open-schedule`);
      },
      handleMarketNull() {
        const url = `/activity/list`;
        // window.location.href = url;
        this.$router.replace(url)
      },
      handleSaleTopChange() {
        this.getSaleTop();
      },
      getSaleTop() {
        return this.$service.post('/Web/Index/amount_ranking', {
          type: this.saleTopSel
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.saleList = res.data.data;
          } else {this.$Message.error(res.data.errormsg);}
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  @indexBorder: 1px solid #e9eaec;
  @spaceMargin: 24px;
  @mainWidth: 1076px;
  @subWidth: 528px;
  @tipWidth: 180px;

  @cardHeight: 40px;
  @mainHeadHeight: 130px;
  @mainBodyHeight: 338px;
  @mainFootHeight: 152px;
  @subBusinessHeight: 396px;
  @subMembershipHeight: 210px;

  @media screen and (min-width: 1900px) {
    .box {
      margin: 0 calc(~'(100% - 1628px)/2');
    }
    .box-main {
      width: @mainWidth + @spaceMargin;
    }
    .item-l {
      height: @subBusinessHeight;
      margin-left: (@mainWidth + @spaceMargin - @subWidth*2) / 2;
    }
    .item-body-l {
      height: @subBusinessHeight - @cardHeight;
    }
    .item-s-auto-height {
      height: @subBusinessHeight;
    }
  }

  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .box {
      margin: 0 calc(~'(100% - 1076px)/2');
    }
    .box-main {
      width: @mainWidth + @spaceMargin;
    }
    .item-s-auto-height {
      height: @subBusinessHeight + @subMembershipHeight + @spaceMargin;
      margin-bottom: @spaceMargin;
    }
    .item-s {
      margin-left: @spaceMargin;
    }
    .item-body-l {
      height: @subBusinessHeight + @subMembershipHeight + @spaceMargin - @cardHeight;
    }
  }

  @media screen and (max-width: 1300px) {
    .box {
      margin: 0 calc(~'(100% - 1060px)/2');
      width: 1060px;
    }
    .box-main {
      width: @mainWidth - @spaceMargin;
    }
    .item-s-auto-height {
      height: @subBusinessHeight + @subMembershipHeight + @spaceMargin;
      margin-bottom: @spaceMargin;
    }
    .item-s {
      margin-left: 2px;
    }
    .item-body-l {
      height: @subBusinessHeight + @subMembershipHeight + @spaceMargin - @cardHeight;
    }
  }

  .sale-top {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .salesperson:nth-child(n+4) {
      .seq {
        color: gray;
        font-weight: normal;
      }
    }

    .salesperson {
      height: 60px;
      width: 90%;
      display: flex;
      flex-direction: row;
      align-items: center;

      .seq {
        width: 60px;
        color: orange;
        font-weight: bold;
      }

      .avatar {

        img {
          height: 37px;
          width: 37px;
          border-radius: 50%;
          overflow: hidden;
        }
      }

      .name {
        width: 288px;
        padding: 0 20px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .card {
    background: #fff;
    border-radius: 4px;
    font-size: 14px;
    position: relative;
    transition: all 0.2s ease-in-out;
  }

  .card-head {
    height: @cardHeight;
    border-bottom: @indexBorder;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .head-title {
      text-indent: 2em;
    }
  }

  .stat-null {
    text-align: center;
    font-size: 20px;
    color: #d7d8d7;
    cursor: pointer;
  }

  .wall {
    display: flex;
    box-sizing: border-box;

    .box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      // margin-top: @spaceMargin;

      .box-main {
        .main-head {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          height: @mainHeadHeight;
          max-width: @mainWidth;

          .main-primary {
            color: #eaf5ff;
            font-size: 18px;
            margin-left: 36px;

            strong {
              color: #ffffff;
              font-size: 42px;
              font-weight: bold;
              margin: 6px;
            }
          }

          .main-secondary {
            color: #eaf5ff;
            font-size: 16px;
            margin-left: 36px;
            .min-span {
              white-space: nowrap;
            }
            strong, .min-strong {
              color: #ffffff;
              font-size: 28px;
              font-weight: bold;
              margin: 4px 6px;
            }
            .min-strong {
              font-size: 22px;
            }
          }

          .main-flow {
            position: relative;
            width: @subWidth;
            background-color: #52a4ea;

            span {
              margin-right: @spaceMargin;
            }

            .help-tips {
              position: absolute;
              top: 10px;
              right: 10px;
            }
          }

          .main-sale {
            width: @subWidth;
            background-color: #ff696a;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .sale-info {
              display: flex;
              flex-direction: row;

              div {
                width: @subWidth / 2;
              }
            }
          }
        }

        .main-body {
          height: @mainBodyHeight;
          max-width: @mainWidth;
          margin-top: @spaceMargin;

          .flow-stat {
            height: @mainBodyHeight;
            max-width: @mainWidth;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .main-foot {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          height: @mainFootHeight;
          max-width: @mainWidth;

          .main-tip {
            height: 114px;
            width: @tipWidth;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            p:nth-child(1) {
              font-size: 40px;
            }

            p:nth-child(2) {
              font-size: 18px;
            }
          }

          .tip-effective {
            color: #52a4ea;
          }

          .tip-potential {
            color: #ff9c28;
          }

          .tip-overdue {
            color: #ff6969;
          }

          .tip-normal {
            color: #909af4;
          }

          .tip-coach {
            color: #1bd4c9;
          }
        }
      }

      .box-sub {
        .sub-business {
          height: @subBusinessHeight;
          width: @subWidth;

          .business-head {
            // height: 80px;
            .card-head;

            .change-biz-stat {
              position: absolute;
              right: 25px;
              z-index: 666;
              margin-top: 10px;
            }

            p {
              color: #52a4ea;
              font-size: 16px;
              text-indent: 2em;
              height: @mainHeadHeight / 2;
              padding-top: 20px;

              strong {
                font-size: 20px;
              }
            }
          }

          .business-body {
            height: @subBusinessHeight - @mainHeadHeight;
            display: flex;
            justify-content: flex-end;

            .business-stat {
              height: @subBusinessHeight - @mainHeadHeight + 50;
              width: @subWidth;
              display: flex;
              justify-content: center;
              align-items: center;

              .zen-stat {
                height: 100%;
                width: 100%;
                display: flex;
                flex-direction: row;

                .biz-legend {
                  margin-top: 1px;

                  p {
                    font-size: 14px;
                    line-height: 12px;
                    padding: 5px 0;
                  }

                  p:before {
                    content: '￥';
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }

        .sub-membership {
          height: @subMembershipHeight;
          width: @subWidth;
          margin-top: @spaceMargin;

          .membership-head {
            .card-head;

            p {
              color: #47535e;
              font-size: 14px;
            }
          }

          .membership-body {
            height: @subMembershipHeight - @cardHeight;

            .membership-stat {
              height: @subMembershipHeight - @cardHeight;
              width: @subWidth;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 10px 0;

              .item-timeline {
                height: 100%;
                width: 100%; // padding-top: 20px;
                overflow-y: auto;
                color: #b4b5ba;
                margin-left: 20px;

                .time {
                  font-size: 14px;

                  span {
                    color: #808890;
                    margin: 0 6px;
                  }
                }

                .content {
                  font-size: 14px;
                  display: flex;
                  flex-direction: row;

                  p {
                    margin-right: @spaceMargin / 2;
                    display: flex;
                    align-items: center;

                    label {
                      margin-left: 2px;
                    }

                    a {
                      color: #b4b5ba;
                    }
                  }
                }

                .selected-timeline {
                  color: orange;

                  span {
                    color: orange;
                  }
                }
              }

              .time {
                width: 190px;
              }

              .person {
                width: 100px;
              }

              .home {
                width: 100px;
              }

              label {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }

      .box-item {
        // height: @subBusinessHeight;
        width: @subWidth;

        .item-head {
          .card-head;

          .change-coach-stat {
            margin-right: 25px;
          }
        }

        .item-body {
          // height: @subBusinessHeight - @cardHeight;
          display: flex;
          justify-content: center;
          align-items: center;

          .market {
            height: 100%;
            width: 500px;
            overflow: hidden;

            .market-img {
              height: 216px;
              width: 500px;
              margin-top: @spaceMargin;

              img {
                height: 100%;
                width: 100%;
              }
            }

            .market-title {
              color: #3598db;
              font-size: 14px;
              margin: 10px 0;
              cursor: pointer;
            }

            .market-subtitle {
              color: #b4b5ba;
              font-size: 12px;
            }

            .market-info {
              color: #808890;
              font-size: 12px;

              span {
                margin-right: @spaceMargin;

                strong {
                  color: #d9544f;
                }
              }
            }
            .market-more {
              color: #3598db;
              font-size: 12px;
              text-align: center;
              cursor: pointer;
            }
          }
        }

        .item-body-s {
          height: @subBusinessHeight - @cardHeight;
        }

        .item-ul {
          // display: flex;
          // justify-content: flex-end;
          padding: 10px 0;

          .membership-list {
            // height: @subMembershipHeight - @cardHeight;
            height: 100%;
            width: 100%;
            overflow-y: auto; // padding: 6px 0;
            li {
              display: flex;
              flex-direction: row;
              color: #b4b5ba;
              font-size: 14px;
              padding: 4px 0 4px 20px;

              p {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }

              .card-name {
                color: #808890;
                width: 160px;
              }

              .card-price {
                width: 100px;
              }

              .card-price:before {
                content: '￥';
                margin-right: 4px;
              }

              .card-person {
                width: 140px;
                display: flex;
                align-items: center;

                img {
                  margin-right: 4px;
                }
              }

              .card-date {
                width: 80px;
                display: flex;
                align-items: center;

                img {
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }

      .item-l {
        margin-bottom: @spaceMargin;
      }

      .box-foot {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: 40px;
        width: 100%;
        color: #313131;
        margin-bottom: 24px;

        img {
          height: 20px;
          width: 25px;
          margin-right: 10px;
        }

        .phone {
          font-size: 20px;
        }

        .line {
          border-left: 1px solid #959595;
          height: 22px;
          margin: 0 30px;
        }

        .company {
          font-size: 18px;
        }
      }
    }
  }
  .right-side {
    position: fixed;
    bottom: 100px;
    right: 0;
    transform: translateY(-50%);
    .ivu-icon-ios-call {
      margin: 10px auto;
    }
    .md-help {
      color: #52a4ea;
        margin: 4px 0px;
        font-size: 34px;
        border-radius: 50%;
        border: 2px solid #F9F9FA;
        box-sizing: border-box;
    }
  }
  .right-side .item {
    display: block;
    width: 70px;
    height: 70px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background:rgba(0, 0, 0, .25);
    cursor: pointer;
    &:nth-child(n-1){
      border-bottom: 1px solid #fff;
    }
    .item-con {
      color: #666;
      font-size: 14px;
    }
  }
</style>
