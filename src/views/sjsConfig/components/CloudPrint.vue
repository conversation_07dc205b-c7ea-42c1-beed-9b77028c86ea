<template>
  <div class="box">
    <Form ref="formValidate" :model="curData" :label-width="140">
      <Form-item label="设备型号">
        <Select v-model="curData.device_type" :disabled="!isDisabled">
          <Option value="0">商米云小票</Option>
          <Option value="1">芯烨云420B</Option>
        </Select>
      </Form-item>
      <Form-item label="设备SN">
        <Input v-model="curData.device_sn" :disabled="!isDisabled" />
      </Form-item>
      <Form-item label="打印小票" v-if="curData.device_type == 0">
        <Checkbox v-model="curData.pt_ticket" :disabled="!isDisabled" true-value="1" false-value="0">私教课</Checkbox>
        <Checkbox v-model="curData.member_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会籍卡
        </Checkbox>
        <Checkbox v-model="curData.buy_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会员购卡/课
        </Checkbox>
        <Checkbox v-model="curData.renew_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会员续卡/课
        </Checkbox>
        <Checkbox v-model="curData.change_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会员升卡/课
        </Checkbox>
        <Checkbox v-model="curData.exchange_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会员转卡/课
        </Checkbox>
        <Checkbox v-model="curData.makeup_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          会员补卡
        </Checkbox>
        <Checkbox v-model="curData.front_money_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          定押金收取
        </Checkbox>
        <Checkbox v-model="curData.rent_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          租柜消费
        </Checkbox>
        <Checkbox v-model="curData.buy_goods_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          商品消费
        </Checkbox>
        <Checkbox v-model="curData.booking_proof_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          入场凭证
        </Checkbox>
        <Checkbox v-model="curData.ticket_settlement_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
          票务结算
        </Checkbox>
      </Form-item>
      <template v-else>
        <Form-item label="纸张规格">
          <RadioGroup v-model="curData.paper_size">
            <Radio label="0" :disabled="!isDisabled">58mm</Radio>
            <Radio label="1" :disabled="!isDisabled">25mm(手环纸带)</Radio>
          </RadioGroup>
        </Form-item>
        <Form-item label="纸张长度" v-if="curData.paper_size == 1">
          <Input-number
            v-model="curData.paper_length"
            :disabled="!isDisabled"
            :step="1"
            :precision="0"
            :min="0"
            :max="1000"
            style="width: 60px"
          />
          <span style="margin-left: 10px">mm</span>
        </Form-item>
        <Form-item label="功能">
          <template v-if="curData.paper_size == 0">
            <Checkbox v-model="curData.pt_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              私教课
            </Checkbox>
            <Checkbox v-model="curData.member_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会籍卡
            </Checkbox>
            <Checkbox v-model="curData.buy_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会员购卡/课
            </Checkbox>
            <Checkbox v-model="curData.renew_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会员续卡/课
            </Checkbox>
            <Checkbox v-model="curData.change_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会员升卡/课
            </Checkbox>
            <Checkbox v-model="curData.exchange_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会员转卡/课
            </Checkbox>
            <Checkbox v-model="curData.makeup_card_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              会员补卡
            </Checkbox>
            <Checkbox v-model="curData.front_money_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              定押金收取
            </Checkbox>
            <Checkbox v-model="curData.rent_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              租柜消费
            </Checkbox>
            <Checkbox v-model="curData.buy_goods_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              商品消费
            </Checkbox>
            <Checkbox v-model="curData.booking_proof_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              入场凭证
            </Checkbox>
            <Checkbox v-model="curData.ticket_settlement_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              票务结算
            </Checkbox>
          </template>
          <template v-else>
            <Checkbox v-model="curData.booking_proof_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              入场凭证
            </Checkbox>
            <Checkbox v-model="curData.scan_ticket" :disabled="!isDisabled" true-value="1" false-value="0">
              扫码取票
            </Checkbox>
          </template>
        </Form-item>
        <Form-item label="取票范围" v-if="curData.paper_size == 1 && curData.scan_ticket == 1">
          <RadioGroup v-model="curData.scan_ticket_range">
            <Radio label="0" :disabled="!isDisabled">全部门店票</Radio>
            <Radio label="1" :disabled="!isDisabled">仅本门店票</Radio>
          </RadioGroup>
        </Form-item>
        <Form-item>
          <Button class="centerbut" shape="circle" @click="handleShowQr">
            <i class="qrcodeIcon"></i>
            <span>取票二维码</span>
          </Button>
        </Form-item>
      </template>
      <Form-item>
        <div v-if="isEdit">
          <Button shape="circle" icon="android-arrow-back" @click="handleCancle">取消</Button>
          <Button shape="circle" type="success" icon="android-checkmark-circle" @click="handleSave">保存</Button>
        </div>
        <div v-else>
          <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
          <Button shape="circle" type="error" icon="minus-round" @click="handleDelete">删除</Button>
        </div>
      </Form-item>
    </Form>

    <Modal v-model="showQr" :width="800" :mask-closable="true" title="取票二维码" @on-cancel="showQr = false">
      <div class="qrModalbody">
        <div class="qrModalContent">
          <!-- 1790/795 -->
          <img class="qrcode-tips" src="https://imagecdn.rocketbird.cn/mainsite-fe/print-flow.jpg" />
          <!-- <div class="image" style="width: 240px; height: 273px; background-color: #cacaca; opacity: 0.4;"></div> -->
          <p>流程图</p>
          <a download="打印流程图" href="https://imagecdn.rocketbird.cn/mainsite-fe/print-flow.jpg">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
        <div class="line"></div>
        <div class="qrModalContent">
          <img class="qrcode" :src="otherImgQr" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="otherImgQr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: [Object],
      validator: (val) => {
        if (!val.paper_length) {
          val.paper_length = 250
        }
        val.paper_length = parseInt(val.paper_length)
        return true
      },
      default: () => {
        return {
          device_type: '0',
          device_sn: '',
          pt_ticket: '0',
          member_ticket: '0',
          buy_goods_ticket: '0',
          rent_ticket: '0',
          front_money_ticket: '0',
          makeup_card_ticket: '0',
          exchange_card_ticket: '0',
          change_card_ticket: '0',
          renew_card_ticket: '0',
          buy_card_ticket: '0',
          booking_proof_ticket: '0',
          ticket_settlement_ticket: '0',
          scan_ticket: '0',
          scan_ticket_range: '0',
          paper_size: '0',
          paper_length: 250,
        }
      },
    },
    busId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isEdit: false,
      isDisabled: false,
      showQr: false,
      imgQr: '',
      otherImgQr: '',
    }
  },
  computed: {
    curData: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    handleShowQr() {
      this.$service
        .post('/web/DeviceSet/getCloudPrintUrl', {
          bus_id: this.busId,
          device_sn: this.curData.device_sn,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.otherImgQr = res.data.data
            this.showQr = true
          }
        })
    },
    handleCancle() {
      this.isEdit = false
      this.isDisabled = false
    },
    getPostData() {
      let paper_length = this.curData.paper_length
      if (!paper_length) {
        paper_length = 250
      }

      let params = {}
      if (this.curData.device_type == '0') {
        params = {
          device_type: this.curData.device_type,
          device_sn: this.curData.device_sn,
          pt_ticket: this.curData.pt_ticket,
          member_ticket: this.curData.member_ticket,
          buy_goods_ticket: this.curData.buy_goods_ticket,
          rent_ticket: this.curData.rent_ticket,
          front_money_ticket: this.curData.front_money_ticket,
          makeup_card_ticket: this.curData.makeup_card_ticket,
          exchange_card_ticket: this.curData.exchange_card_ticket,
          change_card_ticket: this.curData.change_card_ticket,
          renew_card_ticket: this.curData.renew_card_ticket,
          buy_card_ticket: this.curData.buy_card_ticket,
          booking_proof_ticket: this.curData.booking_proof_ticket,
          ticket_settlement_ticket: this.curData.ticket_settlement_ticket,
        }
      } else if (this.curData.device_type == '1' && this.curData.paper_size == '0') {
        params = {
          device_type: this.curData.device_type,
          device_sn: this.curData.device_sn,
          pt_ticket: this.curData.pt_ticket,
          member_ticket: this.curData.member_ticket,
          buy_goods_ticket: this.curData.buy_goods_ticket,
          rent_ticket: this.curData.rent_ticket,
          front_money_ticket: this.curData.front_money_ticket,
          makeup_card_ticket: this.curData.makeup_card_ticket,
          exchange_card_ticket: this.curData.exchange_card_ticket,
          change_card_ticket: this.curData.change_card_ticket,
          renew_card_ticket: this.curData.renew_card_ticket,
          buy_card_ticket: this.curData.buy_card_ticket,
          booking_proof_ticket: this.curData.booking_proof_ticket,
          ticket_settlement_ticket: this.curData.ticket_settlement_ticket,
          // scan_ticket: this.curData.scan_ticket,
          // scan_ticket_range: this.curData.scan_ticket_range,
          paper_size: this.curData.paper_size,
          paper_length,
        }
      } else if (this.curData.device_type == '1' && this.curData.paper_size == '1' && this.curData.scan_ticket == '1') {
        params = {
          device_type: this.curData.device_type,
          device_sn: this.curData.device_sn,
          booking_proof_ticket: this.curData.booking_proof_ticket,
          scan_ticket: this.curData.scan_ticket,
          scan_ticket_range: this.curData.scan_ticket_range,
          paper_size: this.curData.paper_size,
          paper_length,
        }
      } else {
        params = {
          device_type: this.curData.device_type,
          device_sn: this.curData.device_sn,
          booking_proof_ticket: this.curData.booking_proof_ticket,
          scan_ticket: this.curData.scan_ticket,
          paper_size: this.curData.paper_size,
          paper_length,
        }
      }
      return params
    },
    handleSave() {
      const params = this.getPostData()

      if (!this.curData.device_sn) {
        this.$Message.error('请填写设备SN！')
        return false
      }
      const url = this.curData.action == 'save' ? 'web/DeviceSet/addCloudPrint' : 'web/DeviceSet/editCloudPrint'
      this.$service.post(url, params).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.isEdit = false
          this.isDisabled = false
          this.$emit('on-change')
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleEdit() {
      this.isEdit = true
      this.isDisabled = true
    },
    handleDelete() {
      this.$service
        .post('/web/DeviceSet/unbindCloudPrint', {
          device_sn: this.curData.device_sn,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.$emit('on-change')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
  created() {
    this.isEdit = this.curData.action == 'save'
    this.isDisabled = this.curData.action == 'save'
  },
}
</script>

<style lang="less" scoped>
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.qrcodeIcon {
  display: inline-block;
  width: 17px;
  height: 17px;
  margin-right: 5px;
  background: url('~assets/img/qrcode.png') no-repeat;
  background-size: contain;
}

.qrModalbody {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .qrModalContent {
    // width: 240px;
    // height: 353px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    p {
      margin: 10px;
    }
  }
  .line {
    width: 1px;
    height: 630px;
    background: rgba(#333, 0.3);
  }
}

.qrcode {
  width: 250px;
  object-fit: contain;
}

.qrcode-tips {
  width: 400px;
  object-fit: contain;
}
</style>
