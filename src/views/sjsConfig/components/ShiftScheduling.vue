<template>
  <div class="box">
    <Form ref="formValidate" :model="info" :label-width="140">
      <Form-item label="设备选择">
        <Select v-model="info.device_id" :disabled="!isDisabled">
          <Option v-for="item in deviceList" :value="item.device_id" :key="item.device_id">{{ item.device_id }}</Option>
        </Select>
      </Form-item>
      <Form-item label="教室选择">
        <Select v-model="info.classroom_id" :disabled="!isDisabled">
          <Option v-for="item in roomList" :value="item.id" :key="item.id">{{ item.classroom_name }}</Option>
        </Select>
      </Form-item>
      <Form-item>
        <div v-if="isEdit">
          <Button shape="circle" icon="android-arrow-back" @click="handleCancel">取消</Button>
          <Button shape="circle" type="success" icon="android-checkmark-circle" style="margin-left: 20px" @click="handleSave">
            保存
          </Button>
        </div>
        <div v-else>
          <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
          <Button shape="circle" type="error" icon="minus-round" style="margin-left: 20px" @click="handleDelete">删除</Button>
        </div>
      </Form-item>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    info: Object,
    busId: String,
    notSelectedRoom: Array,
    notSelectedDevice: Array,
  },
  data() {
    return {
      isEdit: false,
      isDisabled: false,
      removeLineWay: [],
    }
  },
  computed: {
    roomList() {
      let arr = this.notSelectedRoom ? Array.from(this.notSelectedRoom) : []
      if (arr.filter((item) => item.id == this.info.classroom_id).length === 0) {
        arr.push({
          id: this.info.classroom_id,
          classroom_name: this.info.class_name,
        })
      }
      return arr
    },
    deviceList() {
      let arr = this.notSelectedDevice ? Array.from(this.notSelectedDevice) : []
      if (arr.filter((item) => item.device_id == this.info.device_id).length === 0) {
        arr.push({
          device_id: this.info.device_id,
          device_name: this.info.device_name,
        })
      }
      return arr
    },
  },
  methods: {
    // 取消
    handleCancel() {
      this.isEdit = false
      this.isDisabled = false
    },
    // 保存
    handleSave() {
      let url = '/Web/ClassPlate/addClassPlateSet'
      if (this.info.id) {
        url = '/Web/ClassPlate/editClassPlateSet'
      }
      if(!this.info.device_id){
          this.$Message.error('请选择设备')
          return false;
      }
      if(!this.info.classroom_id){
          this.$Message.error('请选择教室')
          return false;
      }
      const data = {
        device_id: this.info.device_id,
        classroom_id: this.info.classroom_id,
      }
      this.$service.post(url, data).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.isEdit = false
          this.isDisabled = false
          this.$emit('on-change')
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleEdit() {
      this.isEdit = true
      this.isDisabled = true
    },
    handleDelete() {
      if (!this.info.id) {
        this.$emit('on-change')
        return false
      }
      this.$Modal.confirm({
        title: '提示',
        content: `<p>确认删除 ${this.info.classroom_name} 吗？</p>`,
        onOk: () => {
          this.$service.post('/Web/ClassPlate/delClassPlateSet', { device_id: this.info.device_id }).then((res) => {
            if (res.data.errorcode === 0) {
              this.isEdit = false
              this.isDisabled = false
              this.$Message.success(res.data.errormsg)
              this.$emit('on-change')
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        },
      })
    },
  },
  created() {
    this.isEdit = this.info.action === 'add'
    this.isDisabled = this.info.action === 'add'
  },
}
</script>

<style lang="less" scoped>
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.light-box {
  border: 2px solid #eeeeee;

  .line-way {
    display: flex;
    flex-direction: row;
  }
}

.status-box {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
}

@media screen and (min-width: 1900px) {
  .line-way-box {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

@media screen and (max-width: 1900px) {
  .line-way-box {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }
}
</style>
