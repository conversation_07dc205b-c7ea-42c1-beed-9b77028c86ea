<template>
  <div class="box">
    <Form ref="formValidate" :model="curData" :label-width="140">
    <Form-item label="设备编号" v-if="info.action !== 'save'">
      <Input v-model="curData.device_id" disabled/>
    </Form-item>
    <Form-item label="设备选择" v-else>
      <Select v-model="curData.device_id" :disabled="isDisabled" filterable>
          <Option v-for="item in deviceList" :value="item" :key="item">{{ item }}</Option>
        </Select>
    </Form-item>
   
    <Form-item label="教室选择">
       <Select v-model="curData.classroom_id" :disabled="isDisabled" filterable>
          <Option v-for="item in roomList" :value="item.id" :key="item.id">{{ item.classroom_name }}</Option>
        </Select>
    </Form-item>
    <Form-item label="会员限制">
      <RadioGroup v-model="curData.member_limit">
        <Radio label="0" :disabled="isDisabled">所有会员可进</Radio>
        <Radio label="1" :disabled="isDisabled">仅预约团操课的会员可进</Radio>
        <Radio label="2" :disabled="isDisabled">仅预约私教课的会员可进</Radio>
        <Radio label="3" :disabled="isDisabled">指定会员卡可进</Radio>
      </RadioGroup>
    </Form-item>
     <Form-item v-if="curData.member_limit==3" label="可进门的卡种">
      <Select v-model="curData.allow_card" placeholder="请选择" multiple :disabled="isDisabled" clearable filterable>
        <Option v-for="item in cardList" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
      </Select>
    </Form-item>
    <Form-item label="进门时间限制" v-if="curData.member_limit==1||curData.member_limit==2">
      <RadioGroup v-model="curData.time_limit">
        <Radio label="0" :disabled="isDisabled">全天可进</Radio>
        <Radio label="1" :disabled="isDisabled">限定时间内进场</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item v-if="curData.time_limit==1 && (curData.member_limit==1||curData.member_limit==2)">
      课程开始前
      <InputNumber style="width:100px" :max="60" :min="1" v-model="curData.start_begin" size="large" :disabled="isDisabled"></InputNumber>
      分钟 到 课程结束后
      <InputNumber style="width:100px" :max="60" :min="1" v-model="curData.end_after" size="large" :disabled="isDisabled"></InputNumber>
      分钟内可进场
    </Form-item>
    <Form-item>
      <div v-if="!isEdit">
        <Button shape="circle" icon="android-arrow-back" @click="handleCancle">取消</Button>
        <Button shape="circle" type="success" icon="android-checkmark-circle" @click="handleSave">保存</Button>
      </div>
      <div v-else>
        <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
        <Button shape="circle" type="error" icon="minus-round" @click="handleDelete">删除</Button>
      </div>
    </Form-item>
    </Form>
  </div>
</template>
<script>
  
export default {
  props: {
    info: Object,
    busId: String,
    cardList: Array,
    notSelectedRoom: Array
  },
  data() {
    return {
      deviceList: [],
      curData: {
        action: 'save',
        id: '',
        device_id: '',
        member_limit: '0',
        time_limit: '',
        allow_card: [],
        start_begin: 30,
        end_after: 10,
        classroom_id: ''
      },
      isEdit: false,
      isDisabled: false
    };
  },
  watch: {
    info: {
      handler(val) {
        this.curData = JSON.parse(JSON.stringify(this.info));
      },
      immediate: true
    }
  },
  computed: {
    roomList() {
      let arr = this.notSelectedRoom ? Array.from(this.notSelectedRoom) : [];
      if (arr.filter(item => item.id == this.curData.classroom_id).length === 0) {
        arr.push({
          id: this.curData.classroom_id,
          classroom_name: this.curData.class_name
        });
      }
      return arr;
    }
  },
  methods: {
    getDeviceList() {
      this.$service
          .get(`/Web/Business/getSelectGroupClassMachine?bus_id=${this.busId}`)
          .then(res => {
            if (res.data.errorcode == 0) {
              this.deviceList = res.data.data;
            }
          })
    },
    handleCancle() {
      this.isEdit = true;
      this.isDisabled = true;
      this.$emit("on-change");
    },
    handleSave() {
      const url = this.curData.action == 'save' ? 'Web/Business/addGroupClassMachine' : 'Web/Business/updateGroupClassMachine'
      this.$service
        .post(url, {
          ...this.curData,
          bus_id: this.busId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.isEdit = true;
            this.isDisabled = true;
            this.$emit("on-change");
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    handleEdit() {
      this.isEdit = false;
      this.isDisabled = false;
    },
    handleDelete() {
      this.$service
        .post("/Web/Business/delGroupClassMachine", {
          id: this.curData.id,
          bus_id: this.busId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.$emit("on-change");
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    }
  },
  created() {
    this.isEdit = this.info.action !== 'save';
    this.isDisabled = this.info.action !== 'save';
    this.getDeviceList()
  }
};
</script>

<style lang="less" scoped>
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>
