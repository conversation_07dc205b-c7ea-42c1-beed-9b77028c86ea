<template>
  <div class="table-wrap">
    <header>
      <DatePicker
        @on-change="handleDateChange"
        :value="duringDate"
        type="daterange"
        placeholder="选择日期"
        class="option-select"
        style="width: 220px"
        transfer
        :options="dateOptions"
        :clearable="false"
      ></DatePicker>
      <Input v-model="postData.realname" class="w120" placeholder="姓名" />
      <Input v-model="postData.phone" class="w120" placeholder="手机号" />
      <Select v-model="postData.type" class="w120" placeholder="小票类型" clearable>
        <Option v-for="(val, key) in typeList" :value="key" :key="key">{{ val }}</Option>
      </Select>
      <Select v-model="postData.status" class="w120" placeholder="状态" clearable>
        <Option :value="1">推送成功</Option>
        <Option :value="2">推送失败</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <div>注：只能查询云打印机半年内的记录</div>
      <Page
        @on-change="handlePage"
        :total="total"
        :current="currentPage"
        @on-page-size-change="pageSizeChanged"
        show-total
        show-sizer
        transfer
      ></Page>
    </footer>
    <Modal v-model="isShowModal">
      <div class="modal-con">
        <div class="watermark">小票预览只用于查询</div>
        <pre class="pre-con">{{ previewInfo.text }}</pre>
        <div class="qrcode-box" v-if="previewInfo.qr_url">
          <div class="number">{{  previewInfo.qr_data }}</div>
          <img class="qrcode" :src="previewInfo.qr_url" alt="二维码" />
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal v-model="printerSelectorShowing" title="重新打印">
      <div class="printer-selector">
        <Select v-model="printerSN" placeholder="选择打印机" filterable>
          <Option v-for="item in printerList" :value="item.device_sn" :key="item.id">{{ item.device_sn }}</Option>
        </Select>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="rePrint" :disabled="!printerSN">确定</Button>
        <Button @click="printerSelectorShowing = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      postData: {
        start_time: '',
        end_time: '',
        realname: '',
        phone: '',
        type: '',
        status: '',
        page_no: 1,
        page_size: 10,
      },
      isShowModal: false,
      previewInfo: {},
      currentPage: 1,
      total: 0,
      duringDate: [],
      dateOptions: {
        disabledDate (date) {
          return date && date.valueOf() >= Date.now()
        }
      },
      device_num: '',
      typeList: {
        1: '私教课',
        2: '会籍卡',
        3: '会员续卡/课',
        4: '会员转卡/课',
        5: '会员补卡',
        6: '定押金收取',
        7: '柜租消费',
        8: '商品消费',
        9: '我的凭证',
        10: '票务结算',
        11: '会籍签到',
        12: '会员升卡',
        13: '扫码取票'
      },
      space_position_id: '',
      columns: [
        { title: '首次打印时间', key: 'create_time' },
        { title: '会员', key: 'realname' },
        { title: '手机号', key: 'phone' },
        { 
          title: '小票类型', 
          key: 'device_num',
          render: (h, { row }) => {
            return <span>{ this.typeList[+row.type] }</span>;
          }
        },
        { 
          title: '小票内容', 
          key: 'text', 
          render: (h, { row }) => {
            return (
              <i-button
                type="text"
                onClick={() => {
                  if(!row.text) return
                  this.previewInfo = row
                  this.isShowModal = true
                }}
              >
                { row.text ? row.text.slice(0, 9) + '...' : '' }
              </i-button>
            )
          }},
        {
          title: '状态', 
          key: 'status',
          render: (h, { row }) => {
            return row.status === '1'  ? (
              <div style="color: #19be6b">推送成功</div>
              ) : (
              <div style="color: #d9534f">推送失败</div>
            ) 
          }
        },
        { title: '打印次数', key: 'count' },
        {
          title: '操作',
          render: (h, { row }) => {
            return (
              <div>
                <i-button
                  type="text"
                  onClick={() => {
                    this.logId = row.receipt_log_id
                    if (row.source == 0) {
                      this.rePrint()
                    } else {
                      this.printerSelectorShow()
                    }
                  }}
                >
                  重新打印
                </i-button>
              </div>
            );
          }
        }
      ],
      list: [],
      // printer selector
      printerSelectorShowing: false,
      printerList: [],
      printerSN: '',
      logId: ''
    }
  },
  computed: {
    ...mapGetters(['busId']),
  },
  methods: {
    printerSelectorShow() {
      this.printerSN = ''
      this.printerSelectorShowing = true
    },
    getPrinterList() {
      this.$service.post('/Web/ReceiptLog/getCloudPrintList').then((res) => {
        if (res.data.errorcode == 0) {
          this.printerList = res.data.data
        }
      })
    },
    rePrint() {
      // if (!this.printerSN) {
      //   this.$Message.error('请选择打印机')
      //   return
      // }
      this.$service.post('/Web/ReceiptLog/reprint', {
        device_sn: this.printerSN,
        receipt_log_id: this.logId,
        loading: true 
      }).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
          this.printerSN = ''
          this.printerSelectorShowing = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
      // this.$Modal.confirm({
      //   title: '提示',
      //   content: '确定重新打印吗？',
      //   onOk: () => {
      //     this.$service
      //       .post('/Web/ReceiptLog/reprint', { receipt_log_id, loading: true })
      //       .then((res) => {
      //         if (res.data.errorcode == 0) {
      //           this.$Message.success(res.data.errormsg)
      //           this.getList()
      //         } else {
      //           this.$Message.error(res.data.errormsg)
      //         }
      //       })
      //   },
      // });
    },
    handleDateChange(val) {
      this.duringDate = val
      this.postData.start_time = this.duringDate[0]
      this.postData.end_time = this.duringDate[1]
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    handlePage(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList(is_export = 0) {
      return this.$service
        .post('/Web/ReceiptLog/listData', this.postData)
        .then((res) => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            this.list = res.data.data.list
            this.total = parseInt(res.data.data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
  },
  created() {
    const today = new Date()
    const whatDay = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
    this.duringDate = [whatDay, whatDay]

    this.getList()
    this.getPrinterList()
  },
}
</script>
<style lang="less" scoped>
.modal-con {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 400px;
  .watermark {
    position: absolute;
    left: 50px;
    top: 200px;
    transform: rotate(45deg);
    font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
    font-weight: 650;
    font-style: normal;
    font-size: 40px;
    color: rgba(2, 125, 180, 0.3);
  }
}
.pre-con {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  width: 255px;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.qrcode-box {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  width: 255px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .number {
    font-size: 30px;
    font-weight: bold;
    color: #333333;
    width: 100%;
    text-align: center;
  }

  .qrcode {
    width: 200px;
    height: 200px;
  }
}
</style>

