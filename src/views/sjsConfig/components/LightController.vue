<template>
  <div class="box">
    <Form ref="formValidate" :model="info" :label-width="140">
      <Form-item label="规则">
        <Checkbox v-model="info.auto_mode" :disabled="!isDisabled" true-value="1" false-value="0">自动开关灯</Checkbox>
        <span style="margin-left: 10px">（自动开关灯：订场预约时间到即开灯，预订时间结束后关灯）</span>
      </Form-item>
      <div class="light-box">
        <div style="height: 40px"></div>
        <Form-item label="设备">
          <Select
            v-model="info.device_name"
            @on-change="handleDeviceChange"
            style="width: 200px; margin-right: 20px"
            :disabled="!isDisabled"
          >
            <Option v-for="item in deviceList" :value="item.device_name" :key="item.device_name">{{ item.device_name }}</Option>
          </Select>
          <Input
            v-model="info.device_num"
            placeholder="请输入..."
            :disabled="!isDisabled"
            style="width: 300px; margin-right: 20px"
          />
          <span>状态: </span>
          <div class="status-box" v-if="info.lightStatus === 'online'" style="color: #19be6b">
            在线
            <Icon type="md-checkmark" size="20" />
            <Icon @click="handleCheckLightStatus" style="cursor: pointer" type="md-refresh" size="20" />
          </div>
          <div class="status-box" v-else-if="info.lightStatus === 'check'" style="color: #2db7f5">进行检测</div>
          <div class="status-box" v-else-if="info.lightStatus === 'offline'" style="color: #ed4014">
            离线
            <Icon type="md-close" size="20" />
            <Icon @click="handleCheckLightStatus" style="cursor: pointer" type="md-refresh" size="20" />
          </div>
        </Form-item>

        <template v-for="(item, index) in info.ruleLists">
          <Form-item label="线路" :key="index">
            <div class="line-way-box">
              <div class="line-way">
                <Select
                  v-model="item.lineWay"
                  @on-change="handleLineWayChange(index, item.lineWay)"
                  style="width: 60px"
                  :disabled="!isDisabled || info.device_name !== '其他品牌'"
                >
                  <Option v-for="line in lineNumber" :disabled="info.lineWayList.includes(line)" :value="line" :key="line">
                    {{ line }}
                  </Option>
                </Select>
                <Select
                  v-model="item.roomId"
                  @on-change="handleStadiumChange(index, $event)"
                  style="width: 200px; margin: 0 10px"
                   multiple
                  :disabled="!isDisabled"
                  filterable
                  clearable
                >
                  <Option v-for="room in stadiumList" :value="room.id" :key="room.id">{{ room.name }}</Option>
                </Select>
              </div>
              <div class="line-way">
                <label style="width: 60px">开灯指令</label>
                <Input
                  v-model="item.on_cmd"
                  placeholder="请输入..."
                  :disabled="!isDisabled || info.device_name !== '其他品牌'"
                  style="width: 200px; margin: 0 10px"
                />
              </div>
              <div class="line-way">
                <label style="width: 60px">关灯指令</label>
                <Input
                  v-model="item.off_cmd"
                  placeholder="请输入..."
                  :disabled="!isDisabled || info.device_name !== '其他品牌'"
                  style="width: 200px; margin: 0 10px"
                />
              </div>
              <div class="line-way">
                <label style="width: 60px">状态指令</label>
                <Input
                  v-model="item.status_cmd"
                  placeholder="请输入..."
                  :disabled="!isDisabled || info.device_name !== '其他品牌'"
                  style="width: 200px; margin: 0 10px"
                />
              </div>
              <div class="line-way">
                <Button
                  v-if="info.device_name === '其他品牌'"
                  type="error"
                  size="small"
                  shape="circle"
                  icon="md-remove"
                  :disabled="!isDisabled"
                  @click="handleRemoveLineWay(index)"
                ></Button>
              </div>
            </div>
          </Form-item>
        </template>
        <Form-item v-if="info.device_name === '其他品牌'">
          <Button @click="handleAddLineWay" :disabled="!isDisabled">添加线路</Button>
        </Form-item>

        <Form-item>
          <div v-if="isEdit">
            <Button shape="circle" icon="android-arrow-back" @click="handleCancel">取消</Button>
            <Button shape="circle" type="success" icon="android-checkmark-circle" style="margin-left: 20px" @click="handleSave">
              保存
            </Button>
          </div>
          <div v-else>
            <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
            <Button shape="circle" type="error" icon="minus-round" style="margin-left: 20px" @click="handleDelete">删除</Button>
          </div>
        </Form-item>
      </div>
      <div style="height: 20px"></div>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    info: Object,
    busId: String,
  },
  data() {
    const lineNumber = []
    for (let index = 1; index < 100; index++) {
      lineNumber.push(index)
    }
    return {
      isEdit: false,
      isDisabled: false,
      deviceList: [],
      stadiumList: [],
      lineNumber,
      removeLineWay: [],
    }
  },
  methods: {
    getDeviceList() {
      this.$service.post('/web/light_control/getTemplateRuleLists').then((res) => {
        if (res.data.errorcode === 0) {
          this.deviceList = res.data.data
          this.deviceList.push({
            device_name: '其他品牌',
            device_num: '',
            all_on_cmd: '',
            all_off_cmd: '',
            auto_mode: '1',
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getStadiumList() {
      this.$service
        .post('/web/light_control/getRpcTreeListsSpaceByBusId', {
          bus_id: this.busId,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.stadiumList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleCheckLightStatus() {
      if (!this.info.id) {
        return false
      }
      this.info.lightStatus = 'check'
      this.$forceUpdate()
      this.$service
        .post('/web/light_control/getDeviceStatus', {
          id: this.info.id,
          bus_id: this.busId,
          device_num: this.info.device_num
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            if (res.data.data) {
              this.info.lightStatus = 'online'
            } else {
              this.info.lightStatus = 'offline'
            }
            this.$forceUpdate()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleDeviceChange(val) {
      const device = this.deviceList.find((item) => {
        return item.device_name === val
      })
      this.info.auto_mode = device.auto_mode
      this.info.all_on_cmd = device.all_on_cmd
      this.info.all_off_cmd = device.all_off_cmd
      this.info.bus_id = device.bus_id
      this.info.device_name = device.device_name
      this.info.device_num = device.device_num
      this.info.lineWayList = []
      const rules = []
      if (Array.isArray(device.device_line_json)) {
        device.device_line_json.forEach((item) => {
          const lineWay = Number(item.index)
          this.info.lineWayList.push(lineWay)
          rules.push({
            ...item,
            lineWay,
          })
        })
      }
      this.removeLineWay = []
      this.info.ruleLists.forEach((item) => {
        if (item.id) {
          this.removeLineWay.push(item)
        }
      })
      this.info.ruleLists = rules
    },
    handleLineWayChange(index, lineWay) {
      this.info.lineWayList[index] = lineWay
      this.$forceUpdate()
    },
    handleStadiumChange(index, roomId) {
      if (roomId) {
        const arr = []
        roomId.forEach((item) => {
          arr.push({
            space_id: item.split('_')[0],
            position: item.split('_')[1],
          })
        })
        this.info.ruleLists[index].space_data = arr
      } else {
        this.info.ruleLists[index].space_data= [{
          space_id: '',
          position: '',
        }]
      }
    },
    getMaxLine() {
      if (this.info.lineWayList.length === 0) {
        return 0
      } else {
        const arr = [...this.info.lineWayList]
        arr.sort()
        return arr.pop()
      }
    },
    handleAddLineWay() {
      const count = this.getMaxLine() + 1
      this.info.lineWayList.push(count)
      this.info.ruleLists.push({
        id: null,
        on_cmd: '',
        off_cmd: '',
        status_cmd: '',
        space_data: [{
          space_id: '',
          position: '',
        }],
        lineWay: count,
      })
    },
    handleRemoveLineWay(index) {
      this.info.lineWayList.splice(index, 1)
      const item = this.info.ruleLists.splice(index, 1)
      if (item[0].id) {
        this.removeLineWay.push(item[0])
      }
    },
    handleCancel() {
      this.isEdit = false
      this.isDisabled = false
    },
    packageLineWayPost(item, is_delete = 0) {
      return {
        id: item.id,
        is_delete,
        index: item.lineWay,
        on_cmd: item.on_cmd,
        off_cmd: item.off_cmd,
        status_cmd: item.status_cmd,
        space_data: item.space_data,
        bus_id: this.busId,
      }
    },
    handleSave() {
      if (this.info.ruleLists.length === 0) {
        this.$Message.error('请添加线路！')
        return false
      }

      // let flag = true
      // let flagMsg = ''
      const url = '/web/light_control/setRule'
      const rules = []
      this.info.ruleLists.forEach((item) => {
        // if (!item.space_id) {
        //   flag = false
        //   flagMsg = `线路${item.lineWay}未配置场地!`
        //   return false
        // } else if (!item.on_cmd || !item.off_cmd || !item.status_cmd) {
        //   flag = false
        //   flagMsg = `线路${item.lineWay}未配置指令!`
        //   return false
        // }
        rules.push(this.packageLineWayPost(item))
      })

      // if (!flag) {
      //   this.$Message.error(flagMsg)
      //   return false
      // }

      this.removeLineWay.forEach((item) => {
        rules.push(this.packageLineWayPost(item, 1))
      })
      const data = {
        id: this.info.id,
        auto_mode: this.info.auto_mode,
        device_name: this.info.device_name,
        device_num: this.info.device_num,
        all_on_cmd: this.info.all_on_cmd,
        all_off_cmd: this.info.all_off_cmd,
        template_id: this.info.template_id,
        bus_id: this.busId,
        ruleLists: JSON.stringify(rules),
      }
      this.$service.post(url, data).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.isEdit = false
          this.isDisabled = false
          this.$emit('on-change')
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleEdit() {
      this.isEdit = true
      this.isDisabled = true
    },
    handleDelete() {
      if (!this.info.id) {
        this.$emit('on-change')
        return false
      }
      this.$Modal.confirm({
        title: '提示',
        content: `<p>确认删除 ${this.info.device_name} 吗？</p>`,
        onOk: () => {
          this.$service.post('/web/light_control/deleteRule', { id: this.info.id, bus_id: this.busId }).then(res => {
            if (res.data.errorcode === 0) {
              this.isEdit = false
              this.isDisabled = false
              this.$Message.success(res.data.errormsg)
              this.$emit('on-change')
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
  },
  created() {
    this.isEdit = this.info.action === 'add'
    this.isDisabled = this.info.action === 'add'
    this.getDeviceList()
    this.getStadiumList()
  },
}
</script>

<style lang="less" scoped>
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.light-box {
  border: 2px solid #eeeeee;

  .line-way {
    display: flex;
    flex-direction: row;
  }
}

.status-box {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
}

@media screen and (min-width: 1900px) {
  .line-way-box {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

@media screen and (max-width: 1900px) {
  .line-way-box {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }
}
</style>
