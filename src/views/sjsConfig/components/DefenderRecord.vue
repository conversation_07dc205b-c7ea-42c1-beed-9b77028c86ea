<template>
  <div class="table-wrap">
    <header>
      <DatePicker
        ref="datePicker"
        @on-change="handleDateChange"
        :value="duringDate"
        type="daterange"
        placeholder="选择日期"
        class="option-select"
        style="width: 220px"
        transfer
        :options="dateOptions"
        :clearable="false"
      ></DatePicker>
      <Input v-model="searchText" class="w120" placeholder="设备SN" clearable />
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <Button type="success" @click="exportTable">导出Excel</Button>
      <Page
        @on-change="handlePage"
        :total="total"
        :current="currentPage"
        @on-page-size-change="pageSizeChanged"
        show-total
        show-sizer
        transfer
      ></Page>
    </footer>

    <!-- Image Preview Modal -->
    <Modal v-model="showImageModal" title="图像预览" width="800" footer-hide>
      <div style="text-align: center">
        <img :src="previewImageUrl" style="max-width: 100%" alt="图像预览" />
      </div>
    </Modal>
  </div>
</template>

<script>
import { formatDate } from '@/utils'
import { mapState } from 'vuex'

export default {
  name: 'DefenderRecord',
  computed: {
    ...mapState(['busName']),
  },
  data() {
    const refs = this.$refs
    return {
      submitting: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      dateOptions: {
        shortcuts: [
          {
            text: '今天',
            value() {
              const today = new Date()
              return [today, today]
            },
          },
          {
            text: '最近一周',
            value() {
              const today = new Date()
              const lastWeek = new Date()
              lastWeek.setDate(today.getDate() - 6)
              return [lastWeek, today]
            },
          },
        ],
        disabledDate(date) {
          const today = new Date()
          today.setHours(23, 59, 59, 999)

          // Always disable future dates
          if (date > today) {
            return true
          }

          // Print the selected dates for debugging
          const selectStatus = refs?.datePicker?.$refs?.pickerPanel?.rangeState

          if (selectStatus && selectStatus.selecting) {
            const selectedDate = new Date(selectStatus.from)
            const weekBefore = new Date(selectedDate)
            const weekAfter = new Date(selectedDate)
            weekBefore.setDate(selectedDate.getDate() - 7)
            weekAfter.setDate(selectedDate.getDate() + 7)

            if (weekAfter > today) {
              weekAfter.setTime(today.getTime())
            }

            return date < weekBefore || date > weekAfter
          }

          return false
        },
      },
      searchText: '',
      showImageModal: false,
      previewImageUrl: '',
      columns: [
        { title: '序号', type: 'index', width: 80 },
        { title: '设备SN', key: 'device_sn' },
        { title: '时间', key: 'created_time' },
        { title: '区域人数', key: 'head_count' },
        {
          title: '图像预览',
          key: 'picture_url',
          render: (h, params) => {
            if (params.row.picture_url) {
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'primary',
                      size: 'small',
                    },
                    on: {
                      click: () => {
                        this.previewImage(params.row.picture_url)
                      },
                    },
                  },
                  '预览'
                ),
              ])
            } else {
              return h('div', [h('span', '暂无数据')])
            }
          },
        },
      ],
      list: [],
    }
  },
  created() {
    const today = new Date()
    this.duringDate = [today, today]
    this.getList()
  },
  methods: {
    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        const startDate = new Date(dates[0])
        const endDate = new Date(dates[1])

        // Calculate the difference in days
        const diffTime = Math.abs(endDate - startDate)
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        // If the range is more than 7 days, adjust the end date
        if (diffDays > 7) {
          const newEndDate = new Date(startDate)
          newEndDate.setDate(startDate.getDate() + 7)
          this.duringDate = [startDate, newEndDate]
          this.$Message.warning('日期范围已限制为最多一周')
        } else {
          this.duringDate = dates
        }
      } else {
        this.duringDate = dates
      }
    },
    handleSearch() {
      this.currentPage = 1
      this.getList()
    },
    handlePage(page) {
      this.currentPage = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.pageSize = size
      this.currentPage = 1
      this.getList()
    },
    getList() {
      if (this.submitting) return
      this.submitting = true

      let params = {
        page_no: this.currentPage,
        page_size: this.pageSize,
        device_sn: this.searchText,
      }

      if (Array.isArray(this.duringDate) && this.duringDate.length === 2 && this.duringDate[0] && this.duringDate[1]) {
        params.s_date = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        params.e_date = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      }

      return this.$service
        .post('/Web/Business/get_device_log_list', params)
        .then((res) => {
          this.submitting = false
          if (res.data.errorcode === 0) {
            this.list = res.data.data.list || []
            this.total = Number(res.data.data.count) || 0
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(() => {
          this.submitting = false
        })
    },
    exportTable() {
      if (this.submitting) return
      this.submitting = true

      let params = {
        page_no: 1,
        page_size: this.total || 10,
        device_sn: this.searchText,
        is_export: 1,
      }

      if (Array.isArray(this.duringDate) && this.duringDate.length === 2 && this.duringDate[0] && this.duringDate[1]) {
        params.s_date = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        params.e_date = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      }

      return this.$service
        .post('/Web/Business/get_device_log_list', params)
        .then((res) => {
          this.submitting = false

          // if (res.data.errorcode === 0) {
          //   // Generate filename with business name and current date
          //   const currentDate = formatDate(new Date(), 'yyyy-MM-dd')
          //   const filename = `${this.busName}防尾随记录(${currentDate})`

          //   const exportColumns = [{ title: '场馆名称', key: 'bus_name' }, ...this.columns.slice(1)]
          //   this.$refs.table.exportCsv({
          //     filename,
          //     columns: exportColumns,
          //     data: res.data.data.list || [],
          //   })
          // } else {
          //   this.$Message.error(res.data.errormsg)
          // }

          if (res.data.errorcode === 0) {
            this.$Message.success({
              content: '导出任务运行中，请稍后到消息中心下载!',
              duration: 3,
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(() => {
          this.submitting = false
        })
    },
    previewImage(url) {
      this.previewImageUrl = url
      this.showImageModal = true
    },
  },
}
</script>

<style lang="less" scoped>
.table-wrap {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .w120 {
      width: 120px;
      margin-right: 10px;
    }

    .option-select {
      margin-right: 10px;
    }

    button {
      margin-right: 10px;
    }
  }

  footer {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
