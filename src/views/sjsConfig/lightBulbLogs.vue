<template>
  <div class="table-wrap">
    <header>
      <DatePicker
        @on-change="handleDateChange"
        :value="duringDate"
        type="daterange"
        placeholder="选择日期"
        class="option-select"
        style="width: 220px"
        transfer
        :options="dateOptions"
        :clearable="false"
      ></DatePicker>
      <Input v-model="device_num" class="w120" placeholder="设备编号" />
      <Select v-model="action_id" class="w120" placeholder="全部动作">
        <Option v-for="item in actionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="space_position_id" style="width:300px;" placeholder="场地" filterable clearable>
        <Option v-for="item in stadiumList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <Button type="success" @click="exportTable">导出Excel</Button>
      <Page
        @on-change="handlePage"
        :total="total"
        :current="currentPage"
        @on-page-size-change="pageSizeChanged"
        show-total
        show-sizer
        transfer
      ></Page>
    </footer>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      dateOptions: {
        disabledDate (date) {
          return date && date.valueOf() >= Date.now()
        }
      },
      device_num: '',
      action_id: '',
      // 1:系统开灯 2:系统关灯 3:手动开灯 4:手动关灯 5:二维码开灯 6:二维码关灯
      actionList: [
        { value: '', label: '全部' },
        { value: 1, label: '系统开灯' },
        { value: 2, label: '系统关灯' },
        { value: 3, label: '手动开灯' },
        { value: 4, label: '手动关灯' },
      ],
      space_position_id: '',
      columns: [
        { title: '时间', key: 'create_time' },
        { title: '操作人', key: 'admin_name' },
        { title: '动作', key: 'action_name' },
        { title: '设备编号', key: 'device_num' },
        { title: '线路编号', key: 'line_index' },
        { title: '执行指令', key: 'line_cmd' },
        { title: '对应场地', key: 'space_name' }
      ],
      list: [],
      stadiumList: []
    }
  },
  computed: {
    ...mapGetters(['busId']),
  },
  methods: {
    handleDateChange(val) {
      this.duringDate = val
    },
    handleSearch() {
      this.currentPage = 1
      this.getList()
    },
    handlePage(val) {
      this.currentPage = val
      this.getList()
    },
    getList(is_export = 0) {
      let space_id = ''
      let position = ''
      if (this.space_position_id) {
        space_id = this.space_position_id.split('_')[0]
        position = this.space_position_id.split('_')[1]
      }
      return this.$service
        .post('/web/light_control/getLogLists', {
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          device_num: this.device_num,
          action_id: this.action_id,
          space_id,
          position,
          page_no: this.currentPage,
          page_size: this.pageSize,
          bus_id: this.busId,
          is_export,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            this.list = res.data.data.list
            this.total = parseInt(res.data.data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getStadiumList() {
      return this.$service
        .post('/web/light_control/getRpcTreeListsSpaceByBusId', {
          bus_id: this.busId
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            if (Array.isArray(res.data.data)) {
              this.stadiumList = res.data.data
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    pageSizeChanged(size) {
      this.currentPage = 1
      this.pageSize = size
      this.getList()
    },
    exportTable() {
      let space_id = ''
      let position = ''
      if (this.space_position_id) {
        space_id = this.space_position_id.split('_')[0]
        position = this.space_position_id.split('_')[1]
      }
      return this.$service
        .post('/web/light_control/getLogLists', {
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          device_num: this.device_num,
          action_id: this.action_id,
          space_id,
          position,
          page_no: 1,
          page_size: this.total,
          bus_id: this.busId,
          is_export: 1,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.$refs.table.exportCsv({
              filename: '灯控开关记录',
              columns: this.columns,
              data: res.data.data.list
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
  created() {
    const today = new Date()
    const whatDay = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
    this.duringDate = [whatDay, whatDay]

    this.getList()
    this.getStadiumList()
  },
}
</script>
