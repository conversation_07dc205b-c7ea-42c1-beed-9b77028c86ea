<template>
<div>
  <div class="table-wrap">
    <header>
      <Select v-if="deviceList && deviceList.length>0" v-model="postData.device_id" class="w120"  placeholder="中控柜" filterable>
        <Option v-for="item in deviceList" :key="item.device_id" :value="item.device_id">{{item.device_name}}</Option>
      </Select>
      <Select v-model="postData.status" class="w120"  placeholder="状态">
        <Option value="">状态</Option>
        <Option :value="1">使用中</Option>
        <Option :value="99">未使用</Option>
        <Option :value="5">待清洁</Option>
      </Select>
      <Input v-model="postData.user_name" class="w120" placeholder="姓名" @on-enter="search" />
      <Button type="success" class="search" @click="search">搜索</Button>
    </header>
    <main>
      <Table ref="table" :columns="columns" :data="tableData" stripe disabled-hover></Table>
    </main>
    <footer>
      <Page class="page" :total="totalCount" :page-size="postData.page_size" :current.sync="postData.page_no" placement="top" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
      </Page>
    </footer>
  </div>
</div>
</template>

<script>
const statusObj = {
  '1': '使用中',
  '5': '待清洁',
  '99': '未使用',
}

export default {
  name: 'goodsExchange',
  data() {
    return {
      totalCount: 0,
      tableData: [],
      deviceList: [],

      postData: {
        device_id: '',
        status: '',
        user_name: '',
        page_size: 10,
        page_no: 1
      },
      columns: [
        {
          title: '柜号',
          key: 'cabinet_id'
        },
        {
          title: '姓名',
          key: 'username'
        },
        {
          title: '状态',
          key: 'status',
          render: (h, { row }) => {
            return (<span>{ statusObj[row.status] || '未知状态' }</span>);
          }
        },
        {
          title: '分配时间',
          key: 'created'
        },
        {
          title: '操作',
          render: (h, params) => {
            let item = params.row;
            return (
              <div>
                {
                  item.status == '5'
                    ? <i-button
                      type="text"
                      onClick={() => { this.handleSureClean(item.cabinet_id) }}
                    >
                      完成清洁
                    </i-button>
                    : item.status == '1'
                      ? <i-button
                        type="text"
                        onClick={() => {
                          this.clearStatus(item.cabinet_id);
                        }}
                      >
                        清除占有
                      </i-button>
                      : <span style="color: #ccc">-</span>
                }
              </div>
            );
          }
        }
      ]
    };
  },
  components: {},
  async created() {
    await this.getDeviceList();
    if (this.postData.device_id) {
      this.getList();
    }
  },
  methods: {
    clearStatus(cId) {
      this.$service
        .post('/Web/Business/clearCabinet', {
          device_id: this.postData.device_id,
          cabinet_id: cId
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    // 完成清洁-确认弹窗
    handleSureClean(lockerNo) {
      this.$Modal.confirm({
        title: "确认已完成清洁？",
        onOk: () => {
          this.handleCompleteClean(lockerNo);
        }
      });
    },
    // 变更清洁状态为空闲
    handleCompleteClean(lockerNo) {
      const url = "/Web/LockerRent/postCompleteClean"
      const params = {
        lockerNo,
        deviceId: this.postData.device_id // 设备id
      }
      return this.$service.post(url, params).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg);
          this.getList();
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/Business/cabinetLists', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getDeviceList() {
      return this.$service.post('/Web/Business/getAllDevices').then(res => {
        if (res.data.errorcode == 0) {
          this.deviceList = res.data.data;
          if (res.data.data && res.data.data.length) {
            this.postData.device_id = res.data.data[0].device_id;
          } else {
            this.$Message.warning('暂无可选中控柜');
          }
        } else {
          this.$Message.error(res.data.errormsg);
        }
        return res.data
      });
    }
  }
};
</script>

