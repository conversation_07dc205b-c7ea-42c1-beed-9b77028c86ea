<template>
  <div class="table-wrap">
    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <Button type="success" @click="addDragonfly">绑定蜻蜓机</Button>
    </footer>
     <Modal v-model="showAdd" :mask-closable="false" title="添加">
      <Form ref="addForm" :model="modalData" class="modal-form" :label-width="80">
        <Form-item label="设备SN" prop="device_id" :rules="{required: true, message: '请输入SN码', trigger: 'blur'}">
          <Input placeholder="请输入sn码" v-model="modalData.device_id" :disabled="!!modalData.id"/>
        </Form-item>
        <Form-item label="设备名称" prop="device_name"
                   :rules="{required: true, message: '请输入设备名称', trigger: 'blur'}">
          <Input placeholder="请输入设备名称" v-model="modalData.device_name"/>
        </Form-item>
        <Form-item label="备注" prop="remark">
          <Input type="textarea" placeholder="请填写备注" v-model="modalData.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveModal">保存</Button>
        <Button @click="showAdd = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
export default {
  name: 'Dragonfly',
  data() {
    return {
      showAdd: false,
      modalData: {
        device_id: '',
        device_name: '',
        remark: ''
      },
      columns: [
        { title: '设备名称', key: 'device_name' },
        { title: '设备SN', key: 'device_id' },
        { title: '备注', key: 'remark' },
        {
          title: '操作',
          key: 'comes',
          render: (h, params) => {
            return [
              <i-button
                type="text"
                style={{ color: '#52a4ea', minWidth: '0', marginRight: '20px' }}
                onClick={() => {
                  this.modalData = {...params.row}
                  this.showAdd = true
                }}
              >
                编辑
              </i-button>,
              <i-button
                type="text"
                style={{ color: '#ff696a' }}
                onClick={() => {
                  this.cancelDetail(params.row.id)
                }}
              >
                解绑
              </i-button>
            ]
          }
        }
      ],
      list: []
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.modalData = {
          device_id: '',
          device_name: '',
          remark: ''
        }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    addDragonfly() {
      this.showAdd = true
    },
    saveModal() {
      const url = this.modalData.id ? '/Web/DragonflyMachine/edit' : '/Web/DragonflyMachine/bind'
      this.$refs.addForm.validate(val => {
        if (!val) return false
        this.$service.post(url, this.modalData).then(res => {
          if (res.data.errorcode === 0) {
            this.showAdd = false
            this.$Message.success(res.data.errormsg)
            this.getList()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      })
    },
    cancelDetail(id) {
      this.$service.post('/Web/DragonflyMachine/unbind', { id }).then(res => {
        if (res.data.errorcode === 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getList() {
      return this.$service.post('/Web/DragonflyMachine/get_list').then(res => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>
