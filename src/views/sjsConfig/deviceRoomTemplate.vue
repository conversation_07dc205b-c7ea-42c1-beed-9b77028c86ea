<template>
  <div class="box">
    <Form-item label="智能硬件方案">
      <RadioGroup v-model="curData.device_type">
        <Radio label="1" :disabled="!isSelectState">指静脉方案</Radio>
        <Radio label="2" :disabled="!isSelectState">二维码方案</Radio>
        <Radio label="3" :disabled="!isSelectState">人脸识别方案</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="二维码锁编号" v-if="curData.device_type=='2'">
      <Input v-model="curData.qr_device_id" :disabled="!isSelectState" />
    </Form-item>
    <Form-item label="设备编号" v-else-if="curData.device_type=='3'">
      <Input v-model="curData.qr_device_id" :disabled="!isSelectState" />
    </Form-item>
    <Form-item label="设备编号" v-else>
      <Select v-model="curData.device_id" placeholder="请选择设备" :disabled="!isSelectState" clearable filterable>
        <Option v-for="device in deviceList" :key="device" :value="device">{{device}}</Option>
      </Select>
    </Form-item>
    <Form-item label="商汤鉴权" v-if="curData.device_type=='1'">
      <RadioGroup v-model="curData.is_st_relation">
        <Radio label="0" :disabled="!isEdit">关闭</Radio>
        <Radio label="1" :disabled="!isEdit">开启</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="人脸设备" v-if="curData.is_st_relation == 1 && curData.device_type=='1'">
        <Input :disabled="!isEdit" placeholder="商汤设备ID" v-model="curData.st_device_id"/>
      </Form-item>
    <Form-item label="放置位置">
      <RadioGroup v-model="curData.device_place">
        <Radio label="1" :disabled="!isSelectState">教室</Radio>
        <Radio label="2" :disabled="!isSelectState">订场区域</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="自动核票" v-if="curData.device_place==2">
      <RadioGroup v-model="curData.voluntarily_ratify">
        <Radio label="1" :disabled="!isEdit">开启</Radio>
        <Radio label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="会员限制" v-if="curData.device_place==2">
      <RadioGroup v-model="curData.member_limit">
        <Radio label="0" :disabled="!isEdit">全部会员和持票人员可进</Radio>
        <Radio label="3" :disabled="!isEdit">指定会员卡可进</Radio>
        <Radio label="4" :disabled="!isEdit">订场和持票人员可进</Radio>
      </RadioGroup>
      <Alert v-show="curData.member_limit!=3" banner closable type="warning" style="margin-top:20px;max-width:550px;width:53%;">注意持票人员进场需要先进行票务核销 或者 门禁机开启了自动核票</Alert>
    </Form-item>
    <Form-item v-if="curData.device_place==1" label="放置教室">
      <Select v-model="curData.class_id" placeholder="请选择教室" :disabled="!isSelectState" clearable filterable>
        <Option v-for="room in roomList" :key="room.id" :value="room.id">{{room.classroom_name}}</Option>
      </Select>
    </Form-item>
    <Form-item v-if="curData.device_place==1" label="自动签到">
      <RadioGroup v-model="curData.access_auto_sign">
        <Radio label="1" :disabled="!isEdit">开启</Radio>
        <Radio label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <template>
      <Form-item v-if="curData.device_place==1" label="储值卡扣费">
        <RadioGroup v-model="curData.enable_store_card">
          <Radio label="1" :disabled="!isEdit">开启</Radio>
          <Radio label="0" :disabled="!isEdit">关闭</Radio>
        </RadioGroup>
      </Form-item>
      <Form-item v-if="curData.enable_store_card == 1 && curData.device_place==1" label="">
        单次扣费
        <InputNumber :disabled="!isEdit" style="width: 80px" v-model="curData.consumption_price" :min="0.01" @on-blur="handleSinglePrice" /> 元
      </Form-item>
    </template>
    <Form-item label="会员限制" v-if="curData.device_place==1">
      <RadioGroup v-model="curData.member_limit">
        <Radio label="0" :disabled="!isEdit">所有会员可进</Radio>
        <Radio label="3" :disabled="!isEdit">指定会员卡可进</Radio>
        <Radio v-if="curData.device_place==1" label="1" :disabled="!isEdit">仅预约团操课的会员可进</Radio>
        <Radio v-if="curData.device_place==1" label="2" :disabled="!isEdit">仅预约私教课的会员可进</Radio>
        <Radio v-if="curData.device_place==2" label="4" :disabled="!isEdit">仅订场会员可进</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item v-if="curData.member_limit==3" label="可进门的卡种">
      <Select v-model="curData.allow_card" placeholder="请选择" multiple :disabled="!isEdit" clearable filterable>
        <Option v-for="item in cardList" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
      </Select>
    </Form-item>
    <Form-item v-if="curData.member_limit == 4 && curData.device_place == 2" label="门禁所在场地">
      <AdminRegion v-model="curData.allow_space" url="/Web/Space/getTypeSpaceList" multiple placeholder="请选择" style="max-width: 550px;" :noway="!isEdit" singular-primary-key/>
      <Alert banner closable type="warning" style="margin-top:20px;max-width:550px;width:53%;">场地预订开始时间前5分钟会员即可进场，场地预订时间结束则禁止会员进场</Alert>
    </Form-item>
    <Form-item label="进门时间限制" v-if="curData.device_place==1 && (curData.member_limit==1||curData.member_limit==2)">
      <RadioGroup v-model="curData.time_limit">
        <Radio label="0" :disabled="!isEdit">全天可进</Radio>
        <Radio label="1" :disabled="!isEdit">限定时间内进场</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item v-if="curData.time_limit==1 && (curData.member_limit==1||curData.member_limit==2)">
      课程开始前
      <InputNumber style="width:100px" :max="60" :min="1" v-model="curData.start_begin" size="large" :disabled="!isEdit"></InputNumber>
      分钟 到 课程结束后
      <InputNumber style="width:100px" :max="60" :min="1" v-model="curData.end_after" size="large" :disabled="!isEdit"></InputNumber>
      分钟内可进场
    </Form-item>
    <Form-item>
      <div v-if="isEdit">
        <Button shape="circle" icon="android-arrow-back" @click="handleCancle">取消</Button>
        <Button shape="circle" type="success" icon="android-checkmark-circle" @click="handleSave">保存</Button>
      </div>
      <div v-else>
        <Button class="centerbut" shape="circle" @click="handleQrpic" v-if="curData.device_type=='2'">
          <i class="qrcode"> </i>
          <span>开门二维码</span>
        </Button>
        <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
        <Button shape="circle" type="error" icon="minus-round" @click="handleDelete">删除门禁机</Button>
      </div>
    </Form-item>

    <Modal v-model="showqrModal" :mask-closable="true" :width="$store.state.is_qn_j == 1 ? '540' : '280'" title="开门二维码" @on-cancel="cancelqrModal">
      <div class="qrModalbody">
        <div class="qrModalContent">
          <img class="image" :src="qraddr" width="240" height="273" />
          <p>生活运动管家</p>
          <a v-if="qraddr" download="运动生活管家" :href="qraddr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
        <div class="line" v-if="$store.state.is_qn_j == 1"></div>
        <div class="qrModalContent" v-if="$store.state.is_qn_j == 1">
          <img class="image" :src="otherCode" width="240" height="273" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="otherCode">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>
<script>
  import { getBaseUrl } from 'utils/config';
  import AdminRegion from 'components/form/adminRegion.vue';
  export default {
    components: { AdminRegion },
    props: {
      drData: Object,
      notSelectedRoom: Array,
      notSelectedDevice: Array,
      busId: String,
      cardList: {}
    },
    data() {
      return {
        isEdit: false,
        isSelectState: false,
        qraddr: '',
        otherCode: '',
        showqrModal: false,
        curData: {
          action: 'save',
          device_type: '1',
          device_place: '1',
          is_st_relation: '0',
          st_device_id: '',
          device_id: '',
          enable_store_card: '0',
          consumption_price: 0.01,
          class_id: '',
          member_limit: '0',
          time_limit: '',
          allow_card: [],
          start_begin: 30,
          end_after: 10,
          access_auto_sign: '0',
          qr_device_id: ''
        }
      };
    },
    watch: {
      drData(val) {
        if (this.drData.device_type == '1') {
          this.$set(this.drData, 'qr_device_id', '');
        } else {
          this.$set(this.drData, 'qr_device_id', this.drData.device_id);
        }
        this.curData = JSON.parse(JSON.stringify(this.drData));
        this.curData.consumption_price = Number(this.curData.consumption_price);
        this.curData.start_begin = Number(this.curData.start_begin);
        this.curData.end_after = Number(this.curData.end_after);
      }
    },
    computed: {
      deviceList() {
        if (this.curData.device_type == '1') {
          let arr = this.notSelectedDevice ? Array.from(this.notSelectedDevice) : [];
          if (arr.filter(item => item === this.curData.device_id).length === 0) {
            arr.push(this.curData.device_id);
          }
          return arr;
        }
      },
      roomList() {
        let arr = this.notSelectedRoom ? Array.from(this.notSelectedRoom) : [];
        if (arr.filter(item => item.id == this.curData.class_id).length === 0) {
          arr.push({
            id: this.curData.class_id,
            classroom_name: this.curData.class_name
          });
        }
        return arr;
      }
    },
    methods: {
      handleSinglePrice() {
        this.curData.consumption_price = Number(this.curData.consumption_price.toFixed(2))
      },
      handleQrpic() {
        this.showqrModal = true;
        this.qraddr = `${getBaseUrl()}/Web/Business/getDeviceappletqc?${this.curData.qrcode_param}`;
        this.otherCode = `${getBaseUrl()}/Web/Business/getDeviceQnapplet?${this.curData.qrcode_param}`;
      },
      cancelqrModal() {
        this.showqrModal = false;
        this.qraddr = '';
        this.otherCode = '';
      },
      handleCancle() {
        this.isEdit = false;
        this.isSelectState = false;
        this.$emit('kidSay');
      },
      handleSave() {
        this.curData.qr_device_id = this.curData.qr_device_id.replace(/\s+/g, '');
        if (this.curData.device_type == '1' && this.curData.device_id.length === 0) {
          this.$Message.error('请选择会员设备！');
          return false;
        } else if (this.curData.device_type == '2' && this.curData.qr_device_id == '') {
          this.$Message.error('请填写二维码锁编号！');
          return false;
        } else if (this.curData.class_id.length === 0 && this.curData.device_place == 1)  {
          this.$Message.error('请选择会员教室！');
          return false;
        } else if (this.curData.member_limit.length === 0) {
          this.$Message.error('请选择会员限制！');
          return false;
        } else if (
          (this.curData.member_limit == 1 || this.curData.member_limit == 2) &&
          this.curData.time_limit.length === 0
        ) {
          this.$Message.error('进门时间限制！');
          return false;
        } else if (this.curData.member_limit == 3 && this.curData.allow_card.length === 0) {
          this.$Message.error('请选择进门卡种！');
          return false;
        // } else if (this.curData.member_limit == 4 && (!this.curData.allow_space || this.curData.allow_space.length === 0)) {
        //   this.$Message.error('请选择立式门禁机所在场地！');
        //   return false;
        } else if (this.curData.device_type == '1' && this.curData.is_st_relation == 1 && !this.curData.st_device_id) {
          this.$Message.error('请填写人脸设备！');
          return false;
        }
        const postData = {
          ...this.curData,
          ...{
            bus_id: this.busId,
            device_id: this.curData.device_type == '1' ? this.curData.device_id : this.curData.qr_device_id,
            allow_card:
              this.curData.allow_card && this.curData.allow_card.length > 0 ? this.curData.allow_card.join(',') : ''
          }
        };
        this.$service
          .post('/Web/Business/set_guard_config', postData)
          .then(res => {
            this.$Message.success(res.data.errormsg);
            this.isEdit = false;
            this.isSelectState = false;
            this.$emit('kidSay');
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      handleEdit() {
        this.isEdit = true;
        this.isSelectState = false;
      },
      handleDelete() {
        this.curData.qr_device_id = this.curData.qr_device_id.replace(/\s+/g, '');
        this.$service
          .post('/Web/Business/del_guard_config', {
            device_id: this.curData.device_type == '1' ? this.curData.device_id : this.curData.qr_device_id,
            bus_id: this.busId
          })
          .then(res => {
            this.$Message.success(res.data.errormsg);
            this.$emit('kidSay');
          })
          .catch(err => {
            this.$Message.error(err);
          });
      }
    },
    created() {
      this.isEdit = this.drData.action == 'save';
      this.isSelectState = this.drData.action == 'save';
      this.curData = Object.assign(this.curData, JSON.parse(JSON.stringify(this.drData)));
      this.curData.consumption_price = Number(this.curData.consumption_price);
      this.curData.start_begin = Number(this.curData.start_begin);
      this.curData.end_after = Number(this.curData.end_after);
      if (this.drData.device_type == '1') {
        this.curData.qr_device_id = '';
      } else {
        this.curData.qr_device_id = this.drData.device_id;
      }
    }
  };
</script>
<style lang="less" scoped>
  .centerbut,
  .centerbut /deep/ span {
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .qrcode {
    display: inline-block;
    width: 17px;
    height: 17px;
    margin-right: 5px;
    background: url('~assets/img/qrcode.png') no-repeat;
    background-size: contain;
  }
  .qrModalbody {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .qrModalContent{
      width: 240px;
      height: 353px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
    .line {
      width: 1px;
      height: 353px;
      background: rgba(#333, .3);
    }
  }
</style>

