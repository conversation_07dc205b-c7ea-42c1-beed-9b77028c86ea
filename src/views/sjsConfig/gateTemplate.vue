<template>
  <div class="box">
    <Form-item label="智能硬件方案">
      <RadioGroup v-model="curData.device_type">
        <Radio :disabled="!isSelectState" :label="1">指静脉</Radio>
        <Radio :disabled="!isSelectState" :label="2">二维码</Radio>
        <Radio :disabled="!isSelectState" :label="3">人脸</Radio>
      </RadioGroup>
    </Form-item>
    <template v-if="curData.device_type === 2">
      <Form-item label="进场锁编号">
        <Input v-model="curData.device_id" :disabled="!isSelectState" />
      </Form-item>
      <Form-item label="出场锁编号">
        <Input v-model="curData.device_id_out" :disabled="!isSelectState" />
      </Form-item>
    </template>
    <template v-if="curData.device_type === 3">
      <Form-item label="进场设备号">
        <Input v-model="curData.device_id" :disabled="!isSelectState" /> 
        <Checkbox v-model="curData.in_door_flow.camera_device_anti_follow" style="margin-left:8px;" class="" true-value="1" false-value="0" :disabled="!isSelectState">
          防跟随
        </Checkbox>
        <div v-if="curData.in_door_flow.camera_device_anti_follow == 1" style="margin-top: 8px;">
          智能摄像机
          <Select style="width: 120px;margin-right:8px;margin-left:8px;" v-model="curData.in_door_flow.camera_device_id" placeholder="请选择设备" :disabled="!isSelectState" clearable filterable>
            <Option v-for="device in cameraDeviceList" :key="device.device_sn" :value="device.device_sn">{{device.device_name}}</Option>
          </Select>
          人数等于 <Input-number :max="999" :min="1" :precision="0" style="width: 120px;margin-left:8px;" v-model="curData.in_door_flow.camera_device_head_count"  disabled /> 
        </div>
      </Form-item>
      <Form-item label="出场设备号">
        <Input v-model="curData.device_id_out" :disabled="!isSelectState" />
        <Checkbox v-model="curData.out_door_flow.camera_device_anti_follow" style="margin-left:8px;" true-value="1" false-value="0" :disabled="!isSelectState">
          防跟随
        </Checkbox>
        <div v-if="curData.out_door_flow.camera_device_anti_follow == 1" style="margin-top: 8px;">
          智能摄像机
          <Select style="width: 120px;margin-right:8px;margin-left:8px;" v-model="curData.out_door_flow.camera_device_id" placeholder="请选择设备" :disabled="!isSelectState" clearable filterable>
            <Option v-for="device in cameraDeviceList" :key="device.device_sn" :value="device.device_sn">{{device.device_name}}</Option>
          </Select>
          人数等于 <Input-number :max="999" :min="1" :precision="0" style="width: 120px;margin-left:8px;" v-model="curData.out_door_flow.camera_device_head_count"  disabled /> 
        </div>
      </Form-item>
    </template>
    <template v-else>
      <Form-item label="设备编号">
        <Select v-model="curData.device_id" placeholder="请选择设备" :disabled="!isSelectState" clearable filterable>
          <Option v-for="device in deviceList" :key="device" :value="device">{{device}}</Option>
        </Select>
        <Checkbox v-model="curData.in_door_flow.camera_device_anti_follow" style="margin-left:8px;" true-value="1" false-value="0" :disabled="!isEdit">
          防跟随
        </Checkbox>
        <i-Switch v-model="curData.out_device_relevance" true-value="1" false-value="0" :disabled="!isEdit">
        </i-Switch>
        <span style="font-size: 12px;color:#333">出场设备关联</span>
        <div v-if="curData.in_door_flow && curData.in_door_flow.camera_device_anti_follow == 1" style="margin-top: 8px;">
          智能摄像机
          <Select style="width: 120px;margin-right:8px;margin-left:8px;" v-model="curData.in_door_flow.camera_device_id" placeholder="请选择设备" :disabled="!isEdit" clearable filterable>
            <Option v-for="device in cameraDeviceList" :key="device.device_sn" :value="device.device_sn">{{device.device_name}}</Option>
          </Select>
          人数等于 <Input-number :max="999" :min="1" :precision="0" style="width: 120px;margin-left:8px;" v-model="curData.in_door_flow.camera_device_head_count"  disabled /> 
        </div>
      </Form-item>
      <Form-item label="出场设备号" v-if="curData.out_device_relevance == 1">
          <Input v-model="curData.device_id_out" :disabled="!isEdit" />
          <Checkbox v-model="curData.out_door_flow.camera_device_anti_follow" style="margin-left:8px;" true-value="1" false-value="0" :disabled="!isEdit">
            防跟随
          </Checkbox>
          <div v-if="curData.out_door_flow && curData.out_door_flow.camera_device_anti_follow == 1" style="margin-top: 8px;">
            智能摄像机
            <Select style="width: 120px;margin-right:8px;margin-left:8px;" v-model="curData.out_door_flow.camera_device_id" placeholder="请选择设备" :disabled="!isEdit" clearable filterable>
              <Option v-for="device in cameraDeviceList" :key="device.device_sn" :value="device.device_sn">{{device.device_name}}</Option>
            </Select>
            人数等于 <Input-number :max="999" :min="1" :precision="0" style="width: 120px;margin-left:8px;" v-model="curData.out_door_flow.camera_device_head_count"  disabled /> 
          </div>
        </Form-item>
      </template>

    <Form-item label="闸机类型">
      <RadioGroup v-model="curData.gate_type">
        <Radio label="0" :disabled="!isEdit">场馆闸机</Radio>
        <Radio label="1" :disabled="!isEdit">通道闸机</Radio>
      </RadioGroup>
    </Form-item>
    <!-- SAAS23006-5:通道闸机才显示场馆选项,并且是多选 -->
    <Form-item label="场馆" v-if="curData.gate_type === '1'">
      <Select
        v-model="curData.pass_bus_ids"
        :disabled="!isEdit"
        placeholder="归属场馆"
        multiple
        transfer
        clearable
        filterable>
        <Option v-for="option in adminBusList" :key="option.id" :value="option.id">{{ option.name }}</Option>
      </Select>
      <Alert v-show="curData.device_type === 1" banner closable type="warning" style="margin-top:20px;max-width:550px;width:53%;">拥有指定场馆下的任一会员卡、散场票、订场的会员可通过进入</Alert>
    </Form-item>
    <Form-item label="商汤鉴权" v-if="curData.device_type === 1 && curData.gate_type === '0'">
      <RadioGroup v-model="curData.is_st_relation">
        <Radio :label="1" :disabled="!isEdit">开启</Radio>
        <Radio :label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="人脸设备" v-if="curData.is_st_relation === 1 && curData.device_type === 1 && curData.gate_type === '0'">
      <Input :disabled="!isEdit" placeholder="商汤设备ID" v-model="curData.st_device_id"/>
    </Form-item>
    <Form-item label="自动签到" v-if="curData.gate_type === '0'">
      <RadioGroup v-model="curData.private_auto_sign" @on-change="changePrivateAutoSign">
        <Radio :label="1" :disabled="!isEdit">开启</Radio>
        <Radio :label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <!-- 需要自动签到为开启状态显示 -->
    <!-- 自动签到为关闭时,voluntarily_class_sign的值也为0 -->
    <!-- 默认开启 -->
    <Form-item label="自动团课签到" v-show="curData.private_auto_sign === 1 && curData.gate_type === '0'">
      <RadioGroup v-model="curData.voluntarily_class_sign">
        <Radio :label="1" :disabled="!isEdit">开启</Radio>
        <Radio :label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="自动核票" v-if="curData.gate_type === '0'">
      <RadioGroup v-model="curData.voluntarily_ratify">
        <Radio :label="1" :disabled="!isEdit">开启</Radio>
        <Radio :label="0" :disabled="!isEdit">关闭</Radio>
      </RadioGroup>
    </Form-item>
    <Form-item label="会员限制" v-if="curData.gate_type === '0'">
      <RadioGroup v-model="curData.member_limit">
        <Radio :label="0" :disabled="!isEdit">全部会员和持票人员可进</Radio>
        <Radio :label="3" :disabled="!isEdit">指定会员卡可进</Radio>
        <Radio :label="4" :disabled="!isEdit">订场和持票人员可进</Radio>
        <Radio :label="5" :disabled="!isEdit">指定资格证可进</Radio>
      </RadioGroup>
      <Alert v-show="curData.member_limit !== 3 && curData.gate_type === '0'" banner closable type="warning" style="margin-top:20px;max-width:550px;width:53%;">注意持票人员进场需要先进行票务核销 或者 闸机开启了自动核票</Alert>
    </Form-item>
    <Form-item v-if="curData.member_limit === 5 && curData.gate_type === '0'" label="可进门的资格证">
      <Select v-model="curData.document_type" placeholder="请选择" multiple :disabled="!isEdit" clearable filterable>
        <Option v-for="item in certificationList" :value="item.document_type" :key="item.name">{{ item.name }}</Option>
      </Select>
    </Form-item>
    <Form-item v-if="curData.member_limit === 3 && curData.gate_type === '0'" label="可进门的卡种">
      <Select v-model="curData.allow_card" placeholder="请选择" multiple :disabled="!isEdit" clearable filterable>
        <Option v-for="item in cardList" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
      </Select>
    </Form-item>
    <Form-item v-if="curData.member_limit === 4 && curData.gate_type === '0'" label="闸机所在场地">
      <AdminRegion v-model="curData.allow_space" url="/Web/Space/getTypeSpaceList" multiple placeholder="请选择" style="max-width:550px;" :noway="!isEdit" singular-primary-key/>
      <Alert banner closable type="warning" style="margin-top:20px;max-width:550px;width:53%;">场地预订时间结束则禁止会员进场</Alert>
    </Form-item>
    <Form-item>
      <div v-if="isEdit">
        <Button shape="circle" icon="android-arrow-back" @click="handleCancle">取消</Button>
        <Button style="margin-left: 30px" shape="circle" type="success" icon="android-checkmark-circle" @click="handleSave">保存</Button>
      </div>
      <div v-else>
        <Button shape="circle" icon="ios-settings-strong" @click="handleEdit">编辑设置</Button>
        <Button style="margin-left: 30px" shape="circle" type="error" icon="minus-round" @click="handleDelete">删除闸机</Button>
        <Button style="margin-left: 30px" class="centerbut" shape="circle" @click="handleQrpic" v-if="curData.device_type === 2">
          <i class="qrcode"> </i>
          <span>进门二维码</span>
        </Button>
        <Button class="centerbut" shape="circle" @click="handleOutQrpic" v-if="curData.device_type === 2">
          <i class="qrcode"> </i>
          <span>出门二维码</span>
        </Button>
      </div>
    </Form-item>

    <Modal v-model="showqrModal" :mask-closable="true" :width="$store.state.is_qn_j == 1 ? '540' : '280'" :title="modalTitle" @on-cancel="cancelqrModal">
      <div class="qrModalbody">
        <div class="qrModalContent">
          <img class="image" :src="qraddr" width="240" height="273" />
          <p>生活运动管家</p>
          <a v-if="qraddr" download="运动生活管家" :href="qraddr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
        <div class="line" v-if="$store.state.is_qn_j == 1"></div>
        <div class="qrModalContent" v-if="$store.state.is_qn_j == 1">
          <img class="image" :src="otherCode" width="240" height="273" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="otherCode">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>
<script>
import { getBaseUrl } from "utils/config";
import AdminRegion from "components/form/adminRegion.vue";
import { mapState } from 'vuex';
export default {
  components: { AdminRegion },
  props: {
    drData: Object,
    notSelectedGate: Array,
    busId: {},
    cardList: {},
    cameraDeviceList: [],
    certificationList: []
  },
  data() {
    return {
      isEdit: false,
      isSelectState: false,
      showqrModal: false,
      qraddr: "",
      otherCode: '',
      modalTitle:"进门二维码"
    };
  },
  computed: {
    ...mapState(['adminBusList', 'globalBelongBusId']),
    curData() {
      return this.drData;
    },
    deviceList() {
      let arr = this.notSelectedGate ? Array.from(this.notSelectedGate) : [];
      if (arr.filter(item => item === this.curData.device_id).length === 0) {
        arr.push(this.curData.device_id);
      }
      return arr;
    }
  },
  methods: {    cancelqrModal() {
      this.showqrModal = false;
      this.qraddr = "";
      this.otherCode = "";
    },
    handleQrpic() {
      this.showqrModal = true;
      this.modalTitle="进门二维码"
      this.qraddr = `${getBaseUrl()}/Web/Business/getDeviceappletqc?device_id=${
        this.curData.device_id
      }&bus_id=${this.curData.secret_code}`;
      this.otherCode = `${getBaseUrl()}/Web/Business/getDeviceQnapplet?device_id=${
        this.curData.device_id
      }&bus_id=${this.curData.secret_code}`;
    },
    handleOutQrpic() {
      this.showqrModal = true;
      this.modalTitle="出门二维码"
      this.qraddr = `${getBaseUrl()}/Web/Business/getDeviceappletqc?device_id=${
        this.curData.device_id_out
      }&bus_id=${this.curData.secret_code}`;
      this.otherCode = `${getBaseUrl()}/Web/Business/getDeviceQnapplet?device_id=${
        this.curData.device_id_out
      }&bus_id=${this.curData.secret_code}`;
    },
    handleCancle() {
      this.isEdit = false;
      this.isSelectState = false;
      // this.$emit('kidSay');
    },
    handleSave() {
      if (this.curData.device_id.length === 0) {
        this.$Message.error("请选择会员设备！");
        return false;
      } else if (
        this.curData.member_limit === 3 &&
        this.curData.allow_card.length === 0
      ) {
        this.$Message.error("请选择进门卡种！");
        return false;
      } else if (
        (this.curData.in_door_flow.camera_device_anti_follow == 1 && !this.curData.in_door_flow.camera_device_id) || (this.curData.out_door_flow.camera_device_anti_follow == 1 && !this.curData.out_door_flow.camera_device_id)
      ) {
        this.$Message.error("请选择智能摄像机！");
        return false;
      }
      if(this.curData.gate_type === '1' &&
        this.curData.pass_bus_ids.length === 0) {
        this.$Message.error("请选择场馆！");
        return false;
      }
      this.$service
        .post("/Web/Business/set_gate_config", {
          ...this.curData,
          ...{
            bus_id: this.busId,
            allow_card:
              this.curData.allow_card && this.curData.allow_card.length > 0
                ? this.curData.allow_card.join(",")
                : ""
          }
        })
        .then(res => {
          this.$Message.success(res.data.errormsg);
          this.isEdit = false;
          this.isSelectState = false;
          this.$emit("kidSay");
        })
        .catch(err => {
          this.$Message.error(err);
        });
    },
    handleEdit() {
      this.isEdit = true;
      this.isSelectState = false;
    },
    handleDelete() {
      if (!this.curData.device_id) {
        this.$emit("kidSay");
        return false
      }
      this.$service
        .post("/Web/Business/del_gate_config", {
          device_id: this.curData.device_id,
          bus_id: this.busId
        })
        .then(res => {
          this.$Message.success(res.data.errormsg);
          this.$emit("kidSay");
        })
        .catch(err => {
          this.$Message.error(err);
        });
    },
    // 自动签到为0(关闭)时,自动团课签到也为0(关闭)
    changePrivateAutoSign(e) {
      this.curData.voluntarily_class_sign = e
    }
  },
  created() {
    this.isEdit = this.curData.action == "save";
    this.isSelectState = this.curData.action == "save";
    if (!this.drData.card_str || this.drData.card_str === "") {
      this.drData.allow_card = [];
    } else {
      this.drData.allow_card = this.drData.card_str.split(",");
    }
    !this.adminBusList && this.$store.dispatch('getAdminBusList')
  }
};
</script>

<style lang="less" scoped>
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.qrcode {
  display: inline-block;
  width: 17px;
  height: 17px;
  margin-right: 5px;
  background: url("../../assets/img/qrcode.png") no-repeat;
  background-size: contain;
}
.qrModalbody {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .qrModalContent{
    width: 240px;
    height: 353px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }
  .line {
    width: 1px;
    height: 353px;
    background: rgba(#333, .3);
  }
}
</style>
