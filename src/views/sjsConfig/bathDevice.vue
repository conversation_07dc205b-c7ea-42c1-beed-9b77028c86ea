<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>水控方案</h2>
    </div>
     <div class="form-box-con" style="padding-bottom:10px;">

       <Form :label-width="130">
         <Form-item>
          <span style="color:lightgrey;">修改规则需要重启水控系统后生效</span>
        </Form-item>
          <Form-item label="水控管理方案">
            <RadioGroup v-model="postData.bath_device_type" @on-change="handleChange">
                <Radio :label="0">指静脉水控</Radio>
                <Radio :label="1">手环水控</Radio>
                <Radio :label="2">快诺优水控</Radio>
            </RadioGroup>
          </Form-item>
       </Form>
     </div>
    <div class="form-box-title">
      <h2>淋浴间扣费管理</h2>
    </div>
    <div class="form-box-con" style="padding-bottom:17px;">
      <Form :label-width="130">

          <Form-item label="淋浴收费">
            <RadioGroup v-model="postData.bath_pay_switch">
                <Radio label="1">开启</Radio>
                <Radio label="0">关闭</Radio>
            </RadioGroup>
          </Form-item>
          <div>
              <Form-item label="单次最长供水" required>
                  <InputNumber :active-change="false" :precision='0'  :min="0" v-model="postData.bath_keep_work" class="conf" /><span>分钟</span>
              </Form-item>
          </div>
          <div v-if="postData.bath_pay_switch==1">
              <Form-item>
                  <RadioGroup v-model="postData.bath_pay_method">
                    <Radio label="0" :disabled="postData.bath_device_type == 1">计时收费</Radio>
                    <Radio label="1" :disabled="postData.bath_device_type == 2">按次收费</Radio>
                </RadioGroup>
              </Form-item>
                  <Form-item label="可扣费卡种">
                    <Select :clearable="true"
                            filterable
                            multiple
                            v-model="postData.bath_legal_card"
                            placeholder="请选择卡种"
                        >
                    <Option v-for="item in cardList"
                            :key="item.card_id"
                            :value="item.card_id">{{item.card_name}}</Option>
                    </Select>
                </Form-item>
               <div style="margin-top:20px;" v-if="postData.bath_pay_method==='1'">
              <Form-item label="单次收费" required>
                  <InputNumber :active-change="false" :min="0" :precision='2'  v-model="postData.singleCost" class="conf" /><span>元</span>
              </Form-item>
              <Form-item label="租用" required v-if="postData.bath_device_type != 1">
                <InputNumber :active-change="false" :min="0" :precision='0'  v-model="postData.freeBathTime" class="conf" /><span>分钟以内退租将不扣费</span>
              </Form-item>
            </div>
          </div>
        <div style="margin-top:20px;" v-if="postData.bath_pay_switch==='1' && postData.bath_pay_method === '0'">
            <Form-item label="每分钟收费" required>
                <InputNumber :active-change="false" :min="0" :precision='2' v-model="postData.perMinCost" class="conf" /><span>元</span>
                <Alert type="warning" show-icon class="inventory-alert" v-if="postData.bath_device_type == 2">
                  <span>不足1分钟按1分钟收费</span>
                </Alert>
            </Form-item>
            <Form-item label="单次消费上限" required  v-if="postData.bath_device_type != 2">
                <InputNumber :active-change="false" :min="0" :precision='2'  v-model="postData.singleConsumeLim" class="conf" /><span>元</span>
            </Form-item>
            <Form-item label="单次最低消费" required>
                <InputNumber :active-change="false" :min="0" :precision='2'  v-model="postData.singleConsumeMinimo" class="conf" /><span>元</span>
            </Form-item>
            <Form-item label="卡内金额低于" required>
                <InputNumber :active-change="false" :min="0" :precision='2'  v-model="postData.refuseRemainMinimo" class="conf" /><span>元不能进场</span>
            </Form-item>
        </div>
        <Form-item>
          <div>
            <Button type="success" style="margin-right:20px;" @click="submit">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
      </div>
    <div>
      <div class="form-box-title">
        <h2>淋浴间号码管理</h2>
      </div>
      <div class="form-box-con">
        <Form :label-width="130" v-if="postData.bath_device_type == 0">
          <Form-item label="水控设备">
            <Select clearable
                    filterable
                    @on-change="deviceChange"
                    v-model="postData.device_id"
                    placeholder="请选择水控设备"
                    v-if="deviceList">
              <Option v-for="item in deviceList"
                      :key="item.device_id"
                      :value="item.device_id">{{item.device_name}}</Option>
            </Select>
          </Form-item>

          <div v-if="postData.device_id">
            <Form-item label="添加淋浴间">
              <Input v-model="addStr" placeholder="请输入淋浴间，以英文逗号分割" />
              <Button style="margin-left:10px;" type="success" @click="addPro">确定</Button>
            </Form-item>
            <!-- <Form-item label="删除淋浴间">
              <Select v-model="delStr" multiple>
                <Option v-for="item in priorityInfo.All_cabinet" :value="item" :key="item">{{ item }}</Option>
              </Select>
              <Button style="margin-left:10px;" type="success" @click="delPro">确定</Button>
            </Form-item> -->
            <div style="width:60%;">
              <Table :data="allFingerTableData" :columns="fingerColumns" />
              <!-- <Modal title="淋浴间二维码" width="300" v-model="showQRCode">
                <div style="width:100%;" slot="header">
                  <img style="width:100%;display:block;" :src="bathNoQR" />
                </div>
                <div slot="footer" class="modal-buttons">
                  <a download="淋浴间二维码" :href="bathNoQR.replace('show', 'download')">
                    <Button type="success">印刷尺寸下载</Button>
                  </a>
                  <Button style="display: none"></Button>
                </div>
              </Modal> -->
              <Modal v-model="showQRCode" :mask-closable="true" :width="$store.state.is_qn_j == 1 ? '540' : '280'" title="淋浴间二维码" @on-cancel="()=>{showQRCode = false;bathNoQR='';otherBathNoQR='';}">
                <div class="qrModalbody">
                  <div class="qrModalContent">
                    <img class="image" :src="bathNoQR" width="240" height="273" />
                    <p>生活运动管家</p>
                    <a v-if="bathNoQR" download="运动生活管家" :href="bathNoQR">
                      <Button type="success">印刷尺寸下载</Button>
                    </a>
                  </div>
                  <div class="line" v-if="$store.state.is_qn_j==1"></div>
                  <div class="qrModalContent" v-if="$store.state.is_qn_j==1">
                    <img class="image" :src="otherBathNoQR" width="240" height="273" />
                    <p>勤鸟+</p>
                    <a download="勤鸟+" :href="otherBathNoQR">
                      <Button type="success">印刷尺寸下载</Button>
                    </a>
                  </div>
                </div>
                <div slot="footer">
                </div>
              </Modal>
            </div>
          </div>
        </Form>
        <Form :label-width="120"  v-else>
         <Form-item label="淋浴间名称">
            <Select clearable
                    filterable
                    @on-change="bathGroupChange"
                    v-model="bathinfo.bathgroupname"
                    placeholder="沐浴间名称">
              <Option v-for="item in bathGroupList"
                      :key="item"
                      :value="item">{{item}}</Option>
            </Select>
          </Form-item>
          <Form-item label="淋浴间号码">
              <Input v-model="bathinfo.bathno" placeholder="淋浴间号码" />
          </Form-item>
          <Form-item label="对应设备号码">
             <Input v-model="bathinfo.bathdevicemac" placeholder="对应设备号码" />
          </Form-item>
          <Form-item>
          <div>
            <Button type="success" @click="addDev">添加</Button>
          </div>
        </Form-item>
            <Table :columns="columns" :data="tableData" ref="table" v-if="bathinfo.bathgroupname"></Table>
        </Form>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios';
import { getBaseUrl } from 'utils/config';

  export default {
    name: 'bathDevice',
    data () {
      return {
        showQRCode: false,
        bathNoQR: '',
        otherBathNoQR: '',
        fingerTableData: [],
        allFingerTableData: [],
        fingerColumns: [
          {
            title: "淋浴间号码",
            key: "bathNo"
          },
          {
            title: "操作",
            render: (h, params) => {
              let bathNo = params.row.bathNo;
              return h('div', [
                h('Button', {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  on: {
                    'click': () => {
                      this.onShowQR(bathNo)
                    }
                  }
                }, [
                  h('Icon', {
                    props: {
                      type: "md-barcode"
                    }
                  })
                ]),
                h('Button', {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  on: {
                    'click': () => {
                      this.onDeleteBathNo(bathNo)
                    }
                  }
                }, '删除')
              ])
            }
          }
        ],
        fingerPager: {
          total: 0,
        },
        cardList: '',
        conf: {},
        postData: {
            device_id: '',//  设备id
            bath_pay_switch: '0', //收费开关
            bath_pay_method: '0', //计费方式
            bath_device_type: 0,
            bath_legal_card: [], //卡种
            bath_pay_content: '', ////计费内容
            bath_keep_work: 1, //持续放水时间
            perMinCost: 1, // 每分钟收费
            singleConsumeLim: 9, //单次消费上限
            singleConsumeMinimo: 1, // 单次最低消费
            refuseRemainMinimo: 5, //卡内金额低于此数字不能进场
            singleCost: 10, //单次费用
            freeBathTime: 5, // 不扣费的时长
        },
        bathinfo: {
          bathno: '',
          bathdevicemac: '',
          bathgroupname: ''
        },
        priorityInfo: {
          All_cabinet: []
        },
        columns: [
          {
            title: '淋浴间号码',
            key: 'bathNo'
          },
          {
            title: '淋浴间',
            key: 'bathGroupName'
          },
          {
            title: '设备号码',
            key: 'bathDeviceMac',
            render: (h, param) => {
              return (
                <div>
                  {param.row.bathDeviceMac}
                  {
                    this.postData.bath_device_type != 2 ? <span style={{ color: '#ff696a' }}>{param.row.online?'在线':'离线'}</span> : ''
                  }
                </div>
              )
            }
          },
          {
            title: '操作',
            render: (h, param) => {
              const delMe = () => {
                this.handleEdit(param.row);
              };
              return (
                <div>
                  <i-button
                    type="text"
                    style={{ color: '#ff696a', minWidth: '0' }}
                    onClick={delMe}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        bathGroupList: [],
        tableData: [],
        deviceList: '',
        delStr: [],
        addStr: '',
      }
    },
    created() {
      this.getDevice()
      this.getCards()
    },
    computed: {

    },
    watch: {
        'postData.bath_device_type'(info) {
          if((info == 1 || info == 2) && !this.bathGroupList.length) {
            this.getBathGroupList()
          }
          if(info == 1) {
            this.postData.bath_pay_method = '1'
          } else if(info == 2) {
            this.postData.bath_pay_method = '0'
          }
        },
        conf(v) {
            this.postData = { ...v }

            this.postData.bath_keep_work = Number(this.postData.bath_keep_work)
            this.postData.bath_pay_switch = String(this.postData.bath_pay_switch)
            this.postData.bath_pay_method = String(this.postData.bath_pay_method)

            if(String(this.postData.bath_legal_card).indexOf(',') > 0) {
              this.postData.bath_legal_card = this.postData.bath_legal_card.split(",")
            }
            if(this.postData.bath_pay_method == 0 && this.postData.bath_pay_content != 0) {
                [this.postData.perMinCost, this.postData.singleConsumeLim, this.postData.singleConsumeMinimo, this.postData.refuseRemainMinimo] = [...this.postData.bath_pay_content.split("_")];
                this.postData.perMinCost = Number(this.postData.perMinCost) || 2;
                this.postData.singleConsumeLim = Number(this.postData.singleConsumeLim) || 2;
                this.postData.singleConsumeMinimo = Number(this.postData.singleConsumeMinimo) || 2;
                this.postData.refuseRemainMinimo = Number(this.postData.refuseRemainMinimo) || 2;
                this.postData.singleCost = Number(this.postData.singleCost) || 2;
                this.postData.freeBathTime = Number(this.postData.freeBathTime) || 2;
            }
            if(this.postData.bath_pay_method == 1 && this.postData.bath_pay_content != 0) {
                [this.postData.singleCost, this.postData.freeBathTime] = [...this.postData.bath_pay_content.split("_")];
                this.postData.singleCost = Number(this.postData.singleCost) || 2;
                this.postData.freeBathTime = Number(this.postData.freeBathTime) || 2;
                this.postData.perMinCost = Number(this.postData.perMinCost) || 2;
                this.postData.singleConsumeLim = Number(this.postData.singleConsumeLim) || 2;
                this.postData.singleConsumeMinimo = Number(this.postData.singleConsumeMinimo) || 2;
                this.postData.refuseRemainMinimo = Number(this.postData.refuseRemainMinimo) || 2;
            }
            if(this.postData.bath_pay_content == 0) {
              this.postData.singleCost = 2;
              this.postData.freeBathTime = 2;
              this.postData.perMinCost = 2;
              this.postData.singleConsumeLim = 2;
              this.postData.singleConsumeMinimo = 2;
              this.postData.refuseRemainMinimo = 2;
            }

        }
    },
    methods: {
      onShowQR(bathNo) {
        let action = "show";
        this.getCurQr(bathNo, action);
        let base = getBaseUrl();
        base = base === '/api' ? "https://beta.rocketbird.cn" : base;
        this.bathNoQR = `${base}/Web/Business/bathRoomQrcode?bathNo=${bathNo}&deviceId=${this.postData.device_id}&action=show`;
        this.otherBathNoQR = `${base}/Web/Business/qnNewbathRoomQrcode?bathNo=${bathNo}&deviceId=${this.postData.device_id}&action=show`;
        this.showQRCode = true;
      },
      onDeleteBathNo(bathNo) {
        this.$Modal.confirm({
          title: `确定要删除 ${bathNo} 号淋浴间？`,
          onOk: async () => {
            await this.delPro(bathNo);
            setTimeout(() => {
              this.getPriorityInfo(this.postData.device_id);
            }, 500)
          },
          onCancel() {}
        });
      },
      getCurQr(bathNo, action) {
        const url = "/Web/Business/bathRoomQrcode";
        let deviceId = this.postData.device_id;
        let postdata = {
          deviceId,
          bathNo,
          action
        }
        return this.$service.get(url, { params: postdata }, { responseType: 'blob' }).then(res => {
          if (res.status === 200) {
          } else {
            this.$Message.error('网络错误');
          }
        }).catch(err => {
          throw new Error(err);
        })
      },
      handleEdit (row ) {
        this.$service.post("/Web/Business/device_del_bathroom", {
          bathdevicemac: row.bathDeviceMac
        }).then(res => {
          if(res.data.errorcode == 0) {
            this.bathGroupChange(row.bathGroupName)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      addDev() {
        this.$service.post("/Web/Business/device_add_bathroom", this.bathinfo).then(res => {
          if(res.data.errorcode == 0) {
            this.bathinfo.bathno = ''
            this.bathinfo.bathdevicemac = ''
            this.bathGroupChange(this.bathinfo.bathgroupname)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      getBathGroupList() {
        this.$service.post("/Web/Business/getBathGroupList").then(res => {
            if(res.data.errorcode == 0) {
              this.bathGroupList = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      getCards() {
          this.$service.post("/Web/Member/get_all_card", {
              card_type: 3
          }).then(res => {
              if(res.status === 200) {
                  if(res.data.errorcode == 0) {
                    this.cardList = res.data.data
                  } else {
                      this.$Message.error(res.data.errormsg)
                  }
              } else {
                  console.error("服务器扑街！")
              }
          }).catch(res => {
              console.error(res)
          })
      },
      bathGroupChange (bathgroupname) {
        if (!bathgroupname) return;
        this.$service.post("/Web/Business/device_bathroom_list", {
          bathgroupname: bathgroupname
        }).then(res => {
            if(res.data.errorcode == 0) {
              this.tableData = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
        })
      },
      deviceChange (deviceID) {
        if (!deviceID) return;
        this.getPriorityInfo(deviceID);
      },
      submit() {
        if(this.postData.bath_device_type == 0 && this.deviceList == 0) {
          this.$Message.error("该场馆未配置智能水控设备，设置无效！");
          return;
        }
        let bath_legal_card = (Array.isArray(this.postData.bath_legal_card) && this.postData.bath_legal_card.join(",")) || '';
        if(this.postData.bath_pay_method === '1') {
            this.postData.bath_pay_content = `${this.postData.singleCost}_${this.postData.freeBathTime}`
        }
        if(this.postData.bath_pay_method === '0') { // 按时收费
            this.postData.bath_pay_content = `${this.postData.perMinCost}_${this.postData.singleConsumeLim}_${this.postData.singleConsumeMinimo}_${this.postData.refuseRemainMinimo}`
        }
        this.$service.post('/Web/Business/setBathSetting', {
            bath_device_type: this.postData.bath_device_type,
            bath_pay_switch: this.postData.bath_pay_switch,
            bath_pay_method: this.postData.bath_pay_method,
            bath_legal_card,
            bath_pay_content: this.postData.bath_pay_content,
            bath_keep_work: Number(this.postData.bath_keep_work)
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('设置成功!')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      addPro() {
        this.$service.post('/Web/Business/device_add_cabinet', {device_id: this.postData.device_id,add_str: this.addStr}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('添加成功')
            this.addStr = ''
            this.getPriorityInfo(this.postData.device_id)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      delPro(bathNo) {
        this.$service.post('/Web/Business/device_del_cabinet', {device_id: this.postData.device_id, del_str: bathNo}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('删除成功');
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getPriorityInfo(deviceID) {
        this.$service.post('/Web/Business/get_cabint_device_priority_info', {device_id :deviceID}).then(res => {
          if (res.data.errorcode === 0) {
            this.priorityInfo = res.data.data
            let arr = [];
            res.data.data.All_cabinet.map(item => {
              arr.push({
                bathNo: item
              })
            })
            this.allFingerTableData = [ ...arr ];
            this.fingerPager.total = this.allFingerTableData.length;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getDevice() {
        this.$service.post('/Web/Business/getBathSetting').then(res => {
          if (res.data.errorcode === 0) {
            this.deviceList = res.data.data.bath_devices;
            this.conf = res.data.data.bath_configurations;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      handleChange(val) {
        if (!this.conf.quick_new_flag && val == 2) {
          this.postData.bath_device_type = 0
          this.$Message.error("请前往【硬件设置-快诺优设置】中进行配置！");
        }
      }
    }
  }
</script>
<style lang="less">
.bath-card-table {
  width: 800px;
}
.bath-card-table .ivu-card-body {
  padding: 0;
}
</style>
<style lang="less" scoped>
  .inventory-alert {
    display: inline-block;
    width: 220px;
    margin-left: 15px;
  }
  .area-line {
    background-color: rgba(242, 242, 242, 1);
    border-radius: 70px;
    text-align: center;
    height: 40px;
    width: 700px;
    line-height: 40px;
    margin-bottom: 20px;
    vertical-align: middle;
    font-weight: bold;
    font-size: 18px;
  }
  .conf {
    width: 100px;
    margin-right: 20px;
  }
  .qrModalbody {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .qrModalContent{
      width: 240px;
      height: 353px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
    .line {
      width: 1px;
      height: 353px;
      background: rgba(#333, .3);
    }
  }
</style>
