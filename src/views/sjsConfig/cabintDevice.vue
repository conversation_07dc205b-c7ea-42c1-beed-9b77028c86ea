<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>中控柜柜号管理</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="120">
      <Form-item label="中控设备">
        <Select clearable
                filterable
                @on-change="deviceChange"
                v-model="postData.device_id"
                placeholder="请选择中控设备"
                v-if="deviceList">
          <Option v-for="item in deviceList"
                  :key="item.deviceID"
                  :value="item.deviceID">{{item.deviceName}}</Option>
        </Select>
      </Form-item>
      <div v-if="postData.device_id">
        <Form-item label="添加柜号">
          <Input v-model="addStr" placeholder="请输入柜号，以逗号分割"></Input>
          <Button type="success" @click="addPro">确定</Button>
        </Form-item>
        <Form-item label="删除柜号">
          <Select v-model="delStr" multiple>
            <Option v-for="item in priorityInfo.All_cabinet" :value="item" :key="item">{{ item }}</Option>
          </Select>
          <Button type="success" @click="delPro">确定</Button>
        </Form-item>
        <div class="area-line">优先级设置</div>
        <Form-item label="优先级1">
          <Select v-model="postData.priority_1" multiple>
            <Option v-for="item in noChoosePro1" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Form-item>
        <Form-item label="优先级2">
          <Select v-model="postData.priority_2" multiple>
            <Option v-for="item in noChoosePro2" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Form-item>
        <Form-item label="优先级3">
          <Select v-model="postData.priority_3" multiple>
            <Option v-for="item in noChoosePro3" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="submit">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </div>
    </Form>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'cabintDevice',
    data () {
      return {
        postData: {
          device_id: '',//  设备id
          priority_1: '',//  1,2,3,4,5   优先级1
          priority_2: '',//  6,7,8,9,10  优先级2
          priority_3: '',//  11,12,13,14,15 优先级3
        },
        deviceList: '',
        delStr: [],
        addStr: '',
        priorityInfo: {
          All_cabinet: []
        },
        noChoosePro: [],
      }
    },
    created() {
      this.getDevice()
    },
    computed: {
      noChoosePro1(){
        return this.priorityInfo.All_cabinet.filter(item=>this.postData.priority_2.indexOf(item)===-1 && this.postData.priority_3.indexOf(item)===-1 && this.postData.priority_5.indexOf(item)===-1)
      },
      noChoosePro2(){
        return this.priorityInfo.All_cabinet.filter((item)=>this.postData.priority_1.indexOf(item)===-1 && this.postData.priority_3.indexOf(item)===-1 && this.postData.priority_5.indexOf(item)===-1)
      },
      noChoosePro3(){
        return this.priorityInfo.All_cabinet.filter((item)=>this.postData.priority_1.indexOf(item)===-1 && this.postData.priority_2.indexOf(item)===-1 && this.postData.priority_5.indexOf(item)===-1)
      }
    },
    watch: {
    },
    methods: {
      deviceChange (deviceID) {
        if (!deviceID) return
        this.getPriorityInfo(deviceID)
      },
      submit() {
        let postData = {
          device_id: this.postData.device_id,
          priority_1: this.postData.priority_1.join(','),
          priority_2: this.postData.priority_2.join(','),
          priority_3: this.postData.priority_3.join(',')
        }
        this.$service.post('/Web/Business/set_cabint_device_priority_info', postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('设置成功!')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      addPro() {
        this.$service.post('/Web/Business/device_add_cabinet', {device_id: this.postData.device_id,add_str: this.addStr}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('添加成功')
            this.addStr = ''
            this.getPriorityInfo(this.postData.device_id)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      delPro() {
        this.$service.post('/Web/Business/device_del_cabinet', {device_id: this.postData.device_id,del_str: this.delStr.join(',')}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('删除成功')
            this.del_str = []
            this.getPriorityInfo(this.postData.device_id)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getPriorityInfo(deviceID) {
        this.$service.post('/Web/Business/get_cabint_device_priority_info', {device_id :deviceID}).then(res => {
          if (res.data.errorcode === 0) {
            this.priorityInfo = res.data.data
            this.postData.priority_1 = res.data.data.cabinet_priority_1
            this.postData.priority_2 = res.data.data.cabinet_priority_2
            this.postData.priority_3 = res.data.data.cabinet_priority_3
            this.postData.priority_5 = res.data.data.cabinet_priority_5
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getDevice() {
        this.$service.post('/Web/Business/get_cabint_device').then(res => {
          if (res.data.errorcode === 0) {
            this.deviceList = res.data.data
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .area-line {
    height: 60px;
    line-height: 60px;
    vertical-align: middle;
    width: 100%;
    font-weight: bold;
    font-size: 18px;
  }

</style>
