<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>硬件设置</h2>
    </div>
    <div class="form-box-con hadware-form">
      <Form :label-width="140">
        <Form-item label="请选择场馆">
          <Select
            clearable
            filterable
            @on-change="busChange"
            v-model="postData.bus_id"
            placeholder="请选择场馆"
            v-if="busList"
          >
            <Option v-for="item in busList" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="智能ID">
          <Input v-model="postData.appid" :disabled="disabledAppid" />
        </Form-item>
        <Collapse class="b-box" value="teamCourse" accordion>
          <Panel>
            功能配置
            <div slot="content">
              <Form-item label="营业时间" class="qrcode">
                <div>
                  <RadioGroup v-model="open_time_radio">
                    <Radio label="0">全天</Radio>
                    <Radio label="1">指定时段</Radio>
                  </RadioGroup>
                  <div v-if="open_time_radio == 1" class="time-range-list">
                    <div v-for="(item, index) in timeRanges" :key="index" class="time-range-item">
                      <TimePicker
                        class="inner-time-picker"
                        type="timerange"
                        :placeholder="'请选择时间段' + (index + 1)"
                        v-model="timeRanges[index]"
                        format="HH:mm"
                      />
                      <Button 
                        v-if="index === timeRanges.length - 1 && timeRanges.length < 5" 
                        type="text" 
                        icon="md-add" 
                        size="small"
                        @click="addTimeRange"
                      >添加</Button>
                      <Button 
                        v-if="timeRanges.length > 1"
                        type="text" 
                        icon="md-close" 
                        style="color: red"
                        size="small"
                        @click="removeTimeRange(index)"
                      >删除</Button>
                    </div>
                    <Button 
                      type="text" 
                      icon="md-swap" 
                      size="small"
                      style="margin-left: 5px;"
                      @click="sortTimeRanges"
                      title="按时间排序"
                    >排序</Button>
                  </div>
                </div>
              </Form-item>
              <Form-item label="二维码开锁" class="qrcode">
                <RadioGroup v-model="postData.qrcode_gate">
                  <Radio label="1">开</Radio>
                  <Radio label="0">关</Radio>
                </RadioGroup>
                <Form-item label="锁编号" v-if="postData.qrcode_gate != 0" class="qrcodeid">
                  <Input v-model="postData.qrcode_gate_id" />
                </Form-item>
              </Form-item>
              <Form-item label="闸机储值卡扣费">
                <RadioGroup v-model="postData.billing_on_time_switch">
                  <Radio label="on">开</Radio>
                  <Radio label="off">关</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="闸机进出规则">
                <RadioGroup v-model="postData.enter_type">
                  <Radio label="0">不限制</Radio>
                  <Radio label="1">一进一出</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item v-if="postData.enter_type == 1" label="闸机编组">
                <Select v-model="postData.used_groups" style="width: 300px" multiple>
                  <Option value="A">A</Option>
                  <Option value="B">B</Option>
                  <Option value="C">C</Option>
                  <Option value="D">D</Option>
                  <Option value="E">E</Option>
                </Select>
              </Form-item>
              <Form-item label="离场规则">
                <Select v-model="postData.leave_rule" style="width: 300px">
                  <Option value="2">不限制</Option>
                  <Option value="1">手环</Option>
                  <Option value="0">智能柜子</Option>
                </Select>
              </Form-item>
              <Form-item v-if="postData.leave_rule == 0" label="柜控编组">
                <Select v-model="postData.leave_cabinet_group" style="width: 300px" multiple>
                  <Option value="A">A</Option>
                  <Option value="B">B</Option>
                  <Option value="C">C</Option>
                  <Option value="D">D</Option>
                  <Option value="E">E</Option>
                </Select>
              </Form-item>
              <Form-item v-if="postData.leave_rule == 0" label="闸机编组">
                <Select v-model="postData.cabinet_used_groups" style="width: 300px" multiple>
                  <Option value="A">A</Option>
                  <Option value="B">B</Option>
                  <Option value="C">C</Option>
                  <Option value="D">D</Option>
                  <Option value="E">E</Option>
                </Select>
              </Form-item>
              <Form-item v-if="postData.leave_rule == 0" label="">
                <RadioGroup v-model="postData.leave_rule_type">
                  <Radio :label="0">未退柜提醒</Radio>
                  <Radio :label="1">离场强制退柜</Radio>
                </RadioGroup>
                <Select v-if="postData.leave_rule_type == 1" v-model="postData.cabinent_delayed_time" placeholder="请选择" style="width: 150px">
                  <Option value="0">立即执行</Option>
                  <Option value="15">离场 15 分钟</Option>
                  <Option value="30">离场 30 分钟</Option>
                  <Option value="60">离场 60 分钟</Option>
                  <Option value="120">离场 120 分钟</Option>
                </Select>
              </Form-item>
              <Form-item label="自动签到">
                <RadioGroup v-model="postData.gate_auto_sign">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <!-- <Form-item v-if="postData.gate_auto_sign == 1" label="自动激活">
                <RadioGroup v-model="postData.gate_auto_active">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
                <Alert type="warning" show-icon>仅有一张未激活的体验卡或会籍卡才能激活</Alert>
              </Form-item> -->

              <Form-item label="NFC 读卡器">
                <RadioGroup v-model="postData.nfc_switch">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="手环同步RFID">
                <RadioGroup v-model="postData.hand_rfid">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="签到间隔时间">
                <Select v-model="postData.sign_interval" style="width: 300px">
                  <Option value="0">0</Option>
                  <Option value="1">1小时</Option>
                  <Option value="2">2小时</Option>
                  <Option value="3">3小时</Option>
                  <Option value="6">6小时</Option>
                  <Option value="9">9小时</Option>
                  <Option value="12">12小时</Option>
                </Select>
              </Form-item>
              <Form-item label="柜控二维码" class="qr_code_type">
                <RadioGroup v-model="postData.qr_code_type">
                  <Radio label="0">运动生活管家</Radio>
                  <Radio label="1">勤鸟+</Radio>
                </RadioGroup>
              </Form-item>
            </div>
          </Panel>
          <Panel>
            人脸识别配置
            <div slot="content">
              <Form-item label="人脸识别">
                <RadioGroup v-model="postData.face_switch">
                  <Radio label="1">开</Radio>
                  <Radio label="0">关</Radio>
                </RadioGroup>
                <checkbox
                  v-if="postData.face_switch === '1'"
                  style="margin-left: 30px"
                  v-model="postData.face_nfc_switch"
                  true-value="1"
                  false-value="0"
                >同步RFID到人脸设备</checkbox>
                <checkbox
                  style="margin-left: 30px"
                  v-model="postData.face_check"
                  true-value="1"
                  false-value="0"
                >魔点人脸检测</checkbox>
              </Form-item>
              <Form-item label="姓名隐私">
                <RadioGroup v-model="postData.name_private_switch">
                  <Radio label="1">开</Radio>
                  <Radio label="0">关</Radio>
                </RadioGroup>暂时只支持魔点设备
              </Form-item>
              <Form-item label="接口ID">
                <Input v-model="postData.face_appid" />
              </Form-item>
              <Form-item label="设备ID">
                <Input v-model="postData.face_device_id" />
                <Button
                  @click="handleAddFace"
                  type="primary"
                  shape="circle"
                  icon="md-add"
                  style="margin-left:20px;"
                >添加</Button>
              </Form-item>
              <FormItem>
                <Table :columns="faceCols" :data="faceList" width="666"></Table>
              </FormItem>
              <Form-item label="舒华人脸">
                <RadioGroup v-model="postData.face_shuhua_switch">
                  <Radio label="1">开</Radio>
                  <Radio label="0">关</Radio>
                </RadioGroup>
              </Form-item>
            </div>
          </Panel>
          <Panel>
            一体机
            <div slot="content">
              <Form :label-width="150">
              <Form-item label="私教上课前置条件">
                <checkbox
                  v-model="postData.need_membership_sign"
                  true-value="1"
                  false-value="0"
                >会籍签到</checkbox>
              </Form-item>
              <Form-item label="私教签课入场">
                <RadioGroup v-model="postData.private_come">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                  <Radio label="2">指定私教卡可进</Radio>
                </RadioGroup>
                <div v-if="postData.private_come == 2">
                  <Select
                    v-model="postData.private_card_ids"
                    placeholder="请选择"
                    v-if="ptClassList"
                    clearable
                    filterable
                    multiple
                  >
                    <Option
                      v-for="item in ptClassList"
                      :key="item.card_id"
                      :value="item.card_id"
                    >{{ item.card_name }}</Option>
                  </Select>
                </div>
              </Form-item>
              <Form-item label="临时消课">
                <RadioGroup v-model="postData.aio_temp_pt">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="预约私教代课">
                <RadioGroup v-model="postData.aio_book_pt">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="上课课时判定">
                <RadioGroup v-model="postData.pt_start_condition_switch">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
                <div v-if="postData.pt_start_condition_switch == 1" class="tips-con">
                  <RadioGroup v-model="postData.pt_start_condition_type">
                    <Radio label="0">按课时间隔</Radio>
                    <Radio label="1">自定义时间</Radio>
                  </RadioGroup>
                  <Select class="w100" v-model="postData.pt_start_condition_time" placeholder="请选择">
                    <Option value="5">5</Option>
                    <Option value="10">10</Option>
                    <Option value="15">15</Option>
                    <Option value="20">20</Option>
                    <Option value="30">30</Option>
                    <Option value="40">40</Option>
                    <Option value="50">50</Option>
                    <Option value="60">60</Option>
                  </Select>分钟
                </div>
              </Form-item>
              <Form-item label="下课课时判定">
                <RadioGroup v-model="postData.pt_class_duration">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="隐私保护">
                <RadioGroup v-model="postData.is_private">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="选卡签到">
                <RadioGroup v-model="postData.aio_sign_mode">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="私教上课方式">
                <RadioGroup v-model="postData.private_cost_type">
                  <Radio label="0">上课</Radio>
                  <Radio label="1">下课</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="显示卡剩余量">
                <template slot="label">
                  <span style="vertical-align: middle;">显示卡剩余量</span>
                  <Tooltip
                    content="设置后需重启设备"
                    placement="top">
                    <icon size="16" type="ios-help-circle" color="#f4a627"/>
                  </Tooltip>
                </template>
                <!-- <RadioGroup v-model="postData.aio_card_surplus">
                  <Radio label="1">显示</Radio>
                  <Radio label="0">不显示</Radio>
                </RadioGroup> -->
                <Select v-model="postData.aio_card_surplus_data" style="width: 320px" multiple>
                  <Option value="2">次卡</Option>
                  <Option value="3">储值卡</Option>
                  <Option value="4">私教卡</Option>
                  <Option value="5">泳教卡</Option>
                </Select>
              </Form-item>
              <Form-item label="显示卡到期时间">
                <template slot="label">
                  <span style="vertical-align: middle;">显示卡到期时间</span>
                  <Tooltip
                    content="设置后需重启设备"
                    placement="top">
                    <icon size="16" type="ios-help-circle" color="#f4a627"/>
                  </Tooltip>
                </template>
                <!-- <RadioGroup v-model="postData.aio_card_endtime">
                  <Radio label="1">显示</Radio>
                  <Radio label="0">不显示</Radio>
                </RadioGroup> -->
                <Select v-model="postData.aio_card_endtime_data" style="width: 320px" multiple>
                  <Option value="1">期限卡</Option>
                  <Option value="2">次卡</Option>
                  <Option value="3">储值卡</Option>
                  <Option value="4">私教卡</Option>
                  <Option value="5">泳教卡</Option>
                </Select>
              </Form-item>
              </Form>
            </div>
          </Panel>
          <Panel>
            立式门禁机
            <div slot="content">
              <device-room-template
                v-for="(drData, index) in postData.guard_list"
                :key="index"
                @kidSay="handleReloadDeviceRoom"
                :dr-data="drData"
                :busId="postData.bus_id"
                :not-selected-room="postData.no_choose_classroom"
                :not-selected-device="postData.no_choose_device"
                :cardList="cardList"
              />
              <Button
                shape="circle"
                type="info"
                icon="md-add"
                @click="handleAddDeviceRoom"
                style="margin-bottom:20px"
              >添加门禁机</Button>
            </div>
          </Panel>
          <Panel>
            闸机
            <div slot="content">
              <gate-template
                v-for="(drData, index) in postData.gate_config_list"
                :key="'gate_template_'+index"
                @kidSay="handleReloadGate"
                :busId="postData.bus_id"
                :dr-data="drData"
                :not-selected-gate="postData.gate_choose_list"
                :cardList="cardList"
                :cameraDeviceList="cameraDeviceList"
                :certificationList="certificationList"
              ></gate-template>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddGate">添加闸机</Button>
            </div>
          </Panel>
          <Panel>
            佑久设备
            <div slot="content">
              <Form-item label="设备编号" v-if="showAddYoujiu">
                <Input placeholder="请输入设备编号" v-model="postData.youjiu_id" />
              </Form-item>
              <div style="margin-bottom:20px;" v-if="showAddYoujiu">
                <Button style="margin-left:120px;" @click="cancelAddYoujiu">取消</Button>
                <Button type="primary" style="margin-left:20px;" @click="submitAddYoujiu">保存</Button>
              </div>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddYoujiu">添加设备</Button>
            </div>
          </Panel>
          <Panel>
            柜控
            <div slot="content">
              <Form-item label="保持柜号">
                <RadioGroup v-model="postData.keep_cabinet">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="自动清柜">
                <RadioGroup v-model="postData.auto_clear_cabinet">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="员工租柜">
                <RadioGroup v-model="postData.worker_rent_cabinet">
                  <Radio label="1">开启</Radio>
                  <Radio label="0">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <template v-if="cleanAuth">
                <Form-item label="手动清洁">
                  <RadioGroup v-model="postData.hand_clean_cabinet">
                    <Radio label="1">开启</Radio>
                    <Radio label="0">关闭</Radio>
                  <Tooltip
                    content="会员退柜后（不含长租柜）进入待清洁状态（不可用）。清洁完成转为待使用。"
                    max-width="250"
                    placement="right"
                    transfer>
                    <icon size="16" type="ios-help-circle" color="#f4a627"/>
                  </Tooltip>
                  </RadioGroup>
                </Form-item>
                <div v-show="postData.hand_clean_cabinet == 1">
                  <Form-item label="保洁人员">
                    <Input
                      :value="cleanSearch"
                      clearable
                      @on-change="handleInputClean"
                      placeholder="搜索工作人员姓名/电话" />
                    <Button
                      @click="handleEditUsableDevice()"
                      type="primary"
                      shape="circle"
                      icon="md-add"
                      style="margin-left:20px;"
                    >添加</Button>
                  </Form-item>
                  <Form-item >
                    <Table :columns="cleanColumns" :data="filterCleanUserList" width="666"></Table>
                  </Form-item>
                  <Form-item>
                    <Button class="centerbut" shape="circle" @click="cleanQRModal = true">
                      <i class="qrcodeIcon"> </i>
                      <span>清洁二维码</span>
                    </Button>
                  </Form-item>
                </div>
              </template>
              <Form-item>
                <Button class="centerbut" shape="circle" @click="handleShowQr">
                  <i class="qrcodeIcon"> </i>
                  <span>退柜二维码</span>
                </Button>
              </Form-item>
              <!-- <Form-item label="扫码退柜二维码">
                <img
                  style="cursor:pointer"
                  @click="handleShowQr"
                  :src="imgQr"
                  width="100"
                  alt="扫码退柜二维码"
                  title="点击下载"
                />
              </Form-item> -->
            </div>
          </Panel>
          <Panel>
            云打印机
            <div slot="content">
              <CloudPrint
                v-for="(item, index) in postData.print_list"
                :key="index"
                @on-change="handleReloadGate"
                :data="item"
                v-model="postData.print_list[index]"
                :busId="postData.bus_id"
              ></CloudPrint>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddPrint">云打印机</Button>
            </div>
          </Panel>
          <Panel>
            团操一体机
            <div slot="content">
              <GroupClassMachine
                v-for="(item, index) in postData.group_class_machine_list"
                :key="index"
                :info="item"
                @on-change="handleReloadGate"
                :busId="postData.bus_id"
                :cardList="cardList"
                :not-selected-room="postData.no_choose_classroom"
              ></GroupClassMachine>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddClass">添加</Button>
            </div>
          </Panel>
          <Panel>
            灯控
            <div slot="content">
              <light-controller
                v-for="(item, index) in postData.light_control_rule"
                :key="index"
                :info="item"
                :busId="postData.bus_id"
                @on-change="handleReloadGate"
              ></light-controller>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddLight">添加</Button>
            </div>
          </Panel>
          <Panel>
            电子班牌
            <div slot="content">
              <ShiftScheduling
                v-for="(item, index) in postData.class_plate_list"
                :key="index"
                :info="item"
                @on-change="handleShiftScheduling"
                :busId="postData.bus_id"
                :not-selected-room="postData.no_choose_classroom"
                :not-selected-device="no_choose_device"
              ></ShiftScheduling>
              <Button shape="circle" type="info" icon="md-add" @click="handleAddShiftScheduling">添加</Button>
            </div>
          </Panel>
          <Panel>
            快诺优设置
            <div slot="content">
              <Form-item label="QuickNew">
                <RadioGroup v-model="postData.quick_new_switch" @on-change="quickNewChange">
                  <Radio label="1">启用</Radio>
                  <Radio label="2">关闭</Radio>
                </RadioGroup>
                <!-- <checkbox
                  v-if="postData.quick_new_switch === '1'"
                  style="margin-left: 30px"
                  v-model="postData.quick_new_is_sync_face"
                  true-value="1"
                  false-value="2"
                >同步人脸</checkbox> -->
              </Form-item>
              <Form-item label="QuickNewAppID">
                <Input v-model="postData.quick_new_app_id" :disabled="this.quickNewAppId !== ''" />
              </Form-item>
              <Form-item label="是否同步人脸">
                <RadioGroup v-model="postData.quick_new_is_sync_face">
                  <Radio label="1" :disabled="postData.quick_new_switch == 2">启用</Radio>
                  <Radio label="2">关闭</Radio>
                </RadioGroup>
              </Form-item>
              <FormItem>
                <Button
                  @click="handleAddQuick"
                  type="primary"
                  shape="circle"
                  icon="md-add"
                >添加</Button>
              </FormItem>
              <FormItem>
                <Alert style="width: 666px;">智能手环机</Alert>
                <span style="margin-right: 10px;">设备列表</span><Button size="small" style="font-size: 12px;" @click="getConfig(busId)">刷新</Button>
                <Table :columns="quickCols" :data="quickList" width="666"></Table>
              </FormItem>

              <Modal
                v-model="quickDialogFlag"
                title="Quick New">
                <Form :model="quickForm" :rules="quickRules" ref="quickRef" label-position="right" :label-width="140">
                  <FormItem label="设备编号" prop="sn">
                    <Input v-model="quickForm.sn" :disabled="quickForm.quick_new_device_id!==''"></Input>
                  </FormItem>
                  <FormItem label="设备类型" prop="type">
                    <Select v-model="quickForm.type">
                      <Option v-for="item in quickTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                  </FormItem>
                  <FormItem label="设备名称" prop="name">
                    <Input v-model="quickForm.name"></Input>
                  </FormItem>
                  <FormItem label="自动签到" v-if="quickForm.type !== '4'">
                    <i-Switch v-model="quickForm.is_auto_sign" true-value="1" false-value="2">
                      <template #open>
                        <span>开</span>
                      </template>
                      <template #close>
                        <span>关</span>
                      </template>
                    </i-Switch>
                  </FormItem>
                  <FormItem label="自动核票" v-if="quickForm.type !== '4'">
                    <i-Switch v-model="quickForm.is_auto_wicket" true-value="1" false-value="2">
                      <template #open>
                        <span>开</span>
                      </template>
                      <template #close>
                        <span>关</span>
                      </template>
                    </i-Switch>
                  </FormItem>
                </Form>
                <div slot="footer" class="modal-buttons">
                  <Button type="success" @click="handleQuickSubmit">确定</Button>
                  <Button @click="handleQuickCancel">取消</Button>
                </div>
              </Modal>
            </div>
          </Panel>
          <Panel>
            刷掌配置
            <div slot="content">
              <Form-item label="优先显示">
                <RadioGroup v-model="postData.hand_sort">
                  <Radio :label="1">开启</Radio>
                  <Radio :label="0">关闭</Radio>
                </RadioGroup>
                <div class="tips-info">开启后微信小程序注册时，场馆确认时优先排序</div>
              </Form-item>
              <Form-item label="亲友开掌">
                <RadioGroup v-model="postData.assist_open">
                  <Radio :label="1">开启</Radio>
                  <Radio :label="0">关闭</Radio>
                </RadioGroup>
                <div class="tips-info">开启后亲友可以通过小程序开通微信刷掌，前提是商家申请过该功能</div>
              </Form-item>
              <Form-item label="空中开掌">
                <RadioGroup v-model="postData.firmament_open">
                  <Radio :label="1">开启</Radio>
                  <Radio :label="0">关闭</Radio>
                </RadioGroup>
                <div>
                  <Button v-if="postData.firmament_open == 1" class="centerbut" shape="circle" @click="handleShowPalmQr">
                    <i class="qrcodeIcon"> </i>
                    <span>开通页二维码</span>
                  </Button>
                </div>
                <div class="tips-info">开启后本人可以通过小程序开通微信刷掌，前提是商家申请过该功能</div>
              </Form-item>
            </div>
          </Panel>
        </Collapse>

        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="submit">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
    <Modal v-model="showPalmQr" width="300">
      <img :src="palmQr" style="width: 100%" alt />
      <div slot="footer" class="modal-buttons">
        <a v-if="palmQr" download="空中开掌二维码" :href="palmQr">
          <Button type="success">印刷尺寸下载</Button>
        </a>
      </div>
    </Modal>
    <Modal v-model="showQr" :mask-closable="true" :width="$store.state.is_qn_j == 1 ? '540' : '280'" title="退柜二维码" @on-cancel="()=>{showQr = false;}">
      <div class="qrModalbody">
        <div class="qrModalContent">
          <img class="image" :src="imgQr" width="240" height="240" />
          <p>生活运动管家</p>
          <a v-if="imgQr" download="运动生活管家" :href="imgQr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
        <div class="line" v-if="$store.state.is_qn_j == 1"></div>
        <div class="qrModalContent" v-if="$store.state.is_qn_j == 1">
          <img class="image" :src="otherImgQr" width="240" height="240" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="otherImgQr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <Modal
      v-model="cleanQRModal"
      width="440"
      title="清洁二维码"
      @on-cancel="cleanQRModal = false">
      <div class="qrModalbody" style="justify-content:center">
        <div class="qrModalContent">
          <img class="image" :src="cleanImgQr" width="240" height="240" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="cleanImgQr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <Modal
      v-if="cleanAuth"
      v-model="cleanModal"
      :mask-closable="true"
      title="可开柜设备">
      <div class="clean-contain">
        <Form :label-width="80">
          <Form-Item label="保洁人员">
            <div v-if="cleanMsName">
              {{ cleanMsName }}
            </div>
            <Select
              v-else
              v-model="cleanMsId"
              placeholder="搜索工作人员姓名"
              clearable
              filterable>
              <Option
                v-for="item in cleanMarketerList"
                :key="item.marketers_id"
                :value="item.marketers_id"
                :label="item.sale_name + (item.disabled ? ' (已添加)' : '')"
                :disabled="item.disabled"
              />
            </Select>
          </Form-Item>
          <Form-Item label="可开柜设备">
            <Select v-model="cleanCabinet" :multiple="true" filterable>
              <Option v-for="clean in cleanCabinetOptions" :key="clean.device_id" :value="clean.device_id" :label="clean.device_name">{{ clean.device_name }}</Option>
            </Select>
          </Form-Item>
        </Form>
      </div>
      <div slot="footer">
        <Button @click="cleanModal = false">取消</Button>
        <Button type="success" @click="handleSaveDevice">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import DeviceRoomTemplate from './deviceRoomTemplate.vue'
import GateTemplate from './gateTemplate.vue'
import CloudPrint from './components/CloudPrint.vue'
import GroupClassMachine from './components/GroupClassMachine.vue'
import LightController from './components/LightController.vue'
import ShiftScheduling from './components/ShiftScheduling.vue'
import { debounce } from 'lodash-es';
import { getsalesInfo } from '../../service/getData.js';

import { mapGetters } from 'vuex'
export default {
  name: 'valueCardConfig',
  components: {
    DeviceRoomTemplate,
    CloudPrint,
    GroupClassMachine,
    GateTemplate,
    LightController,
    ShiftScheduling
  },

  data() {
    // 设备类型（1：自助收发一体机，2：自助发放机，3：自助回收机）
    const quickTypeList = [
      { label: '自助收发一体机', value: '1' },
      { label: '自助发放机', value: '2' },
      { label: '自助回收机', value: '3' },
      { label: '票务机', value: '4' },
    ]
    return {
      showAddYoujiu: false,
      disabledAppid: false,
      showQr: false,
      showPalmQr: false,
      palmQr: '',
      open_time_radio: '',
      imgQr: '',
      otherImgQr: '',
      open_time: [],
      timeRanges: [[]],
      postData: {
        youjiu_id: '',
        sign_interval: '3',
        bus_id: '',
        used_groups: '',
        appid: '',
        open_time: '0',
        qrcode_gate: 0,
        qrcode_gate_id: '',
        billing_on_time_switch: '',
        enter_type: '',
        leave_rule: '',
        cabinet_used_groups: '',
        leave_rule_type: '',
        cabinent_delayed_time: '0',
        face_switch: '',
        face_shuhua_switch: '0',
        name_private_switch: '0',
        face_nfc_switch: '',
        face_check: '',
        face_appid: '',
        face_device_id: '',
        guard_list: [],
        gate_choose_list: [],
        gate_config_list: [],
        group_class_machine_list: [],
        light_control_rule: [],
        print_list: [],
        no_choose_classroom: [],
        no_choose_device: [],
        gate_auto_sign: '0',
        gate_auto_active: '0',
        keep_cabinet: 0,
        auto_clear_cabinet: 0,
        worker_rent_cabinet: '0',
        hand_clean_cabinet: '0', // 手动清洁设置 1开启0关闭 默认关闭
        cabinet_clean_staff_list: [
          // 提交格式需要为 { marketers_id: '工作人员ID', device_ids: ['用数组形式传输 device_ids[0]:123456'] }
        ],
        aio_temp_pt: 0,
        need_membership_sign: '',
        private_come: '',
        private_card_ids: [],
        aio_book_pt: 0,
        pt_start_condition_switch: '0',
        pt_start_condition_type: '0',
        pt_start_condition_time: '5',
        pt_class_duration: '0',
        is_private: 0,
        aio_sign_mode: 0,
        private_cost_type: 0,
        // aio_card_surplus: '1', // 卡剩余量 显示1 不显示0，暂时不要了，直接展示选项
        aio_card_surplus_data: [], // 需显示的卡类型
        // aio_card_surplus: '1', // 卡到期时间 显示1 不显示0
        aio_card_endtime_data: [], // 需显示的卡类型
        nfc_switch: '',
        hand_rfid: '',
        qr_code_type: 0,
        class_plate_list: [],
        quick_new_switch: '',
        quick_new_is_sync_face: '',
        quick_new_app_id: '',
        hand_sort: 0,
        assist_open: 0,
        firmament_open: 0,
        leave_cabinet_group: 'A',
      },
      no_choose_device: [],
      cardList: [],
      certificationList: [],
      faceCols: [
        { title: '序号', width: 50, render: (h, params) => (<div>{params.index + 1}</div>) },
        { title: '设备ID', key: 'deviceId' },
        { title: '验证提示语', key: 'tips' },
        {
          title: '操作', width: 100, render: (h, params) => {
            return (<div style="display:flex;flex-direction:row;">
              <Button type="text" size="small" onClick={() => {
                this.$Modal.confirm({
                  title: '编辑验证提示语',
                  content: `<input id='faceInput' value="${params.row.tips}" style='border-radius:4px;border:1px solid #e0e3e9;padding:4px 7px;width:300px;' />`,
                  onOk: () => {
                    const ipt = document.getElementById('faceInput')
                    this.$service.post('/Vein/Hardware/setFaceDeviceConf', {
                      device_id: params.row.deviceId,
                      tips: ipt.value
                    }).then(res => {
                      if (res.data.errorcode == 0) {
                        this.getConfig(this.postData.bus_id)
                        this.$Message.info('编辑成功！')
                      } else { this.$Message.error(res.data.errormsg) }
                    })
                  },
                  onCancel: () => {
                    // this.$Message.info('Clicked cancel');
                  }
                })
              }} ghost>编辑</Button>
              <Button type="text" size="small" style="color:red;" onClick={() => {
                this.$Modal.confirm({
                  title: '删除',
                  content: `<p>确定要删除${params.row.deviceId}这台设备？</p>`,
                  onOk: () => {
                    this.$service.post('/Vein/Hardware/deleteFaceDevice', {
                      bus_id: this.postData.bus_id,
                      device_id: params.row.deviceId
                    }).then(res => {
                      if (res.data.errorcode == 0) {
                        this.getConfig(this.postData.bus_id)
                        this.$Message.info('删除成功！')
                      } else { this.$Message.error(res.data.errormsg) }
                    })
                  },
                  onCancel: () => {
                    // this.$Message.info('Clicked cancel');
                  }
                })
              }} ghost>删除</Button>
            </div>)
          }
        }
      ],
      faceList: [],
      quickCols: [
        { title: '序号', width: 50, render: (h, params) => (<div>{params.index + 1}</div>) },
        { title: '设备编号', key: 'sn' },
        { title: '设备类型', key: 'type', render: (h, params) => {
          const item = quickTypeList.find(item => item.value == params.row.type)
          if (item) {
            return <span>{item.label}</span>
          } else {
            return <span>-</span>
          }
        }},
        { title: '设备名称', key: 'name' },
        // { title: '手环剩余数量', key: 'count' },
        { title: '自动签到', key: 'is_auto_sign', render: (h, params) => {
            return params.row.type === '4' ? '' : (<i-Switch v-model={params.row.is_auto_sign} onOn-change={() => {
              this.quickForm = {...params.row}
              this.handleQuickSubmit()
            }} true-value="1" false-value="2"></i-Switch>)
          }},
        { title: '自动核票', key: 'is_auto_wicket', render: (h, params) => {
          return  params.row.type === '4' ? '' : (<i-Switch v-model={params.row.is_auto_wicket} onOn-change={() => {
            this.quickForm = {...params.row}
            this.handleQuickSubmit()
          }} true-value="1" false-value="2"></i-Switch>)
        }},
        {
          title: '操作', width: 100, render: (h, params) => {
            return (<div style="display:flex;flex-direction:row;">
              <Button type="text" size="small" onClick={() => {
                this.quickForm = {...params.row}
                this.quickDialogFlag = true
              }} ghost>编辑</Button>
              <Button type="text" size="small" style="color:red;" onClick={() => {
                this.$Modal.confirm({
                  title: '删除',
                  content: `<p>确定要删除"${params.row.name}"这台设备？</p>`,
                  onOk: () => {
                    this.$service.post('/Web/QuickNew/delDevice', {
                      quick_new_device_id: params.row.quick_new_device_id
                    }).then(res => {
                      if (res.data.errorcode == 0) {
                        this.getConfig(this.postData.bus_id)
                        this.$Message.info('删除成功！')
                      } else {
                        this.$Message.error(res.data.errormsg)
                      }
                    })
                  }
                })
              }} ghost>删除</Button>
            </div>)
          }
        }
      ],
      quickList: [],
      quickDialogFlag: false,
      quickNewAppId: '',
      quickForm: {
        quick_new_device_id: '',
        bus_id: '',
        sn: '',
        type: '',
        name: '',
        is_auto_sign: '2',
        is_auto_wicket: '2',
      },
      quickRules: {
        sn: [
          { required: true, message: '请输入设备编号' }
        ],
        type: [
          { required: true, message: '请选择设备类型' }
        ],
        name: [
          { required: true, message: '请输入设备名称' }
        ]
      },
      quickTypeList,

      cleanAuth: false, // 手动清洁权限
      cleanColumns: [
        { title: '姓名', key: 'name' },
        { title: '电话', key: 'phone', width: 120 },
        {
          title: '可开柜设备',
          key: 'device',
          render:   (h, { row }) => {
            return (<div>
              {
                Array.isArray(row.device_names) && row.device_names.length
                  ? row.device_names.map(item => item.device_name).join('、')
                  : '--'
              }
            </div>)
        }
        },
        {
          title: '操作', width: 100, render: (h, { row }) => {
            return (<div style="display:flex;flex-direction:row;">
              <Button
              type="text"
              size="small"
              ghost
              onClick={() => { this.handleEditUsableDevice(row) }}>
                编辑
              </Button>
              <Button
              type="text"
              size="small"
              style="color:red;"
              ghost
              onClick={() => { this.handleDeleteCleaner(row) }}>
                删除
              </Button>
            </div>)
          }
        }
      ],
      cleanModal: false, // 清洁人员设置弹窗
      cleanQRModal: false, // 会员端清洁页面二维码弹窗
      cleanImgQr: '', // 二维码地址
      cleanSearch: '',
      cleanMsId: '', // 清洁柜控人员id
      cleanMsName: '', // 清洁柜控人员
      cleanMarketerList: [], // 清洁柜控人员选项
      cleanCabinet: [],
      cleanCabinetOptions: [],
      cameraDeviceList: [],
    }
  },
  computed: {
    ...mapGetters(['busList', 'busId']),
    ptClassList() {
      return this.cardList.filter((item) => (item.card_type_id === '4' || item.card_type_id === '5'))
    },
    filterCleanUserList() {
      const search = this.cleanSearch

      return search
        ? this.postData.cabinet_clean_staff_list.filter(v => {
          return v.name.includes(search) || v.phone.includes(search)
        })
        : this.postData.cabinet_clean_staff_list

    }
  },

  created() {
    this.getCardList()
    this.getCertificationList()
    this.getCameraDeviceList()
    this.postData.bus_id = this.busId
    this.busChange(this.busId)
  },

  methods: {
    getCameraDeviceList() {
      this.$service
        .post("/Web/Business/get_camera_device_list")
        .then(res => {
          this.cameraDeviceList = res.data.data.list;
        })
        .catch(err => {
          this.$Message.error(err);
        });
    },
    handleShowQr() {
      this.showQr = true
    },
    handleShowPalmQr() {
      this.$service.post('/Web/Business/getQnQrSkyPalmserviceUrl', {
        bus_id: this.postData.bus_id
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.palmQr = res.data.data
          this.showPalmQr = true
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getImgQr(bus_id) {
      this.$service.post('/Web/Business/getQrCabinetBackUrl', {
        bus_id
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.imgQr = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
      this.$service.post('/Web/Business/getQnQrCabinetBackUrl', {
        bus_id
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.otherImgQr = res.data.data
        } else {
          if(this.$store.state.is_qn_j == 1){
            this.$Message.error(res.data.errormsg)
          }
        }
      })
      this.$service.post('/Web/Business/getQnQrCabinetCleanUrl', {
        bus_id
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.cleanImgQr = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getCardList() {
      this.$service
        .get('/Web/Member/get_all_card')
        .then(res => {
          if (res.status == 200) {
            if (res.data.errorcode == 0) {
              this.cardList = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    },
    getCertificationList() {
      this.$service.post('/web/Document/getDocumentType').then(res => {
        if (res.data.errorcode === 0) {
          this.certificationList = res.data.data
        }
      })
    },
    // 获取手动清洁权限
    getCleanAuth(bus_id) {
      return this.$service.post('/Web/Business/getNodeList', { bus_id }).then(res => {
        if (res.data.errorcode === 0) {
          this.cleanAuth = res.data.data.HandCleanCabinet
        }
        return res
      })
    },
    // 获取智能柜列表
    getLockerList(bus_id) {
      // Web/Business/get_cabint_device
      // Web/LockerRent/getNewLockerRentList
      // Web/Business/getAllDevices
      this.$service.post('Web/Business/getAllDevices', { bus_id }).then(res => {
        if(res.data.errorcode == 0) {
          this.cleanCabinetOptions = res.data.data;
        }
      })
    },
    // 获取会籍列表
    getMarketerList(bus_id) {
      getsalesInfo(bus_id).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data || []
          list.forEach(v => {
            v.disabled = false // 用于判断是否已添加
          })
          this.cleanMarketerList = list;
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    busChange(bus_id) {
      if (!bus_id) return
      this.getConfig(bus_id)
      this.getImgQr(bus_id)
      // 获取手动清洁权限
      this.getCleanAuth(bus_id).then(() => {
      if (this.cleanAuth) {
        // 获取清洁人员设置选项
        this.getLockerList(bus_id)
        this.getMarketerList(bus_id)
      }
    })
    },
    submit() {
      this.postData.qrcode_gate = this.postData.qrcode_gate != 0 ? this.postData.qrcode_gate_id : 0

      if (this.open_time_radio === '1') {
        if (!this.timeRanges[0] || !this.timeRanges[0].length || !this.timeRanges[0][0] || !this.timeRanges[0][1]) {
          this.$Message.error('请至少设置一个营业时间段')
          return Promise.reject(false)
        }

        // Check for time range intersections
        const validRanges = this.timeRanges.filter(range => range && range.length === 2 && range[0] && range[1]);
        // Check that start time is not equal to end time in each range
        for (const range of validRanges) {
          const [start, end] = range;
          if (start === end) {
            this.$Message.error('时间段的开始时间和结束时间不能相同');
            return Promise.reject(false);
          }
        }

        for (let i = 0; i < validRanges.length; i++) {
          for (let j = i + 1; j < validRanges.length; j++) {
            const [startA, endA] = validRanges[i];
            const [startB, endB] = validRanges[j];
            
            if ((startA <= startB && endA > startB) || (startB <= startA && endB > startA)) {
              this.$Message.error({
                content: `${startA}-${endA} 和 ${startB}-${endB} 时间段不能重叠，请重新设置`,
                duration: 5
              });
              return Promise.reject(false);
            }
          }
        }

        // 将多个时间段转换为字符串格式
        this.postData.open_time = this.timeRanges
          .filter(range => range && range.length === 2 && range[0] && range[1])
          .map(range => range.join('-'))
          // .join(',')
      } else {
        this.postData.open_time = '0'
      }
      if(this.postData.leave_rule == 0 && this.postData.cabinet_used_groups.length == 0) {
        this.$Message.error('请选择闸机分组')
        return Promise.reject(false)
      }
      if (this.postData.leave_rule_type == 0) {
        this.postData.cabinent_delayed_time = '0'
      }

      return this.$service
        .post('/Web/Business/Sjs_common_set_config', this.postData)
        .then(res => {
          this.postData.qrcode_gate = this.postData.qrcode_gate != 0 ? '1' : '0'
          this.$Message.success(res.data.errormsg)
          this.$store.dispatch('getAdminInfo')
          return res
        })
        .catch(err => {
          this.postData.qrcode_gate = this.postData.qrcode_gate != 0 ? '1' : '0'
          this.$Message.error(err)
        })
    },
    getConfig(bus_id) {
      this.$service
        .post('/Web/Business/Sjs_common_get_config', { bus_id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.postData = Object.assign(this.postData, res.data.data)

            this.postData.leave_rule_type = Number(res.data.data.leave_rule_type || 0)
            this.disabledAppid = res.data.data.appid ? true : false
            this.postData.qrcode_gate_id = this.postData.qrcode_gate != 0 ? this.postData.qrcode_gate : ''
            this.postData.qrcode_gate = this.postData.qrcode_gate != 0 ? '1' : '0'
            
            this.open_time_radio = this.postData.open_time == 0 ? '0' : '1'
            // this.open_time = this.open_time_radio === '1' && this.postData.open_time.split('-')
            if (this.open_time_radio === '1' && Array.isArray(this.postData.open_time)) {
              // 将字符串格式的时间段转换为数组格式
              // this.timeRanges = this.postData.open_time.split(',').map(range => range.split('-'))
              this.timeRanges = this.postData.open_time.map(range => range.split('-'))
            }

            this.postData.cabinent_delayed_time = this.postData.cabinent_delayed_time || '0';
            this.postData.guard_list.forEach(item => {
              item.action = 'query'
              if (item.allow_card) {
                item.allow_card = item.allow_card.split(',')
              } else {
                item.allow_card = []
              }
            })
            this.postData.gate_config_list.forEach(item => {
              item.action = 'query'
              if (!item.card_str || item.card_str === '') {
                item.allow_card = []
              } else {
                item.allow_card = item.card_str.split(',')
              }
              item.allow_space = item.space_str
              if(item.device_type === '1' || item.device_type === '2' || item.device_type === '3') {
                item.device_type = Number(item.device_type)
              }
              // if(item.gate_type === '0' || item.gate_type === '1') {
              //   item.gate_type = Number(item.gate_type)
              // }
              if(!item.gate_type) {
                item.gate_type = '0'
              }
              if(item.is_st_relation === '0' || item.is_st_relation === '1') {
                item.is_st_relation = Number(item.is_st_relation)
              }
              if(item.private_auto_sign === '0' || item.private_auto_sign === '1') {
                item.private_auto_sign = Number(item.private_auto_sign)
              }
              if(item.voluntarily_class_sign === '0' || item.voluntarily_class_sign === '1') {
                item.voluntarily_class_sign = Number(item.voluntarily_class_sign)
              }
              if(item.voluntarily_ratify === '0' || item.voluntarily_ratify === '1') {
                item.voluntarily_ratify = Number(item.voluntarily_ratify)
              }
              if(item.member_limit === '0' || item.member_limit === '3' || item.member_limit === '4' || item.member_limit === '5') {
                item.member_limit = Number(item.member_limit)
              }
              // 初始化voluntarily_class_sign的值也为0
              // 防止这次改动之前的数据 private_auto_sign为关闭时,voluntarily_class_sign后端默认为开启
              // voluntarily_class_sign后端默认返回为数字  实际上需要格式化为字符串
              // 2023/09/19 这行处理移至890行
              // if( item.private_auto_sign == '0'){
              //   item.voluntarily_class_sign == '0'
              // } else {
              //   item.voluntarily_class_sign = item.voluntarily_class_sign + ''
              // }
              // 编辑时: 处理切换到场馆闸机 商汤鉴权/自动签到/会员限制自动核票没有默认值
              if(item.private_auto_sign !== 0 && item.private_auto_sign !== 1) {
                item.private_auto_sign = 0
                item.voluntarily_class_sign = 0
              } else {
                if( item.private_auto_sign === 0){
                  item.voluntarily_class_sign = 0
                }
              }
              if(item.voluntarily_ratify !== 0 && item.voluntarily_ratify !== 1) {
                item.voluntarily_ratify = 0
              }
              if(item.member_limit !== 0 && item.member_limit !== 3 && item.member_limit !== 4 && item.member_limit !== 5) {
                item.member_limit = 0
              }
            })

            // look my face.
            this.faceList = res.data.data.face_device_lists

            // quick new
            this.quickList = res.data.data.quick_new_device_list
            this.quickNewAppId = res.data.data.quick_new_app_id

            // light bulb
            this.postData.light_control_rule.forEach(item => {
              item.lightStatus = item.is_on ? 'online' : 'offline'
              item.lineWayList = []
              item.ruleLists.forEach(line => {
                const index = Number(line.index)
                line.lineWay = index
                item.lineWayList.push(index)
                line.roomId = line.space_data.map(i => i.space_id + '_' + i.position)
              })
            })

            // clean
            if (!Array.isArray(res.data.data.cabinet_clean_staff_list)) {
              this.postData.cabinet_clean_staff_list = []
            }

            // 获取电子班牌硬件列表
            this.$service.post('/Web/ClassPlate/getClassPlateDeviceList', { bus_id }).then(res=>{
              if (res.data.errorcode === 0) {
                this.no_choose_device = res.data.data || []
              } else {
                this.$Message.error(err)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    },
    handleAddDeviceRoom() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.postData.guard_list.push({
        action: 'save',
        device_type: 1,
        device_id: '',
        class_id: '',
        voluntarily_ratify: 0,
        member_limit: 0,
        time_limit: '',
        allow_card: [],
        start_begin: 30,
        end_after: 10,
        access_auto_sign: '0'
      })
    },
    handleAddGate() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      // 添加闸机时,自动签到为关闭则自动团课签到也默认为关闭
      this.postData.gate_config_list.push({
        action: 'save',
        device_id: '',
        st_device_id: '',
        private_auto_sign: 0,
        voluntarily_class_sign: 0,
        voluntarily_ratify: 0,
        allow_card: [],
        device_type: 1,
        is_st_relation: 0,
        member_limit: 0,
        gate_type: '0',
        out_device_relevance: '0',
        in_door_flow: {
          camera_device_anti_follow: '0',
          camera_device_head_count: 1,
          camera_device_id: ''
        },
        out_door_flow: {
          camera_device_anti_follow: '0',
          camera_device_head_count: 0,
          camera_device_id: ''
        },
        pass_bus_ids: [],
      })
    },
    handleAddClass() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.postData.group_class_machine_list.push({
        id: '',
        action: 'save',
        device_id: '',
        member_limit: 0,
        time_limit: '',
        allow_card: [],
        start_begin: 30,
        end_after: 10,
        classroom_id: ''
      })
      console.log(this.postData.group_class_machine_list)
    },
    handleAddLight() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.postData.light_control_rule.push({
        action: 'add',
        id: null,
        auto_mode: '1',
        device_name: '',
        device_num: '',
        all_on_cmd: '',
        all_off_cmd: '',
        template_id: '',
        lightStatus: 'offline',
        lineWayList: [],
        ruleLists: []
      })
    },
    handleAddPrint() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.postData.print_list.push({
        action: 'save',
        device_type: '0',
        device_sn: '',
        pt_ticket: '0',
        member_ticket: '0',
        scan_ticket: '0',
        booking_proof_ticket: '0',
        scan_ticket_range: '0',
        paper_size: '0',
        paper_length: 250
      })
    },
    handleReloadDeviceRoom() {
      this.getConfig(this.postData.bus_id)
    },
    handleReloadGate() {
      this.getConfig(this.postData.bus_id)
    },
    handleAddYoujiu() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.showAddYoujiu = true
    },
    cancelAddYoujiu() {
      this.postData.youjiu_id = ''
      this.showAddYoujiu = false
    },
    submitAddYoujiu() {
      let postData = Object.create(null)
      postData.youjiu_id = this.postData.youjiu_id
      postData.bus_id = this.postData.bus_id
      this.$service.post("/Web/Business/set_youjiu_config", postData).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.$Message.success("设备添加成功！")
            this.showAddYoujiu = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        } else {
          this.$Message.error("网络有问题！")
        }
      }).catch(err => {
        console.error(err)
      })
    },
    handleAddFace() {
      if (this.postData.face_device_id.length === 0) {
        return false
      }
      return this.$service.post('/Vein/Hardware/addFaceDevice', {
        bus_id: this.postData.bus_id,
        device_id: this.postData.face_device_id
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.getConfig(this.postData.bus_id)
          this.$Message.info('添加设备成功！')
        } else { this.$Message.error(res.data.errormsg) }
      })
    },
    getFaceList() {
      return this.$service.post('', {}).then(res => {
        if (res.data.errorcode == 0) {

        } else { this.$Message.error(res.data.errormsg) }
      })
    },
    handleShiftScheduling() {
      this.getConfig(this.postData.bus_id)
    },
    handleAddShiftScheduling() {
      if (this.postData.bus_id.length === 0) {
        this.$Message.error('请选择场馆！')
        return false
      }
      this.postData.class_plate_list.push({
        action: 'add',
        id: null,
        device_id: '',
        classroom_id: '',
      })
    },
    resetQuickForm() {
      this.quickForm = {
        quick_new_device_id: '',
        bus_id: '',
        sn: '',
        type: '',
        name: '',
        is_auto_sign: '2',
        is_auto_wicket: '2',
      }
    },
    handleAddQuick() {
      this.resetQuickForm()
      this.quickDialogFlag = true
    },
    handleQuickCancel() {
      this.resetQuickForm()
      this.$refs.quickRef.resetFields()
      this.quickDialogFlag = false
    },
    handleQuickSubmit() {
      let url = '/Web/QuickNew/addDevice'
      if (this.quickForm.quick_new_device_id) {
        url = '/Web/QuickNew/editDevice'
      }
      this.quickForm.bus_id = this.busId;
      this.$refs.quickRef.validate(flag => {
        if (flag || !this.quickDialogFlag) {
          return this.$service.post(url, this.quickForm).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              this.resetQuickForm()
              this.$refs.quickRef.resetFields()
              this.quickDialogFlag = false
              this.getConfig(this.busId)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    quickNewChange(val) {
      if (val == 1) {
        this.postData.quick_new_is_sync_face = '1'
      } else {
        this.postData.quick_new_is_sync_face = '2'
      }
    },
    // 搜索已添加的清洁人员
    handleInputClean: debounce(function(event) {
      this.cleanSearch = event.target.value
    }, 700),
    // 打开添加/编辑清洁人员弹窗
    handleEditUsableDevice(row) {
      if (row) {
        this.cleanCabinet = Array.isArray(row.device_names) ? row.device_names.map(({ device_id }) => device_id) : []
        this.cleanMsId = row.marketers_id;
        this.cleanMsName = row.name;
      } else {
        this.cleanCabinet = []
        this.cleanMsId = ''
        this.cleanMsName = '';
      }

      // 禁用已添加的人员
      const list = this.postData.cabinet_clean_staff_list
      if (list.length) {
        const msIds = list.map(v => v.marketers_id)
        this.cleanMarketerList.forEach(v => {
          v.disabled = msIds.includes(v.marketers_id)
        })
      }

      this.cleanModal = true;
    },
    // 添加/编辑清洁人员
    async handleSaveDevice() {
      const { cleanMsId } = this
      if (!this.cleanMsId) {
        return this.$Message.warning('请选择工作人员')
      }

      this.$service.post('/Web/LockerRent/addCleanStaff', {
        bus_id: this.postData.bus_id,
        marketers_id: cleanMsId,
        device_ids: this.cleanCabinet
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('设置成功！')
          this.cleanModal = false;
          if (res.data.data) {
            const { cabinet_clean_staff_list: list } = this.postData
            const index = list.findIndex(v => v.marketers_id == cleanMsId)
            if (index !== -1) {
              this.$set(list, index, res.data.data)
            } else {
              list.push(res.data.data)
            }
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 删除清洁人员
    handleDeleteCleaner(row) {
      this.$Modal.confirm({
        title: '删除保洁人员',
        content: `<p>确定要删除：${row.name} ?</p>`,
        onOk: async () => {
          this.$service.post('/Web/LockerRent/delCleanStaff', {
            bus_id: this.postData.bus_id,
            marketers_id: row.marketers_id,
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              const index = this.postData.cabinet_clean_staff_list.findIndex(v => v.marketers_id == row.marketers_id)
              if (index !== -1) {
                this.postData.cabinet_clean_staff_list.splice(index, 1)
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        },
        onCancel: () => { }
      })
    },
    addTimeRange() {
      if (this.timeRanges.length < 5) {
        if (this.timeRanges.length === 0) {
          this.timeRanges.push(['08:00', '10:00'])
        } else {
          const last = this.timeRanges[this.timeRanges.length - 1]
          if (!last[1]) {
            this.$Message.warning(`请先设置第${this.timeRanges.length}个时间段`)
            return
          }
          this.timeRanges.push([])
        }
      }
    },
    removeTimeRange(index) {
      this.$Modal.confirm({
        title: '删除确认',
        content: `确定要删除第 ${index + 1} 个时间段吗？`,
        onOk: () => {
          this.timeRanges.splice(index, 1)
        }
      })
    },
    sortTimeRanges() {
      // Sort time ranges based on start time
      this.timeRanges.sort((a, b) => {
        // Extract hours and minutes from start time (first element in each range)
        const aStartTime = a[0];
        const bStartTime = b[0];
        
        // If either time is not set, move it to the end
        if (!aStartTime) return 1;
        if (!bStartTime) return -1;
        
        // Convert to minutes for easier comparison
        const aMinutes = Number(aStartTime.replace(":", ""));
        const bMinutes = Number(bStartTime.replace(":", ""));
        
        return aMinutes - bMinutes;
      });
      
      this.$Message.success('时间段已按开始时间排序');
    },
  },
}
</script>
<style lang="less" scoped>
.tips-con {
  font-size: 12px;
}
.form-box .min-input {
  width: 50px;
  margin-right: 5px;
}

.area-line {
  height: 60px;
  line-height: 60px;
  vertical-align: middle;
  width: 100%;
  border-top: 1px dashed black;
  font-weight: bold;
  font-size: 18px;
}

.qrcode /deep/ .ivu-form-item-content {
  display: flex;
  .qrcodeid {
    display: flex;
  }
}
.qrcodeid /deep/ .ivu-form-item-label {
  width: 100px !important;
  padding-right: 15px;
}
.qrcodeid /deep/ .ivu-input-wrapper {
  width: 150px;
}
.centerbut,
.centerbut /deep/ span {
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.qrcodeIcon {
  display: inline-block;
  width: 17px;
  height: 17px;
  margin-right: 5px;
  background: url('~assets/img/qrcode.png') no-repeat;
  background-size: contain;
}
.qrModalbody {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .qrModalContent{
    width: 240px;
    height: 353px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }
  .line {
    width: 1px;
    height: 353px;
    background: rgba(#333, .3);
  }
}
.tips-info {
  font-size: 12px;
  color: #888;
}

.time-range-list {
  margin-top: 20px;
  
  .time-range-item {
    display: flex;
    margin-bottom: 10px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.inner-time-picker {
  width: 200px;

  /deep/ .ivu-input-wrapper {
    width: 180px;
  }
}
</style>
