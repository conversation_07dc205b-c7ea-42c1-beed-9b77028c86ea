<template>
  <div class="container">
    <header><h3>柜号分配规则</h3></header>
    <Form class="form" :label-width="140">
      <Card style="margin: 30px" v-for="(rule, index) in list" :key="index">
        <FormItem label="选择卡种">
          <Select :disabled="rule.disabled" multiple filterable v-model="rule.cards" placeholder="请选择会员卡">
            <Option :disabled="usedCard.includes(card.card_id)" v-for="card in cardList" :key="card.card_id" :value="card.card_id" :label="card.card_name"></Option>
          </Select>
        </FormItem>
        <div v-for="(device, deviceIndex) in rule.deviceList" :key="deviceIndex">
          <Divider dashed></Divider>
          <FormItem label="中控设备">
            <div style="display: flex">
              <Select :disabled="rule.disabled" @on-change="onDeviceChange(index, deviceIndex, device.deviceId)"
                      v-model="device.deviceId">
                <Option v-for="device in deviceList" :disabled="disabledDevice(rule.deviceList, device.deviceID)"
                        :key="device.deviceID" :value="device.deviceID" :label="device.deviceName"></Option>
              </Select>
              <template v-if="!rule.disabled">
                <Button v-if="deviceIndex === 0" type="text" @click="handleAddDevice(index)">添加设备</Button>
                <Button v-else class="button-text-red" style="margin-left: 0;" type="text"
                        @click="handleDeleteDevice(index, deviceIndex)">删除
                </Button>
              </template>
            </div>
          </FormItem>
          <FormItem label="柜号选择">
            <div style="display: flex;">
              <Select :disabled="rule.disabled" multiple filterable v-model="device.cabNum">
                <Option v-for="(cab, cabIndex) in device.cabList" :key="cabIndex" :value="cab" :label="cab"></Option>
              </Select>
              <Button v-if="!rule.disabled" type="text"></Button>
            </div>
          </FormItem>
        </div>
        <FormItem label=" ">
          <Button v-show="rule.disabled" @click="handleEditRule(rule)">编辑规则</Button>
          <Button v-show="!rule.disabled" @click="handleSaveRule(rule)" type="success">保存规则</Button>
          <Button style="margin-left: 30px" type="error" @click="handleDeleteRule(rule)">删除规则</Button>
          <!--<Button style="margin-left: 30px" @click="handleCopyRule(rule)" type="primary" v-if="rule.existed">复制规则</Button>-->
        </FormItem>
      </Card>
      <FormItem label=" ">
        <Button style="margin: 20px 46px;" type="primary" @click="handleAddRule">添加规则</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import CardList from 'src/components/card/cardList';

  export default {
    name: 'assignRule',
    components: { CardList },
    data() {
      return {
        list: [
          // {
          //   cards: [],
          //   disabled: true,
          //   deviceList: [
          //     {
          //       deviceId: '',
          //       cabNum: [],
          //       cabList: []
          //     },
          //     {
          //       deviceId: '',
          //       cabNum: [],
          //       cabList: []
          //     },
          //   ]
          // },
          // {
          //   cards: [],
          //   disabled: true,
          //   deviceList: [
          //     {
          //       deviceId: '',
          //       cabNum: []
          //     }
          //   ]
          // },
        ],
        deviceList: [],
        cardList: []
      };
    },
    computed: {
      usedCard() {
        return this.list.reduce((cards, rule) => {
          return cards.concat(...rule.cards)
        }, [])
      }
    },
    created() {
      this.getDeviceList();
      this.getCardList();
      this.getRuleList();
      window.addEventListener('beforeunload', () => this.checkIsEdit());
    },
    beforeDestroy() {
      this.checkIsEdit()
    },
    methods: {
      checkIsEdit() {
        return new Promise((resolve, reject) => {
          const isEdit = this.list.some(item => !item.disabled && item.existed);
          if (isEdit) {
            const requests = this.list.map(item => {
              if (!item.disabled && item.existed) {
                item.disabled = true;
                return this.handleSaveRule(item, false)
              }
            });
            return Promise.all(requests).then(() => resolve());
          } else {
            resolve();
          }
        })
      },
      getCardList() {
        const url = '/Web/Member/get_all_card';
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.cardList = data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      dealRule(rule) {
        const devices = {};
        rule.deviceList.forEach(item => {
          devices[item['deviceId']] = item.cabNum.join(',');
        });
        return {
          rules: [{
            cards: rule.cards,
            devices
          }]
        };
      },
      handleEditRule(rule) {
        rule.disabled = false;
        this.handleDeleteRule(rule, false);
      },
      handleDeleteRule(rule, refresh = true) {
        const url = '/web/Business/delCabinetRule';
        const postData = this.dealRule(rule);
        this.$service.post(url, postData, { loading: refresh }).then(res => {
          if (!refresh) return;
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.getRuleList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleSaveRule(rule, refresh = true) {
        const url = '/web/Business/setCabinetRule';
        const postData = this.dealRule(rule);
        return new Promise((resolve => {
          this.$service.post(url, postData, { loading: refresh }).then(res => {
            if (!refresh) return resolve();
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.getRuleList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          }).catch(e => {
            throw new Error(e);
          });
        }))
      },
      disabledDevice(list, id) {
        return list && list.map(item => item.deviceId).includes(id);
      },
      getRuleList() {
        const url = '/web/Business/getCabinetRule';
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            const list = data.map(item => {
              return {
                cards: item.cards,
                existed: true,
                disabled: true,
                deviceList: Object.entries(item.devices).map(([key, value]) => {
                  return {
                    deviceId: key,
                    cabNum: value.split(','),
                    cabList: []
                  };
                })
              };
            });
            this.list = list;
            list.forEach((rule, index) => {
              rule.deviceList.forEach((device, deviceIndex) => {
                this.onDeviceChange(index, deviceIndex, device.deviceId);
              });
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      newRule() {
        return {
          cards: [],
          disabled: false,
          deviceList: [
            {
              deviceId: '',
              cabNum: [],
              cabList: []
            }
          ]
        };
      },
      async onDeviceChange(index, deviceIndex, deviceId) {
        this.list[index].deviceList[deviceIndex].cabList = await this.getCabList(deviceId);
        this.$forceUpdate();
      },
      getCabList(device_id) {
        const url = 'Web/Business/get_cabint_device_priority_info';
        return this.$service.post(url, { device_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            const canNotUse = [...data.cabinet_priority_1, ...data.cabinet_priority_2, ...data.cabinet_priority_3];
            return data.All_cabinet.filter(item => !canNotUse.includes(item));
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      async handleAddRule() {
        await this.checkIsEdit();
        this.list.push(this.newRule());
      },
      handleAddDevice(index) {
        this.list[index].deviceList.push({ deviceId: '', cabNum: [] });
      },
      handleDeleteDevice(index, deviceIndex) {
        this.list[index].deviceList.splice(deviceIndex, 1);
      },
      getDeviceList() {
        const url = '/Web/Business/get_cabint_device';
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.deviceList = data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      }
    },
  };
</script>

<style scoped>

</style>
