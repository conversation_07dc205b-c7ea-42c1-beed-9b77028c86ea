<template>
<div>
  <div class="table-wrap">
    <main>
      <Table ref="table" :columns="columns" :data="tableData" stripe disabled-hover></Table>
    </main>
    <footer>
     <Button type="danger" class="search" @click="clearFace">清除人脸</Button>
    </footer>
  </div>
</div>
</template>

<script>
export default {
  name: 'yjCabinet',
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: '设备号',
          key: 'device_id'
        }
      ]
    };
  },
  components: {},
  created() {
    this.getList();
  },
  methods: {
    clearFace() {
      this.$Modal.confirm({
        title: '确定清除吗？',
        content: '将清除设备上所有人脸',
        onOk: () => {
          this.$service
            .post('/Web/YjFace/yj_all_face_del')
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
      });
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/YjFace/getDeviceList')
        .then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    }
  }
};
</script>

