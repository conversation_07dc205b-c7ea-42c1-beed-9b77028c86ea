<template>
  <div class="table-wrap">
    <header>
      <Input v-model="searchTxt" class="w120" placeholder="姓名/电话" />
      <Input v-model="boxNo" class="w120" placeholder="储物柜号码" />
      <Select v-model="courseId" class="w120" placeholder="动作">
        <Option v-for="item in courseList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <DatePicker @on-change="handleDateChange" :value="duringDate" type="daterange" placement="bottom-end" placeholder="选择日期" class="option-select" style="width: 220px"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
      </header>
      <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer transfer></Page>
    </footer>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      courseId: "",
      courseList: [
        { value: "", label: '全部' },
        { value: 1, label: '存柜' },
        { value: 2, label: '取物' },
        { value: 3, label: '退柜' },
        { value: 4, label: '清柜' },
        { value: 5, label: '清洁' }
      ],
      recorderList: [],
      columns: [
        { title: '时间', key: 'create' },
        {
          title: '姓名',
          key: 'username',
          render: (h, params) => {
            if (params.row.user_type == 0) {
              return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.$router.push(`/member/detail/${params.row.user_id}`);
                  }}>
                  {params.row.username}
                </a>
              );
            } else {
              return (<div>{params.row.username}</div>);
            }
          }
        },
        { title: '终端名称', key: 'device_name' },
        { title: '柜控分组', key: 'cabinet_group' },
        { title: '动作', key: 'action', render: (h, params) => {
          let tagElement = null;
          if (params.row.action == 1) {
            tagElement = (<tag color="blue">存柜</tag>);
          } else if(params.row.action == 2) {
            tagElement = (<tag color="green">取物</tag>);
          } else if (params.row.action == 3) {
            tagElement = (<tag color="yellow">退柜</tag>);
          } else if (params.row.action == 4) {
            tagElement = (<tag title={params.row.admin_name} color="red">清柜</tag>);
          } else if (params.row.action == 5) {
            tagElement = (<tag title={params.row.admin_name || params.row.username} color="purple">清洁</tag>);
          }

          const content = `操作账号: ${params.row.admin_name || '无'}`;
          tagElement = (<Tooltip content={content} placement='right' transfer>
            {tagElement}
          </Tooltip>)

          return tagElement;
        } },
        { title: '柜号', key: 'cabinet_num' }
      ],
      list: [],
      searchTxt: '',
      boxNo: ''
    };
  },
  methods: {
    handleDateChange(val) {
      this.duringDate = val;
    },
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    getList() {
      return this.$service
        .post('/web/Business/cabinetRecords', {
          search: this.searchTxt,
          cabinet_num: this.boxNo,
          action: this.courseId,
          begin_time: this.duringDate[0],
          end_time: this.duringDate[1],
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            this.list = res.data.data.list;
            this.total = parseInt(res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  created() {
    const today = new Date();
    const whatDay = `${today.getFullYear()}-${today.getMonth()+1}-${today.getDate()}`;
    this.duringDate = [whatDay, whatDay];

    this.getList();
  }
};
</script>

