<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>储值卡签到设置</h2>
    </div>
    <div class="form-box-con">
      <Form>
      <Radio-group v-model="postData.pay_type" style="margin-bottom:15px">
        <Radio label="BillingOnTime">计时收费</Radio>
        <Radio label="PayPerClick">按次收费</Radio>
      </Radio-group>
      <Form ref="formData" :model="postData" v-show="postData.pay_type == 'BillingOnTime'">
        <Form-item>
          每分钟收费 <Input class="min-input" v-model="postData.oneminpay"/> 元
        </Form-item>
        <Form-item>
          单次进场消费上限 <Input class="min-input"  v-model="postData.onelimit"/> 元
        </Form-item>
        <Form-item>
          <Checkbox v-model="postData.mincost_switch">单次最低消费</Checkbox> <Input class="min-input"  v-model="postData.mincost"/> 元
        </Form-item>
        <Form-item prop="balance" :rules="{required: true,type: 'string',pattern: /^-?[0-9]+(.[0-9]{1,2})?$/,message: '必填项,必须为数字且只能保留两位小数'}">
          <Checkbox v-model="postData.balance_switch" disabled>卡内金额低于</Checkbox> <Input class="min-input" v-model="postData.balance"/> 元不能进场
        </Form-item>
        <Form-item>
          <Checkbox v-model="postData.abnormal_switch">签到进场后未正常签退离场，下次签到时将按 </Checkbox> <Input class="min-input"  v-model="postData.abnormal"/> 元/次补扣费用
        </Form-item>
      </Form>
      <Form v-show="postData.pay_type == 'PayPerClick'">
        <Form-item>
          单次收费 <Input class="min-input" v-model="postData.pertimemoney"/> 元
        </Form-item>
        <Form-item>
           进场 <Input class="min-input"  v-model="postData.freetime"/> 分钟以内离场将不扣费
        </Form-item>
      </Form>
      <Form-item>
        <div class="buttons">
          <Button type="primary"
                  @click="submit">保存</Button>
          <Button 
                  @click="$router.back()">取消</Button>
        </div>
      </Form-item>
    </Form>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
  export default {
    name: 'valueCardConfig',
    data () {
      return {
        postData:{
          pay_type:'BillingOnTime',
          //按时计费
          oneminpay:'', //每分钟金额
          onelimit:'', //一场最高限制
          mincost_switch:false, //最低消费开关
          mincost:'', //最低消费
          balance_switch:true, //储值卡最低金额开关
          balance:'', //储值卡最低金额
          abnormal_switch:'', //异常离场开关
          abnormal:'',//异常离场
          //按次计费
          pertimemoney:'', //每分钟多少钱
          freetime:'', //入场多少分钟内离场返费
        }
      }
    },
    created () {
      this.getConfig()
    },
    watch: {
    },
    computed: {
      ...mapGetters([
        'busName',
        'busList',
        'busId'
      ])
    },
    methods: {
      submit () {
        if(this.postData.pay_type == "BillingOnTime"){
          this.$refs.formData.validate((valid) => {
             if(valid){
               this.$service.post('/Web/Business/Sjs_set_pay_per_time_config',this.postData).then(res => {
                 this.$Message.success(res.data.errormsg)
               }).catch(err => {
                 this.$Message.error(err)
               })
             } else {
               this.$Message.error('请先正确填写数据！');
             }
          })
        }else{
          this.$service.post('/Web/Business/Sjs_set_pay_per_time_config',this.postData).then(res => {
            this.$Message.success(res.data.errormsg)
          }).catch(err => {
            this.$Message.error(err)
          })
        }
      },
      getConfig () {
        this.$service.post('/Web/Business/Sjs_get_pay_per_time_config', {bus_id:this.busId}).then(res => {
          if (res.data.errorcode == 0 && res.data.data.pay_type != false) {
            this.postData = res.data.data
            this.postData.balance = res.data.data.balance.toString()
          }else if(res.data.errorcode != 0){
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      }
    },
  }
</script>
<style lang="less" scoped>
  .form-box .min-input{
    width: 50px;
    margin-right: 5px;
  }
</style>
