<template>
  <div class="table-wrap">
    <header>
      <Input style="width: 180px" v-model="postData.search" @on-enter="handleSearch" placeholder="姓名/电话" />
      <Select v-model="postData.action" placeholder="动作">
        <Option v-for="item in courseList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="postData.user_type" placeholder="类型">
        <Option v-for="type in userTypes" :value="type.value" :key="type.value" :label="type.label"></Option>
      </Select>
      <DatePicker @on-change="handleDateChange" v-model="dateRange" type="daterange" :clearable="false" :editable="false" placement="bottom-end" placeholder="选择日期" style="width: 220px"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer>
      <Button type="success" @click="exportTable">导出Excel</Button>
      <Pager :history="false" :postData="postData" :total="total" @on-change="onPageChange" transfer></Pager>
    </footer>
  </div>
</template>
<script>
  import Pager from 'components/pager';
  import { formatDate } from 'src/utils'
  export default {
    name: 'enterRecord',
    components: { Pager },
    data() {
      return {
        postData: {
          page_no: 1,
          page_size: 10,
          search: '',
          begin_time: formatDate(new Date(), 'yyyy-MM-dd'),
          end_time: formatDate(new Date(), 'yyyy-MM-dd'),
          action: '',
          user_type: ''
        },
        userTypes: [{ value: '', label: '无' }, { value: '1', label: '会员' }, { value: '2', label: '会籍' }, { value: '3', label: '教练' }, ],
        isLoading: false,
        currentPage: 1,
        pageSize: 10,
        total: 0,
        courseId: '',
        courseList: [{ value: '', label: '全部' }, { value: 1, label: '进场' }, { value: 2, label: '出场' }],
        recorderList: [],
        dateRange: [new Date(), new Date()],
        columns: [
          { title: '时间', key: 'create' },
          {
            title: '姓名',
            key: 'username',
            render: (h, params) => {
              if (params.row.user_type == 1) {
                return (
                  <a
                    href="javascript:void(0)"
                    on-click={name => {
                      this.$router.push(`/member/detail/${params.row.user_id}`);
                    }}>
                    {params.row.username}
                  </a>
                );
              } else {
                return <div>{params.row.username}</div>;
              }
            }
          },
          {
            title: '用户类型',
            key: 'userType',
            render: (h, params) => {
              if (params.row.user_type == 1) {
                return <tag color="blue">会员</tag>;
              } else if (params.row.user_type == 2) {
                return <tag color="orange">会籍</tag>;
              } else if (params.row.user_type == 3) {
                return <tag color="purple">教练</tag>;
              }
            }
          },
          {
            title: '动作',
            key: 'comes',
            render: (h, params) => {
              if (params.row.action == 1) {
                return <tag color="primary">进场</tag>;
              } else if (params.row.action == 2) {
                return <tag color="success">出场</tag>;
              }
            }
          }
        ],
        list: [],
        searchTxt: '',
        boxNo: '',
      };
    },
    created() {
      this.getList();
    },
    methods: {
      onPageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getList();
      },
      handleDateChange([s, e]) {
        this.postData.begin_time = s;
        this.postData.end_time = e;
      },
      handleSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList(isExport = false) {
        const { page_no, page_size } = this.postData;
        return this.$service.post('/web/Business/getInAndOutRecords', {
            ...this.postData,
            page_no: isExport ? 1 : page_no,
            page_size: isExport? this.total: page_size
          }, { isExport }).then(res => {
          if (res.data.errorcode === 0) {
            this.total = +res.data.data.count || 0;
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            const list = res.data.data.list.map(item => {
              return {
                ...item,
                userType: this.userTypes.find(type => type.value === item.user_type).label,
                comes: item.action === '1' ? '进场' : '出场'
              }
            });
            this.list = list;
            return list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      async exportTable() {
        const data = await this.getList(true)
        this.$refs.table.exportCsv({
          filename: '进出场记录',
          columns: this.columns,
          data
        })
      },
    }
  };
</script>
<style lang="less">
  .box {
    background-color: #fff;

    .box-head {
      border: 1px solid #dddee1;

      .head-option {
        height: 75px;
        display: flex;
        flex-direction: row;
        align-items: center;

        .option-select {
          width: 120px;
          margin-right: 20px;
        }
      }
    }

    .box-body {
      border-left: 1px solid #dddee1;
    }

    .box-foot {
      border: 1px solid #dddee1;
      border-top: none;

      .foot-option {
        height: 80px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;

        .option-ctrl {
          display: flex;
          flex-direction: row;
        }

      }
    }

    .action-btn {
      width: 60px;
      color: white;
      border-radius: 13px;
      margin: 0 auto;
    }

    .action-save {
      .action-btn;
      background-color: #52a4ea;
    }

    .action-receive {
      .action-btn;
      background-color: #19be6b;
    }

    .action-return {
      .action-btn;
      background-color: orange;
    }
  }
</style>
