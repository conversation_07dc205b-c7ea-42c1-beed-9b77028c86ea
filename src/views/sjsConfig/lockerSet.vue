<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>固定租柜设置</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formCustom"
            :model="formCustom"
            :rules="ruleCustom"
            :label-width="120">
        <Form-item label="请选择场馆"
                    prop="bus_id">
          <Select clearable
                  filterable
                  v-model="formCustom.bus_id"
                  @on-change="busChange"
                  placeholder="请选择场馆"
                  v-if="buslist.length>0">
            <Option v-for="item in buslist"
                    :key="item.id"
                    :value="item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="租柜设置">
          <template>
            <Transfer :data="lockerlist"
                      :titles="transferTitle"
                      :target-keys="selectedlocker"
                      :render-format="render1"
                      @on-change="lockerChange"></Transfer>
          </template>
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary"
                    @click="handleSubmit('formCustom')">保存</Button>
            <Button 
                    @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  export default {
    name: 'lockerSet',
    data () {
      return {
        transferTitle: ['临时租柜', '固定租柜'],
        formCustom: {
          bus_id: '',
        },
        ruleCustom: {
          bus_id: [
            { required: true, message: '请选择场馆', trigger: 'change' },
          ],
        },
        lockerlist: [],
        selectedlocker: []

      }
    },
    computed: {
      ...mapGetters({
        buslist: 'busList'
      })
    },
    methods: {
      handleSubmit (name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            let detentiontemp = [];
            for (let i = 0; i < this.selectedlocker.length; i++) {
              for (let j = 0; j < this.lockerlist.length; j++) {
                if (this.selectedlocker[i] == this.lockerlist[j].key) {
                  detentiontemp.push(this.lockerlist[j].key + '|' + this.lockerlist[j].label);
                }
              }
            }
            let postData = {
              bus_id: this.formCustom.bus_id,
              detention_str: detentiontemp.join(',')
            }
            let url = "/Web/Business/set_detention_list";
            let _this = this;
            this.$service.post(url, postData).then(response => {
              if (response.status == 200) {
                if (response.data.errorcode == 0) {
                  _this.$Message.success(response.data.errormsg);
                } else {
                  _this.$Message.error(response.data.errormsg);
                }
              }
            })
              .catch(function (response) {
                console.log(response)
              })
          }
        })
      },
      lockerChange (newTargetKeys, direction, moveKeys) {
        this.selectedlocker = newTargetKeys;
      },
      render1 (item) {
        return item.label;
      },
      busChange: function (busid) {
        if (busid) {
          let _this = this;
          this.$service.post('/Web/Business/get_detention_list', { bus_id: busid })
            .then(res => {
              if (res.status === 200) {
                let nochoosetemp = [];
                for (let i = 0; i < res.data.data.no_choose.length; i++) {
                  let item = {
                    key: res.data.data.no_choose[i].deviceId,
                    label: res.data.data.no_choose[i].deviceName,
                    disable: false
                  }
                  nochoosetemp.push(item)
                }
                _this.lockerlist = nochoosetemp;
                console.log('lockerlist:', _this.lockerlist)
                let choosetemp = [];
                for (let i = 0; i < res.data.data.choose_detention.length; i++) {
                  choosetemp.push(res.data.data.choose_detention[i].deviceId);
                }
                _this.selectedlocker = choosetemp;
                console.log('selectedlocker:', _this.selectedlocker)
              }
            })
            .catch(function (response) {
              console.log(response)
            })
        }
      },
    },
  }
</script>
<style lang="less" scoped>

</style>
