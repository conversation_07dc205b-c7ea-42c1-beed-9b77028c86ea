<template>
<div>
  <div class="table-wrap">
    <header>
      <Select v-if="deviceList && deviceList.length>0" v-model="postData.device_id" class="w120"  placeholder="设备选择" filterable>
        <Option value="">全部设备</Option>
        <Option v-for="item in deviceList" :key="item.device_id" :value="item.device_id">{{item.device_name}}</Option>
      </Select>
      <Input v-model="postData.cabinet_no" class="w120" placeholder="设备柜号" @on-enter="search" />
      <Input v-model="postData.cabinet_name" class="w120" placeholder="显示柜号" @on-enter="search" />
      <Select v-model="postData.cabinet_all" class="w120"  placeholder="状态">
        <Option :value="0">全部</Option>
        <Option :value="1">已添加</Option>
        <Option :value="2">未添加</Option>
      </Select>
      <Button type="success" class="search" @click="search">搜索</Button>
    </header>
    <main>
      <Table ref="table" :columns="columns" :data="tableData" stripe disabled-hover></Table>
    </main>
    <footer>
      <Page class="page" :total="totalCount" :page-size="postData.page_size" :current.sync="postData.page_no" placement="top" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
      </Page>
    </footer>
  </div>
  <Modal title="编辑" v-model="showEdit" :maskClosable="false">
    <Form class="modal-form" :model="modalData" :label-width="120" label-position="right" style="padding-right: 40px;">
      <FormItem label="设备柜号">
        {{modalData.cabinet_no}}
      </FormItem>
      <FormItem label="显示柜号" :rules="{required: true, message: '显示柜号不能为空', trigger: 'blur'}" prop="cabinet_name">
        <Input v-model="modalData.cabinet_name" />
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="editConfirm('edit')">确定</Button>
      <Button @click="cancelEdit">取消</Button>
    </div>
  </Modal>
</div>
</template>

<script>
export default {
  name: 'cabinetName',
  data() {
    return {
      totalCount: 0,
      tableData: [],
      deviceList: [],
      showEdit: false,
      postData: {
        device_id: '',
        cabinet_no: '',
        cabinet_name: '',
        cabinet_all: 0,
        page_size: 10,
        page_no: 1
      },
      modalData: {
        id: '',
        cabinet_name: ''
      },
      columns: [
        {
          title: '设备名称',
          key: 'device_name'
        },
        {
          title: '设备柜号',
          key: 'cabinet_no'
        },
        {
          title: '显示柜号',
          key: 'cabinet_name'
        },
        {
          title: '操作',
          render: (h, params) => {
            let item = params.row
            return (
              <div>
                <i-button
                  type="text"
                  onClick={() => this.editCabient(item)}
                  size="small"
                >
                  编辑
                </i-button>
                <i-button
                  onClick={() => this.editConfirm(item)}
                  class="button-text-red"
                  style="margin-left: 15px"
                  type="text"
                  size="small"
                >
                  清除
                </i-button>
              </div>
            )
          }
        }
      ]
    }
  },
  components: {},
  created() {
    this.getDeviceList()
    this.getList()
  },
  methods: {
    cancelEdit() {
      this.showEdit = false
      this.modalData = {
        id: '',
        cabinet_name: ''
      }
    },
    editCabient(item) {
      this.showEdit = true
      this.modalData.id = item.id
      this.modalData.cabinet_no = item.cabinet_no
      this.modalData.cabinet_name = item.cabinet_name
    },
    editConfirm(item) {
      if (item.id) {
        this.modalData.id = item.id
        this.modalData.cabinet_name = ''
      } else if (!this.modalData.cabinet_name) {
        return false;
      }
      this.$service.post('/Web/Cabinet/edit', this.modalData).then(res => {
        if (res.data.errorcode === 0) {
          this.getList()
          this.cancelEdit()
          this.$Message.success(res.data.errormsg)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    pageChanged(page) {
      this.postData.page_no = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.postData.page_size = size
      this.getList()
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.$service.post('/Web/Cabinet/getList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          let data = res.data.data
          this.tableData = data.list
          this.totalCount = parseInt(data.count)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getDeviceList() {
      this.$service.post('/Web/Cabinet/getDeviceList').then(res => {
        if (res.data.errorcode == 0) {
          this.deviceList = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>
