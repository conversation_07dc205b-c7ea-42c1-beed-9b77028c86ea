<template>
  <div class="container adds-container">
    <header>
      <h3>中控柜广告管理</h3>
    </header>
    <Form label-position="right"
          ref="form"
          :model="formItem"
          :rules="formRules"
          class="form"
          :label-width="125">
      <FormItem label="广告图库">
        <div class="images">
          <div v-for="(image, index) in templateImgs"
               :key="index"
               class="img-box">
            <Icon v-if="image.is_system!=1" style="cursor: pointer; position: absolute; right: -4px; top: -4px"
                  title="删除"
                  @click.native="delTemplate(image.id,index)"
                  type="md-close-circle"
                  color="red"
                  size="25"></Icon>
            <img :src="image.logo_url">
          </div>
        </div>
        <div class="flex-start">
          <ImgUploader refName="ImageUploader"
                       v-model="uploadImg"
                       multiple
                       :output-height="1920"
                       :output-width="1080"
                       :options="{aspectRatio: 1080/1920}"></ImgUploader>
          <span class="m-r-20">*建议图片尺寸：宽1080*高1920</span>
          <span class="m-r-20">大小：&le;3Mb</span>
          <span class="m-r-20">图片格式：jpeg、png</span>
        </div>
      </FormItem>
      <FormItem label="中控设备" prop="device_id">
        <Select v-model="formItem.deviceIndex" @on-change="deviceChanged" clearable>
          <Option v-for="(item,index) in deviceList" :key="item.device_id" :value="index">{{item.device_name}}</Option>
        </Select>
      </FormItem>
      <FormItem label="展示图片" v-if="formItem.device_id">
        <div class="images" v-if="formItem.showImgs.length>0">
          <div v-for="(image, index) in formItem.showImgs"
               :key="index"
               class="img-box">
            <Icon style="cursor: pointer; position: absolute; right: -4px; top: -4px"
                  title="删除"
                  @click.native="delShowImg(image.id,index)"
                  type="md-close-circle"
                  color="red"
                  size="25"></Icon>
            <img :src="image.logo_url">
          </div>
        </div>
        <div class="specbutton" @click="showAdd=true">
          <label class="upload-btn">从图库添加</label>
        </div>
        <div></div>
      </FormItem>
      <FormItem label="切换间隔" prop="interval" v-if="formItem.device_id&&formItem.showImgs.length>1">
        <InputNumber :min="1"
                     class="inputnumber"
                     v-model="formItem.interval"/>
        <span class="unit">秒</span>
      </FormItem>
      <FormItem v-if="formItem.device_id">
        <div class="buttons">
          <Button type="primary" @click="updataAdds">保存</Button>
          <Button @click="cancelAdds">取消</Button>
        </div>
      </FormItem>
    </Form>

    <Modal width="715" :mask-closable="false"
           v-model="showAdd">
      <div class="images">
        <div v-for="(image, index) in templateImgs"
             :key="index"
             class="img-box">
          <Icon style="cursor: pointer; position: absolute; right: -4px; top: -4px"
                v-if="modalIdSelected.includes(image.id)"
                title="选择"
                type="ios-checkmark-circle"
                color="green"
                size="25"></Icon>
          <img :src="image.logo_url" style="cursor: pointer" @click="checkModalImg(image.id)">
        </div>
      </div>
      <div slot="footer"
           class="modal-buttons">
        <Button type="success"
                @click="saveModalImg">确定
        </Button>
        <Button 
                @click="showAdd = false">取消
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
  import ImgUploader from 'components/form/cropper';
  import { getBaseUrl } from 'utils/config';
  import { mapState } from 'vuex';

  export default {
    components: {
      ImgUploader
    },
    data() {
      return {
        formItem: {
          deviceIndex: '',
          device_id: '',
          showImgs: [],
          logo_id_str: '',
          interval: 30
        },
        templateImgs: [],
        uploadImg: '',
        deviceList: [],
        formRules: {
          device_id: [{ required: true, message: '请选择设备' }]
        },
        showAdd: false,
        modalIdSelected: []
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    watch: {
      uploadImg(newValue, oldValue) {
        if (newValue && this.templateImgs.length < 15) {
          this.addTemplate(newValue);
        } else if (newValue && this.templateImgs.length == 15) {
          this.$Message.error('模版图片最多只能上传10张');
        }
      },
      showAdd(val) {
        if (val) {
          let temp = [];
          this.formItem.showImgs.forEach((item) => {
            temp.push(item.id);
          });
          this.modalIdSelected = temp;
        } else {
          this.modalIdSelected = [];
        }
      }
    },
    created() {
      this.gettemplateImg();
      this.getdeviceList();
    },
    methods: {
      cancelAdds() {
        this.formItem.deviceIndex = '';
        this.formItem.device_id = '';
        this.formItem.showImgs = [];
        this.formItem.logo_id_str = '';
        this.formItem.interval = 30;
      },
      updataAdds() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            return false;
          }
          let tmp = [];
          this.formItem.showImgs.forEach((item) => {
            tmp.push(item.id);
          });
          const url = "/Web/Business/saveCabinentAdInfo";
          let postd = {
            bus_id: this.busId,
            device_id: this.formItem.device_id,
            logo_id_str: tmp.join(','),
            interval: this.formItem.interval
          };
          this.$service
            .post(url, postd)
            .then(res => {
              if (res.status == 200) {
                if (res.data.errorcode == 0) {
                  this.$Message.success('保存成功');
                  this.getdeviceList();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              }
            })
            .catch(err => {
              console.log(err);
            });
        });
      },
      saveModalImg() {
        let tmp = [];
        let tmpid = [];
        this.modalIdSelected.forEach((item) => {
          this.templateImgs.forEach((jtem) => {
            if (item == jtem.id) {
              tmp.push(jtem);
              tmpid.push(item);
            }
          });
        });
        this.formItem.showImgs = tmp;
        this.showAdd = false;
      },
      checkModalImg(id) {
        let ind = this.modalIdSelected.indexOf(id);
        if (ind > -1) {
          this.modalIdSelected.splice(ind, 1);
        } else {
          if (this.modalIdSelected.length == 10) {
            this.$Message.error('最多只能选择10张图片');
          } else {
            this.modalIdSelected.push(id);
          }
        }
      },
      delShowImg(id, index) {
        this.formItem.showImgs.splice(index, 1);
      },
      deviceChanged(index) {
        if (typeof (index) === 'number') {
          this.formItem.showImgs = this.deviceList[index].img_array;
          this.formItem.device_id = this.deviceList[index].device_id;
          this.formItem.interval = Number(this.deviceList[index].interval);
        } else {
          this.formItem.deviceIndex = '';
          this.formItem.device_id = '';
          this.formItem.showImgs = [];
          this.formItem.logo_id_str = '';
          this.formItem.interval = 30;
        }
      },
      delTemplate(id, index) {
        const url = "/Web/Business/deleteCabinetTemplate";
        this.$service
          .post(url, { logo_id: id, bus_id: this.busId })
          .then(res => {
            if (res.status == 200) {
              if (res.data.errorcode == 0) {
                this.$Message.success('删除成功');
                this.templateImgs.splice(index, 1);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      addTemplate(addr) {
        const url = "/Web/Business/uploadCabinetTemplate";
        this.$service
          .post(url, { img_url: addr, bus_id: this.busId })
          .then(res => {
            if (res.status == 200) {
              if (res.data.errorcode == 0) {
                this.uploadImg = '';
                this.gettemplateImg();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      getdeviceList() {
        const url = "/Web/Business/getCabinetDevice";
        this.$service
          .post(url, { bus_id: this.busId })
          .then(res => {
            if (res.status == 200) {
              if (res.data.errorcode == 0) {
                this.deviceList = res.data.data;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      gettemplateImg() {
        const url = "/Web/Business/getCabinetTemplate";
        this.$service
          .post(url, { bus_id: this.busId })
          .then(res => {
            if (res.status == 200) {
              if (res.data.errorcode == 0) {
                this.templateImgs = res.data.data;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      }

    }
  };
</script>
<style lang="less" scoped>
  @btn-color: #19be6b;
  .adds-container {
    min-height: 1000px;
  }

  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .images {
    margin-top: 20px;
    background-color: #f7f7f7;
    padding: 10px;
    .flex-center;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 20px;

    > .img-box {
      width: 150px;
      margin: 1%;
      position: relative;
      // height: 43/75 * 200px;
      > img {
        width: 100%;
        height: 100%;
        border: 1px solid #dcdcdc;
      }
    }
  }

  .specbutton {
    display: flex;
    align-items: center;

    .upload-btn {
      border: 1px solid @btn-color;
      border-radius: 4px;
      height: 32px;
      line-height: 32px;
      padding: 0 10px;
      font-size: 14px;
      display: inline-block;
      cursor: pointer;
      color: @btn-color;

      &:hover {
        color: #fff;
        background-color: @btn-color;
      }
    }
  }

  .inputnumber {
    width: calc(100% - 30px) !important;
  }

  .unit {
    margin-left: 8px;
    font-size: 14px;
  }

  .flex-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .m-r-20 {
    margin-right: 20px;
  }
</style>
