<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="姓名/电话" />
      <Input style="width: 180px" v-model="bathNo" class="option-select" placeholder="淋浴间号码" />
      <DatePicker v-model="dateRange" @on-change="handleDateChange" type="daterange" placement="bottom-end" placeholder="日期" class="option-select" style="width: 220px"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer transfer></Page>
      </Col>
    </Row>
  </div>
</template>
<script>
import { formatDate } from 'utils'

export default {
  data() {
    return {
      dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(Date.now()), 'yyyy-MM-dd') ],
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      courseList: [{ value: "", label: '全部' }, { value: 1, label: '存柜' }, { value: 2, label: '取物' }, { value: 3, label: '退柜' }, { value: 4, label: '清柜' }],
      recorderList: [],
      columns: [
        { 
          title: '日期',
          key: 'date'
        },
        {
          title: '姓名',
          key: 'user_name',
          render: (h, params) => {
            if (params.row.user_type == 0) {
              return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.$router.push(`/member/detail/${params.row.user_id}`);
                  }}>
                  {params.row.user_name}
                </a>
              );
            } else {
              return (<div>{params.row.user_name}</div>);
            }
          }
        },
        {
          title: '角色',
          key: 'role_id',
          render: (h, params) => {
              let role;
              switch (parseInt(params.row.role_id)) {
                  case 0:
                      role = "会员";
                      break;
                  case 1:
                      role = "会籍";
                      break;
                  case 2:
                      role = "教练";
                      break;
              }
              return (
                  <span>{role}</span>
              )
          }
        },
        {
          title: '淋浴间号码',
          key: 'bath_num',
        },
        {
          title: '开始时间',
          key: 'begin_time',
        },
        {
          title: '结束时间',
          key: 'end_time',
        },
        {
          title: '使用时长',
          key: 'duration',
          render: (h, params) => {
            let time = params.row.duration === '--' ? '--' : `${Math.ceil(Number(params.row.duration))}分钟`;
            return <span>{time}</span>
          }
        },
        {
          title: '退租方式',
          key: 'status',
          render: (h, params) => {
            let methods = ["使用中", "已归还", "系统"];
            let idx = Number(params.row.status) - 1;
            return <span>{methods[idx] || 未知}</span>
          }
        },
        {
          title: '使用卡',
          key: 'card_name'
        },
        {
          title: '金额',
          key: 'monetary'
        }
      ],
      list: [],
      searchTxt: '',
      bathNo: '',
      begin_date: '',
      end_date: ''
    };
  },
  methods: {
    handleDateChange(val) {
      this.begin_date = val[0];
      this.end_date = val[1];
    },
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    getList() {
      return this.$service
        .post('/Web/Shower/getShowerList', {
          search: this.searchTxt,
          bath_num: this.bathNo,
          begin_date: this.begin_date,
          end_date: this.end_date,
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if(res.status === 200) {
              if (res.data.errorcode == 0) {
                if (!Array.isArray(res.data.data.list)) {
                this.list = [];
                return false;
                }
                this.list = res.data.data.list;
                this.total = parseInt(res.data.data.count);
            } else {
                this.$Message.error(res.data.errormsg);
            }
          } else {
              this.$Message.error("网络错误。。。")
          }
        }).catch(err => {
            console.error(err)
        });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  created() {
    this.getList();
  }
};
</script>
<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }

  .action-btn {
    width: 60px;
    color: white;
    border-radius: 13px;
    margin: 0 auto;
  }

  .action-save {
    .action-btn;
    background-color: #52a4ea;
  }

  .action-receive {
    .action-btn;
    background-color: #19be6b;
  }

  .action-return {
    .action-btn;
    background-color: orange;
  }
}
</style>
