<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>淋浴号管理</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="120">
      <Form-item label="中控设备">
        <Select clearable
                filterable
                @on-change="deviceChange"
                v-model="postData.device_id"
                placeholder="请选择中控设备"
                v-if="deviceList">
          <Option v-for="item in deviceList"
                  :key="item.device_id"
                  :value="item.device_id">{{item.device_name}}</Option>
        </Select>
      </Form-item>
      <div v-if="postData.device_id">
        <Form-item label="添加沐浴号">
          <Input v-model="addStr" placeholder="请输入沐浴号，以逗号分割" />
          <Button style="margin-left:20px;" type="success" @click="addPro">确定</Button>
        </Form-item>
        <Form-item label="删除沐浴号">
          <Select v-model="delStr" multiple>
            <Option v-for="item in priorityInfo.All_cabinet" :value="item" :key="item">{{ item }}</Option>
          </Select>
          <Button style="margin-left:20px;" type="success" @click="delPro">确定</Button>
        </Form-item>
      </div>    
    </Form>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'cabintDevice',
    data () {
      return {
        postData: {
          device_id: '',//  设备id
        },
        deviceList: '',
        delStr: [],
        addStr: '',
        priorityInfo: {
          All_cabinet: []
        },
        noChoosePro: [],
      }
    },
    created() {
      this.getDevice()
    },
    methods: {
      deviceChange (deviceID) {
        if (!deviceID) return
        this.getNums(deviceID)
      },
      addPro() {
        this.$service.post('/Web/Business/device_add_cabinet', {device_id: this.postData.device_id,add_str: this.addStr}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('添加成功')
            this.addStr = ''
            this.getNums(this.postData.device_id)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      delPro() {
        this.$service.post('/Web/Business/device_del_cabinet', {device_id: this.postData.device_id,del_str: this.delStr.join(',')}).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('删除成功')
            this.delStr = []
            this.getNums(this.postData.device_id)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getNums(deviceID) {
          this.$service.post('/Web/Business/get_cabint_device_priority_info', {device_id :deviceID}).then(res => {
            if (res.data.errorcode === 0) {
                this.priorityInfo = res.data.data
            } else {
                    this.$Message.error(res.data.errormsg)
                }
            }).catch(err => {
                    this.$Message.error(err)
                })
      },
      getDevice() {
        this.$service.post('/Web/Business/getBathSetting').then(res => {
          if (res.data.errorcode === 0) {
            this.deviceList = res.data.data.bath_devices; 
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .area-line {
    height: 60px;
    line-height: 60px;
    vertical-align: middle;
    width: 100%;
    font-weight: bold;
    font-size: 18px;
  }

</style>