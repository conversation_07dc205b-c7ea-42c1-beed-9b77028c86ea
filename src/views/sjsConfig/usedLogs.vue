<template>
  <div class="container tab-table-wrap customized-tabs">
    <Tabs v-if="hasAuthority" name="invoice" v-model="activeIndex" @on-click="clickTabs">
      <TabPane v-if="getInAndOutRecords" name="0" label="进出记录">
				<enter-record />
      </TabPane>
      <TabPane v-if="cabinetRecords" name="1" label="柜子记录">
				<box-record />
      </TabPane>
			<TabPane v-if="getShowerList" name="2" label="水控使用记录">
				<bath-record />
			</TabPane>
			<TabPane v-if="getLogLists" name="3" label="灯控开关记录">
				<light-bulb-logs />
			</TabPane>
			<TabPane v-if="hasPrintLogAuth" name="4" label="小票记录">
				<print-logs />
			</TabPane>
      <TabPane v-if="hasDefenderLogAuth" name="5" label="防尾随记录">
				<defender-record />
      </TabPane>
    </Tabs>
    <div v-else class="none-box">
      没有可以访问的设备记录权限！
    </div>
  </div>
</template>

<script>
import EnterRecord from './enter-record.vue'
import BoxRecord from './boxRecord.vue'
import BathRecord from './bathRecord.vue'
import LightBulbLogs from './lightBulbLogs.vue'
import PrintLogs from './components/PrintLogs.vue'
import DefenderRecord from './components/DefenderRecord.vue'

export default {
  components: {
		EnterRecord,
		BoxRecord,
		BathRecord,
		PrintLogs,
    LightBulbLogs,
    DefenderRecord
  },
  data() {
    return {
      activeIndex: '0',
      hasAuthority: true,
      cabinetRecords: false,
      getInAndOutRecords: false,
      getLogLists: false,
      hasPrintLogAuth: false,
      getShowerList: false,
      hasDefenderLogAuth: false
    }
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
    },
    getAuthority() {
      return this.$service.post('/Web/LightControl/getLogTabPower').then(res => {
        if (res.data.errorcode === 0) {
          const resData = res.data.data
          this.cabinetRecords = resData.cabinetRecords
          this.getInAndOutRecords = resData.getInAndOutRecords
          this.getLogLists = resData.getLogLists
          this.getShowerList = resData.getShowerList
          this.hasPrintLogAuth = resData.ReceiptLogList
          this.hasDefenderLogAuth = resData.hasDefenderLogAuth

          if (this.getInAndOutRecords) {
            this.activeIndex = '0'
          } else if (this.cabinetRecords) {
            this.activeIndex = '1'
          } else if (this.getShowerList) {
            this.activeIndex = '2'
          } else if (this.getLogLists) {
            this.activeIndex = '3'
          }  else if (this.hasPrintLogAuth) {
            this.activeIndex = '4'
          } else if (this.hasDefenderLogAuth) {
            this.activeIndex = '5'
          } else {
            this.hasAuthority = false
          }
        }
      })
    }
  },
  created () {
    this.getAuthority()
  },
}
</script>

<style lang="less" scoped>
.none-box {
  width: 100%;
  height: 60vh;
  font-size: 28px;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
