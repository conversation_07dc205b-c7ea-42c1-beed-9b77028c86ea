<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card">
        <TabPane :label="labelOne" :name="tabList[0]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[0]" :tabList="tabList" />
        </TabPane>
        <TabPane :label="labelTwo" :name="tabList[1]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[1]" :tabList="tabList" lazy />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import TheSearchPanel from './components/TheDeductionSearchPanel.vue'

export default {
  name: 'Alipay2Tabs',
  components: {
    TheSearchPanel,
  },
  data() {
    const badgeFn = (h, label, count) => {
      return h('div', [
        h('span', label),
        h('Badge', {
          style: {
            marginLeft: '10px',
          },
          props: {
            count,
            overflowCount: 999,
          },
        }),
      ])
    }
    return {
      tabName: '1',
      tabList: ['1', '2'],
      valueOne: 0,
      labelOne: (h) => {
        return badgeFn(h, '扣款成功', this.valueOne)
      },
      valueTwo: 0,
      labelTwo: (h) => {
        return badgeFn(h, '扣款失败', this.valueTwo)
      },
    }
  },
  methods: {
    setCount(count, category) {
      if (category === this.tabList[0]) {
        this.valueOne = count
      } else if (category === this.tabList[1]) {
        this.valueTwo = count
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 20px;
}
</style>
