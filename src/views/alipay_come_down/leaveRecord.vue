<template>
  <div class="container">
    <div class="box">
      <Alert type="warning" show-icon>请假结束时会将会员卡的有效期自动延长加上请假天数，以确保会员卡实际使用时间不受影响</Alert>
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" placeholder="请选择场馆" class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">签约会员</div>
              <Input v-model="searchParams.username_phone" placeholder="请输入会员手机号码" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">产品方案</div>
              <Input v-model="searchParams.product_title" placeholder="请输入产品方案" class="value" clearable />
            </div>
            <!-- <div class="search-item" v-if="searchParams.bus_id">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="searchParams.bus_id" />
            </div> -->
            <div class="search-item">
              <div class="label">请假时间</div>
              <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer :clearable="false" />
            </div>
            <div class="search-item">
              <div class="label">状态</div>
              <Select v-model="searchParams.stop_status" placeholder="请选择状态" class="value" transfer>
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <Button style="margin-left: 10px" @click="handleExport">导出</Button>
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
// import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './components/TheDetailModal.vue'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  username_phone: '',
  product_title: '',
  // marketers_id: '',
  stop_status: '',
  stop_time: formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
  active_time: formatDate(new Date(), 'yyyy-MM-dd'),
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'LeaveRecord',
  components: {
    // SalesSelect,
    TheDetailModal
  },
  data() {
    return {
      daterange: [formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 签约会员 场馆 业绩归属 合约编号 产品方案 请假时间 请假天数 状态 请假结束时间
      columns: [
        {
          title: '签约会员',
          key: 'username'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        // {
        //   title: '业绩归属',
        //   key: 'marketers_name'
        // },
        {
          title: '合约编号',
          key: 'subscription_no',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '产品方案',
          key: 'product_title'
        },
        {
          title: '请假时间',
          key: 'stop_time',
        },
        {
          title: '请假天数',
          key: 'stop_days'
        },
        {
          title: '状态',
          key: 'stop_status_name',
          render: (h, params) => {
            const item = this.statusList.find(item => item.value === params.row.stop_status)
            const label = item ? item.label : '-'
            const color = item ? item.color : ''
            return h('div', { style: { color } }, label)
          }
        },
        {
          title: '请假结束时间',
          key: 'active_time'
        }
      ],
      list: [],
      total: 0,
      // 1请假中 2请假结束
      statusList: [
        { label: '请假中', value: 1, color: 'orange' },
        { label: '请假结束', value: 2, color: '#333' },
      ],
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      }
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.stop_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.active_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.stop_time = ''
        this.searchParams.active_time = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.daterange = []
      this.handleSearch()
    },
    getList() {
      return this.$service.post('/Web/AlipayMerchantCard/cardStopList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/AlipayMerchantCard/cardStopList', params).then((res) => {
        if (res.data.errorcode === 0) {
          // this.$Message.success({
          //   content: '导出任务运行中，请稍后到消息中心下载!',
          //   duration: 3
          // })
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
            list.forEach(row => {
              const item = this.statusList.find(item => item.value === row.stop_status)
              const label = item ? item.label : '-'
              row.stop_status_name = label
            })
          }
          this.$refs.selection.exportCsv({
            filename: `请假记录-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.columns,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
  async created() {
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    if (this.$route.params.busId) {
      this.searchParams.bus_id = this.$route.params.busId
    } else {
      this.searchParams.bus_id = this.busId
    }
    if (this.$route.params.subscriptionNo) {
      this.searchParams.subscription_no = this.$route.params.subscriptionNo
    }
    // this.getList()
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    // padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
}
</style>
