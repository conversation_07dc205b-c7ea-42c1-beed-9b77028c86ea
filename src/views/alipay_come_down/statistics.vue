<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <DatePicker class="option-select" type="year" placeholder="时间" v-model="dateValue" :clearable="false"
        @on-change="handleSearch">
      </DatePicker>
      <Select class="option-select" placeholder="请选择场馆" v-model="busId" filterable @on-change="handleSearch">
        <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select class="option-select" placeholder="类型" v-model="typeValue" @on-change="handleSearch">
        <Option value="1">按会员</Option>
        <Option value="2">按合约</Option>
      </Select>
      <!-- <Button type="success" @click="handleSearch">搜索</Button> -->
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table v-if="typeValue == 1" ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
      <Table v-if="typeValue == 2" ref="table" stripe :columns="columns2" :data="list2" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button @click="handleExcel">导出Excel</Button>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script setup>
import { ref, defineComponent, getCurrentInstance, computed } from 'vue';
import { formatDate } from '@/utils/index';
import service from 'src/service';
import { Message } from 'iview';

defineComponent({
  name: 'Alipay2Statistics',
})

const ins = getCurrentInstance()
const adminBusList = computed(() => ins.proxy.$store.getters.adminBusList)
if (!adminBusList.value) {
  ins.proxy.$store.dispatch('getAdminBusList')
}

const dateValue = ref(new Date())
const busId = ref(ins.proxy.$store.state.busId)
const typeValue = ref('1')

const handleSearch = () => {
  getList()
}

// table
// 场馆 签约月份 新签会员 履约中会员 已暂停会员 已解约会员 已完成会员 违约率 签约金额 支付成功 扣款失败 待收款 已暂停收款 已取消收款
const createLink = (h, params, key, category) => {
  return h('a', {
    on: {
      click: () => {
        const date = params.row.date
        ins.proxy.$router.push({ path: '/Web/AlipayMerchantCard/contracts', query: { busId: busId.value, date, category } })
      }
    }
  }, params.row[key])
}
const createLink2 = (h, params, key) => {
  return h('a', {
    on: {
      click: () => {
        const date = params.row.date
        let status = ''
        if (key === 'paid_subscription_money') {
          status = 'PAID'
        } else if (key === 'faild_subscription_money') {
          status = 'PAY_FAILED'
        } else if (key === 'paying_subscription_money') {
          status = 'WAIT_PAY'
        } else if (key === 'paused_subscription_money') {
          status = 'PAUSED'
        } else if (key === 'cancel_subscription_money') {
          status = 'CANCEL'
        }
        ins.proxy.$router.push({ path: '/Web/AlipayMerchantCard/fitItemsList', query: { busId: busId.value, date, status } })
      }
    }
  }, params.row[key])
}
const columns = ref([
  {
    title: '场馆',
    key: 'bus_name',
  },
  {
    title: '签约月份',
    key: 'month',
  },
  {
    title: '新签会员',
    key: 'additional_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'additional_subscription_count', 'ALL')
    },
  },
  {
    title: '履约中会员',
    key: 'cumulative_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'cumulative_subscription_count', 'NORMAL')
    }
  },
  {
    title: '已暂停会员',
    key: 'paused_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'paused_subscription_count', 'PAUSED')
    }
  },
  {
    title: '已解约会员',
    key: 'additional_surrender_count',
    render: (h, params) => {
      return createLink(h, params, 'additional_surrender_count', 'SURRENDER')
    }
  },
  {
    title: '已完成会员',
    key: 'additional_end_count',
    render: (h, params) => {
      return createLink(h, params, 'additional_end_count', 'END')
    }
  },
  {
    title: '违约率',
    key: 'surrender_rate_str',
    renderHeader: (h) => {
      return h('Tooltip', {
        props: { placement: 'top', transfer: true }
      }, [
        h('span', '违约率 '),
        h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
        h('div', {
          slot: 'content',
          style: {
            whiteSpace: 'normal',
            wordBreak: 'break-all',
            textAlign: 'left'
          }
        }, [
          h('p', '违约率 = 已解约 / 新签会员'),
        ])
      ])
    },
    render: (h, params) => {
      return h('div', `${params.row.surrender_rate}%`)
    }
  },
  {
    title: '签约付款',
    key: 'sign_deduction_money',
    render: (h, params) => {
      return createLink(h, params, 'sign_deduction_money', 'ALL')
    }
  },
  {
    title: '签约金额',
    key: 'sign_subscription_money',
    render: (h, params) => {
      return createLink(h, params, 'sign_subscription_money', 'ALL')
    }
  },
  {
    title: '支付成功',
    key: 'paid_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paid_subscription_money')
    }
  },
  {
    title: '扣款失败',
    key: 'faild_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'faild_subscription_money')
    }
  },
  {
    title: '待收款',
    key: 'paying_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paying_subscription_money')
    }
  },
  {
    title: '已暂停收款',
    key: 'paused_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paused_subscription_money')
    }
  },
  {
    title: '已取消收款',
    key: 'cancel_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'cancel_subscription_money')
    }
  },
])
const list = ref([])
// 场馆 签约月份 新签合约 履约中合约 已暂停合约 已解约合约 已完成合约 违约率 新签金额 支付成功 扣款失败 待收款 已暂停收款 已取消收款
const columns2 = ref([
  {
    title: '场馆',
    key: 'bus_name',
  },
  {
    title: '签约月份',
    key: 'month',
  },
  {
    title: '新签合约',
    key: 'additional_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'additional_subscription_count', 'ALL')
    }
  },
  {
    title: '履约中合约',
    key: 'cumulative_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'cumulative_subscription_count', 'NORMAL')
    }
  },
  {
    title: '已暂停合约',
    key: 'paused_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'paused_subscription_count', 'PAUSED')
    }
  },
  {
    title: '已解约合约',
    key: 'surrender_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'surrender_subscription_count', 'SURRENDER')
    }
  },
  {
    title: '已完成合约',
    key: 'end_subscription_count',
    render: (h, params) => {
      return createLink(h, params, 'end_subscription_count', 'END')
    }
  },
  {
    title: '违约率',
    key: 'surrender_rate_str',
    renderHeader: (h) => {
      return h('Tooltip', {
        props: { placement: 'top', transfer: true }
      }, [
        h('span', '违约率 '),
        h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
        h('div', {
          slot: 'content',
          style: {
            whiteSpace: 'normal',
            wordBreak: 'break-all',
            textAlign: 'left'
          }
        }, [
          h('p', '违约率 = 已解约 / 新签会员'),
        ])
      ])
    },
    render: (h, params) => {
      return h('div', `${params.row.surrender_rate}%`)
    }
  },
  {
    title: '签约付款',
    key: 'sign_deduction_money',
    render: (h, params) => {
      return createLink(h, params, 'sign_deduction_money', 'ALL')
    }
  },
  {
    title: '签约金额',
    key: 'sign_subscription_money',
    render: (h, params) => {
      return createLink(h, params, 'sign_subscription_money', 'ALL')
    }
  },
  {
    title: '支付成功',
    key: 'paid_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paid_subscription_money')
    }
  },
  {
    title: '扣款失败',
    key: 'faild_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'faild_subscription_money')
    }
  },
  {
    title: '待收款',
    key: 'paying_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paying_subscription_money')
    }
  },
  {
    title: '已暂停收款',
    key: 'paused_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'paused_subscription_money')
    }
  },
  {
    title: '已取消收款',
    key: 'cancel_subscription_money',
    render: (h, params) => {
      return createLink2(h, params, 'cancel_subscription_money')
    }
  }
])
const list2 = ref([])

const getList = () => {
  const date = formatDate(dateValue.value, 'yyyy')
  return service.post('/Web/AlipayMerchantCard/getStatisticsListAll', {
    date,
    bus_id: busId.value,
    type: typeValue.value
  }).then(({ data }) => {
    if (data.errorcode === 0) {
      data.data.forEach((item) => {
        item.sign_deduction_money = Number(item.sign_deduction_money).toFixed(2)
        item.sign_subscription_money = Number(item.sign_subscription_money).toFixed(2)
        item.paid_subscription_money = Number(item.paid_subscription_money).toFixed(2)
        item.faild_subscription_money = Number(item.faild_subscription_money).toFixed(2)
        item.paying_subscription_money = Number(item.paying_subscription_money).toFixed(2)
        item.paused_subscription_money = Number(item.paused_subscription_money).toFixed(2)
        item.cancel_subscription_money = Number(item.cancel_subscription_money).toFixed(2)
      })
      if (typeValue.value == 1) {
        list.value = data.data
      } else {
        list2.value = data.data
      }
    } else {
      Message.error(data.errormsg)
    }
  })
}
getList()

const table = ref(null)
const handleExcel = () => {
  const date = formatDate(dateValue.value, 'yyyy')
  const data = typeValue.value == 1 ? [...list.value] : [...list2.value]
  data.forEach((item) => {
    item.surrender_rate_str = `${item.surrender_rate}%`
  })
  const exportColumns = typeValue.value == 1 ? columns.value : columns2.value
  const filename = typeValue.value == 1 ? '签约数据统计-按会员-' + date : '签约数据统计-按合约-' + date
  table.value.exportCsv({
    filename,
    columns: exportColumns,
    data
  })
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>