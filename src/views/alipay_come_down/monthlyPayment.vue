<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card" @on-click="handleTabChange">
        <TabPane :label="(h) => badgeFn(h, labelList[index], valueList[index])" :name="item"
          v-for="(item, index) in tabList" :key="index">
          <TheSearchPanel @emitCount="setCount" :category="item" :tabList="tabList" :lazy="index > 0"
            :tabName="tabName" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import TheSearchPanel from './components/TheMonthlySearchPanel.vue'

export default {
  name: 'Alipay2Tabs',
  components: {
    TheSearchPanel,
  },
  data() {
    const badgeFn = (h, label, count) => {
      return h('div', [
        h('span', label),
        h('Badge', {
          style: {
            marginLeft: '10px',
          },
          props: {
            count,
            overflowCount: 999,
          },
        }),
      ])
    }
    return {
      badgeFn,
      tabName: 'ALL',
      tabList: ['ALL', 'NORMAL', 'PAUSED', 'SURRENDER', 'END', 'WAIT_SURRENDER'],
      labelList: ['全部', '履约中', '已暂停', '已解约', '已完成', '待解约'],
      valueList: [0, 0, 0, 0, 0, 0],
    }
  },
  methods: {
    setCount(count, category) {
      if (category === this.tabList[0]) {
        this.valueList[0] = count
      } else if (category === this.tabList[1]) {
        this.valueList[1] = count
      } else if (category === this.tabList[2]) {
        this.valueList[2] = count
      } else if (category === this.tabList[3]) {
        this.valueList[3] = count
      } else if (category === this.tabList[4]) {
        this.valueList[4] = count
      } else if (category === this.tabList[5]) {
        this.valueList[5] = count
      }
      this.$forceUpdate()
    },
    handleTabChange(name) {
      this.tabName = name
    }
  },
  created() {
    setTimeout(() => {
      this.$forceUpdate()
    }, 3000)
  },
  activated() {
    const { category } = this.$route.query
    if (category) {
      // console.log('changed.........')
      this.tabName = category
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 20px;
}
</style>
