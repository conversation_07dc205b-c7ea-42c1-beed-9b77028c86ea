<template>
  <div class="container">
    <header>
      <h3>{{ productId ? '编辑产品' : '新增产品' }}</h3>
    </header>
    <Form label-position="right" ref="form" :model="formItem" class="form" :label-width="200">
      <div style="text-align: center; margin-bottom: 20px">
        <RadioGroup v-model="formItem.product_type" type="button" @on-change="handleProductTypeChange">
          <Radio :label="1">常规方案</Radio>
          <Radio :label="2" :disabled="!combinationAuthority">组合方案</Radio>
        </RadioGroup>
      </div>
      <FormItem label="产品名称" prop="product_title" :rules="{ required: true, message: '请填写产品名称', trigger: 'blur' }">
        <Input v-model="formItem.product_title" class="input" placeholder="产品名称" />
      </FormItem>
      <!-- <Form-item label="会员卡类型" prop="cardType" v-if="!productId">
        <RadioGroup v-model="formItem.cardType" type="button" @on-change="handleFilterCard" :disabled="!!productId">
          <Radio label="-1">全部</Radio>
          <Radio label="0">单店卡</Radio>
          <Radio label="1">多店通用卡</Radio>
        </RadioGroup>
      </Form-item>
      <Form-item label prop="subCardType" v-if="!productId">
        <RadioGroup v-model="formItem.subCardType" type="button" :disabled="!!productId" @on-change="handleFilterCard">
          <Radio label="-1">全部</Radio>
          <Radio label="1">期限卡</Radio>
          <Radio label="7">私教包月</Radio>
        </RadioGroup>
      </Form-item>
      <Form-item label="会员卡名称" prop="card_id" :rules="{ required: true, message: '请选择会员卡' }">
        <Input v-if="productId" v-model="formItem.card_name" class="input" disabled />
        <Select placeholder="请选择" v-if="!productId && filterCardList && filterCardList.length"
          v-model="formItem.card_id" @on-change="cardChange" :disabled="!!productId" filterable>
          <Option v-for="item in filterCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
        </Select>
      </Form-item>

      <div class="card-info" v-if="!productId">
        <div>购买天数：{{ selectedCard.number }}</div>
        <div>赠送天数：{{ selectedCard.gift_number }}</div>
        <div>售价：{{ selectedCard.current_price }}</div>
      </div>
      <Form-item label="产品类型">连续包月</Form-item> -->
      <!-- alipay monthly -->
      <Form-Item label="产品类型" prop="business_type">
        <RadioGroup v-model="formItem.business_type" @on-change="handleBusinessTypeChange">
          <Radio :label="2">
            <Tooltip content="产品分期进行收款，未收款不影响产品使用，需要人工催收回款，适合年卡等长期产品">
              分期付产品
              <Icon type="md-help-circle" />
            </Tooltip>
          </Radio>
          <Radio :label="1">
            <Tooltip>
              月付产品
              <Icon type="md-help-circle" />
              <div slot="content">
                <div>按月订阅，每月进行一次扣款，扣款成功后才下发产品，</div>
                <div>未扣款成功产品无法使用，适合月卡包月等产品</div>
              </div>
            </Tooltip>
          </Radio>
        </RadioGroup>
      </Form-Item>
      <Form-Item label="卡种" prop="card_id" :rules="{ required: true, message: '请选择卡种' }">
        <Select placeholder="请选择" v-model="formItem.card_id" @on-change="cardChange" filterable>
          <Option v-for="item in cardFilter" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
        </Select>
      </Form-Item>
      <Form-item label="合约生成">
        <RadioGroup v-model="formItem.gen_type" @on-change="handleGenTypeChange">
          <Radio :label="0">签约后生成</Radio>
          <Radio :label="1" :disabled="formItem.product_type === 2">首期扣款成功后生成</Radio>
        </RadioGroup>
      </Form-item>
      <Form-Item label="体验卡发放" v-if="formItem.product_type === 1">
        <div style="display: flex; flex-wrap: nowrap; flex-direction: row;">
          <Checkbox style="width: 15%;" v-model="formItem.experience_at_once" :true-value="1" :false-value="0"
            :disabled="formItem.gen_type === 0">发放并激活</Checkbox>
          <ExpCardSelect style="width: 85%;" v-show="formItem.experience_at_once == 1"
            :value="formItem.experience_card_id" :clearable="false" card_type_ids="1,3,4"
            @on-change="handleExperienceCardChange" />
        </div>
      </Form-Item>
      <Divider />
      <div v-if="formItem.product_type === 1" style="margin: 20px">
        <div style="text-align: center; margin-bottom: 20px">
          <RadioGroup v-model="payMode" type="button">
            <Radio :label="1">标准月付</Radio>
            <Radio :label="2">自定义月付</Radio>
          </RadioGroup>
        </div>
        <div style="display: flex; flex-wrap: nowrap; flex-direction: row; overflow: hidden;">
          <transition name="slide-fade">
            <Card v-show="payMode === 1" style="min-width: 100%;" :bordered="false">
              <Form-item label="签约期数" prop="periods" :rules="{ required: true, message: '请选择' }">
                <Select v-model="formItem.periods" @on-change="loadStepDiscountList" filterable>
                  <Option v-for="item in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]" :key="item" :value="item">{{ item }}</Option>
                </Select>
              </Form-item>
              <Form-item label="首期金额" prop="down_payment"
                :rules="{ required: true, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.down_payment">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="划线价" prop="first_amount_underline"
                :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.first_amount_underline">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="单期金额" prop="deduction_amount" :rules="{ required: true, validator: lessThan5000 }">
                <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="划线价" prop="deduction_amount_underline"
                :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount_underline">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="方案总金额">
                {{ totalAmount }} 元
              </Form-item>
            </Card>
          </transition>
          <transition name="slide-fade">
            <Card v-show="payMode === 2" style="min-width: 100%;" :bordered="false">
              <Form-item label="方案总金额" prop="hopeTotalAmount"
                :rules="{ required: true, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.hopeTotalAmount">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="签约期数" prop="periods" :rules="{ required: true, message: '请选择' }">
                <Select placeholder="请选择" v-model="formItem.periods" filterable>
                  <Option v-for="item in 24" :key="item" :value="item">{{ item }}</Option>
                </Select>
              </Form-item>
              <Form-item label="首期金额" prop="down_payment"
                :rules="{ required: true, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.down_payment">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="划线价" prop="first_amount_underline"
                :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.first_amount_underline">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="单期金额">
                <div v-if="gossipSingleAmount >= 0" :class="fakeSingleValue ? 'fake-value' : ''">{{ gossipSingleAmount
                  }}
                  元 <span v-if="gossipSingleAmount > 5000 && !fakeSingleValue" style="color: red">(注意: 单期金额超过
                    5000)</span>
                </div>
                <div v-if="fakeSingleValue">{{ gossipSingleAmount.toFixed(2) }} 元 <span v-if="gossipSingleAmount > 5000"
                    style="color: red">(注意: 单期金额超过 5000)</span></div>
              </Form-item>
              <Form-item label="划线价" prop="deduction_amount_underline"
                :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount_underline">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="方案总金额">
                <div v-if="gossipTotalAmount >= 0" :class="fakeTotalValue ? 'fake-value' : ''">{{ gossipTotalAmount }} 元
                </div>
                <div v-if="fakeTotalValue">{{ gossipTotalAmount2 }} 元</div>
                <!-- {{ gossipTotalAmount2 }} 元 -->
              </Form-item>
            </Card>
          </transition>
        </div>
      </div>
      <div v-if="formItem.product_type === 2">
        <h3 id="payItemListEl" style="margin: 20px; text-align: center;">首次支付订单设置</h3>
        <div
          style="display: flex; flex-wrap: nowrap; flex-direction: row; justify-content: center; align-items: center; margin: 10px;"
          v-for="(item, index) in payItemList" :key="'i_' + index">
          <Tag color="green">{{ index + 1 }}</Tag>
          <Input v-model="item.title" placeholder="请填写收费项目名称" style="width: 300px; margin: 0 10px" />
          <Input v-model="item.amount" placeholder="请填写收费项目金额" type="number" style="width: 300px; margin: 0 10px">
          <span slot="append">元</span>
          </Input>
          <Icon v-if="index === 0" type="md-add-circle" size="30" style="cursor: pointer" @click="handleAddPayItem" />
          <Icon v-else type="ios-loading" size="30" />
          <Icon type="md-remove-circle" size="30" style="cursor: pointer" @click="handleRemovePayItem(index)" />
        </div>
        <Divider />
        <Form-item label="首次支付项目划线价" prop="combine_underline"
          :rules="{ required: true, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
          <Input placeholder="请填写" type="number" v-model="formItem.combine_underline">
          <span slot="append">元</span>
          </Input>
        </Form-item>
      </div>
      <Divider v-if="formItem.product_type === 2" />
      <div v-if="formItem.product_type === 2">
        <h3 style="margin: 20px; text-align: center;">月付期数和金额设置</h3>
        <Form-item label="签约期数" prop="periods" :rules="{ required: true, message: '请选择' }">
          <Select v-model="formItem.periods" @on-change="loadStepDiscountList" filterable>
            <Option v-for="item in 24" :key="item" :value="item">{{ item }}</Option>
          </Select>
        </Form-item>
        <Form-item label="单期金额" prop="deduction_amount" :rules="{ required: true, validator: lessThan5000 }">
          <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount">
          <span slot="append">元</span>
          </Input>
        </Form-item>
        <Form-item label="划线价" prop="deduction_amount_underline"
          :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
          <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount_underline">
          <span slot="append">元</span>
          </Input>
        </Form-item>
        <Form-item label="方案总金额">
          {{ totalAmount }} 元
        </Form-item>
      </div>
      <!-- <Divider />
      <div>
        <h3 style="margin: 20px; text-align: center;">单期优惠金额设置</h3>
        <Form-item label="每期优惠金额" prop="discount_amount"
          :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
          <Input placeholder="请填写" type="number" v-model="formItem.discount_amount">
          <span slot="append">元</span>
          </Input>
        </Form-item>
        <Form-item label="实际首期金额">
          {{ realizedFirstAmount }} 元
        </Form-item>
        <Form-item label="实际后续单期金额">
          {{ realizedDeductionAmount }} 元
        </Form-item>
        <Form-item label="实际方案总金额">
          <div style="color: red">{{ realizedTotalAmount }} 元</div>
        </Form-item>
      </div> -->
      <Divider />
      <div>
        <h3 style="margin-top: 20px; text-align: center;">连续履约奖励设置</h3>
        <Alert type="warning" style="width: 80%; margin: 20px auto;">
          注意：履约奖励的优惠是只要用户没有解约（哪怕是扣款失败），也同样享有优惠。</Alert>
        <div
          style="display: flex; flex-wrap: nowrap; flex-direction: row; justify-content: center; align-items: center; margin: 10px;"
          v-for="(item, index) in stepDiscountList" :key="'i_' + index">
          <span>当会员成功履约</span>
          <Select v-model="item.periods" @on-change="loadStepDiscountList" style="width: 100px; margin: 0 10px"
            filterable>
            <Option v-for="j in item.range" :value="j" :key="'j_' + j">第 {{ j }} 期</Option>
          </Select>
          <span>往后每期优惠</span>
          <Input v-model="item.amount" placeholder="请填写" type="number" style="width: 300px; margin: 0 10px">
          <span slot="append">元</span>
          </Input>
          <Icon v-if="index === 0" type="md-add-circle" size="30" style="cursor: pointer"
            @click="handleAddStepDiscount" />
          <Icon v-else type="ios-loading" size="30" />
          <Icon type="md-remove-circle" size="30" style="cursor: pointer" @click="handleRemoveStepDiscount(index)" />
        </div>
      </div>
      <Divider />
      <div>
        <h3 style="margin-top: 20px; text-align: center;">月付方案规则说明</h3>
        <Alert type="warning" style="width: 80%; margin: 20px auto;">注意：请按提示完成月付方案的规则说明，尽可能详细和完整，同时支付宝将审核本规则说明。</Alert>
        <Form-Item label="使用规则" prop="purchase_notes_list"
          :rules="{ required: true, message: '请填写本月付方案的连续履约奖励的使用规则', trigger: 'blur' }">
          <Input v-model="formItem.purchase_notes_list" type="textarea" :rows="4" :maxlength="50" show-word-limit />
          <div v-if="formItem.purchase_notes_list" style="color: #ed4014; width: 100%; text-align: right;">{{
            formItem.purchase_notes_list.length }}/50
          </div>
        </Form-Item>
        <!-- <Form-Item label="描述" prop="use_desc" :rules="{ required: true, message: '请填写本月付方案连续履约奖励的描述', trigger: 'blur' }">
          <Input type="textarea" :rows="4" :maxlength="50" show-word-limit />
        </Form-Item> -->
        <!-- <Form-Item>
          <div class="image-description image-description-required" slot="label">
            <p class="label"><span style="color: #ed4014">*</span> 图片</p>
            <p class="tip">图片最佳尺寸: 400X600</p>
            <p class="tip">推荐图片大小: &lt;500kb</p>
            <p class="tip">格式限制: jpg、png</p>
          </div>
          <div class="image-box" v-show="formItem.product_pic">
            <div class="photo-box">
              <img class="photo" :src="formItem.product_pic" />
              <div class="delete-photo" @click="handlePhotoDelete">
                <Icon type="ios-close" size="45" />
              </div>
            </div>
          </div>
          <image-crop-upload refName="cropEverything" v-model="imageTemporary" :options="{ aspectRatio: 400 / 600 }"
            multiple url="/Web/AlipayMerchantCard/uploadPic" style="max-width: 690px" :outputWidth="400" :outputHeight="600" />
        </Form-Item> -->
      </div>
      <Divider />
      <div>
        <h3 style="margin: 20px; text-align: center;">会员解约设置</h3>
        <Form-Item label="解约方式" prop="customer_un_sign">
          <RadioGroup v-model="formItem.customer_un_sign" @on-change="handleCustomerUnSignChange">
            <Radio :label="0">场馆解约</Radio>
            <Radio :label="1">会员自行解约</Radio>
          </RadioGroup>
        </Form-Item>
        <Form-item v-if="formItem.customer_un_sign === 1" label="支付金额" prop="violate_amount"
          :rules="{ required: false, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为正数且只能保留两位小数' }">
          <Input placeholder="请填写" type="number" v-model="formItem.violate_amount">
          <span slot="append">元</span>
          </Input>
        </Form-item>
        <Form-Item v-else-if="formItem.customer_un_sign === 0" label="违约金收取方式" prop="un_sign_type">
          <RadioGroup v-model="formItem.un_sign_type" @on-change="handleUnSignTypeChange">
            <Radio :label="1">场馆线下收取</Radio>
            <Radio :label="2">支付宝自动扣款</Radio>
          </RadioGroup>
        </Form-Item>
        <template v-if="formItem.un_sign_type === 2 && formItem.customer_un_sign === 0">
          <Form-Item label="违约金总金额">
            {{ realizedDeductionAmount }} 元
            <span class="tips-info">（非固定值，因上述优惠活动的产生，实际扣款金额可能会减少）</span>
          </Form-Item>
          <Form-Item label="期数" prop="violate_periods">
            <Select v-model="formItem.violate_periods" filterable>
              <Option v-for="i in formItem.periods" :value="i" :key="i">{{ i }}</Option>
            </Select>
            <div class="tips-info">违约金按奖励期数进行金额的分摊计算</div>
          </Form-Item>
          <Form-Item label="单期违约金金额">
            {{ rewardAmount }} 元
            <div class="tips-info" style="font-size: 16px">注：在支付宝小程序上违约金将被称为“奖励金” 避免用户对违约金概念产生抵触</div>
          </Form-Item>
        </template>
      </div>
      <Divider v-if="formItem.product_type === 2" />
      <Form-Item label="折扣券" v-if="formItem.product_type === 2">
        <div style="display: flex; flex-wrap: nowrap; flex-direction: row;">
          <Checkbox style="width: 15%;" v-model="formItem.support_coupon" :true-value="1" :false-value="0">支持</Checkbox>
          <span class="tips-info">（仅支持抵扣首笔付款金额）</span>
        </div>
      </Form-Item>
      <Divider />
      <Form-Item>
        <RadioGroup v-model="formItem.cycle_charge_type">
          <Radio label="FIXED_DATE">固定日期扣款模式</Radio>
          <Radio label="FIXED_DAYS">T+N扣款模式</Radio>
        </RadioGroup>
      </Form-Item>
      <Form-item v-if="formItem.cycle_charge_type === 'FIXED_DAYS'" label="扣款间隔天数" prop="down_payment_date_rule"
        :rules="[{ required: true, pattern: /^\d+$/, message: '必须为正整数' }, { type: 'number', transform: (value) => Number(value), min: 7, max: 60, message: '扣款日期范围为7~60天' }]">
        <div class="custom-input">
          <div class="prepend">签约后</div>
          <InputNumber placeholder="请填写" v-model="formItem.down_payment_date_rule" :min="7" :max="60"
            :active-change="false">
          </InputNumber>
          <div class="append">天扣款</div>
        </div>
        <div class="tips-info" style="line-height: 1.4; margin-top: 6px">选定签约后7~60天中的任意一天进行扣款作为扣款时间，如1号签约设置扣款间隔天数为7天，那么每月8号将作为合约扣款日期。</div>
      </Form-item>
      <!-- <Form-item label="扣款周期">
        <Input placeholder="请填写" disabled :value="30">
        <span slot="append">天</span>
        </Input>
        <div class="tips-info">此卡签署协议成功后立即开卡，后续按照自然月进行扣款</div>
      </Form-item> -->
      <Form-Item v-if="formItem.cycle_charge_type === 'FIXED_DATE'" label="月扣款日期" prop="deduction_days">
        <div class="custom-input">
          <div class="prepend">签约后每月</div>
          <InputNumber placeholder="请输入1-28之间的数字" v-model="formItem.deduction_days" :min="1" :max="28"
            :active-change="false">
          </InputNumber>
          <div class="append">号</div>
        </div>
        <div class="tips-info" style="line-height: 1.4; margin-top: 6px">您需要在这里指定每月的扣款日期，您可以选择1-28号之间任意的日期扣款，在扣款时，如果会员的实际签约时间与设置的月扣款日期小于7天，支付宝将在下月进行扣款；如果会员的实际签约时间与设置的月扣款日期大于7天，则在本月扣款。</div>
      </Form-Item>
      <Divider />
      <div>
        <h3 style="margin: 20px; text-align: center;">会员履约完成的会员卡时间赠送</h3>
        <Form-Item label="赠送时间">
          <Select v-model="formItem.give_mount" clearable>
            <Option v-for="item in [1, 2, 3]" :value="item" :key="item">赠送 {{ item }} 个月</Option>
          </Select>
        </Form-Item>
      </div>
      <Form-item>
        <div class="buttons">
          <Button v-if="canSubmit" type="primary" @click="addPro">提交</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </Form-item>
    </Form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// alipay monthly
import Big from 'big.js';
// import ImageCropUpload from '@/components/form/cropper.vue'
import ExpCardSelect from '@/components/form/expCardSelect';

export default {
  name: 'zhima2ProductAdd',
  components: {
    // ImageCropUpload,
    ExpCardSelect
  },
  data() {
    const lessThan5000 = (rule, value, callback) => {
      if (/^-?[0-9]+(.[0-9]{1,2})?$/.test(value)) {
        if (value < 0) {
          callback(new Error('金额不能小于 0'))
        } else if (value > 5000) {
          callback(new Error('单期金额不能超过 5000'))
        } else {
          callback()
        }
      } else {
        callback(new Error('金额必须为数字且只能保留两位小数'))
      }
    }
    return {
      formItem: {
        cardType: '-1',
        subCardType: '-1',
        product_no: this.$route.query.product_no || '',//服务中心产品编号，编辑时必须
        product_title: '',//产品名称
        card_id: '',//卡id
        card_name: '',//卡名称
        universal_card: '',//是否是通卡
        card_type_id: '',//卡类型
        purchase_volume: '',//购买天数
        gift_volume: '',//赠送天数
        sale_amount: '',//售价
        is_pt_time_limit_card: '',//私教包月卡标识
        sell_type: 1,//1—连续包月，默认为1
        periods: 12,//int	签约期数
        down_payment_date_rule: 7,
        period_day: 30,//每期天数，前期固定30天
        down_payment: '',
        deduction_amount: '',//每月扣减固定金额
        // alipay monthly
        gen_type: 1,
        hopeTotalAmount: 0,
        product_type: 1,
        experience_at_once: 0,
        experience_card_id: '',
        first_amount_underline: '',
        deduction_amount_underline: '',
        discount_amount: '',
        purchase_notes_list: '',
        product_pic: '',
        reward_rule: [],
        customer_un_sign: 0,
        un_sign_type: 1,
        violate_amount: '',
        violate_periods: '',
        give_mount: '',
        combine_list: [],
        combine_underline: '',
        deduction_days: 1,
        business_type: 2,
        cycle_charge_type: 'FIXED_DATE' // 产品计费模式 FIXED_DATE 指定日期 FIXED_DAYS t+n规则 (默认FIXED_DATE)
      },
      selectedCard: {},
      filterCardList: [],
      productId: this.$route.query.product_no,
      open_merchant_id: this.$route.query.open_merchant_id,
      // alipay monthly
      payMode: 1,
      lessThan5000,
      imageTemporary: '',
      stepDiscountList: [],
      payItemList: [],
      combinationAuthority: false
    }
  },
  async created() {
    await this.getCombinationAuthority()
    if (this.productId) {
      await this.getBonusInfo()
    } else {
      await this.changeCardList(this.$store.state.busId)
    }
    this.loadStepDiscountList()
    this.loadPayItemList()

    if (!this.canSubmit) {
      this.$Message.warning('您没有权限编辑产品')
    }
  },
  mounted() { },
  watch: {
    imageTemporary(newValue) {
      if (newValue) {
        this.formItem.product_pic = newValue
      }
    },
  },
  computed: {
    ...mapGetters(['addCardList']),
    totalAmount() {
      // alipay monthly
      // const firstAmount = Number(this.formItem.down_payment) || 0
      // const periods = firstAmount?parseInt(this.formItem.periods)-1:parseInt(this.formItem.periods)
      // const otherAmount = periods * this.formItem.deduction_amount
      // return (firstAmount + otherAmount).toFixed(2)
      let bigFirst = null
      let bigPeriods = null
      if (this.formItem.product_type == 1) {
        bigFirst = new Big(this.formItem.down_payment || 0)
        bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      } else {
        bigFirst = new Big(this.formItem.deduction_amount || 0)
        bigPeriods = new Big(Number(this.formItem.periods) - 1)
      }
      const bigDeduction = new Big(this.formItem.deduction_amount || 0)
      const bigOther = bigPeriods.times(bigDeduction)
      return bigFirst.plus(bigOther)
    },
    gossipSingleAmount() {
      if (!this.formItem.hopeTotalAmount ||
        Number(this.formItem.hopeTotalAmount || 0) <= Number(this.formItem.down_payment || 0)) {
        return new Big(0)
      }
      const bigTotal = new Big(this.formItem.hopeTotalAmount || 0)
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigOther = bigTotal.minus(bigFirst)
      return bigOther.div(bigPeriods)
    },
    gossipTotalAmount() {
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigSingle = this.gossipSingleAmount
      const bigOther = bigPeriods.times(bigSingle)
      return bigFirst.plus(bigOther)
    },
    gossipTotalAmount2() {
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigSingle2 = new Big(this.gossipSingleAmount.toFixed(2))
      const bigOther = bigPeriods.times(bigSingle2)
      return bigFirst.plus(bigOther)
    },
    fakeSingleValue() {
      const bigSingle2 = new Big(this.gossipSingleAmount.toFixed(2))
      return this.gossipSingleAmount >= 0 &&
        (this.gossipSingleAmount.toString() != bigSingle2.toString())
    },
    fakeTotalValue() {
      return this.gossipTotalAmount2 >= 0 &&
        (this.gossipTotalAmount.toString() != this.gossipTotalAmount2.toString())
    },
    realizedFirstAmount() {
      let bigFirst = null
      if (this.formItem.product_type == 1) {
        bigFirst = new Big(this.formItem.down_payment || 0)
      } else {
        bigFirst = new Big(this.formItem.deduction_amount || 0)
      }
      const discountAmount = new Big(this.formItem.discount_amount || 0)
      return bigFirst.minus(discountAmount)
    },
    realizedDeductionAmount() {
      let bigDeduction = null
      if (this.payMode === 1) {
        bigDeduction = new Big(this.formItem.deduction_amount || 0)
      } else {
        bigDeduction = new Big(this.gossipSingleAmount.toFixed(2))
      }
      const discountAmount = new Big(this.formItem.discount_amount || 0)
      return bigDeduction.minus(discountAmount)
    },
    realizedTotalAmount() {
      // let bigFirst = null
      // const discountAmount = new Big(this.formItem.discount_amount || 0)
      // let bigPeriods = null
      // if (this.formItem.product_type == 1) {
      //   bigFirst = new Big(this.formItem.down_payment || 0)
      //   bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      // } else {
      //   bigFirst = new Big(this.formItem.deduction_amount || 0)
      //   bigPeriods = new Big(Number(this.formItem.periods) - 1)
      // }
      // bigFirst = bigFirst.minus(discountAmount)
      // let bigDeduction = new Big(this.formItem.deduction_amount || 0)
      // bigDeduction = bigDeduction.minus(discountAmount)
      // const bigOther = bigPeriods.times(bigDeduction)
      // return bigFirst.plus(bigOther)
      const bigFirst = new Big(this.realizedFirstAmount)
      const bigDeduction = new Big(this.realizedDeductionAmount)
      const bigPeriods = new Big(Number(this.formItem.periods) - 1)
      const bigOther = bigPeriods.times(bigDeduction)
      return bigFirst.plus(bigOther)
    },
    rewardAmount() {
      const price = this.realizedDeductionAmount
      const count = new Big(this.formItem.violate_periods || 1)
      return price.div(count).toFixed(2)
    },
    canSubmit() {
      if (this.formItem.product_type == 1) {
        return true
      } else {
        return this.combinationAuthority
      }
    },
    cardFilter() {
      if (!Array.isArray(this.addCardList)) {
        return []
      }
      if (this.formItem.business_type == 2) {
        // 次卡 期限卡 储值卡 私教课 泳教课
        return this.addCardList.filter(item => {
          const cardTypeId = Number(item.card_type_id)
          const saleStatus = Number(item.sale_status)
          return [2, 1, 3, 4, 5].includes(cardTypeId) && saleStatus === 1
        })
      } else {
        // 私教包月 期限卡
        return this.addCardList.filter(item => {
          const cardTypeId = Number(item.card_type_id)
          const isCoachMonthCard = Number(item.is_pt_time_limit_card)
          const saleStatus = Number(item.sale_status)
          return (cardTypeId === 1 || isCoachMonthCard === 1) && saleStatus === 1
        })
      }
    }
  },
  methods: {
    getCombinationAuthority() {
      return this.$service.post('/Web/AlipayMerchantCard/getCombineOpen').then(res => {
        if (res.data.errorcode === 0) {
          this.combinationAuthority = res.data.data == 2
        }
      })
    },
    cardChange(cardId) {
      for (let item of this.addCardList) {
        if (item.card_id == cardId) {
          this.selectedCard = item
          this.formItem.card_id = item.card_id
          this.formItem.card_name = item.card_name
          this.formItem.universal_card = item.universal_card
          this.formItem.card_type_id = item.card_type_id
          this.formItem.purchase_volume = item.number
          this.formItem.gift_volume = item.gift_number
          this.formItem.sale_amount = item.sale_amount
          this.formItem.is_pt_time_limit_card = item.is_pt_time_limit_card
          break
        }
      }
    },
    changeCardList(busId) {
      return this.$store.dispatch('getAddCardList', { belongBusId: busId }).then(res => {
        this.handleFilterCard()
      })
    },
    handleFilterCard() {
      let arr = [].concat(this.addCardList.filter(item => {
        return item.sale_status === '1' && (item.is_pt_time_limit_card == 1 || item.card_type_id == 1)
      }))
      if (this.formItem.cardType != '-1') {
        arr = arr.filter(item => item.universal_card === this.formItem.cardType)
      }
      if (this.formItem.subCardType === '7') {
        arr = arr.filter(item => item.is_pt_time_limit_card == 1)
      }
      if (this.formItem.subCardType == 1) {
        arr = arr.filter(item => item.card_type_id == 1)
      }
      this.filterCardList = arr
    },
    addPro() {
      // alipay monthly
      if (this.payMode == 2) {
        if (this.gossipSingleAmount > 5000) {
          return this.$Message.error('单期金额不能超过 5000')
        } else {
          this.formItem.deduction_amount = this.gossipSingleAmount.toFixed(2)
        }
      }
      if (this.stepDiscountList.length === 1 && !this.stepDiscountList[0].amount) {
        this.formItem.reward_rule = []
      } else {
        this.formItem.reward_rule = this.stepDiscountList.map(item => {
          return {
            periods: item.periods,
            amount: item.amount
          }
        })
      }
      if (this.formItem.product_type === 2) {
        if (!this.checkPayItemList()) {
          return
        }
        this.formItem.combine_list = this.payItemList
      } else {
        this.formItem.combine_list = []
        this.formItem.combine_underline = ''
      }
      if (this.formItem.un_sign_type == 2 && this.formItem.customer_un_sign == 0) {
        this.formItem.violate_amount = this.realizedDeductionAmount
        if (!this.formItem.violate_periods) {
          return this.$Message.error('请选择奖励期数')
        }
      }
      this.$refs.form.validate(valid => {
        if (!valid) {
          return this.$Message.error('请完成信息填写');
        }
        this.$service
          .post('/Web/AlipayMerchantCard/productUpsert', {
            ...this.formItem,
            down_payment_date_rule: `t+${this.formItem.down_payment_date_rule}`
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      });
    },
    getBonusInfo() {
      return this.$service
        .post('/Web/AlipayMerchantCard/productInfo', { open_merchant_id: this.open_merchant_id, product_no: this.productId })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.formItem = {
              ...this.formItem,
              ...res.data.data,
              down_payment_date_rule: Number(res.data.data.down_payment_date_rule),
              gen_type: Number(res.data.data.gen_type),
              // hopeTotalAmount: Number(res.data.data.total_amount) + Number(res.data.data.discount_amount) * Number(res.data.data.periods),
              hopeTotalAmount: new Big(Number(res.data.data.total_amount)).plus(new Big(Number(res.data.data.discount_amount)).times(new Big(Number(res.data.data.periods)))).toNumber(),
              product_type: Number(res.data.data.product_type),
              first_amount_underline: Number(res.data.data.first_amount_underline),
              deduction_amount_underline: Number(res.data.data.deduction_amount_underline),
              discount_amount: Number(res.data.data.discount_amount),
              customer_un_sign: Number(res.data.data.customer_un_sign),
              un_sign_type: Number(res.data.data.un_sign_type),
              combine_underline: Number(res.data.data.combine_underline),
              // down_payment: Number(res.data.data.down_payment) + Number(res.data.data.discount_amount),
              down_payment: new Big(Number(res.data.data.down_payment)).plus(new Big(Number(res.data.data.discount_amount))).toNumber(),
              // deduction_amount: Number(res.data.data.deduction_amount) + Number(res.data.data.discount_amount),
              deduction_amount: new Big(Number(res.data.data.deduction_amount)).plus(new Big(Number(res.data.data.discount_amount))).toNumber(),
              deduction_days: Number(res.data.data.deduction_days),
              business_type: Number(res.data.data.business_type),
            }
            const rule = res.data.data.reward_rule?.reward_rule || []
            if (Array.isArray(rule)) {
              rule.map(item => {
                item.periods = Number(item.periods)
                item.amount = Number(item.amount)
              })
              this.stepDiscountList = rule
            } else {
              this.stepDiscountList = []
            }
            this.payItemList = res.data.data.combine_list || []
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handlePhotoDelete() {
      this.formItem.product_pic = '';
    },
    loadStepDiscountList() {
      const end = this.formItem.periods - 1
      const range = Array.from({ length: end }, (_, i) => 1 + i)
      if (this.stepDiscountList.length === 0) {
        this.stepDiscountList = [{ periods: '', amount: '', range }]
      } else {
        const list = []
        let begin = 0
        this.stepDiscountList.forEach(item => {
          const itemRange = range.slice(begin, end)
          let itemPeriods = begin + 1
          if (itemRange.includes(item.periods)) {
            itemPeriods = item.periods
          }
          if (itemPeriods <= end) {
            list.push({
              periods: itemPeriods,
              amount: item.amount,
              range: itemRange
            })
          } else if (end > 0) {
            this.$Message.warning(`连续履约奖励不能超过 ${end} 期`)
          } else {
            this.$Message.warning('无法设置连续履约奖励')
          }
          begin = itemPeriods
        })
        this.stepDiscountList = [...list]
      }
    },
    handleAddStepDiscount() {
      this.stepDiscountList.push({
        periods: '',
        amount: ''
      })
      this.loadStepDiscountList()
    },
    handleRemoveStepDiscount(index) {
      this.stepDiscountList.splice(index, 1)
      this.loadStepDiscountList()
    },
    handleGenTypeChange() {
      if (this.formItem.gen_type === 0) {
        this.formItem.experience_at_once = 0
        this.formItem.experience_card_id = ''
      }
    },
    handleProductTypeChange() {
      if (this.formItem.product_type === 1) {
        this.formItem.gen_type = 1
        this.formItem.down_payment_date_rule = 7
      } else {
        this.formItem.gen_type = 0
        this.formItem.down_payment_date_rule = 30
        // this.formItem.down_payment = ''
      }
    },
    handleExperienceCardChange(item) {
      this.formItem.experience_card_id = item.card_id
    },
    handleCustomerUnSignChange() {
      if (this.formItem.customer_un_sign === 0) {
        this.formItem.violate_amount = ''
        this.formItem.un_sign_type = 1
      }
    },
    handleUnSignTypeChange() {
      if (this.formItem.un_sign_type === 1) {
        this.formItem.violate_periods = ''
      }
    },
    loadPayItemList() {
      if (this.payItemList.length === 0) {
        this.payItemList = [{ title: '', amount: '' }]
      }
    },
    checkPayItemList() {
      let flag = true
      this.payItemList.forEach(item => {
        if (!item.title || !item.amount) {
          flag = false
        }
      })
      if (!flag) {
        window.location.href = '#payItemListEl'
        this.$Message.warning('请完善首次支付订单设置')
      }
      return flag
    },
    handleAddPayItem() {
      if (this.payItemList.length >= 5) {
        this.$Message.warning('最多添加5项')
        return
      }
      this.payItemList.push({
        title: '',
        amount: ''
      })
    },
    handleRemovePayItem(index) {
      this.payItemList.splice(index, 1)
      this.loadPayItemList()
    },
    handleBusinessTypeChange() {
      this.formItem.card_id = ''
    }
  }
}
</script>

<style lang="less" scoped>
.card-info {
  font-size: 12px;
  line-height: 26px;
  margin-left: 140px;
  color: #888;
  border: 1px solid #ddd;
  padding: 8px;
  margin-bottom: 20px;
}

.tips-info {
  font-size: 12px;
  color: #888;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.6s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(40px);
  opacity: 0;
  height: 0;
}

.fake-value {
  color: red;
  text-decoration: line-through;
}

.custom-input {
  display: flex;
  flex-direction: row;
  align-items: center;

  .prepend {
    padding: 4px 7px;
    font-size: 12px;
    font-weight: normal;
    line-height: 2;
    color: #666;
    text-align: center;
    background-color: #f8f8f9;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    border-right: none;
    display: table-cell;
    white-space: nowrap;
    vertical-align: middle;
    height: 32px;
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .append {
    padding: 4px 7px;
    font-size: 12px;
    font-weight: normal;
    line-height: 2;
    color: #666;
    text-align: center;
    background-color: #f8f8f9;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    border-left: none;
    display: table-cell;
    white-space: nowrap;
    vertical-align: middle;
    height: 32px;
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
}

.image-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: #f7f7f7;
  padding: 20px;
  margin-bottom: 20px;

  .photo-box {
    display: flex;
    flex-direction: row;

    .photo {
      width: 400px;
      height: 600px;
      margin-top: 10px;
    }

    .delete-photo {
      width: 45px;
      height: 45px;
      position: relative;
      top: -10px;
      left: -25px;
      background-color: rgba(0, 0, 0, 0.4);
      color: red;
      border-radius: 50%;
      overflow: hidden;
      cursor: pointer;
    }
  }
}
</style>
