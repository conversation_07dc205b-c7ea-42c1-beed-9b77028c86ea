<template>
  <div class="container">
    <div class="box">
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" @on-change="searchParams.belong_id = ''" placeholder="请选择场馆"
                class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">单号</div>
              <Input v-model="searchParams.order_sn" placeholder="请输入支付宝流水单号/订单编号/合约编号" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">时间</div>
              <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer :clearable="false" />
            </div>
            <div class="search-item">
              <div class="label">类型</div>
              <Select v-model="searchParams.fit_pay_type" placeholder="请选择类型" class="value" clearable transfer>
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">签约会员</div>
              <Input v-model="searchParams.keyword" placeholder="请输入会员手机号码" class="value" clearable />
            </div>
            <div class="search-item" v-if="searchParams.bus_id">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.belong_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="searchParams.bus_id" />
            </div>
            <div class="search-item">
              <div class="label">收入/支出</div>
              <Select v-model="searchParams.inout_type" placeholder="请选择收入/支出" class="value" clearable transfer>
                <Option value="1">收入</Option>
                <Option value="2">支出</Option>
              </Select>
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <div class="panel-box">
          <div class="panel-item" v-for="(item, index) in panelList" :key="index">
            <Card>
              <div class="value">{{ item.value }}</div>
              <div class="label">{{ item.label }}</div>
            </Card>
          </div>
        </div>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <!-- <Button style="margin-left: 10px" @click="handleExport">导出</Button> -->
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />

    <Modal v-model="saleModal" title="成单明细" width="40%" @on-cancel="handleSaleClose">
      <Table class="avatar-zoom" stripe :columns="saleColumns" :data="saleTable" disabled-hover></Table>
      <div slot="footer">
        <Button @click="handleSaleClose">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './components/TheDetailModal.vue'
import Big from 'big.js'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  order_sn: '',
  pay_time_start: '',
  pay_time_end: '',
  keyword: '',
  belong_id: '',
  fit_pay_type: '',
  inout_type: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'FinanceFlow',
  components: { SalesSelect, TheDetailModal },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 支付宝订单流水号 订单编号 合约编号 产品方案 会员 场馆 业绩归属 收入/ 支出 时间 类型 金额
      columns: [
        {
          title: '支付宝订单流水号',
          key: 'trade_no'
        },
        {
          title: '订单编号',
          key: 'order_sn'
        },
        {
          title: '合约编号',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no, out_subscription_no: params.row.out_subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '手机号',
          key: 'phone'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '业绩归属',
          key: 'sale_name',
          render: (h, params) => {
            // show modal detail
            return h('a', {
              on: {
                click: () => {
                  // const rowAmount = Big(params.row.amount)
                  if ([2, 5, 6, 7].includes(Number(params.row.fit_pay_type))) {
                    const list = Array.isArray(params.row.sale_info)
                      ? params.row.sale_info
                      : []
                    list.forEach(v => {
                      v.name = v.sale_name
                      v.percent = v.proportion + '%'
                      v.amount = '¥ ' + v.amount
                    })
                    this.saleTable = list
                    this.saleModal = true
                  } else if ([3, 4].includes(Number(params.row.fit_pay_type))) {
                    const list = []
                    list.push({
                      name: params.row.sale_name,
                      percent: '100%',
                      amount: "¥ " + params.row.amount
                    })
                    this.saleTable = list
                    this.saleModal = true
                  } else {
                    this.$Message.info('暂无业绩归属')
                  }
                }
              }
            }, params.row.sale_name)
          }
        },
        {
          title: '收入/支出',
          render: (h, params) => {
            if (params.row.fit_pay_type === 3) {
              return h('div', '支出')
            } else {
              return h('div', '收入')
            }
          }
        },
        {
          title: '时间',
          key: 'pay_time',
          render: (h, params) => {
            return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd HH:mm'))
          }
        },
        {
          title: '状态',
          render: (h, params) => {
            const item = this.statusList.find(item => item.value === params.row.fit_pay_type)
            const label = item ? item.label : '-'
            const color = item ? item.color : ''
            return h('div', { style: { color } }, label)
          }
        },
        {
          title: '金额',
          key: 'amount',
          render: (h, params) => {
            const price = Number(params.row.amount)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '操作',
          render: (h, params) => {
            if (params.row.fit_pay_type === 2) {
              return h('Button', {
                props: {
                  type: 'text',
                  size: 'small',
                },
                on: {
                  click: () => {
                    this.handleRefund(params.row.id)
                  }
                }
              }, '退款')
            } else {
              return h('div', '-')
            }
          }
        }
      ],
      list: [],
      total: 0,
      // 数据类型 2签约付款 3签约退款 4解约付款 5解约扣款 6首期扣款 7单期扣款 同时2代表支出，其他代表收入
      statusList: [
        { label: '签约付款', value: 2, color: 'gray' },
        { label: '签约退款', value: 3, color: 'gray' },
        { label: '解约付款', value: 4, color: 'gray' },
        { label: '解约扣款', value: 5, color: 'gray' },
        { label: '首期扣款', value: 6, color: 'gray' },
        { label: '单期扣款', value: 7, color: 'gray' },
      ],
      panelList: [
        { label: '总计金额', code: 'all', value: 0 },
        { label: '收入', code: 'in', value: 0 },
        { label: '支出', code: 'out', value: 0 },
      ],
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      },
      saleModal: false,
      saleTable: [],
      saleColumns: [
        { title: '姓名', key: 'name' },
        { title: '占比', key: 'percent' },
        { title: '金额', key: 'amount' }
      ]
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.pay_time_start = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.pay_time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.pay_time_start = ''
        this.searchParams.pay_time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.setSearchDateRange()
      this.handleSearch()
    },
    getList() {
      return this.$service.post('/Web/AlipayMerchantCard/financeContrastList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          const sum = res.data.data.sum
          this.panelList[0].value = new Big(sum.in).minus(sum.out).toFixed(2)
          this.panelList[1].value = Number(sum.in || 0).toFixed(2)
          this.panelList[2].value = Number(sum.out || 0).toFixed(2)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/AlipayMerchantCard/financeContrastList', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setSearchDateRange() {
      // last 30 days from today
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      this.daterange = [thirtyDaysAgo, today]
    },
    handleSaleClose() {
      this.saleModal = false
    },
    handleRefund(id) {
      // show modal for ensure
      this.$Modal.confirm({
        title: '提示',
        content: '是否确认要退款?',
        onOk: () => {
          this.$service.post('/Web/AlipayMerchantCard/fitAlipayOrderRefund', {
            id
          }).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              // this.getInfo()
              // this.getList(true, '0')
              // this.getList(true, '1')
              // this.refresh = 1
              // setTimeout(() => {
              //   router.push({ path: `/alipay-merchant/deductionOrder/${this.refresh}` })
              // })
            } else {
              this.$Message.error(res.data.errormsg)
            }
            this.getList()
          })
        }
      })
    },
  },
  async created() {
    this.setSearchDateRange()
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    if (this.$route.params.busId) {
      this.searchParams.bus_id = this.$route.params.busId
    } else {
      this.searchParams.bus_id = this.busId
    }
    if (this.$route.params.subscriptionNo) {
      this.searchParams.subscription_no = this.$route.params.subscriptionNo
    }
    // this.getList()
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;
        min-width: 180px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
          text-align: center;
        }
      }
    }
  }
}
</style>
