<template>
  <div class="search-panel">
    <Card>
      <div class="search-line">
        <div class="search-item">
          <div class="label">场馆</div>
          <Select v-model="searchParams.bus_id" placeholder="请选择场馆" class="value" filterable transfer>
            <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="search-item">
          <div class="label">合约编号</div>
          <Input v-model="searchParams.subscription_no" placeholder="请输入合约编号" class="value" clearable />
        </div>
        <div class="search-item" v-if="category === tabList[0]">
          <div class="label">合约状态</div>
          <Select v-model="searchParams.status" placeholder="请选择合约状态" class="value" clearable>
            <Option v-for="item in contractStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div>
        <div class="search-item">
          <div class="label">产品方案</div>
          <Input v-model="searchParams.product_title" placeholder="请输入月付方案名称" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">签约会员</div>
          <Input v-model="searchParams.search" placeholder="请输入会员姓名/手机号码" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">签约时间</div>
          <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
            transfer />
        </div>
        <div class="search-item" v-if="tabList.slice(2, 5).includes(category)">
          <div class="label">{{ dateLabel }}</div>
          <DatePicker v-model="otherDaterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
            transfer />
        </div>
        <div class="search-item" v-if="searchParams.bus_id && category !== tabList[4]">
          <div class="label">签约销售</div>
          <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
            :belongBusId="searchParams.bus_id" />
        </div>
        <div class="search-item">
          <Button type="success" @click="handleSearch">查询</Button>
        </div>
        <div class="search-item">
          <Button @click="handleReset">重置</Button>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 20px">
      <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
      <div style="margin-top: 10px; display: flex; justify-content: space-between;">
        <div>
          <Button type="success" v-if="category === tabList[1]" @click="handlePause">批量暂停</Button>
          <Button type="success" v-if="category === tabList[2]" @click="handleRecover">批量恢复</Button>
          <Button type="success" v-if="category === tabList[5]" @click="handleCancel">批量取消解约</Button>
          <Button type="success" v-if="category === tabList[5]" @click="handleSuspend"
            style="margin-left: 10px">批量解约</Button>
          <Button style="margin-left: 10px" @click="handleExport">导出</Button>
        </div>
        <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
          @on-page-size-change="handlePageSizeChange" show-total show-sizer>
        </Page>
      </div>
    </Card>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />
    <ZhimaSurrenderModal v-model="showSurrenderType" :info="curInfo" @on-success="getList" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './TheDetailModal.vue'
import ZhimaSurrenderModal from "./ZhimaSurrenderModal"

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  subscription_no: '',
  product_title: '',
  search: '',
  status: '', // 合约状态 履约中-NORMAL 扣款失败-PAY_FAILED 合约暂停-PAUSED 合约终止-SURRENDER
  marketers_id: '',
  sign_time_begin: '',
  sign_time_end: '',
  time_begin: '',
  time_end: '',
  delay_surrender: 0,
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TheSearchPanel',
  components: { SalesSelect, TheDetailModal, ZhimaSurrenderModal },
  props: {
    category: {
      type: String,
      required: true
    },
    tabList: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
    tabName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      daterange: [],
      otherDaterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      list: [],
      total: 0,
      // 正常 NORMAL， 已暂停 PAUSED， 已解约 SURRENDER，已完结 END
      contractStatusList: [
        { value: 'NORMAL', label: '正常', color: 'green', text: '履约中' },
        { value: 'PAUSED', label: '暂停', color: 'orange', text: '已暂停' },
        { value: 'SURRENDER', label: '解约', color: 'red', text: '已解约' },
        { value: 'END', label: '完成', color: 'blue', text: '已完成' },
      ],
      refresh: 0,
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      },
      showSurrenderType: false, // 解约方式弹窗
      curInfo: {},
      isLeave: false
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
    dateLabel() {
      if (this.category === this.tabList[2]) {
        return '合约暂停时间'
      } else if (this.category === this.tabList[3]) {
        return '解约时间'
      } else if (this.category === this.tabList[4]) {
        return '履约完成时间'
      } else {
        return '时间'
      }
    },
    columns() {
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 合约状态 操作
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 操作
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 合约暂停时间 操作
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 解约时间 违约金金额 追回奖励金金额 操作
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 履约完成时间 操作
      // 合约编号 产品方案 方案类型 实际签约付费 签约金额 首期金额 单期金额 会员 场馆 签约销售 签约时间 已履约期数 合约状态 预计解约时间 操作
      const chunk = [
        {
          title: '合约编号',
          key: 'subscription_no',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '产品方案',
          key: 'product_title'
        },
        {
          title: '方案类型',
          key: 'product_type',
          render: (h, params) => {
            const type = params.row.product_type
            const typeMap = {
              1: '常规',
              2: '组合'
            }
            return h('div', typeMap[type])
          }
        },
        {
          // title: '实际签约付款',
          key: 'first_pay_amount',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '实际签约付款 '),
              h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '签约付款金额 - 折扣券抵扣'),
              ])
            ])
          },
          render: (h, params) => {
            const price = Number(params.row.first_pay_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          // title: '签约金额',
          key: 'sign_amount',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '签约金额 '),
              h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '首期金额 + 单期金额 * 签约期数 - 优惠总金额'),
              ])
            ])
          },
          render: (h, params) => {
            const price = Number(params.row.sign_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '首期金额',
          key: 'down_payment',
          render: (h, params) => {
            const price = Number(params.row.down_payment || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '单期金额',
          key: 'deduction_amount',
          render: (h, params) => {
            const price = Number(params.row.deduction_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '手机号',
          key: 'phone'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '签约销售',
          key: 'sale_name'
        },
        {
          title: '签约时间',
          key: 'sign_time'
        },
        {
          // title: '已履约期数',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '已履约期数 '),
              h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '已完成扣款期数总和 / 签约期数'),
              ])
            ])
          },
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.paid_count}/${row.periods}`)
          }
        },
      ]
      const action = {
        title: '操作',
        render: (h, params) => {
          if (this.isLeave) {
            return h('span', '-')
          }
          return (
            <Dropdown on-on-click={name => this.handleAction(name, params.row)} transfer>
              <a href="javascript:void(0)">
                更多操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem name='detail'>合约详情</DropdownItem>
                <DropdownItem name='deduction'>履约扣款记录</DropdownItem>
                <DropdownItem name='pause' divided disabled={params.row.status !== 'NORMAL' || params.row.delay_surrender == 1}>暂停</DropdownItem>
                <DropdownItem name='suspend' disabled={!(params.row.status === 'NORMAL' || params.row.delay_surrender == 1)}>解约</DropdownItem>
                <DropdownItem name='recover' disabled={params.row.status !== 'PAUSED'}>恢复</DropdownItem>
                <DropdownItem name='cancel' divided disabled={params.row.delay_surrender != 1}>取消解约</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          )
        }
      }
      const cols = []
      if (this.category === this.tabList[0]) {
        cols.push(
          ...chunk,
          {
            title: '合约状态',
            key: 'status',
            render: (h, params) => {
              const status = params.row.status
              const contractStatus = this.contractStatusList.find(item => item.value === status)
              if (contractStatus) {
                return h('span', { style: { color: contractStatus.color } }, contractStatus.text)
              } else {
                return h('span', '-')
              }
            }
          },
          action)
      } else if (this.category === this.tabList[1]) {
        cols.push(
          {
            type: 'selection',
            width: 60
          },
          ...chunk,
          action)
      } else if (this.category === this.tabList[2]) {
        cols.push(
          {
            type: 'selection',
            width: 60
          },
          ...chunk,
          {
            title: '合约暂停时间',
            key: 'paused_time'
          },
          action)
      } else if (this.category === this.tabList[3]) {
        cols.push(
          ...chunk,
          {
            title: '解约时间',
            key: 'surrender_time'
          },
          {
            title: '违约金金额',
            key: 'violate_amount',
            render: (h, params) => {
              const price = Number(params.row.violate_amount || 0)
              return h('span', price.toFixed(2))
            }
          },
          {
            title: '实付违约金',
            key: 'paid_violate_amount',
            render: (h, params) => {
              const price = Number(params.row.paid_violate_amount || 0)
              return h('span', price.toFixed(2))
            }
          },
          action)
      } else if (this.category === this.tabList[4]) {
        cols.push(
          ...chunk,
          {
            title: '履约完成时间',
            key: 'end_times'
          },
          action)
      } else if (this.category === this.tabList[5]) {
        cols.push(
          {
            type: 'selection',
            width: 60
          },
          ...chunk,
          {
            title: '合约状态',
            key: 'status',
            render: (h, params) => {
              const status = params.row.status
              const contractStatus = this.contractStatusList.find(item => item.value === status)
              if (contractStatus) {
                return h('span', { style: { color: contractStatus.color } }, contractStatus.text)
              } else {
                return h('span', '-')
              }
            }
          },
          {
            title: '预计解约时间',
            key: 'next_plan_deduction_time'
          },
          action)
      }
      return cols
    }
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.sign_time_begin = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.sign_time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.sign_time_begin = ''
        this.searchParams.sign_time_end = ''
      }
      if (Array.isArray(this.otherDaterange) && this.otherDaterange.length === 2 && this.otherDaterange[0] && this.otherDaterange[1]) {
        this.searchParams.time_begin = formatDate(this.otherDaterange[0], 'yyyy-MM-dd')
        this.searchParams.time_end = formatDate(this.otherDaterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.time_begin = ''
        this.searchParams.time_end = ''
      }
      this.getList()
    },
    postCategory() {
      if (this.category === this.tabList[0]) {
        this.searchParams.status = ''
        this.searchParams.delay_surrender = 0
      } else if (this.category === this.tabList[5]) {
        this.searchParams.status = ''
        this.searchParams.delay_surrender = 1
      } else {
        this.searchParams.status = this.category
        this.searchParams.delay_surrender = 0
      }
    },
    handleReset() {
      this.daterange = []
      this.otherDaterange = []
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.postCategory()
      this.handleSearch()
    },
    getList() {
      this.showSurrenderType = false
      // this.postCategory()
      return this.$service.post('/Web/AlipayMerchantCard/contracts', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          this.$emit('emitCount', this.total, this.category)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/AlipayMerchantCard/contracts', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    postAction(openMerchantId, subscriptionNo, url, text, content) {
      let open_merchant_id = ''
      let subscription_no = ''
      if (openMerchantId && subscriptionNo) {
        open_merchant_id = openMerchantId
        subscription_no = subscriptionNo
      } else {
        const list = this.$refs.selection.getSelection()
        if (list.length === 0) {
          this.$Message.warning(text)
          return
        }
        open_merchant_id = list[0].open_merchant_id
        const subscriptionNoList = list.map(item => item.subscription_no)
        subscription_no = subscriptionNoList.join(',')
      }
      this.$Modal.confirm({
        title: '提示',
        content,
        onOk: () => {
          this.$service.post(url, {
            bus_id: this.searchParams.bus_id,
            subscription_no,
            open_merchant_id
          }).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    handlePause(openMerchantId, subscriptionNo) {
      this.postAction(openMerchantId, subscriptionNo, '/Web/AlipayMerchantCard/subscriptionPause', '请勾选要暂停的合约', '确认暂停该合约?')
    },
    handleSuspend(openMerchantId, subscriptionNo) {
      this.postAction(openMerchantId, subscriptionNo, '/Web/AlipayMerchantCard/subscriptionSurrender', '请勾选要解约的合约', '确认立即解约该合约?')
    },
    handleRecover(openMerchantId, subscriptionNo) {
      this.postAction(openMerchantId, subscriptionNo, '/Web/AlipayMerchantCard/subscriptionRegain', '请勾选要恢复的合约', '确认恢复该合约?')
    },
    handleCancel(openMerchantId, subscriptionNo) {
      this.postAction(openMerchantId, subscriptionNo, '/Web/AlipayMerchantCard/subscriptionSurrenderCancel', '请勾选要取消解约的合约', '确认取消解约该合约?')
    },
    handleAction(action, row) {
      if (action === 'detail') {
        this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: row.subscription_no }
        this.detailModal = true
      } else if (action === 'deduction') {
        this.$router.push({
          path: '/Web/AlipayMerchantCard/fitItemsList',
          query: {
            busId: this.searchParams.bus_id,
            subscriptionNo: row.subscription_no
          }
        })
      } else if (action === 'pause') {
        this.handlePause(row.open_merchant_id, row.subscription_no)
      } else if (action === 'suspend') {
        if (row.delay_surrender == 1) {
          this.handleSuspend(row.open_merchant_id, row.subscription_no)
        } else {
          this.curInfo = row
          this.showSurrenderType = true
        }
      } else if (action === 'recover') {
        this.handleRecover(row.open_merchant_id, row.subscription_no)
      } else if (action === 'cancel') {
        this.handleCancel(row.open_merchant_id, row.subscription_no)
      }
    },
  },
  created() {
    const lazyTime = this.lazy ? 1000 : 0
    setTimeout(async () => {
      // 获取场馆列表
      !this.adminBusList && await this.getAdminBusList()
      this.searchParams.bus_id = this.busId

      const reload = this.category === this.$route.query.category
      if (!reload) {
        this.postCategory()
        this.getList()
      }
    }, lazyTime)
  },
  activated() {
    this.isLeave = false
    if (this.$route?.query?.refresh == 1) {
      this.getList()
    }
    const { busId, date, category } = this.$route.query
    const reload = this.category === category
    if (reload) {
      this.daterange = []
      this.otherDaterange = []
      this.searchParams = { ...NONE_SEARCH_PARAMS }

      this.searchParams.bus_id = busId
      const dateObj = new Date(date)
      const firstDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1)
      const lastDate = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0)
      this.daterange = [firstDate, lastDate]
      setTimeout(() => {
        this.postCategory()
        this.handleSearch()
      }, 1000)
      // this.$nextTick(() => {
      //   this.postCategory()
      //   this.handleSearch()
      // })
    } else {
      setTimeout(() => {
        if (this.tabName === this.category) {
          this.postCategory()
          this.handleSearch()
        }
      }, 1000)
      // this.$nextTick(() => {
      //   if (this.tabName === this.category) {
      //     this.postCategory()
      //     this.handleSearch()
      //   }
      // })
    }
  },
  deactivated() {
    this.isLeave = true
    this.detailModal = false
    this.showSurrenderType = false
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.search-panel {
  padding: 20px 0;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;
      margin: 10px;

      .value {
        margin-left: 10px;
        width: 200px;
      }
    }
  }
}
</style>
