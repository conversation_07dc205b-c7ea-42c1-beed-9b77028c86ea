<template>
  <Modal v-model="showAdd" width="800" :mask-closable="false" title="签约快照">
    <Form label-position="right" ref="form" :model="info" class="form" :label-width="200">
      <FormItem label="产品名称">
        <div class="class-text">{{ info.product_title }}</div>
      </FormItem>
      <FormItem label="会员卡名称">
        <div class="class-text">{{ info.card_name }}</div>
      </FormItem>
      <FormItem label="会员卡详情">
        <div class="class-text">
          购买天数： {{ info.product_info.purchase_volume }}天，赠送天数：{{ info.product_info.gift_volume }}天
        </div>
      </FormItem>
      <FormItem label="月付类型">
        <div class="class-text">
          {{ info.product_type == 1 ? '常规' : '组合' }} ({{ info.gen_type == 0 ? '签约后生成合约' : '首期扣款后生成合约' }})
        </div>
      </FormItem>
      <FormItem label="体验卡发放">
        <div class="class-text">{{ info.experience_at_once == 0 ? '无' : info.experience_card_name }}</div>
      </FormItem>
      <FormItem label="违约金收取" v-if="info.customer_un_sign == 0">
        <div class="class-text">{{ info.un_sign_type == 1 ? '线下收取' : '支付宝自动扣款' }} </div>
      </FormItem>
      <FormItem label="违约金收取" v-if="info.customer_un_sign == 1">
        <div class="class-text">会员申请支付{{ info.violate_amount }}元 </div>
      </FormItem>
      <FormItem label="折扣券">
        <div class="class-text">{{ info.support_coupon == 0 ? '不支持' : '支持' }}</div>
      </FormItem>
      <FormItem label="赠送时间" v-if="info.give_mount">
        <div class="class-text">{{ giveDays }} 天</div>
      </FormItem>
      <!-- <div class="tips">此卡签署协议成功后立即开卡，后续按照扣款周期进行扣款</div> -->
    </Form>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>
<script>
export default {
  name: 'ZhimaFastInfo',
  data() {
    return {
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    giveDays() {
      return Number(this.info.give_mount) * 30
    }
  },
  created() { },
  methods: {
  }
}
</script>
<style lang="less" scoped>
.class-text {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tips {
  text-indent: 20px;
  color: #aaa;
}
</style>
