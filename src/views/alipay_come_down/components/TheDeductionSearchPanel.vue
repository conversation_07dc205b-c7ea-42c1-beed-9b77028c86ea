<template>
  <div class="search-panel">
    <Card>
      <div class="search-line">
        <div class="search-item">
          <div class="label">订单编号</div>
          <Input v-model="searchParams.order_no" placeholder="请输入订单编号" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">合约编号</div>
          <Input v-model="searchParams.subscription_no" placeholder="请输入合约编号" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">月付方案</div>
          <Input v-model="searchParams.product_title" placeholder="请输入月付方案名称" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">会员</div>
          <Input v-model="searchParams.phone" placeholder="请输入会员手机号码" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">场馆</div>
          <Select v-model="searchParams.bus_id" @on-change="searchParams.marketers_id = ''" placeholder="请选择场馆"
            class="value" filterable transfer>
            <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="search-item" v-if="searchParams.bus_id">
          <div class="label">签约销售</div>
          <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
            :belongBusId="searchParams.bus_id" />
        </div>
        <div class="search-item">
          <div class="label">扣款时间</div>
          <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
            transfer :clearable="false" />
        </div>
        <div class="search-item">
          <Button type="success" @click="handleSearch">查询</Button>
        </div>
        <div class="search-item">
          <Button @click="handleReset">重置</Button>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 20px">
      <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
      <div style="margin-top: 10px; display: flex; justify-content: space-between;">
        <div>
          <Button style="margin-left: 10px" @click="handleExport">导出</Button>
        </div>
        <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
          @on-page-size-change="handlePageSizeChange" show-total show-sizer>
        </Page>
      </div>
    </Card>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
// import Big from 'big.js'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  subscription_no: '',
  order_no: '',
  product_title: '',
  phone: '',
  marketers_id: '',
  type: '',
  pay_time_start: '',
  pay_time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TheSearchPanel',
  components: { SalesSelect },
  props: {
    category: {
      type: String,
      required: true
    },
    tabList: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      list: [],
      total: 0,
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
    columns() {
      // 序号 订单编号 合约编号 月付方案 月付方案类型 履约期数/总期数 扣款期数 实际扣款金额 支付宝手续费 实际收入金额 会员 场馆 签约销售 扣款时间 操作
      const chunk = [
        {
          title: '序号',
          type: 'index',
          width: 80
        },
        {
          title: '订单编号',
          key: 'order_no'
        },
        {
          title: '合约编号',
          key: 'subscription_no'
        },
        {
          title: '月付方案',
          key: 'product_title'
        },
        {
          title: '月付方案类型',
          key: 'product_type',
        },
        {
          title: '履约期数/总期数',
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.paid_count}/${row.periods}`)
          }
        },
        {
          title: '扣款期数',
          key: 'period'
        },
        {
          title: '实际扣款金额',
          key: 'deduction_amount'
        },
        {
          title: '支付宝手续费',
          // render: (h, params) => {
          //   const rate = new Big(0.006)
          //   const fee = new Big(params.row.amount).mul(rate)
          //   return h('div', fee.toFixed(2))
          // }
          key: 'zfb_amount'
        },
        {
          title: '实际收入金额',
          // render: (h, params) => {
          //   const amount = new Big(params.row.amount)
          //   const rate = new Big(0.006)
          //   const fee = new Big(params.row.amount).mul(rate)
          //   const total = amount.minus(fee)
          //   return h('div', total.toFixed(2))
          // }
          key: 'actual_income'
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '签约销售',
          key: 'salename'
        },
        {
          title: '扣款时间',
          key: 'actual_deduction_time',
          // render: (h, params) => {
          //   if (params.row.pay_time) {
          //     return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd'))
          //   } else {
          //     return h('div', '-')
          //   }
          // }
        },
      ]
      const action = {
        title: '操作',
        render: (h, params) => {
          return h('div', [
            h(
              'Button',
              {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  // route to detail
                  click: () => {
                    this.$router.push(`/alipay-merchant/deductionOrder/${this.category}/${params.row.itmes_id}`)
                  }
                }
              },
              '查看'
            )
          ])
        }
      }
      const cols = []
      if (this.category === this.tabList[0]) {
        cols.push(
          ...chunk,
          action)
      } else if (this.category === this.tabList[1]) {
        // 去掉 实际收入金额
        cols.push(
          ...chunk.slice(0, 9),
          ...chunk.slice(10),
          action)
      }
      return cols
    }
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.pay_time_start = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.pay_time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.pay_time_start = ''
        this.searchParams.pay_time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.setSearchDateRange()
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.handleSearch()
    },
    getList() {
      this.searchParams.type = this.category
      return this.$service.post('/Web/AlipayMerchantCard/itemsPaidList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          this.$emit('emitCount', this.total, this.category)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        type: this.category,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/AlipayMerchantCard/itemsPaidList', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setSearchDateRange() {
      // default 30 days ago to today
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      this.daterange = [thirtyDaysAgo, today]
    }
  },
  async created() {
    this.setSearchDateRange()
    const lazyTime = this.lazy ? 1000 : 0
    setTimeout(async () => {
      // 获取场馆列表
      !this.adminBusList && await this.getAdminBusList()
      this.searchParams.bus_id = this.busId
      // this.getList()
      this.handleSearch()
    }, lazyTime)
  },
  activated() {
    if (this.$route.params.refresh == 1) {
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.search-panel {
  padding: 20px 0;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;
      margin: 10px;

      .value {
        margin-left: 10px;
        width: 200px;
      }
    }
  }
}
</style>
