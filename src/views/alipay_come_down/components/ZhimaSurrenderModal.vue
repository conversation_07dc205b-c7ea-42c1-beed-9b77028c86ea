<template>
  <Modal
    v-model="show"
    width="465"
    :mask-closable="false"
    @on-cancel="handleClose"
    title="提示">
    <Form
      ref="formRef"
      class="modal-form"
      :model="formData"
    >
      <FormItem prop="type" :rules="{ required: true, type: 'string', message: '请选择解约方式', trigger: 'change' }">
        <RadioGroup v-model="formData.type">
          <Radio label="1">立即执行</Radio>
          <Radio v-show="(info && (info.done_period < info.periods)) && lastPeriod" label="2">
            延期解约
            <span style="margin-left:6px;font-size:12px;color:#7f7f7f;">系统会在下次扣款成功后进行解约</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <div v-show="formData.type === '1'">
          解约后未交费的订单（含欠费订单）将不再扣费，确定解约?
        </div>
        <div v-show="formData.type === '2'">
          当前履约进度是
          <b>【{{ info.done_period }}/{{ info.periods }}】</b>
          期，将在
          <b style="color:#70B603;">{{ plan_deduction_time }}</b>
          第
          <!-- <b>【{{ + next_period + (info.down_payment_date_rule !== 't+1' ? 0 : 1) }}/{{ info.periods }}】</b> -->
          <b>【{{ next_period }}/{{ info.periods }}】</b>
          期扣款成功后解约。
        </div>
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleConfirm">确定</Button>
      <Button @click="handleClose">取消</Button>
    </div>
  </Modal>
</template>
<script>
export default {
  name: 'ZhimaSurrenderModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      formData: {
        type: '1'
      },
      lastPeriod: false, // 是否最后一起，用于显示延期解约选项
      plan_deduction_time: '', // 延期解约时间
      next_period: 0
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },

  watch: {
    show(val) {
      if (val) {
        const params = {
          open_merchant_id: this.info.open_merchant_id,
          subscription_no: this.info.subscription_no
        };
        this.$service.post('Web/AlipayMerchantCard/getNextPlan', params).then(res => {
          if (res.data.errorcode === 0) {
            this.plan_deduction_time = res.data.data.plan_deduction_time
            this.lastPeriod = true
            this.next_period = res.data.data.period || this.info.done_period
            if (this.info.un_sign_type == 1) {
              this.formData.type = '1'
            } else if (this.info.un_sign_type == 2) {
              this.formData.type = '2'
            }
          } else if (res.data.errorcode === 404001) {
            this.lastPeriod = false
          } else {
            this.lastPeriod = false
            this.$Message.error(res.data.errormsg)
          }
        })
      }
    }
  },

  methods: {
    handleClose() {
      this.$refs.formRef.resetFields()
      this.lastPeriod = false;
      // this.plan_deduction_time = ''
      this.$emit('input', false)
    },
    handleConfirm() {
      this.$refs.formRef.validate(valid => {
        const { info, formData } = this
        const url = '/Web/AlipayMerchantCard' + (formData.type === '1' ? '/subscriptionSurrender' : '/subscriptionSurrenderDelay'),
        params = {
          open_merchant_id: info.open_merchant_id,
          subscription_no: info.subscription_no
        };

        this.$service.post(url, params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.$emit('on-success')
            this.handleClose()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>
