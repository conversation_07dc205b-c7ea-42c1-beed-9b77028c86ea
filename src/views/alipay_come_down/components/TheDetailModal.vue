<template>
  <div class="detail-modal">
    <Modal v-model="detailModal" title="详情" width="60%" @on-cancel="handleDetailClose">
      <div class="information">
        <div class="line">
          <div class="item">
            <div class="label">合约编号:</div>
            <div class="value">{{ detailData.subscription_no }} {{ statusLabel }}</div>
          </div>
        </div>
        <div class="line" v-if="detailData.status === 'PAUSED'">
          <div class="item">
            <div class="label">暂停时间:</div>
            <div class="value">{{ detailData.paused_time }} </div>
          </div>
        </div>
        <div class="line" v-if="detailData.status === 'SURRENDER'">
          <div class="item">
            <div class="label">解约时间:</div>
            <div class="value">{{ detailData.surrender_time }} ({{ detailData.customer_un_sign == 1 ? '会员解约' : '商家解约'
              }}) </div>
          </div>
        </div>
        <div class="line" v-if="detailData.status === 'END'">
          <div class="item">
            <div class="label">完成时间:</div>
            <div class="value">{{ detailData.end_times }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约会员:</div>
            <div class="value">{{ detailData.username }} {{ detailData.bus_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约销售:</div>
            <div class="value">{{ detailData.sale_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约时间:</div>
            <div class="value">{{ detailData.sign_time }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">当前业绩归属:</div>
            <div class="value">{{ detailData.marketers_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">产品方案:</div>
            <div class="value" v-if="detailData.product_type == 1">(常规)</div>
            <div class="value" v-else-if="detailData.product_type == 2">(组合)</div>
            <div class="value">{{ detailData.product_title }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">合约方式:</div>
            <div class="value">{{ detailData.gen_type == 0 ? '签约后生成合约' : '首期扣款后生成合约' }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">卡种:</div>
            <span class="value">{{ detailData.card_name }}</span>
            <!-- 有效期 30天 ，购买12000.00元，赠送300.00元 -->
            <div class="value">（ 有效期{{ detailData?.period_day }}天，购买{{
              detailData?.product_info?.purchase_volume }}{{ detailData?.unit }}，赠送{{
                detailData?.product_info?.gift_volume
              }}{{ detailData?.unit }}）</div>
          </div>
        </div>
        <div class="line" v-if="combinationsTotal">
          <div class="item">
            <div class="label">签约付款:</div>
            <div class="value">￥{{ combinationsTotal }}</div>
          </div>
        </div>
        <Alert type="warning" v-if="detailData.product_type == 2" style="margin: 0 40px">
          <div slot="desc">
            <div class="line" v-for="(item2b, index2b) in combinations" :key="'b_' + index2b">
              <div class="item" v-for="(item, index) in item2b" :key="'bb_' + index">
                <div class="label" v-if="item">{{ item.title }}:</div>
                <div class="value" v-if="item">￥{{ item.amount }}</div>
              </div>
            </div>
          </div>
        </Alert>
        <div class="line" v-if="detailData.product_type == 1">
          <div class="item">
            <div class="label">首期付款:</div>
            <div class="value">￥{{ detailData.down_payment }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">单期付款:</div>
            <div class="value">￥{{ normalTotal }}</div>
          </div>
        </div>
        <Alert type="warning" style="margin: 0 40px">
          <div slot="desc">
            <div class="line">
              <div class="item">
                <div class="label">扣款:</div>
                <div class="value">单期 ￥{{ detailData.deduction_amount }}, 共 {{ detailData.product_type == 1 ?
                  Number(detailData.periods) - 1 : detailData.periods }}期</div>
              </div>
            </div>
          </div>
        </Alert>
        <div class="line">
          <div class="item">
            <div class="label">违约金:</div>
            <!-- customer_un_sign 
            <Radio :label="0">场馆解约</Radio>
            <Radio :label="1">会员自行解约</Radio>
            un_sign_type 
            <Radio :label="1">线下收取</Radio>
            <Radio :label="2">支付宝自动扣款</Radio> -->
            <div class="value" v-if="detailData.customer_un_sign == 1">{{ detailData.violate_amount }}元</div>
            <div class="value" v-if="detailData.un_sign_type == 2 && detailData.customer_un_sign == 1">(单期 {{
              detailData.violate_single_amount }}元)
            </div>
            <div class="value" v-if="detailData.un_sign_type == 1 && detailData.customer_un_sign == 0">线下收取</div>
            <div class="value" v-if="detailData.un_sign_type == 2 && detailData.customer_un_sign == 0">单期 {{
              detailData.violate_amount }}元
            </div>
          </div>
        </div>
        <div class=" line" v-if="detailData.coupon_amount !== '0.00'">
          <div class="item">
            <div class="label">抵扣券抵扣:</div>
            <div class="value">￥{{ detailData.coupon_amount }}</div>
          </div>
        </div>
        <div class="actions">
          <!-- <Button @click="handleDetailClose">关闭</Button> -->
          <!-- <Button v-if="detailData.status === 'NORMAL'" type="warning" style="margin-left: 20px"
            @click="handlePause(detailData.open_merchant_id, detailData.subscription_no)">暂停</Button>
          <Button v-if="detailData.status === 'PAUSED'" type="primary" style="margin-left: 20px"
            @click="handleRecover">恢复</Button> -->
        </div>
      </div>
      <div slot="footer">
        <Button @click="handleDetailClose">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import Big from 'big.js'

export default {
  name: 'TheDetailModal',
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {
          bus_id: '',
          subscription_no: '',
          out_subscription_no: ''
        }
      }
    }
  },
  data() {
    return {
      detailModal: false,
      detailData: {},
      normalTotal: 0,
      combinationsTotal: 0,
      combinations: [],
    }
  },
  computed: {
    statusLabel() {
      if (this.detailData.status === 'NORMAL') {
        return '(履约中)'
      } else if (this.detailData.status === 'PAUSED') {
        return '(已暂停)'
      } else if (this.detailData.status === 'SURRENDER') {
        return '(已解约)'
      } else if (this.detailData.status === 'END') {
        return '(已完成)'
      } else if (this.detailData.status === 'WAIT_SURRENDER') {
        return '(待解约)'
      }
      return ''
    },
  },
  watch: {
    showModal(val) {
      if (val) {
        this.resetDetailData()
        this.getDetailData(this.params.bus_id, this.params.subscription_no, this.params.out_subscription_no)
      } else {
        this.resetDetailData()
        this.detailModal = false
      }
    }
  },
  methods: {
    resetDetailData() {
      this.detailData = {}
      this.normalTotal = 0
      this.combinationsTotal = 0
      this.combinations = []
    },
    getDetailData(bus_id, subscription_no = '', out_subscription_no = '') {
      return this.$service.post('/Web/AlipayMerchantCard/contractsDetail', {
        bus_id,
        subscription_no,
        out_subscription_no
      }).then((res) => {
        if (res.data.errorcode === 0) {
          this.detailData = res.data.data
          this.detailData.down_payment = Number(this.detailData.down_payment || 0).toFixed(2)
          this.detailData.deduction_amount = Number(this.detailData.deduction_amount || 0).toFixed(2)
          this.detailData.coupon_amount = Number(this.detailData.coupon_amount || 0).toFixed(2)
          this.detailData.violate_amount = Number(this.detailData.violate_amount || 0).toFixed(2)
          if (this.detailData.product_type == 2) {
            const combinations = []
            const combineListCopy = [...this.detailData.combine_list]
            combineListCopy.forEach(item => {
              item.amount = Number(item.amount).toFixed(2)
            })
            if (Array.isArray(combineListCopy)) {
              const len = combineListCopy.length
              const count = Math.ceil(len / 2)
              for (let i = 0; i < count; i++) {
                const one = combineListCopy[2 * i] || ''
                const two = combineListCopy[2 * i + 1] || ''
                combinations.push([one, two])
              }
              this.combinationsTotal = combineListCopy.reduce((total, item) => {
                return total + Number(item.amount)
              }, 0).toFixed(2)
            }
            this.combinations = combinations
            this.normalTotal = new Big(this.detailData.deduction_amount).times(Number(this.detailData.periods) || 0).toFixed(2)
          } else {
            this.normalTotal = new Big(this.detailData.deduction_amount).times(Number(this.detailData.periods) - 1 || 0).toFixed(2)
          }
          this.detailData.violate_single_amount = new Big(this.detailData.violate_amount || 0).div(this.detailData.violate_periods || 1).toFixed(2)

          // 次卡 期限卡 储值卡 私教课 泳教课
          if (this.detailData?.product_info?.card_type_id == 1) {
            this.detailData.unit = '天'
          } else if (this.detailData?.product_info?.card_type_id == 2) {
            this.detailData.unit = '次'
          } else if (this.detailData?.product_info?.card_type_id == 3) {
            this.detailData.unit = '元'
          } else if (this.detailData?.product_info?.card_type_id == 4) {
            this.detailData.unit = '节'
          } else if (this.detailData?.product_info?.card_type_id == 5) {
            this.detailData.unit = '节'
          }
          if (this.detailData?.product_info?.is_pt_time_limit_card == 1) {
            this.detailData.unit = '天'
          }

          this.detailModal = true
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleDetailClose() {
      this.resetDetailData()
      this.$emit('update:showModal', false)
    },
    handlePause(open_merchant_id, subscription_no) {
      return this.$service.post('/Web/AlipayMerchantCard/subscriptionPause', {
        bus_id: this.searchParams.bus_id,
        subscription_no,
        open_merchant_id
      }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleRecover() {
      alert('暂未实现')
    }
  }
}
</script>

<style lang="less" scoped>
.one-line {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.information {
  padding: 40px;

  .line {
    justify-content: space-between;
    min-height: 40px;
    .one-line;

    .item {
      min-width: 280px;
      .one-line;
    }

    .label {
      font-size: 16px;
      color: #999;
      width: 110px;
      text-align: right;
      text-wrap: nowrap;
    }

    .value {
      font-size: 16px;
      color: #333;
      margin-left: 10px;
      text-wrap: nowrap;
    }
  }

  .actions {
    margin: 20px auto 0 auto;
    width: 400px;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }
}
</style>