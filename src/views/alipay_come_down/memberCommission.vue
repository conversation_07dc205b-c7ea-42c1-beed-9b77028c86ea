<template>
  <div class="container">
    <div class="box">
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" @on-change="searchParams.belong_id = ''" placeholder="请选择场馆"
                class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">合约单号</div>
              <Input v-model="searchParams.subscription_no" placeholder="请输入合约编号" class="value" clearable />
            </div>
            <!-- <div class="search-item">
              <div class="label">扣款时间</div>
              <DatePicker v-model="payDaterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer />
            </div> -->
            <div class="search-item">
              <div class="label">签约时间</div>
              <DatePicker v-model="signDaterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer />
            </div>
            <div class="search-item" v-if="searchParams.bus_id">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.belong_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="searchParams.bus_id" />
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <div class="panel-box">
          <div class="panel-item" v-for="(item, index) in panelList" :key="index">
            <Card>
              <div class="value">{{ item.value }}</div>
              <div class="label">{{ item.label }}</div>
            </Card>
          </div>
        </div>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <Button style="margin-left: 10px" @click="handleExport">导出</Button>
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './components/TheDetailModal.vue'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  commission_detail_type: 'sign',
  pay_time_start: '',
  pay_time_end: '',
  sign_time_start: '',
  sign_time_end: '',
  belong_id: '',
  order_no: '',
  subscription_no: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'MemberCommission',
  components: { SalesSelect, TheDetailModal },
  data() {
    return {
      signDaterange: [],
      payDaterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 合约单号 会员 场馆 业绩归属 产品名称 合约签约日期 产品签约金额 人数提成
      columns: [
        {
          title: '合约单号',
          key: 'subscription_no',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '业绩归属',
          key: 'sale_name'
        },
        {
          title: '产品名称',
          key: 'product_title'
        },
        {
          title: '合约签约日期',
          key: 'sign_time',
        },
        {
          title: '产品签约金额',
          key: 'total_amount',
          render: (h, params) => {
            const price = Number(params.row.total_amount || 0).toFixed(2)
            return h('div', price)
          }
        },
        {
          title: '人数提成',
          key: 'commission_amount',
          render: (h, params) => {
            const price = Number(params.row.commission_amount || 0).toFixed(2)
            return h('div', price)
          }
        }
      ],
      list: [],
      total: 0,
      panelList: [
        { label: '人数提成', code: 'allSignAmount', value: 0 },
      ],
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      }
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      if (!this.signDaterange[0] && !this.payDaterange[0]) {
        return this.$Message.warning('计划扣款日期和实际扣款日期至少选择一个')
      }
      this.searchParams.page_no = 1
      if (Array.isArray(this.payDaterange) && this.payDaterange.length === 2 && this.payDaterange[0] && this.payDaterange[1]) {
        this.searchParams.pay_time_start = formatDate(this.payDaterange[0], 'yyyy-MM-dd')
        this.searchParams.pay_time_end = formatDate(this.payDaterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.pay_time_start = ''
        this.searchParams.pay_time_end = ''
      }
      if (Array.isArray(this.signDaterange) && this.signDaterange.length === 2 && this.signDaterange[0] && this.signDaterange[1]) {
        this.searchParams.sign_time_start = formatDate(this.signDaterange[0], 'yyyy-MM-dd')
        this.searchParams.sign_time_end = formatDate(this.signDaterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.sign_time_start = ''
        this.searchParams.sign_time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.setSearchDateRange()
      this.payDaterange = []
      this.handleSearch()
    },
    getList() {
      return this.$service.post('/Web/AlipayMerchantCard/commissionList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          // const sum = res.data.data.sum
          // this.panelList.forEach((item) => {
          //   item.value = sum[item.code]
          // })
          this.panelList[0].value = Number(res.data.data.allSignAmount || 0).toFixed(2)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/AlipayMerchantCard/commissionList', params).then((res) => {
        if (res.data.errorcode === 0) {
          // this.$Message.success({
          //   content: '导出任务运行中，请稍后到消息中心下载!',
          //   duration: 3
          // })
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
          }
          this.$refs.selection.exportCsv({
            filename: `会员提成明细-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.columns,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setSearchDateRange() {
      let date = null
      if (this.$route.params.month) {
        date = new Date(this.$route.params.month)
      } else {
        date = new Date()
      }
      // default current month
      const firstDate = new Date(date.getFullYear(), date.getMonth(), 1)
      const lastDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      this.signDaterange = [firstDate, lastDate]
    }
  },
  async created() {
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    if (this.$route.params.busId) {
      this.searchParams.bus_id = this.$route.params.busId
    } else {
      this.searchParams.bus_id = this.busId
    }
    if (this.$route.params.saleId) {
      this.searchParams.belong_id = this.$route.params.saleId
    }
    this.setSearchDateRange()
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
}
</style>
