<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card" @on-click="handleTabChange">
        <TabPane :label="labelOne" :name="tabList[0]">
          <TheSearchPanel v-if="tabName === tabList[0]" @emitCount="setCount" :category="tabList[0]" :tabList="tabList"
            :date="date" />
        </TabPane>
        <TabPane :label="labelTwo" :name="tabList[1]">
          <TheSearchPanel v-if="tabName === tabList[1]" @emitCount="setCount" :category="tabList[1]" :tabList="tabList"
            :date="date" />
        </TabPane>
        <TabPane :label="labelThree" :name="tabList[2]">
          <TheSearchPanel v-if="tabName === tabList[2]" @emitCount="setCount" :category="tabList[2]" :tabList="tabList"
            :date="date" />
        </TabPane>
        <TabPane :label="labelFour" :name="tabList[3]">
          <TheSearchPanel v-if="tabName === tabList[3]" @emitCount="setCount" :category="tabList[3]" :tabList="tabList"
            :date="date" />
        </TabPane>
        <TabPane :label="labelFive" :name="tabList[4]">
          <TheSearchPanel v-if="tabName === tabList[4]" @emitCount="setCount" :category="tabList[4]" :tabList="tabList"
            :date="date" />
        </TabPane>
        <TabPane :label="labelSix" :name="tabList[5]">
          <TheSearchPanel v-if="tabName === tabList[5]" @emitCount="setCount" :category="tabList[5]" :tabList="tabList"
            :date="date" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import TheSearchPanel from './components/TheMemberSearchPanel.vue'

export default {
  name: 'Alipay2StatTabs',
  components: {
    TheSearchPanel,
  },
  props: {
    date: {
      type: String,
      default: () => {
        return ''
      },
    },
    category: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    // const badgeFn = (h, label, count) => {
    //   return h('div', [
    //     h('span', label),
    //     h('Badge', {
    //       style: {
    //         marginLeft: '10px',
    //       },
    //       props: {
    //         count,
    //         overflowCount: 999,
    //       },
    //     }),
    //   ])
    // }
    return {
      // 新增签约会员总数量 履约中会员总数量 扣款成功会员总数量 扣款失败会员总数量 解约会员总数量 履约完结会员总数量
      tabName: '1',
      tabList: ['1', '2', '3', '4', '5', '6'],
      valueOne: 0,
      // labelOne: (h) => {
      //   return badgeFn(h, '新增签约会员总数量', this.valueOne)
      // },
      labelOne: '新增签约会员总数量',
      valueTwo: 0,
      // labelTwo: (h) => {
      //   return badgeFn(h, '履约中会员总数量', this.valueTwo)
      // },
      labelTwo: '履约中会员总数量',
      valueThree: 0,
      // labelThree: (h) => {
      //   return badgeFn(h, '扣款成功会员总数量', this.valueThree)
      // },
      labelThree: '扣款成功会员总数量',
      valueFour: 0,
      // labelFour: (h) => {
      //   return badgeFn(h, '扣款失败会员总数量', this.valueFour)
      // },
      labelFour: '扣款失败会员总数量',
      valueFive: 0,
      // labelFive: (h) => {
      //   return badgeFn(h, '解约会员总数量', this.valueFive)
      // },
      labelFive: '解约会员总数量',
      valueSix: 0,
      // labelSix: (h) => {
      //   return badgeFn(h, '履约完结会员总数量', this.valueSix)
      // },
      labelSix: '履约完结会员总数量',
    }
  },
  methods: {
    setCount(count, category) {
      if (category === this.tabList[0]) {
        this.valueOne = count
      } else if (category === this.tabList[1]) {
        this.valueTwo = count
      } else if (category === this.tabList[2]) {
        this.valueThree = count
      } else if (category === this.tabList[3]) {
        this.valueFour = count
      } else if (category === this.tabList[4]) {
        this.valueFive = count
      } else if (category === this.tabList[5]) {
        this.valueSix = count
      }
    },
    handleTabChange(name) {
      this.tabName = name
    },
  },
  created() {
    this.tabName = this.category
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 20px;
}
</style>
