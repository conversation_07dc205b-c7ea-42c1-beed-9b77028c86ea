<template>
  <div class="container" style="padding: 20px">
    <Card :style="isEdit ? '' : 'background-color: #e8eaec'">
      <Editor ref="editor" v-model="description" />
    </Card>
    <div class="buttons">
      <Button v-if="!isEdit" type="primary" @click="handleEdit">编辑</Button>
      <blockquote v-else>
        <Button type="primary" @click="handleSave">保存</Button>
        <Button @click="handleCancel">取消</Button>
      </blockquote>
    </div>
  </div>
</template>

<script setup>
import { ref, defineComponent } from 'vue';
import Editor from '@/components/form/Editor.vue';
import service from 'src/service';
import { Message } from 'iview';

defineComponent({
  name: 'Alipay2Protocol',
})

const editor = ref(null)
const description = ref('')
const isEdit = ref(false)
const unescapeHTML = (a) => {
  a = '' + a;
  return a
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
const getProtocol = () => {
  return service.get('/Web/AlipayMerchantCard/agreementInfo').then(({ data }) => {
    if (data.errorcode === 0) {
      description.value = unescapeHTML(data.data.agreement || '')
      editor.value.quill.enable(false)
    } else {
      Message.error(data.errormsg)
    }
  })
}
const handleSave = () => {
  return service.post('/Web/AlipayMerchantCard/agreementUpsert', {
    agreement: description.value,
  }).then(({ data }) => {
    if (data.errorcode === 0) {
      Message.success('保存成功')
      isEdit.value = false
      getProtocol()
    } else {
      Message.error(data.errormsg)
    }
  })
}
const handleEdit = () => {
  isEdit.value = true
  editor.value.quill.enable(true)
  editor.value.quill.focus()
}
const handleCancel = () => {
  isEdit.value = false
  editor.value.quill.enable(false)
}

getProtocol()
</script>

<style lang="less" scoped></style>