<template>
  <div class="table-wrap">
    <main>
      <Table :columns="columns"
             :data="tableData"
             @on-select="selectItem"
             @on-selection-change="selectItemChange"
             @on-select-all="selectItemAll"
             @on-select-cancel="selectItemCancel"
             stripe
             disabled-hover></Table>
    </main>
    <footer>
      <div>
        <Dropdown @on-click="otherCase" placement="top">
          <Button>其他操作 <Icon type="md-arrow-dropdown"></Icon> </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">批量删除</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page :total="totalCount"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            @on-change="gettableList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
  </div>
</template>

<script>
import {formatDate} from 'utils/index'
export default {
  name: 'brandList',
  data() {
    return {
      postData: {
        search: '',
        page_no: 1,
        page_size: 10
      },
      totalCount: 0,
      tableData: [],

      columns: [{
          type: 'selection',
          width: 60,
          align: 'center'
      },{
        title: '用户姓名',
        align: 'center',
        key: 'username',
      }, {
        title: '微信昵称',
        align: 'center',
        key: 'nickname',
      }, {
        title: '联系方式',
        align: 'center',
        key: 'phone',
      }, {
        title: '商家名称',
        align: 'center',
        key: 'mer_name',
      }, {
        title: '场馆名称',
        align: 'center',
        key: 'busname',
      }, {
        title: '评论内容',
        align: 'center',
        width: '300',
        key: 'content',
      }, {
        title: '评论时间',
        align: 'center',
        key: 'create_time'
      }, {
        title: '操作',
        align: 'center',
        render: (h, params) => {
            let item = params.row;
            return (
              <div>
                <i-button
                  type="text"
                  onClick={() => {
                    this.delItem(item.id);
                  }}
                >
                  删除
                </i-button>
              </div>
            );
          }
      }],
      selectedItemIds: []
    }
  },
  activated() {
    this.gettableList()
  },
  methods: {
    selectItem(selection, item) {
      if (!this.selectedItemIds.includes(item.id)) {
        this.selectedItemIds.push(item.id);
      }
    },
    selectItemCancel(selection, item) {
      let index = this.selectedItemIds.indexOf(item.id);
      this.selectedItemIds.splice(index,1);
    },
    selectItemChange(selection) {
      if (selection.length == 0) {
        this.selectedItemIds = [];
      }
    },
    selectItemAll(selection) {
      if (selection.length > 0) {
        selection.forEach(item => {
          this.selectItem(selection, item);
        });
      }
    },
    otherCase(val) {
      if (val === '0') {
        this.delItems();
      }
    },
    delItems() {
      if(this.selectedItemIds.length == 0){
        this.$Message.error('请先勾选需要删除的评论!');
        return;
      }
      let config = {
        title: '提示',
        content: '确定要删除这些评论么？',
        onOk: () => {
          this.$service.post('/Web/Comment/batch_delete', { comment_ids: this.selectedItemIds }).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.gettableList();
              this.selectedItemIds = [];
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        },
      }
      this.$Modal.confirm(config);
    },
    delItem(id) {
      let delUrl = '/Web/Comment/delete';
      let config = {
        title: '删除评论',
        content: '确认删除此条评论吗？',
        onOk: () => {
          this.$service.post(delUrl, {comment_id: id}).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success('删除成功')
              this.selectedItemIds = [];
              this.gettableList();
            } else {
              this.$Message.error(res.data.errormsg)
            }
          }).catch(err => {
            console.error(err)
          })
        }
      }
      this.$Modal.confirm(config);
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.postData.page_no = 1;
      this.gettableList();
    },
    gettableList() {
      let url = '/Web/Comment/index';
      this.$service.post(url,this.postData).then(res => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list;
          this.tableData.forEach((item) => {
            item.create_time = formatDate(new Date(Number(item.create_time+'000')),'yyyy-MM-dd HH:mm');
          })
          this.totalCount = Number(res.data.data.count);
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
