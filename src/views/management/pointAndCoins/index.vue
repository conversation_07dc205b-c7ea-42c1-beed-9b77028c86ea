<template>
  <router-view
    v-if="$route.name !== '积分管理' && $route.meta.breadText !== '积分管理'"
  />
  <div v-else class="tab-table-wrap customized-tabs">
    <PointManagementList class="table-wrap" />
    <!-- <Tabs :value="activeIndex" @on-click="clickTabs">
      <TabPane label="积分" name="0">
        <PointManagementList v-if="activated.includes('0')" class="table-wrap" />
      </TabPane>
      <TabPane label="金币" name="1">
        <CoinsManagementList v-if="activated.includes('1')" class="table-wrap" />
      </TabPane>
    </Tabs> -->
  </div>
</template>

<script>
import PointManagementList from './pointManagementList'
// import CoinsManagementList from './coinsManagementList'
import tabsMixins from 'mixins/tabs'

export default {
  name: 'PointAndCoinsPage',
  components: {
    PointManagementList,
    // CoinsManagementList,
  },

  mixins: [tabsMixins],
  data() {
    return {
    }
  },
}
</script>

<style lang="less" scoped>
/* .tab-table-wrap {
  height: 100%;
  .ivu-tabs {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    /deep/.ivu-tabs-content {
      flex: 1;
    }
  }
} */

.table-wrap {
  padding: 20px 28px;
  // min-height: 100%;
}
</style>
