<template>
  <div class="table-wrap">
    <header>
      <Input class="user-search"
             placeholder="姓名/电话/实体卡号"
             v-model="postData.search"
             @on-enter="doSearch"></Input>
      <Date-picker placeholder="选择查询时间段"
                   @on-change="dateChanged"
                   :value="dateRange"
                   type="daterange"
                   :editable="false"
                   clearable
                   @on-clear="clearDate"
                   format="yyyy年MM月dd日"></Date-picker>
      <Button type="success"
              @click="doSearch">搜索</Button>
    </header>
    <main>
      <Table :columns="columns"
             :data="tableData"
             stripe
             disabled-hover></Table>
    </main>
    <footer>
      <!-- <div>
        <Button type="success"
                style="margin-right: 30px"
                @click="showAjustPoints = true">积分赠送/扣除</Button>
      </div> -->
      <Page :total="totalCount"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            @on-change="gettableList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'oldPointsList',
  data() {
    return {
      postData: {
        search: '',
        begin_date: '',
        end_date: '',
        page_no: 1,
        page_size: 10
      },
      dateRange: [],
      totalCount: 0,
      tableData: [],
      showAjustPoints: false,

      columns: [{
        title: '时间',
        align: 'center',
        key: 'time',
        width: 200,
      }, {
        title: '会员',
        align: 'center',
        key: 'username'
      }, {
        title: '操作积分',
        align: 'center',
        key: 'point'
      }, {
        title: '备注',
        key: 'remark'
      }, {
        title: '操作账号',
        key: 'operation_account'
      }]
    }
  },

  created() {
    this.gettableList()
  },

  methods: {
    dateChanged(val) {
      if (!val[0]) {
        return false
      }
      let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`;
      let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`;
      this.dateRange = [beginDate, endDate];
      this.gettableList();
    },
    clearDate() {
      this.dateRange = []
      this.gettableList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.gettableList();
    },
    doSearch() {
      this.postData.page_size = 10;
      this.postData.page_no = 1;
      this.gettableList()
    },
    gettableList() {
      const url = '/Web/MemberPoint/getPointRecord';
      this.postData.begin_date = this.dateRange[0]
      this.postData.end_date = this.dateRange[1]
      this.$service.post(url, this.postData).then(res => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list;
          this.totalCount = Number(res.data.data.count);
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-date-picker {
  width: 240px;
}

header {
  .user-search {
    width: 160px;
  }
}
</style>
