<template>
  <div class="table-wrap">
    <header>
      <Alert style="max-width:100%;">积分会在购卡/续卡/升级合同审批完成后进行发放，合同撤销或退卡自动回收未使用的积分，储值卡作为支付方式时不再获得积分</Alert>
      <!-- <Button type="success" @click="doSearch">搜索</Button> -->
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
      @on-select="handleSelect"
      @on-select-cancel="handleSelectCancel"
      @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleSelectAllCancel"
    />

    <footer>
      <Button style="margin-right: 15px" type="success" @click="handleShowRuleModal('add')">新增赠送规则</Button>
      <Dropdown placement="top" @on-click="otherCase">
        <Button>其他操作 <Icon type="md-arrow-dropdown"></Icon> </Button>
        <DropdownMenu slot="list">
          <DropdownItem name="edit">批量编辑</DropdownItem>
          <DropdownItem name="delete">批量删除</DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Pager :total="total" :post-data="postData" @on-change="pageChange" />
    </footer>

    <RuleModal
      :show.sync="showSetRules"
      :setType="setType"
      :ids="ids"
      :editData="editData"
      @updateList="() => { selection = []; getList() }"
    />
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import RuleModal from './components/pointRulesShoppingAddModal';
  import { mapState } from 'vuex';

  const orderTypeNames = ['购卡购课', '续卡续课', '升级'];
  // const ruleTypeNames = ['固定分值', '成交价比例'];

  export default {
    name: 'PointRulesShoppingList',
    components: {
      Pager,
      RuleModal,
    },
    data() {
      return {
        postData: {
          type: '1', // 规则类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '卡课名称',
            key: 'obj_name',
            tooltip: true,
          },
          {
            title: '奖励类型',
            key: 'obj_param_text',
            tooltip: true,
          },
          {
            title: '赠送',
            key: 'gift',
          },
          {
            title: '规则',
            key: 'rule_num_text',
            tooltip: true,
          },
          {
            title: '使用有效期',
            key: 'rule_days_text',
          },
          {
            title: '操作',
            minWidth: 70,
            render: (h, { row }) => (
              <div>
                <Button
                  type="text"
                  style="margin-right:16px;"
                  onClick={() => this.handleShowRuleModal('edit', [row.point_rule_id], row)}>
                  编辑
                </Button>
                <Button
                  type="text"
                  style="margin-right:16px;"
                  onClick={() => this.handleShowRuleModal('copy', [row.point_rule_id], row)}>
                  复制规则
                </Button>
                <Button
                  type="text"
                  style="color:red;"
                  onClick={() => this.handleDeleteRules([row.point_rule_id])}>
                  删除
                </Button>
              </div>
            )
          }
        ],

        showSetRules: false,
        setType: 'add', // add edit copy
        selection: [], // 多选选中的
        ids: [], // 最终提交的ids
        editData: {} // 编辑 复制时的规则数据
      }
    },
      computed: {
      ...mapState(['busId']),
    },

    methods: {
      getList() {
        const params = {
          ...this.postData,
        }

        this.$service.post('/Web/PointRule/getPointRuleList', params).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            const { selection } = this;
            data.list.forEach(v => {
              v.gift = '积分';
              v.obj_param_text = v.obj_param.map(k => orderTypeNames[k - 1]).join('、'); // 触发奖励类型 1为购卡 2为续卡 3为升级
              v.rule_num_text = `${ v.rule_type == 1 ? '成交价 x' : '' } ${ v.rule_num }${ v.rule_type == 1 ? '%' : '' }积分`;
              v.rule_days_text = `${ v.rule_days }天`;
              const selectedIndex = selection.findIndex(k => k.point_rule_id === v.point_rule_id)
              selectedIndex !== -1 && (v._checked = true)
              // if (v.edit == 0) {
              //   v._disabled = true
              // }
            })
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      pageChange(postData) {
        this.postData = postData;
        this.getList();
      },

      otherCase(val) {
        const { selection } = this;
        if (selection.length === 0) return this.$Message.error('请勾选规则');
        const ids = selection.map(({ point_rule_id }) => point_rule_id);
        switch (val) {
          case 'edit':
            this.handleShowRuleModal('edit', ids)
            break;

          case 'delete':
            this.handleDeleteRules(ids)
            break;
        }
      },
      handleSelect(_, row) {
        const { selection } = this;
        const index = selection.findIndex(v => v.point_rule_id === row.point_rule_id);
        index === -1 && selection.push(row)
      },
      handleSelectCancel(_, row) {
        const { selection } = this;
        const index = selection.findIndex(v => v.point_rule_id === row.point_rule_id);
        index !== -1 && selection.splice(index, 1)
      },
      handleSelectAll(list) {
        if (list.length) {
          const { handleSelect } = this;
          list.forEach(item => {
            handleSelect(null, item)
          })
        }
      },
      handleSelectAllCancel() {
        const { tableData, handleSelectCancel } = this;
        tableData.forEach(item => {
          handleSelectCancel(null, item)
        })
      },
      handleShowRuleModal(type, ids = [], item = {}) {
        if (typeof type !== 'string') return false;
        this.setType = type;
        this.editData = item;
        this.ids = ids;
        this.showSetRules = true;
      },

      handleDeleteRules(point_rule_ids = []) {
        this.$Modal.confirm({
          title: '删除规则',
          content: `确定删除${ point_rule_ids.length === 1 ? '该' : '已选' }规则吗？`,
          onOk: () => {
            this.$service.post(
              '/Web/PointRule/delPointRule',
              {
                point_rule_ids,
                bus_id: this.busId
              }
            ).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.selection = [];
                this.getList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
          }
        });
      }
    },
  }
</script>

<style lang="less" scoped>

</style>
