<template>
  <div>
    <Collapse
      :value="activeIndex"
      simple
      accordion
      @on-change="clickTabs">
      <Panel name="0">
        <span class="rule-type-name">签到</span>
        <ItemSign v-if="activated.includes('0')" slot="content" class="panel" />
      </Panel>
      <Panel name="1">
        <span class="rule-type-name">团操课</span>
        <ItemCome
          v-if="activated.includes('1')"
          slot="content"
          type="3"
          :region_bus="region_bus"
          alert="通过预约签到团课才能获得积分，无预约直接进场的操课无法获得" />
      </Panel>
      <Panel name="2">
        <span class="rule-type-name">私教课/泳教课</span>
        <ItemCome
          v-if="activated.includes('2')"
          slot="content"
          type="5"
          :region_bus="region_bus"
          alert="上课完成消课后会获得积分，取消消课后会自动回收" />
      </Panel>
      <Panel name="3">
        <span class="rule-type-name">订场</span>
        <ItemCome
          v-if="activated.includes('3')"
          slot="content"
          type="4"
          :region_bus="region_bus"
          alert="订场预约完成后，在到场进行签到成功后获得积分，取消签到撤回" >
          <span slot="tips">每订一个全场场次一个场次获得一次积分，一次半场场次算作1/2积分奖励</span>
        </ItemCome>
      </Panel>
    </Collapse>
  </div>
</template>

<script>
  import ItemSign from './components/pointRulesDailyItemSign';
  import ItemCome from './components/pointRulesDailyItemCome';
  import tabsMixins from 'mixins/tabs'

  export default {
    name: 'PointRulesShoppingList',
    components: {
      ItemSign,
      ItemCome
    },

    mixins: [tabsMixins],
    data() {
      return {
        region_bus: '',
      }
    },

    methods: {
      clickTabs(values) {
        const { activated } = this;
        const index = values[0];
        this.activeIndex = index;
        !activated.includes(index) && activated.push(index);
      }
    },
  }
</script>

<style lang="less" scoped>
.ivu-collapse {
  border: 0;
}
.rule-type-name {
  font-size: 14px;
  font-weight: bold;
}
</style>
