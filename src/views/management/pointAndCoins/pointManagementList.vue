<template>
  <div class="table-wrap">
    <header>
      <Select
        v-model="postData.belong_bus_id"
        placeholder="归属场馆"
        filterable>
        <Option
          v-for="option in adminBusList"
          :key="option.id"
          :value="option.id"
          :label="option.name" />
      </Select>
      <Input
        v-model="postData.search"
        style="width: 158px;"
        placeholder="会员姓名/会员电话"
        clearable
        @on-enter="doSearch" />
      <DatePicker
        v-model="postData.add_date"
        :clearable="false"
        type="daterange"
        placeholder="发放时间"
      />
      <DatePicker
        v-model="postData.end_date"
        type="daterange"
        placeholder="有效期"
      />
      <Select v-model="postData.status" placeholder="积分状态" clearable>
        <Option value="1">有效</Option>
        <Option value="2">过期</Option>
        <Option value="3">用尽</Option>
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>

      <Button
        type="text"
        style="margin-left: auto;"
        @click="$router.push('/management/pointAndCoins/originalPointList')">
        旧版积分列表入口
      </Button>
    </header>
    <Table :columns="columns" :data="tableData" disabled-hover />
    <div class="total-bar">
      <div class="label">合计</div>
      <div>
        发放积分 <span>{{ totalData.all_num || 0 }}</span>
        使用积分 <span>{{ totalData.use_num || 0 }}</span>
        过期积分 <span>{{ totalData.end_num || 0 }}</span>
        剩余积分 <span>{{ totalData.last_num || 0 }}</span>
      </div>
    </div>

    <footer>
      <Button class="point-footer-btn" type="success" @click="handleShowPutPoint">积分调整</Button>
      <Button
        class="point-footer-btn"
        type="success"
        :disabled="!pointRuleSettingAuth"
        :title="pointRuleSettingAuth ? null : '没有积分规则设置权限'"
        @click="handleToPointRulesSetting">
        获取积分设置
      </Button>
      <Button
        class="point-footer-btn"
        :disabled="tableData.length == 0"
        @click="exportData">
        导出发放记录
      </Button>
      <Button
        class="point-footer-btn"
        :disabled="tableData.length == 0"
        @click="exportStatisticsData">
        导出剩余积分
      </Button>
      <Pager
        :total="total"
        :post-data="postData"
        :history="!$route.params.search"
        @on-change="pageChange" />
    </footer>

    <!-- 积分详情 -->
    <DetailModal
      :show.sync="showDetail"
      :itemData="itemData"
    />
    <!-- 积分扣除 -->
    <DeductModal
      :show.sync="showDeduct"
      :itemData="itemData"
      @updateList="getList"
    />
    <!-- 积分调整 赠送/扣除 -->
    <PutModal
      :show.sync="showPut"
      isPointManagement
      @updateData="getList"
    />
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import Pager from 'components/pager';
  import DetailModal from './components/pointManagementDetailModal';
  import DeductModal from './components/pointManagementDeductModal';
  import PutModal from 'components/member/pointPutModal';
  import { formatDate } from 'utils'

  const STATUS = {
    '1': '有效',
    '2': '过期',
    '3': '用尽',
  }

  export default {
    name: 'PointManagementList',
    components: {
      Pager,
      DetailModal,
      DeductModal,
      PutModal
    },
    data() {
      return {
        pointRuleSettingAuth: false, // 积分规则设置跳转权限
        postData: {
          belong_bus_id: '', // 商家user_id，最多1000个
          search: this.$route.params.search || '',
          add_date: [new Date(Date.now() - 30 * 24 * 3600 * 1000),  new Date()], // 查询积分列表发放时间。必须为2个，第一个为开始时间，第二个为结束时间。请求格式为"Y-m-d"
          end_date: [], // 查询积分列表过期时间。必须为2个，第一个为开始时间，第二个为结束时间。请求格式格式为"Y-m-d"。
          status: null, // 0 全部 1有效 2过期 3用尽
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        totalData: {},
        columns: [
          {
            title: '积分编号',
            key: 'merchant_point_sn',
            minWidth: 70
          },
          {
            title: '姓名',
            key: 'username',
            tooltip: true,
            minWidth: 70
          },
          {
            title: '发放时间',
            key: 'create_time',
            minWidth: 50
          },
          {
            title: '发放积分',
            key: 'all_num',
          },
          {
            title: '使用积分',
            key: 'use_num'
          },
          {
            title: '剩余积分',
            key: 'last_num'
          },
          {
            title: '有效期',
            key: 'end_time'
          },
          {
            title: '积分状态',
            key: 'status_text',
            render: (h, { row }) => {
              return <span style={ row.status == 1 ? null : 'color:#ff696a;' }>{ row.status_text }</span>
            },
          },
          {
            title: '发放人',
            key: 'operator_name_text'
          },
          {
            title: '发放门店',
            key: 'bus_name'
          },
          {
            title: '发放备注',
            key: 'remark',
            tooltip: true,
            minWidth: 90
          },
          {
            title: '操作',
            minWidth: 60,
            render: (h, { row }) => (
              <div>
                <Button type="text" style="margin-right:16px;" onClick={() => this.handleShowDetail(row)}>详情</Button>
                <Button type="text" onClick={() => this.handleShowDeduct(row)}>扣除</Button>
              </div>
            )
          }
        ],

        showDetail: false,
        showDeduct: false,
        showPut: false,
        itemData: {},
      };
    },
    computed: {
      ...mapState(['busId', 'adminBusList'])
    },

    created() {
      if (this.$route.params.search) {
        this.postData.belong_bus_id = ''
        this.postData.add_date = []
        this.getList()
      }else {
        this.postData.belong_bus_id = this.busId;
      }
      this.getPointRuleSettingAuth()

      !this.adminBusList && this.getAdminBusList();
    },
    methods: {
      ...mapActions(['getAdminBusList']),

      getList(_export = 0) {
        const url = _export === 0 ? 'getPointList' : 'exportPointList'
        const { add_date, end_date, page_no, page_size, ...rest } = this.postData;
        const formBody = {
          add_date: add_date.length && add_date[0] ? [formatDate(add_date[0], 'yyyy-MM-dd'), formatDate(add_date[1], 'yyyy-MM-dd')] : [],
          end_date: end_date.length && end_date[0] ? [formatDate(end_date[0], 'yyyy-MM-dd'), formatDate(end_date[1], 'yyyy-MM-dd')] : [],
          _export,
          page: page_no,
          limit: page_size,
          ...rest,
        }
        // { headers: { 'Content-Type': 'multipart/form-data' } }
        this.$service.post('/Web/Point/' + url, formBody).then(res => {
          if (res.data.errorcode === 0) {
            if (_export === 0) { // 0列表 1导出
              const { list, total_data, count } = res.data.data;
              this.total = count;
              list.forEach(v => {
                v.status_text = STATUS[v.status]; // 状态 1有效 2过期 3用尽
                v.operator_name_text =  typeof v.operator_name === 'string' ? v.operator_name : '系统';
              })
              this.tableData = list;
              this.totalData = total_data;
            } else {
              this.$Message.success({
                content: '导出任务运行中，请稍后到消息中心下载!',
                duration: 3
              })
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      // 获取积分规则设置权限
      getPointRuleSettingAuth() {
        const params = {
          type: 1,
          page_no: 1,
          page_size: 1
        }
        this.$service.post('/Web/PointRule/getPointRuleList', params).then(res => {
          // 40014 没有积分规则设置权限
          if (res.data.errorcode === 0) {
            this.pointRuleSettingAuth = true
          }
        })
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange(postData) {
        this.postData = postData;
        this.getList();
      },
      exportData() {
        this.getList(1) // 0列表 1导出
      },
      exportStatisticsData() {
        this.$service.get('/Web/Point/getUserPointStatisticsExport').then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success({
              content: '导出任务运行中，请稍后到消息中心下载!',
              duration: 3
            })
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      handleShowPutPoint() {
        this.showPut = true;
      },
      handleToPointRulesSetting() {
        this.$router.push('/management/pointAndCoins/pointRules')
      },
      handleShowDetail(item) {
        this.showDetail = true;
        this.itemData = item;
      },
      handleShowDeduct(item) {
        this.showDeduct = true;
        this.itemData = item;
      },

    }
  };
</script>

<style lang="less" scoped>
  // header {
  //   padding: 0;
  // }
  .total-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 35px;
    height: 40px;
    font-size: 14px;
    .label {
      font-weight: bold;
    }
    span {
      padding-right: 25px;
      font-weight: bold;
      &:last-child {
        padding-right: 0;
      }
    }
  }

  .point-footer-btn {
    margin-right: 15px;
    &[disabled] {
      color: #c5c8ce;
      background-color: #f7f7f7;
      border-color: #dcdee2;
    }
  }
</style>
