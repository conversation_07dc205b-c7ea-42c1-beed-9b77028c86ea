<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs :value="activeIndex" @on-click="clickTabs">
      <TabPane label="购卡等消费获取积分" name="0">
        <PointRulesShoppingList v-if="activated.includes('0')" class="table-wrap" />
      </TabPane>
      <TabPane label="日常任务获取积分" name="1">
        <PointRulesDailyList v-if="activated.includes('1')" class="table-wrap" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import PointRulesShoppingList from './pointRulesShoppingList';
  import PointRulesDailyList from './pointRulesDailyList';
  import tabsMixins from 'mixins/tabs'

  export default {
    name: 'PointRulesPage',
    components: {
    PointRulesShoppingList,
    PointRulesDailyList,
  },
    mixins: [tabsMixins],
    data() {
      return {

      }
    }
  }
</script>

<style lang="less" scoped>

</style>
