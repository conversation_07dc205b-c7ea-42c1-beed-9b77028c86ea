<template>
  <div class="panel">
    <Alert>每天第一次签到成功后获得，撤销签到会自动回收</Alert>
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100">
      <FormItem label="每天签到获得" required>
        <div class="form-item-flex-row">
          <FormItem prop="rule_num" style="flex: 1;">
            <InputNumber
              v-model="formData.rule_num"
              style="width:140px;"
              :disabled="isDisabled"
              :max="999999999"
              :min="0"
              :precision="0"
              :active-change="false"
            />
            <span style="margin-left: 6px;">积分</span>
          </FormItem>
          <!-- <span style="margin: 0 6px;">金币</span>
          <FormItem prop="">
            <InputNumber
              v-model="formData."
              style="width:100%;"
              :disabled="isEdit"
              :max="999999999"
              :min="1"
              :precision="0"
              :active-change="false"
            />
          </FormItem> -->
        </div>
      </FormItem>
      <FormItem label="累积签到获得">
        <FormItem
          v-for="(item, index) in formData.rule_list"
          :key="index"
          :label-width="0"
          class="rules-form-item"
          :prop="'rule_list.' + index"
          :rules="validSameRules">
          <FormItem
            :prop="'rule_list.' + index + '.obj_id'"
            :rules="[{required: true, type: 'number', message: '请选择累积计算周期', trigger: 'change'}]">
            <Select
              v-model="item.obj_id"
              :disabled="isDisabled"
              style="width:140px;"
              @on-change="handleChangeItem">
              <Option
                v-for="({label, value, disabled}, oIdx) in options"
                :key="oIdx"
                :value="value"
                :label="label"
                :disabled="disabled" />
            </Select>
          </FormItem>
          <span style="margin: 0 6px;">累积</span>
          <FormItem
            :prop="'rule_list.' + index + '.obj_param.0'"
            :rules="[{required: true, type: 'number', message: '请填写累积次数', trigger: 'change'}]">
            <InputNumber
              v-model="item.obj_param[0]"
              :disabled="isDisabled"
              style="width:140px;"
              :min="1"
              :max="99999999"
              :precision="0"
              :active-change="false"
              @on-change="handleChangeItem"
            />
          </FormItem>
          <span style="margin: 0 6px;">次&nbsp;&nbsp;额外获得积分</span>
          <FormItem
            :prop="'rule_list.' + index + '.rule_num'"
            :rules="[{required: true, type: 'number', message: '请填写额外赠送积分', trigger: 'change'}]">
            <InputNumber
              v-model="item.rule_num"
              :disabled="isDisabled"
              style="width:140px;"
              :min="1"
              :max="99999999"
              :precision="0"
              :active-change="false"
              @on-change="handleChangeItem"
            />
          </FormItem>
          <Button
            v-show="!isDisabled"
            type="text"
            style="margin-right:0;margin-left:20px;color:#d8321f;"
            :disabled="isDisabled"
            ghost
            @click="handleDeleteRuleItem(item, index)">
            删除
          </Button>
        </FormItem>
        <Button
          type="success"
          :disabled="isDisabled"
          style="min-height:28px;font-size:12px;"
          @click="handleAddRuleItem">
          +添加累积规则
        </Button>
      </FormItem>
      <FormItem label="积分有效期" prop="rule_days">
        <div class="form-item-flex-row">
          <InputNumber
            v-model="formData.rule_days"
            style="width:140px;"
            :disabled="isDisabled"
            :max="999999"
            :min="1"
            :precision="0"
            :active-change="false"
          />
          <span style="margin-left: 6px;">天</span>
        </div>
      </FormItem>
      <FormItem>
        <Button
          v-show="isDisabled"
          type="success"
          @click="isDisabled=false">
          编辑
        </Button>
        <div v-show="!isDisabled">
          <Button
            type="success"
            @click="handleConfirm">
            保存
          </Button>
          <Button @click="handleCancel">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import { mapState } from 'vuex'

  export default {
    name: 'PointRulesDailyItemSign',
    data() {
      return {
        isDisabled: true,
        options: [
          { label: '每周', value: 1, disabled: false },
          { label: '每月', value: 2, disabled: false },
          { label: '每季度', value: 3, disabled: false },
          { label: '每年', value: 4, disabled: false },
        ],
        formData: {
          // rule_type: 0, // 规则类型 0为固定分值 1为成交价比例
          rule_num: null, // 规则值
          rule_days: null, // 有效期（天）
          rule_list: [
            // {
            //   obj_id: 1, // options的value 1为每周 2为每月 3为每季季度 4为每年
            //   obj_param: [null], // obj_param[0]累积数值
            //   rule_num: null // 额外积分
            // }
          ]
        },
        formRules: {
          rule_num: [
            { required: true, type: 'number', message: '请填写积分数值', trigger: 'change' }
          ],
          rule_days: [
            { required: true, type: 'number', message: '请填写有效期', trigger: 'change' }
          ],
          // rule_list: [
          //   { type: 'array', min: 1, message: '至少有一个累积规则', trigger: 'change' },
          // ]
        },
        validSameRules: {
          type: 'number',
          validator: (rule, value, callback) => {
            const { rule_list } =  this.formData
            const index = rule.field.split('.')[1]
            const sameIndex = rule_list.findIndex((v, i) =>
              rule_list[index].obj_id == v.obj_id &&
              rule_list[index].obj_param[0] == v.obj_param[0] &&
              // rule_list[index].rule_num == v.rule_num &&
              +index !== i
            )
            // 让相同规则的行，瞩目？
            if (sameIndex !== -1) {
                callback(new Error('已有周期和累积次数相同的规则'));
            } else {
              callback();
            }
            callback();
          },
          trigger: 'change'
        }
      }
    },
    computed: {
      ...mapState(['busId'])
    },

    // watch: { // 不需要禁用了
    //   'formData.rule_list': {
    //     handler(list) {
    //       const {options} = this
    //       options.forEach(v => {
    //         const index = list.findIndex(k => k.obj_id == v.value)
    //         v.disabled = index !== -1
    //       })
    //     },
    //     deep: true
    //   }
    // },

    created() {
      this.getData()
    },

    methods: {
      // https://www.apifox.cn/apidoc/shared-c983695d-a122-4ddb-a72d-744048e615ee/api-19694919
      getData(loading = true) {
        const params = {
          type: '2', // 触发动作类型action 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课 6会籍签到累积 7团课签到累积 8订场签到累积 9私教/游教签到累积
          // page_no: 1,
          // page_size: 1,
        }
        return this.$service.post('/Web/PointRule/getPointRuleList', params, { loading }).then(res => {
          if (res.data.errorcode === 0) {
            const { list } = res.data.data;
            if (list.length) {
              this.$refs.formRef.resetFields() // 取消编辑时重置表单校验
              const { formData } = this;
              const rule_list = []
              list.forEach(v => {
                switch (+v.action) {
                  case 2:
                    formData.rule_num = v.rule_num; // 每天签到获得
                    formData.rule_days = v.rule_days; // 积分有效期
                    break;
                  case 6: // obj_param对象参数 action in(6,7,8,9)时 为累积次数规则
                    if (v.obj_id) {
                      rule_list.push({
                        ...v,
                        obj_param: v.obj_param && v.obj_param.length ? [v.obj_param[0]] : [null], // obj_param[0]累积数值
                      })
                    }
                    break;
                  default:
                    console.log(v.action);
                    break;
                }
              })
              formData.rule_list = rule_list
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      handleAddRuleItem() {
        const { rule_list } = this.formData
        if(rule_list.length >= 30) return this.$Message.error('规则添加已达上限')
        rule_list.push({
          obj_id: 1,
          obj_param: [null],
          rule_num: null
        })
      },

      handleDeleteRuleItem(item, index) {
        if (item.point_rule_id) {
          this.deleteRuleItem(item)
        } else {
          const { rule_list } = this.formData
          rule_list.splice(index, 1)
          this.handleChangeItem()
        }
      },

      deleteRuleItem(item) {
        this.$Modal.confirm({
          title: '删除规则',
          content: `确认删除已有的累积规则吗？确认后会即时删除,并刷新现有数据`,
          onOk: () => {
            this.$service.post(
              '/Web/PointRule/delPointRule',
              {
                point_rule_ids: [item.point_rule_id],
                bus_id: this.busId
              }
            ).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.getData();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
          }
        });
      },
      handleChangeItem() {
        const { formRef } = this.$refs
        if(formRef) {
          this.formData.rule_list.forEach((v, i) => {
            formRef.validateField('rule_list.' + i)
          })
        }
      },

      // 设置规则 https://www.apifox.cn/apidoc/shared-c983695d-a122-4ddb-a72d-744048e615ee/api-19694920
      handleConfirm() {
        this.$refs.formRef.validate(val => {
          if (val) {
            const { busId, formData } = this;
            const params = {
              bus_id: busId,
              rule_days: formData.rule_days,
              rule_data: [
                {
                  action: 2, // 触发动作类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课 6会籍签到累积 7团课签到累积 8订场签到累积 9私教/游教签到累积
                  rule_type: 0, // 规则类型 0为固定分值 1为成交价比例
                  rule_num: formData.rule_num, // 规则值
                },
                // 累积规则
                ...formData.rule_list.map(v => ({
                  action: 6,
                  rule_type: 0,
                  obj_id: v.obj_id, // action in(6,7,8,9)时 1为每周 2为每月 3为每季季度 4为每年
                  obj_param: v.obj_param,
                  rule_num: v.rule_num, // 额外赠送积分
                }))
              ]
            }
            this.$service.post(
              '/Web/PointRule/addPointRule',
               params,
               { headers: { 'Content-Type': 'application/json' } }
            ).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.isDisabled = true;
                this.getData(false)
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            })
          }
        })
      },
      handleCancel() {
        this.isDisabled = true;
        this.getData(false);
      }
    },
  }
</script>

<style lang="less" scoped>
.panel {
  margin-left: 1%;
  width: 70%;
}
.alert-info {
  display: inline-block;
}
.form-item-flex-row {
  display: flex;
  align-items: center;
}

.rules-form-item {
  margin-bottom: 24px;
  /deep/.ivu-form-item-content {
    display: flex;
    align-items: center;
  }
}

.ivu-btn {
  margin-right: 18px
}
</style>
