<template>
  <Modal
    class="point-rule-modal"
    :title="title"
    :value="show"
    :mask-closable="false"
    width="550"
    @on-visible-change="handleClose">
    <Form
      v-show="setType !== 'copy'"
      ref="addFormRef"
      class="point-rule-form"
      :model="formData"
      :rules="formRules"
      :label-width="70">
      <FormItem label="赠送条件" prop="obj_param">
        <CheckboxGroup v-model="formData.obj_param" @on-change="handleChangeOrderType">
          <Checkbox :label="1">购卡/购课</Checkbox>
          <Checkbox :label="2">续卡/续课</Checkbox>
          <Checkbox :label="3">升级</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <!-- <FormItem label="赠送方式" prop="">
        <CheckboxGroup v-model="formData." @on-change="">
          <Checkbox :label="1">积分</Checkbox>
          <Checkbox :label="2">金币</Checkbox>
          <Checkbox :label="3">积分+金币</Checkbox>
        </CheckboxGroup>
      </FormItem> -->
      <FormItem label="积分">
        <div class="form-item-flex-row">
          <Select v-model="formData.rule_type" style="width: 110px;" @on-change="formData.rule_num = null">
            <Option :value="0" label="固定分值" />
            <Option :value="1" label="成交价比例" />
          </Select>
          <FormItem prop="rule_num" style="margin-left: 16px;flex: 1;">
            <InputNumber
              v-model="formData.rule_num"
              style="width:100%;"
              :min="formData.rule_type == 0 ? 1 : 0.01"
              :max="formData.rule_type == 0 ? 999999999 : 100"
              :precision="formData.rule_type == 0 ? 0 : 2"
              :active-change="false"
            />
          </FormItem>
          <span>&nbsp;{{ formData.rule_type == 0 ? '分' : '%' }}</span>
        </div>
      </FormItem>
      <!-- <FormItem label="金币">
        <div class="form-item-flex-row">
          <Select v-model="formData." style="width: 110px;">
            <Option :value="0" label="固定分值" />
            <Option :value="1" label="成交价比例" />
          </Select>
          <FormItem prop="" style="margin-left: 16px;flex: 1;">
            <InputNumber
              v-model="formData."
              style="width:100%;"
              :max="999999999"
              :min="1"
              :precision="0"
              :active-change="false"
            />
          </FormItem>
          <span>&nbsp;{{ formData. == 0 ? '个' : '%' }}</span>
        </div>
      </FormItem> -->
      <FormItem label="有效期" prop="rule_days">
        <div class="form-item-flex-row">
          <InputNumber
            v-model="formData.rule_days"
            style="width: 100%;"
            :max="999999"
            :min="1"
            :precision="0"
            :active-change="false"
          />
          &nbsp;天
        </div>
      </FormItem>
      <!-- <FormItem
        label="有效期"
        prop="duration_date">
        <DatePicker
          v-model="formData.duration_date"
          :editable="false"
          :options="datePickerOptions"
          type="date"
          format="yyyy-MM-dd"
          placeholder="有效期"
        />
      </FormItem> -->
    </Form>
    <div
      v-show="['add', 'copy'].includes(setType)"
      class="check-tree-form-item"
      :class="{ 'margin-left': setType === 'add' }"
    >
      <div v-show="setType === 'copy'" class="copy-card-tips">
        <span class="card-name">{{ editData.obj_name }}</span>
        的赠送规则复制给
      </div>
      <CheckPtClass
        v-if="show"
        :treeList="cardTreeList"
        placeholder="搜索卡名称、课名称"
      />
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" :disabled="['add', 'copy'].includes(setType) && !cardTreeList.length" @click="handleSummit">确定</Button>
      <Button @click="handleClose(false)">取消</Button>
      <!-- <Button @click="handleReset">重置</Button> -->
    </div>
  </Modal>
</template>

<script>
  import CheckPtClass from 'components/form/checkPtClass';
  import { mapState } from 'vuex';

  const cardGroupNames = {
    card_group_list: '会籍卡',
    pt_card_group_list: '私教课',
    swim_card_group_list: '泳教课',
  };
   const titleNames = {
    add: '积分赠送规则',
    edit: '编辑',
    copy: '复制',
  };

  let changeTreeNodeTimer = null;

  export default {
    name: 'PointRulesShoppingAddModal',
    components: {
      CheckPtClass
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      setType: {
        type: String,
        validator(value) {
          return ['add', 'edit', 'copy'].includes(value)
        },
        required: true
      },
      ids: {
        type: Array,
        default: () => []
      },
      editData: {
        type: Object,
        default: () => ({})
      },
    },
    data() {
      return {
        cardTreeList: [],
        formData: {
          action: 1, // 触发动作类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课  6会籍签到累积 7团课签到累积 8订场签到累积 9私教/游教签到累积
          obj_param: [], // 赠送条件/合同类型（触发动作类型 为1时使用） 1为购卡 2为续卡 3为升级
          rule_type: 0, // 规则类型 0为固定分值 1为成交价比例
          rule_num: null, // 规则值
          rule_days: null, // 过期时间
          // bus_id
          // point_rule_ids 规则id type为编辑时使用 arr
        },
        formRules: {
          obj_param: [
            { required: true, type: 'array', min: 1, message: '至少选择一个条件', trigger: 'change' }
          ],
          rule_num: [
            { required: true, type: 'number', message: '请填写积分数值/比例', trigger: 'change' }
          ],
          rule_days: [
            { required: true, type: 'number', message: '请填写有效期', trigger: 'change' }
          ],
        },
        datePickerOptions: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now() - 24 * 60 * 60 * 1000;
          }
        },
      }
    },

    computed: {
      ...mapState(['busId']),
      title() {
        return titleNames[this.setType]
      },
    },

    watch: {
      show(val) {
        if (val) {
          const { editData, formData } = this;
          if (editData.action) {
            // 回显编辑规则的数据
            Object.keys(formData).forEach(key => {
              const val = editData[key];
              val !== undefined && (formData[key] = val);
            })
          }
          // 添加、复制时获取可选卡课分类列表
          ['add', 'copy'].includes(this.setType) && this.getCardList();
        } else {
          this.handleReset();
        }
      },
    },

    methods: {
      // 获取设置积分规则时会员卡列表
      getCardList() {
        this.$service.post('/Web/PointRule/getSetCardPointRuleCardList', { bus_id: this.busId }).then(res => {
          if (res.data.errorcode === 0) {
            const list = [];
            const copyId = this.editData.obj_id;
            const isCopy = this.setType === 'copy';
            for (const [key, val] of Object.entries(res.data.data)) {
              const parentId = key;
              list.push({
                id: parentId,
                name: cardGroupNames[key] || 'null',
                check: false, // 控制选中状态
                show: true, // 控制是否显示
                disabled: false, // 控制是否可修改
                children: val.map(({ card_list, title, ...rest }) => ({
                  ...rest,
                  parentId,
                  name: title,
                  check: false,
                  show: true,
                  disabled: false,
                  children: card_list.map(({ card_name, ...subRest }) => ({
                    ...subRest,
                    parentId: rest.id,
                    id: subRest.card_id,
                    name: card_name,
                    check: false,
                    show: true,
                    disabled: isCopy && subRest.card_id == copyId, // 禁用复制的卡
                  })) || [],
                }))
              })
            }
            this.cardTreeList = list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      // 设置规则
      setRules(url, params, config = {}) { // config =  { headers: { 'Content-Type': 'multipart/form-data' } }
        this.$service.post(url, params, config).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.$emit('updateList')
            this.handleClose()
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      // 检测提交规则 https://www.apifox.cn/apidoc/shared-c983695d-a122-4ddb-a72d-744048e615ee/api-19694920
      handleSummit() {
        this.$refs.addFormRef.validate((val) => {
          if (val) {
            const { action, rule_days, ...rest } = this.formData;
            const params = {
              bus_id: this.busId,
              rule_days
            };
            let checkIds = [];

            switch (this.setType) {
              case 'add':
              case 'copy':
              // case 'edit':
                checkIds = this.handleGetCheckIds(); // 拿到勾选的卡/课id
                if (checkIds.length) {
                  this.setRules(
                    '/Web/PointRule/addPointRule',
                    {
                      ...params,
                      rule_data: checkIds.map(obj_id => ({
                        obj_id,
                        action,
                        ...rest // rule_type, rule_num, obj_param; action=1时 obj_param=[1为购卡 2为续卡 3为升级]
                      }))
                    },
                    { headers: { 'Content-Type': 'application/json' } }
                  )
                } else {
                  this.$Message.error('至少勾选一个卡/课')
                }
                break;

              case 'edit':
                this.setRules(
                  '/Web/PointRule/editPointRule',
                  { ...params, ...rest, action, point_rule_ids: this.ids }
                )
                break;
              default:
                console.log(this.setType);
                break;
            }

          }
        })
      },

      handleClose(val) {
        this.$emit('update:show', !!val)
      },

      handleReset() {
        this.cardTreeList.length = 0;
        this.$refs.addFormRef.resetFields()
      },
      // 深度改变节点的勾选和禁用
      handleChangeTreeNode(node) {
        // 只处理最低子节点
        if (node.children) {
          node.children.forEach(v => { this.handleChangeTreeNode(v) })
        } else {
          // 如果卡种之前已经设置了对应的赠送规则，那么禁用该卡种的选择框，并取消勾选
          const { obj_param } = this.formData;
          node.disabled = node.obj_param.some(v => obj_param.includes(+v));
          node.disabled && (node.check = false);
        }
      },
      // 勾选赠送条件
      handleChangeOrderType() {
        clearTimeout(changeTreeNodeTimer);
        changeTreeNodeTimer = setTimeout(() => {
          this.cardTreeList.forEach(this.handleChangeTreeNode);
        }, 1000);
      },

      // 拿到勾选的卡，一维数组
      handleGetCheckIds() {
        const foo = v => v.children ?
          v.children.map(foo).filter(Boolean) :
          v.check ? v.id : false;

        return this.cardTreeList.map(foo).flat(2);
      }
    },
  }
</script>

<style lang="less" scoped>
.point-rule-modal {
  .point-rule-form {
    padding-right: 6px;
  }
}
.form-item-flex-row {
  display: flex;
  align-items: center;
}
.check-tree-form-item {
  padding-right: 6px;
  &.margin-left {
    margin-left: 70px;
  }
  .copy-card-tips {
    margin-bottom: 24px;
    font-size: 14px;
    text-align: center;
    .card-name {
      color: #d9001b;
    }
  }

  /deep/.filter-input-row {
    width: 418px;
  }
}
/deep/.check-tree-form-item:not(.margin-left) .filter-input-row {
  width:100%;
}

</style>
