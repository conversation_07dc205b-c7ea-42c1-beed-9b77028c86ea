<template>
  <div class="panel">
    <Alert v-if="alert">{{ alert }}</Alert>
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100">
      <FormItem :label="`每次${ labelText }获得`">
        <div class="batch-btn-row">
          <Button type="text" :disabled="isDisabled" @click="showModal=true">批量设置</Button>
          <slot name="tips"></slot>
        </div>
        <div class="list-box">
          <div v-for="item in list" :key="item.obj_id" class="item">
            <div class="name text_overflow" :title="item.obj_name">{{ item.obj_name }}</div>
            <div class="input-box">
              积分
              <!-- <div class="number">
                {{ item.rule_num }}
              </div> -->
              <InputNumber
                v-model="item.rule_num"
                style="margin: 0 10px;width:100px;"
                :disabled="isDisabled"
                :max="999999999"
                :min="0"
                :precision="0"
                :active-change="false"
                @on-change="handleChangeNum(item)"
              />
            </div>
          </div>
        </div>
        <FormItem v-if="type == 3">
          <FormItem
            label="单天获奖励节数上限"
            :label-width="132"
            prop="max"
            style="flex: 1;"
          >
            <div class="form-item-flex-row">
              <InputNumber
                v-model="formData.max"
                style="width:140px;margin-right: 6px;"
                :disabled="isDisabled"
                :max="999999999"
                :min="1"
                :precision="0"
                :active-change="false"
              />
              <span>节</span>
            </div>
          </FormItem>
        </FormItem>
      </FormItem>
      <!-- <FormItem :label="`累积${ labelText }获得`">
        <div class="form-item-flex-row">
        </div>
      </FormItem> -->
      <FormItem label="积分有效期" prop="rule_days">
        <div class="form-item-flex-row">
          <InputNumber
            v-model="formData.rule_days"
            style="width:140px;"
            :disabled="isDisabled"
            :max="999999"
            :min="1"
            :precision="0"
            :active-change="false"
          />
          <span style="margin-left: 6px;">天</span>
        </div>
      </FormItem>
      <FormItem>
        <Button
          v-show="isDisabled"
          type="success"
          @click="isDisabled=false">
          编辑
        </Button>
        <div v-show="!isDisabled">
          <Button
            type="success"
            @click="handleConfirm">
            保存
          </Button>
          <Button @click="() => { isDisabled=true; getData(false) }">取消</Button>
        </div>
      </FormItem>
    </Form>

    <EditModal
      :show.sync="showModal"
      :type="type"
      :list="list"
      @update-list="handleUpdateList"
    />
  </div>
</template>

<script>
  import EditModal from './pointRulesDailyItemComeModal';
  import { mapState } from 'vuex'

  export default {
    name: 'PointRulesDailyItemSign',
    components: {
      EditModal
    },
    props: {
      type: { // 触发动作类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课
        validator: (val) => [3, 4, 5].includes(+val),
        required: true,
      },
      alert: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        isDisabled: true,
        showModal: false,
        formData: {
          max: null,
          rule_days: null,
        },
        formRules: {
          rule_days: [
            { required: true, type: 'number', message: '请填写有效期', trigger: 'change' }
          ],
          max: [
            { required: true, type: 'number', message: '请填写单天获奖励节数上限', trigger: 'change' }
          ]
        },
        list: [],
      }
    },
    computed: {
      ...mapState(['busId']),
      labelText() {
        return this.type == 4 ? '订场' : '上课';
      },
    },

    created() {
      this.getData()
    },

    methods: {
      getData(loading = true) {
        const params = {
          type: this.type, // 规则类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课
          // page_no: 1,
          // page_size: 1,
        }
        this.$service.post('/Web/PointRule/getPointRuleList', params,  { loading }).then(res => {
          if (res.data.errorcode === 0) {
            const { list, max } = res.data.data;
            if (list.length) {
              const { rule_days } = list[0];
              !loading && this.$refs.formRef.resetFields() // 取消时重置表单校验
              this.formData.rule_days = rule_days || null;
              this.list = list;
            }
            if (max !== 0) {
              this.formData.max = +max;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      handleChangeNum(item) {
        item.editNum = true; // 用于判断是否有修改，只把修改的项提交
      },

      handleUpdateList({ rule_num, checkIds }) {
        checkIds.forEach(id => {
          const item = this.list.find(v => v.obj_id == id);
          item.rule_num = rule_num;
          item.editNum = true;
        })
      },
      // https://www.apifox.cn/apidoc/shared-c983695d-a122-4ddb-a72d-744048e615ee/api-19694920
      handleConfirm() {
        this.$refs.formRef.validate(val => {
          if (val) {
            // 准备数据
            const { busId, type, formData, setRules } = this;
            const params = {
              bus_id: busId,
              rule_days: formData.rule_days,
              rule_data: [
                ...this.list.filter(v => v.editNum || v.rule_num).map(v => ({
                  action: type, // 触发动作类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课
                  obj_id: v.obj_id,
                  rule_type: 0, // 规则类型 0为固定分值 1为成交价比例
                  rule_num: v.rule_num || 0, // 规则值
                })),
              ],
              ...type == 3 ? { max: formData.max } : {} // 除团课外，其他暂时没有max表单

            }
            this.$service.post(
              '/Web/PointRule/addPointRule',
              params,
              { headers: { 'Content-Type': 'application/json' } }
            ).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.getData()
                this.isDisabled = true;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.form-item-flex-row {
  display: flex;
  align-items: center;
}
.panel {
  margin-left: 1%;
  width: 70%;
}

.batch-btn-row {
  .form-item-flex-row;
  justify-content: space-between;
}

.list-box {
  overflow-y: auto;
  margin-bottom: 24px;
  padding: 0 16px;
  width: 100%;
  height: 300px;
  max-height: 300px;
  border: 1px solid #ccc;
  border-radius: 3px;
  .item {
    display: flex;
    align-items: center;
    height: 50px;

    .name {
      width: 50%;
    }
    .input-box {
      display: flex;
      align-items: center;
    }
    .number {
      overflow: hidden;
      display: inline-block;
      margin: 0 10px;
      padding: 0 7px;
      width: 100px;
      height: 32px;
      line-height: 32px;
      color: #666;
      background-color: #fff;
      background-image: none;
      cursor: text;
      transition: border 0.2s ease-in-out;
      vertical-align: middle;
      border: 1px solid #dcdee2;
      border-radius: 4px;

      &:hover {
        border-color: #57a3f3;
      }
    }
  }
}

.ivu-btn {
  margin-right: 18px
}
</style>
