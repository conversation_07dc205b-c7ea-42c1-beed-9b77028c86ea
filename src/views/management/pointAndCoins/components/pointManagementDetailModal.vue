<template>
  <Modal
    title="积分详情"
    :value="show"
    width="870"
    footer-hide
    @on-visible-change="$emit('update:show', $event)">
    <div>
      <div class="point-sn">积分编号 {{ itemData.merchant_point_sn }}</div>
      <Table
        :columns="columns"
        :data="tableData"
        max-height="400"
        disabled-hover
      />
    </div>
  </Modal>
</template>

<script>
  const TYPE = {
    '0': '发放',
    '1': '扣除'
  }

  export default {
    name: 'PointManagementDetailModal',
    props: {
      show: {
        type: Boolean,
        required: true
      },
      itemData: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        tableData: [],
        columns: [
          {
            title: '时间',
            minWidth: 80,
            key: 'create_time',
          },
          {
            title: '类型',
            key: 'type_text',
          },
          {
            title: '积分数量',
            key: 'num',
          },
          {
            title: '操作前',
            key: 'before_num',
          },
          {
            title: '操作后',
            key: 'after_num',
          },
          {
            title: '操作人',
            key: 'operator_name_text',
          },
          {
            title: '操作门店',
            key: 'bus_name',
          },
          {
            title: '描述',
            key: 'remark',
            minWidth: 150,
            tooltip: true,
          },
        ]
      }
    },

    watch: {
      show(val) {
        if (val) {
          this.getDetail();
        } else {
          this.tableData.length = 0;
        }
      }
    },

    methods: {
      getDetail() {
        const {
          id: point_id,
          merchant_user_id
        } = this.itemData;

        this.$service.post('/Web/Point/getPointDetailList', { point_id, merchant_user_id }).then(res => {
          if (res.data.errorcode === 0) {
            const { list } = res.data.data;
            list.forEach(v => {
              v.type_text = TYPE[v.type];
              v.operator_name_text =  typeof v.operator_name === 'string' ? v.operator_name : '系统';
            });
            this.tableData = list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      }
    },
  }
</script>

<style lang="less" scoped>
.point-sn {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}
</style>
