<template>
  <Modal
    title="批量设置"
    :value="show"
    @on-visible-change="handleClose">
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="100">
      <FormItem label="积分" prop="rule_num">
        <InputNumber
          v-model="formData.rule_num"
          style="width:140px;"
          :max="999999999"
          :min="1"
          :precision="0"
          :active-change="false"
        />
      </FormItem>
    </Form>
    <div class="check-tree-form-item margin-left">
      <CheckPtClass
        v-if="show"
        :treeList="cardTreeList"
      />
    </div>
    <div slot="footer" class="modal-buttons">
      <Button :disabled="!cardTreeList.length" type="success" @click="handleSummit">确定</Button>
      <Button @click="handleClose()">取消</Button>
      <!-- <Button @click="handleReset">重置</Button> -->
    </div>
  </Modal>
</template>

<script>
  import CheckPtClass from 'components/form/checkPtClass';
  import { mapState } from 'vuex';

  const cardGroupNames = {
    card_group_list: '会籍卡',
    pt_card_group_list: '私教课',
    swim_card_group_list: '泳教课',
  };
  let changeTreeNodeTimer = null;

  export default {
    name: 'PointRulesDailyItemModal',
    components: {
      CheckPtClass
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      type: { // 触发动作类型 1 合同类型 2 签到 3 团课 4订场 5 私教/泳教销课
        validator: (val) => [3, 4, 5].includes(+val),
        required: true,
      },
      list: {
        type: Array,
        required: true
      },
    },
    data() {
      return {
        cardTreeList: [],
        formData: {
          rule_num: null, // 规则值
        },
        formRules: {
          rule_num: [
            { required: true, type: 'number', message: '请填写积分数值', trigger: 'change' }
          ],
        },
      }
    },

    computed: {
      ...mapState(['busId']),
    },

    watch: {
      show(val) {
        if (val) {
            this.getList();
        } else {
          this.handleReset();
        }
      },
    },

    methods: {
      // 获取列表
      getList() {
        if (this.type == 5) {
          this.$service.post('/Web/PointRule/getSetCardPointRuleCardList', { bus_id: this.busId }).then(res => {
            if (res.data.errorcode === 0) {
              delete res.data.data.card_group_list;
              const list = [];
              for (const [key, val] of Object.entries(res.data.data)) {
                const parentId = key;
                list.push({
                  id: parentId,
                  name: cardGroupNames[key] || 'null',
                  check: false,
                  show: true,
                  disabled: false,
                  children: val.map(({ card_list, title, ...rest }) => ({
                    ...rest,
                    parentId,
                    name: title,
                    check: false,
                    show: true,
                    disabled: false,
                    children: card_list.map(({ card_name, ...subRest }) => ({
                      ...subRest,
                      parentId: rest.id,
                      id: subRest.card_id,
                      name: card_name,
                      check: false,
                      show: true,
                      disabled: false,
                    })) || [],
                  }))
                })
              }
              this.cardTreeList = list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
        } else {
          this.cardTreeList = this.list.map(v => ({
            id: v.obj_id,
            name: v.obj_name,
            check: false,
            show: true,
            disabled: false,
          }));
        }
      },

      // 检测提交规则
      handleSummit() {
        this.$refs.formRef.validate((val) => {
          if (val) {
            let checkIds = this.handleGetCheckIds();
            if(!checkIds.length) return this.$Message.error('至少需勾选一项');

            const { rule_num } = this.formData;
            this.$emit('update-list', { rule_num, checkIds })
            this.handleClose()
          }
        })
      },

      handleClose(val) {
        this.$emit('update:show', !!val)
      },

      handleReset() {
        this.cardTreeList.length = 0;
        this.$refs.formRef.resetFields()
      },
      // 深度改变节点的勾选和禁用
      handleChangeTreeNode(node) {
        // 只处理最低子节点
        if (node.children) {
          node.children.forEach(v => { this.handleChangeTreeNode(v) })
        } else {
          // 如果卡种之前已经设置了对应的赠送规则，那么禁用该卡种的选择框，并取消勾选
          const { obj_param } = this.formData;
          node.disabled = node.obj_param.some(v => obj_param.includes(+v));
          node.disabled && (node.check = false);
        }
      },
      // 勾选赠送条件
      handleChangeOrderType() {
        clearTimeout(changeTreeNodeTimer);
        changeTreeNodeTimer = setTimeout(() => {
          this.cardTreeList.forEach(this.handleChangeTreeNode);
        }, 1000);
      },

      // 拿到勾选的卡，一维数组
      handleGetCheckIds() {
        const foo = v => v.children ?
          v.children.map(foo).filter(Boolean) :
          v.check ? v.id : false;

        return this.cardTreeList.map(foo).flat(2).filter(Boolean);
      }
    },
  }
</script>

<style lang="less" scoped>
.form-item-flex-row {
  display: flex;
  align-items: center;
}
.check-tree-form-item {
  &.margin-left {
    margin-left: 100px;
  }
 }

</style>
