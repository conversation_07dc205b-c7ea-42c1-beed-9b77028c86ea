<template>
  <Modal
    title="扣除"
    :value="show"
    @on-visible-change="handleClose">
    <Form
      ref="pointDeductFormRef"
      :model="formData"
      :rules="formRules"
      :label-width="120">
      <FormItem label="积分编号">{{ itemData.merchant_point_sn }}</FormItem>
      <FormItem label="剩余积分">{{ itemData.last_num || 0 }}</FormItem>
      <FormItem label="扣除数量" prop="delNum">
        <InputNumber
          v-model="formData.delNum"
          style="width: 200px;"
          :max="itemData.last_num ? Math.floor(itemData.last_num) : 0"
          :min="1"
          :precision="0"
          :active-change="false"
        />
      </FormItem>
      <FormItem label="扣除后剩余">{{ result }}</FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleSummit">扣除</Button>
      <Button @click="handleClose(false)">取消</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'PointManagementDeductModal',
    props: {
      show: {
        type: Boolean,
        required: true
      },
      itemData: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        formData: {
          delNum: null,
        },
        formRules: {
          delNum: [
            { required: true, type: 'number', message: '请填写扣除数量', trigger: 'change' }
          ],
        }
      }
    },
    computed: {
      result() {
        const { formData, itemData } = this;
        const result = itemData.last_num - formData.delNum;
        return result.toFixed(0);
      },
    },

    methods: {
      handleSummit() {
        this.$refs.pointDeductFormRef.validate((val) => {
          if (val) {
            const { formData, itemData } = this;
            const params = {
              user_id: itemData.merchant_user_id,
              num: formData.delNum,
              remark: '',
              point_id: itemData.id
            }

            this.$service.post('/Web/Point/PointDeduct', params).then(res => {
              this.$Message.success(res.data.errormsg)
              if (res.data.errorcode === 0) {
                this.$emit('updateList')
                this.handleClose()
              }
            })
            .catch(err => {
              console.error(err);
            });
          }
        })

      },
      handleClose(val = false) {
        this.$emit('update:show', val)
        this.$refs.pointDeductFormRef.resetFields()
      }
    },
  }
</script>

<style lang="less" scoped>

</style>
