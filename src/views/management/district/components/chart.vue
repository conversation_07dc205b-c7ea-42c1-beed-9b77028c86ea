<template>
  <div class="chart-wrap">
    <div style="display: flex; justify-content: space-between">
      <h3 style="padding-left: 10px">{{title}}</h3>
      <RadioGroup v-if="buttons && buttons.length > 1" v-model="checkedButton" @on-change="radioChange" type="button" style="display: flex; flex-wrap: nowrap; align-self: flex-end">
        <Radio style="display: flex; justify-content: center; align-items: center; height: 24px" v-for="(item, index) in buttons" :key="index" :label="index">{{item.title}}</Radio>
      </RadioGroup>
      <div v-else style="height: 24px"></div>
    </div>
    <div ref="chart" class="chart"></div>
  </div>
</template>

<script>
  import Echarts from 'echarts';
  import { debounce } from 'lodash-es';
  const COLOR = [
    '#1abbde',
    '#a76de8',
    '#ff6969',
    '#1bd4c9',
    '#b5db4f',
    '#ff7b44',
    '#80df89',
    '#f4ed28',
    '#ff85b3',
    '#8c8c8c',
    '#0000ff',
    '#972ff8'
  ];
  const DATA_ZOOM = [
    {
      type: 'slider',
      xAxisIndex: [0],
      filterMode: 'filter',
      bottom: '0%',
      startValue: 0,
      endValue: 4
    },
    // {
    //   type: 'slider',
    //   yAxisIndex: [0],
    //   filterMode: 'empty',
    //   minSpan: 100
    // },
    {
      type: 'inside',
      xAxisIndex: [0],
      filterMode: 'filter',
      startValue: 0,
      endValue: 4
    },
    {
      type: 'inside',
      yAxisIndex: [0],
      filterMode: 'empty',
      minSpan: 100
    }
  ];
  export default {
    name: 'regionChart',
    props: {
      value: {}
    },
    data() {
      return {
        title: '',
        buttons: [],
        checkedButton: 0,
        chart: '',
        option: {
          animation: false,
          color: COLOR,
          title: {
            text: '',
            show: false,
            left: 20,
            top: 10,
            textStyle: {
              fontSize: 14,
              color: '#999'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: [],
            type: 'scroll',
            top: 37,
            bottom: 30,
            right: 0,
            pageIconSize: 10,
            width: 120,
            orient: 'vertical',
            formatter: function(name) {
              return name.substring(0, 10);
            },
            itemHeight: 12,
            itemWidth: 12,
            // align: 'left',
            animation: true
          },
          toolbox: {
            show: false,
            feature: {
              magicType: {
                show: true,
                type: ['stack', 'tiled'],
                title: {
                  stack: '叠加总和',
                  tiled: '分类对比'
                }
              },
              saveAsImage: { show: true }
            },
            right: 20
          },
          grid: {
            right: 150,
            left: 80
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value',
            minInterval: 1
          },
          series: []
        }
      };
    },
    watch: {
      'value.done'(val) {
        if (val) {
          const { xAxis, legend, series, buttons, unit, title, selected } = this.value;
          this.option.legend.data = legend;
          this.option.legend.selected = selected;
          this.option.xAxis.data = xAxis;
          this.option.yAxis.name = unit;
          this.option.series = series;
          this.option.title.text = title;
          this.title = title;
          this.buttons = buttons;
          if (series[0].type == 'bar' || series[0].type == 'scatter') {
            this.option.dataZoom = DATA_ZOOM;
            this.option.xAxis.boundaryGap = true;
            this.option.grid.right = 20;
            this.option.legend.show = false;
            this.option.toolbox.feature.magicType.show = false;
          }
          if (series[0].type == 'scatter') {
            this.option.tooltip = {
              position: 'right',
              formatter: function(params) {
                return `<div style="display: flex; padding: 5px">
                          <div style="display: flex; flex-direction: column; align-items: center; padding-right: 10px">
                            <img style="width: 50px; height: 50px; border-radius: 50%;" src="${params.value[5]}">
                            <p>${params.value[6]}</p>
                          </div>
                          <div style="display: flex; flex-direction: column; justify-content: space-between">
                            <p style="font-size: 14px">场馆: ${params.value[2]} 位, 当前: ${params.value[3]} 位</p>
                            <p style="font-size: 16px; color: #f4ed28">￥${params.value[1]}</p>
                            <p style="font-size: 14px">${params.value[4]}</p>
                          </div>
                        </div>`;
              }
            };
          }
          this.$nextTick(() => {
            this.chart.setOption(this.option, { notMerge: true });
            this.value.done = false;
          });
        }
      }
    },
    mounted() {
      this.chart = Echarts.init(this.$refs.chart);
      window.addEventListener('resize', this.onResize);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.onResize);
    },
    methods: {
      radioChange(index) {
        const item = this.buttons[index];
        this.$emit('on-change', item.type);
      },
      onResize: debounce(function() {
        this.chart.resize();
      }, 600)
    }
  };
</script>

<style lang="less" scoped>
  .chart-wrap {
    display: flex;
    flex-direction: column;
  }
  .chart {
    height: 400px;
  }
</style>
