<template>
  <div>
    <TreeMenu class="menu" :data="treeData" @on-add="onAddRegion" @on-edit="onEditRegion" @on-delete="onDeleteRegion" @on-select="id => { $listeners['on-change'](id) }" />
    <Modal title="编辑分区" v-model="showEditModal">
      <Form class="modal-form" :label-width="120" label-position="right" style="padding-right: 40px;">
        <FormItem label="分区名称">
          <Input v-model="editRegionName" @on-enter.stop="editRegion"></Input>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="editRegion">确定</Button>
        <Button @click="cancelEditRegion">取消</Button>
      </div>
    </Modal>
    <Modal title="添加分区" v-model="showAddModal" :maskClosable="false">
      <Form class="modal-form" :label-width="120" label-position="right" style="padding-right: 40px;">
        <FormItem label="分区名称">
          <Input v-model="addRegionName" @on-enter="addRegion"></Input>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="addRegion">确定</Button>
        <Button @click="cancelAddRegion">取消</Button>
      </div>
    </Modal>
    <Modal title="删除分区" v-model="showDeleteModal">
      <Form class="modal-form" :label-width="120" label-position="right" style="padding-right: 40px;">
        <FormItem style="color: red">删除分区会同时删除子分区，请将分区下场馆转移到新分区</FormItem>
        <FormItem label="将场馆转移到">
          <Select placeholder="分区" class="group-select" v-model="transferRegionId" v-if="regionListFlat" filterable>
            <Option v-for="region in regionListFlat" :key="region.id" :value="region.id" :label="region.title" :style="{'padding-left': (+region.level + 1)*10+'px'}"></Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="deleteRegion">确定</Button>
        <Button @click="cancelDeleteRegion">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import TreeMenu from 'components/treeMenu';
  import { defaultsDeep } from 'lodash-es';
  import { treeToList } from 'src/utils'
  const DATA = {};
  export default {
    name: 'districtSideMenu',
    components: { TreeMenu },
    data() {
      return {
        regionListFlat: '',
        treeData: [],
        regionList: [],
        showEditModal: false,
        showAddModal: false,
        showDeleteModal: false,
        transferRegionId: 0,
        addRegionName: '',
        editRegionName: ''
      };
    },
    created() {
      this.getTreeData();
    },
    methods: {
      onAddRegion(data) {
        this.showAddModal = true;
        DATA.addRegionId = data.id;
      },
      onEditRegion(data) {
        this.showEditModal = true;
        DATA.editRegionId = data.id;
        this.editRegionName = data.title;
      },
      onDeleteRegion({ id }) {
        this.showDeleteModal = true;
        DATA.deleteRegionId = id;
      },
      cancelAddRegion() {
        this.addRegionName = '';
        this.showAddModal = false;
      },
      cancelEditRegion() {
        this.editRegionName = '';
        this.showEditModal = false;
      },
      cancelDeleteRegion() {
        this.transferRegionId = 0;
        this.showDeleteModal = false;
      },
      editRegion() {
        const url = '/web/BusRegion/update_bus_region';
        const { editRegionName: title } = this;
        const { editRegionId: id } = DATA;
        if (!title) return this.$Message.error('请输入分区名称');
        this.$service
          .post(url, { title, id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.getTreeData();
              this.cancelEditRegion();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      addRegion() {
        const url = '/web/BusRegion/add_bus_region';
        const { addRegionName: title } = this;
        const { addRegionId: pid } = DATA;
        if (!title) return this.$Message.error('请输入分区名称');
        this.$service
          .post(url, { title, pid })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.getTreeData();
              this.cancelAddRegion();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      deleteRegionTree(data, id) {
        for (let [index, item] of data.children.entries()) {
          if (item.id == id) {
            data.children.splice(index, 1);
            break;
          } else if (item.children && item.children.length) {
            this.deleteRegionTree(item, id);
          }
        }
      },
      deleteRegion() {
        this.$Modal.confirm({
          title: '是否删除分区',
          content: '该修改会影响区域下门店，导致原账号管理门店范围变更。确认是否修改？',
          onOk: () => {
            this.deleteRegionTree(this.treeData[0], DATA.deleteRegionId);
            const url = '/web/BusRegion/delete_bus_region';
            const { deleteRegionId: id } = DATA;
            const { transferRegionId: move_id } = this;
            this.$service
              .post(url, { id, move_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg);
                  this.getTreeData();
                  this.$emit('on-change');
                  this.cancelDeleteRegion();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          },
        })
      },
      getTreeData() {
        const url = '/web/BusRegion/bus_region_list';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.$emit('input', [data]);
              this.regionList = [data];
              const tree = [data].map(item => {
                return {
                  ...item,
                  ...{
                    expand: true,
                    children:
                      item.children &&
                      item.children.map(item => {
                        return {
                          ...item,
                          ...{
                            expand: true,
                            children:
                              item.children &&
                              item.children.map(item => {
                                return {
                                  ...item,
                                  ...{
                                    expand: undefined,
                                    children:
                                      item.children &&
                                      item.children.map(item => {
                                        return {
                                          ...item,
                                          ...{
                                            expand: undefined
                                          }
                                        };
                                      })
                                  }
                                };
                              })
                          }
                        };
                      })
                  }
                };
              });
              this.treeData = [defaultsDeep(tree[0], this.treeData[0])];
              this.regionListFlat = treeToList([data], 'children');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style scoped>
</style>
