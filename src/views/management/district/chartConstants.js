export const TYPES = ['customer', 'income', 'daily', 'ptClass', 'salesRank', 'member'];

export const TITLES = {
  customer: '客流走势',
  income: '收入流水走势',
  daily: '日常经营',
  ptClass: '上课课时走势',
  salesRank: '业绩排行',
  member: '会员人数'
};

export const UNITS = {
  customer: '人',
  income: '元',
  daily: {
    new_user_not_buycard_count: '人',
    new_buycard_count: '张',
    new_buy_pt_num: '节'
  },
  ptClass: '节',
  salesRank: '元',
  member: '人'
};

export const BUTTONS = {
  customer: [
    { title: '客流', type: 'total' },
    { title: '训练', type: 'other_class' },
    { title: '私教', type: 'private_class' },
    { title: '泳教', type: 'swim_class' },
    { title: '体验', type: 'to_visit_list' }
  ],
  daily: [
    { title: '新增潜客', type: 'new_user_not_buycard_count' },
    { title: '新购卡', type: 'new_buycard_count' },
    { title: '新购私教', type: 'new_buy_pt_num' },
    { title: '新购泳教', type: 'new_buy_swim_num' }
  ],
  income: [
    {
      title: '累计流水',
      type: 'flow_sum'
    },
    {
      title: '单日流水',
      type: 'flow_dayly'
    }
  ],
  ptClass: [{ title: '累计对比', type: 'sum_data' }, { title: '单日对比', type: 'days_data' }],
  salesRank: [{ title: '会籍业绩', type: 'mark_data' }, { title: '教练业绩', type: 'coca_data' }],
  member: [
    { title: '潜客', type: 'submersible' },
    { title: '过期', type: 'overdue' },
    { title: '普通会员', type: 'ordinary' },
    { title: '私教会员', type: 'private' },
    { title: '泳教会员', type: 'swim' }
  ]
};

export const URLS = {
  customer: '/web/SubregionStatistics/customer',
  income: '/web/SubregionStatistics/get_income_details',
  daily: '/web/SubregionStatistics/dailyoperations',
  ptClass: '/web/SubregionStatistics/pt_class_trend',
  salesRank: '/web/SubregionStatistics/achievement_rank',
  member: '/web/SubregionStatistics/membership_statistics'
};
