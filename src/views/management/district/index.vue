
<style lang="less">
  .district-table {
    display: flex;
    .table-wrap {
      flex: 1;
    }
  }
  .group-select {
    width: 250px;

    .group {
      font-size: 14px !important;
      font-weight: bold;
    }

    .ivu-select-item-selected,
    .ivu-select-item-selected:hover {
      color: #fff;
      background: rgba(45, 140, 240, 0.9);
    }

    .ivu-select-group-title {
      display: none;
    }
  }
</style>

<template>
  <div class="district-table">
    <TreeMenu v-model="regionList" @on-change="onRegionChange"></TreeMenu>
    <div class="table-wrap">
      <header>
        <Input v-model="postData.bus_name" style="width: 240px" placeholder="场馆名称" @on-enter="doSearch"></Input>
        <Button type="success" @click="doSearch">搜索</Button>
      </header>
      <Table ref="table" @on-selection-change="onSelectionChange" :columns="columns" :data="tableData"></Table>
      <footer>
        <Button type="success" @click="onSettingRegion" style="margin-right: 15px">设置分区</Button>
        <Dropdown placement="top" @on-click="otherCase">
          <Button>其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="export">导出Excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Pager :total="listCount" @on-change="pageChange" :post-data="postData"></Pager>
      </footer>
    </div>
    <Modal title="设置场馆分区" v-model="showSettingModal">
      <Form class="modal-form" :label-width="120" label-position="right" style="padding-right: 40px;">
        <FormItem label="设置分区">
          <Select placeholder="分区" class="group-select" v-model="transferRegionId" v-if="regionListFlat" filterable>
            <Option v-for="region in regionListFlat" :key="region.id" :value="region.id" :label="region.title" :style="{'padding-left': (+region.level + 1)*10+'px'}"></Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="settingRegion">确定</Button>
        <Button @click="cancelSettingRegion">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Pager from 'src/components/pager';
  import TreeMenu from './districtSideMenu.vue';
  import { treeToList } from 'src/utils'

  const DATA = {};
  export default {
    name: 'DistrictSetting',
    components: { Pager, TreeMenu },
    data() {
      return {
        regionListFlat: '',
        regionList: '',
        listCount: 0,
        showSettingModal: false,
        transferRegionId: 0,
        postData: {
          region_id: '',
          bus_name: '',
          page_no: 1,
          page_size: 10
        },
        tableData: [],
        columns: [
          {
            type: 'selection',
            width: '80'
          },
          {
            title: '场馆名称',
            key: 'bus_name'
          },
          {
            title: '区域',
            key: 'region_name'
          },
          {
            title: '场馆组',
            key: 'level_name',
            render: (h, params) => {
              const item = params.row;
              return <span>{ item.level_name || '-' }</span>;
            }
          },
          {
            title: '地址',
            key: 'address',
          },
          {
            title: '联系电话',
            key: 'phone'
          },
          {
            title: '操作',
            render: (h, params) => {
              const item = params.row;
              return <router-link to={{ path: '/gym/detail/edit', query: { id: item.bus_id } }}><i-button type="text">编辑</i-button></router-link>;
            }
          }
        ]
      };
    },
    watch: {
      showSettingModal(val) {
        val && this.getTreeData()
      }
    },
    methods: {
      getTreeData() {
        const url = '/web/BusRegion/bus_region_list';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.regionListFlat = treeToList([data], 'children');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onRegionChange(id) {
        this.postData.region_id = id;
        this.postData.page_no = 1;
        this.getList();
      },
      onSelectionChange(selection) {
        DATA.selectBusId = selection.map(item => item.bus_id);
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      getList() {
        const url = '/web/BusRegion/get_bus_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.listCount = +data.count;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    address: `${item.province_name === item.city_name ? '' : item.province_name || ''}${item.city_name || ''}${item.district_name || ''}${item.address}`
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onSettingRegion() {
        if (!DATA.selectBusId || !DATA.selectBusId.length) {
          return this.$Message.error('请选择要设置的场馆');
        }
        this.showSettingModal = true;
      },
      cancelSettingRegion() {
        this.showSettingModal = false;
        this.transferRegionId = 0;
      },
      settingRegion() {
        this.$Modal.confirm({
          title: '是否设置分区',
          content: '该修改会影响区域下门店，导致原账号管理门店范围变更。确认是否修改？',
          onOk: () => {
            const url = '/web/BusRegion/set_bus_region';
            const postData = {
              bus_ids: DATA.selectBusId.join(','),
              region_id: this.transferRegionId
            };
            this.$service
              .post(url, postData)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg);
                  this.getList();
                  this.cancelSettingRegion();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          },
        })

      },
      getExportData() {
        const url = '/web/BusRegion/get_bus_list';
        return this.$service
          .post(url, { ...this.postData, ...{ page_no: 1, page_size: this.listCount } })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.map(item => {
                return {
                  ...item,
                  ...{
                    region_name: `${item.province_name || ''}${item.city_name || ''}${item.district_name || ''}`,
                    level_name: item.level_name || '-'
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      otherCase(name) {
        const event = {
          export: () => {
            this.exportCsv();
          }
        };
        event[name]();
      },
      async exportCsv() {
        const data = await this.getExportData();
        const columns = this.columns.filter(item => item.key);
        this.$refs.table.exportCsv({ columns, data, filename: '区域场馆导出' });
      }
    }
  };
</script>
