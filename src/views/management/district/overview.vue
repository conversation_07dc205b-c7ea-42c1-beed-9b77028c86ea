<template>
  <div class="region-overview">
    <Card>
      <Form class="modal-form" :label-width="100" label-position="left">
        <FormItem label="时间">
          <DatePicker type="daterange" :clearable="false" :editable="false" :options="dateOptions" v-model="dateRange" @on-change="getChartsData"></DatePicker>
        </FormItem>
        <FormItem label="查看维度">
          <RadioGroup v-model="type" @on-change="onTypeChange">
            <Radio :label="1">看分区数据</Radio>
            <Radio :label="2">看场馆数据</Radio>
          </RadioGroup>
        </FormItem>
        <div class="select">
          <Select class="region-select" placeholder="区域级别" v-model="level" @on-change="getRegionNBusId">
            <Option v-for="(item, index) in regionLevel" :key="item.id" :value="item.id">{{item.name}}</Option>
          </Select>
          <Select v-model="multiple" @on-change="onSelectChange" class="multiple-select" placeholder="查询对象" multiple filterable>
            <Option v-for="(item, index) in regionList" :key="item.id" :value="item.id">{{item.name}}</Option>
          </Select>
          <!-- <Button type="success" style="margin-left: 24px">查询</Button> -->
        </div>
      </Form>
    </Card>
    <template v-if="type && level">
      <Row :gutter="16" style="margin-top: 20px">
        <Col span="12">
        <Card>
          <Chart v-model="customer" @on-change="key => dealFlow('customer', key)"></Chart>
        </Card>
        </Col>
        <Col span="12">
        <Card>
          <Chart v-model="income" @on-change="key => dealFlow('income', key)"></Chart>
        </Card>
        </Col>
      </Row>
      <Row :gutter="16" style="margin-top: 20px">
        <Col span="12">
        <Card>
          <Chart v-model="daily" @on-change="key => dealFlow('daily', key)"></Chart>
        </Card>
        </Col>
        <Col span="12">
        <Card>
          <Chart v-model="ptClass" @on-change="key => dealFlow('ptClass', key)"></Chart>
        </Card>
        </Col>
      </Row>
      <Row :gutter="16" style="margin-top: 20px">
        <Col span="12">
        <Card>
          <Chart v-model="salesRank" @on-change="key => dealFlow('salesRank', key)"></Chart>
        </Card>
        </Col>
        <Col span="12">
        <Card>
          <Chart v-model="member" @on-change="key => getFlow('member', key)"></Chart>
        </Card>
        </Col>
      </Row>
    </template>
  </div>
</template>

<script>
  import Chart from './components/chart.vue';
  import { formatDate } from 'utils';
  import { TYPES, TITLES, BUTTONS, URLS, UNITS } from './chartConstants.js';
  import quickPicker from 'components/picker/quickPicker';
  import { debounce } from 'lodash-es';

  export default {
    name: 'regionOverview',
    components: { Chart },
    data() {
      return {
        dateRange: [
          formatDate(new Date(Date.now() - 7 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
          formatDate(new Date(), 'yyyy-MM-dd')
        ],
        dateOptions: quickPicker.getOptions(['一周', '一个月', '三个月']),
        regionLevel: [],
        regionList: [],
        type: '',
        level: '',
        multiple: [],
        customer: { done: false },
        income: {
          done: false
        },
        daily: {
          done: false
        },
        ptClass: {
          done: false
        },
        salesRank: { done: false },
        member: { done: false }
      };
    },
    created() {
      // this.getRegionLevel();
    },
    methods: {
      onSelectChange: debounce(function() {
        this.getChartsData();
      }, 0),
      // 处理 series 数据
      dealFlow(type, key) {
        if (type === 'daily') {
          this[type].unit = UNITS[type][key];
        }
        this[type]['buttonKey'] = key;
        const data = this[type]['data'][key];
        const map = this[type].map;
        if (type === 'salesRank') {
          let scatterData = [];
          Object.keys(data).forEach(busKey => {
            scatterData = scatterData.concat(
              data[busKey].map(sale => [
                map[busKey],
                sale['amount'],
                sale['team_rank'],
                sale['all_rank'],
                sale['bus_name'],
                sale['avatar'],
                sale['mk_name']
              ])
            );
          });
          this[type].series = [{ type: 'scatter', data: scatterData }];
        } else if (type == 'member') {
          this[type].series = [{ type: 'bar', data: Object.values(data), name: '会员人数' }];
        } else {
          this[type].series = Object.entries(data).map(([key, value]) => {
            return {
              name: map[key],
              type: 'line',
              smooth: true,
              areaStyle: { normal: {} },
              data: Object.values(value)
            };
          });
        }
        this[type].done = true;
      },
      // 处理 x 轴和 legend 数据
      dealChart(type, data, map) {
        if (type == 'member' || type == 'salesRank') {
          this[type].xAxis = Object.keys(data).map(key => map[key]);
        } else {
          this[type].xAxis = Object.keys(Object.values(data)[0]);
          this[type].legend = Object.keys(data).map(key => {
            return { name: map[key], icon: 'rect' };
          });
          this[type].selected = {};
          // 默认显示前 10 个场馆
          Object.keys(data).splice(10).map(key => {
            this[type].selected[map[key]] = false ;
          });
        }
      },
      getFlow(type, key) {
        this[type].buttons = BUTTONS[type];
        this[type].title = TITLES[type];
        this[type].unit = UNITS[type];
        const url = URLS[type];
        const [begin, end] = this.dateRange;
        const s_date = formatDate(begin, 'yyyy-MM-dd');
        const e_date = formatDate(end, 'yyyy-MM-dd');
        const postData = { s_date, e_date, type: this.type, ids: this.multiple.join(','), level: this.level };
        if(type == 'member') {
          postData.data_type = key
        }
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data;
              const { data: dataItem, name_map: map } = data;
              this[type].data = dataItem;
              this[type].map = map;
              this.dealChart(type, dataItem[key], map);
              this.dealFlow(type, key);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getRegionLevel() {
        const url = '/web/BusRegion/getRegionLevel';
        return this.$service
          .post(url, { type: this.type })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.regionLevel = data.level;
              this.level = data.level[0].id;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getChartsData() {
        const { type, level } = this;
        if (!type || !level) return false;
        Promise.all(TYPES.map(type => this.getFlow(type, this[type]['buttonKey'] || BUTTONS[type][0].type)));
      },
      async onTypeChange() {
        await this.getRegionLevel();
        this.getRegionNBusId();
      },
      getRegionNBusId() {
        const url = '/web/BusRegion/getRegionBus';
        const { type, level } = this;
        if (!type || !level) return false;
        this.$service
          .post(url, { type, level })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.regionList = data;
              this.multiple = data.map(item => item.id);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .modal-form {
    width: 50%;
    margin-left: 24px;
  }
  .select {
    display: flex;
  }
  .region-select {
    width: 140px;
    margin-right: 24px;
  }
  .multiple-select {
    flex: 1;
  }
</style>
