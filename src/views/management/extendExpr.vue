<template>
  <div class="table-wrap">
    <Table :columns="columns"
           :data="tableData"
           stripe
           disabled-hover></Table>
    <footer>
      <Button type="success"
              style="margin-right: 30px"
              to="/management/setDelayRule">设置批量延时
      </Button>
      <Page :total="totalCount"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            @on-change="gettableList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <Modal v-model="showSettable" :mask-closable="false" title="设置批量延时">
      <Form ref="form" :model="formData" class="modal-form" :label-width="100" style="padding: 0 30px"
            :rules="formRule">
        <Form-item class="allwidth" label="延长时间" prop="days">
          <InputNumber :min="1" :max="300" placeholder="天" v-model="formData.days"></InputNumber>
        </Form-item>
        <Form-item label="执行原因" prop="reason">
          <Input type="textarea" placeholder="请填写原因" v-model="formData.reason" :rows="5"
                 :autosize="{minRows: 4, maxRows: 6}"/>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="submitForm">确定</Button>
        <Button @click="showSettable = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  export default {
    name: 'extendExpr',
    data() {
      const validatePass = (rule, value, callback) => {
        var inputval = value.replace(/\s+/g, "");
        if (inputval) {
          if (inputval.length < 5) {
            callback(new Error('原因说明至少5个字'));
          } else {
            callback();
          }
        } else {
          callback(new Error('请填写原因'));
        }
      };
      return {
        postData: {
          page_no: 1,
          page_size: 10
        },
        totalCount: 0,
        tableData: [],
        showSettable: false,

        columns: [
          {
            title: '时间',
            key: 'create_time',
            width: 200,
          },
          {
            title: '延期天数',
            key: 'days'
          },
          {
            title: '会员卡',
            key: 'affect_cards',
            tooltip: true
          },
          {
            title: '延期规则',
            key: 'delayed_rules',
            tooltip: true
          },
          {
            title: '延期原因',
            key: 'reason',
            tooltip: true
          },
          {
            title: '操作账号',
            key: 'username'
          }
        ],
        formData: {
          days: null,
          reason: ''
        },
        formRule: {
          days: { required: true, type: 'number', min: 1, max: 300, message: '请填入0～300之间的整数', trigger: 'blur' },
          reason: { required: true, validator: validatePass, trigger: 'blur' }
        }
      };
    },
    created() {
      this.gettableList();
    },
    methods: {
      submitForm() {
        this.$refs.form.validate(val => {
          if (!val) {
            this.$Message.error('请完成信息填写');
            return;
          }
          this.$service
            .post('/Web/BatchDelayed/add_batch_delayed', this.formData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showSettable = false;
                this.formData.days = null;
                this.formData.reason = '';
                this.gettableList();
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              this.$Message.error(err);
            });
        });
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.gettableList();
      },
      gettableList() {
        const url = '/Web/BatchDelayed/batch_delayed_list';
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list;
            this.totalCount = Number(res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(err => {
          console.error(err);
        });
      }
    }
  };
</script>

<style>
  .ivu-tooltip-inner-with-width {
    max-height: 500px;
    max-width: 600px !important;
    overflow: hidden;
  }
</style>

<style lang="less" scoped>
  .allwidth /deep/ .ivu-input-number {
    width: 100%;
  }
</style>
