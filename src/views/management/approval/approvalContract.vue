<template>
  <div class="table-wrap approval">
    <header>
      <Input v-model="postData.keyword" placeholder="姓名/电话/实体卡号" @on-enter="doSearch" clearable />
      <card-list
        v-model="postData.card_id"
        placeholder="会员卡"
        :busId="busId"
        showTestCard
        clearable
        filterable
        showPackage></card-list>
      <Select
        v-if="approveTypes.length"
        v-model="postData.approve_type"
        placeholder="类型"
        clearable
        filterable>
        <Option
          v-for="item in approveTypes"
          :key="item"
          :value="item"
          :label="item"></Option>
      </Select>
      <Select v-model="postData.approve_status" placeholder="状态" clearable>
        <Option value="1">待审批</Option>
        <Option value="2">未通过</Option>
        <Option value="3">已通过</Option>
      </Select>
      <DatePicker
        v-model="dateRange"
        type="daterange"
        placeholder="选择日期"
        @on-change="dateChange"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table :columns="columns" :data="tableData" disabled-hover></Table>
    <footer>
      <Pager :history="false" :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>

    <Modal v-model="showModal" title="审批" :width="450">
      <div class="card">
        <div class="main-info">
          <div class="line">
            <div class="key">合同编号</div>
            <div class="value">{{ approvalInfo.custom_order_sn }}</div>
          </div>
          <div class="line">
            <div class="key">提交账号</div>
            <div class="value">{{ approvalInfo.process_creator_name }}</div>
          </div>
          <div class="line">
            <div class="key">审批类型</div>
            <div class="value">{{ approvalInfo.approve_type }}</div>
          </div>
        </div>
        <div v-for="(item, index) in approvalContent" :key="index" class="line-wrap">
          <div class="line">
            <div class="key">{{ item.title }}</div>
            <div class="value">
              <div v-if="item.image && item.image.length" class="suspend-img">
                <img
                  v-for="(elem, index) in item.image"
                  :key="index"
                  :src="elem"
                  :alt="title"
                  @click="showBigImg(elem)">
              </div>
              <span v-if="item.text">{{ item.text }}</span>
              <span v-if="item.font !== undefined && item.text" style="font-weight: normal; margin: 0 5rpx;">=></span>
              <span v-if="item.font !== undefined" style="color: #e60012">{{ item.font }}</span>
            </div>
            <div v-if="item.warn" class="warn">
              <div>{{ item.warn }}</div>
            </div>
          </div>
          <div v-if="item.sub_detail && item.sub_detail.length" class="line-table">
            <table>
              <thead>
                <tr>
                  <th>名称</th>
                  <th>归属场馆</th>
                  <th>总共</th>
                  <th>价值</th>
                  <th>到期时间</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(subItem, subIndex) in item.sub_detail" :key="subIndex">
                  <td>{{ subItem.name }}</td>
                  <td>{{ subItem.bus_name }}</td>
                  <td>{{ subItem.overplus }}</td>
                  <td>{{ subItem.amount }}元</td>
                  <td>{{ subItem.end_time }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="record">
          <div class="blue-line"></div>
          <div v-for="(item, index) in logs" :key="index" class="item">
            <div class="avatar">
              <img src="https://imagecdn.rocketbird.cn/default/man_member_default.png"></img>
            </div>
            <div class="text">
              <div class="user">{{ item.operator_name }}</div>
              <div class="time">{{ item.create_time }}{{ item.content }}</div>
              <div class="time reason">
                <div v-if="item.reject_reason">说明：{{ item.reject_reason }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="modal-buttons">
        <template v-if="approvalInfo.approve_status === '1'">
          <Button type="success" @click="handlePass">审批通过</Button>
          <Button type="error" style="margin-left: 60px" @click="showReject = true">不予通过</Button>
        </template>
      </div>
    </Modal>

    <Modal v-model="imgModal" title="附件详情" footer-hide>
      <img class="modal-img" :src="curSrc" alt="">
    </Modal>

    <Modal v-model="showReject" title="不予通过">
      <div class="reject-label">请说明不通过的原因:</div>
      <Input
        v-model.trim="reason"
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 8 }"
        placeholder="请输入..."></Input>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleReject">确定</Button>
        <Button @click="showReject = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import cardList from 'components/card/cardList';
  import { isChinese, SEARCH_HINT, formatDate } from '@/utils';
  import { mapState } from 'vuex'

  export default {
    name: 'ApprovalContract',
    components: { Pager, cardList },
    data() {
      return {
        imgModal: false,
        curSrc: '',
        showModal: false,
        showReject: false,
        reason: '',
        approveTypes: [],
        approvalInfo: {
          order_sn: '',
          process_creator_name: '',
          approve_type: ''
        },
        approvalContent: [],
        logs: [],
        dateRange: [],
        total: 0,
        postData: {
          keyword: '',
          card_id: '',
          s_date: '',
          e_date: '',
          approve_type: '',
          approve_status: '',
          page_no: 1,
          page_size: 10
        },
        columns: [
          {
            title: '提交时间',
            key: 'create_time'
          },
          {
            title: '会员',
            key: 'username'
          },
          {
            title: '类型',
            key: 'approve_type'
          },
          {
            // title: '会员卡',
            title: '描述',
            key: 'card_name',
            ellipsis: true,
            tooltip: true,
          },
          {
            title: '提交人员',
            key: 'creator_name'
          },
          {
            title: '状态',
            key: 'status'
          },
          {
            title: '审批人员',
            key: 'approved_name'
          },
          {
            title: '操作',
            render: (h, { row }) => (
              <Button type="text" onClick={() => this.handleOperate(row.id)}>
                {row.approve_status === '1' ? '审批' : '查看'}
              </Button>
            )
          }
        ],
        tableData: []
      };
    },
    computed: {
      ...mapState(['busId']),
    },
    created() {
      // default the dateRange value equal to 30 days
      const today = new Date();
      const date = new Date();
      date.setDate(date.getDate() - 30);
      this.dateRange = [formatDate(date, 'yyyy-MM-dd'), formatDate(today, 'yyyy-MM-dd')]
      this.postData.s_date = this.dateRange[0];
      this.postData.e_date = this.dateRange[1];

      this.getApproveType();
      this.getList()
    },
    methods: {
      // 显示大图
      showBigImg(src) {
        this.curSrc = src
        this.imgModal = true
      },
      // 后端需添加对应类型
      getApproveType() {
        const url = '/web/approve/get_approve_type';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.approveTypes = data.approve_type;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleReject() {
        const url = '/web/approve/reject';
        const { reason, process_id } = this;
        if (reason.length < 5) {
          return this.$Message.warning('不通过原因不低于5个字');
        }
        this.$service
          .post(url, { reason, process_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.showModal = this.showReject = false;
              this.reason = '';
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handlePass() {
        const url = '/web/approve/accept';
        this.$service
          .post(url, { process_id: this.process_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.showModal = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleOperate(process_id) {
        this.process_id = process_id;
        this.getDetail(process_id);
        this.showModal = true;
      },

      getDetail(process_id) {
        const url = '/web/approve/approve_detail';
        this.$service
          .post(url, { process_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.approvalInfo = data.approve_info;
              this.approvalContent = data.approve_info.approve_content.filter(item => item && item);
              this.logs = data.logs;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      dateChange([s, e]) {
        this.postData.s_date = s;
        this.postData.e_date = e;
      },
      pageChange(postData) {
        // const { s_date, e_date } = postData;
        // this.dateRange = [s_date, e_date];
        this.postData = postData;
        this.getList();
      },
      getList() {

        // if numbers or letters must be more than 3 in length you can request
        if (this.postData.keyword && !isChinese(this.postData.keyword)) {
          this.$Message.warning(SEARCH_HINT);
          return;
        }

        // // date range must be less than 30 days
        // if (this.dateRange && !checkRangeLessThan30(this.dateRange)) {
        //   this.$Message.warning(SEARCH_DATE_HINT);
        //   return;
        // }

        const url = '/web/approve/pc_approve_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              const status = ['待审批', '未通过', '已通过'];
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    status: status[item.approve_status - 1]
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  @gray: #79848b;
  @main: #434343;
  @blue: #098dff;
  .modal-img {
    display: block;
    max-width: 480px;
  }
  .suspend-img {
    overflow: hidden;
    > img {
      display: inline-block;
      margin-right: 8px;
      width: 100px;
      cursor: pointer;
    }
  }
  .card {
    padding: 0px 15px;
    font-size: 14px;
    background-color: #fff;
    margin-bottom: 10px;
  }

  .main-info {
    border-bottom: 1px dashed #eee;
    margin-bottom: 15px;
    padding-bottom: 5px;
  }

  .line {
    min-height: 24px;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    line-height: 24px;
    .key {
      color: @gray;
      width: 96px;
    }
    .value {
      font-weight: bold;
      color: @main;
      flex: 1;
    }
    .warn {
      height: 24px;
      display: flex;
      align-items: center;
      div {
        line-height: 18px;
        margin-left: auto;
        font-size: 12px;
        color: #e60012;
        background-color: #fef0f1;
        padding: 0px 6px;
        border-radius: 2px;
      }
    }
  }
  .line-table {
    margin-bottom: 10px;
    font-size: 12px;
     table {
       width: 100%;
        border-collapse: collapse;
        margin: 0 auto;
        text-align: center;
      }
      table td, table th {
        border: 1px solid @gray;
        color: #666;
        height: 30px;
      }
  }

  .record {
    border-top: 1px dashed #eee;
    margin-top: 15px;
    padding: 13px 0px;
    position: relative;
    .blue-line {
      position: absolute;
      height: calc(~'100% - 70px');
      top: 36px;
      left: 21px;
      width: 1.5px;
      background-color: @blue;
      z-index: 1;
    }
    .item {
      display: flex;
      align-items: center;
      height: 64px;
      position: relative;
      z-index: 2;

      .avatar {
        width: 42px;
        height: 42px;
        box-sizing: border-box;
        padding: 4px;
        border-radius: 50%;
        margin-right: 15px;
        background-color: #fff;
        img {
          height: 100%;
          width: 100%;
          border-radius: 50%;
          border: 1px solid @blue;
        }
      }
      .text {
        display: flex;
        flex-direction: column;
        flex: 1;
        .user {
          color: @main;
          font-weight: bold;
          font-size: 13px;
          line-height: 23px;
        }
        .time {
          font-size: 11px;
          line-height: 20px;
          color: @gray;
        }
      }
    }
  }

  .reject-label {
    font-size: 14px;
    padding-bottom: 5px;
    &::before {
      content: '*';
      margin-right: 4px;
      font-family: SimSun;
      font-size: 12px;
      color: #ed4014;
    }
  }
</style>
