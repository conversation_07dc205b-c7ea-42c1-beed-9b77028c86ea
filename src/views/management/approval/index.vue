<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs :value="activeIndex" @on-click="clickTabs">
      <TabPane label="合同审批" name="0">
        <ApprovalContract v-if="activated.includes('0')" class="table-wrap" />
      </TabPane>
      <TabPane label="会员信息审批" name="1">
        <ApprovalMemberInfo v-if="activated.includes('1')" class="table-wrap" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import ApprovalContract from './approvalContract'
import ApprovalMemberInfo from './approvalMemberInfo'
import tabsMixins from 'mixins/tabs'

export default {
  name: 'ApprovalPage',
  components: {
    ApprovalContract,
    ApprovalMemberInfo
  },

  mixins: [tabsMixins],
  data() {
    return {
    }
  },
}
</script>

<style lang="less" scoped>
/* .tab-table-wrap {
  height: 100%;
  .ivu-tabs {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    /deep/.ivu-tabs-content {
      flex: 1;
    }
  }
} */

.table-wrap {
  padding: 20px 28px;
  // min-height: 100%;
}
</style>
