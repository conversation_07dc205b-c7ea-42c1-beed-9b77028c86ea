<template>
  <div class="table-wrap approval">
    <header>
      <Input v-model="postData.keyword" placeholder="姓名/电话" @on-enter="doSearch" clearable />
      <Select
        v-if="approveTypes.length"
        v-model="postData.approve_type"
        placeholder="类型"
        clearable
        filterable>
        <Option
          v-for="item in approveTypes"
          :key="item.value"
          :value="item.value"
          :label="item.label"></Option>
      </Select>
      <Select v-model="postData.status" placeholder="状态" clearable>
        <Option value="1">待审批</Option>
        <Option value="2">已通过</Option>
        <Option value="3">未通过</Option>
      </Select>
      <DatePicker
        v-model="dateRange"
        type="daterange"
        placeholder="选择日期"
        @on-change="dateChange"
        :clearable="true"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table :columns="columns" :data="tableData" disabled-hover></Table>
    <footer>
      <Pager :history="false" :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>

    <Modal v-model="showModal" title="审批" :width="450">
      <div class="card">
        <div class="main-info">
          <div class="line">
            <div class="key">提交账号</div>
            <div class="value">{{ approvalInfo.creator_name }}</div>
          </div>
          <div class="line">
            <div class="key">审批类型</div>
            <div class="value">{{ approvalInfo.approve_type_name }}</div>
          </div>
        </div>
        <!-- 类型为会员信息/头像的渲染 -->
        <template v-if="[1, 2].includes(+approvalInfo.approve_type)">
          <div class="main-info">
            <div class="line">
              <div class="key">会员头像</div>
            </div>
            <div class="avatar-row">
              <div v-if="approvalInfo.content_old" class="avatar-box">
                <img
                  class="member-avatar"
                  :src="approvalInfo.content_old.avatar || defSrc"
                  @click="showBigImg(approvalInfo.content_old.avatar || defSrc)">
                <span v-show="!isEditUserInfo">（旧）</span>
              </div>
              <div v-if="approvalInfo.content_new" v-show="!isEditUserInfo" class="avatar-box">
                <img
                  class="member-avatar"
                  :src="approvalInfo.content_new.avatar"
                  @click="showBigImg(approvalInfo.content_new.avatar)">
                <span style="color: #e60012;">（新）</span>
              </div>
            </div>
          </div>
          <div v-for="(item, index) in approvalContent" :key="index" class="line-wrap">
            <div class="line">
              <div class="key">{{ item.title }}</div>
              <div class="value">
                <!-- <div v-if="item.image && item.image.length" class="suspend-img">
                <img v-for="(elem, index) in item.image" :key="index" :src="elem" :alt="title" @click="showBigImg(elem)">
              </div> -->
                <span v-if="item.text">{{ item.text }}</span>
                <template v-if="isEditUserInfo">
                  <span v-if="item.hasArrows" style="font-weight: normal; margin: 0 5rpx;">=></span>
                  <span v-if="item.font" style="color: #e60012">{{ item.font }}</span>
                </template>
              </div>
              <div v-if="item.warn" class="warn">
                <div>{{ item.warn }}</div>
              </div>
            </div>
          </div>
        </template>
        <!-- 类型为赠积分/折扣券的渲染 -->
        <template v-if="[3, 4].includes(+approvalInfo.approve_type)">
          <div class="line-wrap">
            <div class="line">
              <div class="key">会员姓名</div>
              <div class="value">{{ approvalContent.username }} </div>
            </div>
          </div>
          <div class="line-wrap">
            <div class="line">
              <div class="key">性别</div>
              <div class="value">{{ approvalContent.sex }} </div>
            </div>
          </div>
          <div class="line-wrap">
            <div class="line">
              <div class="key">手机号</div>
              <div class="value">{{ approvalContent.phone }} </div>
            </div>
          </div>
          <div class="line-wrap">
            <div v-if="approvalInfo.approve_type == 4" class="line">
              <div class="key">赠送</div>
              <div class="value">{{ approvalContent.detail.point + '积分' }} </div>
            </div>
            <template v-else>
              <div class="line">
                <div class="key">赠送</div>
                <div class="value">{{ approvalContent.detail.coupon_name }} </div>
              </div>
              <div class="discount-item">
                <div class="discount-card">
                  <div class="discount-wrap">
                    <div class="top">
                      <div class="discount-price">
                        <h4>￥{{ approvalContent.detail.discount_amount }}</h4>
                        <p>满{{ approvalContent.detail.limit_amount }}元可用</p>
                      </div>
                      <div class="discount-info">
                        <h4>{{ approvalContent.detail.coupon_name }}</h4>
                        <p v-if="approvalContent.detail.use_time_type === '2'">发放后{{ approvalContent.detail.use_days }}天有效</p>
                        <p v-else>{{ approvalContent.detail.start_time }}~{{ approvalContent.detail.end_time }}</p>
                        
                        <p>{{ approvalContent.detail.use_limit_text + ' 可用' }}</p>
                      </div>
                    </div>
                    <div class="bottom">
                      <div v-if="approvalContent.detail.limit_card == 1 || approvalContent.detail.limit_card == 2 || approvalContent.detail.limit_card == 3 || approvalContent.detail.limit_card == 4">支持使用的卡种: {{ approvalContent.detail.limit_card_text == '仅限购指定卡种' ? approvalContent.detail.card_species_name && approvalContent.detail.card_species_name.join('、') : approvalContent.detail.limit_card_text }}</div>
                      <div v-if="approvalContent.detail.limit_class == 2 || approvalContent.detail.limit_class == 1">
                      支持使用的团课:{{ approvalContent.detail.limit_class == 2 ? approvalContent.detail.class_type_name : '全部团课' }}
                    </div>
                    <div v-if="approvalContent.detail.limit_space_type == 1 || approvalContent.detail.limit_space_type == 2">
                      支持使用的场地:{{ approvalContent.detail.limit_space_type == 1 ? '全部场地' : approvalContent.detail.space_type_name }}
                    </div>
                    <div v-if="approvalContent.detail.limit_san_rule == 1 || approvalContent.detail.limit_san_rule == 2">
                      支持使用的散场票:{{ approvalContent.detail.limit_san_rule == 1 ? '全部散场票' : approvalContent.detail.san_rule_name }}
                    </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div v-if="approvalInfo.approve_type == 4" class="line-wrap">
            <div class="line">
              <div class="key">积分有效期</div>
              <div class="value">{{ approvalContent.detail.valid_day + '天' }} </div>
            </div>
          </div>
          <div class="line-wrap">
            <div class="line">
              <div class="key">原因</div>
              <div class="value">{{ approvalContent.give_reason }} </div>
            </div>
          </div>
        </template>

        <div class="record">
          <div class="blue-line"></div>
          <div v-for="(item, index) in logs" :key="index" class="item">
            <div class="avatar">
              <img src="https://imagecdn.rocketbird.cn/default/man_member_default.png" />
            </div>
            <div class="text">
              <div class="user">{{ item.operator_name }}</div>
              <div class="time">{{ item.create_time }}{{ item.content }}</div>
              <div class="time reason">
                <div v-if="item.reject_reason">说明：{{ item.reject_reason }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" :class="approvalInfo.status == '1' ? 'modal-buttons' : undefined">
        <template v-if="approvalInfo.status == '1'">
          <Button type="success" @click="handlePass">审批通过</Button>
          <Button type="error" style="margin-left: 60px" @click="showReject = true">不予通过</Button>
        </template>
      </div>
    </Modal>

    <Modal v-model="imgModal" title="附件详情" footer-hide>
      <img class="modal-img" :src="curSrc" alt="">
    </Modal>

    <Modal v-model="showReject" title="不予通过">
      <div class="reject-label">请说明不通过的原因:</div>
      <Input
        v-model.trim="reason"
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 8 }"
        placeholder="请输入..."></Input>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleReject">确定</Button>
        <Button @click="showReject = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  // import cardList from 'components/card/cardList';
  import { formatDate } from 'utils';
  import { DISCOUNT_CARD_LIMIT, DISCOUNT_SCOPE_LIMIT } from 'store/constants';
  // import { isChinese, SEARCH_HINT, checkRangeLessThan30, SEARCH_DATE_HINT, formatDate } from '@/utils';

  const LABEL_CONTENT = {
    username: '会员名称',
    sex: '性别',
    phone_type: '预留',
    phone: '手机号',
    id_code: '证件号',
    birthday: '出生日期',
    source: '获客来源',
    user_tags: '标签',
    emerg_username: '紧急联系人',
    emerg_phone: '紧急联系电话',
    remark: '备注',
  }
  const SEX_TYPE = {
    1: '男',
    2: '女',
  }
  const PHONE_TYPE = {
    1: '本人电话',
    2: '亲友电话',
  }

  const STATUS = {
    1: '待审批',
    2: '已通过',
    3: '未通过'
  }

  const APPROVE_TYPE_NAMES = {
    1: '会员信息',
    2: '会员头像',
    3: '赠折扣券',
    4: '赠积分',
  }

  export default {
    name: 'ApprovalContract',
    components: {
      Pager,
      // cardList
    },

    data() {
      return {
        imgModal: false,
        curSrc: '',
        defSrc: 'https://imagecdn.rocketbird.cn/default/man_member_default.png',
        showModal: false,
        showReject: false,
        reason: '',
        approveTypes: [
          { label: '会员信息', value: '1' },
          { label: '会员头像', value: '2' },
          { label: '赠折扣券', value: '3' },
          { label: '赠积分', value: '4' },
        ],
        approvalInfo: {},
        approvalContent: [],
        logs: [],
        dateRange: [],
        total: 0,
        postData: {
          keyword: '', // 姓名/手机号
          s_date: '',  // 开始日期
          e_date: '',  // 结束日期
          approve_type: '',   // 1会员信息 2会员头像 3赠折扣券 4赠积分
          status: '', // 1待审批 2已通过 3不通过
          page_no: 1,
          page_size: 10
        },
        columns: [
          { title: '提交时间', key: 'create_time' },
          { title: '会员', key: 'username' },
          {
            title: '类型',
            key: 'approve_type_text',
          },
          { title: '提交人员', key: 'creator_name' },
          { title: '状态', key: 'statusText' },
          { title: '审批人员', key: 'admin_username' },
          {
            title: '操作',
            render: (h, { row }) => (
              <Button type="text" onClick={ () => this.handleOperate(row.id) }>
                { row.status == '1' ? '审批' : '查看' }
              </Button>
            )
          }
        ],
        tableData: []
        // disabledDateOptions: {
        //   disabledDate (date) {
        //     // disable after today
        //     return date && date.valueOf() > Date.now();
        //   }
        // }
      };
    },
    computed: {
      // 是否修改会员信息
      isEditUserInfo() {
        return this.approvalInfo.approve_type == 1
      }
    },

    created() {
      // default dateRange value equal to 30 days
      const today = new Date();
      const date = new Date();
      date.setDate(date.getDate() - 30);
      this.dateRange = [formatDate(date, 'yyyy-MM-dd'), formatDate(today, 'yyyy-MM-dd')];

      this.postData.s_date = this.dateRange[0];
      this.postData.e_date = this.dateRange[1];

      this.getList();
    },

    methods: {
      // 显示大图
      showBigImg(src) {
        this.curSrc = src
        this.imgModal = true
      },
      // 不予通过提交
      handleReject() {
        const url = '/Web/Approve/approve_userinfo';
        const { reason, process_id: id } = this;
        if (reason.length < 5) {
          return this.$Message.warning('不通过原因不低于5个字');
        }
        this.$service
          .post(url, { id, reason, type: 2 })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.showModal = this.showReject = false;
              this.reason = '';
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 审批通过
      handlePass() {
        const url = '/Web/Approve/approve_userinfo';
        this.$service
          .post(url, { id: this.process_id, type: 1 })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.showModal = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 打开审批or查看弹窗
      handleOperate(process_id) {
        this.process_id = process_id;
        this.getDetail(process_id);
        this.showModal = true;
      },
      getDetail(id) {
        const url = 'Web/Approve/approve_userinfo_detail';
        this.$service.post(url, { id }).then(res => {
          if (res.data.errorcode === 0) {
            /*
              因为是从原合同审批复制的组件，并且新接口返回数据结构不同
              为保持DOM结构样式不用大动，故调整为和合同审批一致的数据结构
            */
            const { data } = res.data;
            // 1会员信息 2会员头像 3赠折扣券 4赠积分
            data.approve_type_name = APPROVE_TYPE_NAMES[data.approve_type];
            this.approvalInfo = data;

            /* 处理 会员信息/头像 使用的数据结构 */
            if ([1, 2].includes(+data.approve_type)) {
              const {
                content_old,
                content_new,
              } = data;
              const approvalContent = [];
              [content_old, content_new].forEach(v => {
                // 性别、预留电话类型 转换为文本； 生日转换格式
                const sex = v.sex;
                const phone_type = v.phone_type;
                // const birthday = v.birthday;
                [1, 2].includes(+sex) && (v.sex = SEX_TYPE[sex]);
                [1, 2].includes(+phone_type) && (v.phone_type = PHONE_TYPE[phone_type]);
                // v.birthday = +birthday ? formatDate(new Date(+birthday), "yyyy-MM-dd") : '';
              })
              for (const [key, value] of Object.entries(LABEL_CONTENT)) {
                approvalContent.push({
                  title: value,
                  text: content_old[key],
                  hasArrows: content_new[key] !== undefined,
                  font: content_new[key] != content_old[key] && content_new[key]
                })
              }
              this.approvalContent = approvalContent;
            }

            /* 处理 积分/折扣券 使用的数据结构 */
            if ([3, 4].includes(+data.approve_type)) {
              const { username, sex, phone } = data.content_old;
              const approvalContent = {
                username, sex, phone,
                // detail: {},
                // give_reason: '',
              }
              if(data.approve_type == 3) {
                // data.coupon.start_time = formatDate(data.coupon.start_time, 'yyyy-MM-dd')
                // data.coupon.end_time = formatDate(data.coupon.end_time, 'yyyy-MM-dd')
                data.coupon.limit_card_text = DISCOUNT_CARD_LIMIT[data.coupon.limit_card - 1]; // 卡种限制
                data.coupon.use_limit_text = (data.coupon.use_limit ? data.coupon.use_limit.split(',') : ['1']).map(v =>  DISCOUNT_SCOPE_LIMIT[v - 1]).join(); // 使用限制 1 购卡 2续卡 3升卡
                approvalContent.detail = data.coupon;
                approvalContent.give_reason = data.content_new.reason;
              } else {
                approvalContent.detail = data.point;
                approvalContent.give_reason = data.point.give_reason;
              }
              this.approvalContent = approvalContent;
            }

            /* 处理审批节点 数据结构 */
             const {
              status,
              creator_name,
              create_time,
              approve_admin,
              finish_time,
              reject_reason
            } = data;
            const logs = [{
              operator_name: creator_name,
              create_time,
              content: '发起审批',
            }]
            if (status != 1 /* && approve_admin && finish_time != 0 */) {
              logs.unshift({
                operator_name: approve_admin,
                create_time: finish_time,
                content: status == 2 ? '审批通过' : status == 3 ? '审批不通过' : status,
                reject_reason,
              })
            }
            this.logs = logs;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(err => {
          console.error(err);
        });
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      dateChange([s, e]) {
        this.postData.s_date = s;
        this.postData.e_date = e;
      },
      pageChange(postData) {
        // const { s_date, e_date } = postData;
        // this.dateRange = [s_date, e_date];
        this.postData = postData;
        this.getList();
      },
      getList() {
        // if numbers or letters must be more than 4 in length you can request
        // if (this.postData.keyword && !isChinese(this.postData.keyword) && this.postData.keyword.length < 4) {
        //   this.$Message.warning(SEARCH_HINT);
        //   return;
        // }

        // date range must be less than 30 days
        // if (this.dateRange && !checkRangeLessThan30(this.dateRange)) {
        //   this.$Message.warning(SEARCH_DATE_HINT);
        //   return;
        // }
        
        const url = '/web/Approve/approve_userinfo_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              data.list.forEach(item => {
                item.statusText = STATUS[item.status];
                item.approve_type_text = APPROVE_TYPE_NAMES[item.approve_type];
              })
              this.tableData = data.list
              this.total = +data.count;
            } else {
              this.total = 0;
              this.tableData = [];
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  @gray: #79848b;
  @main: #434343;
  @blue: #098dff;
  .modal-img {
    display: block;
    max-width: 480px;
  }
  .suspend-img {
    overflow: hidden;
    > img {
      display: inline-block;
      margin-right: 8px;
      width: 100px;
      cursor: pointer;
    }
  }
  .card {
    padding: 0px 15px;
    font-size: 14px;
    background-color: #fff;
    margin-bottom: 10px;
  }

  .main-info {
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px dashed #eee;

    .avatar-row {
      display: flex;
      margin-bottom: 10px;
      .avatar-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        &:nth-child(2) {
          margin-left: 100px;
        }
      }

      .member-avatar {
        margin-bottom: 8px;
        width: 110px;
        height: 110px;
        cursor: pointer;
        border: 1px solid #f3f3f3;
        border-radius: 4px;
        object-fit: contain;
      }
    }
  }

  .line {
    min-height: 24px;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    line-height: 24px;
    .key {
      color: @gray;
      width: 96px;
    }
    .value {
      font-weight: bold;
      color: @main;
      flex: 1;
    }
    .warn {
      height: 24px;
      display: flex;
      align-items: center;
      div {
        line-height: 18px;
        margin-left: auto;
        font-size: 12px;
        color: #e60012;
        background-color: #fef0f1;
        padding: 0px 6px;
        border-radius: 2px;
      }
    }
  }
  .line-table {
    margin-bottom: 10px;
    font-size: 12px;
     table {
       width: 100%;
        border-collapse: collapse;
        margin: 0 auto;
        text-align: center;
      }
      table td, table th {
        border: 1px solid @gray;
        color: #666;
        height: 30px;
      }
  }

  .record {
    border-top: 1px dashed #eee;
    margin-top: 15px;
    padding: 13px 0px;
    position: relative;
    .blue-line {
      position: absolute;
      height: calc(~'100% - 70px');
      top: 36px;
      left: 21px;
      width: 1.5px;
      background-color: @blue;
      z-index: 1;
    }
    .item {
      display: flex;
      align-items: center;
      height: 64px;
      position: relative;
      z-index: 2;

      .avatar {
        width: 42px;
        height: 42px;
        box-sizing: border-box;
        padding: 4px;
        border-radius: 50%;
        margin-right: 15px;
        background-color: #fff;
        img {
          height: 100%;
          width: 100%;
          border-radius: 50%;
          border: 1px solid @blue;
        }
      }
      .text {
        display: flex;
        flex-direction: column;
        flex: 1;
        .user {
          color: @main;
          font-weight: bold;
          font-size: 13px;
          line-height: 23px;
        }
        .time {
          font-size: 11px;
          line-height: 20px;
          color: @gray;
        }
      }
    }
  }

  .reject-label {
    font-size: 14px;
    padding-bottom: 5px;
    &::before {
      content: '*';
      margin-right: 4px;
      font-family: SimSun;
      font-size: 12px;
      color: #ed4014;
    }
  }

  .discount-item {
    width: 100%;
    .discount-card {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      margin-right: 0;
      padding: 20px;
      flex: 1;
      border-radius: 6px;
      border: 1px solid #dee0e2;
      border-top: 5px solid #ca2e53;
      box-shadow: 0 3px 5px rgba(74, 104, 164, 0.15);
      user-select: none;

      .discount-wrap {
        color: #898989;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        flex: 1;
        .top {
          display: flex;
          align-items: center;
        }
        .bottom {
          padding-top: 10px;
          margin-top: 10px;
          line-height: 1.5;
          border-top: 1px dashed #eee;
        }
      }
    }

    .discount-price {
      margin-right: 18px;
      padding-right: 18px;
      border-right: 1px dashed #eee;
      h4 {
        font-size: 24px;
        color: #ca2e53;
      }
    }

    .discount-info {
      h4 {
        font-size: 16px;
        color: #313131;
      }
    }
  }
</style>
