<template>
  <div class="container">
    <header><h3>设置批量延时</h3></header>
    <Form class="form" :label-width="140">
      <FormItem label="会员卡">
        <Button
          style="justify-content: flex-start; overflow: hidden; text-overflow: ellipsis"
          long
          @click="showSelectModal">
          {{ cardNames }}
        </Button>
      </FormItem>
      <FormItem label="延期规则">
        <i-switch v-model="rule1">
          <span slot="open">是</span>
          <span slot="close">否</span>
        </i-switch>
        <span style="margin: 0 10px;">延长有效会员卡</span>
        <Checkbox v-model="rule1_1">含请假中会员卡</Checkbox>
      </FormItem>
      <FormItem>
        <i-switch
          v-model="rule2"
          style="margin-right: 10px;"
          @on-change="onRule3DateChange"
        >
          <span slot="open">是</span>
          <span slot="close">否</span>
        </i-switch>
        延长
        <DatePicker
          v-model="rule2Date"
          type="daterange"
          class="w250"
          :options="rule2DateOptions"
          :editable="false"
        />
        {{ switchLabel[0] }}
      </FormItem>
      <FormItem>
        <i-switch
          v-model="rule3"
          style="margin-right: 10px;"
          @on-change="onRule3DateChange"
        >
          <span slot="open">是</span>
          <span slot="close">否</span>
        </i-switch>
        <DatePicker
          v-model="rule3Date"
          class="w150"
          :options="rule3DateOptions"
          clearable
          :editable="false"
          @on-change="onRule3DateChange" />
        {{ switchLabel[1] }}
      </FormItem>
      <FormItem required label="延长天数">
        <InputNumber v-model="postData.days" :min="1" :max="300"></InputNumber>
      </FormItem>
      <FormItem required label="执行原因">
        <Input v-model="postData.reason" type="textarea" />
      </FormItem>
      <FormItem style="padding-top: 20px;">
        <Button type="success" @click="submit">确定</Button>
        <Button style="margin-left: 30px;" @click="$router.back()">取消</Button>
      </FormItem>
    </Form>
    <Modal
      v-model="showCourseModal"
      title="请选择会员卡"
      width="800px"
      :mask-closable="false">
      <div class="course-select">
        <ul class="sort">
          <li
            v-for="item in sortData"
            :key="item.type"
            :class="{ 'active': item.type === sortType }"
            @click="handleSorted(item.type)">
            {{ item.name }}
          </li>
        </ul>
        <div class="course">
          <div
            v-if="sortedCard.length"
            class="course-tag"
            style="border-style: solid; margin-right: 20px;"
            :class="{'active': checkAllCard}"
            @click="handleCheckAll">
            全选
          </div>
          <div
            v-for="card in sortedCard"
            :key="card.card_id + checkAllCard"
            class="course-tag"
            :class="{'active': card._checked}"
            @click="checkCard(card)">
            {{ card.card_name }}
          </div>
        </div>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleSelectCard">确定</Button>
        <Button @click="handleSelectCancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import { formatDate } from 'src/utils';

  export default {
    name: 'SetDelayRule',
    data() {
      return {
        rule2Date: [null, null],
        rule3Date: null,
        radioLabel: ['仅延长正常的会员卡', '延长正常和请假中的会员卡'],
        switchLabel: ['之内过期的会员卡', '之后开卡的会员卡不延期'],
        rule2DateOptions: {
          disabledDate: (date) => {
            // 规则3开启，并且有日期才限制选择，否则规则2的结束日期 不能大于 规则3的日期
            return (this.rule3 && this.rule3Date) && (this.rule3Date - date < 0)
          }
        },
        rule3DateOptions: {
          disabledDate(date) {
            return Date.now() - date < 0;
          }
        },
        rule1: false, // 是否延长 有效会员卡
        rule1_1: false, // 是否延长 请假中的有效会员卡
        rule2: false, // 是否延长 时间范围内过期的会员卡
        rule3: false, // 是否 选择时间之后开卡的会员卡不延期
        has: false,
        showCourseModal: false,
        postData: {
          delayed_rules: '',
          affect_cards: '',
          reason: '',
          days: 1
        },
        selectedCard: [],
        cardList: [],
        checkAllCard: false,
        sortType: '1',
        sortedCard: [],
        sortData: [
          {
            name: '期限卡',
            type: '1',
          },
          {
            name: '次卡',
            type: '2',
          },
          {
            name: '储值卡',
            type: '3',
          },
          {
            name: '私教课',
            type: '4',
          },
          {
            name: '泳教课',
            type: '5',
          },
        ]
      };
    },
    computed: {
      ...mapState(['addCardList']),
      cardNames() {
        return this.selectedCard.reduce((names, item) => names + `${item.card_name}、`, '') || '选择需要延时的会员卡（不选默认延长所有卡）';
      }
    },
    created() {
      if (!this.addCardList) {
        this.getAddCardList().then(res => {
          if (res.data.errorcode === 0) {
            this.cardList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      } else {
        this.cardList = this.addCardList.map(item => ({ ...item, _checked: false }));
      }
    },
    methods: {
      ...mapActions(['getAddCardList']),
      onRule3DateChange() {
        const { rule2, rule3, rule2Date, rule3Date } = this
        const date = new Date(rule3Date)
        if (rule2 && rule3 && rule2Date[0] && rule2Date[0] - date > 0) {
          this.rule2Date = []
          return this.$Message.warning({
            content: '规则2的结束日期 不能大于 规则3的日期。已重置',
            duration: 3
          });
        }
        if (rule2 && rule3 && rule2Date[1] && rule2Date[1] - date > 0) {
          rule2Date.splice(1, 1, date)
          this.$Message.warning({
            content: '规则2的结束日期 不能大于 规则3的日期。已更新',
            duration: 3
          });
        }
      },
      submit() {
        if (!(this.rule1 || this.rule2)) return this.$Message.error('请至少启用规则1或规则2');
        if (this.rule2 && !(this.rule2Date[0] && this.rule2Date[1])) return this.$Message.error('请选择规则2的日期范围');
        if (this.rule3 && !this.rule3Date) return this.$Message.error('请选择规则3的日期');
        if (this.rule2 && this.rule3 && this.rule2Date[1] - new Date(this.rule3Date) > 0) {
          return this.$Message.error({
            content: '规则2的结束日期 不能大于 规则3的日期',
            duration: 2.5
          });
        }
        if (!this.postData.days) return this.$Message.error('请输入延长天数');
        if (!this.postData.reason) return this.$Message.error('请输入延长执行原因');

        this.$Modal.confirm({
            content: '延时操作不可撤销，是否延时？',
            onOk: () => {
              const url = '/Web/BatchDelayed/add_batch_delayed';
              const postData = this.dealData();
              this.$service.post(url, postData).then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg);
                  this.$router.back();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              }).catch(e => {
                throw new Error(e)
              })
            },
            onCancel: () => {}
        });
      },
      dealData() {
        const postData = {
          ...this.postData
        };
        const affect_cards = this.selectedCard.length && this.selectedCard.map(item => ({ id: item.card_id, name: item.card_name })) || undefined;
        const delayed_rules = [];
        if (this.rule1) {
          if (!this.rule1_1) {
            delayed_rules.push({
            type: 1,
            name: '仅延长正常的会员卡',
          });
          } else  {
            delayed_rules.push({
            type: 2,
            name: '延长正常和请假中的会员卡',
          });
          }
        }
        if (this.rule2) {
          const dateRange = [formatDate(this.rule2Date[0], 'yyyy-MM-dd'), formatDate(this.rule2Date[1], 'yyyy-MM-dd')]
          delayed_rules.push({
            type: 3,
            name: `延长${dateRange[0]} - ${dateRange[1]}${this.switchLabel[0]}`,
            start_date: dateRange[0],
            end_date: dateRange[1]
          });
        }
        if (this.rule3) {
          const dateStr = formatDate(this.rule3Date, 'yyyy-MM-dd')
          delayed_rules.push({
            type: 4,
            name: `${dateStr}${this.switchLabel[1]}`,
            date: dateStr
          });
        }

        postData.delayed_rules = JSON.stringify(delayed_rules);
        postData.affect_cards = JSON.stringify(affect_cards);
        return postData;
      },
      checkCard(card) {
        card._checked = !card._checked;
        this.$forceUpdate();
      },
      showSelectModal() {
        this.selectedCard.forEach(item => item._checked = true);
        this.handleSorted(this.sortData[0].type);
        this.showCourseModal = true;
      },
      handleSorted(type) {
        this.sortType = type;
        this.sortedCard = this.cardList.filter(item => {
          return item.card_type_id === type;
        });
        this.checkAllCard = !this.sortedCard.some(item => !item._checked);
      },
      handleCheckAll() {
        this.checkAllCard = !this.checkAllCard;
        if (this.checkAllCard) {
          this.sortedCard.forEach(item => {
            item._checked = true;
          });
        } else {
          this.sortedCard.forEach(item => {
            item._checked = false;
          });
        }
      },
      handleSelectCard() {
        this.selectedCard = this.cardList.filter(item => item._checked);
        this.showCourseModal = false;
      },
      handleSelectCancel() {
        this.showCourseModal = false;
        this.cardList.forEach(item => {
          item._checked = this.sortedCard.includes(item.card_id);
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .course-select {
    display: flex;
    max-height: 60vh;
    min-height: 50vh;

    .sort {
      width: 180px;
      overflow-y: scroll;
      background-color: #f1f3f5;

      li {
        text-align: center;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        cursor: pointer;

        &.active {
          background-color: #fff;
          border-left: 4px solid #19be6b;
          color: #19be6b;
          font-weight: bold;
        }
      }
    }

    .course {
      flex: 1;
      overflow-y: scroll;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;

      .course-tag {
        padding: 6px 25px;
        height: auto;
        font-size: 14px;
        margin: 10px 5px;
        border: 1px dashed #19be6b;
        background-color: #fff;
        cursor: pointer;
        user-select: none;

        &.active {
          background-color: #19be6b;
          color: #fff;
        }
      }
    }
  }
</style>
