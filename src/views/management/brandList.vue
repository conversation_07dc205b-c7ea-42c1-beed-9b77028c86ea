<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" clearable placeholder="手环号/芯片号" @on-enter="handleSearch" />
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <main>
      <Table :columns="columns"
             :data="tableData"
             stripe
             disabled-hover></Table>
    </main>
    <footer>
      <div>
        <Button type="success"
                style="margin-right: 30px"
                @click="addShow">新增手环</Button>
      </div>
      <Page :total="totalCount"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            @on-change="gettableList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>

    <Modal v-model="showEdit" :title="modalTitle" :closable="false" :mask-closable="false">
      <Form ref="form" :model="editInfo" :label-width="120" class="modal-form" >
          <FormItem label="手环号" prop="enter_card_number" :rules="{ required: true, message: '请填写手环号', trigger: 'blur'}">
              <Input v-model="editInfo.enter_card_number" type="text"></Input>
          </FormItem>
          <FormItem label="芯片内部卡号" prop="scan_card_number" :rules="{ required: true, message: '请填写芯片内部卡号/卡号为非负数字', pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/, trigger: 'blur'}">
              <Input v-model="editInfo.scan_card_number" type="text"></Input>
          </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="editConfirm">确定</Button>
        <Button @click="showEdit = false">取消</Button>
      </div>
    </Modal>

  </div>
</template>

<script>
export default {
  name: 'brandList',
  data() {
    return {
      postData: {
        search: '',
        page_no: 1,
        page_size: 10
      },
      totalCount: 0,
      tableData: [],

      columns: [{
        title: '手环号',
        align: 'center',
        key: 'enter_card_number',
      }, {
        title: '芯片内部卡号',
        align: 'center',
        key: 'scan_card_number'
      }, {
        title: '操作',
        align: 'center',
        render: (h, params) => {
            let item = params.row;
            return (
              <div>
                <i-button
                  type="text"
                  style="padding: 0 10px"
                  onClick={() => {
                    this.editShow(item);
                  }}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  style="padding: 0 10px"
                  onClick={() => {
                    this.delBrand(item);
                  }}
                >
                  删除
                </i-button>
              </div>
            );
          }
      }],
      showEdit: false,
      editInfo: {
        id: '',
        enter_card_number: '',
        scan_card_number: ''
      },
      modalTitle: '编辑'
    }
  },
  watch: {
    showEdit(val) {
      if(!val) {
        this.editInfo.enter_card_number = '';
        this.editInfo.scan_card_number = '';
        this.editInfo.id = '';
      }
    }
  },
  activated() {
    this.gettableList()
  },
  methods: {
    editConfirm() {
      this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写');
            return;
          } else {
            let url = '';
            if(this.modalTitle == '编辑') {
              url = '/Web/Sign/update_sign_card_number';
            } else {
              url = '/Web/Sign/add_sign_card_number';
            }
            this.$service.post(url, this.editInfo).then(res => {
              if (res.data.errorcode === 0) {
                this.showEdit = false;
                this.$Message.success('编辑成功')
                this.gettableList();
              } else {
                this.$Message.error(res.data.errormsg)
              }
            }).catch(err => {
              console.error(err)
            })
          }
      })
    },
    addShow() {
      this.showEdit = true;
      this.modalTitle = '添加';
      this.editInfo.enter_card_number = '';
      this.editInfo.scan_card_number = '';
      this.editInfo.id = '';
    },
    editShow(row) {
      this.showEdit = true;
      this.modalTitle = '编辑';
      this.editInfo.id = row.id;
      this.editInfo.enter_card_number = row.enter_card_number;
      this.editInfo.scan_card_number = row.scan_card_number;
    },
    delBrand(row) {
      let delUrl = `/Web/Sign/delete_sign_card_number?id=${row.id}`;
      let config = {
        title: '删除手环号',
        content: `确认删除手环号${row.enter_card_number}吗？`,
        onOk: () => {
          this.$service.get(delUrl).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success('删除成功')
              this.gettableList();
            } else {
              this.$Message.error(res.data.errormsg)
            }
          }).catch(err => {
            console.error(err)
          })
        }
      }
      this.$Modal.confirm(config);
    },
    handleSearch() {
      this.postData.page_no = 1;
      this.gettableList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.postData.page_no = 1;
      this.gettableList();
    },
    gettableList() {
      let url = `/Web/Sign/sign_card_number_list?search=${this.postData.search}&page_size=${this.postData.page_size}&page_no=${this.postData.page_no}`;
      this.$service.get(url).then(res => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list;
          this.totalCount = Number(res.data.data.count);
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-date-picker {
  width: 240px;
}

header {
  .user-search {
    width: 160px;
  }
}
</style>
