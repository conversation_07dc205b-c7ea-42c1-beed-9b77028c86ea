
<template>
  <div class="container account-add">
    <header>
      <h3>{{id ? '账号编辑' : '账号添加'}}</h3>
    </header>
    <Form label-position="right"
          ref="adminForm"
          :model="formItem"
          class="form"
          :label-width="140">
      <FormItem label="真实姓名" v-if="!changeSelfPassword" prop="realname" :rules="{required: true, message: '请填写真实姓名'}">
        <Input v-model="formItem.realname" />
      </FormItem>
      <FormItem v-if="!changeSelfPassword" label="手机号" prop="phone" :rules="{required: true, pattern: /^1\d{10}$/, message: '手机号码错误', trigger: 'change'}">
        <Input v-model="formItem.phone" />
      </FormItem>
      <FormItem label="登录账号" v-if="!id" prop="username" :rules="{required: true, validator: checkUsername, trigger: 'blur'}">
        <Input v-model="formItem.username" />
      </FormItem>
      <FormItem label="登录账号" prop="username" v-else>
        <Input v-model="formItem.username" disabled/>
      </FormItem>
      <FormItem label="密码" prop="password" :rules="{required: id && !changeSelfPassword ? false : true, min: 6, max: 16, message: '请正确填写密码（长度6-16）'}">
        <Input v-model="formItem.password" type="password" />
      </FormItem>
      <FormItem label="重复密码" prop="repassword" :rules="{required: id && !changeSelfPassword  ? false : true,validator: validatePass, trigger: 'blur'}">
        <Input v-model="formItem.repassword" type="password" />
      </FormItem>
      <Form-item v-if="!changeSelfPassword" label="管辖范围" prop="admin_type">
        <RadioGroup v-model="formItem.admin_type">
          <Radio v-if="$store.state.adminType == 1" label="1">商家管理(所有场馆)</Radio>
          <Radio label="0">指定管辖范围</Radio>
        </RadioGroup>
      </Form-item>
      <div v-if="!changeSelfPassword" v-show="formItem.admin_type == 0" >
        <Form-item label="区域" prop="region_bus" :required="formItem.admin_type == 0" :rules="{required: formItem.admin_type == 0, message: '请选择区域'}">
          <AdminRegion :shouldDefault="id?0:1" v-model="formItem.region_bus" :id="id||''"/>
        </Form-item>
      </div>
      <FormItem v-if="!changeSelfPassword"  label="角色" prop="role_ids" :rules="{required: true, message: '请选择角色'}">
        <CheckboxGroup v-model="formItem.role_ids">
          <Checkbox v-for="position in roleList" class="checkbox" :key="position.role_id" :label="position.role_id">{{position.name}}</Checkbox>
        </CheckboxGroup>
        <div style="margin-top: 15px;" v-if="$store.state.adminInfo.role_edit">
          <Button type="success" @click="addRole">创建新角色</Button>
        </div>
      </FormItem>
      <FormItem v-if="!changeSelfPassword" label="是否开通BOSS端">
        <RadioGroup v-model="formItem.is_open_boss">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="success" @click="saveEdit">保存</Button>
          <Button v-if="!changeSelfPassword" @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
    <RoleEdit v-model="showRoleAdd" @on-success="getRoles" />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import RoleEdit from './components/roleEdit.vue';
import AdminRegion from 'components/form/adminRegion.vue';
  export default {
    name: 'accountAdd',
    data() {
      return {
        changeSelfPassword: false,
        formItem: {
          username: '',
          realname: '',
          password: '',
          repassword: '',
          phone: '',
          admin_type: '0',
          role_ids: [],
          region_bus: [],
          is_open_boss: '0'
        },
        id: this.$route.query.id || '',
        roleList: [],
        showRoleAdd: false,
        isUserNameUsed: false
      }
    },
    components: {
      RoleEdit,
      AdminRegion
    },
    computed: {
      ...mapState(['adminName', 'adminId'])
    },
    watch: {
      '$route.query.from'(val) {
        this.id = this.$route.query.id
        if (val == 'pass' && this.id && this.id == this.adminId){
          this.changeSelfPassword = true
          this.formItem = {
            username: this.adminName,
            password: '',
            repassword: ''
          }
        } else {
          this.changeSelfPassword = false
        }
      }
    },
    created() {
      if (this.$route.query.from == 'pass' && this.id && this.id == this.adminId){
        this.changeSelfPassword = true
        this.formItem = {
          username: this.adminName,
          password: '',
          repassword: ''
        }
      } else if (this.id) {
        this.getInfo()
        this.getRoles()
      } else {
        this.getRoles()
      }
    },
    methods: {
      checkUsername(rule, value, callback) {
        if (this.id) {
          callback();
        }
        if(!value) {
          callback(new Error('请输入登录账号'));
        }
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/check_admin_username`
        this.$service
          .post(url,{ username: this.formItem.username }, {loading: false})
          .then(res => {
            if (res.data.errorcode === 0) {
              if (res.data.data.status == 40020) {
                this.isUserNameUsed = true;
                callback(new Error('该登录账号已被使用'));
              } else if (res.data.data.status == 0) {
                this.isUserNameUsed = false;
                callback();
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      validatePass(rule, value, callback) {
        if (this.formItem.password && value === '') {
          callback(new Error('请再次输入密码'));
        } else if (this.formItem.password && value !== this.formItem.password) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      },
      addRole() {
        this.showRoleAdd = true
      },
      getInfo() {
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/get_admin_info`
        this.$service
          .post(url, { admin_id: this.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              let data = res.data.data.info
              this.formItem = { ...this.formItem, ...data }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      getRoles() {
        this.$service
          .post(`${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_roles`)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.roleList = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      saveEdit() {
        this.$refs.adminForm.validate(valid => {
          if (!valid ) {
            return false
          }

          let url = 'Admin/add_admin'
          if (this.id) {
            this.formItem.admin_id = this.id
            url = 'Admin/update_admin'
            if (this.changeSelfPassword) {
              url = 'Admin/change_password'
            }
          }
          this.$service
            .post(`${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/${url}`, this.formItem)
            .then(res => {
              if (res.data.errorcode === 0 && !this.changeSelfPassword) {
                this.$router.back()
              } else if (res.data.errorcode === 0) {
                if (this.changeSelfPassword) {
                  this.$router.push({path: '/login', name: '登录'})
                }
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        })
      }
    }
  }
</script>
<style lang="less">
  .account-add {
    .checkbox {
      width: 32%;
    }
  }
</style>
