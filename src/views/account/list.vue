<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs" v-if="$store.state.adminInfo.role_edit" >
      <TabPane :label="'账号('+adminCount+')'" name="0">
       <AdminList @on-change="adminCountChange" />
      </TabPane>
      <TabPane :label="'角色('+roleCount+')'" name="1">
       <RoleList @on-change="roleCountChange"/>
      </TabPane>
    </Tabs>
    <AdminList @on-change="adminCountChange" v-else />
  </div>
</template>

<script>
import RoleList from './components/roleList.vue'
import AdminList from './components/adminList.vue'
  export default {
    name: 'accountList',
    components: {RoleList, AdminList},
    data() {
      return {
        activeIndex: 0,
        adminCount: 0,
        roleCount: 0,
        activated: [0]
      }
    },
    methods: {
      clickTabs(index) {
        this.activeIndex = index
        const active = document.querySelector('.ivu-tabs-ink-bar')
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`
        active.setAttribute('class', className)
        if (!this.activated.includes(index)) {
          this.activated.push(index)
        }
      },
      adminCountChange(total) {
        this.adminCount = total;
      },
      roleCountChange(total) {
        this.roleCount = total
      }
    }
  }
</script>

<style lang="less">

</style>
