<template>
  <div class="table-wrap" style="border: 0">
    <header>
      <Input style="width: 180px" v-model="search" class="option-select" clearable placeholder="角色名" />
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table @on-select="selectMember"
           @on-selection-change="selectMemberChange"
           @on-select-all="selectMemberAll"
           @on-select-cancel="selectMemberCancel"
           :columns="columns" disabled-hover :data="tableData" />
    <footer>
      <div>
        <Button type="success" style="margin-right: 30px" @click="addRole">添加角色</Button>
        <Dropdown @on-click="otherCase" placement="top">
          <Button>其他操作 <Icon type="md-arrow-dropdown"></Icon> </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">批量删除</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
    <RoleBind v-model="showRoleBind" :data="selItem" @on-success="getList"/>
    <RoleEdit v-model="showRoleEdit" :data="selItem" @on-success="getList" />
  </div>
</template>

<script>
import RoleBind from './roleBind.vue';
import RoleEdit from './roleEdit.vue';
import Pager from 'mixins/pager';
export default {
  name: 'roleList',
  mixins: [Pager],
  components: { RoleBind, RoleEdit },
  data() {
    return {
      showRoleBind: false,
      showRoleEdit: false,
      roleIds: '',
      selItem: {},
      selectedMembers: [],
      search: '', // 角色名搜索
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '角色名',
          key: 'name',
          render: (h, params) => {
            return <span><span>{params.row.name}</span>{params.row.is_bus_role == 1 || params.row.is_bus_role == 2?(<sup style="font-size: 12px;color: #5cb85c;margin-left: 6px;" title="商家管理员">管</sup>) :''}</span>
          }
        },
        {
          title: '授权账号',
          key: 'username'
        },
        {
          title: '角色状态',
          key: 'assign_status',
          render: (h, params) => {
            const item = params.row
            return (
              <i-switch
                value={item.assign_status == 1}
                on-on-change={() => {
                  this.handleSwitchChange(
                    { assign_status: item.assign_status, role_id: item.role_id },
                    params.row._index
                  )
                }}
              />
            )
          }
        },
        {
          title: '操作',
          key: 'role_id',
          render: (h, params) => {
            let item = params.row;
            return (
              <div>
                <i-button
                  type="text"
                  style="margin-right: 10px"
                  onClick={() => {
                    this.editRole(item);
                  }}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  style="margin-right: 10px"
                  disabled={item.assign_status == 0}
                  onClick={() => {
                    this.bindRole(item);
                  }}
                >
                  绑定
                </i-button>
                <i-button
                  type="text"
                  class="button-text-red"
                  onClick={() => {
                    this.delRole([item.role_id]);
                  }}
                  disabled={item.is_bus_role === '1'}
                >
                  删除
                </i-button>
              </div>
            );
          }
        }
      ],
      tableData: []
    };
  },
  computed:{
    selectedUserIds() {
      let userIds = [];
      this.selectedMembers.forEach(user => {
        userIds.push(user.role_id);
      });
      return userIds;
    }
  },
  created() {
    this.getList();
  },
  watch: {},
  methods: {
    handleSwitchChange(infoObj, index) {
      const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/update_role_assign_status`
      infoObj.assign_status = this.tableData[index].assign_status == 1 ? 0 : 1
      this.tableData[index].assign_status = this.tableData[index].assign_status == 1 ? 0 : 1
      this.$service
        .post(url, infoObj)
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.tableData[index].assign_status =
              this.tableData[index].assign_status == 1 ? 1 : 0
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(function(error) {
          this.tableData[index].assign_status =
            this.tableData[index].assign_status == 1 ? 1 : 0
          this.$Message.error(error)
        })
    },
    bindRole(item) {
      this.showRoleBind = true
      this.selItem = item
    },
    editRole(item) {
      this.showRoleEdit = true
      this.selItem = item
    },
    addRole() {
      this.showRoleEdit = true
      this.selItem = {}
    },
    selectMember(selection, member) {
      let selUserIds = this.selectedUserIds;
      if (selUserIds.indexOf(member.role_id) === -1) {
        this.selectedMembers.push(member);
      }
    },
    selectMemberCancel(selection, member) {
      this.selectedMembers.forEach((user, index) => {
        if (user.role_id == member.role_id) {
          this.selectedMembers.splice(index, 1);
        }
      });
    },
    selectMemberChange(selection) {
      if (selection.length == 0) {
        this.tableData.forEach(member => {
          this.selectMemberCancel(selection, member);
        });
      }
    },
    selectMemberAll(selection) {
      if (selection.length > 0) {
        selection.forEach(member => {
          this.selectMember(selection, member);
        });
      }
    },
    add() {},
    otherCase(val) {
      if (val === '0') {
        this.delRole();
      }
    },
    clickDetail() {},
    delRole(id) {
      let content = '确定要删除该角色么？'
      if(!id){
        if (this.selectedMembers.length < 1) {
          this.$Message.error('请先勾选需要删除的角色!');
          return;
        }
        let names = [];
        this.selectedMembers.forEach(item => {
          names.push(item.name);
        });
        content = `您确定要删除已选中的角色"${names.join(',')}"吗？`;
        id = this.selectedUserIds;
      }
      this.$Modal.confirm({
        title: '提示',
        content: content,
        onOk: () => {
          const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/del_role`
          this.$service.post(url, { role_id: id }).then(res => {
            if (res.data.errorcode == 0) {
              this.selectedMembers = [];
              this.getList();
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        },
        onCancel() {}
      });
    },
    getList() {
      const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_role_list`
      this.$service
        .post(url, {
          name: this.search,
          page_no: this.page,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.$emit('on-change', data.count);
            this.tableData = data.list;
            this.tableData.forEach((user, index) => {
              if (this.selectedUserIds.indexOf(user.role_id) >= 0) {
                user._checked = true;
              }
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleSearch() {
      this.page = 1
      this.getList();
    }
  }
};
</script>

<style scoped>

</style>
