<template>
  <Modal v-model="showAdd" :mask-closable="false" title="角色绑定">
    <p class="title-content">
      请选择以下哪些账号要与<span class="cw" :title="data.name">{{data.name}}</span><span class="bind">角色绑定</span>
    </p>
    <div style="border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;">
      <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox>
    </div>
    <CheckboxGroup class="check-list" v-model="checkAllGroup" @on-change="checkAllGroupChange">
      <Checkbox v-for="item in accountList" :key="item.id" :label="item.id">{{item.username}}</Checkbox>
    </CheckboxGroup>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="saveBind">保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
  export default {
    name: 'roleBind',
    data() {
      return {
        accountList: [],
        indeterminate: true,
        checkAll: false,
        checkAllGroup: []
      };
    },
    props: {
      data: {
        type: Object,
        required: true
      },
      value: {
        type: Boolean
      }
    },
    computed: {
      showAdd: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      showAdd(val, oldVal) {
        if (val) {
          this.getAccount();
        } else {
          this.checkAllGroup = [];
          this.accountList = [];
          this.indeterminate = true;
          this.checkAll = false;
        }
      }
    },
    created() {
    },
    methods: {
      handleCheckAll() {
        if (this.indeterminate) {
          this.checkAll = false;
        } else {
          this.checkAll = !this.checkAll;
        }
        this.indeterminate = false;

        if (this.checkAll) {
          let arr = [];
          this.accountList.forEach(item => {
            arr.push(item.id);
          });
          this.checkAllGroup = arr;
        } else {
          this.checkAllGroup = [];
        }
      },
      checkAllGroupChange(data) {
        if (data.length === this.accountList.length) {
          this.indeterminate = false;
          this.checkAll = true;
        } else if (data.length > 0) {
          this.indeterminate = true;
          this.checkAll = false;
        } else {
          this.indeterminate = false;
          this.checkAll = false;
        }
      },
      saveBind() {
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/account_bind_role`
        this.$service
          .post(url, { role_id: this.data.role_id, admin_id: this.checkAllGroup })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showAdd = false;
              this.$emit('on-success');
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getAccount() {
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_account`
        this.$service
          .post(url, { role_id: this.data.role_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.accountList = res.data.data;
              res.data.data.forEach(item => {
                if (item.is_bind == 1) {
                  this.checkAllGroup.push(item.id);
                }
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            this.$Message.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .title-content {
    text-align: center;
    font-size: 14px;
    color: #999999;
    margin-bottom: 15px;
  }

  .check-list {
    max-height: 300px;
    overflow-y: scroll;

    .ivu-checkbox-group-item {
      display: block;
      width: 50%;
      float: left;
      margin-bottom: 10px;
      margin-right: 0;
    }
  }
</style>
