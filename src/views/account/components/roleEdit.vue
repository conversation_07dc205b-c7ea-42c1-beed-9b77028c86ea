<template>
<Modal v-model="showAdd" :mask-closable="false" width="1180" :title="+postData.role_id ? '编辑角色' : '添加角色'">
	<Form ref="roleForm"
		:model="postData"
		:label-width="120">
		<Form-item label="角色名" prop="name" :rules="{required: true, type: 'string', message: '请填写角色名'}">
			<Input v-model="postData.name" placeholder="请填写角色名" />
      <Checkbox
        v-model="postData.is_bus_role"
        true-value="2"
        false-value="0"
        :disabled="!adminInfo.is_admin && data && data.is_bus_role == 1">
        角色在系统更新时自动得到新功能赋权（建议管理员拥有）
      </Checkbox>
		</Form-item>
		<Form-item label="财务流水查询范围" prop="check_days" :rules="[{required: true, type: 'string', message: '请填写'}, {type: 'string', pattern: /^\d+$/, message: '必须为正整数或0'}, {type: 'number', transform:(value)=> Number(value), min: 0, max: 360,  message: '0~360,0表示不限制' }]">
			<Input v-model="postData.check_days" placeholder="0~360,0表示不限制" />
		</Form-item>
		<!-- <Form-item label="是否管理员" prop="is_bus_role" :rules="{required: true, type: 'string', message: '请选择'}">
			<RadioGroup v-model="postData.is_bus_role">
				<Radio label="2" :disabled="!adminInfo.is_admin && data && data.is_bus_role == 1">
					管理员
					<Tooltip>
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <p>管理员账户能在系统更新时</p>
							<p>自动得到新功能赋权</p>
            </div>
          </Tooltip>
				</Radio>
				<Radio :disabled="!adminInfo.is_admin && data && data.is_bus_role == 1" label="0">普通员工</Radio>
			</RadioGroup>
		</Form-item> -->
	</Form>
	<div class="edit-table" ref="editTable">
    <template v-for="(table, key) in tableData">
      <table
        v-if="table.show"
        :key="key"
        class="table tableoption"
        style="table-layout:fixed;width: 100%">
          <thead>
            <tr>
              <th colspan="2" style="width:400px">
              <div>
                  <i-switch
                    v-model="table.open"
                    :disabled="!adminInfo.is_admin && data && data.is_bus_role == 1"
                    @on-change="handleToggledOpen(key, $event)" />
                  <p class="title">{{ table.title }}</p>
              </div>
              </th>
              <th style="display:none;"></th>
              <th>
                <div class="collapse-btn" @click="handleToggledCollapse(key)">
                  <p>
                    <span>{{ table.collapse ? '收起' : '展开' }} </span>
                    <icon size="16" type="ios-arrow-down" />
                  </p>
                </div>
              </th>
            </tr>
            <tr v-show="table.collapse">
              <th>一级菜单</th>
              <th>二级菜单</th>
              <th>功能权限</th>
            </tr>
          </thead>
          <tbody class="tbody" v-show="table.collapse">
            <template v-for="item in table.list">
              <tr v-for="(subItem, subIndex) in item.son" :key="subItem.id">
                <td v-if="subIndex==0" :rowspan="item.son.length">
                  <i-switch v-model="item.is_default" @on-change="changeTopSwitch(item)" :true-value="1" :false-value="0" :disabled="!adminInfo.is_admin && data && data.is_bus_role == 1"></i-switch>
                  <p>{{item.name}}</p>
                </td>
                <td>
                  <div v-if="item.son.length>1">
                    <i-switch v-model="subItem.is_default" @on-change="changeSwitch(subItem)" :true-value="1" :false-value="0" :disabled="!item.is_default || (!adminInfo.is_admin && data && data.is_bus_role == 1)"></i-switch>
                    <p style="white-space: nowrap">{{subItem.title}}</p>
                  </div>
                </td>
                <td>
                  <Checkbox v-if="subItem.son && subItem.son.length>1" v-model="subItem.is_checkall" @on-change="changeCheckedAll(subItem)" :disabled="!item.is_default || !subItem.is_default ||  (!adminInfo.is_admin && data && data.is_bus_role == 1)">全选</Checkbox>
                  <Checkbox v-for="(subSubItem, subSubIndex) in subItem.son" :disabled="!item.is_default || !subItem.is_default ||  (!adminInfo.is_admin && data && data.is_bus_role == 1)" :key="subSubItem.id" @on-change="changeChecked(subItem, subSubIndex)" v-model="subSubItem.is_default" :true-value="1" :false-value="0">{{subSubItem.title}}</Checkbox>
                </td>
              </tr>
            </template>
          </tbody>
      </table>
    </template>
	</div>
	<div slot="footer" class="modal-buttons">
		<Button type="success" @click="saveEdit">保存</Button>
		<Button @click="showAdd = false">取消</Button>
	</div>
</Modal>
</template>
<script>
import { mapState } from 'vuex';
export default {
  name: 'roleEdit',
  data() {
    return {
      postData: {
        is_bus_role: '0',// 0 普通员工 1 初始管理员（自动得到新功能赋权） 2 一般管理员
        role_id: '0',
        name: '',
				check_days: '0',
        node_id: [],
      },
      tableData: {
        merchant: {
          title: '商家后台功能权限',
          show: false,
          open: false,
          collapse: false,
          list: [],
        },
        bus: {
          title: '门店功能权限',
          show: false,
          open: false,
          collapse: false,
          list: [],
        }
      }
    }
  },
  props: {
    data: {
      type: Object
    },
    value: {
      type: Boolean,
      required: true
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
		},
		...mapState(['adminInfo'])
  },
  watch: {
    showAdd(val, oldVal) {
      if (val) {
				if (this.data) {
					this.postData.is_bus_role = this.data.is_bus_role == 1 || this.data.is_bus_role == 2 ? '2' : '0'
					this.postData.role_id = this.data.role_id ? this.data.role_id : ''
					this.postData.name = this.data.name ? this.data.name : ''
					this.postData.check_days = typeof this.data.check_days === 'undefined' ? '0':this.data.check_days
				}
				this.getAuthList()
      } else {
				if (this.$refs.editTable) {
					this.$refs.editTable.scrollTop = 0
				}
        Object.assign(this.$data.tableData, this.$options.data.call(this).tableData)
        this.$refs.roleForm.resetFields()
      }
    }
  },
  methods: {
		changeTopSwitch(item) {
			if (item.is_default == 0) {
				item.son && item.son.forEach(arr => {
					arr.is_default = 0
					this.changeSwitch(arr);
				});
			} else if (item.is_default == 1 && item.son && item.son.length===1 ) {
				item.son[0].is_default = 1
			}
		},
		changeSwitch(item) {
			if (item.is_default == 0) {
				item.son && item.son.forEach(arr => {
					arr.is_default = 0
				});
				item.is_checkall = false
			}
		},
		changeChecked(parentItem, childIndex) {
			let findNotDefault = parentItem.son && parentItem.son.find((v) => {
				return v.is_default == 0;
			})
			parentItem.is_checkall = findNotDefault === undefined
		},
		changeCheckedAll(item) {
			item.son && item.son.forEach(arr => {
				arr.is_default = item.is_checkall ? 1 : 0
			});
		},
    // 切换是否开启 商家/门店功能权限
    handleToggledOpen(key, value) {
      this.tableData[key].collapse = value
    },
    // 切换展开/收起
    handleToggledCollapse(key) {
      const item = this.tableData[key]
      item.collapse = !item.collapse
    },
    //保存提交，角色名不为空，返回二级菜单和三级勾选。当二级菜单关闭时，三级勾选不提交。
    saveEdit() {
      let url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/add_role`
			if(this.data && this.data.role_id) {
				url	= `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/update_role`
			}

      // 开启了对应商家/门店权限，才传
      const nodes = []
      const { merchant, bus } = this.tableData
      merchant.open && nodes.push(...merchant.list)
      bus.open && nodes.push(...bus.list)
			this.postData.node_id = this.getNodeIds(nodes);

			this.$refs.roleForm.validate(val => {
        if (!val) return false
				this.$service.post(url, this.postData).then((res)=> {
					if (res.data.errorcode === 0) {
						this.showAdd = false
						this.$emit('on-success')
						this.$Message.success(res.data.errormsg)
					} else {
						this.$Message.error(res.data.errormsg)
					}
				})
			});
		},
		getNodeIds(list) {
			let nodeIds = []
			list.forEach(item => {
				item.son && item.son.forEach(subitem => {
					if (subitem.is_default == 1) {
						nodeIds.push(subitem.id)
						subitem.son && subitem.son.forEach(subSubItem => {
							if (subSubItem.is_default == 1) {
								nodeIds.push(subSubItem.id)
							}
						});
					}
				});
			});
			return nodeIds;
		},
		initAuthList(res) {
			if (res.data.errorcode === 0) {
        const {
          web_node_list = [],
          merchant_node_list = []
        } = res.data.data

        const dataMap = new Map([
          ['bus', web_node_list],
          ['merchant', merchant_node_list],
        ])

        for (const [key, list] of dataMap) {
          for (let i = 0; i < list.length; i++) {
            list[i].is_default = 0
            for (let j = 0; j < list[i].son.length; j++) {
              let findNotDefault = list[i].son[j].son && list[i].son[j].son.find((v) => {
                return v.is_default == 0;
              })
              list[i].son[j].is_checkall = findNotDefault === undefined
              if(list[i].son[j].is_default == 1) {
                list[i].is_default = 1
              }
            }
          }

          const open = !!(this.getNodeIds(list).length)
          this.tableData[key].open = open
          this.tableData[key].collapse = open
          this.tableData[key].show = list.length
          this.tableData[key].list = list
        }

			} else {
				this.$Message.error(res.data.errormsg)
			}
		},
    getAuthList() {
      if (this.data && this.data.role_id) {
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_role_access`
        this.$service
          .post(url, { role_id: this.data.role_id })
          .then(res => {
            this.initAuthList(res)
          })
      } else {
        const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_admin_access`
        this.$service.get(url).then(res => {
          this.initAuthList(res)
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>


.edit-table{
	// overflow: auto;
	width: 100%;
	// height: 420px;
	margin-top: 30px;
	.ivu-switch{
		float: right;
		margin-left: 50px;
		margin-top: 8px;
	}
}
.edit-table thead tr th{
	text-align: center;
	background: #fff;
  .ivu-switch{
		margin-top: 0;
	}
}
.table{
	margin:0;
  margin-bottom: 50px;
	border-spacing: 0;
	border-collapse: collapse;
}
.table>thead>tr>th{
	padding: 8px 15px;
	border: 1px solid #cccccc;
	border-bottom:none;
	border-right: none;
	line-height: 24px;

  .title {
    font-weight: bold;
    font-size: 15px;
  }

  .collapse-btn {
    margin-left: auto;
    width: 55px;
    text-align: center;
    cursor: pointer;
  }
}
.tbody{
	border-top: 1px solid #cccccc;
}
.table>thead>tr{
	border: 1px solid #cccccc;
	line-height: 40px;
	height: 40px;
}
.table tbody tr{
	line-height: 40px;
	height: 40px;
}
.table tbody tr td{
	border: 1px solid #cccccc;
	vertical-align: middle;
	padding: 8px 15px;
	text-align: center;
	color: #333;
	font-size: 14px;
	font-weight: 400;
	&:last-child{
		text-align: left;
	}
	.ivu-checkbox-wrapper {
		margin-right: 20px;
		color: #333;
		font-size: 14px;
		font-weight: 400;
	}
}
.table th:nth-child(1){
	width: 177px;
}
.table th:nth-child(2){
	width: 230px;
}

</style>
