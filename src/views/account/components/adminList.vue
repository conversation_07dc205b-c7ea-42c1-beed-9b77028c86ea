<template>
  <div class="table-wrap" style="border: 0">
    <header>
      <AdminRegion :multiple="false" :shouldDefault="1" v-model="postData.region_bus" />
      <Input style="width: 180px" v-model="postData.search" class="option-select" clearable placeholder="姓名/用户名" />
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>
    <Table @on-select="selectMember"
           @on-selection-change="selectMemberChange"
           @on-select-all="selectMemberAll"
           @on-select-cancel="selectMemberCancel"
           :columns="columns" disabled-hover :data="tableData" />
    <footer>
      <div>
        <Button type="success" style="margin-right: 30px" @click="add">添加账号</Button>
        <Dropdown @on-click="otherCase" placement="top">
          <Button>其他操作 <Icon type="md-arrow-dropdown"></Icon> </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">批量删除</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page :total="+total"
            :current.sync="postData.page_no"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
    <Modal v-model="showModal" width="500" title="管理门店">
      <table class="table" style="border-collapse: collapse" borderColor='#eeeeee' cellSpacing='0' align='center' border='1'>
          <thead>
            <tr>
              <th>序号</th>
              <th>管理门店</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(data, index) in busCountList" :key="data.id">
              <td>{{index + 1}}</td>
              <td>{{data.name}}</td>
            </tr>
          </tbody>
        </table>
        <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import AdminRegion from 'components/form/adminRegion.vue';
export default {
  name: 'adminList',
  computed: {},
  components: {
    AdminRegion
  },
  data() {
    return {
      total: 0,
      showModal: false,
      busCountList: [],
      postData: {
        search: '',
        region_bus: '',
        page_no: 1,
        page_size: 10
      },
      roleIds: '',
      selectedMembers: [],
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '用户名',
          key: 'username'
        },
        {
          title: '姓名',
          key: 'realname'
        },
        {
          title: '管理门店',
          key: 'busCount',
          render: (h, params) => {
            return (
              <i-button type="text"
                  onClick={() => {
                    this.showModal = true;
                    this.busCountList = params.row.busList
                  }}
                >
                  {params.row.busCount}
                </i-button>
            );
          }
        },
        {
          title: '角色',
          key: 'role_name'
        },
        {
          title: '最近登录时间',
          key: 'last_login_time'
        },
        {
          title: '禁用/启用',
          key: 'status',
          render: (h, params) => {
            const item = params.row
            return (
              <i-switch
                value={item.status == 1}
                disabled={params.row.edit == 0}
                title={params.row.edit == 0?'权限不足，不可修改':''}
                on-on-change={() => {
                  this.handleSwitchChange(
                    { status: item.status, admin_id: item.admin_id },
                    params.row._index
                  )
                }}
              />
            )
          }
        },
        {
          title: 'BOSS端',
          key: 'is_open_boss',
          render: (h, params) => {
            let use = params.row.is_open_boss
            return (
              <div
                class={+use === 1 ? 'ico-phone-use' : 'ico-phone-notuse'}
                title={+use === 1 ? '已开通' : '未开通'}
              />
            )
          }
        },
        {
          title: '操作',
          key: 'admin_id',
          render: (h, params) => {
            let item = params.row
            return (
              <div>
                <i-button
                  type="text"
                  style="margin-right: 10px"
                  disabled={params.row.edit == 0}
                  title={params.row.edit == 0?'权限不足，不可修改':''}
                  onClick={() => {
                    this.$router.push(`/admin/add?id=${params.row.admin_id}`)
                  }}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  class="button-text-red"
                  disabled={params.row.edit == 0}
                  title={params.row.edit == 0?'权限不足，不可修改':''}
                  onClick={() => {
                    this.delMember([item.admin_id])
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: []
    }
  },
  computed: {
    selectedUserIds() {
      let userIds = []
      this.selectedMembers.forEach(user => {
        userIds.push(user.admin_id)
      })
      return userIds
    }
  },
  created() {
    // this.getList()
  },
  watch: {
    'postData.region_bus'() {
      this.getList()
    }
  },
  methods: {
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageSizeChanged(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getList()
    },
    handleSwitchChange(infoObj, index) {
      const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/update_admin_status`
      this.tableData[index].status = this.tableData[index].status == 1 ? 0 : 1
      this.$service
        .post(url, infoObj)
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.tableData[index].status =
              this.tableData[index].status == 1 ? 1 : 0
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(function(error) {
          this.tableData[index].status =
            this.tableData[index].status == 1 ? 1 : 0
          this.$Message.error(error)
        })
    },
    selectMember(selection, member) {
      let selUserIds = this.selectedUserIds
      if (selUserIds.indexOf(member.admin_id) === -1) {
        this.selectedMembers.push(member)
      }
    },
    selectMemberCancel(selection, member) {
      this.selectedMembers.forEach((user, index) => {
        if (user.admin_id == member.admin_id) {
          this.selectedMembers.splice(index, 1)
        }
      })
    },
    selectMemberChange(selection) {
      if (selection.length == 0) {
        this.tableData.forEach(member => {
          this.selectMemberCancel(selection, member)
        })
      }
    },
    selectMemberAll(selection) {
      if (selection.length > 0) {
        selection.forEach(member => {
          this.selectMember(selection, member)
        })
      }
    },
    add() {
      this.$router.push('/admin/add')
    },
    otherCase(val) {
      if (val === '0') {
        this.delMember()
      }
    },
    clickDetail() {},
    delMember(id) {
      let content = '确定要删除该账号么？'
      if (!id) {
        if (this.selectedMembers.length < 1) {
          this.$Message.error('请先勾选需要删除的账号!')
          return
        }
        let names = []
        this.selectedMembers.forEach(item => {
          names.push(item.username)
        })
        content = `您确定要删除已选中的账号"${names.join(',')}"吗？`
        id = this.selectedUserIds
      }
      this.$Modal.confirm({
        title: '提示',
        content: content,
        onOk: () => {
          const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/delete_admin`
          this.$service
            .post(url, { admin_ids: id })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.selectedMembers = []
                this.getList()
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        },
        onCancel() {}
      })
    },
    getList() {
      const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/get_admin_list`
      this.$service
        .post(url, this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.total = data.count
            this.$emit('on-change', data.count)
            this.tableData = data.list
            this.tableData.forEach((user, index) => {
              if (this.selectedUserIds.indexOf(user.admin_id) >= 0) {
                user._checked = true
              }
              if (user.edit == 0) {
                user._disabled = true
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>
<style lang="less" scoped>
table {
    width: 100%;
    margin: 0 auto;
    font-size: 14px;
    color: #313131;
    td,
    th {
      height: 30px;
      font-weight: normal;
      text-align: center;
      word-break: break-all;
      .red {
        color: #e60012;
      }
      .green {
        color: #5fb75d;
      }
    }
  }
</style>

