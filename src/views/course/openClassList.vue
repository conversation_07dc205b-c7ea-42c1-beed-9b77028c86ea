<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.class_name"
        placeholder="课程名称"
        style="width: 180px"
        @on-enter="getList(1)" />
      <Select
        v-model="postData.class_type"
        class="w120"
        placeholder="课种"
        clearable>
        <Option value="1">团课</Option>
        <Option value="2">操课</Option>
      </Select>
      <AdminRegion
        v-model="postData.region_bus"
        style="width: 180px;max-width: none;"
        url="/Web/OpenClass/get_openclass_region_bus"
        :multiple="false" />
      <Select
        v-model="postData.is_config"
        class="w120"
        placeholder="适合卡种"
        clearable>
        <Option value="1">已配置</Option>
        <Option value="0">未配置</Option>
      </Select>
      <Button type="success" @click.native="getList(1)">搜索</Button>
    </header>
    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
      stripe
      @on-selection-change="onSelectionChange"
      @on-select="onSelect"
      @on-select-all="onSelectAll"
      @on-select-cancel="onSelectCancel"
    />
    <footer>
      <div>
        <Button type="success" style="margin-right: 30px" @click="addClass">添加课程</Button>
        <Dropdown placement="top" @on-click="otherCase">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">批量删除</DropdownItem>
            <DropdownItem name="1">批量导入适合卡种</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page
        :total="+totalCount"
        :current.sync="postData.p"
        placement="top"
        show-total
        show-sizer
        @on-change="getList"
        @on-page-size-change="pageSizeChanged" />
    </footer>
    <Modal v-model="supportStoreModal" title="支持店面">
      <Table
        stripe
        :columns="supportStoreCols"
        :data="supportStoreData"
        disabled-hover></Table>
      <div slot="footer"></div>
    </Modal>
    <Modal
      v-model="setConfigModal"
      title="批量导入适合卡种配置"
      ok-text="保存"
    >
      <div>
        <Alert style="display: inline-block;">如果所选课程已有配置，则会更新为新的配置</Alert>
        <Form ref="configFormRef" :model="configForm" :label-width="72">
          <FormItem label="选择课程">
            <div style="margin-top:6px;line-height:1.6;">{{ configForm.classNames }}</div>
          </FormItem>
          <FormItem label="复制配置" prop="configId" :rules="{required: true,message: '请选择已配置的课程'}">
            <Select
              v-model="configForm.configId"
              placeholder="选择已配置的课程"
            >
              <Option
                v-for="item in configClassOptions"
                :key="item.id"
                :value="item.id">
                {{ item.class_name }}
              </Option>
            </Select>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
        <Button type="success" @click="handleSaveConfig">保存</Button>
        <Button @click="setConfigModal=false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import AdminRegion from '../../components/form/adminRegion.vue';
  import Selection from 'mixins/selection';

  export default {
    name: 'Openclass',
    components: { AdminRegion },
    mixins: [Selection],
    data() {
      return {
        supportStoreModal: false,
        supportStoreCols: [
          {type: 'index', title: '序号'},
          {title: '支持场馆', key: 'name'}
        ],
        supportStoreData: [],
        postData: {
          p: 1,
          page_size: 10,
          class_name: '',
          class_type: '', // 课种
          is_config: '', // 是否配置适合卡种 '0'未配置 '1'已配置
          region_bus: [],
        },
        tableData: [],
        totalCount: 0,
        showAdd: false,
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '课程名称',
            key: 'class_name'
          },
          {
            title: '课程种类',
            key: 'is_free',
            render: (h, param) => {
              return h('span', param.row.is_free == 0 ? '团课' : '操课');
            }
          },
          {
            title: '是否需要预约',
            key: 'is_free',
            render: (h, param) => {
              return h('span', param.row.is_free == 0 ? '需要预约' : '无需预约');
            }
          },
          {
            title: '单节时长',
            key: 'class_hour',
            render: (h, param) => {
              return h('span', param.row.class_hour + '分钟');
            }
          },
          {
            title: '最少开课人数',
            key: 'min_number'
          },
          {
            title: '最大报名人数',
            key: 'reserve_number'
          },
          {
            title: '支持店面',
            key: 'support_store',
            render: (h, param) => {
              return h('div', [
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                      shape: 'circle',
                      size: 'small'
                    },
                    style: {
                      color: '#52a4ea',
                      minWidth: '0'
                    },
                    on: {
                      click: () => {
                        this.supportStoreData = [];
                        this.$service.post('/Web/OpenClass/openclass_support_bus', {
                          id: param.row.id,
                          bus_id: this.region_bus,
                          region_bus: this.region_bus
                        }).then(res => {
                          if (res.data.errorcode == 0) {
                            this.supportStoreData = res.data.data;
                            this.supportStoreModal = true;
                          } else { this.$Message.error(res.data.errormsg); }
                        })
                      }
                    }
                  },
                  '详情'
                )
              ]);
            }
          },
          {
            title: '适合卡种',
            key: 'is_config',
            render: (h, { row }) => {
              return row.is_free == 0 ? h('div', [
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                    },
                    style: {
                      color: '#52a4ea',
                      minWidth: '0'
                    },
                    on: {
                      click: () => {
                        this.$router.push({
                          name: '适合卡种配置',
                          params: {
                            classId: row.id, // 课程id
                            className: row.class_name, // 排课id
                            isConfig: row.is_config, // 是否配置
                            support_online: row.support_online // 是否支持在线购买
                          }
                        })
                      }
                    }
                  },
                  row.is_config == '1' ?'已配置' : '未配置'
                )
              ]) : <span>-</span>;
            }
          },
          {
            title: '操作',
            render: (h, param) => {
              return h('div', [
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                      shape: 'circle',
                      size: 'small'
                    },
                    style: {
                      color: '#52a4ea',
                      minWidth: '0'
                    },
                    on: {
                      click: () => {
                        if (param.row.is_deal == 1) {
                          this.$router.push(`/course/openClass/editClass/${param.row.id}`);
                        } else {
                          this.$Message.error("您的账号操作权限不足，请联系管理员操作。");
                        }
                      }
                    }
                  },
                  '编辑'
                ),
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                      shape: 'circle',
                      size: 'small'
                    },
                    style: {
                      color: '#d9544f',
                      marginLeft: '10px',
                      minWidth: '0'
                    },
                    on: {
                      click: () => {
                        if (param.row.is_deal == 1) {
                          this.deleteClass(param.row.id)
                        } else {
                          this.$Message.error("您的账号操作权限不足，请联系管理员操作。");
                        }
                      }
                    }
                  },
                  '删除'
                ),
              ]);
            }
          }
        ],
        setConfigModal: false,
        configClassOptions: [], // 已配置的课程列表
        configForm: {
          classNames: '', // 需要配置的课程名称
          selectedIds: '', // 需要配置的课程id
          configId: '' // 复制已配置的课程id
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      addClass() {
        this.$router.push('/course/openClass/addClass');
      },
      otherCase(val) {
        switch (val) {
          case '0':
            this.deleteSeleted()
            break;
          case '1':
            this.showSetConfig()
            break;
        }
      },
      deleteClass(ids) {
        this.$Modal.confirm({
          title: '删除课程',
          content: '确认删除该课程么？',
          onOk: () => {
            this.doDelClass(ids)
          },
          onCancel() {}
        });
      },
      deleteSeleted() {
        this.selection.forEach(v => {
          if (v.is_deal != 1) {
            this.$Message.error('您的账号操作权限不足，请联系管理员操作。');
            return false;
          }
        });
        if (!this.selection.length) return this.$Message.error('请选择删除项');
        this.doDelClass(this.selectionId.toString())
      },
      doDelClass(ids) {
        this.$service
          .post('/Web/OpenClass/hidden_open_class', { ids })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.selection = []
              this.getList();
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      // 打开批量导入配置窗口
      showSetConfig() {
        const list = this.selection.filter(v => v.is_free === '0')
        if (list.length) {
          this.configForm.classNames = list.map(({ class_name }) => class_name).join('、')
          this.configForm.selectedIds = list.map(({ id }) => id)
          this.setConfigModal = true
          this.getConfigClassList()
        } else {
          this.$Message.error('请勾选团课')
        }
      },
      // 保存批量配置
      handleSaveConfig() {
        this.$refs.configFormRef.validate(valid => {
          if (valid) {
            const params = {
              class_ids: this.configForm.selectedIds,
              copy_id: this.configForm.configId
            }
            this.$service.post('/Web/OpenClass/set_open_class_detail_all', params).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.setConfigModal = false
                this.selection = []
                this.$refs.configFormRef.resetFields()
                this.getList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
          }
        })
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.postData.p = 1;
        this.getList();
      },
      getList(isSearch) {
        if (isSearch === 1) {
          this.postData.p = 1;
        }
        const url = '/Web/OpenClass/get_open_class_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const { selectionId } = this
              res.data.data.list.forEach(v => {
                v._checked = selectionId.some(id => id == v.id)
              })
              this.tableData = res.data.data.list;
              this.totalCount = res.data.data.count;
            } else if (res.data.errorcode === 40005) {
              this.tableData = [];
              this.totalCount = 0;
              // this.$Message.error(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 获取已配置卡课列表
      getConfigClassList() {
        this.$service.get('/Web/OpenClass/get_open_class_config_list?class_name=').then(res => {
          if (res.data.errorcode === 0) {
            const { data } = res.data
            if (Array.isArray(data)) {
              this.configClassOptions = data
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
    }
  };
</script>
<style lang="less">
</style>

