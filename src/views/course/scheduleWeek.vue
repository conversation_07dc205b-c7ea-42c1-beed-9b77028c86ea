<template>
  <Card class="db-box">
    <Row>
      <Col span="24" class="box-head">
      <div class="filter-area">
        <Select
          v-model="sel_bus_id"
          style="width:200px;margin-left:22px;"
          filterable
          @on-change="getCourseListForWeek">
          <Option v-for="item in adminBusList" :key="item.id" :value="item.id">{{ item.name }}</Option>
        </Select>
        <DatePicker
          :open="weekPickerFlag"
          type="date"
          style="width:200px;margin-left:22px;"
          placeholder="默认本周"
          @on-change="handleWeekChange">
          <Input
            v-model="weekDateString"
            icon="ios-calendar-outline"
            placeholder="默认本周"
            style="width: 200px"
            readonly
            @on-focus="weekPickerFlag=true"
            @on-blur="weekPickerFlag=false"></Input>
        </DatePicker>
        <Select
          v-if="!hasAidongClass"
          v-model="scheduleThisWeek.courseId"
          style="width:140px;margin-left:22px;"
          placeholder="全部课程"
          clearable
          filterable
          @on-change="handleScheduleFilter">
          <Option v-for="item in scheduleThisWeek.courseList" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
        <Select
          v-if="!hasAidongClass"
          v-model="scheduleThisWeek.coachId"
          style="width:140px;margin-left:22px;"
          placeholder="全部教练"
          clearable
          filterable
          @on-change="handleScheduleFilter">
          <Option v-for="item in scheduleThisWeek.coachList" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </div>
      <div class="controller-area">
        <Button class="controller-btn" type="success" @click="handleToClassPreview ">生成课表</Button>
        <Button
          v-if="!hasAidongClass"
          class="controller-btn"
          :disabled="pickUpFlag"
          type="dashed"
          @click="showClearModal=true">
          清空本周排课
        </Button>
        <Button
          v-if="!hasAidongClass"
          class="controller-btn"
          style="background-color:#52a4ea;"
          type="info"
          @click="handlePickUpToCopy">
          {{ pickUpFlag?'复制到':'选择性复制' }}
        </Button>
        <Button
          v-if="!hasAidongClass"
          :disabled="pickUpFlag"
          class="controller-btn"
          type="success"
          @click="handleCopyOpen">
          复制本周排课
        </Button>
      </div>
      </Col>
    </Row>
    <Row>
      <Col span="24" class="plan-week">
      <div v-for="(item, index) in noOfWeek" :key="index" class="plan-day">
        <Card :class="['plan-head', item.isToday ? 'plan-today' : '']">
          <h3>{{ item.week }}</h3>
          <h5>{{ item.day }}</h5>
        </Card>
        <div class="course-scroll" :style="{height: autoHeight+'px'}">
          <Card
            v-for="(cs, i) in item.list"
            :key="i"
            class="plan-body"
            :class="[{nohover: pickUpFlag}, {'scroll-down': isScrollDown(cs.id)}]"
            :style="{border: (pickUpFlag && !isScrollDown(cs.id))?'1px solid #FF696A':'none'}">
            <!-- <div v-if="pickUpFlag" class="pick-up-me" @click="handlePickUp(cs.id)">
              <Icon size="40" type="md-checkmark" />
              <FaIcon name="check" size="30" :color="pickUpList.findIndex(item=>(item===cs.id))===-1?'gray':'#d9534f'"></FaIcon>
            </div> -->
            <div class="plan-info" style="cursor: pointer;" @click="handlePickUp(cs.id)">
              <p slot="title" class="title">{{ cs.class_name }}</p>
              <p class="time">{{ cs.beg_time }}</p>
              <p class="coche">{{ cs.coach_name }}</p>
              <div class="gym">
                <p class="gym-name">{{ cs.classroom_name }}</p>
                <!-- <Badge v-if="cs.class_category==0" :count="(cs.already_order==0?'O':cs.already_order) + '/' + cs.reserve_number"></Badge> -->
                <div v-if="cs.class_category==0" class="zj-badage">{{ cs.already_order + '/' + cs.reserve_number }}</div>
              </div>
            </div>
            <div v-if="!pickUpFlag" class="plan-contraller" style="height: 100%;">
              <Row style="height: 50%;display: flex;align-items: center;">
                <Col span="8" class="plan-ctrl-btn">
                <Tooltip
                  :delay="1300"
                  content="详情"
                  placement="top"
                  transfer>
                  <div @click="handleHoverDetailClick(cs.id)">
                    <i class="fa fa-2x fa-file-text-o" aria-hidden="true"></i>
                  </div>
                </Tooltip>
                </Col>
                <Col v-if="cs.outer_type !== 1" span="8" class="plan-ctrl-btn">
                <Tooltip
                  :delay="1300"
                  content="修改"
                  placement="top"
                  transfer>
                  <div @click="handleHoverUpdateClick(cs.id)">
                    <i class="fa fa-2x fa-pencil-square-o" aria-hidden="true"></i>
                  </div>
                </Tooltip>
                </Col>
                <Col v-if="cs.outer_type !== 1" span="8" class="plan-ctrl-btn">
                <Tooltip
                  :delay="1300"
                  content="删除"
                  placement="top"
                  transfer>
                  <div @click="handleHoverDeleteClick(cs.id, cs.class_name)">
                    <i class="fa fa-2x fa-trash-o" aria-hidden="true"></i>
                  </div>
                </Tooltip>
                </Col>
              </Row>
              <Row v-if="cs.outer_type !== 1" style="height: 50%;display: flex;align-items: center;">
                <Col span="8" class="plan-ctrl-btn">
                <Tooltip :delay="1300" content="复制" transfer>
                  <div @click="handleHoverCopyClick(cs.id, cs.class_name)">
                    <i class="fa fa-2x fa-files-o" aria-hidden="true"></i>
                  </div>
                </Tooltip>
                </Col>
                <Col v-if="cs.outer_type !== 1" span="8" class="plan-ctrl-btn">
                <Tooltip :delay="1300" content="批量删除" transfer>
                  <div @click="handleHoverBatchDeleteClick(cs.id, cs.class_name)">
                    <i class="fa fa-2x fa-minus-square-o" aria-hidden="true"></i>
                  </div>
                </Tooltip>
                </Col>
              </Row>
            </div>
          </Card>
        </div>
        <div v-if="!hasAidongClass&&!pickUpFlag" class="plan-foot">
          <Button :class="{'foot-btn':!item.isAdd}" :disabled="item.isAdd" @click="handleSaveModalOpen(item.date)">添加排课</Button>
        </div>
      </div>
      </Col>
    </Row>
    <Modal v-model="showClearModal" title="清空本周排课" width="520">
      <p class="modal-txt">
        确认删除
        <strong>{{ weekDateString }}</strong> 所有未开始课程排课么？
      </p>
      <p class="modal-txt">若课程有学员预约，请自行通知学员课程已变更</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="clearOk">确定</Button>
        <Button @click="showClearModal = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="showCopyModal" :title="pickUpFlag?'选择本周排课复制':'复制本周排课'" width="520">
      <p class="modal-txt">
        将
        <strong>{{ weekDateString }}</strong> 的课程复制到
      </p>
      <CheckboxGroup v-model="checkCopyGroup" class="copy-group">
        <Checkbox
          v-for="(item, index) in copyWeekList"
          :key="index"
          class="modal-txt"
          :label="index">
          {{ item.start }} 至 {{ item.end }}
          <span v-if="index===0"> ({{ isThisWeek?'本周':'下周' }})</span>
        </Checkbox>
      </CheckboxGroup>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="copyOk">确定</Button>
        <Button @click="handleCancleCopy">取消</Button>
      </div>
    </Modal>
    <Modal
      v-model="showSaveModal"
      title="课程排课"
    >
      <Form :model="saveForm" label-position="right" :label-width="100">
        <FormItem label="课程">
          <Select
            v-if="!(isJustLook || !!saveForm.id)"
            v-model="saveForm.courseId"
            style="width:333px;"
            filterable
            @on-change="handleChangeCourse">
            <Option v-for="item in courseList" :key="item.class_id" :value="item.class_id">{{ item.class_name }}</Option>
          </Select>
          <div v-else class="desc-pro">{{ saveForm.class_name }}</div>
        </FormItem>
        <FormItem label="日期">
          <DatePicker
            v-if="!(isJustLook || !!saveForm.id)"
            v-model="saveForm.courseDate"
            type="date"
            placeholder="请选择排课日期"
            style="width:333px"
            :editable="false"></DatePicker>
          <div v-else class="desc-pro">{{ saveForm.courseDateStr }}</div>
        </FormItem>
        <FormItem label="时间">
          <div v-if="!isJustLook">
            <TimePicker
              v-model="saveForm.courseBeginTime"
              :steps="[1, 5]"
              format="HH:mm"
              placeholder="开始时间"
              style="width: 156px"
              @on-change="handleBeginTimeChange"></TimePicker>
            至
            <TimePicker
              v-model="saveForm.courseEndTime"
              :steps="[1, 5]"
              format="HH:mm"
              placeholder="结束时间"
              style="width: 156px"
              @on-change="handleEndTimeChange"></TimePicker>
          </div>
          <div v-else class="desc-pro">{{ saveForm.beg_time }} 至 {{ saveForm.end_time }}</div>
        </FormItem>
        <FormItem v-if="!isFree" label="可预约人数">
          <InputNumber
            v-if="!isJustLook"
            v-model="saveForm.appointment"
            :max="maxReserveNumber"
            :min="1"
            style="width:333px"></InputNumber>
          <div v-else class="desc-pro">{{ saveForm.appointment }}</div>
        </FormItem>
        <FormItem label="教练">
          <Select
            v-if="!isJustLook"
            v-model="saveForm.coachId"
            style="width:333px;"
            filterable>
            <Option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id">{{ item.coach_name }}</Option>
          </Select>
          <div v-else class="desc-pro">{{ saveForm.coach_name }}</div>
        </FormItem>
        <FormItem label="教室">
          <Select
            v-if="!isJustLook"
            v-model="saveForm.roomId"
            style="width:333px;"
            filterable>
            <Option v-for="item in roomList" :key="item.id" :value="item.id">{{ item.classroom_name }}</Option>
          </Select>
          <div v-else class="desc-pro">{{ saveForm.classroom_name }}</div>
        </FormItem>
        <FormItem v-if="!isFree || saveForm.class_category == '0'" label="适合卡种">
          <div>
            <span style="font-size:14px;color:#999;">
              当前课程{{ isConfig == '1' ? `已配置${configType === 1 ? '标签' : '卡种'}数【${configCount}】` : '未配置适合卡种' }}
            </span>
            <Button type="text" style="line-height:1;min-height:unset;" @click="handleToConfigPage">
              {{ isConfig == '1' ? '查看/编辑>>' : '去配置>>' }}
            </Button>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button v-if="!isJustLook" type="success" @click="saveOk">确定</Button>
        <Button v-if="!isJustLook" @click="saveCancel">取消</Button>
      </div>
    </Modal>
    <Modal v-model="showDeleteModal" title="删除排课" width="520">
      <p class="modal-txt">
        确认删除
        <strong>{{ deleteForm.name }}</strong> 么？
      </p>
      <p class="modal-txt">若课程有学员预约，请自行通知学员课程已变更</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="deleteOk">确定</Button>
        <Button @click="showDeleteModal = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="showHCopyModal" title="复制排课" width="600">
      <p v-for="(item, index) in hCopyForm.dateList" :key="index" class="modal-line">
        <!-- <Badge :count="index+1" type="normal"></Badge> -->
        将
        <strong>{{ hCopyForm.name }}</strong> 复制到
        <DatePicker
          v-model="hCopyForm.dateList[index]"
          type="date"
          placeholder="请选择排课日期"
          editable></DatePicker>
        <Button v-if="!(hCopyForm.dateList.length===1)" type="error" @click="hCopyForm.dateList.splice(index, 1)">删除</Button>
        <Button v-if="index===(hCopyForm.dateList.length-1) && hCopyForm.dateList.length!==7" @click="hCopyForm.dateList.push('')">添加</Button>
      </p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="hCopyOk">确定</Button>
        <Button @click="showHCopyModal = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="showHDeleteModal" title="批量删除排课" width="520">
      <p class="modal-txt">
        确认删除所有未开始的
        <strong>{{ hDeleteForm.name }}</strong> 么？
      </p>
      <p class="modal-txt">若课程有学员预约，请自行通知学员课程已变更</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="hDeleteOk">确定</Button>
        <Button @click="showHDeleteModal = false">取消</Button>
      </div>
    </Modal>
  </Card>
</template>
<script>
  import { mapState } from 'vuex'

  const ONE_DAY = 24 * 60 * 60 * 1000;
  const WEEK_LABEL = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];

  export default {
    data() {
      return {
        hasAidongClass: false, // 是否爱动
        today: new Date(),
        autoHeight: 222,
        /* 筛选使用数据 */
        sel_bus_id: '', // 筛选场馆
        weekPickerFlag: false, // 手动控制日期选择器的显示状态
        weekDate: [], // 筛选时间周期
        weekDateString: "", // 时间周期文本
        scheduleThisWeek: { // 从本周排课中获取到的教练、课程选项列表
          courseId: "",
          courseList: [],
          coachId: "",
          coachList: []
        },
        noOfWeek: [], // 已排课数据

        /* 添加排课窗口 */
        showSaveModal: false,
        isJustLook: false,
        isFree: true, // true 操课 false 团课
        maxReserveNumber: 999, // 可预约最大数
        isConfig: '0', // '1'已配置 '0'未配置
        configType: null, // 配置类型 1标签 2卡种 3卡类（此处未使用）
        configCount: 0, // 对应类型配置数量
        saveForm: {
          id: "",
          courseId: "",
          courseDate: "",
          courseBeginTime: "",
          courseEndTime: "",
          beginDateTime: null,
          endDateTime: null,
          appointment: 1, // 可预约人数
          coachId: "",
          roomId: ""
        },
        courseList: [],
        coachList: [],
        roomList: [],

        /* 选择性复制排课 */
        pickUpFlag: false,
        pickUpList: [],
        /* 复制本周排课多选窗口 */
        showCopyModal: false,
        copyWeekList: [], // 选项
        checkCopyGroup: [], // 已选
        isThisWeek: true,
        /* hover按钮单个复制排课窗口 */
        showHCopyModal: false,
        hCopyForm: {
          id: "",
          name: "",
          dateList: []
        },
        /* hover按钮 单个删除排课 确认窗口 */
        showDeleteModal: false,
        deleteForm: {
          id: "",
          name: ""
        },
        /* hover按钮 批量删除排课 确认窗口 */
        showHDeleteModal: false,
        hDeleteForm: {
          id: "",
          name: ""
        },
        /* 清空本周排课确认窗口 */
        showClearModal: false,
        // 是否支持在线购买(isFree为true的情况下)  true支持  false不支持
        onlinePurchase: false
      };
    },
    computed: {
      ...mapState(['busId', 'adminBusList']),
    },
    mounted() {
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      this.sel_bus_id = this.busId;
      this.setWeekDate(this.today);
      // this.getScheduleForSave();
      this.autoSetHeight();
      this.getCourseListForWeek();
    },
    methods: {
      isScrollDown(id) {
        if (this.pickUpFlag) {
          return this.pickUpList.findIndex(item => (item==id)) === -1;
        } else {
          return false;
        }
      },
      handleBeginTimeChange() {
        let activeTime = 45;
        if (this.saveForm.courseId != "") {
          const course = this.courseList.find(
            item => item.class_id == this.saveForm.courseId
          );
          if (course) {
            activeTime = parseInt(course.class_hour);
          }
        }
        if (this.saveForm.courseBeginTime != "") {
          // what the hell! why not is the Date?
          if (typeof(this.saveForm.courseBeginTime)==='object') {
            const begin = this.saveForm.courseBeginTime;
            const end = new Date(begin.getTime() + activeTime * 60 * 1000);
            this.saveForm.courseEndTime = end;
            this.saveForm.beginDateTime = begin;
            this.saveForm.endDateTime = end;
          } else {
            this.saveForm.beginDateTime = new Date(this.getDateString(new Date())+' '+this.saveForm.courseBeginTime);
            this.saveForm.endDateTime = new Date(this.saveForm.beginDateTime.getTime() + activeTime * 60 * 1000);
            this.saveForm.courseEndTime = this.saveForm.endDateTime;
          }
        }
      },
      handleEndTimeChange() {
        if (this.saveForm.courseEndTime != "") {
          this.saveForm.endDateTime = new Date(this.getDateString(new Date())+ ' ' +this.saveForm.courseEndTime);
        }
      },
      setWeekDate(date) {
        let offset = date.getDay();
        offset = offset==0?7:offset;
        let bb = new Date(
          date.getTime() + ONE_DAY * (1 - offset)
        );
        let ee = new Date(
          date.getTime() + ONE_DAY * (7 - offset)
        );

        bb.setHours(0);
        bb.setMinutes(0);
        bb.setSeconds(0);
        ee.setHours(23);
        ee.setMinutes(59);
        ee.setSeconds(59);

        this.weekDate[0] = bb;
        this.weekDate[1] = ee;

        this.weekDateString =
          this.getDateString(this.weekDate[0]) +
          " ~ " +
          this.getDateString(this.weekDate[1]);
      },
      handleWeekChange(val) {
        this.setWeekDate(new Date(val));
        this.getCourseListForWeek();
      },
      setNoOfWeek() {
        let itemStartDate = this.weekDate[0];
        const itemEndDate = this.weekDate[1];
        let isCurrentWeek = false;
        if (
          itemStartDate.getTime() < this.today.getTime() &&
          this.today.getTime() < itemEndDate.getTime()
        ) {
          isCurrentWeek = true;
        }
        this.noOfWeek = [];
        let tt = new Date(this.today.getTime());
        tt.setHours(0);
        tt.setMinutes(0);
        tt.setSeconds(0);
        for (let i = 0; i < 7; i++) {
          this.noOfWeek.push({
            week: WEEK_LABEL[i],
            date: itemStartDate,
            day: itemStartDate.getMonth() + 1 + "/" + itemStartDate.getDate(),
            isToday:
              isCurrentWeek && itemStartDate.getDay() === tt.getDay(),
            isAdd: itemStartDate.getTime() < tt.getTime() -1000,
            list: []
          });
          itemStartDate = new Date(itemStartDate.getTime() + ONE_DAY);
        }
      },
      handleScheduleFilter() {
        this.noOfWeek.forEach(item => {
          item.list = item.orz.filter(
            day =>
              (this.scheduleThisWeek.courseId
                ? day.class_id == this.scheduleThisWeek.courseId
                : true) &&
              (this.scheduleThisWeek.coachId
                ? day.coach_id == this.scheduleThisWeek.coachId
                : true)
          );
        });
      },
      getCourseListForWeek() {
        this.setNoOfWeek();
        return this.$service
          .post("/Web/CourseSchedule/get_class_list", {
            s_date: this.getDateString(this.weekDate[0]),
            e_date: this.getDateString(this.weekDate[1]),
            bus_id: this.sel_bus_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              // put it on schedule.
              const week = res.data.data.schedule_list;
              this.hasAidongClass = res.data.data.is_ad === 0 ? false : true
              this.noOfWeek[0].list = week.Mon;
              this.noOfWeek[1].list = week.Tue;
              this.noOfWeek[2].list = week.Wed;
              this.noOfWeek[3].list = week.Thu;
              this.noOfWeek[4].list = week.Fri;
              this.noOfWeek[5].list = week.Sat;
              this.noOfWeek[6].list = week.Sun;
              this.noOfWeek[0].orz = week.Mon;
              this.noOfWeek[1].orz = week.Tue;
              this.noOfWeek[2].orz = week.Wed;
              this.noOfWeek[3].orz = week.Thu;
              this.noOfWeek[4].orz = week.Fri;
              this.noOfWeek[5].orz = week.Sat;
              this.noOfWeek[6].orz = week.Sun;

              /* 从本周已排课程中，获取用于筛选的教练、课程选项列表 */
              let ccArr = [];
              let courseArr = [];
              let coachArr = [];
              this.noOfWeek.forEach(item => {
                ccArr = ccArr.concat(item.list);
              });
              this.scheduleThisWeek.courseList = [];
              this.scheduleThisWeek.coachList = [];
              ccArr.forEach(item => {
                if (courseArr.indexOf(item.class_id) === -1) {
                  this.scheduleThisWeek.courseList.push({
                    value: item.class_id,
                    label: item.class_name
                  });
                  courseArr.push(item.class_id);
                }
                if (coachArr.indexOf(item.coach_id) === -1) {
                  this.scheduleThisWeek.coachList.push({
                    value: item.coach_id,
                    label: item.coach_name
                  });
                  coachArr.push(item.coach_id);
                }
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getScheduleForSave() {
        return this.$service
          .post("/Web/CourseSchedule/get_add_course_schedule_data", {bus_id: this.sel_bus_id})
          .then(res => {
            if (res.data.errorcode == 0) {
              this.courseList = res.data.data.class_list;
              this.coachList = res.data.data.coach_list;
              this.roomList = res.data.data.classroom_list;
              // this.orginalCardList = res.data.data.card_list;

            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      // 获取团课的配置信息
      getConfigForSave(isAdd, value) {
        if (value === undefined) {
          return false
        }
        const params = {
          // 添加时: 课程class_id, 编辑/打开详情时: 团课排课id
          [isAdd ? 'class_id' : 'id']: value,
          // ...!isAdd ? { bus_id: this.sel_bus_id } : null
          bus_id: this.sel_bus_id // 后端说现在所有情况都传bus_id
        }


        return this.$service
          .post("/Web/CourseSchedule/get_course_schedule_config", params)
          .then(res => {
            if (res.data.errorcode == 0) {
              const { data } = res.data
              if(!Array.isArray(data) && data instanceof Object) {
                this.isFree = data.is_free == '1' // 0团课 1操课
                this.configType = +data.config_type || null // 1标签 2卡种 3卡类
                this.configCount = +data.count || +data.detail_count || 0 // 对应类型配置数量
                // 团课情况下 is_free == 0
                if( data.is_free == '0' ) {
                  this.onlinePurchase = data.support_online == '1' // 是否支持在线购买
                } else {
                  this.onlinePurchase = false
                }
                this.isConfig = this.configCount != 0 ? '1' : '0'
              }else {
                this.isConfig = '0'
                this.configType = null
                this.configCount = 0
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      handleChangeCourse(val) {
        if (!val || val && val.length === 0) {
          return false;
        }
        const item = this.courseList.find(v => v.class_id == val)
        if (item) {
          this.isFree = item.is_free == '1'
          this.saveForm.appointment = item.is_free == '1' ? 0 : null
          this.maxReserveNumber = item.reserve_number !== undefined ? +item.reserve_number : 999
        } else {
          this.maxReserveNumber = 999
        }
        this.getConfigForSave(true, val)
        // return this.$service
        //   .post("/Web/CourseSchedule/get_last_class_course_schedule", {
        //     class_id: val,
        //     bus_id: this.sel_bus_id
        //   })
        //   .then(res => {
        //     if (res.data.errorcode == 0) {
        //       const cs = res.data.data.info;
        //       this.checkin(cs);
        //     } else {
        //       this.$Message.error(res.data.errormsg);
        //     }
        //   });
      },

      handleCancleCopy() {
        this.showCopyModal = false;
        this.pickUpFlag = false;
        this.pickUpList = [];
      },

      // 打开添加排课弹窗
      handleSaveModalOpen(date) {
        this.getScheduleForSave().then(() => {
          this.showSaveModal = true;
          this.isJustLook = false;
          this.isFree = true;
          this.saveForm.id = "";
          this.saveForm.courseId = "";
          this.saveForm.courseDate = date;
          this.saveForm.courseBeginTime = "";
          this.saveForm.courseEndTime = "";
          this.saveForm.coachId = "";
          this.saveForm.roomId = "";
        })
      },
      saveOk() {
        const course = this.courseList.find(
          item => item.class_id == this.saveForm.courseId
        );
        const coach = this.coachList.find(
          item => item.coach_id == this.saveForm.coachId
        );
        let room = this.roomList.find(item => item.id == this.saveForm.roomId);

        if (!course && this.saveForm.id == "") { // 董卿说编辑的时候去掉这个判断
          this.$Message.error("请选择课程！");
          return false;
        } else if (this.saveForm.courseDate == "") {
          this.$Message.error("请选择排课日期！");
          return false;
        } else if (this.saveForm.courseBeginTime == "") {
          this.$Message.error("请选择排课开始时间！");
          return false;
        } else if (
          !!this.saveForm.endDateTime && !!this.saveForm.beginDateTime &&
          (this.saveForm.endDateTime.getTime() < this.saveForm.beginDateTime.getTime())
        ) {
          this.$Message.error("排课结束时间必须大于开始时间！");
          return false;
        } else if (!coach) {
          this.$Message.error("请选择教练！");
          return false;
        } else if (!room) {
          // this.$Message.error("请选择教室！");
          // return false;
          room = {
            id: '',
            classroom_name: ''
          };
        }

        let bean = {
          class_id: course?course.class_id:this.saveForm.courseId,
          class_name: course?course.class_name:this.saveForm.class_name,
          class_category: this.isFree ? 1 : 0,
          date_time: this.getDateString(this.saveForm.courseDate),
          reserve_number: this.saveForm.appointment, // 可预约人数
          coach_id: coach.coach_id,
          coach_name: coach.coach_name,
          classroom_id: room.id,
          classroom_name: room.classroom_name,
          bus_id: this.sel_bus_id,
        };

        if (!!this.saveForm.endDateTime && this.saveForm.beginDateTime) {
          bean.beg_time = this.getTimeString(this.saveForm.beginDateTime);
          bean.end_time = this.getTimeString(this.saveForm.endDateTime);
        } else {
          bean.beg_time = this.saveForm.courseBeginTime;
          bean.end_time = this.saveForm.courseEndTime;
        }

        let configUrl = `/Web/OpenClass/get_open_class_detail?class_id=${this.saveForm.courseId}&bus_id=${this.sel_bus_id}`;
        if(this.saveForm.id) {
          configUrl = `/Web/CourseSchedule/get_course_schedule_config_detail?bus_id=${this.sel_bus_id}&course_schedule_id=${this.saveForm.id}`
        }
        if (!this.isFree) {
          bean.update_class = '0'
          return this.$service.get(configUrl).then(res => {
            if (res.data.errorcode === 0) {
              const data = this.saveForm.id ? res.data.data.data : res.data.data
              const { config_type, list } = data
              // 原型地址: http://prd.kaifa.rocketbird.cn/01_SaaS/01_%E7%AE%A1%E7%90%86%E7%AB%AF/20_%E5%9B%A2%E8%AF%BE%E6%8E%92%E8%AF%BE/#id=5vvbf4&p=%E6%B7%BB%E5%8A%A0%E6%8E%92%E8%AF%BE%EF%BC%88%E5%BC%B9%E6%A1%86%EF%BC%89&g=1
              // 2023/03/01 原型 -> 团课排课(弹窗) -> 注释4
              // 如果是团课并且开启了在线购买 则不需要配置适合卡种 直接保存
              if( this.onlinePurchase ) {
                // 默认没有config_type以及list
                let apply_card = ''
                let configType = '0'
                // 有config_type的情况处理
                if( config_type ) {
                  configType = config_type
                }
                // 有list的情况处理
                if( Array.isArray(list) ) {
                  apply_card = list.filter(v => v.type_id || v.config_type_id).map(v => ({
                    config_type_id: v.type_id || v.config_type_id,
                    config_type_name: v.apply_card.config_type_name,
                    todeduct_type: v.apply_card.todeduct_type, // 配置的类型 1会籍卡 2私教卡 3泳教卡
                    todeduct_type_detail: v.apply_card.todeduct_type_detail, //  私教包月？配置的类型具体 1期限卡 2次卡 3储值卡 4私教课 5泳教课
                    todeduct_c: v.apply_card.todeduct_c, // 次/人
                    todeduct_y: v.apply_card.todeduct_y, // 元/人
                    maxresv_num: v.apply_card.maxresv_num // 限制人数
                  }))
                }
                // 赋值传递
                bean.config_type = configType
                bean.apply_card = apply_card ? JSON.stringify(apply_card) : ''
                this.saveData(bean)
              }else if ( !this.onlinePurchase && config_type && Array.isArray(list) ) {
                if (+config_type !== 0) {
                  bean.config_type = config_type // 1标签 2卡种 3卡类型
                  const apply_card = list.filter(v => v.type_id || v.config_type_id).map(v => ({
                    config_type_id: v.type_id || v.config_type_id,
                    config_type_name: v.apply_card.config_type_name,
                    todeduct_type: v.apply_card.todeduct_type, // 配置的类型 1会籍卡 2私教卡 3泳教卡
                    todeduct_type_detail: v.apply_card.todeduct_type_detail, //  私教包月？配置的类型具体 1期限卡 2次卡 3储值卡 4私教课 5泳教课
                    todeduct_c: v.apply_card.todeduct_c, // 次/人
                    todeduct_y: v.apply_card.todeduct_y, // 元/人
                    maxresv_num: v.apply_card.maxresv_num // 限制人数
                  }))
                  bean.apply_card = JSON.stringify(apply_card)
                }
                this.saveData(bean)
              } else {
                this.$Message.error('非付费课程，请先配置适合卡种！');
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
        } else {
          return this.saveData(bean)
        }
      },
      saveData(bean) {
        let url = "";
        if (this.saveForm.id == "") {
          url = "/Web/CourseSchedule/add_course_schedule";
        } else {
          url = "/Web/CourseSchedule/update_course_schedule";
          bean.id = this.saveForm.id;
        }
        return this.$service.post(url, bean).then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.saveCancel();
          } else {
            this.$Message.error(res.data.errormsg);
          }
          this.getCourseListForWeek();
        });
      },
      saveCancel() {
        this.showSaveModal = false;
        this.isFree = true;
        this.saveForm.id = "";
        this.saveForm.courseId = "";
        this.saveForm.courseDate = "";
        this.saveForm.courseBeginTime = "";
        this.saveForm.courseEndTime = "";
        this.saveForm.coachId = "";
        this.saveForm.roomId = "";
        this.configType = null;
        this.configCount = 0;
      },
      clearOk() {
        return this.$service
          .post("/Web/CourseSchedule/batch_del_schedules", {
            s_date: this.getDateString(this.weekDate[0]),
            e_date: this.getDateString(this.weekDate[1]),
            bus_id: this.sel_bus_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.showClearModal = false;
            this.getCourseListForWeek();
          });
      },
      getNextWeek(week) {
        return [
          new Date(week[0].getTime() + ONE_DAY * 7),
          new Date(week[1].getTime() + ONE_DAY * 7)
        ];
      },
      handleCopyOpen() {
        this.showCopyModal = true;
        this.copyWeekList = [];

        let offset = this.today.getDay();
        offset = offset==0?7:offset;
        let next1 = [];
        next1[0] = new Date(
          this.today.getTime() + ONE_DAY * (1 - offset)
        );
        next1[1] = new Date(
          this.today.getTime() + ONE_DAY * (7 - offset)
        );
        next1[0].setHours(0);
        next1[0].setMinutes(0);
        next1[0].setSeconds(0);
        next1[1].setHours(23);
        next1[1].setMinutes(59);
        next1[1].setSeconds(59);

        if (this.weekDate[0].getTime() < this.today.getTime() && this.today.getTime() < this.weekDate[1].getTime()) {
          this.isThisWeek = false;
          next1 = this.getNextWeek(next1);
        } else {
          this.isThisWeek = true;
        }

        const next2 = this.getNextWeek(next1);
        const next3 = this.getNextWeek(next2);
        const next4 = this.getNextWeek(next3);

        this.copyWeekList.push({
          start: this.getDateString(next1[0]),
          end: this.getDateString(next1[1])
        });
        this.copyWeekList.push({
          start: this.getDateString(next2[0]),
          end: this.getDateString(next2[1])
        });
        this.copyWeekList.push({
          start: this.getDateString(next3[0]),
          end: this.getDateString(next3[1])
        });
        this.copyWeekList.push({
          start: this.getDateString(next4[0]),
          end: this.getDateString(next4[1])
        });
      },
      copyOk() {
        let cpStartList = [];
        this.checkCopyGroup.forEach(item => {
          cpStartList.push(this.copyWeekList[item].start);
        });
        let idd = null;
        if (this.pickUpFlag) {
          idd = this.pickUpList
        } else {
          idd = null;
        }

        return this.$service
          .post("/Web/CourseSchedule/schedule_copy_this_week", {
            s_date: this.getDateString(this.weekDate[0]),
            e_date: this.getDateString(this.weekDate[1]),
            copy_s_time: cpStartList,
            id: idd,
            bus_id: this.sel_bus_id
          })
          .then(res => {
             if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.showCopyModal = false;
            this.checkCopyGroup = [];
            this.getCourseListForWeek();
            this.pickUpFlag = false;
            this.pickUpList = [];
          });
      },
      getDateString(date) {
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? "0" + month : month;
        let day = date.getDate();
        day = day < 10 ? "0" + day : day;
        return `${year}/${month}/${day}`;
      },
      getTimeString(date) {
        let hour = date.getHours();
        let minute = date.getMinutes();
        hour = (hour<10)?'0'+hour:hour;
        minute = (minute<10)?'0'+minute:minute;
        return `${hour}:${minute}`;
      },
      checkin(cs) {
        // const flag = Array.isArray(cs.apply_card)
        const flag =  cs.class_category === '0'
        if (flag) {
          // card for a course.
          this.isFree = false;
          this.saveForm.appointment = parseInt(cs.reserve_number); // 可预约人数
        } else {
          // all card.
          this.isFree = true;
          this.saveForm.appointment = 0;
        }
      },
      openDetailModel(id) {
        return this.$service
          .post("/Web/CourseSchedule/get_course_schedule", {
            id: id,
            bus_id: this.sel_bus_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              const info = res.data.data.info;
              this.saveForm = {
                ...info,
                id: info.id,
                courseId: info.class_id,
                courseDate: new Date(info.date_time * 1000),
                courseBeginTime: info.beg_time,
                courseEndTime: info.end_time,
                coachId: info.coach_id,
                roomId: info.classroom_id,
                support_online: this.onlinePurchase,
                appointment: parseInt(info.reserve_number) // 可预约人数
              };
              this.saveForm.courseDateStr = this.getDateString(this.saveForm.courseDate);
              const item = this.courseList.find(v => v.class_id == info.class_id)
              if (item) {
                this.isFree = item.is_free == '1'
                this.maxReserveNumber = item.reserve_number !== undefined ? +item.reserve_number : 999
              } else {
                this.maxReserveNumber = 999
              }
              // const cs = res.data.data.info;
              // this.checkin(cs);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      // 打开排课详情
      handleHoverDetailClick(id) {
        const { getScheduleForSave: p1, getConfigForSave: p2 } = this
        Promise.all([p1(), p2(false, id)]).then(resArr => {
          this.showSaveModal = true;
          this.isJustLook = true;
          this.openDetailModel(id);
        })
      },
      // 打开排课修改
      async handleHoverUpdateClick(id) {
        const { getScheduleForSave: p1, getConfigForSave: p2 } = this
        Promise.all([p1(), p2(false, id)]).then(resArr => {
          this.showSaveModal = true;
          this.isJustLook = false;
          this.openDetailModel(id);
        })
      },
      handleHoverDeleteClick(id, name) {
        this.showDeleteModal = true;
        this.deleteForm.id = id;
        this.deleteForm.name = name;
      },
      deleteOk() {
        return this.$service
          .post("/Web/CourseSchedule/del_schedule_once", {
            schedule_id: this.deleteForm.id,
            bus_id: this.sel_bus_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.showDeleteModal = false;
            this.deleteForm.id = "";
            this.deleteForm.name = "";
            this.getCourseListForWeek();
          });
      },
      handleHoverCopyClick(id, name) {
        this.showHCopyModal = true;
        this.hCopyForm.id = id;
        this.hCopyForm.name = name;
        this.hCopyForm.dateList = [""];
      },
      hCopyOk() {
        let dateArr = [];
        this.hCopyForm.dateList.forEach(item => {
          dateArr.push(this.getDateString(item));
        });
        return this.$service
          .post("/Web/CourseSchedule/schedule_copy_once", {
            schedule_id: this.hCopyForm.id,
            copy_time: dateArr,
            bus_id: this.sel_bus_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.showHCopyModal = false;
            this.hCopyForm.id = "";
            this.hCopyForm.name = "";
            this.hCopyForm.dateList = [];
            this.getCourseListForWeek();
          });
      },
      handleHoverBatchDeleteClick(id, name) {
        this.showHDeleteModal = true;
        this.hDeleteForm.id = id;
        this.hDeleteForm.name = name;
      },
      hDeleteOk() {
        return this.$service
          .post("/Web/CourseSchedule/del_schedules", {
            schedule_id: this.hDeleteForm.id,
            bus_id: this.sel_bus_id
          })
          .then(res => {
             if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.showHDeleteModal = false;
            this.hDeleteForm.id = "";
            this.hDeleteForm.name = "";
            this.getCourseListForWeek();
          });
      },
      handlePickUpToCopy() {
        if (this.pickUpFlag) {
          if (Array.isArray(this.pickUpList) && this.pickUpList.length>0) {
            this.handleCopyOpen();
          } else {
            this.pickUpFlag = false;
            this.pickUpList = [];
          }
        } else {
          this.pickUpFlag = true;
        }
      },
      handlePickUp(id) {
        const idx = this.pickUpList.findIndex(item => (item===id));
        if (idx === -1) {
          this.pickUpList.push(id);
        } else {
          this.pickUpList.splice(idx, 1);
        }
      },
      autoSetHeight() {
        // course scroll max height
        const bbox = document.querySelector('.db-box');
        this.autoHeight = bbox.clientHeight - 240;
      },
      handleToClassPreview () {
        const scheduleList = this.noOfWeek.map(({ list }) => list)
        // if(scheduleData.length !== 7) return this.$Message.info('未获取到全部排课数据')
        this.$router.push({
          name: '课表预览',
          params: {
            selectedBusId: this.sel_bus_id,
            scheduleList,
            weekDateString: this.weekDateString
          },
        })
      },
      handleToConfigPage() {
        const {
          courseId,
          id,
          class_name,
        } = this.saveForm
        this.$router.push({
          name: '适合卡种配置',
          params: {
            classId: courseId, // 课程id
            className: class_name,
            courseScheduleId: id, // 排课id
            selBusId: this.sel_bus_id, // 当前选择的场馆id
            configType: this.configType,
            isConfig: this.isConfig, // 是否配置

            isFree: this.isFree,
            maxReserveNumber: this.maxReserveNumber,
            saveForm: { ...this.saveForm, ...{ support_online: this.onlinePurchase } },
            courseList: this.courseList,
            coachList: this.coachList,
            roomList: this.roomList,
          }
        })
      }
    }
  };
</script>

<style lang="less" scoped>
  .db-box {
    height: 100%;

    .course-scroll {
      overflow-y: scroll;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .box-head {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 78px;

    .filter-area {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .controller-area {
      display: flex;
      flex-direction: row;
      align-items: center;

      .controller-btn {
        margin-left: 22px;
      }
    }
  }

  .zj-badage {
    padding: 0 6px;
    // height: 15px;
    // line-height: 15px;
    min-width: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: white;
    background-color: #ddd;
    // border-radius: 30px;
    overflow: hidden;
  }

  .plan-week {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .plan-day {
      margin-top: 16px;
      width: 14%;

      .plan-today {
        background-color: #52a4ea;
        box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5);
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
        z-index: 99;
        color: white;
      }

      .plan-head {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1px solid #ccc;
        height: 66px;
      }

      .plan-body {
        vertical-align: middle;
        -webkit-transform: perspective(1px) translateZ(0);
        transform: perspective(1px) translateZ(0);
        box-shadow: 0 0 1px transparent;
        position: relative;
        -webkit-transition-property: color;
        transition-property: color;
        -webkit-transition-duration: 0.5s;
        transition-duration: 0.5s;
        margin: 6px 0;
        background-color: #FBFBFB;
        width: 98%;

        .pick-up-me {
          height: 40px;
          width: 40px;
          border-radius: 50%;
          overflow: hidden;
          position: absolute;
          top: 50% - 20px;
          left: 50% - 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 999;
          cursor: pointer;
        }

        .plan-info {

          .gym {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .gym-name {
              white-space: nowrap;
              width: 60%;
            }
          }
        }

        .plan-contraller {
          display: none;
          position: fixed;
          top: 0;
          left: 0;
          padding: 16px;
          width: 100%;
          color: #666;

          .plan-ctrl-btn {
            display: flex;
            justify-content: center;
            cursor: pointer;
          }
        }
      }

      .plan-body:not(.nohover):before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: gray;
        opacity: 0.4;
        -webkit-transform: scaleY(0);
        transform: scaleY(0);
        -webkit-transform-origin: 50% 0;
        transform-origin: 50% 0;
        -webkit-transition-property: transform;
        transition-property: transform;
        -webkit-transition-duration: 0.5s;
        transition-duration: 0.5s;
        -webkit-transition-timing-function: ease-out;
        transition-timing-function: ease-out;
      }

      .scroll-down {
        background: white;
        opacity: 0.4;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
        transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
      }

      .plan-body:not(.nohover):hover:before,
      .plan-body:not(.nohover):focus:before,
      .plan-body:not(.nohover):active:before {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
        transition-timing-function: cubic-bezier(0.52, 1.64, 0.37, 0.66);
      }

      .plan-body:not(.nohover):hover .plan-contraller {
        display: flex;
        flex-direction: column;
      }

      .plan-body:not(.nohover):hover .plan-info {
        opacity: 0.4;
      }

      .plan-foot {
        height: 60px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .foot-btn {
          color: #52a4ea;
          border: 1px solid #52a4ea;
        }
      }
    }
  }

  .checkbox-group {
    max-height: 281px;
    overflow-y: scroll;

    .checkbox-card {
      display: flex;
      flex-direction: row;

      .checkbox-card-name {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        width: 173px;
      }

      .checkbox-card-settings {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
      }
    }
  }

  .copy-group {
    display: flex;
    flex-direction: column;
    margin-top: 22px;
  }

  .modal-txt {
    font-size: 14px;
    margin-bottom: 10px;

    strong {
      color: red;
    }
  }

  .modal-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 40px;
    font-size: 14px;

    * {
      margin: 0 3px;
    }
  }

  .desc-pro {
    font-size: 14px;
    color: #464c5b;

  }

  .ivu-form-item {
    margin-bottom: 12px;
  }
</style>
