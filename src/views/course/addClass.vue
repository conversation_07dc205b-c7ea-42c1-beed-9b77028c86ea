<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{formItem.id?'编辑团课':'添加团课'}}</h2>
    </div>
    <div class="form-box-con">
      <Form label-position="right" ref="form" :model="formItem" :rules="formRules" class="form" :label-width="180">
        <FormItem label="课程名称" prop="class_name">
          <Input v-model="formItem.class_name" class="input" />
        </FormItem>
        <FormItem label="单节时长" prop="class_hour">
          <Input v-model="formItem.class_hour" class="input" placeholder="分钟" />
        </FormItem>
        <FormItem label="课程种类" prop="class_type">
          <RadioGroup v-model="formItem.class_type">
            <Radio label="0" :disabled="!!formItem.id">团课(预约上课)
            </Radio>
            <Radio label="1" :disabled="!!formItem.id">操课(无需预约)
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="课程强度" prop="class_level">
          <RadioGroup v-model="formItem.class_level">
            <Radio :label="1">1星</Radio>
            <Radio :label="2">2星</Radio>
            <Radio :label="3">3星</Radio>
            <Radio :label="4">4星</Radio>
            <Radio :label="5">5星</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="课程颜色" prop="class_color">
          <ColorPicker
            v-model="formItem.class_color"
            alpha
            recommend
            :colors="['rgba(168, 233, 255, 1)','rgba(171, 255, 229, 1)','rgba(238, 242, 147, 1)','rgba(255, 189, 225, 1)','rgba(213, 193, 255, 1)']"
           />
        </FormItem>
        <FormItem label="适用场馆" prop="region_bus">
          <AdminRegion v-model="formItem.region_bus" url="/Web/OpenClass/get_openclass_region_bus" :id="formItem.id" :shouldDefault=1 filterable />
        </FormItem>
        <FormItem label="最少开课人数" prop="min_number">
          <Input v-model="formItem.min_number" @on-blur="handleReserveNumber" />
        </FormItem>
        <FormItem label="最大预约人数" prop="reserve_number" v-if="formItem.class_type==0">
          <Input v-model="formItem.reserve_number" @on-blur="handleReserveNumber" />
        </FormItem>
        <FormItem label="预约时间限制" prop="mark_time_switch" v-if="formItem.class_type==0">
          <RadioGroup v-model="formItem.mark_time_switch">
            <Radio label="1">是
            </Radio>
            <Radio label="2">否
            </Radio>
          </RadioGroup>
          <div class="oneline">
            <span>上课前</span>
            <InputNumber class="closebox" size="large" :disabled="formItem.mark_time_switch==2" :min="1" v-model="formItem.stop_mark"></InputNumber>
            <span>分钟停止预约，</span>
            <InputNumber class="farbox" size="large" :disabled="formItem.mark_time_switch==2" :min="1" v-model="formItem.stop_result_mark"></InputNumber>
            <span>分钟停止取消</span>
          </div>
          <div class="oneline">
            <span>可提前</span>
            <InputNumber class="closebox" size="large" :disabled="formItem.mark_time_switch==2" :max="5" :min="0" v-model="formItem.mark_day"></InputNumber>
            <span>天开始约课（最大只能提前5天，0表示只能约当天课程）</span>
          </div>
          <div class="oneline">
            <span>设  置</span>
             <TimePicker class="closebox time-box" :disabled="formItem.mark_time_switch==2" format="HH:mm" v-model="formItem.mark_time" placeholder="" style="width: 112px" :clearable="false"></TimePicker>
            <span>后才能约课</span>
          </div>
          <div class="oneline">
            <Checkbox true-value="1" false-value="2" v-model="formItem.result_send">人数不满时自动取消预约，并发送消息通知用户</Checkbox>
          </div>
        </FormItem>
        <FormItem prop="thumb" class="formpic">
          <div class="image-description image-description-required" slot="label">
            <p class="label">课程封面</p>
            <p class="tip">图片最佳尺寸: 750X424</p>
            <p class="tip">推荐图片大小: &lt;100kb</p>
            <p class="tip">格式限制: jpg、png</p>
          </div>
          <div class="logo" v-if="formItem.thumb&&!noshowPic">
            <img :src="formItem.thumb">
          </div>
          <ImgUploader refName="classUploader" :options="{aspectRatio: 750/424}" v-model="formItem.thumb" multiple nopreview v-on:noshowpic="checkShow" :modelPicAddr="modelPicAddr" />
        </FormItem>
        <Form-item label="课程介绍" prop="description">
             <FormEditor class="description" v-model="formItem.description" />
          <!-- <Input type="textarea" :rows="5" :maxlength="500" v-model="formItem.description" /> -->
        </Form-item>
        <FormItem prop="support_online" v-if="formItem.class_type==0">
          <div slot="label">
            <span>在线购买</span>
            <Tooltip>
              <div slot="content" style="width: 160px; white-space: normal">功能开启后，用户可以不购买会员卡直接通过手机付费购买单节课程进行上课</div>
              <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
            </Tooltip>
          </div>
          <RadioGroup v-model="formItem.support_online" @on-change="handleSupportMobile">
            <Radio label="1">支持
            </Radio>
            <Radio label="0">不支持
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem prop="support_waitting" v-if="formItem.class_type==0">
          <div slot="label">
            <span>候补预约</span>
            <Tooltip>
              <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
              <div slot="content" style="width: 160px; white-space: normal">功能开启后，团课约满时，会员可在小程序中进行候补排队。开启在线购买，则不支持开启候补</div>
            </Tooltip>
          </div>
          <RadioGroup v-model="formItem.support_waitting">
            <Radio :label="1" :disabled="formItem.support_online==='1'">支持
            </Radio>
            <Radio :label="0" :disabled="formItem.support_online==='1'">不支持
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="单价" prop="all_class_price" v-if="formItem.class_type==0&&formItem.support_online==1">
          <Input v-model="formItem.all_class_price" placeholder="元" />
          <div style="color: red; font-size: 12px">
            注意：如果课程无法正常开课或课程开课时间有调整，请联系购买人协商处理，系统无法自动执行退款操作
          </div>
        </FormItem>
        <FormItem>
          <div class="buttons">
            <Button type="primary" v-if="$route.params.id" @click="updateInfo">提交</Button>
            <Button type="primary" v-else @click="addCourse">提交</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </FormItem>
      </Form>
    </div>

    <Modal v-model="supportMobileModal" title="提示">
       <div class="bodybuilding">
        <p class="w-tip" style="color:red;">在线支付功能未开通，请联系勤鸟工作人员</p>
        <p class="w-tip" style="margin-top:20px;"><label style="color:red;">{{noneBusNames.length}}家</label>场馆未配置: {{noneBusNames.join(",")}}</p>
        <!-- <p class="w-red" style="margin-top:20px;">启用在线支付购卡购课功能</p>
        <p class="w-red">需要先申请微信特约服务商</p> -->
        <!-- <img class="word" src="../../assets/img/word_support_mobile.png" alt="word">
        <p class="w-blue">微信支付特约商户配置</p> -->
       </div>
       <div slot="footer" class="modal-buttons">
        <!-- <Button type="info" @click="handleDownloadWord">下载文档</Button> -->
        <Button @click="supportMobileModal = false">关闭</Button>
        </div>
     </Modal>
  </div>
</template>
<script>
  import ImgUploader from 'components/form/cropper';
  import AdminRegion from '../../components/form/adminRegion.vue';
  import FormEditor from 'components/form/Editor';

  export default {
    components: {
      ImgUploader,
      AdminRegion,
      FormEditor
    },
    data() {
      return {
        formItem: {
          id: '',
          class_name: '',
          class_hour: '',
          class_type: '0',
          min_number: '',
          reserve_number: '',
          mark_time_switch: '1',
          stop_mark: 1,
          stop_result_mark: 1,
          mark_day: 0,
          mark_time: '00:00',
          result_send: '2',
          thumb: '',
          description: '',
          support_online: '0',
          all_class_price: '0.01',
          region_bus: [],
          class_level: 0, // 课程强度（1-5） 默认为0
          class_color: '', // 课程颜色
          support_waitting: 0 // 是否支持候补
        },
        formRules: {
          class_name: [{ required: true, message: '请填写课程名称', trigger: 'blur' }],
          class_hour: [
            { required: true, message: '单节时长为大于等于5的整数', pattern: /^[5-9]|([1-9]\d+)$/, trigger: 'blur' }
          ],
          region_bus: [{ required: true, message: '请选择适用场馆', trigger: 'blur', type: 'array', len: 1, defaultField: {type: "string"} }],
          min_number: [
            {
              required: true,
              message: '最小开课人数必须是大于等于1的整数',
              pattern: /^[1-9]|([1-9]\d+)$/,
              trigger: 'blur'
            }
          ],
          reserve_number: [
            {
              required: true,
              message: '最大预约人数必须是大于等于1的整数',
              pattern: /^[1-9]|([1-9]\d+)$/,
              trigger: 'blur'
            }
          ],
          thumb: [{ required: true, message: '请上传图片' }],
          all_class_price: [
            {
              required: true,
              pattern: /^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d*(\.\d{0,2})?)$/,
              message: '价格必须大于0且最多只能保留两位小数',
              trigger: 'change'
            }
          ]
        },
        noshowPic: false,
        modelPicAddr: [
          { addr: 'https://imagecdn.rocketbird.cn/test/image/37584282d2958670517fa3cf378b422a.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/1c8f8ff699a73c27ebcba43c339e7a04.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/c240b15e2433990cbc475723bbc582b3.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/4b2d605d0bc6a30cda14ac1fdddedaf9.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/7e985e8d86fe06ae60a8f0e593761b6a.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/14f99aade526ee50ea7d0e0efca1b62d.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/fff1055e6f8d9d7d9901583a4eff2bde.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/41fe2014c999bd593dcd274991f55656.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/2d5cbf19122a6b8893262ee8945bfdc3.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/007534b1d20d6efebc772293584ba557.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/505521d05f8e2fc5e8046550bd733252.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/b4da47f421d1fe04c578841e68c09350.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/c4cebbca71e94945f35cdf356402965e.png' },
          { addr: 'https://imagecdn.rocketbird.cn/test/image/ef81aaf838f093962764a288fe6cd465.png' }
        ],
        supportMobileAuthority: false,
        supportMobileModal: false,
        noneBusNames: []
      };
    },
    created() {
      if (this.$route.params.id) {
        this.formItem.id = this.$route.params.id;
        this.geteditInfo();
      } else {
        this.formItem.id = '';
        this.getInfo();
      }
      // this.getSupportMobile();
    },
    methods: {
      handleReserveNumber() {
        const { min_number, reserve_number, class_type } = this.formItem;
        if (reserve_number && +min_number > +reserve_number && class_type === '0') {
          this.$Message.error({ content: '最大预约人数不能少于开课人数', duration: 2.5 });
          return false;
        }
        return true;
      },
      checkShow(val) {
        this.noshowPic = val;
      },
      // 添加修改团课的请求处理
      handleOpenClassRequest(url) {
        this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写');
            return;
          }
          valid = this.handleReserveNumber();
          if (!valid) return false;
          if (this.formItem.class_type == 1) {
            this.formItem.reserve_number = '';
            this.formItem.mark_time_switch = '';
            this.formItem.stop_mark = '';
            this.formItem.stop_result_mark = '';
            this.formItem.mark_day = '';
            this.formItem.mark_time = '00:00';
            this.formItem.result_send = '';
            this.formItem.support_online = '';
            this.formItem.all_class_price = '';
            this.formItem.support_waitting = 0;
          }
          if (this.formItem.support_online == 0) {
            this.formItem.all_class_price = '';
          }
          this.$service
            .post(url, this.formItem)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back();
                this.$Message.success(res.data.errormsg);
              } else if (res.data.errorcode === 5000) {
                this.supportMobileModal = true;
                this.noneBusNames = res.data.bus_name;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
        });
      },
      addCourse() {
        const url = '/Web/OpenClass/add_open_class';
        this.handleOpenClassRequest(url)
      },
      updateInfo() {
        const url = '/Web/OpenClass/update_open_class';
        this.handleOpenClassRequest(url)
      },
    unescapeHTML (a) {
      a = '' + a;
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    },
      geteditInfo() {
        const url = '/Web/OpenClass/get_open_class_info';
        this.$service
          .post(url, { id: this.formItem.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.formItem.stop_mark = Number(res.data.data.stop_mark);
              this.formItem.stop_result_mark = Number(res.data.data.stop_result_mark);
              this.formItem.mark_day = Number(res.data.data.mark_day);
              this.formItem.mark_time = res.data.data.mark_time ? res.data.data.mark_time : '00:00';
              this.formItem.class_name = String(res.data.data.class_name);
              this.formItem.class_hour = String(res.data.data.class_hour);
              this.formItem.class_type = String(res.data.data.is_free);
              this.formItem.min_number = String(res.data.data.min_number);
              this.formItem.reserve_number = String(res.data.data.reserve_number);
              this.formItem.mark_time_switch = String(res.data.data.mark_time_switch);
              this.formItem.result_send = String(res.data.data.result_send);
              this.formItem.thumb = String(res.data.data.thumb);
              this.formItem.description = this.unescapeHTML(res.data.data.description);
              this.formItem.support_online = String(res.data.data.support_online);
              this.formItem.all_class_price = String(res.data.data.all_class_price);

              this.formItem.class_level = Number(res.data.data.class_level);
              this.formItem.class_color = res.data.data.class_color === null ? '' : res.data.data.class_color
              this.formItem.support_waitting = Number(res.data.data.support_waitting);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getInfo() {
        const url = '/Web/OpenClass/get_open_class_top';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.formItem.stop_mark = Number(res.data.data.stop_mark);
              this.formItem.stop_result_mark = Number(res.data.data.stop_result_mark);
              this.formItem.mark_day = Number(res.data.data.mark_day);
              this.formItem.mark_time = res.data.data.mark_time ? res.data.data.mark_time : '00:00';
            } else {
              // this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getSupportMobile() {
        return this.$service.get('/Web/Card/check_surport_online').then(res => {
          if (res.data.errorcode == 0) {
            this.supportMobileAuthority = res.data.data.status == 1;
          }
        });
      },
      // 切换是否支持在线购买
      handleSupportMobile(val) {
        // if (!this.supportMobileAuthority && this.formItem.support_online == 1) {
          // this.supportMobileModal = true;
        //   setTimeout(() => {
        //     this.formItem.support_online = '0';
        //   }, 1000);
        // }
        // 开启在线购买，则不支持开启候补
        if(val === '1') this.formItem.support_waitting = 0;
      },
      handleDownloadWord() {
        window.open("https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf");
      }
    }
  };
</script>
<style>
.form-box .time-box .ivu-input-wrapper{
  width: 100%;
}
</style>
<style lang="less" scoped>
  @btn-color: #19be6b;
  .formpic /deep/ .ivu-form-item-content {
    width: 53%;
  }
  .ivu-color-picker {
    line-height: 1.5;
    /deep/.ivu-color-picker-rel, /deep/.ivu-input-wrapper {
      width: 100%;
    }
    /deep/.ivu-color-picker-confirm-color {
      display: block;
      position: static;
      margin-bottom: 8px;
    }
    /deep/.ivu-btn {
      line-height: 1.5;
    }
  }
  .oneline {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 15px;
    margin-bottom: 20px;
    // font-size: 12px;
  }
  // .existpic {
  //   display: flex;
  //   flex-direction: row;
  //   flex-wrap: nowrap;
  //   justify-content: flex-start;
  //   align-items: flex-end;
  // }
  .oneline:last-child {
    margin-bottom: 0;
  }
  .form-box .ivu-input-number {
    width: 60px;
  }
  .closebox {
    margin-left: 6px;
    margin-right: 6px;
  }
  .farbox {
    // margin-left: 25px;
    margin-right: 6px;
  }
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .logo {
    .flex-center;
    border: 5px solid #dcdcdc;
    width: 500px;
    max-height: 300px;
    box-sizing: border-box;
    padding: 10px;
    margin-bottom: 20px;
    > img {
      width: 100%;
      height: 100%;
      max-width: 470px;
      max-height: 260px;
    }
  }
  .description {
      width: 500px;
  }

  .bodybuilding {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .w-tip {
      font-size: 14px;
      color: #333;
      max-width: 400px;
      display: inline-block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .w-red {
      font-size: 16px;
      color: red;
    }

    .w-blue {
      font-size: 16px;
      color: blue;
    }

    .word {
      margin-top: 30px;
      height: 118px;
      width: 242px;
    }
  }
</style>
