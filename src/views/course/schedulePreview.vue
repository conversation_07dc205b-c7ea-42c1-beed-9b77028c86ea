<template>
  <div class="schedule-container">
    <div ref="timetableRef" class="timetable-wrapper" :style="bgStyles">
      <div class="top-info-box">
        <div class="qr-box">
          <img
            ref="qrImgRef"
            class="qr"
            :src="qrSrc"
            alt="二维码">
          <p class="qr-tip">扫码预约</p>
        </div>
        <div class="logo-box">
          <img class="logo-img" :src="busLogoSrc" alt="">
          <h2 class="bus-name">{{ busName }}</h2>
        </div>
        <p class="time">{{ formData.time }}</p>
        <p class="name">{{ formData.name }}</p>
      </div>
      <ul ref="timetableRef" class="timetable">
        <li class="timetable-row">
          <span v-for="{name,week} in thead" :key="week" class="th-item uni-item">
            <span class="th-week">{{ week }}</span>
            <span class="th-name">{{ name }}</span>
          </span>
        </li>
        <li v-for="(val, key, index) in target" :key="index" class="timetable-row">
          <span class="td-first-col uni-item" :style="bgColorStyle">{{ key.split('_')[0] }}</span>
          <span
            v-for="(subItem, index) in val"
            :key="index"
            class="td-item uni-item"
            :style="subItem? `backgroundColor:${subItem.class_color}`:undefined"
          >
            <template v-if="subItem">
              <span v-show="formData.info.includes('2')">
                <Icon
                  v-for="(_, idx) in subItem.class_level"
                  :key="idx"
                  type="md-star"
                  size="12"
                  color="#F9B663" />
              </span>
              <span style="font-weight:bold;">{{ subItem.class_name || '--' }}</span>
              <span v-show="formData.info.includes('1')">{{ subItem.classroom_name || '--' }}</span>
              <span v-show="formData.info.includes('0')">{{ subItem.coach_name || '--' }}</span>
              <span style="display:flex;align-items:center">
                <Icon
                  type="md-time"
                  size="12"
                  color="#050505"
                  style="margin-top:1px;font-weight:bold;" />
                <span style="padding-left:2px;font-weight:bold;">{{ subItem.class_hour }}min</span>
              </span>
            </template>
          </span>
        </li>
      </ul>
      <p style="white-space:pre-line;font-size:14px;text-shadow: 0px 0px 4px #f3f3f3">{{ formData.tips }}</p>
    </div>

    <div class="setting-wrapper">
      <div class="setting-box">
        <h3 style="margin-bottom: 16px;">课表设置</h3>
        <div>
          <Form
            ref="formRef"
            label-position="top"
            :model="formData"
            :rules="formRules">
            <Form-Item label="课表名称" prop="name">
              <Input
                v-model.trim="formData.name"
                style="max-width:565px"
                :maxlength="16"
                autofocus
                placeholder="请填写" />
            </Form-Item>
            <Form-Item label="课表时间" prop="time">
              <Input
                v-model.trim="formData.time"
                style="max-width:565px"
                :maxlength="30"
                placeholder="请填写" />
            </Form-Item>
            <Form-Item label="课表信息显示" prop="info">
              <CheckboxGroup v-model="formData.info">
                <Checkbox label="0">教练</Checkbox>
                <Checkbox label="1">教室</Checkbox>
                <Checkbox label="2">课程强度</Checkbox>
              </CheckboxGroup>
            </Form-Item>
            <Form-Item label="提示信息" prop="tips">
              <Input
                v-model.trim="formData.tips"
                style="max-width:565px"
                type="textarea"
                :autosize="{minRows:3, maxRows:6}"
                :maxlength="1000"
                placeholder="请填写"
              />
            </Form-Item>
            <Form-Item label="背景图">
              <ul class="bg-img-list">
                <li class="bg-item" @click="handleShowCropper">
                  <span class="add">+</span>
                  <p>图片最佳尺寸：1500*2668</p>
                  <p>图片最大大小：5M</p>
                  <p>格式限制：jpg、png</p>
                </li>
                <li
                  v-for="(t, i) in bgImgOptions"
                  :key="i"
                  class="bg-item"
                  :class="i < defaultImgList.length ? 'default-' + (i+1) : undefined"
                  @click="bgActive=i+1;imgSrc=t">
                  <div class="bg-img-box">
                    <img class="model-img" :src="t" alt="背景模板">
                  </div>
                  <Icon
                    v-if="i >= defaultImgList.length"
                    type="md-close-circle"
                    class="delete-icon"
                    title="删除"
                    @click.native.stop="handleSaveImgUrl(i-defaultImgList.length)" />
                </li>
              </ul>
            </Form-Item>
            <Form-Item>
              <Button type="success" @click="handleToPicture">下载</Button>
            </Form-Item>
          </Form>
        </div>
      </div>
    </div>

    <Modal
      v-model="showCropper"
      title="选择课表背景图"
      footer-hide
      :mask-closable="false"
    >
      <!-- @on-ok="handleConfirm"
      @on-cancel="handleCancel" -->
      <div style="min-height:300px">
        <CropperPlus
          ref="cropperPlusRef"
          width="400px"
          height="400px"
          :maxSize="5"
          :ratio="ratio"
          @input="handleSaveImgUrl"
        >
        </CropperPlus>
      </div>
    </Modal>
  </div>
</template>

<script>
// import Cropper from 'components/form/cropper'
import CropperPlus from 'components/form/cropperPlus'
import html2canvas from 'html2canvas'
import { mapGetters, mapMutations } from 'vuex';
import { gymInfo } from '@/service/getData'
import { getBaseUrl } from './../../utils/config';
// import { formatDate } from 'utils/index.js';

export default {
  name: 'SchedulePreview',
  components: {
    // Cropper,
    CropperPlus
  },
  data() {
    return {
      curBusId: '',
      thead: [
        { name: '时间',  week: 'TIME' },
        { name: '星期一', week: 'MON.' },
        { name: '星期二', week: 'TUE.' },
        { name: '星期三', week: 'WED.' },
        { name: '星期四', week: 'THU.' },
        { name: '星期五', week: 'FRI.' },
        { name: '星期六', week: 'SAT.' },
        { name: '星期天', week: 'SUN.' },
      ],
      target: {
        // '09:00': [
        //   { beg_time: '开始时间', class_level: '2',  class_name: '团课名称', classroom_name: '教室名称', coach_name: '教练名称', class_hour: '时长', class_color: '' }
        // ],
      },
      formData: {
        name: '团课课表',
        time: '', // 课表时间
        info: ['0', '1', '2'], // 课表信息显示
        tips: '1. 请您根据自己的身体状况选择适合的强度的课程。\n2. 星星数量表示课程强度，星星数量越多难度等级越高。',
      },
      formRules: {
        name: [
            { required: true, message: '课表名称不能为空', trigger: 'change' }
        ],
      },
      bgActive: 1,
      showCropper: false,
      ratio: 0.562, // 1500/2668
      defaultImgList: [
        require('../../assets/img/schedule_top_01.png'),
        require('../../assets/img/schedule_top_02.png'),
        require('../../assets/img/schedule_top_03.png'),
      ],
      cloudImgList: [],
      busName: '',
      busLogoSrc: '',
      // qrSrc: getBaseUrl() + '/Web/ClassMark/member_class_mark_qrcode',
      qrSrc: '',
      imgSrc: '', // 课表背景图
    }
  },
  computed: {
    ...mapGetters(['busId']),
    bgImgOptions() {
      return this.defaultImgList.concat(this.cloudImgList)
    },
    // 课表背景样式属性
    bgStyles() {
      const bgObj = {
        'background-position': 'left top',
        'background-size': 'contain',
        'background-repeat': 'no-repeat',
      };

      if(this.bgActive > this.defaultImgList.length) {
        bgObj['background-image'] = `url(${this.imgSrc})`;
        bgObj['background-size'] = 'cover';
        bgObj['background-position'] = 'left top';
      }else {
        switch (this.bgActive) {
          case 1:
            const url1 = 'linear-gradient(#A0DAEE, #F0E7F3, #F0E7F3, #95F3C9)';
            const url2 = require('../../assets/img/schedule_top_01.png');
            bgObj['background-image'] = `url(${url2}),${url1}`;
            bgObj['background-color'] = '#A0DAEE';
            bgObj['background-position'] = '0 -56px,left top';
            break;
          case 2:
            bgObj['background-image'] = `url(${require('../../assets/img/schedule_top_02.png')})`;
            bgObj['background-color'] = '#FBF4F0';
            bgObj['background-position'] = '0 -50px';
            break;
          case 3:
            bgObj['background-image'] = `url(${require('../../assets/img/schedule_top_03.png')})`;
            bgObj['background-color'] = '#EDF0F8';
            bgObj['background-position'] = '0 -102px';
            break;
        }
      }
      return bgObj;
    },
    // 课表时间背景色
    bgColorStyle() {
      let color = 'unset';
      switch (this.bgActive) {
        case 1:
          color = '#DAF3FF';
          break;
        case 2:
          color = '#FFEEE5';
          break;
        case 3:
          color = '#E7E4FC';
          break;
      }
      return `backgroundColor:${color}`;
    }
  },

  created() {
    this.curBusId = history.state.selectedBusId || this.$route.params.selectedBusId || this.busId
    // 获取场馆信息
    gymInfo(this.curBusId).then(res => {
      const { errorcode, data } = res.data
      if (errorcode == 0) {
        this.busLogoSrc = data[0] && data[0].thumb;
        this.busName = data[0] && data[0].name;
      }else {
        this.$Message.error(res.data.errormsg);
      }
    })
    this.getBusinessPic()

    /* 处理二维码图片转换为base64用于最后下载
    canvas.toDataURL时需要后端支持跨域，否则会报Failed to execute 'toDataURL' on 'HTMLCanvasElement': Tainted canvases may
    调整为后端返回图片路径 */
    // this.$nextTick(() => {
    //   const imgRef = this.$refs.qrImgRef;
    //   const canvas = document.createElement("canvas");
    //   canvas.width = canvas.height = 430;
    //   let ctx = canvas.getContext("2d");
    //   imgRef.onload = () => {
    //     ctx.drawImage(imgRef, 0, 0, 430, 430);
    //     const dataURL = canvas.toDataURL("image/jpg");
    //     this.qrSrc = dataURL;
    //   }
    // })
    this.$service.post('/Web/ClassMark/member_class_mark_qrcode', {bus_id: this.curBusId}).then(res => {
      if(res.data.errorcode == 0) {
        this.qrSrc = 'data:image/png;base64,' + res.data.data
      }else {
        this.$Message.error(res.data.errormsg);
      }
    })

    // 拿到排课数据
    const { scheduleList, weekDateString } = history.state || this.$route.params;
    const list = JSON.parse(scheduleList)
    this.formData.time = weekDateString;

    if(list && list.length) {
      // 最终数据
      const target = {};
      // 处理最终数据结构
      const fn = (val, num) => {
        const [time, wIdx] = [val.beg_time, val.weekIdx];
        const k = `${time}_${num}`;
        if(!target[k]) { // 没有创建当前时间的数据
          target[k] = Array(7);
          target[k][wIdx] = val;
        }else {
          if(!target[k][wIdx]) { // 当前时间的第wIdx有空位
            target[k][wIdx] = val;
          }else { // 创建第num个当前时间数据
            fn(val, ++num)
          }
        }
      }

      list.map((arr, idx) => {
        // 准备数据
        arr.forEach(t => {
          t.class_level = +t.class_level;
          t.weekIdx = idx;
          t.timestamp = new Date(`1970-1-1 ${t.beg_time}`).getTime()
        })
        return arr;
      })
      .flat()
      .sort((a, b) => a.timestamp - b.timestamp)
      .forEach(v => fn(v, 0));

      this.target = target;
    }

    const localData = JSON.parse(localStorage.getItem('SCHEDULE_PREVIEW'))
    if (localData && localData[this.busId]) {
      const { formData } = this
      for (const [key, value] of Object.entries(localData[this.busId])) {
        formData[key] = value
      }
    }

    this.$nextTick(() => {
      const vipMainCon = document.getElementById('vipMainCon');
      if(vipMainCon) vipMainCon.style['padding'] = 0;
    })
  },
  destroyed() {
    const vipMainCon = document.getElementById('vipMainCon');
    if(vipMainCon) vipMainCon.style['padding'] = "";

    const { name, info, tips } = this.formData
    localStorage.setItem('SCHEDULE_PREVIEW', JSON.stringify({
      [this.busId]: {
        name,
        info,
        tips
      }
    }))
  },

  methods: {
    ...mapMutations(['SET_LOADING', 'SET_LOADING_TEXT']),
    // 获取场馆上传的自定义背景图
    getBusinessPic() {
      this.$service.post('/Web/BusinessPic/get_business_pic', { type: 1, bus_id: this.curBusId }).then(res => {
        if (res.data.errorcode === 0) {
          const { data } = res.data
          if (data && Array.isArray(data.pic_url)) {
            this.cloudImgList = data.pic_url
          }
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    // 打开背景图选择弹窗
    handleShowCropper() {
      if (this.cloudImgList.length >= 5) {
        this.$Message.warning({
          content: '请删除已上传的图片，云端最多可存5张',
          duration: 2.5
        })
        return false
      }
      const timetableRef = this.$refs['timetableRef']
      if(timetableRef) {
        const ratio = timetableRef.clientWidth / timetableRef.clientHeight;
        this.ratio = Number(ratio.toFixed(3))
      }
      this.showCropper = true;
    },
    // 确认选择背景图
    handleConfirm() {
      const cropperPlusRef = this.$refs['cropperPlusRef'];
      this.imgSrc = cropperPlusRef.cropImage();
      cropperPlusRef.cancelUpload()
    },
    // 取消选择背景图
    handleCancel() {
      this.$refs['cropperPlusRef'].cancelUpload()
    },
    // 保存上传/删除后的图片地址数据
    handleSaveImgUrl(path) {
      const imgs = [...this.cloudImgList]
      // 数字删除，字符串添加
      if (typeof path === 'number') {
        imgs.splice(path, 1)
      } else {
        this.showCropper = false
        this.$refs['cropperPlusRef'].cropImg = ''
        imgs.push(path)
      }
      const params = {
        type: 1, // 图片类型：1课表背景
        pic_url: imgs // 图片数组 （不超过5张）
      }
      this.$service.post('/Web/BusinessPic/set_business_pic', params).then(res => {
        if (res.data.errorcode === 0) {
          this.$Message.success(typeof path === 'number' ? '删除成功' : '添加成功');
          this.getBusinessPic()
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    // 生成课表图片
    handleToPicture() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          const el = this.$refs.timetableRef

          if(el) {
            this.handleSetLoading(true, '图片生成中，请稍等...')

            const options = {
              // width: 1500,
              // height: 2668,
              scale: 2,
              useCORS: true, // 开启跨域配置
              allowTaint: true, // 允许跨域图片
              taintTest: false, // 是否在渲染前测试图片
            }
            html2canvas(el, options).then(canvas => {
              // _${formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss');}
              /*
              const downRef = document.createElement('a')
              downRef.href = canvas.toDataURL();
              downRef.download = `${this.formData.name}_${this.formData.time}`;
              downRef.click();
              */

              /* 兼容其他浏览器问题 like QQ */
              // 截取base64的数据内容（去掉前面的描述信息，类似这样的一段：data:image/png;base64,）并解码为2进制数据
              const binStr = atob(canvas.toDataURL().split(',')[1]);
              // 获取解码后的二进制数据的长度，用于后面创建二进制数据容器
              let num = binStr.length;
              // 创建一个Uint8Array类型的数组以存放二进制数据
              const u8arr = new Uint8Array(num);
              // 将二进制数据存入Uint8Array类型的数组中
              while (num--) {
                u8arr[num] = binStr.charCodeAt(num);
              }
              // 创建blob对象
              const blob = new Blob([u8arr])

              const downRef = document.createElement('a')
              downRef.href = URL.createObjectURL(blob);
              downRef.download = `${this.formData.name}_${this.formData.time}.png`;
              downRef.onclick = function() {
                requestAnimationFrame(function() {
                    URL.revokeObjectURL(downRef.href);
                });
              };
              downRef.click();

            }).finally(setTimeout(this.handleSetLoading, 2000));

          }else {
            console.log('err: not find formRef');
          }
        }else {
          this.$Message.error('请完善必要信息')
        }
      })
    },

    handleSetLoading(bool=false, cont='') {
      this.SET_LOADING(bool)
      this.SET_LOADING_TEXT(cont)
    }
  },
}
</script>

<style lang="less" scoped>
.schedule-container {
  overflow: auto;
  display: flex;
  padding: 40px 48px;
  flex: 1;

  .timetable-wrapper {
    padding: 42px/2 52px/2 80px/2;
    flex: 1;
    max-width: 1500px / 2;
    // min-width: 1500px / 2;
    min-height: 1334px;
    color: #212121;
    background-color: #fff;
    border-radius: 3px;
    .top-info-box {
      position: relative;
      margin-top: 20px;
      margin-bottom: 1px;
      height: 590px / 2;
    }
    .timetable {
      margin-bottom: 65px / 2;
      width: 100%;
    }
  }

  .setting-wrapper {
    position: relative;
    margin-left: 20px;
    // flex: 1;
    min-width: 440px;
    max-width: 750px;
    background-color: #fff;
    border: 1px solid #e6e6e7;
    border-radius: 3px;
    .setting-box {
      // position: sticky;
      // top: 0;
      // right: 0;
      padding: 20px;
    }
  }
}

.timetable-wrapper {
  .top-info-box {
    .qr-box {
      position: absolute;
      top: 0;
      right: 0;
      .qr {
        display: block;
        margin-bottom: 16px / 2;
        width: 160px / 2;
        height: 160px / 2;
        box-shadow: 0 0 9px 0 rgba(209, 209, 209, 0.33);
      }
      .qr-tip {
        text-align: center;
        text-shadow: 0px 0px 4px #f3f3f3;
        font-size: 28px / 2;
        font-weight: bold;
      }
    }

    .logo-box {
      display: flex;
      align-items: center;
      margin-bottom: 240px / 2;
      .logo-img {
        width: 122px / 2;
        height: 122px / 2;
        border-radius: 50%;
        box-shadow: 0 0 9px 0 rgba(209, 209, 209, 0.33);
      }
      .bus-name {
        margin-left: 10px;
        text-shadow: 0px 0px 4px #f3f3f3;
      }
    }

    .name {
      letter-spacing: 2px;
      font-size: 80px / 2;
      font-weight: 800;
    }
    .time {
      font-size: 40px / 2;
    }
    .name, .time {
      text-shadow: 0px 0px 4px #f3f3f3;
    }
  }

  .timetable-row {
    display: flex;
  }
  .uni-item {
    margin-right: 10px / 2;
    margin-bottom: 10px / 2;
    width: 166px / 2;
    font-family: Source Han Sans CN;
    background-color: #fff;
    // &:first-child {
    //   background-color: #DAF3FF;
    // }
    &:last-child {
      margin-right: 0;
    }
    box-shadow: 0 0 9px 0 rgba(209, 209, 209, 0.33);
  }

  .th-item {
    position: relative;
    height: 77px / 2;
    text-align: center;

    .th-week {
      position: absolute;
      top: 0;
      right: 0;
      line-height: 1;
      font-size: 28px / 2;
      font-family: DIN Black;
      color: #E6E6E6;
    }

    .th-name {
      line-height: 77px / 2;
      font-size: 28px / 2;
      font-weight: bold;
    }
  }

  .td-item {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 8px/2 12px/2 8px/2;
    min-height: 203px / 2;
    line-height: 1.2;
  }
  .td-first-col {
    line-height: 203px / 2;
    text-align: center;
    font-weight: bold;
    font-size: 30px / 2;
  }

}

.setting-wrapper {
  .bg-img-list {
    display: flex;
    flex-wrap: wrap;
  }

  .bg-item {
    position: relative;
    margin-bottom: 20px;
    margin-right: 20px;
    width: 175px;
    height: 110px;
    line-height: 1.3;
    border: 1px solid #e6e6e7;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      box-shadow: 0 0 9px 0 rgba(209, 209, 209, 0.33);
    }

    &:first-child {
      padding: 10px;
      border-style: dashed;
    }

    &.default-1 {
      background-color: #A0DAEE;
    }
    &.default-2 {
      background-color: #FBF4F0;
    }
    &.default-3 {
      background-color: #EDF0F8;
    }

    .add {
      display: block;
      text-align: center;
      font-size: 30px;
    }

    input[type="file"] {
      display: none;
    }

    .model-img {
      width: 100%;
    }

    .bg-img-box {
      overflow: hidden;
      width: 100%;
      height: 100%;
    }

    .delete-icon {
      cursor: pointer;
      position: absolute;
      right: -9px;
      top: -9px;
      font-size: 18px;
      color: #d9544f;
    }
  }
}

</style>
