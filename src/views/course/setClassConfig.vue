<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>配置</h2>
    </div>
    <div class="form-box-con">
      <Form
        v-if="saveForm"
        ref="saveFormRef"
        class="save-form"
        :model="saveForm"
        :label-width="100">
        <FormItem label="课程">
          <Select
            v-model="saveForm.courseId"
            style="width:333px;"
            disabled
            filterable
          >
            <Option v-for="item in courseList" :key="item.class_id" :value="item.class_id">{{ item.class_name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="日期">
          <DatePicker
            v-model="saveForm.courseDate"
            type="date"
            :disabled="!!saveForm.id"
            placeholder="请选择排课日期"
            style="width:333px"
            :editable="false"></DatePicker>
        </FormItem>
        <FormItem label="时间">
          <div>
            <TimePicker
              v-model="saveForm.courseBeginTime"
              :steps="[1, 5]"
              format="HH:mm"
              placeholder="开始时间"
              style="width: 156px"
              @on-change="handleBeginTimeChange"></TimePicker>
            至
            <TimePicker
              v-model="saveForm.courseEndTime"
              :steps="[1, 5]"
              format="HH:mm"
              placeholder="结束时间"
              style="width: 156px"
              @on-change="handleEndTimeChange"></TimePicker>
          </div>
        </FormItem>
        <FormItem v-if="!is_free" label="可预约人数">
          <InputNumber
            v-model="saveForm.appointment"
            :max="999"
            :min="1"
            style="width:333px"></InputNumber>
        </FormItem>
        <FormItem label="教练">
          <Select
            v-model="saveForm.coachId"
            style="width:333px;"
            filterable>
            <Option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id">{{ item.coach_name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="教室">
          <Select
            v-model="saveForm.roomId"
            style="width:333px;"
            filterable>
            <Option v-for="item in roomList" :key="item.id" :value="item.id">{{ item.classroom_name }}</Option>
          </Select>
        </FormItem>
      </Form>
      <Form
        ref="formRef"
        class="form"
        :model="formData"
        :rules="formRules"
        :label-width="90">
        <FormItem v-if="!saveForm" label="课程名称">
          <div>{{ classNames }}</div>
        </FormItem>

        <FormItem label="选择卡种" prop="config">
          <Button type="success" @click="handleShowSelectModal">添加</Button>
          <Button type="text" style="margin-left:16px;" @click="handleShowImportConfigModal">导入配置</Button>
        </FormItem>
        <FormItem>
          <div class="flex-center">
            <span>已添加{{ configType === 1 ? '标签' : '卡种' }}数【{{ configNum }}】，请配置用卡规则（输入0表示不扣次、不扣费、或可预约的人数无上限）</span>
            <Button type="text" style="margin-left:8px;" @click="handleShowBatchModal">批量填充规则</Button>
          </div>
        </FormItem>
        <template v-if="formData.config.ms.length">
          <FormItem v-if="configType === 1">
            <Card
              class="card-container"
              title="会籍卡"
              dis-hover>
              <ClassConfigFormItem
                v-for="(item, index) in formData.config.ms"
                :key="item.myCustom.configTypeId"
                :class="{ width_100: configType === 1 }"
                :configType="configType"
                tagType="ms"
                :item="item"
                :itemProps="'config.ms.' + index"
                @delete-item="handleDeleteConfigItem($event, 'ms', index)"
                @show-cards="handleShowCardsModal(item)"
              />
            </Card>
          </FormItem>
          <template v-else>
            <template v-for="({ title, list }, key) of msFilter">
              <FormItem v-if="list.length" :key="key">
                <Card class="card-container" :title="title" dis-hover>
                  <ClassConfigFormItem
                    v-for="item in list"
                    :key="item.myCustom.configTypeId"
                    :class="{ width_100: configType === 1 }"
                    :configType="configType"
                    tagType="ms"
                    :item="item"
                    :itemProps="`config.ms.${item.msIndex}`"
                    @delete-item="handleDeleteConfigItem($event, 'ms', item.msIndex)"
                    @show-cards="handleShowCardsModal(item)"
                  />
                </Card>
              </FormItem>
            </template>
          </template>
        </template>
        <FormItem v-if="formData.config.pt.length">
          <Card class="card-container" title="私教课" dis-hover>
            <ClassConfigFormItem
              v-for="(item, index) in formData.config.pt"
              :key="item.myCustom.configTypeId"
              :class="{ width_100: configType === 1 }"
              :configType="configType"
              tagType="pt"
              :item="item"
              :itemProps="'config.pt.' + index"
              hasName
              hasDelete
              @delete-item="handleDeleteConfigItem($event, 'pt', index)"
              @show-cards="handleShowCardsModal(item)"
            />
          </Card>
        </FormItem>
        <FormItem v-if="formData.config.swim.length">
          <Card class="card-container" title="泳教课" dis-hover>
            <ClassConfigFormItem
              v-for="(item, index) in formData.config.swim"
              :key="item.myCustom.configTypeId"
              :class="{ width_100: configType === 1 }"
              :configType="configType"
              tagType="swim"
              :item="item"
              :itemProps="'config.swim.' + index"
              @delete-item="handleDeleteConfigItem($event, 'swim', index)"
              @show-cards="handleShowCardsModal(item)"
            />
          </Card>
        </FormItem>
        <FormItem v-if="saveForm">
          <Checkbox
            v-model="update_class"
            true-value="1"
            false-value="0"
          >
            将当前配置保存到课程中
          </Checkbox>
        </FormItem>
        <FormItem>
          <div class="buttons">
            <Button type="success" @click="handleSubmit">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </FormItem>
      </Form>
    </div>

    <Modal v-model="importConfigModal" title="导入配置">
      <div style="margin-bottom: 12px;">
        <Input
          v-model="importConfigSearch"
          placeholder="输入课种名称查询"
          clearable
          @on-change="handleSearchImportConfig" />
      </div>
      <Table
        v-if="importConfigModal"
        :columns="importConfigColumns"
        :data="importConfigList"
        :max-height="512"
        disabled-hover
      />
      <div slot="footer"></div>
    </Modal>

    <Modal
      v-model="batchConfigModal"
      title="批量填充用卡规则"
      :width="configType===1 ? 800 : 550"
      :mask-closable="false">
      <Alert style="display: inline-block;">输入0表示不扣次、不扣费、或可预约的人数无上限</Alert>
      <Form
        ref="batchFormRef"
        :model="batchFormData"
        label-position="left"
        :label-width="100">
        <FormItem v-for="(item, index) in batchFormData.config[configType]" :key="index" :label="item.label">
          <ClassConfigFormItem
            style="margin-bottom:0;"
            :configType="configType"
            :tagType="item.tagType"
            :item="item"
            :itemProps="`config.${configType}.${index}`"
            :hasName="false"
            :hasDelete="false"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
        <Button type="success" @click="handleFillConfig">保存</Button>
        <Button @click="batchConfigModal=false">取消</Button>
      </div>
    </Modal>

    <CardDetailModal
      :show.sync="showCardModal"
      :showData="showCardData"
    />

    <SelectModal
      :show.sync="selectModal"
      :config-type="configType"
      :selected="selected"
      :tabs="[1, 2]"
      :current-bus-id="selBusId"
      :custom-data-fn="handleCustomData"
      :need-pt-limit="false"
      :hide-price="!$route.params.courseScheduleId"
      @on-confirm="handleUpdateSelected"
    />
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { debounce } from 'lodash-es';
  import ClassConfigFormItem from './components/ClassConfigFormItem'
  import SelectModal from '@/components/cardSelect'
  import CardDetailModal from '@/components/cardSelect/cardDetailModal'
  import { CONFIG_TYPE_KEYS, CARD_TYPE_DATA } from '@/components/cardSelect//constants';


  export default {
    name: 'SetClassConfig',
    components: {
      ClassConfigFormItem,
      SelectModal,
      CardDetailModal
    },

    data() {
      return {
        /* 初始数据 */
        classId: this.$route.params.classId || '', // 需要设置的课种id
        classNames: this.$route.params.className || '未获取到课程名', // 需要设置的课种名称
        courseScheduleId: this.$route.params.courseScheduleId || '', // 需要编辑的排课id
        selBusId: this.$route.params.selBusId || '', // 需要编辑的排课对应bus_id
        configType: this.$route.params.configType || 1, // 1标签 2卡种 3卡类型（此处未使用）
        isConfig: this.$route.params.isConfig == '1', // 课种是否已配置
        /* 排课的初始数据，原名带入 */
        is_free: this.$route.params.is_free !== undefined ? this.$route.params.is_free : false, // true操课 false团课，此处目前只会有团课
        maxReserveNumber: this.$route.params.maxReserveNumber !== undefined ? +this.$route.params.maxReserveNumber : 999,
        saveForm: this.$route.params.saveForm || null,
        courseList: this.$route.params.courseList || [],
        coachList: this.$route.params.coachList || [],
        roomList: this.$route.params.roomList || [],
        update_class: '0', // 是否与课程同时更新 0否 1是

        formData: {
          config: {
            ms: [],
            pt: [],
            swim: [],
          }
        },
        formRules: {},

        /* 导入配置 */
        importConfigModal: false,
        importConfigSearch: '',
        importConfigList: [],
        importConfigColumns: [
          {
            title: '课种名称',
            key: 'class_name',
            tooltip: true
          },
          {
            title: '操作',
            render: (h, { row }) => {
              return <div>
                <i-button
                  type="text"
                  style="line-height:1;min-height:unset;"
                  onClick={() => { this.handleImportConfig(row.id) }}
                >导入</i-button>
              </div>
            }
          }
        ],

        /* 批量填充用卡规则 */
        batchConfigModal: false,
        batchFormData: {
          config: {
            1: [ // 标签
              { tagType: 'ms', label: '会籍卡标签', todeduct_type: '1', todeduct_c: null, todeduct_y: null, maxresv_num: null },
              { tagType: 'pt', label: '私教课标签', todeduct_type: '2', todeduct_c: null, maxresv_num: null },
              { tagType: 'swim', label: '泳教课标签', todeduct_type: '3', todeduct_c: null, maxresv_num: null },
            ],
            2: [ // 卡种
              { tagType: 'ms', label: '会籍卡-次卡', todeduct_type: '1', todeduct_type_detail: '2', todeduct_c: null, maxresv_num: null },
              { tagType: 'ms', label: '会籍卡-储值卡', todeduct_type: '1', todeduct_type_detail: '3', todeduct_y: null, maxresv_num: null },
              { tagType: 'pt', label: '私教课', todeduct_type: '2', todeduct_type_detail: '0', todeduct_c: null, maxresv_num: null },
              { tagType: 'swim', label: '泳教课', todeduct_type: '3', todeduct_type_detail: '0', todeduct_c: null, maxresv_num: null },
            ]
          }
        },

        /* 包含详情 */
        showCardModal: false,
        showCardData: {
          // select_type: 1, // 1标签 3卡类型
          // type_id: '', // 具体想获取的数据id
          // is_pt_time_limit_card: '' // 是否为包月：1是 0否
          // query_limit: ['is_pt_time_limit_card,pt'] // 传这个值就不会返回私教包月
        },

        /* 卡种选择器 */
        selectModal: false,
        selected: [],

        // 是否支持在线购买
        support_online: false
      }
    },
    computed: {
      ...mapState(['busId']),
      configNum() {
        const { ms, pt, swim } = this.formData.config
        return ms.length + pt.length + swim.length
      },
      msFilter() {
        const dateCards = [], countCards = [], prepaidCards = []
        if (this.configType === 2) {
          this.formData.config.ms.forEach((v, i) => {
            v.msIndex = i
            v.card_type_id = v.card_type_id || v.todeduct_type_detail
            switch (+v.card_type_id) {
              case 1:
                dateCards.push(v)
                break;
              case 2:
                countCards.push(v)
                break;
              case 3:
                prepaidCards.push(v)
                break;
            }
          })
        }
        return {
          1: {
            title: '会籍卡-期限卡',
            list: dateCards,
          },
          2: {
            title: '会籍卡-次卡',
            list: countCards,
          },
          3: {
            title: '会籍卡-储值卡',
            list: prepaidCards,
          },
        }
      },
    },

    watch: {
      batchConfigModal(val) {
        !val && Object.assign(this.$data.batchFormData.config, this.$options.data.call(this).batchFormData.config)
      },
      importConfigModal(val) {
        val && this.getConfigClassList()
      }
    },
    created() {
      if ((this.classId || this.courseScheduleId) && this.isConfig) {
        this.getClassConfig()
      } else if (!this.classId && !this.courseScheduleId) {
        // this.$Message.error('未获取到课程数据！')
        this.$router.replace({ name: '团课管理' })
      }
      // 是否支持在线购买  课种管理 -> 配置/未配置
      if( this.$route.params ) {
        this.support_online = this.$route.params.support_online || false
      }
    },

    methods: {
      // 获取配置 团课/团课排课
      getClassConfig(id) {
        const isCourseSchedule = this.courseScheduleId && !id
        let url = `/Web/OpenClass/get_open_class_detail?class_id=${id || this.classId}&bus_id=${this.selBusId}`
        if(isCourseSchedule) {
          url = `/Web/CourseSchedule/get_course_schedule_config_detail?bus_id=${this.selBusId}&course_schedule_id=${this.courseScheduleId}`
        }

        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = isCourseSchedule ? res.data.data.data : res.data.data
            const { config_type, list } = data
            if (config_type && Array.isArray(list)) {
              this.configType = +config_type === 0 ? 1 : +config_type // 1标签 2卡种 3卡类型
              const { config } = this.formData
              const { handleCustomData } = this
              list.forEach(v => {
                if (v.type_id || v.config_type_id) {
                  const data = v.apply_card
                  const item = {
                    config_type_id: v.type_id || v.config_type_id,
                    config_type_name: data.config_type_name,
                    todeduct_type: data.todeduct_type, // 配置的类型 1会籍卡 2私教卡 3泳教卡
                    todeduct_type_detail: data.todeduct_type_detail, //  TODO私教包月？？？配置的类型具体 1期限卡 2次卡 3储值卡 4私教课 5泳教课
                    todeduct_c: data.todeduct_c === undefined ? undefined : +data.todeduct_c, // 次/人
                    todeduct_y: data.todeduct_y === undefined ? undefined : +data.todeduct_y, // 元/人
                    maxresv_num: data.maxresv_num === undefined ? undefined : +data.maxresv_num, // 限制人数
                    ...v
                  }
                  handleCustomData(item)
                  config[CARD_TYPE_DATA[data.todeduct_type].key].push(item)
                }
              })
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 获取已配置卡课列表
      getConfigClassList() {
        const url = `/Web/OpenClass/get_open_class_config_list?class_name=${this.importConfigSearch}&bus_id=${this.selBusId || this.busId}`
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const { data } = res.data
            if (Array.isArray(data)) {
              this.importConfigList = data
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 打开导入配置选项窗口
      handleShowImportConfigModal() {
        this.importConfigModal = true;
      },

      handleSearchImportConfig: debounce(function() {
        this.getConfigClassList()
      }, 400),
      // 导入
      handleImportConfig(id) {
        this.$Modal.confirm({
           title: '导入确认',
          content: '导入后将会覆盖页面数据，确认导入？',
          onOk: () => {
            this.importConfigModal = false;
            this.formData.config = {
              ms: [],
              pt: [],
              swim: [],
            }
            this.getClassConfig(id)
          },
        })
      },
      // 打开批量填充用卡规则窗口
      handleShowBatchModal() {
        // const { ms, pt, swim } = this.formData.config
        // const list = [...ms, ...pt, ...swim]
        // if (list.length === 0) {
        //   return this.$Message.error("请先选择卡种！");
        // }
        this.batchConfigModal = true;
      },
      // 填充
      handleFillConfig() {
        // this.$refs.batchFormRef.validate(valid => {
          // if (valid) {
            const configList = this.batchFormData.config[this.configType]
            const { configType } = this
            const { ms, pt, swim } = this.formData.config
            const targetList = [...ms, ...pt, ...swim]
            const foo = (item, data) => {
              if (item.myCustom.todeduct_type == data.todeduct_type) {
                if (item.myCustom.todeduct_type != '1') {
                  data.todeduct_c !== null && (item.todeduct_c = data.todeduct_c)
                  data.maxresv_num !== null && (item.maxresv_num = data.maxresv_num)
                } else {
                  data.todeduct_c !== null && (item.todeduct_c = data.todeduct_c)
                  data.todeduct_y !== null && (item.todeduct_y = data.todeduct_y)
                  data.maxresv_num !== null && (item.maxresv_num = data.maxresv_num)
                }
                return true
              }
               return false
            }
            const bar = (item, data) => {
              if ((item.myCustom.todeduct_type_detail == data.todeduct_type_detail) && (item.myCustom.todeduct_type == data.todeduct_type)) {
                switch (+item.myCustom.todeduct_type_detail) {
                  case 0:
                  case 1:
                  case 2:
                    data.todeduct_c !== null && (item.todeduct_c = data.todeduct_c)
                    data.maxresv_num !== null && (item.maxresv_num = data.maxresv_num)
                    break;
                  case 3:
                    data.todeduct_y !== null && (item.todeduct_y = data.todeduct_y)
                    data.maxresv_num !== null && (item.maxresv_num = data.maxresv_num)
                    break;
                }
                return true
              }
              return false
            }
            targetList.forEach(t => {
              for (let i = 0; i < configList.length; i++) {
                // console.log(i, '---', t, configList[i]);
                const isBreak = (configType === 1 ? foo : bar)(t, configList[i])
                if (isBreak) break;
              }
            })
            this.batchConfigModal = false
            this.$refs.formRef.fields.forEach(field => {
              field.validateState === 'error' && field.validate('change');
            })

          // }
        // })
      },
      // 打开卡种选择器
      handleShowSelectModal() {
        const { ms, pt, swim } = this.formData.config
        this.selected = [
          ...ms,
          ...pt,
          ...swim
        ]
        this.selectModal = true;
      },
      handleCustomData(item, that = this) {
        try {
          const value = that.tabValue || this.configType
          item.myCustom = {
            name: item.config_type_name || item[CONFIG_TYPE_KEYS[value].name], // 标签/卡种名称
            configType: value, // 1标签 2卡种
            configTypeId: item.config_type_id || item[CONFIG_TYPE_KEYS[value].id]  // 标签id 卡种card_id
          }
          // todeduct_type配置的类型 1会籍卡 2私教卡 3泳教卡 当tabValue为1时直接就是标签的type值，为2时根据card_type_id判断
          // todeduct_type_detail配置的类型具体 当tabValue为1时，值为0，config_type_id为2时，值为1期限卡 2次卡 3储值卡 todeduct_type2,3时值为0
          switch (value) {
            case 1: {
              const type = item.todeduct_type || (item.type === '2' ? '1' : item.type === '1' ? '2' : '3')
              item.myCustom.typeName = CARD_TYPE_DATA[type].name
              item.myCustom.typeKey = CARD_TYPE_DATA[type].key
              item.myCustom.typeValue = type
              item.myCustom.todeduct_type = type === '1' ? '1' : type === '2' ? '2' : '3'
              item.myCustom.todeduct_type_detail =  '0'
              break;
            }
            case 2: {
              let cardTypeId = +item.card_type_id
              if (item.card_type_id === undefined) { // 从选择器获取到的有，从已配置数据获取没有
                const todeductType = +(item.todeduct_type || item.apply_card.todeduct_type)
                const todeductTypeDetail = +(item.todeduct_type_detail || item.apply_card.todeduct_type_detail)
                cardTypeId = todeductTypeDetail !== 0 ? todeductTypeDetail : todeductType === 2 ? 4 : 5
              }
              const type = [1, 2, 3].includes(cardTypeId) ? '1' : cardTypeId === 4 ? '2' : '3'
              item.myCustom.typeName = CARD_TYPE_DATA[type].name
              item.myCustom.typeKey = CARD_TYPE_DATA[type].key
              item.myCustom.typeValue = type
              item.myCustom.todeduct_type = type === '1' ? '1' : type === '2' ? '2' : '3'
              item.myCustom.todeduct_type_detail = [1, 2, 3].includes(cardTypeId) ?cardTypeId : '0'
              break;
            }
          }
        } catch (error) {
          console.log('handleCustomData', error);
        }
      },
      handleUpdateSelected({ tabActive, list }) {
        this.configType = +tabActive
        const { config } = this.formData
        const newConfig = {
          ms: [],
          pt: [],
          swim: []
        }
        // 处理选中的数据
        list.forEach(v => {
          const target = config[v.myCustom.typeKey]
          const index = target.findIndex(k => (k.config_type_id || k[CONFIG_TYPE_KEYS[tabActive].id]) === v.myCustom.configTypeId)
          if (index !== -1) {
            newConfig[v.myCustom.typeKey].push(target[index])
          } else {
            if (tabActive == 1) {
              if (v.myCustom.todeduct_type != '1') {
                v.todeduct_c = v.todeduct_c === undefined ? null : v.todeduct_c
                v.maxresv_num = v.maxresv_num === undefined ? null : v.maxresv_num
              } else {
                v.todeduct_c = v.todeduct_c === undefined ? null : v.todeduct_c
                v.todeduct_y = v.todeduct_y === undefined ? null : v.todeduct_y
                v.maxresv_num = v.maxresv_num === undefined ? null : v.maxresv_num
              }
            } else {
              switch (+v.myCustom.todeduct_type_detail) {
                case 0:
                case 1:
                case 2:
                  v.todeduct_c = v.todeduct_c === undefined ? null : v.todeduct_c
                  v.maxresv_num = v.maxresv_num === undefined ? null : v.maxresv_num
                  break;
                case 3:
                  v.todeduct_y = v.todeduct_y === undefined ? null : v.todeduct_y
                  v.maxresv_num = v.maxresv_num === undefined ? null : v.maxresv_num
                  break;
              }
            }
            newConfig[v.myCustom.typeKey].push(v)
          }
        })
        this.formData.config = newConfig
      },
      // 打开标签包含详情窗口
      handleShowCardsModal(item) {
        const data = {
          select_type: this.configType, // 1标签 2卡种 3卡类型
          type_id: item.myCustom.configTypeId, //  具体想获取的数据id
        }
        if (this.configType == 1) {
          data.query_limit = ['is_pt_time_limit_card,pt']
        }

        this.showCardData = data
        this.showCardModal = true
      },

      handleDeleteConfigItem(item, key, index) {
        this.formData.config[key].splice(index, 1)
      },

      handleSubmit() {
        this.saveForm ? this.handleSaveFormData() : this.handleSaveConfig()
      },
      // 保存配置数据
      handleSaveConfig() {
        const { ms, pt, swim } = this.formData.config
        const list = [...ms, ...pt, ...swim]
        if (!this.support_online && list.length === 0) {
          return this.$Message.error("请添加配置！");
        }
        this.$refs.formRef.validate(valid => {
          if (valid) {
            const { classId, configType } = this
            const apply_card = list.map(v => {
              const obj = {
                config_type_id: v.config_type_id || v.myCustom.configTypeId,
                config_type_name: v.config_type_name || v.myCustom.name,
                todeduct_type: v.todeduct_type || v.myCustom.todeduct_type, // 配置的类型 1会籍卡 2私教卡 3泳教卡
                todeduct_type_detail: v.todeduct_type_detail || v.myCustom.todeduct_type_detail, //  私教包月？配置的类型具体 1期限卡 2次卡 3储值卡 4私教课 5泳教课
                todeduct_c: v.todeduct_c === undefined ? undefined : v.todeduct_c, // 次/人
                todeduct_y: v.todeduct_y === undefined ? undefined : v.todeduct_y, // 元/人
                maxresv_num: v.maxresv_num === undefined ? undefined : v.maxresv_num // 限制人数
              }
              if (configType == 1) {
                if (v.myCustom.todeduct_type != '1') {
                  delete obj.todeduct_y
                }
              } else {
                switch (+obj.todeduct_type_detail) {
                  case 1:
                    delete obj.todeduct_c;
                    delete obj.todeduct_y;
                    delete obj.maxresv_num;
                    break;
                  case 2:
                    delete obj.todeduct_y;
                    break;
                  case 3:
                    delete obj.todeduct_c;
                    break;
                  case 0:
                    delete obj.todeduct_y;
                    break;
                }
              }

              return obj
            })

            const params = {
              class_ids: [classId],
              config_type: configType,
              apply_card: JSON.stringify(apply_card)
            }
            this.$service.post('/Web/OpenClass/set_open_class_detail', params).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.$router.back()
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          }
        })
      },
      // 保存排课跳转过来的数据
      handleSaveFormData() {
        const course = this.courseList.find(
          item => item.class_id == this.saveForm.courseId
        );

        const coach = this.coachList.find(
          item => item.coach_id == this.saveForm.coachId
        );
        let room = this.roomList.find(item => item.id == this.saveForm.roomId);

        if (!course && this.saveForm.id == "") {
          this.$Message.error("请选择课程！");
          return false;
        } else if (this.saveForm.courseDate == "") {
          this.$Message.error("请选择排课日期！");
          return false;
        } else if (this.saveForm.courseBeginTime == "") {
          this.$Message.error("请选择排课开始时间！");
          return false;
        } else if (
          !!this.saveForm.endDateTime && !!this.saveForm.beginDateTime &&
          (this.saveForm.endDateTime.getTime() < this.saveForm.beginDateTime.getTime())
        ) {
          this.$Message.error("排课结束时间必须大于开始时间！");
          return false;
        } else if (!coach) {
          this.$Message.error("请选择教练！");
          return false;
        } else if (!room) {
          // this.$Message.error("请选择教室！");
          // return false;
          room = {
            id: '',
            classroom_name: ''
          };
        }
        const { ms, pt, swim } = this.formData.config
        const list = [...ms, ...pt, ...swim]
        if (!this.saveForm.support_online && list.length === 0) {
          return this.$Message.error("请添加配置！");
        }

        this.$refs.formRef.validate(valid => {
          if (valid) {
            const bean = {
              class_id: course?course.class_id:this.saveForm.courseId,
              class_name: course?course.class_name:this.saveForm.class_name,
              class_category: this.is_free ? 1 : 0, // 团课 0 操课1 不过目前只有团课会进入到这里
              date_time: this.getDateString(this.saveForm.courseDate),
              reserve_number: this.saveForm.appointment, // 可预约人数
              coach_id: coach.coach_id,
              coach_name: coach.coach_name,
              classroom_id: room.id,
              classroom_name: room.classroom_name,
              bus_id: this.selBusId
            };

            if (!!this.saveForm.endDateTime && this.saveForm.beginDateTime) {
              bean.beg_time = this.getTimeString(this.saveForm.beginDateTime);
              bean.end_time = this.getTimeString(this.saveForm.endDateTime);
            } else {
              bean.beg_time = this.saveForm.courseBeginTime;
              bean.end_time = this.saveForm.courseEndTime;
            }

            const apply_card = list.map(v => {
              const obj = {
                config_type_id: v.config_type_id || v.myCustom.configTypeId,
                config_type_name: v.config_type_name || v.myCustom.name,
                todeduct_type: v.todeduct_type || v.myCustom.todeduct_type, // 配置的类型 1会籍卡 2私教卡 3泳教卡
                todeduct_type_detail: v.todeduct_type_detail || v.myCustom.todeduct_type_detail, //  私教包月？配置的类型具体 1期限卡 2次卡 3储值卡 4私教课 5泳教课
                todeduct_c: v.todeduct_c === undefined ? undefined : v.todeduct_c, // 次/人
                todeduct_y: v.todeduct_y === undefined ? undefined : v.todeduct_y, // 元/人
                maxresv_num: v.maxresv_num === undefined ? undefined : v.maxresv_num // 限制人数
              }

              if (this.configType == 1) {
                if (v.myCustom.todeduct_type != '1') {
                  delete obj.todeduct_y
                }
              } else {
                switch (+obj.todeduct_type_detail) {
                  case 1:
                    delete obj.todeduct_c;
                    delete obj.todeduct_y;
                    delete obj.maxresv_num;
                    break;
                  case 2:
                    delete obj.todeduct_y;
                    break;
                  case 3:
                    delete obj.todeduct_c;
                    break;
                  case 0:
                    delete obj.todeduct_y;
                    break;
                }
              }
              return obj
            })

            bean.config_type = this.configType
            bean.apply_card = JSON.stringify(apply_card)
            bean.update_class = this.update_class

            let url = "";
            if (this.saveForm.id == "") {
              url = "/Web/CourseSchedule/add_course_schedule";
            } else {
              url = "/Web/CourseSchedule/update_course_schedule";
              bean.id = this.saveForm.id;
            }
            return this.$service.post(url, bean).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.$router.back()
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          } else {
            console.log('formRef valid', valid);
          }
        })
      },
      getDateString(date) {
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? "0" + month : month;
        let day = date.getDate();
        day = day < 10 ? "0" + day : day;
        return `${year}/${month}/${day}`;
      },
      getTimeString(date) {
        let hour = date.getHours();
        let minute = date.getMinutes();
        hour = (hour<10)?'0'+hour:hour;
        minute = (minute<10)?'0'+minute:minute;
        return `${hour}:${minute}`;
      },
      handleBeginTimeChange() {
        let activeTime = 45;
        if (this.saveForm.courseId != "") {
          const course = this.courseList.find(
            item => item.class_id == this.saveForm.courseId
          );
          if (course) {
            activeTime = parseInt(course.class_hour);
          }
        }
        if (this.saveForm.courseBeginTime != "") {
          // what the hell! why not is the Date?
          if (typeof(this.saveForm.courseBeginTime)==='object') {
            const begin = this.saveForm.courseBeginTime;
            const end = new Date(begin.getTime() + activeTime * 60 * 1000);
            this.saveForm.courseEndTime = end;
            this.saveForm.beginDateTime = begin;
            this.saveForm.endDateTime = end;
          } else {
            this.saveForm.beginDateTime = new Date(this.getDateString(new Date())+' '+this.saveForm.courseBeginTime);
            this.saveForm.endDateTime = new Date(this.saveForm.beginDateTime.getTime() + activeTime * 60 * 1000);
            this.saveForm.courseEndTime = this.saveForm.endDateTime;
          }
        }
      },
      handleEndTimeChange() {
        if (this.saveForm.courseEndTime != "") {
          this.saveForm.endDateTime = new Date(this.getDateString(new Date())+ ' ' +this.saveForm.courseEndTime);
        }
      },
    }
  }
</script>

<style lang="less" scoped>
.form-box-con {
  padding: 17px 12px 70px 12px;
}
.card-container {
  width: 1300px;
  /deep/.ivu-card-body {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.save-form {
  /deep/.ivu-input-wrapper {
    width: 100%;
  }
}

/deep/.form-item-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 24px;
  width: 48%;
  .name {
    display: inline-block;
    vertical-align: bottom;
    width: 120px;
  }
  .tag-label {
    margin-right: 15px;
    min-width: 235px;
    .name {
      width: unset;
      max-width: 120px;
    }
  }
  .tip-btn {
    margin-left: 10px;
    color: dodgerblue;
    cursor: pointer;
  }

  &.card-type-1 {
    width: 31%;
    .name {
      width: 180px;
    }
  }
  &.width_100 {
    width: 100%;
  }
  .ivu-form-item-error-tip {
    white-space: nowrap;
  }
}
.ivu-modal-body {
  .form-item-row {
    width: 100%;
  }
}

@media screen and (max-width: 1600px) {
  .card-container {
    width: 100%;
  }
  /deep/.form-item-row {
    width: 100%;
    .tag-label {
      margin-right: 4px;
      min-width: 212px;
      .name {
        max-width: 100px;
      }
    }
    &.card-type-1 {
      width: 48%;
      .name {
        width: 140px;
      }
    }
  }
}
</style>
