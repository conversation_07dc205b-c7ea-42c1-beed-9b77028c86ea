<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{postData.teamclass_id?'编辑班级':'添加班级'}}</h2>
    </div>
    <div class="form-box-con">
      <Form label-position="right" ref="form" :model="postData" class="form" :label-width="140">
        <FormItem label="班级名称" prop="teamclass_name" :rules="{required: true, message: '请填写'}">
          <Input v-model="postData.teamclass_name" class="input" />
        </FormItem>
        <FormItem label="课程" prop="curriculum_id" :rules="{required: true, message: '请填写'}">
          <Select v-model="postData.curriculum_id" placeholder="课程" @on-change="changeClass"  :label-in-value="true" filterable clearable :disabled="!!this.postData.teamclass_id">
            <Option v-for="course in allCourses" :key="course.id" :value="course.id">{{ course.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="班级教练" prop="coach_id" :rules="{required: true, message: '请填写'}">
          <SaleSelect v-model="postData.coach_id" placeholder="教练" :isPtCoach="!isSwim" :isSwimCoach="isSwim" :isMembership="false" :showCoachType="false" @on-change="changeSale"  :label-in-value="true"></SaleSelect>
        </FormItem>
        <FormItem label="学员名单" prop="user_all">
          <UserBox v-model="userList" :showAction="true" :cardId="postData.curriculum_id"/>
        </FormItem>
        <FormItem>
          <div class="buttons">
            <Button type="primary" v-if="$route.params.id" @click="updateInfo">提交</Button>
            <Button type="primary" v-else @click="addCourse">提交</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>
<script>
import UserBox from './components/UserBox';
import SaleSelect from 'src/components/membership/salesSelect'
  export default {
    name: 'AddTeamClass',
    components: {
      SaleSelect,
      UserBox
    },
    data() {
      return {
        userList: [],
        postData: {
          teamclass_id: '',
          teamclass_name: '',
          curriculum_id: '',
          curriculum_name: '',
          coach_id: '',
          coach_name: '',
          user_all: []
        },
        allCourses: [],
        isSwim: this.$route.query.isSwim ==='true' ? true : false
      };
    },
    created() {
      this.getAllCourses()
      if (this.$route.query.id) {
        this.postData.teamclass_id = this.$route.query.id;
        this.geteditInfo();
      }
    },
    watch: {
      userList(val) {
        this.postData.user_all = this.userList.map(item=> item.user_id)
      }
    },
    methods: {
      addCourse() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写')
            return false
          }
          this.$service
            .post(`/Web/${this.isSwim?'TeamclassSwim':'TeamclassPrivate'}/${this.postData.teamclass_id?'teamclass_edit':'teamclass_add'}`, this.postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        })
      },
      changeClass(info) {
        if(info && info.value !== this.postData.curriculum_id) {
          this.userList = []
        }
        this.postData.curriculum_id = info?info.value:''
        this.postData.curriculum_name = info?info.label:''
      },
      changeSale(info) {
        this.postData.coach_id = info?info.value:''
        this.postData.coach_name = info?info.label:''
      },
      getAllCourses() {
        const url = '/Web/PtSchedule/pt_user_all_card'
        this.$service.post(url, { is_swim: this.isSwim ? 1 : 0, no_pt_time_limit_card: 1 }).then(res => {
          if (res.data.errorcode === 0) {
            this.allCourses = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      updateInfo() {},
      geteditInfo() {
        this.$service
          .post(`/Web/${this.isSwim?'TeamclassSwim':'TeamclassPrivate'}/teamclass_info`, { teamclass_id: this.postData.teamclass_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const resData = res.data.data
              this.userList = resData.student.filter(item=>item.status !== '1')
              this.postData = resData
              delete this.postData.student
              this.postData.teamclass_id = resData.id
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
<style>
.form-box .time-box .ivu-input-wrapper{
  width: 100%;
}
</style>
<style lang="less" scoped>
  // .user-box {
    
  // }
</style>
