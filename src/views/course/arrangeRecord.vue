<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <DatePicker class="option-item" type="daterange" placeholder="排课时间" v-model="dateRange" style="width: 200px"
        :clearable="false" :editable="false" @on-change="handleSearch" />
      <Input class="option-item" v-model="postParams.search" placeholder="会员名称/电话" @on-enter="handleSearch"
        :clearable="true" @on-clear="handleSearch" />
      <Select class="option-item" v-model="postParams.coach_id" placeholder="教练" @on-change="handleSearch" filterable
        clearable>
        <Option value="">所有教练</Option>
        <Option v-for="(coach, coachIndex) in coachList" :key="coachIndex" :value="coach.coach_id">{{ coach.coach_name }}
        </Option>
      </Select>
      <Select class="option-item" v-model="postParams.card_id" placeholder="课程" @on-change="handleSearch" filterable
        clearable>
        <Option value="">所有课程</Option>
        <Option style="overflow-x: hidden" v-for="(course, courseIndex) in cardList" :key="courseIndex"
          :value="course.id">{{ course.name }}
        </Option>
      </Select>
      <Select class="option-item" v-model="postParams.status" placeholder="课程状态" @on-change="handleSearch" filterable
        clearable>
        <Option :value="item.value" v-for="item in statusList" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select class="option-item" v-model="postParams.class_user_no_type" placeholder="授课方式" @on-change="handleSearch" filterable
        clearable>
          <Option :value="0">授课方式</Option>
          <Option :value="2">1对1</Option>
          <Option :value="1">1对多</Option>
      </Select>
      <!-- <Button type="success" @click="handleSearch">搜索</Button> -->
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button @click="handleExcel">导出Excel</Button>
      </div>
      <Page class="page" :total="totalCount" :page-size="postParams.page_size" :current.sync="postParams.page_no"
        placement="top" show-total show-sizer @on-change="pageChanged" @on-page-size-change="pageSizeChanged"></Page>
      </Col>
    </Row>
  </div>
</template>

<script setup>
import { ref, defineComponent, watch, nextTick } from 'vue';
import service from 'src/service'
import { Message } from 'iview';
import { formatDate } from 'src/utils'
import { getcoachsInfo } from '@/service/getData'
import { useState } from 'vuex-composition-helpers'
import router from 'src/router'

defineComponent({
  name: 'ArrangeRecord',
})

const props = defineProps({
  isSwimFlag: Boolean
})
const isSwimFlag = ref(props.isSwimFlag)

const isSearch = ref(true)
watch(() => props.isSwimFlag, (val) => {
  isSwimFlag.value = val
  isSearch.value = false
  postParams.value.coach_id = ''
  postParams.value.card_id = ''
  postParams.value.status = ''
  postParams.value.search = ''
  nextTick(() => {
    isSearch.value = true
  })
  getCoachList()
  getCardList()
  getList()
})

const { busId } = useState(['busId'])
const statusList = ref([
  {
    label: '已约',
    color: '#a3a1ff',
    value: 1
  },
  {
    label: '已上',
    color: '#a4ddb4',
    value: 2
  },
  {
    label: '未上',
    color: '#fc88c2',
    value: 3
  },
  {
    label: '完成',
    color: '#f5b57d',
    value: 4
  }
])

// search params
const dateRange = ref([])
// default dateRange to today
dateRange.value = [new Date(), new Date()]
const postParams = ref({
  coach_id: '',
  card_id: '',
  s_date: '',
  e_date: '',
  status: '',
  search: '',
  class_user_no_type: 0,
  page_no: 1,
  page_size: 10
})

// search select options
const coachList = ref([])
const cardList = ref([])
const getCoachList = () => {
  const type = isSwimFlag.value ? 2 : 1
  return getcoachsInfo(busId.value, 0, type).then(res => {
    if (res.data.errorcode === 0) {
      coachList.value = res.data.data
    }
  })
}
const getCardList = () => {
  const is_swim = isSwimFlag.value ? 1 : 0
  return service.post('/Web/PtSchedule/pt_user_all_card', { is_swim }).then(res => {
    if (res.data.errorcode === 0) {
      cardList.value = res.data.data
    }
  })
}

// search handler
const handleSearch = () => {
  if (!isSearch.value) {
    return
  }
  postParams.value.page_no = 1
  getList()
}

// table
const columns = ref([
  {
    title: '预约时间',
    key: 'create_time',
  },
  {
    title: '教练',
    key: 'coach_name',
  },
  {
    title: '会员',
    key: 'username',
    render: (h, params) => {
      return h(
        'a',
        {
          on: {
            click: () => {
              router.push({ path: `/member/detail/${params.row.user_id}` });
            }
          }
        },
        params.row.username
      )
    }
  },
  {
    title: '授课方式',
    key: 'userNoDes',
    render: (h, params) => {
      return h('span', `1对${params.row.user_no}`)
    }
  },
  {
    title: '预约课程',
    key: 'class_name',
    render: (h, params) => {
      if (params.row.appt_type == 1) {
        return h('div', { style: { display: 'flex', justifyContent: 'center', alignItems: 'center' } }, [
          h('div', params.row.class_name),
          h('div', { style: { marginLeft: '6px', fontSize: '10px', alignSelf: 'flex-start', color: '#19be6b' } }, '付')
        ])
      } else {
        return h('span', params.row.class_name)
      }
    }
  },
  {
    title: '课程时长（分钟）',
    key: 'class_duration',
  },
  {
    title: '课程状态',
    key: 'status_label',
    render: (h, params) => {
      const status = Number(params.row.status)
      const item = statusList.value.find(item => item.value === status)
      return h('span', { style: { color: item.color } }, item.label)
    }
  },
  {
    title: '消课时间',
    key: 'finish_time',
  },
  {
    title: '总课时数',
    key: 'all_num',
  },
  {
    title: '目前课时',
    key: 'expend_num',
  },
  {
    title: '目前剩余课时',
    key: 'last_num',
  }
])
const totalCount = ref(0)
const list = ref([])

const getList = () => {
  const url = isSwimFlag.value ? '/Web/PtSchedule/swim_schedule_log' : '/Web/PtSchedule/pt_schedule_log'
  if (dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    postParams.value.s_date = formatDate(dateRange.value[0], 'yyyy-MM-dd')
    postParams.value.e_date = formatDate(dateRange.value[1], 'yyyy-MM-dd')
  } else {
    postParams.value.s_date = ''
    postParams.value.e_date = ''
  }
  return service.post(url, postParams.value).then(({ data }) => {
    if (data.errorcode === 0) {
      list.value = data.data.list
      totalCount.value = Number(data.data.count)
    } else {
      Message.error(data.errormsg)
    }
  })
}
const pageChanged = (page) => {
  postParams.value.page_no = page
  getList()
}
const pageSizeChanged = (size) => {
  postParams.value.page_no = 1
  postParams.value.page_size = size
  getList()
}

const table = ref(null)
const handleExcel = () => {
  const url = isSwimFlag.value ? '/Web/PtSchedule/swim_schedule_log' : '/Web/PtSchedule/pt_schedule_log'
  const fakeParams = { ...postParams.value, page_no: 1, page_size: totalCount.value, is_export: 1 }
  service.post(url, fakeParams).then(({ data }) => {
    if (data.errorcode === 0) {
      const list = data.data.list
      list.forEach(item => {
        const status = Number(item.status)
        const statusItem = statusList.value.find(chunk => chunk.value === status)
        item.status_label = statusItem.label
        item.userNoDes = `1对${item.user_no}`
      });
      table.value.exportCsv({
        filename: `排课记录-${postParams.value.s_date}-${postParams.value.e_date}`,
        columns: columns.value,
        data: list
      })
    }
  })
}

// created
getCoachList()
getCardList()
getList()
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-item {
        width: 128px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}</style>