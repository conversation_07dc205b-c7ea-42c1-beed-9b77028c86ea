<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="800">
    <div>
      <div class="taxform-header">
        <Select class="w120 mr5" v-model="postData.sorting" @on-change="typeChange" placeholder="类型" clearable>
          <Option value="">全部</Option>
          <Option :value="0">未分班学员</Option>
          <Option :value="1">已分班学员</Option>
        </Select>
        <Input v-model="postData.search" class="w150 mr5" @on-enter="getList" placeholder="会员姓名、电话" />
        <Button type="success" @click="getList">搜索</Button>
      </div>
      <div class="modaltable-scroll">
        <Table :height="400" ref="table" @on-selection-change="handleSelect" :columns="modalColumns" :data="tableData" stripe />
      </div>
      
      <Alert v-if="postData.sorting!==0" class="user-tips" type="warning" show-icon>温馨提示:已有班级的学员操作后可能重复出现在多个班级</Alert>
      <Page :total="+total"
            class="modal-page"
            :current.sync="postData.page_no"
            :page-size="postData.page_size"
            placement="top"
            show-total
            show-sizer
            :page-size-opts="[10, 20, 50]"
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success"
              @click="handleConfirm">确定</Button>
      <Button 
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
const initModalColumns = [
  {
    type: 'selection',
    width: 80
  },
  {
    title: '姓名',
    key: 'username'
  },
  
  {
    title: '余节数',
    key: 'last_num'
  }
]
export default {
  name: 'SelectUser',
  props: {
    value: {
      type: Boolean
    },
    cardId: {
      type: [Number, String]
    },
    ids: {
      type: Array
    }
  },
  data() {
    return {
      selection: [],
      modalColumns: [...initModalColumns],
      tableData: [],
      total: 0,
      postData: {
        card_id: '',
        sorting: 0,
        page_no: 1,
        page_size: 50
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    cardId(val) {
      if(val) {
        this.getList()
      } else {
        this.tableData = []
        this.selection = []
      }
    },
    showModal(val) {
      if (!val) {
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            _checked: false
          }
        })
        this.selection = []
      } else {
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            _disabled: this.ids.indexOf(item.user_id) !== -1 ? true : false
          }
        })
      }
    }
  },
  methods: {
    handleConfirm() {
      if (!this.selection.length) return this.$Message.error('请先选择人员')
      this.$emit('on-success', this.selection)
      this.showModal = false
    },
    handleSelect(selection) {
      this.selection = selection
    },
    typeChange(val) {
      this.modalColumns = [...initModalColumns]
      if(val !== 0) {
        this.modalColumns.splice(2, 0, {
          title: '所在班级',
          key: 'teamclass_name'
        })
      }
      this.selection = []
      this.handleSearch()
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageSizeChanged(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/TeamclassSwim/add_student_list', {
          ...this.postData,
          card_id: this.cardId
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.total = +data.count
            this.tableData = data.list.map(item => {
              return {
                ...item,
                _disabled: this.ids.indexOf(item.user_id) !== -1 ? true : false
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style scoped>

.modal-page {
  text-align: right;
  margin: 15px 0;
}
.taxform-header {
  display: flex;
  padding-bottom: 30px;
}
.user-tips {
  width: 400px;
  margin-top: 15px;
}
</style>
