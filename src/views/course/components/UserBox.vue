<template>
  <div class="user-list">
    <div v-if="showAction" class="list-top" @click="addItem">
      <Icon type="md-add" color="#18BF6A" size="16" />
      新增学员
    </div>
    <div class="list-con">
      <div class="list-item" v-for="(item, index) in userList">
        <div class="item-left">
          <img class="avatar" :src="item.avatar" alt="">
          <span class="name" :title="item.username">{{item.username}}</span>
        </div>
        <div class="item-cen">
          <Icon type="ios-phone-portrait" color="#52A6EA" />
          <span class="phone-text">{{item.phone}}</span>
        </div>
        <div class="item-rig" v-if="showSignStatus">
          <span v-if="item.status === '1'" class="color-green">已签到</span>
          <span v-if="item.status === '0'" class="color-red">未到</span>
          <span v-if="item.status === '2'" class="color-org">临时上课</span>
        </div>
        <div class="item-rig" v-else>
          <span>剩余{{item.last_num}}节</span>
          <span v-if="showStudentStatus && item.status === '1'" class="text-red">(已移除)</span>
          <Icon v-if="showAction" @click="deleteItem(index)" class="delete-icon" title="删除" color="red" size="16" type="md-remove-circle"/>
        </div>
      </div>
    </div>
    <SelectUser :ids="ids" :cardId="cardId" v-model="showSelectUser" @on-success="addUserItemConfirm"/>
  </div>
</template>
<script>
import SelectUser from './SelectUser'
export default {
  name: 'UserBox',
  components: {
    SelectUser
  },
  data() {
    return {
      roomList: [],
      ids: [],
      showSelectUser: false,
    }
  },
  props: {
    showAction: {
      type: Boolean,
      default: false
    },
    showSignStatus: {
      type: Boolean,
      default: false
    },
    showStudentStatus: {
      type: Boolean,
      default: false
    },
    cardId: {
      type: [Number, String]
    },
    value: {
      type: Array,
      default: ()=> []
    },
    isSwim: {
      type: Boolean
    }
  },
  computed: {
    userList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    userList(val) {
      this.ids = val.map(item=> item.user_id)
    }
  },
  created() {
  },
  methods: {
    addItem() {
      this.showSelectUser = true
    },
    deleteItem(index) {
      this.userList.splice(index, 1)
    },
    addUserItemConfirm(list) {
      this.userList = this.userList.concat(list)
    }
  }
}
</script>

<style lang="less" scoped>
.user-list {
  width: 434px;
  height: 338px;
  padding: 19px 19px 0;
  box-sizing: border-box;
  background: #F1F4F6;
  border-radius: 6px;
  overflow-y: scroll;
}
.list-top {
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #18BF6A;
  line-height: 22px;
  margin-bottom: 20px;
}
.list-item {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #1b1b1b;
  justify-content: space-between;
  margin-bottom: 18px;
  .item-left, .item-cen, .item-rig {
    display: flex;
    align-items: center;
  }
  .item-rig {
    flex: 1;
    justify-content: flex-end;
  }
  .text-red {
    margin-left: 5px;
  }
  .color-green {
    color:#18BF6A;
  }
  .color-red {
    color: #E60012;
  }
  .color-org {
    color: #F39800;
  }
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 13px;
  }
  .name {
    width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .delete-icon {
    cursor: pointer;
    margin-left: 5px;
  }
}
</style>
