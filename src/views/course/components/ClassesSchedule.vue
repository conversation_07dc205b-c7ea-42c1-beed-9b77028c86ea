<template>
    <div class="table-wrap">
        <header class="bb-none">
            <Input class="w120" v-model="postData.search" placeholder="班级" @on-enter="handleSearch"></Input>
            <DatePicker type="daterange" placeholder="时间" v-model="dateRange" @on-change="dateChange"></DatePicker>
            <Select v-model="postData.status" placeholder="课程状态" clearable>
                <Option :value="1">已上</Option>
                <Option :value="0">未上</Option>
            </Select>
            <SaleSelect v-model="postData.coach_id" placeholder="教练" :isPtCoach="!isSwim" :isSwimCoach="isSwim" :isMembership="false" :showCoachType="false"></SaleSelect>
            <Button type="success" @click="handleSearch">搜索</Button>
        </header>
        <main>
          <div class="classes-box">
            <div class="classes-box-header">
              <div class="item-left">班级</div>
              <div class="item-rig">课程安排</div>
            </div>
            <div class="classes-box-body">
              <div class="classes-item" v-for="item in tableData" :key="item.id">
                <div class="item-left">
                  <div class="item-wrap" :class="{'status-gray':item.status==='1'}">
                    <div class="tit">{{item.teamclass_name}}</div>
                    <div class="con"><img class="icon-course" :src="`/static/img/course/person${item.status==='1'?'-gray':''}.png`" alt="student" /><span class="con-text">{{item.student}}人</span></div>
                    <div class="con"><img class="icon-course" :src="`/static/img/course/name${item.status==='1'?'-gray':''}.png`" alt="name" /><span class="con-text">{{item.coach_name}}</span></div>
                  </div>
                </div>
                <div class="item-rig">
                  <div class="course-box hasadd">
                    <div class="course-wrap" :class="{'status-red':info.sign==='0'}" v-for="info in item.schedule_list" :key="info.id">
                      <div class="action-wrap">
                          <div class="act-btn" title="详情" @click="handleHoverDetailClick(info.id)">
                            <i class="fa fa-2x fa-file-text-o" aria-hidden="true"></i>
                          </div>
                          <div v-if="info.sign==='0'" class="act-btn" title="修改" @click="handleHoverUpdateClick(info, item)">
                            <i class="fa fa-2x fa-pencil-square-o" aria-hidden="true"></i>
                          </div>
                          <div v-if="info.sign==='0'" class="act-btn" title="删除" @click="handleHoverDeleteClick(info.id)">
                            <i class="fa fa-2x fa-trash-o" aria-hidden="true"></i>
                          </div>
                      </div>
                      <div class="top-hea">
                        <span>{{info.sign}}/{{info.student}}</span>
                        <span class="top-status">{{info.sign==='0'?'未上课':'已上课'}}</span>
                      </div>
                      <div class="con">
                        <img class="icon-course" :src="`/static/img/course/time${info.sign==='0'?'-red':''}.png`" alt="" /> <span class="con-text">{{info.date}} {{info.s_time}}</span> 
                      </div>
                      <div class="con">
                        <img class="icon-course" :src="`/static/img/course/name${info.sign==='0'?'-red':'-green'}.png`" alt="" /><span class="con-text">{{info.coach_name}}</span>
                      </div>
                    </div>
                  </div>
                 
                </div>
                 <div class="course-add-wrap"v-if="item.status==='0'" @click="addClass(item)">
                    <Icon class="course-add" type="ios-add-circle-outline" size="44" color="#52A6EA"/>
                  </div>
              </div>
            </div>
          </div>
           
        </main>
        <AddTeamCourse :id="curId" :cardId="curCardId" :info="curInfo" :isSwim="isSwim" :isEdit="isEdit" v-model="showAddTeamCourse" @on-success="getList()"/>
        <ScheduleDetailModal :id="curId" :isSwim="isSwim" v-model="showClassDetail"/>
    </div>
</template>
<script>
import SaleSelect from 'src/components/membership/salesSelect'
import AddTeamCourse from './AddTeamCourse'
import ScheduleDetailModal from './ScheduleDetailModal'
import { formatDate } from 'src/utils'
export default {
  name: 'ClassesSchedule',
  components: {
    SaleSelect,
    ScheduleDetailModal,
    AddTeamCourse
  },
  data() {
    return {
      isEdit: false,
      dateRange: [
        formatDate(new Date(Date.now() - 15 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
        formatDate(new Date(Date.now() + 15 * 24 * 3600 * 1000), 'yyyy-MM-dd')
      ],
      showAddTeamCourse: false,
      showClassDetail: false,
      isSwim: false,
      postData: {
        page_no: 1,
        page_size: 1000,
        search: '',
        s_date: formatDate(new Date(Date.now() - 15 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
        e_date: formatDate(new Date(Date.now() + 15 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
        curriculum_id: '',
        coach_id: '',
        status: ''
      },
      curInfo: null,
      curId: '',
      curCardId: '',
      tableData: [],
      total: 0
    }
  },
  created() {
    this.init()
  },
  watch: {
    $route(info, oldInfo) {
      if (info.name !== oldInfo.name) {
        this.init()
      }
    }
  },
  computed: {},
  methods: {
    dateChange([s, e]) {
      this.postData.s_date = s
      this.postData.e_date = e
    },
    init() {
      this.isSwim = this.$route.name === '泳教班管理' ? true : false
      this.getList()
    },
    handleHoverDetailClick(classId) {
      this.curId = classId
      this.showClassDetail = true
    },
    handleHoverUpdateClick(info, item) {
      this.curInfo = info
      this.curId = info.id
      this.curCardId = item.card_id
      this.isEdit = true
      this.showAddTeamCourse = true
    },
    handleHoverDeleteClick(id) {
      this.$Modal.confirm({
        title: '提示',
        content: '确定要删除吗？',
        onOk:()=> {
          this.$service.post(this.isSwim?'/Web/TeamclassSwim/teamclass_schedule_delete':'/Web/TeamclassPrivate/teamclass_schedule_delete', { teamclass_schedule_id: id }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    },
    addClass(info) {
      this.curId = info.id
      this.curCardId = info.card_id
      this.isEdit = false
      this.showAddTeamCourse = true
    },
    getList() {
      return this.$service
        .post(this.isSwim?'/Web/TeamclassSwim/teamclass_schedule_list':'/Web/TeamclassPrivate/teamclass_schedule_list', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.classes-box {
  margin: 0 35px 30px;
  border: 2px solid #f1f3f7;
  box-sizing: border-box;
}
.classes-box-header {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  color: #1b1b1b;
  display: flex;
  border-bottom: 2px solid #f1f3f7;
  text-align: center;
  .item-left {
    width: 272px;
    border-right: 2px solid #f1f3f7;
  }
  .item-rig {
    flex: 1;
  }
}
.classes-item {
  width: 100%;
  display: flex;
  border-bottom: 2px solid #f1f3f7;
  text-align: center;
  position: relative;
  .item-left {
    width: 272px;
    border-right: 2px solid #f1f3f7;
    overflow: hidden;
  }
  .item-rig {
    flex: 1;
  }
}
.bb-none {
  border-bottom: none !important;
}
.classes-box-body {
  height: calc(~'100vh - 360px');
  overflow-y: scroll;
  .item-wrap {
    width: 220px;
    height: 110px;
    background: #e6f3ff;
    border-radius: 6px 0px 0px 6px;
    font-size: 14px;
    color: #1b1b1b;
    box-sizing: border-box;
    margin: 10px 15px;
    padding: 15px;
    text-align: left;
    position: relative;
    &::after {
      position: absolute;
      right: -80px;
      top: 0;
      content: ' ';
      width: 0;
      height: 0;
      border: 55px solid transparent;
      border-left: 25px solid #e6f3ff;  
    }
    .tit {
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 10px;
    }
    .con {
      width: 100%;
      margin-bottom: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .status-gray {
    color: #959595;
    background: #F1F3F7;
    &::after {
      border-left: 25px solid #F1F3F7;
    }
  }
  .icon-course {
    width: 16px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
  }
  .course-add-wrap {
    position: absolute;
    right: 0;
    top: 0;
    width: 100px;
    height: 100%;
    background: #fff;
    text-align: center;
  }
  .course-add {
    cursor: pointer;
    margin-top: 50px;
    font-weight: bold;
  }
  .con-text {
    vertical-align: middle;
  }
  .item-rig {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
    overflow-x: scroll;
  }
  .hasadd {
    box-sizing: border-box;
    padding-right: 100px;
  }
  .course-box {
    display: flex;
    white-space: nowrap;
  }
  .action-wrap {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    padding: 6px 0;
    box-sizing: border-box;
    width: 100%;
    height: 110px;
    background: rgba(0, 0, 0, .2);
    justify-content: center;
    .act-btn {
      width: 33%;
      cursor: pointer;
    }
  }
  .course-wrap {
    position: relative;
    width: 220px;
    height: 110px;
    box-sizing: border-box;
    padding: 10px 15px;
    background: #ebfaef;
    border-radius: 6px;
    margin: 10px 0 10px 13px;
    text-align: left;
    font-size: 16px;
    color: #1b1b1b;
    
    &:hover {
      .action-wrap {
        display: flex;
        align-items: center;
        text-align: center;
      }
    }
    .top-hea {
      display: flex;
      justify-content: space-between;
      color: #18bf6a;
      margin-bottom: 10px;
    }
    .top-status {
      font-size: 14px;
    }
    .con {
      width: 100%;
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .status-red {
    background: #FFF1F8;
    .top-hea {
      color: #FF45A3;
    }
    .top-status {
      width: 60px;
      height: 22px;
      
      line-height: 22px;
      text-align: center;
      border: 1px solid #FF45A3;
      border-radius: 11px;
    }
  }
}
</style>
