<template>
<Modal v-model="showAdd" :mask-closable="false" title="批量排课" width="600">
  <Form ref="morepostForm"
          :model="postData"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item label="排课日期" prop="s_date" :rules="{ required: true, message: '请选择时间范围'}">
        <Date-picker @on-change="dateChanged" :value="dateRange" type="daterange" placeholder="请选择时间范围" :editable="false" :clearable="false" :options="options"></Date-picker>
      </Form-item>
      <Form-item prop="week" :rules="{ required: true, message: '请选择周期'}">
       <Checkbox-group v-model="postData.week">
          <Checkbox :label="1">周一</Checkbox>
          <Checkbox :label="2">周二</Checkbox>
          <Checkbox :label="3">周三</Checkbox>
          <Checkbox :label="4">周四</Checkbox>
          <Checkbox :label="5">周五</Checkbox>
          <Checkbox :label="6">周六</Checkbox>
          <Checkbox :label="7">周日</Checkbox>
        </Checkbox-group>
      </Form-item>
      <Form-item label="上课时间" prop="s_time" :rules="{ required: true, message: '请选择时间'}">
        <TimePicker v-model="postData.s_time" format="HH:mm" placeholder="时间"></TimePicker>
      </Form-item>
      <Form-item label="上课教练" prop="coach_id" :rules="{ required: true, message: '请选择教练'}">
        <Select v-model="postData.coach_id"  @on-change="changeSale($event)" :labelInValue="true" placeholder="教练" filterable>
          <Option v-for="item in coachList" :value="item.coach_id" :key="item.coach_id">{{ item.coach_name }}</Option>
        </Select>
      </Form-item>
      <Form-item label="教室" prop="class_root_id">
        <Select v-model="postData.class_root_id" @on-change="roomChange($event)"  :labelInValue="true" placeholder="教室" filterable>
          <Option v-for="item in roomList" :value="item.id" :key="item.id">{{ item.classroom_name }}</Option>
        </Select>
      </Form-item>
      
    </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doAdd">确定</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
export default {
  name: 'AddMoreTeamCourse',
  data() {
    return {
      dateRange: [],
      options: {
        disabledDate(date) {
          return date && (date.valueOf() < Date.now()- 24*60*60*1000 || date.valueOf() > Date.now() + 93*24*60*60*1000) ;
        }
      },
      postData: {
        teamclass_id: '',
        s_date: '',
        e_date: '',
        s_time: '',
        coach_id: '',
        coach_name: '',
        class_root_id: '',
        class_root_name: '',
        week: []
      },
      roomList: [],
      coachList: []
    }
  },
  props: {
    id: {
      type: [Number, String]
    },
    cardId: {
      type: [Number, String]
    },
    value: {
      type: Boolean
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    },
    isSwim: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if(!val) {
        this.dateRange = []
        this.postData = {
          teamclass_id: '',
          s_date: '',
          e_date: '',
          s_time: '',
          coach_id: '',
          coach_name: '',
          class_root_id: '',
          class_root_name: '',
          week: []
        }
      }
    },
    cardId: {
      handler(val) {
        if(val) {
          this.getCoachList()
        }
      },
      immediate: true
    }
  },
  created() {
    this.getRoomList()
  },
  methods: {
    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    changeSale(info) {
      this.postData.coach_id = info?info.value:''
      this.postData.coach_name = info?info.label:''
    },
    roomChange(info) {
      this.postData.class_root_id = info?info.value:''
      this.postData.class_root_name = info?info.label:''
    },
    getCoachList() {
      this.$service
        .get('/Web/Member/get_all_private_coach_list?card_id='+this.cardId)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.coachList = res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getRoomList() {
      this.$service
        .post('/Web/TeamclassSwim/get_classroom')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.roomList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    doAdd() {
      this.$refs.morepostForm.validate(val => {
        if(!val) {
          this.$Message.warning('请正确填写')
        } else {
          this.postData.teamclass_id = this.id
          this.$service
            .post(`/Web/${this.isSwim?'TeamclassSwim':'TeamclassPrivate'}/teamclass_schedule_add_by_week`, this.postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showAdd = false
                this.$emit('on-success')
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.top-add {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #18BF6A;
  line-height: 22px;
  margin-bottom: 24px;
  div {
    margin-right: 20px;
  }
  /deep/ .ivu-icon {
    font-weight: bold;
  }
}
.item-wrap {
  margin-bottom: 17px;
  .w160 {
    width: 160px;
  }
  .w100 {
    width: 100px;
  }
  .delete-icon {
    cursor: pointer;
    margin-left: 20px;
  }
}
</style>
