<template>
<Modal v-model="showAdd" title="班级详情" width="600">
  <Form label-position="right" ref="form" :model="info" class="form" :label-width="80">
      <FormItem label="班级名称" prop="class_name">
        <div class="class-text">{{info.teamclass_name}}</div>
      </FormItem>
      <FormItem label="班级教练" prop="class_hour">
        <div class="class-text">{{info.coach_name}}</div>
      </FormItem>
      <FormItem label="学员名单" prop="class_hour">
        <UserBox v-model="info.student" showStudentStatus/>
      </FormItem>
  </Form>
  <div slot="footer"></div>
</Modal>
</template>
<script>
import UserBox from './UserBox'
export default {
  name: 'ClassDetailModal',
  components: {
    UserBox
  },
  data() {
    return {
      roomList: [],
      info: {
        teamclass_name: '',
        curriculum_name: '',
        coach_name: '',
        student: []
      }
    }
  },
  props: {
    id: {
      type: [Number, String]
    },
    value: {
      type: Boolean
    },
    isSwim: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
      } else {
        this.getDetail()
      }
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      this.$service
        .post(this.isSwim?'/Web/TeamclassSwim/teamclass_info':'/Web/TeamclassPrivate/teamclass_info', {
          teamclass_id: this.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.info = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.top-add {
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #18BF6A;
  line-height: 22px;
  margin-bottom: 24px;
  /deep/ .ivu-icon {
    font-weight: bold;
  }
}
.item-wrap {
  margin-bottom: 17px;
  .w160 {
    width: 160px;
  }
  .w100 {
    width: 100px;
  }
  .delete-icon {
    cursor: pointer;
    margin-left: 20px;
  }
}
.class-text {
  font-size: 14px;
  font-weight: bold;
  color: #434343;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
