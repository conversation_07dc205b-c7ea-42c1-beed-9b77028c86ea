<template>
    <div class="table-wrap">
        <header>
            <Input class="w120" v-model="postData.search" placeholder="班级/学员名称" @on-enter="handleSearch"></Input>
             <Select v-model="postData.curriculum_id" placeholder="课程" filterable clearable>
              <Option v-for="course in allCourses" :key="course.id" :value="course.id">{{ course.name }}
              </Option>
            </Select>
             <saleSelect v-model="postData.coach_id" placeholder="教练" :isPtCoach="!isSwim" :isSwimCoach="isSwim" :isMembership="false" :showCoachType="false"></saleSelect>
            <Select v-model="postData.status" placeholder="班级状态" clearable>
                <Option :value="0">未结业</Option>
                <Option :value="1">已结业</Option>
            </Select>
            <Button type="success" @click="handleSearch">搜索</Button>
        </header>
        <main>
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="tableData"
                   ref="table"></Table>
        </main>
        <footer>
          <Button type="success" @click="addClass">新增班级</Button>
          <Button style="margin-left: 20px" @click="exportList">导出execl</Button>
          <Pager :name="'ClassesList'+isSwim" :total="+total" :postData="postData" @on-change="handleWhichPage" />
        </footer>
        <ClassDetailModal :id="curId" :isSwim="isSwim" v-model="showClassDetail" />
    </div>
</template>
<script>
import saleSelect from 'src/components/membership/salesSelect'
import Pager from 'components/pager'
import ClassDetailModal from './ClassDetailModal'
export default {
  name: 'ClassesList',
  components: {
    saleSelect,
    ClassDetailModal,
    Pager
  },
  data() {
    return {
      isSwim: false,
      showClassDetail: false,
      curId: '',
      postData: {
        page_no: 1,
        page_size: 10,
        search: '',
        curriculum_id: '',
        coach_id: '',
        status: ''
      },
      cardColumns: [
        {
          title: '班级名称',
          key: 'teamclass_name'
        },
        {
          title: '课程',
          key: 'curriculum_name'
        },
        {
          title: '负责教练',
          key: 'coach_name'
        },
        {
          title: '学员名单',
          key: 'student',
          render: (h, params) => {
            return (<div style="color:dodgerblue" onClick={()=>{
              this.curId = params.row.id
              this.showClassDetail = true
            }}>{params.row.student}</div>)
          }
        },
        {
          title: '上课节数',
          key: 'already'
        },
        {
          title: '剩余节数',
          key: 'last_num',
          render: (h, params) => {
            return (<div style="color:dodgerblue" onClick={()=>{
              this.curId = params.row.id
              this.showClassDetail = true
            }}>{params.row.last_num}</div>)
          }
        },
        {
          title: '班级状态',
          key: 'status_text'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            const row = params.row
            return (
              <div>
                <i-button
                  type="text"
                  class="mr5"
                  onClick={()=>{
                    this.curId = row.id
                    this.showClassDetail = true
                  }}
                >
                  详情
                </i-button>
                {row.status == 0?<i-button
                  type="text"
                  class="mr5"
                  onClick={() => {
                    this.$router.push(
                      `/course/addTeamClass?id=${row.id}&isSwim=${this.isSwim}`
                    )
                  }}
                >
                  编辑
                </i-button>:''}
                {row.status == 0?<i-button
                  type="text"
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={()=>{
                    this.$Modal.confirm({
                      title: '提示',
                      content: '是否确定要将班级进行结业？',
                      onOk: () => {
                        this.updateClassStatus(row.id, 1)
                      }
                    })
                  }}
                >
                  结业
                </i-button>:''}
                {row.status == 1?<i-button
                  type="text"
                  class="mr5"
                  onClick={()=>{
                    this.updateClassStatus(row.id, 0)
                  }}
                >
                  恢复
                </i-button>:''}
                {row.status == 1?<i-button
                  type="text"
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={()=>{
                    this.deleteClass(row.id)
                  }}
                >
                  删除
                </i-button>:''}
              </div>
            )
          }
        }
      ],
      tableData: [],
      allCourses: [],
      total: 0
    }
  },
  created() {
    this.init()
  },
  watch: {
    $route(info, oldInfo) {
      if (info.name !== oldInfo.name) {
        this.init()
      }
    }
  },
  computed: {},
  methods: {
    deleteClass(id) {
      this.$Modal.confirm({
        title: '提示',
        content: '是否确定要将班级进行删除？',
        onOk: () => {
          this.$service.post(this.isSwim?'/Web/teamclassSwim/teamclass_delete':'Web/TeamclassPrivate/teamclass_delete', { teamclass_id: id }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    updateClassStatus(id, status) {
      this.$service.post(this.isSwim?'/Web/teamclassSwim/teamclass_graduation':'Web/TeamclassPrivate/teamclass_graduation', { teamclass_id: id, status: status }).then(res => {
        if (res.data.errorcode === 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    addClass() {
      this.$router.push(`/course/addTeamClass?isSwim=${this.isSwim}`)
    },
    init() {
      this.isSwim = this.$route.name === '泳教班管理' ? true : false
      this.getAllCourses()
      this.getList()
    },
    getAllCourses() {
      this.$service.post('/Web/PtSchedule/pt_user_all_card', { is_swim: this.isSwim ? 1 : 0, no_pt_time_limit_card: 1 }).then(res => {
        if (res.data.errorcode === 0) {
          this.allCourses = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getList(allPageCount) {
      let postObj = Object.assign({}, this.postData)
      if (allPageCount) {
        postObj.page_size = allPageCount
        postObj.page_no = 1
      }
      return this.$service
        .post(this.isSwim?'/Web/teamclassSwim/teamclass_list':'/Web/TeamclassPrivate/teamclass_list', postObj)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.list.map(item => {
                return {
                  ...item,
                  status_text: item.status == 0 ? '未结业' : '已结业'
                }
              })
            if (!allPageCount) {
              this.tableData = data
              this.total = parseInt(res.data.data.count)
            }
            return data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleWhichPage(postData) {
      this.postData = { ...this.postData, ...postData }
      this.getList()
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    handleDelete(idArr, callback) {
      return this.$service
        .get(`/Web/Card/delete_cards/ids/${idArr}`)
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success({ content: '删除成功' })
              callback()
            } else {
              this.$Message.error({ content: `删除失败，${res.data.errormsg}` })
            }
          } else {
            console.log('服务器扑街！')
          }
        })
    },
    async exportList() {
      let resData = await this.getList(this.total)
      this.$refs.table.exportCsv({
        filename: this.isSwim ? '泳教班管理' : '私教班管理',
        columns: this.cardColumns.filter((col, index) => {
          return col.key && col.key !== 'action'
        }),
        data: resData
      })
    }
  }
}
</script>
