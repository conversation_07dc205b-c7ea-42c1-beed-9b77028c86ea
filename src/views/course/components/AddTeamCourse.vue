<template>
<div>
<Modal v-model="showAdd" :mask-closable="false" :title="isEdit?'编辑班级排课':'新增班级排课'" width="600">
  <div class="top-add" v-if="!isEdit">
    <div @click="addItem">
      <Icon type="md-add" color="#18BF6A" size="16" />
      新增一节排课
    </div>
    <div @click="addMoreItem">
      <Icon type="ios-browsers-outline" color="#18BF6A" size="16" />
      批量排课
    </div>
    
  </div>
  <div class="item-wrap" v-for="(item, index) in itemList" :key="index">
    <DatePicker class="w160" v-model="itemList[index].date" :options="disableDayAfter" type="date" placeholder="日期" :editable="false" format="yyyy-MM-dd" @on-change="changedate($event, index)"></DatePicker>
    <TimePicker class="w100" v-model="itemList[index].s_time" format="HH:mm" placeholder="时间"></TimePicker>
    <!-- <saleSelect class="w100" v-model="itemList[index].coach_id" placeholder="教练" :isPtCoach="!isSwim" :isSwimCoach="isSwim" :isMembership="false" :showCoachType="false" @on-change="changeSale($event, index)" :labelInValue="true"></saleSelect> -->
    <Select class="w100" v-model="itemList[index].coach_id" @on-change="changeSale($event, index)"  :labelInValue="true" placeholder="教练" filterable>
      <Option v-for="item in coachList" :value="item.coach_id" :key="item.coach_id">{{ item.coach_name }}</Option>
    </Select>
    <Select class="w100" v-model="itemList[index].class_root_id" @on-change="roomChange($event, index)"  :labelInValue="true" placeholder="教室" filterable>
      <Option v-for="item in roomList" :value="item.id" :key="item.id">{{ item.classroom_name }}</Option>
    </Select>
    <Icon v-if="index>0" @click="deleteItem(index)" class="delete-icon" title="删除" color="red" size="16" type="md-remove-circle"/>
  </div>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doAdd">保存</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
<AddMoreTeamCourse  :id="id" :cardId="cardId" :info="info" :isSwim="isSwim" v-model="isShowAddMore" @on-success="onMoreSuccess"/>
 </div>
</template>
<script>
const initCourseItem = {
  date: '',
  s_time: '',
  coach_id: '',
  coach_name: '',
  class_root_name: '',
  class_root_id: ''
}
import { formatDate } from 'utils'
import saleSelect from 'src/components/membership/salesSelect'
import AddMoreTeamCourse from './AddMoreTeamCourse'
export default {
  name: 'AddTeamCourse',
  components: {
    saleSelect,
    AddMoreTeamCourse
  },
  data() {
    return {
      isShowAddMore: false,
      disableDayAfter: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now()- 24*60*60*1000;
        }
      },
      roomList: [],
      coachList: [],
      itemList: [{...initCourseItem}]
    }
  },
  props: {
    id: {
      type: [Number, String]
    },
    cardId: {
      type: [Number, String]
    },
    value: {
      type: Boolean
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    },
    isSwim: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.itemList = [{...initCourseItem}]
      } else {
        if(this.isEdit) {
          this.itemList= [{
            teamclass_schedule_id: this.id,
            date: this.info.date,
            s_time: this.info.s_time,
            coach_id: this.info.coach_id,
            coach_name: this.info.coach_name || '',
            class_root_name: this.info.class_root_name || '',
            class_root_id: this.info.class_root_id || ''
          }]
        }
      }
    },
    cardId: {
      handler(val) {
        if(val) {
          this.getCoachList()
        }
      },
      immediate: true
    }
  },
  created() {
    this.getRoomList()
  },
  methods: {
    changedate(date, index) {
      this.itemList[index].date = formatDate(new Date(date), 'yyyy-MM-dd')
    },
    changeSale(info, index) {
      this.itemList[index].coach_id = info?info.value:''
      this.itemList[index].coach_name = info?info.label:''
    },
    roomChange(info, index) {
      this.itemList[index].class_root_id = info?info.value:''
      this.itemList[index].class_root_name = info?info.label:''
    },
    addItem() {
      const lastItem = this.itemList[this.itemList.length-1]
      this.itemList.push({
        date: '',
        s_time: lastItem.s_time,
        coach_id: lastItem.coach_id,
        coach_name: lastItem.coach_name,
        class_root_name: lastItem.class_root_name,
        class_root_id: lastItem.class_root_id
      })
    },
    addMoreItem() {
      const lastItem = this.itemList[this.itemList.length-1]
      this.isShowAddMore = true
    },
    deleteItem(index) {
      this.itemList.splice(index, 1)
    },
    getCoachList() {
      this.$service
        .get('/Web/Member/get_all_private_coach_list?card_id='+this.cardId)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.coachList = res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getRoomList() {
      this.$service
        .post('/Web/TeamclassSwim/get_classroom')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.roomList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    onMoreSuccess() {
      this.showAdd = false
      this.$emit('on-success')
    },
    doAdd() {
      let strArr = []
      for (const item of this.itemList) {
        strArr.push(''+ item.date + item.s_time + item.coach_id + item.class_root_id)
      }
      if(strArr.length > 0 && new Set(strArr).size !== strArr.length) {
        this.$Message.error('有重复的课程')
        return false;
      }
      let postData = null
      if(this.isEdit) {
        if(typeof this.itemList[0].date === 'object') {
          this.itemList[0].date = formatDate(new Date(this.itemList[0].date), 'yyyy-MM-dd')
        }
        postData = {
          ...this.itemList[0]
        }
      } else {
        postData = { teamclass_id: this.id, schedule_data: this.itemList}
      }
      this.$service
        .post(`/Web/${this.isSwim?'TeamclassSwim':'TeamclassPrivate'}/${this.isEdit?'teamclass_schedule_up':'teamclass_schedule_add'}`, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.showAdd = false
            this.$emit('on-success')
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.top-add {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #18BF6A;
  line-height: 22px;
  margin-bottom: 24px;
  div {
    margin-right: 20px;
  }
  /deep/ .ivu-icon {
    font-weight: bold;
  }
}
.item-wrap {
  margin-bottom: 17px;
  .w160 {
    width: 160px;
  }
  .w100 {
    width: 100px;
  }
  .delete-icon {
    cursor: pointer;
    margin-left: 20px;
  }
}
</style>
