<template>
<Modal v-model="showAdd" title="排课详情">
  <div class="main-wrap">
    <div class="top-wrap">
      <div class="top-left">
        {{info.curriculum_name}}
      </div>
      <div>
        <span class="tit-text">时间</span> <span class="class-text">{{info.date}} {{info.s_time}}</span> 
        <span class="tit-text">教练</span> <span class="class-text">{{info.coach_name}}</span>
      </div>
    </div>
    <div>
      <UserBox v-model="info.user_sign" showSignStatus/>
    </div>
  </div>
  
  <div slot="footer"></div>
</Modal>
</template>
<script>
import UserBox from './UserBox'
export default {
  name: 'ScheduleDetailModal',
  components: {
    UserBox
  },
  data() {
    return {
      roomList: [],
      info: {
        date: '',
        s_time: '',
        coach_name: '',
        user_sign: []
      }
    }
  },
  props: {
    id: {
      type: [Number, String]
    },
    value: {
      type: Boolean
    },
    isSwim: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
      } else {
        this.getDetail()
      }
    }
  },
  created() {
  },
  methods: {
    getDetail() {
      this.$service
        .post(this.isSwim?'/Web/TeamclassSwim/teamclass_schedule_info':'/Web/TeamclassPrivate/teamclass_schedule_info', {
          teamclass_schedule_id: this.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.info = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.main-wrap {
  width: 434px;
  margin: 0 auto;
}
.top-left {
  width: 120px;
}
.top-wrap {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.tit-text {
  font-size: 14px;
  color: #52A6EA;
  margin-left: 10px;
  margin-right: 5px;
}
.class-text {
  font-size: 14px;
  font-weight: bold;
  color: #434343;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
