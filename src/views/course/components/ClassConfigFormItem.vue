<template>
  <div
    class="form-item-row"
    :class="{'card-type-1': item.card_type_id == 1 }"
  >
    <slot v-if="hasName" name="name">
      <div v-if="configType === 1" class="tag-label">
        <span
          class="name text_overflow"
          :title="item.myCustom.name || 'null'">
          {{ item.myCustom.name || 'null' }}
        </span>
        <span class="tip-btn" @click.stop="handleShowCardsModal">查看</span>
        <!-- {{ item.myCustom.typeName }} -->
      </div>
      <span v-else-if="configType === 2" class="name text_overflow" :title="item.myCustom.name || 'null'">{{ item.myCustom.name || 'null' }}</span>
    </slot>

    <template v-if="(item.card_type_id != 1 && item.todeduct_type_detail != 1) || item.tagType">
      <template v-if="configType === 1 && tagType === 'ms'">
        <span style="margin: 0 6px;">所含次卡每次预约扣</span>
        <FormItem
          :prop="itemProps + '.todeduct_c'"
          :rules="[{required: !item.tagType, type: 'number', message: '请填写扣除数值', trigger: 'change'}]">
          <InputNumber
            v-model="item.todeduct_c"
            style="width:80px;"
            :min="0"
            :max="999"
            :precision="0"
            :active-change="false"
            @on-change="handleChangeItem"
          />
        </FormItem>
        <span style="margin: 0 6px;">次/人、储值卡每次预约扣</span>
        <FormItem
          :prop="itemProps + '.todeduct_y'"
          :rules="[{required: !item.tagType, type: 'number', message: '请填写扣除数值', trigger: 'change'}]">
          <InputNumber
            v-model="item.todeduct_y"
            style="width:80px;"
            :min="0"
            :max="999"
            :precision="2"
            :active-change="false"
            @on-change="handleChangeItem"
          />
        </FormItem>
        <span style="margin: 0 6px;">元/人，可预约</span>
      </template>
      <template v-else>
        <span style="margin: 0 6px;">每次预约扣</span>
        <FormItem
          :prop="itemProps + '.' + (itemType ? 'todeduct_y' : 'todeduct_c')"
          :rules="[{required: !item.tagType, type: 'number', message: '请填写扣除数值', trigger: 'change'}]">
          <InputNumber
            v-model="item[itemType ? 'todeduct_y' : 'todeduct_c']"
            style="width:80px;"
            :min="0"
            :max="999"
            :precision="itemType ? 2 : 0"
            :active-change="false"
            @on-change="handleChangeItem"
          />
        </FormItem>
        <span style="margin: 0 6px;">{{ itemType ? '元' : '次' }}/人，可预约</span>
      </template>
      <FormItem
        :prop="itemProps + '.maxresv_num'"
        :rules="[{required: !item.tagType, type: 'number', message: '请填写最高预约人数', trigger: 'change'}]">
        <InputNumber
          v-model="item.maxresv_num"
          style="width:80px;"
          :min="0"
          :max="999"
          :precision="0"
          :active-change="false"
          @on-change="handleChangeItem"
        />
      </FormItem>
      <span style="margin: 0 6px;">人</span>
    </template>

    <Icon
      v-if="hasDelete"
      style="margin-left:12px;cursor: pointer;"
      type="md-close-circle"
      title="删除"
      size="20"
      color="#d9544f"
      @click.native="handleDeleteConfigItem" />
  </div>
</template>

<script>
  export default {
    name: 'ClassConfigFormItem',

    props: {
      item: {
        type: Object,
        required: true
      },
      configType: { // 1标签 2卡种 3卡类型（此处未使用）
        validator: (value) => [1, 2].includes(value),
        required: true,
      },
      tagType: {
        validator: (value) => ['ms', 'pt', 'swim'].includes(value),
        required: true,
      },
      itemProps: {
        type: String,
        default: ''
      },
      hasName: {
        type: Boolean,
        default: true
      },
      hasDelete: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {

      }
    },
    computed: {
      // item.tagType 批量填充时
      itemType() {
        const { item, configType } = this
        return configType === 2 &&
          (item.tagType
            ? item.todeduct_y !== undefined
            : (item.todeduct_type_detail || item.myCustom.todeduct_type_detail) == '3')
      },
    },

    methods: {
      handleChangeItem() {},
      handleDeleteConfigItem() {
        this.$emit('delete-item', { ...this.item })
      },
      handleShowCardsModal() {
        this.$emit('show-cards')
      }
    }
  }
</script>

<style lang="less" scoped>
// 由父组件统一控制
</style>
