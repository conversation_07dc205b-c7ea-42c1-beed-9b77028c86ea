<template>
  <div class="customized-tabs">
    <Tabs @on-click="clickTabs" :value="activeIndex">
      <TabPane label="排课" name="0">
        <PrivateClass v-if="activated.includes('0')" />
      </TabPane>
      <TabPane label="排课记录" name="1">
        <ArrangeRecord v-if="activated.includes('1')" :is-swim-flag="props.isSwimFlag" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import PrivateClass from './privateClass.vue';
import ArrangeRecord from './arrangeRecord.vue';

const activeIndex = ref('0');
const activated = ref(['0']);
const clickTabs = (index) => {
  activeIndex.value = index;
  const active = document.querySelector('.ivu-tabs-ink-bar');
  active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
  sessionStorage.setItem('cardListActive', index);
  if (!activated.value.includes(index)) {
    activated.value.push(index);
  }
}

const props = defineProps({
  isSwimFlag: Boolean
})
</script>

<style lang="less" scoped></style>