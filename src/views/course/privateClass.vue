/*
 * @Author: linghucq
 * @Date: 2017-08-22 08:51:57
 * @Last Modified by: linghucq
 * @Last Modified time: 2018-03-20 16:10:07
 */
<template>
  <div class="course-private-class">
    <header>
      <div class="left">
        <div class="picker">
          <button @click="changeDay(-1)">前一天</button>
          <Date-picker type="date" v-model="datePicker" :editable="false" :clearable="false"></Date-picker>
          <button @click="changeDay(1)">后一天</button>
        </div>
        <Input class="coach-select" v-model="searchAlpha" placeholder="会员名称/电话" @on-enter="getCourseList" :clearable="true" @on-clear="getCourseList" />
        <Select class="coach-select" v-model="selectedCoach" placeholder="教练" filterable clearable>
          <Option value="">所有教练</Option>
          <Option v-for="(coach, coachIndex) in allCoaches" :key="coachIndex" :value="coach.coach_id">{{ coach.coach_name }}
          </Option>
        </Select>
        <Select class="class-select" v-model="selectedCourse" placeholder="课程" filterable clearable>
          <Option value="">所有课程</Option>
          <Option style="overflow-x: hidden" v-for="(course, courseIndex) in allCourses" :key="courseIndex" :value="course.id">{{ course.name }}
          </Option>
        </Select>
        <Select class="class-select" v-model="selectedStatus" placeholder="课程状态" filterable clearable>
          <Option :value="item.value" v-for="item in statusList" :key="item.value">{{ item.label }}</Option>
        </Select>
        <Select class="class-select" v-model="selectedNoType" placeholder="授课方式" filterable clearable>
          <Option :value="0">授课方式</Option>
          <Option :value="2">1对1</Option>
          <Option :value="1">1对多</Option>
        </Select>
      </div>
    </header>
    <main ref="mainContent" @mousewheel="mainScrool">
      <div class="table-header" :style="{width:timeArray.length*100+'px'}">
        <div class="line">
          <div class="name">教练
            <Poptip trigger="hover" placement="right" content="设置成私人教练，并且向会员公开才会显示">
              <Icon size="16" type="ios-help-circle" style="padding-left: 5px" color="#F7DC6F"></Icon>
            </Poptip>
          </div>
          <div class="time">
            <span v-for="time in timeArray" :key="time">{{time}}</span>
          </div>
        </div>
      </div>
      <div class="table-body hidden-line" :style="{width:timeArray.length*100+'px'}">
        <div style="width: 100%">
          <div class="line">
            <div class="name"></div>
            <div style="width: 100%; padding-right: 100px">
              <div class="work-time">
                <Poptip placement="left-start" class="time-line" :style="{left: calPosition(timeNow) + '%'}" trigger="hover">
                  <h3 style="color: #57a3f3" slot="content">
                    当前时间 {{timeNow}}
                  </h3>
                </Poptip>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="table-body" v-if="coachList" :style="{width:timeArray.length*100+'px'}">
        <div class="coach-line" v-for="(coach, coachIndex) in coachList" :key="coach.coach_id">
          <div class="line">
            <div class="name" @click.ctrl="toCoachDetail(coach.coach_id)">
              <div class="fix-name" :style="{left:nameLeft}">{{ coach.coach_name }}</div>
            </div>
            <div class="time">
              <div class="work-time">
                <div v-for="(time, timeIndex) in coach.working_hours" :key="timeIndex" class="working-time" :title="coach.suspend_status == 1 ? '暂停中' : '工作时间'" :class="coach.suspend_status == 1 ? 'pause-img' : 'working-img'" :style="{left: calPosition(time[0]) + '%', width: calPosition(time[1]) - calPosition(time[0]) + '%'}"></div>
                <singleClass v-for="course in coach.class_list" :key="course.pt_schedule_id" v-if="coach.class_list" :timeArrayLength="timeArray.length" :date="dateString" :coach="coach.coach_name" :type="course.status" :coachStatus="coach.suspend_status" :classData="course">
                </singleClass>
              </div>
              <div class="coach-buttons" :style="{left:buttonLeft}">
                <template v-if="coach.suspend_status == 0">
                  <img src="~assets/img/coach-reserve.png" alt="预约" title="预约" @click="addNewClass(coach.coach_id)">
                  <img src="~assets/img/coach-edit.png" alt="教练设置" title="教练设置" @click="clickCoachSetting(coach.coach_id)">
                  <img src="~assets/img/coach-pause.png" alt="暂停" title="暂停" @click="settingPause(coach.coach_id, coach.coach_name)">
                </template>
                <template v-if="coach.suspend_status == 1">
                  <img src="~assets/img/coach-open.png" style="margin-left: auto" title="启用" @click="settingStart(coach.coach_id)">
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal :title="isSwim?'泳教课预约':'私教课预约'" :mask-closable="false" v-model="newReserve" width="800" class="reserve-class private-modal">
        <Form :model="newReserveData" v-if="newReserveData" style="padding: 0 50px" :label-width="80">
          <Form-item label="日期">
            <div style="font-size: 14px">{{dateString}}</div>
          </Form-item>
          <Form-item label="教练">
            <Select v-model="newReserveData.coach_id" class="selects" @on-change="coachChanged" transfer filterable>
              <Option v-for="coach in filteredCoaches" :key="coach.coach_id" :value="coach.coach_id">{{coach.name}}</Option>
            </Select>
          </Form-item>
          <Form-item label="会员">
            <UserPtSearchNew ref="userSearchRef" v-if="newReserve" @on-change="userSelected" @on-class-change="classChange" :search="userSearch" :isUserId="isUserId" @isUserId="(val)=> isUserId = val" :isSwim="isSwim"></UserPtSearchNew>
          </Form-item>
          <Form-item v-if="selectedUser" label="预约">
            <RadioGroup v-model="newReserveData.type" @on-change="handleTypeChanged">
              <Radio :label="1">用卡预约</Radio>
              <Radio :label="2">直接付费预约</Radio>
            </RadioGroup>
          </Form-item>
          <Form-item label="课程" class="selects" v-if="selectedUser && newReserveData.type === 1">
            <Select v-if="selectedUser.card_list.length" v-model="newReserveData.index" @on-change="cardSelected" transfer>
              <Option v-for="(card, index) in selectedUser.card_list" :key="index" :value="index">
                {{card.name}}
              </Option>
            </Select>
            <div v-else style="text-align: center">
              <img src="~assets/img/stat_null.png" mode="widthFix" style="width: 208px" alt="暂无数据" />
              <Alert type="error" style="margin-top: 20px;">您暂无对应课程, 无法预约!</Alert>
            </div>
          </Form-item>
          <Form-item label="课程" class="selects" v-if="selectedUser && newReserveData.type === 2">
            <Select v-if="oneTimePayCourseList.length" v-model="newReserveData.index" @on-change="cardSelected" transfer>
              <Option v-for="(course, index) in oneTimePayCourseList" :key="index" :value="index">
                {{ course.name }} (时长{{ course.class_duration }}分钟, 标准价{{ course.single_price }}元)
              </Option>
            </Select>
            <div v-else style="text-align: center">
              <img src="~assets/img/stat_null.png" mode="widthFix" style="width: 208px" alt="暂无数据" />
              <Alert type="error" style="margin-top: 20px;">此教练不支持单节付费课方案, 无法预约!</Alert>
            </div>
          </Form-item>
          <Form-item label="时间" v-if="radioTimeData">
            <radioTime :date="datePicker" :data="radioTimeData" :courseActiveTime="courseActiveTime" v-on:selectedTime="classTimeSelected"></radioTime>
          </Form-item>
          <template v-if="newReserveData.type === 2 && oneTimePayCourseList.length && oneTimePayPrice && radioTimeData">
            <Form-item label="标准价格">
              <div class="price-box" style="font-size: 20px">{{ oneTimePayPrice?.original_amount }}</div>
            </Form-item>
            <Form-item label="持卡优惠" v-if="oneTimePayPrice">
              <div class="price-box">
                <!-- <Tag v-if="oneTimePayPrice?.card_name" size="small">{{ oneTimePayPrice?.card_name }}</Tag> -->
                <span v-if="oneTimePayPrice?.card_name" style="font-size: 14px; color: #999">{{ oneTimePayPrice?.card_name }}</span>
                <span v-if="oneTimePayPrice?.discount_amount" style="color: red; font-size: 14px; margin-left: 10px">{{ oneTimePayPrice?.discount_amount }}</span>
              </div>
            </Form-item>
            <Divider></Divider>
            <Form-item label="应收金额">
              <div class="price-box" style="color: red; font-size: 24px;">{{ oneTimePayPrice?.pay_amount }}</div>
            </Form-item>
            <Form-item v-if="oneTimePayPrice" label="支付方式">
              <pay-type-list
                v-model="oneTimePayType"
                :amount="Number(oneTimePayPrice?.pay_amount)"
                :sqbOption="{ describe: `私教课预约[${coachName}]`, serviceType: 2, isEqual: false }"
                :userId="newReserveData.user_id"
                :showCardPay="true"
                :isMaxAmount="true"
                style="width: 100%"
                @on-card-change="handleCardChange"
              />
            </Form-item>
          </template>
        </Form>
        <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
          <Button type="success" :disabled="newReserveData && !newReserveData.begin_date" @click="handleConfirmAddClass">确定</Button>
          <Button @click="newReserve = false">取消</Button>
        </div>
      </Modal>
      <Modal title="教练个人设置" :mask-closable="false" class="private-modal" v-model="coachSetting">
        <Form :model="coachSettingData" v-if="coachSettingData" label-position="right" :label-width="100" style="padding: 0 30px">
          <Form-item label="教练">
            <Select v-model="coachSettingData.coach_id" class="selects" @on-change="getCoachSetting" filterable>
              <Option v-for="coach in filteredCoaches" :key="coach.coach_id" :value="coach.coach_id">{{coach.name}}</Option>
            </Select>
          </Form-item>
          <Form-item label="每日上课上限">
            <Input v-model="coachSettingData.reservation_max" />
          </Form-item>
          <Form-item label="工作时间">
            <ul>
              <li style="display: flex; align-items: center; height: 30px; margin-bottom: 8px" v-for="(range, index) in coachSettingData.working_hours" :key="index">
                <Time-picker v-model="coachSettingData.working_hours[index]" :clearable="false" :editable="false" hide-disabled-options :disabled-hours="calDisabledTime(calDisabledHour(coachSettingData.working_hours, index))" :disabled-minutes="disabledMinutes" type="timerange" format="HH:mm" placement="top" confirm>
                </Time-picker>
                <div @click="deleteWorkTime(coachSettingData.working_hours, index)" class="button-icon"><Icon type="ios-trash" color="#fc88c2" size="16"></Icon>删除</div>
                <div @click="addWorkTime(coachSettingData.working_hours)" class="button-icon" v-if="index == coachSettingData.working_hours.length - 1"><Icon type="md-add-circle" color="#a4ddb4" size="16"></Icon>新增</div>
              </li>
            </ul>
          </Form-item>
          <Form-item label="持续时间" class="flex-item">
            <Date-picker type="date" format="yyyy-MM-dd" v-model="coachSettingData.keep_begin_time" :editable="false"></Date-picker>
            <span style="padding: 0 10px; font-size: 14px">到</span>
            <Date-picker type="date" format="yyyy-MM-dd" :options="settingDateOption" v-model="coachSettingData.keep_end_time" :editable="false"></Date-picker>
          </Form-item>
        </Form>
        <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
          <Button type="success" @click="postCoachSetting">确定</Button>
          <Button @click="coachSetting = false">取消</Button>
        </div>
      </Modal>
      <Modal title="暂停约课" :mask-closable="false" class="private-modal" v-model="pauseSetting">
        <Form :model="pauseSettingData" v-if="pauseSettingData" style="padding: 0 40px" :label-width="68" label-position="right">
          <Form-item label="注:" style="color: red">
            <div style="font-size: 14px">在指定周期内会员无法再预约该教练的私教课程<br>已经预约课程不会取消，请处理好会员交接事宜</div>
          </Form-item>
          <Form-item label="教练">
            <div style="font-size: 14px">{{pauseSettingData.coach_name}}</div>
          </Form-item>
          <Form-item label="持续时间" class="flex-item">
            <Date-picker type="date" format="yyyy-MM-dd" v-model="pauseSettingData.suspend_begin_time" :editable="false"></Date-picker>
            <span style="padding: 0 10px; font-size: 14px">到</span>
            <Date-picker type="date" format="yyyy-MM-dd" :options="pauseDateOption" v-model="pauseSettingData.suspend_end_time" :editable="false"></Date-picker>
          </Form-item>
        </Form>
        <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
          <Button type="success" @click="confirmPause">确定</Button>
          <Button @click="pauseSetting = false">取消</Button>
        </div>
      </Modal>
      <Modal v-model="isDelCourse" :mask-closable="false" v-if="isDelCourseData">
        <div style="font-size: 16px; text-align: center; padding: 50px 0 20px; font-weight: bold">
          确认删除
          <span style="color: red">"教练:{{isDelCourseData.coachName}}"</span>,
          <span style="color: red">"会员:{{isDelCourseData.username}}"</span> 的
          <span style="color: red">"{{isDelCourseData.cardName}}"</span> 吗？
        </div>
        <div slot="footer" class="modal-buttons" style="padding-bottom: 30px">
          <Button type="success" @click="doDelCourse(isDelCourseData.id)">确定</Button>
          <Button @click="isDelCourse = false">取消</Button>
        </div>
      </Modal>
    </main>
    <footer>
      <div class="total" v-if="totalCount">
        <strong>合计:</strong>
        <span>已约
          <strong>{{ totalCount.status1_count }}</strong>节</span>
        <span>已上
          <strong>{{ totalCount.status2_count }}</strong>节</span>
        <span>完成
          <strong>{{ totalCount.status4_count }}</strong>节</span>
        <span>未上
          <strong>{{ totalCount.status3_count }}</strong>人</span>
      </div>
    </footer>
  </div>
</template>

<script>
  import singleClass from 'src/components/privateClassArrange/singleClass.vue';
  import radioTime from 'src/components/picker/radioTime.vue';
  import { formatDate } from 'utils';
  import EventBus from '@/utils/eventBus.js'
  import UserPtSearchNew from 'src/components/form/UserPtSearchNew.vue';
  import PayTypeList from 'components/form/PayTypeList.vue'
  import _ from 'lodash-es'

  export default {
    name: 'coachList',
    data() {
      let that = this;
      return {
        designatedCoach: true,
        timeArray: [],
        datePicker: new Date(),
        settingDateOption: {
          disabledDate(date) {
            return date && date.valueOf() < Date.parse(that.coachSettingData.keep_begin_time) - 1 * 24 * 3600 * 1000;
          }
        },
        userId: this.$route.query.userId || '',
        pauseDateOption: {
          disabledDate(date) {
            return date && date.valueOf() < Date.parse(that.pauseSettingData.suspend_begin_time) - 1 * 24 * 3600 * 1000;
          }
        },
        coachLinkUrl: `/v2/employee/coach/add?id=`,
        userSearch: '',
        isUserId: false,
        cardSearch: '',
        courseActiveTime: '',
        selectedCoach: null,
        selectedCourse: null,
        allCourses: [],
        filteredCoaches: [],
        timeNow: '',
        totalSetting: false,
        isSwim: 0,
        newReserve: false,
        coachSetting: false,
        pauseSetting: false,
        newTimeRange: true,
        isDelCourse: false,
        searchingUser: false,

        newReserveData: null,
        coachSettingData: null,
        pauseSettingData: null,
        isDelCourseData: null,
        totalSettingData: null,
        radioTimeData: null,
        coachList: null, // 私教课教练及其课程
        allUsers: null,
        searchUserList: null,
        selectedUser: null,
        selectedUserId: '',
        totalCount: null,
        allCoaches: null,
        buttonLeft: '1000px',
        nameLeft: '0px',

        // one time pay
        oneTimePayCourseList: [],
        oneTimePayPrice: null,
        oneTimePayType: [],
        oneTimePayNoCardPrice: null,
        oneTimePayCardUserId: '',

        statusList: [
          {
            label: '已约',
            color: 'purple',
            value: 1
          },
          {
            label: '已上',
            color: 'orange',
            value: 2
          },
          {
            label: '未上',
            color: 'red',
            value: 3
          },
          {
            label: '完成',
            color: 'green',
            value: 4
          }
        ],
        selectedStatus: '',
        selectedNoType: 0,
        searchAlpha: '',
        isWatch: true
      };
    },
    components: {
      UserPtSearchNew,
      singleClass,
      radioTime,
      PayTypeList
    },
    created() {
      if (this.$route.query.date) {
        const newDate = new Date(Number(this.$route.query.date)*1000);
        this.datePicker = newDate;
      }
      this.init()
      EventBus.$on("courseCopy", info => {
        this.copyCourse(info.coach_id, info.user_id, info.username)
      })
      EventBus.$on("courseDel", info => {
        this.delCourse(info.pt_schedule_id, info.coach_name, info.username, info.class_name)
      })
    },
    beforeDestroy () {
      EventBus.$off('courseCopy')
      EventBus.$off('courseDel')
    },
    mounted() {
      const hour = new Date().getHours()
      let walkStep = 0
      if (hour <= 6) {
        walkStep = 0
      } else if (6 < hour && hour <= 10) {
        walkStep = 4
      } else if (10 < hour && hour <= 14) {
        walkStep = 8
      } else if (14 < hour && hour <= 18) {
        walkStep = 12
      } else {
        walkStep = 16
      }
      this.$refs.mainContent.scrollLeft = walkStep * 90
      this.$refs.mainContent.addEventListener('scroll', this.mainScrool);
      this.buttonLeft = this.$refs.mainContent.clientWidth- 180 + 'px'
      this.nameLeft = '0px'

      setTimeout(() => {
        const { courseId, coachName, username, cardName } = this.$route.params
        if (courseId) {
          this.delCourse(courseId, coachName, username, cardName)
        }
      }, 500)
    },
    computed: {
      disabledMinutes() {
        let arr = [],
          ten = 5,
          bit = 9;
        while (ten >= 0) {
          while (bit > 0) {
            arr.push(ten * 10 + bit);
            bit--;
          }
          ten--;
          bit = 9;
        }
        return arr;
      },
      dateString() {
        let date = this.datePicker,
          year = date.getFullYear(),
          month = date.getMonth() + 1,
          day = date.getDate();
        month = month < 10 ? `0${month}` : month;
        day = day < 10 ? `0${day}` : day;
        return `${year}-${month}-${day}`;
      },
      coachName() {
        const coach = this.filteredCoaches.find(item => item.coach_id === this.newReserveData.coach_id)
        return coach ? coach.name: ''
      }
    },
    methods: {
      mainScrool() {
        const mainContent = this.$refs.mainContent
        let scrolled = mainContent.scrollLeft;
        this.buttonLeft = mainContent.clientWidth + mainContent.scrollLeft- 180 + 'px'
        this.nameLeft = mainContent.scrollLeft + 'px'
      },
      init() {
        this.isSwim = this.$route.name === '泳教排课' ? 1 : 0
        this.calTimeArray();
        this.timeLineInterval();
        this.getCourseList();
        this.getAllCourses();
        this.getAllPrivateCoach();
        this.getMapCoachList();
        if (this.$route.query.userId) {
          this.addNewClass('', this.$route.query.userId);
        }
      },
      calTimeArray(startAt = 0, endAt = 23) {
        this.timeArray = Array.from({ length: endAt - startAt + 1 }, (item, index) => `${startAt + index}:00`);
      },
      toCoachDetail(coachId) {
        window.open(`${this.coachLinkUrl}${coachId}`);
      },
      // 月份和天数小于 10
      isLeftTen(num) {
        return num < 10 ? `0${num}` : num;
      },
      // 获取全部排课
      getCourseList() {
        const url = this.isSwim ?  '/Web/PtSchedule/swim_schedule_list' : '/Web/PtSchedule/pt_schedule_list'
        this.$service
          .post(url, {
          is_swim: this.isSwim,
          coach_id: this.selectedCoach,
          card_id: this.selectedCourse,
          day_date: this.dateString,
          search: this.searchAlpha,
          class_user_no_type : this.selectedNoType,
          status: this.selectedStatus
        })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.coachList = this.calBegTime(res.data.data.list);
              this.totalCount = res.data.data.info;
              if (!this.coachList.length) {
                this.$Message.error('暂无排课信息');
                return;
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 获取所有教练筛选项
      getAllPrivateCoach() {
        this.$service.post('/Web/PtSchedule/get_pt_schedule_coach', {
          is_swim: this.isSwim,
          day_date: this.dateString
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.allCoaches = res.data.data;
          }
        });
      },
      // 获取 header 下拉筛选所有课程
      getAllCourses() {
        const url = '/Web/PtSchedule/pt_user_all_card';
        this.$service
          .post(url, { is_swim: this.isSwim })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.allCourses = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 排课, 获取非暂停教练
      getMapCoachList() {
        const url = '/Web/Coach/map_coach_list';
        let postData = {
          is_swim: this.isSwim,
          date: this.dateString
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.filteredCoaches = res.data.data.list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },

      calBegTime(coachList) {
        coachList.forEach(coach => {
          if (coach.class_list) {
            coach.class_list.forEach(course => {
              this.setTimeInfo(course);
              if (course.class_list) {
                course.class_list.forEach(sub => {
                  this.setTimeInfo(sub);
                });
              }
            });
          }
        });
        return coachList;
      },
      setTimeInfo(info) {
        let beginTime = `${info.begin_time}000`;
        let endTime = `${info.end_time}000`;
        let date = new Date(parseInt(beginTime));
        let endDate = new Date(parseInt(endTime));
        let hour = this.isLeftTen(date.getHours());
        let min = this.isLeftTen(date.getMinutes());
        let endHour = this.isLeftTen(endDate.getHours());
        let endMin = this.isLeftTen(endDate.getMinutes());
        info.beg_time = `${hour}:${min}`;
        info.endTime = `${endHour}:${endMin}`;
      },
      getOneTimePayCourseList() {
        if (this.newReserveData.coach_id === '') return;

        this.$service.post('/Web/PtChargePlan/get_coach_pt_card_list', {
          bus_id: this.$store.state.busId,
          coach_id: this.newReserveData.coach_id,
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.oneTimePayCourseList = res.data.data;
          }
        })
      },
      getOneTimePayPrice(course, hasValueCard = false, reload = false) {
        if (!hasValueCard && this.oneTimePayNoCardPrice && !reload) {
          this.oneTimePayPrice = _.cloneDeep(this.oneTimePayNoCardPrice);
          return;
        }

        this.$service.post('/Web/PtChargePlan/get_pt_card_low_price', {
          bus_id: this.$store.state.busId,
          user_id: this.selectedUser.user_id,
          card_id: course.id,
          sort: course.sort,
          pt_charge_plan_id: course.pt_charge_plan_id,
          is_stored: hasValueCard ? 1 : 0,
          card_user_id: course.card_user_id,
          stored_card_id: course.stored_card_id,
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.oneTimePayPrice = res.data.data;
            if (!hasValueCard) {
              this.oneTimePayNoCardPrice = _.cloneDeep(res.data.data);
            }

            let change = false;
            const newPayType = _.cloneDeep(this.oneTimePayType);
            newPayType.forEach(item => {
              if (item.pay_type === 8) {
                change = true;
                item.card_user_id = this.oneTimePayPrice.card_user_id;
                item.amount = this.oneTimePayPrice.pay_amount;
                item.autoSelect = true;
              }
            })
            
            if (change) {
              this.oneTimePayCardUserId = this.oneTimePayPrice.card_user_id;
              this.oneTimePayType = newPayType;
            } else {
              this.oneTimePayCardUserId = '';
            }
          }
        })
      },
      // 添加预约
      addNewClass(coach_id, user_id = '') {
        this.newReserve = true;
        this.newReserveData = {
          coach_id,
          id: '',
          user_id,
          card_id: '',
          begin_date: '',
          card_user_id: '',
          action: 'add',
          index: '',
          type: 1,
        };

        this.oneTimePayCourseList = [];
        this.oneTimePayPrice = null;
        this.getOneTimePayCourseList()
      },
      coachChanged() {
        this.userSearch = '';
        this.selectedUser = null;
        this.radioTimeData = null;
        this.newReserveData.user_id = '';
        this.newReserveData.index = '';
        this.newReserveData.type = 1;
        this.$refs.userSearchRef.$refs.selectRef.values = [];

        // if select coach, fetch one time pay cpt course list
        this.getOneTimePayCourseList()
      },
      classChange(value) {
        this.designatedCoach = value;
      },
      // 选中会员
      userSelected(selectedUser) {
        if (!selectedUser) {
          this.selectedUser = this.radioTimeData = null;

          this.userSearch = '';
          // this.selectedUser = null;
          // this.radioTimeData = null;
          this.newReserveData.user_id = '';
          this.newReserveData.index = '';
          this.newReserveData.type = 1;
          this.$refs.userSearchRef.$refs.selectRef.values = [];
          return;
        }
        // if (!selectedUser.card_list.length) {
        //   this.$Message.error(`该会员没有适用的${this.isSwim?'泳教':'私教'}卡`);
        //   this.selectedUser = this.radioTimeData = null;
        //   return;
        // }
        this.selectedUser = selectedUser;
        if (this.newReserveData) {
          this.newReserveData.user_id = selectedUser.user_id;
          this.newReserveData.index = '';
          this.newReserveData.type = 1;
        }
        this.radioTimeData = null;
      },
      // 选中会员卡
      cardSelected(index = '') {
        if (index === '') return;

        this.newReserveData.begin_date = '';

        let selectedCard = this.selectedUser.card_list[index];
        if (this.designatedCoach && this.newReserveData.type === 1) {
          if (Array.isArray(this.selectedUser.card_list)) {
            let findCoach = Array.isArray(selectedCard.class_coach_id) ? selectedCard.class_coach_id.indexOf(this.newReserveData.coach_id)!== -1 : selectedCard.class_coach_id == this.newReserveData.coach_id
            if (!findCoach) {
              this.$Message.error('该教练不是会员的上课教练');
              this.radioTimeData = false;
              return false;
            }
          }
        }

        if (this.newReserveData && this.newReserveData.type === 1) {
          this.newReserveData = Object.assign(this.newReserveData, {
            card_user_id: selectedCard.card_user_id,
            card_id: selectedCard.card_id
          });
          this.getCoachTime(this.newReserveData);
        } else {
          const course = this.oneTimePayCourseList[index];

          this.newReserveData = Object.assign(this.newReserveData, {
            card_id: course.id,
          }); 
          this.getCoachTime(this.newReserveData);
          // default to get price
          this.getOneTimePayPrice(course, false, true);
          this.oneTimePayType = []
        }
      },
      // 获取教练上课时间
      getCoachTime(data) {
        const { action, coach_id, card_id, id } = data;
        const { dateString: date, courseActiveTime: hour_date } = this;
        const url = '/Web/Coach/pt_schedule_time';
        let postData = {
          is_swim: this.isSwim,
          card_id,
          coach_id,
          id,
          action,
          date,
          hour_date,
          appt_type: this.newReserveData.type - 1
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.radioTimeData = res.data.data.list;
              this.courseActiveTime = ''
              this.newReserveData.begin_date = ''
            } else {
              this.$Message.error(res.data.errormsg);
              this.radioTimeData = false;
            }
            return res
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      classTimeSelected(info) {
        const { hour_date:val, pid_id } = info;
        if (this.newReserveData) {
          this.newReserveData.begin_date = `${this.dateString} ${val}`;
          this.newReserveData.id = pid_id;
        }
        this.courseActiveTime = val
      },
      // 添加预约点击确定按钮
      checkUserLimitByType(params) {
        return this.$service.post('/Web/UserBlacklist/checkUser', params, 
        { headers: { 'Content-Type': 'application/json' } }).then(res => {
          return res.data.data
        })
      },
      async handleConfirmAddClass() {
        

        const flag = await this.checkUserLimitByType({
          user_id: this.newReserveData.user_id,
          bus_id: this.$store.state.busId,
          member_rule: 4,
          loading: true
        })
        if(flag) {
          this.$Modal.confirm({
            title: '确认预约?',
            content: '此会员已在门店黑名单中',
            okText: '仍要预约',
            onOk: () => {
              this.confirmAddClass()
            },
          });
        } else {
          this.confirmAddClass()
        }
      },
      confirmAddClass() {
        if (!this.newReserveData.begin_date) {
          this.$Message.error('请选择上课时间');
          return false;
        }
        let url = '';
        let params = {};
        if (this.newReserveData.type === 2) {
          url = this.isSwim ? '/Web/PtSchedule/add_swim_schedule_payment' : '/Web/PtSchedule/add_pt_schedule_payment';
          params = {
            bus_id: this.$store.state.busId,
            user_id: this.newReserveData.user_id,
            coach_id: this.newReserveData.coach_id,
            coach_name: this.coachName,
            begin_date: this.newReserveData.begin_date,
            card_id: this.newReserveData.card_id,
            card_type_id: Number(this.isSwim) + 4,
            amount: this.oneTimePayPrice.pay_amount,
            pt_charge_plan_detail_id: this.oneTimePayPrice.pt_charge_plan_detail_id,
            new_pay_type: this.oneTimePayType,
          }
        } else {
          url = this.isSwim ? 'Web/PtSchedule/add_swim_schedule' : 'Web/PtSchedule/add_pt_schedule';
          params = {
            ...this.newReserveData,
            is_swim: this.isSwim,
          }
        }
        this.$service
          .post(url, params)
          .then(res => {
            if (res.data.errorcode === 0) {
              setTimeout(() => {
                this.newReserve = false;
                this.getCourseList();
              }, 500);
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      // 点击教练设置
      async clickCoachSetting(coachId) {
        await this.getCoachSetting(coachId);
        this.coachSetting = true;
      },
      // 获取教练个人设置
      getCoachSetting(coach_id) {
        const url = '/Web/Coach/get_pt_reservation_setting';
        let postData = {
          coach_id,
          is_swim: this.isSwim,
          date: this.dateString
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              let info = res.data.data.info;
              this.coachSettingData = Object.assign({}, info, {
                keep_begin_time: new Date(info.keep_begin_time),
                keep_end_time: new Date(info.keep_end_time),
                coach_id
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      postCoachSetting() {
        let setting = this.coachSettingData;
        if (!setting.keep_begin_time || !setting.keep_end_time) return this.$Message.error('请选择设置持续时间');

        const url = '/Web/Coach/update_pt_reservation_setting';
        let postData = Object.assign({}, setting, {
          is_swim: this.isSwim,
          // working_hours: this.dateArrToTime(setting.working_hours),
          working_hours: setting.working_hours,
          keep_begin_time: formatDate(setting.keep_begin_time, 'yyyy-MM-dd'),
          keep_end_time: formatDate(setting.keep_end_time, 'yyyy-MM-dd')
        });
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              setTimeout(() => {
                this.coachSetting = false;
                this.getCourseList();
              }, 500);
            }
            this.$Message.success(res.data.errormsg);
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      // 删除工作时间段
      deleteWorkTime(workTime, index) {
        if (workTime.length <= 1) {
          this.$Message.error('至少保留一个工作时间段');
          return false;
        }
        workTime.splice(index, 1);
      },
      addWorkTime(workTime) {
        let lastTime = workTime[workTime.length - 1][1];
        let lastTimeHour = typeof lastTime == 'string' ? Number(lastTime.split(':')[0]) : lastTime.getHours();

        if (workTime.length >= 5) return this.$Message.error('最多只能添加五个时间段');
        if (lastTimeHour + 1 > 23) return this.$Message.error('已经到了时间的尽头');

        let newLastRange = [];
        if (typeof lastTime == 'string') {
          newLastRange = [lastTime, `${lastTimeHour + 1}:${lastTime.split(':')[1]}`];
        } else {
          newLastRange = [lastTime, new Date(Date.parse(lastTime) + 60 * 60 * 1000)];
        }
        workTime.push(newLastRange);
      },
      // 教练暂停设置
      settingPause(coach_id, coach_name) {
        this.pauseSetting = true;
        this.pauseSettingData = {
          coach_id,
          coach_name,
          suspend_begin_time: new Date(),
          suspend_end_time: new Date()
        };
      },
      confirmPause() {
        const url = '/Web/Coach/pt_suspend_setting';
        let postData = Object.assign({}, this.pauseSettingData, {
          is_swim: this.isSwim,
          suspend_begin_time: formatDate(this.pauseSettingData.suspend_begin_time, 'yyyy-MM-dd'),
          suspend_end_time: formatDate(this.pauseSettingData.suspend_end_time, 'yyyy-MM-dd')
        });
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              setTimeout(() => {
                this.pauseSetting = false;
                this.getCourseList();
                this.getMapCoachList();
              }, 500);
            }
            this.$Message.success(res.data.errormsg);
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      settingStart(coach_id) {
        const url = '/Web/Coach/pt_enable_setting';
        this.$service
          .post(url, {
            coach_id,
            is_swim: this.isSwim
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getMapCoachList();
              this.coachList.forEach(coachItem => {
                if (coachItem.coach_id === coach_id) {
                  coachItem.suspend_status = 0;
                }
              });
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      copyCourse(coachId, userId, username) {
        this.addNewClass(coachId, userId);
        this.userSearch = userId;
        this.isUserId = true;
      },
      delCourse(courseId, coachName, username, cardName) {
        this.isDelCourse = true;
        this.isDelCourseData = {
          id: courseId,
          coachName,
          username,
          cardName
        };
      },
      doDelCourse(id) {
        this.isDelCourse = false;
        const url = this.isSwim ? '/Web/PtSchedule/swim_schedule_del' : '/Web/PtSchedule/pt_schedule_del';
        this.$service
          .post(url, {
            is_swim: this.isSwim,
            id
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              setTimeout(() => {
                this.getCourseList();
              }, 500);
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            this.$Message.error(err);
          });
      },
      dateArrToTime(arr) {
        let newArr = [];
        arr.forEach(timeArr => {
          let [first, second] = timeArr;
          let firstHour, firstMin, secondHour, secondMin;
          if (typeof first == 'string') {
            firstHour = first.split(':')[0];
            firstMin = first.split(':')[1];
            secondHour = second.split(':')[0];
            secondMin = second.split(':')[1];
          } else {
            firstHour = `${this.isLeftTen(first.getHours())}`;
            firstMin = `${this.isLeftTen(first.getMinutes())}`;
            secondHour = `${this.isLeftTen(second.getHours())}`;
            secondMin = `${this.isLeftTen(second.getMinutes())}`;
          }
          if (+firstHour >= 23) {
            if (+firstMin > 30) {
              firstMin = 30;
            }
          }
          if (+secondHour >= 23) {
            if (+secondMin > 30) {
              secondMin = 30;
            }
          }
          newArr.push([`${firstHour}:${firstMin}`, `${secondHour}:${secondMin}`]);
        });
        return newArr;
      },
      // 前一天、后一天切换
      changeDay(flag) {
        let oldDate = new Date(this.datePicker);
        this.datePicker = new Date(oldDate.getTime() + 60 * 60 * 1000 * 24 * flag);
      },
      // 计算 left 距离百分比
      calPosition(val) {
        if (!val) return false;
        const length = this.timeArray.length;
        let beginTimeArr = val.split(':'),
          unit =  100 / length,
          hour = Number(beginTimeArr[0]) + beginTimeArr[1] / 60;
        return hour * unit;
      },
      // 时间线定时器
      timeLineInterval() {
        this.timeNow = formatDate(new Date(), 'HH:mm');
        setInterval(() => {
          this.timeNow = formatDate(new Date(), 'HH:mm');
        }, 1000);
      },
      // 计算不可用的小时数
      calDisabledHour(workHour, timeRangeIndex) {
        let workTime = [...workHour];
        if (!workTime || timeRangeIndex === 0) {
          return 0;
        }
        if (typeof workTime[0][0] === 'string') {
          return parseInt(workTime[timeRangeIndex - 1][1].split(':')[0]) - 1;
        } else {
          let arr = [];
          workTime.forEach((timeRange, rangeIndex) => {
            let range = [];
            timeRange.forEach(time => {
              if (typeof time === 'object') {
                let hour = time.getHours(),
                  min = time.getMinutes();
                min = min < 10 ? `0${min}` : min;
                range.push(`${hour}:${min}`);
              } else {
                range.push(time);
              }
            });
            arr.push(range);
          });
          return parseInt(arr[timeRangeIndex - 1][1].split(':')[0]) - 1;
        }
      },
      // 返回不可用小时时间数组
      calDisabledTime(val) {
        let arr = [];
        if(val == 0) return []
        while (val >= 0) {
          arr.push(val--);
        }
        return arr;
      },
      handleTypeChanged() {
        this.newReserveData.index = '';
        this.newReserveData.begin_date = '';
        this.newReserveData.card_user_id = '';
        this.radioTimeData = null;
        this.oneTimePayPrice = null
        this.oneTimePayType = []
      },
      handleCardChange(params) {
        const course = this.oneTimePayCourseList[this.newReserveData.index];
        this.getOneTimePayPrice({
          ...course,
          ...params,
        }, true);
      }
    },
    watch: {
      $route(info, oldInfo) {
        if (info.name !== oldInfo.name) {
          this.isWatch = false
          this.searchAlpha = ''
          this.selectedCoach = ''
          this.selectedCourse = ''
          this.selectedStatus = ''
          this.selectedNoType = '0'
          this.$nextTick(() => {
            this.isWatch = true
          })
          this.init()
        }
      },
      datePicker() {
        this.getCourseList();
        this.getMapCoachList();
      },
      selectedCoach() {
        if (this.isWatch) {
          this.getCourseList();
        }
      },
      selectedCourse() {
        if (this.isWatch) {
          this.getCourseList();
        }
      },
      selectedNoType() {
        if (this.isWatch) {
          this.getCourseList();
        }
      },
      selectedStatus() {
        if (this.isWatch) {
          this.getCourseList();
        }
      },
      newReserve(val) {
        if (!val) {
          this.userSearch = '';
          this.newReserveData = this.selectedUser = this.radioTimeData = this.searchUserList = null;
        }
      },
      coachSetting(val) {
        if (!val) {
          this.coachSettingData = null;
        }
      },
      oneTimePayType(val) {
        // if change pay type then get price
        if (Array.isArray(val) && typeof this.newReserveData.index === 'number') {
          let run = true;

          const index = val.findIndex(item => item.pay_type === 8);
          if (index !== -1) {
            const item = val[index];
            if (item.autoSelect) {
              run = false;
              item.autoSelect = false;
            }
            
            // const oldIndex = oldVal.findIndex(item => item.pay_type === 8);
            // if (oldIndex !== -1) {
            //   run = false;
            // }
            if (this.oneTimePayCardUserId === item.card_user_id) {
              run = false;
            }
          }

          if (run) {
            const course = this.oneTimePayCourseList[this.newReserveData.index];
            this.getOneTimePayPrice(course, index !== -1);
          }
        }
      },
    }
  };
</script>

<style lang="less">
  @boxBorder: 1px solid #e0e3e9;
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .private-modal {
    .ivu-input,
    .ivu-select-input,
    .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
    .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
      font-size: 14px;
    }
    .ivu-form-item-content {
      display: flex;
      align-items: center;
      flex: 1;
      margin-left: 0;
    }
    .ivu-form .ivu-form-item-label {
      font-size: 14px;
      font-weight: bold;
      text-align: right;
      white-space: nowrap;
    }
  }
  .total-setting {
    .ivu-modal {
      margin-bottom: 50px;
    }
  }

  .course-private-class {
    .reserve-class,
    .course-edit {
      .ivu-modal {
        width: 700px !important;
      }
    }

    .ivu-input {
      font-size: 14px;
      text-align: center;
    }

    .selects {
      .ivu-select-input {
        font-size: 14px;
      }
    }

    .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
    .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
      font-size: 14px;
    }

    .flex-item {
      .ivu-form-item-content {
        display: flex;
      }
    }
    .ivu-form .ivu-form-item-content {
      display: flex;
      align-items: center;
      flex: 1;
      margin-left: 0;
    }

    border: @boxBorder;
    min-width: 900px;
    font-family: 'Microsoft YaHei', sans-serif;
    header {
      .flex-center;
      font-size: 14px;
      justify-content: space-between;
      background-color: #f7f7f7;
      height: 80px;
      padding: 0 35px;

      .left {
        display: flex;
      }
      .picker {
        .flex-center;
        background-color: #fff;
        height: 38px;
        border: 1px solid #dedede;
        button {
          background-color: transparent;
          color: #b5b5b5;
          width: 66px;
          height: 100%;
          border: 0;
          outline: 0;
          &:hover {
            color: #52a4ea;
            cursor: pointer;
          }
        }
        .ivu-input {
          font-size: 14px;
          padding-left: 20px;
          color: #434343;
          width: 158px;
          border: 0;
          border-left: 1px solid #dedede;
          border-right: 1px solid #dedede;
          outline: 0;
          box-shadow: none;
          border-radius: 0;
          height: 100%;
        }
      }

      .coach-select {
        width: 128px;
        height: 38px;
        margin-left: 17px;
        .ivu-select-placeholder {
          .flex-center;
          height: 100%;
          line-height: 100%;
          justify-content: flex-start;
          padding-left: 16px;
        }
        &:first-child {
          border-radius: 0;
        }
        .ivu-select-selection {
          border-radius: 0;
          height: 100%;
        }
        .ivu-input {
          height: 100%;
          text-align: left;
        }
      }

      .class-select {
        .coach-select;
      }
    }
    main {
      background-color: #fff;
      border-top: @boxBorder;
      font-size: 14px;
      // padding-left: 18px;
      overflow-y: hidden;
      overflow-x: scroll;
      .table-header {
        height: 66px;
        border-bottom: 1px solid #eee;
        font-weight: bold;
        width: 1698px;

        .time {
          .flex-center;
          margin-right: 80px;
          justify-content: space-around;
          span {
            .flex-center;
            // FIXME: 16 is a magic number
            width: 1/16 * 100%;
            font-size: 16px;
            font-weight: bold;
            height: 14px;
            line-height: 14px;
            border-right: 2px solid #959595;
          }
        }
      }
      .table-body {
        // height: calc(~'100vh - 360px');
        height: calc(~'100vh - 440px');
        overflow-y: scroll;
        width: 1698px;
        .coach-line {
          height: 70px;
          border-bottom: 1px solid #eee;
        }
        .work-time {
          height: 100%;
          width: 100%;
          position: relative;
          margin-right: 80px;
        }
        .coach-buttons {
          .flex-center;
          position: relative;
          z-index: 10;
          justify-content: space-between;
          width: 80px;
          height: 100%;
          position: absolute;
          right: 0;
          img {
            cursor: pointer;
          }
        }
        .time {
          padding: 10px 0;
          position: relative;
          .flex-center;

          .working-time {
            position: absolute;
            height: 100%;
          }
          .working-img {
            background-image: url('../../assets/img/working-time.jpg');
          }
          .pause-img {
            background-image: url('../../assets/img/pause-img.jpg');
          }
        }
      }
      .hidden-line {
        overflow-y: visible;
        height: 0;
      }
      .line {
        height: 100%;
        .flex-center;
        justify-content: flex-start;
      }
      .name {
        .flex-center;
        height: 100%;
        width: 70px;
        text-align: center;
        word-wrap: break-word;
        word-break: break-all;
        border-right: 1px solid #eee;
        position: relative;
        z-index: 2;

        .fix-name {
          width: 70px;
          height: 100%;
          line-height: 70px;
          text-align: center;
          vertical-align: middle;
          background-color: white;
          position: absolute;
          left: 0;
          right: 0;
        }
      }
      .time {
        height: 100%;
        width: calc(~'100% - 80px');
      }
      .time-line {
        width: 2px;
        height: 4000px;
        top: -80px;
        background-color: #57a3f3;
        position: absolute;
        z-index: 10;
      }
    }
    footer {
      height: 60px;
      .flex-center;
      font-size: 14px;
      padding-right: 56px;
      justify-content: flex-end;
      background-color: #f7f7f7;
      border-top: @boxBorder;
      strong {
        font-size: 18px;
        font-weight: bolder;
      }
      .total {
        .flex-center;
        justify-content: space-around;
        span {
          margin-left: 15px;
        }
      }
    }
  }

  .button-icon {
    width: 44px;
    margin-left: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
  }

  .price-box {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    font-weight: bold;
  }
</style>
