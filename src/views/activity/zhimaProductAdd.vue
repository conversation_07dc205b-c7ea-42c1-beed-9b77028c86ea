<template>
  <div class="container">
    <header>
      <h3>{{ productId ? '编辑产品' : '新增产品' }}</h3>
    </header>
    <Form label-position="right" ref="form" :model="formItem" class="form" :label-width="140">
      <FormItem label="产品名称" prop="product_title" :rules="{ required: true, message: '请填写产品名称', trigger: 'blur' }">
        <Input v-model="formItem.product_title" class="input" placeholder="产品名称" />
      </FormItem>
      <Form-item label="会员卡类型" prop="cardType" v-if="!productId">
        <RadioGroup v-model="formItem.cardType" type="button" @on-change="handleFilterCard" :disabled="!!productId">
          <Radio label="-1">全部</Radio>
          <Radio label="0">单店卡</Radio>
          <Radio label="1">多店通用卡</Radio>
        </RadioGroup>
      </Form-item>
      <Form-item label prop="subCardType" v-if="!productId">
        <RadioGroup v-model="formItem.subCardType" type="button" :disabled="!!productId" @on-change="handleFilterCard">
          <Radio label="-1">全部</Radio>
          <Radio label="1">期限卡</Radio>
          <Radio label="7">私教包月</Radio>
        </RadioGroup>
      </Form-item>
      <Form-item label="会员卡名称" prop="card_id" :rules="{ required: true, message: '请选择会员卡' }">
        <Input v-if="productId" v-model="formItem.card_name" class="input" disabled />
        <Select placeholder="请选择" v-if="!productId && filterCardList && filterCardList.length" v-model="formItem.card_id"
          @on-change="cardChange" :disabled="!!productId" filterable>
          <Option v-for="item in filterCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
        </Select>
      </Form-item>

      <div class="card-info" v-if="!productId">
        <div>购买天数：{{ selectedCard.number }}</div>
        <div>赠送天数：{{ selectedCard.gift_number }}</div>
        <div>售价：{{ selectedCard.current_price }}</div>
      </div>
      <Form-item label="产品类型">连续包月</Form-item>
      <!-- alipay monthly -->
      <Form-item label="合约生成">
        <RadioGroup v-model="formItem.gen_type">
          <Radio label="0">签约后生成</Radio>
          <Radio label="1">首期扣款成功后生成</Radio>
        </RadioGroup>
      </Form-item>
      <div style="margin: 20px 0">
        <div style="text-align: center; margin-bottom: 20px">
          <RadioGroup v-model="payMode" type="button">
            <Radio :label="1">标准月付</Radio>
            <Radio :label="2">自定义月付</Radio>
          </RadioGroup>
        </div>
        <div style="display: flex; flex-wrap: nowrap; flex-direction: row; overflow: hidden;">
          <transition name="slide-fade">
            <Card v-show="payMode === 1" style="min-width: 100%;">
              <Form-item label="签约期数" prop="periods" :rules="{ required: true, message: '请选择' }">
                <Select placeholder="请选择" v-model="formItem.periods" filterable>
                  <Option v-for="item in 24" :key="item" :value="item">{{ item }}</Option>
                </Select>
              </Form-item>
              <Form-item label="首期金额" prop="down_payment"
                :rules="{ required: true, pattern: /^-?[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.down_payment">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="单期金额" prop="deduction_amount" :rules="{ required: true, validator: lessThan5000 }">
                <Input placeholder="请填写" type="number" v-model="formItem.deduction_amount">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="方案总金额">
                {{ totalAmount }} 元
              </Form-item>
            </Card>
          </transition>
          <transition name="slide-fade">
            <Card v-show="payMode === 2" style="min-width: 100%;">
              <Form-item label="方案总金额" prop="hopeTotalAmount"
                :rules="{ required: true, pattern: /^-?[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.hopeTotalAmount">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="签约期数" prop="periods" :rules="{ required: true, message: '请选择' }">
                <Select placeholder="请选择" v-model="formItem.periods" filterable>
                  <Option v-for="item in 24" :key="item" :value="item">{{ item }}</Option>
                </Select>
              </Form-item>
              <Form-item label="首期金额" prop="down_payment"
                :rules="{ required: true, pattern: /^-?[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数' }">
                <Input placeholder="请填写" type="number" v-model="formItem.down_payment">
                <span slot="append">元</span>
                </Input>
              </Form-item>
              <Form-item label="单期金额">
                <div v-if="gossipSingleAmount >= 0" :class="fakeSingleValue ? 'fake-value' : ''">{{ gossipSingleAmount }}
                  元 <span v-if="gossipSingleAmount > 5000 && !fakeSingleValue" style="color: red">(注意: 单期金额超过 5000)</span>
                </div>
                <div v-if="fakeSingleValue">{{ gossipSingleAmount.toFixed(2) }} 元 <span v-if="gossipSingleAmount > 5000"
                    style="color: red">(注意: 单期金额超过 5000)</span></div>
              </Form-item>
              <Form-item label="方案实际总金额">
                <!-- <div v-if="gossipTotalAmount >= 0" :class="fakeTotalValue ? 'fake-value' : ''">{{ gossipTotalAmount }} 元
                </div>
                <div v-if="fakeTotalValue">{{ gossipTotalAmount2 }} 元</div> -->
                <div style="color: red">{{ gossipTotalAmount2 }} 元</div>
              </Form-item>
            </Card>
          </transition>
        </div>
      </div>
      <Form-item label="扣款日期" prop="down_payment_date_rule"
        :rules="[{required: true, pattern: /^\d+$/, message: '必须为正整数'}, {type: 'number', transform:(value)=> Number(value), min: 7, max: 60,  message: '扣款日期范围为7~60天' }]">
        <!-- <Input placeholder="请填写" v-model="formItem.down_payment_date_rule" :min="7" :max="60" type="number">
          <span slot="prepend">签约后</span>
          <span slot="append">天扣款</span>
        </Input> -->
        <div class="custom-input">
          <div class="prepend">签约后</div>
          <InputNumber placeholder="请填写" v-model="formItem.down_payment_date_rule" :min="7" :max="60" :active-change="false"></InputNumber>
          <div class="append">天扣款</div>
        </div>
      </Form-item>
      <Form-item label="扣款周期">
        <Input placeholder="请填写" disabled :value="30">
        <span slot="append">天</span>
        </Input>
        <div class="tips-info">此卡签署协议成功后立即开卡，后续按照自然月进行扣款</div>
      </Form-item>
      <Form-item>
        <div class="buttons">
          <Button type="primary" @click="addPro">提交</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </Form-item>
    </Form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// alipay monthly
import Big from 'big.js';

export default {
  name: 'zhimaProductAdd',
  components: {},
  data() {
    const lessThan5000 = (rule, value, callback) => {
      if (/^-?[0-9]+(.[0-9]{1,2})?$/.test(value)) {
        if (value < 0) {
          callback(new Error('金额不能小于 0'))
        } else if (value > 5000) {
          callback(new Error('单期金额不能超过 5000'))
        } else {
          callback()
        }
      } else {
        callback(new Error('金额必须为数字且只能保留两位小数'))
      }
    }
    return {
      formItem: {
        cardType: '-1',
        subCardType: '-1',
        product_no: this.$route.query.product_no || '',//服务中心产品编号，编辑时必须
        product_title: '',//产品名称
        card_id: '',//卡id
        card_name: '',//卡名称
        universal_card: '',//是否是通卡
        card_type_id: '',//卡类型
        purchase_volume: '',//购买天数
        gift_volume: '',//赠送天数
        sale_amount: '',//售价
        is_pt_time_limit_card: '',//私教包月卡标识
        sell_type: 1,//1—连续包月，默认为1
        periods: 12,//int	签约期数
        down_payment_date_rule: 7,
        period_day: 30,//每期天数，前期固定30天
        down_payment: '',
        deduction_amount: '',//每月扣减固定金额
        // alipay monthly
        gen_type: '1',
        hopeTotalAmount: 0,
      },
      selectedCard: {},
      filterCardList: [],
      productId: this.$route.query.product_no,
      open_merchant_id: this.$route.query.open_merchant_id,
      // alipay monthly
      payMode: 1,
      lessThan5000
    }
  },
  created() {
    if (this.productId) {
      this.getBonusInfo()
    } else {
      this.changeCardList(this.$store.state.busId)
    }
  },
  mounted() { },
  watch: {},
  computed: {
    ...mapGetters(['addCardList']),
    totalAmount() {
      // alipay monthly
      // const firstAmount = Number(this.formItem.down_payment) || 0
      // const periods = firstAmount?parseInt(this.formItem.periods)-1:parseInt(this.formItem.periods)
      // const otherAmount = periods * this.formItem.deduction_amount
      // return (firstAmount + otherAmount).toFixed(2)
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigDeduction = new Big(this.formItem.deduction_amount || 0)
      const bigOther = bigPeriods.times(bigDeduction)
      return bigFirst.plus(bigOther)
    },
    gossipSingleAmount() {
      if (!this.formItem.hopeTotalAmount ||
        Number(this.formItem.hopeTotalAmount || 0) <= Number(this.formItem.down_payment || 0)) {
        return new Big(0)
      }
      const bigTotal = new Big(this.formItem.hopeTotalAmount || 0)
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigOther = bigTotal.minus(bigFirst)
      return bigOther.div(bigPeriods)
    },
    gossipTotalAmount() {
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigSingle = this.gossipSingleAmount
      const bigOther = bigPeriods.times(bigSingle)
      return bigFirst.plus(bigOther)
    },
    gossipTotalAmount2() {
      const bigFirst = new Big(this.formItem.down_payment || 0)
      const bigPeriods = new Big(this.formItem.down_payment ? Number(this.formItem.periods) - 1 : Number(this.formItem.periods))
      const bigSingle2 = new Big(this.gossipSingleAmount.toFixed(2))
      const bigOther = bigPeriods.times(bigSingle2)
      return bigFirst.plus(bigOther)
    },
    fakeSingleValue() {
      const bigSingle2 = new Big(this.gossipSingleAmount.toFixed(2))
      return this.gossipSingleAmount >= 0 &&
        (this.gossipSingleAmount.toString() != bigSingle2.toString())
    },
    fakeTotalValue() {
      return this.gossipTotalAmount2 >= 0 &&
        (this.gossipTotalAmount.toString() != this.gossipTotalAmount2.toString())
    }
  },
  methods: {
    cardChange(cardId) {
      for (let item of this.addCardList) {
        if (item.card_id == cardId) {
          this.selectedCard = item
          this.formItem.card_id = item.card_id
          this.formItem.card_name = item.card_name
          this.formItem.universal_card = item.universal_card
          this.formItem.card_type_id = item.card_type_id
          this.formItem.purchase_volume = item.number
          this.formItem.gift_volume = item.gift_number
          this.formItem.sale_amount = item.sale_amount
          this.formItem.is_pt_time_limit_card = item.is_pt_time_limit_card
          break
        }
      }
    },
    changeCardList(busId) {
      this.$store.dispatch('getAddCardList', {belongBusId: busId}).then(res => {
        this.handleFilterCard()
      })
    },
    handleFilterCard() {
      let arr = [].concat(this.addCardList.filter(item => {
        return item.sale_status === '1' && (item.is_pt_time_limit_card == 1 || item.card_type_id == 1)
      }))
      if (this.formItem.cardType != '-1') {
        arr = arr.filter(item => item.universal_card === this.formItem.cardType)
      }
      if (this.formItem.subCardType === '7') {
        arr = arr.filter(item => item.is_pt_time_limit_card == 1)
      }
      if (this.formItem.subCardType == 1) {
        arr = arr.filter(item => item.card_type_id == 1)
      }
      this.filterCardList = arr
    },
    addPro() {
      // alipay monthly
      if (this.payMode == 2) {
        if (this.gossipSingleAmount > 5000) {
          return this.$Message.error('单期金额不能超过 5000')
        } else {
          this.formItem.deduction_amount = this.gossipSingleAmount.toFixed(2)
        }
      }
      this.$refs.form.validate(valid => {
        if (!valid) {
          return this.$Message.error('请完成信息填写');
        }
        this.$service
          .post('/Web/ZhimaFitPay/productUpsert', {
            ...this.formItem,
            down_payment_date_rule: `t+${this.formItem.down_payment_date_rule}`
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      });
    },
    getBonusInfo() {
      this.$service
        .post('/Web/ZhimaFitPay/productInfo', { open_merchant_id: this.open_merchant_id, product_no: this.productId })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.formItem = {
              ...this.formItem,
              ...res.data.data,
              down_payment_date_rule: Number(res.data.data.down_payment_date_rule),
              gen_type: res.data.data.gen_type + '',
              hopeTotalAmount: res.data.data.total_amount
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
<style lang="less" scoped>
.card-info {
  font-size: 12px;
  line-height: 26px;
  margin-left: 140px;
  color: #888;
  border: 1px solid #ddd;
  padding: 8px;
  margin-bottom: 20px;
}

.tips-info {
  font-size: 12px;
  color: #888;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.6s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(40px);
  opacity: 0;
  height: 0;
}

.fake-value {
  color: red;
  text-decoration: line-through;
}

.custom-input {
  display: flex;
  flex-direction: row;
  align-items: center;

  .prepend {
    padding: 4px 7px;
    font-size: 12px;
    font-weight: normal;
    line-height: 2;
    color: #666;
    text-align: center;
    background-color: #f8f8f9;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    border-right: none;
    display: table-cell;
    white-space: nowrap;
    vertical-align: middle;
    height: 32px;
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }

  .append {
    padding: 4px 7px;
    font-size: 12px;
    font-weight: normal;
    line-height: 2;
    color: #666;
    text-align: center;
    background-color: #f8f8f9;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    border-left: none;
    display: table-cell;
    white-space: nowrap;
    vertical-align: middle;
    height: 32px;
    border-bottom-left-radius: 0 !important;
    border-top-left-radius: 0 !important;
  }
}
</style>
