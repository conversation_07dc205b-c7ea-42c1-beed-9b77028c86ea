<template>
  <div class="container step-edit-container">
    <header>
      <h3>{{ isReadonly ? '活动详情' : activityId ? '编辑活动' : '添加活动' }}</h3>
    </header>
    <Form
      ref="formRef"
      class="form step-edit-form"
      :model="formData"
      :label-width="120"
      :rules="formRules"
    >
      <h3>基础信息</h3>
      <FormItem prop="name" label="活动名称">
        <Input
          v-model="formData.name"
          :disabled="isReadonly"
          :maxlength="12"
          placeholder="不超过12字符"
        />
      </FormItem>
      <FormItem label="活动方式" prop="type" required>
        <RadioGroup v-model="formData.type" @on-change="handleChangeRadio('1')">
          <Radio label="1" :disabled="isReadonly">折扣券梯度升级</Radio>
          <Radio label="2" :disabled="isReadonly">会员卡梯度升级</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        label="活动时间"
        prop="autoDateRange"
      >
        <Date-picker
          v-model="formData.autoDateRange"
          type="datetimerange"
          placeholder="选择活动时间范围"
          :disabled="isReadonly"
          :editable="false"
          :clearable="false"
          format="yyyy-MM-dd HH:mm:ss"
          @on-change="handleDateChange"
        />
        <!-- :options="dateOptions" -->
      </FormItem>

      <h3>梯度配置</h3>
      <FormItem label="购买金额" prop="price">
        <InputNumber
          v-model="formData.price"
          style="width: 100%;"
          :disabled="isReadonly"
          :min="0.01"
          :max="999999999"
          :precision="2"
          :active-change="false"
          placeholder="请填写购买金额（元）"
        />
      </FormItem>
      <FormItem>
        <div
          v-for="(item, index) in formData.step_1"
          :key="item.timestamp"
          class="form-item-row"
        >
          <span class="step-num">阶梯{{ index + 1 }}</span>
          <span style="margin: 0 6px;">{{ index === 0 ? formData.type === '1' ? '初始可获得折扣券' : '初始可购买到的会员卡' : '当接龙人数达到' }}</span>
          <template v-if="index !== 0">
            <FormItem
              :prop="'step_1.' + index + '.num'"
              :rules="[
                {required: true, type: 'number', message: '请填写阶梯人数', trigger: 'change'},
                {validator(rule, val, cb) { numValidator1(rule, val, cb, index) }, trigger: 'change'}
              ]">
              <InputNumber
                v-model="item.num"
                class="w140"
                :disabled="isReadonly"
                :min="2"
                :max="99999999"
                :precision="0"
                :active-change="false"
                placeholder="请填写"
              />
            </FormItem>
            <span style="margin: 0 6px;">人时，{{ formData.type === '1' ? '享受抵扣金额' : '可购买到的会员卡' }}</span>
          </template>
          <FormItem
            :prop="'step_1.' + index + '.value'"
            :rules="[
              {required: true, type: 'number', message: '请选择阶梯奖励', trigger: 'change'},
              {validator(rule, val, cb) { valueValidator1(rule, val, cb, index) }, trigger: 'change'}
            ]">
            <Select
              :value="item.value"
              class="w140"
              transfer
              filterable
              :disabled="isReadonly"
              clearable
              @on-change="handleSelect($event, '1', index)">
              <Option
                v-for="({ label, value, disabled }) in formData.type === '1' ? couponList : memberCardList"
                :key="value"
                :value="value"
                :label="label"
                :disabled="disabled"
              />
            </Select>
          </FormItem>
          <Icon
            v-if="!isReadonly"
            v-show="index > 1"
            style="margin-left:12px;cursor: pointer;"
            type="md-close-circle"
            title="删除"
            size="20"
            color="#d9544f"
            @click.native="handleDeleteStep('1', item.timestamp)" />
          <Button
            v-if="index === 0 && formData.type === '1' && !isReadonly"
            type="text"
            style="margin-left: 6px;"
            @click="showAddDiscount = true"
          >
            添加折扣券
          </Button>
        </div>
        <div>
          <Button
            v-if="!isReadonly"
            type="success"
            @click="handleAddStep('1')"
          >
            添加阶梯
          </Button>
        </div>
      </FormItem>

      <h3>分销配置</h3>
      <FormItem>
        <i-switch
          slot="label"
          v-model="formData.is_fission"
          true-value="1"
          false-value="0"
          :disabled="isReadonly"
          @on-change="handleChangeIsFission"
        />
        <span class="tips" style="line-height: 42px;">
          允许客户、会籍根据此活动的配置，成为团长并发起新的接龙团（会籍不享受以下奖励
          {{ formData.type == '1' ? '）' : '，但开团成交的业绩会计算给会籍）' }}
        </span>
      </FormItem>
      <FormItem
        label="奖励内容"
        prop="reward_type"
        required>
        <RadioGroup
          v-model="formData.reward_type"
          @on-change="handleChangeRadio('2')"
        >
          <Radio label="1" :disabled="isReadonly || isDistributionDisabled">销售提成</Radio>
          <Radio label="2" :disabled="isReadonly || isDistributionDisabled">会员卡</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <div
          v-for="(item, index) in formData.step_2"
          :key="item.timestamp"
          class="form-item-row"
        >
          <span class="step-num">阶梯{{ index + 1 }}</span>
          <span style="margin: 0 6px;">当接龙人数达到</span>
          <FormItem
            :prop="'step_2.' + index + '.num'"
            :rules="[
              {required: formData.is_fission === '1', type: 'number', message: '请填写阶梯人数', trigger: 'change'},
              {validator(rule, val, cb) { numValidator2(rule, val, cb, index) }, trigger: 'change'}
            ]">
            <InputNumber
              v-model="item.num"
              :disabled="isReadonly || isDistributionDisabled"
              class="w140"
              :min="1"
              :max="99999999"
              :precision="0"
              :active-change="false"
              placeholder="请填写"
            />
          </FormItem>
          <span style="margin: 0 6px;">人时，{{ formData.reward_type === '1' ? '享受销售提成' : '奖励会员卡' }}</span>
          <FormItem
            :prop="'step_2.' + index + '.value'"
            :rules="[
              {required: formData.is_fission === '1', type: 'number', message: `请${formData.reward_type === '1'?'填写':'选择'}阶梯奖励`, trigger: 'change'},
              {validator(rule, val, cb) { valueValidator2(rule, val, cb, index) }, trigger: 'change'}
            ]"
          >
            <div v-show="formData.reward_type === '1'">
              <InputNumber
                v-model="item.value"
                :disabled="isReadonly || isDistributionDisabled"
                class="w140"
                :min="0.01"
                :max="100"
                :precision="2"
                :active-change="false"
                placeholder="请填写"
              />
              <span style="margin: 0 6px;">%</span>
            </div>
            <Select
              v-show="formData.reward_type === '2'"
              :value="item.value"
              transfer
              filterable
              clearable
              :disabled="isReadonly || isDistributionDisabled"
              class="w140"
              @on-change="handleSelect($event, '2', index)">
              <Option
                v-for="({ label, value, disabled_2 }) in memberCardList"
                :key="value"
                :value="value"
                :label="label"
                :disabled="disabled_2"
              />
            </Select>
          </FormItem>
          <Icon
            v-if="index > 0 && !isReadonly"
            v-show="formData.is_fission === '1'"
            style="margin-left:12px;cursor: pointer;"
            type="md-close-circle"
            title="删除"
            size="20"
            color="#d9544f"
            @click.native="handleDeleteStep('2', item.timestamp)" />
        </div>
        <div>
          <Button
            v-if="!isReadonly"
            type="success"
            :disabled="isDistributionDisabled"
            @click="handleAddStep('2')"
          >
            添加阶梯
          </button>
        </div>
      </FormItem>
      <FormItem>
        <div :style="isDistributionDisabled?'' : 'color: red;'">
          <div v-show="formData.reward_type == '1'">
            <p>注释：</p>
            <p>1. 根据其发起的接龙，总计成交的人数和金额进行计算</p>
            <p>2. 最终提成金额的发放，由场馆和团长自行结算，系统不会代结算</p>
            <p>3. 团长能参与自己发起的接龙</p>
            <p>4. 下一阶梯的人数需大于上一阶梯，销售提成比例同理</p>
            <p>5. 最多设置5级阶梯</p>
          </div>
          <div v-show="formData.reward_type == '2'">
            <p>注释：</p>
            <p>1. 根据其发起的接龙，总计成交的人数进行计算</p>
            <p>2. 会员卡会在活动结束时发放</p>
            <p>3. 团长能参与自己发起的接龙</p>
            <p>4. 下一阶梯的人数需大于上一阶梯</p>
            <p>5. 最多设置5级阶梯</p>
          </div>
        </div>
      </FormItem>

      <h3>宣传配置</h3>
      <FormItem label="活动说明" prop="description">
        <Editor v-model="formData.description" :height="200" :options="{ readOnly: isReadonly }"></Editor>
      </FormItem>
      <FormItem label="朋友圈传播">
        <template v-if="isReadonly">
          <div v-if="formData.friend_url">
            <img
              style="width:100%;"
              :src="formData.friend_url"
              alt="朋友圈传播图">
            <img
              style="width: 150px; height: 181px; position: absolute; right: 40px; bottom: 40px;"
              src="../../../assets/img/fake-code.jpg" 
              alt="小程序二维码">
          </div>
          <div v-else>未上传朋友圈分享图</div>
        </template>
        <template v-else>
          <Cropper
            v-model="formData.friend_url"
            :ratio="9/16"
            :maxSize="0.1"
            width="300px"
            height="400px"
            :output-width="900"
            style="width: 100%"></Cropper>
            <img
              v-if="formData.friend_url"
              style="width: 80px; height: 97px; position: absolute; left: 190px; bottom: 60px;"
              src="../../../assets/img/fake-code.jpg"
              alt="小程序二维码">
        </template>
      </FormItem>
      <FormItem>
        <p class="tips">1. 上传图片后，系统会在图片中放置活动二维码，用于朋友圈分享</p>
        <p class="tips">2. 图片最佳尺寸: 900X1600，推荐图片大小: &lt;100kb，格式限制: jpg、png</p>
      </FormItem>
      <FormItem label="微信转发">
        <Input
          v-model="formData.weixin_title"
          :disabled="isReadonly"
          placeholder="请输入转发标题, 不超过20个字符"
          :maxlength="20" />
      </FormItem>
      <FormItem>
        <template v-if="isReadonly">
          <img
            v-if="formData.weixin_url"
            style="width:100%;"
            :src="formData.weixin_url"
            alt="微信转发图">
          <div v-else>未上传微信转发图</div>
        </template>
        <Cropper
          v-else
          v-model="formData.weixin_url"
          :ratio="5/4"
          :maxSize="0.1"
          :output-width="500"
          style="width: 300px"></Cropper>
      </FormItem>
      <FormItem>
        <p class="tips">图片最佳尺寸: 500X400,，推荐图片大小: &lt;100kb，格式限制: jpg、png</p>
        <p class="tips">示例</p>
        <img src="~assets/img/example_group_buy.png" alt="">
        <!-- <p class="tips">2. 发布后，不支持再次编辑修改</p>
        <p class="tips">3. 客户开团后，如果成团有效时间结束时，其拼团仍不满足成团人数，系统会自动模拟用户加入其拼团并视为拼团成功</p>
        <p class="tips">4. 如果在活动过程中，修改或删除了此活动对应的会员卡，活动仍会以修改或删除之前的会员卡进行</p> -->
      </FormItem>

      <h3>Tips</h3>
      <FormItem>
        <div style="color: red;">
          <template v-if="formData.type == '1'">
            <p>1. 活动结束后，系统统一发放对应梯度抵扣金额的折扣券，到会员名下</p>
            <p>2. 此折扣券活动的销售不会计入会籍的个人业绩统计中</p>
          </template>
          <template v-else>
            <p>1. 活动结束后，系统统一发放对应梯度的会员卡到会员名下，卡为未激活状态</p>
            <p>2. 活动开始后，如果活动关联的会员卡发生变动，活动仍以变动前的会员卡进行</p>
          </template>
          <p>3. 活动开始后，不支持修改、删除。可以提前结束</p>
          <p>4. 活动创建后，以接龙团的形式体现，初始默认只有一个团，团长是场馆。开启分销配置后，可以由会籍、客户在活动基础上创建新团，从而分裂出无数接龙团</p>
          <p>5. 每个客户可以支付参与任意一个或多个接龙团，但每个接龙团只能支付参与一次</p>
        </div>
      </FormItem>

      <FormItem v-if="!isReadonly">
        <div class="form-bottom-buttons">
          <Button type="success" @click="handleSubmit">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>

    <!-- 添加折扣券 -->
    <Modal
      v-model="showAddDiscount"
      :mask-closable="false"
      footer-hide
      width="600"
      title="添加折扣券"
    >
      <div>
        <EditForm
          v-if="showAddDiscount"
          :labelWidth="90"
          @success="getCouponList(true),showAddDiscount = false"
          @cancel="showAddDiscount = false" />
      </div>
    </Modal>
  </div>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex';
  import { formatDate } from 'utils'
  import { queryBusCoupon } from 'src/service/getData';
  // import CardList from 'src/components/card/cardList';
  import Editor from 'components/form/Editor';
  import Cropper from 'components/form/cropperPlus';
  import EditForm from "../discount/editForm";


  export default {
    name: 'StepActivityEdit',
    components: {
      // CardList,
      Editor,
      Cropper,
      EditForm
    },
    data() {
      return {
        activityId: '',
        isReadonly: false,
        itemData: null,
        couponList: [], // 折扣券
        memberCardList: [], // 会员卡
        // dateOptions: {
        //   disabledDate (date) {
        //     return date <= Date.now() - 86400000
        //   }
        // },
        formData: {
          name: '',
          type: '1', // 1折扣券梯度升级  2会员卡梯度升级
          autoDateRange: [], // 活动时间范围 yyyy-MM-dd
          price: null, // 购买金额
          step_1: [ // 购买阶梯
            { num: 1, value: null, timestamp: Date.now(), valueType: '1' },
            { num: null, value: null, timestamp: Date.now() + 1, valueType: '1' },
          ],
          is_fission: '0', // 是否允许客户/会籍 发起活动 0否 1允许
          reward_type: '1', // 1销售提成  2会员卡
          step_2: [ // 分销阶梯
            { num: null, value: null, timestamp: Date.now() },
          ],
          description: '<p>1. 一人仅限购买一次</p><p>2. 如有任意疑问可咨询客服</p>', // 活动说明
          friend_url: '',
          weixin_title: '',
          weixin_url: ''
        },
        formRules: {
          name: { required: true, type: 'string', message: '请填写活动名称', trigger: 'change' },
          autoDateRange: [
            {
              required: true, type: 'array', len: 2, message: '请选择活动时间',
              fields: {
                0: { type: 'date', required: true, message: '请选择活动时间' },
                1: { type: 'date', required: true, message: '请选择活动时间' },
              }
            },
            {
              trigger: 'blur',
              validator(rule, arr, cb) {
                // if (new Date(arr[0]) <= Date.now()) {
                //   cb(new Error('开始时间必须晚于当前时间'))
                // } else
                if (new Date(arr[0]) >= new Date(arr[1])) {
                  cb(new Error('结束时间必须晚于开始时间'))
                } else {
                  cb()
                }
              }
            }
          ],
          price: { required: true, type: 'number', message: '请填写购买金额', trigger: 'change' },
          description: {required: true, type: 'string', message: '请填写活动说明', trigger: 'change'}
        },
        /* 添加折扣券 */
        showAddDiscount: false,
      }
    },
    computed: {
      ...mapGetters(['busId', 'adminId', 'addCardList']),
      isDistributionDisabled() {
        return this.formData.is_fission !== '1'
      }
    },

    async created() {
      const { params } = this.$route;
      if (params.id && params.data) {
        this.activityId = params.id;
        this.isReadonly = params.isReadonly;
        this.itemData = params.data
        this.$route.meta.breadText = params.isReadonly ? '活动详情' : '编辑活动';
        this.getActivityData()
      }
      this.getCouponList()
      this.getCardList()
    },

    mounted() {
      document.getElementById('vipMainCon').scrollTop = 0
    },

    methods: {
      // 获取会员卡列表选项
      ...mapActions(['getAddCardList']),
      // 获取折扣券列表选项
      getCouponList(isAdd = false) {
        queryBusCoupon().then(res => {
          if (res.data.errorcode == 0) {
            const list = Array.isArray(res.data.data) ? res.data.data : []

            // 将已选择的禁用
            let selected = []
            if (isAdd === true) {
              const { step_1 } = this.formData
              selected = step_1.filter(v => v.value).map(v => +v.value)
            } else if (this.activityId && this.itemData) {
              const { type, rule } = this.itemData
              selected = type == '1' && Array.isArray(rule) ? rule.map(v => +v.value) : []
            }
            list.length && list.forEach(v => {
              v.value = +v.coupon_id
              v.label = `￥${v.discount_amount || '0'} - ${v.coupon_name}`
              v.disabled = selected.length ? selected.includes(+v.coupon_id) : v.is_delete == '1'
              // v.is_delete = v.is_delete == '1' ? '0' : '1'
            })

            // 已删除，但是被接龙用到的折扣券
            const deletedList = [];
            // 如果是编辑或者详情 需要回显被删除的折扣券。将活动数据里面被删除的折扣券，添加到可选项并禁用
            if (this.activityId && this.itemData) {
              const { type, rule } = this.itemData
              if ( type === '1' && Array.isArray(rule)) {
                rule.forEach(v => {
                  // 如果没找到，说明被删除了，进去
                  if (!list.some(k => k.value == v.value)) {
                    deletedList.push({
                      label: `￥${v.value} - ${v.name} (已删除)`,
                      value: +v.value, // 转换为数字类型，用于处理InputNumber值类型判断
                      disabled: true, // 用于梯度配置
                      is_delete: '1'
                    })
                  }
                })
              }
            }

              this.couponList = [...list, ...deletedList]
            }
          })
        // this.$service.post('/Web/Coupon/coupon_list', { page_no: 1, page_size: 999 }).then(res => {
        //   if (res.data.errorcode == 0) {
        //     const list = Array.isArray(res.data.data.list) ? res.data.data.list : []
        //     list.length && list.forEach(v => {
        //       v.value = v.coupon_id
        //       v.label = v.coupon_name
        //       // v.disabled = false
        //     })
        //     this.couponList = list
        //   }
        // })
      },
      // 获取会员卡列表
      async getCardList() {
        if (!Array.isArray(this.addCardList) || this.addCardList.length === 0) {
          await this.getAddCardList({belongBusId: this.busId})
        }

        const exceptType = ['1', '2', '3']
        let selected_1 = [], selected_2 = []
        if (this.activityId && this.itemData) {
          const { type, rule, reward_type, distribution_rule } = this.itemData
          selected_1 = type === '2' && Array.isArray(rule) ? rule.map(v => +v.value) : []
          selected_2 = reward_type === '2' && Array.isArray(distribution_rule) ? distribution_rule.map(v => +v.value) : []
        }

        // v.sale_status === '1' &&
        const cardList = Array.isArray(this.addCardList)
          ? this.addCardList.filter(v => exceptType.includes(v.card_type_id))
            .map(v => ({
              label: v.card_name,
              value: +v.card_id, // 转换为数字类型，用于处理InputNumber值类型判断
              disabled: selected_1.length ? selected_1.includes(+v.card_id) : false, // 用于梯度配置
              disabled_2: selected_2.length ? selected_2.includes(+v.card_id) : false, // 用于分销配置
              // is_delete: v.is_delete == '1' ? '0' : '1'
            }))
          : [];

        const experienceCards = (await this.getExperienceCardList()).map(v => ({
          label: v.name,
          value: +v.card_id, // 转换为数字类型，用于处理InputNumber值类型判断
          disabled: selected_1.length ? selected_1.includes(+v.card_id) : false, // 用于梯度配置
          disabled_2: selected_2.length ? selected_2.includes(+v.card_id) : false, // 用于分销配置
          // is_delete: v.is_delete == '1' ? '0' : '1'
        }))

        // 未删除的会员卡
        const validCardList = cardList.concat(experienceCards)

        // 已删除，但是被接龙用到的会员卡
        const deletedList = [];
        // 如果是编辑或者详情 需要回显被删除的会员卡。将活动数据里面被删除的会员卡，添加到可选项并禁用
        if (this.activityId && this.itemData) {
          const { type, rule, reward_type, distribution_rule } = this.itemData
          if (type === '2' && Array.isArray(rule)) {
            rule.forEach(v => {
              // 1. 如果没找到，说明被删除了，进去
              if (!validCardList.some(k => k.value == v.value)) {
                deletedList.push({
                  label: v.name + ' (已删除)',
                  value: +v.value, // 转换为数字类型，用于处理InputNumber值类型判断
                  disabled: true, // 用于梯度配置
                  disabled_2: true, // 用于分销配置
                  is_delete: '1'
                })
              }
            })
          }
          if (reward_type === '2' && Array.isArray(distribution_rule)) {
            distribution_rule.forEach(v => {
              // 2. 如果没找到 并且 在1.的时候没有添加，进去
              if (!validCardList.some(k => k.value == v.value) && !deletedList.some(k => k.value == v.value)) {
                deletedList.push({
                  label: v.name + ' (已删除)',
                  value: +v.value, // 转换为数字类型，用于处理InputNumber值类型判断
                  disabled: true, // 用于梯度配置
                  disabled_2: true, // 用于分销配置
                  is_delete: '1'
                })
              }
            })
          }
        }

        this.memberCardList = [...validCardList, ...deletedList]
      },
      // 获取所有体验卡包括未售卖的
      getExperienceCardList() {
        const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
        return this.$service
          .post(url, {
            bus_id: this.busId,
            page_no: 1,
            page_size: 9999,
            card_type: 1, // 会籍卡
            card_class: 'experience_card' // 体验卡
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              return Array.isArray(res.data.data.list) ? res.data.data.list : []
            } else {
              this.$Message.error(res.data.errormsg)
              return []
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      // 获取活动详情
      getActivityData() {
        // 该接口没写，从列表拿
        // this.$service.post(
        //   '/Web/ChainsFission/detail',
        //   { id: this.activityId, bus_id: this.busId },
        //   { headers: { 'Content-Type': 'application/json' } }
        // ).then(res => {})
        const {
          name,
          type,
          start_time,
          end_time,
          price,
          rule,
          is_fission,
          reward_type,
          distribution_rule,
          description,
          friend_url,
          weixin_title,
          weixin_url
        } = this.itemData
        this.formData = {
          name,
          type,
          autoDateRange: [new Date(start_time), new Date(end_time)],
          price: +price,
          step_1: Array.isArray(rule)
            ? rule.map(v => ({
              name: v.name,
              num: v.people || null,
              value: v.value,
              timestamp: `${Date.now()}_${v.people}`,
              valueType: type
            }))
            : [
              { num: 1, value: null, timestamp: Date.now(), valueType: '1' },
              { num: null, value: null, timestamp: Date.now() + 1, valueType: '1' },
            ],
          is_fission,
          reward_type: reward_type || '1',
          step_2: reward_type && Array.isArray(distribution_rule)
            ? distribution_rule.map(v => ({
              name: v.name,
              num: v.people || null,
              value: +v.value,
              timestamp: `${Date.now()}_${v.value}`
            }))
            : [{ num: null, value: null, timestamp: Date.now() }],
          description,
          friend_url,
          weixin_title,
          weixin_url
        }
      },
      // 重置指定表单
      handleResetField(prop) {
        const field = this.$refs.formRef.fields.find(field => field.prop === prop);
        // console.log(prop, !!field, this.$refs.formRef.fields.map(v => v.prop));
        field && field.resetField()
      },
      // 校验指定表单
      handleValidate(prop, trigger = 'change') {
        const field = this.$refs.formRef.fields.find(field => field.prop === prop);
        // console.log(prop, !!field, this.$refs.formRef.fields.map(v => v.prop))
        field && field.validate(trigger)
      },
      numValidator1(rule, val, cb, index) {
        const lastNum = this.formData.step_1[index - 1].num
        if (lastNum && val <= lastNum) {
          cb(new Error('当前人数需大于上一阶梯'))
        } else {
          this.formData.step_1[index + 1] && this.formData.step_1[index + 1].num && this.handleValidate('step_1.' + (index + 1) + '.num')
          cb()
        }
      },
      valueValidator1(rule, val, cb, index) {
        if (this.formData.type !== '1') {
          cb()
          return;
        }

        const nextItem = this.formData.step_1[index + 1]
        nextItem && nextItem.value && this.handleValidate('step_1.' + (index + 1) + '.value')
        if (index === 0) {
          cb()
        } else {
          const lastAmount = +this.formData.step_1[index - 1].discount_amount || 0
          const currentAmount = +this.formData.step_1[index].discount_amount
          if (lastAmount && currentAmount <= lastAmount) {
            cb(new Error("抵扣金额需大于上一阶梯"))
          } else {
            cb()
          }
        }
      },
      numValidator2(rule, val, cb, index) {
        if (this.formData.is_fission !== '1') {
          cb()
          return;
        }

        if (index === 0) {
          this.formData.step_2[index + 1] && this.formData.step_2[index + 1].num && this.handleValidate('step_2.' + (index + 1) + '.num')
          cb()
        } else {
          const lastNum = this.formData.step_2[index - 1].num
          if (lastNum && val <= lastNum) {
            cb(new Error('当前人数需大于上一阶梯'))
          } else {
            this.formData.step_2[index + 1] && this.formData.step_2[index + 1].num && this.handleValidate('step_2.' + (index + 1) + '.num')
            cb()
          }
        }
      },
      valueValidator2(rule, val, cb, index) {
        if (this.formData.is_fission !== '1' || this.formData.reward_type !== '1') {
          cb()
          return;
        }

        if (index === 0) {
          this.formData.step_2[index + 1] && this.formData.step_2[index + 1].value && this.handleValidate('step_2.' + (index + 1) + '.value')
          cb()
        } else {
          const lastNum = this.formData.step_2[index - 1].value
          if (lastNum && val <= lastNum) {
            cb(new Error('当前提成比例需大于上一阶梯'))
          } else {
            this.formData.step_2[index + 1] && this.formData.step_2[index + 1].value && this.handleValidate('step_2.' + (index + 1) + '.value')
            cb()
          }
        }
      },
      // 更换阶梯类型
      handleChangeRadio(type) {
        const { ['step_' + type]: list } = this.formData
        const { fields } = this.$refs.formRef
        list.forEach((v, i) => {
          const prop = `step_${type}.${i}.value`
          v.valueType = type === '1' ? this.formData.type : '2' // 用于判断值的类型，处理下拉选项disabled, 1折扣券  2会员卡
          v.value = null
          const field = fields.find(field => field.prop === prop);
          setTimeout(() => {
            field && (field.validateState = '')
          }, 0);
        })

        if (type === '1') {
          const options = this[[this.formData.type === '1' ? 'memberCardList' : 'couponList']]
          options.forEach(v => {
            v.disabled = v.is_delete == '1'
          })
        } else if (this.formData.reward_type === '1') {
          this.memberCardList.forEach(v => {
            v.disabled_2 = v.is_delete == '1'
          })
        }
      },
      /* 更改活动执行时间 */
      handleDateChange(val) {

      },
      // 选择阶梯奖励
      handleSelect(value, configType /* 1梯度配置 2分销配置 */, index) {
        const target = this.formData['step_' + configType][index]

        // 解除选项的禁用
        // console.log('handleSelect', value, configType, index);
        if (target.value) {
          const lastType = target.valueType // 1折扣券  2会员卡
          const lastOptions = this[lastType === '1' ? 'couponList' : 'memberCardList']
          const lastSelect = lastOptions.find(v => v.value === target.value)
          if (lastSelect) {
            lastSelect.disabled = lastSelect.is_delete == '1'
            lastSelect.disabled_2 !== undefined && (lastSelect.disabled_2 = lastSelect.is_delete == '1')
          }
        }

        if (value) {
          const currentType = configType === '1' ? this.formData.type : '2' // 1折扣券  2会员卡
          const currentOptions = this[currentType === '1' ? 'couponList' : 'memberCardList']
          const currentSelect = currentOptions.find(v => v.value === value)
          if (configType === '1') {
            target.discount_amount = currentSelect.discount_amount // 用于校验阶梯的抵扣金额大小
            currentSelect.disabled = true
          } else {
            currentSelect.disabled_2 = true
          }
          target.name = currentSelect.coupon_name || currentSelect.label
          target.value = value
        } else if (value === undefined) {
          target.value = null
          target.discount_amount = null
          const nextItem = this.formData.step_1[index + 1]
          nextItem && nextItem.value && this.handleValidate('step_' + configType + '.' + (index + 1) + '.value')
        }
      },

      // 添加阶梯
      handleAddStep(type) {
        const { ['step_' + type]: list } = this.formData
        const limitNum = type === '1' ? 100 : 5

        if (list.length < limitNum) {
          list.push({
            num: null,
            value: null,
            timestamp: Date.now(),
            valueType: type === '1' ? this.formData.type : '2' // 用于判断值的类型，处理下拉选项disabled, 1折扣券  2会员卡
          })
        } else {
          this.$Message.warning(`最多设置${limitNum}级阶梯`)
        }
      },
      // 删除阶梯
      handleDeleteStep(type, key) {
        const { ['step_' + type]: list } = this.formData
        const index = list.findIndex(v => v.timestamp === key)

        const target = list[index]
        const lastType = target.valueType // 1折扣券  2会员卡
        const lastOptions = this[lastType === '1' ? 'couponList' : 'memberCardList']
        const lastSelect = lastOptions.find(v => v.value === target.value)
        lastSelect && (lastSelect[type === '1' ? 'disabled' : 'disabled_2'] = lastSelect.is_delete == '1')

        index !== -1 && list.splice(index, 1)
      },
      // 开关分销配置
      handleChangeIsFission(value) {
        if (value === '0') {
          const { formData: { step_2 }, handleValidate } = this
          this.$nextTick(() => {
            step_2.forEach((_, i) => {
              handleValidate('step_2.' + i + '.num')
              handleValidate('step_2.' + i + '.value')
            })
          })
        }
      },

      /* 提交保存活动 */
      handleSubmit() {
        this.$refs.formRef.validate(val => {
          if(!val) {
            this.$Message.warning('请检查是否正确填写')
          } else {
            const {
              autoDateRange,
              step_1,
              step_2,
              ...rest
            } = this.formData;

            const params = {
              ...rest,
              start_time: formatDate(autoDateRange[0], 'yyyy-MM-dd HH:mm:ss'),
              end_time: formatDate(autoDateRange[1], 'yyyy-MM-dd HH:mm:ss'),
              rule: step_1.map((v, i) => ({
                level: i + 1, // 阶梯
                people: v.num, // 到达人数
                value: v.value, // 奖励值
                name: v.name, // 奖励名称
                bus_id: this.busId // 场馆id
              })),
              distribution_rule: rest.is_fission === '1'
                ? step_2.map((v, i) => ({
                  level: i + 1, // 阶梯
                  people: v.num, // 到达人数
                  value: v.value, // 奖励值
                  name: v.name, // 奖励名称
                  bus_id: this.busId // 场馆id
                }))
                : null,
              bus_id: this.busId
            };

            let url = ''
            if (this.activityId) {
              url = '/Web/ChainsRule/edit'
              params.id = this.activityId
              } else {
              url = '/Web/ChainsRule/add'
              params.user_id = this.adminId
            }

            // console.log(url, params);
            this.$service.post(url, params, { headers: { 'Content-Type': 'application/json' } }).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.$router.back()
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
          }
        });
      },
    },
  }
</script>

<style lang="less" scoped>
.step-edit-container {
  .step-edit-form {
    padding-left: 20px;
    h3 {
      margin: 50px 0 20px;
      &:first-child {
        margin-top: 0;
      }
    }
  }
  /deep/.form-item-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 24px;
    .step-num {
      margin-right: 10px;
      min-width: 55px;
    }
    .w140 {
      width: 140px;
    }
    .ivu-form-item-error-tip {
      white-space: nowrap;
    }
  }
  /deep/.ivu-select-input {
    font-size: 14px;
  }
  .tips {
    font-size: 12px;
  }
}

</style>
