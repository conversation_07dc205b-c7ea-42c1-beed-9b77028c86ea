<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.name"
        class="w200"
        clearable
        placeholder="活动标题"
        @on-enter="doSearch"
      />
      <Input
        v-model="postData.head_username_or_phone"
        class="w200"
        clearable
        placeholder="团长姓名/电话"
        @on-enter="doSearch"
      />
      <Select
        v-model="postData.user_identity"
        class="w120"
        clearable
        placeholder="团长身份">
        <Option
          v-for="item in positionOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </Select>
      <Select
        v-model="postData.reward_type"
        class="w120"
        clearable
        placeholder="奖励类型">
        <Option
          v-for="item in activityTypeOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </Select>
      <Select
        v-model="postData.awards_status"
        class="w120"
        clearable
        placeholder="发放状态">
        <Option
          v-for="item in activityStatusOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </Select>
      <Select
        v-model="postData.standard_level"
        class="w120"
        clearable
        placeholder="达标等级">
        <Option
          v-for="item in levelOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label" />
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
    />

    <footer>
      <!-- <Button @click="exportCsv">导出</Button> -->
      <Pager
        :history="false"
        :total="total"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Pager from 'components/pager';

  export default {
    name: 'StepGift',
    components: {
      Pager,
    },

    props: {
      index: {
        type: String,
        required: true
      },
      activeIndex: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        positionOptions: [ // 团长身份
          // { label: '场馆', value: '1' },
          { label: '会籍', value: '2' },
          { label: '会员', value: '3' },
        ],
        activityTypeOptions: [ // 活动奖励类型
          { label: '销售提成', value: '1' },
          { label: '会员卡', value: '2' },
        ],
        activityStatusOptions: [ // 活动发放状态
          { label: '已发放', value: '4' },
          { label: '待发放', value: '3' },
          { label: '不发放', value: '2' },
          { label: '会籍不发放', value: '1' },
        ],
        levelOptions: [
          { label: '达标等级 1', value: '1' },
          { label: '达标等级 2', value: '2' },
          { label: '达标等级 3', value: '3' },
          { label: '达标等级 4', value: '4' },
          { label: '达标等级 5', value: '5' },
          { label: '未达标', value: '0' },
        ],
        postData: {
          name: '', // 活动标题
          head_username_or_phone: '', // "团长姓名/电话
          user_identity: '', // 团长身份 1:场馆 2:会籍 3:会员
          reward_type: '', // 活动奖励类型 1:销售提成 2:会员卡
          standard_level: '', // 达标等级
          awards_status: '', // 1:会籍不发放 2:不发放 3:待发放 4:已发放
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            title: '团长姓名',
            key: 'head_username_copy',
            render: (h, { row }) =>
              row.user_identity == '3'
                ? <router-link to={{ name: '会员详情', params: { userId: row.user_id || row.m_member_id } }}>
                  <div>{ row.head_username }</div>
                  <span>（{ row.head_phone }）</span>
                </router-link>
                : row.user_identity == '2' ?
                  <div>
                    <div>{ row.head_username }</div>
                    <span>（{ row.head_phone }）</span>
                  </div>
                  : <span>{ row.head_username }</span>
          },
          {
            title: '团长身份',
            key: 'user_identity_copy',
            tooltip: true,
          },
          {
            title: '活动标题',
            key: 'name',
            tooltip: true,
          },
          {
            title: '支付人数',
            key: 'pay_nums',
          },
          {
            title: '成交金额',
            key: 'total_account',
          },
          {
            title: '达标等级',
            key: 'standard_level_copy',
          },
          {
            title: '奖励类型',
            key: 'reward_type_copy',
          },
          {
            title: '奖励明细',
            key: 'reward_copy',
          },
          {
            title: '发放状态',
            key: 'status_copy',
          },
          {
            title: '发放时间',
            key: 'awards_time',
          },
          {
            title: '操作人',
            key: 'award_action_username',
          },
          {
            title: '操作',
            key: 'id',
            render: (h, { row }) => {
              return row.awards_status == '3' && row.reward_type == '1'
                ? <i-button
                  type="text"
                  onClick={() => { this.handleConfirmModal(row); }}>
                  发放奖励
                </i-button>
                : <span>-</span>
            }
          },
        ],
      }
    },
    computed: {
      ...mapGetters(['busId', 'adminId'])
    },

    watch: {
      activeIndex(active) {
        active === this.index && this.getList()
      }
    },
    created() {
      this.activeIndex === this.index && this.getList()
    },

    methods: {
      getList() {
        const params = {
          ...this.postData,
          bus_id: this.busId
        }

        this.$service.post(
          '/Web/ChainsFission/getListAwards',
          params,
          { headers: { 'Content-Type': 'application/json' } }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;

            const statusList = ['会籍不发放', '不发放', '待发放', '已发放']
            data.list.forEach(v => {
              v.head_username_copy = v.user_identity == 1 ? v.head_username : `${v.head_username} (${v.head_phone})`
              v.status_copy =  statusList[v.awards_status - 1]
              v.standard_level_copy = v.standard_level_copy || (v.awards_level == '0' ? '未达标' : v.awards_level)
              v.awards_time = v.awards_time === '0000-00-00 00:00:00' ? '-' : v.awards_time || '-'
              v.award_action_username = v.award_action_username || '-'
              v.reward_copy =  v.awards_level == '0' ? '-' :( v.reward_copy || '-')
            })
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      pageChange(postData) {
        this.postData = { ...postData };
        this.getList();
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },

      handleConfirmModal(item) {
        const content = item.reward_type == '1'
          ?  `<div>
              <p>奖励内容 <span style="margin-left:20px;">销售提成 </span><span style="color:#D9001B;">${item.standard_distribution_rule.value}%</span></p>
              <p>最终金额 <span style="margin-left:20px;">￥${item.total_account} * ${item.standard_distribution_rule.value}% = </span><span style="color:#D9001B;">${item.reward_copy.split('(')[0]}元</span></p>
              <p style="margin-top:20px;text-align:right;font-size:12px;color:#AAAAAA;">此处操作仅是手动标记发放状态</p>
            </div>`
          : `<div>
              <p>奖励内容 <span style="margin-left:20px;"></span><span style="color:#D9001B;">${item.reward_copy}</span></p>
              <p style="margin-top:20px;text-align:right;font-size:12px;color:#AAAAAA;">此处操作仅是手动标记发放状态</p>
            </div>`;
        this.$Modal.confirm({
            title: '发放奖励',
            content,
            okText: '提交',
            onOk: () => {
              this.$service.post(
                '/Web/ChainsFission/receiveAwards',
                { id: item.id,
                  admin_id: this.adminId,
                  bus_id: this.busId
                },
                { headers: { 'Content-Type': 'application/json' } }
              ).then(res => {
                if (res.data.errorcode == 0) {
                  item.awards_status = '4';
                  setTimeout(() => {
                    this.getList()
                  }, 1000);
                  this.$Message.success(res.data.errormsg)
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              })
            },
            // onCancel: () => {}
        });
      }
    },
  }
</script>

<style lang="less" scoped>
// /deep/.icon-tips {
//   padding-left: 5px;
//   vertical-align: text-bottom;
// }
</style>
