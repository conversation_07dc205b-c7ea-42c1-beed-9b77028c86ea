<template>
  <div class="table-wrap">
    <header>
      <DatePicker type="daterange" placeholder="创建时间" v-model="dateRange" @on-change="dateChange"></DatePicker>
      <CardList v-model="postData.card_id" clearable></CardList>
      <Select v-model="postData.status" clearable>
        <Option value="1" label="未开始"></Option>
        <Option value="2" label="已结束"></Option>
        <Option value="3" label="进行中"></Option>
      </Select>
      <Button type="success" @click="doSearch">查询</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns">
      <template slot="order_count" slot-scope="{ row, index }">
        <Button type="text" ghost @click="handleModal(row.mgb_activity_id)">{{row.order_count}}</Button>
      </template>
    </Table>
    <footer>
      <Button type="success" @click="handleAdd">添加拼团</Button>
      <Pager :total="total" :post-data="postData" @on-change="pageChange"></Pager>
    </footer>
    <Modal title="拼团统计" v-model="showModal" width="700">
      <Card :bordered="false" dis-hover :title="`拼团中数量(${modalInfo.going.count})`">
        <div style="display: flex; flex-wrap: wrap">
          <Card dis-hover v-for="(item, index) in modalInfo.going.list" :key="item.mgb_order_id">
            <h3>{{modalInfo.going.activity_title}}({{item.length}}/{{people_num}})</h3>
            <p>手机号码</p>
            <div class="phone-number" v-for="phone in item" :key="phone">
              <Tag color="primary"><Icon type="ios-call" />{{phone.phone}}</Tag>
              <!-- <Tooltip :content="phone.transaction_details_serial_number" placement="right">
                <Icon type="ios-document" />流水单号
              </Tooltip> -->
            </div>
            <!-- <p>开始时间：{{formatDate(new Date(item[0].create_time * 1000), 'yyyy-MM-dd HH:mm')}}</p> -->
            <p style="margin-top: 20px;">开始时间</p>
            <p>{{formatDate(new Date(item[0].create_time * 1000), 'yyyy-MM-dd HH:mm')}}</p>
          </Card>
        </div>
      </Card>
      <Card :bordered="false" dis-hover :title="`拼团成功数量(${modalInfo.finish.count})`">
        <div style="display: flex; flex-wrap: wrap">
          <Card style="width: 30%; margin-right: 20px; margin-bottom: 20px" dis-hover v-for="(item, index) in modalInfo.finish.list"
                :key="index">
            <h3>{{modalInfo.finish.activity_title}}({{item.length}}/{{people_num}})</h3>
            <p style="margin-top: 20px;">手机号码</p>
            <div class="phone-number" v-for="phone in item" :key="phone">
              <Tag color="primary"><Icon type="ios-call" />{{phone.phone}}</Tag>
              <Tooltip :content="'流水单号:'+phone.transaction_details_serial_number">
                <Icon type="ios-document" />
              </Tooltip>
            </div>
            <p style="margin-top: 20px;">开始时间</p>
            <p>{{formatDate(new Date(item[0].create_time * 1000), 'yyyy-MM-dd HH:mm')}}</p>
          </Card>
        </div>
      </Card>
      <Card :bordered="false" dis-hover :title="`拼团失败数量(${modalInfo.fail.count})`">
        <div style="display: flex; flex-wrap: wrap">
          <Card style="width: 30%; margin-right: 20px; margin-bottom: 20px" dis-hover v-for="(item, index) in modalInfo.fail.list"
                :key="index">
            <h3>{{modalInfo.fail.activity_title}}({{item.length}}/{{people_num}})</h3>
            <p style="margin-top: 20px;">手机号码</p>
            <div class="phone-number" v-for="phone in item" :key="phone">
              <Tag color="primary"><Icon type="ios-call" />{{phone.phone}}</Tag>
              <!-- <Tooltip :content="phone.transaction_details_serial_number" placement="right">
                <Icon type="ios-document" />流水单号
              </Tooltip> -->
            </div>
            <!-- <p style="margin-top: 20px;">开始时间：{{formatDate(new Date(item[0].create_time * 1000), 'yyyy-MM-dd HH:mm')}}</p> -->
            <p style="margin-top: 20px;">开始时间</p>
            <p>{{formatDate(new Date(item[0].create_time * 1000), 'yyyy-MM-dd HH:mm')}}</p>
          </Card>
        </div>
      </Card>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import { formatDate } from 'src/utils';
  import Pager from 'components/pager';
  import CardList from 'src/components/card/cardList';

  const STATUS = { 1: '未开始', 2: '已结束', 3: '进行中' };

  export default {
    name: '',
    components: { Pager, CardList },
    data() {
      return {
        people_num: 0,
        modalInfo: {
          finish: {
            list: [],
            count: 0
          },
          going: {
            list: [],
            count: 0
          },
          fail: {
            list: [],
            count: 0
          }
        },
        showModal: false,
        hasSetting: false,
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd')],
        postData: {
          b_time: formatDate(new Date(), 'yyyy-MM-dd'),
          e_time: formatDate(new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
          card_id: '',
          status: ''
        },
        total: 0,
        columns: [
          {
            title: '活动标题',
            key: 'activity_title',
            width: 200
          },
          {
            title: '创建时间',
            key: 'createDate',
            width: 150
          },
          {
            title: '日常/拼团售价',
            key: '_price',
            width: 150
          },
          {
            title: '活动起止时间',
            key: 'date',
            width: 260
          },
          {
            title: '成团人数',
            key: 'people_num',
          },
          {
            title: '成团有效时间',
            key: 'group_buy_duration',
          },
          // {
          //   title: '成果干预',
          //   key: '',
          // },
          {
            title: '拼团统计',
            slot: 'order_count',
          },
          {
            title: '拼团业绩',
            key: 'order_total_price',
          },
          {
            title: '状态',
            key: '_status',
          },
          {
            title: '操作',
            key: '',
            width: 160,
            render: (h, param) => {
              const item = param.row;
              return <div>
                <i-button ghost type="text"
                          to={{ path: '/activity/groupBuyDetail', query: { mgb_activity_id: item.mgb_activity_id } }}>详情
                </i-button>
                <i-button ghost disabled={item.status === 2} onClick={() => {
                  this.handleFinish(item.mgb_activity_id);
                }} class="button-text-red" type="text">提前结束
                </i-button>
              </div>;
            }
          },
        ],
        tableData: []
      };
    },
    created() {
      this.getSetting();
    },
    methods: {
      formatDate,
      handleModal(mgb_activity_id) {
        const url = '/Web/Groupbuy/get_groupbuy_totallist';
        this.$service.post(url, { mgb_activity_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.people_num = data.people_num;
            this.modalInfo = data.order_total_list;
            this.showModal = true;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      getSetting() {
        const url = '/Web/Groupbuy/check_bus_pay_conf';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            this.hasSetting = true;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleAdd() {
        if (!this.hasSetting) {
          this.$Modal.confirm({
            title: '请先配置“微信支付特约商户”',
            content: '完成配置后，所有在线支付的费用，会直接到您配置的账户上',
            okText: '去配置',
            onOk: () => {
              this.$router.push({ path: '/paraset/settings' });
            },
          });
        } else {
          this.$router.push({ path: '/activity/groupBuyDetail' });
        }
      },
      handleFinish(mgb_activity_id) {
        this.$Modal.confirm({
          title: '确定提前结束吗?',
          content: '结束后，不可再发起拼团，进行中的拼团仍可继续拼团',
          onOk: () => {
            const url = '/Web/Groupbuy/stop_groupbuy';
            this.$service.post(url, { mgb_activity_id }).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.getList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }).catch(e => {
              throw new Error(e);
            });
          }
        });
      },
      dateChange([s, e]) {
        this.postData.b_time = s;
        this.postData.e_time = e;
      },
      pageChange(postData) {
        const { page_no, page_size } = postData;
        this.postData = { ...this.postData, page_no, page_size };
        this.getList();
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList(isExport) {
        const url = '/Web/Groupbuy/groupbuy_list';
        const postData = isExport ? { ...this.postData, page_no: 1, page_size: this.total } : this.postData;
        return this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data.map(item => {
              return {
                ...item,
                _price: `${item.price} / ${item.group_buy_price}`,
                date: `${formatDate(new Date(item.activity_b_time * 1000), 'yyyy-MM-dd')} ~ ${formatDate(new Date(item.activity_stop_time * 1000), 'yyyy-MM-dd')}`,
                _group_buy_meddle: item.group_buy_meddle === '1' ? '启用' : '禁用',
                _status: STATUS[item.status],
                createDate: formatDate(new Date(item.create_time * 1000), 'yyyy-MM-dd')
              };
            });
            this.total = +res.data.count;
            this.tableData = list;
            return list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      async exportCsv() {
        const list = await this.getList(true);
        const columns = this.columns;
        const data = list.map(item => {
          return { ...item, };
        });
        this.$refs.table.exportCsv({
          filename: '导出列表',
          data,
          columns
        });
      }
    },
  };
</script>

<style scoped>
  .phone-number {
    padding-top: 10px;
  }
</style>
