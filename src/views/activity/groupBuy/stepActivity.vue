<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.name"
        class="w200"
        clearable
        :maxlength="100"
        placeholder="活动标题"
        @on-enter="doSearch"
      />
      <Select v-model="postData.type" clearable placeholder="活动类型">
        <Option
          v-for="({label, value}) of activityTypeOptions"
          :key="value"
          :value="value"
          :label="label" />
      </Select>
      <Select v-model="postData.status" clearable placeholder="活动状态">
        <Option
          v-for="({label, value}) of activityStatusOptions"
          :key="value"
          :value="value"
          :label="label" />
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
    />

    <footer>
      <Button type="success" @click="handleToEditPage('add')">添加</Button>
      <Pager
        :history="false"
        :total="total"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Pager from 'components/pager';

  export default {
    name: 'StepStatistic',
    components: {
      Pager,
    },

    props: {
      index: {
        type: String,
        required: true
      },
      activeIndex: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        activityTypeOptions: [
          { label: '折扣券梯度升级', value: 1 },
          { label: '会员卡梯度升级', value: 2 },
        ],
        activityStatusOptions: [
          { label: '未开始', value: '1' },
          { label: '进行中', value: '2' },
          { label: '已结束', value: '3' },
        ],
        postData: {
          // bus_id
          name: null,
          type: null, // 活动类型 1:折扣卷 2:会员卡
          status: null, // 活动状态 1:未开始 2:进行中 3:已结束
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            title: '活动标题',
            key: 'name',
            tooltip: true,
          },
          {
            title: '创建时间',
            key: 'create_time',
          },
          {
            title: '类型',
            key: 'type_copy',
          },
          {
            title: '活动起止时间',
            key: 'start_stop_time',
            minWidth: 90
          },
          {
            title: '活动状态',
            key: 'status_copy',
          },
          {
            title: '操作',
            key: 'id',
            render: (h, { row }) => {
              return <div>
                {
                  row.status != 1
                    ? <i-button
                      type="text"
                      style="margin-right: 10px"
                      onClick={ () => { this.handleToEditPage('detail', row) } }>
                      详情
                    </i-button>
                    : null
                }
                {
                  row.status == 1
                    ? <div>
                      <i-button
                        type="text"
                        style="margin-right: 10px"
                        onClick={ () => { this.handleToEditPage('put', row); } }
                      >
                        编辑
                      </i-button>
                      <i-button
                        type="text"
                        class="button-text-red"
                        onClick={ () => { this.handleFinal('delete', row); } }
                      >
                        删除
                      </i-button>
                    </div>
                    : null
                }
                {
                  row.status == 2
                    ? <i-button
                      type="text"
                      class="button-text-red"
                      onClick={ () => { this.handleFinal('stop', row) } }>
                      提前结束
                    </i-button>
                    : null
                }
              </div>
            }
          },
        ],
      }
    },
    computed: {
      ...mapGetters(['busId'])
    },

    watch: {
      activeIndex(active) {
        active === this.index && this.getList()
      }
    },
    created() {
      this.activeIndex === this.index && this.getList()
    },

    methods: {
      getList() {
        const params = {
          bus_id: this.busId,
          ...this.postData,
        }

        this.$service.post(
          '/Web/ChainsRule/lists',
          params,
          { headers: { 'Content-Type': 'application/json' } }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            data.list.forEach(v => {
              v.start_stop_time = `${v.start_time} - ${v.end_time}`
              v.type_copy = v.type === '1' ? '折扣券梯度升级' : '会员卡梯度升级'
            })
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      pageChange(postData) {
        this.postData = { ...postData };
        this.getList();
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },
      // 添加、编辑、详情
      handleToEditPage(type, item) {
        this.$router.push({
          name: '团购接龙活动详情',
          // path: '/activity/groupBuyStep/editActivity',
          params: {
            id: type === 'add' ? '' : item.id,
            isReadonly: type === 'detail',
            data: item
          }
        })
      },
      // 结束、删除
      handleFinal(type, item) {
        this.$Modal.confirm({
          title: type === 'stop' ? '提前结束' : '删除活动',
          content: type === 'stop' ? '提前结束后，已付款的客户会直接发放对应梯度的商品到其账户中' : '确认删除该活动吗？',
          onOk: () => {
            this.$service.post(
              type === 'stop' ? '/Web/ChainsRule/earlyEnd' : '/Web/ChainsRule/del',
              { id: item.id, bus_id: this.busId, loading: true },
              { headers: { 'Content-Type': 'application/json' } }
            ).then(res => {
              if (res.data.errorcode == 0) {
                item.status = '3';
                setTimeout(() => {
                  this.getList()
                }, 1000);
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
          },
        })
      }
    },
  }
</script>

<style lang="less" scoped>
// /deep/.icon-tips {
//   padding-left: 5px;
//   vertical-align: text-bottom;
// }
</style>
