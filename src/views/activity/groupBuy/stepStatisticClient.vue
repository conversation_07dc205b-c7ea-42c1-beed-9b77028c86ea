<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.username_or_phone"
        class="w200"
        clearable
        placeholder="会员名称/电话"
        @on-enter="doSearch"
      />
      <Select v-model="postData.chains_rule_id" filterable placeholder="选择活动">
        <Option
          v-for="item in activityOptions"
          :key="item.id"
          :value="item.id"
          :label="item.name" />
      </Select>
      <SalesSelect
        v-model="postData.achievement_user_id"
        :belongBusId="busId"
        placeholder="选择业绩归属"
      />
      <Button type="success" @click="doSearch">搜索</Button>
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
    />

    <footer>
      <Button @click="exportCsv">导出</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :history="false"
        :total="total"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Pager from 'components/pager';
  import SalesSelect from 'components/membership/salesSelect'
  import Export from 'src/components/Export'

  export default {
    name: 'StepStatisticClient',
    components: {
      Pager,
      SalesSelect,
      Export
    },

    data() {
      return {
        activityOptions: [],
        msOptions: [],
        postData: {
          chains_rule_id: this.$route.params.id,
          username_or_phone: '',
          achievement_user_id: '',
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            title: '活动标题',
            key: 'name',
            tooltip: true,
          },
          {
            title: '客户姓名',
            key: 'username',
            render: (h, { row }) => {
              return <router-link to={{ name: '会员详情', params: { userId: row.user_id || row.m_member_id } }}>
                <div>{ row.username }</div>
              </router-link>
              // <span>（{ row.user_phone }）</span>
            }

          },
          {
            title: '支付时间',
            key: 'create_time_copy',
          },
          {
            title: '领取时间',
            key: 'awards_time',
          },
          {
            title: '团长姓名',
            key: 'head_username',
            render: (h, { row }) =>
              row.user_identity != '1'
                ? <div>
                    <div>{ row.head_username }</div>
                    <span>（{ row.head_phone }）</span>
                  </div>
                : <span>{ row.head_username }</span>
          },
          {
            title: '团长身份',
            key: 'user_identity_copy',
          },
          {
            title: '接龙名次',
            key: 'rank_copy',
          },
          {
            title: '成交金额',
            key: 'account_copy',
          },
          {
            title: '业绩归属',
            key: 'achievement_username_copy',
          },
        ],

      }
    },
    computed: {
      ...mapGetters(['busId'])
    },

    created() {
      if (this.$route.params.id) {
        this.getList()
        this.getAllActivity()
      } else {
        this.$router.push({ name: '团购接龙' })
      }
    },

    methods: {
      getList(is_export = false) {
        const params = {
          ...this.postData,
          bus_id: this.busId,
          is_export,
        }

        return this.$service.post(
          '/Web/ChainsOrder/statistics',
          params,
          { headers: { 'Content-Type': 'application/json' }, isExport: is_export }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            data.list.forEach(v => {
              // v.awards_time = v.awards_time === "0000-00-00 00:00:00" ? '' : v.awards_time
              v.create_time_copy = v.is_pay != 0 ? v.create_time : '-'
              v.rank_copy = v.is_pay != 0 ? v.rank : '-'
              v.account_copy =  v.is_pay != 0 ? v.account : '-'
              v.achievement_username_copy =  v.is_pay != 0 ? v.achievement_username : '-'
            })
            this.tableData = data.list;
            return data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      getAllActivity() {
        const params = {
          bus_id: this.busId,
          page_no: 1,
          page_size: 999
        }

        this.$service.post(
          '/Web/ChainsRule/lists',
          params,
          { headers: { 'Content-Type': 'application/json' } }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.activityOptions = [
              { name: '全部活动', id: '' },
              ...data.list
            ];
          }
        })
      },

      pageChange(postData) {
        this.postData = { ...postData };
        this.getList();
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },

      async exportCsv() {
        const resData = await this.getList(true)
        this.$refs.export.export({
          filename: '团购接龙 - 客户明细统计',
          columns: this.columns,
          data: resData
        })
      }
    },
  }
</script>

<style lang="less" scoped>
/deep/.icon-tips {
  padding-left: 5px;
  vertical-align: text-bottom;
}
</style>
