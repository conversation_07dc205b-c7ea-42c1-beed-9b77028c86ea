<template>
  <div class="container group-buy-detail">
    <header><h3>拼团配置</h3></header>
    <Form class="form" ref="form" :model="postData" :label-width="200">
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">上传活动宣传图(非必传)</p>
          <p class="tip">图片最佳尺寸: 900X1600</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <Cropper v-model="postData.propaganda_img" :ratio="9/16" width="300px" height="400px"></Cropper>
      </FormItem>
      <FormItem label=" ">
        <div class="tips">用于朋友圈等分享和传播此活动，请确认上传的图片为16:9(高:宽)的比例，以便系统自动在图片最底部追加放置此活动的二维码</div>
      </FormItem>
      <FormItem label=" ">
        <Divider dashed></Divider>
      </FormItem>
      <FormItem label="拼团活动标题" prop="activity_title" :rules="{ required: true, message: '请填写' }">
        <Input v-model="postData.activity_title" :maxlength="12" placeholder="最多12个字符"/>
      </FormItem>
      <FormItem label="选择会员卡" prop="card_id" :rules="{ required: true, message: '请选择' }">
        <CardList v-model="postData.card_id" :except-type="['4','5']" only-sale filterable></CardList>
      </FormItem>
      <FormItem label="日常售价" prop="price" :rules="{ required: true, message: '请填写' }">
        <InputNumber :min="0" :precision="2" :active-change="false" v-model="postData.price"/>
      </FormItem>
      <FormItem label="拼团售价" prop="group_buy_price" :rules="{ required: true, message: '请填写' }">
        <InputNumber :min="0" :precision="2" :active-change="false" v-model="postData.group_buy_price "/>
      </FormItem>
      <FormItem label="活动起止时间" prop="activity_b_time" :rules="{ required: true, message: '请选择' }">
        <DatePicker :editable="false" type="daterange" v-model="dateRange" @on-change="dateChange"
                    :options="dateOptions"></DatePicker>
      </FormItem>
      <FormItem label="成团人数" prop="people_num" :rules="{ required: true, message: '请填写' }">
        <div class="item-box">
          <Select v-model="postData.people_type" style="width:80px;margin-right:20px">
            <Option :value="0">仅限</Option>
            <Option :value="1">最少</Option>
          </Select>
          <InputNumber v-model="postData.people_num" placeholder="最多10人" :precision="0" :min="1" :max="10"/>
        </div>
      </FormItem>
      <FormItem label="成团有效时间">
        <div class="item-box">
          <RadioGroup type="button" v-model="postData.group_buy_duration">
            <Radio :label="12" :disabled="disableList[0]">12小时</Radio>
            <Radio :label="24" :disabled="disableList[1]">24小时</Radio>
            <Radio :label="36" :disabled="disableList[2]">36小时</Radio>
            <Radio :label="48" :disabled="disableList[3]">48小时</Radio>
            <Radio :label="7*24" :disabled="disableList[4]">7天</Radio>
          </RadioGroup>
          <div class="check-box">
            <Checkbox v-model="postData.group_buy_meddle" :true-value="1" :false-value="0">自动加入机器人促使成团</Checkbox>
            <Checkbox v-model="postData.can_repeated" :true-value="1" :false-value="0">同一用户仅能发起一次拼团</Checkbox>
          </div>
        </div>
      </FormItem>
      <FormItem label="拼团介绍">
        <Editor v-model="postData.content" :height="200"></Editor>
      </FormItem>
      <FormItem label=" " style="margin-top: 50px;">
        <Divider>用户转发邀请配置(不配置将使用系统默认模板)</Divider>
      </FormItem>
      <FormItem label="示例">
        <img src="~assets/img/example_group_buy.png" alt="">
      </FormItem>
      <FormItem label="转发标题">
        <Input v-model="share_config.title" placeholder="请输入标题, 不超过20个字符" :maxlength="20"/>
      </FormItem>
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">转发图片</p>
          <p class="tip">图片最佳尺寸: 500X400</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <Cropper v-model="share_config.image" :ratio="5/4" :output-width="600" style="width: 300px"></Cropper>
      </FormItem>
      <FormItem label=" ">
        <div class="tips">
          <p>1. 发布后，不支持再次编辑修改</p>
          <p>2. 客户开团后，如果成团有效时间结束时，其拼团仍不满足成团人数，系统会自动模拟用户加入其拼团并视为拼团成功</p>
          <p>3. 如果在活动过程中，修改或删除了此活动对应的会员卡，活动仍会以修改或删除之前的会员卡进行</p>
        </div>
      </FormItem>
      <FormItem label=" " style="padding-top: 50px; padding-bottom: 100px;">
        <Button type="success" style="margin-right: 30px" @click="submit" :disabled="!!mgb_activity_id">发布</Button>
        <Button :to="{ path: 'groupBuyList' }">取消</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import CardList from 'src/components/card/cardList';
  import { formatDate, unescapeHTML } from 'src/utils';
  import Editor from 'src/components/form/Editor';
  import Cropper from 'src/components/form/cropperPlus';

  const DISABLE_LIST_DEFAULT = [false, false, false, false, false];

  export default {
    name: 'groupBuyDetail',
    components: { CardList, Editor, Cropper },
    data() {
      return {
        mgb_activity_id: '',
        dateRange: [],
        share_config: {
          title: '',
          image: ''
        },
        postData: {
          price: 0,
          group_buy_price: 0,
          activity_title: '',
          card_id: '',
          activity_b_time: '',
          activity_e_time: '',
          people_num: 1,
          group_buy_duration: 12,
          content: '',
          propaganda_img: '',
          share_config: '',
          people_type: 0,
          can_repeated: 1,
          group_buy_meddle: 1
        },
        dateOptions: {
          disabledDate(date) {
            return Date.now() - date > 24 * 3600 * 1000;
          }
        },
        disableList: DISABLE_LIST_DEFAULT
      };
    },
    created() {
      this.mgb_activity_id = this.$route.query.mgb_activity_id;
      if (this.mgb_activity_id) {
        this.getInfo();
      }
    },
    methods: {
      dateChange([s, e]) {
        this.postData.activity_b_time = s;
        this.postData.activity_e_time = e;

        // 禁用成团时间
        const startDate = new Date(s);
        const endDate = new Date(e)
        const betweenTime = endDate.getTime() - startDate.getTime();
        const oneDayTime = 1000*60*60*24;
        const betweenDay = betweenTime/oneDayTime+1;
        if (betweenDay === 1) {
          this.disableList = [false, false, true, true, true]
          if (![12, 24].some(item=>item===this.postData.group_buy_duration)) {
            this.postData.group_buy_duration = 12;
          }
        } else if (betweenDay < 7) {
          this.disableList = [false, false, false, false, true]
          if (this.postData.group_buy_duration === 7*24) {
            this.postData.group_buy_duration = 12;
          }
        } else {
          this.disableList = DISABLE_LIST_DEFAULT;
        }
      },
      getInfo() {
        const url = '/Web/Groupbuy/get_groupbuy_info';
        this.$service.post(url, { mgb_activity_id: this.mgb_activity_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            const { activity_b_time, activity_e_time } = data;
            this.postData = {
              ...data,
              price: +data.price,
              group_buy_price: +data.group_buy_price,
              people_num: +data.people_num,
              group_buy_duration: data.group_buy_duration / 3600,
              content: unescapeHTML(data.content)
            };
            this.share_config = data.share_config;
            this.dateRange = [formatDate(new Date(activity_b_time * 1000), 'yyyy-MM-dd HH:mm:ss'), formatDate(new Date(activity_e_time * 1000), 'yyyy-MM-dd HH:mm:ss')];
            this.dateChange(this.dateRange);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      async submit() {
        const valid = await this.$refs.form.validate();
        if (!valid) return false;

        this.postData.share_config = JSON.stringify(this.share_config);

        if (this.postData.group_buy_price > this.postData.price) {
          return this.$Message.error('拼团售价大于日常售价')
        }

        const url = '/Web/Groupbuy/add_groupbuy';
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.$router.back();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      }
    },
  };
</script>

<style scoped lang="less">
  .tips {
    color: red
  }

  .item-box {
    display: flex;
    flex-direction: row;

    .check-box {
      display: flex;
      flex-direction: column;
      margin-left: 30px;
    }
  }
</style>
