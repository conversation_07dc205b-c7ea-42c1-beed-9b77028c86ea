<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.name"
        class="w200"
        clearable
        :maxlength="100"
        placeholder="活动名称"
        @on-enter="doSearch"
      />
      <Select v-model="postData.type" clearable placeholder="活动类型">
        <Option
          v-for="({label, value}) of activityTypeOptions"
          :key="value"
          :value="value"
          :label="label" />
      </Select>
      <Select v-model="postData.status" clearable placeholder="活动状态">
        <Option
          v-for="({label, value}) of activityStatusOptions"
          :key="value"
          :value="value"
          :label="label" />
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
    />

    <footer>
      <Button @click="exportCsv">导出</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :history="false"
        :total="total"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Pager from 'components/pager';
  import Export from 'src/components/Export'

  export default {
    name: 'StepStatistic',
    components: {
      Pager,
      Export
    },

    props: {
      index: {
        type: String,
        required: true
      },
      activeIndex: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        activityTypeOptions: [
          { label: '折扣券梯度升级', value: 1 },
          { label: '会员卡梯度升级', value: 2 },
        ],
        activityStatusOptions: [
          { label: '未开始', value: 1 },
          { label: '进行中', value: 2 },
          { label: '已结束', value: 3 },
        ],
        postData: {
          // bus_id
          name: null,
          type: null, // 活动类型 1:折扣卷 2:会员卡
          status: null, // 活动状态 1:未开始 2:进行中 3:已结束
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            title: '活动标题',
            key: 'name',
            tooltip: true,
          },
          {
            title: '活动类型',
            key: 'type_copy',
          },
          {
            title: '活动状态',
            key: 'status_copy',
          },
          {
            title: '开团数',
            key: 'initiate_nums',
            render: (h, { row }) =>
              <router-link to={{ name: '开团明细统计', params: { id: row.id } }}>{ row.initiate_nums }</router-link>
          },
          {
            title: '支付人次',
            key: 'pay_nums',
            renderHeader(h, params) {
              return <div>
                支付人次
                <Tooltip content="统计所有活动中，总计的支付人次，即购买了接龙活动的次数，叠加计算、不去重" max-width="200" transfer>
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
            render: (h, { row }) =>
              <router-link to={{ name: '客户明细统计', params: { id: row.id } }}>{  row.pay_nums }</router-link>
          },
          {
            title: '支付人数',
            key: 'pay_distinct_nums',
            renderHeader(h, params) {
              return <div>
                支付人数
                <Tooltip content="统计所有活动中，总计的支付人数，即购买了接龙活动的人数，每个活动范围内计算去重" max-width="200" transfer>
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
            render: (h, { row }) =>
              <router-link to={{ name: '客户明细统计', params: { id: row.id } }}>{  row.pay_distinct_nums }</router-link>
          },
          {
            title: '新增客户数',
            key: 'new_add_user_nums',
            renderHeader(h, params) {
              return <div>
                新增客户数
                <Tooltip content="统计所有活动中，通过访问接龙活动页面注册的人数(通过a活动页面注册，即计入a活动)" max-width="200" transfer>
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
            render: (h, { row }) =>
              <router-link to={{ name: '客户明细统计', params: { id: row.id } }}>{ row.new_add_user_nums }</router-link>
          },
          {
            title: '成交金额',
            key: 'total_account_copy',
            renderHeader(h, params) {
              return <div>
                成交金额
                <Tooltip content="统计所有活动中，总计的成交金额">
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
          },
          {
            title: '30日内使用人次(率)',
            key: 'activation_by_30_nums_copy',
            renderHeader(h, params) {
              return <div>
                30日内使用人次(率)
                <Tooltip content="购买的会员卡/折扣券在30日内用该卡签到/使用该折扣券的人次和比率" max-width="200" transfer>
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
          },
          {
            title: '30日内使用人数(率)',
            key: 'activation_by_30_distinct_nums_copy',
            renderHeader(h, params) {
              return <div>
                30日内使用人数(率)
                <Tooltip content="购买的会员卡/折扣券在30日内用该卡签到/使用该折扣券的人数和比率" max-width="200" transfer placement="left-start">
                  <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
                </Tooltip>
              </div>
            },
          },

        ],

      }
    },
    computed: {
      ...mapGetters(['busId'])
    },

    watch: {
      activeIndex(active) {
        active === this.index && this.getList()
      }
    },

    created() {
      this.activeIndex === this.index && this.getList()
    },

    methods: {
      getList(is_export = false) {
        const params = {
          ...this.postData,
          bus_id: this.busId,
          is_export,
        }

        return this.$service.post(
          '/Web/ChainsRule/statistics',
          params,
          { headers: { 'Content-Type': 'application/json' }, isExport: is_export }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            data.list.forEach(v => {
              v.total_account_copy = '￥' + (v.total_account || '0.00')
              v.type_copy = v.type === '1' ? '折扣券梯度升级' : '会员卡梯度升级'
              v.activation_by_30_nums_copy = `${v.activation_by_30_nums} （${v.activation_by_30_utilization}%）`
              v.activation_by_30_distinct_nums_copy = `${v.activation_by_30_distinct_nums} （${v.activation_by_30_distinct_utilization}%）`
            })
            if (!is_export) {
              this.total = +data.count;
              this.tableData = data.list;
            }
            return data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      pageChange(postData) {
        this.postData = { ...postData };
        this.getList();
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },

      async exportCsv() {
        const resData = await this.getList(true)
        this.$refs.export.export({
          filename: '团购接龙 - 活动统计',
          columns: this.columns,
          data: resData
        })
      }
    },
  }
</script>

<style lang="less" scoped>
/deep/.icon-tips {
  padding-left: 5px;
  vertical-align: text-bottom;
}
</style>
