<template>
  <router-view v-if="$route.name !== '团购接龙'" />
  <div v-else class="tab-table-wrap customized-tabs">
    <Tabs :value="activeIndex" @on-click="clickTabs">
      <TabPane label="活动列表" name="0">
        <StepActivity
          v-if="activated.includes('0')"
          index="0"
          class="table-wrap"
          :activeIndex="activeIndex" />
      </TabPane>

      <TabPane label="团长奖励" name="1">
        <StepGift
          v-if="activated.includes('1')"
          index="1"
          class="table-wrap"
          :activeIndex="activeIndex"
        />
      </TabPane>

      <TabPane label="活动统计" name="2">
        <StepStatistic
          v-if="activated.includes('2')"
          index="2"
          class="table-wrap"
          :activeIndex="activeIndex"
        />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import StepActivity from './stepActivity';
  import StepGift from './stepGift';
  import StepStatistic from './stepStatistic';
  import tabsMixins from 'mixins/tabs'

  export default {
    name: 'StepTabs',
    components: {
    StepActivity,
    StepGift,
    StepStatistic,
  },
    mixins: [tabsMixins],
    data() {
      return {

      }
    }
  }
</script>

<style lang="less" scoped>

</style>
