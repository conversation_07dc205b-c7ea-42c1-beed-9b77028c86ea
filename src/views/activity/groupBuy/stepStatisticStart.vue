<template>
  <div class="table-wrap">
    <header>
      <Select
        v-model="postData.chains_rule_id"
        filterable
        placeholder="选择活动"
        @on-change="doSearch">
        <Option
          v-for="item in activityOptions"
          :key="item.id"
          :value="item.id"
          :label="item.name" />
      </Select>
      <!-- <Button type="success" @click="doSearch">搜索</Button> -->
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
    />

    <footer>
      <Button @click="exportCsv">导出</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :history="false"
        :total="total"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import Pager from 'components/pager';
  import Export from 'src/components/Export'

  export default {
    name: 'StepStatisticStart',
    components: {
      Pager,
      Export
    },
    data() {
      return {
        activityOptions: [],
        postData: {
          chains_rule_id: this.$route.params.id,
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        columns: [
          {
            title: '活动标题',
            key: 'name',
            tooltip: true,
          },
          {
            title: '团长姓名',
            key: 'head_username',
            render(h, { row }) {
              return row.user_identity == '3'
                ? <router-link to={{ name: '会员详情', params: { userId: row.user_id || row.m_member_id } }}>
                  <div>{ row.head_username }</div>
                  <span>（{ row.head_phone }）</span>
                </router-link>
                : row.user_identity == '2' ?
                  <div>
                    <div>{ row.head_username }</div>
                    <span>（{ row.head_phone }）</span>
                  </div>
                  : <span>{ row.head_username }</span>
            }
          },
          {
            title: '团长身份',
            key: 'user_identity_copy',
          },
          {
            title: '开团时间',
            key: 'create_time',
          },
          {
            title: '支付人数',
            key: 'pay_nums',
            render: (h, { row }) =>
              <router-link to={{ name: '客户明细统计', params: { id: this.postData.chains_rule_id } }}>{  row.pay_nums }</router-link>
          },
          {
            title: '成交金额',
            key: 'total_account_copy',
          },
        ],

      }
    },
    computed: {
      ...mapGetters(['busId'])
    },

    created() {
      if (this.$route.params.id) {
        this.getList()
        this.getAllActivity()
      } else {
        this.$router.push({ name: '团购接龙' })
      }
    },

    methods: {
      getList(is_export = false) {
        const params = {
          ...this.postData,
          bus_id: this.busId,
          is_export
        }

        return this.$service.post(
          '/Web/ChainsFission/statistics',
          params,
          { headers: { 'Content-Type': 'application/json' }, isExport: is_export }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            data.list.forEach(v => {
              v.total_account_copy = '￥' + (v.total_account || '0.00')
            })
            this.tableData = data.list;
            return data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      getAllActivity() {
        const params = {
          bus_id: this.busId,
          page_no: 1,
          page_size: 999
        }

        this.$service.post(
          '/Web/ChainsRule/lists',
          params,
          { headers: { 'Content-Type': 'application/json' } }
        ).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.activityOptions = data.list;
          }
        })
      },

      pageChange(postData) {
        this.postData = { ...postData };
        this.getList();
      },

      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },

      async exportCsv() {
        const resData = await this.getList(true)
        this.$refs.export.export({
          filename: '团购接龙 - 开团明细统计',
          columns: this.columns,
          data: resData
        })
      }
    },
  }
</script>

<style lang="less" scoped>
/deep/.icon-tips {
  padding-left: 5px;
  vertical-align: text-bottom;
}
</style>
