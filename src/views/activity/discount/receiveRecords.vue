<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" placeholder="姓名/电话" />
      <Select v-model="postData.is_member" placeholder="会员/潜客" clearable>
        <Option value="1">会员</Option>
        <Option value="2">潜客</Option>
      </Select>
      <Select v-model="postData.receive_type" placeholder="获取方式" clearable>
        <Option v-for="(type, index) in receiveTypes" :key="index" :value="index + 1">{{ type }}</Option>
      </Select>
      <busDiscount v-model="postData.coupon_id" show-history />
      <Select v-model="postData.status" placeholder="使用状态" clearable>
        <Option value="1">未使用</Option>
        <Option value="2">已使用</Option>
        <Option value="3">已过期</Option>
      </Select>
      <Button style="width: auto" type="success" @click="doSearch">搜索</Button>
    </header>
    <Total v-model="totalItems" />
    <Table ref="table" :columns="columns" :data="tableData" />
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager
        :total="listCount"
        :history="false"
        :post-data="postData"
        @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
  import busDiscount from 'components/form/busDiscount';
  import Total from 'components/form/Total';
  import Pager from 'components/pager';

  const STATUS = ['未使用', '已使用', '已过期'];
  const RECEIVES = ['前台发放', '红包', '魅力值兑换', '他人转赠(前台)', '他人转赠(红包)', '他人转赠(魅力值)', '运营计划', '客带客活动奖励', '积分兑换', '他人转赠(积分)', '接龙活动购买'];

  const TOTAL = [
    { value: 0, name: '总发放量' },
    { value: 0, name: '转赠次数' },
    { value: 0, name: '已使用量' },
    { value: '0 / 0', name: '潜客 / 会员' }
  ];
  export default {
    name: 'ReceiveRecords',
    components: { Total, Pager, busDiscount },
    data() {
      return {
        totalItems: TOTAL,
        receiveTypes: RECEIVES,
        postData: {
          search: '',
          is_member: '',
          receive_type: '',
          status: '',
          coupon_id: '',
          page_no: 1,
          page_size: 10
        },
        listCount: 0,
        tableData: [],
        columns: [
          {
            title: '姓名/昵称',
            key: 'user_name',
            render: (h, params) => {
              const item = params.row;
              if (item.user_id) {
                return (
                  <router-link to={{ name: '会员详情', params: { userId: item.user_id } }}>{item.user_name}</router-link>
                );
              } else {
                return <div>{item.user_name}</div>;
              }
            }
          },
          {
            title: '电话',
            key: 'phone'
          },
          {
            title: '会员/潜客',
            key: 'is_member'
          },
          {
            title: '获取方式',
            key: 'receive_type'
          },
          {
            title: '转赠次数',
            key: 'transfer_times'
          },
          {
            title: '最初拥有者',
            key: 'first_owner',
            render: (h, params) => {
              const item = params.row;
              if (item.first_owner_id) {
                return (
                  <router-link to={{ path: '/member/detail', name: '会员详情', params: { userId: item.first_owner_id } }}>
                    {item.first_owner}
                  </router-link>
                );
              } else {
                return <div>{item.first_owner || '-'}</div>;
              }
            }
          },
          {
            title: '折扣券',
            key: 'coupon_name'
          },
          {
            title: '使用状态',
            key: 'usage',
            render: (h, params) => {
              const item = params.row;
              return <div class={item.status == 2 ? 'color-success' : ''}>{item.usage}</div>;
            }
          }
        ]
      };
    },
    created() {
      this.postData.coupon_id = this.$route.params.id;
      this.getList();
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData, ...{ coupon_id: this.postData.coupon_id } };
        this.getList();
      },
      getList() {
        const url = '/Web/Coupon/get_receive_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.listCount = data.count;
              this.totalItems[0].value = data.grant_count;
              this.totalItems[1].value = data.transfer_count;
              this.totalItems[2].value = data.used_count;
              this.totalItems[3].value = `${data.submersible_count} / ${data.member_count}`;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    usage: STATUS[item.status - 1]
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Coupon/get_receive_list';
        return this.$service
          .post(url, { ...this.postData, ...{ page_no: 1, page_size: this.listCount } }, { isExport: true })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.list.map(item => {
                return {
                  ...item,
                  ...{
                    usage: STATUS[item.status - 1],
                    isUser: item.user_id ? '会员' : '潜客'
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const data = await this.getExportData();
        this.$refs.table.exportCsv({ columns: this.columns, data, filename: '折扣券领取记录' });
      }
    }
  };
</script>

<style lang="less" scoped>
  header > * {
    width: 150px;
  }
</style>
