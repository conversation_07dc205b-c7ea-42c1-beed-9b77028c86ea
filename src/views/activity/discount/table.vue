<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.coupon_name" placeholder="折扣券名称" @on-enter="doSearch" />
      <Select
        v-model="postData.status"
        class="w120"
        clearable
        placeholder="状态">
        <Option value="1">启用</Option>
        <Option value="2">禁用</Option>
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table
      ref="table"
      :columns="columns"
      :data="tableData"
      stripe
      @on-selection-change="onSelectionChange"
      @on-select="onSelect"
      @on-select-all="onSelectAll"
      @on-select-cancel="onSelectCancel" />
    <footer>
      <router-link :to="{name: '折扣券设置'}">
        <Button style="margin-right: 15px" type="success">新增折扣券</Button>
      </router-link>
      <Button style="margin-right: 15px" @click="handleSwitchChangeSome('1')">批量启用</Button>
      <Button style="margin-right: 15px" @click="handleSwitchChangeSome('2')">批量禁用</Button>
      <Button style="margin-right: 15px" @click="handleDelete">批量删除</Button>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { DISCOUNT_CARD_LIMIT as LIMIT_TYPE } from 'store/constants';
  import Selection from 'mixins/selection';

  const useLimit = {
    '1': '购卡/购课',
    '2': '续卡/续课',
    '3': '卡课升级',
    '4': '订场地',
    '5': '购散场票',
  }

  export default {
    name: 'DiscountList',
    components: {
      Pager
    },
    mixins: [Selection],

    data() {
      return {
        columns: [
          {
            type: 'selection',
            width: 80
          },
          {
            title: '折扣券名称',
            key: 'coupon_name'
          },
          {
            title: '折扣金额',
            key: 'discount_amount'
          },
          {
            title: '限制',
            key: 'limits',
            render: (h, params) => {
              const item = params.row;
              return <div>{item.limits.map(item => <p>{item}</p>)}</div>;
            }
          },
          {
            title: '使用范围',
            key: 'use_limit',
            render: (h, { row }) => {
              const text = row.use_limit ?
                row.use_limit.split(',').map(v => useLimit[v]).join('、') :
                useLimit['1']
              return <span>{ text }</span>;
            }
          },
          {
            title: '使用有效期',
            key: 'validDate'
          },
          {
            title: '已使用/已发出',
            key: 'usage',
            render: (h, params) => {
              const item = params.row;
              return (
                <router-link to={{ name: '折扣券领取记录', params: { id: item.coupon_id } }}>{item.usage}</router-link>
              );
            }
          },
          {
            title: '使用状态',
            key: 'status',
            render: (h, { row }) => {
              return <i-switch
                value={row.status == '1'}
                on-on-change={() => { this.handleSwitchChange(row) }}
              />
            }
          },
          {
            title: '操作',
            key: '',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <router-link to={{ name: '折扣券发放记录', params: { id: item.coupon_id } }}>
                    <i-button style="margin-right: 15px" type="text">
                      发放记录
                    </i-button>
                  </router-link>

                  <i-button onClick={() => this.handleDelete(item.coupon_id)} type="text" class="button-text-red">
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        tableData: [],
        listCount: 0,
        postData: {
          coupon_name: '',
          status: '',
          page_no: 1,
          page_size: 10
        },
        selectionItemKey: 'coupon_id'
      };
    },
    created() {
      // this.getList();
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange(postData) {
        this.postData = {...this.postData, ...postData};
        this.getList();
      },
      getList() {
        const url = '/Web/Coupon/coupon_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const { selectionId } = this
              const data = res.data.data;
              this.listCount = data.count;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    usage: `${item.use_num}/${item.issue_num}`,
                    discount_amount: `￥${item.discount_amount}`,
                    validDate: `${item.start_time}~${item.end_time}`,
                    limits: [
                      item.limit_type == 1 ? `消费满${item.limit_amount}元使用` : '',
                      LIMIT_TYPE[item.limit_card - 1],
                      item.limit_space_type == 1 ? '全部场地' : item.limit_space_type == 2 ? '仅限指定场地类型' : null,
                      item.limit_san_rule == 1 ? '全部散场票' : item.limit_san_rule == 2 ? '仅限指定散场票' : null,
                    ],
                    _checked: selectionId.some(id => id == item.coupon_id)
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Coupon/coupon_list';
        const postData = { ...this.postData, ...{ page_no: 1, page_size: this.listCount } };
        return this.$service
          .post(url, postData, { isExport: true })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.list.map(item => {
                let str = `${item.limit_type == 1 ? `消费满${item.limit_amount}元使用、` : ''}${LIMIT_TYPE[item.limit_card - 1] || ''}${LIMIT_TYPE[item.limit_card - 1] ? '、' : ''}${item.limit_space_type == 1 ? '全部场地、' : item.limit_space_type == 2 ? '仅限指定场地类型、' : ''}${item.limit_san_rule == 1 ? '全部散场票' : item.limit_san_rule == 2 ? '仅限指定散场票' : ''}`
                return {
                  ...item,
                  ...{
                    usage: ` ${item.use_num}/${item.issue_num}`,
                    discount_amount: `￥${item.discount_amount}`,
                    validDate: `${item.start_time}~${item.end_time}`,
                    limits: str
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },

      handleSwitchChange({ _index: index, status, coupon_id }) {
        const newStatus = status == '1' ? '2' : '1'
        this.tableData[index].status = newStatus
        this.$service.post('/Web/Coupon/set_coupon_status', {
            status: newStatus,
            coupon_ids: coupon_id
          })
          .then(res => {
            if (res.data.errorcode !== 0) {
              this.tableData[index].status = status
              this.$Message.error(res.data.errormsg)
            } else {
              this.$Message.success(res.data.errormsg || '切换状态成功')
            }
          })
          .catch(function(error) {
            this.tableData[index].status = status
            this.$Message.error(error)
          })
      },
      handleSwitchChangeSome(status) {
        this.$service.post('/Web/Coupon/set_coupon_status', {
            status: status,
            coupon_ids: this.selectionId.join(',')
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg || '批量修改状态成功')
              this.selection = []
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg || '批量修改状态失败')
            }
          })
      },
      handleDelete(data) {
        const coupon_id = typeof data === 'string' ? data : this.selectionId.join(',')
        if (!coupon_id) return this.$Message.error('请选择要删除的折扣券');

        this.$Modal.confirm({
          title: '删除折扣券',
          content: '确定要删除折扣券吗?',
          onOk: () => {
            const url = '/Web/Coupon/del_coupon';
            this.$service
              .post(url, { coupon_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg);
                  if (typeof data !== 'string') {
                    this.selection = []
                  }
                  this.getList();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        });
      },

      async exportCsv() {
        const data = await this.getExportData();
        Array.isArray(data) && data.forEach(v => {
          v.status = v.status == '1' ? '已启用' : '禁用'
          v.use_limit = v.use_limit ? v.use_limit.split(',').map(v => useLimit[v]).join('、') : useLimit['1']
        })
        const columns = this.columns.filter(item => item.key);
        this.$refs.table.exportCsv({ columns, data, filename: `折扣券列表` });
      }
    }
  };
</script>

<style scoped>
</style>
