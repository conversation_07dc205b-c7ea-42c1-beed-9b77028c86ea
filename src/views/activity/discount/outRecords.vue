<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" placeholder="姓名/电话" />
      <busDiscount v-model="postData.coupon_id" showHistory />
      <Select v-model="postData.status" placeholder="使用状态" clearable>
        <Option value="1">未使用</Option>
        <Option value="2">已使用</Option>
        <Option value="3">已过期</Option>
      </Select>
      <Button style="width: auto" type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" :data="tableData"></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager :total="listCount" :history="false" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import busDiscount from 'components/form/busDiscount';
  import Pager from 'components/pager';

  const STATUS = ['未使用', '已使用', '已过期'];

  export default {
    name: 'outRecords',
    components: { Pager, busDiscount },
    data() {
      return {
        postData: {
          search: '',
          coupon_id: '',
          status: '',
          page_no: 1,
          page_size: 10
        },
        listCount: 0,
        tableData: [],
        columns: [
          {
            title: '发放时间',
            key: 'create_time'
          },
          {
            title: '折扣券',
            key: 'coupon_name'
          },
          {
            title: '姓名',
            key: 'user_name',
            render: (h, params) => {
              const item = params.row;
              return (
                <router-link to={{ name: '会员详情', params: { userId: item.user_id } }}>{item.user_name}</router-link>
              );
            }
          },
          {
            title: '赠送原因',
            key: 'reason',
            render: (h, params) => {
              const item = params.row;
              return <div>{item.reason}</div>;
            }
          },
          {
            title: '发放账号',
            key: 'admin_name'
          },
          {
            title: '使用状态',
            key: 'usage',
            render: (h, params) => {
              const item = params.row;
              return <div class={item.status == 2 ? 'color-success' : ''}>{item.usage}</div>;
            }
          }
        ]
      };
    },
    created() {
      this.postData.coupon_id = this.$route.params.id;
      this.getList();
    },
    methods: {
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData, ...{ coupon_id: this.postData.coupon_id } };
        this.getList();
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        const url = '/Web/Coupon/get_grant_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.listCount = data.count;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    usage: STATUS[item.status - 1]
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Coupon/get_grant_list';
        return this.$service
          .post(url, { ...this.postData, ...{ page_no: 1, page_size: this.listCount } }, { isExport: true })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.list.map(item => {
                return {
                  ...item,
                  ...{
                    usage: STATUS[item.status - 1],
                    reason: item.reason.replace(',', '，')
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const data = await this.getExportData();
        this.$refs.table.exportCsv({ columns: this.columns, data, filename: '折扣券发放记录' });
      }
    }
  };
</script>

<style lang="less" scoped>
  header > * {
    width: 150px;
  }
</style>
