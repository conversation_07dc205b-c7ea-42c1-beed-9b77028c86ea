<template>
  <Form
    ref="form"
    class="form"
    :model="form"
    :rules="formRule"
    :label-width="labelWidth">
    <FormItem prop="coupon_name" label="折扣券名称">
      <Input v-model="form.coupon_name" :disabled="isEdit" :maxlength="20" />
    </FormItem>
    <FormItem prop="discount_amount" label="折扣金额">
      <InputNumber
        v-model="form.discount_amount"
        style="width:100%;"
        :disabled="isEdit"
        :min="1" />
    </FormItem>
    <FormItem label="满减条件">
      <RadioGroup v-model="form.limit_type" :disabled="isEdit">
        <Radio :disabled="isEdit" label="99">无条件</Radio>
        <Radio :disabled="isEdit" label="1">达到指定金额可用</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem v-if="form.limit_type == 1" prop="limit_amount" label="使用条件">
      <p class="form-item-row">
        单笔合同订单消费满
        <InputNumber
          v-model="form.limit_amount"
          :disabled="isEdit"
          :min="1"
          class="input" />元可使用折扣券
      </p>
    </FormItem>
    <FormItem prop="use_limit" label="使用范围" :rules="{required: true, validator: useLimitValid, trigger: 'change'}">
      <CheckboxGroup v-model="form.use_limit_zero">
        <Checkbox label="1">购卡/购课</Checkbox>
        <Checkbox label="2">续卡/续课</Checkbox>
        <Checkbox label="3">卡课升级</Checkbox>
      </CheckboxGroup>
      <FormItem label="" class="reset-padding" v-show="form.use_limit_zero.length > 0">
        <RadioGroup v-model="form.limit_card" :disabled="isEdit" @on-change="onTypeChange">
          <Radio :disabled="isEdit || form.use_limit_zero.length == 0" label="1">除储值卡所有卡种</Radio>
          <Radio :disabled="isEdit || form.use_limit_zero.length == 0" label="2">仅限购会员卡(除储值卡)</Radio>
          <Radio :disabled="isEdit || form.use_limit_zero.length == 0" label="3">仅限购私教课</Radio>
          <Radio :disabled="isEdit || form.use_limit_zero.length == 0" label="4">指定卡种</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="form.limit_card == 4 && form.use_limit_zero.length > 0"
        prop="card_species"
        class="reset-padding"
        :rules="{ required: form.limit_card == 4, type: 'array', min: 1, message: '请选择可使用的卡种' }"
        label="指定卡种">
        <Select
          multiple
          filterable
          :disabled="isEdit"
          v-if="cardList.length"
          v-model="form.card_species"
          placeholder="选择或搜索会员卡">
          <Option
            v-for="card in cardList"
            :key="card.card_id"
            :value="card.card_id"
            :label="card.card_name"></Option>
        </Select>
      </FormItem>

      <CheckboxGroup v-model="form.use_limit_one">
        <Checkbox label="4">订场地</Checkbox>
      </CheckboxGroup>
      <FormItem label="" class="reset-padding" v-show="form.use_limit_one.length > 0">
        <RadioGroup v-model="form.limit_space_type" :disabled="isEdit" @on-change="onOneChange">
          <Radio :disabled="isEdit || form.use_limit_one.length == 0" label="1">全部场地</Radio>
          <Radio :disabled="isEdit || form.use_limit_one.length == 0" label="2">指定场地类型</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="form.limit_space_type == 2 && form.use_limit_one.length > 0"
        prop="space_type_species"
        class="reset-padding"
        :rules="{ required: form.limit_space_type == 2, type: 'array', min: 1, message: '请选择场地类型' }"
        label="指定场地类型">
        <Select
          multiple
          filterable
          :disabled="isEdit"
          v-if="bookingTypeList.length"
          v-model="form.space_type_species"
          placeholder="选择或搜索场地类型"
        >
          <Option
            v-for="item in bookingTypeList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></Option>
        </Select>
      </FormItem>

      <CheckboxGroup v-model="form.use_limit_two">
        <Checkbox label="5">购散场票(散场票折扣券只支持小程序购票使用)</Checkbox>
      </CheckboxGroup>
      <FormItem label="" class="reset-padding" v-show="form.use_limit_two.length > 0">
        <RadioGroup v-model="form.limit_san_rule" :disabled="isEdit" @on-change="onTwoChange">
          <Radio :disabled="isEdit || form.use_limit_two.length == 0" label="1">全部散场票</Radio>
          <Radio :disabled="isEdit || form.use_limit_two.length == 0" label="2">指定散场票</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="form.limit_san_rule == 2 && form.use_limit_two.length > 0"
        prop="san_rule_species"
        class="reset-padding"
        :rules="{ required: form.limit_san_rule == 2, type: 'array', min: 1, message: '请选择散场票' }"
        label="指定散场票">
        <Select
          multiple
          filterable
          :disabled="isEdit"
          v-if="sanRuleSpeciesList.length"
          v-model="form.san_rule_species"
          placeholder="选择或搜索散场票"
        >
          <Option
            v-for="item in sanRuleSpeciesList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></Option>
        </Select>
      </FormItem>
    </FormItem>
    <FormItem prop="dateRange" label="有效期">
      <DatePicker
        v-model="form.dateRange"
        style="width:100%;"
        type="daterange"
        :options="dateOptions"
        @on-change="dateChange" />
    </FormItem>
    <FormItem label="描述">
      <Input v-model="form.describe" type="textarea" />
    </FormItem>
    <FormItem class="form-bottom-buttons">
      <Button type="success" style="margin-right:72px;" @click="handleSubmit">提交</Button>
      <Button @click="handleCancel">取消</Button>
    </FormItem>
  </Form>
</template>

<script>
  export default {
    name: 'EditForm',

    props: {
      id: {
        type: [Number, String],
        default: ''
      },
      isEdit: {
        type: Boolean,
        default: false
      },
      labelWidth: {
        type: Number,
        default: 140
      }
    },
    data() {
      return {
        dateOptions: {
          disabledDate(date) {
            return date.valueOf() < Date.now() - 24 * 3600 * 1000
          },

        },
        cardList: [],
        form: {
          coupon_name: '',
          discount_amount: 0,
          limit_amount: 0,
          use_limit: [], // 使用范围 1购卡购课 2续卡续课 3卡课升级 多选逗号隔开
          limit_type: '1',
          limit_card: '',
          card_species: [],
          dateRange: [],
          start_time: '',
          end_time: '',
          describe: '',
          use_limit_zero: [],
          use_limit_one: [],
          limit_space_type: '0',
          space_type_species: [],
          use_limit_two: [],
          limit_san_rule: '0',
          san_rule_species: [],
        },
        bookingTypeList: [],
        sanRuleSpeciesList: [],
        useLimitValid: (rule, value, callback) => {
          this.$nextTick(() => {
            if (this.form.use_limit.length > 0) {
              callback()
            } else {
              callback('至少选择一个范围')
            }
          });
        },
        formRule: {
          coupon_name: { required: true, message: '请输入折扣券名称' },
          discount_amount: [
            { required: true, message: '请输入折扣金额' },
            { min: 1, type: 'number', message: '请输入折扣金额', trigger: 'change' },
            { pattern: /^\d+(\.\d{0,2})?$/, message: '最多保留两位小数' }
          ],
          limit_amount: [{ pattern: /^\d+(\.\d{0,2})?$/, message: '最多保留两位小数' }, { min: 1, type: 'number', message: '请输入金额', trigger: 'change' }],
          // use_limit: [{ required: true, type: 'array', min: 1, message: '至少选择一个范围', trigger: 'change' }],
          dateRange: {
            len: 2,
            required: true,
            type: 'array',
            message: '请选择折扣券有效期',
            fields: {
              0: { type: 'date', required: true, message: '请选择折扣券有效期' },
              1: { type: 'date', required: true, message: '请选择折扣券有效期' }
            }
          }
        }
      }
    },

    watch: {
      'form.use_limit_zero'(val) {
        let arrZero = this.form.use_limit_zero
        let arrOne = this.form.use_limit_one
        let arrTwo = this.form.use_limit_two
        let arr = arrZero.concat(arrOne,arrTwo)
        this.form.use_limit = arr
        if(val.length > 0 && this.form.limit_card == ''){
          this.form.limit_card = '1'
        }
      },
      'form.use_limit_one'(val) {
        let arrZero = this.form.use_limit_zero
        let arrOne = this.form.use_limit_one
        let arrTwo = this.form.use_limit_two
        let arr = arrZero.concat(arrOne,arrTwo)
        this.form.use_limit = arr
        if(val.length > 0 && this.form.limit_space_type == '0'){
          this.form.limit_space_type = '1'
        }
      },
      'form.use_limit_two'(val) {
        let arrZero = this.form.use_limit_zero
        let arrOne = this.form.use_limit_one
        let arrTwo = this.form.use_limit_two
        let arr = arrZero.concat(arrOne,arrTwo)
        this.form.use_limit = arr
        if(val.length > 0 && this.form.limit_san_rule == '0'){
          this.form.limit_san_rule = '1'
        }
      },
    },

    created() {
      if (this.id) {
        this.getInfo();
      }
    },

    methods: {
      getInfo() {
        const url = '/Web/Coupon/get_coupon';
        this.$service
          .post(url, { coupon_id: this.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              data.limit_card == 4 && this.getCardList();

              const {
                coupon_name,
                limit_type,
                limit_amount,
                use_limit,
                limit_card,
                start_time,
                end_time,
                describe,
                card_species,
                discount_amount,
              } = data;
              this.form = {
                coupon_name,
                limit_type,
                limit_amount: +limit_amount,
                use_limit: use_limit ? use_limit.split(',') : ['1'],
                limit_card,
                start_time,
                end_time,
                describe,
                card_species: card_species && card_species.length && card_species.split(','),
                dateRange: [new Date(start_time), new Date(end_time)],
                discount_amount: +discount_amount,
              };
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      dateChange(range) {
        this.form.start_time = range[0];
        this.form.end_time = range[1];
      },
      onTypeChange(type) {
        if (type == 4 && !this.cardList.length) {
          this.getCardList();
        }
      },
      getCardList() {
        const url = '/Web/Coupon/card_list';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.cardList = data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },

      async handleSubmit() {
        const valid = await this.$refs.form.validate();
        if (!valid) return false;
        let url = '/Web/Coupon/add_coupon';
        if (this.isEdit) {
          url = '/Web/Coupon/edit_coupon';
        }
        const { form } = this;

        let postForm = this.form;
        
        if(postForm.limit_card != '4'){
          postForm.card_species=[]
        }
        if(postForm.limit_space_type != '2'){
          postForm.space_type_species = []
        }
        if(postForm.limit_san_rule != '2'){
          postForm.san_rule_species = []
        }

        delete postForm.use_limit_zero
        delete postForm.use_limit_one
        delete postForm.use_limit_two

        const postData = {
          ...postForm,
          card_species: postForm.card_species && postForm.card_species.length ? postForm.card_species.join(',') : '',
          use_limit: form.use_limit && form.use_limit.length ? form.use_limit.sort().join() : '',
          space_type_species: postForm.space_type_species && postForm.space_type_species.length ? postForm.space_type_species.sort().join() : '',
          san_rule_species: postForm.san_rule_species && postForm.san_rule_species.length ? postForm.san_rule_species.sort().join() : '',
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success('操作成功');
              setTimeout(() => {
                this.$emit('success');
              }, 1000);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },

      handleCancel() {
        this.$emit('cancel')
      },
      
      getBookingTypeList() {
        const url = '/Web/Space/getTypes';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.bookingTypeList = data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onOneChange(type) {
        if (type == 2 && !this.bookingTypeList.length) {
          this.getBookingTypeList();
        }
      },
      getSanRuleSpeciesList() {
        const url = '/Web/SanRule/getListAll';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data || [];
              this.sanRuleSpeciesList = data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onTwoChange(type) {
        if (type == 2 && !this.sanRuleSpeciesList.length) {
          this.getSanRuleSpeciesList();
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .form-item-row {
    display: flex;
    align-items: center;
    font-size: inherit;
    .input {
      width: 100px;
      margin: 0 10px;
    }
  }
  .reset-padding /deep/ .ivu-form-item-content{
    padding-left: 40px;
  }
  .reset-padding /deep/ .ivu-form-item-error-tip{
    position: relative;
  }
</style>
