<template>
  <div class="container discount-detail">
    <header>
      <h3>{{ title }}</h3>
    </header>
    <EditForm
      :id="id"
      :isEdit="isEdit"
      @success="$router.back()"
      @cancel="$router.back()"
    />
  </div>
</template>

<script>
  import EditForm from "./editForm";

  export default {
    name: 'DiscountEdit',
    components: {
      EditForm
    },
    data() {
      return {
        title: '编辑',
        isEdit: false,
        id: ''
      };
    },
    created() {
      this.id = this.$route.params.id;
      if (!this.id) {
        this.title = '添加';
        this.$route.meta.breadText = '添加';
      } else {
        this.isEdit = true;
        this.$route.meta.breadText = '编辑';
      }
    },
    methods: {}
  };
</script>

<style lang="less">

</style>
