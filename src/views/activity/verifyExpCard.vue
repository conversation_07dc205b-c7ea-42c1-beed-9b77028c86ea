<template>
<div class="form-box">
  <div class="form-box-title">
    <h2>体验卡验证</h2>
  </div>
  <div class="form-box-con">
    <Form ref="verifyForm" class="mt30" :label-width="120">
      <Form-item label="验证码" prop="cardSn">
        <Input class="min-input" v-model="serialNum " placeholder="请输入验证码进行查询" @on-enter="verifyInfo" />
        <Button type="primary" @click="verifyInfo">查询</Button>
      </Form-item>
    </Form>
    <Form v-if="userInfo.phone" class="half-item clearfix" :label-width="120" ref="userInfo" :model="userInfo">
      <Form-item label="用户昵称：">
        {{userInfo.nickname}}
      </Form-item>
      <Form-item label="手机号：">
        {{userInfo.phone}}
      </Form-item>
      <Form-item label="用户姓名：" prop="username" :rules="{required: !!userId ? false : true, message: '请填写用户姓名'}">
        <span v-if="!!userId">{{userInfo.username}}</span>
        <Input v-model="userInfo.username" v-else />
      </Form-item>
      <Form-item label="性别：" prop="sex">
        <span v-if="!!userId">{{userInfo.sex}}</span>
        <Radio-group v-model="userInfo.sex" v-else>
          <Radio label="1">男</Radio>
          <Radio label="2">女</Radio>
        </Radio-group>
      </Form-item>
      <Form-item label="跟进会籍：" prop="marketers_id">
        <span v-if="!!userId">{{userInfo.sale_name}}</span>
        <Select v-model="userInfo.marketer_id" placeholder="请选择" clearable filterable v-else>
            <Option v-for="item in saleList" :key="item.marketers_id" :value="item.marketers_id" :label="item.sale_name"></Option>
        </Select>
      </Form-item>
      <Form-item label="获取来源：">
        <span v-if="!!userId">{{userInfo.source_name}}</span>
        <span v-else>勤鸟平台推广</span>
      </Form-item>
      <Form-item label="状态信息：">
        <span v-if="!!userId">历史用户</span>
        <span v-else>新用户</span>
      </Form-item>
      <Form-item label="健身卡：">
        {{userInfo.cu_card_name}}
      </Form-item>

    </Form>
    <Card v-if="activeInfo && activeInfo.length>0" class="bottom-box" :bordered="false" :dis-hover="true">
      <p slot="title">历史激活记录</p>
      <p v-for="item in activeInfo" :key="item.active_date"><span class="time">{{item.active_date}}</span> {{item.cu_card_name}} </p>
    </Card>
    <Form v-if="userInfo.phone" :label-width="120">
      <Form-item>
        <div class="buttons">
          <Button type="primary" @click="handleSubmit">提交</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </Form-item>
    </Form>
  </div>
</div>
</template>
<script>
import {
  getSales
} from "src/service/getData";

export default {
  name: 'verifyExpCard',
  created() {
    this.getSalesList();
  },
  data() {
    return {
      serialNum: '',
      userId: '',
      cuUserCardid: '',
      activeInfo: [],
      userInfo: {
        username: '',
        nickname: '',
        phone: '',
        sex: '1',
        marketer_id: '',
        sale_name: '',
        source_name: '',
        source_id: '',
        cu_card_name: '',
      },
      saleList: [],
    }
  },
  methods: {
    dataInit() {
      this.serialNum = '';
      this.cuUserCardid = '';
      this.activeInfo = [];
      this.userInfo.username = '';
      this.userInfo.nickname = '';
      this.userInfo.marketer_id = '';
      this.userInfo.sale_name = '';
      this.userInfo.source_name = '';
      this.userInfo.source_id = '';
      this.userInfo.phone = '';
      this.userInfo.sex = '1';
      this.userInfo.cu_card_name = '';
    },
    handleSubmit() {
      let postData = {
        user_id: this.userId,
        cu_user_card_id: this.cuUserCardid,
        username: this.userInfo.username,
        sex: this.userInfo.sex,
        marketer_id: this.userInfo.marketer_id,
      };
      if (!!this.userId) {
        postData = {
          user_id: this.userId,
          cu_user_card_id: this.cuUserCardid
        }
      }
      this.$refs.userInfo.validate((valid) => {
        if (valid) {
          this.$service.post('/Web/Active/active_c_card', postData)
            .then(res => {
              if (res.data.errorcode == 0) {
                this.dataInit();
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
        } else {
          this.$Message.error('请先正确填写数据！');
        }
      })

    },
    getSalesList() {
      getSales().then(res => {
        if (res.data.errorcode === 0) {
          this.saleList = res.data.data;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    verifyInfo() {
      if (!this.serialNum) {
        this.$Message.error('请先输入验证码!');
        return false;
      }

      this.$service.post('/Web/Active/check_serial_num', {
          serial_num: this.serialNum.replace(/\s/g, "")
        })
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.$Message.error(res.data.errormsg);
          } else {
            let userInfo = res.data.data.user_info;
            if (userInfo) {
              this.userId = userInfo.user_id;
              this.userInfo.username = userInfo.username;
              this.userInfo.nickname = userInfo.nickname;
              this.userInfo.sex = userInfo.sex ? userInfo.sex : '1';
              this.userInfo.phone = userInfo.phone;
              this.userInfo.sale_name = userInfo.sale_name;
              this.userInfo.source_name = userInfo.source_name;
              this.userInfo.cu_card_name = res.data.data.card_name;
            }
            this.activeInfo = res.data.data.active_info
            this.cuUserCardid = res.data.data.cu_user_card_id
          }
        })
        .catch(err => {
          this.$Message.error('网络异常，请稍后再试！');
        });
    }
  },
}
</script>

<style scoped lang="less">
.min-input {
    width: 260px;
}
.mt30 {
    margin-top: 30px;
}
.half-item {
    max-width: 800px;
    border: 1px solid #ddd;
    padding-top: 15px;
    border-radius: 5px;
    margin-bottom: 15px;

    .ivu-form-item {
        width: 50%;
        float: left;
    }
}

.bottom-box {
    max-width: 800px;

    .time {
        margin-right: 15px;
    }

}
</style>
