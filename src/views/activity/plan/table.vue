<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.title"
        clearable
        placeholder="计划名称"
        @on-enter="handleSearch"
      />
      <Select v-model="postData.status" clearable placeholder="计划状态">
        <!-- #12793 12930 -->
        <Option
          v-for="(value, key) of planStatusOptions"
          :key="key"
          :value="key+''"
          :label="value">
          {{ value }}
        </Option>
      </Select>
      <Button
        type="success"
        @click="handleSearch"
      >
        搜索
      </Button>
    </header>
    <Table
      ref="table"
      :columns="columns"
      :data="tableData"
      disabled-hover
    />
    <footer>
      <Button type="success" @click="handleTo('运营计划设置')">
        新增计划
      </Button>
      <!-- <Dropdown @on-click="handleDropdown" placement="top">
        <Button>其他操作
          <Icon type="md-arrow-dropdown"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem name="export">导出Excel</DropdownItem>
          <DropdownItem name="delete">批量删除</DropdownItem>
        </DropdownMenu>
      </Dropdown> -->
      <Pager
        :total="listCount"
        :post-data="postData"
        @on-change="pageChange"
      />
    </footer>
  </div>
</template>

<script>
  import Pager from 'components/pager';

  const planStatusBtnObj = {
    '0': ['数据', '编辑', '删除'],        // 未开始
    '1': ['数据', '编辑', '暂停', '终止'], // 进行中
    '2': ['数据', '编辑', '恢复', '终止'], // 已暂停
    '3': ['数据', '复制', '删除'],        // 已结束
    '4': ['数据', '复制', '删除'],        // 已终止
  }
  const planTypes = {
    '1': '自动长期计划',
    '2': '手动定制计划',
    '3': '手动定制计划',
  }

  const planStatus = {
    '0': '未开始',
    '1': '进行中',
    '2': '已暂停',
    '3': '已结束',
    '4': '已终止'
  }

  export default {
    name: 'PlanTableList',
    components: {
      Pager
    },
    data() {
      return {
        selection: '',
        planStatusOptions: planStatus,
        columns: [
          {
            title: '计划名称',
            key: 'title',
            render: (_, params) => {
              return <i-button
                title={ params.row.title }
                type="text"
                onClick={() => { this.handleTo('运营计划详情', params.row) }}>
                { params.row.title }
              </i-button>
            },
          },
          {
            title: '会员人群',
            key: 'user_group_title'
          },
          {
            title: '计划方式',
            key: 'execution_type',
            render(_, params) {
              return <span>{ planTypes[params.row.execution_type] }</span>
            },
          },
          {
            title: '计划时间',
            minWidth: 50,
            key: 'plan_time'
          },
          {
            title: '状态',
            key: 'plan_name',
            render(h, params) {
              const { status } = params.row;
              return <span class="status-text" status={ 's' + status }>{planStatus[status]}</span>
            },
          },
          {
            title: '操作',
            minWidth: 50,
            render: (h, params) => {
              const { status, execution_type } = params.row;
              // 进行中的手动计划只显示数据, 已终止的手动计划不显示复制
              let arr = null;
              if (execution_type != 1 && status == 1) {
                arr = ['数据'];
              } else if (execution_type != 1 && status == 4) {
                arr = ['数据', '删除'];
              } else {
                arr = planStatusBtnObj[status];
              }

              return <div>
                {
                  arr.map(name => (
                    <i-button
                      type="text"
                      class={ name === '删除' ? 'button-text-red' : undefined }
                      style={ arr.length !== 1 ? 'margin-right:15px' : undefined }
                      onClick={() => this.handleClickBtn(name, params.row)}>
                      { name }
                    </i-button>
                  ))
                }
              </div>;
            }
          }
        ],
        tableData: [],
        listCount: 0,
        postData: {
          title: '',
          status: null,
          page_no: 1,
          page_size: 10
        }
      }
    },
    methods: {
      getList() {
        const params = {
          ...this.postData
        }
        this.$service.post('/Web/MemberGroup/cronList', params).then(res => {
          if (res.data.errorcode === 0) {
            this.listCount = res.data.data.count;
            this.tableData = res.data.data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      putPlanStatus({ url, planData }) {
        this.$service.post(url, { id: planData.id }).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.getList()
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      handleSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange(postData) {
        this.postData = {...this.postData, ...postData};
        this.getList();
      },
      handleTo(name, planData) {
        this.$router.push({
          name,
          params: {
            ...planData
          }
        })
      },
      handleConfirmDelete(data) {
        this.$Modal.confirm({
          title: '删除计划',
          content: '确定要删除计划吗？',
          onOk: () => { this.putPlanStatus(data) }
        });
      },
      handleClickBtn(typeName, planData) {
        switch (typeName) {
          case '数据':
            this.handleTo('运营计划数据', planData)
            break;
          case '复制':
             this.handleTo('运营计划设置', { isCopy: true, ...planData })
            break;
          case '编辑':
            this.handleTo('运营计划设置', planData)
            break;
          case '删除':
            this.handleConfirmDelete({
              url: '/Web/MemberGroup/cronDelete',
              planData
            })
            break;
          case '暂停':
            this.putPlanStatus({
              url: '/Web/MemberGroup/cronPause',
              planData
            })
            break;
          case '恢复':
            this.putPlanStatus({
              url: '/Web/MemberGroup/cronRegain',
              planData
            })
            break;
          case '终止':
            this.putPlanStatus({
              url: '/Web/MemberGroup/cronTerminate',
              planData
            })
            break;
        }
      },
      handleDropdown(name) {
        const events = {
          export: () => {
            // this.exportCsv();
          },
          delete: () => {
            // this.handleDelete(this.selection);
          }
        };
        events[name]();
      },
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-wrapper {
    /deep/.status-text {
      display: inline-block;
      padding: 4px 6px;
      line-height: 1;
      text-align: center;
      border-radius: 4px;
      border-width: 1px;
      border-style: solid;
      &[status = s0] {
        color: rgb(144, 147, 153);
        background-color: rgb(244, 244, 245);
        border: 1px solid  rgb(233, 233, 235);
        // color: rgb(128, 128, 128);
        // background-color: rgb(232, 234, 236);
        // border-color: rgb(197, 200, 206);
      }
      &[status = s1] {
        color: rgb(64, 158, 255);
        background-color: rgb(236, 245, 255);
        border-color: rgb(217, 236, 255);
      }
      &[status = s2] {
        color: rgb(103, 194, 58);
        background-color: rgb(240, 249, 235);
        border-color: rgb(225, 243, 216);
      }
      &[status = s3] {
        color: rgb(230, 162, 60);
        background-color: rgb(253, 246, 236);
        border-color: rgb(250, 236, 216);
      }
      &[status = s4] {
        color: 	rgb(245,108,108);
        background-color: rgb(254,240,240);
        border: 1px solid rgb(253,226,226);
        border-radius: 5px;
      }
    }
  }
</style>
