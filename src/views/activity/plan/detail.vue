<template>
  <div class="container">
    <header>
      <h3>计划详情</h3>
    </header>
    <div>
      <Form
        class="form"
        :model="detail"
        :label-width="140"
      >
        <FormItem label="计划名称">
          <p class="detail-content">{{ detail.name }}</p>
        </FormItem>
        <FormItem label="计划人群">
          <p class="detail-content">{{ detail.groupTitle }}</p>
        </FormItem>
        <FormItem label="计划方式">
          <p class="detail-content">{{ detail.typeName }}</p>
        </FormItem>
        <FormItem label="计划执行时间">
          <p class="detail-content">{{ detail.time }}</p>
        </FormItem>
        <FormItem label="赠送权益">
          <Card
            v-if="detail.hasPrize"
            class="bonus-card bonus-card-table"
            title="权益礼包"
            dis-hover
          >
            <Table
              :columns="columns"
              :data="tableData"
              disabled-hover
            />
          </Card>
          <p v-else class="detail-content">未添加</p>
        </FormItem>
        <FormItem label="通知配置">
          <Card
            v-if="detail.hasSms"
            title="短信内容"
            dis-hover
          >
            <div slot="extra" style="line-height:20px;"> 每天{{ detail.msgSendTime }}开始发送</div>
            <p>{{ detail.content }}</p>
          </Card>
          <p v-else class="detail-content">未配置</p>
        </FormItem>
        <!-- <FormItem v-if="detail.hasSms" label="通知时间">
          每天{{ detail.msgSendTime }}开始发送
        </FormItem> -->
      </Form>
    </div>
  </div>
</template>

<script>
  const PRIZE_ACTIVE = { 1: '赠送激活', 2: '到店激活' };

  export default {
    name: 'PlanDetail',
    data() {
      return {
        planId: '',
        memberGroupList: [],
        sign: '勤鸟运动',
        detail: {
          name: '',
          groupId: '', // 计划人群id
          groupTitle: '',
          typeName: '',
          time: '',
          hasPrize: false,
          hasSms: false,
          content: '',
          msgSendTime: '', // HH:mm
        },
        tableData: [],
        columns: [
          {
            type: 'index',
            width: 60,
            align: 'center'
          },
          {
            title: '权益类型',
            key: 'type_name',
          },
          {
            title: '权益明细',
            key: 'definite_name',
          },
          {
            title: '激活方式',
            render: (h, params) => {
              return  ['experience_card', 'experience_course'].includes(params.row.type) ? <div>{ PRIZE_ACTIVE[params.row.active] }</div> : '';
            },
          },
          {
            title: '权益数量',
            key: 'payout_num',
          },
        ],
      }
    },

    created() {
      const { params } = this.$route;
      if (params.id) {
       this.planId = params.id;
       this.getPlanData()
      // this.getMemberGroupList()
      // this.getMsgSign()
      } else {
        this.$router.push({ name: '运营计划' })
      }
    },

    methods: {
      // 获取会员分群列表
      /* getMemberGroupList() {
        return this.$service
          .post('/Web/MemberGroup/list', {
            page_no: 1,
            page_size: 999,
            type: 0
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.memberGroupList = res.data.data.list;
              // this.total = parseInt(res.data.data.count)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }, */
      // 获取短信签名
      /* getMsgSign() {
        this.$service.post('/MsgCenter/Msg/getSign').then(res => {
          if (res.data.errorcode === 0) {
            this.sign = res.data.data.title;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      }, */
      getPlanData() {
        this.$service.post('Web/MemberGroup/cronInfo', { id: this.planId }).then(res => {
          if (res.data.errorcode === 0) {
            const {
              title,
              execution_type,
              assign_time,
              begin_date,
              end_date,
              // status,
              // type,
              user_group_title,
              gift_task,
              gift_params,
              sms_task,
              sms_content,
              sms_time,
            } = res.data.data;
            const { detail } = this;

            detail.name = title;
            detail.groupTitle = user_group_title;

            // 处理计划类型 时间
            const types = {
              '1': '自动长期计划',
              '2': '手动定制计划 - 立即执行',
              '3': '手动定制计划 - 定时执行',
            }
            detail.typeName = types[execution_type];
            detail.time = execution_type == '1' ? `${begin_date} - ${end_date}` : assign_time

            // 处理权益
            if (gift_task == '1') {
              detail.hasPrize = true;
              const obj = {
                'experience_card': { name: '体验卡', unit: '张' },
                'experience_course': { name: '体验课', unit: '节' },
                'coupon': { name: '折扣券', unit: '张' },
              }
              this.tableData = gift_params.map(v => ({
                id: v.id,
                type: v.type,
                type_name: obj[v.type].name,
                // definite: v.type_id,
                definite_name: v.name,
                // payout_num: +v.count + obj[v.type].unit,
                payout_num: +v.count + '张',
                ...['experience_card', 'experience_course'].includes(v.type) ? { active: v.active || 2 } : {} // 如果是体验卡/课，加入激活方式参数, 以前的默认为2到店激活
              }))
            }

            // 处理短信
            if (sms_task == '1') {
              detail.hasSms = true;
              detail.content = sms_content;
              detail.msgSendTime = sms_time;
            }

          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
/deep/.detail-content {
  line-height: 34px;
}

.bonus-card-table {
  .ivu-card-extra {
    line-height: 1.5;
  }
  .ivu-card-body {
    padding: 0;
  }
}
.bonus-card {
  // margin: 0 0 24px 50px;
}
</style>
