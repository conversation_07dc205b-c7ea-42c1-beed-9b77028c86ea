<template>
  <div>
    <div class="plan-data-container form-box">
      <div class="plan-info-wrapper form-box-title">
        <h1 class="text_overflow" :title="planData.title">{{ planData.title }}</h1>
        <span class="status" :status="'s' + planData.status">{{ planStatus[planData.status] }}</span>
        <p>{{ planData.typeText }}</p>
      </div>
      <div class="plan-data-body">
        <div class="filter-wrapper">
          <Form
            ref="filterFormRef"
            :model="form"
            :rules="formRule"
            :label-width="120"
            label-position="left">
            <FormItem prop="dateRange" label="通知时间筛选">
              <Date-picker
                v-model="form.dateRange"
                placeholder="选择通知时间范围"
                type="daterange"
                :editable="false"
                :clearable="false"
                format="yyyy-MM-dd"
                placement="bottom-start"
                @on-change="handleDateChange"
              />
            </FormItem>
          </Form>
        </div>
        <div class="flex-wrapper">
          <Card
            class="card-box summarized"
            dis-hover>
            <p slot="title">数据汇总</p>
            <div class="summarized-content">
              <div
                v-for="(v, i) in summarizedList"
                :key="i"
                class="data-box"
              >
                <div class="text">{{ v.value }}</div>
                <div class="label">
                  {{ v.label }}
                  <Tooltip v-if="v.tips" max-width="200" :content="v.tips">
                    <Icon type="ios-help-circle" style="color: #f4a627" />
                  </Tooltip>
                </div>
                <!-- <div class="ratio">{{  }}%</div> -->
              </div>
            </div>
          </Card>
          <Card
            class="card-box conversion"
            dis-hover>
            <p slot="title">转化效果</p>
            <div class="conversion-content">
              <div
                v-for="(v, i) in conversionList"
                :key="i"
                class="chat-box">
                <div class="left">
                  <div class="text">{{ v.value }}</div>
                  <div class="label">
                    {{ v.label }}
                    <Tooltip v-if="v.tips" max-width="200" :content="v.tips">
                      <Icon type="ios-help-circle" style="color: #f4a627" />
                    </Tooltip>
                  </div>
                </div>
                <div class="right">
                  <div class="chat">
                    <img class="icon-font" :src="v.icon">
                    <div>{{ v.chatText }}</div>
                  </div>
                </div>
              </div>
              <div
                v-for="v in conversionList"
                :key="v.label"
                class="line-box">
                <div class="percent-box">
                  <p>转化率</p>
                  <p>{{ v.percent }}</p>
                </div>
                <i class="arrow" />
              </div>
            </div>
          </Card>
        </div>
        <Card
          class="card-box"
          dis-hover>
          <p slot="title">数据详情</p>
          <Table
            ref="table"
            :columns="columns"
            :data="tableData"
            stripe
            disabled-hover
          />
          <div class="plan-table-footer">
            <Button
              type="success"
              :disabled="tableCount == 0 || !notExport"
              @click="exportData">
              导出Excel
            </Button>
            <Pager
              :total="tableCount"
              :post-data="form"
              :history="false"
              @on-change="handlePageChange"
            />
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script>
  // import Total from 'components/form/Total';
  import Pager from 'components/pager';
  import { formatDate } from 'utils'

  const planTypes = {
    '1': '自动长期计划',
    '2': '手动定制计划',
    '3': '手动定制计划',
  }

  export default {
    name: 'OperationPlanDataPage',
    components: {
      // Total,
      Pager
    },

    data() {
      return {
        planId: '',
        planData: {
          title: '运营计划',
          status: '0',
          typeText: '',
        },
        planStatus: {
          '0': '未开始',
          '1': '进行中',
          '2': '已暂停',
          '3': '已结束',
          '4': '已终止'
        },

        form: {
          dateRange: [
            new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
            new Date()
          ],
          page_no: 1,
          page_size: 10
        },
        formRule: {},
        summarizedList: [ // 数据汇总
          {
            label: '7日内支付金额 (元)',
            value: '0',
            tips: '筛选时间内已通知客户，被通知/发券后7日内在门店下单付款金额。',
          },
          {
            label: '7日内支付人数',
            value: '0',
            tips: '筛选时间内已通知，被通知后7日内在店铺下单付款的会员数，跨天去重。',
          },
          {
            label: '7日内合同数量',
            value: '0',
            tips: '筛选时间内已通知券客户，被通知/发券后7日内在门店下单付款的合同数。',
          },
          {
            label: '通知人数',
            value: '0',
            tips: '统计时间内，满足运营条件的人数，不考虑发通知、发体验卡课、发券的损耗，跨天去重。',
          },
          {
            label: '7日内到店',
            value: '0',
            tips: '筛选时间内已通知客户，被通知后7日内进入门店签到/上私教/上团课的会员数，跨天去重。',
          },
          {
            label: '7日内使用',
            value: '0',
            tips: '筛选时间内已通知客户，被通知后7日内使用卡/券，跨天去重。',
          },
        ],
        conversionList: [ // 转化效果
          {
            label: '通知人数',
            value: '0',
            percent: '0%', // 用于外层 通知->支付 转化率
            tips: '统计时间内，满足运营条件的人数，不考虑发通知、发体验卡课、发券的损耗，跨天去重。',
            chatText: '通知',
            icon: require('../../../assets/img/plan-icon-message.svg')
          },
          {
            label: '7日内到店',
            value: '0',
            percent: '0%', // 用于内层 通知->到店 转化率
            tips: '筛选时间内已通知客户，被通知后7日内进入门店签到/上私教/上团课的会员数，跨天去重。',
            chatText: '到店',
            icon: require('../../../assets/img/plan-icon-people.svg')
          },
          {
            label: '7日内支付人数',
            value: '0',
            percent: '0%', // 用于内层 到店->支付 转化率
            tips: '筛选时间内已通知，被通知后7日内在店铺下单付款的会员数，跨天去重。',
            chatText: '支付',
            icon: require('../../../assets/img/plan-icon-RMB.svg')
          },
        ],
        // 数据详情
        tableData: [],
        tableCount: 0,
        columns: [
          {
            title: '日期',
            key: 'create_time'
          },
          {
            title: '通知人数',
            key: 'user_ids'
          },
          {
            title: '7日内到店数',
            key: 'sign_user_ids'
          },
          {
            title: '7日内支付人数',
            key: 'pay_user_ids'
          },
          {
            title: '7日内支付合同数',
            key: 'pay_contracts_count'
          },
          {
            title: '7日内支付金额',
            key: 'pay_amount'
          },
          {
            title: '通知-7日到店转化率',
            key: 'sign_user_cvr'
          },
          {
            title: '7日到店-7日支付转化率',
            key: 'sign_pay_cvr'
          },
          {
            title: '通知-7日支付转化率',
            key: 'user_pay_cvr'
          },
        ],
        notExport: true,
      }
    },

    created() {
      const { params } = this.$route;
      if (params.id) {
        const { _index, _rowKey, ...rest } = params;
        this.planId = params.id;
        this.planData = { ...rest };
        this.planData.typeText = `${planTypes[params.execution_type]}  ${params.plan_time}`;

        this.getData()
      } else {
        this.$router.push({ name: '运营计划' })
      }
    },

    methods: {
      getData() {
        const { dateRange, ...rest } = this.form
        const params = {
          begin_date: dateRange[0] ? formatDate(dateRange[0], "yyyy-MM-dd") : '',
          end_date: dateRange[1] ? formatDate(dateRange[1], "yyyy-MM-dd") : '',
          cr_op_id: this.planId,
          ...rest
        }
        this.$service.post('/Web/MemberGroup/OperationDataList', params).then(res => {
          if (res.data.errorcode === 0) {
            const { list: resData } = res.data.data
            if (resData && !Array.isArray(resData)) {
              const {
                su_amount_num, // 汇总支付金额
                su_pay_num, // 汇总支付人数
                su_contract_num, // 汇总合同数量
                su_notice_num, // 汇总通知人数
                su_sign_num, // 汇总到店人数
                su_gift_num, // 汇总使用人数
                su_notice_pay_cvr, // 通知->支付 转化率
                su_notice_sign_cvr, // 通知->到店 转化率
                su_sign_pay_cvr, // 到店->支付 转化率
              } = resData.summary;

              /* 数据汇总赋值 */
              [
                su_amount_num,
                su_pay_num,
                su_contract_num,
                su_notice_num,
                su_sign_num,
                su_gift_num,
              ].forEach((num, idx) => {
                this.summarizedList[idx].value = num;
              });
              /* 转换效果赋值 */
              [
                { num: su_notice_num, cvr: su_notice_pay_cvr },
                { num: su_sign_num, cvr: su_notice_sign_cvr },
                { num: su_pay_num, cvr: su_sign_pay_cvr },
              ].forEach(({ num, cvr }, idx) => {
                this.conversionList[idx].value = num;
                this.conversionList[idx].percent = cvr + '%';
              });
              /* 数据详情列表赋值 */
              resData.lists.forEach(v => {
                v.sign_user_cvr = v.sign_user_cvr + '%';
                v.sign_pay_cvr = v.sign_pay_cvr + '%';
                v.user_pay_cvr = v.user_pay_cvr + '%';
              })
              this.tableData = resData.lists;
              this.tableCount = res.data.data.count;

            } else {
              // 清空
              this.summarizedList.forEach(v => {
                v.value = '0';
              });
              this.conversionList.forEach(v => {
                v.value = '0';
                v.percent = '0';
              });
              this.tableData = [];
              this.tableCount = 0;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      exportData() {
        this.notExport = false;
        const { dateRange, ...rest } = this.form
        const params = {
          begin_date: dateRange[0] ? formatDate(dateRange[0], "yyyy-MM-dd") : '',
          end_date: dateRange[1] ? formatDate(dateRange[1], "yyyy-MM-dd") : '',
          cr_op_id: this.planId,
          _export: 1,
          ...rest
        }
        this.$service.post('/Web/MemberGroup/OperationDataList', params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success({
              content:'导出任务运行中，请稍后到消息中心下载!',
              duration: 3
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).finally(() => {
          this.notExport = true;
        })
      },
      handleDateChange() {
        this.form.page_no = 1;
        this.form.page_size = 10;
        this.getData()
      },
      handlePageChange(data) {
        this.form = {...this.form, ...data};
        this.getData();
      },
      handleExport() {

      }
    },
  }
</script>

<style lang="less" scoped>
  .plan-data-container {
    width: 100%;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 2px;
  }

  .plan-info-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 30px;
    border-bottom: 1px solid #e8eaec;

    h1 {
      max-width: 600px;
      font-size: 15px;
      font-weight: bold;
    }
    .status {
      margin: 0 10px;
      width: 46px;
      height: 22px;
      line-height: 20px;
      text-align: center;
      white-space: nowrap;
      &[status = s0] {
        // color: rgb(144, 147, 153);
        // background-color: rgb(244, 244, 245);
        // border: 1px solid  rgb(233, 233, 235);
        color: rgb(128, 128, 128);
        background-color: rgb(232, 234, 236);
        border: 1px solid  rgb(197, 200, 206);
        border-radius: 5px;
      }
      &[status = s1] {
        color: rgb(64, 158, 255);
        background-color: rgb(236, 245, 255);
        border: 1px solid  rgb(217, 236, 255);
        border-radius: 5px;
      }
      &[status = s2] {
        color: rgb(103, 194, 58);
        background-color: rgb(240, 249, 235);
        border: 1px solid  rgb(225, 243, 216);
        border-radius: 5px;
      }
      &[status = s3] {
        color: rgb(230, 162, 60);
        background-color: rgb(253, 246, 236);
        border: 1px solid  rgb(250, 236, 216);
        border-radius: 5px;
      }
      &[status = s4] {
        color: 	rgb(245,108,108);
        background-color: rgb(254,240,240);
        border: 1px solid rgb(253,226,226);
        border-radius: 5px;
      }
    }
  }

  .plan-data-body {
    padding: 0 30px;
    .filter-wrapper {
      /deep/.ivu-date-picker-editor {
        max-width: 300px;
      }
    }
    .flex-wrapper {
      display: flex;
      flex-wrap: nowrap;
    }
    .card-box {
      margin-bottom: 30px;
      flex: 1;
      &:first-child {
        margin-right: 20px;
      }
    }

    .summarized-content {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      .data-box {
        width: 33%;
        height: 125px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        color: #52a4ea;
        .text {
          font-size: 36px;
        }
        .label {
          margin-left: 14px;
          font-size: 14px;
          color: #666666;
        }
        .ratio {
          font-size: 14px;
        }
        .orange {
          color: #ff9c27;
        }
      }
    }

    .conversion-content {
      position: relative;

      .chat-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-left: 20px;
        width: 70%;
        height: 80px;
        .left {
          .text {
            font-size: 18px;
            font-weight: bold;
            color: #ff9c27;
          }
        }
        .right {
          width: 225px;
          height: 100%;
        }

        .chat {
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
          height: 100%;
          text-align: center;
          color: #ffffff;
          .icon-font {
            width: 30px;
            height: 30px;
          }
          &::before {
            position: absolute;
            top: 0;
            left: -25px;
            content: "";
            width: 0;
            height: 0;
          }
          &::after {
            position: absolute;
            top: 0;
            right: -25px;
            content: "";
            width: 0;
            height: 0;

          }
        }
        &:nth-child(1) {
          background-color: #eaf4fe;
          .chat {
            width: 225px;
            background-color: #57a9fb;
            &::before {
              border-top: 80px solid #57a9fb;
              border-left: 25px solid transparent;
            }
            &::after {
              border-bottom: 80px solid transparent;
              border-left: 25px solid #57a9fb;
            }
          }
        }
        &:nth-child(2) {
          background-color: #f2f2fa;
           .chat {
            width: 175px;
            background-color: #6c6ca7;
            .icon-font {
              margin-bottom: 4px;
              width: 25px;
              height: 25px;
            }
            &::before {
              border-top: 80px solid #6c6ca7;
              border-left: 25px solid transparent;
            }
            &::after {
              border-bottom: 80px solid #ffffff;
              border-left: 25px solid #6c6ca7;
            }
          }
        }
        &:nth-child(3) {
          background-color: #f7f2fc;
           .chat {
             width: 125px;
            background-color: #a973df;
            .icon-font {
              margin-bottom: 2px;
              width: 28px;
              height: 28px;
            }
            &::before {
              border-top: 80px solid #a973df;
              border-left: 25px solid transparent;
            }
            &::after {
              right: -50px;
              width: 50px;
              background-color: #ffffff;
              border-bottom: 80px solid #ffffff;
              border-left: 25px solid #a973df;
            }
          }
        }
      }

      .line-box {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
        &::before {
          position: absolute;
          content: "";
          background-color: #eee;
        }
        &::after {
          position: absolute;
          content: "";
          background-color: #eee;
        }
        .arrow {
          display: inline-block;
          position: absolute;
          bottom: -3px;
          width: 0;
          height: 0;
          border: 4px solid transparent;
          border-right: 16px solid #eee;
        }
        .percent-box {
          padding-left: 10px;
        }
        &:nth-child(4) {
          top: 50%;
          right: 0%;
          transform: translateY(-50%);
          width: 10%;
          height: 86%;
          border-left: 2px solid #eee;
          &::before {
            top: 0;
            right: 100%;
            width: 150%;
            height: 2px;
          }
          &::after {
            bottom: 0;
            right: 100%;
            width: 220%;
            height: 2px;
          }
          .arrow {
            left: -235%;
          }
        }
        &:nth-child(5) {
          top: 50%;
          right: 10%;
          transform: translateY(-105%);
          width: 10%;
          height: 37%;
          border-left: 2px solid #eee;
          &::before {
            top: 0;
            right: 100%;
            width: 55%;
            height: 2px;
          }
          &::after {
            bottom: 0;
            right: 100%;
            width: 75%;
            height: 2px;
          }
          .arrow {
            left: -90%;
          }
        }
        &:nth-child(6) {
          bottom: 50%;
          right: 10%;
          transform: translateY(105%);
          width: 10%;
          height: 37%;
          border-left: 2px solid #eee;
          &::before {
            top: 0;
            right: 100%;
            width: 85%;
            height: 2px;
          }
          &::after {
            bottom: 0;
            right: 100%;
            width: 110%;
            height: 2px;
          }
          .arrow {
            left: -125%;
          }
        }
      }

    }
    .plan-table-footer {
      display: flex;
      align-items: center;
      height: 80px;
      padding: 0 35px;

      .ivu-page {
        margin-left: auto;
        /deep/.ivu-select {
          width: 100%;
        }
      }
    }

    @media screen and (max-width: 1600px) {
      .flex-wrapper {
        flex-wrap: wrap;
        .card-box {
          margin-right: 0;
          flex: none;
          width: 100%;
        }
      }
      .summarized-content {
        flex-wrap: nowrap;
        .data-box {
          width: auto;
          flex: 1;
        }
      }
    }
  }

</style>
