<template>
  <div class="container">
    <header>
      <h3>{{ isCopy ? '复制计划' : planId ? '编辑计划' : '新增计划' }}</h3>
    </header>
    <Form
      ref="formRef"
      class="form"
      :model="form"
      :rules="formRule"
      :label-width="140"
    >
    <div class="SMSNumber-box">
      <FormItem label="短信条数">
          <SMSNumber ref="smsNumberRef"/>
      </FormItem>
    </div>
    
      <FormItem prop="name" label="计划名称">
        <Input
          v-model="form.name"
          :disabled="isDisabled"
          :maxlength="30"
        />
      </FormItem>
      
      <FormItem
        label="计划人群"
        prop="groupId">
        <Select
          v-model="form.groupId"
          :disabled="isDisabled"
          filterable
          placeholder="选择计划覆盖人群"
          @on-change="handleChangeGroup">
          <Option
            v-for="item in memberGroupList"
            :key="item.id"
            :value="item.id"
            :label="item.title" />
        </Select>
      </FormItem>
      <FormItem label="计划方式" prop="type" required>
        <RadioGroup
          v-model="form.type"
          @on-change="handleChangeRadio"
        >
          <Radio label="1" :disabled="isDisabled">
            自动长期计划
          </Radio>
          <Radio label="0" :disabled="isDisabled">
            手动定制计划
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        label="计划执行时间"
        :prop="form.type=='1' ? 'autoDateRange' : form.type=='0' && form.manualType == '3' ? 'timedDate' : 'manualType'"
        required>
        <div v-show="form.type=='1'">
          <Date-picker
            v-model="form.autoDateRange"
            placeholder="选择计划执行时间范围"
            type="daterange"
            :disabled="isDisabled"
            :editable="false"
            :clearable="false"
            format="yyyy-MM-dd"
            @on-change="handleDateChange"
          />
          <Alert v-if="count !== null" type="warning" style="margin-top:16px;">
            人群中如有满足条件的新增客户，新增的客户会自动收到营销通知。长期运营计划剩余可创建{{ count }}条
          </Alert>
        </div>
        <div v-show="form.type=='0'" class="excute-radio-box">
          <RadioGroup
            v-model="form.manualType"
            vertical
            @on-change="handleChangeRadio"
          >
            <Radio label="2" :disabled="isDisabled">
              立即执行
            </Radio>
            <Radio
              label="3"
              :disabled="isDisabled"
              style="margin-top: 8px;"
            >
              定时执行
            </Radio>
          </RadioGroup>
          <DatePicker
            v-model="form.timedDate"
            type="datetime"
            :disabled="form.manualType!=='3' || isDisabled"
            :editable="false"
            :clearable="false"
            :options="datePickerOptions"
            placement="top"
            format="yyyy-MM-dd HH:mm"
            @on-change="handleTimeChange"
          />
        </div>
      </FormItem>
      <FormItem label="赠送权益" prop="hasPrize">
        <Checkbox v-model="form.hasPrize" @on-change="handleChangeCheckbox($event, 'hasPrize')">
          发放体验卡、折扣券
        </Checkbox>
        <Card
          v-show="form.hasPrize"
          class="bonus-card bonus-card-table"
          dis-hover
        >
          <p slot="title">
            权益礼包
          </p>
          <Button slot="extra" type="text" @click.prevent="showSetPrize = true">
            <!-- <Icon type="plus-circled" /> -->
            添加权益
          </Button>
          <Table
            ref="table"
            :columns="columns"
            :data="tableData"
            disabled-hover
          />
        </Card>
      </FormItem>
      <!-- <FormItem label="通知配置" :prop="form.hasSms ? 'content' : undefined"> -->
      <FormItem label="通知配置" prop="hasSms">
        <Checkbox v-model="form.hasSms" @on-change="handleChangeCheckbox($event, 'hasSms')">
          短信通知
        </Checkbox>
        <div v-show="form.hasSms">
          <p>短信签名【{{ sign }}】</p>
          <Input
            v-model="form.content"
            type="textarea"
            :class="{'word-length-error': wordLeft < 0}"
            :autosize="{minRows: 7, maxRows: 10}" />
          <div style="display: flex; justify-content: space-between; padding-top: 3px">
            <p>{{ singleWords }}个字算作1条短信，一次发送最多{{ singleWords * 4 }}个字(4条)</p>
            <p>
              签名<span style="color: red">{{ sign.length }}</span>个字，
              内容<span style="color: red">{{ form.content.length }}</span>个字，
              还可输入<span style="color: red">{{ wordLeft }}</span>个字
            </p>
          </div>
        </div>
      </FormItem>
      <FormItem
        v-if="form.hasSms"
        label="通知时间"
        prop="msgSendTime"
        required>
        <div>
          每天
          <TimePicker
            :value="form.msgSendTime"
            style="width: 120px"
            :disabled-hours="[0,1,2,3,4,5,6,19,20,21,22,23]"
            :editable="false"
            :clearable="false"
            placement="top"
            format="HH:mm"
            placeholder="请选择"
            @on-change="handleChangeSendeTime" />
          开始发送
        </div>
        <Alert type="warning" style="margin-top:16px;width: 350px;">
          为避免骚扰用户，限制通知时间范围 07:00 - 19:00
        </Alert>
      </FormItem>
      <FormItem>
        <div class="form-bottom-buttons">
          <Button type="success" @click="checkSmsNumber">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>

    <!-- 添加权益 -->
    <Modal
      v-model="showSetPrize"
      :mask-closable="false"
      title="添加权益"
    >
      <Form
        ref="prizeFromRef"
        :model="prizeFrom"
        class="modal-form"
        :label-width="80"
      >
        <Form-item
          label="权益类型"
          prop="type"
        >
          <Select
            v-model="prizeFrom.type"
            @on-change="handleChangePrizeType"
          >
            <Option :value="1">
              体验卡
            </Option>
            <Option :value="2">
              体验课
            </Option>
            <Option :value="3">
              折扣券
            </Option>
          </Select>
        </Form-item>
        <Form-item
          :label="prizeTypes[prizeFrom.type - 1]"
          prop="definite"
          :rules="{required: true, message: `请选择${prizeTypes[prizeFrom.type - 1]}`}"
        >
          <ExpCardSelect
            v-show="prizeFrom.type == 1"
            :value="prizeFrom.definite"
            :clearable="false"
            @on-change="handleSelectPrize($event, 2)"
          />
          <ExpClassSelect
            v-show="prizeFrom.type == 2"
            :value="prizeFrom.definite"
            :clearable="false"
            @on-change="handleSelectPrize($event, 2)"
          />
          <BusDiscount
            v-show="prizeFrom.type == 3"
            :value="prizeFrom.definite"
            :clearable="false"
            @on-change="handleSelectPrize($event, 1)"
          />
        </Form-item>
        <Form-item
          label="发放张数"
          prop="payout_num"
          :rules="{required: true, type: 'number', message: '请填写每人发放数量', trigger: 'change'}"
        >
          <Input-number
            v-model="prizeFrom.payout_num"
            style="width:100%"
            :max="9999999"
            :min="1"
            :step="1"
            :precision="0"
            :active-change="false"
            placeholder="请填写每人发放数量"
          />
        </Form-item>
        <Form-item v-show="[1, 2].includes(+prizeFrom.type)" label="激活方式" prop="active">
          <RadioGroup v-model="prizeFrom.active">
            <Radio :label="1">赠送激活</Radio>
            <Radio :label="2">到店激活</Radio>
          </RadioGroup>
        </Form-item>
      </Form>
      <div
        slot="footer"
        class="modal-buttons"
      >
        <Button
          type="success"
          @click="handleSavePrize"
        >
          保存
        </Button>
        <Button @click="showSetPrize = false">
          取消
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import BusDiscount from 'components/form/busDiscount';
  import SMSNumber from '../SMS/smsNumber';
  import ExpClassSelect from 'components/form/ExpClassSelect';
  import ExpCardSelect from 'components/form/expCardSelect';
  import { formatDate } from 'utils'

  const PRIZE_TYPE = ['体验卡', '体验课', '折扣券'];
  // const PRIZE_UNIT = { 1: '张',2: '节',  3: '张' };
  const PRIZE_ACTIVE = { 1: '赠送激活', 2: '到店激活' };

  export default {
    name: 'PlanEdit',
    components: {
      ExpCardSelect,
      ExpClassSelect,
      BusDiscount,
      SMSNumber,
    },
    data() {
      return {
        planId: '',
        isCopy: false,
        memberGroupList: [],
        count: null,
        singleWords: 56,
        sign: '勤鸟运动',
        form: {
          name: '',
          groupId: '', // 计划人群id
          type: '1',
          manualType: '2',
          autoDateRange: [], // 自动长期计划时间范围 yyyy-MM-dd
          timedDate: new Date(Date.now() + 3 * 60 * 1000), // 定时计划执行时间 yyyy-MM-dd HH:mm
          hasPrize: false,
          hasSms: false,
          content: '',
          msgSendTime: '' // HH:mm
        },
        formRule: {
          name: { required: true, message: '计划名称不能为空', trigger: 'blur' },
          groupId: { // 计划人群
            validator: (rule, value, callback) => {
              if (value) {
                callback();
              } else {
                callback(new Error('计划人群不能为空'));
              }
            },
            trigger: 'change'
          },
          autoDateRange: { // 长期计划时间范围
            validator: (rule, arr, callback) => {
              if (Array.isArray(arr) && arr[0] && arr[1]) {
                callback();
              } else {
                callback(new Error('计划时间范围不能为空'));
              }
            },
            trigger: 'change'
          },
          timedDate: { // 定时执行时间
            validator: (rule, value, callback) => {
              if (value) {
                callback();
              } else {
                callback(new Error('定时执行时间不能为空'));
              }
            },
            trigger: 'change'
          },
          hasPrize: {
            validator: (rule, value, callback) => {
              if (value && !this.tableData.length) {
                callback(new Error('未添加权益礼包'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          },
          hasSms: {
            validator: (rule, value, callback) => {
              if (value && !this.form.content.trim()) {
                callback(new Error('短信内容不能为空'))
              } else if (value && this.wordLeft < 0) {
                callback(new Error('短信内容超过最大的字数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          },
          msgSendTime: { required: true, message: '通知时间不能为空', trigger: 'change' },
        },
        datePickerOptions: {
          disabledDate (date) {
            // const isToday = new Date(new Date().toLocaleDateString()).getTime() === date.valueOf()
            // const isBefore = date && date.valueOf() < Date.now()
            // isToday 用于放开当天日期的禁用
            // console.log(
            //   isToday ? false : isBefore,
            //   isBefore,
            //   formatDate(date, "HH:mm:ss"),
            //   formatDate(Date.now(), "HH:mm:ss"),
            // );
            // return isToday ? false : isBefore
            /* 组件BUG ？ */
            return date && date.valueOf() < Date.now() - 24 * 60 * 60 * 1000;
          }
        },
        /* 添加权益相关 */
        showSetPrize: false,
        prizeTypes: [...PRIZE_TYPE],
        prizeFrom: {
          type: 1,
          definite: '',
          definite_name: '',
          payout_num: null,
          active: 1 // 激活方式 1 赠送激活 2 到店激活，体验卡/课才传
        },
        tableData: [],
        columns: [
          {
            type: 'index',
            width: 60,
            align: 'center'
          },
          {
            title: '权益类型',
            render: (h, params) => {
              const item = params.row;
              return <div>{PRIZE_TYPE[item.type - 1]}</div>;
            }
          },
          {
            title: '权益明细',
            key: 'definite_name',
          },
          {
            title: '权益数量',
            render: (h, params) => <div>{ params.row.payout_num }张</div>,
          },
          {
            title: '激活方式',
            render: (h, params) => {
              return  [1, 2].includes(+params.row.type) ? <div>{ PRIZE_ACTIVE[params.row.active] }</div> : '';
            },
          },
          {
            title: '操作',
            render: (h, param) => (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={() => {
                    this.tableData.splice(param.index, 1)
                    this.handleValidate('hasPrize', 'blur')
                  }}
                >
                  取消权益
                </i-button>
              </div>
            )
          }
        ],

      }
    },
    computed: {
      isDisabled() {
        return this.isCopy ? false : !!this.planId;
      },
      wordLeft() {
        return this.singleWords * 4 - this.form.content.length;
      }
    },

    created() {
      const { params } = this.$route;
      if (params.id) {
       this.planId = params.id;
       this.isCopy = params.isCopy;
       this.getPlanData()
      }
      this.getMemberGroupList()
      this.getMsgSign()
      this.getCronLongLeft()
    },
    methods: {
      // 获取会员分群列表
      getMemberGroupList() {
        return this.$service
          .post('/Web/MemberGroup/list', {
            page_no: 1,
            page_size: 999,
            type: 0
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.memberGroupList = res.data.data.list;
              // this.total = parseInt(res.data.data.count)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      // 获取短信签名
      getMsgSign() {
        this.$service.post('/MsgCenter/Msg/getSign').then(res => {
          if (res.data.errorcode === 0) {
            this.sign = res.data.data.title;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      getCronLongLeft() {
        this.$service.get('/Web/MemberGroup/cronLongLeft').then(res => {
          if (res.data.errorcode === 0) {
            const { count } = res.data.data;
            count && (this.count = res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      getPlanData() {
        this.$service.post('Web/MemberGroup/cronInfo', { id: this.planId }).then(res => {
          if (res.data.errorcode === 0) {
            const {
              title,
              execution_type,
              assign_time,
              begin_date,
              end_date,
              // status,
              // type,
              extension,
            } = res.data.data;
            const { form } = this;

            form.name = title;
            // 处理计划类型
            if (execution_type == '1') {
              // form.type = '1';
              form.autoDateRange = [
                new Date(begin_date),
                new Date(end_date),
              ];
            } else {
              form.type = '0';
              if (execution_type == '3') {
                form.manualType = '3'
                form.timedDate = new Date(assign_time)
              }
            }

            const {
              gift_task,
              gift_params,
              sms_task,
              sms_content,
              sms_time,
              user_group_id,
              // user_group_title,
            } = extension || res.data.data;

            form.groupId = user_group_id
            // 处理权益
            if (gift_task == '1') {
              form.hasPrize = true;
              const keys = {
                'experience_card': '1',
                'experience_course': '2',
                'coupon': '3',
              }
              this.tableData = gift_params.map(v => ({
                id: v.id,
                type: keys[v.type],
                definite: v.type_id,
                definite_name: v.name,
                payout_num: +v.count || null,
                ...['experience_card', 'experience_course'].includes(v.type) ? { active: v.active } : {} // 如果是体验卡/课，加入激活方式参数
              }))
            } else {
              form.hasPrize = false;
            }
            // 处理短信
            if (sms_task == '1') {
              form.hasSms = true;
              form.content = sms_content;
              form.msgSendTime = sms_time;
            }else {
              form.hasSms = false;
            }


          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      handleResetField(prop) {
        const field = this.$refs.formRef.fields.filter(field => field.prop === prop)[0];
        // console.log(field);
        field && field.resetField()
      },
      handleValidate(prop, trigger = 'change') {
        const field = this.$refs.formRef.fields.filter(field => field.prop === prop)[0];
        // console.log(field);
        field && field.validate(trigger)
      },
      handleChangeGroup(val) { },
      /* 更换计划类型 */
      handleChangeRadio(val) {
        if (val == '0') {
          this.handleResetField('autoDateRange')
        }
        // this.handleResetField('timedDate')
      },
      /* 更改计划执行时间 */
      handleDateChange(val) {
        // val[0] && this.handleResetField('autoDateRange')
        this.handleValidate('autoDateRange')
      },
      /* 更改执行定时 */
      handleTimeChange(date) {
        // 处理选择当天日期时，时分秒低于当前时间的问题
        if(new Date(date).getTime() < Date.now()) {
          this.form.timedDate = new Date(Date.now() + 3 * 60 * 1000)
        }
      },
      /* 更改权益、通知勾选状态 */
      handleChangeCheckbox(val, type /* hasPrize, hasSms */) {
        !val && this.handleValidate(type, 'blur')
      },
      handleChangeSendeTime(val) {
        if (val) {
          const time = val.split(':');
          [0, 1, 2, 3, 4, 5, 6, 19, 20, 21, 22, 23].includes(+time[0]) && (time[0] = '08');
          this.form.msgSendTime = time.join(':');
        }
        this.handleValidate('msgSendTime')
      },
      handleChangePrizeType() {
        this.prizeFrom.definite = '';
        this.prizeFrom.definite_name = '';
        this.prizeFrom.payout_num = null;
        this.prizeFrom.active = 1;

        const field = this.$refs.prizeFromRef.fields.filter(field => field.prop === 'definite')[0];
        // console.log(field);
        field && field.resetField()
      },
      handleSelectPrize(item, type/* 1 折扣券, 2 体验卡 体验课 */) {
        switch (type) {
          case 1:
            this.prizeFrom.definite = item.value;
            this.prizeFrom.definite_name = item.label;
            break;
          case 2:
            this.prizeFrom.definite = item.card_id;
            this.prizeFrom.definite_name = item.card_name;
            break;
        }
      },
      /* 保存权益 */
      handleSavePrize() {
        this.$refs.prizeFromRef.validate(val => {
          if (val) {
            const item = { ...this.prizeFrom };
            ![1, 2].includes(+item.type) && delete item.active;
            this.tableData.push(item);

            this.$nextTick(() => {
              this.showSetPrize = false;
              this.$refs.prizeFromRef.resetFields()
              this.handleValidate('hasPrize', 'blur')
            });
          }
        });
      },
      checkSmsNumber() {
        //如果没有添加短信通知，直接保存计划
        if(!this.form.hasSms){
          this.handleSubmit();
          return;
        }
        this.$refs.formRef.validate(val=>{
          if(val){
            const url = '/MsgCenter/Msg/mathAndCheckSmsSum';
            const postData = {
              ...{
                user_group_id: this.form.groupId,
                sms_content: this.form.content,
                title: this.form.name
              }
            };
            this.$service.post(url, postData).then(res => {
              if(res.data.data){
                const {
                  res: content, // 广告词内容
                  consume_sms,  // 消耗量
                  surplus_sms   // 场馆剩余短信量
                } = res.data.data;
                const warning_sms = Array.isArray(content) ? content.join() : content;

                if (res.data.errorcode === 49012) {        // 检测ok，保存
                  this.messageNumberChecked(consume_sms);
                } else if (res.data.errorcode === 49010) { // 短信条数不足
                  this.pleaseRecharge(consume_sms - surplus_sms);
                } else if (res.data.errorcode === 49022) { // 广告词
                  this.hasAbandonWord(warning_sms, consume_sms);
                } else if (res.data.errorcode === 49025) { // 敏感词
                  this.hasAbandonWord(warning_sms, null, true);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err)
              this.$Message.error(res.data.errormsg)
            });
          }
        });
        
        
      },
      pleaseRecharge(missNumber) {
        this.$Modal.confirm({
          title: '发送确认',
          render: () => {
            return (
              <div style="display: flex; align-items: center; padding: 20px 0 0">
                <icon type="ios-help-circle" color="#ff9900" size="36" />
                <p style="padding-left: 10px; font-size: 14px">
                  短信条数不足，差<span style="color: red">{missNumber}</span>条请充值
                </p>
              </div>
            );
          },
          okText: '充值',
          onOk: () => {
            const { smsNumberRef } = this.$refs;
            if(smsNumberRef) smsNumberRef.showBuySms = true;
          }
        });
      },
      // step 2.5 处理短信内容问题
      hasAbandonWord(words, number, isErr) {
        const errTip = isErr ? '以下词为敏感词，可能被运营商拦截导致短信发送失败：' : '以下内容被定义为广告，可能被运营商拦截导致短信发送失败：'
        const con = (
          <div style="display: flex; align-items: center; padding: 20px 0 0">
            <icon type="alert" color="#ff9900" size="36" />
            <p style="padding-left: 20px; font-size: 14px">
              {errTip}
              <span style="color: red">{words}</span>，请修改
            </p>
          </div>
        )
        if (isErr) {
          this.$Modal.error({
            title: '内容错误',
            render: () => con,
            okText: '返回修改'
          });
        } else {

          const my_modal = this.$Modal.confirm({
            title: '内容错误',
            render: () => con,
            okText: '返回修改',
            cancelText: '仍要发送',
            onCancel: () => {
              setTimeout(() => {
                this.messageNumberChecked(number);
              }, 800)
            },
          });
        }
      },
      // step 3 发送确认
      messageNumberChecked(number) {
        this.$Modal.confirm({
          title: '发送确认',
          render: () => (
            <div style="display: flex; align-items: center; padding: 20px 0 0">
              <icon type="ios-help-circle" color="#ff9900" size="36" />
              <p style="padding-left: 10px; font-size: 14px">
                本次发送将消耗<span style="color: red">{number}</span>条短信，是否发送？
              </p>
            </div>
          ),
          okText: '发送',
          onOk: () => {
            this.handleSubmit();
          }
        });
      },
      /* 提交保存计划 */
      handleSubmit() {
        /* 长期计划最早开始时间为明天 */
        this.$refs.formRef.validate(val => {
          if (val) {
            try {
              const { count, form, tableData, memberGroupList } = this;

              /* if (!this.form.hasPrize && !this.form.hasSms) {
                return this.$Message.warning('赠送权益和短信通知需至少选择一项')
              } */
              if (form.type == '1' && count !== null && count <= 0) {
                return this.$Message.warning(`长期运营计划剩余可创建0条`)
              }

              const params = {
                title: form.name,
                user_group_id: form.groupId,
                user_group_title: memberGroupList.find(v => form.groupId == v.id).title,
                execution_type: form.type == '1' ? '1' : form.manualType,
                gift_task: form.hasPrize ? '1' : '0',
                sms_task: form.hasSms ? '1' : '0',
              };

              // 处理计划类型
              if (params.execution_type == '1') {
                params.begin_date = formatDate(form.autoDateRange[0], "yyyy-MM-dd");
                params.end_date = formatDate(form.autoDateRange[1], "yyyy-MM-dd");
              } else if (params.execution_type == '3') {
                params.assign_time = formatDate(form.timedDate, "yyyy-MM-dd HH:mm");
              }

              // 处理权益
              if (params.gift_task == '1') {
                const keys = {
                  '1': 'experience_card',
                  '2': 'experience_course',
                  '3': 'coupon',
                }
                params.gift_params = tableData.map((v, i) => ({
                  id: i,
                  type: keys[v.type],
                  type_id: v.definite,
                  name: v.definite_name,
                  count: v.payout_num,
                  ...[1, 2].includes(+v.type) ? { active: v.active } : {} // 如果是体验卡/课，加入激活方式参数
                }))
              }

              // 处理短信
              if (params.sms_task == '1') {
                params.sms_content = form.content;
                params.sms_time = form.msgSendTime;
              }

              let url = '';
              if (!this.isCopy && this.planId) {
                url = '/Web/MemberGroup/cronUpdate'
                params.id = this.planId;
              } else {
                url = '/Web/MemberGroup/cronCreate'
              }

              this.$service.post(url, params).then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg);
                  this.$router.back()
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
            } catch (error) {
              console.log(error);
            }
          }
        });
      }
    },
  }
</script>

<style lang="less" scoped>
/deep/.ivu-select-input {
  font-size: 14px;
}


.excute-radio-box {
  position: relative;

  /deep/.ivu-date-picker {
    position: absolute;
    bottom: 0;
    left: 90px;
    width: 220px;
    .ivu-date-picker-editor {
      width: 220px;
    }
    .ivu-btn-text {
      line-height: 22px;
    }
    .ivu-btn-primary {
      padding: 3px 7px;;
    }
    .ivu-btn-default {
      display: none;
    }
  }
}

.bonus-card-table {
  .ivu-card-extra {
    line-height: 1.5;
  }
  .ivu-card-body {
    padding: 0;
  }
}
.bonus-card {
  // margin: 0 0 24px 50px;
}

.word-length-error {
  .ivu-input {
    border-color: red;
    box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.1);
  }
}

.SMSNumber-box{
  display:none;
}
</style>
