<template>
  <div>
    <div class="mask-position" v-show="show && activeIndex == 0"><span>请联系售后或商务人员咨询功能开通相关事宜</span></div>
    <div v-show="$route.name === '消息推送'" class="tab-table-wrap customized-tabs">
      <Tabs :value="activeIndex" @on-click="clickTabs">
        <TabPane label="微信公众号消息通知" name="0">
          <WxMsgNotice v-if="activated.includes('0')" class="table-wrap" @setShow="setShow" />
        </TabPane>
        <TabPane label="短信通知" name="1">
          <MsgNotice v-if="activated.includes('1')" class="table-wrap" />
        </TabPane>
        <TabPane label="短信群发" name="2">
          <MsgSend v-if="activated.includes('2')" class="table-wrap" @update-statistic="handleRefresh" />
        </TabPane>
        <TabPane label="短信统计" name="3">
          <MsgStatisic v-if="activated.includes('3')" class="table-wrap" />
        </TabPane>
      </Tabs>
    </div>
    <router-view />
  </div>
</template>

<script>
import MsgSend from './msgSend'
import MsgNotice from './msgNotice'
import tabsMixins from 'mixins/tabs'
import MsgStatisic from './msgStatisic'
import WxMsgNotice from './wxMsgNotice'

export default {
  name: 'MessagePage',
  components: {
    MsgNotice,
    MsgSend,
    MsgStatisic,
    WxMsgNotice,
  },

  mixins: [tabsMixins],
  data() {
    return {
      activeIndex: '0',
      activated: ['0'],
      // 无权限显示提示文案开关
      show: false
    }
  },

  methods: {
    // 无权限显示提示文案的触发方法
    setShow(bool) {
      this.show = bool;
    },
    handleRefresh(val) {
      const idx = this.activated.indexOf(val)
      idx !== -1 && this.activated.splice(idx, 1)
    }
  },
}
</script>

<style lang="less" scoped>
/* .tab-table-wrap {
  height: 100%;
  .ivu-tabs {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    /deep/.ivu-tabs-content {
      flex: 1;
    }
  }
} */

.table-wrap {
  padding: 20px 28px;
  // min-height: 100%;
}

.mask-position{
  pointer-events: none;
  width: 100%;
  height: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.5);
  position: sticky;
  z-index: 99;
  top: 400px;
}
</style>
