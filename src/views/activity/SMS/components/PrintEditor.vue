<template>
  <div class="quill-editor" :style="{width: a4W+'px'}">
    <!-- <Upload id="uploader" :action="uploadUrl" :data="{savePath: './Uploads/'}" :default-file-list="defaultList" :show-upload-list="false" :format="['jpg','jpeg','png']" ref="upload" :before-upload="handleBeforeUpload" :on-success="handleSuccess" :max-size="1024" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize" style="width: 0; height: 0" multiple /> -->
    <div id="printToolbar" class="print-toolbar ql-snow" ref="printToolbar" v-show="!disabled">
      <!-- <span>
        <select class="ql-font">
          <option selected="selected"></option>
          <option value="monospace"></option>
          <option value="serif"></option>
        </select>
      </span>
      <span class="ql-formats">
        <select class="ql-size">
          <option v-for="(item, index) in sizeArr" :value="item" :key="index">{{item}}</option>
        </select>
      </span>
      <span class="ql-formats">
        <button class="ql-bold"></button>
        <button class="ql-italic"></button>
        <button class="ql-underline"></button>
        <button class="ql-strike"></button>
      </span>
      <span class="ql-formats">
        <span class="ql-table ql-picker" :class="ishowTable?'ql-expanded':''" @click="tableHandler">
          <span class="ql-picker-label">
        </span>
        <span class="ql-picker-options">
          <span class="ql-picker-item" data-value="newtable_1_1"></span><span class="ql-picker-item" data-value="newtable_1_2"></span><span class="ql-picker-item" data-value="newtable_1_3"></span><span class="ql-picker-item"
            data-value="newtable_1_4"></span><span class="ql-picker-item" data-value="newtable_1_5"></span><span class="ql-picker-item" data-value="newtable_2_1"></span><span class="ql-picker-item" data-value="newtable_2_2"></span><span class="ql-picker-item"
            data-value="newtable_2_3"></span><span class="ql-picker-item" data-value="newtable_2_4"></span><span class="ql-picker-item" data-value="newtable_2_5"></span><span class="ql-picker-item" data-value="newtable_3_1"></span><span class="ql-picker-item"
            data-value="newtable_3_2"></span><span class="ql-picker-item" data-value="newtable_3_3"></span><span class="ql-picker-item" data-value="newtable_3_4"></span><span class="ql-picker-item" data-value="newtable_3_5"></span><span class="ql-picker-item"
            data-value="newtable_4_1"></span><span class="ql-picker-item" data-value="newtable_4_2"></span><span class="ql-picker-item" data-value="newtable_4_3"></span><span class="ql-picker-item" data-value="newtable_4_4"></span><span class="ql-picker-item"
            data-value="newtable_4_5"></span><span class="ql-picker-item" data-value="newtable_5_1"></span><span class="ql-picker-item" data-value="newtable_5_2"></span><span class="ql-picker-item" data-value="newtable_5_3"></span><span class="ql-picker-item"
            data-value="newtable_5_4"></span><span class="ql-picker-item" data-value="newtable_5_5"></span><span class="ql-picker-item" data-value="newtable_6_1"></span><span class="ql-picker-item" data-value="newtable_6_2"></span><span class="ql-picker-item"
            data-value="newtable_6_3"></span><span class="ql-picker-item" data-value="newtable_6_4"></span><span class="ql-picker-item" data-value="newtable_6_5"></span><span class="ql-picker-item" data-value="newtable_7_1"></span><span class="ql-picker-item"
            data-value="newtable_7_2"></span><span class="ql-picker-item" data-value="newtable_7_3"></span><span class="ql-picker-item" data-value="newtable_7_4"></span><span class="ql-picker-item" data-value="newtable_7_5"></span><span class="ql-picker-item"
            data-value="newtable_8_1"></span><span class="ql-picker-item" data-value="newtable_8_2"></span><span class="ql-picker-item" data-value="newtable_8_3"></span><span class="ql-picker-item" data-value="newtable_8_4"></span><span class="ql-picker-item"
            data-value="newtable_8_5"></span><span class="ql-picker-item" data-value="newtable_9_1"></span><span class="ql-picker-item" data-value="newtable_9_2"></span><span class="ql-picker-item" data-value="newtable_9_3"></span><span class="ql-picker-item"
            data-value="newtable_9_4"></span><span class="ql-picker-item" data-value="newtable_9_5"></span><span class="ql-picker-item" data-value="newtable_10_1"></span><span class="ql-picker-item" data-value="newtable_10_2"></span><span class="ql-picker-item"
            data-value="newtable_10_3"></span><span class="ql-picker-item" data-value="newtable_10_4"></span><span class="ql-picker-item" data-value="newtable_10_5"></span>
          </span>
        </span>
      </span>
      <span class="ql-formats">
        <select class="ql-color"></select>
        <select class="ql-background"></select>
      </span>
      <span class="ql-formats">
        <button class="ql-list" value="ordered"></button>
        <button class="ql-list" value="bullet"></button>
        <select class="ql-align">
          <option selected></option>
          <option value="center"></option>
          <option value="right"></option>
          <option value="justify"></option>
        </select>
      </span>
      <span class="ql-formats">
        <button class="ql-image"></button>
      </span> -->
      <div class="editor-tips">点击下方按钮，可插入想发送的数据元，在发送时系统将自动替换为真实数据</div>
        <div class="ql-formats" id="qlPrint" @click="printeTextHandler">
          <span class="ql-print" v-for="(item, index) in fields" :key="index">{{item}}</span>
        </div>
    </div>
    <div id="printEditor" :style="{width: a4W+'px', height: a4H+'px', 'border-top': disabled ? '1px solid #ccc': 0}" class="editor" ref="editor"></div>
  </div>
</template>

<script>
import { getBaseUrl, getNewHost } from 'utils/config'
import Quill from 'quill'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'src/styles/quill-table.less'

const AlignStyle = Quill.import('attributors/style/align')
const BackgroundStyle = Quill.import('attributors/style/background')
const ColorStyle = Quill.import('attributors/style/color')
const DirectionStyle = Quill.import('attributors/style/direction')
const SizeStyle = Quill.import('attributors/style/size')
Quill.register(AlignStyle, true)
Quill.register(BackgroundStyle, true)
Quill.register(ColorStyle, true)
Quill.register(DirectionStyle, true)
Quill.register(SizeStyle, true)
let Embed = Quill.import('blots/embed')
class ImageBlot extends Embed {
  static create(value) {
    let node = super.create()
    node.setAttribute('alt', value.alt)
    node.setAttribute('src', value.url)
    node.setAttribute('width', value.width)
    node.setAttribute('height', value.height)
    return node
  }
  static value(node) {
    return {
      alt:
        node.getAttribute('alt') !== 'undefined'
          ? node.getAttribute('alt')
          : '',
      width:
        node.getAttribute('width') !== 'undefined'
          ? node.getAttribute('width')
          : '',
      height: node.getAttribute('height') || '',
      url: node.getAttribute('src') || ''
    }
  }
}
ImageBlot.blotName = 'image'
ImageBlot.tagName = 'img'
Quill.register(ImageBlot)
// quill-table-breaks.js
let Container = Quill.import('blots/container')
let Scroll = Quill.import('blots/scroll')
let Inline = Quill.import('blots/inline')
let Block = Quill.import('blots/block')
let Delta = Quill.import('delta')
let Parchment = Quill.import('parchment')
let BlockEmbed = Quill.import('blots/block/embed')
let TextBlot = Quill.import('blots/text')
class ContainBlot extends Container {
  static create(value) {
    let tagName = 'contain'
    let node = super.create(tagName)
    return node
  }
  insertBefore(blot, ref) {
    if (blot.statics.blotName == this.statics.blotName) {
      super.insertBefore(blot.children.head, ref)
    } else {
      super.insertBefore(blot, ref)
    }
  }
  static formats(domNode) {
    if (!domNode) {
      return  null
    }
    return domNode.tagName
  }
  formats() {
    // We don't inherit from FormatBlot
    return {
      [this.statics.blotName]: this.statics.formats(this.domNode)
    }
  }
  replace(target) {
    if (target.statics.blotName !== this.statics.blotName) {
      let item = Parchment.create(this.statics.defaultChild)
      target.moveChildren(item)
      this.appendChild(item)
    }
    if (target.parent == null) return
    super.replace(target)
  }
}
ContainBlot.blotName = 'contain'
ContainBlot.tagName = 'contain'
ContainBlot.scope = Parchment.Scope.BLOCK_BLOT
ContainBlot.defaultChild = 'block'
ContainBlot.allowedChildren = [Block, BlockEmbed, Container]
Quill.register(ContainBlot)
class TableRow extends Container {
  static create(value) {
    let tagName = 'tr'
    let node = super.create(tagName)
    return node
  }
  optimize() {
    super.optimize()
    var parent = this.parent
    if (parent != null && parent.statics.blotName != 'table') {
      this.processTable()
    }
  }
  processTable() {
    var currentBlot = this
    var rows = []
    while (currentBlot) {
      if (!(currentBlot instanceof TableRow)) {
        break
      }
      rows.push(currentBlot)
      currentBlot = currentBlot.next
    }
    let mark = Parchment.create('block')
    this.parent.insertBefore(mark, this.next)
    let table = Parchment.create('table')
    rows.forEach(function(row) {
      table.appendChild(row)
    })
    table.replace(mark)
  }
}
TableRow.blotName = 'tr'
TableRow.tagName = 'tr'
TableRow.scope = Parchment.Scope.BLOCK_BLOT
TableRow.defaultChild = 'td'
Quill.register(TableRow)
class Table extends Container {
  optimize() {
    super.optimize()
    let next = this.next
    if (
      next != null &&
      next.prev === this &&
      next.statics.blotName === this.statics.blotName &&
      next.domNode.tagName === this.domNode.tagName
    ) {
      next.moveChildren(this)
      next.remove()
    }
  }
}
Table.blotName = 'table'
Table.tagName = 'table'
Table.scope = Parchment.Scope.BLOCK_BLOT
Table.defaultChild = 'tr'
Table.allowedChildren = [TableRow]
Quill.register(Table)
//
//
// CONTAINER TD
//
class TableCell extends ContainBlot {
  format() {
    return 'td'
  }
  optimize() {
    super.optimize()
    let parent = this.parent
    if (parent != null && parent.statics.blotName != 'tr') {
      this.processTR()
    }
    // merge same TD id
    let next = this.next
    if (
      next != null &&
      next.prev === this &&
      next.statics.blotName === this.statics.blotName &&
      next.domNode.tagName === this.domNode.tagName
    ) {
      next.moveChildren(this)
      next.remove()
    }
  }
  processTR() {
    // find next row break
    var currentBlot = this
    var rowItems = [this]
    while (currentBlot) {
      if (currentBlot.statics && (currentBlot.statics.tagName !== 'TD')) {
        break
      }
      rowItems.push(currentBlot)
      if (currentBlot instanceof RowBreak) {
        break
      }
      currentBlot = currentBlot.next
    }
    // create row, add row items as TDs
    var prevItem
    var cellItems = []
    var cells = []
    rowItems.forEach(function(rowItem) {
      cellItems.push(rowItem)
      if (rowItem instanceof TableCell) {
        prevItem = rowItem
      } else if (rowItem instanceof CellBreak) {
        cells.push(cellItems)
        cellItems = []
      }
    })
    if (cellItems.length > 0) {
      cells.push(cellItems)
    }
    let mark = Parchment.create('block')
    this.parent.insertBefore(mark, this.next)
    // create row
    var row = Parchment.create('tr')
    cells.forEach(function(cell) {
      // add row elements
      cell.forEach(function(cellItem) {
        row.appendChild(cellItem)
      })
    })
    row.replace(mark)
  }
}
TableCell.blotName = 'td'
TableCell.tagName = 'td'
TableCell.scope = Parchment.Scope.BLOCK_BLOT
TableCell.defaultChild = 'block'
TableCell.allowedChildren = [Block, BlockEmbed, Container]
Quill.register(TableCell)
Container.order = [
  'list',
  'contain', // Must be lower
  'td',
  'tr',
  'table' // Must be higher
]
class RowBreak extends BlockEmbed {
  formats() {
    return {
      trbr: true
    }
  }
}
RowBreak.blotName = 'trbr'
RowBreak.tagName = 'td'
RowBreak.className = 'trbr'
Quill.register(RowBreak)
class CellBreak extends BlockEmbed {
  formats() {
    return {
      tdbr: true
    }
  }
}
CellBreak.blotName = 'tdbr'
CellBreak.tagName = 'td'
CellBreak.className = 'tdbr'
Quill.register(CellBreak)
export default {
  name: 'PrintEditor',
  data() {
    return {
      _options: {},
      _content: '',
      quill: '',
      uploadUrl: getBaseUrl() + '/Admin/Public/upload',
      defaultList: [],
      imgUrl: '',
      visible: false,
      uploadList: [],
      addImgRange: [],
      mmToPx: 3.7795275590551185,
      ppi: 96, //web windows pc分辨率一般96像素/英寸
      a4W: 560,
      a4H: 200,
      ishowTable: false,
      size: [12, 16, 18, 22, 26, 32] //px
    }
  },
  computed: {
    sizeArr() {
      let div = document.createElement('div')
      div.style.width = '1in'
      let body = document.getElementsByTagName('body')[0]
      body.appendChild(div)
      let ppi = document.defaultView
        .getComputedStyle(div, null)
        .getPropertyValue('width')
      body.removeChild(div)
      ppi = parseFloat(ppi)
      let mmToPx = ppi / 25.4
      return this.size.map(item => {
        return Math.round(parseFloat(item) / parseFloat(mmToPx)) + 'mm'
      })
    }
  },
  props: {
    value: String,
    fields: {
      type: Array,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.initialize()
    // 微信公众号消息通知不需要设置为A4纸大小
    // this.setA4Size()
  },
  beforeDestroy() {
    this.quill = null
    delete this.quill
  },
  methods: {
    findImgPos() {
      let imgNode = document.querySelectorAll('#printEditor img')

      let posObj = {
        userPosi: {
          x: '',
          y: '',
          pageNo: ''
        },
        busPosi: {
          x: '',
          y: '',
          pageNo: ''
        }
      }
      imgNode = [].slice.call(imgNode);
      imgNode.forEach(item => {
        let imgBlot = Quill.find(item)
        if (item.src.indexOf('user-signature.png') !== -1 && imgBlot) {
          let posInfo = this.quill.getBounds(this.quill.getIndex(imgBlot))
          let pageNo = Math.ceil(posInfo.top / this.a4H)
          let x = posInfo.left % this.a4W
          let y = posInfo.top % this.a4H
          posObj.userPosi.x = x / this.a4W
          posObj.userPosi.y = y / this.a4H
          posObj.userPosi.pageNo = pageNo
        } else if (item.src.indexOf('bus-signature.png') !== -1 && imgBlot) {
          let busPosInfo = this.quill.getBounds(this.quill.getIndex(imgBlot))
          let pageNo = Math.ceil(busPosInfo.top / this.a4H)
          let x = busPosInfo.left % this.a4W
          let y = busPosInfo.top % this.a4H
          posObj.busPosi.x = x / this.a4W
          posObj.busPosi.y = y / this.a4H
          posObj.busPosi.pageNo = pageNo
        }
      })
      this.$emit('pos-update', posObj)
      return posObj
    },
    //像素/英寸
    getPPI() {
      // create an empty element
      let div = document.createElement('div')
      div.style.width = '1in'
      let body = document.getElementsByTagName('body')[0]
      body.appendChild(div)
      let ppi = document.defaultView
        .getComputedStyle(div, null)
        .getPropertyValue('width')
      body.removeChild(div)
      ppi = parseFloat(ppi)
      this.ppi = ppi
      let mmToPx = ppi / 25.4
      this.mmToPx = mmToPx
      return ppi
    },
    setA4Size() {
      //A4纸 高 宽 210*297mm
      let a4W = 210
      let a4H = 297
      let ppi = this.getPPI()
      this.a4W = Math.round(a4W * this.mmToPx)
      this.a4H = Math.round(a4H * this.mmToPx)
    },
    setFontToMM(fontSize) {},
    initialize() {
      SizeStyle.whitelist = this.sizeArr
      Quill.register(SizeStyle, true)
      this.quill = new Quill('#printEditor', {
        modules: {
          toolbar: {
            container: '#printToolbar',
            handlers: {
              image: this.imageHandler,
              table: this.tableHandler
            }
          }
        },
        theme: 'snow'
      })
      this.quill.enable(false)
      if (this.value || this.content) {
        this.quill.pasteHTML(this.value || this.content)
      }

      this.quill.on('text-change', (delta, oldDelta, source) => {
        let obj = this.quill.getContents()
        if(obj.ops[0] && obj.ops[0].insert && obj.ops[0].insert.length > 101){
          this.quill.pasteHTML(oldDelta.ops[0].insert)
        } else {
          let html = this.$refs.editor.children[0].innerHTML
          // if (html.indexOf('id="printWrapStyle"') === -1) {
          //   html = `<div id="printWrapStyle" class="ql-editor" style="width:210mm;margin:0 auto;overflow-x: hidden;box-sizing: border-box;line-height: 1.42;outline: none;tab-size: 4;-moz-tab-size: 4;text-align: left;white-space: pre-wrap;word-wrap:break-word;overflow-x: hidden;overflow-y: auto;color: #000000;font-size: 3mm;position: relative;padding: 12px 15px;background-color:#fff;">${html}</div>`
          // }
          // const quill = this.quill
          // const text = this.quill.getText()
          // if (html === '<p><br></p>') {
          //   html = ''
          // }
          this._content = html
          this.$emit('input', this._content)
          // this.$emit('change', {
          //   html,
          //   text,
          //   quill
          // })
          let obj = this.quill.getContents()
          this.$emit('change', obj.ops[0].insert)
        }
      })

      // Disabled editor
      if (!this.disabled) {
        this.quill.enable(true)
      }
      this.$emit('ready', this.quill)
    },
    imageHandler(state) {
      if (state) {
        this.$refs.upload.handleClick()
      }
    },
    tableHandler(e) {
      if(this.disabled) {
        return false;
      }
      this.ishowTable = !this.ishowTable
      if (
        e.target &&
        e.target.dataset.value &&
        e.target.dataset.value.includes('newtable_')
      ) {
        let sizes = e.target.dataset.value.split('_')
        let rows = Number.parseInt(sizes[1])
        let columns = Number.parseInt(sizes[2])
        let table = Parchment.create('table')
        this.quill.focus()
        const range = this.quill.getSelection()
        if (!range) return
        const newLineIndex = this.getClosestNewLineIndex(
          this.quill.getContents(),
          range.index + range.length
        )
        let changeDelta = new Delta().retain(newLineIndex)
        changeDelta = changeDelta.insert('\n')
        for (let i = 0; i < rows; i++) {
          for (let j = 0; j < columns; j++) {
            changeDelta = changeDelta.insert('\n', {
              td: true
            })
            if (j < columns - 1) {
              changeDelta = changeDelta.insert({
                tdbr: true
              })
            }
          }
          changeDelta = changeDelta.insert({
            trbr: true
          })
        }
        this.quill.updateContents(changeDelta, Quill.sources.USER)
        this.quill.setSelection(newLineIndex + 1)
      } else {
        // TODO
      }
    },
    getClosestNewLineIndex(contents, index) {
      return (
        index +
        contents
          .map(op => {
            return typeof op.insert === 'string' ? op.insert : ' '
          })
          .join('')
          .slice(index)
          .indexOf('\n')
      )
    },
    printeTextHandler(e) {
      if (e.target && e.target.tagName === 'SPAN' && !this.disabled) {
        let innerHTML = e.target.innerHTML
        this.quill.focus()
        let selInfo = this.quill.getSelection()
        let index = selInfo ? selInfo.index : 0
        if (innerHTML === '用户签名图片' || innerHTML === '商家签名图片') {
          let url =
            innerHTML === '用户签名图片'
              ? 'user-signature.png'
              : 'bus-signature.png'
          url = 'https://imagecdn.rocketbird.cn/default/' + url
          let img = this.quill.insertEmbed(
            index,
            'image',
            {
              alt: innerHTML,
              width: 75,
              height: 50,
              url: url
            },
            Quill.sources.USER
          )
          this.quill.setSelection(index + 1)
        } else {
          this.quill.insertText(index, `\$\{${innerHTML}\}`)
        }
      }
    },
    handleSuccess(res, file) {
      const url = `${res.info}@70q_1pr`
      if (url != null && url.length > 0) {
        this.addImgRange = this.quill.getSelection()
        this.quill.insertEmbed(
          this.addImgRange != null ? this.addImgRange.index : 0,
          'image',
          {
            url: url
          },
          Quill.sources.USER
        )
      } else {
        this.$Message.error('添加图片失败')
      }
    },
    handleFormatError() {
      this.$Notice.warning({
        title: '文件格式不正确',
        desc: '文件格式不正确，请上传 jpg 或 png 格式的图片。'
      })
    },
    handleMaxSize() {
      this.$Notice.warning({
        title: '超出文件大小限制',
        desc: '文件太大，不能超过 1M。'
      })
    },
    handleBeforeUpload() {
      this.uploadList.shift()
    },
  },
  watch: {
    // Watch disabled change
    disabled(newVal, oldVal) {
      if (this.quill) {
        this.quill.enable(!newVal)
      }
    },
    // Watch content change
    content(newVal, oldVal) {
      if (this.quill) {
        if (newVal && newVal !== this._content) {
          this._content = newVal
          this.quill.pasteHTML(newVal)
        } else if (!newVal) {
          this.quill.setText('')
        }
      }
    },
    // Watch content change
    value(newVal, oldVal) {
      if (this.quill) {
        this.findImgPos()
        if (newVal && newVal !== this._content) {
          this._content = newVal
          this.quill.pasteHTML(newVal)
          this.quill.blur()
          document.getElementById('vipMainCon').scrollTop = -40
        } else if (!newVal) {
          this.quill.setText('')
        }
      }
    }
  }
}
</script>

<style lang="less">
.ql-snow .ql-picker.ql-font {
  .ql-picker-label[data-value=serif]::before,
  .ql-picker-item[data-value=serif]::before {
    content: '黑体'
  }

  .ql-picker-label[data-value=monospace]::before,
  .ql-picker-item[data-value=monospace]::before {
    content: '微软雅黑'
  }

  .ql-picker-item[data-value=serif]::before {
    font-family: SimHei
  }

  .ql-picker-item[data-value=monospace]::before {
    font-family: 'Microsoft YaHei'
  }
}

.ql-editor {
  .ql-font-serif {
    font-family: SimHei
  }

  .ql-font-monospace {
    font-family: 'Microsoft YaHei'
  }
}
</style>

<style lang='less' scoped>
.quill-editor {
  background: #fff;
}
.editor {
  height: 400px;
  white-space: pre;
  font-size: 3mm;
  // color: #495060;
  color: black;
  font-family: 'Microsoft YaHei', '微软雅黑', Helvetica, sans-serif, Arial;
}
.editor-tips {
  padding: 5px 0 10px;
}
.ql-print {
  display: inline-block;
  height: 25px;
  line-height: 25px;
  padding: 0 5px;
  border: 1px solid #d1d1d1;
  cursor: pointer;
  color: #333;
  margin: 0 0 10px 5px;
  vertical-align: middle;
}
</style>
