<template>
  <div class="msg-info-row">
    <p class="info-box">
      <span class="prefix">当前剩余 </span>
      <span class="num">{{adminInfo.sms_number}}</span> 条
    </p>
    <Button type="text" style="font-weight:bold;" @click="showBuySms=true">充值短信</Button>

    <Modal
      title="短信购买"
      width="800"
      class="buy-sms"
      v-model="showBuySms"
    >
      <BuySMS v-if="showBuySms" @paySuccess="onPaySuccess"/>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import BuySMS from 'components/onlinePay/buySMS';
  import { mapState } from 'vuex';

  export default {
    name: 'SMSNumber',
    components: {
      BuySMS
    },

    data() {
      return {
        showBuySms: false, // 控制购买短信弹窗
      }
    },
    computed: {
      ...mapState(['adminInfo']),
    },

    methods: {
       onPaySuccess() {
        this.$store.dispatch('getAdminInfo').then(() => {
          this.showBuySms = false;
        });
      },
    },
  }
</script>

<style lang="less" scoped>
.msg-info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
  .info-box {
    padding-right: 5px;
    .prefix, .num, {
      font-weight: bold;
    }
    .num {
      color:red;
    }
  }
}
</style>
