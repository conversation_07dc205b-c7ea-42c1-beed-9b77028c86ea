<template>
  <div class="msg-send-container form-box">
    <div class="form-box-con">
      <Form :model="sendObj"
            :label-width="100"
            label-position="left">
        <FormItem label="短信条数">
          <SMSNumber ref="smsNumberRef"/>
        </FormItem>
        <!-- :rules="[{required: true, message: '短信签名为必填项', trigger: 'change'}]" -->
        <FormItem label="短信签名" prop="sign">
          <div class="signature-wrap">
            <span>【{{ [0, '2'].includes(signState.approve_status) ? signState.title : signState.approve_title }}】</span>
            <FaIcon
              v-if="signState.update == 1 && signState.approve_status !== '1'"
              class="item edit-btn"
              name="edit"
              size="16"
              color="#18BF6A"
              title="编辑"
              @click.native.prevent="handleShowEditSignModal"
            />
            <img
              v-if="signState.approve_status === '1'"
              class="item"
              src="../../../assets/img/msg_signature_pending.png"
              alt="待审"
            >

            <template v-else-if="signState.approve_status==='2'">
              <img class="item" src="../../../assets/img/msg_signature_resolve.png" alt="通过">
            </template>

            <template v-else-if="signState.approve_status === '3'" >
              <img
                class="item"
                src="../../../assets/img/msg_signature_reject.png"
                alt="驳回"
              >
              <Tooltip class="item" :content="signState.approve_remake" max-width="300" placement="bottom">
                  <FaIcon name="ellipsis-h"  size="16" color="#E60012"></FaIcon>
              </Tooltip>
            </template>
            <span
              v-if="['1', '3'].includes(signState.approve_status)"
              style="font-size:14px;color:#959595;margin-right: 20px;"
            >
              原签名：【{{ signState.title }}】
            </span>
            <!-- <span class="item" style="font-size:14px;color:#959595;">小提示：30天内仅允许编辑一次</span> -->
          </div>
          <div style="font-size:14px;color:#959595;">小提示：30天内仅允许变更一次, 变更后需联系勤鸟售后提交相关资料</div>
        </FormItem>
        <!-- v-if="initLabel && userList" -->
        <FormItem label="接收对象">
          <Select
            ref="selectRef"
            v-model="sendObj.send_object"
            multiple
            :disabled="!!userId"
            :label="!userId ? initLabel : undefined"
            :loading="searchingUser"
            :remote-method="getUserList"
            filterable
            placeholder="请选择或搜索会员姓名、电话号码">
            <Option
              v-for="user in userList" :key="user.user_id"
              :value="user.user_id"
              :label="user.phone ? `${user.username}(${user.phone})` : user.username"></Option>
          </Select>
        </FormItem>
        <FormItem label="主题">
          <Input v-model="sendObj.title"
                  :maxlength="20"
                  placeholder="短信主题最多20字（默认带‘：’）" />
        </FormItem>
        <FormItem :label-width="0">
          <Input type="textarea"
                :class="{'word-length-error': wordLeft < 0}"
                v-model="sendObj.content"
                :autosize="{minRows: 7, maxRows: 10}" />
          <div style="display: flex; justify-content: space-between; padding-top: 3px">
            <p>{{ singleWords }}个字算作1条短信，一次发送最多{{ singleWords * 4 }}个字(4条)</p>
            <p>
              主题<span style="color: red">{{ sendObj.title.length ? (sendObj.title.length + 1) : 0 }}</span>个字，
              签名<span style="color: red">{{ sendObj.sign.length + 2 }}</span>个字，
              内容<span style="color: red">{{ sendObj.content.length }}</span>个字，
              还可输入<span style="color: red">{{ wordLeft }}</span>个字
            </p>
          </div>
          <div class="rule-tips">
            教育、医疗、酒类、引导加微信短信容易被运营商屏蔽...
            <router-link :to="{ name: '短信屏蔽规则' }">了解更多屏蔽规则</router-link>
          </div>
        </FormItem>
        <FormItem label="发送时间">
          <div class="send-time-radio-box">
            <RadioGroup v-model="sendObj.is_now" vertical>
              <Radio label="1">
                立即发送
              </Radio>
              <Radio label="0" style="margin-top: 8px;">
                定时发送：
              </Radio>
            </RadioGroup>
            <DatePicker
              type="datetime"
              v-model="sendObj.call_time"
              :disabled="sendObj.is_now!=='0'"
              :clearable="false"
              :editable="false"
              :options="datePickerOptions"
              placement="top"
              format="yyyy-MM-dd HH:mm"
              @on-change="handleChangeDate"
            />
          </div>
        </FormItem>
        <FormItem>
          <!-- <div style="color:red;fontWeight:bold;">短信发送功能维护中，暂无法使用，敬请谅解！</div> -->
          <div class="buttons">
            <Button type="success"
                    :disabled="wordLeft < 0 || sendObj.sign.length <= 0"
                    @click="checkSmsNumber">发送</Button>
            <Button Button @click="handleReset">重置</Button>
          </div>
        </FormItem>
      </Form>
    </div>

    <Modal
      title="签名编辑"
      width="500"
      class="buy-sms"
      v-model="showEditSign"
    >
      <Input ref="signInputRef" :value="editSign" :maxlength="8" @on-change="handleChangeSign" />
      <div style="padding-top: 10px;">
        上次编辑时间：{{ signState.update_time || '暂无编辑签名通过记录' }}
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="putSignName">提交</Button>
        <Button @click="showEditSign=false;">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import SMSNumber from './smsNumber';
  import EventBus from 'components/EventBus.js';
  import { mapState } from 'vuex';
  import { formatDate } from 'utils'

  /* 用于检测是否在发送时间范围内 */
  const getStartTimeStamp = (date = new Date()) => new Date(formatDate(date, 'yyyy-MM-dd' + ' 08:00:00')).getTime()
  const getEndTimeStamp = (date = new Date()) => new Date(formatDate(date, 'yyyy-MM-dd' + ' 21:29:59')).getTime()

  export default {
    name: 'msgSend',
    components: {
      SMSNumber,
    },

    data() {
      return {
        showEditSign: false, // 控制签名修改弹窗
        editTime: '',
        editSign: '',
        // 签名相关
        signState: {
          title: "勤鸟运动",     // 当前签名
          create_time: "",
          update_time: "",
          approve_title: "",   // 待审批签名
          approve_status: "1", // 1待审批 2审批通过 3审批驳回 0初始状态，没有审批记录
          approve_remake: "",  // 审核备注
          update: "0"          // 0 不可修改 1 可以修改
        },
        singleWords: 56,
        keyword: '',
        userId: null,
        searchingUser: false,
        selectedMembersDes: '',
        selectedUser: [],
        initLabel: [],
        userList: [],
        defaultUserList: [],
        sendObj: {
          title: '',
          send_object: [],
          content: '',
          sign: '勤鸟运动',
          from: '',
          is_now: '1', // 是否立即发送 0不立即 1立即
          call_time: new Date(Date.now() + 60 * 60 * 1000), // 延迟发送时间 string Y-m-d H:i:s, is_now 传1 就不取call_time了
        },

        datePickerOptions: {
            disabledDate (date) {
              // const time = 24 * 60 * 60 * 1000 //
              const dateValue = date.valueOf();
              const isToday = new Date(new Date().toLocaleDateString()).getTime() === dateValue; // 是否当天00:00:00
              const isBefore = dateValue < Date.now() // 是否在当前时间之前
              // const isInTime = getStartTimeStamp(date) >= dateValue || dateValue > getEndTimeStamp(date) // 是否在时间范围之内
              // isToday 用于放开当天日期的禁用
              return isToday ? false : isBefore
            }
        },
      }
    },
    computed: {
      ...mapState(['busName']),
       wordLeft() {
        return this.singleWords * 4 - (this.sendObj.title.length ? (this.sendObj.title.length + 1) : 0) - this.sendObj.content.length - (this.sendObj.sign.length  + 2); // 签名要算上括号
      }
    },

    async created() {
      const {
        params: {
          from,
          title,
          content,
          selectedUserId,
          selectedMembers,
        }
      } = this.$route;

      this.sendObj.from = from || '';
      this.sendObj.title = title || this.busName;
      this.sendObj.content = content || '';
      this.getSign();
      await this.getDefaultUserGroup();

      /* 从会员详情跳转 单个会员 */
      if (selectedUserId) {
        this.userId = selectedUserId;
        this.$nextTick(() => {
          this.$refs.selectRef.setQuery(selectedUserId);
        });
      }else if (Array.isArray(selectedMembers) && selectedMembers.length) {
        /* 处理路由传参已选会员的展示 多个会员 */
        let tips = '';
        const selectedMembersDes = {};
        const length = selectedMembers.length;
        const names = selectedMembers.map(({user_id, username}) => {
          selectedMembersDes[user_id] = username;
          return username;
        });

        this.selectedMembersDes = selectedMembersDes;

        if(length > 3) {
          const prefix = names.slice(0, 3).join('、');
          tips = `${prefix}...等${length}位用户`;
        }else {
          tips = names.join('、')
        }

        this.defaultUserList.push({
          user_id: 'selectedMembers',
          username: tips
        });
        this.userList = this.defaultUserList;
        this.sendObj.send_object = ['selectedMembers'];
        this.initLabel = [tips];

      }
    },

    beforeDestroy() {
      this.handleReset()
    },

    watch: {
      'sendObj.send_object'(val) {
        if (val.length) {
          const id = val[val.length - 1];
          const selectUser = this.userList.find(user => user.user_id === id);
          if (selectUser) {
            this.selectedUser.push(selectUser);
          }
        }
      },
    },

    methods: {
      // 获取默认用户组列表
      getDefaultUserGroup() {
        return this.$service.post('/MsgCenter/Msg/targets').then(res => {
          if (res.data.errorcode === 0) {
            this.defaultUserList = res.data.data;
            this.userList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 获取搜索关键字 用户列表
      getUserList(keyword) {
        const firstUser = `${this.userList[0].username}(${this.userList[0].phone})`
        if (!keyword || keyword === firstUser) return;

        const postData = this.userId ? { user_id: this.userId } : { keyword }
        this.$service.post('/MsgCenter/Msg/searchMember', postData).then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data;
            if (list.length) {
              if (this.userId) {
                this.userList = list;
                this.sendObj.send_object = [list[0].user_id];
                this.$refs.selectRef.visible = false;
                // window.location.replace('#/activity/reminder');
              }else {
                this.userList = [...list, ...this.defaultUserList];
              }
            }else {
              this.userList = this.userId ? [] : this.defaultUserList;
            }
          }else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(err => console.error(err));
      },
      // 获取签名数据
      getSign() {
        this.$service.post('/MsgCenter/Msg/getSign').then(res => {
          if (res.data.errorcode === 0) {
            const { data } = res.data
            this.sendObj.sign = data.title;
            this.signState = data
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 提交修改签名
      putSignName() {
        const sign_name = this.editSign;
        if(!sign_name.trim()) {
          return this.$Message.error('签名不能为空！')
        }
        if(sign_name.length < 3) {
          return this.$Message.error('签名限制3-8个字！')
        }
        if(sign_name === this.signState.title) {
          return this.$Message.error('新签名与原签名相同！')
        }
        if (/^[a-z]+$/ig.test(sign_name)) {
          return this.$Message.error('签名不能为全英文！')
        }
        this.$service.post('/Web/NewSendMessage/updateMessageSignName', { sign_name }).then(res => {
          if (res.data.errorcode === 0) {
            this.getSign()
            this.showEditSign = false
            // this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 打开编辑签名弹窗
      handleShowEditSignModal() {
        this.showEditSign = true
        this.editSign = this.sendObj.sign
      },
      // 编辑签名
      handleChangeSign(e) {
        let value = e.target.value.replace(/[^\u4e00-\u9fa5a-z0-9]/ig, '')
        this.$nextTick(() => {
          this.editSign = value;
        })
        /* const length = this.getStringLength(value)
        if(length <= 8) {
          this.editSign = value;
        }else {
          this.$refs.signInputRef.currentValue = this.editSign
        } */
      },
      // 区分字符长度
      /* getStringLength (str) {
        let len = 0, strLen = str.length;
        for (let i = 0; i < strLen; i++) {
          const flag = str.charAt(i).match(/[\u4e00-\u9fa5]/g) != null;
          flag ? len += 2 : len += 1
        }
        return len;
      }, */

      handleChangeDate(date, dateType) {
        // 处理选择当天日期时，时分秒低于当前时间的问题
        if(new Date(date).getTime() < Date.now()) {
          this.sendObj.call_time = new Date(Date.now() + 60 * 60 * 1000)
        }
      },
      // step 1 发送前检测
      checkSmsNumber() {
        if (!this.checkSend()) return;
        const url = '/MsgCenter/Msg/getInfoBeforeSend';
        const postData = {
          ...this.sendObj,
          ...{
            send_object: JSON.stringify(this.calSendObject()),
            // 恢复 不是错别字，用来通过短信敏感字检测，回复 是敏感字，但是可以发送出去
            content: `${this.sendObj.content}，拒收请恢复R`,
            call_time: formatDate(this.sendObj.call_time, "yyyy-MM-dd HH:mm:00")
          }
        };

        this.$service.post(url, postData).then(res => {
          if(res.data.data){
            const {
              res: content, // 广告词内容
              consume_sms,  // 消耗量
              surplus_sms   // 场馆剩余短信量
            } = res.data.data;
            const warning_sms = Array.isArray(content) ? content.join() : content;

            if (res.data.errorcode === 49012) {        // 检测ok，发送确认
              this.messageNumberChecked(consume_sms);
            } else if (res.data.errorcode === 49010) { // 短信条数不足
              this.pleaseRecharge(consume_sms - surplus_sms);
            } else if (res.data.errorcode === 49022) { // 广告词
              this.hasAbandonWord(warning_sms, consume_sms);
            } else if (res.data.errorcode === 49025) { // 敏感词
              this.hasAbandonWord(warning_sms, null, true);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err)
          this.$Message.error(res.data.errormsg)
        });
      },
      // step 2 检查是否有接收对象和内容
      checkSend() {
        const { is_now, call_time, send_object, content } = this.sendObj;

        if (!send_object.length) {
          this.$Message.error('请选择接收对象');
          return false;
        }
        if (!content) {
          this.$Message.error('请输入内容');
          return false;
        }
        if (is_now == 1 && (getStartTimeStamp() > Date.now() || Date.now() > getEndTimeStamp())) {
          this.$Message.error('只能在08:00-21:30期间发送!');
          return false;
        }
        if (is_now == 0 && (getStartTimeStamp(call_time) > call_time.getTime() || call_time.getTime() > getEndTimeStamp(call_time))) {
          this.$Message.error('只能在08:00-21:30期间发送!');
          return false;
        }
        return true;
      },
      // step 2.5 处理短信内容问题
      hasAbandonWord(words, number, isErr) {
        const errTip = isErr ? '以下词为敏感词，可能被运营商拦截导致短信发送失败：' : '以下内容被定义为广告，可能被运营商拦截导致短信发送失败：'
        const con = (
          <div style="display: flex; align-items: center; padding: 20px 0 0">
            <icon type="alert" color="#ff9900" size="36" />
            <p style="padding-left: 20px; font-size: 14px">
              {errTip}
              <span style="color: red">{words}</span>，请修改
            </p>
          </div>
        )
        if (isErr) {
          this.$Modal.error({
            title: '内容错误',
            render: () => con,
            okText: '返回修改'
          });
        } else {

          const my_modal = this.$Modal.confirm({
            title: '内容错误',
            render: () => con,
            okText: '返回修改',
            cancelText: '仍要发送',
            onCancel: () => {
              setTimeout(() => {
                this.messageNumberChecked(number);
              }, 800)
            },
          });
        }
      },
      // step 2.5 处理短信条数不足
      pleaseRecharge(missNumber) {
        this.$Modal.confirm({
          title: '发送确认',
          render: () => {
            return (
              <div style="display: flex; align-items: center; padding: 20px 0 0">
                <icon type="ios-help-circle" color="#ff9900" size="36" />
                <p style="padding-left: 10px; font-size: 14px">
                  短信条数不足，差<span style="color: red">{missNumber}</span>条请充值
                </p>
              </div>
            );
          },
          okText: '充值',
          onOk: () => {
            const { smsNumberRef } = this.$refs;
            if(smsNumberRef) smsNumberRef.showBuySms = true;
          }
        });
      },
      // step 3 发送确认
      messageNumberChecked(number) {
        this.$Modal.confirm({
          title: '发送确认',
          render: () => (
            <div style="display: flex; align-items: center; padding: 20px 0 0">
              <icon type="ios-help-circle" color="#ff9900" size="36" />
              <p style="padding-left: 10px; font-size: 14px">
                本次发送将消耗<span style="color: red">{number}</span>条短信，是否发送？
              </p>
            </div>
          ),
          okText: '发送',
          onOk: () => {
            this.doSend();
          }
        });
      },
      // step 4 发送
      doSend() {
        const url = '/Web/SendMessage/sendMsg';
        const postData = {
          ...this.sendObj,
          ...{
            send_object: JSON.stringify(this.calSendObject()),
            content: `${this.sendObj.content}，拒收请回复R`,
            call_time: formatDate(this.sendObj.call_time, "yyyy-MM-dd HH:mm:00")
          }
        };
        // return this.$emit('update-statistic', "2")

        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$store.dispatch('getAdminInfo');
            this.$emit('update-statistic', "3")
            this.handleReset()
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(err => {
          console.error(err)
          this.$Message.error(res.data.errormsg);
        });
      },

      calSendObject() {
        let arr = [];
        let hasSelectedMembers = false;
        this.sendObj.send_object.forEach(id => {
          const user = this.selectedUser.find(user => {
            if (id === 'selectedMembers') {
              hasSelectedMembers = true;
              return false;
            }
            return user.user_id === id;
          });
          user && arr.push(user);
        });
        let obj = {};
        arr.length > 0 &&
          arr.forEach(user => {
            obj[user.user_id] = user.username;
          });
        hasSelectedMembers && Object.assign(obj, this.selectedMembersDes);
        return obj;
      },

      handleReset() {
        const { selectRef } = this.$refs
        if (selectRef) {
          selectRef.lastQuery = '';
          selectRef.setQuery('');
          selectRef.selectedMultiple = [];
        }

        this.sendObj = {
          title: this.busName,
          send_object: [],
          content: '',
          sign: this.signState.title,
          from: '',
          is_now: '1', // 是否立即发送 0不立即 1立即
          call_time: new Date(Date.now() + 60 * 60 * 1000), // 延迟发送时间 string Y-m-d H:i:s, is_now 传1 就不取call_time了
        },

        // delSelectedMembersArr
        this.defaultUserList.forEach((user, index) => {
          if (user.user_id == 'selectedMembers') {
            this.defaultUserList.splice(index, 1);
          }
        });

        this.userList = this.defaultUserList;
        this.selectedUser = [];
        this.userId = null;
        this.initLabel = []
        this.selectedMembersDes = '';
      }

    },
  }
</script>

<style lang="less" scoped>
.form-box {
  .form-box-con {
    padding: 0;
    max-width: 800px;
  }
  .ivu-input-wrapper, .ivu-select, .ivu-checkbox-group, .form-other {
    width: 100%;
    max-width: unset;
  }
  .send-time-radio-box {
    position: relative;

    /deep/.ivu-date-picker {
      position: absolute;
      bottom: 0;
      left: 90px;
      width: 220px;
      .ivu-date-picker-editor {
        width: 220px;
      }
      .ivu-btn-text {
        line-height: 22px;
      }
      .ivu-btn-primary {
        padding: 3px 7px;;
      }
      .ivu-btn-default {
        display: none;
      }
  }
  }
}

.ivu-form-item-label {
    font-size: 14px;
    color: #333;
    padding-right: 28px;
}

.signature-wrap {
  display: flex;
  align-items: center;
  .item {
    margin-right: 10px;
    &.edit-btn {
      cursor: pointer;
    }
  }
}

.word-length-error {
  .ivu-input {
    border-color: red;
    box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.1);
  }
}

.rule-tips {
  // margin-top: 10px;
  text-align: right;
}
</style>
