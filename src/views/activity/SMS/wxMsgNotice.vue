<template>
  <div>
    <div class="wx-tips-row">
      <div class="tips">
        <span>会员需关注公众号才能接受消息</span>
        <Tooltip>
          <div slot="content" style="white-space: normal">
            <p>若商家使用第三方自己的小程序则关注自己的公众号</p>
            <p>若使用勤鸟的标准小程序请会员关注 "勤鸟运动" 公众号接收消息</p>
          </div>
          <Icon size="16" type="ios-help-circle" style="padding-left: 5px" color="#ff9933"></Icon>
        </Tooltip>
      </div>
      <div class="tips">
        <span>微信模板消息受微信监管，消息会有一定延迟性，部分情况会出现无法接收</span>
      </div>
    </div>

    <div class="notice-container">
      <div class="tabs-bar">
        <span
          class="tab-item"
          :class="{ active: tabIndex === index }"
          v-for="(item, key, index) in renderData"
          :key="key"
          @click="handleChangeTab(index)"
        >
          {{ item.title }}
        </span>
        <span class="right-wrapper">
          <span>提醒总数</span>
          <span class="num">{{ sum }}</span>
          <span>启用数量</span>
          <span class="num">{{ activeNum }}</span>
        </span>
      </div>
      <div class="notice-content">
        <div v-show="tabIndex === index" v-for="(item, key, index) in renderData" :key="key">
          <div v-for="(subItem, subKey) in item.list" :key="subKey">
            <div class="label">{{ subItem.title }}</div>
            <ul class="card-box">
              <li
                class="card-content"
                :class="{ on: target.status === '1' }"
                v-for="target in subItem.list"
                :key="target.id"
                @click="handleToDetail(target)"
              >
                <div style="display: flex">
                  <template v-if="target.status === '1'">
                    <Icon type="md-checkmark-circle" size="16" />
                    <span class="status-text">开启中</span>
                  </template>
                  <template v-else>
                    <Icon type="md-close-circle" size="16" />
                    <span class="status-text">关闭中</span>
                  </template>
                </div>
                <p class="title">{{ target.title }}</p>
                <div class="bottom">
                  <template v-if="target.status === '0'">
                    <span>去开启</span>
                    <Icon type="md-arrow-dropright" size="16" />
                  </template>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="mask" v-show="show"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'msgNotice',
  components: {},

  data() {
    return {
      tabIndex: 0,
      renderData: {},
      sum: 0, // 提醒总数
      activeNum: 0, // 启用数量
      signName: '勤鸟运动',
      scrollTop: 0,
      vipMainCon: document.getElementById('vipMainCon'),
      show: false,
    }
  },

  created() {
    this.getList()
    // 获取签名
    // this.$service.post('/MsgCenter/Msg/getSign').then(res => {
    //   if (res.data.errorcode === 0) {
    //     this.signName = res.data.data.title;
    //   } else {
    //     this.$Message.error(res.data.errormsg);
    //   }
    // })
  },

  watch: {
    '$route.name'(val, lastName) {
      if (lastName === '微信通知') {
        const { refresh } = this.$route.params
        refresh && this.getList()

        const { vipMainCon, scrollTop } = this
        if (vipMainCon && scrollTop) {
          vipMainCon.scrollTop = scrollTop
        }
      }
    },
  },

  methods: {
    getList() {
      this.$service.get('Web/PublicNumRemind/get_list').then(({ data: resData }) => {
        const { errorcode, errormsg, data } = resData
        if (errorcode === 0) {
          this.renderData = data.list

          let sum = 0 // 提醒总数
          let activeNum = 0 // 启用数量
          for (const item of Object.values(this.renderData)) {
            for (const subItem of Object.values(item.list)) {
              const { list } = subItem
              list &&
                list.forEach((v) => {
                  activeNum += +v.status
                })
              sum += list.length
            }
          }
          // 设置是否开启了模板消息通知
          this.show = data.pub_num_template_msg == 1 ? false : true
          this.$emit('setShow', data.pub_num_template_msg == 1 ? false : true)
          this.sum = sum
          this.activeNum = activeNum
        } else this.$Message.error(errormsg)
      })
    },

    handleChangeTab(idx) {
      this.tabIndex = idx
    },

    handleToDetail(target) {
      const { vipMainCon } = this
      if (vipMainCon) this.scrollTop = vipMainCon.scrollTop
      this.$router.push({
        name: '微信通知',
        query: { bus_id: target.bus_id, id: target.id },
      })
    },
  },
}
</script>

<style lang="less" scoped>
@text-color: #2c3945;
@tab-color: #2b8df2;
@notice-num-color: #e60012;
@turn-on-color: #18bf6a;
@turn-on-bg: #ebfaef;
@turn-off-color: #959595;
@turn-off-bg: #f1f3f7;

.wx-tips-row {
  .tips {
    display: flex;
    align-items: center;
    position: relative;
    span {
      font-size: 14px;
      line-height: 1.5;
      color: #515a6e;
      font-weight: bold;
    }
    /deep/ .ivu-tooltip .ivu-tooltip-rel {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.notice-container {
  margin-top: 20px;
  font-size: 14px;
  border: 1px solid #e1e3e9;
  position: relative;
  .tabs-bar {
    display: flex;
    align-items: center;
    padding-right: 20px;
    height: 46px;
    border-bottom: 1px solid #e1e3e9;
  }
}

.tabs-bar {
  color: @text-color;
  .tab-item {
    width: 100px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    &:hover {
      color: @tab-color;
    }
    &.active {
      color: @tab-color;
      border-bottom: 2px solid @tab-color;
    }
  }
  .right-wrapper {
    margin-left: auto;
    font-weight: bold;
    .num {
      margin-left: 10px;
      color: @notice-num-color;
    }
    & :nth-child(2) {
      margin-right: 26px;
    }
  }
}

.notice-content {
  padding: 18px 30px 26px;
  .label {
    margin-bottom: 16px;
    font-size: 16px;
    color: @text-color;
    &::before {
      display: inline-block;
      margin-right: 9px;
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: @tab-color;
      vertical-align: middle;
    }
  }
  .card-box {
    display: flex;
    flex-wrap: wrap;
  }
  .card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 30px;
    margin-bottom: 20px;
    padding: 12px 10px;
    width: 220px;
    height: 110px;
    color: @turn-off-color;
    background-color: @turn-off-bg;
    border-radius: 6px;
    cursor: pointer;
    transition: box-shadow, translateY, 0.2s;
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    }
    &.on {
      color: @turn-on-color;
      background-color: @turn-on-bg;
      border: 1px solid @turn-on-color;
      &:hover {
        box-shadow: 1px 3px 10px rgba(24, 191, 106, 0.15);
      }
      .status-text {
        color: #000000;
      }
    }
    .status-text {
      margin-left: 5px;
    }
    .title {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
    }
    .bottom {
      height: 20px;
      text-align: right;
      color: @turn-on-color;
    }
  }
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 98;
  width: 100%;
  height: 100%;
  user-select: none;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.5);
}
</style>
