<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{target.title}}</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formRef" class="form" :model="formData" :rules="formRules" :label-width="180" @submit.native.prevent>
        <FormItem label="开启/关闭">
          <i-switch
            v-model="formData.status"
            true-value="1"
            false-value="0"
            @on-change="handleChangeSwitch"
           />
        </FormItem>
        <FormItem label="发送对象" v-if="formData.send_to_phone">
          <Input
            style="width: 375px"
            v-model="phone"
            :disabled="isOpen"
            :maxlength="11"
            placeholder="输入手机号后，回车键添加"
            @on-keyup="handleAddPhone"
          />
          <div style="width: 375px;">
            <Tag
              v-for="(num, index) in formData.send_to_phone" :key="num"
              :name="index"
              :closable="!isOpen"
              @on-close="handleDelPhone"
            >{{ num }}</Tag>
           </div>
        </FormItem>
        <FormItem label="触发条件">
          <template v-for="(item, index) in textArray">
            <span v-if="typeof item === 'string'" :key="index">
              {{ item }}
            </span>
            <InputNumber
              v-else
              :key="index"
              v-model="formData.value[item]"
              :disabled="isOpen"
              :min="1"
              :max="999999"
              :precision="0"
            />
          </template>
            <CheckboxGroup
              v-if="formData.send_to_coach_type"
              v-model="formData.send_to_coach_type"
            >
              <!-- <Checkbox label="0">通知跟进会籍</Checkbox> -->
              <Checkbox label="1" :disabled="isOpen">通知上课教练</Checkbox>
              <Checkbox label="2" :disabled="isOpen">通知跟进教练</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="发送时间段" v-if="sms_time_remark">
          {{ sms_time_remark }}
        </FormItem>
        <FormItem label="提醒内容示例">
          <div class="example-box">
            <div class="content">
              <!-- 【签名】购卡提醒：您成功购得&lt;卡名称&gt;&lt;金额&gt;元。 -->
              {{ sms_content }}
            </div>
          </div>
        </FormItem>
        <FormItem>
          <div class="buttons">
            <Button type="success" @click="handlePutSetting">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex';

  export default {
    name: 'detail',

    data() {
      return {
        // signName: '',
        target: {}, // 短信通知项数据
        formRules: {},
        formData: {
          id: '', // 设置ID
          status: '0', // 开关状态；0：关闭；1：开启
          // value: [1, 2], // input框对应的值，数组格式
          // send_to_coach_type: [], // 教练提醒中，选择的教练类型，1：上课教练；2：跟进教练
          // send_to_phone  // 发送手机号, 数组格式
        },
        textArray: [], // 触发条件的文案内容
        phone: '',
        sms_time_remark: '', // 发送时间段
        sms_content: '',        // 短信示例内容
      }
    },
    computed: {
      ...mapState(['adminInfo']),
      isOpen() {
        return this.formData.status === '0'
      }
    },

    created() {
      // 短信通知项数据
      const { target } = this.$route.params;

      if(target && target.rule) {
        const { id, status, rule } = target
        // console.table(Object.entries(rule))
        const {
          // is_delay,           // （必返回）是否为延迟发送，0：否；1：是
          text,               // （必返回）显示的文案描述，如果有“$”字符，则需前端替换成input框
          value,              // （可能返回）input框对应的值，数组格式
          send_to_coach_type, // （可能返回）发送给教练的类型，数组格式，1：上课教练；2：跟进教练
          send_to_phone,      // （可能返回）需要发送短信的对象手机号
          sms_time_remark,    // 发送时间段
          sms_content,        // 短信示例内容
        } = rule

        const formData = {
          id,
          status,
          value: value && value.map(parseInt),
          send_to_coach_type,
          send_to_phone: Array.isArray(send_to_phone) ? [...send_to_phone] : null,
        }
        for (const [key, val] of Object.entries(formData)) {
          if(val) this.$set(this.formData, key, val)
        }
        this.sms_time_remark = sms_time_remark;
        this.sms_content = sms_content;
        /* 处理 触发条件栏的DOM结构 */
        const textArray = text.split('$')
        let i = textArray.length // 用于控制插入索引
        let idx = i - 2          // 用于控制输入框值存放的索引
        while (--i) {
          textArray.splice(i, 0, idx--); // 插入输入框
        }
        this.textArray = textArray;

      }else {
        this.$Message.error('未获取到数据！')
        this.$router.replace({ 
          name: '消息推送',
          params: {
            activeIndex: '1',
          }
        })
      }
    },

    methods: {
      handleChangeSwitch(val) {
        if(val === '1' && this.adminInfo.sms_number <= 0) {
          this.$nextTick(() => {
            this.formData.status = '0';
          })
          this.$Message.warning('短信剩余0条，暂无法启用')
        }
      },
      handleAddPhone({ key, target }) {
        const { value } = target

        if(key === 'Enter' && value) {
          const reg = /^1\d{10}$/;
          const { send_to_phone } = this.formData;

          if(!reg.test(value)) {
            this.$Message.error('请输入正确手机号！')
          }else if(send_to_phone.includes(value)) {
            this.$Message.error('请不要重复添加！')
          }else {
            send_to_phone.push(value)
            this.phone = ''
          }
        }
      },

      handleDelPhone(e, index) {
        this.formData.send_to_phone.splice(index, 1)
      },
      // 提交修改配置
      handlePutSetting() {
        const {
          send_to_coach_type,
          send_to_phone,
          ...rest
        } = this.formData;
        const params = { ...rest }
        const obj = { send_to_coach_type, send_to_phone }
        for (const key in obj) {
          const item = obj[key]
          if (item !== undefined) {
            params[key] = item
          }
        }

        this.$service.post('/Web/SmsRemindSetting/edit', params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('提交修改成功！')
            // this.$router.back()
            this.$router.replace({ name: '消息推送', params: { refresh: true, activeIndex: '1' } });

          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.form-box  {
  .ivu-input-number {
    width: 80px;
    max-width: unset;
  }

  .ivu-tag {
    height: 24px;
    .ivu-icon-ios-close {
      top: -2px;
    }
  }
}

.example-box {
  position: relative;
  width: 375px;
  height: 518px;
  background: url(../../../assets/img/msg_example_bg.png) center / contain no-repeat ;
  border: 1px solid #f4f4f4;
  border-radius: 3px;

  .content {
    position: absolute;
    top: 138px;
    left: 18px;
    padding: 10px 15px;
    width: 280px;
    min-height: 110px;
    line-height: 1.6;
    background-color: #f2f2f2;
    border-radius: 10px;
    &::before {
      content: '';
      position: absolute;
      left: -10px;
      bottom: 16px;
      border-width: 10px 10px 10px 0px;
      border-style: solid;
      border-color: transparent  #f2f2f2 transparent transparent;
    }
  }
}
</style>
