<template>
  <div class="msg-statisic-container">
    <div class="top-container">
      <div class="top-label">今日统计数</div>
      <div class="top-content">
        <div>
          <p>发送总量</p>
          <p class="num">{{ msg_number }}</p>
        </div>
        <div>
          <p>短信通知量</p>
          <p class="num">{{ notice_msg_number }}</p>
        </div>
        <div>
          <p>短信群发量</p>
          <p class="num">{{ push_msg_number }}</p>
        </div>
        <div>
          <p>剩余条数</p>
          <p class="num">{{ sms_number }}</p>
        </div>
      </div>
    </div>

    <div class="record-container">
      <div class="tabs-bar">
        <span class="tab-item" :class="{active: tabIndex===0}" @click="handleChangeTab(0)">发送记录</span>
        <span class="tab-item" :class="{active: tabIndex===1}" @click="handleChangeTab(1)">充值记录</span>
        <span class="right-search-wrapper">
          <DatePicker
            v-model="dateRange"
            type="daterange"
            placement="bottom-end"
            format="yyyy-MM-dd"
            :clearable="false"
          />
          <Button type="success" @click="getRenderList(tabIndex)">搜索</Button>
        </span>
      </div>
      <div class="record-content">
        <div
          v-if="activated.includes(index)"
          v-show="tabIndex===index"
          v-for="(item, index) in columns.length" :key="index"
        >
          <Table
            :columns="columns[index]"
            :data="tableData[index]"
            disabled-hover />
          <Page
            :total="total[index]"
            :current.sync="currentPage[index]"
            show-total
            show-sizer
            placement="top"
            @on-change="getRenderList(index)"
            @on-page-size-change="pageSizeChanged($event, index)"/>
        </div>
      </div>
    </div>

    <Modal title="通知详情"
           width="800"
           v-model="showNoticeDetail">
      <Form class="modal-form"
            :label-width="80"
            label-position="left">
        <FormItem label="接收对象">
          <div v-if="detailData.receiver">
            <Tag v-for="member in detailData.receiver.split('、')" style="cursor: auto"
                 :key="member.id">{{member}}</Tag>
          </div>
        </FormItem>
        <FormItem label="主题">
          <div type="text"
               style="font-size: 14px; padding-top: 2px">{{detailData.title}}</div>
        </FormItem>
        <div class="send-info-row">
          <span class="label">发送数量</span>
          <span class="cont">{{ detailData.sms_num }}</span>
          <span class="label">发送方式</span>
          <span class="cont">{{ ['0', '1'].includes(detailData.send_way) ? '短信群发' : '短信通知' }}</span>
        </div>
        <div style="border: 1px solid #dddee1; border-radius: 4px; padding: 10px; font-size: 14px; min-height: 200px; word-break: break-all"
             v-html="detailData.content && detailData.content.replace(/\n/g, '<br>').replace('，拒收请回复R', '')"></div>
      </Form>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import { formatDate } from 'utils'

  export default {
    name: 'msgStatisic',
    data() {
      return {
        // 0 发放记录, 1 充值记录
        tabIndex: 0,
        activated: [],
        sizer: [10, 10],
        currentPage: [1, 1],
        total: [0, 0],
        keyword: '',
        showNoticeDetail: false,
        dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
        detailData: {},
        columns: [
          // 发放记录
          [{
            title: '时间',
            key: 'send_time',
            maxWidth: 160,
          },
          {
            title: '通知对象',
            key: 'receiver',
            ellipsis: true,
            render: (h, params) => {
              return (
                <div style="overflow: hidden; text-overflow: ellipsis" title={params.row.receiver}>
                  {params.row.receiver}
                </div>
              );
            }
          },
          {
            title: '内容',
            key: 'content',
            ellipsis: true,
            align: 'center',
            render: (h, params) => {
              const content = params.row.content.replace('，拒收请回复R', '')
              return (
                <div style="width: 100%; overflow: hidden; text-overflow: ellipsis">
                  <span style="padding-right: 15px; font-weight: bold">{ params.row.title }</span>
                  <span style="color: #666" title={ content }>
                    { content }
                  </span>
                </div>
              );
            }
          },
          {
            title: '发送数量',
            key: 'sms_num',
            maxWidth: 160,
          },
          {
            title: '发送方式',
            key: 'send_way',
            maxWidth: 160,
            render: (h, params) => {
              // （0,1） 短信群发, 其余的（2，3）都是短信通知
              const cont = ['0', '1'].includes(params.row.send_way) ? '短信群发' : '短信通知'
              return <span>{ cont }</span>
            }
          },
          {
            title: '操作账号',
            key: 'sender',
            maxWidth: 160,
          },
          {
            title: '操作',
            key: 'operation',
            maxWidth: 200,
            render: (h, params) => {
              return (
                <i-button
                  type="text"
                  onClick={() => {
                    // this.getNoticeDetail(params.row.msg_id);
                    this.detailData = params.row;
                    this.showNoticeDetail = true;
                  }}>
                  详情
                </i-button>
              );
            }
          }],
          // 充值记录
          [{
            title: '时间',
            key: 'create_time'
          },
          {
            title: '购买金额',
            key: 'amount'
          },
          {
            title: '购买数量',
            key: 'sms_number'
          },
          {
            title: '操作账号',
            key: 'operator_name'
          }]
        ],
        tableData: [[], []],
        // 短信统计
        sms_number: 0, // 剩余短信条数
        push_msg_number: 0, // 短信群发数
        notice_msg_number: 0, // 短信通知数量
        msg_number: 0, // 发送总量
      }
    },

    created() {
      this.getNoticeNumber()
      const { tabIndex: idx } = this;
      this.getRenderList(idx)
      this.activated.push(idx);
    },

    methods: {
      // 获取统计数字
      getNoticeNumber() {
        this.$service.post('Web/NewSendMessage/messageStatistics').then(res => {
          if (res.data.errorcode === 0) {
            for (const [key, val] of Object.entries(res.data.data)) {
              this[key] = val;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      // 获取发送/充值记录列表
      getRenderList(index /* 0 发送, 1充值 */) {
        const url = [
          '/Web/SendMessage/getMsgList',
          'Web/consumption_record/get_recharge_record',
        ][index];
        const { keyword, dateRange, sizer, currentPage } = this;
        // type 1 短信,2 电子合同， 不传或则为空就是全部类型
        const rest = index ? { type: "1" } : { keyword }
        const pamars = {
          begin_date: formatDate(dateRange[0], 'yyyy-MM-dd'),
          end_date: formatDate(dateRange[1], 'yyyy-MM-dd'),
          page_size: sizer[index],
          page_no: currentPage[index],
          ...rest,
        };


        this.$service.post(url, pamars).then(({ data }) => {
          if(data.errorcode === 0) {
            const { count, list } = data.data;
            this.$set(this.total, index, +count)
            this.$set(this.tableData, index, list)
          }else {
            this.$Message.error(data.errormsg);
          }
        })
      },

      // 获取发放记录详情数据
      /* getNoticeDetail(msg_id) {
        this.showNoticeDetail = true;
        this.$service.post('/MsgCenter/Msg/getMsgInfo', { msg_id }).then(res => {
          if(res.data.errorcode === 0) {
            this.detailData = res.data.data;
          }else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => { console.error(err) });
      }, */

      handleChangeTab(idx) {
        this.tabIndex = idx;
        if(!this.activated.includes(idx)) {
          this.getRenderList(idx)
          this.activated.push(idx)
        }
      },

      pageSizeChanged(size, index) {
        this.currentPage[index] = 1;
        this.sizer[index] = size;
        this.getRenderList(index)
      },
    },
  }
</script>

<style lang="less" scoped>
.top-container {
  font-size: 14px;
  color: #2C3945;
  .top-content {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 12px;
    width: 575px;
    height: 80px;
    text-align: center;
    background: #EBFAEF;
    border-radius: 4px;
  }

  .num {
    margin-top: 6px;
    font-size: 16px;
    font-weight: bold;
  }
}

.record-container {
  margin-top: 20px;
  font-size: 14px;
  border: 1px solid #E1E3E9;
  .tabs-bar {
    display: flex;
    align-items: center;
    padding-right: 20px;
    height: 46px;
    border-bottom: 1px solid #E1E3E9;
  }
  .tab-item {
    width: 100px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    &:hover {
      color: #2B8DF2;
    }
    &.active {
      color: #2B8DF2;
      border-bottom: 2px solid #2B8DF2;
    }
  }

  .right-search-wrapper {
    margin-left: auto;
    .ivu-date-picker {
      margin-right: 15px;
      width: 200px;
    }
  }

  .record-content .ivu-page {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 80px;
    padding: 0 35px;
  }
}

.send-info-row {
  margin-bottom: 4px;
  text-align: right;
  font-size: 14px;
  .cont {
    margin-left: 10px;
    color: #7f7f7f;
  }
  & :nth-child(3) {
    margin-left: 16px;
  }
}

</style>
