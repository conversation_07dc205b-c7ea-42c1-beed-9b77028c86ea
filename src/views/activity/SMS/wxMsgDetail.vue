<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{ title }}</h2>
    </div>
    <div class="form-box-con flex-form">
      <Form ref="formRef" class="form" :model="formData" :rules="formRules" :label-width="180">
        <FormItem label="开启/关闭">
          <i-switch v-model="formData.status" true-value="1" false-value="0" />
        </FormItem>
        <FormItem label="发送对象" v-if="formData.send_to_phone">
          <Input
            style="width: 375px"
            v-model="phone"
            :disabled="isOpen"
            :maxlength="11"
            placeholder="输入手机号后，回车键添加"
            @on-keyup="handleAddPhone"
          />
          <div style="width: 375px">
            <Tag
              v-for="(num, index) in formData.send_to_phone"
              :key="num"
              :name="index"
              :closable="!isOpen"
              @on-close="handleDelPhone"
            >
              {{ num }}
            </Tag>
          </div>
        </FormItem>
        <FormItem label="触发条件">
          <template v-for="(item, index) in textArray">
            <span v-if="typeof item === 'string'" :key="index">
              {{ item }}
            </span>
            <InputNumber
              v-else
              :key="index"
              v-model="formData.value[item]"
              :disabled="isOpen"
              :min="1"
              :max="999999"
              :precision="0"
            />
          </template>
          <!-- <CheckboxGroup v-if="formData.send_to_coach_type" v-model="formData.send_to_coach_type">
            <Checkbox label="0">通知跟进会籍</Checkbox>
            <Checkbox label="1" :disabled="isOpen">通知上课教练</Checkbox>
            <Checkbox label="2" :disabled="isOpen">通知跟进教练</Checkbox>
          </CheckboxGroup> -->
        </FormItem>
        <!-- <FormItem label="发送时间段" v-if="wx_time_remark">
          {{ wx_time_remark }}
        </FormItem> -->
        <FormItem label="内容">
          <div class="content-top">
            <span @click="status != 1 ? (isEdit = !isEdit) : ''">修改内容模板</span>
            <img v-show="status != 1" @click="status != 1 ? (isEdit = !isEdit) : ''" src="../../../assets/img/pop-edit.png" />
            <!-- 待审 -->
            <img v-show="status == 1" class="status_icon" src="~assets/img/msg_signature_pending.png" />
            <!-- 驳回 -->
            <img v-show="status == 3" class="status_icon" src="~assets/img/msg_signature_reject.png" />
            <!-- 通过 -->
            <img v-show="status == 2" class="status_icon" src="~assets/img/msg_signature_resolve.png" />
            <span>小提示: 30天内仅允许编辑一次</span>
          </div>
          <div class="content-bottom">
            <PrintEditor
              v-model="curEditContent.detail"
              :fields="curEditContent.fields"
              @change="(value) => handleChange(value)"
              :disabled="!isEdit"
            />
          </div>
          <div class="tips">
            <div class="tips-word">内容{{ word.length - 1 }}个字, 还可以输入{{ 101 - word.length }}个字</div>
            <div class="tips-rule">
              <span>教育、医疗、酒类、营销、违法等内容不允许发送...</span>
              <a href="https://mp.weixin.qq.com/mp/opshowpage?action=newoplaw#t3-4" target="_blank">了解更多屏蔽规则</a>
            </div>
          </div>
        </FormItem>
      </Form>
      <Form :model="formData" :rules="formRules" :label-width="180">
        <FormItem label="演示示例">
          <div class="example-box">
            <div class="content">
              <div class="content_title">收到报名申请通知</div>
              <div class="content_box">
                <div class="content_box_name">报名类型:</div>
                <div>
                  <span>{{ title }}</span>
                </div>
              </div>
              <div class="content_box">
                <div class="content_box_name">申请来自:</div>
                <div>
                  <span>${场馆署名}</span>
                </div>
              </div>
              <div class="content_box">
                <div class="content_box_name">开始时间:</div>
                <div>
                  <span>{{ wx_update_time || '' }}</span>
                </div>
              </div>
              <div class="content_box">
                <div class="content_box_name">申请理由:</div>
                <div>
                  <span>{{ word.length > 20 ? `${word.slice(0,20)}...` : word || '' }}</span>
                </div>
              </div>
            </div>
            <div class="template-tips">
              <Alert type="warning" show-icon>
                由于微信限制，消息预览仅能查看内容字数有限,请控制字数或者点击消息进入小程序可查看完整消息内容。
              </Alert>
            </div>
          </div>
        </FormItem>
      </Form>
      <div class="buttons flex-buttons">
        <Button type="success" @click="handlePutSetting">保存</Button>
        <Button @click="$router.back()">取消</Button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import PrintEditor from './components/PrintEditor'

export default {
  name: 'detail',

  components: {
    PrintEditor,
  },

  data() {
    return {
      title: '', // 右上角标题
      isEdit: false, // 是否可编辑
      formRules: {},
      formData: {},
      textArray: [], // 触发条件的文案内容
      phone: '',
      wx_time_remark: '', // 发送时间段
      wx_content: '', // 短信示例内容
      wx_update_time: '', // 短信示例内容
      // PrintEditor使用对象
      curEditContent: {
        detail: '',
        fields: [],
      },
      word: 0,
      status: '',
    }
  },
  computed: {
    ...mapState(['adminInfo']),
    isOpen() {
      return this.formData.status === '0'
    },
  },

  created() {
    // 短信通知项数据
    const target = this.$route.query
    this.target = target
    if (target && target.id) {
      // 获取详情
      this.getDetail(target)
    } else {
      this.$Message.error('未获取到数据！')
      this.$router.replace({
        name: '消息推送',
        params: {
          activeIndex: '1',
        },
      })
    }
  },

  methods: {
    handleAddPhone({ key, target }) {
      const { value } = target

      if (key === 'Enter' && value) {
        const reg = /^1\d{10}$/
        const { send_to_phone } = this.formData

        if (!reg.test(value)) {
          this.$Message.error('请输入正确手机号！')
        } else if (send_to_phone.includes(value)) {
          this.$Message.error('请不要重复添加！')
        } else {
          send_to_phone.push(value)
          this.phone = ''
        }
      }
    },

    handleDelPhone(e, index) {
      this.formData.send_to_phone.splice(index, 1)
    },

    // 提交修改配置
    handlePutSetting() {
      let postData = {
        id: this.formData.id,
        status: this.formData.status,
        content: this.word,
      }
      if (this.formData.value) {
        postData.value = this.formData.value || ''
      }
      if (this.formData.send_to_coach_type) {
        postData.send_to_coach_type = this.formData.send_to_coach_type || []
      }
      if (this.formData.send_to_phone) {
        postData.send_to_phone = this.formData.send_to_phone || []
      }

      this.$service.post('/Web/PublicNumRemind/edit', postData).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success('提交修改成功！')
          this.$router.replace({ name: '消息推送', params: { refresh: true, activeIndex: '0' } })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 获取详情
    getDetail(target) {
      const url = '/Web/PublicNumRemind/get_info?bus_id=' + target.bus_id + '&id=' + target.id
      return this.$service.get(url).then((res) => {
        if (res.data.errorcode === 0) {
          let resData = res.data.data.info
          this.templateList = resData.list
          this.setItemVal(resData, target)
          this.status = resData.approve_status || ''
          this.isEdit = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 设置渲染PrintEditor的对象和修改提交的对象
    setItemVal(info, target) {
      // 设置右上角标题
      this.title = target.title

      let formData = {
        id: info.id,
        status: info.status + '', // 开关状态
      }
      // （可能返回）input框对应的值，数组格式
      // （可能返回）发送给教练的类型，数组格式，1：上课教练；2：跟进教练
      if (info.rule && info.rule.value) {
        formData.value = info.rule.value || []
        formData.send_to_coach_type = info.rule.send_to_coach_type || []
      }
      // （可能返回）需要发送短信的对象手机号
      if (info.rule && info.rule.send_to_phone) {
        formData.send_to_phone =
          info.rule.send_to_phone && Array.isArray(info.rule.send_to_phone) ? [...info.rule.send_to_phone] : null
      }
      // 设置formData
      this.formData = formData
      // 发送时间段
      // this.wx_time_remark = ''
      // 短信示例内容
      this.wx_content = info.rule.content
      this.wx_update_time = info.update_time

      // 设置渲染PrintEditor的显示内容
      this.curEditContent.detail = info.rule.content
      // 设置渲染PrintEditor可以点击的标签
      this.curEditContent.fields = info.type_fields

      /* 处理 触发条件栏的DOM结构 */
      const textArray = info.rule.text.split('$')
      let i = textArray.length // 用于控制插入索引
      let idx = i - 2 // 用于控制输入框值存放的索引
      while (--i) {
        textArray.splice(i, 0, idx--) // 插入输入框
      }
      this.textArray = textArray
    },
    handleChange(value) {
      this.word = value
    },
  },
}
</script>

<style lang="less" scoped>
.form-box {
  .ivu-input-number {
    width: 80px;
    max-width: unset;
  }

  .ivu-tag {
    height: 24px;
    .ivu-icon-ios-close {
      top: -2px;
    }
  }
}

.example-box {
  position: relative;
  width: 375px;
  height: 812px;
  background: url(../../../assets/img/wx_msg_example_bg.jpg) center / contain no-repeat;
  background-size: cover;
  overflow: hidden;

  .content {
    margin-top: 105px;
    margin-left: 15px;
    padding: 10px 15px;
    width: 345px;
    min-height: 225px;
    line-height: 1.6;
    border-radius: 10px;
    background-color: #fff;
    & > div + div {
      margin-top: 12px;
    }

    .content_title {
      font-size: 20px;
      font-weight: bold;
    }

    .content_name {
      font-size: 18px;
    }

    .content_box {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;

      .content_box_name {
        min-width: 72px;
      }
    }
  }
}
.content-top {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  span,
  img {
    cursor: pointer;
  }
  span {
    user-select: none;
  }
  img {
    width: 20px;
    height: 20px;
    margin-left: 12px;
  }
  .status_icon {
    width: 34px;
    height: 34px;
  }
  img + span {
    padding-left: 12px;
  }
}
.tips {
  width: 560px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;

  .tips-word,
  .tips-rule {
    width: 100%;
    text-align: right;
  }

  .tips-rule {
    width: 100%;
    display: flex;
    white-space: nowrap;
    span {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    a {
      min-width: 120px;
    }
  }
}
.flex-form {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  .flex-buttons {
    padding-left: 200px;
    min-width: 100%;
  }
}
.template-tips {
  margin: 20px;
}
</style>
