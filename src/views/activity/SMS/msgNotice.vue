<template>
  <div>
    <div class="sms-number-row">
      <span class="label">短信条数</span>
      <SMSNumber ref="smsNumberRef"/>
    </div>

    <div class="notice-container">
      <div class="tabs-bar">
        <span
          class="tab-item"
          :class="{active: tabIndex===index}"
          v-for="(item, key, index) in renderData" :key="key"
          @click="handleChangeTab(index)"
        >{{ item.title }}</span>
        <span class="right-wrapper">
          <span>提醒总数</span>
          <span class="num">{{sum}}</span>
          <span>启用数量</span>
          <span class="num">{{activeNum}}</span>
        </span>
      </div>
      <div class="notice-content">
        <div
          v-show="tabIndex===index"
          v-for="(item, key, index) in renderData" :key="key"
        >
          <div v-for="(subItem, subKey) in item.list" :key="subKey">
            <div class="label">{{ subItem.title }}</div>
            <ul class="card-box">
              <li
                class="card-content"
                :class="{ on: target.status === '1' }"
                v-for="target in subItem.list" :key="target.id"
                @click="handleToDetail(target)"
              >
                <div style="display: flex;">
                  <template v-if="target.status === '1'">
                    <Icon type="md-checkmark-circle" size="16"/>
                    <span class="status-text">开启中</span>
                  </template>
                  <template v-else>
                    <Icon type="md-close-circle" size="16"/>
                    <span class="status-text">关闭中</span>
                  </template>
                </div>
                <p class="title">{{ target.title }}</p>
                <div class="bottom">
                  <template v-if="target.status === '0'">
                    <span>去开启</span>
                    <Icon type="md-arrow-dropright" size="16"/>
                  </template>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import SMSNumber from './smsNumber';

  export default {
    name: 'msgNotice',
    components: {
      SMSNumber,
    },

    data() {
      return {
        tabIndex: 0,
        renderData: {},
        sum: 0, // 提醒总数
        activeNum: 0, // 启用数量
        signName: '勤鸟运动',
        scrollTop: 0,
        vipMainCon: document.getElementById('vipMainCon')
      }
    },

    created() {
      this.getList()
      // 获取签名
      // this.$service.post('/MsgCenter/Msg/getSign').then(res => {
      //   if (res.data.errorcode === 0) {
      //     this.signName = res.data.data.title;
      //   } else {
      //     this.$Message.error(res.data.errormsg);
      //   }
      // })
    },

    watch: {
      '$route.name'(val, lastName) {
        if (lastName === '短信通知') {
          const { refresh } =  this.$route.params;
          refresh && this.getList()

          const { vipMainCon, scrollTop } = this;
          if (vipMainCon && scrollTop) {
            vipMainCon.scrollTop = scrollTop;
          }
        }
      }
    },

    methods: {
      getList() {
        this.$service.get('Web/SmsRemindSetting/get_list').then(({ data: resData }) => {
          const { errorcode, errormsg, data, } = resData;
          if (errorcode === 0) {
            this.renderData = data.list;

            let sum = 0; // 提醒总数
            let activeNum = 0; // 启用数量
            for (const item of Object.values(this.renderData)) {
              for (const subItem of Object.values(item.list)) {
                const { list } = subItem
                list && list.forEach(v => { activeNum += +v.status });
                sum += list.length;
              }
            }
            this.sum = sum;
            this.activeNum = activeNum;

          } else this.$Message.error(errormsg);
        })
      },

      handleChangeTab(idx) {
        this.tabIndex = idx;
      },

      handleToDetail(target) {
        const { vipMainCon } = this;
        if (vipMainCon) this.scrollTop = vipMainCon.scrollTop;

        this.$router.push({
          name: '短信通知',
          params: { target },
        })
      }
    },
  }
</script>

<style lang="less" scoped>
@text-color: #2C3945;
@tab-color: #2B8DF2;
@notice-num-color: #E60012;
@turn-on-color: #18BF6A;
@turn-on-bg: #EBFAEF;
@turn-off-color: #959595;
@turn-off-bg: #F1F3F7;

.sms-number-row {
  display: flex;
  align-items: center;
  .label {
    width: 100px;
    font-size: 14px;
    color: @text-color;
  }
}

.notice-container {
  margin-top: 20px;
  font-size: 14px;
  border: 1px solid #E1E3E9;
  .tabs-bar {
    display: flex;
    align-items: center;
    padding-right: 20px;
    height: 46px;
    border-bottom: 1px solid #E1E3E9;
  }
}

.tabs-bar {
  color: @text-color;
  .tab-item {
      width: 100px;
      height: 46px;
      line-height: 46px;
      text-align: center;
      cursor: pointer;
      user-select: none;
      &:hover {
        color: @tab-color;
      }
      &.active {
        color: @tab-color;
        border-bottom: 2px solid @tab-color;
      }
    }
    .right-wrapper {
      margin-left: auto;
      font-weight: bold;
      .num {
        margin-left: 10px;
        color: @notice-num-color;
      }
      & :nth-child(2) {
        margin-right: 26px;
      }
    }
}

.notice-content {
  padding: 18px 30px 26px;
  .label {
    margin-bottom: 16px;
    font-size: 16px;
    color: @text-color;
    &::before {
      display: inline-block;
      margin-right: 9px;
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: @tab-color;
      vertical-align: middle;
    }
  }
  .card-box {
    display: flex;
    flex-wrap: wrap;
  }
  .card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 30px;
    margin-bottom: 20px;
    padding: 12px 10px;
    width: 220px;
    height: 110px;
    color: @turn-off-color;
    background-color: @turn-off-bg;
    border-radius: 6px;
    cursor: pointer;
    transition: box-shadow,translateY, .2s;
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
    }
    &.on {
      color: @turn-on-color;
      background-color: @turn-on-bg;
      border: 1px solid @turn-on-color;
      &:hover {
        box-shadow: 1px 3px 10px rgba(24, 191, 106, 0.15);
      }
      .status-text {
        color: #000000;
      }
    }
    .status-text {
      margin-left: 5px;
    }
    .title {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
    }
    .bottom {
      height: 20px;
      text-align: right;
      color: @turn-on-color;
    }
  }
}
</style>
