<template>
      <div class="box tab-table-wrap customized-tabs">
                <div class="tab-head">
                     <Input style="margin-left:20px;width: 180px;" v-model="postData2.inviter" placeholder="参与人姓名/电话"/>

                  <Select v-model="postData2.activity_id" placeholder="全部活动" style="margin-left:20px;margin-right:20px;width:200px" @on-change="handleActivityChange" filterable clearable>
                    <Option
                      v-for="item in activitys"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option>
                  </Select>
                  <DatePickerWithButton v-if="days.length" :days.sync="days"  :options="options2" @on-change="dateChange2"></DatePickerWithButton>
                  <Select v-model="postData2.status" style="width:200px;margin-left:20px;" @on-change="handleStatusChange2" placeholder="完成任务状态" clearable>
                    <Option :value="1">未完成</Option>
                    <Option :value="2">已完成</Option>
                  </Select>
                   <Button style="margin-left:20px;" type="success" @click="doSearch2">搜索</Button>
                </div>
                <Table :columns="columns2" :data="data2" disabled-hover></Table>
                <footer>
                    <Button @click="exportCsv2">导出Excel</Button>
                    <Export ref="export"></Export>
                    <Page :total="total2" :history="false" @on-change="handlePageChange2" @on-page-size-change="pageSizeChanged2" show-total show-sizer></Page>
                </footer>
    </div>
</template>
<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'

import DatePickerWithButton from 'components/picker/datePickerWithButton'
import AdminRegion from 'components/form/adminRegion.vue'
import Export from 'src/components/Export'
import { getNewHost } from 'utils/config'
import {
  queryPassengerList,
  queryPassengerStatisticsDetailList
} from 'src/service/getData'


export default {
  name: 'passengerStatisticsDetailList',
  components: { DatePickerWithButton, AdminRegion, Export },
  // computed: {
  //   ...mapGetters(['coachGroupList'])
  // },
  computed: {
    ...mapState(['busId'])
  },
  data() {
    const formatDate = (source, format) => {
      const o = {
        'M+': source.getMonth() + 1, // 月份
        'd+': source.getDate(), // 日
        'H+': source.getHours(), // 小时
        'm+': source.getMinutes(), // 分
        's+': source.getSeconds(), // 秒
        'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
        'f+': source.getMilliseconds() // 毫秒
      }
      if (/(y+)/.test(format)) {
        format = format.replace(
          RegExp.$1,
          (source.getFullYear() + '').substr(4 - RegExp.$1.length)
        )
      }
      for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
          format = format.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          )
        }
      }
      return format
    }
    return {
      formatDate,
      tabName: '',
      options2: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      // userUrl: `${getNewHost()}/#/member/detail/`,
      userUrl: '/member/detail/',
      columns2: [
        {
          title: '参与人',
          key: 'inviter_name',
          render: (h, param) => {
            let url = this.userUrl + param.row.inviter_user_id
            return (
              // <a target="_blank" href={url}>
              //   {param.row.inviter_name}
              // </a>
              <router-link target="_blank" to={ url }>{ param.row.inviter_name }</router-link>

            )
          }
        },
        {
          title: '带客姓名',
          key: 'invitee_name',
          render: (h, param) => {
            let url = this.userUrl + param.row.invitee_user_id
            return (
              // <a target="_blank" href={url}>
              //   {param.row.invitee_name}
              // </a>
              <router-link target="_blank" to={ url }>{ param.row.invitee_name }</router-link>

            )
          }
        },
        {
          title: '注册时间',
          key: 'regist_time'
        },
        {
          title: '完成任务状态',
          key: 'status_t'
        },
        {
          title: '完成任务时间',
          key: 'finished_time'
        }
      ],
      data2: [],
      total2: 0,
      activitys: [],
      days:[],

      selectBusId2: '',
      postData2: {
        page_no: 1,
        page_size: 10
      },
    }
  },
  mounted() {
      this.postData2 = {
        ...this.$route.query,
        ...{
             page_no: 1,
             page_size: 10
        }
        }
    this.days = [new Date(this.postData2.start_time).getTime(),new Date(this.postData2.end_time).getTime()]
    this.queryAllPassengerList()
    },
  created() {
    // this.selectBusId2 = this.busId
    // this.queryAllPassengerList()
  },
  methods: {
    handleActivityChange() {
      this.postData2.page_no = 1
      this.getTab2List()
    },
    doSearch2() {
      this.getTab2List()
    },
     handleStatusChange2(){
      this.postData2.page_no = 1
      this.getTab2List()
    },
    dateChange2(dateRange) {
      this.postData2.page_no = 1
      this.postData2.start_time = dateRange[0]
      this.postData2.end_time = dateRange[1]
      this.getTab2List()
    },
    handlePageChange2(pageNo) {
      this.postData2.page_no = pageNo
      this.getTab2List()
    },
    queryAllPassengerList() {
      return queryPassengerList().then(res => {
        if (res.data.errorcode == 0) {
          this.activitys = res.data.data.list
        }
      })
    },
    getTab2List(extract = 0) {
        let tempPostData2 = extract===0?this.postData2:{...this.postData2,
        ...{
            page_no:1,
            page_size:this.total2
            }
      }
      return queryPassengerStatisticsDetailList(tempPostData2).then(res => {
        if (res.data.errorcode == 0) {
          if (extract === 0) {
          this.data2 = res.data.data.list
          this.total2 = parseInt(res.data.data.count)
          } else {
            this.$refs.export.export({
              filename: `客带客明细统计(${this.postData2.start_time}~${
                this.postData2.end_time
              })`,
              columns: this.columns2,
              data: res.data.data.list
            })
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    exportCsv2() {
      this.getTab2List(1)
    },
    pageSizeChanged2(size) {
      this.postData2.page_no = 1
      this.postData2.page_size = size
      this.getTab2List()
    },
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap {
    border-top: 0;
  }
}
.mgb-10 {
  margin-bottom: 10px;
}
.mgb-20 {
  margin-bottom: 20px;
}
.mgt-20 {
  margin-top: 20px;
}

.box {
  .tab-head {
    background-color: white;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 50px;
  }

  .total-stat {
    background-color: white;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    border-top: @border;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
}
</style>
