<template>
      <div class="box tab-table-wrap customized-tabs">
        <Tabs value="tabNameOne" @on-click="handleTabClick" v-model="tabName">
            <TabPane label="活动列表" name="tabNameOne">
                 <div class="tab-head">
                    <Input style="margin-left:20px;width: 180px;" v-model="postData1.name" placeholder="名称"/>

                     <Select v-model="postData1.status" style="margin-left:20px;width:200px;margin-right:20px;" @on-change="handleStatusChange" clearable>
                        <Option :value="1">待开始</Option>
                        <Option :value="2">进行中</Option>
                        <Option :value="3">下架</Option>
                  </Select>
                    <DatePickerWithButton select="本月" :options="options1" @on-change="dateChange1"></DatePickerWithButton>
                      <Button style="margin-left:20px;" type="success" @click="doSearch1">搜索</Button>

                </div>

                <Table :columns="columns1" :data="data1" disabled-hover></Table>
                <footer>
                    <Button @click="handleAddActivity" type="primary">新建活动</Button>
                    <Page :total="total1" :history="false" @on-change="handlePageChange1" @on-page-size-change="pageSizeChanged1" show-total show-sizer></Page>
                </footer>
            </TabPane>
            <TabPane label="活动统计" name="tabNameTwo">
                <div class="tab-head">
                     <Input style="margin-left:20px;width: 180px;" v-model="postData2.inviter" placeholder="参与人姓名/电话"/>

                  <Select v-model="postData2.activity_id" placeholder="全部活动" style="margin-left:20px;margin-right:20px;width:200px" @on-change="handleActivityChange" filterable clearable>
                    <Option
                      v-for="item in activitys"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option>
                  </Select>
                  <DatePickerWithButton v-if="isShowDatePicker2" :days.sync="days" select="本月" :options="options2" @on-change="dateChange2"></DatePickerWithButton>
                  <Button style="margin-left:20px;" type="success" @click="doSearch2">搜索</Button>
                  <!-- <Select v-model="replyValue" style="width:200px;margin-left:20px;" @on-change="handleStatusChange2" placeholder="完成任务状态" clearable>
                    <Option :value="1">未完成</Option>
                    <Option :value="2">已完成</Option>
                  </Select> -->
                </div>
                 <div class="total-stat" v-if="commentInformation.length">
                    <div class="stat" v-for="(tip, index) in TAB_TEXT" :key="index">
                        <h3>{{ commentInformation[index] }}</h3>
                        <p>{{tip.name}}
                            <Tooltip v-if="tip.tip" :content="tip.tip" placement="bottom-end">
                            <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                            </Tooltip>
                         </p>
                    </div>
                </div>
                <Table :columns="columns2" :data="data2" disabled-hover></Table>
                <footer>
                    <Button @click="exportCsv2">导出Excel</Button>
                    <Export ref="export"></Export>
                    <!-- <Page :total="total2" :history="false" @on-change="handlePageChange2" @on-page-size-change="pageSizeChanged2" show-total show-sizer></Page> -->
                </footer>
            </TabPane>
        </Tabs>
        <Drawer width='60' :title="drawerStatus===0?'添加活动':drawerStatus===1?'编辑活动':'查看活动'" :mask-closable="drawerStatus===2" closable v-model="drawerflag">
             <Form ref="activityItem" :model="activityItem" :label-width="80" class="form" :rules="ruleActivityItem">
                <h3 >基础信息</h3>
                <FormItem  label="活动标题" prop="name">
                    <Input :disabled="drawerStatus===2" v-model="activityItem.name" :maxlength="12" class="input" placeholder="不超过12字符" />
                </FormItem>
                  <FormItem label="活动时间" prop="dateRange">
                     <Date-picker style="width: 100%" placeholder="不能与其他活动有时间交叉" v-model="activityItem.dateRange" type="datetimerange" format="yyyy-MM-dd HH:mm"
                     :disabled="drawerStatus===2" :editable="false" @on-change="dateChange" :clearable="false"/>
                </FormItem>
                <h3 >达成条件</h3>
                <FormItem label="达成条件" prop="condition">
                    <div v-for="(conditionItem,conditionIndex) in condition" :key="conditionIndex" :value="conditionItem">
                        <div class="mgb-10">
                            <div>
                                <Select :disabled="drawerStatus===2" style="width: 50%"  v-model="conditionItem.id" placeholder="请选择条件" @on-change="handleConditionChange">
                                    <Option v-for="(conditionSelect) in CONDITIONS" :key="conditionSelect.id" :value="conditionSelect.id" :disabled="conditionSelect.disabled" >{{conditionSelect.tip}}</Option>
                                </Select>
                                <Icon v-if="conditionIndex!==0 && drawerStatus!==2" style="margin-left:20px;"   type="ios-close-circle" size="24" @click="handleDeleteCondition(conditionIndex)" />
                            </div>
                            <Checkbox :disabled="drawerStatus===2" v-if="conditionItem.id>1 && CONDITIONS[conditionItem.id-1].checkMsg"   v-model="conditionItem.isChecked">{{CONDITIONS[conditionItem.id-1].checkMsg}}</Checkbox>
                        </div>
                     </div>
                    <RadioGroup  style="width:100%" v-model="activityItem.reach_rule">
                        <Radio :disabled="drawerStatus===2" label="1">多条件时，需都达成后才发放奖励</Radio>
                        <Radio :disabled="drawerStatus===2" label="2">多条件时，达成任意一个条件即发放奖励（多条件都达成时，也只发放一次奖励）</Radio>
                    </RadioGroup>
                    <Button v-if="drawerStatus!==2 && addShow" type="primary" @click="handleAddCondition">添加条件</Button>
                </FormItem>
                <h3>邀请人达成奖励</h3>
                <FormItem label="" prop="inviter_award_type">
                    <h4>任何注册过会员端的客户都可以进行邀请</h4>
                     <Select :disabled="drawerStatus===2" style="width: 50%"  v-model="activityItem.inviter_award_type" placeholder="请选择奖励" @on-change="handleInviteTypeChange">
                        <Option value="1">赠送会员卡</Option>
                        <Option value="2">赠送折扣劵</Option>
                    </Select>
                    <Select :disabled="drawerStatus===2" style="width: 40%;margin-left:20px;"  v-model="activityItem.inviter_award_id"
                    v-if="activityItem.inviter_award_type!=0"
                    :placeholder="activityItem.inviter_award_type==1?'请选择需赠送的会员卡':'请选择需赠送的折扣劵'" filterable>
                        <Option v-for="(item) in activityItem.inviter_award_type==1?cards:coupons"
                        :key="activityItem.inviter_award_type==1?item.card_id:item.coupon_id"
                        :value="activityItem.inviter_award_type==1?item.card_id:item.coupon_id">{{activityItem.inviter_award_type==1?item.card_name:item.coupon_name}}</Option>
                    </Select>
                </FormItem>
                 <h3>被邀请人达成奖励</h3>
                 <FormItem label="" prop="invitees_award_type">
                    <h4>在本门店客户池中没有的新客都可成为被邀请人 </h4>
                     <Select :disabled="drawerStatus===2" style="width: 50%"  v-model="activityItem.invitees_award_type" placeholder="请选择奖励方案" @on-change="handleInviteesTypeChange">
                        <Option value="1">赠送会员卡</Option>
                        <Option value="2">赠送折扣劵</Option>
                        <Option value="3">不设置奖励方案</Option>
                    </Select>
                    <Select :disabled="drawerStatus===2" style="width: 40%;margin-left:20px;"  v-model="activityItem.invitees_award_id"
                    v-if="activityItem.invitees_award_type!=0 && activityItem.invitees_award_type!=3"
                    :placeholder="activityItem.invitees_award_type==1?'请选择需赠送的会员卡':'请选择需赠送的折扣劵'" filterable>
                        <Option v-for="(item) in activityItem.invitees_award_type==1?cards:coupons"
                        :key="activityItem.invitees_award_type==1?item.card_id:item.coupon_id"
                        :value="activityItem.invitees_award_type==1?item.card_id:item.coupon_id">{{activityItem.invitees_award_type==1?item.card_name:item.coupon_name}}</Option>
                    </Select>
                    <p style="color:#ed4014">注：被邀请人在达成任务后，系统自动发放奖励，一人只能领取一次</p>
                </FormItem>

                 <h3>活动规则</h3>
                  <FormItem label="邀请说明">
                 <p>1.您邀请的好友，需是新用户，即不在门店己有的用户中</p>
                 <p>2.您邀请的好友完成任务后，即可获得奖励；邀请多人都达成任务，可获得多份奖励</p>
                 <p>3.您的好友同一手机号码仅可领取一次邀请奖励</p>
                 <p>4.您可在本页面点击“立即邀请”、或在团课预约页面点击“约朋友”，来参加此活动</p>
                 <p>5.活动时间 {{activityItem.begin_time}} ～ {{activityItem.end_time}}</p>
                 <p>6.如有疑问可到【{{busName}}】咨询</p>
                  </FormItem>
                 <h3>tips</h3>
                 <p>1.同一时间，只能有一个活动在进行</p>
                 <p>2.被邀请的新客，会自动进入会籍公海</p>
                 <p>3.邀请人可以是任何注册了会员端的客户；被邀请人只能是不存在于门店已有客户池中的新客</p>
                 <p>4.新客通过邀请人的链接注册并登录会员端后，即与邀请人进行绑定，之后新客完成了指定条件，邀请人即可获得奖励</p>
                 <p>5.邀请人只能在同一新客上，获得一次奖励</p>
                 <FormItem style="margin-top:20px;" v-if="drawerStatus!==2">
                    <Button  @click="handleCancel">取消</Button>
                    <Button style="margin-left: 20px" type="primary" @click="handleSave">保存</Button>
                 </FormItem>
              </Form>
        </Drawer>
        <Modal v-model="isModelShow"  ok-text="删除" @on-ok="handleModelDeleteOk">
            <p>是否确认删除</p>
        </Modal>
    </div>
</template>
<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'

import DatePickerWithButton from 'components/picker/datePickerWithButton'
import AdminRegion from 'components/form/adminRegion.vue'
import Export from 'src/components/Export'
import { getNewHost } from 'utils/config'
import {
  queryPassengerList,
  delPassengerActivity,
  offPassengerActivity,
  getCards,
  queryBusCoupon,
  addActivity,
  editActivity,
  queryPassengerActivityDetail,
  queryPassengerStatisticsList
} from 'src/service/getData'

const TAB_TEXT = [
  { name: '参与人数' },
  { name: '带客人数' },
  { name: '人均带客数', tip: '带入新客数【除以】参与人数' },
  { name: '完成任务数' },
  {
    name: '完成任务率',
    tip: '完成任务数【除以】带入新客数'
  }
]

const CONDITIONS = [
  { id: 1, tip: '被邀请人完成会员端注册', disabled: false },
  {
    id: 2,
    tip: '被邀请人购买会员卡',
    checkMsg: '被邀请人购买会员卡后，需再完成签到，才算达成',
    disabled: false
  },
  {
    id: 3,
    tip: '被邀请人完成团课约课',
    checkMsg: '被邀请人完成团购预约后，需再完成团课签到，才算达成',
    disabled: false
  },
  {
    id: 4,
    tip: '被邀请人完成私教消课',
    disabled: false
  }
]

export default {
  name: 'passengerList',
  components: { DatePickerWithButton, AdminRegion, Export },
  // computed: {
  //   ...mapGetters(['coachGroupList'])
  // },
  computed: {
    ...mapState(['busId']),
    ...mapState(['busName'])
  },
  data() {
    const formatDate = (source, format) => {
      const o = {
        'M+': source.getMonth() + 1, // 月份
        'd+': source.getDate(), // 日
        'H+': source.getHours(), // 小时
        'm+': source.getMinutes(), // 分
        's+': source.getSeconds(), // 秒
        'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
        'f+': source.getMilliseconds() // 毫秒
      }
      if (/(y+)/.test(format)) {
        format = format.replace(
          RegExp.$1,
          (source.getFullYear() + '').substr(4 - RegExp.$1.length)
        )
      }
      for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
          format = format.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          )
        }
      }
      return format
    }
    const validateCondition = (rule, value, callback) => {
      if (this.condition.length && this.condition[0].id !== 0) {
        callback()
      } else {
        callback(new Error('请至少选择一个达成条件'))
      }
    }
    return {
      formatDate,
      tabName: '',
      dateRange1: '',
      dateRange2: '',
      options1: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      options2: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      days: null,
      isShowDatePicker2: true,
      commentInformation: [0, 0, 0, 0, 0],
      // userUrl: `${getNewHost()}/#/member/detail/`,
      userUrl: '/member/detail/',
      columns1: [
        {
          title: '标题',
          key: 'name'
        },
        {
          title: '起止时间',
          key: 'begin_time',
          render: (h, param) => {
            let begin_time = param.row.begin_time.split(' ')[0]
            let end_time = param.row.end_time.split(' ')[0]
            return (
              <span>
                {begin_time}~{end_time}
              </span>
            )
          }
        },
        {
          title: '创建时间',
          key: 'create_time'
        },
        {
          title: '参与人数',
          key: 'inviter_num',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  this.handleClickToTabTwo(param.row.id)
                }}
              >
                {param.row.inviter_num}
              </a>
            )
          }
        },
        {
          title: '带客人数',
          key: 'invitee_num',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  this.handleClickToTabTwo(param.row.id)
                }}
              >
                {param.row.invitee_num}
              </a>
            )
          }
        },
        {
          title: '完成任务人数',
          key: 'done_num',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  this.handleClickToTabTwo(param.row.id)
                }}
              >
                {param.row.done_num}
              </a>
            )
          }
        },
        {
          title: '状态',
          key: 'status_t'
        },
        {
          title: '操作',
          key: 'status',
          render: (h, param) => {
            let status = parseInt(param.row.status)
            let leftStr = status === 1 ? '编辑' : '查看'
            let rightStr = status === 2 ? '下架' : '删除'
            const goDetail = () => {
              this.drawerStatus = status === 1 ? 1 : 2
              this.queryActivity(param.row.id)
            }
            const delMe = () => {
              this.handleDelete(param.row.id)
            }
            const offMe = () => {
              this.handleOff(param.row.id)
            }

            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  style={{
                    color: '#52a4ea',
                    minWidth: '0',
                    marginRight: '20px'
                  }}
                  onClick={goDetail}
                >
                  {leftStr}
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={status === 2 ? offMe : delMe}
                >
                  {rightStr}
                </i-button>
              </div>
            )
          }
        }
      ],
      columns2: [
        {
          title: '参与人',
          key: 'username',
          render: (h, param) => {
            let url = this.userUrl + param.row.inviter_user_id
            return (
              // <a target="_blank" href={url}>
              //   {param.row.username}
              // </a>
              <router-link target="_blank" to={ url }>{ param.row.username }</router-link>
            )
          }
        },
        {
          title: '带客人数',
          key: 'invitee_num',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  let inviter_user_id = param.row.inviter_user_id
                  this.$router.push({
                    path: `/Web/Activity/pc_passenger_statistics_detail_list`,
                    query: {
                      ...this.postData2
                      //   ...{ inviter_user_id: param.row.inviter_user_id }
                    }
                  })
                }}
              >
                {param.row.invitee_num}
              </a>
            )
          }
        },
        {
          title: '完成任务数',
          key: 'done_num'
        },
        {
          title: '完成任务率',
          key: 'completion_rate'
        }
      ],
      data1: [],
      data2: [],
      total1: 0,
      total2: 0,

      activitys: [],
      selectActivityId: '',

      drawerflag: false,
      drawerStatus: 0, //0添加 1编辑 2查看
      selectedClass2: '',
      classList2: null,
      selectBusId2: '',
      TAB_TEXT,
      CONDITIONS,
      postData1: {
        page_no: 1,
        page_size: 10
      },
      postData2: {
        // page_no: 1,
        // page_size: 10
      },
      condition: [{ id: 0 }],
      activityItem: {
        reach_condition: [],
        reach_rule: '2'
      },
      ruleActivityItem: {
        name: [
          {
            required: true,
            type: 'string',
            max: 12,
            trigger: 'blur',
            message: '请输入标题'
          }
        ],
        dateRange: [
          {
            required: true,
            type: 'array',
            trigger: 'change',
            message: '请选择活动时间'
          }
        ],
        condition: [
          {
            validator: validateCondition,
            trigger: 'change'
          }
        ],
        inviter_award_type: [
          {
            required: true,
            trigger: 'change',
            message: '请选择邀请人达成奖励'
          }
        ],
        inviter_award_id: [
          {
            required: true,
            trigger: 'change'
          }
        ],
        invitees_award_type: [
          {
            required: true,
            trigger: 'change',
            message: '请选择被邀请人达成奖励'
          }
        ],
        invitees_award_id: [
          {
            required: true,
            trigger: 'change'
          }
        ]
      },
      cards: [],
      coupons: [],
      isModelShow: false,
      deleteId: '',
      addShow: true
    }
  },
  watch: {
    choseGroup1: function(val, old) {
      if (val) {
        const groupIdArr = val.split('_')
        if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
          this.pageNo1 = 1
          this.choseGroup1Id = groupIdArr[0]
        }
      } else {
        this.choseGroup1Id = ''
      }
      this.getOverall(1)
      this.getTab1List()
    }
    // condition: {
    //   handler(newVal,oldVal) {
    //     console.log("watch",newVal,oldVal)
    //     let tempConditions = []
    //     this.CONDITIONS.forEach(item => {
    //       let isCanAdd = true
    //       newVal.forEach(valItem => {
    //         if (valItem.id === item.id) isCanAdd = false
    //       })
    //       if(isCanAdd) tempConditions.push(item)

    //     })
    //     this.$nextTick(()=>{
    //         this.conditionOptions = tempConditions
    //     })

    //   },
    //   deep:true
    // }
    //   choseGroup2: function(val, old) {
    //     console.log(val);
    //     const groupIdArr = val.split("_");
    //     if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
    //       this.choseGroup2CoachId = groupIdArr[0];
    //       this.getOverall();
    //       this.getTab2List();
    //     }
    //   }
  },
  // async mounted() {
  //     if (!this.coachGroupList || !this.coachGroupList.length) {
  //         this.groupList = await this.getGroupDB();
  //     } else {
  //         this.groupList = this.coachGroupList;
  //     }
  // },
  created() {
    this.selectBusId2 = this.busId
    this.queryAllPassengerList()
  },
  methods: {
    handleStatusChange() {
      this.postData1.page_no = 1
      this.getTab1List()
    },
    handleActivityChange() {
      //   this.postData2.page_no = 1
      this.getTab2List()
    },
    doSearch1() {
      this.getTab1List()
    },
    doSearch2() {
      this.getTab2List()
    },
    //  handleStatusChange2(){
    //   this.postData2.page_no = 1
    //   this.getTab2List()
    // },
    dateChange1(dateRange) {
      this.postData1.page_no = 1
      this.postData1.start_time = dateRange[0]
      this.postData1.end_time = dateRange[1]
      this.getTab1List()
    },
    dateChange2(dateRange) {
      //   this.postData2.page_no = 1
      this.postData2.start_time = dateRange[0]
      this.postData2.end_time = dateRange[1]
      this.getTab2List()
    },
    handlePageChange1(pageNo) {
      this.postData1.page_no = pageNo
      this.getTab1List()
    },
    // handlePageChange2(pageNo) {
    //   this.postData2.page_no = pageNo
    //   this.getTab2List()
    // },
    handleClickToTabTwo(id) {
      this.tabName = 'tabNameTwo'
      this.postData2.activity_id = id
      this.postData2.start_time = this.postData1.start_time
      this.postData2.end_time = this.postData1.end_time
      this.isShowDatePicker2 = false
      this.days = [
        new Date(this.postData2.start_time).getTime(),
        new Date(this.postData2.end_time).getTime()
      ]
      setTimeout(() => {
        this.isShowDatePicker2 = true
      }, 500)
      this.handleTabClick('tabNameTwo')
    },
    handleTabClick(tabName) {
      if (tabName == 'tabNameOne') {
        this.getTab1List()
      } else if (tabName == 'tabNameTwo') {
        this.getTab2List()
      }
    },
    queryAllPassengerList() {
      return queryPassengerList().then(res => {
        if (res.data.errorcode == 0) {
          this.activitys = res.data.data.list
        }
      })
    },
    getTab1List() {
      return queryPassengerList(this.postData1).then(res => {
        if (res.data.errorcode == 0) {
          this.data1 = res.data.data.list
          this.total1 = parseInt(res.data.data.count)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getTab2List(extract = 0) {
      return queryPassengerStatisticsList(this.postData2).then(res => {
        if (res.data.errorcode == 0) {
          if (extract === 0) {
            this.data2 = res.data.data.list
            //   this.total2 = res.data.data.count
            let data = res.data.data
            this.commentInformation[0] = data.inviter_num
            this.commentInformation[1] = data.invitee_num
            this.commentInformation[2] = data.inviter_rate
            this.commentInformation[3] = data.done_num
            this.commentInformation[4] = data.completion_rate
          } else {
            //   let list = res.data.data.list
            //   if (Array.isArray(list)) {
            //     list.forEach(item => {
            //       if (!!item.class_time) {
            //         item.class_time = this.formatDate(
            //           new Date(item.class_time * 1000),
            //           'yyyy-MM-dd HH:mm:ss'
            //         )
            //       }
            //       if (!!item.comment_time) {
            //         item.comment_time = this.formatDate(
            //           new Date(item.comment_time * 1000),
            //           'yyyy-MM-dd HH:mm:ss'
            //         )
            //       }
            //       if (Array.isArray(item.set_type_json)) {
            //         let tagStr = ''
            //         const len = item.set_type_json.length
            //         item.set_type_json.forEach((tag, index) => {
            //           tagStr += tag.value + ': ' + tag.star + '分'
            //           if (index !== len - 1) {
            //             tagStr += ', '
            //           }
            //         })
            //         item.assessDetail = tagStr
            //       }
            //       if (Array.isArray(item.tag_json)) {
            //         let tagStr = ''
            //         const len = item.tag_json.length
            //         item.tag_json.forEach((tag, index) => {
            //           tagStr += tag.value
            //           if (index !== len - 1) {
            //             tagStr += ', '
            //           }
            //         })
            //         item.mark = tagStr
            //       }
            //     })
            //   }
            this.$refs.export.export({
              filename: `客带客活动统计(${this.postData2.start_time}~${
                this.postData2.end_time
              })`,
              columns: this.columns2,
              data: res.data.data.list
            })
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    exportCsv2() {
      this.getTab2List(1)
    },
    pageSizeChanged1(size) {
      this.postData1.page_no = 1
      this.postData1.page_size = size
      this.getTab1List()
    },
    // pageSizeChanged2(size) {
    //   this.pageNo2 = 1
    //   this.pageSize2 = size
    //   this.getTab2List()
    // },
    handleModelDeleteOk() {
      delPassengerActivity(this.deleteId).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.getTab1List()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleDelete(id) {
      //   this.deleteId = id
      //   this.isModelShow = true
      this.$Modal.confirm({
        title: '删除活动',
        content: '确认删除吗？',
        onOk: () => {
          this.deleteId = id
          this.handleModelDeleteOk()
        }
      })
    },
    handleOff(id) {
      offPassengerActivity(id).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.getTab1List()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    ///添加 查询 页面
    handleAddActivity() {
      this.activityItem = {
        reach_condition: [],
        reach_rule: '2'
        // condition: [{ id: 0 }]
      }
      this.condition = [{ id: 0 }]
      this.drawerStatus = 0
      this.addShow = true
      this.drawerflag = true
    },
    dateChange(val) {
      this.activityItem.dateRange = val
      this.activityItem.begin_time = val[0]
      this.activityItem.end_time = val[1]
    },
    handleAddCondition() {
      this.condition.push({
        id: 0,
        isChecked: false
      })
      this.addShow = this.condition.length < 4
    },
    handleDeleteCondition(index) {
      this.condition.splice(index, 1)
      this.addShow = this.condition.length < 4
      this.handleConditionChange()
    },
    handleConditionChange() {
      //原始所有选项
      this.CONDITIONS.forEach(item => {
        item.disabled = false
        //遍历已选择项
        this.condition.forEach(valItem => {
          if (valItem.id === item.id) item.disabled = true
        })
      })
    },
    handleInviteTypeChange(value) {
      this.activityItem.inviter_award_id = 0
      if (value == 1) {
        this.queryCards()
      } else if (value == 2) {
        this.queryBusCoupon()
      }
    },
    handleInviteesTypeChange(value) {
      this.activityItem.invitees_award_id = 0
      if (value == 1) {
        this.queryCards()
      } else if (value == 2) {
        this.queryBusCoupon()
      }
    },
    queryCards(isQueryDetail) {
      if (this.cards.length > 0) {
        if (isQueryDetail) {
          this.handleExceptCard()
        }
        return
      }
      return getCards(this.busId, 1).then(res => {
        if (res.data.errorcode == 0) {
          let arr = res.data.data
          this.cards = arr.filter(
            item =>
              parseInt(item.universal_card) === 0 &&
              parseInt(item.sale_status)===1 &&
              (parseInt(item.card_type_id) === 1 ||
                parseInt(item.card_type_id) === 2 ||
                parseInt(item.card_type_id) === 3)
          )

          this.handleExceptCard()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleExceptCard() {
      if (this.drawerStatus !== 0) {
        // 只修改编辑和查看
        let isInviterSelect =
          parseInt(this.activityItem.inviter_award_type) === 1
        let isInviteesSelect =
          parseInt(this.activityItem.invitees_award_type) === 1

        if (isInviterSelect) {
          // 是否邀请人选择会员卡
          let tempCards = this.cards.filter(
            item => item.card_id === this.activityItem.inviter_award_id
          )
          if (tempCards.length === 0) {
            this.cards.push({
              card_id: this.activityItem.inviter_award_id,
              card_name: this.activityItem.inviter_award_gift
            })
          }
        }

        if (isInviteesSelect) {
          // 是否被邀请人选择会员卡
          let tempCards = this.cards.filter(
            item => item.card_id === this.activityItem.invitees_award_id
          )
          if (tempCards.length === 0) {
            this.cards.push({
              card_id: this.activityItem.invitees_award_id,
              card_name: this.activityItem.invitees_award_gift
            })
          }
        }
      }
    },
    queryBusCoupon(isQueryDetail) {
      if (this.coupons.length > 0) {
        if (isQueryDetail) {
          this.handleExceptCoupon()
        }
        return
      }
      return queryBusCoupon().then(res => {
        if (res.data.errorcode == 0) {
          this.coupons = res.data.data
          this.handleExceptCoupon()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleExceptCoupon() {
      if (this.drawerStatus !== 0) {
        // 只修改编辑和查看
        let isInviterSelect =
          parseInt(this.activityItem.inviter_award_type) === 2
        let isInviteesSelect =
          parseInt(this.activityItem.invitees_award_type) === 2
        if (isInviterSelect) {
          // 是否邀请人选择折扣劵
          let tempCoupons = this.coupons.filter(
            item => item.coupon_id === this.activityItem.inviter_award_id
          )
          if (tempCoupons.length === 0) {
            this.coupons.push({
              coupon_id: this.activityItem.inviter_award_id,
              coupon_name: this.activityItem.inviter_award_gift
            })
          }
        }
        if (isInviteesSelect) {
          // 是否被邀请人选择折扣劵
          let tempCoupons = this.coupons.filter(
            item => item.coupon_id === this.activityItem.invitees_award_id
          )
          if (tempCoupons.length === 0) {
            this.coupons.push({
              coupon_id: this.activityItem.invitees_award_id,
              coupon_name: this.activityItem.invitees_award_gift
            })
          }
        }
      }
    },
    handleCancel() {
      this.drawerflag = false
    },
    handleSave() {
      this.$refs['activityItem'].validate(valid => {
        console.log(valid)
        if (valid) {
          if (!this.activityItem.inviter_award_id) {
            return this.$Message.error('请选择邀请人达成奖励')
          }
          if (
            this.activityItem.invitees_award_type != 3 &&
            !this.activityItem.invitees_award_id
          ) {
            return this.$Message.error('请选择被邀请人达成奖励')
          }
          if (this.drawerStatus == 0) {
            this.addActivity()
          } else {
            this.editActivity()
          }
        }
      })
    },
    addActivity() {
      let postData = this.initPostData()
      return addActivity(postData).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.drawerflag = false
          this.getTab1List()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    editActivity() {
      let postData = this.initPostData()
      postData.id = this.activityItem.id
      return editActivity(postData).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.drawerflag = false
          this.getTab1List()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    initPostData() {
      let postData = {
        reach_condition: []
      }
      this.condition.forEach(item => {
        if (item.id == 2 && item.isChecked) {
          postData.reach_condition.push(5)
        } else if (item.id == 3 && item.isChecked) {
          postData.reach_condition.push(6)
        } else {
          postData.reach_condition.push(item.id)
        }
      })
      postData.name = this.activityItem.name
      postData.begin_time = this.activityItem.begin_time
      postData.end_time = this.activityItem.end_time
      postData.reach_rule = this.activityItem.reach_rule
      postData.inviter_award_type = this.activityItem.inviter_award_type
      postData.inviter_award_id = this.activityItem.inviter_award_id
      postData.invitees_award_type = this.activityItem.invitees_award_type
      postData.invitees_award_id = this.activityItem.invitees_award_id
      return postData
    },
    queryActivity(id) {
      return queryPassengerActivityDetail(id).then(res => {
        if (res.data.errorcode == 0) {
          let data = res.data.data
          this.activityItem = { ...res.data.data }
          this.activityItem.dateRange = [data.begin_time, data.end_time]
          //   this.activityItem.condition = []
          this.condition.length = 0
          this.activityItem.reach_condition.forEach(item => {
            let itemNum = parseInt(item)
            if (itemNum == 5) {
              this.condition.push({
                id: 2,
                isChecked: true
              })
            } else if (itemNum == 6) {
              this.condition.push({
                id: 3,
                isChecked: true
              })
            } else {
              this.condition.push({
                id: itemNum,
                isChecked: false
              })
            }
          })
          this.addShow = this.condition.length < 4

          this.handleConditionChange()
          this.queryCards(true)
          this.queryBusCoupon(true)
          this.drawerflag = true
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap {
    border-top: 0;
  }
}
.mgb-10 {
  margin-bottom: 10px;
}
.mgb-20 {
  margin-bottom: 20px;
}
.mgt-20 {
  margin-top: 20px;
}
.form{
    h3{
         margin-top: 50px;
        .mgb-20;
    }

}

.box {
  .tab-head {
    background-color: white;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 50px;
  }

  .total-stat {
    background-color: white;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    border-top: @border;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
}
</style>
