<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs" :value="activeIndex">
      <TabPane label="自定义人群" name="0">
       <Customize v-if="activated.includes('0')"/>
      </TabPane>
      <TabPane label="系统推荐" name="1">
        <System v-if="activated.includes('1')"/>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import Customize from "./components/Customize";
import System from "./components/System";
export default {
  name: 'TeamClass',
  components: {
    System,
    Customize
  },
  data () {
    return {
      activated: ['0'],
      activeIndex: '0'
    };
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
      sessionStorage.setItem('cardListActive', index);
      if (!this.activated.includes(index)) {
        this.activated.push(index);
      }
    }
  }
};
</script>

<style lang="less">
  .tab-table-wrap .ivu-tabs {
    min-height: 0;
  }
</style>
