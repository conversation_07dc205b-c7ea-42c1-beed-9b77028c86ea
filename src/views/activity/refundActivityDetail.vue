<template>
  <div class="table-wrap signNumberDetail">
    <header>
      <Input v-model="search" placeholder="请输入姓名或电话" @on-enter="getList()"></Input>
      <Button type="success" @click="searchList()">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" :data="tableData" disabled-hover stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Page :total="+total" :current.sync="page" placement="top" show-total show-sizer @on-change="getList()" @on-page-size-change="pageSizeChanged" />
    </footer>
    <Modal v-model="isShowModal" title="退款详情">
      <div class="detail-wrap">
        <div class="detail-tit">首次出入场时间</div>
        <div>
          <p>入场时间：{{refundInfo.enter_time}}</p>
          <p>出场时间：{{refundInfo.leave_time}}</p>
          <p>时长：{{refundInfo.duration}}</p>
        </div>
        <div class="detail-tit">退款详情</div>
        <div>
          <p>退款结果：{{statusArr[refundInfo.status]}}</p>
          <p>退款金额：{{refundInfo.curr_cost}}元</p>
          <p>交易单号：{{refundInfo.trade_sn}}</p>
          <p>业务单号：{{refundInfo.flow_sn}}</p>
        </div>
      </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>

<script>
 import Pager from 'mixins/pager';

 export default {
    name: 'refundActivityDetail',
    mixins: [Pager],
    data() {
      // 0未使用 1已使用 2已退款 3前台销卡
      
      return {
        statusArr: ['未使用', '已使用', '已退款', '前台销卡'],
        search: '',
        isShowModal: false,
        refundInfo: {},
        selection: [],
        columns: [
          {
            title: '联系人姓名',
            key: 'username'
          },
          {
            title: '联系电话',
            key: 'phone'
          },
          {
            title: '会员卡',
            key: 'card_name'
          },
          {
            title: '原价',
            key: 'ori_cost'
          },
          {
            title: '活动价',
            key: 'curr_cost'
          },
          {
            title: '购卡时间',
            key: 'buy_time'
          },
          {
            title: '状态',
            key: 'statusName'
          },
          {
            title: '操作',
            render: (h, params) => {
              const item = params.row;
              return (item.status == 2?
                <div>
                  <i-button onClick={() => this.handleShowModal(item.involved_id)} type="text">
                    退费详情
                  </i-button>
                </div>:<div>-</div>
              );
            }
          }
        ],
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      searchList() {
        this.page = 1
        this.getList()
      },
      handleShowModal(involvedId) {
        this.$service.post('/Web/Activity/involvedRefund', { involved_id: involvedId }).then((res) => {
          if (res.data.errorcode === 0) {
            this.refundInfo = res.data.data
            this.isShowModal = true
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      getList(isExport) {
        const url = '/Web/Activity/experienceInvolvedList';
        const postData = {
          search: this.search,
          activity_id: this.$route.query.id,
          page_no: isExport ? 1 : this.page,
          page_size: isExport ? this.total : this.pageSize
        };
        return this.$service
          .post(url, postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              const list = data.list.map(item => {
                return {
                  ...item,
                  statusName: this.statusArr[item.status]
                }
              })
              if(!isExport) {
                this.total = data.count;
                this.tableData = list
              }
              return list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      async exportCsv() {
        const columns = this.columns.filter((item, index) => index < this.columns.length - 1);
        const data = await this.getList(true);
        if (!data) return this.$Message.error('网络错误');
        this.$refs.table.exportCsv({
          columns,
          data,
          filename: '活动购卡列表'
        });
      }
    }
  };
</script>

<style scoped>
.detail-wrap {
  line-height: 1.75;
  .detail-tit {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
    margin-top: 10px;
  }
  p {
    text-indent: 2em;
  }
}
</style>
