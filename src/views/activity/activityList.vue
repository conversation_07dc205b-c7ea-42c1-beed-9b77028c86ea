<template>
  <div>
    <router-view v-if="$route.name !== '活动列表'"></router-view>
    <div v-else class="table-wrap activityList">
      <header>
        <Input v-model="search" placeholder="活动名称" @on-enter="getList" />
        <Button type="success" @click="searchList">搜索</Button>
      </header>
      <Table
        :columns="columns"
        :data="tableData"
        disabled-hover
        stripe
        @on-selection-change="handleSelect" />
      <footer>
        <div>
          <Button type="success" style="margin-right: 30px" @click="add">新增活动</Button>
          <Dropdown placement="top" @on-click="otherCase">
            <Button>
              其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="0">批量删除</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
        <Page
          :total="+total"
          :current.sync="page"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged" />
      </footer>
    </div>
    <Modal v-model="showActQr" title=" " :width="activityQr && activityQnjQr ?  650 : 300">
      <div class="flex-center">
        <div style="text-align: center;">
          <img :src="activityQr" style="width: 100%;margin-bottom: 12px;" alt="">
            <a v-if="activityQr" download="活动二维码" :href="activityQr" target="_blank">
              二维码下载
            </a>
        </div>
        <div v-if="activityQnjQr" style="margin-left: 30px;text-align: center;">
          <img :src="activityQnjQr" style="width: 100%;margin-bottom: 12px;" alt="">
            <a download="活动二维码 勤鸟+" :href="activityQnjQr" target="_blank">
              二维码下载
            </a>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
  </div>
</template>

<script>
  import Pager from 'mixins/pager';

  const ACTIVITY_TYPE = {
    '1': '一般活动',
    '2': '付费活动',
    '3': '会员卡抢购',
    '4': '购卡赠积分活动',
    '5': '门店体验活动',
  }

  export default {
    name: 'ActivityList',
    mixins: [Pager],
    data() {
      return {
        search: '',
        activityQr: '',
        activityQnjQr: '',
        showActQr: false,
        selection: [],
        columns: [
          {
            type: 'selection'
          },
          {
            title: '活动名称',
            key: 'name',
            render: (h, params) => {
              const item = params.row;
              let isTop =
                item.is_top == 1 ? (
                  <tag color="red" style="border-radius: 4px; margin-left: 5px; cursor: auto">
                    置顶
                  </tag>
                ) : (
                  ''
                );
              return (
                <div>
                  <span>{item.name}</span>
                  {isTop}
                </div>
              );
            }
          },
          {
            title: '活动类型',
            key: 'type',
            render: (h, { row }) => <span>{ ACTIVITY_TYPE[row.type] }</span>
          },
          {
            title: '活动时间',
            key: 'date',
            render: (h, params) => {
              let item = params.row;
              return (
                <div>
                  {item.beg_time} ~ {item.end_time}
                </div>
              );
            }
          },
          {
            title: '报名截止时间',
            key: 'sign_end_time',
            render: (h, params) => {
              const item = params.row;
              return <div>{item.cutoff_time}</div>;
            }
          },
          {
            title: '已报名/可报名人数',
            key: 'number',
            render: (h, { row }) => {
              if (row.type == 3 || row.type == 4 || row.type == 5) {
                return <span>-</span>;
              }

              return (
                <i-button
                  type="text"
                  onClick={() => { this.clickSignNumber(row.id) }}
                >
                  {row.signcount}/{row.about_number}
                </i-button>
              );
            }
          },
          {
            title: '已购卡数量/卡总量',
            key: 'number',
            render: (h, { row }) => {
              if (row.type != 3 && row.type != 5) {
                return <span>-</span>;
              }

              return (
                <i-button
                  type="text"
                  onClick={() => {
                    this.$router.push({
                      path: row.type == 5 ? '/activity/list/refundActivityDetail' : '/activity/list/sellCardDetail',
                      query: { id: row.id, type: row.type }
                    });
                  }}>
                  {row.sell_card_sum}/{row.type ==5?'-':row.card_set_total}
                </i-button>
              );
            }
          },
          {
            title: '状态',
            key: 'status',
            render: (h, params) => {
              const item = params.row;
              // 活动状态 status: 3 为关闭
              return (
                <i-switch
                  size="large"
                  value={item.status != 3}
                  on-on-change={() => {
                    this.handleSwitchChange(item.id);
                  }}>
                  <span slot="open">开启</span>
                  <span slot="close">关闭</span>
                </i-switch>
              );
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  {item.type!=='5' && <i title="二维码" class="act-qrcode" onClick={() => this.handleShowQr(item)} />}
                  <i-button
                    type="text"
                    style="margin-right: 5px"
                    onClick={() => {
                      this.clickDetail(item.id);
                    }}>
                    编辑
                  </i-button>
                  <i-button
                    type="text"
                    class="button-text-red"
                    disabled={item.sell_card_sum>0 || item.signcount>0}
                    onClick={() => {
                      this.clickDeleteBtn(item.id);
                    }}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        tableData: []
      };
    },
    watch: {
      '$route.name'(val) {
        if (val == '活动列表') {
          this.getList();
        }
      }
    },
    created() {
      this.getList();
    },
    methods: {
      handleShowQr(item) {
        this.activityQr = ''
        this.activityQnjQr = item.qnj_qrcode_path || ''
        if (!item.qrcode_path) {
          this.getActQr(item.id);
        } else {
          this.activityQr = item.qrcode_path
          // this.activityQnjQr = item.qnj_qrcode_path
          this.showActQr = true;
        }
      },

      getActQr(activity_id) {
        this.$service.post('/Web/Activity/up_activity_qrcode', { activity_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.activityQr = data.qrcode_path
            this.activityQnjQr = data.qnj_qrcode_path || this.activityQnjQr
            this.showActQr = true;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      searchList() {
        this.page = 1
        this.getList()
      },
      getList() {
        const url = '/Web/Activity/pc_get_activity_list';
        const postData = {
          name: this.search,
          page_no: this.page,
          page_size: this.pageSize
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.activity_list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      clickDetail(id) {
        this.$router.push({ path: '/activity/list/detail', query: { id } });
      },
      add() {
        this.$router.push({ path: '/activity/list/detail' });
      },
      handleSwitchChange(id) {
        const url = '/Web/Activity/ajax_status';
        this.$service
          .post(url, { id }, { loading: false })
          .then(res => {
            if (res.data.errorcode !== 0) {
              this.$Message.error(res.data.errormsg);
              this.getList();
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      clickSignNumber(id) {
        this.$router.push({ name: '报名信息', path: '/activity/list/number', query: { id } });
      },
      handleSelect(items) {
        this.selection = items.map(item => {
          return item.id;
        });
      },
      clickDeleteBtn(id) {
        this.$Modal.confirm({
          title: "删除活动",
          content: "确认删除吗？",
          onOk: () => {
            this.selection = [id];
            this.deleteList();
          }
        });
      },
      deleteList() {
        if (!this.selection.length) return this.$Message.error('请选择删除项');
        const url = '/Web/Activity/delete_activity';
        this.$service
          .post(url, { ids: this.selection })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.selection = [];
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      otherCase(val) {
        if (val === '0') {
          this.deleteList();
        }
      }
    }
  };
</script>

<style lang="less">
 .act-qrcode {
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    width: 17px;
    height: 17px;
    margin-right: 5px;
    background: url('~assets/img/qrcode.png') no-repeat;
    background-size: contain;
  }
</style>
