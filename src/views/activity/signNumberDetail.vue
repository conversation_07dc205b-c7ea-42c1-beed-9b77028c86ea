<template>
  <div class="table-wrap signNumberDetail">
    <header>
      <Input v-model="search" placeholder="请输入姓名或电话" @on-enter="getList"></Input>
      <Button type="success" @click="searchList()">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" :data="tableData" @on-selection-change="handleSelect" disabled-hover stripe></Table>
    <footer>
      <Dropdown @on-click="otherCase" placement="top">
        <Button>
          其他操作
          <Icon type="md-arrow-dropdown"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem name="0" v-if="actType == 1">批量删除</DropdownItem>
          <DropdownItem name="edit">导出列表</DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Page :total="+total" :current.sync="page" placement="top" show-total show-sizer @on-change="getList" @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import Pager from 'mixins/pager';
  export default {
    name: 'signNumberDetail',
    mixins: [Pager],
    data() {
      return {
        search: '',
        actType: 1,
        selection: [],
        columns: [
          {
            type: 'selection'
          },
          {
            title: '联系人姓名',
            key: 'realname'
          },
          {
            title: '微信昵称',
            key: 'nickname'
          },
          {
            title: '联系电话',
            key: 'phone'
          },
          {
            title: '是否是会员',
            key: 'members'
          },
          {
            title: '报名时间',
            key: 'create_time'
          },
          // {
          //   title: '分享访问量',
          //   key: 'access_count'
          // },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <i-button
                    type="text"
                    class="button-text-red"
                    disabled={this.actType==2}
                    onClick={() => {
                      this.clickDeleteBtn(item.id);
                    }}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      searchList() {
        this.page = 1
        this.getList()
      },
      getList() {
        const url = '/Web/Activity/pc_activity_sign';
        const postData = {
          search: this.search,
          id: this.$route.query.id,
          page_no: this.page,
          page_size: this.pageSize
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.activity_sign_list.map(item => {
                return { ...item, ...{ members: item.members === '1' ? '是' : '否' } };
              });
              if (this.tableData.length>0) {
                this.actType = data.activity_sign_list[0].type;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Activity/pc_activity_sign';
        const postData = {
          search: this.search,
          id: this.$route.query.id,
          page_no: 1,
          page_size: this.total
        };
        return this.$service
          .post(url, postData, { isExport: true })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.activity_sign_list.map(item => {
                return { ...item, ...{ members: item.members === '1' ? '是' : '否' } };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSelect(items) {
        this.selection = items.map(item => {
          return item.id;
        });
      },
      clickDeleteBtn(id) {
        this.selection = [id];
        this.deleteList();
      },
      deleteList() {
        if (!this.selection.length) return this.$Message.error('请选择删除项');
        const url = '/Web/Activity/delete_sign_activity';
        this.$service
          .post(url, { ids: this.selection })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.selection = [];
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      otherCase(val) {
        const events = {
          '0': () => {
            this.deleteList();
          },
          edit: () => {
            this.exportCsv();
          }
        };
        events[val]();
      },
      async exportCsv() {
        const columns = this.columns.filter((item, index) => index > 0 && index < this.columns.length - 1);
        const data = await this.getExportData();
        if (!data) return this.$Message.error('网络错误');
        this.$refs.table.exportCsv({
          columns,
          data,
          filename: '活动报名列表'
        });
      }
    }
  };
</script>

<style scoped>
</style>
