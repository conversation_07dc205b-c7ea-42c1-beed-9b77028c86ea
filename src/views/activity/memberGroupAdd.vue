<template>
  <div class="container">
    <header>
      <h3>筛选特征</h3>
    </header>
    <div class="table-wrap">
       <Form label-position="right" ref="form" :model="postData" :label-width="100">
         <FormItem label="会员" prop="theme">
          <MemberForm v-model="postData" :busId="busId" :sources="sources" :birthDateRange="birthDateRange"  :createDateRange="createDateRange"  :buyDateRange="buyDateRange"/>
        </FormItem>
         <FormItem label="预估人数" prop="count" :rules="{type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^\d+$/,message: '必须为正整数或0' }">
          <Input v-model="postData.count" class="w120" />
          <Button class="count-btn" type="success" @click="getCount">计算</Button>
        </FormItem>
         <FormItem label="人群名称" prop="title" :rules="[{required: true, message: '请填写名称', trigger: 'blur'},{max: 10, message: '名称最多10个字', trigger: 'blur'}]">
          <Input v-model="postData.title" class="w120" />
        </FormItem>
        <FormItem>
        <div class="buttons">
          <Button @click="$router.back()">取消</Button>
          <Button type="primary" @click="addGroup">保存</Button>
        </div>
      </FormItem>
       </Form>
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import MemberForm from 'src/views/member/components/MemberForm'
  import { dateDiff, formatDate } from 'utils/index.js';
  export default {
    name: 'MemberGroupAdd',
    components: {
      MemberForm
    },
    data() {
      return {
        birthDateRange: ['', ''],
        createDateRange: ['', ''],
        buyDateRange: ['', ''],
        sources: {},
        id: this.$route.query.id,
        postData: {
          count: '',
          title: '',
          buy_card_begin_time: '',
          buy_card_end_time: '',
          marketers_id: '',
          followup_coach_id: '',
          user_level: '',
          user_tag_id: '',
          user_source_id: '',
          sex: '',
          age_begin_num: null,
          age_end_num: null,
          birthday_begin_time: '',
          birthday_end_time: '',
          ub_create_begin_time: '',
          ub_create_end_time: '',
          follow_begin_num: null,
          follow_end_num: null,
          pt_follow_begin_num: null,
          pt_follow_end_num: null,
          swim_follow_begin_num: null,
          swim_follow_end_num: null,
          sign_begin_num: null,
          sign_end_num: null,
          signpt_begin_num: null, //最近未上私教|		
          signpt_end_num: null, //最近有上私教|		
          signsw_begin_num: null, //最近未上泳教|		
          signsw_end_num : null, //最近有上泳教|		
          card_buy_begin_num: null, //最近未购买|		
          card_buy_end_num : null, //最近有购买|		
          card_buy_times_logic: '1', //有效购买次数筛选方式：1--大于， 2--小于，3--区间|		
          card_buy_times_begin_num : null, //有效购买次数（开始）|		
          card_buy_times_end_num : null, // 有效购买次数（结束）|		
          card_buy_amount_logic: '1', //消费金额筛选方式：1--大于， 2--小于，3--区间|		
          card_buy_amount_begin_sum : null, //消费金额（开始）|		
          card_buy_amount_end_sum: null, // 消费金额（结束）|
          card_buy_hj_times_logic: '1', // 购买会籍卡次数筛选方式：1--大于， 2--小于，3--区间|
          card_buy_hj_times_begin_num: null, // 购买会籍卡次数（开始）|
          card_buy_hj_times_end_num: null, // 购买会籍卡次数（结束）|
          card_buy_sj_times_logic: '1', // 购买私教卡次数筛选方式：1--大于， 2--小于，3--区间
          card_buy_sj_times_begin_num: null, // 购买私教卡次数（开始）
          card_buy_sj_times_end_num: null, // 购买私教卡次数（结束）
          card_buy_yj_times_logic: '1', // 购买泳教卡次数筛选方式：1--大于， 2--小于，3--区间
          card_buy_yj_times_begin_num: null, // 购买泳教卡次数（开始）
          card_buy_yj_times_end_num: null, // 购买泳教卡次数（结束）
        }
      };
    },
    created() {
      if (this.id) {
        this.getInfo();
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    methods: {
      getCount() {
        this.$service
          .post('/Web/MemberList/new_member_list', {
            ...this.postData,
            belong_bus_id: this.busId,
            is_count: 1
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.postData.count = res.data.data.count
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      getInfo() {
        this.$service
          .post('/Web/MemberGroup/info', {id: this.id})
          .then(res => {
            if (res.data.errorcode === 0) {
              this.postData = {
                id: this.id,
                ...this.postData,
                title: res.data.data.title,
                count: res.data.data.count,
                ...res.data.data.params
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      addGroup() {
        for (const key in this.postData) {
          if(key !== 'card_buy_amount_begin_sum' && key !== 'card_buy_amount_end_sum' &&  (/^([0-9]{1,}[.][0-9]*)$/.test(this.postData[key]))) {
            this.$Message.error('除金额之外不可输入小数')
            return;
          } 
        }
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$service
              .post(this.id ? '/Web/MemberGroup/update' : '/Web/MemberGroup/create', this.postData)
              .then(res => {
                if (res.data.errorcode === 0) {
                    this.$Message.success(res.data.errormsg);
                    this.$router.back();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
          }
        });
      }
    }
  };
</script>

<style lang="less" scoped>
.table-wrap {
  padding: 15px 0;
}
  .count-btn {
    margin-left: 15px;
  }
</style>
