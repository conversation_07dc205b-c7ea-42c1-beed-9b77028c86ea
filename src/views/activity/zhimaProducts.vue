<template>
  <div>
    <div class="table-wrap">
      <header>
        <Input v-model="postData.product_title" class="w120" placeholder="产品名称" @on-enter="search" />
        <card-list placeholder="会员卡" clearable filterable v-model="postData.card_id"></card-list>
        <Select v-model="postData.status" class="w120" clearable filterable placeholder="状态">
          <Option value="0">禁用</Option>
          <Option value="1">启用</Option>
        </Select>
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>
      <main>
        <Table
          ref="table"
          class="avatar-zoom"
          :columns="columns"
          :data="tableData"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Button type="success" style="margin-right:30px" @click="goAdd">新增产品</Button>
        <Page
          class="page"
          :total="totalCount"
          :page-size="postData.page_size"
          :current.sync="postData.page_no"
          placement="top"
          @on-change="pageChanged"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </footer>
    </div>
  </div>
</template>

<script>
import cardList from 'components/card/cardList'
export default {
  name: 'zhimaProducts',
  components: {
    cardList
  },
  data() {
    return {
      statusInfoArr: [{
        key: 0,
        name: '待审批'
      }, {
        key: 1,
        name: '上架'
      }, {
        key: 2,
        name: '下架'
      }, {
        key: 3,
        name: '未通过'
      }],
      totalCount: 0,
      tableData: [],
      cardList: [],
      postData: {
        search: '',
        product_title: '',
        status: '',
        page_size: 10,
        page_no: 1
      },
      columns: [
        {
          title: '产品名称',
          key: 'product_title',
          ellipsis: true
        },
        {
          title: '卡课名称',
          key: 'card_name'
        },
        {
          title: '期数',
          key: 'periods'
        },
        {
          title: '首月服务费',
          key: 'down_payment'
        },
        {
          title: '单月服务费',
          key: 'deduction_amount'
        },
        {
          title: '类型',
          key: 'sell_type',
          render: (h, params) => {
            return (<div>连续包月</div>)
          }
        },
        {
          title: '状态',
          key: 'status',
          align: 'center',
          width: 150,
          render: (h, params) => {
            return <i-switch
              value={params.row.status}
              key={params.row.id + '1'}
              true-value={1}
              false-value={0}
              on-on-change={e => {
                this.handleStatus(params.row, e)
              }}
            />
          }
        },
        {
          title: '上架状态',
          key: 'approve_status',
          render: (h, params) => {
            return (<div>{this.statusNameByKey(params.row.approve_status)}</div>)
          }
        },
        {
          title: '操作',
          width: 120,
          render: (h, params) => {
            return (
              <div>
                <a
                  class="mr5"
                  onClick={() => {
                    this.$router.push(`/alipay/zhimaProductAdd?open_merchant_id=${params.row.open_merchant_id}&product_no=${params.row.product_no}`)
                  }}>
                  编辑
                </a>
                <a
                  class="button-text-red"
                  onClick={() => {
                    this.handleDelete(params.row)
                  }}>
                  删除
                </a>
              </div>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleStatus(info, e) {
      this.$service
        .post(
          '/Web/ZhimaFitPay/productSetStatus',
          {
            open_merchant_id: info.open_merchant_id,
            product_no: info.product_no,
            status: e
          },
          {
            loading: false
          }
        )
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.$Message.error({ content: `设置失败，${res.data.errormsg}` })
            this.getList()
          }
        })
    },
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if (key === iterator.key) {
          return iterator.name
        }
      }
    },
    handleDelete(info) {
      this.$Modal.confirm({
        title: '提示',
        content: '确定要删除吗？',
        onOk: () => {
          this.$service.post('/Web/ZhimaFitPay/productDelete', { open_merchant_id: info.open_merchant_id, product_no: info.product_no }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    goAdd() {
      this.$router.push('/alipay/zhimaProductAdd')
    },
    pageChanged(page) {
      this.postData.page_no = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.postData.page_size = size
      this.getList()
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/ZhimaFitPay/products', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data
            this.tableData = data.list
            this.totalCount = parseInt(data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
