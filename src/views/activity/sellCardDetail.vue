<template>
  <div class="table-wrap signNumberDetail">
    <header>
      <Input v-model="search" placeholder="请输入姓名或电话" @on-enter="getList"></Input>
      <Button type="success" @click="searchList()">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" :data="tableData" disabled-hover stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Page :total="+total" :current.sync="page" placement="top" show-total show-sizer @on-change="getList" @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import Pager from 'mixins/pager';
  export default {
    name: 'sellCardDetail',
    mixins: [Pager],
    data() {
      return {
        search: '',
        selection: [],
        columns: [
          {
            title: '联系人姓名',
            key: 'username'
          },
          {
            title: '微信昵称',
            key: 'nickname'
          },
          {
            title: '联系电话',
            key: 'phone'
          },
          {
            title: '会员卡',
            key: 'card_name'
          },
          {
            title: '原价',
            key: 'ori_cost'
          },
          {
            title: '活动价',
            key: 'curr_cost'
          },
          {
            title: '购卡时间',
            key: 'create_time'
          }
        ],
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      searchList() {
        this.page = 1
        this.getList()
      },
      getList() {
        const url = '/Web/Activity/sellcard_list';
        const postData = {
          search: this.search,
          id: this.$route.query.id,
          page_no: this.page,
          page_size: this.pageSize
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Activity/sellcard_list';
        const postData = {
          search: this.search,
          id: this.$route.query.id,
          page_no: 1,
          page_size: this.total,
        };
        return this.$service
          .post(url, postData, { isExport: true })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const columns = this.columns.filter((item, index) => index < this.columns.length - 1);
        const data = await this.getExportData();
        if (!data) return this.$Message.error('网络错误');
        this.$refs.table.exportCsv({
          columns,
          data,
          filename: '活动购卡列表'
        });
      }
    }
  };
</script>

<style scoped>
</style>
