<template>
  <div class="container activityAdd">
    <header>
      <h3>{{ title }}</h3>
    </header>
    <Form
      ref="form"
      label-position="right"
      class="form"
      :label-width="180"
      :rules="formRules"
      :model="form">
      <FormItem label="活动类型">
        <RadioGroup v-model="form.type" @on-change="onTypeChange">
          <Radio label="1" :disabled="!!$route.query.id">一般活动</Radio>
          <Radio label="2" :disabled="!!$route.query.id">付费活动</Radio>
          <Radio label="3" :disabled="!!$route.query.id">会员卡抢购</Radio>
          <Radio label="4" :disabled="!!$route.query.id">购卡赠积分活动</Radio>
          <Radio label="5" :disabled="!!$route.query.id">门店体验活动</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="活动名称" prop="name">
        <Input v-model="form.name" />
      </FormItem>
      <FormItem label="活动时间" prop="beg_time" :required="form.type != 5">
        <DatePicker
          v-model="form.time"
          :editable="false"
          :clearable="form.type == 5"
          :placeholder="form.type == 5 ? '不填代表不限制' : '请选择活动时间'"
          style="width: 100%"
          format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          @on-change="validDateTime" />
      </FormItem>
      <FormItem v-if="form.type != 5" label="报名截止时间" prop="cutoff_time">
        <DatePicker
          v-model="form.cutoff_time"
          :editable="false"
          style="width: 100%"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          @on-change="validDateTime" />
      </FormItem>
      <template v-if="form.type == 3">
        <Card class="bonus-card bonus-card-table" dis-hover>
          <p slot="title">会员卡设置</p>
          <a slot="extra" href="#" @click.prevent="showAdd = true">
            <Icon type="plus-circled" />
            添加卡种
          </a>
          <Table
            ref="table"
            :columns="columns"
            :data="cardListData"
            disabled-hover></Table>
        </Card>
        <FormItem
          key="buy_num_limit"
          prop="buy_num_limit"
          label="数量限制">
          <RadioGroup v-model="form.buy_num_limit">
            <Radio label="0">无限制</Radio>
            <Radio label="1">每种卡每人限购一张</Radio>
            <Radio label="2">每人限购一张</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem
          key="buy_obj_limit"
          prop="buy_obj_limit"
          label="购买对象限制">
          <RadioGroup v-model="form.buy_obj_limit">
            <Radio label="0">无限制</Radio>
            <Radio label="1">仅限潜在用户购买</Radio>
            <Radio label="2">仅限会员购买</Radio>
          </RadioGroup>
        </FormItem>
      </template>
       <div v-if="form.type == 5">
        <FormItem label="会员卡" prop="card_id">
          <Select v-model="form.card_id" filterable @on-change="refundCardChange">
            <Option v-for="item in timesCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="原价">
          {{form.ori_cost}}元
        </FormItem>
        <FormItem label="活动价" prop="curr_cost">
          <Input v-model="form.curr_cost" />
        </FormItem>
        <FormItem label="退款条件">
          <RadioGroup v-model="form.refund_condition">
            <Radio label="0">不支持退款</Radio>
            <Radio label="1">
              首次入场不满
              <InputNumber
                v-model="form.refund_time"
                style="width: 140px;"
                :min="1"
                :precision="0"
              />
              分钟可全额退款
            </Radio>
          </RadioGroup>
          <div class="tips-info">仅支持闸机出场记录离场时间</div>
        </FormItem>
        <!-- <FormItem
          key="buy_num_limit"
          prop="buy_num_limit"
          label="每人限购买数量">
            <InputNumber
              v-model="form.buy_num_limit"
              placeholder="不填代表不限制"
              :min="1"
              :precision="0"
            />
        </FormItem> -->
      </div>
      <FormItem v-if="[1, 2].includes(+form.type)" label="可报名人数" prop="about_number">
        <Input v-model="form.about_number" />
      </FormItem>
      <FormItem v-if="form.type == 2" label="报名费用" prop="signup_cost">
        <Input v-model="form.signup_cost" />
      </FormItem>
      <FormItem v-if="form.type != 4 && form.type != 5" label="置顶展示">
        <RadioGroup v-model="form.is_top">
          <Radio label="1" :disabled="form.is_top_count >= 3 && is_top == 0">是</Radio>
          <Radio label="0" :disabled="form.is_top_count >= 3 && is_top == 0">否</Radio>
        </RadioGroup>
        <Alert v-if="form.is_top_count >= 3 && is_top == 0" type="warning" style="display: inline-block">最多置顶3个活动</Alert>
      </FormItem>
      <template v-if="form.type == 4">
        <FormItem label="参加活动卡种" prop="activity_card_limit">
          <RadioGroup v-model="form.activity_card_limit">
            <Radio label="1">全部卡课</Radio>
            <Radio label="2">指定卡课</Radio>
          </RadioGroup>
          <FormItem v-if="form.activity_card_limit == 2" prop="card_data">
            <Select
              v-model="form.card_data"
              multiple
              filterable
              placeholder="请选择参加活动卡课"
            >
              <Option v-for="item in cardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
            </Select>
          </FormItem>
        </FormItem>
        <FormItem label="活动期间购买" prop="point_multiple" required>
          <InputNumber
            v-model="form.point_multiple"
            style="width: 140px;"
            :max="999999999"
            :min="1"
            :precision="1"
            :active-change="false"
          />
          <span style="margin: 0 12px;">倍积分</span>
        </FormItem>
        <FormItem label="方式" prop="buy_card_way">
          <CheckboxGroup v-model="form.buy_card_way">
            <Checkbox label="0">会员端线上购买</Checkbox>
            <Checkbox label="1">线下工作人员处购买</Checkbox>
          </CheckboxGroup>
        </FormItem>
      </template>
      <FormItem prop="thumb" v-if="form.type == 5">
        <div slot="label" class="image-description image-description-required">
          <p class="label">活动封面</p>
          <p class="tip">图片最佳尺寸: 710X170</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div v-if="form.thumb" class="activity-refund-image"><img style="width: 100%; max-height: 100%" :src="form.thumb"></div>
        <ImgUploader v-model="form.thumb" :options="{aspectRatio: 71/17}" multiple />
      </FormItem>
      <FormItem prop="thumb" v-else>
        <div slot="label" class="image-description image-description-required">
          <p class="label">活动封面</p>
          <p class="tip">图片最佳尺寸: 750X430</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div v-if="form.thumb" class="activity-image"><img style="width: 100%; max-height: 100%" :src="form.thumb"></div>
        <ImgUploader v-model="form.thumb" :options="{aspectRatio: 75/43}" multiple />
      </FormItem>
      <FormItem v-if="form.type != 5" label="活动介绍" prop="description" style="position: relative; z-index: 1">
        <FormEditor v-model="form.description" />
      </FormItem>
      <FormItem v-if="form.type != 5" prop="share-image">
        <div slot="label" class="image-description">
          <p class="label">活动分享图</p>
          <p class="tip">图片最佳尺寸: 750X1334</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div v-if="form.share_pic" class="share-image"><img style="width: 100%; max-height: 100%" :src="form.share_pic"></div>
        <Button @click="showImgUpload=true">编辑分享图</Button>
      </FormItem>
      <FormItem>
        <div class="form-bottom-buttons">
          <Button v-if="$route.query.id" type="success" @click="updateInfo">保存</Button>
          <Button v-else type="success" @click="addActivity">添加</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
    <Modal v-model="showAdd" :mask-closable="false" title="添加奖项">
      <Form
        ref="addForm"
        :model="modalData"
        class="modal-form"
        :label-width="80">
        <Form-item
          label="会员卡"
          prop="card_id"
          :rules="[
            { required: true, message: '请选择会员卡', trigger: 'change' },
            { validator(rule, cardId, callback) {
              if (cardListData.some(v => v.card_id == cardId)) {
                callback('该会员卡已添加');
              } else {
                callback();
              }
            }, trigger: 'change' }
          ]">
          <Select v-model="modalData.card_id" filterable @on-change="cardChange">
            <Option v-for="item in cardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
          </Select>
        </Form-item>
        <Form-item
          :label="modalData.is_pt_time_limit_card != 1 && (modalData.card_type_id == 4 || modalData.card_type_id == 5)?'单节售价':'原价'"
          prop="ori_cost">
          {{ modalData.is_pt_time_limit_card != 1 && (modalData.card_type_id == 4 || modalData.card_type_id == 5) ? modalData.single_price:modalData.ori_cost }}元
        </Form-item>
        <Form-item
          v-if="modalData.is_pt_time_limit_card != 1 && (modalData.card_type_id == 4 || modalData.card_type_id == 5)"
          label="课时数"
          prop="pt_class_num"
          :rules="{required: true,type: 'string',pattern: /^[1-9]\d*$/, message: '请正确填写课时数', trigger: 'blur'}">
          <Input v-model="modalData.pt_class_num" placeholder="请填写课时数" />
        </Form-item>
        <Form-item
          label="数量"
          prop="sell_num_limit"
          :rules="{required: true,type: 'string',pattern: /^[1-9]\d*$/, message: '请正确填写数量', trigger: 'blur'}">
          <Input v-model="modalData.sell_num_limit" placeholder="请填写数量" />
        </Form-item>
        <Form-item
          label="活动价"
          prop="curr_cost"
          :rules="{required: true,type: 'string',pattern: /^(?:0|[1-9]\d*)(?:\.\d{2})?$/,message: '实收金额必须大于等于0且只能保留两位小数', trigger: 'blur'}">
          <Input v-model="modalData.curr_cost" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveModal">保存</Button>
        <Button @click="showAdd = false">取消</Button>
      </div>
    </Modal>
    <ImgUploadWithTemplate v-model="showImgUpload" :options="{aspectRatio: 188/334}" @on-change="imgChange" />
  </div>
</template>

<script>
  import ImgUploader from 'components/form/cropper';
  import ImgUploadWithTemplate from 'components/form/ImgUploadWithTemplate';
  import { formatDate } from 'utils';
  import FormEditor from 'components/form/Editor';
  export default {
    name: 'ActivityAdd',
    components: { ImgUploader, FormEditor, ImgUploadWithTemplate },
    data() {
      return {
        dateOptions: {
          disabledDate(date) {
            return date.getTime() - Date.now() < -24 * 60 * 60 * 1000;
          }
        },
        cardListData: [],
        showImgUpload: false,
        showAdd: false,
        modalData: {
          is_pt_time_limit_card: 0,
          card_id: '', //卡ID
          card_nam: '', //卡名称
          card_type_id: '', //卡类型
          sell_num_limit: '', //售卖数量
          pt_class_num: '', //私教卡课时数
          ori_cost: '', // 原价
          single_price: '', // 私教单节售价
          curr_cost: '', //当前价
        },
         columns: [
          {
            type: 'index',
            width: 60,
            align: 'center'
          },
          {
            title: '会员卡名称',
            key: 'card_name'
          },
          {
            title: '数量',
            key: 'sell_num_limit'
          },
          {
            title: '原价',
            key: 'ori_cost'
          },
          {
            title: '活动价',
            key: 'curr_cost'
          },
          {
            title: '操作',
            render: (h, param) => {
              const delMe = () => {
                this.cardListData.splice(param.index, 1);
              };
              return (
                <div>
                  <i-button
                    type="text"
                    shape="circle"
                    size="small"
                    disabled={ param.row.isAdded }
                    style={{ color: param.row.isAdded ? '#bbbec4' : '#ff696a', minWidth: '0' }}
                    onClick={delMe}>
                    删除会员卡
                  </i-button>
                </div>
              );
            }
          }
        ],
        title: '编辑',
        is_top: 0,
        cardList: [],
        form: {
          type: '1',
          name: '',
          signup_cost: '',
          is_top: '0',
          beg_time: '',
          time: '',
          cutoff_time: '',
          about_number: '',
          description: '',
          share_pic: '',
          buy_num_limit: '0', //购卡数量限制 0不限 1每种卡每人限购一张 2每人限购一张 
          buy_obj_limit: '0', //购买对象限制 0不限 1仅限潜在用户购买 2仅限会员购买
          sell_card_list: '', //本活动可购卡列表的json字符串 包含多张卡列表
          /* 购卡赠积分活动 相关参数 */
          activity_card_limit: '1', // 参加活动卡种 1 全部 2 指定
          card_data: [], // 指定卡课
          point_multiple: null, // 活动积分倍数
          buy_card_way: [], // 参与活动方式
          thumb: '',
          curr_cost: '',
          refund_condition: '1',
          card_id: '', // 门店体验活动的卡id
          card_name: '',
          card_type_id: '',
          ori_cost: '',
        }
      };
    },
    computed: {
      formRules() {
        return {
          name: [{ required: true, message: '请填写活动名称' }],
          beg_time: [{ required: this.form.type != 5, message: '请选择活动时间' }],
          cutoff_time: [{ required: this.form.type != 5, message: '请选择报名截止时间' }],
          about_number: [{ required: this.form.type != 5, message: '请填写可报名人数' }],
          description: [{ required: this.form.type != 5, message: '请填写活动介绍' }],
          signup_cost: [
            { required: this.form.type != 5, type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数' }
          ],
          curr_cost: [
            { required: this.form.type == 5, type: 'string',  pattern:/^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/, message: '金额必须大于0且最多只能保留两位小数' }
          ],
          card_id: [{ required: this.form.type == 5, message: '请选择活动卡' }],
          activity_card_limit: [{ required: this.form.type != 5, message: '请选择参加活动卡种' }],
          point_multiple: [{ required: this.form.type != 5, type: 'number', message: '请填写积分倍数', trigger: 'change' }],
          card_data: [{ required: this.form.type != 5, type: 'array', min: 1, message: '至少选择一个卡/课', trigger: 'change' }],
          buy_card_way: [{ required: this.form.type != 5, type: 'array', min: 1, message: '至少选择一个方式', trigger: 'change' }],
          thumb: [{ required: true, message: '请上传活动封面' }]
        };
      },
      // 次卡
      timesCardList() {
        return this.cardList.filter(v => v.card_type_id == 2);
      },
    },
    watch: {
      showAdd(val) {
        if (!val) {
          this.modalData = {
            card_id: '', //卡ID
            card_name: '', //卡名称
            card_type_id: '', //卡类型
            pt_class_num: '', //私教卡课时数
            ori_cost: '', // 原价
            single_price: '', // 私教单节售价
            curr_cost: '', //当前价
          };
        }
      }
    },
    created() {
      if (this.$route.query.id) {
        this.getInfo();
        this.$route.meta.breadText = '活动编辑';
        this.title = '编辑';
      } else {
        this.$route.meta.breadText = '活动添加';
        this.title = '添加';
      }
      this.getCards()
    },
    methods: {
      onTypeChange(type){
        if(type==5) {
          if (!this.form.thumb) {
            this.form.thumb = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/refund-act-bg.png'
          }
          if(this.form.refund_condition == 1 && !this.form.refund_time) {
            this.form.refund_time = 15
          }
          // this.form.buy_num_limit = this.form.buy_num_limit === '0' ? null : +this.form.buy_num_limit
        } else {
          // this.form.buy_num_limit =  '0'
        }
      },
      imgChange(path) {
        this.form.share_pic = path;
      },
      validDateTime(info) {
        if (Array.isArray(info) && info[0]) {
          this.form.end_time = formatDate(info[1], 'yyyy-MM-dd HH:mm')
          this.form.beg_time = formatDate(info[0], 'yyyy-MM-dd HH:mm')
        }
        const { cutoff_time, time } = this.form;
        if (cutoff_time && time[1]) {
          if (cutoff_time.getTime() - time[1].getTime() > 0) {
            this.$Message.error({ content: '报名截止时间不能晚于活动结束时间', duration: 2.5 });
            // #11405 选择报名时间时有超过提示，但错误数据依然可以提交
            this.form.cutoff_time = '';
            return false;
          }
        }
      },
      getInfo() {
        const url = '/Web/Activity/get_activity_info';
        this.$service
          .post(url, { id: this.$route.query.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const { info } = res.data.data;
              this.is_top = info.is_top || 0;
              
              this.form = {
                ...info,
                ...{
                  description: this.unescapeHTML(info.description),
                  cutoff_time: new Date(info.cutoff_time),
                  time: info.beg_time && info.end_time ? [new Date(info.beg_time), new Date(info.end_time)] : [],
                  point_multiple: info.point_multiple ? +info.point_multiple : null,
                  card_data: info.card_data ? info.card_data.split(',') : [],
                  buy_card_way: info.buy_card_way ? info.buy_card_way.split(',') : [],
                }
              };
              // if (info.type == 5) {
              //   this.form.buy_num_limit = !info.buy_num_limit || info.buy_num_limit === '0' ? null : +info.buy_num_limit
              // }
              this.cardListData = info.card_info.map(v => {
                v.isAdded = true; // 用于区分之前已添加的卡，置灰删除会员卡按钮 #11406
                return v;
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSubmitData() {
        const { form } = this;
        let other = null;

        switch (+form.type) {
          case 3:
            other = {
              sell_card_list: JSON.stringify(this.cardListData),
            }
            break;
          case 4:
            other = {
              card_data: form.activity_card_limit == 2 ? form.card_data.join() : '',
              buy_card_way: form.buy_card_way.join(),
            }
            break;
        }

        return {
          ...form,
          ...other,
          cutoff_time: form.cutoff_time?formatDate(form.cutoff_time, 'yyyy-MM-dd HH:mm'):'',
        };
      },
      addActivity() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            return this.$Message.error('请完成信息填写');
          }
          // if (!this.validDateTime()) return;
          /* 数据准备 */
          const postData = this.handleSubmitData();

          this.$service
            .post('/Web/Activity/add_activity', postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back();
              }
              this.$Message.success(res.data.errormsg);
            })
            .catch(err => {
              console.error(err);
            });
        });
      },
      async updateInfo() {
        const valid = await this.$refs.form.validate();
        if (!valid) return this.$Message.error('请完成信息填写');
        // if (!this.validDateTime()) return;
        const postData = this.handleSubmitData();
        postData.id = this.$route.query.id;
        // console.log(postData);

        this.$service
          .post('/Web/Activity/update_activity', postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.$router.back();
            }
            this.$Message.success(res.data.errormsg);
          })
          .catch(err => {
            console.error(err);
          });
      },
      unescapeHTML(a) {
        a = '' + a;
        return a
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .replace(/&quot;/g, '"')
          .replace(/&apos;/g, "'");
      },
      saveModal() {
        if (this.modalData.is_pt_time_limit_card !=1 && (this.modalData.card_type_id == 4 || this.modalData.card_type_id == 5)) {
          this.modalData.ori_cost = this.modalData.single_price * this.modalData.pt_class_num
        }
        this.$refs.addForm.validate(val => {
          if (!val) return false;
          this.cardListData.push(this.modalData);
          this.$nextTick(() => {
            this.showAdd = false;
          });
        });
      },
      refundCardChange(cardId) {
        let selectedCard = {}
        for (let item of this.cardList) {
          if (item.card_id == cardId) {
            selectedCard = item
            break;
          }
        }
        this.form.card_id = selectedCard.card_id
        this.form.card_name = selectedCard.card_name
        this.form.card_type_id = selectedCard.card_type_id
        this.form.ori_cost = selectedCard.current_price
        if(!this.form.curr_cost) {
          this.form.curr_cost = selectedCard.current_price
        }
      },
      cardChange(cardId) {
        let selectedCard = {}
        for (let item of this.cardList) {
          if (item.card_id == cardId) {
            selectedCard = item
            break;
          }
        }
        this.modalData.card_id = selectedCard.card_id
        this.modalData.card_name = selectedCard.card_name
        this.modalData.card_type_id = selectedCard.card_type_id
        this.modalData.is_pt_time_limit_card = selectedCard.is_pt_time_limit_card
        this.modalData.ori_cost = selectedCard.current_price
        if (selectedCard.is_pt_time_limit_card !=1 && (selectedCard.card_type_id == 4 || selectedCard.card_type_id == 5)) {
          this.modalData.pt_class_num = selectedCard.pt_class_num
          this.modalData.single_price = selectedCard.single_price
        }
      },
      getCards() {
        this.$service.get('/Web/Activity/get_cards_for_activity').then(res => {
          if (res.data.errorcode === 0) {
            this.cardList = res.data.data;
          } else {
            this.cardList = [];
            this.$Message.error(res.data.errormsg);
          }
        });
      }
    }
  };
</script>

<style>
  .activity-image {
    width: 500px;
    max-height: 43 / 75 * 500px;
  }
  .activity-refund-image {
    width: 500px;
    max-height: 17 / 71 * 500px;
  }
  .share-image {
    width: 188px;
    height: 334px;
    margin-bottom: 10px;
  }
  .bonus-card-table .ivu-card-body {
    padding: 0;
  }
</style>
<style lang="less" scoped>
.bonus-card {
  margin: 0 0 24px 50px;
}

.tips-info {
  font-size: 12px;
  color: #888;
}
</style>
