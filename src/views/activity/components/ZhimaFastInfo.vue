<template>
  <Modal v-model="showAdd" width="800" :mask-closable="false" title="签约快照">
    <Form label-position="right" ref="form" :model="info" class="form" :label-width="80">
      <FormItem label="产品名称">
        <div class="class-text">{{info.product_title}}</div>
      </FormItem>
      <FormItem label="会员卡名称">
        <div class="class-text">{{info.card_name}}</div>
      </FormItem>
      <FormItem label="会员卡详情">
        <div class="class-text">购买天数： {{info.product_info.purchase_volume}}天，赠送天数：{{info.product_info.gift_volume}}天</div>
      </FormItem>
      <FormItem label="产品类型">
        <div class="class-text">连续包月</div>
      </FormItem>
      <FormItem label="签约期数">
        <div class="class-text">{{info.periods}}</div>
      </FormItem>
      <FormItem label="首期金额">
        <div class="class-text">{{info.down_payment}}</div>
      </FormItem>
      <FormItem label="单期金额">
        <div class="class-text">{{info.deduction_amount}}</div>
      </FormItem>
      <FormItem label="产品总额">
        <div class="class-text">{{info.total_amount}}</div>
      </FormItem>
      <FormItem label="扣款日期">
        <div class="class-text">签约后 {{info.down_payment_date_rule}} 天扣款</div>
      </FormItem>
      <FormItem label="扣款周期">
        <div class="class-text">{{info.period_day}} 天</div>
      </FormItem>
      <div class="tips">此卡签署协议成功后立即开卡，后续按照扣款周期进行扣款</div>
  </Form>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>
<script>
export default {
  name: 'ZhimaFastInfo',
  data() {
    return {
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  created() { },
  methods: {
  }
}
</script>
<style lang="less" scoped>
.class-text{
  font-size: 12px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tips {
  text-indent: 20px;
  color: #aaa;
}
</style>
