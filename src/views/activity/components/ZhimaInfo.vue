<template>
  <Modal v-model="showAdd" width="800" :mask-closable="false" title="扣款计划">
    <Table disabled-hover :columns="cardColumns" :data="info.items" ref="table"></Table>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>
<script>
export default {
  name: 'ZhimaInfo',
  data() {
    return {
      statusInfoArr: [{
        key: 'PAUSED',
        name: '已暂停'
      }, {
        key: 'ORDERING',
        name: '下单中'
      }, {
        key: 'ORDERED',
        name: '下单成功'
      }, {
        key: 'PAID',
        name: '支付成功'
      }, {
        key: 'PAY_FAILED',
        name: '扣款失败'
      }, {
        key: 'REFUNDED',
        name: '已全额退款'
      }, {
        key: 'UNCREATED',
        name: '未生成订单'
      }, {
        key: 'CANCEL',
        name: '已取消'
      }],
      tableData: [],
      cardColumns: [{
        title: '期数',
        key: 'period'
      },{
        title: '计划扣款日期',
        key: 'plan_deduction_time'
      },{
        title: '实际扣款日期',
        key: 'actual_deduction_time'
      },{
        title: '扣款金额',
        key: 'deduction_amount'
      },{
        title: '订单编号',
        key: 'order_no'
      },{
        title: '订单状态',
        key: 'status',
        render: (h, params) => {
          return (<div>{this.statusNameByKey(params.row.status)}</div>)
        }
      },
      // {
      //     title: '操作',
      //     width: 125,
      //     render: (h, params) => {
      //       return (
      //         <div>
      //           {params.row.status=='ORDERED' || params.row.status=='UNCREATED'? (<a
      //             class="button-text-red"
      //             onClick={() => {
      //               this.cancelOrder(params.row)
      //             }}>
      //             取消订购
      //           </a>):''}
      //         </div>
      //       )
      //     }
      //   }
        ],
      postData: {
        items: [],
        merchant_pid: '',
        smid: '',
        merchant_name: '',
        ledger_rate: '',
        phone: '',
        comment: ''
      }
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  created() { },
  methods: {
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if(key === iterator.key) {
          return iterator.name
        }
      }
    },
    cancelOrder(obj) {
      this.$service
        .post('/Web/ZhimaFitPay/subscriptionSpecificCancel', {
          open_merchant_id: this.info.open_merchant_id,
          store_no: this.info.store_no,
          subscription_no: this.info.subscription_no,
          periods: obj.period
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.showAdd = false
            this.$emit('on-success')
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style scoped>
</style>
