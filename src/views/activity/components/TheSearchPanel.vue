<template>
  <div class="search-panel">
    <Card>
      <div class="search-line">
        <div class="search-item">
          <div class="label">合约编号</div>
          <Input v-model="searchParams.subscription_no" placeholder="请输入合约编号" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">月付方案</div>
          <Input v-model="searchParams.product_title" placeholder="请输入月付方案名称" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">签约会员</div>
          <Input v-model="searchParams.phone" placeholder="请输入会员手机号码" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">签约门店</div>
          <Select v-model="searchParams.bus_id" placeholder="请选择门店" class="value" transfer>
            <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="search-item">
          <div class="label">{{ dateLabel }}</div>
          <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
            transfer />
        </div>
        <div class="search-item">
          <Button type="primary" @click="handleSearch">查询</Button>
        </div>
        <div class="search-item">
          <Button @click="handleReset">重置</Button>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 20px">
      <Table :columns="columns" :data="list"></Table>
      <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
        @on-page-size-change="handlePageSizeChange" show-total show-sizer style="margin-top: 10px; text-align: right">
      </Page>
    </Card>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  subscription_no: '',
  product_title: '',
  phone: '',
  status: '', // 合约状态 履约中-NORMAL 扣款失败-PAY_FAILED 合约暂停-PAUSED 合约终止-SURRENDER
  time_begin: '',
  time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TheSearchPanel',
  props: {
    category: {
      type: String,
      required: true
    },
    tabList: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      list: [],
      total: 0,
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
    dateLabel() {
      if (this.category === this.tabList[0]) {
        return '签约时间'
      } else if (this.category === this.tabList[1]) {
        return '扣款时间'
      } else if (this.category === this.tabList[2]) {
        return '合约取消时间'
      } else if (this.category === this.tabList[3]) {
        return '合约暂停时间'
      } else {
        return '时间'
      }
    },
    columns() {
      const chunk = [
        {
          title: '序号',
          type: 'index',
          width: 80
        },
        {
          title: '合约编号',
          key: 'subscription_no'
        },
        {
          title: '月付方案',
          key: 'product_title'
        },
        {
          // HACK: link to member detail
          title: '签约会员',
          key: 'username'
        },
        {
          title: '签约门店',
          key: 'bus_name'
        },
        {
          title: '签约销售',
          key: 'sale_name'
        },
        {
          title: '签约时间',
          key: 'sign_time'
        },
        {
          title: '合约状态',
          key: 'status',
          render: (h, params) => {
            const status = params.row.status
            const statusMap = {
              NORMAL: '合约生效中',
              PAY_FAILED: '扣款失败',
              PAUSED: '合约暂停',
              SURRENDER: '合约终止'
            }
            let color = 'black'
            if (status === this.tabList[0]) {
              color = 'green'
            } else if (status === this.tabList[1]) {
              color = 'red'
            } else if (status === this.tabList[2]) {
              color = 'orange'
            } else if (status === this.tabList[3]) {
              color = 'blue'
            }
            return h('span', { style: { color } }, statusMap[status])
          }
        },
      ]
      const action = {
        title: '操作',
        render: (h, params) => {
          return h('div', [
            h(
              'Button',
              {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  // route to detail
                  click: () => {
                    this.$router.push({
                      name: '合约详情',
                      params: {
                        busId: this.searchParams.bus_id,
                        subscriptionNo: params.row.subscription_no,
                        aliUserId: params.row.ali_user_id
                      }
                    })
                  }
                }
              },
              '查看'
            )
          ])
        }
      }
      const cols = []
      if (this.category === this.tabList[0]) {
        cols.push(...chunk, action)
      } else if (this.category === this.tabList[1]) {
        cols.push(...chunk, {
          title: '扣款时间',
          key: 'actual_deduction_time'
        }, action)
      } else if (this.category === this.tabList[2]) {
        cols.push(...chunk, {
          title: '合约取消时间',
          key: 'surrender_time'
        }, action)
      } else if (this.category === this.tabList[3]) {
        cols.push(...chunk, {
          title: '合约暂停时间',
          key: 'paused_time'
        }, action)
      }
      return cols
    }
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.time_begin = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.time_begin = ''
        this.searchParams.time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.daterange = []
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.getList()
    },
    getList() {
      this.searchParams.status = this.category
      return this.$service.post('/Web/ZhimaFitPay/contracts', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          this.$emit('emitCount', this.total, this.category)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
  },
  created() {
    const lazyTime = this.lazy ? 1000 : 0
    setTimeout(async () => {
      // 获取场馆列表
      !this.adminBusList && await this.getAdminBusList()
      this.searchParams.bus_id = this.busId
      this.getList()
    }, lazyTime)
  },
  activated() {
    if (this.$route.params.refresh == 1) {
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.search-panel {
  padding: 20px 0;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;
      margin: 10px;

      .value {
        margin-left: 10px;
        width: 200px;
      }
    }
  }
}
</style>
