<template>
    <div class="table-wrap">
        <header>
            <Input class="w120" v-model="postData.search" placeholder="人群名称" @on-enter="handleSearch"></Input>
            <Button type="success" @click="handleSearch">搜索</Button>
        </header>
        <main>
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="tableData"
                   ref="table"></Table>
        </main>
        <footer>
          <Button type="success" @click="goAdd">新建人群</Button>
        </footer>
    </div>
</template>
<script>
export default {
  name: 'Customize',
  data() {
    return {
      isSwim: false,
      showClassDetail: false,
      curId: '',
      postData: {
        page_no: 1,
        page_size: 99,
        search: '',
        type: 1
      },
      cardColumns: [
        {
          title: '人群名称',
          key: 'title'
        },
        {
          title: '人群描述',
          key: 'description',
          tooltip: true
        },
        {
          title: '人群数量',
          key: 'count',
          render: (h, params) => {
            return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.goMember(params.row)
                  }}
                >
                  {params.row.count}
                </a>
            )
          }
        },
        {
          title: '更新时间',
          key: 'update_time'
        },
        {
          title: '操作人',
          key: 'admin_name'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            const row = params.row
            return (
              <div>
                <i-button
                  type="text"
                  class="mr5"
                  onClick={()=>{
                    this.goMember(row)
                  }}
                >
                  查看客户
                </i-button>
                <i-button
                  type="text"
                  class="mr5"
                  onClick={() => {
                    this.$router.push(
                      `/activity/memberGroupAdd?id=${row.id}`
                    )
                  }}
                >
                  编辑
                </i-button>
               <i-button
                  type="text"
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={()=>{
                    this.deleteClass(row.id)
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      allCourses: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  watch: {
  },
  computed: {},
  methods: {
    deleteClass(id) {
      this.$Modal.confirm({
        title: '提示',
        content: '是否确定要删除？',
        onOk: () => {
          this.$service.post('Web/MemberGroup/delete', { id: id }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    goAdd() {
      if(this.total>=10) {
        this.$Message.error('已达到最大可新建数量10个')
      } else {
        this.$router.push('/activity/memberGroupAdd');
      }
    },
    goMember(info) {
      this.$service
        .post('/Web/MemberGroup/updateCount', {id:info.id})
        .then(res => {
          this.$router.push(`/member?curMenu=${info.group||'会籍会员'}&fastMenu=${info.title}`);
        }).catch(()=>{
          this.$router.push(`/member?curMenu=${info.group||'会籍会员'}&fastMenu=${info.title}`);
        })
      
    },
    updateClassStatus(id, status) {
      this.$service.post(this.isSwim?'/Web/teamclassSwim/teamclass_graduation':'Web/TeamclassPrivate/teamclass_graduation', { teamclass_id: id, status: status }).then(res => {
        if (res.data.errorcode === 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getList() {
      return this.$service
        .post('/Web/MemberGroup/list', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.total = parseInt(res.data.data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleWhichPage(postData) {
      this.postData = { ...this.postData, ...postData }
      this.getList()
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    handleDelete(idArr, callback) {
      return this.$service
        .get(`/Web/Card/delete_cards/ids/${idArr}`)
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success({ content: '删除成功' })
              callback()
            } else {
              this.$Message.error({ content: `删除失败，${res.data.errormsg}` })
            }
          } else {
            console.log('服务器扑街！')
          }
        })
    }
  }
}
</script>
