<template>
    <div class="table-wrap">
        <header>
            <Input class="w120" v-model="postData.search" placeholder="人群名称" @on-enter="handleSearch"></Input>
            <Button type="success" @click="handleSearch">搜索</Button>
        </header>
        <main>
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="tableData"
                   ref="table"></Table>
        </main>
        <footer>
          <Pager name="System" :history="false" :total="+total" :postData="postData" @on-change="handleWhichPage" />
        </footer>
    </div>
</template>
<script>
import Pager from 'components/pager'
export default {
  name: 'System',
  components: {
    Pager
  },
  data() {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
        search: '',
        type: 2
      },
      cardColumns: [
        {
          title: '人群名称',
          key: 'title'
        },
        {
          title: '人群描述',
          key: 'description',
          tooltip: true
        },
        {
          title: '人群数量',
          key: 'count',
          render: (h, params) => {
            return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.goMember(params.row)
                  }}
                >
                  {params.row.count}
                </a>
            )
          }
        },
        {
          title: '更新时间',
          key: 'update_time'
        },
        {
          title: '操作人',
          key: 'admin_name'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            const row = params.row
            return (
              <div>
                <i-button
                  type="text"
                  class="mr5"
                  onClick={()=>{
                    this.goMember(row)
                  }}
                >
                  查看客户
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  watch: {
  },
  computed: {},
  methods: {
    getList() {
      return this.$service
        .post('/Web/MemberGroup/list', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.total = parseInt(res.data.data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    goMember(info) {
      this.$service
        .post('/Web/MemberGroup/updateCount', {id:info.id})
        .then(res => {
          this.$router.push(`/member?curMenu=${info.group}&fastMenu=${info.title}`);
        }).catch(()=>{
          this.$router.push(`/member?curMenu=${info.group}&fastMenu=${info.title}`);
        })
      
    },
    handleWhichPage(postData) {
      this.postData = { ...this.postData, ...postData }
      this.getList()
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    }
  }
}
</script>
