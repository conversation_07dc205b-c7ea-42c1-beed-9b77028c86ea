<template>
  <div class="table-wrap deposit">
    <header>
      <Input v-model="postData.search" style="width: 180px" placeholder="名称/手机号" />
      <Select v-model="postData.user_type" class="w120 group-select">
        <Option :value="0">全部</Option>
        <Option :value="1">潜客</Option>
        <Option :value="2">会员</Option>
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <main>
      <section class="statistics">
        <div class="analysis" v-for="(item, index) in statisticData" :key="index">
          <Card>
            <p class="label" slot="title" :title="item.label">{{item.label}}</p>
            <p class="value">{{item.value}}</p>
          </Card>
        </div>
      </section>
      <section class="options">
        <Button type="success" @click="sendMsg">发送短信</Button> 
        <Dropdown placement="top" style="margin: 0 30px" @on-click="otherCase">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown" />
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="excel">导出Excel</DropdownItem>
          </DropdownMenu>
        </Dropdown> 
      </section>
      <section>
        <Table
          ref="table"
          :columns="columns"
          :data="tableData"
          disabled-hover
          @on-select="selectMember"
          @on-selection-change="selectMemberChange"
          @on-select-all="selectMemberAll"
          @on-select-cancel="selectMemberCancel"></Table>
      </section>
    </main>
    <footer>
      <!-- <div style="display: flex">
        <Button type="success" @click="sendMsg">发送短信</Button>
        <Dropdown placement="top" style="margin: 0 30px" @on-click="otherCase">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown" />
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="excel">导出Excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <div style="height: 32px;line-height: 32px;">
          {{ receiveDetailText }}
        </div>
      </div> -->
      <Page
        :total="totalCount"
        :current.sync="postData.page_no"
        show-total
        show-sizer
        placement="top"
        @on-change="getList(false)"
        @on-page-size-change="pageSizeChanged"></Page>
    </footer>
  </div>
</template>

<script>
  // import { getNewHost } from 'utils/config';
  // import { formatDate } from 'utils';

  const PRIZE_TYPE = ['红包', '体验卡', '折扣券', '体验课', '积分'];

  export default {
    name: 'BonusRecord',
    data() {
      return {
        selectedMembers: [],
        postData: {
          bonus_id: this.$route.params.bonusId,
          search: '',
          user_type: 0,
          page_no: 1,
          page_size: 10
        },
        dateRange: [],
        status: '',
        resData: '',
        tableData: [],
        exportData: [],
        totalCount: 0,
        depositChart: null,
        showBackMoney: false,
        takeDeposit: false,
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '类型',
            key: 'is_user',
            render: (h, param) => {
              return h('span', param.row.is_user === 1 ? '会员' : '潜客');
            }
          },
          {
            title: '姓名',
            key: 'nickname',
            width: 200,
            render: (h, params) => {
              if (params.row.user_id) {
                return h('a', {
                  on: {
                    click: () => {
                      this.$router.push(`/member/detail/${params.row.user_id}`)
                    }
                  }
                }, params.row.nickname);
              } else {
                return h('span', params.row.nickname);
              }
            }
          },
          // {
          //   title: '头像',
          //   key: 'image',
          //   className: 'avatar-wrap',
          //   render: (h, params) => {
          //     return <img class="avatar" src={params.row.image}/>;
          //   }
          // },
          {
            title: '电话号码',
            key: 'phone'
          },
          {
            title: '领奖时间',
            key: 'award_time'
          },
          {
            title: '类型',
            key: 'type_text'
          },
          {
            title: '明细',
            key: 'definite',
            render: (h, { row }) => {
              const { type, definite } = row;
              const unit = type == 1 ?  '元' : type == 5 ? '积分' : '';
              return h('span', `${definite}${unit}`);
            }
          },
        ],
        // statistic data
        statisticData: []
      };
    },
    computed: {
      selectedUserIds() {
        return this.selectedMembers.map(user => user.phone);
      },
      // receiveDetailText() {
      //   const { resData } = this;
      //   if (!(resData && resData.detail_list)) {
      //      return ''
      //   }

      //   const {
      //     get_money,
      //     total_get, // 红包总领取个数
      //     get_cards,
      //     get_class,
      //     get_coupon,
      //     get_point,

      //     total_money,
      //     total_payout_num, // 红包总派发个数
      //     total_cards,
      //     total_class,
      //     total_coupon,
      //     total_point,
      //   } = resData.detail_list;
      //   const {
      //     count_member,
      //     count_potential
      //   } = resData.record;

      //   return `
      //     已领取${ total_get }/${ total_payout_num }个，
      //     现金红包${ get_money }/${ total_money }元、
      //     体验卡${ get_cards }/${ total_cards }张、
      //     体验课${ get_class }/${ total_class }张、
      //     折扣券${ get_coupon }/${ total_coupon }张、
      //     积分${ get_point }/${ total_point }积分，
      //     其中会员${ count_member }人、
      //     潜客${ count_potential }人
      //   `
      // }
    },

    created() {
      if (this.$route.params.bonusId) {
        this.getList();
      }
    },

    methods: {
      selectMember(selection, member) {
        if (!this.selectedUserIds.includes(member.phone)) {
          this.selectedMembers.push(member);
        }
      },
      selectMemberAll(selection) {
        if (selection.length > 0) {
          selection.forEach(member => {
            this.selectMember(selection, member);
          });
        }
      },
      selectMemberCancel(selection, member) {
        const index = this.selectedMembers.findIndex(user => user.phone === member.phone);
        this.selectedMembers.splice(index, 1);
      },
      selectMemberChange(selection) {
        if (selection.length === 0) {
          this.tableData.forEach(member => {
            this.selectMemberCancel(selection, member);
          });
        }
      },
      sendMsg() {
        if (this.selectedUserIds.length > 0) {
          this.$router.push({
            name: '消息推送',
            params: {
              from: 'bonus',
              activeIndex: '2',
              selectedMembers: this.selectedMembers.map(item => {
                return { ...item, user_id: item.phone, username: item.nickname };
              })
            }
          });
        } else {
          this.$Message.error('请先选择需要发送短信的人员');
        }
      },
      doSearch() {
        this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.getList();
      },
      async exportList() {
        let resData = await this.getList(this.totalCount);
        let exportData = resData.map(item => {
          return Object.assign({}, item, {
            type: PRIZE_TYPE[item.type - 1],
            nickname: item.nickname.replace(/,/g, '，'),
            is_user: item.is_user === 1 ? '会员' : '潜在客户',
            definite: `${item.definite}${item.type == 1 ?  '元' : item.type == 5 ? '积分' : ''}`
          });
        });
        this.$refs.table.exportCsv({
          filename: '红包记录',
          columns: this.columns.filter(col => col.key && col.key !== 'image'),
          data: exportData
        });
      },
      getList(allPageCount) {
        let postObj = Object.assign({}, this.postData, { isExport: !!allPageCount });
        if (allPageCount) {
          postObj.page_size = allPageCount;
          postObj.page_no = 1;
        }
        return this.$service.post('/Web/Bonus/get_award_record', postObj, { isExport: !!allPageCount }).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data;
            this.resData = resData;
            if (!allPageCount) {
              this.tableData = resData.record.list.map(item => {
                return {
                  ...item,
                  _disabled: !item.phone,
                  _checked: this.selectedUserIds.includes(item.phone)
                };
              });
              this.totalCount = parseInt(resData.record.count);
            }

            // statistics data
            const {
              get_money,
              total_get, // 红包总领取个数
              get_cards,
              get_class,
              get_coupon,
              get_point,

              total_money,
              total_payout_num, // 红包总派发个数
              total_cards,
              total_class,
              total_coupon,
              total_point,
            } = resData.detail_list;
            const {
              count_member,
              count_potential
            } = resData.record;
            this.statisticData = [];
            this.statisticData.push({
              label: '领取个数',
              value: `${ total_get }/${ total_payout_num }`
            })
            this.statisticData.push({
              label: '现金红包（元）',
              value: `${ get_money }/${ total_money }`
            })
            this.statisticData.push({
              label: '体验卡（张）',
              value: `${ get_cards }/${ total_cards }`
            })
            this.statisticData.push({
              label: '体验课（次）',
              value: `${ get_class }/${ total_class }`
            })
            this.statisticData.push({
              label: '折扣券（张）',
              value: `${ get_coupon }/${ total_coupon }`
            })
            this.statisticData.push({
              label: '积分',
              value: `${ get_point }/${ total_point }`
            })
            this.statisticData.push({
              label: '会员领取人数',
              value: count_member
            })
            this.statisticData.push({
              label: '潜在客户领取人数',
              value: count_potential
            })

            return resData.record.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.postData.page_no = 1;
        this.getList();
      },
      otherCase(val) {
        ({
          'excel': () => this.exportList()
        }[val]());
      }
    }
  };
</script>

<style lang="less" scoped>
.statistics {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  height: 150px;
  margin: 0 30px;
  overflow-x: auto;

  .analysis {
    width: 200px;
    min-width: 130px;

    .label {
      text-align: center;
      font-size: 14px;
      font-weight: bold;
      color: #333333;
    }

    .value {
      font-size: 12px;
      font-weight: bold;
      color: #2C3945;
      text-align: center;
    }
  }
}

.options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 30px;
  height: 50px;
}
</style>
