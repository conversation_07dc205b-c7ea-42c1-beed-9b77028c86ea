<template>
  <div>
    <div v-if="$route.name !== '营销红包'">
      <router-view></router-view>
    </div>
    <div v-else class="table-wrap deposit">
      <header>
        <Input v-model="postData.search" style="width: 180px" placeholder="名称" />
        <Button type="success" @click="doSearch">搜索</Button>
      </header>
      <Table
        ref="table"
        :columns="columns"
        :data="tableData"
        disabled-hover></Table>
      <footer>
        <div style="display: flex">
          <Button type="success" style="margin-right: 30px" @click="goAdd">新增红包</Button>
        </div>
        <Page
          :total="totalCount"
          :current.sync="postData.page_no"
          show-total
          show-sizer
          placement="top"
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"></Page>
      </footer>
      <Modal title="红包详情" width="750" v-model="showDetailModal" @on-cancel="curDetailInfo=''">
        <!-- <div class="modal-tit">
          {{ receiveDetailText }}
        </div> -->
        <div class="grid-new66">
          <div class="item-new66" v-for="item in receiveDetailText" :key="item">{{ item }}</div>
        </div>
        <Table
          v-if="curDetailInfo && curDetailInfo.award"
          class="gift-table"
          :columns="giftColumns"
          :data="curDetailInfo.award"
          :show-header="true"
          disabled-hover></Table>
        <!-- <div v-if="curDetailInfo && curDetailInfo.record && curDetailInfo.record.length>0" class="modal-tit modal-line">
          最近10条记录
        </div>
        <Table
          v-if="curDetailInfo && curDetailInfo.record && curDetailInfo.record.length>0"
          class="gift-table"
          :columns="recordColumns"
          :data="curDetailInfo.record"
          :show-header="false"
          disabled-hover></Table>
        <div slot="footer"></div> -->
      </Modal>
      <Modal v-model="showBonusQr" title=" " width="300">
        <img :src="bonusQr" style="width: 100%" alt="">
        <div slot="footer" class="modal-buttons">
          <a v-if="bonusQr" download="红包二维码" :href="bonusQr">
            <Button type="success">印刷尺寸下载</Button>
          </a>
          <Button style="display: none"></Button>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
  import axios from 'axios';

  const PRIZE_TYPE = ['红包', '体验卡', '折扣券', '体验课', '积分'];

  export default {
    name: 'BonusList',
    data() {
      return {
        showBonusQr: false,
        bonusQr: '',
        postData: {
          search: '',
          page_no: 1,
          page_size: 10
        },
        dateRange: [],
        tableData: [],
        giftData: [],
        totalCount: 0,
        curDetailInfo: null,
        showDetailModal: false,
        recordColumns: [
          {
            title: '头像',
            width: 76,
            render: (h, param) => {
              return <img class="image" src={param.row.image} width="60" height="60"/>;
            }
          },
          {
            title: '昵称',
            key: 'nickname'
          },
          {
            title: '时间',
            key: 'time'
          },
          {
            title: '分类',
            key: 'type',
            render: (h, param) => {
              const item = param.row;
              let type = PRIZE_TYPE[item.type - 1];
              return <div>{type}</div>;
            }
          },
          {
            title: '详情',
            key: 'definite',
            render: (h, { row }) => {
              const { type, definite } = row;
              const unit = type == 1 ?  '元' : type == 5 ? '积分' : '';
              return h('span', `${definite}${unit}`);
            }
          }
        ],
        giftColumns: [
          {
            title: '奖品',
            width: 60,
            render: (h, param) => {
              return h('span', `奖品${param.row._index + 1}`);
            }
          },
          {
            title: '奖品类型',
            key: 'type',
            render: (h, param) => {
              const item = param.row;
              let type = PRIZE_TYPE[item.type - 1];
              return <div>{type}</div>;
            }
          },
          {
            title: '奖品名称',
            key: 'definite',
            render: (h, { row }) => {
              const { type, definite } = row;
              const unit = type == 1 ?  '元' : type == 5 ? '积分' : '';
              return h('span', `${definite}${unit}`);
            }
          },
          {
            title: '统计数量',
            render: (h, { row }) => {
              const text = row.type == 5
                ? `${row.get_num}/${row.definite}积分` // fix: 14075 积分红包详情 - 积分奖品领取个数占比，调整为领取积分量占比
                : `${row.get_num}/${row.payout_num}`
              return h('span', text);
            }
          }
        ],
        columns: [
          {
            title: '名称',
            key: 'theme',
            width: 200
          },
          {
            title: '开始时间',
            key: 'begin_day'
          },
          {
            title: '结束时间',
            key: 'end_day'
          },
          {
            title: '红包剩余量',
            render: (h, param) => {
              return (
                <a
                  onClick={() => {
                    this.curDetailInfo = param.row.detail_list;
                    this.showDetailModal = true;
                  }}>
                  {param.row.remain_num}/{param.row.total_payout_num}
                </a>
              );
            }
          },
          {
            title: '领奖人数/参与人数',
            key: 'rcount',
            render: (h, param) => {
              let dom = (
                <a
                  onClick={() => {
                    this.$router.push(`/activity/bonusList/bonusRecord/${param.row.bonus_id}`);
                  }}>
                  {param.row.rcount}
                </a>
              );
              if (!param.row.rcount) {
                dom = <span>{param.row.rcount}</span>;
              }
              return dom;
            }
          },
          {
            title: '状态',
            key: 'status',
            render: (h, param) => {
              return <span>{param.row.status == 1 ? '已启用' : '不在有效期'}</span>;
            }
          },
          {
            title: ' ',
            width: 80,
            render: (h, param) => {
              const item = param.row;
              return <faIcon name="qrcode" color="#52a4ea" style="cursor: pointer"
                             onClick={() => this.handleShowQr(item.bonus_id)}/>;
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, param) => {
              const goDetail = () => {
                this.$router.push(`/activity/bonusList/bonusEdit/${param.row.bonus_id}`);
              };
              const delMe = () => {
                this.handleDelete(param.row.bonus_id);
              };
              return (
                <div>
                  <i-button
                    type="text"
                    shape="circle"
                    size="small"
                    style={{ color: '#52a4ea', minWidth: '0', marginRight: '20px' }}
                    onClick={goDetail}>
                    编辑
                  </i-button>
                  <i-button
                    type="text"
                    shape="circle"
                    size="small"
                    style={{ color: '#ff696a', minWidth: '0' }}
                    onClick={delMe}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ]
      };
    },
    computed: {
      receiveDetailText() {
        const { curDetailInfo } = this;
        if (!(curDetailInfo && curDetailInfo.detail)) {
          //  return ''
          return []
        }

        const {
          get_money,
          total_get, // 红包总领取个数
          get_cards,
          get_class,
          get_coupon,
          get_point,

          total_money,
          total_payout_num, // 红包总派发个数
          total_cards,
          total_class,
          total_coupon,
          total_point,
        } = curDetailInfo.detail;

        // return `
        //   已领取${ total_get }/${ total_payout_num }个，
        //   其中现金${ get_money }/${ total_money }元、
        //   体验卡${ get_cards }/${ total_cards }张、
        //   体验课${ get_class }/${ total_class }张、
        //   折扣券${ get_coupon }/${ total_coupon }张、
        //   积分${ get_point }/${ total_point }积分
        // `
        return [
          `红包领取 ${ total_get } / ${ total_payout_num } 个`,
          `体验卡领取 ${ get_cards } / ${ total_cards } 张`,
          `现金领取 ${ get_money } / ${ total_money } 元`,
          `折扣券领取 ${ get_coupon } / ${ total_coupon } 张`,
          `体验课领取 ${ get_class } / ${ total_class } 张`,
          `积分领取 ${ get_point } / ${ total_point } 积分`,
        ]
      }
    },

    watch: {
      '$route.name'(_, lastName) {
        if (['红包添加', '红包编辑', '领奖记录'].includes(lastName)) {
          this.getList();
        }
      }
    },

    activated() {
      if (this.$route.name === '营销红包') {
        this.getList();
      }
    },

    methods: {
      handleShowQr(id) {
        URL.revokeObjectURL(this.bonusQr);
        this.getBonusQr(id);
        this.showBonusQr = true;
      },
      getBase64(url) {
        axios.get(url, { responseType: 'blob' }).then(res => {
          if (res.status === 200) {
            const blob = res.data;
            this.bonusQr = URL.createObjectURL(blob);
          } else {
            this.$Message.error('网络错误');
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      getBonusQr(bonus_id) {
        const url = '/Web/Bonus/bonusQrCode';
        this.$service.post(url, { bonus_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.getBase64(data.qrcode_path);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      goAdd() {
        this.$router.push('/activity/bonusList/bonusAdd');
      },
      doSearch() {
        // this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        return this.$service.post('/Web/Bonus/get_bonus_list', this.postData).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data;
            this.tableData = resData.bonus_list;
            this.totalCount = parseInt(resData.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleDelete(delId) {
        let _this = this;
        this.$Modal.confirm({
          title: '',
          content: '确定要删除红包吗？',
          onOk() {
            this.$service.post('/Web/Bonus/delete_bonus', { bonus_id: delId }).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                _this.getList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          }
        });
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.postData.page_no = 1;
        this.getList();
      }
    }
  };
</script>

<style>
  .gift-table .ivu-table::before,
  .gift-table .ivu-table::after {
    height: 0;
  }
</style>

<style lang="less" scoped>
  @border: 1px solid #dcdcdc;

  .total-stat {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px;
    height: 150px;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }

  .back-money {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    padding: 50px 0;
  }

  footer {
    display: flex;
    justify-content: space-between;
  }

  .gift-table {
    margin-top: 15px;
  }

  .image {
    width: 60px;
    height: 60px;
  }

  .modal-tit {
    font-size: 12px;
    font-weight: bold;
    color: rgb(70, 76, 91);
  }

  .modal-line {
    margin-top: 15px;
    border-top: 1px dashed #9ea7b4;
    padding-top: 15px;
  }

  .grid-new66 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-gap: 20px;
    grid-template-areas: ".item-new66 .item-new66" ".item-new66 .item-new66" ".item-new66 .item-new66";
  }

  .item-new66 {
    font-size: 12px;
    font-weight: bold;
    color: rgb(70, 76, 91);
    text-align: center;
  }
</style>
