<template>
  <div>
    <div class="table-wrap">
      <header>
        <RadioGroup v-model="postData.dateType">
          <Radio label="1">计划扣款日期</Radio>
          <Radio label="2">实际扣款日期</Radio>
        </RadioGroup>
        <Date-picker
          ref="datePikerRef"
          v-model="postData.dateRange"
          type="daterange"
          :options="dateOptions"
          :editable="false"
          :clearable="false"
          format="yyyy-MM-dd"
          placeholder="日期范围"
          @mouseup.native="handlePikerClick"
        />
        <Input
          v-model="postData.username"
          class="w150"
          :maxlength="64"
          clearable
          placeholder="会员姓名"
          @on-enter="search" />
        <Input
          v-model="postData.phone"
          class="w150"
          :maxlength="11"
          clearable
          placeholder="会员手机号"
          @on-enter="search" />
        <SalesSelect
          v-model="postData.marketers_id"
          isCoach
          style="width:150px;"
          :belongBusId="busId"
          placeholder="选择业绩归属"
        />
        <Select
          v-model="postData.status"
          class="w150"
          placeholder="是否支付"
          clearable
        >
          <Option
            v-for="(label, key) in statusOptions"
            :key="key"
            :value="key"
            :label="label" />
        </Select>
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>
      <main>
        <Table
          ref="table"
          class="avatar-zoom"
          :columns="columns"
          :data="tableData"
          stripe
          disabled-hover
        ></Table>
        <div class="total">
          <p>合计</p>
          <p>已支付金额：<span>{{ sumObj.paid_sum }}</span></p>
          <p>已支付订单数：<span>{{ sumObj.paid_count }}</span></p>
          <p>待支付金额：<span>{{ sumObj.ordered_sum }}</span></p>
          <p>待支付订单：<span>{{ sumObj.ordered_count }}</span></p>
          <p>扣款失败金额: <span>{{ sumObj.pay_failed_sum }}</span></p>
          <p>扣款失败订单: <span>{{ sumObj.pay_failed_count }}</span></p>
        </div>
      </main>
      <footer>
        <Button
          :disabled="totalCount === 0"
          @click="exportCsv">
          导出Excel
        </Button>
        <Export ref="export">导出Excel</Export>
        <Page
          class="page"
          :total="totalCount"
          :page-size="postData.page_size"
          :current.sync="postData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="pageChanged"
          @on-page-size-change="pageSizeChanged"
        ></Page>
      </footer>
    </div>

    <Modal v-model="saleDetailModal" title="成单明细">
      <Table
        class="avatar-zoom"
        stripe
        :columns="saleDetailCols"
        :data="saleDetailList"
        disabled-hover></Table>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import SalesSelect from 'components/membership/salesSelect'
import Export from 'src/components/Export'
import { mapGetters } from 'vuex';
import { formatDate } from "@/utils"

export default {
  name: 'ZhimaPlan',
  components: {
    SalesSelect,
    Export
  },

  data() {
    return {
      statusOptions: {
        PAID: '支付成功',
        ORDERED: '下单成功',
        UNCREATED: '未生成订单',
        PAY_FAILED: '扣款失败'
      },
      totalCount: 0,
      tableData: [],
      sumObj: {},
      postData: {
        dateType: '1',
        dateRange: [],
        username: '',
        phone: '',
        marketers_id: '',
        status: '',
        page_no: 1,
        page_size: 10,
      },
      dateOptions: {
        disabledDate: () => false
      },
      columns: [
        {
          type: 'index',
          title: '序号',
        },
        {
          title: '计划扣款日期',
          key: 'plan_deduction_time'
        },
        {
          title: '期数',
          key: 'period_done_period'
        },
        {
          title: '会员',
          key: 'username',
          ellipsis: true
        },
        {
          title: '手机号',
          key: 'phone'
        },
        {
          title: '扣款金额',
          key: 'deduction_amount',
        },
        {
          title: '签约总金额',
          key: 'total_amount'
        },
        {
          title: '业绩归属',
          key: 'sale_name',
          render: (h, params) => {
            return h(
              'a',
              {
                on: {
                  click: () => {
                    const list = Array.isArray(params.row.sale_detail)
                      ? params.row.sale_detail
                      : []
                      list.forEach(v => {
                        v.role = v.is_main === '1' ? '主归属' : '协助'
                        v.percent = v.proportion + '%'
                      })
                    this.saleDetailList = list
                    this.saleDetailModal = !!list.length
                  }
                }
              },
              params.row.sale_name
            )
          }
        },
        {
          title: '是否支付',
          key: 'status_text',
        },
        {
          title: '实际扣款时间',
          key: 'actual_deduction_time'
        },
        {
          title: '订购编号',
          key: 'subscription_no',
        },
      ],

      saleDetailModal: false,
      saleDetailList: [],
      saleDetailCols: [
        { title: '姓名', key: 'name' },
        { title: '充当角色', key: 'role' },
        { title: '贡献占比', key: 'percent' },
        { title: '业绩金额', key: 'amount' }
      ]
    }
  },
  computed: {
    ...mapGetters(['busId']),
  },

  created() {
    // 初始化日期范围，上月的第一天到最后天
    // const lastDate = new Date(new Date().toLocaleDateString())
    // lastDate.setDate(0)
    // const firstDate = new Date(lastDate)
    // firstDate.setDate(1)
    // this.postData.dateRange = [firstDate, lastDate]
    // 初始化日期范围，本月的第一天到最后天
    const today = new Date(new Date().toLocaleDateString())
    const firstDate = new Date(today.setDate(1))
    const lastDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    this.postData.dateRange = [firstDate, lastDate]

    this.getList()
  },

  methods: {
    getList(is_export = 0) {
      const { dateType, dateRange, ...rest } = this.postData
      const params = {
        ...rest,
        is_export,
        page_no: is_export === 0 ? rest.page_no : 1,
        page_size: is_export === 0 ? rest.page_size : this.totalCount,
        // 计划扣款日期 : 实际扣款日期
        [dateType === '1' ? 'plan_deduction_time' : 'actual_deduction_time']:
          `${formatDate(dateRange[0], 'yyyy-MM-dd 00:00:00')},${formatDate(dateRange[1], 'yyyy-MM-dd 23:59:59')}`,
      }
      this.$service
        .post('/Web/ZhimaFitPay/subscription_item', params, { isExport: is_export === 1 })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (is_export === 0) { // 0列表 1导出
              const statusOptions = this.statusOptions
              const data = res.data.data
              data.list && data.list.forEach(v => {
                v.period_done_period = `${v.periods}-${v.period}`
                v.status_text = statusOptions[v.status]
              })
              this.sumObj = data.sum || {}
              this.tableData = data.list
              this.totalCount = parseInt(data.count)
            } else {
              this.$Message.success({
                content: '导出任务运行中，请稍后到消息中心下载!',
                duration: 3
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    // 处理动态日期禁用
    handlePikerClick() {
      this.$nextTick(() => {
        const target = this.$refs.datePikerRef.$refs.pickerPanel;
        // console.log("from: " + target.rangeState.from);
        // console.log("to: " + target.rangeState.to);
        // console.log("selecting: " + target.rangeState.selecting);

        if (target.rangeState.selecting) { // 选择了第一个日期
          const firstDate = target.rangeState.from;
          this.dateOptions.disabledDate = (date) => {
            const dateStart = new Date(firstDate)
            const dateEnd = new Date(firstDate)
            dateStart.setDate(firstDate.getDate() - 31)
            dateEnd.setDate(firstDate.getDate() + 31)
            return date <= dateStart || date >= dateEnd
          }
        } else { // 选择了第二个日期
          this.dateOptions.disabledDate = () => false
        }
      })
    },

    pageChanged(page) {
      this.postData.page_no = page
      this.getList()
    },

    pageSizeChanged(size) {
      this.postData.page_size = size
      this.getList()
    },

    search() {
      this.postData.page_no = 1
      this.getList()
    },

    async exportCsv() {
      this.getList(1)
    }
  }
}
</script>

<style lang="less" scoped>
.total {
  display: flex;
  padding: 20px 20px 0;
  font-size: 16px;
  > p {
    padding-left: 40px;
    padding-right: 40px;
    span {
      font-weight: bold;
    }
  }
}

</style>
