<template>
  <div class="container">
    <header>
      <h3>{{ bonusId ? '编辑红包' : '发布红包' }}</h3>
    </header>
    <Form
      ref="form"
      label-position="right"
      :model="formItem"
      class="form"
      :label-width="140">
      <FormItem label="红包名称" prop="theme" :rules="{required: true, message: '请填写红包名称', trigger: 'blur'}">
        <Input v-model="formItem.theme" class="input" placeholder="20个字以内" />
      </FormItem>
      <FormItem label="转发副标题" prop="title">
        <Input v-model="formItem.title" class="input" placeholder="红包转发时显示,20个字以内" />
      </FormItem>
      <FormItem label="有效期限" prop="begin_day" :rules="{required: true, message: '选择红包有效期限'}">
        <Date-picker
          v-model="dateRange"
          style="width: 100%"
          placeholder="选择红包有效期限"
          type="daterange"
          :disabled="dataDisabled"
          :editable="false"
          :clearable="false"
          @on-change="dateChange" />
      </FormItem>
      <Card class="bonus-card bonus-card-table" dis-hover>
        <p slot="title">奖品设置</p>
        <a
          v-if="!bonusId"
          slot="extra"
          href="#"
          @click.prevent="showAdd = true">
          <Icon type="plus-circled" />
          添加奖品
        </a>
        <Table
          ref="table"
          :columns="columns"
          :data="tableData"
          disabled-hover></Table>
        <div v-if="tableData.length>0" class="card-tips">总计：现金 {{ totalMoney }}元 &nbsp; 积分 {{ totalPoint }}积分 &nbsp; 红包个数 {{ totalNum }}个</div>
      </Card>
      <Card class="bonus-card" dis-hover>
        <p slot="title">开奖设置</p>
        <FormItem label="单人最多开奖">
          <Input
            v-model="formItem.single_max"
            class="input"
            placeholder="次"
            :disabled="!!bonusId" />
        </FormItem>
        <FormItem label="单人一天最多开奖">
          <Input
            v-model="formItem.single_per_day_max"
            class="input"
            placeholder="次"
            :disabled="!!bonusId" />
        </FormItem>
        <FormItem label="分享几次增加开奖">
          <Input
            v-model="formItem.single_share"
            class="input"
            placeholder="次"
            :disabled="!!bonusId" />
        </FormItem>
      </Card>
      <FormItem label="领取限制">
        设置地理信息
        <i-switch v-model="formItem.open_geo" true-value="1" false-value="0">
          <span slot="open">开</span>
          <span slot="close">关</span>
        </i-switch>
      </FormItem>
      <template v-if="formItem.open_geo === '1'">
        <FormItem label="">
          <Table :columns="positionColumn" :data="positionTableData"></Table>
        </FormItem>
        <FormItem label="">
          <Button
            size="small"
            type="success"
            icon="plus"
            @click="handleAddBusPosition">
            场馆位置
          </Button>
          <Button
            size="small"
            type="success"
            icon="plus"
            @click="handleAddAddressPosition">
            地理位置
          </Button>
        </FormItem>
        <FormItem v-show="addingBusPosition" label="地理信息">
          <Row>
            <Col span="20">
              <FormItem v-show="addPositionType == 1">
                <Select
                  v-model="busPosition.bus_id"
                  filterable
                  placeholder="请选择场馆"
                  style="width: 260px"
                  @on-change="onPositionBusChange"
                  transfer>
                  <Option
                    v-for="bus in busList"
                    :key="bus.id"
                    :value="bus.id"
                    :label="bus.label"></Option>
                </Select>
                <span style="font-size: 12px; color: #666">在“管理-场馆资料-编辑场馆”中修改位置信息</span>
              </FormItem>
              <Address v-if="addPositionType == 2" ref="address" v-model="addressForm" @on-location="onAddressLocation" @changeSearchDetailWord="changeSearchDetailWord" @changeSearchWord="changeSearchWord"></Address>
              <FormItem label="区域范围" :label-width="100" style="padding-top: 10px">
                <AutoComplete v-model="busPosition.radius" style="width: 160px" :data="[500, 1000, 2000, 5000]" transfer />
              </FormItem>
            </Col>
            <Col span="3" offset="1">
              <Button type="success" :disabled="disabledSavePositionBtn" @click="saveBusPosition">保存</Button>
              <Button @click="addingBusPosition = false">取消</Button>
            </Col>
          </Row>
          <!-- <AMap v-model="mapCenter" :radius="busPosition.radius" /> -->
          <div style="width: 100%; height: 300px; margin-top: 20px;" v-if="addPositionType > 0">
            <!-- <Tianditu :center="mapCenter" :zoom="zoom" :circleNumber="busPosition.radius" :search="searchCityDistrict" @changeCenter="changeCenter" @changeCenterWord="changeCenterWord"></Tianditu> -->
            <TencentMap :center="mapCenter" :zoom="zoom" :circleNumber="Number(busPosition.radius || 0)" :search="searchCityDistrict" @emitCenter="changeCenter" @emitCenterWord="changeCenterWord" />
          </div>
        </FormItem>
      </template>
      <FormItem label="玩法说明">
        <Input
          v-model="formItem.desc"
          type="textarea"
          :autosize="{minRows: 4, maxRows: 7}"
          placeholder="200个字以内" />
      </FormItem>
      <FormItem v-if="!bonusId" style="color:red">温馨提示：红包发布后奖项就不能再次编辑了，未发放完毕的现金会在活动结束后24小时内原路返还</FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="primary" @click="addBonus">发红包</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>

    <Modal v-model="showAdd" :mask-closable="false" title="添加奖项">
      <Form
        ref="addForm"
        :model="modalData"
        class="modal-form"
        :label-width="110">
        <Form-item label="奖品类型" prop>
          <Select v-model="modalData.type" @on-change="typeChanged">
            <Option :value="1" v-if="this.$store.state.is_qn_j != 1">红包</Option>
            <Option :value="2">体验卡</Option>
            <Option :value="4">体验课</Option>
            <Option :value="3">折扣券</Option>
            <Option :value="5">积分</Option>
          </Select>
        </Form-item>
        <Form-item v-if="modalData.type == 5" label="红包类型">
          <RadioGroup v-model="modalData.point_num_type">
            <Radio label="1">随机积分</Radio>
            <Radio label="0">固定积分</Radio>
          </RadioGroup>
        </Form-item>
        <Form-item v-if="modalData.type == 5 && modalData.point_num_type == 1" label="随机区间">
          <div class="flex-input-row">
            <Form-item prop="minNum" :rules="{ required: true, type: 'number', message: '请填写最小值', trigger: 'change' }">
              <InputNumber
                v-model="modalData.minNum"
                :min="1"
                :max="modalData.maxNum ? modalData.maxNum - 1 : 999999998"
                :precision="0"
                :active-change="false"
                @on-change="modalData.point_sum = null"
              />
            </Form-item>
            <span class="divider">~</span>
            <Form-item prop="maxNum" :rules="{ required: true, type: 'number', message: '请填写最大值', trigger: 'change' }">
              <InputNumber
                v-model="modalData.maxNum"
                :min="modalData.minNum ? modalData.minNum + 1 : 2"
                :max="999999999"
                :precision="0"
                :active-change="false"
                @on-change="modalData.point_sum = null"
              />
            </Form-item>
          </div>
        </Form-item>
        <Form-item
          v-if="modalData.type == 1"
          label="单个金额"
          prop="definite"
          :rules="{required: true,type: 'string',pattern: /^-?[0-9]+(.[0-9]{1,2})?$/,message: '实收金额必须为数字且只能保留两位小数', trigger: 'blur'}">
          <Input v-model="modalData.definite" placeholder="金额必须大于1元，例如：1.68" />
        </Form-item>
        <Form-item
          v-if="modalData.type == 5 && modalData.point_num_type == 0"
          label="单个红包积分"
          prop="definite"
          :rules="{required: true,type: 'string',pattern: /^[1-9]\d*$/,message: '必须为数字且只能是正整数', trigger: 'blur'}">
          <Input v-model="modalData.definite" placeholder="请填写单个红包积分" />
        </Form-item>
        <Form-item
          v-if="modalData.type == 1 || modalData.type == 5"
          label="红包个数"
          prop="payout_num"
          :rules="{required: true,type: 'string',pattern: /^[1-9]\d*$/, message: '请正确填写红包个数', trigger: 'blur'}">
          <Input v-model="modalData.payout_num" placeholder="请填写红包个数" @on-change="modalData.point_sum = null" />
        </Form-item>
        <!-- <Form-item v-if="modalData.type == 5 && modalData.point_num_type == 0">
          总计 {{ Number(modalData.definite) * Number(modalData.payout_num) }}积分
        </Form-item> -->
        <Form-item
          v-if="modalData.type == 2"
          label="体验卡"
          prop="definite"
          :rules="{required: true, message: '请选择体验卡'}">
          <Select v-model="modalData.definite" label-in-value @on-change="onChange">
            <Option v-for="item in expCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
          </Select>
        </Form-item>
        <Form-item
          v-if="modalData.type == 4"
          label="体验课"
          prop="definite"
          :rules="{required: true, message: '请选择体验课'}">
          <ExpClassSelect v-model="modalData.definite" @on-change="classChange" />
        </Form-item>
        <Form-item
          v-if="modalData.type == 3"
          label="折扣券"
          prop="coupon_id"
          :rules="{required: true, message: '请选择折扣券'}">
          <busDiscount v-model="modalData.coupon_id" @on-change="onChange"></busDiscount>
        </Form-item>
        <Form-item
          v-if="modalData.type != 1 && modalData.type != 5"
          label="发放张数"
          prop="payout_num"
          :rules="{required: true, type: 'string', pattern: /^[1-9]\d*$/, message: '请填写发放张数（正整数）', trigger: 'blur'}">
          <Input v-model="modalData.payout_num" placeholder="请填写发放张数" />
        </Form-item>
        <Form-item
          v-if="modalData.type == 5"
          label="积分总数"
          prop="point_sum"
          :rules="{ required: modalData.point_num_type == 1, type: 'number', message: '请填写积分总数', trigger: 'change' }">
          <InputNumber
            v-show="modalData.point_num_type == 1"
            v-model="modalData.point_sum"
            style="width:100%;"
            :max="modalData.maxNum * modalData.payout_num || 999999999"
            :min="modalData.minNum * modalData.payout_num || 1"
            :precision="0"
            :active-change="false"
            placeholder="积分总数 必须大于 最小区间 * 红包数量"
          />
          <div v-show="modalData.point_num_type == 0" style="line-height:34px;font-size: 14px;">
            {{ Number(modalData.definite) * Number(modalData.payout_num) }} 积分
          </div>
        </Form-item>
        <FormItem
          v-if="modalData.type == 5"
          label="积分有效期"
          prop="point_days"
          :rules="{ required: true, type: 'number', message: '请填写积分有效天数', trigger: 'change' }">
          <div class="flex-input-row">
            <InputNumber
              v-model="modalData.point_days"
              style="width:100%;"
              :max="999999"
              :min="1"
              :precision="0"
              :active-change="false"
              placeholder="请填写积分有效天数"
            />
            <span style="margin-left: 6px;">天</span>
          </div>
        </FormItem>
        <div v-if="modalData.type == 1" class="modal-tips">￥ {{ modalAmount }}元</div>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveModal">保存</Button>
        <Button @click="showAdd = false">取消</Button>
      </div>
    </Modal>
    <Modal
      v-model="showPayModal"
      title="发红包"
      width="500"
      class="buy-sms"
      :mask-closable="false">
      <BonusPay v-if="showPayModal" :pay-info="payInfo" @paySuccess="paySuccess"></BonusPay>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import BonusPay from 'components/onlinePay/BonusPay';
  import busDiscount from 'components/form/busDiscount';
  import ExpClassSelect from 'components/form/ExpClassSelect';
  import { dateDiff, formatDate } from 'utils/index.js';
  import Address from 'components/address';
  // import AMap from 'components/map';
  // import Tianditu from 'components/maps/index';
  // import gcoord from 'gcoord'
  import TencentMap from 'components/maps/TencentMap';

  const PRIZE_TYPE = ['红包', '体验卡', '折扣券', '体验课', '积分'];
  const PRIZE_UNIT = { 1: '人', 2: '张', 3: '张', 4: '节', 5: '份' };

  export default {
    name: 'BonusEdit',
    components: {
      BonusPay,
      busDiscount,
      ExpClassSelect,
      Address,
      // AMap,
      // Tianditu,
      TencentMap
    },
    data() {
      return {
        addPositionType: '',
        addressForm: {
          province_id: '',
          city_id: '',
          district_id: '',
          address: ''
        },
        addingBusPosition: false,
        busList: [],
        busPosition: {
          radius: 500,
          bus_id: ''
        },
        mapCenter: [0, 0],
        zoom: 15,
        positionColumn: [
          {
            type: 'index',
            width: 50
          },
          {
            title: '可领取红包的地点',
            key: 'detailAddress'
          },
          {
            title: '范围(m)',
            key: 'range',
            width: 100
          },
          {
            title: '选项',
            width: 200,
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <i-button type="text" onClick={() => this.handleEditPosition(item)} size="small">
                    编辑
                  </i-button>
                  <i-button
                    onClick={() => this.handleDeletePosition(item.id)}
                    class="button-text-red"
                    style="margin-left: 15px"
                    type="text"
                    size="small">
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        positionTableData: [],
        formItem: {
          theme: '',
          title: '',
          begin_day: formatDate(new Date(), 'yy-MM-dd'),
          end_day: formatDate(new Date(), 'yy-MM-dd'),
          gift: '',
          single_max: '',
          single_per_day_max: '',
          single_share: '',
          desc: '',
          open_geo: '0',
          geo_info: '',
        },
        tableData: [],
        expCardList: [],
        bonusId: this.$route.params.bonusId,
        showAdd: false,
        showPayModal: false,
        dataDisabled: false,
        payInfo: null,
        totalMoney: 0,
        totalPoint: 0,
        totalNum: 0,
        modalData: {
          type: 1,
          definite: '',
          definite_name: '',
          payout_num: '',
          minNum: null,
          maxNum: null,
          point_num_type: '1',
          point_sum: null,
          point_days: null,
        },
        columns: [
          {
            type: 'index',
            width: 60,
            align: 'center'
          },
          {
            title: '奖品类型',
            render: (h, params) => {
              const item = params.row;
              return <div>{PRIZE_TYPE[item.type - 1]}</div>;
            }
          },
          {
            title: '奖品明细',
            render: (h, { row }) => {
              const { type, definite, definite_name, point_num_type, minNum, maxNum } = row;
              let text = '';
              if (type == 1) {
                text = `${definite}元`;
              } else if (type == 5) {
                text = point_num_type == 1 ? `${minNum}~${maxNum}积分` : `${definite}积分`
              } else {
                text = definite_name;
              }
              return <span>{ text }</span>;
            }
          },
          {
            title: '奖品数量',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  {item.payout_num}
                  {PRIZE_UNIT[item.type]}
                </div>
              );
            }
          },
          {
            title: '操作',
            render: (h, param) => {
              const delMe = () => {
                this.tableData.splice(param.index, 1);
              };
              return (
                <div>
                  <i-button
                    type="text"
                    shape="circle"
                    size="small"
                    style={{ color: this.bonusId ? '#bbbec4' : '#ff696a', minWidth: '0' }}
                    onClick={delMe}
                    disabled={!!this.bonusId}>
                    取消奖品
                  </i-button>
                </div>
              );
            }
          }
        ],
        dateRange: [],
        searchCityDistrict: ''
      };
    },
    computed: {
      modalAmount() {
        let amount = this.modalData.definite;
        let count = this.modalData.payout_num;
        if (amount && count) {
          return (Number(amount) * Number(count)).toFixed(2);
        } else {
          return 0;
        }
      },
      disabledSavePositionBtn() {
        return !(this.busPosition.radius && (this.busPosition.bus_id && this.addPositionType === 1 || this.addPositionType === 2 && this.addressForm.address));
      }
    },
    watch: {
      tableData(val) {
        if (val.length === 0) {
          this.totalMoney = 0;
          this.totalPoint = 0;
          this.totalNum = 0;
          if (!this.bonusId) {
            this.formItem.gift = '';
          }
          return false;
        }
        let totalMoney = 0, totalPoint = 0, totalNum = 0;
        let giftStrArr = [];
        val.forEach((item, index) => {
          if (item.type == 1) {
            let curMoney = Number(item.definite) * Number(item.payout_num);
            let curcount = parseInt(item.payout_num);
            totalMoney += curMoney;
            totalNum += curcount;
            giftStrArr.push(`1_${item.definite}_${item.payout_num}`);
          } else if (item.type == 2 || item.type == 4) {
            totalNum += parseInt(item.payout_num);
            giftStrArr.push(`${item.type}_${item.definite}_${item.payout_num}`);
          } else if (item.type == 3) {
            totalNum += parseInt(item.payout_num);
            giftStrArr.push(`3_${item.coupon_id}_${item.payout_num}`);
          } else if (item.type == 5) { // 积分
            /*
              5_100_10_1_30_1_50
              5 积分红包
              100 积分总数                   point_sum (固定金额时为definite * payout_num)
              10 红包数量                    payout_num
              30 有效期(天)                  point_days
              1 固定金额0/随机金额1           point_num_type
              1 随机区间最小值（固定金额时为0）  minNum
              50 随机区间最大值（固定金额时为0） maxNum
            */
            let curPoint = 0, giftStr = '';
            // 随机
            if (item.point_num_type == 1) {
              curPoint = item.point_sum;
              giftStr = `5_${item.point_sum}_${item.payout_num}_1_${item.point_days}_${item.minNum}_${item.maxNum}`;
            // 固定
            } else {
              curPoint = Number(item.definite) * Number(item.payout_num);
              giftStr = `5_${curPoint}_${item.payout_num}_0_${item.point_days}_0_0`;
            }
            totalPoint += curPoint;
            totalNum += parseInt(item.payout_num);
            giftStrArr.push(giftStr);
          }
        });
        this.totalMoney = totalMoney.toFixed(2);
        this.totalPoint = totalPoint;
        this.totalNum = totalNum;
        if (!this.bonusId) {
          this.formItem.gift = giftStrArr.join(',');
        }
      },
      showAdd(val) {
        if (!val) {
          this.modalData = {
            type: 1,
            definite: '',
            definite_name: '',
            payout_num: '',
            minNum: null,
            maxNum: null,
            point_num_type: '1',
            point_sum: null,
            point_days: null,
          };
        }
      }
    },
    created() {
      this.dateRange = [new Date(), new Date()];
      this.getExpCard();
      if (this.bonusId) {
        this.getBonusInfo();
      }
      this.getGeoBusList();
    },
    mounted() {
    },
    methods: {
      changeCenter(arr) {
        // let result = gcoord.transform(arr, gcoord.WGS84, gcoord.GCJ02)
        this.formItem.lng = arr[1]
        this.formItem.lat = arr[0]
        this.mapCenter = arr
      },
      changeCenterWord(result) {
        this.formItem.address = result.result
        this.addressForm.address = result.result
        if (this.$refs.address) {
          this.$refs.address.formItem.address= result.result
        }
        this.formItem.lng = result.lnglat.lng
        this.formItem.lat = result.lnglat.lat
      },
      changeSearchWord(word){
        this.searchCityDistrict = word.keyword
        this.$set(this.addressForm, 'province_name', word.provinceName)
        this.$set(this.addressForm, 'city_name', word.cityName)
        this.$set(this.addressForm, 'district_name', word.districtName)
        this.$set(this.addressForm, 'province_id', word.province_id)
        this.$set(this.addressForm, 'city_id', word.city_id)
        this.$set(this.addressForm, 'district_id', word.district_id)
        this.$set(this.addressForm, 'address', word.address)
        this.$set(this.addressForm, 'detailAddress', word.address)
      },
      changeSearchDetailWord(word){
        this.searchCityDistrict = word.word
        this.$set(this.addressForm, 'address', word.address)
        this.$set(this.addressForm, 'detailAddress', word.address)
      },
      getGeoBusList() {
        const url = '/Web/Bonus/getAdminBusList';
        this.$service.get(url, { loading: false }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.busList = data.map(item => {
              return {
                ...item,
                label: `${item.name}${item.lng ? '' : ' [未设置]'}`
              };
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      chooseDefaultBus() {
        const data = this.busList;
        this.busPosition.bus_id = data[0] && data[0].lng ? data[0].id : '';
        // 选择场馆位置按钮初始化经纬度
        if(data[0].lat != data[0].lng != 0){
          // let arr = [data[0].lng, data[0].lat]
          // let result = gcoord.transform(arr, gcoord.GCJ02, gcoord.WGS84)
          this.mapCenter=[data[0].lat, data[0].lng]
        }
      },
      onAddressLocation(address) {
        const { location, ...form } = address;
        this.mapCenter = location;
        this.addressForm = form;
      },
      saveBusPosition() {
        if (!this.busPosition.radius) return this.$Message.error('请填写区域范围');
        if (this.busPosition.radius < 500 || this.busPosition.radius > 5000) return this.$Message.error('区域合理范围 500~5000');
        // const [lng, lat] = this.mapCenter;
        const [lat, lng] = this.mapCenter;
        let obj = {};
        let index = -1;
        if (this.addPositionType === 1) {
          const { bus_id, radius, id } = this.busPosition;
          if (!bus_id) return this.$Message.error('请选择场馆');
          const bus = this.busList.find(item => item.id === bus_id);
          index = this.positionTableData.findIndex(item => item.id === id);
          obj = {
            ...bus,
            ...{
              id: Date.now(),
              type_id: bus.id,
              detailAddress: `[场馆]${bus.name}`,
              range: radius,
              lng,
              lat
            }
          };
        } else {
          const { province_name, city_name, district_name, address, id } = this.addressForm;
          if (!province_name || !city_name || !district_name || !address) return this.$Message.error('请填写地址');
          if (id !== undefined) {
            index = this.positionTableData.findIndex(item => item.id === this.addressForm.id);
          }
          obj = {
            ...this.addressForm,
            ...{
              id: Date.now(),
              range: this.busPosition.radius,
              lng,
              lat,
              type_id: '0'
            }
          };
        }
        if (index >= 0) {
          const arr = [...this.positionTableData];
          arr[index] = obj;
          this.positionTableData = [...arr];
        } else {
          this.positionTableData.push(obj);
        }
        this.formItem.geo_info = this.positionTableData;
        this.hideAddPosition();
      },
      hideAddPosition() {
        this.addingBusPosition = false;
        this.addPositionType = '';
        this.busPosition.bus_id = '';
      },
      handleAddBusPosition() {
        this.chooseDefaultBus();
        this.addPositionType = 1;
        this.addingBusPosition = true;
      },
      handleAddAddressPosition() {
        this.addressForm = {
          province_id: '',
          city_id: '',
          district_id: '',
          address: '',
        };
        this.addPositionType = 2;
        this.addingBusPosition = true;
      },
      handleEditPosition(item) {
        const { lng, lat, type_id, range, id } = item;
        // this.mapCenter = [lng, lat];
        this.mapCenter = [lat, lng];
        if (type_id === '0') {
          this.addressForm = item;
          this.busPosition.radius = range;
        } else {
          this.busPosition = {
            bus_id: type_id,
            radius: +range,
            id
          };
        }
        if (type_id === '0') {
          this.addPositionType = '';
          this.$nextTick(() => {
            this.addPositionType = 2;
          });
        } else {
          this.addPositionType = 1;
        }
        this.addingBusPosition = true;
      },
      handleDeletePosition(id) {
        const index = this.positionTableData.findIndex(item => item.id === id);
        this.positionTableData.splice(index, 1);
        this.formItem.geo_info = this.positionTableData;
      },
      onPositionBusChange(id) {
        const bus = this.busList.find(item => item.id === id);
        if (!bus) return;
        const { lng, lat } = bus;
        if (!lng || !lat) {
          this.$nextTick(() => {
            this.busPosition.bus_id = '';
          });
          return this.$Message.error('该场馆尚未设置地理位置');
        }
        // let arr = [lng, lat]
        // let result = gcoord.transform(arr, gcoord.GCJ02, gcoord.WGS84)
        this.mapCenter=[lat, lng]
      },
      onChange(res) {
        this.modalData.definite = res.value;
        this.modalData.definite_name = res.label;
      },
      classChange(res) {
        this.modalData.definite = res.card_id;
        this.modalData.definite_name = res.card_name;
      },
      paySuccess() {
        this.showPayModal = false;
        this.$Message.success('发红包成功！');
        this.$router.back();
      },
      typeChanged() {
        this.modalData.definite = '';
        this.modalData.definite_name = '';
        this.modalData.payout_num = '';
        this.modalData.minNum = null;
        this.modalData.maxNum = null;
        this.modalData.point_num_type = '1';
        this.modalData.point_sum = null;
        this.modalData.point_days = null;
        this.$refs.addForm.resetFields()
      },
      dateChange(val) {
        this.dateRange = val;
        this.formItem.begin_day = val[0];
        this.formItem.end_day = val[1];
      },
      saveModal() {
        this.$refs.addForm.validate(val => {
          if (!val) return false;
          this.tableData.push(this.modalData);
          this.$nextTick(() => {
            this.showAdd = false;
          });
        });
      },
      getBonusInfo() {
        this.$service.post('/Web/Bonus/before_bonus_upd', { bonus_id: this.bonusId }).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data;
            this.formItem = resData;
            this.dateRange = [resData.begin_day, resData.end_day];
            /* 处理积分红包的回显 */
            this.tableData = resData.gift.map(v => {
              if (v.type == 5) {
                // v.definite 积分总数(积分)
                const [
                  point_num_type, // 积分红包类型（0固定积分 1随机积分）
                  point_days,     // 有效期(天)
                  minNum,         // 随机区间最小值（固定金额时为0）
                  maxNum,         // 随机区间最大值（固定金额时为0）
                  payout_num,     // 红包数量(个)
                  get_point_sum   // 已领取积分量
                ] = v.other_gift;
                return {
                  ...v,
                  definite: point_num_type == 0 ? Math.round(v.definite / payout_num) : '',
                  point_num_type,
                  point_days,
                  minNum,
                  maxNum,
                  payout_num,
                  point_sum: +v.definite
                }
              } else {
                return {...v}
              }
            });
            this.positionTableData = resData.geo_info.map(item => {
              const { province_name, city_name, district_name, address: ad } = item;
              let address = '';
              if (item.type_id === '0') {
                address = `${province_name.region_name === city_name.region_name ? '' : province_name.region_name}${city_name.region_name}${district_name.region_name}${ad}`;
              }
              return {
                ...item,
                detailAddress: item.type_id === '0' ? address : `[场馆]${item.bus_name}`,
              };
            });
            if (dateDiff(resData.begin_day, formatDate(new Date(), 'yyyy-MM-dd')) <= 0) {
              this.dataDisabled = true;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getExpCard() {
        this.$service.get('/Web/Member/get_experience_card').then(res => {
          if (res.data.errorcode === 0) {
            this.expCardList = res.data.data.list;
          } else {
            this.expCardList = [];
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      addBonus() {
        if (!this.bonusId && this.tableData.length === 0) {
          this.$Message.error('你还没有添加奖品噢！');
        }
        this.$refs.form.validate(valid => {
          if (valid) {
            let url = '/Web/Bonus/bonus_add';
            let postData = this.formItem;
            if (this.bonusId) {
              url = '/Web/Bonus/bonus_upd';
              postData.bonus_id = this.bonusId;
            }
            this.$service
              .post(url, postData)
              .then(res => {
                if (res.data.errorcode === 0) {
                  let resData = res.data.data;
                  if (!this.bonusId && resData.need_pay === 1) {
                    this.showPayModal = true;
                    this.payInfo = resData;
                  } else {
                    this.$Message.success(res.data.errormsg);
                    this.$router.back();
                  }
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        });
      }
    }
  };
</script>

<style>
  .bonus-card-table .ivu-card-body {
    padding: 0;
  }
</style>
<style lang="less" scoped>
  .map-wrapper {
    width: 100%;
    height: 300px;
    margin-top: 20px;
  }

  .modal-tips {
    color: red;
    font-size: 16px;
    margin-left: 80px;
  }

  .card-tips {
    text-align: right;
    font-size: 12px;
    line-height: 35px;
    margin-right: 15px;
    color: #888;
  }

  .bonus-card {
    margin: 0 0 24px 50px;
  }

  .container .form {
    padding-bottom: 44px;
  }
  .flex-input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .ivu-form-item {
      margin-bottom: 0;
      flex: 1;
    }
    .ivu-input-number {
      width: 100%;
    }
    .divider {
      margin: 0 16px
    }

  }

  .modal-form .ivu-input-number {
    font-size: 14px;
  }
</style>
