<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="姓名/电话/实体卡号" />
      <Select v-model="courseId" class="option-select" placeholder="会员卡" filterable>
        <Option v-for="item in courseList" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
      </Select>
      <Select v-model="coachId" class="option-select" placeholder="开单会籍/教练">
        <Option v-for="item in coachList" :value="item.id+'_'+item.billed_type" :key="item.id+'_'+item.billed_type">{{ item.name }}</Option>
      </Select>
      <Select v-model="orderState" class="option-select" placeholder="状态">
        <Option v-for="item in orderStateList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select v-model="orderType" class="option-select" placeholder="类型" clearable>
        <Option value="1">购卡</Option>
        <Option value="2">续卡</Option>
        <Option value="3">升卡</Option>
      </Select>
      <DatePicker v-model="duringDate" type="daterange" placement="bottom-end" placeholder="选择日期" class="option-select" style="width: 220px"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button v-if="hasExportAuth" @click="handleExcel">导出Excel</Button>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
    <Modal v-model="withdrawModal" title="退单原因">
        <Form ref="formValidate" :model="formValidate" :rules="withdrawValidate" :label-width="80">
          <FormItem label="备注" prop="withdrawRemark">
              <Input v-model="formValidate.withdrawRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入..."></Input>
          </FormItem>
        </Form>
        <div slot="footer" class="modal-buttons">
          <Button type="success" @click="withdrawOk">确定</Button>
          <Button @click="withdrawCancel">取消</Button>
        </div>
    </Modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      courseId: '',
      coachId: '',
      billedType: 1,
      courseList: [],
      coachList: [],
      orderType: '',
      recorderList: [],
      withdrawModal: false,
      formValidate: {
        withdrawId: '',
        withdrawRemark: ''
      },
      withdrawValidate: {
        withdrawRemark: [{ required: true, message: '请添加退单理由！', trigger: 'blur' }]
      },
      columns: [
        { title: '姓名', key: 'recorder' },
        {
          title: '类型',
          key: 'order_type',
          render: (h, params) => (<div>{params.row.order_type == 3 ? '升卡' : params.row.order_type == 2 ? '续卡' : '购卡'}</div>)
        },
        {
          title: '会员卡',
          key: 'comes',
          render: (h, params) => (<div>{params.row.comes}</div>)
        },
        {
          title: '合同金额',
          key: 'machine',
          render: (h, params) => (<div>{params.row.machine}</div>)
        },
        {
          title: '开单员工',
          key: 'coach',
          render: (h, params) => (<div>{params.row.coach}</div>)
        },
        {
          title: '开单时间',
          key: 'signTime',
          render: (h, params) => (<div>{params.row.signTime}</div>)
        },
        {
          title: '状态',
          key: 'stateTxt',
          render: (h, params) => (<div>{params.row.stateTxt}</div>)
        },
        {
          title: '操作',
          key: 'option',
          width: 150,
          render: (h, params) => {
            if (params.row.stateTxt == '待核单') {
              return (
                <div style="display: flex;flex-direction: row;justify-content: space-around;">
                  <a onClick={()=>{this.handleOpenCheck(params.row);}}>
                    核单
                  </a>
                  <a onClick={()=>{this.handleWithdraw(params.row);}}>
                    退单
                  </a>
                </div>
              );
            }
          }
        }
      ],
      list: [],
      searchTxt: '',
      hasExportAuth: true,
      orderState: '',
      orderStateList: [{id:'', name:'全部'}, {id:0, name:'待核单'}, {id:1, name:'已开单'}, {id:2, name:'已退单'}, {id:3, name:'已撤销'}, {id:4, name:'待审批'}],
      open: false,
    };
  },
  methods: {
    getDateString(date) {
      if (date) {
        date = new Date(date);
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let day = date.getDate();
        day = day < 10 ? '0' + day : day;
        return `${year}-${month}-${day}`;
      } else {
        return '';
      }
    },
    packRow(item) {
      let stateTxt = "";
      if (item.order_status == 0) {
        stateTxt = '待核单';
      } else if(item.order_status == 1) {
        stateTxt = '已开单';
      } else if(item.order_status == 2) {
        stateTxt = '已退单';
      } else if(item.order_status == 3) {
        stateTxt = '已撤销';
      } else if(item.order_status == 4) {
        stateTxt = "待审批";
      }
      return {
        rowId: item.id,
        userId: item.user_id,
        cardUserId: item.old_card_user_id,
        recorder: item.username,
        comes: item.name,
        machine: item.order_amount,
        coach: item.biller_name,
        signTime: item.create_time,
        order_type: item.order_type,
        state: item.order_status,
        stateTxt: stateTxt
      };
    },
    getCoach() {
      return this.$service.get('/Web/preBilling/getCoachAndSale').then(res => {
        if (res.data.errorcode == 0) {
          this.coachList = [{ id: '', name: '全部' }].concat(res.data.data);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getCourse() {
      return this.$service.get('/Web/preBilling/getCard').then(res => {
        if (res.data.errorcode == 0) {
          this.courseList = [{ card_id: '', card_name: '全部' }].concat(res.data.data.list);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    handleSearch() {
      this.currentPage = 1;
      if (this.duringDate.length === 2) {
        const dateArr = this.duringDate;
        this.duringDate = [this.getDateString(dateArr[0]), this.getDateString(dateArr[1])];
      }
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      if (this.duringDate.length === 2) {
        const dateArr = this.duringDate;
        this.duringDate = [this.getDateString(dateArr[0]), this.getDateString(dateArr[1])];
      }
      this.getList();
    },
    getList() {
      return this.$service
        // .post('/Web/Sign/get_miss_sign_list', {
        .post('/Web/preBilling/listData', {
          search: this.searchTxt,
          card_id: this.courseId,
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          billed_id: this.coachId.split('_')[0],
          billed_type: this.coachId.split('_')[1],
          order_status: this.orderState,
          order_type: this.orderType,
          page_no: this.currentPage,
          page_size: this.pageSize
        }).then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item));
            });
            this.list = arr;
            this.total = parseInt(res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleWithdraw(row) {
      this.withdrawModal = true;
      this.formValidate.withdrawId = row.rowId;
      this.formValidate.withdrawRemark = "";
    },
    withdrawOk() {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          return this.$service.post('/Web/preBilling/reject', {
            id: this.formValidate.withdrawId,
            remark: this.formValidate.withdrawRemark.trim()
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              setTimeout(() => {
                this.withdrawCancel();
                this.getList();
              }, 1000);
            } else {this.$Message.error(res.data.errormsg);}
          });
        } else {
            this.$Message.error('请添加退单理由！');
        }
      })
    },
    withdrawCancel() {
      this.withdrawModal = false;
      this.formValidate.withdrawId = "";
      this.formValidate.withdrawRemark = "";
    },
    checkUserLimitByType(params) {
      return this.$service.post('/Web/UserBlacklist/checkUser', params, 
      { headers: { 'Content-Type': 'application/json' } }).then(res => {
        return res.data.data
      })
    },
    // 新增点击核单时,检测核单的会员是否在黑名单中,在则弹窗提示反之走原流程
    async handleOpenCheck(row){
      const flag = await this.checkUserLimitByType({
        user_id: row.userId,
        bus_id: this.$store.state.busId,
        member_rule: 5,
        loading: true
      })
      let that = this;
      if(that.open == false){
        that.open = true;
        if(flag == false){
          that.handleCheckOrder(row)
        } else {
          that.$Modal.confirm({
            title: '客户在黑名单中',
            content: '',
            okText: '继续核单',
            onOk: () => {
              that.handleCheckOrder(row)
            },
            onCancel: ()=>{
              that.open = false;
            }
          });
        }
      //   this.$service
      //     .post('/Web/UserBlacklist/checkUser', {
      //       user_id: row.userId,
      //       bus_id: that.$store.state.busId,
      //       loading: true
      //     }, { headers: { 'Content-Type': 'application/json' } })
      //     .then(response => {
      //       if(response.data.errorcode == 0){
      //         if(response.data.data == false){
      //           that.handleCheckOrder(row)
      //         } else {
      //           that.$Modal.confirm({
      //             title: '客户在黑名单中',
      //             content: '',
      //             okText: '继续核单',
      //             onOk: () => {
      //               that.handleCheckOrder(row)
      //             },
      //             onCancel: ()=>{
      //               that.open = false;
      //             }
      //           });
      //         }
      //       } else {
      //         that.open = false;
      //         this.$Message.error(response.data.errormsg);
      //       }
      //     })
      //     .catch(err => {
      //       that.open = false;
      //       console.log(err);
      //     });
      }
    },
    handleCheckOrder(row) {
      const checkOrder = true
      const preOrdId = row.rowId
      const userId = row.userId
      this.open = false;
      if (row.order_type == 1) {
        this.$router.push({path: '/member/buyCard/' + userId, query: {checkOrder, preOrdId, userId}});
      } else if (row.order_type == 2) {
        this.$router.push({path: `/member/renewCard/${userId}/${row.cardUserId}`, query: {checkOrder, preOrdId, userId}});
      } else if (row.order_type == 3) {
        this.$router.push({path: `/member/changeCard/${userId}/${row.cardUserId}`, query: {checkOrder, preOrdId, userId}});
      }
      
    },
    handleExcel() {
      this.$service
        .post('/Web/preBilling/listData', {
          search: this.searchTxt,
          card_id: this.courseId,
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          billed_id: this.coachId.split('_')[0],
          billed_type: this.coachId.split('_')[1],
          order_status: this.orderState,
          order_type: this.orderType,
          page_no: 1,
          page_size: this.total
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              item.order_type =item.order_type == 3 ? '升卡' : item.order_type == 2 ? '续卡' : '购卡'
              arr.push(this.packRow(item));
            });
            this.$refs.table.exportCsv({
              filename: `前台核单-${this.getDateString(new Date())}`,
              columns: this.columns.filter((col, index) => index + 1 < this.columns.length),
              data: arr
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getExcelAuth() {
      this.$service.get('/Web/preBilling/export_excel').then(res => {
        if (res.data.errorcode === 40014) {
          this.hasExportAuth = false;
        }
      });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  created() {
    const txt = this.$route.params.search;
    if (typeof txt !== 'undefined' && txt !== '') {
      this.searchTxt = txt;
      this.courseId = 1;
    }
    this.getList();
    this.getCoach();
    this.getCourse();
    this.getExcelAuth();
  }
};
</script>

<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }
     
    }
  }
}
</style>
