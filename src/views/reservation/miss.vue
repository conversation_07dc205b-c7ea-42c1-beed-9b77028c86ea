<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="姓名/电话/实体卡号" />
      <Select v-model="courseId" class="option-select" placeholder="爽约类型">
        <Option v-for="item in courseList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="coachId" class="option-select" placeholder="教练">
        <Option v-for="item in coachList" :value="item.coach_id" :key="item.coach_id">{{ item.coach_name }}</Option>
      </Select>
      <DatePicker v-model="duringDate" type="daterange" placement="bottom-end" placeholder="选择日期" class="option-select" style="width: 220px" :clearable="false"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button v-if="hasExportAuth" @click="handleExcel">导出Excel</Button>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
  </div>
</template>
<script>
import { formatDate } from 'utils';
export default {
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      courseId: '',
      coachId: '',
      courseList: [{ value: 0, label: '全部' }, { value: 1, label: '团课' }, { value: 2, label: '私教' }, { value: 3, label: '泳教' }],
      coachList: [],
      recorderList: [],
      columns: [
        {
          title: '头像',
          key: 'course',
          className: 'avatar-wrap',
          render: (h, params) => {
            return (
              <a
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.userId}`);
                }}>
                <img class="avatar" src={params.row.course} />
              </a>
            );
          }
        },
        { title: '姓名', key: 'recorder' },
        {
          title: '爽约类型',
          key: 'comes',
          render: (h, params) => {
            if(params.row.isGray) {
              return h('a', {style: {color: '#ccc'}}, params.row.comes);
            } else {
              return h('span', params.row.comes)
            }
          }
        },
        {
          title: '爽约课程',
          key: 'machine',
          render: (h, params) => {
            if(params.row.isGray) {
              return h('a', {style: {color: '#ccc'}}, params.row.machine);
            } else {
              return h('span', params.row.machine)
            }
          }
        },
        {
          title: '教练',
          key: 'coach',
          render: (h, params) => {
            if(params.row.isGray) {
              return h('a', {style: {color: '#ccc'}}, params.row.coach);
            } else {
              return h('span', params.row.coach)
            }
          }
        },
        {
          title: '时间',
          key: 'signTime',
          render: (h, params) => {
            if(params.row.isGray) {
              return h('a', {style: {color: '#ccc'}}, params.row.signTime);
            } else {
              return h('span', params.row.signTime)
            }
          }
        },
        {
          title: '操作',
          key: 'option',
          width: 150,
          render: (h, params) => {
            if (params.row.isGray) {
              return <a disabled>取消爽约</a>;
            } else {
              return (
                <div style="display: flex;flex-direction: row;justify-content: space-around;">
                  <a
                    onClick={() => {
                      this.handleSignCancel(params.row);
                    }}>
                    取消爽约
                  </a>
                </div>
              );
            }
          }
        }
      ],
      list: [],
      searchTxt: '',
      hasExportAuth: true
    };
  },
  methods: {
    getDateString(date) {
      if (!!date) {
        const theDate = new Date(date)
        const year = theDate.getFullYear();
        let month = theDate.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let day = theDate.getDate();
        day = day < 10 ? '0' + day : day;
        return `${year}-${month}-${day}`;
      } else {
        return '';
      }
    },
    packRow(item) {
      let missType = this.courseList.find(miss => miss.value == item.miss_type);
      if (!!missType) {
        missType = missType.label;
      } else {
        missType = '爽约类型';
      }
      return {
        rowId: item.sign_id,
        userId: item.user_id,
        course: item.avatar,
        recorder: item.username,
        comes: missType,
        machine: item.class_name,
        coach: item.coach_name,
        signTime: item.create_time,
        isGray: item.miss_status == 1
      };
    },
    getCoach() {
      return this.$service.get('/Web/Sign/get_miss_sign_coach_list').then(res => {
        if (res.data.errorcode == 0) {
          this.coachList = [{ coach_id: '', coach_name: '全部' }].concat(res.data.data);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    handleSearch() {
      this.currentPage = 1;
      if (this.duringDate.length === 2) {
        const dateArr = this.duringDate;
        this.duringDate = [this.getDateString(dateArr[0]), this.getDateString(dateArr[1])];
      }
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    getList() {
      return this.$service
        .post('/Web/Sign/get_miss_sign_list', {
          search: this.searchTxt,
          miss_type: this.courseId,
          s_date: this.duringDate[0],
          e_date: this.duringDate[1],
          coach_id: this.coachId,
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item));
            });
            this.list = arr;
            this.total = parseInt(res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleSignCancel(row) {
      return this.$service
        .post('/Web/Sign/edit_miss_status', {
          sign_id: row.rowId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            const self = this;
            setTimeout(() => {
              this.getList();
            }, 1000);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleExcel() {
      this.$service
        .post('/Web/Sign/get_miss_sign_list', {
          search: this.searchTxt,
          miss_type: this.courseId,
          s_date: this.duringDate[0],
          e_date: this.duringDate[1],
          coach_id: this.coachId,
          page_no: 1,
          page_size: this.total
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item));
            });
            this.$refs.table.exportCsv({
              filename: `爽约记录-${this.getDateString(new Date())}`,
              columns: this.columns.filter((col, index) => 0 < index && index + 1 < this.columns.length),
              data: arr
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getExcelAuth() {
      this.$service.get('/Web/Sign/miss_sign_list_excel').then(res => {
        if (res.data.errorcode === 40014) {
          this.hasExportAuth = false;
        }
      });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  created() {
    const txt = this.$route.params.search;
    if (typeof txt !== 'undefined' && txt !== '') {
      this.searchTxt = txt;
      this.courseId = 1;
    }
    this.getList().then(this.getCoach);
    this.getExcelAuth();
  }
};
</script>
<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
