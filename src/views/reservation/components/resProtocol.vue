<template>
  <div class="container gym-edit">
    <header>
      <h3>编辑</h3>
    </header>
    <Form label-position="right" ref="form" :model="formItem" :rules="formRules" class="form" :label-width="140">
      <FormItem label="团课预约协议" prop="protocol_content">
        <FormEditor v-model="formItem.protocol_content" :options="options"/>
      </FormItem>
      <FormItem v-if="canEdit">
        <div class="form-bottom-buttons">
          <Button type="success" @click="editBus">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import FormEditor from 'components/form/Editor'
export default {
  name: 'resProtocol',
  components: {
    FormEditor
  },

  data() {
    return {
      formItem: {
        protocol_content: null
      },
      canEdit: false,
      options: {
        modules: {
          toolbar: [
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ color: [] }, { background: [] }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ align: [] }],
            [{ script: 'sub' }, { script: 'super' }]
          ]
        }
      },
      formRules: {
        bus_description: [{ required: true, message: '请填写协议内容' }]
      }
    }
  },
  created() {
    this.getBusDetail()
  },
  computed: {},
  watch: {},
  methods: {
    editBus() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$service
            .post('Web/CourseProtocol/update', {
              content: this.formItem.protocol_content
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.$router.go(-1)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      })
    },
    getBusDetail() {
      this.$service
        .post('Web/CourseProtocol/getContent', {
          bud_id: this.$store.state.busId
        })
        .then(response => {
          if (response.data.errorcode === 0) {
            let dt = response.data.data.content
            this.canEdit = true
            this.formItem.protocol_content = this.unescapeHTML(
              dt || ''
            )
          } else {
            this.$Message.error(response.data.errormsg)
          }
        })
    },
    unescapeHTML(a) {
      a = '' + a
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
    }
  }
}
</script>
