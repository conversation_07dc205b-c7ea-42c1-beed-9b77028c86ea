<template>
  <Modal v-model="showAdd" :mask-closable="false" title="添加到店预约">
    <Form ref="addresData" :model="addresData" class="modal-form" :rules="addRules" :label-width="80">
      <Form-item label="手机号"
                prop="phone">
        <Input v-model="addresData.phone" />
      </Form-item>
      <Form-item label="姓名"
                prop="username">
        <Input v-model="addresData.username" />
      </Form-item>
      <Form-item label="跟进会籍" prop="ms_id">
        <Select v-model="addresData.ms_id"  placeholder="请选择" clearable>
          <Option v-for="item in marketersList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
      </Form-item>
      <Form-item label="备注" prop="remark">
        <Input type="textarea" v-model="addresData.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="confirmAdd">保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
export default {
  name: 'AddRes',
  props: {
    marketersList: {
      type: Array
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.$refs.addresData.resetFields()
      }
    }
  },
  data() {
    return {
      addresData: {
        phone: '',
        username: '',
        ms_id: '',
        remark: ''
      },
      addRules: {
        phone: [{required: true, pattern: /^1\d{10}$/, message: "请输入正确的手机号码", trigger: "blur"}],
        username: [{ required: true, message: "会员名称不能为空", trigger: "blur" }],
        ms_id: [{ required: true, message: '请选择跟进会籍', trigger: 'change' }]
      }
    }
  },
  methods: {
    confirmAdd() {
      this.$refs.addresData.validate(val => {
        if (!val) return false
        this.$service
          .post('/Web/Reservation/add', this.addresData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showAdd = false;
              this.$Message.success(res.data.errormsg)
              this.$emit("updatefar");
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      })
    },
  }
}
</script>