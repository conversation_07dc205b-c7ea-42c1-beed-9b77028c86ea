<template>
  <div>
    <div>
      <header>
        <DatePicker :value="days" :editable="false" :clearable="false" placement="bottom-start" format="yyyy-MM-dd" type="daterange" style="width: 200px"></DatePicker>
        <Input placeholder="手机号/姓名"
              style="width: 180px"
              v-model="vispostData.search"
              @on-enter="search" />
        <Select class="w120" v-model="vispostData.ms_id" placeholder="请选择会籍" clearable @on-change="search">
          <Option v-for="item in marketersList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
        <Button type="success"
                @click="search">搜索</Button>
      </header>
      <Table :columns="viscolumns"
            :data="vistableData"
            disabled-hover
            ref="table"
            stripe />
      <footer>
        <Select placeholder="其他操作"
              placement="top"
              style="width: 100px"
              @on-change="otherCase"
              v-if="notExport">
          <Option value="0">导出excel</Option>
        </Select>
        <Page :total="+vistotalCount"
              :current.sync="vispostData.page_no"
              placement="top"
              show-total
              show-sizer
              @on-change="getvisList"
              @on-page-size-change="vispageSizeChanged" />
      </footer>
    </div>

    <Modal v-model="leftStoreModal" title="离店">
      <Form :model="leftForm" :label-width="80">
        <Form-item label="接待人">
          <p>{{leftForm.welcomeName}}</p>
        </Form-item>
        <Form-item label="离店时间">
          <TimePicker v-model="leftForm.time" format="HH:mm" placeholder="可选范围：到店时间~当前时间" style="width: 400px"></TimePicker>
        </Form-item>
        <Form-item label="备注">
          <Input type="textarea" v-model="leftForm.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleLeftFormSubmit">保存</Button>
        <Button @click="leftStoreModal=false">取消</Button>
      </div>
    </Modal>

  </div>
</template>
<script>
import { formatDate } from 'utils'
export default {
  props: [
    'marketersList',
    'resup',
    'visup'
  ],
  data() {
    return {
      leftStoreModal: false,
      leftForm: {
        id: '',
        welcomeName: '',
        time: '',
        remark: ''
      },
      notExport: true,
      vispostData: {
        page_no: 1,
        page_size: 10,
        s_date: '',
        e_date: '',
        search: '',
        ms_id: ''
      },
      days:[formatDate(new Date(Date.now() - ((new Date()).getDate()-1) * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      vistableData: [],
      vistotalCount: 0,
      showCancel: false,
      viscolumns: [
        {
          title: '姓名',
          key: 'username'
        },
        {
          title: '电话',
          key: 'phone'
        },
        {
          title: '预约会籍',
          key: 'ms_name'
        },
        {
          title: '到店体验时间',
          key: 'to_visit_date'
        },
        {
          title: '离店时间',
          key: 'leave_time'
        },
        {
          title: '备注',
          key: 'remark'
        },
        {
          title: '操作',
          render: (h,param) => {
            if (param.row.leave_time == '-') {
              return h('div',[
                h('i-button',{
                  props: {
                    type: 'text',
                    shape: 'circle',
                    size: 'small'
                  },
                  style: {
                    color: '#52a4ea',
                    minWidth: '0'
                  },
                  on: {
                    click: () => {
                      this.leftForm.id = param.row.id;
                      this.leftForm.welcomeName = param.row.reception_name;
                      this.leftForm.time = new Date();
                      this.leftForm.remark = param.row.remark;
                      this.leftStoreModal = true;
                    }
                  }
                },'离店'),
                h('i-button',{
                  props: {
                    type: 'text',
                    shape: 'circle',
                    size: 'small'
                  },
                  style: {
                    color: '#52a4ea',
                    minWidth: '0'
                  },
                  on: {
                    click: () => {
                      this.cancelModal(param.row.id)
                    }
                  }
                },'取消')
              ]);
            } else {
              return h('div',[
                h('i-button',{
                  props: {
                    type: 'text',
                    shape: 'circle',
                    size: 'small'
                  },
                  style: {
                    color: '#52a4ea',
                    minWidth: '0'
                  },
                  on: {
                    click: () => {
                      this.cancelModal(param.row.id)
                    }
                  }
                },'取消')
              ]);
            }

          }
        }
      ],
    }
  },
  created() {
    this.getvisList();
  },
  watch: {
    visup: function(val,oldval) {
      if(val) {
        this.getvisList();
      }
    }
  },
  methods: {
    async otherCase(val) {
      if (val === '0') {
        let resData = await this.getvisList(undefined,this.vistotalCount);
        if (!resData) return false;
        this.notExport = false;
        this.$refs.table.exportCsv({
          filename: '到店体验',
          columns: this.viscolumns.filter((col, index) => index < 5),
          data: resData
        });
        setTimeout(() => {
          this.notExport = true;
        }, 100);
      }
    },
    search() {
      this.vispostData.page_no = 1;
      this.getvisList();
    },
    dateChanged(val) {
      if(val[0]) {
        this.vispostData.s_date = val[0];
        this.vispostData.e_date = val[1];
      } else {
        this.vispostData.s_date = formatDate(new Date(Date.now() - ((new Date()).getDate()-1) * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
        this.vispostData.e_date = formatDate(new Date(Date.now()),'yyyy-MM-dd');
      }
      this.getvisList();
    },
    vispageSizeChanged(size) {
      this.vispostData.page_size = size;
      this.vispostData.page_no = 1;
      this.getvisList();
    },
    getvisList(resup,exceltotal) {
      const url = "/Web/Reservation/visit_list"
      const postdata = {
        page_no: this.vispostData.page_no,
        page_size: this.vispostData.page_size,
        s_date: this.vispostData.s_date,
        e_date: this.vispostData.e_date,
        search: this.vispostData.search,
        ms_id: this.vispostData.ms_id
      }
      if(exceltotal) {
        postdata.page_size = exceltotal;
      }
      return this.$service
        .post(url, {params: this.postData})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.vistableData = res.data.data.list;
            this.vistotalCount = res.data.data.count;
            //resup用来判断在到店体验页面数据变化或搜索之后，预约人数页面是否需要重新加载，
            //resup初始值为true，预约人数页面加载完毕后变为false
            //到店体验页面里面点击取消会改变其状态，其余操作保持原状
            if(resup!=undefined) {
              this.$emit('titlechange',res.data.data.reservationCount,res.data.data.visitCount,resup,false);
            } else {
              this.$emit('titlechange',res.data.data.reservationCount,res.data.data.visitCount,this.resup,false);
            }
            return res.data.data.list;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    confirmCancel(id) {
      const url = "/Web/Reservation/cancel_visit"
      this.$service
        .post(url, {ids: id})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getvisList(true);
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    cancelModal(id) {
      this.showCancel = true;
      this.$Modal.confirm({
        title: '取消到店',
        content: '确定取消到店体验？',
        onOk: () => {
          this.confirmCancel(id);
        }
      })
    },
    handleLeftFormSubmit() {
      return this.$service.post('/Web/Reservation/leaveVisit', {
        id: this.leftForm.id,
        remark: this.leftForm.remark,
        leave_time: this.leftForm.time
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg);
          setTimeout(() => {
            this.leftStoreModal = false;
            this.getvisList();
          }, 1000);
        } else {this.$Message.error(res.data.errormsg);}
      });
    }
  }
}
</script>
<style lang="less" scoped>
</style>
