<template>
  <Modal v-model="showConfirm" :mask-closable="false" title="确认到店">
    <Form :model="confirmData" class="modal-form" :label-width="80">
      <Form-item label="接待人" prop="">
        <Select v-model="confirmData.welcomeIdType" placeholder="请选接待人" filterable clearable>
          <Option v-for="item in welList" :key="item.id+'-'+item.type" :value="item.id+'-'+item.type">{{item.name}}</Option>
        </Select>
      </Form-item>
      <Form-item label="备注" prop="remark">
        <Input type="textarea" v-model="confirmData.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="confirmArr">保存</Button>
      <Button @click="showConfirm = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
export default {
  name: 'ConfirmArr',
  data() {
    return {
      confirmData: {
        reservation_id: '',
        welcomeIdType: '',
        remark: '确认到店'
      }
    }
  },
  props: {
    id: {
      type: [Number, String]
    },
    welIdType: {
      type: [String]
    },
    value: {
      type: Boolean
    },
    welList: {
      type: Array
    }
  },
  computed: {
    showConfirm: {
      get() {
        if(this.value) {
          this.confirmData.reservation_id = this.id;
          this.confirmData.welcomeIdType = this.welIdType;
        }
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    confirmArr() {
      const arr = this.confirmData.welcomeIdType.split('-');
      this.confirmData.reception_id = arr[0];
      this.confirmData.reception_type = arr[1];
      const obj = this.welList.find(item=>(item.id == arr[0]));
      this.confirmData.remark = '接待人：' + obj.name + '\n' + this.confirmData.remark;
      const url = "/Web/Reservation/to_visit"
      let _confirmData = {};
      _confirmData.reception_id = arr[0];
      _confirmData.reception_type = arr[1];
      _confirmData.remark = this.confirmData.remark;
      _confirmData.reservation_id = this.confirmData.reservation_id;

      this.$service
        .post(url, _confirmData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.confirmData.reservation_id = '';
            this.confirmData.welcomeIdType = '';
            this.confirmData.reception_id = '';
            this.confirmData.reception_type = '';
            this.confirmData.remark = '确认到店';
            this.showConfirm = false;
            this.$emit('updatefcfa',true);
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>