<template>
  <div>
    <div>
      <header>
        <Input placeholder="姓名/电话"
              style="width: 180px"
              v-model="postData.search"
              @on-enter="search" />
        <Select class="w120" v-model="postData.appoint_ms_id"  placeholder="请选择预约会籍" clearable @on-change="search">
          <Option v-for="item in marketersList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
        <Select class="w120" v-model="postData.status"  placeholder="状态" clearable @on-change="search">
          <Option v-for="item in clientStatus" :key="item.item" :value="item.id">{{item.item}}</Option>
        </Select>
        <Select class="w120" v-model="postData.receive_ms_id"  placeholder="请选择接待会籍" clearable @on-change="search">
          <Option v-for="item in marketersList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
        <DatePicker :value="days" :editable="false" :clearable="true" placement="bottom-start" format="yyyy-MM-dd" type="daterange" style="width: 200px" @on-change="showDate" :placeholder="'请选择搜索时间'"></DatePicker>
        
        <Button type="success"
                @click="search">搜索</Button>
      </header>
      <Table :columns="rescolumns"
            :data="restableData"
            class="avatar-zoom ivu-table"
            :row-class-name="rowClassName"
            stripe
            ref="table" />
      <footer>
        <div>
          <Button type="success"
                  style="margin-right: 30px;"
                  @click="showAdd = true">添加到店预约</Button>
          <Dropdown placement="top" @on-click="otherCase" style="margin-left: 10px">
            <Button>其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="0">导出excel</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
        <Page :total="+restotalCount"
              :current.sync="postData.page_no"
              placement="top"
              show-total
              show-sizer
              @on-change="getresList"
              @on-page-size-change="respageSizeChanged" />
      </footer>
    </div>
    <add-res v-model="showAdd" :marketersList="marketersList" v-on:updatefar="updateFar" />
    <confirm-arr v-model="showConfirm" :id="reservationId" :wel-id-type="welcomeIdType" :wel-list="welcomeList" v-on:updatefcfa="updateFcfa" />
    <Modal v-model="leftStoreModal" title="离店">
      <Form :model="leftForm" :label-width="80">
        <Form-item label="接待人">
          <p>{{leftForm.welcomeName}}</p>
        </Form-item>
        <Form-item label="离店时间">
          <TimePicker v-model="leftForm.time" format="HH:mm" placeholder="可选范围：到店时间~当前时间" style="width: 400px"></TimePicker>
        </Form-item>
        <Form-item label="备注">
          <Input type="textarea" v-model="leftForm.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleLeftFormSubmit">保存</Button>
        <Button @click="leftStoreModal=false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import AddRes from './addres.vue';
import ConfirmArr from './confirmarr.vue';
import { formatDate } from 'utils'
export default {
  name: 'ResTable',
  components: {
    AddRes,
    ConfirmArr
  },
  props: [
    'marketersList',
    'resup',
    'resup',
    'clientStatus'
  ],
  created() {
    this.postData.start_date = formatDate(new Date(), 'yyyy-MM-dd');
    this.postData.end_date = formatDate(new Date(Date.now() + (7 - (new Date()).getDay()) * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
    this.getresList();
    this.getWelcomeList();
  },
  data() {
    return {
      leftStoreModal: false,
      leftForm: {
        id: '',
        welcomeName: '',
        time: '',
        remark: ''
      },
      postData: {
        search: '', //电话或者姓名
        appoint_ms_id: '', //搜索预约会籍
        status: '', //搜索状态  
        receive_ms_id: '', //接待会籍
        start_date: '', //开始时间
        end_date: '', //结束时间
        page_no: 1,
        page_size: 10
      },
      days: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(Date.now() + (7 - (new Date()).getDay()) * 24 * 60 * 60 * 1000), 'yyyy-MM-dd') ],
      restableData: [],
      restotalCount: 0,
      showConfirm: false,
      showAdd: false,
      reservationId: '',
      vistableData: '',
      notExport: true,
      excelData: '',
      welcomeIdType: '',
      vistotalCount: '',
      welcomeList: [],
      excelcolumns: [
        {
          title: '姓名',
          key: 'username'
        },
        {
          title: '电话',
          key: 'phone'
        },
        {
          title: '预约会籍',
          key: 'ms_name'
        },
        {
          title: '创建时间',
          key: 'create_reservation_time'
        },
        {
          title: '预约时间',
          key: 'create_date',
          sortable: true
        },
        {
          title: '备注',
          key: 'remark'
        },
        {
          title: '状态',
          key: 'status',
        },
        {
          title: '到店时间',
          key: 'visit_time'
        },
        {
          title: '接待会籍',
          key: 'reception_name'
        },
        {
          title: '离店时间',
          key: 'leave_time'
        }],
      rescolumns: [
        {
          title: '姓名',
          key: 'username'
        },
        {
          title: '电话',
          key: 'phone',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '预约会籍',
          key: 'ms_name'
        },
        {
          title: '创建时间',
          key: 'create_reservation_time'
        },
        {
          title: '预约时间',
          key: 'create_date',
          sortable: true
        },
        {
          title: '备注',
          key: 'remark',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
                if(params.row.status === 1) {
                    return h('span', '预约中')
                }
                if(params.row.status === 2) {
                    return h('span', '未到店')
                }
                if(params.row.status === 3) {
                    return h('span', '已到店')
                }
                if(params.row.status === 4) {
                    return h('span', '已离店')
                }
          }
        },
        {
          title: '到店时间',
          key: 'visit_time'
        },
        {
          title: '接待会籍',
          key: 'reception_name'
        },
        {
          title: '离店时间',
          key: 'leave_time'
        },
        {
          title: '操作',
          render: (h,param) => {
            if(param.row.status === 3) {
                return h('div',[
                    h('i-button',{
                        props: {
                        type: 'text',
                        shape: 'circle',
                        size: 'small'
                        },
                        style: {
                        color: '#52a4ea',
                        minWidth: '0'
                        },
                        on: {
                        click: () => {
                            this.leftForm.id = param.row.id;
                            this.leftForm.arrTime= param.row.visit_time.split(' ')[1];
                            this.leftForm.welcomeName = param.row.reception_name;
                            this.leftForm.time = new Date();
                            this.leftForm.remark = param.row.remark;
                            this.leftStoreModal = true;
                        }
                    }
                    },'离店'),
                    h('i-button',{
                        props: {
                        type: 'text',
                        shape: 'circle',
                        size: 'small'
                        },
                        style: {
                        color: 'red',
                        minWidth: '0'
                        },
                        on: {
                        click: () => {
                            this.cancelModal(param.row.id)
                        }
                    }
                    },'取消到店')
                ])
            }
            if(param.row.status === 1) {
                return h('div',[
                    h('i-button',{
                        props: {
                        type: 'text',
                        shape: 'circle',
                        size: 'small'
                        },
                        style: {
                        color: '#52a4ea',
                        minWidth: '0'
                        },
                        on: {
                        click: () => {
                            this.showconfirmModal(param.row.id, param.row.reception_id, param.row.reception_type)
                        }
                        }
                    },'确认到店')
                ])
            } else {
                return (<span>-</span>)
            }   
          }
        }
      ]

    }
  },
  watch: {
    resup: function(val,oldval) {
      if(val) {
        this.getresList();
      }
    }
  },
  methods: {
    getvisList(resup,exceltotal) {
      const url = "/Web/Reservation/new_reservation_list/"
      const postdata = {
        ...this.postData, page_no: 1
      }
      if(exceltotal) {
        postdata.page_size = exceltotal;
        postdata.isExport = true;
      }
      return this.$service
        .post(url, postdata, { isExport: Boolean(exceltotal) })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.excelData = res.data.data.list;
            // this.vistotalCount = res.data.data.count;
            //resup用来判断在到店体验页面数据变化或搜索之后，预约人数页面是否需要重新加载，
            //resup初始值为true，预约人数页面加载完毕后变为false
            //到店体验页面里面点击取消会改变其状态，其余操作保持原状
            if(resup!=undefined) {
              this.$emit('titlechange',res.data.data.reservationCount,res.data.data.visitCount,resup,false);
            } else {
              this.$emit('titlechange',res.data.data.reservationCount,res.data.data.visitCount,this.resup,false);
            }
            return res.data.data.list;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    async otherCase(val) {
      if (val === '0') {
        if(this.restotalCount == 0) {
            this.$Message.error("没有可以导出的数据！");
            return;
        }
        var resData = await this.getvisList(undefined, this.restotalCount);
        if (!resData) return false;
        resData.map((item) => {
            switch (item.status) {
                case 1:
                    item.status = "预约中";
                    break;
                case 2:
                    item.status = "未到店";
                    break;
                case 3:
                    item.status = "已到店";
                    break;
                case 4:
                    item.status = "已离店";
                    break;
            }
        })
        resData.map((item) => {
          let remark = item.remark.replace(/\s+/g, "");
          item.remark = this.handleStr(remark)
        })
        this.notExport = false;
        this.$refs.table.exportCsv({
          filename: '预约名单',
          columns: this.excelcolumns,
          data: resData
        });
        setTimeout(() => {
          this.notExport = true;
        }, 100);
      }
    },
    //  字符串处理，防止字符串中含有英文的逗号，导致导出的csv文件错行
    handleStr(str){
      let handleStr=str.replace(/[\r\n]/g,""); 
      //先判断字符里是否含有逗号
      if(str.indexOf(",") != -1){
        //如果还有双引号，先将双引号转义，避免两边加了双引号后转义错误              
        if(str.indexOf("\"") != -1){
          handleStr=str.replace("\"", "\"\"");
        }
        //将逗号转义  
        handleStr="\""+handleStr+"\"";  
        return handleStr
      } 
      return "\""+handleStr+"\"";  
    },
    showDate(e) { //时间选择器选择时间后，e就是选择的时间
        this.postData.start_date = e[0];
        this.postData.end_date = e[1];
    },
    search() {
      this.postData.page_no = 1;
      this.getresList();
    },
    updateFar() {
      this.getresList(false);
    },
    updateFcfa() {
      this.getresList(true);
    },
    respageSizeChanged(size) {
      this.postData.page_size = size;
      this.postData.page_no = 1;
      this.getresList();
    },
    getresList(resup) {
      const url = "/Web/Reservation/new_reservation_list"
      this.$service
        .post(url, this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.restableData = res.data.data.list;
            this.restotalCount = res.data.data.count;    
            //visup用来判断在预约人数页面数据变化或搜索之后，到店体验页面是否需要重新加载，
            //visup初始值为true，到店体验页面加载完毕后变为false
            //预约人数页面里面点击到店确认会改变其状态，其余操作保持原状
            if(resup!=undefined){
              this.$emit('titlechange',res.data.data.reservationCount, res.data.data.visitCount,false,resup);
            } else {
              this.$emit('titlechange',res.data.data.reservationCount, res.data.data.visitCount,false,this.resup);
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getWelcomeList() {
      this.$service.post('/Web/Reservation/getMarketAndCoachList').then(res => {
        if (res.data.errorcode == 0) {
          this.welcomeList = res.data.data;
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    handleLeftFormSubmit() {
      if(this.leftForm.time < this.leftForm.arrTime) {
        this.$Message.error("离店时间必须在到店时间之后！");
        this.leftForm.time = '';    
        return;
      }
      let data = {};
      data.id = this.leftForm.id;
      data.remark = this.leftForm.remark;
      data.leave_time = this.leftForm.time;     
      this.leftForm.time = '';
      this.leftForm.remark = '';
      this.leftForm.id = '';
      this.leftForm.welcomeName = '';
      return this.$service.post('/Web/Reservation/leaveVisit', data).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg);
          setTimeout(() => {
            this.leftStoreModal = false;
            this.getresList();
          }, 1000);
        } else {this.$Message.error(res.data.errormsg);}
      });
      
    },
    cancelModal(id) {
      this.showCancel = true;
      this.$Modal.confirm({
        title: '取消到店',
        content: '确定取消到店体验？',
        onOk: () => {
          this.confirmCancel(id);
        }
      })
    },
    confirmCancel(id) {
      const url = "/Web/Reservation/cancel_visit"
      this.$service
        .post(url, {ids: id})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getvisList(true);
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    showconfirmModal(id, welId, welType) {
      this.reservationId = id;
      this.welcomeIdType = welId + '-' + welType;
      this.showConfirm = true;
    },
    rowClassName (row, index) {
      if (index % 2 === 1) {
        return 'demo-table-info-row';
      } else {
        return '';
      }
    }
  }
}
</script>

<style>
    .ivu-table th {
        background-color: #f7f7f7 !important;
    }

    .demo-table-info-row td {
        background: #f7f7f7 !important;        
    }
</style>