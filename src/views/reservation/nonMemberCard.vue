<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="入场卡名称" />
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button type="success" style="margin-right: 30px;" @click="handleShowAddClick">入场卡添加</Button>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>

    <Modal v-model="showAdd" :mask-closable="false" :title="!!addData.cardId?'编辑入场卡':'添加入场卡'" width="660">
        <Form ref="addData" :model="addData" class="modal-form" :rules="addRules" :label-width="100">
            <Form-item label="入场卡" prop="cardId">
                <Select v-model="addData.cardId" placeholder="请选择" clearable>
                  <Option v-for="(item, index) in nonMemberCardListCopy" :key="index" :value="item.card_id">{{item.card_name}}</Option>
                </Select>
            </Form-item>
            <Form-item label="基础时长" prop="duration">
                <div style="display:flex;flex-direction:row;">
                  <InputNumber :max="99999" :min="0" :step="1" v-model="addData.duration" />
                  <Select v-model="addData.unit" style="margin-left:10px;width:80px;">
                    <Option v-for="(item, index) in unitList" :key="index" :value="index">{{item}}</Option>
                  </Select>
                </div>
            </Form-item>
            <Form-item label="基础费用" prop="baseFee">
                <InputNumber :precision="2" :active-change="false" :max="99999" :min="0" :step="0.1" v-model="addData.baseFee" />
                <span style="margin-left:10px;">元</span>
            </Form-item>
            
            <Form-item label="智能结算" prop="is_auto_bill">
              <RadioGroup v-model="addData.is_auto_bill">
                <Radio label="1">开启</Radio>
                <Radio label="0">关闭</Radio>
              </RadioGroup>
            </Form-item>

            <Form-item label="超时计费规则" prop="rule">
                <div class="rule-box" v-for="(item, index) in addData.rule" :key="index">
                  <InputNumber style="width:60px;" :precision="0" :max="99999" :min="0" :step="1" v-model="item.start" disabled />
                  <span style="margin:0 10px;">~</span>
                  <InputNumber style="width:60px;" :precision="0" :max="99999" :min="1" :step="1" v-model="item.end" />
                  <span style="margin:0 10px;">{{unitList[addData.unit]}}内，每</span>
                  <InputNumber style="width:60px;" :precision="0" :max="99999" :min="1" :step="1" v-model="item.count" />
                  <span style="margin:0 10px;">{{unitList[addData.unit]}}，收</span>
                  <InputNumber style="width:60px;" :precision="2" :active-change="false" :max="99999" :min="0" :step="0.1" v-model="item.price" />
                  <span style="margin:0 10px;">元</span>
                  <Button size="small" type="error" @click="handleDeleteRuleClick(index)" ghost>删除</Button>
                </div>
                <div style="margin-top:10px;">
                   <Button type="dashed" @click="handleAddRuleClick">添加超时梯度</Button>
                </div>
            </Form-item>
        </Form>
        <div slot="footer" class="modal-buttons">
            <Button type="success" @click="handleNonMemberCardSaveClick">保存</Button>
            <Button @click="handleNonMemberCardCancelClick">取消</Button>
        </div>
    </Modal>

  </div>
</template>

<script>
export default {
  name: 'nonMemberCard',
  data() {
    const validateSelect = (rule, value, callback) => {
      if (!value) {

      } else if (!/^[0-9]*$/.test(value)) {
        callback(new Error('请选择入场卡!'));
      } else {
        callback();
      }
    };
    return {
      showAdd: false,
      nonMemberCardList: [],
      nonMemberCardListCopy: [],
      unitList: ['小时', '分钟'],
      addData: {
        cardId: '',
        duration: 0,
        unit: 0,
        baseFee: 0,
        is_auto_bill: '0',
        rule: []
      },
      addRules: {
        cardId: [{ required: true, validator: validateSelect, trigger: "change" }],
        duration: [{ required: true, type: 'integer', message: "请输入正整数!", trigger: "blur" }],
        baseFee: [{ required: true, type: 'number', message: "请输入数字!", trigger: "blur" }]
      },
      currentPage: 1,
      pageSize: 10,
      total: 0,
      columns: [
          { title: '入场卡', key: 'cardName', width: 220 },
          { title: '基础时长', key: 'duration', width: 220, render:(h, params)=>(<div>{params.row.duration}{this.unitList[params.row.unit]}</div>) },
          { title: '基础费用', key: 'baseFee', width: 220, render:(h, params)=>(<div>{params.row.baseFee}元</div>) },
          {
            title: '超时规则',
            key: 'rule',
            render: (h, params) => {
              let ruleLi = (<li>空</li>);
              let ruleLiTip = (<li></li>);
              if (Array.isArray(params.row.rule)) {
                ruleLiTip = params.row.rule.map(item=>{
                  return (<li>{item.start}~{item.end}{this.unitList[params.row.unit]}，收费{item.price}元</li>);
                });
                if (params.row.rule.length > 2) {
                  ruleLi = params.row.rule.map((item, index)=>{
                    if (index < 2) {
                      return (<li>{item.start}~{item.end}{this.unitList[params.row.unit]}，收费{item.price}元</li>);
                    } else if (index == 2) {
                      return (<li>...</li>);
                    } else {
                      return false;
                    }
                  });
                } else {
                  ruleLi = ruleLiTip;
                }
              }
              return (<Tooltip placement="right"><ul>{ruleLi}</ul><ul slot="content">{ruleLiTip}</ul></Tooltip>);
            }
          },
          {
            title: '操作',
            key: 'option',
            width: 150,
            render: (h, params) => {
                if (params.row.isGray) {
                  return <a disabled>结算</a>;
                } else {
                  return (
                    <div style="display: flex;flex-direction: row;justify-content: space-around;">
                    <a onClick={() => {
                          this.nonMemberCardListCopy = this.nonMemberCardList.concat([{
                            card_id: params.row.cardId,
                            card_name: params.row.cardName
                          }]);
                          this.addData = params.row;
                          this.showAdd = true;
                        }}>
                        编辑
                    </a>
                    <a style="color:red;" onClick={() => {
                          this.handleDeleteInfo(params.row.id).then(this.getList).then(this.getNonMemberCardList);
                        }}>
                        删除
                    </a>
                    </div>
                );
            }
          }
          }
      ],
      list: [],
      searchTxt: ''
    };
  },
  methods: {
    handleShowAddClick() {
      this.addData = {
        cardId: '',
        duration: 0,
        is_auto_bill: '0',
        unit: 0,
        baseFee: 0,
        rule: []
      };
      this.nonMemberCardListCopy = this.nonMemberCardList.concat([]);
      this.showAdd = true;
    },
    handleAddRuleClick() {
      let start = 0, end = null, count = 1, price = 0;
      if (Array.isArray(this.addData.rule)) {
        const len = this.addData.rule.length;
        if (len === 0) {
          start = 0;
        } else {
          const last = this.addData.rule[len-1];
          start = last.end;
          if (start == null) {
            this.$Message.error('请先完成上一梯度值设置！');
            return false;
          } else {
            if (!(last.start < last.end)) {
              this.$Message.error('超时规则开始时间必须小于结束时间！');
              return false;
            }
          }
        }
      } else {
        this.addData.rule = [];
      }
      this.addData.rule.push({start, end, count: 1, price: 0});
    },
    handleDeleteRuleClick(index) {
      this.addData.rule.splice(index, 1);
    },
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handleNonMemberCardCancelClick() {
      this.showAdd = false;
      this.getList();
    },
    handleNonMemberCardSaveClick() {
      this.$refs['addData'].validate(valid => {
        if (valid) {
          const cardId = this.addData.cardId;
          let cardName = '';
          if (!!cardId) {
            const card = this.nonMemberCardListCopy.find(item=>(item.card_id==cardId));
            if (!!card) {
              cardName = card.card_name;
            }
          }
          if (Array.isArray(this.addData.rule)) {
            if (this.addData.rule.length > 0) {
              const len = this.addData.rule.length;
              if (this.addData.rule[len-1].end == null) {
                this.addData.rule[len-1].end = 99999;
              }
            }
          } else {
            this.addData.rule = [];
          }
          this.$service.post('/Web/SanRule/save', {
            id: this.addData.id,
            card_id: cardId,
            card_name: cardName,
            base_duration: this.addData.duration,
            is_auto_bill: this.addData.is_auto_bill,
            duration_unit: this.addData.unit + 1,
            base_fee: this.addData.baseFee,
            rule: this.addData.rule
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.info(res.data.errormsg);
              this.showAdd = false;
              this.addData = {
                cardId: '',
                duration: 0,
                is_auto_bill: '0',
                unit: 0,
                baseFee: 0,
                rule: []
              };
              this.getList();
              this.getNonMemberCardList();
            } else {this.$Message.error(res.data.errormsg);}
          });
        }
      });
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    getList() {
      return this.$service.post('/Web/SanRule/getList', {
          page_no: this.currentPage,
          page_size: this.pageSize,
          card_name: this.searchTxt
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.total = parseInt(res.data.data.count);
            this.list = [];
            if (Array.isArray(res.data.data.list)) {
              res.data.data.list.forEach(item => {
                this.list.push({
                  id: item.id,
                  cardId: item.card_id,
                  cardName: item.card_name,
                  is_auto_bill: item.is_auto_bill || '0',
                  duration: parseInt(item.base_duration),
                  unit: parseInt(item.duration_unit) - 1,
                  baseFee: parseFloat(item.base_fee),
                  rule: item.rule
                });
              });
            }
          } else {this.$Message.error(res.data.errormsg);}
        });
    },
    handleDeleteInfo(id) {
      return this.$service.post('/Web/SanRule/del', {id}).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.info(res.data.errormsg);
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    getNonMemberCardList() {
      return this.$service.post('/Web/SanRule/getValueCard').then(res => {
        if (res.data.errorcode == 0) {
          this.nonMemberCardList = res.data.data;
          this.nonMemberCardListCopy = res.data.data;
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  activated() {
    this.getList();
    this.getNonMemberCardList();
  }
};
</script>

<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
