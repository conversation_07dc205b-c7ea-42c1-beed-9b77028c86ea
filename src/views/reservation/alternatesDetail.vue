<template>
<div class="table-wrap">
  <header>
    <Input v-model="postData.search" placeholder="姓名/电话/实体卡号" class="w200" @on-enter="search"></Input>
    <Button type="success" class="search" @click="search">搜索</Button>
  </header>
  <main>
    <Table :columns="columns" :data="tableData" @on-selection-change="handleCheckedArray" stripe disabled-hover></Table>
  </main>
  <footer>
    <!-- <Button type="success" @click="handleBatchCancel">批量取消</Button> -->
    <Page class="page" :total="totalCount" placement="top" :page-size="postData.page_size" :current.sync="postData.page_no" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
    </Page>
  </footer>
</div>
</template>

<script>
// import { formatDate } from 'utils/index.js';

export default {
  name: 'AlternatesDetail',
  data() {
    return {
      totalCount: 0,
      postData: {
        course_schedule_id: this.$route.params.courseScheduleId,
        search: '',
        page_size: 10,
        page_no: 1
      },
      columns: [
        // {
        //   type: 'selection',
        //   width: 120,
        //   align: 'center'
        // },
        {
          title: '头像',
          className: 'avatar-wrap',
          render: (h, params) => {
            return (
              <a
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.user_id}`);
                }}>
                <img class="avatar" src={params.row.avatar} />
              </a>
            );
          }
        },
        {
          title: '姓名',
          render: (h, params) => {
            return (
              <a
                class="link"
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.user_id}`);
                }}>
                {params.row.username ? params.row.username : params.row.nickname ? params.row.nickname : '未知'}
              </a>
            );
          }
        },
        {
          title: '电话',
          key: 'phone'
        },
        {
          title: '预约用卡',
          key: 'name'
        },
        {
          title: '候补时间',
          key: 'create_time',
          width: 200,
          // render: (h, params) => {
          //   return <span>{formatDate(new Date(params.row.create_time * 1000), 'yyyy-M-d HH:mm')}</span>
          // }
        },
        {
          title: '当前排位',
          key: 'ranking'
        },
        {
          title: '操作',
          width: 200,
          render: (h, params) => (
            // 1候补中，2确认中，3候补成功，4候补失败
            <a
              disabled={['3', '4'].includes(params.row.status)}
              onClick={() => {this.cancelAlternate(params.row.id)}}
            >取消</a>
          )
        },
        {
          title: '备注',
          minWidth: 150,
          render: (h, params) =>
            <div class="text_overflow" title={params.row.remark}>
              {params.row.remark || '-'}
            </div>
        }
      ],
      tableData: [],
      checkedArray: [],
    };
  },

  created() {
    this.getRenderList();
  },
  watch: {

  },

  methods: {

    handleCheckedArray(selection) {
      this.checkedArray = selection;
    },

    pageChanged(page) {
      this.postData.page_no = page;
      this.getRenderList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getRenderList();
    },
    search() {
      this.postData.page_no = 1;
      this.getRenderList();
    },
    getRenderList() {
      this.$service
        .post('/Web/ClassMark/class_waitting_list', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    //取消预约
    cancelAlternate(id) {
      this.$Modal.confirm({
        title: '提示',
        content: '您确定要取消候补吗？',
        onOk: () => {
          this.$service.post('/Web/ClassMark/cancel_class_waitting', { id })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success('取消成功');
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .finally(this.getRenderList)
        }
      });
    },
    // 批量取消
    /* handleBatchCancel(val) {
      if(!this.checkedArray.length) return this.$Message.error('请先勾选条目！');

      this.$Modal.confirm({
        title: '提示',
        content: '您确定要批量取消候补吗？',
        onOk: () => {
          const class_mark_ids = this.checkedArray.map(({ id }) => id);

          this.$service.post('xxxxxxxxxx', { class_mark_ids })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success('批量取消成功');
              }else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .finally(this.getRenderList)
        },
      });
    }, */
  }
};
</script>

<style lang="less" scoped>
/deep/.ivu-poptip-inner {
  text-align: left;
}
</style>
