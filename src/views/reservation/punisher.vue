<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="姓名/电话/实体卡号" />
      <Select v-model="courseId" class="option-select" placeholder="状态">
        <Option v-for="item in courseList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <DatePicker v-model="duringDate" type="daterange" placement="bottom-end" placeholder="选择日期" class="option-select" style="width: 220px"></DatePicker>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button v-if="hasExportAuth" @click="handleExcel">导出Excel</Button>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      courseId: '',
      courseList: [
        { value: 0, label: '全部' },
        { value: 1, label: '已完成' },
        { value: 2, label: '惩罚中' },
        { value: 3, label: '已取消' }
      ],
      recorderList: [],
      columns: [
        {
          title: '头像',
          key: 'course',
          className: 'avatar-wrap',
          render: (h, params) => {
            return (
              <a
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.userId}`);
                }}>
                <img class="avatar" src={params.row.course} />
              </a>
            );
          }
        },
        { title: '姓名', key: 'recorder' },
        {
          title: '团课爽约次数',
          key: 'comes',
          render: (h, params) => {
            if (params.row.isGray) {
              return <a disabled>{params.row.comes}</a>;
            } else {
              const url = {
                props: {
                  to: `/Web/Sign/get_miss_sign_list/${params.row.phone}`
                }
              };
              console.log(url);
              return <router-link {...url}>{params.row.comes}</router-link>;
            }
          }
        },
        {
          title: '周期内爽约次数',
          key: 'machine',
          render: (h, params) => (params.row.isGray ? <a disabled>{params.row.machine}</a> : <div>{params.row.machine}</div>)
        },
        {
          title: '最近一次爽约',
          key: 'coach',
          render: (h, params) => (params.row.isGray ? <a disabled>{params.row.coach}</a> : <div>{params.row.coach}</div>)
        },
        {
          title: '惩罚到期时间',
          key: 'signTime',
          render: (h, params) => (params.row.isGray ? <a disabled>{params.row.signTime}</a> : <div>{params.row.signTime}</div>)
        },
        {
          title: '状态',
          key: 'state',
          render: (h, params) => (params.row.isGray ? <a disabled>{params.row.state}</a> : <div>{params.row.state}</div>)
        },
        {
          title: '操作',
          key: 'option',
          width: 150,
          render: (h, params) => {
            if (params.row.isGray) {
              return <a disabled>取消惩罚</a>;
            } else {
              return (
                <div style="display: flex;flex-direction: row;justify-content: space-around;">
                  <a
                    onClick={() => {
                      this.handleSignCancel(params.row);
                    }}>
                    取消惩罚
                  </a>
                </div>
              );
            }
          }
        }
      ],
      list: [],
      searchTxt: '',
      hasExportAuth: true
    };
  },
  methods: {
    getDateString(date) {
      if (!!date) {
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let day = date.getDate();
        day = day < 10 ? '0' + day : day;
        return `${year}-${month}-${day}`;
      } else {
        return '';
      }
    },
    packRow(item) {
      return {
        rowId: item.id,
        userId: item.user_id,
        course: item.avatar,
        recorder: item.username,
        comes: item.total,
        machine: item.cycle_count,
        coach: item.last_miss_time,
        signTime: item.punitive_exp_time,
        state: item.status,
        isGray: item.status == '已完成' || item.status == '已取消',
        phone: item.phone
      };
    },
    handleSearch() {
      this.currentPage = 1;
      if (this.duringDate.length === 2) {
        const dateArr = this.duringDate;
        this.duringDate = [this.getDateString(dateArr[0]), this.getDateString(dateArr[1])];
      }
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    getList() {
      return this.$service
        .post('/Web/MissPunitive/get_miss_punitive_list', {
          search: this.searchTxt,
          status: this.courseId,
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          is_excel: false,
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item));
            });
            this.list = arr;
            this.total = parseInt(res.data.data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleSignCancel(row) {
      return this.$service
        .post('/Web/MissPunitive/cancel_miss_punitive', {
          id: row.rowId,
          cycle_count: row.machine,
          total: row.comes,
          last_miss_time: row.coach,
          punitive_exp_time: row.signTime
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            const self = this;
            setTimeout(() => {
              this.getList();
            }, 1000);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleExcel() {
      this.$service
        .post('/Web/MissPunitive/get_miss_punitive_list', {
          search: this.searchTxt,
          status: this.courseId,
          start_time: this.duringDate[0],
          end_time: this.duringDate[1],
          is_excel: false,
          page_no: 1,
          page_size: this.total
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = [];
              return false;
            }
            let arr = [];
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item));
            });
            this.$refs.table.exportCsv({
              filename: `爽约惩罚-${this.getDateString(new Date())}`,
              columns: this.columns.filter((col, index) => 0 < index && index + 1 < this.columns.length),
              data: arr
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getExcelAuth() {
      this.$service.get('/Web/MissPunitive/miss_punitive_list_excel').then(res => {
        if (res.data.errorcode === 40014) {
          this.hasExportAuth = false;
        }
      });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    }
  },
  created() {
    this.getList();
    this.getExcelAuth();
  }
};
</script>
<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
