<template>
<div class="table-wrap">
  <header>
    <Input v-model="postData.search" placeholder="姓名/电话/实体卡号" class="w200" @on-enter="search"></Input>
    <Button type="success" class="search" @click="search">搜索</Button>
  </header>
  <main>
    <Table ref="table" :columns="columns" :data="tableData" @on-selection-change="handleCheckedArray" stripe disabled-hover></Table>
  </main>
  <footer>
    <Button type="success" @click="signAll">批量签到</Button>
    <Dropdown style="margin-left: 20px" @on-click="otherCase">
      <Button>
        其他操作
        <Icon type="md-arrow-dropdown"></Icon>
      </Button>
      <Dropdown-menu slot="list">
        <Dropdown-item name="1">批量取消</Dropdown-item>
      </Dropdown-menu>
    </Dropdown>
    <Page class="page" :total="totalCount" placement="top" :page-size="postData.page_size" :current.sync="postData.page_no" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
    </Page>
  </footer>
  <div v-if="isShowAdd">
    <Modal v-model="isShowAdd" title="编辑预约人数" :mask-closable="false">
      <Form class="modal-form" style="padding: 0 30px" :label-width="100">
        <Form-item label="会员名称">
          <Input :value="curEditInfo.username ? curEditInfo.username : curEditInfo.nickname ? curEditInfo.nickname : '未知'+'('+curEditInfo.phone+')'" disabled></Input>
        </Form-item>
          <Form-item label="消费会员卡">
            <Select :value="curEditInfo.cu_card_id" disabled>
              <Option :value="curEditInfo.cu_card_id">{{curEditInfo.c_name}}</Option>
            </Select>
          </Form-item>
          <Form-item label="预约人数">
            <Input-number :max="100" :min="1" v-model="resData.num" :disabled="numDisabled"></Input-number>
          </Form-item>
          <Form-item label="单价" v-if="curEditInfo.card_type_id!=1" style="text-align:right;">
            {{cardData.todeduct}}{{curEditInfo.unit}}/人
          </Form-item>
          <Form-item label="总价消费" v-if="curEditInfo.card_type_id!=1" style="text-align:right;">
            <span v-if="curEditInfo.card_type_id!=3">{{resData.num*cardData.todeduct}}{{curEditInfo.unit}}</span>
            <span v-else>{{(resData.num*cardData.todeduct).toFixed(2)}}元</span>
          </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="updateRes" :disabled="curEditInfo.cu_card_id?false:true">确定</Button>
        <Button @click="isShowAdd = false">取消</Button>
      </div>
    </Modal>
  </div>
   <Modal v-model="isShowSeats" title="座位排布" :mask-closable="false">
      <SeatsSet v-if="seatInfo && seatInfo.length" v-model="seatInfo" :is-show-set="false" :is-can-choose="false"/>
      <div slot="footer" class="modal-buttons">
      </div>
    </Modal>
</div>
</template>

<script>
import { formatDate } from 'utils/index.js';
import SeatsSet from "views/stadium/components/SeatsSet";

export default {
  name: 'detail',
  components: { SeatsSet },
  data() {
    return {
      isShowAdd: false, //是否显示预约弹窗
      isShowSeats: false,
      seatInfo: [],
      curEditInfo: {},
      totalCount: 0,
      cardData: {},
      numDisabled: false, //预约人数是否可编辑
      postData: {
        course_schedule_id: this.$route.params.courseScheduleId,
        search: '',
        page_size: 10,
        page_no: 1
      },
      resData: {
        class_mark_id: '',
        card_user_id: '',
        num: '',
        todeduct: ''
      },
      cardList: [],
      columns: [
        {
          type: 'selection',
          width: 120,
          align: 'center'
        },
        {
          title: '头像',
          className: 'avatar-wrap',
          render: (h, params) => {
            return (
              <a
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.user_id}`);
                }}>
                <img class="avatar" src={params.row.avatar} />
              </a>
            );
          }
        },
        {
          title: '姓名',
          render: (h, params) => {
            return (
              <a
                class="link"
                href="javascript:void(0)"
                on-click={name => {
                  this.$router.push(`/member/detail/${params.row.user_id}`);
                }}>
                {params.row.username ? params.row.username : params.row.nickname ? params.row.nickname : '未知'}
              </a>
            );
          }
        },
        {
          title: '电话',
          key: 'phone'
        },
        {
          title: '预约用卡',
          key: 'c_name'
        },
        {
          title: '预约人数',
          key: 'sign_number',
          render: (h, params) => {
            if (!!params.row.sign_number && params.row.is_miss==1) {
              return (<div>{params.row.sign_number}<span style="color:#e60012;">(爽约)</span></div>);
            } else {
              return (<div>{params.row.sign_number}</div>);
            }
          }
        },
        {
          title: '座位号',
          key: 'seats_num',
          render: (h, params) => {
            if (params) {
              return (<i-button
                  type="text"
                  style={{ color: "#52a4ea", minWidth: "0", marginRight: "5px" }}
                  onClick={() => {
                    this.showSeatsDetail(params.row)
                  }}
                >
                  {params.row.seats_num}
                </i-button>);
              } else {
                return (<span>-</span>)
              }
            }

        },
        {
          title: '预约时间',
          key: 'create_time',
          width: 200,
          render: (h, params) => {
            return <span>{formatDate(new Date(params.row.create_time * 1000), 'yyyy-M-d HH:mm')}</span>
          }
        },
        {
          title: '状态',
          render: (h, params) => {
            return <div>{params.row.status == 1 ? '已预约' : params.row.status == 2 ? '已完成' : '已取消'}</div>;
          }
        },
        {
          title: '操作',
          width: 260,
          render: (h, params) => {
            return params.row.outer_type === 1?<div>-</div>:(
              <div class="handle">
                <a
                  onClick={() => {
                    this.goSign(params.row.id);
                  }}
                  disabled={params.row.status != 1 ? true : false}>
                  签到
                </a>
                <a
                  disabled={params.row.status != 1 || params.row.edit != 1 || this.$route.params.waitting_num > 0 ? true : false}
                  onClick={() => {
                    this.handleReser(params.row);
                  }}>
                  编辑人数
                </a>
                <a
                  onClick={() => {
                    this.cancelReservation(params.row.id);
                  }}
                  disabled={params.row.status != 1 ? true : false}>
                  取消预约
                </a>
              </div>
            );
          }
        }
      ],
      tableData: [],
      checkedArray: [],
      handleTips: false
    };
  },
  created() {
    if (this.postData.course_schedule_id) {
      this.getClass();
    }
  },
  watch: {
    isShowAdd(val) {
      //关闭添加弹窗
      if (!val) {
        this.initResData();
      }
    }
  },
  methods: {
    showSeatsDetail(info) {
      this.isShowSeats = true
    },
    handleCheckedArray(selection) {
      this.checkedArray = selection;
    },
    //编辑预约人数
    handleReser(classObj) {
      this.isShowAdd = true;
      this.curEditInfo = classObj;
      let unit = '';
      switch (parseInt(classObj.card_type_id)) {
        case 1:
          unit = '天';
          break;
        case 2:
          unit = '次';
          break;
        case 3:
          unit = '元';
          break;
        case 4:
          unit = '节';
          break;
      }
      for (let item of classObj.apply_card) {
        if (item.card_id == classObj.cu_card_id) {
          this.cardData = item;
          break;
        }
      }
      this.curEditInfo.unit = unit;
      this.resData.num = parseInt(classObj.sign_number);
      this.resData.todeduct = parseFloat(this.cardData.todeduct ? this.cardData.todeduct : 0);
      this.resData.card_user_id = classObj.card_user_id;
      if (classObj.card_type_id == 1) {
        this.numDisabled = true;
      } else {
        this.numDisabled = false;
      }
      this.resData.class_mark_id = classObj.id;
    },
    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getClass();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getClass();
    },
    search() {
      this.postData.page_no = 1;
      this.getClass();
    },
    getClass() {
      this.$service
        .post('/Web/ClassMark/class_mark_detail', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            Array.isArray(data.cm_detail_list) && data.cm_detail_list.forEach(v => {
              v._disabled = v.status != 1 || v.outer_type === 1
            })
            this.tableData = data.cm_detail_list;
            this.totalCount = parseInt(data.count);
            this.seatInfo = data.seats_list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    //签到
    goSign(id) {
      let signItem = {
        class_mark_id: id,
        type: 0
      };
      this.$service.post('/Web/Sign/user_sign', signItem).then(res => {
        if (res.data.errorcode == 0) {
          this.getClass();
          this.$Message.success(res.data.errormsg);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    //批量签到
    signAll() {
      this.$Modal.confirm({
        title: '提示',
        content: '您确定要批量签到吗？',
        onOk: () => {
          let idArr = [];
          this.checkedArray.forEach((item, index) => {
            idArr.push(item.id);
          });
          let idObj = {
            class_mark_ids: idArr,
            type: 0 //PC签到
          };
          if (idObj.class_mark_ids.length == 0) {
            this.$Message.error('请先勾选条目！');
          } else {
            this.$service.post('/Web/Sign/batch_user_sign', idObj).then(response => {
              if (response.data.errorcode == 0) {
                this.checkedArray = []
                this.getClass();
              }
              this.$Message.success(response.data.errormsg);
            });
          }
        },
        onCancel() {}
      });
    },
    //取消预约
    cancelReservation(id) {
      this.$Modal.confirm({
        title: '提示',
        content: '您确定要取消预约吗？',
        onOk: () => {
          this.$service.post('/Web/ClassMark/cancel_class_mark', { class_mark_id: id }).then(res => {
            if (res.data.errorcode == 0) {
              this.getClass();
              this.$Message.success('取消成功');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    },
    //批量取消
    otherCase(val) {
      if(val == 1) {
        this.$Modal.confirm({
          title: '提示',
          content: '您确定要批量取消预约吗？',
          onOk: () => {
            let idArr = [];
            this.checkedArray.forEach((item, index) => {
              idArr.push(item.id);
            });
            let idObj = {
              class_mark_ids: idArr
            };
            if (idObj.class_mark_ids.length == 0) {
              this.$Message.error('请先勾选条目！');
            } else {
              this.$service.post('/Web/ClassMark/batch_cancel_class_mark', idObj).then(response => {
                if (response.data.errorcode == 0) {
                  this.getClass();
                }
                this.$Message.success(response.data.errormsg);
              });
            }
          },
          onCancel() {}
        });
      }
    },
    //初始化添加预约弹窗数据
    initResData() {
      this.numDisabled = false;
      this.resData = {
        class_mark_id: '',
        card_user_id: '',
        num: '',
        todeduct: ''
      };
    },
    updateRes() {
      this.$service
        .post('/Web/ClassMark/update_mark', this.resData)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.getClass();
            this.isShowAdd = false;
            this.initResData();
            this.$Message.success('修改成功');
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }
};
</script>
<style>
.handle a {
  margin-right: 10px;
}
</style>
