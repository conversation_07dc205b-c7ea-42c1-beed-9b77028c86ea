<template>
  <div>
    <router-view v-if="$route.name !== '课程预约'"></router-view>
    <div class="table-wrap" v-else>
      <header>
        <DatePickerWithButton :days.sync="days" @on-change="dateChanged" :showing-biu="false" :clearable="true"></DatePickerWithButton>
        <Select v-model="postData.coach_id" class="w120" filterable>
          <Option value="0">全部教练</Option>
          <Option v-for="item in coachList" :value="item.coach_id" :key="item.coach_id">{{ item.coach_name }}</Option>
        </Select>
        <Select v-model="postData.class_id" class="w120" filterable v-if="classList.length">
          <Option value="0">全部课程</Option>
          <Option v-for="item in classList" :value="item.id" :key="item.id">{{ item.class_name }}</Option>
        </Select>
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>
      <main>
        <Table ref="table" :columns="columns" :data="tableData" stripe disabled-hover></Table>
      </main>
      <footer>
        <!-- <div> -->
          <Button type="success"
                  style="margin-right: 30px;"
                  @click="$router.push('/reservation/class/resProtocol')">团课预约协议</Button>
          <Dropdown style="margin-left: 20px" @on-click="otherCase">
            <Button>
              其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <Dropdown-menu slot="list">
              <Dropdown-item name="0">导出Excel</Dropdown-item>
            </Dropdown-menu>
          </Dropdown>
        <!-- </div> -->
        <Page class="page" :total="totalCount" :page-size="postData.page_size" :current.sync="postData.page_no" placement="top" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
        </Page>
      </footer>
      <Modal v-model="isShowAdd" width="700" title="选择预约会员" :mask-closable="false">
        <Form class="modal-form" style="padding: 0 30px" :label-width="100">
          <Form-item label="会员名称">
            <userSearch url="/Web/Sign/search_user_info" v-model="resData.user_id"></userSearch>
          </Form-item>
          <div v-show="showAddInput">
            <Form-item label="消费会员卡">
              <Select v-model="resData.card_user_id" @on-change="cardChange">
                <Option v-for="item in cardList" :value="item.card_user_id" :key="item.card_user_id">{{item.card_name}}（剩{{item.last_num}}{{item.unit}})</Option>
              </Select>
              <div v-show="addMarkWarning" style="line-height: normal; margin-top: 4px">
                <Icon type="ios-alert-outline" size="16" color="#F59A23" />
                <span style="vertical-align: middle; padding-left: 4px">{{addMarkWarning}}</span>
              </div>
            </Form-item>
            <Form-item label="预约人数">
              <Input-number :max="canAddMax" :min="1" v-model="resData.num" :disabled="numDisabled"></Input-number>
            </Form-item>
            <Form-item v-if="isShowAdd && showAddInput && currentClass.seats_list && currentClass.seats_list.length" label="选座">
             <SeatsSet :choose-number="resData.num" v-model="currentClass.seats_list" @on-choose="chooseSeat"/>
            </Form-item>
            <Form-item label="单价" v-if="currentCard.card_type_id!=1" style="text-align:right;">
              {{currentCard.todeduct}}{{currentCard.unit}}/人
            </Form-item>
            <Form-item label="总价消费" v-if="currentCard.card_type_id!=1" style="text-align:right;">
              <span v-if="currentCard.card_type_id!=3">{{resData.num*currentCard.todeduct}}{{currentCard.unit}}</span>
              <span v-else>{{(resData.num*currentCard.todeduct).toFixed(2)}}元</span>
            </Form-item>
          </div>
        </Form>
        <div slot="footer" class="modal-buttons">
          <Button type="success" @click="handleAddRes" :disabled="disabledAddRes">确定</Button>
          <Button @click="isShowAdd = false">取消</Button>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
  import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';
  import userSearch from 'components/user/userSearch';
  import SeatsSet from "views/stadium/components/SeatsSet";
  import { formatDate } from 'utils/index.js';

  export default {
    name: 'TheClass',
    data() {
      return {
        maxReserveNum: 1,
        isShowAdd: false, //是否显示预约弹窗
        showAddInput: false, //是否显示预约的表单项
        disabledAddRes: true, //预约条件是否不满足提交
        totalCount: 0,
        currentCard: {}, //预约弹窗中选中的卡信息
        currentClass: {}, //预约时选中的课程信息
        numDisabled: false, //预约人数是否可编辑
        addMarkWarning: '', // 控制是否显示超过最大预约次数提示
        days: [Date.now(), this.$route.query.from == 'index' ? Date.now() : Date.now() + 5 * 24 * 60 * 60 * 1000],
        postData: {
          is_export: 0,
          s_date: '',
          e_date: '',
          class_id: this.$route.query.classId ? this.$route.query.classId : 0,
          coach_id: '0',
          page_size: 10,
          page_no: 1
        },
        resData: {
          card_user_id: '',
          user_id: '',
          course_schedule_id: '',
          card_type_id: '',
          num: 1,
          seats: '',
          todeduct: ''
        },
        cardList: [],
        columns: [
          {
            title: '课程名称',
            key: 'class_name'
          },
          {
            title: '开课时间',
            key: 'b_time',
            width: 250,
            render: (h, params) => {
              return <div>{formatDate(new Date(params.row.b_time * 1000), 'yyyy-M-d HH:mm')}</div>;
            }
          },
          {
            title: '课程教练',
            key: 'coach_name'
          },
          {
            title: '教室',
            key: 'classroom_name'
          },
          {
            title: '可预约人数',
            key: 'reserve_number'
          },
          {
            title: '已预约人数',
            key: 'sncount',
            render: (h, params) => {
              const data = {
                props: {
                  to: { name: '预约详情', params: { courseScheduleId: params.row.schedule_id, waitting_num: params.row.waitting_num } }
                }
              };
              return <router-link {...data}>{params.row.sncount}</router-link>;
            }
          },
          {
            title: '候补人数',
            key: 'waitting_num',
            render: (h, params) => {
                const props = {
                  to: { name: '候补详情', params: { courseScheduleId: params.row.schedule_id } }
                }
              return <router-link props={props}>{params.row.waitting_num}</router-link>;
            }
          },
          {
            title: '操作',
            render: (h, params) => {
              if (params.row.outer_type !== 1) {
                return (
                  <a
                    onClick={() => {
                      this.handleReser(params.row);
                    }}>
                    预约
                  </a>
                );
              } else {
                return <span>-</span>
              }
            }
          }
        ],
        excelcolumns: [
          {
            title: '课程名称',
            key: 'class_name'
          },
          {
            title: '开课时间',
            key: 'b_time',
          },
          {
            title: '课程教练',
            key: 'coach_name'
          },
          {
            title: '教室',
            key: 'classroom_name'
          },
          {
            title: '可预约人数',
            key: 'reserve_number'
          },
          {
            title: '预约会员',
            key: 'username'
          },
          {
            title: '会员电话',
            key: 'phone'
          },
          {
            title: '预约用卡',
            key: 'card_name'
          },
          {
            title: '预约人数',
            key: 'sign_number'
          },
          {
            title: '状态',
            key: 'status',
          },
        ],
        tableData: [],
        coachList: [],
        classList: []
      };
    },
    computed: {
      //能预约的最大值
      canAddMax() {
        let num = parseInt(this.currentClass.reserve_number) - parseInt(this.currentClass.sncount);
        return Math.min(Math.max(num, 1), this.maxReserveNum);
      }
    },
    components: {
      DatePickerWithButton,
      SeatsSet,
      userSearch
    },
    activated() {
      this.getClass();
    },
    watch: {
      'resData.user_id'(val) {
        let courseScheduleId = this.resData.course_schedule_id;
        if (val) {
          this.getUserMarkCards(val, this.resData.course_schedule_id);
        } else {
          this.initResData();
          this.resData.course_schedule_id = courseScheduleId;
        }
      },
      isShowAdd(val) {
        //关闭添加弹窗
        if (!val) {
          this.initResData();
        }
      }
    },
    methods: {
      chooseSeat(ids) {
        this.resData.seats = ids
      },
      async otherCase(val) {
        if(val == 0) {
          // if(this.totalCount == 0) {
          //   this.$Message.error("没有可导出的数据！");
          //   return;
          // }
           /* 调整为异步导出 */
          this.getExcelData();
          /* let data = await this.getExcelData();
          data.map(item => {
            item.status = item.status === '1' ? '已预约' : item.status === '2' ? '已完成' : item.status === '3' ? '已取消' : '';
            item.b_time = formatDate(new Date(item.b_time * 1000), 'yyyy-M-d HH:mm');
          })
          this.$refs.table.exportCsv({
            filename: `团课预约名单${this.postData.s_date}至${this.postData.e_date}`,
            columns: this.excelcolumns,
            data: data
          }); */
        }
      },
      getExcelData() {
        let postObj = Object.assign({}, this.postData);
        postObj.page_size = this.totalCount;
        postObj.is_export = 1;
        return this.$service.post('Web/ClassMark/pc_class_mark_list', postObj, { isExport: true }).then(res => {
          if(res.status == 200) {
            if(res.data.errorcode == 0) {
              // return res.data.data.class_mark_list;
              /* 调整为异步导出 */
              this.$Message.success({
                content:'导出任务运行中，请稍后到消息中心下载!',
                duration: 3
              })
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            console.error("网络有问题！")
          }
        }).catch(err => {
          console.error(err)
        })
      },
      //预约
      handleReser(classObj) {
        this.isShowAdd = true;
        this.currentClass = classObj;
        this.resData.course_schedule_id = classObj.schedule_id;
      },
      dateChanged(val) {
        this.postData.s_date = val[0];
        this.postData.e_date = val[1];
      },
      pageChanged(page) {
        this.postData.page_no = page;
        this.getClass();
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.getClass();
      },
      cardChange(cardId) {
        if (cardId) {
          let selectedCard = this.cardList.find(item => item.card_user_id == cardId);
          this.resData.num = 1;
          if (selectedCard.card_type_id == 1) {
            this.numDisabled = true;
            this.resData.todeduct = 0;
            // 获取该卡是否已超过最大预约次数
            const card_user_id = selectedCard.card_user_id
            this.$service.post('/Web/ClassMark/check_limit_card', { card_user_id }).then(res => {
              if([0, 40001].includes(res.data.errorcode)) {
                this.addMarkWarning = res.data.errormsg;
              }else {
                this.$Message.error(res.data.errormsg);
              }
            }).catch(err => console.error(err));
          } else {
            this.numDisabled = false;
            this.resData.todeduct = selectedCard.todeduct;
            let num = this.currentClass.reserve_number - this.currentClass.sncount;
            this.maxReserveNum = +selectedCard.maxresv_num || num;
            this.addMarkWarning = '';
          }
          this.resData.card_type_id = selectedCard.card_type_id;
          this.resData.card_id = selectedCard.card_id;
          this.currentCard = selectedCard;
        }
      },
      search() {
        this.postData.page_no = 1;
        this.getClass();
      },
      getClass() {

        // date range must be less than 30 days
        // if (this.days&& !checkRangeLessThan30(this.days)) {
        //   this.$Message.warning(SEARCH_DATE_HINT);
        //   return;
        // }

        this.$service
          .post('Web/ClassMark/pc_class_mark_list', this.postData)
          .then(res => {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              this.tableData = data.class_mark_list;
              this.totalCount = data.count;
              this.coachList = data.coach_list;
              this.classList = data.open_class_list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      //初始化添加预约弹窗数据
      initResData() {
        this.showAddInput = false;
        this.currentCard = {};
        this.cardList = [];
        this.numDisabled = false;
        this.resData = {
          card_user_id: '',
          user_id: '',
          course_schedule_id: '',
          card_type_id: '',
          num: 1,
          todeduct: '',
          seats: '',
          card_id: ''
        };
        this.addMarkWarning = '';
      },
      checkUserLimitByType(params) {
        return this.$service.post('/Web/UserBlacklist/checkUser', params, 
        { headers: { 'Content-Type': 'application/json' } }).then(res => {
          return res.data.data
        })
      },
      async handleAddRes() {
        const flag = await this.checkUserLimitByType({
          user_id: this.resData.user_id,
          bus_id: this.$store.state.busId,
          member_rule: 3,
          loading: true
        })
        if(flag) {
          this.$Modal.confirm({
            title: '确认预约?',
            content: '此会员已在门店黑名单中',
            okText: '仍要预约',
            onOk: () => {
              this.addRes()
            },
          });
        } else {
          this.addRes()
        }
      },
      addRes() {
        this.$service
          .post('/Web/ClassMark/add_mark', this.resData)
          .then(res => {
            if (res.data.errorcode == 0) {
              this.initResData();
              this.getClass();
              this.isShowAdd = false;
              this.$Message.success('预约成功');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      },
      getUserMarkCards(userId, classId) {
        let postData = {};
        postData.user_id = userId;
        postData.course_schedule_id = classId;
        this.$service
          .post('/Web/ClassMark/get_user_mark_cards', postData)
          .then(res => {
            if (res.data.errorcode == 0) {
              this.disabledAddRes = false;
              this.showAddInput = true;
              let list = res.data.data;
              for (let item of list) {
                let unit = '';
                switch (parseInt(item.card_type_id)) {
                  case 1:
                    unit = '天';
                    break;
                  case 2:
                    unit = '次';
                    break;
                  case 3:
                    unit = '元';
                    break;
                  default:
                    unit = '节';
                }
                item.unit = unit;
              }
              this.resData.card_user_id = list[0].card_user_id;
              this.resData.card_id = list[0].card_id;
              this.cardList = list;
              this.cardChange(list[0].card_user_id)
            } else {
              this.disabledAddRes = true;
              this.showAddInput = false;
              this.currentCard = {};
              this.cardList = [];
              this.numDisabled = false;
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    }
  };
</script>
