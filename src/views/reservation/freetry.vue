<template>
<div class="tab-table-wrap customized-tabs">
      <res-table :clientStatus="clientStatus" :marketersList="marketersList" :resup="resup" :visup="visup" v-on:titlechange="titleChange" />
  </Tabs>
</div>
</template>
<script>
import ResTable from './components/restable.vue';
import { formatDate } from 'utils'

export default {
  name: 'freetry',
  data() {
    return {
        activeIndex: 0,
        activated: [0],
        restitleCount: '',
        vistitleCount: '',
        marketersList: [],
        resup: true,
        visup: true,
        clientStatus: [
            {item: '预约中', id: 1},
            {item:  '已到店', id: 3},
            {item: '未到店', id: 2},
            {item: '已离店', id: 4}
        ],

    }
  },
  components: {
    ResTable
  },
  created() {
    this.getMarketers();
  },
  methods: {
    titleChange(restitle,vistitle,resup,visup) {
      this.restitleCount = restitle;
      this.vistitleCount = vistitle;
      this.resup = resup;
      this.visup = visup;
    },
    clickTabs(index) {
      this.activeIndex = index
      const active = document.querySelector('.ivu-tabs-ink-bar')
      const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`
      active.setAttribute('class', className)
      if (!this.activated.includes(index)) {
        this.activated.push(index)
      }
    },
    getMarketers() {
      const url = "/Web/Marketers/get_all_marketers_list"
      this.$service
        .get(url)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.marketersList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },

  }
}
</script>
<style lang="less" scoped>
</style>
