<template>
  <div class="container">
    <div class="search-panel">
      <Card>
        <div class="search-line">
          <div class="search-item">
            <div class="label">签约场馆</div>
            <Select v-model="selectBusId" placeholder="请选择场馆" class="value-200" @on-change="getPlanList" filterable
              transfer>
              <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
          </div>
        </div>
      </Card>
    </div>
    <div class="search-panel">
      <Card>
        <div class="search-line">
          <div class="search-item">
            <div class="label">请选择月付方案</div>
            <Button type="success" style="margin-left: 10px;" @click="getPlanList">同步月付方案</Button>
          </div>
        </div>
        <div class="corridor">
          <div v-for="(item, index) in planList" :key="item.id" @click="setPlanIndex(index)">
            <Card class="corridor-item" :class="planIndex === index ? 'corridor-item-active' : ''">
              <div class="title">{{ item.product_title }}</div>
              <template v-if="item.product_type == 1">
                <div class="line">
                  <div class="label">首月</div>
                  <div class="value">¥{{ item.down_payment }}</div>
                </div>
                <div class="line">
                  <div class="label">后续</div>
                  <div class="value">¥{{ item.deduction_amount }}</div>
                </div>
              </template>
              <template v-else>
                <div class="line">
                  <div class="label">签约</div>
                  <div class="value">¥{{ item.combination_amount }}</div>
                </div>
                <div class="line">
                  <div class="label">每期</div>
                  <div class="value">¥{{ item.deduction_amount }}</div>
                </div>
              </template>
              <div class="line">
                <div class="label">使用权益</div>
                <div class="value">{{ item.periods }}个月</div>
              </div>
            </Card>
          </div>
        </div>
      </Card>
    </div>
    <div class="search-panel">
      <Card>
        <Form label-position="right" ref="refForm" :model="postForm" :label-width="220">
          <Form-Item label="请输入您的场馆月综合成本" prop="cost"
            :rules="{ required: true, pattern: /^-?[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数' }">
            <Input placeholder="请填写" type="number" v-model="postForm.cost" style="width: 200px;">
            <span slot="append">万元</span>
            </Input>
          </Form-Item>
          <Form-Item label="请输入您的场馆月新增会员数量" prop="newMemberCount"
            :rules="{ required: true, pattern: /^[1-9][0-9]*$/, message: '数量必须为正整数' }">
            <Input placeholder="请填写" type="number" v-model="postForm.newMemberCount" style="width: 200px;">
            <span slot="append">人</span>
            </Input>
          </Form-Item>
          <Form-Item label="请输入您的场馆月签约会员解约率" prop="unsignedRate" :rules="{
            required: true, pattern: /^(?:[0-4]?\d(?:\.\d{1,2})?|50(?:\.0{1,2})?)$/, message: '最高为50%且只能保留两位小数'
          }">
            <Input placeholder="请填写" type="number" v-model="postForm.unsignedRate" style="width: 200px;">
            <span slot="append">%</span>
            </Input>
          </Form-Item>
        </Form>
      </Card>
    </div>
    <div class="search-panel" style="text-align: center;">
      <Button type="success" @click="handleCalculate" :disabled="!getPlan">开始估算</Button>
    </div>
    <Divider />
    <div class="search-panel" v-if="estimated.needCost && getPlan">
      <Card>
        <div class="search-line">
          <div class="search-item">
            <div class="label">以下为估算结果: </div>
          </div>
        </div>
        <div class="search-line" style="flex-wrap: wrap;">
          <div class="search-item">
            <div class="label">场馆月综合成本:</div>
            <div class="value">{{ postForm.cost }}万</div>
          </div>
          <div class="search-item">
            <div class="label">场馆月新增会员数量:</div>
            <div class="value">{{ postForm.newMemberCount }}个</div>
          </div>
          <div class="search-item">
            <div class="label">会员首期差价金额:</div>
            <div class="value">{{ estimated.firstDiffAmount }}元</div>
          </div>
          <div class="search-item">
            <div class="label">单会员月付费:</div>
            <div class="value">{{ getPlan.deduction_amount }}元</div>
          </div>
          <div class="search-item">
            <div class="label">场馆月签约会员解约率:</div>
            <div class="value">{{ postForm.unsignedRate }}%</div>
          </div>
          <div class="search-item">
            <div class="label">达到收支平衡总计需要的新会员数量:</div>
            <div class="value">{{ estimated.needNewMemberCount }}个</div>
          </div>
          <div class="search-item">
            <div class="label">达到收支平衡的时间总计需要:</div>
            <div class="value">{{ estimated.needTime }}个月</div>
          </div>
          <div class="search-item">
            <div class="label">达到收支平衡的备用金总金额:</div>
            <div class="value">{{ estimated.needCost }}元</div>
          </div>
        </div>
        <div class="search-line" style="margin: 20px 0 10px 0;">
          <div class="search-item">
            <div class="label">场馆1年月付经营数据预估:</div>
          </div>
        </div>
        <Table :columns="columns" :data="tableData" stripe></Table>
        <div class="search-line">
          <div class="search-item">
            <div class="label">场馆1年月付经营结余:</div>
            <div class="value">{{ surplus }}元</div>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import Big from 'big.js'

export default {
  name: 'PredictiveValue',
  data() {
    return {
      postForm: {
        cost: '',
        newMemberCount: '',
        unsignedRate: 15,
      },
      estimated: {
        firstDiffAmount: 0,
        needNewMemberCount: 0,
        needTime: 0,
        needCost: 0,
      },
      selectBusId: '',
      planIndex: 0,
      planList: [],
      // planTotal: 0,
      columns: [
        {
          title: ' ',
          key: 'name',
        },
        {
          title: '第1个月',
          key: 'first',
        },
        {
          title: '第2个月',
          key: 'second',
        },
        {
          title: '第3个月',
          key: 'third',
        },
        {
          title: '第4个月',
          key: 'fourth',
        },
        {
          title: '第5个月',
          key: 'fifth',
        },
        {
          title: '第6个月',
          key: 'sixth',
        },
        {
          title: '第7个月',
          key: 'seventh',
        },
        {
          title: '第8个月',
          key: 'eighth',
        },
        {
          title: '第9个月',
          key: 'ninth',
        },
        {
          title: '第10个月',
          key: 'tenth',
        },
        {
          title: '第11个月',
          key: 'eleventh',
        },
        {
          title: '第12个月',
          key: 'twelfth',
        }
      ],
      tableData: [],
      surplus: 0
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
    getPlan() {
      if (!this.planList.length) return null
      const plan = this.planList[this.planIndex]
      plan.deduction_amount = Number(plan.deduction_amount || 0).toFixed(2)
      return plan
    }
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    getPlanList() {
      this.planIndex = 0
      this.resetCalculate()
      return this.$service.post('/Web/BusinessFit/businessCalc', {
        bus_id: this.selectBusId,
        page_no: 1,
        page_size: 1000
      }).then(res => {
        if (res.data.errorcode === 0) {
          const planList = res.data.data.list
          planList.forEach(plan => {
            let amount = new Big(0)
            if (Array.isArray(plan.combine_list)) {
              plan.combine_list.forEach(item => {
                amount = amount.plus(new Big(item.amount))
              })
            }
            plan.combination_amount = amount.toFixed(2)
          })
          // this.planTotal = Number(res.data.data.count)
          this.planList = planList
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setPlanIndex(index) {
      this.planIndex = index
      this.resetCalculate()
    },
    resetCalculate() {
      this.estimated = {
        firstDiffAmount: 0,
        needNewMemberCount: 0,
        needTime: 0,
        needCost: 0
      }
      this.tableData = []
    },
    async handleCalculate() {
      if (!this.getPlan) return this.$Message.warning('请先选择月付方案')
      const flag = await this.$refs.refForm.validate()
      if (!flag) return

      const cost = new Big(this.postForm.cost).times(10000)
      const members = cost.div(this.getPlan.deduction_amount)

      // 场馆月综合成本/单会员月付费金额+（场馆月综合成本/单会员月付费金额*解约率）
      this.estimated.needNewMemberCount = members.plus(members.times(this.postForm.unsignedRate).div(100)).toFixed(0)
      // 达到收支平衡总计需要的新会员数量 / 场馆月新增会员数量，不管小数点后面几位，全部在个位进1
      this.estimated.needTime = Math.ceil(new Big(this.estimated.needNewMemberCount).div(this.postForm.newMemberCount))
      // 将计算出来的每月备用金金额相加，每月的备用金金额 = 场馆月综合成本 -（月营业收入 + 月差价收入），如果月备用金金额为负数，则取为0
      this.estimated.needCost = 0

      // 会员首期差价金额为：会员首期支付金额-会员月付单期金额；
      if (this.getPlan.product_type == 1) {
        this.estimated.firstDiffAmount = new Big(this.getPlan.down_payment).minus(this.getPlan.deduction_amount).toFixed(2)
      } else if (this.getPlan.product_type === 2) {
        this.estimated.firstDiffAmount = new Big(this.getPlan.combination_amount).minus(this.getPlan.deduction_amount).toFixed(2)
      }

      // 1.按12个月，显示计算出来的月营业收入、月差价收入、月备用金金额以及月结余；
      // 2.月营业收入算法：单会员月付费金额 * 场馆月新增会员数量
      // 3.月差价收入算法：会员首期差价金额 * 场馆月新增会员数量
      // 4.月备用金算法：每月的备用金金额 = 场馆月综合成本 -（月营业收入 + 月差价收入），如果月备用金金额为负数，则取为0
      // 5.月结余金额算法：（月营业收入 + 月差价收入）-场馆月解约金额 - 场馆月成本金额，如果月结余金额为负数，则取为0
      // 6.月综合成本：显示上面所填写的场馆月综合成本
      const list = []
      // const nameList = ['月综合成本', '营业收入', '差价收入', '解约金额', '备用金', '结余']
      // 月综合成本
      list.push({
        name: '月综合成本',
        first: cost.toFixed(2),
        second: cost.toFixed(2),
        third: cost.toFixed(2),
        fourth: cost.toFixed(2),
        fifth: cost.toFixed(2),
        sixth: cost.toFixed(2),
        seventh: cost.toFixed(2),
        eighth: cost.toFixed(2),
        ninth: cost.toFixed(2),
        tenth: cost.toFixed(2),
        eleventh: cost.toFixed(2),
        twelfth: cost.toFixed(2)
      })
      // 营业收入
      const amount = new Big(this.getPlan.deduction_amount).times(this.postForm.newMemberCount)
      list.push({
        name: '营业收入',
        first: amount.toFixed(2),
        second: amount.times(2).toFixed(2),
        third: amount.times(3).toFixed(2),
        fourth: amount.times(4).toFixed(2),
        fifth: amount.times(5).toFixed(2),
        sixth: amount.times(6).toFixed(2),
        seventh: amount.times(7).toFixed(2),
        eighth: amount.times(8).toFixed(2),
        ninth: amount.times(9).toFixed(2),
        tenth: amount.times(10).toFixed(2),
        eleventh: amount.times(11).toFixed(2),
        twelfth: amount.times(12).toFixed(2)
      })
      // 差价收入
      const diffAmount = new Big(this.estimated.firstDiffAmount).times(this.postForm.newMemberCount)
      list.push({
        name: '差价收入',
        first: diffAmount.toFixed(2),
        second: diffAmount.toFixed(2),
        third: diffAmount.toFixed(2),
        fourth: diffAmount.toFixed(2),
        fifth: diffAmount.toFixed(2),
        sixth: diffAmount.toFixed(2),
        seventh: diffAmount.toFixed(2),
        eighth: diffAmount.toFixed(2),
        ninth: diffAmount.toFixed(2),
        tenth: diffAmount.toFixed(2),
        eleventh: diffAmount.toFixed(2),
        twelfth: diffAmount.toFixed(2)
      })
      // 解约金额
      const rate = new Big(this.postForm.unsignedRate).div(100)
      list.push({
        name: '解约金额',
        first: '0.00',
        second: amount.times(2).times(rate).toFixed(2),
        third: amount.times(3).times(rate).toFixed(2),
        fourth: amount.times(4).times(rate).toFixed(2),
        fifth: amount.times(5).times(rate).toFixed(2),
        sixth: amount.times(6).times(rate).toFixed(2),
        seventh: amount.times(7).times(rate).toFixed(2),
        eighth: amount.times(8).times(rate).toFixed(2),
        ninth: amount.times(9).times(rate).toFixed(2),
        tenth: amount.times(10).times(rate).toFixed(2),
        eleventh: amount.times(11).times(rate).toFixed(2),
        twelfth: amount.times(12).times(rate).toFixed(2)
      })
      // 备用金
      const getPrice = (val) => (Number(val) > 0 ? val.toFixed(2) : '0.00')
      const backup = {
        name: '备用金',
        first: getPrice(cost.minus(amount).minus(diffAmount)),
        second: getPrice(cost.minus(amount.times(2)).minus(diffAmount)),
        third: getPrice(cost.minus(amount.times(3)).minus(diffAmount)),
        fourth: getPrice(cost.minus(amount.times(4)).minus(diffAmount)),
        fifth: getPrice(cost.minus(amount.times(5)).minus(diffAmount)),
        sixth: getPrice(cost.minus(amount.times(6)).minus(diffAmount)),
        seventh: getPrice(cost.minus(amount.times(7)).minus(diffAmount)),
        eighth: getPrice(cost.minus(amount.times(8)).minus(diffAmount)),
        ninth: getPrice(cost.minus(amount.times(9)).minus(diffAmount)),
        tenth: getPrice(cost.minus(amount.times(10)).minus(diffAmount)),
        eleventh: getPrice(cost.minus(amount.times(11)).minus(diffAmount)),
        twelfth: getPrice(cost.minus(amount.times(12)).minus(diffAmount))
      }
      list.push(backup)
      let sumBackup = new Big(0)
      for (const key in backup) {
        if (Object.hasOwnProperty.call(backup, key) && key !== 'name') {
          const element = backup[key];
          // this.estimated.needCost += Number(element)
          sumBackup = sumBackup.plus(element)
        }
      }
      this.estimated.needCost = sumBackup.toFixed(2)
      // 结余
      list.push({
        name: '结余',
        first: getPrice(amount.plus(diffAmount).minus(cost)),
        second: getPrice(amount.times(2).plus(diffAmount).minus(amount.times(2).times(rate)).minus(cost)),
        third: getPrice(amount.times(3).plus(diffAmount).minus(amount.times(3).times(rate)).minus(cost)),
        fourth: getPrice(amount.times(4).plus(diffAmount).minus(amount.times(4).times(rate)).minus(cost)),
        fifth: getPrice(amount.times(5).plus(diffAmount).minus(amount.times(5).times(rate)).minus(cost)),
        sixth: getPrice(amount.times(6).plus(diffAmount).minus(amount.times(6).times(rate)).minus(cost)),
        seventh: getPrice(amount.times(7).plus(diffAmount).minus(amount.times(7).times(rate)).minus(cost)),
        eighth: getPrice(amount.times(8).plus(diffAmount).minus(amount.times(8).times(rate)).minus(cost)),
        ninth: getPrice(amount.times(9).plus(diffAmount).minus(amount.times(9).times(rate)).minus(cost)),
        tenth: getPrice(amount.times(10).plus(diffAmount).minus(amount.times(10).times(rate)).minus(cost)),
        eleventh: getPrice(amount.times(11).plus(diffAmount).minus(amount.times(11).times(rate)).minus(cost)),
        twelfth: getPrice(amount.times(12).plus(diffAmount).minus(amount.times(12).times(rate)).minus(cost))
      })

      const lastItem = list[list.length - 1]
      let sum = new Big(0)
      for (const key in lastItem) {
        if (Object.hasOwnProperty.call(lastItem, key) && key !== 'name') {
          sum = sum.plus(lastItem[key])
        }
      }
      this.surplus = sum.toFixed(2)
      this.tableData = list
    }
  },
  async created() {
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    this.selectBusId = this.busId
    this.getPlanList()
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}

.search-panel {
  margin: 20px;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;

      .label {
        font-size: 14px;
        color: #333;
      }

      .label-right {
        .label;
        width: 370px;
        text-align: right;
        margin: 10px;
      }

      .value {
        font-size: 16px;
        font-weight: bold;
        color: #999;
        margin: 4px 40px 4px 10px;
      }

      .value-200 {
        .value;
        width: 200px;
      }
    }
  }

  .corridor {
    .wrap-line;
    overflow-x: auto;

    .corridor-item {
      width: 300px;
      height: 150px;
      margin: 10px;
      flex-shrink: 0;
      cursor: pointer;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .line {
        .wrap-line;

        .label {
          font-size: 14px;
          color: #333;
        }

        .value {
          font-size: 16px;
          color: #999;
          margin-left: 13px;
        }
      }
    }

    .corridor-item-active {
      border: 1px dashed #2b8df2;
      box-sizing: border-box;
      background: aliceblue;
      box-shadow: 4px 6px 8px 0 rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
