<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Select class="option-select" placeholder="年份" v-model="yearValue" @on-change="getList">
        <Option v-for="year in yearList" :value="year" :key="year">{{ year }} 年</Option>
      </Select>
      <!-- <Button type="success" @click="handleSearch">搜索</Button> -->
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
      <Col span="22" offset="1">
      <div class="total">
        <div class="total-item">
          <span class="total-label">月付签约会员统计说明：</span>
          <span class="total-value">
            支付宝月付因支持会员在同一个月份签约多个月付产品，所以我们以会员维度进行统计时，会为您作会员的排重处理，但在查看明细时，将按合约显示所有的数据，不做排重处理，所以，有可能明细的数据会大于总表的数据。
          </span>
        </div>
      </div>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button @click="handleExcel">导出Excel</Button>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
// import { ref, defineComponent } from 'vue';
// import service from 'src/service'
// import { Message } from 'iview';
// import router from 'src/router'

// defineComponent({
//   name: 'Alipay2Statistics',
// })

export default {
  name: 'Alipay2Stat',
  data() {
    const startYear = 2022
    const currentYear = new Date().getFullYear()
    const yearList = []
    for (let i = startYear; i <= currentYear; i++) {
      yearList.push(i)
    }
    const createLink = (h, params, key, category) => {
      if (params.row.month === '合计') {
        return h('div', params.row[key])
      } else {
        return h('a', {
          on: {
            click: () => {
              const date = this.yearValue + '-' + params.row.month.split('月')[0]
              this.$router.push({ name: '会员明细统计', params: { date, category } })
            }
          }
        }, params.row[key])
      }
    }
    return {
      yearValue: null,
      yearList,
      list: [],
      columns: [
        {
          title: '月份',
          key: 'month',
        },
        {
          title: '新增签约会员总数量',
          key: 'additional_subscription_count',
          render: (h, params) => {
            return createLink(h, params, 'additional_subscription_count', '1')
          }
        },
        {
          title: '履约中会员总数量',
          key: 'cumulative_subscription_count',
          render: (h, params) => {
            return createLink(h, params, 'cumulative_subscription_count', '2')
          }
        },
        {
          title: '扣款成功会员总数量',
          key: 'paid_count',
          render: (h, params) => {
            return createLink(h, params, 'paid_count', '3')
          }
        },
        {
          title: '扣款失败会员总数量',
          key: 'pay_failed_count',
          render: (h, params) => {
            return createLink(h, params, 'pay_failed_count', '4')
          }
        },
        {
          title: '解约会员总数量',
          key: 'additional_surrender_count',
          render: (h, params) => {
            return createLink(h, params, 'additional_surrender_count', '5')
          }
        },
        {
          title: '履约完结会员总数量',
          key: 'additional_end_count',
          render: (h, params) => {
            return createLink(h, params, 'additional_end_count', '6')
          }
        },
        {
          title: '会员违约率',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '会员违约率 '),
              h('Icon', { props: { type: 'md-help-circle' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '月会员违约率的计算规则：'),
                h('br'),
                h('p', '为确保月会员违约率计算的准确，我们将根据会员月付签约的实际月份，实时获取后续月份会员月付订单的履约状态进行违约率的计算。'),
                h('br'),
                h('p', '计算公式为：月解约的会员数量/月签约会员总数量*100%')
              ])
            ])
          },
          key: 'surrender_rate_text',
        }
      ]
    }
  },
  methods: {
    getList() {
      const date = this.yearValue
      return this.$service.post('/Web/BusinessFit/memberByStore', { date }).then(({ data }) => {
        if (data.errorcode === 0) {
          const listValue = data.data
          listValue.forEach(item => {
            item.surrender_rate_text = `${item.surrender_rate || 0} %`
          })
          this.list = listValue
        } else {
          this.$Message.error(data.errormsg)
        }
      })
    },
    handleExcel() {
      this.$refs.table.exportCsv({
        filename: '月付签约会员统计-' + this.yearValue,
        columns: this.columns,
        data: this.list
      })
    }
  },
  created() {
    this.yearValue = new Date().getFullYear()
  },
  activated() {
    this.getList()
  },
}

// search params
// const yearList = ref([])
// const startYear = 2022
// const currentYear = new Date().getFullYear()
// for (let i = startYear; i <= currentYear; i++) {
//   yearList.value.push(i)
// }

// const yearValue = ref(null)

// const handleSearch = () => {
//   getList()
// }

// // table
// const createLink = (h, params, key, category) => {
//   if (params.row.month === '合计') {
//     return h('div', params.row[key])
//   } else {
//     return h('a', {
//       on: {
//         click: () => {
//           const date = yearValue.value + '-' + params.row.month.split('月')[0]
//           router.push({ name: '会员明细统计', params: { date, category } })
//         }
//       }
//     }, params.row[key])
//   }
// }
// const columns = ref([
//   {
//     title: '月份',
//     key: 'month',
//   },
//   {
//     title: '新增签约会员总数量',
//     key: 'additional_subscription_count',
//     render: (h, params) => {
//       return createLink(h, params, 'additional_subscription_count', '1')
//     }
//   },
//   {
//     title: '履约中会员总数量',
//     key: 'cumulative_subscription_count',
//     render: (h, params) => {
//       return createLink(h, params, 'cumulative_subscription_count', '2')
//     }
//   },
//   {
//     title: '扣款成功会员总数量',
//     key: 'paid_count',
//     render: (h, params) => {
//       return createLink(h, params, 'paid_count', '3')
//     }
//   },
//   {
//     title: '扣款失败会员总数量',
//     key: 'pay_failed_count',
//     render: (h, params) => {
//       return createLink(h, params, 'pay_failed_count', '4')
//     }
//   },
//   {
//     title: '解约会员总数量',
//     key: 'additional_surrender_count',
//     render: (h, params) => {
//       return createLink(h, params, 'additional_surrender_count', '5')
//     }
//   },
//   {
//     title: '履约完结会员总数量',
//     key: 'additional_end_count',
//     render: (h, params) => {
//       return createLink(h, params, 'additional_end_count', '6')
//     }
//   },
//   {
//     title: '会员违约率',
//     renderHeader: (h) => {
//       return h('Tooltip', {
//         props: { placement: 'top', transfer: true }
//       }, [
//         h('span', '会员违约率 '),
//         h('Icon', { props: { type: 'md-help-circle' } }),
//         h('div', {
//           slot: 'content',
//           style: {
//             whiteSpace: 'normal',
//             wordBreak: 'break-all',
//             textAlign: 'left'
//           }
//         }, [
//           h('p', '月会员违约率的计算规则：'),
//           h('br'),
//           h('p', '为确保月会员违约率计算的准确，我们将根据会员月付签约的实际月份，实时获取后续月份会员月付订单的履约状态进行违约率的计算。'),
//           h('br'),
//           h('p', '计算公式为：（月付款失败的会员数量 + 月解约的会员数量）/月签约会员总数量。'),
//           h('br'),
//           h('p', '因月付款失败后，会员可以通过追缴方式对付款失败的订单进行补齐，所以，会员违约率将根据月付款失败的会员数量进行动态计算。'),
//         ])
//       ])
//     },
//     key: 'surrender_rate_text',
//   }
// ])
// const list = ref([])
// const getList = () => {
//   const date = yearValue.value
//   return service.post('/Web/BusinessFit/memberByStore', { date }).then(({ data }) => {
//     if (data.errorcode === 0) {
//       const listValue = data.data
//       listValue.forEach(item => {
//         item.surrender_rate_text = `${item.surrender_rate || 0} %`
//       })
//       list.value = listValue
//     } else {
//       Message.error(data.errormsg)
//     }
//   })
// }

// const table = ref(null)
// const handleExcel = () => {
//   table.value.exportCsv({
//     filename: '月付签约会员统计-' + yearValue.value,
//     columns: columns.value,
//     data: list.value
//   })
// }

// yearValue.value = currentYear
// getList()
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;

    .total {
      padding: 10px 0;

      .total-item {
        font-size: 16px;

        .total-label {
          color: #666;
        }

        .total-value {
          color: #999;
        }
      }
    }
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
