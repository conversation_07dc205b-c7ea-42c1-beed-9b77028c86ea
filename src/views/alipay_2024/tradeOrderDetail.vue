<template>
  <div class="container">
    <Card style="margin: 20px">
      <div class="information" v-if="info">
        <div class="line">
          <div class="item">
            <div class="label">订单编号:</div>
            <div class="value">{{ order.order_sn }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">月付方案:</div>
            <div class="value">{{ info.product_title }}</div>
          </div>
          <div class="item">
            <div class="label">月付方案类型:</div>
            <div class="value" v-if="info.product_type == 1">常规</div>
            <div class="value" v-else-if="info.product_type == 2">组合</div>
          </div>
        </div>
        <Alert type="warning">
          <div slot="desc">
            <div class="line">
              <div class="item">
                <div class="label">首期扣款金额:</div>
                <div class="value">￥{{ info.down_payment }}</div>
              </div>
              <div class="item">
                <div class="label">单期扣款金额:</div>
                <div class="value">￥{{ info.deduction_amount }}</div>
              </div>
            </div>
            <div class="line">
              <div class="item">
                <div class="label">月卡使用天数:</div>
                <div class="value">{{ info.period_day }}天</div>
              </div>
              <div class="item">
                <div class="label">扣款期限:</div>
                <div class="value">{{ info.periods }}期</div>
              </div>
            </div>
          </div>
        </Alert>
        <div class="line" v-if="combinationsTotal && ['2', '3'].includes(category)">
          <div class="item">
            <div class="label">首次付款总金额:</div>
            <div class="value">￥{{ combinationsTotal }}</div>
          </div>
        </div>
        <Alert type="warning" v-if="info.product_type == 2 && ['2', '3'].includes(category)">
          <div slot="desc">
            <div class="line" v-for="(item2b, index2b) in combinations" :key="'b_' + index2b">
              <div class="item" v-for="(item, index) in item2b" :key="'bb_' + index">
                <div class="label" v-if="item">{{ item.title }}:</div>
                <div class="value" v-if="item">￥{{ item.amount }}</div>
              </div>
            </div>
          </div>
        </Alert>
        <div class="line" v-if="['4', '5'].includes(category)">
          <div class="item">
            <div class="label" v-if="category == 4">会员解约付款金额:</div>
            <div class="label" v-else-if="category == 5">会员解约扣款金额:</div>
            <div class="value">￥{{ info.violate_amount }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item" v-if="['2', '3'].includes(category)">
            <div class="label">抵扣券:</div>
            <div class="value">￥{{ info.coupon_amount }}</div>
          </div>
          <div class="item">
            <div class="label">支付宝手续费:</div>
            <div class="value">￥{{ order.order_fee }}</div>
          </div>
        </div>
        <div class="line" v-if="['2', '3'].includes(category)">
          <!-- <div class="item">
            <div class="label">优惠金额:</div>
            <div class="value">￥{{ info.discount_amount }}</div>
          </div> -->
          <div class="item">
            <div class="label">会员实际付款金额:</div>
            <div class="value">￥{{ order.amount }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item" v-if="category != 3">
            <div class="label">实际收入金额:</div>
            <div class="value">￥{{ order.final_amount }}</div>
          </div>
          <div v-if="['3'].includes(category)" class="item">
            <div class="label">申请退款金额:</div>
            <div class="value">￥{{ order.amount }}(含支付宝手续费)</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">场馆:</div>
            <div class="value">{{ order.bus_name }}</div>
          </div>
          <div class="item">
            <div class="label">会员:</div>
            <div class="value">{{ order.phone }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">签约时间:</div>
            <div class="value">{{ info.sign_time }}</div>
          </div>
          <div class="item">
            <div class="label">签单销售:</div>
            <div class="value">{{ order.marketers_name || order.coach_name }}</div>
          </div>
        </div>
        <div class="line">
          <div class="item">
            <div class="label">订单状态:</div>
            <!-- 状态 1待付款 ,2已支付,3已取消,4已关闭 -->
            <!-- 状态，1待付款 ,2已支付,3已取消,4已关闭,5已退款 -->
            <div class="value" v-if="order.status == 1" style="color: orange">待付款</div>
            <div class="value" v-if="order.status == 2" style="color: green">已支付</div>
            <div class="value" v-if="order.status == 3" style="color: gray">已取消</div>
            <div class="value" v-if="order.status == 4" style="color: red">已关闭</div>
            <div class="value" v-if="order.status == 5" style="color: red">已退款</div>
          </div>
        </div>
        <div class="actions">
          <!-- <Button @click="$router.go(-1)">返回</Button> -->
          <!-- <router-link :to="{ name: '合约管理2', params: { refresh } }"> -->
          <router-link :to="`/alipay2/tradeOrder/${refresh}`">
            <Button>返回</Button>
          </router-link>
          <Button v-if="category == 2" type="warning" style="margin-left: 20px" @click="handleRefund">退款</Button>
        </div>
      </div>
    </Card>
    <!-- <Tabs v-model="tabName" style="margin: 20px">
      <TabPane label="历史扣款记录" name="0">
        <Card style="margin-top: 20px">
          <Table :columns="columnsOne" :data="listOne"></Table>
          <Page @on-change="handlePageChange" :total="totalOne" :current="searchPageOne.page_no"
            @on-page-size-change="handlePageSizeChange" show-total show-sizer style="margin-top: 10px; text-align: right">
          </Page>
        </Card>
      </TabPane>
      <TabPane label="未来扣款计划" name="1">
        <Table :columns="columnsTwo" :data="listTwo"></Table>
        <Page @on-change="handlePageChange" :total="totalTwo" :current="searchPageTwo.page_no"
          @on-page-size-change="handlePageSizeChange" show-total show-sizer style="margin-top: 10px; text-align: right">
        </Page>
      </TabPane>
    </Tabs> -->
  </div>
</template>

<script>
import Big from "big.js"
import router from '@/router'

export default {
  name: 'Alipay2Detail',
  props: {
    category: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabName: '0',
      searchParams: {
        bus_id: '',
        ali_user_id: '',
        status: '0', // 0历史记录 1未来计划
        subscription_no: '',
      },
      info: null,
      order: null,
      combinations: [],
      combinationsTotal: 0,
      // table one
      columnsOne: [
        {
          title: '序号',
          type: 'index',
          width: 80
        },
        {
          title: '订单编号',
          key: 'order_no'
        },
        {
          title: '扣款金额(元)',
          key: 'deduction_amount'
        },
        {
          title: '扣款期数/总期数',
          render: (h, params) => {
            const { period, periods } = params.row
            return h('div', `${period}/${periods}`)
          }
        },
        {
          title: '扣款时间',
          key: 'actual_deduction_time',
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            const status = params.row.status
            // 已暂停 PAUSED，下单中 ORDERING, 下单成功 ORDERED，支付成功 PAID，扣款失败 PAY_FAILED, 已全额退款 REFUNDED, 未生成订单 UNCREATED ,已取消 CANCEL
            if (status === 'PAUSED') {
              return h('span', '已暂停')
            } else if (status === 'ORDERING') {
              return h('span', '下单中')
            } else if (status === 'ORDERED') {
              return h('span', '下单成功')
            } else if (status === 'PAID') {
              return h('span', '支付成功')
            } else if (status === 'PAY_FAILED') {
              return h('span', '扣款失败')
            } else if (status === 'REFUNDED') {
              return h('span', '已全额退款')
            } else if (status === 'UNCREATED') {
              return h('span', '未生成订单')
            } else if (status === 'CANCEL') {
              return h('span', '已取消')
            } else {
              return h('span', '-')
            }
          }
        }
      ],
      listOne: [],
      totalOne: 0,
      searchPageOne: {
        page_no: 1,
        page_size: 10
      },
      // table two
      columnsTwo: [
        {
          title: '序号',
          type: 'index',
          width: 80
        },
        {
          title: '扣款期数/总期数',
          render: (h, params) => {
            const { period, periods } = params.row
            return h('div', `${period}/${periods}`)
          }
        },
        {
          title: '预计扣款时间',
          key: 'plan_deduction_time'
        },
        {
          title: '扣款金额(元)',
          key: 'deduction_amount'
        },
      ],
      listTwo: [],
      totalTwo: 0,
      searchPageTwo: {
        page_no: 1,
        page_size: 10
      },
      refresh: 0
    }
  },
  methods: {
    getList(getAll = false, status) {
      const params = {
        ...this.searchParams
      }
      if (!getAll && this.tabName === '0') {
        params.status = '0'
        params.page_no = this.searchPageOne.page_no
        params.page_size = this.searchPageOne.page_size
      } else if (!getAll && this.tabName === '1') {
        params.status = '1'
        params.page_no = this.searchPageTwo.page_no
        params.page_size = this.searchPageTwo.page_size
      } else {
        params.status = status
        params.page_no = 1
        params.page_size = 10
      }
      return this.$service.post('/Web/BusinessFit/userSubscriptionItem', params).then((res) => {
        if (res.data.errorcode === 0) {
          if (params.status === '0') {
            this.listOne = res.data.data.list
            this.totalOne = res.data.data.count
          } else {
            this.listTwo = res.data.data.list
            this.totalTwo = res.data.data.count
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getInfo() {
      return this.$service.post('/Web/BusinessFit/fitAlipayOrderDetail', {
        id: this.id
      }).then((res) => {
        if (res.data.errorcode === 0) {
          this.order = res.data.data

          const rate = new Big(0.006)
          const amount = new Big(this.order.amount || 0)
          const fee = amount.mul(rate)
          this.order.order_fee = fee.toFixed(2)
          const finalAmount = amount.minus(fee)
          this.order.final_amount = finalAmount.toFixed(2)

          this.info = res.data.data.subscription
          if (this.info.product_type == 2) {
            const combinations = []
            if (Array.isArray(this.info.combine_list)) {
              const len = this.info.combine_list.length
              const count = Math.ceil(len / 2)
              for (let i = 0; i < count; i++) {
                const one = this.info.combine_list[2 * i] || ''
                const two = this.info.combine_list[2 * i + 1] || ''
                combinations.push([one, two])
              }
              this.combinationsTotal = this.info.combine_list.reduce((total, item) => {
                return total + Number(item.amount)
              }, 0)
            }
            this.combinations = combinations
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      if (this.tabName === '0') {
        this.searchPageOne.page_no = page
      } else {
        this.searchPageTwo.page_no = page
      }
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      if (this.tabName === '0') {
        this.searchPageOne.page_size = pageSize
      } else {
        this.searchPageTwo.page_size = pageSize
      }
      this.getList()
    },
    handleRefund() {
      // show modal for ensure
      this.$Modal.confirm({
        title: '提示',
        content: '是否确认要退款?',
        onOk: () => {
          this.$service.post('/Web/BusinessFit/fitAlipayOrderRefund', {
            id: this.id
          }).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getInfo()
              // this.getList(true, '0')
              // this.getList(true, '1')
              this.refresh = 1
              setTimeout(() => {
                router.push({ path: `/alipay2/tradeOrder/${this.refresh}` })
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
  },
  created() {
    this.getInfo()
    // this.getList(true, '0')
    // this.getList(true, '1')
  }
}
</script>

<style lang="less" scoped>
.one-line {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.information {
  padding: 40px;

  .line {
    .one-line;
    justify-content: space-between;
    min-height: 40px;

    .item {
      min-width: 280px;
      width: 50%;
      .one-line;
    }

    .label {
      font-size: 16px;
      color: #999;
    }

    .value {
      font-size: 16px;
      color: #333;
      margin-left: 10px;
    }
  }

  .actions {
    margin: 20px auto 0 auto;
    width: 400px;
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }
}
</style>