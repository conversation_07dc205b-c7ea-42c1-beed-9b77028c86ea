<template>
  <div class="container">
    <div class="box">
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" @on-change="searchParams.marketers_id = ''" placeholder="请选择场馆"
                class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">签约会员</div>
              <Input v-model="searchParams.search" placeholder="请输入会员姓名/手机号码" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">产品方案</div>
              <Input v-model="searchParams.product_title" placeholder="请输入产品方案" class="value" clearable />
            </div>
            <div class="search-item" v-if="searchParams.bus_id">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="searchParams.bus_id" />
            </div>
            <div class="search-item">
              <div class="label">状态</div>
              <Select v-model="searchParams.un_sign_status" placeholder="请选择状态" class="value" multiple transfer>
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">申请时间</div>
              <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer :clearable="false" />
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <Button style="margin-left: 10px" @click="handleExport">导出</Button>
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './components/TheDetailModal.vue'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  product_title: '',
  search: '',
  marketers_id: '',
  un_sign_status: '',
  time_begin: '',
  time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TerminateUp',
  components: { SalesSelect, TheDetailModal },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 申请时间 合约编号 签约会员 电话号码 场馆 业绩归属 产品方案 已履约期数 已履约金额 违约金总金额 状态 操作
      columns: [
        {
          title: '申请时间',
          key: 'un_sign_time',
        },
        {
          title: '合约编号',
          key: 'subscription_no',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '签约会员',
          key: 'username'
        },
        {
          title: '电话号码',
          key: 'phone'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '业绩归属',
          key: 'marketers_name'
        },
        {
          title: '产品方案',
          key: 'product_title'
        },
        {
          title: '已履约期数',
          key: 'format_period',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '已履约期数 '),
              h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '已完成扣款总期数 / 签约期数'),
              ])
            ])
          },
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.done_period}/${row.periods}`)
          }
        },
        {
          title: '已履约金额',
          key: 'actual_amount',
          render(h, params) {
            const price = Number(params.row.actual_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '违约金总金额',
          key: 'violate_amount',
          // renderHeader: (h) => {
          //   return h('Tooltip', {
          //     props: { placement: 'top', transfer: true }
          //   }, [
          //     h('span', '违约金总金额 '),
          //     h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
          //     h('div', {
          //       slot: 'content',
          //       style: {
          //         whiteSpace: 'normal',
          //         wordBreak: 'break-all',
          //         textAlign: 'left'
          //       }
          //     }, [
          //       h('p', '已完成扣款期数的金额总和'),
          //     ])
          //   ])
          // },
          render: (h, params) => {
            const price = Number(params.row.violate_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '状态',
          key: 'format_status',
          render: (h, params) => {
            const item = this.statusList.find(item => item.value === params.row.un_sign_status)
            const label = item ? item.label : '-'
            const color = item ? item.color : ''
            return h('div', { style: { color } }, label)
          }
        },
        {
          title: '操作',
          render: (h, params) => {
            const agreeButton = h('Button', {
              props: {
                type: 'text',
                size: 'small',
              },
              on: {
                click: () => {
                  this.handleAgree(params.row)
                }
              }
            }, '同意')
            const rejectButton = h('Button', {
              props: {
                type: 'text',
                size: 'small',
              },
              on: {
                click: () => {
                  this.handleReject(params.row)
                }
              }
            }, '拒绝')
            if (params.row.un_sign_status === 1) {
              return h('div', [agreeButton, rejectButton])
            } else if (params.row.un_sign_status === 3) {
              return h('div', [rejectButton])
            } else {
              return h('div', '-')
            }
          }
        }
      ],
      list: [],
      total: 0,
      // 申请状态 1申请中 2已拒绝 3已同意待付款 4已完成
      statusList: [
        { value: 1, label: '申请中', color: 'gray' },
        { value: 3, label: '已同意待付款', color: 'grey' },
        { value: 2, label: '已拒绝', color: 'red' },
        { value: 4, label: '已完成', color: 'green' },
      ],
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      }
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.time_begin = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.time_begin = ''
        this.searchParams.time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      // recent 90 days
      this.daterange = [
        new Date(new Date().setDate(new Date().getDate() - 90)),
        new Date()
      ]
      this.handleSearch()
    },
    getList() {
      return this.$service.post('/Web/BusinessFit/memberUnSignList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/BusinessFit/memberUnSignList', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
          // let list = []
          // if (Array.isArray(res.data.data.list)) {
          //   list = res.data.data.list
          //   list.forEach(row => {
          //     const item = this.statusList.find(item => item.value === row.un_sign_status)
          //     const label = item ? item.label : '-'
          //     row.format_status = label
          //     row.format_period
          //   })
          // }
          // this.$refs.selection.exportCsv({
          //   filename: `解约申请-${formatDate(new Date(), 'yyyy-MM-dd')}`,
          //   columns: this.columns.slice(0, -1),
          //   data: list,
          // })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    postAction(alipay_user_id, subscription_no, card_type, status) {
      this.$service.post('/Web/BusinessFit/approveSubscriptionUnSignStatus', {
        alipay_user_id,
        subscription_no,
        card_type,
        status
      }).then((res) => {
        if (res.data.errorcode === 0) {
          if (status === 3) {
            this.$Message.success('同意解约, 等待会员支付违约金')
          } else {
            this.$Message.success('不同意解约')
          }
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleAgree(row) {
      this.$Modal.confirm({
        title: '提示',
        content: '是否确认同意该申请?',
        onOk: () => {
          this.postAction(row.alipay_user_id, row.subscription_no, row.card_type, 3)
        }
      })
    },
    handleReject(row) {
      this.$Modal.confirm({
        title: '提示',
        content: '是否确认拒绝该申请?',
        onOk: () => {
          this.postAction(row.alipay_user_id, row.subscription_no, row.card_type, 2)
        }
      })
    }
  },
  async created() {
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    if (this.$route.params.busId) {
      this.searchParams.bus_id = this.$route.params.busId
    } else {
      this.searchParams.bus_id = this.busId
    }
    if (this.$route.params.subscriptionNo) {
      this.searchParams.subscription_no = this.$route.params.subscriptionNo
    }
    // recent 30 days
    this.daterange = [
      new Date(new Date().setDate(new Date().getDate() - 30)),
      new Date()
    ]
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
}
</style>
