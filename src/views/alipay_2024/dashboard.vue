<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card" @on-click="handleTabChange">
        <TabPane :label="tab.name + ' ' + tab.date" :name="tab.name" v-for="tab in tabList" :key="tab.name">
          <div class="card-box">
            <Card v-for="card in cardList" :key="card.label" style="min-width: 400px; width: 22%; margin: 20px">
              <div v-if="card.tip" slot="title">
                <Tooltip :content="card.tip" placement="right">
                  <div style="display: flex; align-items: center">
                    <span>{{ card.label }}</span>
                    <Icon type="md-help-circle" size="20" style="margin-left: 4px" />
                  </div>
                </Tooltip>
              </div>
              <div v-else slot="title">{{ card.label }}</div>
              <div class="value"> {{ card.value }} </div>
              <div class="desc" v-if="card.tender === 'up'">
                较{{ trendName }}上涨
                <Icon type="md-trending-up" size="30" color="red" /> {{ card.count }} （{{ card.percentage }}%）
              </div>
              <div class="desc" v-else-if="card.tender === 'none'">
                和{{ trendName }}持平
                <Icon type="md-arrow-round-forward" size="30" color="blue" /> {{ card.count }} （{{ card.percentage }}%）
              </div>
              <div class="desc" v-else-if="card.tender === 'down'">
                较{{ trendName }}下滑
                <Icon type="md-trending-down" size="30" color="green" /> {{ card.count }} （{{ card.percentage }}%）
              </div>
            </Card>
          </div>
        </TabPane>
      </Tabs>
      <Divider>本年度各项指标月度增长趋势</Divider>
      <div class="chart-box">
        <Card :title="chart.label" style="min-width: 400px; width: 22%; height: 350px; margin: 20px" :padding="0"
          v-for="chart in chartList" :key="chart.label">
          <LineChart v-if="!loadingChart" :options="chart.options" />
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineComponent } from 'vue'
import LineChart from './components/LineChart.vue'
import { formatDate } from '@/utils'
import service from 'src/service'
import { Message } from 'iview';

defineComponent({
  name: 'Alipay2Dashboard',
})

// tab
const tabName = ref('本年度')
const trendName = ref('上年度')
// this year and today
const today = new Date()
// this week
const monday = new Date()
monday.setDate(monday.getDate() - (monday.getDay() - 1))
const sunday = new Date()
sunday.setDate(sunday.getDate() - (sunday.getDay() - 1) + 6)
// this month
const firstDayOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
const lastDayOfThisMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
// last year
const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
// yesterday
const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
// last week
const lastMonday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
lastMonday.setDate(lastMonday.getDate() - (lastMonday.getDay() - 1))
const lastSunday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
lastSunday.setDate(lastSunday.getDate() - (lastSunday.getDay() - 1) + 6)
// last month
const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
const tabList = ref([
  {
    name: '本年度',
    trendName: '上年度',
    date: formatDate(today, 'yyyy年'),
    start_date: formatDate(today, 'yyyy-01-01'),
    end_date: formatDate(today, 'yyyy-12-31'),
    type: 1,
    child_type: 4
  },
  {
    name: '今日',
    trendName: '昨日',
    date: formatDate(today, 'MM月dd日'),
    start_date: formatDate(today, 'yyyy-MM-dd'),
    end_date: formatDate(today, 'yyyy-MM-dd'),
    type: 1,
    child_type: 1
  },
  {
    name: '本周',
    trendName: '上周',
    date: formatDate(monday, 'MM月dd日') + ' ~ ' + formatDate(sunday, 'MM月dd日'),
    start_date: formatDate(monday, 'yyyy-MM-dd'),
    end_date: formatDate(sunday, 'yyyy-MM-dd'),
    type: 1,
    child_type: 2
  },
  {
    name: '本月',
    trendName: '上月',
    date: formatDate(today, 'MM月'),
    start_date: formatDate(firstDayOfThisMonth, 'yyyy-MM-dd'),
    end_date: formatDate(lastDayOfThisMonth, 'yyyy-MM-dd'),
    type: 1,
    child_type: 3
  },
  {
    name: '上年度',
    trendName: '上年度',
    date: formatDate(lastYear, 'yyyy年'),
    start_date: formatDate(lastYear, 'yyyy-01-01'),
    end_date: formatDate(lastYear, 'yyyy-12-31'),
    type: 2,
    child_type: 4
  },
  {
    name: '昨日',
    trendName: '昨日',
    date: formatDate(yesterday, 'MM月dd日'),
    start_date: formatDate(yesterday, 'yyyy-MM-dd'),
    end_date: formatDate(yesterday, 'yyyy-MM-dd'),
    type: 2,
    child_type: 1
  },
  {
    name: '上周',
    trendName: '上周',
    date: formatDate(lastMonday, 'MM月dd日') + ' ~ ' + formatDate(lastSunday, 'MM月dd日'),
    start_date: formatDate(lastMonday, 'yyyy-MM-dd'),
    end_date: formatDate(lastSunday, 'yyyy-MM-dd'),
    type: 2,
    child_type: 2
  },
  {
    name: '上月',
    trendName: '上月',
    date: formatDate(firstDayOfLastMonth, 'MM月'),
    start_date: formatDate(firstDayOfLastMonth, 'yyyy-MM-dd'),
    end_date: formatDate(lastDayOfLastMonth, 'yyyy-MM-dd'),
    type: 2,
    child_type: 3
  }
])


const handleTabChange = (name) => {
  const tab = tabList.value.find(item => item.name === name)
  tabName.value = name
  trendName.value = tab.trendName
  getCount()
}

// card
const cardList = ref([
  {
    label: '签约总数',
    code: 'all_subscription_count',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '合约履约数',
    code: 'exercise_subscription_count',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
    tip: '包含合约状态为履约中、已暂停'
  },
  {
    label: '扣款失败订单总数',
    code: 'pay_failed_subscription_count',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '合约终止数',
    code: 'abort_subscription_count',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '签约总金额',
    code: 'all_subscription_money',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '已扣款金额(￥)',
    code: 'pay_subscription_money',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '未扣款总金额(￥)',
    code: 'no_pay_subscription_money',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  },
  {
    label: '解约总金额(￥)',
    code: 'surrender_subscription_money',
    value: 0,
    count: 0,
    percentage: 0,
    tender: 'none',
  }
])
const getCount = () => {
  const tab = tabList.value.find(item => item.name === tabName.value)
  return service.post('/Web/BusinessFit/getStatistics', {
    type: tab.type, // 1实时（今日本周本月本年度） 2扎帐数据（昨日上周上月上年度）
    start_date: tab.start_date,
    end_date: tab.end_date,
    child_type: tab.child_type, // 1每日统计 2每周统计 3每月统计 4每年统计
  }).then(({ data }) => {
    if (data.errorcode === 0) {
      for (const card of cardList.value) {
        card.value = data.data[card.code] || 0
        card.count = Number(data.data[card.code + '_rate']?.up_count) || 0
        if (card.count > 0) {
          card.tender = 'up'
          card.percentage = Number(data.data[card.code + '_rate']?.chain) || 0
        } else if (card.count === 0) {
          card.tender = 'none'
          card.percentage = 0
        } else {
          card.tender = 'down'
          card.percentage = Number(data.data[card.code + '_rate']?.chain) || 0
        }
      }
    } else {
      Message.error(data.errormsg)
    }
  })
}
getCount()

// chart
const loadingChart = ref(true)
const monthList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
const thisMonth = new Date().getMonth() + 1
const chartOptions = {
  xAxis: {
    type: 'category',
    data: monthList.slice(0, thisMonth),
  },
  yAxis: {
    type: 'value',
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}月: {c}',
  },
}
const chartData = {
  type: 'line',
  smooth: true,
  symbol: 'circle', // 显示折线点
  itemStyle: {
    color: '#007BFF', // 折线颜色
  },
  emphasis: {
    focus: 'series',
    blurScope: 'coordinateSystem',
  },
}
const chartList = ref([
  {
    label: '签约总数增长趋势',
    code: 'all_subscription_count',
    options: {}
  },
  {
    label: '合约履约数增长趋势',
    code: 'exercise_subscription_count',
    options: {}
  },
  {
    label: '扣款失败订单数增长趋势',
    code: 'pay_failed_subscription_count',
    options: {}
  },
  {
    label: '合约终止数增长趋势',
    code: 'abort_subscription_count',
    options: {}
  },
  {
    label: '签约总金额增长趋势',
    code: 'all_subscription_money',
    options: {}
  },
  {
    label: '已扣款总金额增长趋势',
    code: 'pay_subscription_money',
    options: {}
  },
  {
    label: '未扣款总金额增长趋势',
    code: 'no_pay_subscription_money',
    options: {}
  },
  {
    label: '解约总金额增长趋势',
    code: 'surrender_subscription_money',
    options: {}
  },
])
const getChart = () => {
  loadingChart.value = true
  return service.get('/Web/BusinessFit/getChartLists').then(({ data }) => {
    if (data.errorcode === 0) {
      for (const chart of chartList.value) {
        chart.options = {
          ...chartOptions,
          series: [
            {
              ...chartData,
              data: data.data[chart.code]
            }
          ]
        }
      }
      loadingChart.value = false
    } else {
      Message.error(data.errormsg)
    }
  })
}
getChart()
</script>

<style lang="less" scoped>
.box {
  margin-top: 20px;

  .card-box,
  .chart-box {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;

    .value {
      font-size: 40px;
      font-weight: bold;
      text-align: center;
    }

    .desc {
      font-size: 12px;
      text-align: center;
    }
  }
}
</style>