<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card">
        <TabPane :label="labelOne" :name="tabList[0]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[0]" :tabList="tabList" />
        </TabPane>
        <TabPane :label="labelTwo" :name="tabList[1]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[1]" :tabList="tabList" lazy />
        </TabPane>
        <TabPane :label="labelThree" :name="tabList[2]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[2]" :tabList="tabList" lazy />
        </TabPane>
        <TabPane :label="labelFour" :name="tabList[3]">
          <TheSearchPanel @emitCount="setCount" :category="tabList[3]" :tabList="tabList" lazy />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script>
import TheSearchPanel from './components/TheTradeSearchPanel.vue'

export default {
  name: 'Alipay2Tabs',
  components: {
    TheSearchPanel,
  },
  data() {
    const badgeFn = (h, label, count) => {
      return h('div', [
        h('span', label),
        h('Badge', {
          style: {
            marginLeft: '10px',
          },
          props: {
            count,
            overflowCount: 999,
          },
        }),
      ])
    }
    return {
      tabName: '2',
      tabList: ['2', '3', '4', '5'],
      valueOne: 0,
      labelOne: (h) => {
        return badgeFn(h, '签约付款', this.valueOne)
      },
      valueTwo: 0,
      labelTwo: (h) => {
        return badgeFn(h, '签约退款', this.valueTwo)
      },
      valueThree: 0,
      labelThree: (h) => {
        return badgeFn(h, '解约付款', this.valueThree)
      },
      valueFour: 0,
      labelFour: (h) => {
        return badgeFn(h, '解约扣款', this.valueFour)
      },
    }
  },
  methods: {
    setCount(count, category) {
      if (category === this.tabList[0]) {
        this.valueOne = count
      } else if (category === this.tabList[1]) {
        this.valueTwo = count
      } else if (category === this.tabList[2]) {
        this.valueThree = count
      } else if (category === this.tabList[3]) {
        this.valueFour = count
      }
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 20px;
}
</style>
