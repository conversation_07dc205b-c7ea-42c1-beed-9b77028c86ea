<template>
  <div class="container">
    <div class="box">
      <Tabs :value="tabName" type="card">
        <TabPane label="安心付-售后工单" name="first">
          <TerminateAfterSales />
        </TabPane>
        <TabPane label="勤鸟-解约申请" name="second">
          <TerminateUp />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
import TerminateUp from './terminateUp.vue'
import TerminateAfterSales from './terminateAfterSales.vue'
import { ref } from 'vue'

const tabName = ref('first')
</script>

<style lang="less" scoped>
.box {
  padding: 20px;
}
</style>