<template>
  <div class="search-panel">
    <Card>
      <div class="search-line">
        <div class="search-item">
          <div class="label">日期</div>
          <DatePicker v-model="chooseDate" type="month" format="yyyy-MM" placeholder="请选择时间" class="value" transfer
            :clearable="false" @on-change="handleSearch" />
        </div>
        <!-- <div class="search-item">
          <Button type="primary" @click="handleSearch">查询</Button>
        </div>
        <div class="search-item">
          <Button @click="handleReset">重置</Button>
        </div> -->
      </div>
    </Card>
    <Card style="margin-top: 20px">
      <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
      <div style="margin-top: 10px; display: flex; justify-content: space-between;">
        <div>
          <Button style="margin-left: 10px" @click="handleExport">导出</Button>
        </div>
        <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
          @on-page-size-change="handlePageSizeChange" show-total show-sizer transfer>
        </Page>
      </div>
    </Card>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'

const NONE_SEARCH_PARAMS = {
  date: '',
  type: '', // 1签约会员 2 履约会员 3扣款成功会员 4扣款失败会员 5解约会员 6完结会员
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TheSearchPanel',
  props: {
    category: {
      type: String,
      required: true
    },
    tabList: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
    date: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chooseDate: '',
      searchParams: { ...NONE_SEARCH_PARAMS },
      list: [],
      total: 0,
      // 序号 会员昵称/姓名 会员手机号码 签约时间 月付方案类型 月付方案名称 首笔/首期金额 后续每期金额 已履约期数/总期数 签约场馆 签约销售
      columns: [
        {
          title: '序号',
          type: 'index',
        },
        {
          title: '会员昵称/姓名',
          key: 'username'
        },
        {
          title: '会员手机号码',
          key: 'phone'
        },
        {
          title: '签约时间',
          key: 'sign_time',
        },
        {
          title: '月付方案类型',
          key: 'product_type',
        },
        {
          title: '月付方案名称',
          key: 'product_title'
        },
        {
          title: '首笔/首期金额',
          key: 'down_payment'
        },
        {
          title: '后续每期金额',
          key: 'deduction_amount'
        },
        {
          title: '已履约期数/总期数',
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.paid_count}/${row.periods}`)
          }
        },
        {
          title: '签约场馆',
          key: 'bus_name'
        },
        {
          title: '签约销售',
          key: 'salename'
        },
      ]
    }
  },
  methods: {
    handleSearch() {
      this.searchParams.page_no = 1
      this.searchParams.date = formatDate(this.chooseDate, 'yyyy-MM')
      this.getList()
    },
    handleReset() {
      this.daterange = []
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.handleSearch()
    },
    getList() {
      this.searchParams.type = this.category
      return this.$service.post('/Web/BusinessFit/memberByStoreDetail', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          this.$emit('emitCount', this.total, this.category)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        type: this.category,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/BusinessFit/memberByStoreDetail', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePause() {
      const list = this.$refs.selection.getSelection()
      if (list.length === 0) {
        this.$Message.warning('请勾选要暂停的合约')
        return
      }
      const open_merchant_id = list[0].open_merchant_id
      const subscriptionNoList = list.map(item => item.subscription_no)
      const subscription_no = subscriptionNoList.join(',')
      return this.$service.post('/Web/BusinessFit/subscriptionPause', {
        bus_id: this.searchParams.bus_id,
        subscription_no,
        open_merchant_id
      }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  },
  created() {
    this.chooseDate = this.date
    const lazyTime = this.lazy ? 1000 : 0
    setTimeout(async () => {
      this.handleSearch()
    }, lazyTime)
  },
  activated() {
    if (this.$route.params.refresh == 1) {
      this.handleSearch()
    }
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.search-panel {
  padding: 20px 0;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;
      margin: 10px;

      .value {
        margin-left: 10px;
        width: 200px;
      }
    }
  }
}
</style>
