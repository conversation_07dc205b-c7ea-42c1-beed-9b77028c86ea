<template>
  <div class="search-panel">
    <Card>
      <div class="search-line">
        <div class="search-item">
          <div class="label">订单交易编号</div>
          <Input v-model="searchParams.order_sn" placeholder="请输入订单交易编号" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">月付方案</div>
          <Input v-model="searchParams.product_title" placeholder="请输入月付方案名称" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">会员</div>
          <Input v-model="searchParams.phone" placeholder="请输入会员手机号码" class="value" clearable />
        </div>
        <div class="search-item">
          <div class="label">场馆</div>
          <Select v-model="searchParams.bus_id" @on-change="searchParams.marketers_id = ''" placeholder="请选择场馆"
            class="value" filterable transfer>
            <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="search-item" v-if="searchParams.bus_id">
          <div class="label">关联销售</div>
          <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
            :belongBusId="searchParams.bus_id" />
        </div>
        <div class="search-item" v-if="category === tabList[2]">
          <div class="label">订单状态</div>
          <Select v-model="searchParams.pay_status" placeholder="请选择订单状态" class="value" transfer clearable>
            <Option v-for="item in payStatusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
        <!-- <div class="search-item" v-if="category === tabList[3]">
          <div class="label">扣款类型</div>
          <Select v-model="searchParams.fit_unsign_type" placeholder="请选择扣款类型" class="value" transfer clearable>
            <Option v-for="item in payTypeList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div> -->
        <div class="search-item">
          <div class="label">付款时间</div>
          <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
            transfer />
        </div>
        <div class="search-item">
          <Button type="success" @click="handleSearch">查询</Button>
        </div>
        <div class="search-item">
          <Button @click="handleReset">重置</Button>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 20px">
      <Table :columns="columns" :data="list" stripe disabled-hover></Table>
      <div style="margin-top: 10px; display: flex; justify-content: space-between;">
        <div>
          <!-- <Button type="primary" v-if="category === tabList[0] || category === tabList[3]" disabled>一键暂停</Button> -->
          <Button style="margin-left: 10px" @click="handleExport">导出</Button>
        </div>
        <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
          @on-page-size-change="handlePageSizeChange" show-total show-sizer transfer>
        </Page>
      </div>
    </Card>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import Big from 'big.js'
import SalesSelect from 'components/membership/salesSelect'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  order_sn: '',
  product_title: '',
  phone: '',
  marketers_id: '',
  pay_status: '', // 状态 1待付款 ,2已支付,3已取消,4已关闭
  fit_unsign_type: '', // 解约扣款 1解约延期扣款 2履约奖励金追回
  fit_pay_type: '', // 类型 1芝麻订单 2签约付款 3签约退款 4解约付款 5解约扣款 默认2
  pay_time_start: '',
  pay_time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TheSearchPanel',
  components: { SalesSelect },
  props: {
    category: {
      type: String,
      required: true
    },
    tabList: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      list: [],
      total: 0,
      payStatusList: [
        {
          id: 1,
          name: '待付款'
        },
        {
          id: 2,
          name: '已支付'
        },
        {
          id: 3,
          name: '已取消'
        },
        {
          id: 4,
          name: '已关闭'
        }
      ],
      payTypeList: [
        {
          id: 1,
          name: '解约延期扣款'
        },
        {
          id: 2,
          name: '履约奖励金追回'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
    columns() {
      // 序号 订单交易编号 月付方案 订单金额 支付宝手续费 抵扣券金额 实际付款金额 实际收入金额 会员 场馆 签约销售 付款时间 订单状态 操作
      const action = {
        title: '操作',
        render: (h, params) => {
          return h('div', [
            h(
              'Button',
              {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.$router.push(`/alipay2/tradeOrder/${this.category}/${params.row.id}`)
                  }
                }
              },
              '查看'
            )
          ])
        }
      }
      const status = {
        title: '订单状态',
        key: 'status',
        render: (h, params) => {
          // 状态，1待付款, 2已支付, 3已取消, 4已关闭, 5已退款
          const status = Number(params.row.status) - 1
          const statusList = [
            '待付款',
            '已支付',
            '已取消',
            '已关闭',
            '已退款'
          ]
          let color = 'black'
          if (status === 1) {
            color = '#ff9800'
          } else if (status === 2) {
            color = '#4caf50'
          } else if (status === 3) {
            color = '#808080'
          } else if (status === 4) {
            color = '#000000'
          } else if (status === 5) {
            color = '#ffa500'
          }
          return h('span', { style: { color } }, statusList[status])
        }
      }
      const cols = []
      if (this.category === this.tabList[0]) {
        cols.push(
          {
            title: '序号',
            type: 'index',
            width: 80
          },
          {
            title: '订单交易编号',
            key: 'order_sn'
          },
          {
            title: '月付方案',
            key: 'product_title'
          },
          {
            title: '订单金额',
            key: 'order_amount',
          },
          {
            title: '支付宝手续费',
            render: (h, params) => {
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              return h('div', fee.toFixed(2))
            }
          },
          {
            title: '抵扣券金额',
            key: 'discount_amount'
          },
          {
            title: '实际付款金额',
            key: 'amount'
          },
          {
            title: '实际收入金额',
            render: (h, params) => {
              const amount = new Big(params.row.amount)
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              const total = amount.minus(fee)
              return h('div', total.toFixed(2))
            }
          },
          {
            title: '会员',
            key: 'username'
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '签约销售',
            key: 'saleName',
            render: (h, params) => {
              // marketers_name or coach_name
              return h('div', params.row.marketers_name || params.row.coach_name)
            }
          },
          {
            title: '付款时间',
            key: 'pay_time',
            render: (h, params) => {
              if (params.row.pay_time) {
                return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd'))
              } else {
                return h('div', '-')
              }
            }
          },
          status,
          action)
      } else if (this.category === this.tabList[1]) {
        cols.push(
          {
            title: '序号',
            type: 'index',
            width: 80
          },
          {
            title: '订单交易编号',
            key: 'order_sn'
          },
          {
            title: '月付方案',
            key: 'product_title'
          },
          {
            title: '订单金额',
            key: 'order_amount',
          },
          {
            title: '支付宝手续费',
            render: (h, params) => {
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              return h('div', fee.toFixed(2))
            }
          },
          {
            title: '抵扣券金额',
            key: 'discount_amount'
          },
          {
            title: '实际付款金额',
            key: 'amount'
          },
          {
            title: '会员',
            key: 'username'
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '签约销售',
            key: 'saleName',
            render: (h, params) => {
              // marketers_name or coach_name
              return h('div', params.row.marketers_name || params.row.coach_name)
            }
          },
          {
            title: '实际退款金额',
            key: 'amount'
          },
          {
            title: '退款时间',
            key: 'actual_deduction_time',
            render: (h, params) => {
              if (params.row.pay_time) {
                return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd'))
              } else {
                return h('div', '-')
              }
            }
          },
          status,
          action)
      } else if (this.category === this.tabList[2]) {
        cols.push(
          {
            title: '序号',
            type: 'index',
            width: 80
          },
          {
            title: '订单交易编号',
            key: 'order_sn'
          },
          {
            title: '月付方案',
            key: 'product_title'
          },
          {
            title: '订单金额',
            key: 'order_amount',
          },
          {
            title: '支付宝手续费',
            render: (h, params) => {
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              return h('div', fee.toFixed(2))
            }
          },
          {
            title: '实际付款金额',
            key: 'amount'
          },
          {
            title: '实际收入金额',
            render: (h, params) => {
              const amount = new Big(params.row.amount)
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              const total = amount.minus(fee)
              return h('div', total.toFixed(2))
            }
          },
          {
            title: '会员',
            key: 'username'
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '签约销售',
            key: 'saleName',
            render: (h, params) => {
              // marketers_name or coach_name
              return h('div', params.row.marketers_name || params.row.coach_name)
            }
          },
          {
            title: '付款时间',
            key: 'pay_time',
            render: (h, params) => {
              if (params.row.pay_time) {
                return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd'))
              } else {
                return h('div', '-')
              }
            }
          },
          status,
          action)
      } else if (this.category === this.tabList[3]) {
        cols.push(
          {
            title: '序号',
            type: 'index',
            width: 80
          },
          {
            title: '订单交易编号',
            key: 'order_sn'
          },
          // {
          //   title: '扣款类型',
          //   key: 'fit_unsign_type',
          //   render: (h, params) => {
          //     const payType = this.payTypeList.find(item => item.id == params.row.fit_unsign_type)
          //     if (payType) {
          //       return h('div', payType.name)
          //     } else {
          //       return h('div', '-')
          //     }
          //   }
          // },
          {
            title: '月付方案',
            key: 'product_title'
          },
          {
            title: '扣款金额',
            key: 'order_amount',
          },
          {
            title: '实际收入金额',
            render: (h, params) => {
              const amount = new Big(params.row.amount)
              const rate = new Big(0.006)
              const fee = new Big(params.row.amount).mul(rate)
              const total = amount.minus(fee)
              return h('div', total.toFixed(2))
            }
          },
          {
            title: '会员',
            key: 'username'
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '签约销售',
            key: 'saleName',
            render: (h, params) => {
              // marketers_name or coach_name
              return h('div', params.row.marketers_name || params.row.coach_name)
            }
          },
          {
            title: '付款时间',
            key: 'pay_time',
            render: (h, params) => {
              if (params.row.pay_time) {
                return h('div', formatDate(Number(params.row.pay_time) * 1000, 'yyyy-MM-dd'))
              } else {
                return h('div', '-')
              }
            }
          },
          status,
          action)
      }
      return cols
    }
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.pay_time_start = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.pay_time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.pay_time_start = ''
        this.searchParams.pay_time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.daterange = []
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.handleSearch()
    },
    getList() {
      this.searchParams.fit_pay_type = this.category
      return this.$service.post('/Web/BusinessFit/fitAlipayOrderList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
          this.$emit('emitCount', this.total, this.category)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        fit_pay_type: this.category,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/BusinessFit/fitAlipayOrderList', params).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
  created() {
    const lazyTime = this.lazy ? 1000 : 0
    setTimeout(async () => {
      // 获取场馆列表
      !this.adminBusList && await this.getAdminBusList()
      this.searchParams.bus_id = this.busId
      this.getList()
    }, lazyTime)
  },
  activated() {
    if (this.$route.params.refresh == 1) {
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.search-panel {
  padding: 20px 0;

  .search-line {
    .wrap-line;

    .search-item {
      .wrap-line;
      margin: 10px;

      .value {
        margin-left: 10px;
        width: 200px;
      }
    }
  }
}
</style>
