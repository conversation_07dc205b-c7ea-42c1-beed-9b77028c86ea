<template>
  <div ref="chartRef" style="width: 400px; height: 300px;"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';

// props
const props = defineProps({
  options: Object
})

// chart
const chartRef = ref(null)
onMounted(() => {
  const chart = echarts.init(chartRef.value);
  chart.setOption(props.options);
});
</script>

<style lang="less" scoped></style>