<template>
  <Modal v-model="show" width="600" :mask-closable="false" @on-cancel="handleClose" title="提示">
    <Form ref="formRef" class="modal-form" :model="formData" :label-width="180">
      <!-- 解约方式选择 -->
      <FormItem prop="type" :rules="{ required: true, type: 'string', message: '请选择解约方式', trigger: 'change' }">
        <RadioGroup v-model="formData.type">
          <Radio label="1">立即执行</Radio>
          <Radio v-show="info && info.done_period < info.periods && lastPeriod" label="2">
            延期解约
            <span style="margin-left: 6px; font-size: 12px; color: #7f7f7f">系统会在下次扣款成功后进行解约</span>
          </Radio>
        </RadioGroup>
      </FormItem>

      <template v-if="formData.type === '1'">
        <!-- 剩余期数 -->
        <FormItem label="剩余期数">
          <span>{{ remainingPeriods }}期</span>
        </FormItem>
  
        <!-- 待付款金额 -->
        <FormItem label="待付款金额">
          <span>{{ pendingAmount }}元</span>
        </FormItem>
  
        <!-- 违约金比例 -->
        <FormItem label="违约金比例">
          <span>{{ penaltyRate }}%</span>
        </FormItem>
  
        <!-- 预估违约金 -->
        <FormItem label="预估违约金">
          <InputNumber v-model="formData.damages_cash" :min="0" :precision="2" style="width: 200px; display: inline-block"></InputNumber>
          <span style="margin-left: 8px">元</span>
        </FormItem>
      </template>
      <template v-else>
        <FormItem label="预估解约时待付款金额">
          <div>{{ nextInfo?.refund_cash }}元</div>
          <em>预估解约时待付款金额=当前待付款金额-下期扣款金额</em>
        </FormItem>
        <FormItem label="违约金比例">
          <span>{{ nextInfo?.damages_rate }}%</span>
        </FormItem>
        <FormItem label="预估违约金">
          <span>{{ nextInfo?.damages_cash }}元</span>
        </FormItem>
      </template>

      <!-- 确认提示 -->
      <FormItem>
        <div v-show="formData.type === '1'">
          <span style="color: red; font-size: 14px; margin-top: 20px">*</span>
          解约后未交费的订单（含欠费订单）将不再扣费，确定解约?
        </div>
        <div v-show="formData.type === '2'">
          当前履约进度是
          <b>【{{ info.done_period }}/{{ info.periods }}】</b>
          期，将在
          <b style="color: #70b603">{{ plan_deduction_time }}</b>
          第
          <!-- <b>【{{ + next_period + (info.down_payment_date_rule !== 't+1' ? 0 : 1) }}/{{ info.periods }}】</b> -->
          <b>【{{ next_period }}/{{ info.periods }}】</b>
          期扣款成功后解约。
        </div>
      </FormItem>
    </Form>

    <div slot="footer" class="modal-buttons">
      <Button type="primary" @click="handleConfirm" :loading="submitting">确定</Button>
      <Button @click="handleClose" style="margin-left: 8px">取消</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TheTerminateWithMoney',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      required: true,
    },
    hasNextPlan: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {
        type: '1', // 默认选择立即执行
        damages_cash: null, // 预估违约金
      },
      submitting: false,
      plan_deduction_time: '',
      lastPeriod: false,
      next_period: 0,
      nextInfo: {}
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
    // 剩余期数
    remainingPeriods() {
      return this.info?.remain_count || '0'
    },
    // 待付款金额
    pendingAmount() {
      return this.info?.refund_cash || '0'
    },
    // 违约金比例
    penaltyRate() {
      return this.info?.damages_rate || '0'
    },
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.initFormData()

        if (this.hasNextPlan) {
          const params = {
            open_merchant_id: this.info.open_merchant_id,
            subscription_no: this.info.subscription_no,
            card_type: this.info.card_type
          };
          this.$service.post('/Web/BusinessFit/getNextPlan', params).then(res => {
            if (res.data.errorcode === 0) {
              this.plan_deduction_time = res.data.data.plan_deduction_time
              this.lastPeriod = true
              this.next_period = res.data.data.period || this.info.done_period
              if (this.info.un_sign_type == 1) {
                this.formData.type = '1'
              } else if (this.info.un_sign_type == 2) {
                this.formData.type = '2'
              }
  
              this.nextInfo = res.data.data.damages
            } else if (res.data.errorcode === 404001) {
              this.lastPeriod = false
            } else {
              this.lastPeriod = false
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      }
    },
  },
  methods: {
    initFormData() {
      // 根据传入的info计算预估违约金
      this.formData = {
        type: '1',
        damages_cash: Number(this.info?.damages_cash || 0),
      }

      this.lastPeriod = false
    },
    handleClose() {
      this.$refs.formRef.resetFields()
      this.submitting = false
      this.$emit('input', false)
    },
    handleConfirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.submitting = true

          // const url = '/Web/BusinessFit/subscriptionSurrenderCancel'
          const url = '/Web/BusinessFit' + (this.formData.type === '1' ? '/subscriptionSurrender' : '/subscriptionSurrenderDelay')
          const params = {
            bus_id: this.info.bus_id,
            open_merchant_id: this.info.open_merchant_id,
            subscription_no: this.info.subscription_no,
            card_type: this.info.card_type,
            total_cash: this.pendingAmount,
            ...this.formData,
          }

          this.$service
            .post(url, params)
            .then((res) => {
              this.submitting = false
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.$emit('on-success')
                this.handleClose()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(() => {
              this.submitting = false
              this.$Message.error('请求失败，请重试')
            })
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.modal-form {
  .ivu-form-item-label {
    font-weight: normal;
    color: #333;
  }

  .ivu-form-item-content {
    span {
      color: #333;
      font-size: 14px;
    }
  }
}

.modal-buttons {
  text-align: center;

  .ivu-btn {
    min-width: 80px;
    height: 36px;
  }
}
</style>
