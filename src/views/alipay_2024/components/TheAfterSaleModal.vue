<template>
  <Modal
    v-model="show"
    title="售后详情"
    width="500"
    @on-cancel="handleClose">
    <div class="after-sale-info">
      <div class="info-line">
        <div class="info-label">售后单号:</div>
        <div class="info-value">{{ info.sales_id || '' }}</div>
      </div>
      <div class="info-line">
        <div class="info-label">卡号:</div>
        <div class="info-value">{{ info.subscription_no || '' }}</div>
      </div>
      <div class="info-line">
        <div class="info-label">售后状态:</div>
        <div class="info-value" :style="{ color: info.sales_status_color }">{{ info.sales_status_label || '' }}</div>
      </div>
      <div class="info-line">
        <div class="info-label">退卡原因:</div>
        <div class="info-value">{{ info.reason || '无' }}</div>
      </div>
      <div class="info-line">
        <div class="info-label">描述:</div>
        <div class="info-value">{{ info.des || '无' }}</div>
      </div>
      <div class="info-line">
        <div class="info-label">退卡图片:</div>
        <div class="info-value">
          <div v-if="info.images && info.images.length" class="image-container">
            <img
              v-for="(image, index) in info.images"
              :key="index"
              :src="image"
              class="refund-image"
              @click="previewImage(image)"
            />
          </div>
          <span v-else>无</span>
        </div>
      </div>
      <div class="info-line">
        <div class="info-label">创建时间:</div>
        <div class="info-value">{{ info.create_time || '' }}</div>
      </div>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button @click="handleClose">关闭</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TheAfterSaleModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // Any additional data properties can be added here
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    previewImage(url) {
      // If you have an image preview component, you can use it here
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.after-sale-info {
  padding: 10px 0;

  .info-line {
    display: flex;
    margin-bottom: 20px;

    .info-label {
      width: 100px;
      color: #333;
      font-weight: 500;
      text-align: right;
      padding-right: 10px;
    }

    .info-value {
      flex: 1;
      color: #666;
    }
  }

  .image-container {
    display: flex;
    flex-wrap: wrap;

    .refund-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}

.modal-buttons {
  text-align: center;
}
</style>