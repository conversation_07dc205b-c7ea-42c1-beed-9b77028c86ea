<template>
  <div>
    <div class="table-wrap">
      <header>
        <Input v-model="postData.search" class="w120" placeholder="会员/手机号" @on-enter="search" />
        <Input v-model="postData.product_title" class="w120" placeholder="产品名称" @on-enter="search" />
        <Select v-model="postData.status" class="w120" placeholder="订购状态" clearable filterable>
          <Option v-for="item in statusInfoArr" :key="item.key" :value="item.key">{{ item.name }}</Option>
        </Select>
        <!-- <SalesSelect v-model="postData.marketers_id" isCoach style="width:150px;" :belongBusId="busId"
          placeholder="选择业绩归属" /> -->
        <Select v-model="postData.time_type" class="w120">
          <Option label="签约时间" value="0"></Option>
          <Option label="解约时间" value="1"></Option>
        </Select>
        <DatePicker type="daterange" v-model="dateRange" class="w200" placeholder="选择时间" />
        <Button type="success" class="search" @click="search">搜索</Button>
        <!-- <Button type="text" :to="{ name: '查询扣款计划2' }">查询扣款计划</Button> -->
        <Button type="text" :to="`/alipay2/zhimaPlan`">查询扣款计划</Button>
      </header>
      <main>
        <Table ref="table" class="avatar-zoom" :columns="columns" :data="tableData" stripe disabled-hover></Table>
      </main>
      <footer>
        <Button @click="handleExcel">导出</Button>
        <Page class="page" :total="totalCount" :page-size="postData.page_size" :current.sync="postData.page_no"
          placement="top" show-total show-sizer @on-change="pageChanged" @on-page-size-change="pageSizeChanged"></Page>
      </footer>
    </div>
    <div v-if="showInfo">
      <ZhimaInfo v-model="showInfo" :info="curInfo" @on-success="getList" />
    </div>
    <div v-if="showFastInfo">
      <ZhimaFastInfo v-model="showFastInfo" :info="curInfo" @on-success="getList" />
    </div>

    <ZhimaSurrenderModal v-model="showSurrenderType" :info="curInfo" @on-success="getList" />
  </div>
</template>

<script>
import ZhimaInfo from "./components/ZhimaInfo"
import ZhimaFastInfo from "./components/ZhimaFastInfo"
import ZhimaSurrenderModal from "./components/ZhimaSurrenderModal"
// import SalesSelect from 'components/membership/salesSelect'
import { formatDate } from '@/utils/index'
import { mapGetters } from 'vuex'
import Big from 'big.js'

export default {
  name: 'Zhima2Subscriptions',
  components: {
    ZhimaInfo,
    ZhimaFastInfo,
    ZhimaSurrenderModal,
    // SalesSelect
  },
  data() {
    return {
      curInfo: {},
      statusInfoArr: [{
        key: 'NORMAL',
        name: '正常'
      }, {
        key: 'PAUSED',
        name: '已暂停'
      }, {
        key: 'WAIT_SURRENDER',
        name: '延期解约'
      }, {
        key: 'SURRENDER',
        name: '已解约'
      }, {
        key: 'END',
        name: '已完结'
      }],
      showInfo: false,
      showFastInfo: false,
      showSurrenderType: false, // 解约方式弹窗
      totalCount: 0,
      tableData: [],
      dateRange: [],
      postData: {
        card_id: '',
        product_title: '',
        status: '',
        marketers_id: '',
        time_type: '0',
        time_begin: '',
        time_end: '',
        page_size: 10,
        page_no: 1
      },
      columns: [
        {
          title: '会员',
          key: 'username',
          ellipsis: true
        },
        {
          title: '手机号',
          key: 'phone'
        },
        {
          title: '产品名称',
          key: 'product_title',
          ellipsis: true,
          render: (h, params) => {
            return (<a
              onClick={() => {
                this.handleShowFastInfo(params.row)
              }}>
              {params.row.product_title}
            </a>)
          }
        },
        {
          title: '签约时间',
          key: 'sign_time'
        },
        {
          title: '解约时间',
          key: 'surrender_time'
        },
        {
          title: '月付类型',
          render: (h, params) => {
            return <div>{params.row.product_type === 1 ? '常规' : '组合'}</div>
          }
        },
        {
          title: '月付金额',
          render: (h, params) => {
            if (params.row.product_type == 1) {
              return (<div>
                <div>首期￥{params.row.down_payment}</div>
                <div>后续￥{params.row.deduction_amount}</div>
              </div>)
            } else {
              let amount = new Big(0)
              params.row.combine_list.forEach(item => {
                amount = amount.plus(new Big(item.amount))
              })
              amount = amount.toFixed(2)
              return (<div>
                <div>签约￥{amount}</div>
                <div>每月￥{params.row.deduction_amount}</div>
              </div>)
            }
          }
        },
        // {
        //   title: '业绩归属',
        //   key: 'sale_name'
        // },
        {
          title: '订购状态',
          key: 'status',
          align: 'center',
          render: (h, params) => {
            return (<div>{this.statusNameByKey(params.row.status)}</div>)
          }
        },
        {
          title: '订购编号',
          key: 'subscription_no'
        },
        {
          title: '扣款计划',
          key: 'approve_status',
          render: (h, params) => {
            return (<a
              onClick={() => {
                this.handleShowInfo(params.row)
              }}>
              {params.row.done_period}/{params.row.periods}
            </a>)
          }
        },
        {
          title: '操作',
          width: 125,
          render: (h, params) => {
            return (
              <div>
                {
                  ['NORMAL', 'WAIT_SURRENDER'].includes(params.row.status)
                    ? <a
                      class="button-text-red mr5"
                      onClick={() => {
                        this.handleActionV2(params.row, params.row.status)
                      }}>
                      {params.row.status === 'NORMAL' ? '解约' : '取消解约'}
                    </a>
                    : null
                }
                <a
                  onClick={() => {
                    this.handleShowInfo(params.row)
                  }}>
                  查看
                </a>
              </div>
            )
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['busId']),
  },
  created() {
    this.getList()
  },
  methods: {
    handleShowInfo(info) {
      this.curInfo = info
      this.showInfo = true
    },
    handleShowFastInfo(info) {
      this.curInfo = info
      this.showFastInfo = true
    },
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if (key === iterator.key) {
          return iterator.name
        }
      }
    },
    /* handleAction(info, status) {
      const url = `Web/BusinessFit/${status == 1 ? 'subscriptionRegain' : status == 2 ? 'subscriptionPause' : 'subscriptionSurrender'}`
      this.$Modal.confirm({
        title: '提示',
        content: `${status==3?'解约后未交费的订单（含欠费订单）将不再扣费，':''}确定${status == 1 ? '恢复' : status == 2 ? '暂停' : '解约'}?`,
        onOk: () => {
          this.$service.post(url, { open_merchant_id: info.open_merchant_id, subscription_no: info.subscription_no }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    }, */
    handleActionV2(info, status) {
      this.curInfo = info
      if (status === 'NORMAL') { // 选择解约方式
        this.showSurrenderType = true
      } else if (status === 'WAIT_SURRENDER') { // 取消解约
        this.$service.post(
          'Web/BusinessFit/subscriptionSurrenderCancel',
          {
            open_merchant_id: info.open_merchant_id,
            subscription_no: info.subscription_no
          }
        )
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    },
    pageChanged(page) {
      this.postData.page_no = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.postData.page_size = size
      this.getList()
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      if (this.dateRange.length === 2 && this.dateRange[0] && this.dateRange[1]) {
        this.postData.time_begin = formatDate(this.dateRange[0], 'yyyy-MM-dd')
        this.postData.time_end = formatDate(this.dateRange[1], 'yyyy-MM-dd')
      } else {
        this.postData.time_begin = ''
        this.postData.time_end = ''
      }
      this.$service
        .post('/Web/BusinessFit/subscriptions', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data
            this.tableData = data.list
            this.totalCount = parseInt(data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleExcel() {
      const fakeParams = {
        ...this.postData,
        page_size: this.totalCount,
        page_no: 1,
        is_export: 1
      }
      if (this.dateRange.length === 2 && this.dateRange[0] && this.dateRange[1]) {
        fakeParams.time_begin = formatDate(this.dateRange[0], 'yyyy-MM-dd')
        fakeParams.time_end = formatDate(this.dateRange[1], 'yyyy-MM-dd')
      } else {
        fakeParams.time_begin = ''
        fakeParams.time_end = ''
      }
      this.$service.post('/Web/BusinessFit/subscriptions', fakeParams).then(res => {
        if (res.data.errorcode == 0) {
          // const data = res.data.data.list
          // data.forEach(item => {
          //   item.phone = '`' + item.phone
          //   item.sign_time = '`' + (item.sign_time || '')
          //   item.surrender_time = '`' + (item.surrender_time || '')
          //   item.sell_type = '连续包月'
          //   item.status = this.statusNameByKey(item.status)
          //   item.approve_status = '`' + item.done_period + '/' + item.periods
          // })
          // this.$refs.table.exportCsv({
          //   filename: '签约会员列表',
          //   columns: this.columns.slice(0, 10),
          //   data
          // })

          this.$Message.success({
            content: '导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>
