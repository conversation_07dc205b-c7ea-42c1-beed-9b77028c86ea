<template>
  <div class="container">
    <div class="box">
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" @on-change="searchParams.belong_id = ''" placeholder="请选择场馆"
                class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">合约编号</div>
              <Input v-model="searchParams.subscription_no" placeholder="请输入合约编号" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">签约会员</div>
              <Input v-model="searchParams.keyword" placeholder="请输入会员手机号码" class="value" clearable />
            </div>
            <div class="search-item" v-if="searchParams.bus_id">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.belong_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="searchParams.bus_id" />
            </div>
            <div class="search-item">
              <div class="label">签约时间</div>
              <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer />
            </div>
            <div class="search-item">
              <div class="label">计划扣款时间</div>
              <DatePicker v-model="planDaterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer />
            </div>
            <div class="search-item">
              <div class="label">实际扣款时间</div>
              <DatePicker v-model="realDaterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer />
            </div>
            <div class="search-item">
              <div class="label">扣款状态</div>
              <Select v-model="statusSelected" placeholder="请选择扣款状态" class="value" multiple transfer>
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <div class="panel-box">
          <div class="panel-item" v-for="(item, index) in panelList" :key="index">
            <Card>
              <div class="value">{{ item.value }}</div>
              <div class="label">{{ item.label }}</div>
            </Card>
          </div>
        </div>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <Button style="margin-left: 10px" @click="handleExport">导出</Button>
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheDetailModal :showModal.sync="detailModal" :params="detailParams" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheDetailModal from './components/TheDetailModal.vue'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  subscription_no: '',
  keyword: '',
  belong_id: '',
  sign_time_begin: '',
  sign_time_end: '',
  actual_deduction_time_begin: '',
  actual_deduction_time_end: '',
  status: '',
  plan_deduction_time_begin: '',
  plan_deduction_time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'DeductionRecord',
  components: { SalesSelect, TheDetailModal },
  data() {
    return {
      daterange: [],
      planDaterange: [],
      realDaterange: [],
      statusSelected: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 合约编号 场馆 签约会员 业绩归属 扣款期数 计划扣款日期 实际扣款时间 扣款金额 扣款状态
      columns: [
        {
          title: '合约编号',
          key: 'subscription_no',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.detailParams = { bus_id: this.searchParams.bus_id, subscription_no: params.row.subscription_no }
                  this.detailModal = true
                }
              }
            }, params.row.subscription_no)
          }
        },
        {
          title: '支付宝订单',
          key: 'deduction_order_id'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '签约会员',
          key: 'username'
        },
        {
          title: '合约卡种',
          key: 'card_name'
        },
        {
          title: '手机号',
          key: 'phone'
        },
        {
          title: '业绩归属',
          key: 'sale_name'
        },
        {
          title: '扣款期数',
          key: 'period_div',
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.period}/${row.periods}`)
          }
        },
        {
          title: '计划扣款日期',
          key: 'plan_deduction_time',
        },
        {
          title: '实际扣款时间',
          key: 'actual_deduction_time'
        },
        {
          title: '扣款金额',
          key: 'deduction_amount',
          render: (h, params) => {
            const price = Number(params.row.deduction_amount)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '扣款状态',
          key: 'status_name',
          render: (h, params) => {
            const item = this.statusList.find(item => item.value === params.row.status)
            const label = item ? item.label : '-'
            const color = item ? item.color : ''
            return h('div', { style: { color } }, label)
          }
        }
      ],
      list: [],
      total: 0,
      // 支付状态：待付款 WAIT_PAY, 暂停 PAUSED，支付成功 PAID，扣款失败 PAY_FAILED, 已取消 CANCEL，多种状态以逗号分隔
      statusList: [
        { value: 'WAIT_PAY', label: '待付款', color: 'gray' },
        { value: 'PAUSED', label: '暂停', color: 'grey' },
        { value: 'PAID', label: '支付成功', color: 'green' },
        { value: 'PAY_FAILED', label: '扣款失败', color: 'red' },
        { value: 'CANCEL', label: '已取消', color: 'gray' },
      ],
      panelList: [
        { label: '待付款总金额', code: 'wait', value: 0 },
        { label: '支付成功总金额', code: 'paid', value: 0 },
        { label: '扣款失败总金额', code: 'fail', value: 0 },
        { label: '合约暂停总金额', code: 'pause', value: 0 },
        { label: '合约取消总金额', code: 'cancel', value: 0 },
      ],
      detailModal: false,
      detailParams: {
        bus_id: '',
        subscription_no: '',
      }
    }
  },
  computed: {
    ...mapGetters(['busId', 'adminBusList']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      // if (!this.planDaterange[0] && !this.realDaterange[0]) {
      //   return this.$Message.warning('计划扣款日期和实际扣款日期至少选择一个')
      // }
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.sign_time_begin = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.sign_time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.sign_time_begin = ''
        this.searchParams.sign_time_end = ''
      }
      if (Array.isArray(this.planDaterange) && this.planDaterange.length === 2 && this.planDaterange[0] && this.planDaterange[1]) {
        this.searchParams.plan_deduction_time_begin = formatDate(this.planDaterange[0], 'yyyy-MM-dd')
        this.searchParams.plan_deduction_time_end = formatDate(this.planDaterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.plan_deduction_time_begin = ''
        this.searchParams.plan_deduction_time_end = ''
      }
      if (Array.isArray(this.realDaterange) && this.realDaterange.length === 2 && this.realDaterange[0] && this.realDaterange[1]) {
        this.searchParams.actual_deduction_time_begin = formatDate(this.realDaterange[0], 'yyyy-MM-dd')
        this.searchParams.actual_deduction_time_end = formatDate(this.realDaterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.actual_deduction_time_begin = ''
        this.searchParams.actual_deduction_time_end = ''
      }
      if (Array.isArray(this.statusSelected) && this.statusSelected.length > 0) {
        this.searchParams.status = this.statusSelected.join(',')
      } else {
        this.searchParams.status = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      this.daterange = []
      this.planDaterange = []
      this.realDaterange = []
      this.statusSelected = []
      this.setSearchDateRange()
      this.handleSearch()
    },
    getList() {
      this.list = []
      this.total = 0
      const panel = {
        cancel: 0,
        fail: 0,
        paid: 0,
        pause: 0,
        wait: 0
      }
      return this.$service.post('/Web/BusinessFit/fitItemsList', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          if (Array.isArray(res.data.data.list)) {
            this.list = res.data.data.list
          }
          this.total = Number(res.data.data.count || 0)
          const sum = { ...panel, ...res.data.data.sum }
          this.panelList.forEach((item) => {
            item.value = Number(sum[item.code] || 0).toFixed(2)
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
    handleExport() {
      // this.handleSearch()
      const params = {
        ...this.searchParams,
        is_export: 1,
        page_no: 1,
        page_size: this.total
      }
      return this.$service.post('/Web/BusinessFit/fitItemsList', params).then((res) => {
        if (res.data.errorcode === 0) {
          // this.$Message.success({
          //   content: '导出任务运行中，请稍后到消息中心下载!',
          //   duration: 3
          // })
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
            list.forEach(row => {
              row.subscription_no = `\u0009${row.subscription_no}`
              row.phone = `\u0009${row.phone}`
              row.period_div = `(${row.period}/${row.periods})`
              const item = this.statusList.find(item => item.value === row.status)
              const label = item ? item.label : '-'
              row.status_name = label
              row.sale_name = row.sale_name.replace(',', ' ')
            })
          }
          this.$refs.selection.exportCsv({
            filename: `履约扣款记录-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.columns,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setSearchDateRange() {
      // default 30 days ago to today
      // const today = new Date()
      // const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      // this.planDaterange = [thirtyDaysAgo, today]
      // default current month
      const today = new Date()
      const firstDate = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      this.planDaterange = [firstDate, lastDate]
    }
  },
  async created() {
    const { busId, subscriptionNo, date, status } = this.$route.query
    // 获取场馆列表
    !this.adminBusList && await this.getAdminBusList()
    if (busId) {
      this.searchParams.bus_id = busId
    } else {
      this.setSearchDateRange()
      this.searchParams.bus_id = this.busId
    }
    if (subscriptionNo) {
      this.searchParams.subscription_no = subscriptionNo
    }
    if (date) {
      const dateObj = new Date(date)
      const firstDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1)
      const lastDate = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0)
      this.daterange = [firstDate, lastDate]
    }
    if (status) {
      this.statusSelected = [status]
    }
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
}
</style>
