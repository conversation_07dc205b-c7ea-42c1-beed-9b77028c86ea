<template>
  <div class="container">
    <div class="box">
      <div class="search-panel">
        <Card>
          <div class="search-line">
            <!-- <div class="search-item">
              <div class="label">场馆</div>
              <Select v-model="searchParams.bus_id" @on-change="searchParams.marketers_id = ''" placeholder="请选择场馆"
                class="value" filterable transfer>
                <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </div> -->
            <div class="search-item">
              <div class="label">签约会员</div>
              <Input v-model="searchParams.search" placeholder="请输入会员姓名/手机号码" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">产品方案</div>
              <Input v-model="searchParams.product_title" placeholder="请输入产品方案" class="value" clearable />
            </div>
            <div class="search-item">
              <div class="label">业绩归属</div>
              <SalesSelect v-model="searchParams.marketers_id" placeholder="选择销售人员" class="value" isCoach
                :belongBusId="busId" />
            </div>
            <div class="search-item">
              <div class="label">状态</div>
              <Select v-model="searchParams.status" placeholder="请选择状态" class="value" clearable transfer>
                <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </div>
            <div class="search-item">
              <div class="label">申请时间</div>
              <DatePicker v-model="daterange" type="daterange" format="yyyy-MM-dd" placeholder="请选择时间" class="value"
                transfer :clearable="false" />
            </div>
            <div class="search-item">
              <Button type="success" @click="handleSearch">查询</Button>
            </div>
            <div class="search-item">
              <Button @click="handleReset">重置</Button>
            </div>
          </div>
        </Card>
        <Card style="margin-top: 20px">
          <Table ref="selection" :columns="columns" :data="list" stripe disabled-hover></Table>
          <div style="margin-top: 10px; display: flex; justify-content: space-between;">
            <div>
              <!-- <Button style="margin-left: 10px" @click="handleExport">导出</Button> -->
            </div>
            <Page @on-change="handlePageChange" :total="total" :current="searchParams.page_no"
              @on-page-size-change="handlePageSizeChange" show-total show-sizer>
            </Page>
          </div>
        </Card>
      </div>
    </div>

    <TheAfterSaleModal v-model="afterSaleModal" :info="curInfo" />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapGetters, mapActions } from 'vuex'
import SalesSelect from 'components/membership/salesSelect'
import TheAfterSaleModal from './components/TheAfterSaleModal.vue'
import { statusList } from './types'

const NONE_SEARCH_PARAMS = {
  bus_id: '',
  product_title: '',
  search: '',
  marketers_id: '',
  status: '',
  time_begin: '',
  time_end: '',
  page_no: 1,
  page_size: 10,
}

export default {
  name: 'TerminateAfterSales',
  components: { SalesSelect, TheAfterSaleModal },
  data() {
    return {
      daterange: [],
      searchParams: { ...NONE_SEARCH_PARAMS },
      // 申请时间 合约编号 签约会员 电话号码 场馆 业绩归属 产品方案 已履约期数 已履约金额 违约金总金额 状态 操作
      columns: [
        {
          title: '申请时间',
          key: 'create_time',
        },
        {
          title: '售后单号',
          key: 'sales_id',
          render: (h, params) => {
            return h('a', {
              on: {
                click: () => {
                  this.curInfo = params.row
                  const item = this.statusList.find(item => item.value === params.row.sales_status)
                  const label = item ? item.label : '-'
                  const color = item ? item.color : ''
                  this.curInfo.sales_status_label = label
                  this.curInfo.sales_status_color = color
                  this.afterSaleModal = true
                }
              }
            }, params.row.sales_id)
          }
        },
        {
          title: '签约会员',
          key: 'username'
        },
        {
          title: '电话号码',
          key: 'phone'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '业绩归属',
          key: 'marketers_name'
        },
        {
          title: '产品方案',
          key: 'product_title'
        },
        {
          title: '已履约期数',
          key: 'format_period',
          renderHeader: (h) => {
            return h('Tooltip', {
              props: { placement: 'top', transfer: true }
            }, [
              h('span', '已履约期数 '),
              h('Icon', { props: { type: 'md-help-circle', size: '16' } }),
              h('div', {
                slot: 'content',
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-all',
                  textAlign: 'left'
                }
              }, [
                h('p', '已完成扣款总期数 / 签约期数'),
              ])
            ])
          },
          render: (h, params) => {
            const row = params.row
            return h('div', `${row.done_period}/${row.periods}`)
          }
        },
        {
          title: '已履约金额',
          key: 'actual_amount',
          render(h, params) {
            const price = Number(params.row.actual_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '未履约金额',
          key: 'no_amount',
          render(h, params) {
            const price = Number(params.row.no_amount || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '违约金比例',
          key: 'refund_cash',
          render(h, params) {
            const price = Number(params.row.damages_rate || 0)
            return h('span', price.toFixed(2) + '%')
          }
        },
        {
          title: '违约金金额',
          key: 'damages_cash',
          render(h, params) {
            const price = Number(params.row.damages_cash || 0)
            return h('span', price.toFixed(2))
          }
        },
        {
          title: '状态',
          key: 'format_status',
          render: (h, params) => {
            const item = this.statusList.find(item => item.value === params.row.sales_status)
            const label = item ? item.label : '-'
            const color = item ? item.color : ''
            return h('div', { style: { color } }, label)
          }
        },
      ],
      list: [],
      total: 0,
      // 售后-初始化: INIT 已同意，待支付(退款中): REFUNDING 待商户确认: WAIT_MERCHANT_CONFIRM 待用户确认: WAIT_USER_CONFIRM 售后完成（退卡成功）: SUCCESS 退卡失败: FAIL 已取消: CANCEL 已关闭: CLOSED 纠纷中: IN_DISPUTE
      statusList,
      afterSaleModal: false,
      curInfo: {},
    }
  },
  computed: {
    // ...mapGetters(['busId', 'adminBusList']),
    ...mapGetters(['busId']),
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    handleSearch() {
      this.searchParams.page_no = 1
      if (Array.isArray(this.daterange) && this.daterange.length === 2 && this.daterange[0] && this.daterange[1]) {
        this.searchParams.time_begin = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchParams.time_end = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchParams.time_begin = ''
        this.searchParams.time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.searchParams = { ...NONE_SEARCH_PARAMS }
      this.searchParams.bus_id = this.busId
      // recent 90 days
      this.daterange = [
        new Date(new Date().setDate(new Date().getDate() - 90)),
        new Date()
      ]
      this.handleSearch()
    },
    getList() {
      return this.$service.post('/Web/BusinessFit/saleafter_list', this.searchParams).then((res) => {
        if (res.data.errorcode === 0) {
          this.list = res.data.data.list
          this.total = res.data.data.count
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePageChange(page) {
      this.searchParams.page_no = page
      this.getList()
    },
    handlePageSizeChange(pageSize) {
      this.searchParams.page_size = pageSize
      this.getList()
    },
  },
  async created() {
    // 获取场馆列表
    // TODO: 跳转过来的未查明
    // !this.adminBusList && await this.getAdminBusList()
    // if (this.$route.params.busId) {
    //   this.searchParams.bus_id = this.$route.params.busId
    // } else {
    //   this.searchParams.bus_id = this.busId
    // }
    // if (this.$route.params.subscriptionNo) {
    //   this.searchParams.subscription_no = this.$route.params.subscriptionNo
    // }
    this.searchParams.bus_id = this.busId
    // recent 30 days
    this.daterange = [
      new Date(new Date().setDate(new Date().getDate() - 30)),
      new Date()
    ]
    this.handleSearch()
  },
}
</script>

<style lang="less" scoped>
.wrap-line {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}

.box {
  padding: 20px;

  .search-panel {
    padding: 20px 0;

    .search-line {
      .wrap-line;

      .search-item {
        .wrap-line;
        margin: 10px;

        .value {
          margin-left: 10px;
          width: 200px;
        }
      }
    }

    .panel-box {
      .wrap-line;
      padding-top: 20px;

      .panel-item {
        margin-right: 20px;

        .value {
          font-size: 30px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
}
</style>
