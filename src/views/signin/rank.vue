<template>
<div class="box-wrap">
   <section class="rank-box" v-if="curIndex === 1" ref="rankBox" :style="{zoom:screenZoom}">
      <div class="box-lef">
        <img class="qrcode-img" :src="select===1 ? miniSignImg : otherMiniSignImg">
        <div class="lef-tips">
          今日共有<span>{{busTrainCount}}</span>位健身达人到店训练
        </div>
        <!-- TODO 无法校验是否有勤鸟+权限  【直接给的URL地址,v-if永远是TRUE】  -->
        <div class="changeQrcode" v-if="otherMiniSignImg">
          <Select v-model="select" size="large" style="width: 100%;">
            <Option v-for="(item,i) in list" :value="item.value" :key="i">{{ item.label }}</Option>
          </Select>
        </div>
      </div>
      <div class="box-cen">
        <div class="cen-top">
          <ul class="rank-list">
            <li v-for="(data, index) in beforeSignRank" :key="data.user_id">
              <img class="li-avatar" :src="data.avatar" alt="" />
              <div class="li-name">{{data.nickname}}</div>
              <div class="li-count">{{data.sign_count}}天</div>
              <div class="li-no">NO.{{index + 1}}</div>
            </li>
          </ul>
        </div>
        <div class="cen-bottom">
          <ul class="rank-list">
            <li v-for="(data, index) in beforePtSignRank" :key="data.user_id">
              <img class="li-avatar" :src="data.avatar" alt="" />
              <div class="li-name">{{data.nickname}}</div>
              <div class="li-count">{{data.sign_count}}次</div>
              <div class="li-no">NO.{{index + 1}}</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="box-rig">
        <button class="show-charm-btn" @click="showCharmModal = true; charmForm.type = charmType">
          <icon size="18" type="ios-arrow-down" />
        </button>
        <ul class="rank-list">
            <li v-for="(data, index) in charmRank" :key="data.user_id">
              <img class="li-avatar" :src="data.avatar" alt="" />
              <div class="li-name">{{data.nickname}}</div>
              <div class="li-count">{{data.charm_value}}</div>
              <div class="li-no">NO.{{index + 1}}</div>
            </li>
          </ul>
      </div>
  </section>
  <section class="rank-box box2" ref="rankBox" :style="{zoom:screenZoom}" v-if="curIndex === 2">
      <div class="box-lef">
        <img class="logo-img" :src="logo">
        <div class="logo-name">{{busName}}</div>
      </div>
      <div class="box2-rig">
        <div class="tit">
          本月到店排行榜
        </div>
        <div class="rig-top">
          <div class="top-lef" v-if="signRank[1]">
            <img class="avatar" :src="signRank[1].avatar" alt="" />
            <div class="name">{{signRank[1].nickname}}</div>
            <div class="count">{{signRank[1].sign_count}}天</div>
          </div>
          <div class="top-cen" v-if="signRank[0]">
            <img class="avatar" :src="signRank[0].avatar" alt="" />
            <div class="name">{{signRank[0].nickname}}</div>
            <div class="count">{{signRank[0].sign_count}}天</div>
          </div>
          <div class="top-rig" v-if="signRank[2]">
            <img class="avatar" :src="signRank[2].avatar" alt="" />
            <div class="name">{{signRank[2].nickname}}</div>
            <div class="count">{{signRank[2].sign_count}}天</div>
          </div>
        </div>
         <div class="rig-bottom">
          <ul class="rank-list">
              <li v-for="(data, index) in cenSignRank" :key="data.user_id">
                <img class="li-avatar" :src="data.avatar" alt="" />
                <div class="li-name">{{data.nickname}}</div>
                <div class="li-count">{{data.sign_count}}天</div>
                <div class="li-no">NO.{{index + 4}}</div>
              </li>
            </ul>
        </div>
      </div>
  </section>
  <section class="rank-box box2" ref="rankBox" :style="{zoom:screenZoom}" v-if="curIndex === 3">
      <div class="box-lef">
        <img class="logo-img" :src="logo">
        <div class="logo-name">{{busName}}</div>
      </div>
      <div class="box2-rig">
        <div class="tit">
          本月私教排行榜
        </div>
        <div class="rig-top">
          <div class="top-lef" v-if="ptSignRank[1]">
            <img class="avatar" :src="ptSignRank[1].avatar" alt="" />
            <div class="name">{{ptSignRank[1].nickname}}</div>
            <div class="count">{{ptSignRank[1].sign_count}}次</div>
          </div>
          <div class="top-cen" v-if="ptSignRank[0]">
            <img class="avatar" :src="ptSignRank[0].avatar" alt="" />
            <div class="name">{{ptSignRank[0].nickname}}</div>
            <div class="count">{{ptSignRank[0].sign_count}}次</div>
          </div>
          <div class="top-rig" v-if="ptSignRank[2]">
            <img class="avatar" :src="ptSignRank[2].avatar" alt="" />
            <div class="name">{{ptSignRank[2].nickname}}</div>
            <div class="count">{{ptSignRank[2].sign_count}}次</div>
          </div>
        </div>
         <div class="rig-bottom">
          <ul class="rank-list">
              <li v-for="(data, index) in cenPtSignRank" :key="data.user_id">
                <img class="li-avatar" :src="data.avatar" alt="" />
                <div class="li-name">{{data.nickname}}</div>
                <div class="li-count">{{data.sign_count}}次</div>
                <div class="li-no">NO.{{index + 4}}</div>
              </li>
            </ul>
        </div>
      </div>
  </section>
  <Modal v-model="showModal" :style="{zoom:screenZoom}" :width="510" :closable="false" class-name="rank-modal">
        <div class="rank-modal-title">签到成功！</div>
        <img class="rank-modal-avatar" :src="signUserData.user_avatar" alt="" />
        <div class="rank-modal-text">到店排名<span>{{signUserData.user_sign_rank}}</span></div>
        <div class="rank-modal-text" >魅力值排名<span>{{signUserData.user_charm_value_rank}}</span></div>
        <div slot="footer"></div>
      </Modal>
  <Modal v-model="showCharmModal" :width="510" :closable="false">
      <Form
        ref="packageForm"
        :model="charmForm"
      >
        <FormItem label="排行榜">
          <Select v-model="charmForm.type" placeholder="请选择">
            <Option value="1">商家积分排行榜</Option>
            <Option value="2">场馆积分排行榜</Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleConfirmCharm">确定</Button>
        <Button @click="showCharmModal = false; charmForm.type = charmType">取消</Button>
      </div>
  </Modal>
</div>
</template>

<script>
  import socket from 'mixins/socket'
  import { getBaseUrl } from 'utils/config';
  import { toggleFullScreen } from 'utils';
  import {
    mapState
  } from 'vuex'

  const RANK_CHARM_TYPE = 'RANK_CHARM_TYPE'

  export default {
    name: 'screen',
    props: {},
    mixins: [socket],
    components: {
    },
    data() {
      return {
        miniSignImg: '',
        curIndex: 1,
        logo: '',
        screenW: 1920,
        screenZoom: 1,
        showModal: false,
        modalTimeOut: '',
        socketNoticeArray: [],
        charmValueRank: [], // 商家积分排行榜
        charmStoreValueRank: [], // 场馆积分排行榜
        signRank: [],
        ptSignRank: [],
        busTrainCount: 0,
        signUserData: {
          user_id: '',
          user_avatar: '',
          user_sign_rank: '',
          user_charm_value_rank: '',
          symbol: ''
        },
        list:[
          {value:1,label:"运动生活管家"},
          {value:2,label:"勤鸟+"},
        ],
        select:1,
        otherMiniSignImg: '',

        showCharmModal: false,
        charmForm: {
          type: '1', // 1商家 2场馆
        },
        charmType: '1' // 默认 1商家
      }
    },
    computed: {
      ...mapState(['busId', 'busName']),
      ...mapState('websocket', [
        'websocket'
      ]),
      beforeSignRank() {
        return this.signRank.slice(0, 5)
      },
      beforePtSignRank() {
        return this.ptSignRank.slice(0, 5)
      },
      cenSignRank() {
        return this.signRank.slice(3, 13)
      },
      cenPtSignRank() {
        return this.ptSignRank.slice(3, 13)
      },
      charmRank() {
        return this.charmType === '1' ? this.charmValueRank : this.charmStoreValueRank
      }
    },
    watch: {
      websocket(val, oldVal) {
        if (val && val !== oldVal) {
          this.websocket.onmessage = this.websocketonmessage
        }
      },
      showModal(val) {
        if (val) {
          this.setCurSignInfo()
          this.modalTimeOut = setTimeout(() => {
            this.showModal = false;
          }, 6000);
        } else {
          this.socketNoticeArray.splice(0, 1)
        }
      },
      socketNoticeArray(val, oldVal) {
        if (val && val.length === 0) {
          this.showModal = false
        }
      }
    },
    created() {
      const charmType = localStorage.getItem(RANK_CHARM_TYPE) || '1'
      this.charmType = charmType
      this.$store.dispatch('getBusInfo').then(res => {
        document.title = `${this.busName}_${this.$route.name}`
        this.logo = res.data.data.thumb
      })
      this.getRank();
      this.miniSignImg = getBaseUrl() + '/Web/Applet/applet_qrcode';
      this.otherMiniSignImg = getBaseUrl() + '/Web/Applet/qn_plus_qrcode';
    },
    beforeDestroy() {
      document.removeEventListener('keydown', this.handleKeyDown); //解绑
    },
    mounted() {
      document.addEventListener('keydown', this.handleKeyDown); //绑定
      this.$Modal.confirm({
        title: '提示',
        content: '按下左右箭头键可以切换排行榜类型，点击确定全屏展示',
        onOk: () => {
          toggleFullScreen();
        }
      });
      this.screenW = document.documentElement.clientWidth || document.body.clientWidth;
      this.screenZoom = this.screenW / 1920;
      window.addEventListener("resize", ()=>{
        this.screenW = document.documentElement.clientWidth || document.body.clientWidth;
        this.screenZoom = this.screenW / 1920;
      },false);
    },
    beforeDestroy() {
      this.destroyEvent()
    },
    methods: {
      handleKeyDown(e) {
        let event = e || window.event;
        let code = event.keyCode || event.which || event.charCode;
        // 左箭头
        if (code === 37) {
          if (this.curIndex >1) {
            this.curIndex = this.curIndex - 1
          } else {
             this.curIndex = 3
          }
        }
        // 右箭头
        if (code === 39) {
          if (this.curIndex > 0 && this.curIndex < 3) {
            this.curIndex = this.curIndex + 1
          } else {
             this.curIndex = 1
          }
        }
      },
      websocketonmessage(e) {
        //{"user_id":"8289","create_time":1524730365,"sign_log_id":"14279","type":"sign","action":"push_info","symbol":"01|34"}
        //{"admin_id":"1","create_time":1524730191,"msg_count":null,"type":"","action":"push_info","symbol":"01|34"}
        const resdata = JSON.parse(e.data)
        if (
          resdata.action === 'push_info' &&
          resdata.type === 'rank' &&
          resdata.bus_id == this.busId
        ) {
          this.socketNoticeArray.push(resdata)
            if (this.showModal === true) {
              this.next()
            } else {
              this.showModal = true
            }
        }
      },
      getRank() {
        let postData = {
          sign_rank_num: 13,
          pt_sign_rank_num: 13,
          charm_value_rank_num: 10,
          charm_store_value_rank_num: 10
        }
        return this.$service
          .post('/Web/SignScreen/rank', postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const resData = res.data.data;
              // 去掉.00小数
              Array.isArray(resData.charm_value_rank) && resData.charm_value_rank.forEach(v => {
                v.charm_value = +v.charm_value
              })
              Array.isArray(resData.store_value_rank) && resData.store_value_rank.forEach(v => {
                v.charm_value = +v.charm_value
              })
              this.charmValueRank = resData.charm_value_rank || []; // 商家
              this.charmStoreValueRank = resData.store_value_rank || []; // 场馆
              this.signRank = resData.sign_rank;
              this.ptSignRank = resData.pt_sign_rank;
              this.busTrainCount = resData.bus_train_count;
            } else {
              this.$Message.error(res.data.errormsg)
              if (res.data.errorcode === 40002) {
                this.$router.push({path: '/login', name: '登录', params: { from: window.location.hash }});
              }
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      destroyEvent() {
        this.noticeUserInfo = null
      },
      setCurSignInfo() {
        let curSignInfo = this.socketNoticeArray[0]
          this.signUserData = curSignInfo

          this.signRank = curSignInfo.sign_rank;
          this.ptSignRank = curSignInfo.pt_sign_rank;
          this.busTrainCount = curSignInfo.bus_train_count;
      },
      //下一条
      next() {
        this.socketNoticeArray.splice(0, 1)
        this.signUserData = {
          user_id: '',
          user_avatar: '',
          user_sign_rank: '',
          user_charm_value_rank: '',
          symbol: ''
        }
        if (this.socketNoticeArray.length !== 0) {
          this.setCurSignInfo()
        }
      },

      handleConfirmCharm() {
        this.charmType = this.charmForm.type
        localStorage.setItem(RANK_CHARM_TYPE, this.charmType)
        this.showCharmModal = false
      }
    }
  }
</script>
<style lang="less">
body {
  background: #000;
}
.rank-modal {
  top: 10%;
  .ivu-modal-content {
    width: 510px;
    height: 604px;
    background-image: url(~assets/img/screen_modal.png);
    background-repeat: no-repeat;
    background-size: cover;
    background-color: transparent;
    .rank-modal-avatar {
      display: block;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      margin: 38px 0 55px 130px;
    }
    .rank-modal-text {
      display: block;
      width: 330px;
      height: 56px;
      line-height: 56px;
      font-size: 28px;
      color: #fff;
      margin: 0 auto 30px;
      text-align: center;
      span{
        color: #de2283;
      }
    }
    .rank-modal-title {
      font-size: 38px;
      height: 46px;
      margin: 35px 0 0 30px;
      line-height: 46px;
      text-align: center;
      color: #24eefa;
    }
  }
}
</style>
<style lang="less" scoped>
  .box-wrap {
    width: 100%;
    height: 100%;
  }
  .rank-box {
    height: 1080px;
    width: 1920px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-image: url(~assets/img/screen_bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    box-sizing: border-box;
    padding: 87px 70px 64px;
    position: relative;
    .box-lef {
      width: 710px;
      height: 100%;
      float: left;
      margin-right: 27px;
      .qrcode-img {
        width: 210px;
        height: 210px;
        display: block;
        margin: 375px auto 95px;
      }
      .logo-name {
        text-align: center;
        font-size: 66px;
        color: #fff;
      }
      .logo-img {
        display: block;
        width: 200px;
        height: auto;
        margin: 225px auto 70px;
      }
      .lef-tips {
        text-align: center;
        margin: 0 auto;
        width: 500px;
        height: 50px;
        line-height: 50px;
        font-size: 32px;
        color: #fff;
        span {
          color: #24eefa;
        }
      }
    }
    .box-cen {
      float: left;
      width: 511px;
      height: 100%;
      margin-right: 22px;
      .cen-top {
        margin-bottom: 45px;
        height: 445px;
      }
    }
    .box-rig {
      position: relative;
      float: left;
      min-width: 509px;
      height: 100%;

      .show-charm-btn {
        position: absolute;
        top: -12px;
        left: 50%;
        padding-right: 16px;
        width: 200px;
        height: 36px;
        text-align: right;
        transform: translate(-50%, -50%);
        outline: 0;
        border: 0;
        background-color: transparent;
        cursor: pointer;
      }
    }
  }
  .box2-rig {
    float: left;
    width: 1043px;
    height: 100%;
    .tit {
      font-size: 22px;
      color: #000;
      text-align: center;
      margin-top: -28px;
    }
    .rig-top {
      width: 100%;
      height: 480px;
      overflow: hidden;
      text-align: center;
      color: #fff;
      font-size: 24px;
      img {
        margin-bottom: 43px;
        width: 68px;
        height: 68px;
        border-radius: 50%;
      }
    }
    .top-lef {
      margin-top: 190px;
      float: left;
      width: 225px;
      margin-left: 107px;
      .count {
        color: #40fae1;
      }
    }
    .top-cen {
      float: left;
      margin-top: 186px;
      width: 253px;
      margin-left: 55px;
      .count {
        color: #ff9804;
      }
      img {
        margin-bottom: 50px;
      }
    }
    .top-rig {
      margin-top: 190px;
      float: left;
      width: 228px;
      margin-left: 56px;
      .count {
        color: #00d2ff;
      }
    }
  }
  .box2 {
    background-image: url(~assets/img/screen_bg2.jpg);
    background-repeat: no-repeat;
    background-size: cover;
  }
  .rank-list {
    color: #fff;
    font-size: 23px;
    padding: 40px 0 0 24px;
    li {
      width: 464px;
      height: 61px;
      line-height: 61px;
      box-sizing: border-box;
      padding-left: 26px;
      padding-right: 10px;
      background-image: url(~assets/img/screen_list.png);
      background-repeat: no-repeat;
      background-size: cover;
      margin-bottom: 17px;
      clear: both;
    }
    .li-avatar {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      margin:11px 14px 0 5px;
      float: left;
    }
    .li-name {
      float: left;
      width: 170px;
      // margin-right: 10px;
      height: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .li-count {
      float: left;
      width: 100px;
      color: #1d959c;
      height: 100%;
    }
    .li-no {
      float: right;
      width: 70px;
      height: 100%;
    }
  }
  .box2-rig .rank-list li{
    float: left;
    margin-right: 15px;
    clear: none;
  }
  .changeQrcode{
    width: 160px;
    height: 50px;
    transform: translate(520px,-520px);
  }
  .changeQrcode /deep/ .ivu-select-selection{
    background: none;
    border: 0px
  }
  .changeQrcode /deep/ .ivu-select{
    color: #FFF;
  }
  .changeQrcode /deep/ .ivu-select-selected-value{
    font-size: 18px !important;
  }
  .changeQrcode /deep/ .ivu-select-item{
    font-size: 18px !important;
  }
</style>
