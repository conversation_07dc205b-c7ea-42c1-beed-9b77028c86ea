<template>
  <div id="sign_search" style="display: flex; flex-wrap: nowrap">
    <Select v-model="searchType" @on-change="searchTypeChanged" style="width: 110px; font-size: 12px"
            class="search_type">
      <Option value="0">搜会员</Option>
      <Option value="1">搜姓名</Option>
      <Option value="2">搜电话</Option>
      <Option value="3">搜卡号(模糊)</Option>
      <Option value="4">搜卡号(精准)</Option>
    </Select>
    <Select filterable style="flex: 1" ref="signSelect" class="search_input" clearable :remote="remote"
            :not-found-text="notFoundText" :remote-method="getUsers" v-model="userId" :loading="searching"
            placeholder="姓名/电话/实体卡号" transfer>
      <Option v-for="user in userList" :key="user.user_id" :value="user.user_id"
              :label="`${user.username}(${user.phone})`"></Option>
    </Select>
  </div>
</template>

<script>
  import { isChinese } from '@/utils'
  import { debounce } from 'lodash-es';

  export default {
    name: 'signUserSearch',
    data() {
      return {
        userId: '',
        searchType: '0', // 搜索方式: 姓名/电话/实体卡号 等
        searching: false,
        searchText: '',
        userList: [],
        selectedUser: null,
        isFromOut: false,
        support_fingerprint: '',
        is_print_ticket: '1',
        remote: true,
        notFoundText: '未搜索到该会员'
      };
    },
    props: {
      initial: {
        type: Boolean,
        default: false
      },
      isUserId: {
        type: Boolean,
        default: false
      },
      search: {},
      from: {
        type: [Number, String]
      }
    },
    created() {
      let type;
      /**@augments from: 搜索参数, 2: 普通会员签到搜索, 3: 私教会员搜索 */
      if (this.from == 3) {
        type = localStorage.getItem('ptSearchType');
      } else if (this.from == 2) {
        type = localStorage.getItem('searchType');
      }
      if (!type || type === 'null') {
        if (this.from == 3) {
          localStorage.setItem('ptSearchType', 0);
        } else if (this.from == 2) {
          localStorage.setItem('searchType', 0);
        }
        this.search_type = '0';
      } else {
        this.searchType = type;
      }
    },
    mounted() {
      if (this.search) {
        this.isFromOut = true
        this.getSearchUserList(this.search);
        this.$emit('update:search', '');
      } else {
        this.$refs.signSelect.$refs.reference.focus();
      }
    },
    methods: {
      getUsers: debounce(function (search) {
        this.getSearchUserList(search)
      }, 400),
      // 搜索会员
      getSearchUserList(search) {
        if(this.selectedUser && search === `${this.selectedUser.username}(${this.selectedUser.phone})` || (this.userList && this.userList[0] && search === this.userList[0].user_id)) {
          return false;
        }

        // if numbers or letters must be more than 3 in length you can request
        if (!isChinese(search)) {
          this.notFoundText = '至少输入3位数字'
          return;
        }

        this.notFoundText = '未搜索到该会员'

          search = search.trim();
          if (search === '' || search === this.searchText) return;
          this.searchText = search;
          this.searching = true;
          this.userList = null;
          const url = '/Web/Sign/search_user_info';
          let postData = {
            search: search,
            type: this.searchType,
            is_user_id: this.isUserId ? 1 : 0,
            from: this.from
          };
          return this.$service
            .post(url, postData, { loading: false })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.userList = res.data.data.list;
                this.support_fingerprint = res.data.data.support_fingerprint;
                this.is_print_ticket = res.data.data.is_print_ticket;
                this.searching = false;
                if (this.userList.length === 1) {
                  if (this.isUserId) {
                    this.$emit('isUserId', false);
                  }
                  let user = this.userList[0];
                  this.userId = user.user_id;
                  this.$refs.signSelect.hideMenu();
                } else {
                  if (this.isFromOut) {
                    this.isFromOut = false
                    this.$refs.signSelect.$refs.reference.click();
                    this.$refs.signSelect.$refs.reference.focus();
                  }
                }
              }
            })
            .catch(err => {
              console.error(err);
            });
      },
      searchTypeChanged(type) {
        if (this.from == 3) {
          localStorage.setItem('ptSearchType', type);
        } else if (this.from == 2) {
          localStorage.setItem('searchType', type);
        }
      }
    },
    watch: {
      initial(val) {
        if (val) {
          this.userId = '';
          this.$refs.signSelect.clearSingleSelect();
        }
      },
      userId(val) {
        if (!val) {
          this.userList = null;
          this.$emit('on-change', null);
          return;
        }
        this.selectedUser = this.userList.find(user => user.user_id === val);
        this.selectedUser.support_fingerprint = this.support_fingerprint;
        this.selectedUser.is_print_ticket = this.is_print_ticket;
        this.$emit('on-change', this.selectedUser);
      }
    }
  };
</script>

<style lang="less">
  #sign_search {
    margin-bottom: 10px;

    .ivu-select-selection {
      border-radius: 0;
      outline: 0;
      border-color: #dddee1;
      box-shadow: none;
    }

    .search_type {
      .ivu-select-selection {
        border-right: 0;
      }
    }

    .search_input {
      .ivu-select-selection {
        border-left: 0;
      }
    }
  }
</style>
