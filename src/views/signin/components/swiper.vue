<template>
  <swiper ref="mySwiper" class="swiper-container" id="my-swiper" :options="options">
    <swiper-slide v-for="(item, index) in swiperList" :key="'box-lef-' + index" class="swiper-slide">
      <div>
        <img :src="item.promotional_photo || 'https://imagecdn.rocketbird.cn/test/image/992e8001f62c2e32948dc5e44347184f.png'" />
      </div>
    </swiper-slide>
  </swiper>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

export default {
  name: 'zjh-swiper',
  components: {
    Swiper,
    SwiperSlide,
  },
  props: {
    swiperList: {
      type: Array,
      default() {
        return []
      },
    },
    options: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    optionsWatch() {
      let num = 7
      if (this.options.slidesPerView) {
        num = this.options.slidesPerView
      }
      return num
    },
    mySwiper() {
      return this.$refs.mySwiper.$swiper
    },
  },
  watch: {
    optionsWatch(newV, oldV) {
      this.mySwiper.params.slidesPerView = newV
    },
  },
  mounted() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.swiper-container {
  width: 100%;
  height: 100%;
  .swiper-slide {
    div {
      width: 100px;
      height: 100px;
      overflow: hidden;
      border-radius: 10px;
      box-sizing: border-box;
      border: 2px solid #464d37;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
