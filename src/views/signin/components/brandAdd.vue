<template>
  <div class="">
    <Form class="modal-form add-brand" :label-width="80">
      <!--添加一个隐藏输入框 防止从其它域名跳到当前域名form只一个input时按enter刷新页面-->
      <input id="hiddenText" type="text" style="display:none"/>
      <FormItem v-for="(item, index) in brandId" :key="index" label="手环号">
        <Row>
          <Col span="16">
            <Input type="text" v-model="brandId[index]" @on-enter="handleEnter" ref="brand" placeholder="请输入"
                   autofocus></Input>
            <cardReader @on-change="onReaderChange" style="float: right"></cardReader>
          </Col>
          <Col span="6">
            <span class="add" v-if="!handRFID" @click="handleAdd">添加</span>
            <span class="remove" v-if="index!=0" @click="handleRemove(index)">删除</span>
          </Col>
        </Row>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import cardReader from 'components/card/cardReader.vue';
  export default {
    name: 'brandAdd',
    components: { cardReader },
    data() {
      return {
        brandId: [''],
      };
    },
    props: {
      number: {
        type: Array,
        default: ['']
      },
    },
    computed: {
      handRFID() {
        return this.$store.state.adminInfo.hand_rfid === '1';
      }
    },
    methods: {
      onReaderChange(id) {
        this.brandId = [id];
      },
      handleAdd() {
        if (this.brandId.length >= 5) {
          this.$Message.error('最多添加五个手环号');
          return;
        }
        this.brandId.push('');
        this.focusLast();
      },
      handleRemove(index) {
        this.brandId.splice(index, 1);
        this.focusLast();
      },
      handleEnter() {
        this.$emit('on-enter', this.brandId);
      },
      focusLast() {
        this.$nextTick(() => {
          this.$refs.brand[this.brandId.length - 1] && this.$refs.brand[this.brandId.length - 1].focus();
        });
      }
    },
    mounted() {
      if (event && event.type == 'keydown') {
        setTimeout(() => {
          this.focusLast();
        }, 1000);
      } else {
        this.focusLast();
      }
    },
    watch: {
      brandId(val, oldVal) {
        this.$emit('update:number', val);
      },
      number(val) {
        if (!val || val.length == 0) {
          this.brandId = [''];
        } else {
          this.brandId = val;
        }
      }
    },
  };
</script>

<style lang="less">
  .add-brand {
    .add,
    .remove {
      display: inline-block;
      margin-left: 10px;
      cursor: pointer;
      font-size: 14px;
      color: #3598db;
      &:hover {
        opacity: 0.8;
      }
    }
    .remove {
      color: #ff0000;
    }
  }
</style>
