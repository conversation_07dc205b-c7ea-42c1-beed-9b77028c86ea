<template>
  <div class="sign-card-list">
    <div v-for="(card, index) in cardList"
         :key="card.card_user_id"
         class="card"
         :title="card.mul_coach_str ? `${ card.card_name }(${ card.mul_coach_str })` : `${ card.card_name }`"
         @click="cardSelected(index)"
         :style="{borderColor: calCardColor(card.status)}">
      <p style="padding-bottom: 10px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
        <span v-if="card.status != 1">
          <span v-if="card.status == 2">【请假中】</span>
          <span v-if="card.status == 3">【未激活】</span>
          <span v-if="card.status == 4">【已过期】</span>
        </span>
        {{ card.card_name }}
        <span v-if="card.card_type_id == 4 && card.coach_name">({{ card.coach_name }})</span>
      </p>
      <p v-if="card.card_type_id == 4 || card.card_type_id == 5">课单价{{ card.price }}元</p>
      <p v-if="card.is_pt_time_limit_card != 1 && (card.card_type_id == 2 || card.card_type_id == 4 || card.card_type_id == 5)">剩余{{ card.last_num }}次</p>
      <p v-else-if="card.card_type_id == 1 || card.is_pt_time_limit_card == 1">剩余{{ card.last_day }}天</p>
      <p v-else>剩余{{ card.last_num }}元</p>
      <p v-if="card.end_date == '永久'">永久有效</p>
      <p v-else>截止{{ card.end_date }}</p>
      <div class="circle"
           v-if="card.status != 4 && card.status != 5">
        <Icon v-if="index === chooseCardIndex"
              :color="calCardColor(card.status)"
              size="20"
              type="ios-checkmark-circle"></Icon>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'signCardList',
    data () {
      return {
        chooseCard: '',
        chooseCardIndex: '',
      }
    },
    props: {
      cardList: {
        type: Array,
        default: []
      },
    },
    computed: {
      cardColor () {
        return '#5fb75d'
      },
      initialChoose: {
        get () {
          let a = 0
          for (let i = 0; i < this.cardList.length; i++) {
            if (this.cardList[i].status == 1) {
              a = i
              break
            }
          }

          return  {
            index: this.cardList[a] && this.cardList[a].status != 4 ? a : '',
            card_user_id: this.cardList[a] && this.cardList[a].card_user_id
          }
        },
        set () { }
      }
    },
    created () {
    },
    watch: {
      initialChoose: {
        immediate: true,
        handler(val) {
          if(val.index!== '')
            this.cardSelected(val.index)
          }
        }
    },
    methods: {
      cardSelected (index) {
        if (this.cardList[index].status == 4) {
          return
        }
        this.chooseCardIndex = index
        this.chooseCard = this.cardList[index]
        this.$emit('on-choose', this.chooseCard)
      },
      calCardColor (status) {
        switch (status) {
          case 1: return '#5fb75d'; break;
          case 2:
          case 3: return '#f4a628'; break;
          case 4: return '#eb3341'; break;
          default: return '#5fb75d'
        }
      }
    },
  }
</script>

<style lang="less" scoped>
.sign-card-list {
  display: flex;
  flex-wrap: wrap;
  padding-top: 10px;
  .card {
    width: 30%;
    margin-right: 3%;
    margin-bottom: 20px;
    border: 1px solid;
    /*height: 80px;*/
    box-sizing: border-box;
    padding: 8px;
    cursor: pointer;
    position: relative;

    .circle {
      position: absolute;
      right: 8px;
      bottom: 8px;
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      border: 1px solid #ccc;
    }
  }
}
</style>
