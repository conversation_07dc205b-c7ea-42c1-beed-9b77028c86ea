<template>
  <swiper ref="mySwiperRight" class="swiper-appointment-container" id="my-swiper-right" :options="options">
    <swiper-slide
      v-for="(item, index) in swiperList"
      :key="'box-right' + index"
      :data-id="item.id"
      class="swiper-appointment-slide"
    >
      <div class="appointment-avatar">
        <img :src="item.coach_avatar || 'https://imagecdn.rocketbird.cn/test/image/49d1b096d16581ef18b9844c160c6514.png'" />
      </div>
      <div class="appointment-name text-color">{{ item.coach_name || '-' }}</div>
      <div class="appointment-class title">{{ item.name || '-' }}</div>
      <div class="appointment-time text-color">{{ item.time || '-' }}</div>
      <div class="appointment-member text-color">{{ item.username || '-' }}</div>
      <div class="appointment-status text-color">{{ item.status || '-' }}</div>
      <img v-show="mode === 'dark'" class="appointment-icon" src="~assets/img/screen/icon_one_dark.png" />
      <img v-show="mode === 'light'" class="appointment-icon" src="~assets/img/screen/icon_one_light.png" />
    </swiper-slide>
  </swiper>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

export default {
  name: 'zjh-swiper',
  components: {
    Swiper,
    SwiperSlide,
  },
  props: {
    swiperList: {
      type: Array,
      default() {
        return []
      },
    },
    options: {
      type: Object,
      default() {
        return {}
      },
    },
    mode: {
      type: String,
      default: 'dark',
    },
  },
  data() {
    return {}
  },
  computed: {
    optionsWatch() {
      let num = 5
      if (this.options.slidesPerView) {
        num = this.options.slidesPerView
      }
      return num
    },
    loopWatch() {
      return this.options.loop
    },
    mySwiperRight() {
      return this.$refs.mySwiperRight.$swiper
    },
  },
  watch: {
    optionsWatch(newV, oldV) {
      this.mySwiperRight.params.slidesPerView = newV
    },
    loopWatch(newV, oldV) {
      this.mySwiperRight.params.loop = newV
    },
  },
  mounted() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.swiper-appointment-container {
  width: 100%;
  height: 100%;
  .swiper-appointment-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 18px;
    box-sizing: border-box;
  }
}
</style>
