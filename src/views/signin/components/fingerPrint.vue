<template>
  <div class="enter_finger">
    <Modal :title="title"
           @on-cancel="closeThis"
           :mask-closable="false"
           v-model="showEnterFinger">

      <div class="before_enter"
           v-if="beforeEnter">
        <div class="tips">
          <p>私教课签到需要学员进行指纹确认</p>
          <h3>请先为该学员录入指纹</h3>
        </div>
      </div>

      <div class="connecting"
           v-if="connecting">
        <Spin>
          <Icon type="load-c"
                size='88'
                class="spin-load"></Icon>
          <div>Loading...</div>
        </Spin>
        <div class="connecting_tips"
             style="padding-top: 30px">
          <p>指纹设备连接中</p>
          <p>请确保指纹仪已正常连入电脑</p>
        </div>
      </div>

      <div class="connecting_false"
           v-if="connectingFalse">
        <img src="../../../assets/img/pay_false.png">
        <div v-if="notLinkIn"
             class="connecting_tips"
             style="padding-bottom: 30px; padding-top: 10px">
          <p>未检测到连接设备</p>
          <p>请检查指纹仪是否已正确接入电脑</p>
        </div>
        <div v-else
             class="connecting_tips"
             style="padding-bottom: 30px; padding-top: 10px;">
          <p>指纹仪设备连接失败</p>
          <p>请确保考勤客户端已打开</p>
        </div>
        <Button type="success"
                @click="initWebSocket">重新连接</Button>
      </div>

      <div class="first_enter"
           v-if="entering">
        <div v-if="secondEnter"
             style="color: #d9544f; font-size: 20px; padding-bottom: 20px">已经成功录入指纹1枚，请换手指再录1枚</div>
        <img src="../../../assets/img/fingerprint.png"
             style="margin-bottom: 20px">
        <Progress style="width: 60%; padding-left: 10%; margin-bottom: 30px"
                  :stroke-width="20"
                  :percent="enterPercent">
          <span style="font-size: 16px; color: #d9544f">{{ enterStep }}/3</span>
        </Progress>
        <p>请将手指放在指纹机上进行录入</p>
        <p>同一枚指纹还需要录入
          <span style="font-size: 16px; color: #d9544f">{{ 3 - enterStep }}</span>次</p>
      </div>

      <div class="finger_sign"
           v-if="isSign">
        <img src="../../../assets/img/fingerprint.png">
        <p style="color: #999; font-size: 14px; padding: 30px 0">请将手指放在指纹机上进行确认</p>
      </div>

      <div slot="footer"
           v-if="beforeEnter"
           class="modal-buttons">
        <Button type="success"
                @click="clickEnter">录入指纹</Button>
        <Button 
                @click="closeThis">取消</Button>
      </div>
      <div v-else
           slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  export default {
    name: 'fingerPrint',
    data() {
      return {
        beforeEnter: true,
        connecting: false,
        connectingFalse: false,
        entering: false,
        isSign: false,

        enterPercent: 0,
        secondEnter: false,
        notLinkIn: false,

        socket: null,
        socketUrl: 'ws://127.0.0.1:9919/'
      }
    },
    computed: {
      showEnterFinger: {
        get() {
          if (!this.value) {
            this.modalClosed()
          } else {
            if (this.userInfo.fingerprint_template) {
              this.connecting = true
              this.initWebSocket()
            } else {
              this.beforeEnter = true
            }
          }
          return this.value
        },
        set() {}
      },
      title: {
        get() {
          return this.isSign ? '验证指纹' : '录入指纹'
        },
        set() {}
      },
      enterStep: {
        get() {
          switch (this.enterPercent) {
            case 33:
              return 1
              break
            case 66:
              return 2
              break
            case 100:
              return 3
              break
            default:
              return 0
          }
        },
        set() {}
      }
    },
    props: {
      value: {
        type: Boolean
      },
      userInfo: {},
      signData: {
        type: Object
      },
      signType: {
        default: 'signin'
      }
    },
    watch: {
      beforeEnter(val) {
        val && (this.connecting = this.connectingFalse = this.entering = this.isSign = false)
      },
      connecting(val) {
        val && (this.beforeEnter = this.connectingFalse = this.entering = this.isSign = false)
      },
      connectingFalse(val) {
        val && (this.beforeEnter = this.connecting = this.entering = this.isSign = false)
      },
      entering(val) {
        val && (this.beforeEnter = this.connecting = this.connectingFalse = this.isSign = false)
      },
      isSign(val) {
        val && (this.beforeEnter = this.connecting = this.connectingFalse = this.entering = false)
      }
    },
    methods: {
      initWebSocket() {
        if (!window.WebSocket) {
          this.$Message.error('请使用最新版的Chrome浏览器!')
          return
        }
        if (this.socket && (this.socket.readyState === 1 || this.socket.readyState === 0)) return
        try {
          this.socket = new WebSocket(this.socketUrl)
        } catch (e) {
          this.socketUrl = 'wss://localhost.rocketbird.cn:9919/'
          this.socket = new WebSocket(this.socketUrl)
        }

        this.socket.addEventListener('open', () => {
          this.socketOpened()
        })
        this.socket.addEventListener('message', e => {
          this.socketOnMsg(e.data)
        })
        this.socket.addEventListener('close', e => {})
        this.socket.addEventListener('error', e => {
          if (this.socketUrl !== 'wss://localhost.rocketbird.cn:9919/') {
            this.socketUrl = 'wss://localhost.rocketbird.cn:9919/'
            this.initWebSocket()
          } else {
            this.connecting = false
            setTimeout(() => {
              this.connectingFalse = true
              this.notLinkIn = false
            }, 30)
          }
        })
      },
      socketOpened() {
        let template = this.userInfo.fingerprint_template || []
        let msg = {
          cmd: 1,
          data: {
            template: template
          }
        }
        this.send(msg)
      },
      socketOnMsg(data) {
        if (typeof data === 'string') {
          data = JSON.parse(data)
        }
        switch (data.cmd) {
          case 0:
            if (data.errorcode === 1000) {
              this.connecting = false
              setTimeout(() => {
                this.connectingFalse = this.notLinkIn = true
              }, 30)
              this.socket.close()
            }
            break
          case 1:
            let template = this.userInfo.fingerprint_template
            if (!template) {
              this.connecting = false
              setTimeout(() => {
                this.entering = true
              }, 30)
            } else {
              this.connecting = false
              setTimeout(() => {
                this.isSign = true
              }, 30)
            }
            break
          case 2:
            if (data.errorcode === 0) {
              this.enterPercent = data.data.seq * 33
            } else {
              this.$Message.error(data.errormsg)
            }
            break
          case 3:
            if (data.errorcode === 0) {
              let template = data.data
              let userId = this.userInfo.user_id
              let localTemplate = localStorage.getItem(userId)
              if (!localTemplate) {
                localStorage.setItem(userId, template)
                this.secondEnter = true
                this.enterPercent = 0
                return
              }
              let postTemplate = [localTemplate, template]
              this.postFingerPrint(postTemplate)
            } else {
              this.$Message.error(data.errormsg)
            }
            break
          case 4:
            if (data.errorcode === 0) {
              if (this.signType === 'signin') {
                this.postSignData()
              } else if (this.signType === 'signOut') {
                this.$emit('success')
                this.closeThis()
              }
            } else {
              this.$Message.error(data.errormsg)
            }
            break
          default:
        }
      },
      postSignData() {
        const url = '/Web/Sign/sign_pt'
        let postData = this.signData
        postData.type = 3
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$emit('success', res.data.sign_id,postData.user_id,res.data.ticket_id)
              this.closeThis()
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }

          })
          .catch(err => {
            console.error(err)
          })
      },
      postFingerPrint(template) {
        let user_id = this.userInfo.user_id

        const url = '/Web/Sign/add_fingerprint_template'
        let postData = {
          user_id,
          fingerprint_template: template
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.userInfo.fingerprint_template = template
              let msg = {
                cmd: 7,
                data: {
                  template: template
                }
              }
              this.send(msg)
              localStorage.removeItem(user_id)
              setTimeout(() => {
                this.isSign = true
              }, 30)
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      checkSocket(operation) {
        let sc = this.socket
        if (!sc || sc.readyState !== 1) {
          this.initWebSocket()
        }
      },
      send(msg) {
        if (this.socket.readyState !== 1) return false
        let data = JSON.stringify(msg)
        this.socket.send(data)
      },
      modalClosed() {
        if (this.socket) {
          let msg = {
            cmd: 5,
            errorcode: 0,
            errormsg: '清除成功'
          }
          this.send(msg)
          this.socket.close()
        }
        if (this.userInfo) {
          let localTemplate = localStorage.getItem(this.userInfo.user_id)
          if (localTemplate) {
            this.secondEnter = true
            this.beforeEnter = true
          }
        }
      },
      closeThis() {
        this.$emit('input', false)
      },
      clickEnter() {
        this.checkSocket()
        this.connecting = true
      }
    }
  }
</script>

<style lang="less">
  .flex-column {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .spin-load {
    -webkit-animation: ani-spin 1s linear infinite;
    animation: ani-spin 1s linear infinite;
  }

  @keyframes ani-spin {
    from {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .before_enter {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-top: 20px;

    h3 {
      font-size: 20px;
      color: #d9544f;
      font-weight: normal;
      height: 200px;
      line-height: 200px;
    }
    p {
      font-size: 14px;
      color: #999;
    }
  }

  .connecting_tips {
    font-size: 20px;
    color: #d9544f;
    text-align: center;
  }

  .connecting {
    padding-top: 20px;
  }

  .connecting_false {
    .flex-column;
  }

  .first_enter {
    .flex-column;
    padding-bottom: 50px;
    p {
      font-size: 14px;
      color: #999;
    }
  }

  .finger_sign {
    .flex-column;
    text-align: center;
  }
</style>
