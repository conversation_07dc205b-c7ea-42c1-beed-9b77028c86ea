<template>
  <Modal :mask-closable="false" v-model="isShow" :width="800">
    <div>
      <div style="border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;">
        <Checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            @click.prevent.native="handleCheckAll">全选</Checkbox>
    </div>
    <div class="sellgroup-wrap">
      <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange" :style="styles.chooseReserved">
        <Checkbox v-for="(item, index) in list" :label="index" :key="index" :style="styles.reservedRadio">
          <p :title="item.username" style="margin-left: 10px;width:80px;overflow:hidden;whiteSpace:noWrap;textOverflow:ellipsis">{{ item.username }}</p>
          <div>
            <div :title="item.card_name" style="width:140px;overflow:hidden;whiteSpace:noWrap;textOverflow:ellipsis">{{ item.card_name }}</div> 
            <div>{{ item.begin_time }}</div>
            <div>
               <Icon type="md-person"></Icon>
            <span :title="item.coach_name" style="width:120px;overflow:hidden;whiteSpace:noWrap;textOverflow:ellipsis">{{ item.coach_name }}</span>
            </div>
          </div>
          <p style="margin-left: 20px;text-align:right;width:60px;color:#777" @click.stop.prevent="handleCancel(item)">
           取消已上
          </p>
        </Checkbox>
      </CheckboxGroup>
    </div>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button v-if="hasAuth" type="success" @click="confirmSign">确认签到</Button>
      <Button @click="isShow = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'NotSellClass',
    props: {
      value: {
        type: Boolean
      }
    },
    components: {
    },
    data() {
      return {
        styles: {
          chooseReserved: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            paddingTop: '5px'
          },
          reservedRadio: {
            height: '64px',
            width: '48%',
            border: '1px solid #19be6b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 10px',
            marginBottom: '10px'
          }
        },
        indeterminate: true,
        checkAll: false,
        hasAuth: false,
        checkAllGroup: [],
        checkedData: [],
        list: []
      };
    },
    computed: {
      isShow: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      isShow(val) {
        if (!val) {
        } else {
          this.getList();
        }
      }
    },
    created() {
    },
    methods: {
      handleCancel(info) {
        this.$Modal.confirm({
          title: '提示',
          content: '取消已上状态后，课程会回到之前已预约的状态，是否确认取消？',
          onOk: () => {
            this.$service.post('/Web/Sign/cancel_attendclass',{
              pt_id: info.pt_id,
              from_ivep: info.from_ivep,
              attend_id: info.attend_id,
              service_type: info.service_type,
            }).then(res => {
              if (res.data.errorcode == 0) {
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          }
        })
      },
      handleCheckAll () {
          if (this.indeterminate) {
              this.checkAll = false;
          } else {
              this.checkAll = !this.checkAll;
          }
          this.indeterminate = false;

          if (this.checkAll) {
              this.checkAllGroup = [...(new Array(this.list.length)).keys()];
              this.checkedData = this.list
          } else {
              this.checkAllGroup = [];
              this.checkedData = [];
          }
      },
      checkAllGroupChange (data) {
        if (data.length === this.list.length) {
          this.indeterminate = false;
          this.checkAll = true;
        } else if (data.length > 0) {
          this.indeterminate = true;
          this.checkAll = false;
        } else {
          this.indeterminate = false;
          this.checkAll = false;
        }
        this.checkedData = []
        this.list.forEach((item, index)=>{
          if(data.indexOf(index) !== -1) {
            this.checkedData.push(item)
          }
        })
      },
      confirmSign() {
        if(!this.checkedData || this.checkedData.length === 0) {
          this.$Message.error('请先选择！');
        }
        this.$service.post('/Web/Sign/sign_pt_batch',{sign_data: this.checkedData}).then(res => {
          if (res.data.errorcode == 0) {
            this.isShow = false;
            this.$emit('on-confirm', this.checkedData)
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getList() {
        this.$service.get('/Web/Sign/getNotSellList').then(res => {
          if (res.data.errorcode == 0) {
            this.list = res.data.data;
            this.hasAuth = res.data.auth === 1;
            this.checkAllGroup = [];
            this.checkedData = [];
          } else {
            this.list = [];
            this.$Message.error(res.data.errormsg);
          }
        });
      }
    }
  };
</script>

<style scoped>
.sellgroup-wrap {
  max-height: 500px;
  overflow-y: scroll;
}
</style>
