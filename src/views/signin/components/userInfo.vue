<template>
  <div class="user_info"
       v-if="data">
    <div class="avatar">
      <img :src="data.avatar">
    </div>
    <ul class="info_box">
      <li>
        <div>姓名</div>
        <span>
          {{ data.username || data.nickname }}
          <img v-if="data.bind_wx == 1" style="height: 12px"
               src="../../../assets/img/wx.png">
        </span>
      </li>
      <li>
        <div>年龄</div>
        <span>{{ data.age }}</span>
      </li>
      <li>
        <div>性别</div>
        <span>{{ data.sex == 2 || data.sex == '女' ? '女' : '男' }}</span>
      </li>
      <li>
        <div>跟进会籍</div>
        <span>{{ data.sale_name }}</span>
      </li>
      <li>
        <div>手机号</div>
        <span>{{ data.phone }}</span>
      </li>
      <li>
        <div>生日</div>
        <span>{{ data.birthday }}</span>
      </li>
      <li>
        <div>备注</div>
        <span>{{ data.remark }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
  export default {
    name: 'userInfo',
    data () {
      return {

      }
    },
    props: {
      data: {
        type: Object,
        default: null
      },
    },
  }
</script>

<style lang="less" scoped>
  .user_info {
    margin-top: 10px;
    height: 200px;
    display: flex;
    justify-content: space-between;
    .avatar {
      height: 100%;
      width: 200px;
      border: 1px solid #ddd;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .info_box {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;
      padding: 5px 10px;
      border: 1px solid #dddddd;
      overflow: hidden;
      li {
        width: 50%;
        box-sizing: border-box;
        padding-left: 10px;
        padding-bottom: 2px;
        display: flex;
        &:last-child{
          width: 100%;
        }
        div {
          width: 70px;
        }
        span{
          flex: 1;
        }
      }
    }
  }
</style>
