<style lang="less">
  .sign_modal {
    .ivu-modal-body {
      padding-bottom: 0;
      min-height: 300px;
    }
  }

  .ivu-modal-footer {
    padding-top: 0;
  }

  .sign-buttons {
    display: flex;
    justify-content: center;
  }

  .sign_confirm {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .text {
      font-size: 16px;
      padding-top: 30px;
    }
  }

  .print_sign {
    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
      color: #19be6b;
      font-size: 20px;
      display: flex;
      align-items: center;

      h3 {
        padding-left: 5px;
        font-weight: normal;
      }
    }

    .print_table {
      border: 1px solid #ccc;
      margin: 20px 0;
      text-align: center;
      border-collapse: collapse;
      width: 80%;
      height: 300px;

      tr {
        td:nth-child(even) {
          font-weight: bold;
        }
      }
    }
  }

  .pt_sign {
    .ivu-date-picker {
      width: 240px;
    }

    .ivu-modal {
      padding-bottom: 100px;
    }

    .sign_modal {
      .coach_select {
        .ivu-select-dropdown {
          top: 30px !important;
        }
      }
    }

    .ivu-poptip-footer {
      .ivu-btn {
        font-size: 12px;
        min-width: 0;
      }
    }

    header {
      .user-search {
        width: 160px;
      }
    }

    footer {
      justify-content: space-between;
    }
  }

  .sign-out {
    font-size: 16px;
    padding: 0 90px 10px;
    line-height: 2;

    p {
      display: flex;
      justify-content: space-between;
    }
  }
  .fr-wrap {
    flex: 1;
    text-align: right;
    max-width: none !important;
    .fr-num {
      position: relative;
      color: #d9534f;
    }
    .fr-link {
      color: #2D8cF0;
      cursor: pointer;
    }
  }
</style>

<template>
  <div class="table-wrap pt_sign">
    <header>
      <Input class="user-search" placeholder="姓名/电话/实体卡号" v-model="postData.search" @on-enter="doSearch"></Input>
      <Select style="width: 150px" v-model="postData.class_id" placeholder="课程名称" filterable clearable>
        <Option value="">所有课程</Option>
        <Option style="overflow-x: hidden" v-for="course in allCourses" :key="course.id" :value="course.id"
                :label="course.name">
        </Option>
      </Select>
      <Date-picker placeholder="选择查询时间段" @on-change="dateChanged" :value="dateRange" type="daterange" :editable="false"
                   :clearable="false" format="yyyy-MM-dd"></Date-picker>
      <Button type="success" @click="doSearch">搜索</Button>
      <div class="fr-wrap">
        共计 <span class="fr-num">{{notSellCount}}</span> 节课时，上课后未消课 <span v-show="notSellCount && notSellCount!=='?'" class="fr-link" @click="showDetail">查看详情</span>
      </div>
    </header>
    <main>
      <Table :columns="columns" :data="tableData" ref="table" class="avatar-zoom" :row-class-name="rowClassName" stripe
             disabled-hover></Table>
    </main>
    <footer>
      <div style="display: flex">
        <Button type="success" style="margin-right: 30px" @click="showSignModal = true">签到</Button>
        <Dropdown @on-click="otherCase" placement="top">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">导出excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Pager :postData="postData" :total="totalCount" @on-change="pageChange"></Pager>
    </footer>
    <Modal title="签到确认" width="600" :transfer="false" class="sign_modal" :mask-closable="false" v-model="showSignModal">
      <UserSearchNew :initial="showSignModal" :from="3" v-if="showSignModal" :search="$route.query.userId" :isUserId="isUserId" @isUserId="changeIsUserId" @on-change="signUserChanged"></UserSearchNew>
      <div style="min-height: 200px; overflow-y: scroll" v-if="userInfo">
        <RemindMessage :userId="userInfo.user_id"></RemindMessage>
        <UserInfo :data="userInfo"></UserInfo>
        <div style="margin-top: 10px">
          <p :style="styles.noCardBox" v-if="!ptCardList || !ptCardList.length">
            该会员暂未购私教课
          </p>
          <div v-else style="padding-bottom: 0px">
            <Form v-if="userInfo.pt_list && userInfo.pt_list.length || (userInfo.not_sign_arr && userInfo.not_sign_arr.length)">
              <FormItem label="上课方式" style="margin-bottom: 0; display: flex; align-items: center">
                <RadioGroup v-model="hasReservation">
                  <Radio v-show="userInfo.not_sign_arr && userInfo.not_sign_arr.length" :label="0">已上课未消课(补签)</Radio>
                  <Radio v-show="userInfo.pt_list && userInfo.pt_list.length" :label="1">正常预约上课</Radio>
                  <Radio :label="2">未预约临时上课</Radio>
                </RadioGroup>
              </FormItem>
            </Form>
            <div v-if="userInfo.not_sign_arr && hasReservation === 0" style="padding-top: 10px">
              <div>已完成课程</div>
              <RadioGroup v-model="chooseUnsigned" :style="styles.chooseReserved">
                <Radio :label="reserved.pt_id" v-for="reserved in userInfo.not_sign_arr" :key="reserved.pt_id"
                       :style="styles.reservedRadio">
                  <p>{{ reserved.begin_time }}</p>
                  <p :style="styles.radioPtName" :title="reserved.card_name">{{ reserved.card_name }}</p>
                  <p style="margin-left: 20px">
                    <Icon type="md-person"></Icon>
                    <span>{{ reserved.coach_name }}</span>
                  </p>
                </Radio>
              </RadioGroup>
            </div>
            <div v-else-if="userInfo.pt_list && hasReservation === 1" style="padding-top: 10px">
              <div>已预约课程</div>
              <RadioGroup v-model="chooseReserved" :style="styles.chooseReserved">
                <Radio :label="reserved.pt_schedule_id" v-for="reserved in userInfo.pt_list"
                       :key="reserved.pt_schedule_id" :style="styles.reservedRadio">
                  <p>{{ reserved.begin_time }}</p>
                  <p :style="styles.radioPtName" :title="reserved.card_name">{{ reserved.card_name }}</p>
                  <p style="margin-left: 20px">
                    <Icon type="md-person"></Icon>
                    <span>{{ reserved.coach_name }}</span>
                  </p>
                </Radio>
              </RadioGroup>
            </div>
            <div v-else>
              <SignCardList @on-choose="ptCardChoose" :cardList="ptCardList2"></SignCardList>
            </div>
          </div>
        </div>
      </div>
      <Form v-if="userInfo && hasReservation == 2 && chooseCard.teamclass_status !== '1'" label-position="left" style="padding-top: 20px" :rules="rules" :model='modal'  ref='deduct' :label-width="80">
        <FormItem label="上课教练" style="margin-bottom: 10px">
          <Select v-model="ptClassCoach" v-if="coachList" style="width: 460px" class="coach_select" filterable>
            <Option v-for="coach in coachList" :key="coach.coach_id" :value="coach.coach_id" :label="coach.coach_name" v-show="coach.is_swim ? chooseCard.card_type_id === '5' : chooseCard.card_type_id === '4'"></Option>
          </Select>
        </FormItem>
        <FormItem v-if="chooseCard.is_pt_time_limit_card != 1" label="扣除次数" prop="signCount" aria-valuenow="">
          <Input v-model="modal.signCount" style="width: 460px" />
        </FormItem>
      </Form>
      <Form v-if="userInfo && hasReservation == 2 && chooseCard.teamclass_status === '1'" label-position="left" style="padding-top: 20px" :label-width="80">
        <FormItem label="上课日期" style="margin-bottom: 10px">
          <DatePicker v-model="teamClassDate" style="width: 460px" @on-change="teamClassDateChange" :options="disableDayAfter" :editable="false" :clearable="false"></DatePicker>
        </FormItem>
        <div v-if="teamClassList && teamClassList.length" style="padding-top: 10px">
          <div>班级课程</div>
          <RadioGroup v-model="teamclassScheduleId" :style="styles.chooseReserved">
            <Radio :label="item.teamclass_schedule_id" v-for="item in teamClassList" :key="item.teamclass_schedule_id" :style="styles.reservedRadio">
              <p>{{ item.s_time }}</p>
              <p :style="styles.radioPtName" :title="item.teamclass_name">{{ item.teamclass_name }}</p>
              <p style="margin-left: 5px">
                <Icon type="md-person"></Icon>
                <span>{{ item.coach_name }}</span>
              </p>
            </Radio>
          </RadioGroup>
        </div>
      </Form>
      <div slot="footer" class="modal-buttons" style="padding-bottom: 20px; padding-top: 20px" v-if="userInfo">
        <Button v-if="userInfo.support_fingerprint != 0" :disabled="hasReservation == 2 && chooseCard.teamclass_status === '1' && !teamclassScheduleId" @click="newFingerPrintSign" type="success">{{ isDisabledCard ?
          '启用并签到' : '指纹签到' }}
        </Button>
        <Button :type="userInfo.support_fingerprint == 0 ? 'success' : 'default'" style="margin: 0 30px"
                v-if="userInfo.support_fingerprint == 0 || showNormalSign == 1" :disabled="hasReservation == 2 && chooseCard.teamclass_status === '1' && !teamclassScheduleId" @click="newNormalSign">{{ isDisabledCard ?
          '启用并签到' : '确认签到' }}
        </Button>
        <Button :style="{marginLeft: showNormalSign == 1 ? '0' : '30px'}" @click="showSignModal = false">
          取消
        </Button>
      </div>
      <div v-else slot="footer"></div>
    </Modal>
    <FingerPrint v-model="showFinger" :signData="signData" :signType="signType" @success="signSuccess"
                 :userInfo="userInfo">
    </FingerPrint>
    <Modal title="指纹确认" @on-cancel="cancelSignConfirm" v-model="showShiJinShi">
      <div class="sign_confirm">
        <img src="../../assets/img/fingerprint.png" alt="">
        <div class="text">等待教练和学员指静脉确认...</div>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal title="签退确认" v-model="showSignOut">
      <div class="sign-out" v-if="signOutInfo">
        <p>
          <span>课程名称</span>
          <span>{{signOutInfo.card_name}}</span>
        </p>
        <p>
          <span>教练</span>
          <span>{{signOutInfo.coach_name}}</span>
        </p>
        <p>
          <span>会员</span>
          <span>{{signOutInfo.user_name}}</span>
        </p>
        <p>
          <span>开始时间</span>
          <span>{{signOutInfo.create_time}}</span>
        </p>
      </div>
      <div slot="footer" class="modal-buttons" style="padding-bottom: 20px; padding-top: 10px" v-if="signOutInfo">
        <Button v-if="signOutInfo.support_fingerprint == 1" @click="fingerPrintSignOut" type="success">指纹签退</Button>
        <Button
          :type="signOutInfo.support_fingerprint == 0 || signOutInfo.support_fingerprint == 2 ? 'success' : ''"
          style="margin: 0 30px"
          v-if="signOutInfo.support_fingerprint == 0 || signOutInfo.support_fingerprint == 2 || showNormalSignOut == 1"
          @click="confirmSignOut">确认签退
        </Button>
        <Button :style="{marginLeft: showNormalSignOut == 1 ? '0' : '30px'}" @click="showSignOut = false">
          取消
        </Button>
      </div>
    </Modal>

    <Modal v-model="showPrint" title="信息核对">
      <div class="print_sign" v-if="signInfo">
        <div class="header">
          <Icon type="ios-checkmark-circle-outline" size="32"></Icon>
          <h3>签到成功!</h3>
        </div>
        <table class="print_table" border="1">
          <tr>
            <td>会员</td>
            <td>{{ signInfo.username }}</td>
            <td>电话号码</td>
            <td>{{ signInfo.phone }}</td>
          </tr>
          <tr>
            <td style="font-weight: bold" colspan="4">会员卡信息</td>
          </tr>
          <tr>
            <td>课程</td>
            <td colspan="3">{{ signInfo.card_name }}</td>
          </tr>
          <tr>
            <td>总计</td>
            <td>{{ signInfo.is_pt_time_limit_card!=1?signInfo.all_num+'次':signInfo.all_days+'天' }}</td>
            <td>剩余{{ signInfo.is_pt_time_limit_card!=1?'次':'天' }}数</td>
            <td>{{ signInfo.last_num }}{{ signInfo.is_pt_time_limit_card!=1?'次':'天' }}</td>
          </tr>
          <tr>
            <td>使用时间</td>
            <td colspan="3">{{ signInfo.date }}</td>
          </tr>
          <tr>
            <td style="font-weight: bold" colspan="4">本次消费后</td>
          </tr>
          <tr>
            <td>本次扣费</td>
            <td>{{ signInfo.sign_number }}次</td>
            <td>消费后剩余</td>
            <td>{{ signInfo.sign_last_num }}{{ signInfo.is_pt_time_limit_card!=1?'次':'天' }}</td>
          </tr>
          <tr>
            <td>教练</td>
            <td colspan="3">{{ signInfo.sign_coach_name }}</td>
          </tr>
        </table>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success">
          <router-link style="color: #fff" target="_blank" :to="toPath">打印小票</router-link>
        </Button>
        <Button @click="showPrint = false">取消</Button>
      </div>
    </Modal>
    <FaceSignPhoto v-model="showPhotoModel" :data="curPhotoInfo" />
    <NotSellClass v-model="showDetailModal" @on-confirm="getSignList" />
  </div>
</template>

<script>
  import { formatDate, ten } from 'utils';
  import { getNewHost } from 'utils/config';
  import RemindMessage from 'src/components/user/remindMessage.vue';
  import UserSearchNew from 'src/components/form/UserSearchNew.vue';
  import UserInfo from './components/userInfo.vue';
  import SignCardList from './components/signCardList.vue';
  import FingerPrint from './components/fingerPrint.vue';
  import NotSellClass from './components/NotSellClass.vue';
  import Pager from 'components/pager';
  import receipt from 'mixins/receipt.js';
  import FaceSignPhoto from 'src/views/member/components/FaceSignPhoto.vue'
  export default {
    name: 'ptSign',
    mixins: [receipt],
    data() {
      return {
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        rules: {
            signCount: [
                {required: true, message: '请输入消课次数', trigger: 'change'},
                {type: 'string', pattern: /^[0-9]\d*$/, message: '请输入大于等于0的整数', trigger: 'change'},
            ]
        },
        postData: {
          search: '',
          class_id: '',
          s_date: formatDate(new Date(), 'yyyy-MM-dd'),
          e_date: formatDate(new Date(), 'yyyy-MM-dd'),
          page_no: 1,
          page_size: 10
        },
        showPhotoModel: false,
        isUserId: false,
        allCourses: [],
        teamClassDate: formatDate(new Date(), 'yyyy-MM-dd'),
        teamClassList: [],
        teamclassScheduleId: '',
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')], // 列表数据时间段 默认今天
        chooseReserved: '0', // 选择预约的课程
        chooseUnsigned: '',
        showNormalSign: 0,
        showNormalSignOut: 0,
        signUserId: '',
        userInfo: null, // 搜索后选中的用户信息
        signOutInfo: null,
        ptCardList: null, // 未预约时的私教卡列表
        ptCardList2: null,
        coachList: null, // 上课教练列表
        userDetailUrl: `/v2/member/detail/`,
        // signPrintUrl: `${getNewHost()}/#/signInfoPrint/`,

        showSignModal: false, // 显示签到弹窗
        showFinger: false, // 显示指纹录入窗口
        isDisabledCard: false,
        showSignOut: false,
        signOutId: '',

        hasReservation: 2, // 是否有预约 1.预约上课 2.临时上课
        chooseCard: {}, // 选中的私教卡
        ptClassCoach: '', // 选中的上课教练
        modal: {
            signCount: 1
        }, // 扣除次数
        signType: '',

        // 释金石签到
        continueSign: true,
        signConfirmTimer: null,
        showShiJinShi: false,

        sign_log_id: '', // 签到成功 id
        signInfo: null,
        curPhotoInfo: null,

        totalCount: 0,
        sizer: 10,
        currentPage: 1,
        hasExportAuth: true,

        styles: {
          noCardBox: {
            minHeight: '100px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '14px',
            fontWeight: 'bold',
            border: '1px solid #ddd',
            color: '#19be6b'
          },
          chooseReserved: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            paddingTop: '5px'
          },
          reservedRadio: {
            height: '50px',
            width: '48%',
            border: '1px solid #19be6b',
            display: 'flex',
            alignItems: 'center',
            padding: '0 10px',
            marginBottom: '10px'
          },
          radioPtName: {
            width: '45%',
            marginLeft: '10px',
            whiteSpace: 'nowrap',
            width: '45%',
            height: '22px',
            lineHeight: '22px',
            textOverflow: 'ellipsis',
            overflow: 'hidden'
          }
        },

        columns: [
          {
            title: '头像',
            key: 'avatar',
            className: 'avatar-wrap',
            render: (h, params) => {
              return (
                <a>
                  <img src={params.row.avatar} class="avatar"/>
                </a>
              );
            }
          },
          {
            title: '姓名',
            key: 'user',
            render: (h, params) => {
              if (!params.row.user) return '';
              return (
                <a class="link" href={`${this.userDetailUrl}${params.row.user.user_id}`}>
                  {params.row.user.username}
                </a>
              );
            }
          },
          {
            title: '课程名称',
            key: 'card_name'
          },
          {
            title: '上课教练',
            key: 'coach_name'
          },
          {
            title: '扣除次数',
            key: 'sign_number',
            render: (h, params) => {
              const { sign_number, pt_status } = params.row.sign_number;
              if (!!sign_number && params.row.is_miss == 1) {
                return (
                  <div>
                    {sign_number}
                    <span style="color:#e60012;">(爽约)</span>
                  </div>
                );
              } else {
                return <div>{sign_number}</div>;
              }
              // const { sign_number, pt_status } = params.row.sign_number
              // if (pt_status == 3) {
              //   return (
              //     <div>
              //       {sign_number}
              //       <span style="color: red">(爽约)</span>
              //     </div>
              //   )
              // } else {
              //   return sign_number
              // }
            }
          },
          {
            title: '签到时间',
            key: 'create_date'
          },
          {
            title: '签退时间',
            key: 'end_date'
          },
          {
            title: '课程时长',
            key: 'duration'
          },
          {
            title: '确认方式',
            key: 'type',
            renderHeader: (h, params) => {
              return (
                <div className="name">
                  确认方式
                  <tooltip placement="bottom">
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      <p>签到方式/签退方式</p>
                      <p>指纹：由会员本人通过指纹确认进行消课</p>
                      <p>前台：由工作人员在系统上进行签到消课</p>
                      <p>系统：预约后未进行签到由系统代签</p>
                    </div>

                  </tooltip>
                </div>
              );
            },
            render: (h, params) => {
              const { type, end_type } = params.row.type;
              return (
                <div>
                  <span title="签到方式">{type}</span>{end_type?'/':''}{end_type?(<span title="签退方式">{end_type}</span>):''}
                  {params.row.sign_in_img || params.row.sign_out_img?<span style="cursor: pointer" class="icon-faceimg" title="签到照片" onClick={()=>{
                    this.curPhotoInfo = params.row
                    this.showPhotoModel = true
                  }}></span>:''}
                </div>
              );

            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              return (
                <div style="display: flex; justify-content: space-around">
                  <i-button
                    ghost
                    type="text"
                    onClick={() => {
                      this.clickSignOut(params.row.operation.id);
                    }}
                    disabled={params.row.operation.signOuted || params.row.operation.disabled}>
                    签退
                  </i-button>
                  <i-button
                    type="text"
                    onClick={() => this.cancelSign(params.row.operation.id)}
                    disabled={params.row.operation.disabled}>
                    取消签到
                  </i-button>
                </div>
              );
            }
          }
        ],
        tableData: [],
        notSellCount: '?',
        showDetailModal: false
      };
    },
    components: {
      UserSearchNew,
      FaceSignPhoto,
      UserInfo,
      RemindMessage,
      SignCardList,
      FingerPrint,
      NotSellClass,
      Pager
    },
    watch: {
      showSignModal(val) {
        if (!val) {
          this.initial();
        }
      },
      chooseCard(info) {
        if(info.teamclass_status === '1') {
          this.teamClassDate = formatDate(this.teamClassDate ? new Date(this.teamClassDate) : new Date(), 'yyyy-MM-dd')
          this.getTeamClassList()
        } else {
          this.teamClassList = []
          this.teamclassScheduleId = ''
        }

      }
    },
    computed: {
      signData: {
        get() {
          if (!this.userInfo) return {};

          const { user_id } = this.userInfo;
          if (this.hasReservation === 1) {
            if (!this.chooseReserved) return {};
            let data = this.userInfo.pt_list.find(item => item.pt_schedule_id === this.chooseReserved);
            if (!data) return {};

            const { card_user_id, pt_schedule_id, coach_id, coach_name, card_name } = data;
            return {
              user_id,
              card_user_id,
              pt_schedule_id,
              coach_id,
              coach_name,
              card_name,
              sign_number: 1
            };
          } else if (this.hasReservation === 0) {
            if (!this.chooseUnsigned) return {};
            let data = this.userInfo.not_sign_arr.find(item => item.pt_id === this.chooseUnsigned);
            if (!data) return {};

            const { card_user_id, pt_id: pt_schedule_id, coach_id, coach_name, card_name, from_ivep, mark_id_type, mark_id, teamclass_sign_id, suspend_from } = data;
            return {
              mark_id_type,
              mark_id,
              from_ivep,
              user_id,
              card_user_id,
              pt_schedule_id,
              coach_id,
              coach_name,
              card_name,
              suspend_from,
              teamclass_sign_id: teamclass_sign_id || '',
              sign_number: 1
            };
          } else {
            if (!this.coachList) return {};
            const { card_user_id, card_name } = this.chooseCard;
            let coach = this.coachList.filter(item => {
              return item.coach_id === this.ptClassCoach;
            });
            return {
              user_id,
              card_user_id,
              coach_id: this.ptClassCoach,
              coach_name: coach[0] ? coach[0].coach_name : '',
              card_name,
              sign_number: this.chooseCard.is_pt_time_limit_card != 1 ? this.modal.signCount : 1
            };
          }
        },
        set() {
        }
      }
      // printUrl: {
      //   get() {
      //     if (!this.sign_log_id) return '';
      //     return this.signPrintUrl + this.sign_log_id;
      //   },
      //   set() {}
      // }
    },
    created() {
      window.addEventListener('storage', this.eventFun);
      this.getNormalSign();
      // this.getExcelAuth(); 拿到的数据并没有使用？所以暂时先隐藏了。并且该get请求是查全表，如果后期要放出来，接口需要改一下
      this.getAllCourses();
    },
    mounted() {
      this.setDisabledTitle();
    },
    activated() {
      if (this.$route.query.userId) {
        this.isUserId = true;
        this.showSignModal = true;
      }
      this.getNotSellCount()
    },
    deactivated() {
      this.showSignModal = false;
    },
    beforeDestroy() {
      window.removeEventListener('storage', this.eventFun);
    },
    methods: {
      showDetail() {
        this.showDetailModal=true
      },
      teamClassDateChange(val) {
        this.teamClassDate = formatDate(new Date(val), 'yyyy-MM-dd')
        this.getTeamClassList()
      },
      getTeamClassList() {
        this.$service
          .post('Web/TeamclassSwim/teamclass_schedule_sign_list', {
            sign_date: this.teamClassDate,
            card_id: this.chooseCard.card_id,
            user_id: this.userInfo.user_id,
            teamclass_type: this.chooseCard.teamclass_type
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.teamClassList = res.data.data;
              if(this.teamClassList.length > 0) {
                if(this.teamClassList.length === 1) {
                  this.teamclassScheduleId = this.teamClassList[0].teamclass_schedule_id
                } else {
                  for (const item of this.teamClassList) {
                    if(item.schedule_default === 1) {
                      this.teamclassScheduleId = item.teamclass_schedule_id
                      break;
                    }
                  }
                }

              } else {
                this.teamclassScheduleId = ''
              }
            } else {
              this.teamClassList = [];
              this.teamclassScheduleId = ''
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      eventFun(event) {
        if (event.key == 'receipt') {
          localStorage.removeItem('receipt');
          this.showPrint = false;
        }
      },
      fingerPrintSign() {
        if (!this.checkSignData()) return;
        try {
          // await this.checkCardStatus();
          if (this.userInfo.support_fingerprint === '1') {
            this.signType = 'signin';
            this.showFinger = true;
          } else if (this.userInfo.support_fingerprint === '2') {
            this.normalSign('isShiJinShi');
          } else {
            console.error('这个功能还没有开发, 指纹签到状态码', this.userInfo.support_fingerprint);
          }
        } catch (error) {
          this.$Message.error(error.message);
        }
      },
      async newFingerPrintSign() {
        if (!this.checkSignData()) return;
        if (this.userInfo.suspend_status == 0) {
          if(this.chooseCard.status == 3 || this.chooseCard.status == 2) {
            await this.enableCard(this.chooseCard.card_user_id);
            this.fingerPrintSign();
          } else {
            this.fingerPrintSign();
          }
        }
        if(this.userInfo.suspend_status == 1) {
          if((this.chooseCard.status == 2 || this.chooseCard.status == 3) && this.chooseCard.suspend_from == 2) {
            this.$Modal.confirm({
              title: "此操作将启用全部请假状态的会员卡，是否启用？",
              loading: true,
              onOk: () => {
                this.activateAllCards();
                this.$Modal.remove();
                this.fingerPrintSign();
              }
            })
          } else if(this.chooseCard.status == 2 || this.chooseCard.status == 3 && this.chooseCard.suspend_from != 2) {
            await this.enableCard(this.chooseCard.card_user_id);
            this.fingerPrintSign();
          }
        }
      },
      fingerPrintSignOut() {
        if (this.signOutInfo.support_fingerprint === '1') {
          this.signType = 'signOut';
          this.showSignOut = false;
          this.showFinger = true;
          this.userInfo = this.signOutInfo;
        } else if (this.signOutInfo.support_fingerprint === '2') {
          this.confirmSignOut('isShiJinShi');
        }
      },
      changeIsUserId() {
        this.isUserId = false;
      },
      getExcelAuth() {
        const url = '/Web/Sign/pt_sign_excel';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 40014) {
              this.hasExportAuth = false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      initial() {
        this.userInfo = null;
        this.hasReservation = 2;
        this.chooseReserved = '';
        this.chooseCard = {};
        this.signData = null;
        this.modal.signCount = 1;
        this.ptClassCoach = '';
      },
      checkCardStatus() {
        if (this.chooseCard.status == 3 || this.chooseCard.status == 2) {
          return this.enableCard(this.chooseCard.card_user_id);
        }
      },

      async newNormalSign() {
        if (!this.checkSignData()) return;
        // 已上未消  会员请假
        if(this.signData.suspend_from == 1) {
          this.memberSuspendModal()
          return false;
        }
         // 已上未消  卡请假
        if(this.signData.suspend_from == 2) {
          this.cardSuspendModal()
          return false;
        }
        // 会员请假中和不在请假中
        if (this.userInfo.suspend_status === '0') {
          if(this.chooseCard.status == 3 || this.chooseCard.status == 2) {
            await this.enableCard(this.chooseCard.card_user_id);
            await this.normalSign('');
          } else {
            this.normalSign('');
          }
          return false;
        }
        if(this.userInfo.suspend_status === '1') {
          if(this.chooseCard.status == 3) {
            this.memberSuspendModal()
            return false;
          }
          if(this.chooseCard.status == 2) {
            this.cardSuspendModal()
            return false;
          }
          await this.enableCard(this.chooseCard.card_user_id);
          await this.normalSign('');
        }
      },
      memberSuspendModal(){
        this.$Modal.confirm({
          title: "自动激活",
          content: "会员卡处于未激活，签到将自动启用该卡，同时会员将自动结束请假！",
          loading: true,
          onOk: async () => {
            this.normalSign('')
            this.$Modal.remove();
          }
        })
      },
      cardSuspendModal(){
        this.$Modal.confirm({
          title: "自动激活",
          content: "此操作将启用全部请假状态的会员卡，是否启用？",
          loading: true,
          onOk: () => {
            this.normalSign('')
            this.$Modal.remove();
          }
        })
      },
      activateAllCards() {
        let user_id = this.userInfo.user_id;
        let url = '/Web/Member/user_suspend';
        this.$service.post(url, {
          user_id,
          action: 2
        }).then(res => {
          if(res.data.errorcode != 0) {
            throw new Error(res.data.errormsg);
          }
        }).catch(err => {
          console.error(err)
        })
      },
      enableCard(card_user_id) {
        const url = '/Web/MemberCard/enableCard';
        let postData = {
          user_id: this.userInfo.user_id,
          card_user_id
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode !== 0) {
              throw new Error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async ptCardChoose(info) {
        this.chooseCard = info;

        if (!Array.isArray(this.coachList)) {
          await this.getPtCoachList();
        }

        // 选中本场馆教练，其他场馆的置空不选。
        const co = this.coachList.filter(item => item.coach_id == this.chooseCard.coach_id);
        if (co.length == 1) {
          this.ptClassCoach = this.chooseCard.coach_id;
        } else {
          this.ptClassCoach = '';
        }
        // zj end

        // this.ptClassCoach = this.chooseCard.coach_id
        this.isDisabledCard = this.chooseCard.status == 2 || this.chooseCard.status == 3;
      },
      getAllCourses() {
        const url = '/Web/PtSchedule/pt_user_all_card';
        this.$service
          .post(url, {type: 0})
          .then(res => {
            if (res.data.errorcode === 0) {
              this.allCourses = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // 设置取消签到 tr 的'已取消' title
      setDisabledTitle() {
        setTimeout(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled');
          let disabledTrArr = Array.prototype.slice.call(disabledTr);
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已取消');
          });
        }, 500);
      },
      pageChange(postData) {
        const { s_date, e_date } = postData;
        this.dateRange = [s_date, e_date];
        this.postData = { ...this.postData, ...postData };
        this.getSignList();
      },
      signSuccess(sign_log_id, user_id, ticket_id) {
        this.showSignModal = false;
        this.getSignList();
        if (!sign_log_id) {
          this.confirmSignOut('', 3);
          return;
        }
        const url = '/Web/Sign/sign_pt_info';
        this.$service
          .post(url, { sign_log_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.sign_log_id = sign_log_id;
              this.signInfo = res.data.data.info;
              this.ptsignComplete(user_id, ticket_id, 'ptsign');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getNormalSign() {
        const url = '/Web/Sign/check_pt_continue_fingerprint_sign';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            this.showNormalSign = res.data.continue_fingerprint;
            this.showNormalSignOut = res.data.continue_fingerprint;
          }
        });
      },
      rowClassName(row, index) {
        if (row.status !== '0') {
          return 'disabled';
        }
      },
      // 释金石签到
      isShiJinShi(sign_id) {
        this.showShiJinShi = this.continueSign = true;
        const url = '/Web/Sign/rotation_sjs_sign_log';
        let postData = {
          sign_id
        };
        this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              // 释金石签到返回状态 status: '0' 确认成功, '2': 继续轮询
              if (res.data.status === '2' && this.continueSign) {
                this.signConfirmTimer = setTimeout(() => {
                  this.isShiJinShi(sign_id);
                }, 2000);
              } else if (res.data.status === '0') {
                if (sign_id === this.sign_log_id) {
                  this.$Message.success('签到成功');
                  this.signSuccess(this.sign_log_id, postData.user_id, res.data.ticket_id);
                  this.sign_log_id = '';
                } else if (sign_id === this.signOutId) {
                  this.$Message.success('签退成功');
                  this.getSignList();
                }
                this.showShiJinShi = false;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      cancelSignConfirm() {
        clearTimeout(this.signConfirmTimer);
        this.continueSign = false;
        const url = '/Web/Sign/close_sjs_pt_windown';
        let postData = {
          sign_id: this.sign_log_id
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode !== 0) {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      checkSignData() {
        if (!this.signData.card_user_id) {
          this.$Message.error('请选择上课课程');
          return false;
        }
        if (!this.signData.coach_id && this.hasReservation == 2 && !this.teamclassScheduleId) {
          this.$Message.error('请选择上课教练');
          return false;
        }
        if (this.modal.signCount < 0 || this.modal.signCount%1 != 0) {
          this.$Message.error('请输入正确的扣除次数');
          return false;
        }
        return true;
      },
      normalSign(type = '') {
        // if (!this.checkSignData()) return;
        // await this.checkCardStatus();
        let postData = Object.assign({}, this.signData, {
          continue_fingerprint: type === 'isShiJinShi' ? 0 : this.showNormalSign,
          sign_method: this.hasReservation===0?1:this.hasReservation===1?2:this.hasReservation===2?3:'',
          teamclass_schedule_id: this.teamclassScheduleId || ''
        });
        {/* 2023/03/02 条件:私教存在已上课未消课,签到时额外传递service_type */}
        let service_type = ''
        if( this.userInfo && this.userInfo.not_sign_arr && this.userInfo.not_sign_arr.length > 0 && this.hasReservation === 0 ){
          this.userInfo.not_sign_arr.forEach(item => {
            if(item.pt_id == this.chooseUnsigned) {
              service_type = item.service_type
            }
          })
          postData.service_type = service_type
        }
        this.$service
          .post('/Web/Sign/sign_pt', postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.sign_log_id = res.data.sign_id;
              if (type === 'isShiJinShi') {
                this.isShiJinShi(this.sign_log_id);
              } else {
                this.signSuccess(res.data.sign_id, postData.user_id, res.data.ticket_id);
                this.$Message.success(res.data.errormsg);
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getPtCoachList() {
        const url = '/Web/Sign/get_coach_list';
        return this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.coachList = res.data.data.list;
            } else {
              throw new Error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      signUserChanged(userInfo) {
        this.userInfo = userInfo;
        if (!userInfo) {
          this.initial();
          return;
        }
        this.getPtCoachList();
        // 取出所有私教、泳教卡
        this.ptCardList = userInfo.card_list.filter(card => {
          return card.card_type_id === '4' || card.card_type_id === '5';
        });
        this.ptCardList2 = userInfo.card_list.filter(card => {
          return (card.card_type_id === '4' || card.card_type_id === '5') && card.is_hidden == 0;
        });
        if (!userInfo.pt_list) return;
        // 有预约
        if (userInfo.pt_list.length) {
          this.hasReservation = 1;
          this.chooseReserved = userInfo.pt_list[0].pt_schedule_id;
        }
        // 有补签
        if (userInfo.not_sign_arr && userInfo.not_sign_arr.length) {
          this.hasReservation = 0;
          this.chooseUnsigned = userInfo.not_sign_arr[0].pt_id;
        }
        this.userInfo.pt_list = userInfo.pt_list.map(ptClass => {
          let ptCard = userInfo.card_list.filter(card => {
            return card.card_user_id === ptClass.card_user_id;
          });
          return Object.assign({}, ptClass, {
            card_name: ptCard[0] && ptCard[0].card_name
          });
        });
      },
      dateChanged(val) {
        const [start, end] = val;
        const time = 3600 * 1000 * 24
        const minTime = new Date(start).getTime() / time;
        const maxTime = new Date(end).getTime() / time;
        const test = maxTime - minTime;
        if (maxTime - minTime > 89) {
          this.$Message.error('时间范围不能超过90天')
          this.dateRange = [...this.dateRange];
          return false;
        }

        // let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`;
        // let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`;
        // this.dateRange = [beginDate, endDate];
        // this.postData.s_date = beginDate;
        // this.postData.e_date = endDate;
        this.dateRange = [start, end];
        this.postData.s_date = start;
        this.postData.e_date = end;
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getSignList();
      },
      clickSignOut(sign_id) {
        const url = '/Web/Sign/class_info_notarize';
        this.$service
          .post(url, { sign_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.signOutInfo = data;
              this.signOutId = sign_id;
              this.showSignOut = true;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      confirmSignOut(type, signType) {
        const url = '/Web/Sign/pt_end_class';
        const postData = {
          sign_log_id: this.signOutId,
          type: signType,
          continue_fingerprint: type === 'isShiJinShi' ? 0 : this.showNormalSignOut
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.showSignOut = false;
            if (type === 'isShiJinShi') {
              this.isShiJinShi(this.signOutId);
            } else {
              this.$Message.success(res.data.errormsg);
              this.getSignList();
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
          this.initial();
        });
      },
      cancelSign(id) {
        this.$Modal.confirm({
          title: '取消签到',
          content: '确认取消这次签到吗？',
          onOk: () => {
            this.confirmCancel(id);
          }
        });
      },
      confirmCancel(sign_log_id) {
        const url = '/Web/Sign/pt_cancel_user_sign';
        let postData = {
          sign_log_id
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode != 0) {
            this.$Message.error(res.data.errormsg);
          }
          this.getSignList();
          this.setDisabledTitle();
        });
      },
       getNotSellCount() {
        return this.$service
          .post('/Web/Sign/private_sign_log_count',{}, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.notSellCount = +res.data.data;
             
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      getSignList() {
        const url = '/Web/Sign/private_sign_log_list';
        // let postData = {
        //   search,
        //   class_id,
        //   s_date: this.dateRange[0],
        //   e_date: this.dateRange[1],
        //   page_no: this.currentPage,
        //   page_size: this.sizer
        // };
        return this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              let list = res.data.data.list;
              if (!list || !list.length) {
                this.tableData = [];
                this.totalCount = 0;
                return;
              }
              this.totalCount = +res.data.data.count;
              this.tableData = list.map(item => {
                return {
                  ...item,
                  ...{
                    operation: {
                      disabled: item.status !== '0',
                      id: item.id,
                      signOuted: !!item.end_date
                    },
                    user: {
                      user_id: item.user_id,
                      username: item.username
                    },
                    type: {
                      type: item.type,
                      end_type: item.end_type
                    },
                    duration: item.duration || item.duration === 0 ? `${item.duration}分钟` : item.duration,
                    sign_number: {
                      sign_number: item.sign_number,
                      pt_status: item.pt_status
                    }
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData(isExport) {
        const { search, class_id } = this.postData;
        const url = '/Web/Sign/pt_sign_excel';
        let postData = {
          search,
          class_id,
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          page_no: 1,
          page_size: this.totalCount
        };
        return this.$service
          .post(url, postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              return res.data.data.list.map(item => {
                return {
                  ...item,
                  ...{
                    operation: {
                      disabled: item.status !== '0',
                      id: item.id,
                      signOuted: !!item.end_date
                    },
                    card_name: `${item.card_name}${item.status != 0 ? '（已取消）' : ''}`,
                    user: item.username,
                    type: item.end_type ? `${item.type}/${item.end_type}` : item.type,
                    create_date: formatDate(new Date(item.create_time * 1000), 'yyyy-MM-dd HH:mm'),
                    sign_number: item.pt_status == 3 ? `${item.sign_number}（爽约）` : item.sign_number
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      otherCase(val) {
        if (val === '0') {
          // #11621 导出时请求接口换成/Web/Sign/pt_sign_excel以校验导出权限
          this.$service
            .post('Web/Sign/pt_sign_excel', { ...this.postData, _export: 1})
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success({
                  content:'导出任务运行中，请稍后到消息中心下载!',
                  duration: 3
                })
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      }
    }
  };
</script>
