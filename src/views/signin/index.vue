<template>
<div>
  <Button type="success" @click="show=true">确定</Button>
  <brandAdd></brandAdd>

  <!--领手环-->
  <Modal v-model="show" title="手环领取" :mask-closable="false">
    <brandAdd></brandAdd>
  </Modal>
</div>
</template>

<script type="text/javascript">
  import brandAdd from './components/brandAdd.vue'
  export default {
    name: 'memberSignin',
    data() {
      return {
        num:[],
        num2:[],
        show:false
      }
    },
    components: {
      brandAdd
    },
  }
</script>
