<style scoped>
  .freedomSign {
    overflow: auto;
    height: 100%;
  }

  .freedomSign-content {
    overflow: hidden;
    width: 100%;
    background: #ececec;
    font-size: 14px;
  }

  .freedomSign-title {
    width: 100%;
    height: 40px;
  }

  .title-child {
    width: 180px;
    height: 40px;
    background: #f7f7f7;
    border: 1px solid #cccccc;
    float: left;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }

  .titleactive {
    background: #fff;
    border-top: 2px solid #5cb85c;
    font-weight: bold;
    border-bottom: 0;
    border-right: 0;
    border-right: 1px solid #cccccc;
  }

  /*使用微信客户端*/
  .freedomSign_l {
    overflow: hidden;
    width: 100%;
    padding-top: 54px;
    padding-left: 54px;
    border: 1px solid #dcdcdc;
    margin-top: -1px;
    background: #fff;
    /*display: none;*/
  }

  .freedomSign_l1_num {
    clear: both;
    float: left;
    color: #5cb85c;
    font-size: 48px;
    text-decoration: underline;
    font-weight: 600;
  }

  .freedomSign_l1_title {
    margin: 0;
    padding: 0;
    display: block;

    color: #666666;
    font-size: 16px;
    margin-left: 30px;
    padding-top: 22px;
    margin-bottom: 12px;
  }

  .freedomSign_l1_fl {
    float: left;
  }

  .mg50 {
    margin-bottom: 50px;
  }

  .freedomSign_l1_img {
    display: block;
    width: 230px;
    height: 230px;
    background: url(~assets/img/web-fe-v1/qrcode_test.jpeg) no-repeat;
    background-size: cover;
    margin-left: 13px;
    margin-top: 13px;
  }

  .toggle_img {
    background: url(~assets/img/web-fe-v1/qrcode_online.jpg) no-repeat;
    background-size: cover;
  }

  .freedomSign_l1_content {
    color: #999999;
    font-size: 14px;
    margin-bottom: 8px;
    padding-left: 65px;
  }

  .freedomSign_l2_text {
    margin: 0;
    margin-left: 30px;
    color: #999999;
    font-size: 14px;
    margin-top: 5px;
  }

  .freedomSign_l2_img {
    width: 264px;
    height: 435px;
    background: url(~assets/img/web-fe-v1/fm1_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .freedomSign_l3_img {
    width: 263px;
    height: 432px;
    background: url(~assets/img/web-fe-v1/fm2_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .freedomSign_l4_img {
    width: 264px;
    height: 483px;
    background: url(~assets/img/web-fe-v1/fm3_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .gzhlj {
    font-size: 14px;
    color: #3598db;
    margin-left: 38px;
    cursor: pointer;
  }

  .gzhlj:hover {
    opacity: 0.7;
  }

  /*自主扫码签到*/
  .freedomSign_c {
    overflow: hidden;
    width: 100%;
    padding-top: 30px;
    border: 1px solid #dcdcdc;
    margin-top: -1px;
    background: #fff;
    /*display: none;*/
  }

  .freedomSign_c_title {
    color: #5cb85c;
    font-size: 14px;
    margin: 0;
    padding-left: 20px;
  }

  .freedomSign_cbox {
    overflow: hidden;
    padding-left: 54px;
    padding-top: 40px;
  }

  .freedomSign_c1_num {
    clear: both;
    float: left;
    color: #5cb85c;
    font-size: 48px;
    text-decoration: underline;
    font-weight: 600;
  }

  .freedomSign_c1_title {
    margin: 0;
    padding: 0;
    display: block;
    color: #666666;
    font-size: 16px;
    margin-left: 31px;
    padding-top: 22px;
    margin-bottom: 12px;
  }

  .freedomSign_c1_fl {
    float: left;
  }

  .freedomSign_c1_img {
    display: block;
    width: 215px;
    height: 217px;
    margin-left: 30px;
    margin-top: 13px;
  }

  .download-btn {
    display: block;
    width: 150px;
    height: 35px;
    border: 1px solid #5cb85c;
    color: #5cb85c;
    font-size: 14px;
    margin: 23px 0;
    margin-left: 61px;
    text-align: center;
    line-height: 34px;
    border-radius: 4px;
    cursor: pointer;
  }

  .download-btn:hover {
    opacity: 0.7;
  }

  .freedomSign_c2_text {
    margin: 0;
    margin-left: 30px;
    color: #999999;
    font-size: 14px;
    margin-top: 5px;
    width: 660px;
  }

  .freedomSign_c2_img1 {
    width: 755px;
    height: 748px;
    background: url(~assets/img/web-fe-v1/fm2_2_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
    float: left;
  }

  .freedomSign_c3_img1 {
    width: 925px;
    height: 432px;
    background: url(~assets/img/web-fe-v1/fm2_3_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
    float: left;
  }

  .greencolor {
    color: #5cb85c;
  }

  .freedomSign_c4_img1 {
    width: 346px;
    height: 501px;
    background: url(~assets/img/web-fe-v1/fm2_4_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    float: left;
  }

  .freedomSign_c4_img2 {
    clear: both;
    width: 950px;
    height: 509px;
    background: url(~assets/img/web-fe-v1/fm2_5_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-bottom: 50px;
  }

  /*使用场馆自身微信公众号*/
  .freedomSign_r {
    overflow: hidden;
    width: 100%;
    padding-top: 30px;
    border: 1px solid #dcdcdc;
    margin-top: -1px;
    background: #fff;
    /*display: none;*/
  }

  .freedomSign_r_title {
    color: #5cb85c;
    font-size: 14px;
    margin: 0;
    padding-left: 54px;
  }

  .freedomSign_rbox {
    overflow: hidden;
    padding-left: 54px;
    padding-top: 40px;
  }

  .freedomSign_r1_num {
    clear: both;
    float: left;
    color: #5cb85c;
    font-size: 48px;
    text-decoration: underline;
    font-weight: 600;
  }

  .freedomSign_r1_title {
    margin: 0;
    padding: 0;
    display: block;
    color: #666666;
    font-size: 16px;
    margin-left: 31px;
    padding-top: 22px;
    margin-bottom: 12px;
  }

  .freedomSign_r1_fl {
    float: left;
  }

  .freedomSign_r1_content {
    color: #999999;
    font-size: 14px;
    padding-left: 31px;
  }

  .content-r-text {
    margin: 0;
    margin-left: 30px;
    color: #999999;
    font-size: 14px;
    margin-top: 5px;
    clear: both;
    width: 715px;
    word-break: break-all;
  }

  .mg10 {
    margin-left: 45px;
  }

  .mg15 {
    margin-left: 52px;
  }

  .mgtop {
    margin-top: 20px;
  }

  .bluecolor {
    color: #3598db;
  }

  .bluecolor:hover {
    opacity: 0.7;
  }

  .freedomSign_r2_img1 {
    width: 715px;
    height: 592px;
    background: url(~assets/img/web-fe-v1/fm3_3_03.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
    float: left;
  }

  .freedomSign_r2_img2 {
    width: 674px;
    height: 594px;
    background: url(~assets/img/web-fe-v1/fm3_3_07.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 20px;
    float: left;
  }

  .freedomSign_r3_img1 {
    width: 379px;
    height: 141px;
    background: url(~assets/img/web-fe-v1/fm3_4_10.jpg) no-repeat;
    background-size: cover;
    margin-left: 38px;
    margin-top: 30px;
    margin-bottom: 50px;
    float: left;
  }

  .freedom_tootip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 472px;
    height: 304px;
    background: #fff;
    border: 1px solid #5cb85c;
    padding: 43px;
  }

  .freedom_tootip_test {
    margin: 0;
    text-indent: 1em;
    color: #666666;
    font-size: 26px;
    text-align: center;
    line-height: 42px;
  }

  .freedom_tootip_gefont {
    margin: 0;
    color: #5cb85c;
    font-weight: 600;
  }

  .freedom_tootip_know {
    margin: 0;
    font-size: 20px;
    text-decoration: underline;
    color: #999999;
    text-align: center;
    margin-top: 20px;
    cursor: pointer;
  }

  .signurl2 {
    display: inline-block;
    cursor: pointer;
  }

  /*模板弹窗*/
  .freedom_picbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    /*display: none;*/
    z-index: 9;
  }

  .freedom_pic {
    width: 891px;
    min-height: 576px;
    background: #fff;
    margin: 120px auto;
    padding: 0 21px;
    padding-bottom: 0;
    border: 1px solid #dcdcdc;
  }

  .freedom_pic-t {
    width: 100%;
    height: 49px;
  }

  .freedom_pic h2 {
    font-size: 18px;
    line-height: 49px;
    display: block;
    float: left;
    margin: 0;
    font-weight: bold;
  }

  .freedom_pic-t i {
    display: block;
    float: right;
    width: 20px;
    height: 20px;
    font-size: 20px;
    border: 1px solid black;
    text-align: center;
    line-height: 15px;
    border-radius: 50%;
    cursor: pointer;
    margin-top: 14px;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    transform: rotate(45deg);
    font-style: normal;
  }

  .freedom_pic-b {
    overflow: hidden;
    margin-top: 18px;
  }

  .freedom_pic-b ul {
    margin: 0;
    overflow: hidden;
  }

  .freedom_picList {
    list-style: none;
    display: block;
    float: left;
    width: 258px;
    height: 360px;
    position: relative;
    margin-left: 18px;
    border: 3px solid #dcdcdc;
    border-radius: 4px;
  }

  .freedom_picList:nth-child(1) {
    margin-left: 20px;
  }

  .toggleborder {
    border: 3px solid #5cb85c;
  }

  .coach-sign-images {
    padding: 10px 0 10px 30px;
  }
   .coach-sign-images  img{
     display: inline-block;
     width: 375px;
     height: auto;
     margin-right: 10px;
   }
  .mc {
    overflow: hidden;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: black;
    cursor: pointer;
  }

  .mc img {
    width: 100%;
    height: 100%;
  }

  .mc img.picopacity {
    opacity: 0.7;
  }

  .freedom_pic_title {
    color: #5cb85c;
    font-size: 14px;
    margin: 0;
    margin-left: 20px;
    margin-top: 20px;
  }

  .mb-on {
    display: inline-block;
    width: 70px;
    height: 30px;
    line-height: 30px;
    background-color: #5fb75d;
    color: #fff;
    border: none;
    border-radius: 4px;
    margin-left: 310px;
    margin-top: 30px;
    cursor: pointer;
    text-align: center;
  }

  .mb-on:hover {
    opacity: 0.9;
  }

  .mb-off {
    width: 70px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    color: #5fb75d;
    border: 1px solid #5fb75d;
    border-radius: 4px;
    margin-left: 72px;
    margin-top: 30px;
  }

  .mb-off:hover {
    background-color: #5fb75d;
    color: #fff;
  }

  .switch {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .switch-item {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 180px;
    padding: 0 15px;
    height: 40px;
    color: #5cb85c;
    font-size: 16px;
    background-color: #fff;
    cursor: pointer;
    border: 2px solid #eee;
  }

  .switch-item:first-child {
    border-radius: 10px 0 0 10px;
    border-right: 0;
  }

  .switch-item:last-child {
    border-radius: 0 10px 10px 0;
    border-left: 0;
  }

  .switch-item-active {
    background-color: #5cb85c;
    color: #fff;
  }

  .new-login {
    background: url(~assets/img/web-fe-v1/new-login.jpg) no-repeat center top / contain;
  }

  .new-mytrain {
    background: url(~assets/img/web-fe-v1/new-mytrain.jpg) no-repeat center top / contain;
  }

  .new-sign-images {
    display: flex;
  }

  .new-sign-class {
    background: url(~assets/img/web-fe-v1/new-sign-class.jpg) no-repeat center top / contain;
  }

  .new-sign-card {
    background: url(~assets/img/web-fe-v1/new-sign-card.jpg) no-repeat center top / contain;
  }

  .new-sign-success {
    background: url(~assets/img/web-fe-v1/new-sign-success.jpg) no-repeat center top / contain;
  }

  .new-operation-1 {
    padding-bottom: 50%;
    height: 0;
    background: url(~assets/img/web-fe-v1/new-operation-1.jpg) no-repeat center top / contain;
  }

  .new-operation-2 {
    padding-bottom: 50%;
    height: 0;
    background: url(~assets/img/web-fe-v1/new-operation-2.jpg) no-repeat center top / contain;
  }

  .new-operation-3 {
    padding-bottom: 50%;
    height: 0;
    background: url(~assets/img/web-fe-v1/new-operation-3.jpg) no-repeat center top / contain;
  }

  .new-operation-4 {
    padding-bottom: 50%;
    height: 0;
    background: url(~assets/img/web-fe-v1/new-operation-4.jpg) no-repeat center top / contain;
  }

  .new-menu {
    width: 900px;
    padding-bottom: 50%;
    height: 0;
    background: url(~assets/img/web-fe-v1/new-menu.jpg) no-repeat center top / contain;
  }

  .mini-program-img {
    height: 245px;
  }

  .ul-list li {
    list-style: circle;
  }

  .freedomSign_c /deep/ .ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-vertical .ivu-menu-submenu-title:hover{
    color: #2d8cf0;
    background: #f0faff;
  }

  .helpCenter {
    padding: 10px 20px;
    /* display: flex;
    align-items: flex-start;
    justify-content: flex-start; */
    position: relative;
  }

  .menuBorder {
    width: 162px;
    /* padding: 10px 20px; */
    box-sizing: border-box;
    border: 1px solid #dcdcdc;
    position: absolute;
    top: 10px;
    left: 20px;
    bottom: 10px;
  }

  .menuBorder .menu,.menuBorder .activeMenu {
    width: 100%;
    padding: 12px 12px;
    text-align: center;
    user-select: none;
    box-sizing: border-box;
    background: #fff;
    cursor: pointer;
  }

  .menuBorder .menu:hover {
    color: #39f;
    background: #f3f3f3;
  }

  .menuBorder .activeMenu {
    color: #39f;
    background: #f3f3f3;
  }

  .member-login {
    background: url(~assets/img/new-member/login.png) no-repeat center top / contain;
  }
  
  .member-scan-code-one {
    background: url(~assets/img/new-member/scan-code-one.png) no-repeat center top / contain;
  }

  .member-scan-code-two {
    background: url(~assets/img/new-member/scan-code-two.png) no-repeat center top / contain;
  }

  .member-scan-result {
    width: 263px;
    height: 432px;
    margin-bottom: 30px;
    background: url(~assets/img/new-member/scan-result.png) no-repeat center top / contain;
  }

  .member-remind {
    background: url(~assets/img/new-member/member-remind.jpg) no-repeat;
  }

</style>
<template>
  <div>
    <div class="freedomSign"
         id="freedom">
      <div class="new-sign">
        <!-- 标题 -->
        <div class="freedomSign-content">
          <div class="freedomSign-title-l title-child"
               @click="switchTab(0)"
               v-bind:class="{titleactive: active == 0}">会员端绑定
          </div>
          <div class="freedomSign-title-c title-child"
               @click="switchTab(1)"
               v-bind:class="{titleactive: active == 1}">会员扫码签到
          </div>
           <div class="freedomSign-title-c title-child"
               @click="switchTab(4)"
               v-bind:class="{titleactive: active == 4}">团操教练扫码签到
          </div>
          <div class="freedomSign-title-r title-child"
               @click="switchTab(2)"
               v-bind:class="{titleactive: active == 2}">公众号绑定
          </div>
          <div class="freedomSign-title-r title-child"
               @click="switchTab(3)"
               v-bind:class="{titleactive: active == 3}">特约商户配置
          </div>
          <div class="freedomSign-title-r title-child"
               @click="switchTab(5)"
               v-bind:class="{titleactive: active == 5}">爱动团操扫码签到
          </div>
          <div class="freedomSign-title-r title-child"
               @click="switchTab(6)"
               v-bind:class="{titleactive: active == 6}">勤鸟+帮助中心
          </div>
        </div>
        <!-- 会员端绑定 -->
        <div class="freedomSign_l"
             v-if="active == 0">
          <div class="freedomSign_l1">
            <div class="freedomSign_l1_num">1</div>
            <div class="freedomSign_l1_fl">
              <p class="freedomSign_l1_title">微信“扫一扫”关注公众号</p>
              <img class="freedomSign_l1_img"
                   :src="bus_ewmcode">
              <p class="freedomSign_l1_content">关注勤鸟运动公众号</p>
            </div>
          </div>
          <div class="freedomSign_l2">
            <div class="freedomSign_l1_num">2</div>
            <div class="freedomSign_l1_fl">
              <p class="freedomSign_l1_title">点击菜单【运动管理】,选择【点击进入场馆首页】</p>
              <div class="freedomSign_l2_img"></div>
            </div>
          </div>
          <div class="freedomSign_l3">
            <div class="freedomSign_l1_num">3</div>
            <div class="freedomSign_l1_fl">
              <p class="freedomSign_l1_title">填写预留在系统的手机号点击【登录】即可</p>
              <p class="freedomSign_l2_text">事先如果没将会员资料录入系统可直接注册也可直接绑定</p>
              <div class="freedomSign_l3_img new-login"></div>
            </div>
          </div>
          <div class="freedomSign_l4">
            <div class="freedomSign_l1_num">4</div>
            <div class="freedomSign_l1_fl mg50">
              <p class="freedomSign_l1_title">拍照进行签到</p>
              <p class="freedomSign_l2_text">根据要求完成拍照，将自动完成上课人数统计 即可完成签到</p>
              <div class="freedomSign_l4_img new-mytrain"></div>
            </div>
          </div>
        </div>
        <!-- 会员扫码签到 -->
        <div class="freedomSign_c"
             v-if="active == 1">
          <p class="freedomSign_c_title">会员微信扫描二维码即可完成到馆签到，省时省力，提升效率！</p>
          <div class="freedomSign_cbox">
            <div class="freedomSign_c1">
              <div class="freedomSign_c1_num">1</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">【准备工作】下载二维码打印放置前台</p>
                <img class="freedomSign_c1_img mini-program-img"
                     :src="miniSignImg">
                <p class="download-btn"
                   @click="downloadCode">下载签到二维码</p>
              </div>
            </div>
            <div class="freedomSign_c2">
              <div class="freedomSign_c1_num">2</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">【准备工作】打开新签到提醒页面（可跳过）</p>
                <p class="freedomSign_c2_text">提醒页面打开后，每当有会员成功用手机签到，页面就会立即展示签到信息并可用语音直接播报</p>
                <div class="freedomSign_c2_img1"></div>
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">3</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">会员使用微信扫签到二维码</p>
                <p class="freedomSign_c2_text">首次使用的会员需要用手机号登录</p>
                <p class="freedomSign_c2_text">如果会员今天有预约过课程信息，可选择本次签到对应的课程
                  <span class="greencolor">（没预约则省略此步）</span>
                </p>
                <p class="freedomSign_c2_text">如果会员有多张可用的会员卡，则可根据自身需要选择本次消费用卡
                  <span class="greencolor">（没预约则省略此步）</span>
                </p>
                <div class="new-sign-images">
                  <div class="freedomSign_l3_img new-login"></div>
                  <div class="freedomSign_l3_img new-sign-class"></div>
                  <div class="freedomSign_l3_img new-sign-card"></div>
                </div>
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">4</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">签到成功</p>
                <p class="freedomSign_c2_text">签到成功后，手机即会有签到成功显示，并在电脑上可看到剩余次数、有效期，</p>
                <p class="freedomSign_c2_text">新签到提醒页面也会同步自动显示出会员的签到信息，并且会用语音提醒前台人员</p>
                <div class="freedomSign_c4_img1 new-sign-success"></div>
                <div class="freedomSign_c4_img2"></div>

              </div>
            </div>
          </div>
        </div>
        <!-- 团操教练扫码签到 -->
        <div class="freedomSign_c" v-if="active == 4">
          <p class="freedomSign_c_title">教练微信扫码即可完成团操课程签到，自动统计人数，省时省力，提升效率！</p>
          <div class="freedomSign_cbox">
            <div class="freedomSign_c1">
              <div class="freedomSign_c1_num">1</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">【准备工作】下载二维码打印放置在每间教室</p>
                <img class="freedomSign_c1_img mini-program-img"
                     :src="miniCoachSignImg">
                     <a class="download-btn" v-on:click="download"  download :href="miniCoachSignImg">下载签到二维码</a>
              </div>
            </div>
            <div class="freedomSign_c2">
              <div class="freedomSign_c1_num">2</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">【准备工作】将团操课教练信息录入系统</p>
                <p class="freedomSign_c2_text">特别注意，一定要保证教练的手机号录入准确</p>
                <img class="content-r-text" src="~assets/img/coach-signin/4.png" alt="" />
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">3</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">教练使用微信扫签到二维码</p>
                <p class="freedomSign_c2_text">首次使用的教练需要用手机号登录</p>
                <p class="freedomSign_c2_text">选择本次上课的课程进行进行签到</p>
                <div class="coach-sign-images">
                  <img src="~assets/img/coach-signin/1.jpg" alt="" />
                  <img src="~assets/img/coach-signin/2.jpg" alt="" />
                </div>
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">4</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">签到成功</p>
                <p class="freedomSign_c2_text">签到成功后，手机即会有签到成功显示，并在电脑上可看到剩余次数、有效期，</p>
                <p class="freedomSign_c2_text">新签到提醒页面也会同步自动显示出会员的签到信息，并且会用语音提醒前台人员</p>
                 <div class="coach-sign-images">
                  <img src="~assets/img/coach-signin/3.jpg" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 公众号绑定 -->
        <div class="freedomSign_r"
             v-if="active == 2">
          <p class="freedomSign_r_title">如果场馆拥有自己的公众号，也可以将会员端的功能嫁接到公众号上使用</p>
          <div class="freedomSign_rbox">
            <div class="freedomSign_r1">
              <div class="freedomSign_r1_num">1</div>
              <div class="freedomSign_r1_fl">
                <p class="freedomSign_r1_title">进入公众号后台</p>
                <p class="freedomSign_r1_content">微信公众号后台地址：
                  <a href="http://mp.weixin.qq.com"
                     class="signurl1 bluecolor"
                     target="_blank">http：//mp.weixin.qq.com</a>
                </p>
                <p class="freedomSign_r1_content">还未开通微信公众号的用户，点击查看如何开通公众号：
                  <a class="signurl2 bluecolor"
                     @click="wxurl"
                     target="_blank">点这里</a>
                </p>
              </div>
            </div>
            <div class="freedomSign_r2">
              <div class="freedomSign_r1_num">2</div>
              <div class="freedomSign_r1_fl">
                <p class="freedomSign_r1_title">设置菜单</p>
                <p class="content-r-text">1、进入【小程序】</p>
                <p class="content-r-text">2、在【小程序管理】点击条件操作</p>
                <p class="content-r-text">3、点击【关联小程序】</p>
                <div class="freedomSign_r2_img1 new-operation-1"></div>
                <p class="content-r-text">4、管理员扫码授权</p>
                <div class="freedomSign_r2_img1 new-operation-2"></div>
                <p class="content-r-text">5、输入appId:
                  <span style="font-weight: bold">{{appId}}</span>
                </p>
                <div class="freedomSign_r2_img1 new-operation-3"></div>
                <p class="content-r-text">6、等待勤鸟进行审核，待审核通过后方可进行第三步添自定义菜单</p>
                <div class="freedomSign_r2_img1 new-operation-4"></div>

              </div>
            </div>
            <div class="freedomSign_r3">
              <div class="freedomSign_r1_num">3</div>
              <div class="freedomSign_r1_fl">
                <p class="freedomSign_r1_title">菜单设置</p>
                <p class="content-r-text">1、进入【自定义菜单】</p>
                <p class="content-r-text">2、添加菜单并输入菜单名称，例如课表，教练介绍等</p>
                <p class="content-r-text">3、菜单内容，选择跳转小程序</p>
                <p class="content-r-text">4、选择小程序之前关联的小程序，并根据需要复制对应页面路径</p>
                <p class="freedomSign_r1_title">连锁商家，路由页面</p>
                <em style="color:red;margin-left:30px;">点击菜单，跳转至商家名下的路由页面，点击任一图标跳转至小程序对应页面</em>
                <div>
                  <div v-for="item in miniPath"
                     :key="item.name">
                    <p class="content-r-text mg10">【{{item.name}}】</p>
                    <p class="content-r-text mg15">{{item.pagepath}}</p>
                  </div>
                </div>
                <br/>
                <p class="freedomSign_r1_title">单场馆，小程序各页面对应路径</p>
                <em style="color:red;margin-left:30px;">点击菜单，跳转至小程序对应页面</em>
                <div v-for="item in miniUrl"
                     :key="item.name">
                  <p class="content-r-text mg10">【{{item.name}}】</p>
                  <p class="content-r-text mg15">{{item.pagepath}} <a :href="item.qrcode" :download="item.name">下载会员端二维码</a></p>
                </div>
                <br/>
                <p class="content-r-text">5、填写备用路径，建议填写 https://www.rocketbird.cn</p>
                <p class="content-r-text">6、点击保存并发布</p>
                <div class="freedomSign_r2_img1 new-menu"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 如何配置特约商户 -->
      <div class="freedomSign_r"
           v-if="active == 3">
        <div class="freedomSign_rbox">
          <div class="freedomSign_r3">
            <div class="freedomSign_r1_num">1</div>
            <div class="freedomSign_r1_fl">
              <p class="freedomSign_r1_title">注册特约商户</p>
              <p class="content-r-text">1、联系勤鸟售后人员，提供如下信息：</p>
              <ul class="mg10 ul-list">
                <li class="content-r-text">基本信息及结算信息 <a download
                                                             href="http://rb-platform.oss-cn-shenzhen.aliyuncs.com/0b0d5d73f28785b8b5ed2312adc03d2b.docx">下载收款账户资料证明表</a>
                </li>
                <li class="content-r-text">
                  商户信息：营业执照图片、经办人或者法人的身份证正反面图片（图片格式为bmp、png、jpeg、jpg，大小不超过2M）
                </li>
              </ul>
              <p class="content-r-text">2、勤鸟工作人员提交信息至微信服务商平台</p>
              <p class="content-r-text">3、根据微信发送的邮件进行手机签约或电脑签约（签约步骤为账户验证-->签訂协议）</p>
              <!--<div class="freedomSign_r2_img1 new-operation-1"></div>-->
              <img class="content-r-text" src="~assets/img/web-fe-v1/manual-01.png" alt="">
              <p class="content-r-text">4、登陆<a target="_blank" href="https://pay.weixin.qq.com">微信支付平台</a>，进入产品中心--我授权的产品页面，选择我的服务商为重庆勤鸟圈科技有限公司，对服务商平台退款、服务商API退款选择授权操作（完成此步操作后，则商家可进行线上退款，如会员线上预约团课，取消预约时，相应的团课费将自动退款到会员微信余额中）
              </p>
              <img class="content-r-text mg10" src="~assets/img/web-fe-v1/manual-weixin.jpg" alt="">
            </div>
          </div>
          <div class="freedomSign_r3">
            <div class="freedomSign_r1_num">2</div>
            <div class="freedomSign_r1_fl" style="padding-bottom: 40px">
              <p class="freedomSign_r1_title">配置特约商户</p>
              <p class="content-r-text">在
                <router-link :to="{ path: '/paraset/settings' }"><span
                  class="greencolor">勤鸟系统管理端->设置->系统设置->微信支付特约商户配置</span></router-link>
                填写已注册的商户名称及商户号
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 爱动预约课程扫码签到 -->
        <div class="freedomSign_c"
             v-if="active == 5">
          <p class="freedomSign_c_title">会员微信扫描二维码即可完成到馆签到，省时省力，提升效率！</p>
          <div class="freedomSign_cbox">
            <div class="freedomSign_c1">
              <div class="freedomSign_c1_num">1</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">【准备工作】下载二维码打印放置前台</p>
                <img class="freedomSign_c1_img mini-program-img"
                     :src="aidongMiniSignImg">
                <a class="download-btn" download :href="aidongMiniSignImg">下载签到二维码</a>
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">2</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">会员使用微信扫签到二维码</p>
                <p class="freedomSign_c2_text">首次使用的会员需要用手机号登录</p>
                <p class="freedomSign_c2_text">如果会员今天有预约过课程信息，可选择本次签到对应的课程
                  <span class="greencolor">（没预约则省略此步）</span>
                </p>
                <div class="new-sign-images">
                  <div class="freedomSign_l3_img new-login"></div>
                  <div class="freedomSign_l3_img new-sign-class"></div>
                </div>
              </div>
            </div>
            <div class="freedomSign_c3">
              <div class="freedomSign_c1_num">3</div>
              <div class="freedomSign_c1_fl">
                <p class="freedomSign_c1_title">签到成功</p>
                <p class="freedomSign_c2_text">签到成功后，手机即会有签到成功显示。</p>
                <div class="freedomSign_c4_img1 new-sign-success"></div>
              </div>
            </div>
          </div>
        </div>
      <!-- 勤鸟+帮助中心 -->
      <div v-if="active===6" class="freedomSign_c">
        <div class="helpCenter">
          <div class="menuBorder">
            <div :class="[ activeMenu === 1 ? 'activeMenu' : 'menu' ]" @click="activeMenu = 1">会员端扫码签到</div>
            <div :class="[ activeMenu === 2 ? 'activeMenu' : 'menu' ]" @click="activeMenu = 2">公众号绑定</div>
            <div :class="[ activeMenu === 3 ? 'activeMenu' : 'menu' ]" @click="activeMenu = 3">其他二维码</div>
          </div>
          <!-- 会员扫码签到 -->
          <div class="freedomSign_c" style="marginLeft: 182px; width:auto;"  v-show="activeMenu == 1">
            <p class="freedomSign_c_title">会员微信扫描二维码即可完成到馆签到，省时省力，提升效率！</p>
            <div class="freedomSign_cbox">
              <div class="freedomSign_c1">
                <div class="freedomSign_c1_num">1</div>
                <div class="freedomSign_c1_fl">
                  <p class="freedomSign_c1_title">【准备工作】下载二维码打印放置前台</p>
                  <img class="freedomSign_c1_img mini-program-img"
                      :src="otherMiniSignImg">
                  <a class="download-btn" v-on:click="download"  download :href="otherMiniSignImg">下载签到二维码</a>
                </div>
              </div>
              <div class="freedomSign_c2">
                <div class="freedomSign_c1_num">2</div>
                <div class="freedomSign_c1_fl">
                  <p class="freedomSign_c1_title">【准备工作】打开新签到提醒页面（可跳过）</p>
                  <p class="freedomSign_c2_text">提醒页面打开后，每当有会员成功用手机签到，页面就会立即展示签到信息并可用语音直接播报</p>
                  <div class="freedomSign_c2_img1 member-remind"></div>
                </div>
              </div>
              <div class="freedomSign_c3">
                <div class="freedomSign_c1_num">3</div>
                <div class="freedomSign_c1_fl">
                  <p class="freedomSign_c1_title">会员使用微信扫签到二维码</p>
                  <p class="freedomSign_c2_text">首次使用的会员需要用手机号登录</p>
                  <p class="freedomSign_c2_text">如果会员今天有预约过课程信息，可选择本次签到对应的课程
                    <span class="greencolor">（没预约则省略此步）</span>
                  </p>
                  <p class="freedomSign_c2_text">如果会员有多张可用的会员卡，则可根据自身需要选择本次消费用卡
                    <span class="greencolor">（没预约则省略此步）</span>
                  </p>
                  <div class="new-sign-images">
                    <div class="freedomSign_l3_img member-login"></div>
                    <div class="freedomSign_l3_img member-scan-code-one"></div>
                    <div class="freedomSign_l3_img member-scan-code-two"></div>
                  </div>
                </div>
              </div>
              <div class="freedomSign_c3">
                <div class="freedomSign_c1_num">4</div>
                <div class="freedomSign_c1_fl">
                  <p class="freedomSign_c1_title">签到成功</p>
                  <p class="freedomSign_c2_text">签到成功后，手机即会有签到成功显示，并在电脑上可看到剩余次数、有效期，</p>
                  <p class="freedomSign_c2_text">新签到提醒页面也会同步自动显示出会员的签到信息，并且会用语音提醒前台人员</p>
                  <div class="freedomSign_c4_img1 member-scan-result"></div>
                  <div class="freedomSign_c4_img2"></div>

                </div>
              </div>
            </div>
          </div>
          <!-- 公众号绑定 -->
          <div class="freedomSign_r" style="marginLeft: 182px; width:auto;" v-show="activeMenu == 2">
            <p class="freedomSign_r_title">如果场馆拥有自己的公众号，也可以将会员端的功能嫁接到公众号上使用</p>
            <div class="freedomSign_rbox">
              <div class="freedomSign_r1">
                <div class="freedomSign_r1_num">1</div>
                <div class="freedomSign_r1_fl">
                  <p class="freedomSign_r1_title">进入公众号后台</p>
                  <p class="freedomSign_r1_content">微信公众号后台地址：
                    <a href="http://mp.weixin.qq.com"
                      class="signurl1 bluecolor"
                      target="_blank">http：//mp.weixin.qq.com</a>
                  </p>
                  <p class="freedomSign_r1_content">还未开通微信公众号的用户，点击查看如何开通公众号：
                    <a class="signurl2 bluecolor"
                      @click="wxurl"
                      target="_blank">点这里</a>
                  </p>
                </div>
              </div>
              <div class="freedomSign_r2">
                <div class="freedomSign_r1_num">2</div>
                <div class="freedomSign_r1_fl">
                  <p class="freedomSign_r1_title">设置菜单</p>
                  <p class="content-r-text">1、进入【小程序】</p>
                  <p class="content-r-text">2、在【小程序管理】点击条件操作</p>
                  <p class="content-r-text">3、点击【关联小程序】</p>
                  <div class="freedomSign_r2_img1 new-operation-1"></div>
                  <p class="content-r-text">4、管理员扫码授权</p>
                  <div class="freedomSign_r2_img1 new-operation-2"></div>
                  <p class="content-r-text">5、输入appId:
                    <span style="font-weight: bold">{{memberAppId}}</span>
                  </p>
                  <div class="freedomSign_r2_img1 new-operation-3"></div>
                  <p class="content-r-text">6、等待勤鸟进行审核，待审核通过后方可进行第三步添自定义菜单</p>
                  <div class="freedomSign_r2_img1 new-operation-4"></div>

                </div>
              </div>
              <div class="freedomSign_r3">
                <div class="freedomSign_r1_num">3</div>
                <div class="freedomSign_r1_fl">
                  <p class="freedomSign_r1_title">菜单设置</p>
                  <p class="content-r-text">1、进入【自定义菜单】</p>
                  <p class="content-r-text">2、添加菜单并输入菜单名称，例如课表，教练介绍等</p>
                  <p class="content-r-text">3、菜单内容，选择跳转小程序</p>
                  <p class="content-r-text">4、选择小程序之前关联的小程序，并根据需要复制对应页面路径</p>
                  <p class="freedomSign_r1_title">点击菜单，跳转至商家场馆选择页面，选择场馆跳转至场馆对应页面</p>
                  <div>
                    <div v-for="item in merchantPath"
                      :key="item.name">
                      <p class="content-r-text mg10">【{{item.name}}】</p>
                      <p class="content-r-text mg15">{{item.pagepath}}</p>
                    </div>
                  </div>
                  <br/>
                  <p class="freedomSign_r1_title">点击菜单，跳转至当前场馆对应页面</p>
                  <div>
                    <div v-for="item in menuList"
                      :key="item.name">
                      <p class="content-r-text mg10">【{{item.name}}】</p>
                      <p class="content-r-text mg15">{{item.pagepath}}</p>
                    </div>
                  </div>
                  <br/>
                  <p class="content-r-text">5、填写备用路径，建议填写 https://www.rocketbird.cn</p>
                  <p class="content-r-text">6、点击保存并发布</p>
                  <div class="freedomSign_r2_img1 new-menu"></div>
                </div>
              </div>
            </div>
          </div>
          <!-- 其他二维码 -->
          <div class="freedomSign_r" style="marginLeft: 182px; width:auto;" v-show="activeMenu == 3">
            <div class="freedomSign_rbox" style="paddingBottom: 20px;">
              <div class="freedomSign_r1">
                <div class="freedomSign_r1_num">1</div>
                <div class="freedomSign_r1_fl">
                  <p class="freedomSign_r1_title">退柜二维码</p>
                  <p class="freedomSign_r1_content">会员微信扫描该二维码，可完成退柜</p>
                  <img class="freedomSign_l1_img" :src="counterCode" v-if="counterCode">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="freedom_picbox"
         v-if="picbox">
      <div class="freedom_pic">
        <div class="freedom_pic-t">
          <h2>请选择二维码模板</h2>
          <i @click="cancel">+</i>
        </div>
        <div class="freedom_pic-b">
          <ul>
            <li class="freedom_picList"
                @click="toggleli1(1)"
                v-bind:class="{toggleborder:temp==1}">
              <div class="mc">
                <img src="~assets/img/web-fe-v1/2wm_05.jpg"
                     :class="{picopacity:temp!=1}">
              </div>
            </li>
            <li class="freedom_picList"
                @click="toggleli1(2)"
                v-bind:class="{toggleborder:temp==2}">
              <div class="mc">
                <img src="~assets/img/web-fe-v1/2wm_03.jpg"
                     :class="{picopacity:temp!=2}">
              </div>
            </li>
            <li class="freedom_picList"
                @click="toggleli1(3)"
                v-bind:class="{toggleborder:temp==3}">
              <div class="mc">
                <img src="~assets/img/web-fe-v1/2wm_07.jpg"
                     :class="{picopacity:temp!=3}">
              </div>
            </li>
          </ul>
          <p class="freedom_pic_title">使用相纸打印效果最佳</p>
          <a class="mb-on"
             v-on:click="download"
             download
             :href="href">下载</a>
          <button class="mb-off"
                  @click="cancel">取消
          </button>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
  import { mapState, mapActions } from 'vuex';
  import { getBaseUrl } from 'utils/config';

  export default {
    name: 'manual',
    data() {
      return {
        active: 0,
        tab: 0,
        appId: '',
        memberAppId: '',
        miniUrl: [],
        miniPath: [],
        title_A: true,
        title_B: false,
        title_C: false,
        title_D: false,
        title_E: false,
        pop: true,
        imgA: false,
        imgsrc: '',
        miniSignImg: '',
        aidongMiniSignImg: getBaseUrl() + '/Web/Applet/aidong_qrcode',
        miniCoachSignImg: '',
        picbox: false,
        urllist: {},
        temp: 1,
        href: '#',
        bus_ewmcode: '',
        activeMenu: 1,
        otherMiniSignImg:'',
        counterCode:'',
        menuList: [],
        merchantPath: [{
          "name": "我的",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/my/index')}&from=jiajie`
        },
        {
          "name": "私教预约",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/class/class?type=2')}&from=jiajie`
        },
        {
          "name": "团课预约",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/class/class?type=1')}&from=jiajie`
        },
        {
          "name": "套餐包",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/card/list?type=3')}&from=jiajie`
        },
        {
          "name": "泳教购买",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/card/list?type=2')}&from=jiajie`
        },
        {
          "name": "私教购买",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/card/list?type=1')}&from=jiajie`
        },
        {
          "name": "购卡购课",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/card/list')}&from=jiajie`
        },
        {
          "name": "场馆介绍",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/bus/detail')}&from=jiajie`
        },
        {
          "name": "场内活动",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/activity/index')}&from=jiajie`
        },
        {
          "name": "教练介绍",
          "pagepath": `pages/busSelect?path=${encodeURIComponent('/pages/coach/list')}&from=jiajie`
        }],
      };
    },
    created() {
      this.active = this.$route.query.active || 0;
      this.getMiniProgramUrl();
      this.getMemberProgramUrl();
      this.hosturl();
      this.get_bind_link();
      this.imgsrc = getBaseUrl() + '/Web/Business/getQrCode';
      this.miniSignImg = getBaseUrl() + '/Web/Applet/applet_qrcode';
      this.otherMiniSignImg = getBaseUrl() + '/Web/Applet/qn_plus_qrcode';
      this.miniCoachSignImg = getBaseUrl() + '/Web/CoachSign/coach_sign_qrcode';
      this.href = getBaseUrl() + '/Web/Business/get_bus_sign_qrcode_url/type/mini/temp_id/1';
      this.bus_ewmcode = getBaseUrl() + '/Web/Business/getBusQrCode/';
      this.$service.post('/Web/Business/getQnQrCabinetBackUrl', {
        busId: this.busId
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.counterCode = res.data.data
        } else {
          if(this.$store.state.is_qn_j == 1){
            this.$Message.error(res.data.errormsg)
          }
        }
      })
    },
    route: {
      data(transition) {
        let sign_id = transition.to.query.sign_id;
        if (!sign_id) {
          return;
        } else {
          this.toggleB();
        }
      }
    },
    // 这里有场馆id
    computed: {
      ...mapState(['adminInfo', 'receiptAuth', 'busName', 'busId', 'busList', 'adminName', 'adminId', 'supportPay', 'gatedLaunch']),
    },
    methods: {
      switchTab(index) {
        this.active = index;
      },
      getMiniProgramUrl() {
        const url = '/Web/Applet/get_applet_menu';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.miniUrl = data.list;

            this.miniPath = data.m_list;

            this.miniUrl.forEach(item => {
              item.qrcode = `${getBaseUrl()}/Web/Applet/download_applet_qrcode?filename=${item.name}&path=${encodeURIComponent(item.pagepath)}`
            });

            this.appId = data.list[0].appid;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      // 勤鸟+ 公众号绑定路径接口
      getMemberProgramUrl() {
        const url = '/Web/Applet/get_qn_new_applet_menu';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.menuList = data.m_list;
            this.memberAppId = data.m_list[0].appid;
          } else {
            if(this.$store.state.is_qn_j == 1){
              this.$Message.error(res.data.errormsg);
            }
          }
        });
      },
      // 获取场馆二维码
      toggleA() {
        this.title_A = true;
        this.title_B = false;
        this.title_C = false;
      },
      toggleB() {
        this.title_A = false;
        this.title_B = true;
        this.title_C = false;
      },
      toggleC() {
        this.active = 2;
        this.$nextTick(() => {
          document.getElementById('freedom').scrollTop = 0;
        });
      },
      close() {
        this.pop = false;
      },
      wxurl() {
        window.open('https://mp.weixin.qq.com/cgi-bin/readtemplate?t=register/step1_tmpl&lang=zh_CN');
      },
      hosturl() {
        let hosts = window.location.host;
        let ihost = hosts.split('.')[0];
        if (ihost === 'i-test' || ihost === 'web' || ihost === 'web2') {
          this.imgA = false;
        } else if (ihost === 'i-sim') {
          this.imgA = false;
        } else if (ihost === 'i') {
          this.imgA = true;
        }
      },
      // 加载链接
      get_bind_link() {
        let url = '/Web/Business/get_bind_link';
        this.$service
          .get(url)
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                this.urllist = response.data.data;
              }
            }
          })
          .catch(e => {
            console.error(e);
          });
      },
      // 激活弹窗
      downloadCode() {
        this.picbox = true;
      },
      cancel() {
        this.picbox = false;
      },
      toggleli1(index) {
        console.log(this.tab);
        this.temp = index;
        let temp = this.temp;
        if (this.tab == 0) {
          this.href = getBaseUrl() + '/Web/Business/get_bus_sign_qrcode_url/type/mini/temp_id/' + temp;
        } else {
          this.href = getBaseUrl() + '/Web/Business/get_bus_sign_qrcode_url/temp_id/' + temp;
        }
      },
      download() {
        this.$Message.success('已请求下载，请稍后');
      }
    }
  };
</script>
