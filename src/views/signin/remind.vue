<template>
  <section class="app-main">
    <div class="container">
      <header>
        <h3>用户自主签到提醒 <span v-if="socketNoticeArray && socketNoticeArray.length>0">({{socketNoticeArray.length}}条)</span></h3>
      </header>
      <div class="remind-box">
        <div class="voice-wrap">
          <span>语音提示</span>
          <i-switch v-model="isPlayVoice" @on-change="voiceStatusChange"></i-switch>
        </div>
         <div v-if="showModal" class="sign-box">
        <div from="2" style="min-height: 100px;" v-if="commitItem.user_id">
          <RemindMessage :userId="commitItem.user_id"></RemindMessage>
          <UserInfo :data="noticeUserInfo"></UserInfo>
        </div>
        <div class="newsign-lang">
          <div class="newsign-lang-t">
            会员卡信息
          </div>
          <div class="newsign-lang-b">
            <table class="table" style="border-collapse: collapse" borderColor='#eeeeee' cellSpacing='0' align='center' border='1'>
              <thead>
                <tr>
                  <th>卡种</th>
                  <th width="80">卡号</th>
                  <th>天数</th>
                  <th>有效期</th>
                  <th>状态</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="data in cardData" :key="data.card_id">
                  <td>{{data.cardname}}</td>
                  <td width="80">{{data.card_sn}}</td>
                  <td>
                    总计<span v-if="data.charge_type==1 && data.card_type_id!=3"><span class="red">{{data.all_num}}</span>次</span>
                    <span v-if="data.card_type_id==3"><span class="red">{{data.all_num}}</span>元</span>
                    <span v-if="data.charge_type==2"><span class="red">{{data.all_days}}</span>天</span>, 剩余
                    <span class="red">{{data.overplus}}</span>
                  </td>
                  <td>{{data.validity}}</td>
                  <td :class="{green:data.status!='正常'}">{{data.status}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <brandAdd v-if="noticeUserInfo" @on-enter="commit" :number.sync="commitItem.brand_number"></brandAdd>
        <div class="remind-buttons" v-if="noticeUserInfo">
          <div class="newsign-footer">
            <div class="footer-on" @click="commit">确认无误,下一条</div>
            <div class="footer-off" @click="cancel">信息有误,撤销签到</div>
          </div>
        </div>
      </div>
      <div class="newsign-nodata" v-else>
        <p class="kong">暂无签到消息</p>
        <span class="kongspan">页面关闭后不接收会员自主签到消息提醒</span>
      </div>

      </div>


    </div>
    <!-- 音频播放 -->
    <audio controls="controls" id="audio" style="display:none">
        <source src="/static/media/welcome.mp3" type="audio/mpeg">
      </audio>
  </section>
</template>

<script>
  import socket from 'mixins/socket'
  import brandAdd from 'src/views/signin/components/brandAdd.vue'
  import UserInfo from 'src/views/signin/components/userInfo.vue'
  import RemindMessage from 'components/user/remindMessage.vue'
  import {
    mapState
  } from 'vuex'
  export default {
    name: 'remind',
    props: {},
    mixins: [socket],
    components: {
      UserInfo,
      brandAdd,
      RemindMessage
    },
    data() {
      return {
        maxTime: '0',
        index: '',
        msg: '有新签到消息提醒',
        noticeUserInfo: null,
        cardData: [],
        isEntering: false,
        isFlash: false,
        oTitle: '',
        isPlayVoice: localStorage.getItem('isPlayVoice') !== 'false',
        commitItem: {
          user_id: '',
          symbol: '',
          sign_log_id: '',
          brand_number: []
        }
      }
    },
    computed: {
      ...mapState(['busId', 'busName']),
      ...mapState('websocket', [
        'websocket',
        'socketNoticeArray',
        'socketMsgCount',
        'socketModalShow'
      ]),
      showModal: {
        get() {
          return this.socketModalShow
        },
        set(value) {
          this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', value)
        }
      }
    },
    watch: {
      websocket(val, oldVal) {
        if (val && val !== oldVal) {
          this.websocket.onmessage = this.websocketonmessage
        }
      },
      showModal(val) {
        if (val) {
          this.commitItem.user_id = this.socketNoticeArray[0].user_id
          this.commitItem.sign_log_id = this.socketNoticeArray[0].sign_log_id
          this.commitItem.symbol = this.socketNoticeArray[0].symbol
          this.getUser()
          this.getCardUser()
        } else {
          this.clear()
        }
      },
      socketNoticeArray(val, oldVal) {
        if (val && val.length === 0) {
          this.$Notice.close('socketNotice')
          this.showModal = false
          this.clear()
        }
      },
      'commitItem.sign_log_id' (val, oldVal) {
        if (val && val !== oldVal && this.showModal) {
          this.getUser()
          this.getCardUser()
        }
      }
    },
    created() {
      console.log('remind')
      this.$store.dispatch('getBusInfo').then(res => {
        document.title = `${this.busName}_${this.$route.name}`
      })
    },
    beforeDestroy() {
      this.destroyEvent()
    },
    methods: {
      websocketonmessage(e) {
        //{"user_id":"8289","create_time":1524730365,"sign_log_id":"14279","type":"sign","action":"push_info","symbol":"01|34"}
        //{"admin_id":"1","create_time":1524730191,"msg_count":null,"type":"","action":"push_info","symbol":"01|34"}
        const resdata = JSON.parse(e.data)
        if (
          resdata.action === 'push_info' &&
          resdata.type === 'sign' &&
          resdata.bus_id == this.busId
        ) {
          this.$store.commit(
            'websocket/SET_SOCKET_NOTICE_ARRAY',
            this.socketNoticeArray.concat(resdata)
          )
          this.flash()
          if (localStorage.getItem('isPlayVoice') !== 'false') {
            this.playVoice()
          }
          this.noticeModalShow(resdata)
        } else if (resdata.action == 'push_info' && resdata.type === 'message') {
          localStorage.setItem('socketMsgCount', +resdata.msg_count)
          this.$store.commit('websocket/SET_SOCKET_MSG_COUNT', +resdata.msg_count)
        } else if (resdata.action == 'clean_sign') {
          this.noticeOut(resdata.sign_log_id)
        }
      },
      getUnreadCount() {
        return this.$service
          .post('/Web/Msg/get_unread_count', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              localStorage.setItem('socketMsgCount', +res.data.data.count)
              this.$store.commit(
                'websocket/SET_SOCKET_MSG_COUNT', +res.data.data.count
              )
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      // 音频播放按钮
      playVoice() {
        let audio = document.getElementById('audio')
        audio.play()
      },
      noticeModalShow(resdata) {
        this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', true)
      },
      destroyEvent() {
        this.noticeUserInfo = null
      },
      //用户信息
      getUser() {
        this.$service
          .post(
            '/Web/Member/userMsg', {
              user_id: this.commitItem.user_id
            }, {
              loading: false
            }
          )
          .then(res => {
            if (res.data.errorcode == 0) {
              this.noticeUserInfo = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(function(errerr) {
            console.log(err)
          })
      },
      //用户会员卡列表
      getCardUser() {
        this.$service
          .post(
            '/Web/Member/carduserList', {
              user_id: this.commitItem.user_id
            }, {
              loading: false
            }
          )
          .then(res => {
            if (res.data.errorcode == 0) {
              this.cardData = res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(function(err) {
            console.log(err)
          })
      },
      voiceStatusChange(val) {
        localStorage.setItem('isPlayVoice', val)
      },
      //签到数据提交
      commit() {
        if (this.isEntering) {
          return false
        }
        this.isEntering = true
        let commitUrl = 'Web/Sign/sign_confirm_web'
        let postData = {
          sign_log_id: this.commitItem.sign_log_id,
          symbol: this.commitItem.symbol
        }
        if (
          this.commitItem.brand_number.length > 0 &&
          this.commitItem.brand_number[0] != ''
        ) {
          commitUrl = '/Web/Sign/add_brand_number'
          postData = this.commitItem
        }
        this.$service
          .post(commitUrl, postData)
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              this.next()
            } else {
              this.$Message.error(res.data.errormsg)
            }
            this.isEntering = false
          })
          .catch(err => {
            this.isEntering = false
          })
      },
      //当其它客户端或页面已经确认或取消了某条签到时
      noticeOut(signLogId) {
        //在队列中清除
        for (let index = 0; index < this.socketNoticeArray.length; index++) {
          if (this.socketNoticeArray[index].sign_log_id == signLogId) {
            this.socketNoticeArray.splice(index, 1)
            this.$store.commit(
              'websocket/SET_SOCKET_NOTICE_ARRAY',
              this.socketNoticeArray
            )
            break
          }
        }
        //如果已经展示在页面弹窗上
        if (this.commitItem.sign_log_id == signLogId) {
          this.next()
        }
      },
      //下一条
      next() {
        this.socketNoticeArray.splice(0, 1)
        this.$store.commit(
          'websocket/SET_SOCKET_NOTICE_ARRAY',
          this.socketNoticeArray
        )
        this.noticeUserInfo = null
        this.commitItem = {
          user_id: '',
          sign_log_id: '',
          symbol: '',
          brand_number: []
        }
        if (this.socketNoticeArray.length !== 0) {
          this.commitItem.user_id = this.socketNoticeArray[0].user_id
          this.commitItem.sign_log_id = this.socketNoticeArray[0].sign_log_id
          this.commitItem.symbol = this.socketNoticeArray[0].symbol
        }
      },
      //撤销签到
      cancel() {
        this.$service
          .post('/Web/Sign/cancel_user_sign', {
            sign_log_id: this.socketNoticeArray[0].sign_log_id,
            symbol: this.socketNoticeArray[0].symbol
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success('取消成功')
              this.next()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      //title闪烁
      flash(msg) {
        if (this.isFlash) {
          this.clear() //先停止
        } else {
          this.oTitle = document.title //保存原来的title
        }
        this.isFlash = true
        if (typeof msg == 'undefined') {
          msg = this.msg
        }
        this.message = [msg, this.getSpace(msg)]
        let th = this
        this.timer = setInterval(function() {
          th._flash(msg)
        }, 500)
      },
      _flash(msg) {
        this.index = !this.index ? 1 : 0
        document.title = '【' + this.message[this.index] + '】'
      },
      getSpace(msg) {
        let n = msg.length
        let s = ''
        let num = msg.match(/\w/g)
        let n2 = num != null ? num.length : 0 //半角字符的个数
        n = n - n2 //全角字符个数
        let t = parseInt(n2 / 2)
        if (t > 0) {
          //两个半角替换为一个全角
          n = n + t
        }
        s = n2 % 2 ? ' ' : '' //如果半角字符个数为奇数
        while (n > 0) {
          s = s + '　' //全角空格
          n--
        }
        return s
      },
      clear() {
        clearInterval(this.timer)
        if (this.isFlash) {
          // 如果正在闪
          document.title = this.oTitle //将title复原
        }
        this.isFlash = false
      }
    }
  }
</script>

<style lang="less" scoped>
  .app-main {
    flex: 1;
    overflow-y: scroll;
    background: #f1f3f7;
    box-sizing: border-box;
    height: 100%;
    padding: 40px 48px;
    .container {
      min-height: 100%;
    }
    .remind-box {
      position: relative;
    }
    .sign-box {
      padding: 40px;
    }
  }
  .newsign-lang {
    overflow: hidden;
    clear: both;
    background: #fff;
    border-top: none;
    background: #fff;
    margin-bottom: 40px;
    /*min-height: 100px;*/
  }
  .newsign-lang-b {
    table {
      width: 100%;
      margin: 0 auto;
      font-size: 14px;
      color: #313131;
      td,
      th {
        height: 30px;
        font-weight: normal;
        text-align: center;
        word-break: break-all;
        .red {
          color: #e60012;
        }
        .green {
          color: #5fb75d;
        }
      }
    }
  }
  .newsign-lang-t {
    width: 100%;
    background: #fff;
    height: 40px;
    line-height: 40px;
    overflow: hidden;
    border: 1px solid #eee;
    text-align: center;
    border-left: none;
    border-right: none;
    font-size: 14px;
    font-weight: bold;
  }
  .voice-wrap {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 16px;
    color: #2c3e50;
    span {
      vertical-align: middle;
    }
  }
  .ivu-switch-checked {
    border-color: #5fb75d;
    background-color: #5fb75d;
  }

  .remind-buttons {
    width: 100%;
    height: 65px;
    position: fixed;
    left: 0;
    bottom: 0px;
    background: rgba(0, 0, 0, 0.5);
  }
  .newsign-footer {
    clear: both;
    overflow: hidden;
    width: 380px;
    margin: 0 auto;
  }
  .footer-on {
    clear: both;
    overflow: hidden;
    float: left;
    width: 160px;
    height: 45px;
    background: #00c1c7;
    color: #fff;
    text-align: center;
    line-height: 45px;
    border-radius: 4px;
    cursor: pointer;
    /*margin-left: 40%;*/
    margin-top: 10px;
  }
  .footer-on:hover {
    opacity: 0.8;
  }
  .footer-off {
    float: left;
    margin-left: 40px;
    width: 160px;
    height: 45px;
    border: 1px solid #eb6a62;
    color: #fff;
    background: #eb6a62;
    text-align: center;
    line-height: 45px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
  }
  .footer-off:hover {
    opacity: 0.6;
  }
  .newsign-nodata {
    overflow: hidden;
    width: 100%;
    height: 750px;
    background: #fff;
  }
  .kong {
    display: block;
    overflow: hidden;
    clear: both;
    font-size: 40px;
    width: 100%;
    margin-top: 200px;
    text-align: center;
    font-weight: normal;
  }
  .kongspan {
    display: block;
    overflow: hidden;
    clear: both;
    font-size: 30px;
    width: 100%;
    margin-top: 20px;
    text-align: center;
    font-weight: normal;
  }
</style>
