<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Input style="width: 180px" v-model="postData.user_name" class="option-select" placeholder="姓名" />
      <Input style="width: 180px" v-model="postData.bracelet_number" class="option-select" placeholder="手环号" />
      <Input style="width: 180px" v-model="postData.sn" class="option-select" placeholder="手环序列号" />
      <DatePicker v-model="duringDate" type="daterange" placement="bottom-end" placeholder="选择日期" class="option-select" style="width: 220px" :clearable="false"></DatePicker>
      <Select v-model="postData.action" class="option-select" placeholder="动作" clearable>
        <Option v-for="item in actionList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <Page @on-change="handlePage" :total="total" :current="postData.page_no" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
  </div>
</template>
<script>
import { formatDate } from '@/utils/index'

export default {
  name: 'braceletLogs',
  data() {
    // 动作（1：归还，2：领取）'
    const actionList = [{ value: '', label: '全部' }, { value: 1, label: '归还' }, { value: 2, label: '领取' }]
    // 发放场景（1：自助发放，2：签到发放，3：购票发放，4：自助归还，5：签到归还，6：结算归还，7：前台归还）
    const scenarioList = ['', '自助发放', '签到发放', '购票发放', '自助归还', '签到归还', '结算归还', '前台归还']
    return {
      postData: {
        user_name: '',
        action: '',
        bracelet_number: '',
        sn: '',
        start_time: '',
        end_time: '',
        page_no: 1,
        page_size: 10
      },
      total: 0,
      duringDate: [],
      actionList,
      columns: [
        { title: '序号', type: 'index', width: 60 },
        { title: '姓名', key: 'user_name' },
        {
          title: '动作',
          key: 'action',
          render: (h, params) => {
            let label = '-'
            label = actionList.find(item => item.value == params.row.action).label
            return <span>{label}</span>
          }
        },
        { title: '手环号', key: 'bracelet_number' },
        { title: '手环序列号', key: 'sn' },
        { title: '时间', key: 'action_time' },
        { title: '设备ID', key: 'device_sn' },
        {
          title: '发放场景',
          key: 'scenario',
          render: (h, params) => {
            const idx = Number(params.row.scenario)
            return <span>{scenarioList[idx]}</span>
          }
        },
        {
          title: '操作',
          key: 'option',
          width: 150,
          render: (h, params) => {
            if (params.row.is_return == 2) {
              return <a disabled>还手环</a>
            } else {
              return (
                <div style="display: flex;flex-direction: row;justify-content: space-around;">
                  <a
                    onClick={() => {
                      this.handleReturnBracelet(params.row)
                    }}
                  >
                    还手环
                  </a>
                </div>
              )
            }
          }
        }
      ],
      list: []
    }
  },
  methods: {
    handleSearch() {
      this.postData.page_no = 1
      if (Array.isArray(this.duringDate) && this.duringDate.length === 2 && this.duringDate[0] && this.duringDate[1]) {
        this.postData.start_time = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        this.postData.end_time = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }
      this.getList()
    },
    handlePage(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList() {
      return this.$service.post('/Web/QuickNew/braceletLogList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          if (Array.isArray(res.data.data.list)) {
            this.list = res.data.data.list
          } else {
            this.list = []
          }
          this.total = parseInt(res.data.data.count)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleReturnBracelet(row) {
      return this.$service
        .post('/Web/QuickNew/returnBracelet', {
          quick_new_bracelet_log_id: row.quick_new_bracelet_log_id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
          const self = this
          setTimeout(() => {
            this.getList()
          }, 1000)
        })
    },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    }
  },
  created() {
    // default date is today
    const today = new Date()
    this.postData.start_time = formatDate(today, 'yyyy-MM-dd')
    this.postData.end_time = formatDate(today, 'yyyy-MM-dd')
    this.duringDate = [today, today]
    this.getList()
  }
}
</script>
<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }
    }
  }
}
</style>
