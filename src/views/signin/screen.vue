<template>
  <div :class="mode === 'dark' ? 'box-wrap box-dark' : 'box-wrap box-light'">
    <section class="screen-box" ref="rankBox" :style="{ zoom: screenZoom }">
      <!-- 轮播显示私教教练list -->
      <div class="box-lef">
        <div class="swiper-box" :style="{ height: height }">
          <template v-if="swiperList.length > 0">
            <MySwiper :swiperList="swiperList" :options="swiperOption"></MySwiper>
          </template>
        </div>
        <!-- 显示轮播当前教练的信息 -->
        <div class="info-box">
          <div class="info-avatar">
            <img
              :src="
                swiperActiveInfo.promotional_photo ||
                'https://imagecdn.rocketbird.cn/test/image/992e8001f62c2e32948dc5e44347184f.png'
              "
              :key="key"
            />
          </div>
          <div class="info-course">
            <div class="course-title">
              <img v-show="mode === 'dark'" src="~assets/img/screen/icon_two_light.png" />
              <img v-show="mode === 'light'" src="~assets/img/screen/icon_two_dark.png" />
              <span class="title">可授课程</span>
            </div>
            <div class="course-list" v-if="swiperActiveInfo.permitted_class && swiperActiveInfo.permitted_class.length > 0">
              <div class="course-content" v-for="(ele, key) in swiperActiveInfo.permitted_class" :key="'course_' + key">
                <div class="course-item">
                  <div class="img" :style="ele.thumb ? { backgroundImage: 'url(' + ele.thumb + ')' } : {}"></div>
                  <div>
                    <span>{{ ele.card_name || '暂无课程标题' }}</span>
                    <span>{{ ele.title || '暂无标签' }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="course-none" v-else>暂无可授课程</div>
          </div>
        </div>
      </div>
      <div class="box-rig">
        <!-- 日期显示 -->
        <div class="time-info">
          <span>{{ date || '' }}</span>
          <span>{{ week }}</span>
          <span>{{ time }}</span>
        </div>
        <!-- 显示轮播当前教练的信息 -->
        <div class="train-info">
          <div class="train-introduction">
            <div class="train-position train-info-style">{{ swiperActiveInfo.position || '暂无教练职位' }}</div>
            <div class="train-name train-info-style">{{ swiperActiveInfo.coach_name || '暂无教练姓名' }}</div>
            <div class="train-genius train-info-style">
              <div>擅长</div>
              <div>{{ swiperActiveInfo.specialty || '暂无擅长信息' }}</div>
            </div>
            <div
              class="train-genius train-info-style"
              :style="{ opacity: swiperActiveInfo.aptitude || swiperActiveInfo.other_intr ? '1' : '0' }"
            >
              <div>{{ swiperActiveInfo.aptitude ? '荣誉' : swiperActiveInfo.other_intr ? '介绍' : '荣誉' }}</div>
              <div
                class="train-describe"
                v-html="swiperActiveInfo.aptitude || swiperActiveInfo.other_intr || '暂无教练荣誉'"
              ></div>
            </div>
          </div>
        </div>
        <!-- 今日预约 -->
        <div class="appointment-content">
          <div class="appointment-title">
            <img v-show="mode === 'light'" src="~assets/img/screen/icon_two_dark.png" />
            <img v-show="mode === 'dark'" src="~assets/img/screen/icon_two_light.png" />
            <span class="title">今日预约</span>
          </div>
          <div class="appointment-list">
            <div class="appointment-title-content">
              <div class="appointment-avatar title"></div>
              <div class="appointment-name title">上课教练</div>
              <div class="appointment-class title" style="text-align: center">课程名称</div>
              <div class="appointment-time title">上课时间</div>
              <div class="appointment-member title">上课会员</div>
              <div class="appointment-status title">状态</div>
            </div>
            <div
              class="swiper-appointment"
              v-if="appointmentSwiperList.length > 0"
              :style="{ height: heightRight }"
              :key="hasRest"
            >
              <MySwiperRight :swiperList="appointmentSwiperList" :options="appointmentSwiperOption" :mode="mode"></MySwiperRight>
            </div>
            <div class="swiper-appointment-none" v-else>
              <img
                :src="
                  mode == 'dark'
                    ? 'https://imagecdn.rocketbird.cn/mainsite-fe/screen-none-dark.png'
                    : 'https://imagecdn.rocketbird.cn/mainsite-fe/screen-none-light.png'
                "
              />
              <span>暂无预约</span>
            </div>
          </div>
        </div>
        <div class="mouse-point">
          <img
            src="~assets/img/screen/dark.png"
            @click="
              () => {
                this.mode = this.mode === 'dark' ? 'light' : 'dark'
              }
            "
            v-show="mode === 'dark'"
          />
          <img
            src="~assets/img/screen/light.png"
            @click="
              () => {
                this.mode = this.mode === 'dark' ? 'light' : 'dark'
              }
            "
            v-show="mode === 'light'"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import socket from 'mixins/socket'
import { mapState } from 'vuex'
import { formatDate, toggleFullScreen } from 'utils'
import MySwiper from './components/swiper'
import MySwiperRight from './components/swiperRight'
export default {
  name: 'screen',
  props: {},
  mixins: [socket],
  components: {
    Swiper,
    SwiperSlide,
    MySwiper,
    MySwiperRight,
  },
  data() {
    let that = this
    return {
      // 缩放比例
      screenZoom: 1,
      // 深色dark/浅色light模式
      mode: 'dark',

      swiperList: [],
      swiperActiveIndex: 0,
      swiperActiveInfo: {},
      height: '100%',
      swiperOption: {
        // 垂直显示
        direction: 'vertical',
        // 是否开启循环
        loop: true,
        speed: 2000,
        // 复制的数量 0为首尾各一个
        loopAdditionalSlides: 0,
        // 填充空白slide
        loopFillGroupWithBlank: false,
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 7,
        loopedSlides: 7,
        // slide之间的距离 单位px
        spaceBetween: 38,
        slidesPerGroup: 1,
        // 鼠标移入变为手掌 拖动变为抓手
        grabCursor: true,
        // 开启自动检测数组更新
        observer: true,
        observeParents: true,
        slideToClickedSlide: true,
        watchSlidesProgress: true,
        // 自动切换
        autoplay: {
          // 间隔时间
          delay: 10000,
          // 鼠标移入停止切换
          pauseOnMouseEnter: true,
          disableOnInteraction: false,
        },
        on: {
          slideChange: function () {
            that.swiperActiveIndex = this.activeIndex % that.swiperList.length
            that.swiperActiveInfo = that.swiperList[that.swiperActiveIndex]
            that.key = that.swiperActiveIndex
          },
        },
      },

      appointmentSwiperList: [],
      heightRight: '100%',
      appointmentSwiperOption: {
        // 水平显示
        direction: 'vertical',
        // 是否开启循环
        loop: true,
        speed: 2000,
        // 复制的数量 0为首尾各一个
        loopAdditionalSlides: 0,
        // 填充空白slide
        loopFillGroupWithBlank: false,
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 5,
        // slide之间的距离 单位px
        spaceBetween: 18,
        // 鼠标移入变为手掌 拖动变为抓手
        grabCursor: true,
        // 开启自动检测数组更新
        observer: true,
        observeParents: true,
        autoplay: {
          delay: 1500,
          // loop为false时,此项为true则切换到最后一个时停止
          stopOnLastSlide: false,
          pauseOnMouseEnter: true,
          disableOnInteraction: false,
        },
      },

      date: '',
      week: '',
      time: '',

      // 监控键盘按键定时器
      timer: null,
      // key用于教练信息更新
      key: null,

      // 定时查询右侧轮播数据
      timeInterval: null,
      // hasRest用于右侧轮播信息更新
      hasRest: '1',
    }
  },
  computed: {
    ...mapState(['busId', 'busName']),
    ...mapState('websocket', ['websocket']),
  },
  beforeDestroy() {
    // 解绑键盘按键事件
    document.removeEventListener('keydown', this.handleKeyDown)
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  created() {
    // 实时-时间-计时
    let that = this
    this.timer = setInterval(function () {
      that.getTime()
    }, 1000)
    // 绑定键盘按键事件
    document.addEventListener('keydown', this.handleKeyDown)
    // 新增每次网页只显示一次弹窗提示
    if(!(sessionStorage.getItem('hasScreen')==='true')){
      sessionStorage.setItem('hasScreen', 'true');
      this.$Modal.confirm({
        title: '提示',
        content: '提供两种背景颜色，按下左右箭头可以切换，点击确定投屏展示',
        onOk: () => {
          toggleFullScreen()
        },
      })
    }
    // 设置初始缩放比例
    this.screenZoom = this.setScale()
    // 对window添加resize的监听事件 后续缩放逻辑处理
    window.addEventListener(
      'resize',
      () => {
        this.screenZoom = this.setScale()
      },
      false
    )
    that.getDta()
    this.timeInterval && clearInterval(timeInterval)
    this.timeInterval = setInterval(function () {
      that.getDta(true)
    }, 300000)
  },
  methods: {
    // 计算缩放逻辑
    setScale() {
      let designWidth = 1920 //设计稿的宽度，根据实际项目调整
      let designHeight = 1080 //设计稿的高度，根据实际项目调整
      return document.documentElement.clientWidth / document.documentElement.clientHeight < designWidth / designHeight
        ? document.documentElement.clientWidth / designWidth
        : document.documentElement.clientHeight / designHeight
    },
    // 按键监控事件
    handleKeyDown(e) {
      let event = e || window.event
      let code = event.keyCode || event.which || event.charCode
      // 左/右箭头
      if (code === 37 || code === 39) {
        this.mode = this.mode === 'dark' ? 'light' : 'dark'
      }
    },
    // 格式化富文本
    unescapeHTML(a) {
      a = '' + a
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
        .replace(/<img.*?>/g, '')
        .replace(/&nbsp;/gi, '')
        .replace(/<[^<>]+>/g, '')
    },
    // 获取当前年月日, 星期
    getTime() {
      this.date = formatDate(new Date(), 'yyyy/MM/dd')
      this.week = this.getWeekDate()
      this.time = formatDate(new Date(), 'HH:mm:ss')
    },
    // 当前天的是周几
    getWeekDate() {
      var now = new Date()
      var day = now.getDay()
      var weeks = new Array('星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')
      var week = weeks[day]
      return week
    },
    // 获取数据
    getDta(bool) {
      let that = this
      let url = '/Web/CoachScreen/getCoachPTCourseList'
      let postData = {}
      this.$service
        .post(url, postData, { loading: bool ? false : true })
        .then((response) => {
          if (response.data.errorcode === 0) {
            let { TodayPTScheduleList, CoachList } = response.data.data
            // 格式化
            CoachList.forEach((item) => {
              item.permitted_class = item.permitted_class.length > 0 ? item.permitted_class.slice(0, 4) : []
              item.other_intr = that.unescapeHTML(item.other_intr)
              item.aptitude = that.unescapeHTML(item.aptitude)
            })

            // 设置左侧轮播list
            that.swiperList = CoachList
            // 初次赋值
            that.swiperActiveInfo = CoachList.length > 3 ? CoachList[2] : CoachList.length > 3 ? CoachList[1] : {}
            if (!bool) {
              // 设置高度 如果数量大于6则固定为100%,反之需要计算
              if (CoachList.length > 0 && CoachList.length >= 7) {
                that.height = '100%'
              } else if (CoachList.length > 0 && CoachList.length < 7) {
                let num = 38 * (CoachList.length - 1) + 104 * CoachList.length
                that.height = `${num}px`
              }
              // 设置option
              that.swiperOption = {
                // 垂直显示
                direction: 'vertical',
                // 是否开启循环
                loop: true,
                speed: 2000,
                // 复制的数量 0为首尾各一个
                loopAdditionalSlides: 0,
                // 填充空白slide
                loopFillGroupWithBlank: false,
                // 设置slider容器能够同时显示的slides数量
                slidesPerView: CoachList.length > 7 ? 7 : CoachList.length,
                loopedSlides: CoachList.length > 7 ? 7 : CoachList.length,
                // slide之间的距离 单位px
                spaceBetween: 38,
                slidesPerGroup: 1,
                // 鼠标移入变为手掌 拖动变为抓手
                grabCursor: true,
                // 开启自动检测数组更新
                observer: true,
                observeParents: true,
                slideToClickedSlide: true,
                watchSlidesProgress: true,
                // 自动切换
                autoplay: {
                  // 间隔时间
                  delay: 10000,
                  // 鼠标移入停止切换
                  pauseOnMouseEnter: true,
                  disableOnInteraction: false,
                },
                on: {
                  slideChange: function () {
                    let number = that.swiperList.length > 6 ? 7 : that.swiperList.length
                    that.swiperActiveIndex = (this.activeIndex - number + that.swiperList.length) % that.swiperList.length
                    that.swiperActiveInfo = that.swiperList[that.swiperActiveIndex]
                    that.key = that.swiperActiveIndex
                  },
                },
              }
            }

            // 设置右侧轮播list
            that.appointmentSwiperList = TodayPTScheduleList
            let oldAppointmentSwiperList = that.appointmentSwiperList
            if (oldAppointmentSwiperList.length > 7) {
              // 如果是接收参数则不需要设置swiperOption和height
            } else {
              // 设置高度 如果数量大于6则固定为100%,反之需要计算
              if (TodayPTScheduleList.length > 0 && TodayPTScheduleList.length >= 5) {
                that.heightRight = '100%'
              } else if (TodayPTScheduleList.length > 0 && TodayPTScheduleList.length < 5) {
                let num = 18 * (TodayPTScheduleList.length - 1) + 72 * TodayPTScheduleList.length
                that.heightRight = `${num}px`
              }
              let newOption = {
                // 水平显示
                direction: 'vertical',
                // 是否开启循环
                loop: TodayPTScheduleList.length > 5 ? true : false,
                speed: 2000,
                // 复制的数量 0为首尾各一个
                loopAdditionalSlides: 0,
                // 填充空白slide
                loopFillGroupWithBlank: false,
                // 设置slider容器能够同时显示的slides数量
                slidesPerView: 5,
                // slide之间的距离 单位px
                spaceBetween: 18,
                // 鼠标移入变为手掌 拖动变为抓手
                grabCursor: true,
                // 开启自动检测数组更新
                observer: true,
                observeParents: true,
                autoplay: {
                  delay: 1500,
                  // loop为false时,此项为true则切换到最后一个时停止
                  // stopOnLastSlide: true,
                  pauseOnMouseEnter: true,
                  disableOnInteraction: false,
                },
              }

              // 设置option
              that.appointmentSwiperOption = newOption
            }
            this.hasRest = this.hasRest == '1' ? '2' : '1'
          } else {
            this.$Message.error(response.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
        })
    },
  },
}
</script>
<style lang="less">
body {
  background: none;
}
</style>
<style lang="less" scoped>
.box-wrap {
  width: 100%;
  height: 100%;
}
.screen-box {
  width: 1920px;
  height: 1080px;
  padding: 52px 62px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: relative;
  display: flex;
  .box-lef {
    float: left;
    width: 812px;
    height: 100%;
    margin-right: 60px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .swiper-box {
      width: 100px;
      height: 100%;
    }
    .info-box {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-start;
      position: relative;
      .info-avatar {
        width: 660px;
        height: 660px;
        overflow: hidden;
        border-radius: 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 660px;
          height: 660px;
          border-radius: 10px;
          // animation: imgScale 0.8s ease-in 1;
        }
        // @keyframes imgScale {
        //   0% {
        //     opacity: 1;
        //     transform: translate(-330px, -330px) scale(0.1) rotate(0deg);
        //   }
        //   100% {
        //     opacity: 1;
        //     transform: translateX(0px) scale(1) rotate(0deg);
        //   }
        // }
      }
      .info-course {
        flex: 1;
        width: 660px;
        .course-title {
          width: 100%;
          height: 66px;
          margin: 8px 0;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          img {
            width: 18px;
            height: 24px;
            margin-right: 8px;
          }
          span {
            font-size: 22px;
            font-weight: bold;
            font-family: Source Han Sans CN;
          }
          .dark-title {
            color: #8deb11;
          }
          .light-title {
            color: #000000;
          }
        }
        .course-list {
          height: calc(100% - 82px);
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
          justify-content: flex-start;
          .course-content {
            width: 50%;
            height: 108px;
            padding: 0 6px 0 0;
            box-sizing: border-box;
            .course-item {
              width: 100%;
              height: 100%;
              padding: 20px 22px;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              .img {
                width: 66px;
                height: 68px;
                overflow: hidden;
                margin-right: 18px;
                border-radius: 10px;
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
                background-image: url('https://imagecdn.rocketbird.cn/test/image/9cb7ad210d9faa225bd6a7475665b4a3.png');
              }
              .img + div {
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                span {
                  line-height: normal;
                  width: 100%;
                  font-size: 20px;
                  padding-left: 2px;
                  font-weight: bold;
                  font-family: Source Han Sans CN;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
                span + span {
                  font-size: 18px;
                  margin-top: 12px;
                  font-weight: 400;
                  font-family: PingFang SC;
                }
              }
            }
          }
          & > .course-content:nth-child(2n) {
            padding: 0 0 0 6px;
          }
          & > .course-content:nth-child(n + 3) {
            margin-top: 18px;
          }
        }
        .course-none {
          width: 100%;
          height: calc(100% - 82px);
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          letter-spacing: 1px;
        }
      }
    }
  }
  .box-rig {
    flex: 1;
    height: 100%;
    float: left;
    display: flex;
    flex-direction: column;
    position: relative;
    .time-info {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      top: -30px;
      right: 0;
      span {
        line-height: normal;
        font-size: 22px;
        font-weight: bold;
        font-family: Source Han Sans CN;
      }
      span + span {
        margin-left: 32px;
      }
    }
    .train-info {
      width: 924px;
      height: 346px;
      padding: 12px 0 12px 0;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      overflow: hidden;
      .train-introduction {
        width: 100%;
        height: 100%;
        position: relative;
        padding-right: 126px;
        box-sizing: border-box;
        .train-position {
          font-size: 26px;
          font-weight: bold;
          padding-left: 4px;
          line-height: normal;
          box-sizing: border-box;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .train-name {
          font-size: 72px;
          font-weight: bold;
          padding-left: 2px;
          line-height: normal;
          margin: 10px 0 38px 0;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .train-genius {
          width: 100%;
          display: flex;
          padding-left: 8px;
          div {
            min-width: 48px;
            font-size: 24px;
            font-weight: 400;
            line-height: normal;
            font-family: Source Han Sans CN;
            overflow: hidden;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            /* autoprefixer: ignore next */
            -webkit-box-orient: vertical;
          }
          div + div {
            margin-left: 36px;
            // overflow: hidden;
            // white-space: nowrap;
            // word-wrap: break-word;
            // box-sizing: border-box;
            // text-overflow: ellipsis;
          }
        }
        .train-genius + .train-genius {
          margin-top: 18px;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
        }
        .train-describe {
          flex: 1;
          width: 814px;
          font-size: 24px;
          font-weight: 400;
          margin-left: 36px;
          font-family: Source Han Sans CN;
          overflow: hidden;
          word-wrap: break-word;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          /* autoprefixer: ignore next */
          -webkit-box-orient: vertical;
        }
      }
    }
    .appointment-content {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .appointment-title {
        width: 100%;
        height: 66px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin: 8px 0;
        img {
          width: 18px;
          height: 24px;
          margin-right: 8px;
        }
        span {
          font-size: 22px;
          font-weight: bold;
          line-height: normal;
          font-family: Source Han Sans CN;
        }
      }
      .appointment-list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-sizing: border-box;
        padding: 14px 33px;
        .appointment-title-content {
          width: 100%;
          height: auto;
          padding: 0 10px;
          margin-bottom: 12px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          div {
            font-size: 22px;
            line-height: normal;
            font-weight: 400;
            font-family: Source Han Sans CN;
          }
        }
        .swiper-appointment {
          flex: 1;
          width: 100%;
          overflow: hidden;
          // .swiper-appointment-container {
          //   width: 100%;
          //   height: 100%;
          //   .swiper-appointment-slide {
          //     display: flex;
          //     align-items: center;
          //     justify-content: center;
          //     padding: 0 18px;
          //     box-sizing: border-box;
          //   }
          // }
        }
        .swiper-appointment-none {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          span {
            margin-top: 20px;
            font-size: 30px;
            font-weight: 400;
            font-family: Source Han Sans CN;
          }
        }
        /deep/ .appointment-avatar {
          width: 50px;
          height: 50px;
          overflow: hidden;
          border-radius: 50%;
          img {
            width: 100%;
            height: 100%;
          }
        }
        /deep/ .appointment-name {
          width: 136px;
          margin: 0 16px;
          font-size: 22px;
          font-weight: bold;
          text-align: center;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        /deep/ .appointment-class {
          width: 236px;
          font-size: 22px;
          font-weight: bold;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        /deep/ .appointment-time {
          width: 88px;
          margin: 0 16px;
          font-size: 22px;
          font-weight: bold;
          text-align: center;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        /deep/ .appointment-member {
          width: 136px;
          font-size: 22px;
          font-weight: bold;
          text-align: center;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        /deep/ .appointment-status {
          width: 66px;
          margin-left: 16px;
          font-size: 22px;
          font-weight: bold;
          text-align: center;
          font-family: Source Han Sans CN;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .mouse-point {
      width: 126px;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      padding-top: 43px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      opacity: 0;
      transition-property: opacity;
      transition-duration: 2s;
      img {
        width: 75px;
        height: 75px;
        cursor: pointer;
      }
    }
    .mouse-point:hover {
      opacity: 1;
    }
  }
}
.box-dark {
  background: #070707;
  .screen-box {
    background-image: url(~assets/img/screen/bg_dark.png);
    /deep/ .title {
      color: #8deb11;
    }
    /deep/ .text-color {
      color: #fff;
    }
    .box-lef {
      /deep/ .swiper-slide > div {
        border: 4px solid rgba(#000, 0);
      }
      /deep/ .swiper-slide-active > div {
        border: 4px solid #8deb11;
      }
      .info-box {
        .info-avatar {
          overflow: hidden;
          border: 4px solid #8deb11;
          box-shadow: 0px 4px 17px 1px rgba(141, 235, 17, 0.4);
          background: #000;
          border-radius: 50px;
        }
        .course-item {
          border-radius: 50px;
          border: 2px dashed rgba(#a1ea2b, 0.6);
          span {
            color: #fff;
          }
        }
        .course-none {
          font-weight: 400;
          font-size: 30px;
          color: #8deb11;
          font-family: Source Han Sans CN;
          border-radius: 50px;
          border: 2px dashed rgba(#a1ea2b, 0.6);
        }
      }
      .info-box::before {
        content: '';
        display: block;
        width: 628px;
        height: 644px;
        border: 1px solid #ffffff;
        border-radius: 50px;
        position: absolute;
        top: 10px;
        right: -10px;
        z-index: -1;
      }
    }
    .box-rig {
      .time-info {
        color: #fff;
      }
      .train-genius {
        div {
          color: #8deb11;
        }
        div + div {
          color: #ffffff;
        }
      }
      .train-info-style {
        color: #fff;
      }
      .train-position {
        color: #8deb11;
      }
      .train-name {
        margin-bottom: 60px;
      }
      .train-describe {
        color: #fff;
      }
      .appointment-list {
        border-radius: 50px;
        border: 2px dashed rgba(#a1ea2b, 0.6);
        .swiper-appointment-none {
          span {
            color: #8deb11;
          }
        }
        /deep/ .swiper-appointment-slide {
          // height: 83px;
          // border-radius: 35px;
          // border: 2px solid transparent;
          // background-clip: padding-box, border-box;
          // background-origin: padding-box, border-box;
          // box-shadow: inset 0px 0px 32px 0px rgba(#b3db32, 0.4);
          background-image: url('~assets/img/screen/icon_three_dark.png');
          background-size: 100%;
          background-repeat: no-repeat;
          background-position: center;
          position: relative;
          .appointment-icon {
            width: 59px;
            height: auto;
            position: absolute;
            top: -12px;
            left: 216px;
            z-index: -1;
          }
        }
        // /deep/ .swiper-appointment-slide::after {
        //   content: '';
        //   width: 100%;
        //   height: 100%;
        //   background: #303030;
        //   position: absolute;
        //   z-index: 99;
        //   z-index: -2;
        //   border-radius: 35px;
        // }
        // /deep/ .swiper-appointment-slide::before {
        //   content: '';
        //   width: 150px;
        //   height: 1px;
        //   position: absolute;
        //   top: 2px;
        //   left: 370px;
        //   z-index: 99;
        //   background-clip: padding-box, border-box;
        //   background-origin: padding-box, border-box;
        //   background-image: linear-gradient(to right, rgba(#586b1e, 0.6), rgba(#b3db32, 0.6), rgba(#586b1e, 0.6));
        // }
      }
    }
  }
}
.box-light {
  background-image: linear-gradient(to bottom, #f2e1f1, #cdeaf0);
  .screen-box {
    background-image: url(~assets/img/screen/bg_light.png);
    /deep/ .title {
      color: #000000;
    }
    /deep/ .text-color {
      color: #000;
    }
    .box-lef {
      /deep/ .swiper-slide > div {
        border: 4px solid rgba(#000, 0);
      }
      /deep/ .swiper-slide-active > div {
        border: 4px solid transparent;
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        background-image: linear-gradient(to right, #ebf7f9, #ebf7f9), linear-gradient(90deg, #5ad6f0, #e67ce2);
      }
      .course-item {
        border-radius: 10px;
        border: 2px solid transparent;
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        background-image: linear-gradient(to right, #ebf7f9, #ebf7f9), linear-gradient(90deg, #87eaff, #e78fe2);
        span {
          color: #000;
        }
      }
      .course-none {
        color: #000;
        font-weight: 400;
        font-size: 30px;
        font-family: Source Han Sans CN;
        border-radius: 10px;
        background: #fff;
      }
    }
    .box-rig {
      .time-info {
        color: #000;
      }
      .train-genius {
        div {
          color: #9b9b9b;
        }
        div + div {
          color: #000;
        }
      }
      .train-info-style {
        color: #000;
      }
      .train-name {
        margin-bottom: 30px;
      }
      .train-describe {
        color: #0f0f0f;
      }
      .appointment-list {
        border-radius: 10px;
        background: #ffffff;
        .swiper-appointment-none {
          span {
            color: #000;
          }
        }
        /deep/ .swiper-appointment-slide {
          border-radius: 10px;
          overflow: hidden;
          position: relative;
          .appointment-icon {
            width: 214px;
            height: 58px;
            position: absolute;
            z-index: 99;
            top: 2px;
            left: 240px;
            z-index: -1;
          }
        }
        /deep/ .swiper-appointment-slide::before {
          content: '';
          width: 100%;
          height: 100%;
          background: linear-gradient(to right, #cdeaf0, #f2e1f1);
          position: absolute;
          z-index: -2;
        }
      }
    }
  }
}
</style>
