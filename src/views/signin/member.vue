<template>
  <div>
    <div class="side-right">
      <div class="con cursor sign-ico" :class="{isactive:brandBoxShow}" @click="handleBrand">
        还手环
      </div>
      <div class="con sign-ico ico-singnin" @mouseover="ispop=true" @mouseout="ispop=false">
        自主签到
        <div class='scanqr' v-show="ispop">
          <div class='scanwords'>
            <p class='lineone'>手机签到提升效率</p>
            <p class='linetwo'>会员用微信扫码，即可完成签到</p>
          </div>
          <span class='detailurl' @click="goDetail">了解使用</span>
        </div>
      </div>
      <div class="con" :class="{cursor:socketNoticeArray.length>0}" @click="handleNotice">
        <i-switch v-model="notice" @on-change="noticeStatusChange" size="small"></i-switch>
        <div v-if="socketNoticeArray.length>0" class="badge">{{socketNoticeArray.length}}</div>
        扫码签到通知
      </div>
      <div class="con cursor" @click="handleRank">
        <Icon type="ios-desktop-outline" size="26"></Icon>
        签到投屏
      </div>
      <div class="con cursor" @click="handleScreen">
        教练投屏
      </div>
    </div>
    <!--手环归还弹窗-->
    <div class="brand-box" v-show="brandBoxShow">
      <div class="close" @click="handleBrand">
        <img src="../../assets/img/brand_close.png" alt="">
      </div>
      <cardReader v-if="brandBoxShow" @on-change="onReaderChange" style=""></cardReader>
      <div class="brand-main">
        <Input ref="brandInput" v-model="brandNum" class="head-search" @on-enter="brandReturn(brandNum)" autofocus>
          <span slot="append" @click="brandReturn(brandNum)">归还</span>
        </Input>
        <p class="tips">
          <span>点击下方手环号</span>或将
          <span>手环放在感应器上</span>即可完成手环号归还</p>
        <div class="num-box">
          <span v-for="brand in brandList" :key="brand" @click="brandReturn(brand)">{{brand}}</span>
        </div>
      </div>
    </div>
    <!--列表-->
    <div class="table-wrap">
      <header>
        <Input style="width: 180px" class="notdo-enter" v-model="postData.search" placeholder="姓名/电话/实体卡号"
               @on-enter="search" clearable />
        <Input v-model="postData.number" class="w120 notdo-enter" placeholder="手环号" @on-enter="search"/>
        <Select v-model="postData.class_id" class="w120" filterabl placeholder="全部课程">
          <Option value="0">全部课程</Option>
          <Option v-for="item in classList" :value="item.class_id" :key="item.class_id">{{ item.class_name }}</Option>
        </Select>
        <Select v-model="postData.card_id" class="w120" filterable placeholder="会员卡">
          <Option value="0">会员卡</Option>
          <Option v-for="item in cardList" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
        </Select>
        <!-- <DatePickerWithButton :days.sync="days" :clearable="true" @on-change="dateChanged"></DatePickerWithButton> -->
        <DatePicker format="yyyy-MM-dd" type="daterange" v-model="datePickerVal" :clearable="false" :options="disabledDateOptions" @on-change="dateChanged" />
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>
      <main>
        <Table ref="table" class="avatar-zoom" :columns="columns" :data="tableData" :row-class-name="rowClassName"
               stripe disabled-hover></Table>
      </main>
      <footer>
        <Button type="success" style="margin-right:30px" @click="showSignModal = true">手动签到</Button>
        <Dropdown @on-click="otherCase" placement="top">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0" v-if="notExport && hasExportAuth">导出excel</DropdownItem>
            <!-- <DropdownItem name="1">签到设置</DropdownItem> -->
          </DropdownMenu>
        </Dropdown>
        <Pager :postData="postData" class="page" :total="totalCount" :page-size="postData.page_size" :current.sync="postData.page_no"
            placement="top" @on-change="pageChanged" @on-page-size-change="pageSizeChanged" show-total show-sizer>
        </Pager>
      </footer>
    </div>

    <!--手动签到-->
    <Modal title="签到确认" width="600" class="sign_modal" :mask-closable="false" v-model="showSignModal"
           @on-cancel="closeSignCallBack" :transfer="false">
      <UserSearchNew :from="2" v-if="showSignModal" :search="searchWord" :isUserId="isUserId" @isUserId="(val)=> isUserId = val" @on-change="signUserChanged"></UserSearchNew>
      <div style="min-height: 200px;" v-if="userInfo">
        <RemindMessage :userId="userInfo.user_id"></RemindMessage>
        <Alert type="error" v-show="is_blacklist">此会员在黑名单中</Alert>
        <UserInfo :data="userInfo"></UserInfo>
        <div style="margin-top: 10px">
          <div v-if="bsList&&bsList.length>0||userInfo.cm_list&&userInfo.cm_list.length>0||allCardList&&allCardList.length>0">
            <div>
              <div v-if="bsList&&bsList.length>0" style="padding-top: 10px">
                <div>今日已预约场地</div>
                <CheckboxGroup v-model="signInItem.bookingChecked" :style="styles.chooseReserved">
                  <Checkbox v-for="reserved in bsList" :key="reserved.id"
                            :style="styles.bookingRadio" :label="reserved.id">
                    <p style="margin-left: 10px;">{{ reserved.time_desc }}</p>
                    <p :style="styles.radioPtName">{{reserved.space_name}}</p>
                    <p style="margin-left: 20px">
                      <Icon type="md-person"></Icon>
                      <span>{{ reserved.space_type_name }}</span>
                    </p>
                  </Checkbox>
                </CheckboxGroup>
              </div>
              <div v-if="userInfo.cm_list&&userInfo.cm_list.length>0" style="padding-top: 10px">
                <div>今日已预约课程</div>
                <CheckboxGroup v-model="signInItem.class_mark_id" :style="styles.chooseReserved">
                  <Checkbox v-for="reserved in userInfo.cm_list" :key="reserved.class_mark_id"
                            :style="styles.reservedRadio" :label="reserved.class_mark_id">
                    <p>{{ reserved.beg_time }}</p>
                    <p :style="styles.radioPtName">{{ reserved.card_name }} <br> 预约了{{ reserved.sign_number }}人</p>
                    <p style="margin-left: 20px">
                      <Icon type="md-person"></Icon>
                      <span>{{ reserved.coach_name }}</span>
                    </p>
                  </Checkbox>
                </CheckboxGroup>
              </div>
              <div v-if="showSignModal && userInfo.cm_list.length===0&&bsList.length===0 && allCardList && allCardList.length>0">
                <SignCardList @on-choose="signCardChange" :cardList="allCardList"></SignCardList>
              </div>
            </div>
          </div>
          <p :style="styles.noCardBox" v-else>
            该会员暂未购卡
          </p>
        </div>
      </div>
      <Form v-if="userInfo&&(userInfo.cm_list&&userInfo.cm_list.length>0||allCardList&&allCardList.length>0)"
            ref="signInForm" label-position="left" :model="signInItem" style="padding-top: 20px" :label-width="80">
        <!-- <FormItem :label="chooseCard.card_type_id==2?'扣除次数':'扣除金额'" prop="consumption_form" :rules="{required: true,type: 'string',pattern: /^0*[1-9][0-9]*(.[0-9]+)?|0+.[0-9]*[1-9][0-9]*$/,message: '扣除数量必须为大于0的数字'}" v-if="userInfo.cm_list.length==0&&chooseCard&&chooseCard.card_type_id!=1"> -->
        <FormItem :label="chooseCard.card_type_id==2?'扣除次数':'扣除金额'" prop="consumption_form"
                  :rules="{required: true,type: 'string',pattern: /^10|(\d(\.\d+)?)$/,message: '扣除数量必须为大于0的数字', trigger: 'change'}"
                  v-if="userInfo.cm_list.length==0&&chooseCard&&chooseCard.card_type_id!=1">
          <Input v-model="signInItem.consumption_form" style="width: 320px"></Input>
        </FormItem>
        <brandAdd @on-enter.self="createTips" ref="signBrand" :number.sync="signInItem.brand_number"></brandAdd>
        <FormItem v-if="!!chooseCard && chooseCard.card_type_id != 1" label="打印小票">
          <i-switch size="small" true-value="1" false-value="0" v-model="signInItem.is_print_ticket" />
        </FormItem>
        <FormItem v-if="!!currentCardType && (currentCardType == 2 || currentCardType == 3)" label="打印小票">
          <i-switch size="small" true-value="1" false-value="0" v-model="signInItem.is_print_ticket" />
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons" style="padding-bottom: 20px; padding-top: 10px" v-if="userInfo">
        <Button style="margin: 0 30px" type="success" @click="createTips"
          :disabled="disableSignFlag">确认签到
        </Button>
        <Button @click="closeSignCallBack">取消</Button>
      </div>
      <div slot="footer" v-else></div>
    </Modal>

    <!--领手环-->
    <Modal v-model="isShowAdd" title="手环领取" @on-cancel="closeBrandSure" :mask-closable="false">
      <brandAdd v-if="isShowAdd" @on-enter="addBrandSure" :number.sync="brandItems.brand_number"></brandAdd>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="addBrandSure" :disabled="false">确定</Button>
        <Button @click="closeBrandSure">取消</Button>
      </div>
    </Modal>

    <!--还手环-->
    <Modal v-model="isShowReturn" title="归还手环" :mask-closable="false">
      <Form class="modal-form add-brand" style="padding: 0 10px" :label-width="80">
        <CheckboxGroup v-model="returnBrandItems.brand_number">
          <FormItem label="手环号">
            <Checkbox v-for="item in brandNumbers" :label="item" :key="item"></Checkbox>
          </FormItem>
        </CheckboxGroup>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="retBrandSure" :disabled="false">确定</Button>
        <Button @click="isShowReturn = false">取消</Button>
      </div>
    </Modal>
    <!--签到设置-->
    <Modal v-model="isShowSet" title="签到设置" @on-cancel="cancelSetSave" :mask-closable="false">
      <Form class="modal-form set-form" style="padding: 0 10px" ref="setForm" :model="setData">
        <Form-item>
          <i-switch v-model="setData.is_value_card_sign" true-value="1" false-value="0"></i-switch>
          储值卡可直接扫码扣费进场
        </Form-item>
        <Form-item prop="value_card_sign_price" v-if="setData.is_value_card_sign == 1"
                   :required="setData.is_value_card_sign == 1"
                   :rules="{type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须大于等于0且最只能保留两位小数'}">
          储值卡单次进场扣费
          <Input class="min-input" v-model="setData.value_card_sign_price"/> 元/次
        </Form-item>
        <Form-item prop="allow_user_active_card">
          <i-switch v-model="setData.allow_user_active_card" true-value="1" false-value="0"></i-switch>
          扫码签到时会员可自行启用“请假或未激活”的会员卡
        </Form-item>
        <Form-item v-if="hasLocationAuth" prop="is_bus_location_sign">
          <i-switch v-model="setData.is_bus_location_sign" true-value="1" false-value="0"></i-switch>
          签退和扫码签到必须在场馆周围
        </Form-item>
        <Form-item v-if="setData.is_bus_location_sign == 1 && hasLocationAuth" prop="bus_location_sign_range"
                   :rules="{ type: 'number',min: 200, max: 3000, message: '请输入正确的范围值（200~3000）' }">
          <div style="color:red">
            请确保场馆地图位置定位准确，地图位置可在“场馆”中设置
          </div>
          <div>
            扫码签到范围限定在场馆定位半径
            <Input class="min-input" :required="setData.is_bus_location_sign == 1"
                   v-model="setData.bus_location_sign_range" :number="true"/>米内（200米~3000米）
          </div>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="setSave" :disabled="false">确定</Button>
        <Button @click="cancelSetSave">取消</Button>
      </div>
    </Modal>

    <Modal v-model="isSignActive" title="自动激活">
      <p class="sign-active-modal">会员卡处于<label v-if="chooseCard && chooseCard.status==2">请假中</label><label
        v-else-if="chooseCard && chooseCard.status==3">未激活</label>，签到将自动启用该卡？</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="goSign">确定</Button>
        <Button @click="isSignActive=false">取消</Button>
      </div>
    </Modal>

    <Modal v-model="activateAll" title="自动激活">
      <p class="sign-active-modal">此操作将启用<label>全部</label>请假状态的会员卡，是否启用？</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="activateAllCards">确定</Button>
        <Button @click="activateAll=false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="activateCardEndSuspend" title="自动激活">
      <p class="sign-active-modal">会员卡处于<label>未激活</label>，签到将自动启用该卡，</p><p class="sign-active-modal">同时会员将自动结束请假。</p>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="activateAllCards">确定</Button>
        <Button @click="activateCardEndSuspend=false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
//   import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';
  import { mapState } from 'vuex';
  import { formatDate, getCookie, setCookie, isChinese, SEARCH_HINT, checkRangeLessThan30, SEARCH_DATE_HINT } from 'utils/index.js';
  import UserSearchNew from 'src/components/form/UserSearchNew.vue';
  import UserInfo from './components/userInfo.vue';
  import brandAdd from './components/brandAdd.vue';
  import RemindMessage from 'src/components/user/remindMessage.vue';
  import SignCardList from './components/signCardList.vue';
  import { EventBus } from 'components/EventBus.js';
  import cardReader from 'components/card/cardReader.vue';
  import Pager from 'components/pager';

  export default {
    name: 'memberSignin',
    watch: {
      showSignModal(val) {
        if (!val) {
          this.signInItem = {
            class_mark_id: [],
            user_id: '',
            card_user_id: '',
            sign_number: 1,
            brand_number: [],
            consumption_form: '',
            is_print_ticket: '1',
            bookingChecked: [],
          };
          this.allCardList = []
          this.userInfo = null;
          this.chooseCard = null;
          this.searchWord = '';
          localStorage.setItem('keyEnterNum', '');
        }
      },
      socketModalShow(val) {
        if (val) {
          this.enterType = 3;
        } else {
          this.enterType = 1;
          localStorage.setItem('keyEnterNum', '');
        }
      },
      brandBoxShow(val) {
        if (val) {
          this.enterType = 2;
          this.$nextTick(() => {
            this.$refs.brandInput.focus();
          });
        } else {
          this.enterType = 1;
          localStorage.setItem('keyEnterNum', '');
        }
      },
      $route(val, oldVal) {
        if (val.query.brand_number != oldVal.query.brand_number) {
          this.postData.number = val.query.brand_number ? val.query.brand_number : '';
          this.getSignList();
        } else if (val.query.userId != oldVal.query.userId) {
          this.searchWord = val.query.userId ? val.query.userId : '';
          this.isUserId = val.query.userId ? true : false;
          if (val.query.userId) {
            // 监听 $route 会在私教消课的时候触发改变 query.userId, 遂注释之 - 18.1.16 11:07 linghu
            // this.showSignModal = true;
          }
        }
      },
      noticeType(val) {
        this.$store.commit('websocket/SET_SOCKET_NOTICE_TYPE', val);
      }
    },
    computed: {
      ...mapState(['busName', 'busId']),
      ...mapState('websocket', ['socketNoticeArray', 'socketNoticeType', 'socketModalShow']),
      //新扫码签到通知的方式 0弹窗 2有其它弹窗时红色角标数字
      noticeType() {
        return this.socketModalShow ||
               this.showSignModal ||
               this.brandBoxShow ||
               this.isShowAdd ||
               this.isShowReturn ||
               this.showCancelSig
               ? 2
               : 0;
      },
      handRFID() {
        return this.$store.state.adminInfo.hand_rfid === '1';
      },
      currentCardType() {
        let card_type_id = '';
        if (Array.isArray(this.signInItem.class_mark_id)) {
          this.signInItem.class_mark_id.forEach(id => {
            const card = this.userInfo.cm_list.find(item=>(item.class_mark_id==id));
            if (!!card && card.card_type_id != 1 && card.card_type_id != 4) {
              card_type_id = card.card_type_id;
            }
          });
        }
        return card_type_id;
      },
      disableSignFlag() {
        // ((signInItem.bookingChecked&&signInItem.bookingChecked.length==0)||signInItem.class_mark_id.length==0)&&!signInItem.card_user_id
        if (this.bsList && this.bsList.length > 0) { // 有订场
          if (this.userInfo.cm_list.length > 0) { // 有订场 有约课
            return this.signInItem.bookingChecked.length === 0 && this.signInItem.class_mark_id.length === 0;
          } else { // 有订场 没有约课
            return this.signInItem.bookingChecked.length === 0;
          }
        } else if (this.userInfo.cm_list.length > 0) { // 没有订场 有约课
          return this.signInItem.class_mark_id.length === 0;
        } else { // 没有订场 没有约课
          return !this.signInItem.card_user_id;
        }
      }
    },
    data() {
      return {
        activateCardEndSuspend: false, // 激活该张未激活的卡，并结束会员请假
        activateAll: false, //激活该会员所有卡
        datePickerVal: [formatDate(new Date(Date.now()), 'yyyy-MM-dd'), formatDate(new Date(Date.now()), 'yyyy-MM-dd')],
        isSignActive: false,
        searchWord: this.$route.query.userId || '',
        isUserId: !!this.$route.query.userId,
        brandNumbers: [],
        totalCount: 0,
        notice: true, //扫码签到通知是否开启
        isFocusNow: true, //当前是否需要触发获取焦点
        isPlayVoice: true, //扫码签到声音
        chooseCard: null, //手动签到时当前选中卡
        isShowAdd: false, //添加手环
        isShowReturn: false, //还手环
        showCancelSig: false, //取消签到
        ispop: true, //自主签到提示框显示
        isShowSet: false, //签到设置
        enterType: 1, //全局enter事件的响应方式 0不做操作 1手动签到弹窗 2还手环 3扫码签到确认
        showSignModal: false, //展示手动签到弹窗
        showNoticeModal: false, //展示签到通知弹窗
        brandBoxShow: false, //侧边栏还手环弹框
        isEntering: false,
        hasExportAuth: true, //导出权限
        hasLocationAuth: false, // 管理定位权限
        brandNum: '',
        brandNumArr: [],
        notExport: true,
        days: [Date.now(), Date.now()],
        tableData: [],
        cardList: [],
        classList: [],
        brandList: [],
        allCardList: [],
        signData: '',
        userInfo: null,
        bsList: [],
        hasReservation: 2,
        chooseReserved: '',
        message: null,
        brandItems: {
          user_id: '',
          sign_log_id: '',
          brand_number: ['']
        },
        returnBrandItems: {
          user_id: '',
          sign_log_id: '',
          brand_number: ['']
        },
        signInItem: {
          class_mark_id: [], //预约id 无预约则没有
          user_id: '',
          card_user_id: '',
          sign_number: 1,
          brand_number: [],
          consumption_form: '',
          is_print_ticket: '1',
          bookingChecked: [],
        },

        postData: {
          s_date: '',
          e_date: '',
          search: '',
          number: this.$route.query.brand_number || '',
          class_id: '0',
          card_id: '0',
          page_size: 10,
          page_no: 1
        },
        setData: {
          is_value_card_sign: '0', //储值卡是否支持直接扫码扣费进场
          value_card_sign_price: '', //储值卡直接扫码扣费进场，扣费金额，元/次
          allow_user_active_card: '0', //扫码签到时会员可自行启用“请假或未激活”的会员卡
          is_bus_location_sign: '0', //场馆是否开启扫码签到必须在场馆周围
          bus_location_sign_range: '' //扫码签到范围限定在场馆定位半径，单位米
        },
        styles: {
          noCardBox: {
            minHeight: '100px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '14px',
            fontWeight: 'bold',
            border: '1px solid #ddd',
            color: '#5fb75d'
          },
          chooseReserved: {
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            paddingTop: '5px'
          },
          reservedRadio: {
            height: '50px',
            width: '48%',
            border: '1px solid #5fb75d',
            display: 'flex',
            alignItems: 'center',
            padding: '0 10px'
          },
          bookingRadio: {
            height: '50px',
            width: '80%',
            border: '1px solid #5fb75d',
            display: 'flex',
            alignItems: 'center',
            padding: '0 10px',
            margin: '5px 0'
          },
          radioPtName: {
            width: '40%',
            marginLeft: '10px',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }
        },
        columns: [
          {
            title: '头像',
            className: 'avatar-wrap',
            render: (h, params) => {
              return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.$router.push(`/member/detail/${params.row.user_id}`);
                  }}>
                  <img class="avatar" src={params.row.avatar}/>
                </a>
              );
            }
          },
          {
            title: '姓名',
            key: 'username',
            ellipsis: true,
            render: (h, params) => {
              let sign = params.row;
              let tooltip = '';
              let more = '';
              if (sign.remind.length > 0) {
                let remindName = sign.remind[0].remind_name ? sign.remind[0].remind_name : '未知';
                if (sign.remind.length > 1) {
                  more = (
                    <a
                      class="warn"
                      style="margin-left:5px;"
                      href="javascript:void(0)"
                      onClick={() => {
                        // sessionStorage.setItem("signUserId", sign.user_id)
                        this.$router.push(`/member/detail/${sign.user_id}`);
                      }}>
                      查看更多提醒
                    </a>
                  );
                }
                tooltip = (
                  <tooltip>
                    <icon type="ios-bell" style="color: #f4a627;margin-left:3px"/>
                    <div slot="content" style="white-space:normal">
                      【{sign.remind[0].remind_category == 1
                        ? '提醒'
                        : sign.remind[0].remind_category == 2
                          ? '挂账'
                          : sign.remind[0].remind_category == 3 ? '租柜' : '定金'}】
                      <span style="margin-right:3px;"> {sign.remind[0].create_time}</span>
                      {sign.remind[0].remind_category == 3
                       ? `租${sign.remind[0].locker_id}号柜，截至有效期${sign.remind[0].end_date}`
                       : ''}
                      {sign.remind[0].content ? `，备注：${sign.remind[0].content}` : ''}{' '}
                      {sign.remind[0].remind_category == 4 ? `，收款人：${sign.remind[0].name}` : ''}{' '}
                      {sign.remind[0].remind_name ? `，操作账号：${remindName}` : ''} {more}
                    </div>
                  </tooltip>
                );
              }
              return (
                <div>
                  <a
                    class="link"
                    href="javascript:void(0)"
                    on-click={name => {
                      this.$router.push(`/member/detail/${sign.user_id}`);
                    }}>
                    {sign.username ? sign.username : sign.nickname ? sign.nickname : '未知'}
                  </a>
                  {tooltip}
                </div>
              );
            }
          },
          {
            title: '会员卡',
            key: 'card_name'
          },
          {
            title: '签到人数',
            key: 'sign_number'
          },
          {
            title: '扣费',
            key: 'consumption'
          },
          {
            title: '课程',
            key: 'class_name',
            render: (h, params) => {
              if (!!params.row.class_name && params.row.is_miss == 1) {
                return (
                  <div>
                    {params.row.class_name}
                    <span style="color:#e60012;">(爽约)</span>
                  </div>
                );
              } else {
                return <div>{params.row.class_name}</div>;
              }
            }
          },
          {
            title: '签到方式',
            key: 'type',
            render: (h, params) => {
              return <div>{this.signType(params.row.type)}</div>;
            }
          },
          {
            title: '签到时间',
            key: 'create_time',
            render: (h, params) => {
              return (
                <div>
                  {params.row.create_time == 0 ? '' : formatDate(new Date(params.row.create_time * 1000), 'yyyy-MM-dd HH:mm')}
                </div>
              );
            }
          },
          {
            title: '订单编号',
            key: 'order_sn'
          },
          {
            title: '手环号',
            key: 'brand_number',
            render: (h, params) => {
              if (params.row.brand_number != '') {
                return <span> {params.row.brand_number} </span>;
              } else {
                return <span style="color:#ccc"> {params.row.return_brand_number} </span>;
              }
            }
          },
          {
            title: '离场时间',
            key: 'return_time',
            renderHeader: (h, params) => {
              return (
                <div>
                  离场时间
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      离场时间记录的是归还手环，会员签退、闸机出场的时间
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            },
            render: (h, params) => {
              return (
                <div>
                  {params.row.return_time == 0 ? '' : formatDate(new Date(params.row.return_time * 1000), 'MM-d HH:mm')}
                </div>
              );
            }
          },
          {
            title: '操作',
            width: 210,
            render: (h, params) => {
              const disabledAdd = this.handRFID && params.row.brand_number_arr.length > 0;
              return (
                <div>
                  <a
                    class="mr5"
                    disabled={params.row.status == 1 || params.row.brand_number_count == 5 || disabledAdd}
                    onClick={() => {
                      this.addBrand(params.row);
                    }}>
                    领手环
                  </a>
                  <a
                    class="mr5"
                    disabled={params.row.status == 1 || params.row.brand_number_count == 0}
                    onClick={() => {
                      this.retBrand(params.row);
                    }}>
                    还手环
                  </a>
                  <a
                    disabled={params.row.status == 1}
                    onClick={() => {
                      this.cancelBrandSig(params.row);
                    }}>
                    取消签到
                  </a>
                </div>
              );
            }
          }
        ],
        is_blacklist: false,
        disabledDateOptions: {
          disabledDate (date) {
            // disable after today
            return date && date.valueOf() > Date.now();
          }
        }
      };
    },
    components: {
      UserSearchNew,
      UserInfo,
      RemindMessage,
      SignCardList,
      brandAdd,
      cardReader,
      Pager
    },
    created() {
      this.notice = localStorage.getItem('noticeStatus' + this.busId) !== 'false'
      this.postData.s_date = this.datePickerVal[0];
      this.postData.e_date = this.datePickerVal[1];
      this.postData.class_id = sessionStorage.class_id || '';
      this.postData.card_id = sessionStorage.card_id || '';
      this.getClass();
      this.getCardList();
      this.getExcelAuth();
      let signcodeid = getCookie('signcodeid');
      if (signcodeid) {
        this.ispop = false;
      } else {
        //cookie默认主域名为rocketbird.cn
        setCookie('signcodeid', '1', 3600 * 24 * 365);
        this.ispop = true;
      }
      EventBus.$on('qrSignAllRead', () => {
        this.getSignList();
      });
    },
    mounted() {
      this.setDisabledTitle();
    },
    async activated() {
      this.$store.commit('websocket/SET_SOCKET_NOTICE_TYPE', 0);
      document.addEventListener('keydown', this.handleKeyDown); //绑定
      localStorage.setItem('keyEnterNum', '');
      this.getSignList();
      this.getSignSetting();
      if (this.$route.query.userId) {
        this.showSignModal = true;
      }

      const cn = !!this.$route.query.card_number;
      if (cn) {
        let canReturn = await this.brandReturn(cn, true);
        if (!canReturn) {
          this.showSignModal = true;
          this.searchWord = cn;
        }
      }
    },
    deactivated() {
      sessionStorage.setItem('class_id', '0');
      sessionStorage.setItem('card_id', '0');
      this.destroyEvent();
    },
    beforeDestroy() {
      this.destroyEvent();
    },
    methods: {
      // 激活所有卡（激活此会员）
      activateAllCards() {
        this.goSign();
      },
      onReaderChange(id) {
        this.brandNum = id;
        this.brandReturn(id);
      },
      signType(type) {
        switch (+type) {
          case 1:
            return '扫码签到';
          case 2:
            return '系统代签';
          case 3:
            return '指静脉签到';
          case 4:
            return '智能手环';
          case 5:
            return '人脸识别';
          case 8:
            return '按时计费';
          case 10:
            return '蜻蜓机签到';
          case 12:
            return '微信刷掌';
          case 7:
            return '订场签到';
          case 20:
            return '二维码签到';
          case 21:
            return '刷卡签到';
          default:
            return '手动代签';
        }
      },
      // 获取场馆定位设置权限
      getLocationAuth() {
        const url = '/Web/SignSetting/location_sign_auth';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.hasLocationAuth = true;
            } else {
              this.hasLocationAuth = false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      //销毁全局dom事件等
      destroyEvent() {
        this.$store.commit('websocket/SET_SOCKET_NOTICE_TYPE', 1);
        document.removeEventListener('keydown', this.handleKeyDown); //解绑
      },
      goDetail() {
        window.open('/signin/manual');
      },
      getExcelAuth() {
        this.$service.get('/Web/Sign/user_sign_excel').then(res => {
          if (res.data.errorcode === 40014) {
            this.hasExportAuth = false;
          }
        });
      },
      rowClassName(row, index) {
        if (row.status == 1) {
          return 'disabled';
        }
        return '';
      },
      // getMemberType() {
      //   const cn = localStorage.getItem('keyEnterNum');
      //   return this.$service.post('/Web/San/getUserType', {
      //     card_number: cn,
      //     type: 1
      //   }).then(res => {
      //     if (res.data.errorcode == 0) {
      //       if (res.data.data.jump) {
      //         const routeUrl = this.$router.resolve({path:'/signin/nonMember', query: {card_number: cn}});
      //         window.open(routeUrl.href, '_blank');
      //       } else {
      //         this.goAuto();
      //       }
      //     } else {this.$Message.error(res.data.errormsg);}
      //   });
      // },
      async goAuto() {
         //enterType 全局enter事件的响应方式 0不做操作 1手动签到弹窗 2还手环 3扫码签到确认
        if (
          !this.enterType ||
          event.target.localName == 'input' ||
          this.isShowAdd ||
          this.isShowReturn ||
          this.showCancelSig
        ) {
          localStorage.setItem('keyEnterNum', '');
          return false;
        } else if (this.enterType == 1) {
          //userInfo已经存在 证明弹窗已经打开
          if (this.userInfo) {
            if (localStorage.getItem('keyEnterNum')) {
              this.signInItem.brand_number.push(localStorage.getItem('keyEnterNum'));
            }
            !this.isEntering && this.normalSign();
          } else {
            //先看该手环是否可以归还
            let canReturn = await this.brandReturn(localStorage.getItem('keyEnterNum'), true);
            if (!canReturn) {
              this.showSignModal = true;
              this.searchWord = localStorage.getItem('keyEnterNum');
            }
          }
        } else if (this.enterType == 2) {
          this.brandNum = localStorage.getItem('keyEnterNum');
          !this.isEntering && this.brandReturn(localStorage.getItem('keyEnterNum'));
        } else if (this.enterType == 3) {
          // if (localStorage.getItem('keyEnterNum')) {
          //   this.commitItem.brand_number.push(localStorage.getItem('keyEnterNum'));
          // }
          // !this.isEntering && this.commit();
        }
        localStorage.setItem('keyEnterNum', '');
      },
      handleKeyDown(e) {
        let event = e || window.event;
        let key = event.key;

        if (event.target.localName != 'input' && /^[0-9]*$/.test(key)) {
          let num = localStorage.getItem('keyEnterNum') == null ? '' : localStorage.getItem('keyEnterNum');
          localStorage.setItem('keyEnterNum', num + key);
          if (this.enterType == 1 && this.userInfo) {
            if (this.signInItem.brand_number.length > 0) {
              this.signInItem.brand_number.splice(
                this.signInItem.brand_number.length - 1,
                1,
                this.signInItem.brand_number[this.signInItem.brand_number.length - 1] + key
              );
            } else {
              this.signInItem.brand_number.push(key);
            }
          }
        }

        let num = localStorage.getItem('keyEnterNum') == null ? '' : localStorage.getItem('keyEnterNum');
        if (key === 'Enter') {
          // if (num.length > 5) {
          //   this.getMemberType();
          // } else {
          this.goAuto();
          // }
        }
      },
      //关闭领手环弹窗
      closeBrandSure() {
        this.isShowAdd = false;
        this.brandItems.brand_number = [];
      },
      //领手环
      addBrand(info) {
        this.brandItems.user_id = info.user_id;
        this.brandItems.RFID_id = info.RFID_id;
        this.brandItems.sign_log_id = info.id;
        this.isShowAdd = true;
      },
      //领手环确认
      addBrandSure() {
        if (this.brandItems.brand_number.length == 0 || this.brandItems.brand_number[0] == '') {
          this.$Message.error('请先输入手环号');
          return false;
        }
        if (this.isEntering) {
          return false;
        }
        if (this.brandItems.RFID_id && this.handRFID) {
          this.$Modal.confirm({
            title: '同步RFID',
            content: '该会员名下已绑定过手环，是否进行同步？',
            onOk: () => {
              this.confirmAddBrand();
            },
            onCancel: () => {
              this.isEntering = false;
            }
          });
        } else {
          this.confirmAddBrand();
        }
      },
      confirmAddBrand() {
        this.isEntering = true;
        this.$service.post('/Web/Sign/add_brand_number', this.brandItems).then(res => {
          if (res.data.errorcode === 0) {
            this.isShowAdd = false;
            this.getSignList();
            this.closeBrandSure();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
          this.isEntering = false;
        });
      },
      //还手环
      retBrand(info) {
        this.isShowReturn = true;
        this.returnBrandItems.user_id = info.user_id;
        this.returnBrandItems.sign_log_id = info.id;
        this.returnBrandItems.brand_number = info.brand_number_arr;
        this.brandNumbers = info.brand_number_arr;
      },
      //还手环确认
      retBrandSure() {
        if (this.returnBrandItems.brand_number.length > 0) {
          this.$service.post('/Web/Sign/return_brand_number', this.returnBrandItems).then(res => {
            if (res.data.errorcode === 0) {
              this.isShowReturn = false;
              this.getSignList();
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        } else {
          this.$Message.error('请先选择至少一个手环号');
        }
      },
      //取消签到
      cancelBrandSig(info) {
        this.showCancelSig = true;
        this.$Modal.confirm({
          title: '提示',
          content: '您确定要取消签到吗？',
          onOk: () => {
            if (info.brand_number_arr.length > 0) {
              this.$Message.error('请先归还手环号!');
              return;
            }
            this.$service
              .post('/Web/Sign/cancel_user_sign', {
                sign_log_id: info.id
              })
              .then(res => {
                if (res.data.errorcode == 0) {
                  this.getSignList();
                  this.$Message.success(res.data.errormsg);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              });
            this.showCancelSig = false;
          },
          onCancel() {
            this.showCancelSig = false;
          }
        });
      },
      dateChanged(val) {
        this.postData.s_date = val[0];
        this.postData.e_date = val[1];
      },
      pageChanged(postData) {
        const { s_date, e_date } = postData;
        this.postData = { ...this.postData, ...postData };
        // this.postData = { ...this.postData, ...{ page_no, page_size, s_date, e_date } };
        this.datePickerVal =  [s_date, e_date];
        this.$nextTick(()=> {
            this.getSignList();
        })
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.getSignList();
      },
      search() {
        sessionStorage.setItem('class_id', this.postData.class_id);
        sessionStorage.setItem('card_id', this.postData.card_id);
        this.postData.page_no = 1;
        this.getSignList();
      },
      getClass() {
        this.$service.get('/Web/CourseSchedule/get_add_course_schedule_data').then(res => {
          if (res.data.errorcode == 0) {
            this.classList = res.data.data.class_list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      //关闭取消弹窗回调
      closeSignCallBack() {
        this.showSignModal = false;
        this.userInfo = null;
      },
      // 订场签到
      bookingSign() {
        return this.$service
          .post("/Web/SpaceOrder/userSign", {
            space_order_id: this.signInItem.bookingChecked,
            enter_card_number: this.signInItem.brand_number[0]
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.showSignModal = false;
              this.getSignList()
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      //手动签到
      normalSign() {
        if (this.signInItem.bookingChecked && this.signInItem.bookingChecked.length > 0) {
          this.bookingSign();
          if (this.signInItem.class_mark_id.length === 0) {
            return false;
          }
        }

        if(this.signInItem.consumption_form < 0) {
            this.$Message.error("不可以输入负数！");
            this.signInItem.consumption_form = '';
            return false;
        }
        if (this.chooseCard && this.userInfo.suspend_status == '1') { // 请假中1
          if(this.chooseCard.status == 2) {
            this.activateAll = true;
          } else if(this.chooseCard.status == 3) {
            this.activateCardEndSuspend = true;
          }
        } else if(this.chooseCard && this.userInfo.suspend_status == '0') { // 没请假0
          if(this.chooseCard.status == 3 || this.chooseCard.status == 2) { // 会员没在请假，且选中了请假中或未激活的卡
            this.isSignActive = true;
          } else {
            this.goSign();
          }
        } else if (this.userInfo.suspend_status == '1') {
          this.$Modal.confirm({
            title: "自动激活",
            content: "此操作将启用全部请假状态的会员卡，是否启用？",
            loading: true,
            onOk: () => {
              this.goSign();
              this.$Modal.remove();
            }
          })
        } else {
          this.goSign();
        }
      },
      signSave() {
        this.$service.post('/Web/MemberCard/enableCard', {
          user_id: this.userInfo.user_id,
          card_user_id: this.chooseCard.card_user_id
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            setTimeout(() => {
              this.goSign();
            }, 1000);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      goSign() {
        if (this.isEntering) {
          return false;
        }
        this.isEntering = true;
        try {
          let valid;
          this.$refs.signInForm.validate(val => {
            valid = val;
          });
          if (
            this.userInfo.cm_list.length == 0 &&
            this.chooseCard &&
            (this.chooseCard.card_type_id == 2 || this.chooseCard.card_type_id == 3) &&
            !valid
          ) {
            this.$Message.error('请先正确输入');
            return false;
          }
          if (this.signInItem.brand_number.length && this.signInItem.brand_number[0] && this.userInfo.RFID_id && this.handRFID) {
            this.$Modal.confirm({
              title: '同步RFID',
              content: '该会员名下已绑定过手环，是否进行同步？',
              onOk: () => {
                this.confirmSign();
              },
              onCancel: () => {
                this.isEntering = false;
                this.isSignActive = false;
                this.activateAll = false;
                this.activateCardEndSuspend = false;
              }
            });
          } else {
            this.confirmSign();
          }
        } catch (err) {
          this.isEntering = false;
          this.$Message.error(err.message);
        }
      },
      confirmSign() {
        this.$service.post('/Web/Sign/user_sign', this.signInItem).then(res => {
          if (res.data.errorcode === 0) {
            this.showSignModal = false;
            this.getSignList();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
          this.isEntering = false;
          this.isSignActive = false;
          this.activateAll = false;
          this.activateCardEndSuspend = false;

          if (res.data.errorcode === 0 && this.signInItem.is_print_ticket == 1) {
            if (this.chooseCard) {
              if (this.chooseCard.card_type_id != 1) {
                let arr = [];
                arr.push(res.data.sign_id);
                arr = arr.join(',');
                const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinIds: arr}});
                window.open(routeUrl.href, '_blank');
              }
            } else {
              if (this.currentCardType != 1 && this.currentCardType != 4 && Array.isArray(res.data.sign_ids) && res.data.sign_ids.length > 0) {
                // res.data.sign_ids.forEach(signinId => {
                //   const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinId}});
                //   window.open(routeUrl.href, '_blank');
                // });
                let arr = res.data.sign_ids;
                arr = arr.join(',');
                const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinIds: arr}});
                window.open(routeUrl.href, '_blank');
              }
            }
          }
        });
      },
      handleBrand() {
        this.brandBoxShow = !this.brandBoxShow;
        if (!this.brandBoxShow) {
          this.brandNum = '';
        } else {
          this.getBrandList();
        }
      },
      handleRank() {
        window.open('/rank');
      },
      handleScreen() {
        window.open('/screen');
      },
      //快速还手环showSignAfter 是否之后会弹出签到弹窗
      brandReturn(num, showSignAfter) {
        if (!num) {
          if (!showSignAfter) {
            this.$Message.error('请先填写或感应手环');
          }
          return false;
        }
        if (this.isEntering) {
          return false;
        }
        this.isEntering = true;
        return this.$service
          .post('/Web/Sign/revert_brand_number', {
            brand_number: num,
            unlike: showSignAfter ? true : false
          })
          .then(res => {
            let result = true;
            if (res.data.errorcode == 0) {
              this.brandBoxShow = false;
              this.$Message.success(res.data.errormsg);
              this.getSignList();
            } else {
              if (!showSignAfter) {
                this.$Message.error(res.data.errormsg);
              }
              result = false;
            }
            this.brandNum = '';
            this.isEntering = false;
            return result;
          })
          .catch(err => {
            this.isEntering = false;
            console.error(err);
            return false;
          });
      },
      getCardList() {
        this.$service.get('/Web/Member/get_card').then(res => {
          if (res.data.errorcode == 0) {
            this.cardList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getBrandList() {
        this.$service
          .post('/Web/Sign/brand_number_list', '', {
            loading: false
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.brandList = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getSignList(allPageCount) {
        this.datePickerVal = [this.postData.s_date, this.postData.e_date];
        let postObj = Object.assign({}, this.postData);
        if (allPageCount) {
          postObj.page_size = allPageCount;
          postObj.page_no = 1;
        }

        // if numbers or letters must be more than 3 in length you can request
        if (this.postData.search && !isChinese(this.postData.search)) {
          this.$Message.warning(SEARCH_HINT);
          return;
        }

        // date range must be less than 30 days
        if (this.datePickerVal && !checkRangeLessThan30(this.datePickerVal)) {
          this.$Message.warning(SEARCH_DATE_HINT);
          return;
        }

        return this.$service
          .post('/Web/Sign/pc_sign_log', postObj, { isExport: Boolean(allPageCount) })
          .then(res => {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              if (!allPageCount) {
                this.tableData = data.sign_log_list;
                this.setDisabledTitle();
              }
              this.totalCount = parseInt(data.count);
              return data.sign_log_list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getBSList(user_id) {
        return this.$service
          .post('/Web/Sign/check_space_order', {user_id})
          .then(res => {
            if (res.data.errorcode == 0) {
              this.bsList = res.data.data;
            } else {
              this.bsList = [];
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      //手动签到弹窗会员改变
      async signUserChanged(userInfo) {
        localStorage.setItem('keyEnterNum', '');
        this.signInItem.brand_number = [];
        this.userInfo = userInfo;
        if (this.$route.query.userId) {
          this.isUserId = false;
        }
        if (!userInfo) {
          return false;
        }
        await this.getBSList(userInfo.user_id)

        this.allCardList = userInfo.card_list;
        // 默认选中第一张
        if (userInfo.cm_list.length > 0) {
          //有预约
          this.signInItem = {
            class_mark_id: [],
            brand_number: [],
            bookingChecked: [],
            type: 0
          };
          userInfo.cm_list.map(item => {
            this.signInItem.class_mark_id.push(item.class_mark_id);
          });
        } else if (this.bsList.length === 0) {
          //无预约
          this.signInItem.class_mark_id = [];
          this.signInItem.user_id = userInfo.user_id;
          // userInfo.card_list.length > 0 && this.signCardChange(0);
        }

        this.is_blacklist = userInfo.is_blacklist
        this.signInItem.is_print_ticket = userInfo.is_print_ticket;
      },
      //手动签到弹窗卡改变
      signCardChange(info) {
        let chooseCard = info;
        this.chooseCard = chooseCard;
        this.signInItem.card_user_id = chooseCard.card_user_id;
        if (this.chooseCard.card_type_id == 1) {
          this.signInItem.consumption_form = '0';
        } else if (this.chooseCard.card_type_id == 2) {
          this.signInItem.consumption_form = '1';
        } else if (this.chooseCard.card_type_id == 3) {
          this.signInItem.consumption_form = chooseCard.sign_amount;
        } else {
          this.signInItem.consumption_form = '';
        }
        this.$refs.signBrand && this.$refs.signBrand.focusLast();
      },

      noticeStatusChange(val) {
        localStorage.setItem('noticeStatus' + this.busId, val);
      },

      //右侧边栏点击签到通知
      handleNotice() {
        if (this.noticeType === 0 && this.socketNoticeArray.length > 0) {
          this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', true);
        }
      },
      //获取场馆签到设置
      getSignSetting() {
        this.$service.post('/Web/SignSetting/get_sign_setting', {}, { loading: false }).then(res => {
          if (res.data.errorcode == 0) {
            this.setData = res.data.data.info;
          }
        });
      },
      setSave() {
        this.$refs.setForm.validate(valid => {
          if (valid) {
            this.$service.post('/Web/SignSetting/update_sign_setting', this.setData).then(res => {
              if (res.data.errorcode == 0) {
                this.isShowSet = false;
                this.$Message.success('设置成功!');
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          } else {
            this.$Message.error('请先正确填写数据！');
          }
        });
      },
      cancelSetSave() {
        this.isShowSet = false;
        this.getSignSetting();
      },
      otherCase(val) {
        //导出
        if (val === '0') {
          this.notExport = false;
          this.$service
            .post('Web/Sign/pc_sign_log', { ...this.postData, _export: 1})
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success({
                  content:'导出任务运行中，请稍后到消息中心下载!',
                  duration: 3
                })
                this.notExport = true;
              } else {
                this.notExport = true;
                this.$Message.error(res.data.errormsg)
              }
            })
        } else if (val === '1') {
          //签到设置
          this.getLocationAuth();
          this.isShowSet = true;
        }
      },
      // 设置取消签到 tr 的已取消 title
      setDisabledTitle() {
        setTimeout(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled');
          let disabledTrArr = Array.prototype.slice.call(disabledTr);
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已取消');
          });
        }, 500);
      },
      createTips(){
        if(this.is_blacklist){
          this.$Modal.confirm({
            title: '确认签到?',
            content: '此会员已在门店黑名单中',
            okText: '仍要签到',
            onOk: () => {
              this.normalSign()
            },
          });
        }else{
          this.normalSign()
        }
      },
    }
  };
</script>

<style lang="less" scoped>
  .sign-active-modal {
    font-size: 18px;

    label {
      font-weight: bold;
      color: red;
    }
  }

  .brand-box {
    width: 365px;
    position: absolute;
    z-index: 33;
    right: 55px;
    top: 0;
    padding: 115px 15px 15px;
    height: 100%;
    max-height: 100%;
    box-sizing: border-box;
    background: #fff;
    box-shadow: -3px 0 3px rgba(0, 0, 0, 0.15);
    .brand-main {
      width: 100%;
      height: 100%;
      position: relative;
      overflow-y: auto;
    }
    .close {
      position: absolute;
      left: -16px;
      top: 50%;
      cursor: pointer;
      transform: translateY(-50%);
    }

    .tips {
      color: #434343;
      font-size: 14px;
      margin: 15px 0;
      span {
        color: #3976db;
      }
    }
  }

  .side-right {
    position: absolute;
    right: 0;
    top: 70px;
    height: calc(100% - 70px);
    background: #35495d;
    width: 55px;
    min-height: 500px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    font-size: 16px;
    color: #fff;
    overflow-y: auto;
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    box-sizing: border-box;
    .con {
      width: 55px;
      text-align: center;
      padding: 20px 13.5px;
      position: relative;
      &::after {
        display: block;
        width: 26px;
        height: 1px;
        content: ' ';
        background-color: #3999db;
        position: absolute;
        left: 50%;
        margin-left: -13px;
        bottom: 0;
      }
      &:last-child::after {
        display: none;
      }
    }
    .cursor {
      cursor: pointer;
    }
    .sign-ico::before {
      display: block;
      width: 26px;
      height: 26px;
      content: ' ';
      margin-bottom: 10px;
      background: url('../../assets/img/signin_ico.png') no-repeat;
    }

    .isactive {
      color: #788289;
      &::before {
        background-position: 0 -75px;
      }
    }

    .ico-singnin::before {
      background-position: 0 -38px;
    }

    .badge {
      position: absolute;
      width: 20px;
      height: 20px;
      top: 40px;
      left: 30px;
      background: #ff0000;
      border-radius: 50%;
      color: #fff;
      text-align: center;
      line-height: 20px;
    }
  }
  .side-right::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  .scanqr {
    z-index: 9;
    position: absolute;
    top: 50%;
    right: 55px;
    width: 360px;
    height: 100px;
    border-radius: 10px 0 0 10px;
    background: #35495d;
    transform: translateY(-50%);
  }

  .scanqr .scanwords {
    width: 228px;
    display: block;
    float: left;
  }

  .scanwords .lineone {
    font-family: 微软雅黑;
    font-size: 24px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    margin: 26px 0 0;
  }

  .scanwords .linetwo {
    font-family: 微软雅黑;
    font-size: 14px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    margin: 12px 0 0;
  }

  .detailurl {
    float: right;
    display: block;
    width: 110px;
    height: 40px;
    font-size: 13px;
    color: #fff;
    text-align: center;
    border: 1px solid #5fb75d;
    border-radius: 4px;
    line-height: 40px;
    margin: 30px 0 0;
    cursor: pointer;
    margin-right: 20px;
  }

  .num-box {
    overflow-y: auto;
    span {
      display: inline-block;
      color: #636363;
      font-size: 12px;
      margin-right: 5px;
      padding: 5px 12px;
      border: 1px solid #bfbfbf;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        background-color: #51a4ea;
        color: #fff;
        border-color: #51a4ea;
      }
    }
  }

  .ivu-switch-checked {
    border-color: #5fb75d;
    background-color: #5fb75d;
  }

  .set-form .min-input {
    width: 50px;
    margin-right: 5px;
  }

  .set-form .ivu-switch {
    margin-right: 10px;
  }
</style>
