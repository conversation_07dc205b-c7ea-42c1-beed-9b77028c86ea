<template>
  <div class="table-wrap">
    <header class="pos-rel">
      <Button class="rig-button" @click="goHelp" type="warning">教练自助扫码签到</Button>
      <Select  v-model="postData.class_id" placeholder="课程名称" filterable clearable>
        <Option value="">所有课程</Option>
        <Option v-for="course in classList" :key="course.id" :value="course.id" :label="course.class_name"></Option>
      </Select>
      <Select  v-model="postData.coach_id" placeholder="教练" filterable clearable>
        <Option value="">所有教练</Option>
        <Option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id" :label="item.coach_name"></Option>
      </Select>
      <Date-picker placeholder="选择查询时间段" @on-change="dateChanged" :value="dateRange" type="daterange" :editable="false" clearable @on-clear="dateRange = []" format="yyyy-MM-dd"></Date-picker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <main>
      <Table :columns="columns" :data="tableData" ref="table" stripe  disabled-hover></Table>
    </main>
    <footer>
      <Button @click="otherCase">导出excel</Button>
      <Pager :postData="postData" :total="totalCount" @on-change="pageChange"></Pager>
    </footer>
     <Modal v-model="isShowRecord" title="操作记录">
       <Table :columns="modalColumns" :data="recordTableData" ref="table" stripe  disabled-hover></Table>
       <div slot="footer"></div>
     </Modal>
     <Modal v-model="isShowImg">
       <div class="coach-imgmodal">
         <span>{{imgData.sign_image_num}}人</span>
         <img :src="imgData.sign_image_url" />
       </div>
       <div slot="footer"></div>
     </Modal>
     <Modal v-model="isShowSign" title="签到" :mask-closable="false">
      <Form class="modal-form" style="padding: 0 30px" :label-width="100">
        <Form-item label="课程">
          <Input v-model="signModalData.class_name" placeholder="课程" :disabled="true"/>
        </Form-item>
        <Form-item label="上课教练">
          <Select v-model="signModalData.coach_id" @on-change="coachChange" :label-in-value="true" filterable>
            <Option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id" :label="item.coach_name"></Option>
          </Select>
        </Form-item>
        <Form-item label="实到人数">
          <Input-number :min="1" v-model="signModalData.sign_number" placeholder="签到后可修改"></Input-number>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="addSign">确定</Button>
        <Button @click="isShowSign = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { formatDate } from 'utils';
  import Pager from 'components/pager';

  export default {
    name: 'coachSign',
    data() {
      return {
        postData: {
          class_id: '',
          coach_id: '',
          begin_date: formatDate(new Date(), 'yyyy-MM-dd'),
          end_date: formatDate(new Date(), 'yyyy-MM-dd'),
          page_no: 1,
          page_size: 10
        },
        signModalData: {
          coach_id: '',
          coach_name: '',
          class_id: '',
          class_name: '',
          sign_number: 1,
        },
        imgData: {
          sign_image_url: '',
          sign_image_num: ''
        },
        isShowImg: false,
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')], // 列表数据时间段 默认今天
        coachList: [],
        classList: [],
        isShowRecord: false,
        isShowSign: false,
        totalCount: 0,
        modalColumns:[
          {
            title: '时间',
            key: 'date'
          },
          {
            title: '账号',
            key: 'admin_name'
          },
          {
            title: '操作',
            key: 'content'
          }
        ],
        columns: [
          {
            title: '课程',
            key: 'class_name'
          },
          {
            title: '时间',
            key: 'date_time',
            render: (h, params) => {
              return (<span>{params.row.date_time} {params.row.beg_time}</span>);
            }
          },
          {
            title: '排课教练',
            key: 'class_coach_name'
          },
          {
            title: '预约人数',
            key: 'mark_number',
            render: (h, params) => {
              return (<span>{params.row.class_category == 1 ? '-' : params.row.mark_number}</span>);
            }
          },
          {
            title: '实到人数',
            key: 'sign_number',
            render: (h, params) => {
              let content = (<div class="coach-table-imgwrap">
              {params.row.sign_number}
                <img class="coach-table-img" src={params.row.sign_image_url} onClick={() => {
                    this.showImgModal(params.row)
                  }} />
              </div>)
              return content;
            }
          },
          {
            title: '签到时间',
            key: 'sign_time'
          },
          {
            title: '上课教练',
            key: 'coach_name'
          },
          {
            title: '操作记录',
            key: 'csor_num',
            render: (h, params) => {
              let content = (<i-button ghost type="text"
                    onClick={() => {
                      this.showRecordModal(params.row.coach_sign_id)
                    }}>
                    {params.row.csor_num}条
                  </i-button>)
                if (!params.row.csor_num) {
                  content = (<span>-</span>)
                }
              return content;
            }
          },
          {
            title: '操作',
            key: 'csor_num',
            render: (h, params) => {
              if(params.row.outer_type === 1) {
                return (<div>-</div>)
              } else if(params.row.status == 0 || params.row.status == 3) {
                return (<i-button
                    ghost
                    type="text"
                    onClick={() => {
                      this.doSign(params.row)
                    }}>
                    签到
                  </i-button>)
              } else {
                return (
                  <div style="display: flex; justify-content: space-around">
                    <i-button
                      ghost
                      type="text"
                      onClick={() => {
                        this.doSign(params.row, true)
                      }}>
                      编辑
                    </i-button>
                    <i-button
                      ghost
                      type="text"
                      onClick={() => this.cancelSign(params.row.coach_sign_id)}>
                      取消签到
                    </i-button>
                  </div>
                );
              }
              
            }
          }
        ],
        recordTableData: [],
        tableData: []
      };
    },
    components: {
      Pager
    },
    watch: {
      showSignModal(val) {
        if (!val) {
        }
      }
    },
    computed: {
     
    },
    created() {
      this.getCoachList();
      this.getClassList();
    },
    mounted() {
    },
    activated() {
    },
    methods: {
      goHelp() {
        this.$router.push('/signin/manual?active=4');
      },
      async showRecordModal(coach_sign_id) {
        await this.getRecordList(coach_sign_id)
        this.isShowRecord = true; 
      },
      showImgModal(rowInfo) {
        this.imgData=rowInfo
        this.isShowImg = true;
      },
      pageChange(postData) {
        const { begin_date, end_date } = postData;
        this.dateRange = [begin_date, end_date];
        this.postData = { ...this.postData, ...postData };
        this.getSignList();
      },
      coachChange(obj) {
        this.signModalData.coach_id = obj.value
        this.signModalData.coach_name = obj.label
      },
      getRecordList(coach_sign_id) {
        return this.$service
          .post('/Web/CoachSign/coach_sign_operating_record_list',{ coach_sign_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.recordTableData = res.data.data.list;
            } else {
              this.recordTableData = []
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getCoachList() {
        return this.$service
          .post('/Web/CoachSign/get_bus_coach_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.coachList = res.data.data.list;
            } else {
              throw new Error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getClassList() {
        return this.$service
          .post('/Web/CoachSign/get_bus_class_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.classList = res.data.data;
            } else {
              throw new Error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      dateChanged(val) {
        if (!val[0]) {
          return false;
        }
        let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`;
        let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`;
        this.dateRange = [beginDate, endDate];
        this.postData.begin_date = beginDate;
        this.postData.end_date = endDate;
      },
      doSign(rowInfo, isEdit) {
        let { class_id, class_name, class_category, date_time, beg_time, end_time, course_schedule_id } = {...rowInfo};
        this.signModalData = {
          class_id, class_name, class_category, date_time, beg_time, end_time, course_schedule_id,
          coach_id: isEdit ? rowInfo.coach_id : rowInfo.class_coach_id || '',
          coach_name: isEdit ? rowInfo.coach_name : rowInfo.class_coach_name || '',//TODO  当更新了教练名字时，还是取行信息的排课教练名会不匹配（排课时教练名称已固定）
          sign_number: isEdit ? +rowInfo.sign_number : null,
          coach_sign_id: isEdit ? rowInfo.coach_sign_id : ''
        }
        this.isShowSign = true;        
      },
      addSign() {
        this.$service
          .post('/Web/CoachSign/add_coach_sign', this.signModalData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getSignList()
              this.isShowSign = false
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getSignList();
      },
      clickSignOut(coach_sign_id) {
        this.$service
          .post('/Web/CoachSign/update_coach_sign', { coach_sign_id })
          .then(res => {
            if (res.data.errorcode === 0) {
               this.getSignList()
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      cancelSign(id) {
        this.$Modal.confirm({
          title: '取消签到',
          content: '确认取消这次签到吗？',
          onOk: () => {
            this.clickSignOut(id);
          }
        });
      },
      getSignList() {
        return this.$service
          .post('/Web/CoachSign/get_coach_sign_list', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              let list = res.data.data.list;
              if (!list || !list.length) {
                this.tableData = [];
                return;
              }
              this.totalCount = +res.data.data.count;
              this.tableData = list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      getExportData(isExport) {
        const { coach_id, class_id } = this.postData;
        let postData = {
          coach_id,
          class_id,
          begin_date: this.dateRange[0],
          end_date: this.dateRange[1],
          page_no: 1,
          page_size: this.totalCount,
          isExport: true
        };
        return this.$service
          .post('/Web/CoachSign/get_coach_sign_list', postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              return res.data.data.list.map(item => {
                return Object.assign({}, item, {
                  date_time: `${item.date_time} ${item.beg_time}`
                });
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      async otherCase() {
          let exportData = await this.getExportData(true);
          this.$refs.table.exportCsv({
            filename: `团操课教练签到(${this.dateRange[0]}~${this.dateRange[1]})`,
            columns: this.columns.filter((col, index) => index < 8),
            data: exportData
          });
      }
    }
  };
</script>
<style lang="less">
.coach-imgmodal {
  margin-top: 20px;
  width: 100%;
  position: relative;
  img {
    width: 100%;
  }
  span {
    display: block;
    position: absolute;
    left: 10px;
    top: 10px;
    width: 70px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    color: #fff;
    background: rgba(0, 0, 0, .3);
  }
}
.coach-table-imgwrap {
  position: relative;
}
.coach-table-img {
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  vertical-align: middle;
  width: 30px;
  margin-left: 10px;
}
</style>
<style lang="less" scoped>
.min-img {
  width: 30px;
  margin-left: 10px;
}
.pos-rel {
  position: relative;
}
.rig-button {
  position: absolute;
  right: 15px;
  top: 25px;
}
</style>
