<template>
  <div class="page-list">
    <div class="item" v-for="(item, index) in setting" :key="item.group_id">
      <div class="preview" :class="{'active': item.use_status === '1'}">
        <previewWindow class="window" :value="item.list" onlyWindow pageType="index"></previewWindow>
        <previewWindow class="window bus" :value="item.list" onlyWindow pageType="bus"></previewWindow>
      </div>
      <h3>{{names[index]}}
        <span style="color: hsl(10, 100%, 50%)" v-if="item.has_new === '1'"> (草稿尚未发布)</span>
      </h3>
      <div class="button">
        <Button :to="{path: '/activity/pageConfig/config', query: {group: item.group_id}}" style="margin-right: 30px;">配置</Button>
        <Button v-if="item.use_status === '1'" type="primary">使用中</Button>
        <Button v-else type="error" @click="toggleUse(item)">使用</Button>
      </div>
    </div>
  </div>
</template>

<script>
  import previewWindow from './components/previewWindow.vue';
  export default {
    name: 'pageConfigList',
    components: { previewWindow },
    data() {
      return {
        setting: [],
        names: ['经典模板', '活力模板', '清爽模板', '青春模板', '简约模板', '炫酷模板']
      };
    },
    created() {
      this.getList();
      this.getBusImages();
    },
    methods: {
      getList() {
        const url = '/web/pageDiy/diyList';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.setting = data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      toggleUse(item) {
        const url = '/web/pageDiy/apply';
        this.$service
          .post(url, { group_id: item.group_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.setting.forEach(item => (item.use_status = '0'));
              item.use_status = '1';
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getBusImages() {
        const url = '/Web/Business/get_bus_info';
        return this.$service
          .post(url, { bus_id: this.$store.state.busId })
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                let dt = response.data.data;
                if (dt.length >= 1) {
                  const busImages = dt[0].images;
                  this.$store.commit('SET_BUS_IMAGES', busImages);
                } else {
                  this.$Message.error(response.data.errormsg);
                }
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(error) {
            console.log(error);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .page-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .item {
    width: 49%;
    background-color: #fff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    .preview {
      width: 100%;
      display: flex;
      justify-content: center;
      background-color: #f1f3f7;
      padding: 30px 0;
      position: relative;
      overflow: hidden;
      &.active {
        box-shadow: 0 0 0 2px #52a4ea;
        &::after {
          content: '使用中';
          position: absolute;
          background-color: #52a4ea;
          top: -12px;
          right: -92px;
          color: #fff;
          padding: 25px 90px 10px;
          font-size: 16px;
          transform: rotate(40deg);
        }
      }
      .window {
        zoom: 0.7;
        height: 700px;
        overflow: hidden;
      }
      .bus {
        margin-left: 50px;
      }
    }
    h3 {
      line-height: 40px;
    }
  }
</style>
