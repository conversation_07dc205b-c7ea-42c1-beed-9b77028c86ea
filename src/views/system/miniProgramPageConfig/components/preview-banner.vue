<template>
  <div class="preview-banner">
    <div class="logo">
      <img :src="$store.state.adminInfo.logo" alt="">
      <p>{{$store.state.busName}}</p>
    </div>
    <img v-if="value.type === '0'" :src="busImages[0]" style="width: 100%; height: 210px" alt="">
    <div v-else class="banner-type-1">
      <img :src="busImages[0]" style="grid-column: 1 / 3" alt="">
      <img :src="busImages[1]" alt="">
      <img :src="busImages[2]" alt="">
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'previewBanner',
    props: { value: {}, index: { type: Number } },
    data() {
      return {
        activeImage: 0
      };
    },
    computed: {
      busImages() {
        return this.$store.getters.busImages;
      }
    }
  };
</script>

<style lang="less" scoped>
  .preview-banner {
    display: flex;
    position: relative;
    .logo {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #fff;
      top: 8px;
      left: 12px;
      img {
        height: 44px;
        width: 44px;
        border-radius: 50%;
      }
      p {
        padding: 0px 8px;
        height: 16px;
        line-height: 16px;
        max-width: 100px;
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        margin-top: 6px;
      }
    }
    .banner-type-1 {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 210px 105px;
      grid-gap: 3px;
      img {
        height: 100%;
        width: 100%;
      }
    }
  }
</style>
