<template>
  <div class="preview-side">
    <div v-if="showArrow" class="arrow">
      <div class="arrow-up"></div>
      <div class="line"></div>
    </div>
    <div class="name">
      <slot></slot>
      <div class="icons" v-if="index !== -1">
        <!-- <Icon @click.native="$emit('on-delete', index)" title="删除模块" style="cursor: pointer" type="ios-close-circle-outline" size="20" color="red"></Icon>
        <Icon v-if="index > 0" @click.native="$emit('on-up', index)" title="上移" style="cursor: pointer" class="up" type="arrow-up-a" size="17" color="#fff"></Icon>
        <Icon @click.native="$emit('on-down', index)" title="下移" style="cursor: pointer; transform: rotate(180deg)" class="up" type="arrow-up-a" size="17" color="#fff"></Icon> -->
        <FaIcon @click.native="$emit('on-delete', index)" title="删除模块" style="cursor: pointer" name="times-circle" size="18" color="#fd6a6b" />
        <FaIcon v-if="index > 0" @click.native="$emit('on-up', index)" title="上移" style="cursor: pointer" class="up" name="arrow-circle-up" size="18" color="#23d2c8" />
        <FaIcon @click.native="$emit('on-down', index)" title="下移" style="cursor: pointer" class="up" name="arrow-circle-down" size="18" color="#52a4e9" />
      </div>
    </div>
    <div v-if="showArrow" class="arrow">
      <div class="line"></div>
      <div class="arrow-down"></div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'previewSide',
    props: { showArrow: { type: Boolean, default: true }, index: { type: Number } }
  };
</script>

<style lang="less" scoped>
  .preview-side {
    width: 60px;
    position: absolute;
    right: -60px;
    top: 0;
    height: 100%;
    box-sizing: border-box;
    // padding: 5px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .name {
      color: #333;
      text-align: center;
      position: relative;
      width: 100%;
      .icons {
        position: absolute;
        left: 100%;
        top: 0;
        white-space: nowrap;
        .up {
          // background-color: gray;
        }
      }
    }
    .arrow {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .line {
        flex: 1;
        width: 1px;
        background-color: #333;
      }
      .arrow-up {
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-bottom-color: #333;
      }
      .arrow-down {
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-top-color: #333;
      }
    }
  }
</style>
