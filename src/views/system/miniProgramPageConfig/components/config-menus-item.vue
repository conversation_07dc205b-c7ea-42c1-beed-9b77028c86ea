<template>
  <div class="config-menus-item">
    <div class="icon-buttons">
      <!-- <FaIcon @click.native="$emit('on-delete', index)" name="times-circle" size="24" color="#fd6a6b" style="cursor: pointer" title="删除" /> -->
      <FaIcon v-if="index > 0" @click.native="$emit('on-up', index)" name="arrow-circle-up" size="24" color="#23d2c8" style="cursor: pointer" title="上移" />
    </div>
    <div class="block">
      <IconPicker v-if="showIcon" class="picker" @on-change="icon => value.icon = icon" v-model="value.icon"></IconPicker>
      <Form class="form" :label-width="60">
        <FormItem label="名称">
          <Input v-model="value.title"></Input>
        </FormItem>
        <FormItem label="链接">
          <Select v-model="value.url" @on-change="onLinkChange" labelInValue filterable>
            <Option v-for="(item, index) in urls" :key="index" :value="item.url" :label="item.title"></Option>
          </Select>
        </FormItem>
        <template v-if="from === 'magnet'">
          <FormItem label="字体颜色">
            <Input v-model="value.color" />
            <ColorPicker v-model="value.color" recommend transfer></ColorPicker>
          </FormItem>
          <FormItem label="背景颜色">
            <Input v-model="value.backgroundColor" />
            <ColorPicker v-model="value.backgroundColor" recommend transfer></ColorPicker>
          </FormItem>
          <FormItem label="背景图">
            <div style="width: 250px; display: inline-block">
              <Cropper v-model="value.backgroundImage" width="300px" height="200px">
                <div slot="result" style="font-size: 0; line-height: 0; margin-bottom: 5px; position: relative">
                  <Icon type="md-close-circle" color="red" style="cursor: pointer; position: absolute; right: 46px; top: -4px" size="18" title="删除" v-if="value.backgroundImage" @click.native="value.backgroundImage = ''" />
                  <img style="width: 200px" :src="value.backgroundImage" alt="">
                </div>
              </Cropper>
            </div>
          </FormItem>
        </template>
      </Form>
    </div>
  </div>
</template>

<script>
  import IconPicker from './icon-picker';
  import Cropper from 'components/form/cropperPlus';
  export default {
    name: 'configMenusItem',
    props: {
      value: {},
      urls: {},
      index: { type: Number },
      showIcon: { type: Boolean, default: true },
      from: {}
    },
    components: { IconPicker, Cropper },
    methods: {
      onLinkChange({ value, label }) {
        this.value.title = label;
      }
    }
  };
</script>

<style lang="less" scoped>
  .ivu-input-wrapper,
  .ivu-select {
    width: auto;
    min-width: 200px;
  }
  .ivu-form-item {
    margin: 5px 0;
  }
  .config-menus-item {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    .icon-buttons {
      // width: 80px;
      margin-right: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 20px;
    }
    .block {
      display: flex;
      justify-content: center;
      align-items: center;
      // flex: 1;
      background-color: #f7f9fd;
      padding: 30px 60px;
      min-width: 600px;
    }
    .picker {
      flex: 0;
    }
    .form {
      width: 350px;
      margin-left: 20px;
    }
  }
  @btn-color: #19be6b;
  .upload-btn {
    // margin-left: 80px;
    border: 1px solid @btn-color;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    font-size: 14px;
    display: inline-block;
    cursor: pointer;
    color: @btn-color;
    white-space: nowrap;
    &:hover {
      color: #fff;
      background-color: @btn-color;
    }
  }
</style>
