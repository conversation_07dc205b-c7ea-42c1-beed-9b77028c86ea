<template>
  <Form :label-width="100">
    <FormItem label="选择导航布局">
      <RadioGroup v-model="value.type" @on-change="onTypeChange">
        <Radio label="0" style="text-align: center">1行4列
          <!-- <div :class="{'radio-select': value.type === 0}"><img style="width: 200px" src="~assets/img/rank.jpg" alt=""></div> -->
        </Radio>
        </Radio>
        <Radio label="1" style="text-align: center">1行5列
          <!-- <div :class="{'radio-select': value.type === 1}"><img style="width: 200px" src="~assets/img/rank.jpg" alt=""></div> -->
        </Radio>
        </Radio>
        <Radio label="2" style="text-align: center">2行3列
          <!-- <div :class="{'radio-select': value.type === 2}"><img style="width: 200px" src="~assets/img/rank.jpg" alt=""></div> -->
        </Radio>
        </Radio>
        <Radio label="3" style="text-align: center">2行4列
          <!-- <div :class="{'radio-select': value.type === 3}"><img style="width: 200px" src="~assets/img/rank.jpg" alt=""></div> -->
        </Radio>
        </Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="背景颜色">
      <Input v-model="value.backgroundColor" style="width: 120px" />
      <ColorPicker v-model="value.backgroundColor" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="字体颜色">
      <Input v-model="value.color" style="width: 120px" />
      <ColorPicker v-model="value.color" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="选择功能">
      <Item v-for="(item, index) in value.links" :value="item" :index="index" @on-delete="handleDelete" @on-up="handleUp" :urls="urls" :key="index" />
      <Button v-show="showAddButton" @click="handleAdd" style="margin-left: 80px" >添加功能</Button>
    </FormItem>
    <FormItem>
      <slot></slot>
    </FormItem>
  </Form>
</template>

<script>
  import Item from './config-menus-item';
  import { pathUrls } from '../default.js';

  const LENGTH = [4, 5, 6, 8];

  export default {
    name: 'configMenus',
    props: {
      value: {}
    },
    components: { Item },
    data() {
      return {
        urls: pathUrls
      };
    },
    computed: {
      showAddButton() {
        return this.value.links.length < LENGTH[this.value.type];
      }
    },
    methods: {
      handleDelete(index) {
        this.value.links.splice(index, 1);
      },
      handleUp(index) {
        let links = [...this.value.links];
        const upItem = this.value.links[index];
        const downItem = this.value.links[index - 1];
        links[index] = downItem;
        links[index - 1] = upItem;
        this.$emit('on-change', links);
      },
      newItem() {
        return { title: '无', icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png', url: '' };
      },
      handleAdd() {
        this.value.links.push(this.newItem());
      },
      onTypeChange(type) {
        const arrLength = this.value.links.length;
        if (arrLength < LENGTH[type]) {
          const defaultArr = Array.from({ length: LENGTH[type] - arrLength }, () => {
            return this.newItem();
          });
          this.value.links = this.value.links.concat(defaultArr);
        } else {
          this.value.links.length = LENGTH[type];
        }
      }
    }
  };
</script>

<style scoped>
</style>
