<template>
  <div class="menus" :class="`menus-type-${value.type}`">
    <div :style="{backgroundColor: value.backgroundColor, color: value.color}" class="item" v-for="(item, index) in value.links" :key="index">
      <img :src="item.icon" alt="">
      <div class="title">{{item.title}}</div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'previewMenus',
    props: { value: {}, index: { type: Number } }
  };
</script>

<style lang="less" scoped>
  .menus {
    display: flex;
    &.menus-type-2 {
      flex-wrap: wrap;
      .item {
        width: 33.3%;
      }
    }
    &.menus-type-3 {
      flex-wrap: wrap;
      .item {
        width: 25%;
      }
    }
    .item {
      height: 96px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      img {
        width: 44px;
        height: 44px;
      }
      .title {
        font-size: 13px;
        white-space: nowrap;
      }
    }
  }
</style>
