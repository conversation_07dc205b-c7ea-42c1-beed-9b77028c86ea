<template>
  <Form :label-width="100">
    <FormItem label="选择图片布局">
      <RadioGroup v-model="value.type">
        <Radio label="0" style="text-align: center">大图横向轮播
          <previewBanner :value="previewType0" style="zoom: .7; height: 210px; width: 375px"></previewBanner>
        </Radio>
        <Radio label="1" style="text-align: center">小图横向轮播
          <previewBanner :value="previewType1" style="zoom: .7; height: 315px; width: 375px"></previewBanner>
        </Radio>
      </RadioGroup>
    </FormItem>
    <FormItem>
      <Alert type="warning" style="display: inline-block" showIcon>如您需要更换场馆图片，请在管理-->场馆资料-->编辑场馆中进行相应操作</Alert>
    </FormItem>
    <FormItem>
      <slot></slot>
    </FormItem>
  </Form>
</template>

<script>
  import previewBanner from './preview-banner';
  export default {
    name: 'configBanner',
    components: { previewBanner },
    props: {
      value: {}
    },
    computed: {
      previewType0() {
        return { ...this.value, ...{ type: '0' } };
      },
      previewType1() {
        return { ...this.value, ...{ type: '1' } };
      }
    }
  };
</script>

<style scoped>
  .ivu-radio-group {
    display: flex;
  }
</style>
