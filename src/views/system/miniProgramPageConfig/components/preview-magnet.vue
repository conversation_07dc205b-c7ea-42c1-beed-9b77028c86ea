<template>
  <div ref="menus" class="menus" :class="`menus-type-${value.type}`" :style="{backgroundColor: value.splitColor, gridGap: value.gap + 'px', padding: value.padding + 'px'}">
    <div :style="{ backgroundImage: `url(${item.backgroundImage})`, color: item.color, backgroundColor: item.backgroundColor, textAlign: showIcon ? 'center': 'left'}" class="magnet-item" :class="`item-${index}`" v-for="(item, index) in value.links" :key="index">
      <img v-if="showIcon" :src="item.icon" alt="">
      <div class="title" :style="{ paddingLeft: value.type === '1' ? '0' : showIcon ? '0' : '26px' }">{{item.title}}</div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'previewMagnet',
    props: { value: {}, index: { type: Number } },
    data() {
      return {
        wrapper: null
      };
    },
    watch: {
      'value.links.length'(val) {
        this.dealLayout(val);
      }
    },
    computed: {
      showIcon() {
        return this.value.showIcon === '1';
      }
    },
    mounted() {
      this.wrapper = this.$refs.menus;
    },
    methods: {
      dealLayout(num) {
        if (num < 3) {
          this.wrapper.style.setProperty('--templateRows', '90px');
        } else if (num < 6) {
          this.wrapper.style.setProperty('--templateRows', '90px 90px');
        } else {
          this.wrapper.style.setProperty('--templateRows', '90px 90px 90px');
        }
      }
    }
  };
</script>

<style lang="less" scoped>
  .menus {
    --templateRows: 90px 90px 90px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 90px 90px;

    &.menus-type-0 {
      grid-template-columns: 4fr 3fr 3fr;
      grid-template-rows: var(--templateRows);
      grid-template-areas: 'item0 item1 item1' '. . .' '. . .';
      .item-0 {
        grid-area: item0;
      }
      .item-1 {
        grid-area: item1;
      }
    }
    &.menus-type-1 {
      .magnet-item {
        .title {
          font-size: 13px;
          font-weight: normal;
          text-align: center;
          padding-left: 0;
          width: auto;
        }
      }
    }
    .magnet-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      overflow: hidden;
      img {
        width: 44px;
        height: 44px;
      }
      .title {
        width: 100%;
        font-size: 15px;
        font-weight: bold;
        white-space: nowrap;
      }
    }
  }
</style>
