<template>
  <div class="navigator" :class="`navigator-${value.type}`" :style="{backgroundColor: value.splitColor, gridGap: value.gap + 'px', padding: value.padding + 'px'}">
    <div v-for="(item, index) in value.links" :key="item.title" :class="`nav-item item-${index}`" :style="{backgroundImage: `url(${item.backgroundImage})`, color: item.color}">
      <h3>{{item.title}}</h3>
      <p>{{item.description}}</p>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: 'previewNavigator',
    props: { value: {}, index: { type: Number } }
  };
</script>

<style lang="less" scoped>
  .navigator {
    display: grid;
    width: 100%;
    grid-template-columns: 164px 1fr;
    grid-template-rows: 98px 98px;
    grid-template-areas: 'left .' 'left .';
    &.navigator-1 {
      grid-template-areas: 'left .';
      grid-template-rows: 194px;
    }
    .nav-item {
      box-sizing: border-box;
      padding: 20px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .item-0 {
      grid-area: left;
    }
  }
</style>
