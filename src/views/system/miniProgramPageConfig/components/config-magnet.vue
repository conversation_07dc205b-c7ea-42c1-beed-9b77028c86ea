<template>
  <Form :label-width="100">
    <FormItem label="选择导航布局">
      <RadioGroup v-model="value.type" style="display: flex;" @on-change="onTypeChange">
        <Radio label="0" style="text-align: center">布局1
          <previewMagnet :class="{'radio-select': value.type === '0'}" style="zoom: .8; width: 375px" :value="previewType0"></previewMagnet>
        </Radio>
        <Radio label="1" style="text-align: center">布局2
          <previewMagnet :class="{'radio-select': value.type === '1'}" style="zoom: .8; width: 375px" :value="previewType1"></previewMagnet>
        </Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="边距">
      <Input-number :min="1" v-model="padding"></Input-number> px
    </FormItem>
    <FormItem label="间距">
      <Input-number :min="1" v-model="gap"></Input-number> px
    </FormItem>
    <FormItem label="背景颜色">
      <Input v-model="value.splitColor" style="width: 200px" />
      <ColorPicker v-model="value.splitColor" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="显示图标">
      <RadioGroup v-model="value.showIcon">
        <Radio label="1">是</Radio>
        <Radio label="0">否</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="选择功能">
      <Item v-for="(item, index) in value.links" :value="item" :index="index" from="magnet" :showIcon="value.showIcon === '1'" @on-delete="handleDelete" @on-up="handleUp" :urls="urls" :key="index" />
      <Button v-show="showAddButton" @click="handleAdd" style="margin-left: 80px" >添加功能</Button>
    </FormItem>
    <FormItem>
      <slot></slot>
    </FormItem>
  </Form>
</template>

<script>
  import Item from './config-menus-item';
  import { pathUrls } from '../default.js';
  import previewMagnet from './preview-magnet.vue';

  const LENGTH = [8, 8];

  export default {
    name: 'configMagnet',
    props: {
      value: {}
    },
    components: { Item, previewMagnet },
    data() {
      return {
        urls: pathUrls
      };
    },
    computed: {
      showAddButton() {
        return this.value.links.length < LENGTH[this.value.type];
      },
      previewType0() {
        return { ...this.value, ...{ type: '0' } };
      },
      previewType1() {
        return { ...this.value, ...{ type: '1' } };
      },
      padding: {
        get() {
          return +this.value.padding;
        },
        set(val) {
          this.value.padding = val;
        }
      },
      gap: {
        get() {
          return +this.value.gap;
        },
        set(val) {
          this.value.gap = val;
        }
      }
    },
    methods: {
      handleDelete(index) {
        this.value.links.splice(index, 1);
      },
      handleUp(index) {
        if (index <= 0) return;
        let links = [...this.value.links];
        const upItem = this.value.links[index];
        const downItem = this.value.links[index - 1];
        links[index] = downItem;
        links[index - 1] = upItem;
        this.$emit('on-change', links);
      },
      newItem() {
        return {
          title: '',
          icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
          url: '',
          backgroundImage: '',
          backgroundColor: '#fff',
          color: '#333'
        };
      },
      handleAdd() {
        this.value.links.push(this.newItem());
      },
      onTypeChange(type) {
        const arrLength = this.value.links.length;
        if (arrLength < LENGTH[type]) {
          const defaultArr = Array.from({ length: LENGTH[type] - arrLength }, () => {
            return this.newItem();
          });
          this.value.links = this.value.links.concat(defaultArr);
        } else {
          this.value.links.length = LENGTH[type];
        }
      }
    }
  };
</script>

<style scoped>
.ivu-input-number {
  width: auto;
  min-width: 200px;
}
</style>
