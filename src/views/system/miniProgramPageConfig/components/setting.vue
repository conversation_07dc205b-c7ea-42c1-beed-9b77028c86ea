<template>
  <div class="setting">
    <div class="header">
      <RadioGroup :value="pageType" @on-change="e => $emit('update:pageType', e)" type="button">
        <Radio label="bus">场馆页面</Radio>
        <Radio label="index">训练页面</Radio>
      </RadioGroup>
      <Button style="margin-left: auto; margin-right: 30px" @click="$emit('on-restore')">恢复到默认模板</Button>
      <Button type="success" @click="publish">发布</Button>
    </div>
    <Tabs v-model="tabIndex" class="tabs" type="card" v-show="pageType === 'bus'" :animated="false">
      <TabPane label="顶栏">
        <Form :label-width="100">
          <FormItem label="顶栏背景色">
            <Input v-model="value.bus.navigationBarColor.backgroundColor" style="width: 120px" />
            <ColorPicker v-model="value.bus.navigationBarColor.backgroundColor" recommend transfer></ColorPicker>
          </FormItem>
          <FormItem label="顶栏字体色">
            <RadioGroup v-model="value.bus.navigationBarColor.frontColor">
              <Radio label="#ffffff">白色</Radio>
              <Radio label="#000000">黑色</Radio>
            </RadioGroup>
            <Alert style="display: inline-block" type="warning" show-icon>小程序限制只能白色或黑色</Alert>
          </FormItem>
          <FormItem>
            <Button @click="handleSave('top')" type="success">{{saveText}}</Button>
          </FormItem>
        </Form>
      </TabPane>
      <TabPane v-for="(item, index) in value.bus.modules" :key="index" :label="item.moduleName">
        <configBanner :value="item" v-if="item.name === 'banner' && tabIndex==(index+1)">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </configBanner>
        <configMenus @on-change="links => item.links = links" :value="item" v-if="item.name === 'menus' && tabIndex==(index+1)">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </configMenus>
        <configMagnet @on-change="links => item.links = links" :value="item" v-if="item.name === 'magnet' && tabIndex==(index+1)">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </configMagnet>
        <config-split :value="item" v-if="item.name === 'split' && tabIndex==(index+1)">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </config-split>
        <config-navigator :value="item" v-if="item.name === 'navigator' && tabIndex==(index+1)">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </config-navigator>
        <!-- <configTabBar :value="item" v-if="item.name === 'tabbar'">
          <Button @click="handleSave(item.name)" type="success">{{saveText}}</Button>
        </configTabBar> -->
        <Alert v-if="item.name === 'dynamic'" style="display: inline-block" type="warning" show-icon>本模块将自动加载显示会员动态</Alert>
        <Alert v-if="item.name === 'rank'" style="display: inline-block" type="warning" show-icon>本模块将自动加载显示会员积分排行榜</Alert>
        <config-custom @on-change="type => $emit('on-custom-type-change', type, index)" @on-save="handleSave" :value="item" v-if="item.name === 'custom' && tabIndex==(index+1)" />
      </TabPane>
      <div @click="handleAdd" slot="extra" class="add-module" title="添加模块">
        <Icon type="md-add" size="16" color="red" />
      </div>
    </Tabs>
    <Form :label-width="180" v-show="pageType === 'index'" class="training">
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">场馆图片</p>
          <p class="tip">图片最佳尺寸: 750X885</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div style="width: 350px; display: inline-block">
          <Cropper v-model="value.index.trainingBg" :ratio="750/885" width="350px" height="450px">
            <div slot="result" style="font-size: 0; line-height: 0; margin-bottom: 5px">
              <img style="width: 350px" :src="value.index.trainingBg" alt="">
            </div>
          </Cropper>
        </div>
      </FormItem>
      <FormItem>
        <Button @click="handleSave('trainingBg')" type="success">{{saveText}}</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import configBanner from './config-banner.vue';
  import configMenus from './config-menus.vue';
  import configMagnet from './config-magnet.vue';
  import configSplit from './config-split.vue';
  import configNavigator from './config-navigator.vue';
  import configCustom from './config-custom.vue';

  import Cropper from 'components/form/cropperPlus';

  export default {
    name: 'pageSetting',
    components: {
      configBanner,
      configMenus,
      configMagnet,
      configSplit,
      configNavigator,
      configCustom,
      Cropper
    },
    props: {
      value: {},
      pageType: {}
    },
    data() {
      return {
        saveText: '保存到草稿',
        tabIndex: 0
      };
    },
    watch: {
      'value.bus.navigationBarColor.backgroundColor'(val) {
        this.value.index.navigationBarColor.backgroundColor = val;
      },
      'value.bus.navigationBarColor.frontColor'(val) {
        this.value.index.navigationBarColor.frontColor = val;
      }
    },
    methods: {
      async publish() {
        await this.$parent.handleSave();
        const url = '/web/pageDiy/publish';
        this.$service
          .post(url, { group_id: this.$route.query.group })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.$router.back();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSave(type) {
        this.$emit('on-save');
      },
      handleAdd() {
        this.$emit('on-add-module');
        this.$nextTick(() => {
          this.tabIndex = this.value.bus.modules.length;
        });
      }
    }
  };
</script>

<style>
  .radio-select {
    box-shadow: 0 0 0 2px hsl(200, 100%, 50%);
  }
</style>

<style lang="less" scoped>
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .tabs {
    padding-bottom: 150px;
  }
  .training {
    background-color: #fff;
    padding: 50px 30px 100px;
  }
  .add-module {
    height: 24px;
    width: 24px;
    border: 2px solid red;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-top: 3px;
    cursor: pointer;
  }
  .ivu-tabs-tabpane {
    padding: 50px;
    background-color: #fff;
  }
</style>
