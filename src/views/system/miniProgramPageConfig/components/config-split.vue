<template>
  <Form :label-width="100">
    <FormItem label="间隔颜色">
      <Input v-model="value.backgroundColor" style="width: 120px" />
      <ColorPicker v-model="value.backgroundColor" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="间隔高度">
      <Input-number :min="1" v-model="height"></Input-number> px
    </FormItem>
    <FormItem>
      <slot></slot>
    </FormItem>
  </Form>
</template>

<script>
  export default {
    name: 'configSplit',
    props: { value: {} },
    computed: {
      height: {
        get() {
          return +this.value.height;
        },
        set(val) {
          this.value.height = val;
        }
      }
    }
  };
</script>

<style scoped>
</style>
