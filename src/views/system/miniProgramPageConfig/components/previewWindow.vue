<template>
  <div class="preview-box">
    <div class="item navigation-bar" :style="{backgroundColor: value.bus.navigationBarColor.backgroundColor, color: value.bus.navigationBarColor.frontColor}">
      <h2 class="page-title">运动生活管家</h2>
      <Side v-if="!onlyWindow" :showArrow="false" :index="-1">顶栏</Side>
    </div>
    <img src="~src/assets/img/toggle.png" style="width: 92px; height: 19px; position: absolute; top: 50px; right: 12px; z-index: 1" alt="">
    <div class="item" v-show="pageType === 'bus'" v-for="(item, index) in value.bus.modules" :key="index">
      <Banner v-if="item.name == 'banner' || item.moduleType == 'banner'" :value="item" :index="index">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </Banner>
      <Menus v-if="item.name == 'menus' || item.moduleType == 'menus'" :value="item" :index="index">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </Menus>
      <Magnet v-if="item.name == 'magnet' || item.moduleType == 'magnet'" :value="item" :index="index">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </Magnet>
      <Split v-if="item.name == 'split' || item.moduleType == 'split'" :value="item" :index="index">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index" :showArrow="false">{{item.moduleName}}</Side>
      </Split>
      <Navigator v-if="item.name == 'navigator' || item.moduleType == 'navigator'" :value="item" :index="index">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </Navigator>
      <div v-if="item.name == 'dynamic' || item.moduleType == 'dynamic'" class="item">
        <img style="width: 100%" src="~assets/img/dynamic.jpg" alt="">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </div>
      <div v-if="item.name == 'rank' || item.moduleType == 'rank'" class="item">
        <img style="width: 100%" src="~assets/img/rank.jpg" alt="">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </div>
      <div v-if="item.name == 'image' || item.moduleType == 'image'" class="item">
        <img style="width: 100%" :src="item.imageUrl" alt="">
        <Side v-if="!onlyWindow" @on-delete="handleDeleteModule" @on-up="handleModuleUp" @on-down="handleModuleDown" :index="index">{{item.moduleName}}</Side>
      </div>
      <!-- <TabBar v-if="item.name === 'tabbar'" :value="item"></TabBar> -->
    </div>
    <div v-show="pageType === 'index'">
      <div class="item training">
        <img style="width: 100%" :src="value.index.trainingBg" alt="">
        <img style="width: 100%; position: absolute; bottom: 0" src="~assets/img/page-config-train-1.png" alt="">
      </div>
      <div class="item">
        <img style="width: 100%" src="~assets/img/page-config-train.png" alt="">
      </div>
    </div>
  </div>
</template>

<script>
  import Banner from './preview-banner.vue';
  import Menus from './preview-menus.vue';
  import Magnet from './preview-magnet.vue';
  import Split from './preview-split.vue';
  import Navigator from './preview-navigator.vue';
  import Side from './preview-side';
  // import TabBar from './preview-tabbar';

  export default {
    name: 'previewWindow',
    components: { Banner, Menus, Split, Navigator, Side, Magnet },
    props: {
      value: {},
      pageType: {},
      onlyWindow: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      handleDeleteModule(index) {
        this.$Modal.confirm({
          title: '删除模块',
          content: '点击确定删除该模块',
          onOk: () => {
            let modules = [...this.value[this.pageType].modules];
            modules.splice(index, 1);
            this.$emit('on-change', modules);
          }
        });
      },
      handleModuleUp(index) {
        let modules = [...this.value[this.pageType].modules];
        const upItem = modules[index];
        const downItem = modules[index - 1];
        modules[index] = downItem;
        modules[index - 1] = upItem;
        this.$emit('on-change', modules);
      },
      handleModuleDown(index) {
        let modules = [...this.value[this.pageType].modules];
        if (index === modules.length - 1) return;
        const downItem = modules[index];
        const upItem = modules[index + 1];
        modules[index] = upItem;
        modules[index + 1] = downItem;
        this.$emit('on-change', modules);
      }
    }
  };
</script>

<style lang="less">
  .page-title {
    font-weight: normal;
    height: 38px;
    line-height: 38px;
    font-size: 14px;
    padding: 0 15px;
  }
  .preview-box {
    position: relative;
    .item {
      max-width: 375px;
      width: 24vw;
      background-color: #fff;
      position: relative;
    }
  }
</style>
