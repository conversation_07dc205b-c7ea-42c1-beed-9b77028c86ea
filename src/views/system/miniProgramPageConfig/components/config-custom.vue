<template>
  <Form :label-width="180">
    <FormItem label="模块类型">
      <Select
        v-model="value.moduleType"
        labelInValue
        filterable
        @on-change="onTypeChange">
        <Option value="image" label="个性图片"></Option>
        <Option value="banner" label="场馆展示"></Option>
        <Option value="menus" label="宫格导航"></Option>
        <Option value="magnet" label="磁贴导航"></Option>
        <Option value="navigator" label="区块导航"></Option>
        <Option value="split" label="间隔"></Option>
        <Option value="dynamic" label="会员动态"></Option>
        <Option value="rank" label="排行榜"></Option>
      </Select>
    </FormItem>
    <FormItem label="模块名称">
      <Input v-model="value.moduleName"></Input>
    </FormItem>
    <template v-if="value.moduleType === 'image'">
      <FormItem>
        <div slot="label" class="image-description">
          <p class="label">图片</p>
          <p class="tip">图片最佳尺寸: 无</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div style="width: 350px; display: inline-block">
          <Cropper v-model="value.imageUrl">
            <div slot="result" style="font-size: 0; line-height: 0; margin-bottom: 5px">
              <img style="width: 350px" :src="value.imageUrl" alt="">
            </div>
          </Cropper>
        </div>
      </FormItem>
      <FormItem label="链接">
        <Select v-model="value.linkUrl" filterable>
          <Option
            v-for="(item, index) in urls"
            :key="index"
            :value="item.url"
            :label="item.title"></Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
      </FormItem>
    </template>
    <!-- 会员动态 -->
    <FormItem v-if="value.moduleType === 'dynamic'">
      <Alert style="display: inline-block" type="warning" show-icon>本模块将自动加载显示会员动态</Alert>
      <Button style="display: block; margin-top: 30px" type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </FormItem>
    <!-- 排行榜 -->
    <FormItem v-if="value.moduleType === 'rank'">
      <Alert style="display: inline-block" type="warning" show-icon>本模块将自动加载显示会员魅力值排行榜</Alert>
      <Button style="display: block; margin-top: 30px" type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </FormItem>
    <configBanner v-if="value.moduleType === 'banner'" :value="value">
      <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </configBanner>
    <config-menus v-if="value.moduleType === 'menus'" :value="value" @on-change="links => value.links = links">
      <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </config-menus>
    <config-split v-if="value.moduleType === 'split'" :value="value">
      <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </config-split>
    <config-navigator v-if="value.moduleType === 'navigator'" :value="value">
      <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </config-navigator>
    <config-magnet v-if="value.moduleType === 'magnet'" :value="value" @on-change="links => value.links = links">
      <Button type="success" @click="$emit('on-save', value.moduleType)">{{ saveText }}</Button>
    </config-magnet>
  </Form>
</template>

<script>
  import Cropper from 'components/form/cropperPlus';
  import configBanner from './config-banner.vue';
  import configMenus from './config-menus.vue';
  import configSplit from './config-split.vue';
  import configNavigator from './config-navigator.vue';
  import configMagnet from './config-magnet.vue';

  import { pathUrls } from '../default.js';
  export default {
    name: 'ConfigCustom',
    components: {
      Cropper,
      configBanner,
      configMenus,
      configSplit,
      configNavigator,
      configMagnet
    },
    props: { value: {} },
    data() {
      return {
        saveText: '保存到草稿',
        urls: pathUrls
      };
    },
    methods: {
      onTypeChange({ value, label }) {
        this.value.moduleName = label;
        this.$emit('on-change', value);
      }
    }
  };
</script>

<style scoped>
  .ivu-input-wrapper,
  .ivu-select {
    width: auto;
    min-width: 200px;
  }
</style>
