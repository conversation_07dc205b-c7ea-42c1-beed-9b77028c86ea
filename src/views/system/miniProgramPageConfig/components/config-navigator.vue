<template>
  <Form :label-width="100">
    <FormItem label="选择导航布局">
      <RadioGroup v-model="value.type" @on-change="onTypeChange">
        <Radio label="0">3个</Radio>
        <Radio label="1">2个</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="边距">
      <Input-number :min="1" v-model="padding"></Input-number> px
    </FormItem>
    <FormItem label="间距">
      <Input-number :min="1" v-model="gap"></Input-number> px
    </FormItem>
    <FormItem label="背景颜色">
      <Input v-model="value.splitColor" style="width: 120px" />
      <ColorPicker v-model="value.splitColor" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="编辑布局内容">
      <Item v-for="(item, index) in value.links" :key="index" :value="item" :fixedNumber="fixedNumber[index]" :urls="urls" style="margin: 30px 0">{{item.title}}</Item>
      <Button v-show="showAddButton" @click="handleAdd" style="margin-left: 80px" >添加内容</Button>
    </FormItem>
    <FormItem>
      <slot></slot>
    </FormItem>
  </Form>
</template>

<script>
  import Item from './config-navigator-item';
  import { pathUrls } from '../default.js';

  const LENGTH = [3, 2];

  export default {
    name: 'configNavigator',
    props: { value: {} },
    components: { Item },
    data() {
      return {
        urls: pathUrls
      };
    },
    computed: {
      showAddButton() {
        return this.value.links.length < LENGTH[this.value.type];
      },
      padding: {
        get() {
          return +this.value.padding;
        },
        set(val) {
          this.value.padding = val;
        }
      },
      gap: {
        get() {
          return +this.value.gap;
        },
        set(val) {
          this.value.gap = val;
        }
      },
      fixedNumber() {
        return this.value.type === '0' ? [[375, 370], [375, 180], [375, 180]] : [[375, 370], [375, 370]];
      }
    },
    methods: {
      newItem() {
        return {
          title: '标题',
          description: '描述',
          url: '',
          backgroundImage: '',
          color: '#333'
        };
      },
      handleAdd() {
        this.value.links.push(this.newItem());
      },
      onTypeChange(type) {
        const arrLength = this.value.links.length;
        if (arrLength < LENGTH[type]) {
          const defaultArr = Array.from({ length: LENGTH[type] - arrLength }, () => {
            return this.newItem();
          });
          this.value.links = this.value.links.concat(defaultArr);
        } else {
          this.value.links.length = LENGTH[type];
        }
      }
    }
  };
</script>

<style scoped>
</style>
