<template>
  <Form :label-width="60" class="form">
    <FormItem label="标题">
      <Input v-model="value.title"></Input>
    </FormItem>
    <FormItem label="描述">
      <Input v-model="value.description"></Input>
    </FormItem>
    <FormItem label="链接">
      <Select v-model="value.url" filterable>
        <Option v-for="(item, index) in urls" :key="index" :value="item.url" :label="item.title"></Option>
      </Select>
    </FormItem>
    <FormItem label="字体颜色">
      <Input v-model="value.color" style="width: 120px" />
      <ColorPicker v-model="value.color" recommend transfer></ColorPicker>
    </FormItem>
    <FormItem label="背景图">
      <div style="width: 250px; display: inline-block">
        <Cropper v-model="value.backgroundImage">
          <div slot="result" style="font-size: 0; line-height: 0; margin-bottom: 5px">
            <img style="width: 250px" :src="value.backgroundImage" alt="">
          </div>
        </Cropper>
      </div>
    </FormItem>
  </Form>
</template>

<script>
  import Cropper from 'components/form/cropperPlus';
  export default {
    name: 'configNavigatorItem',
    props: { value: {}, urls: {}, fixedNumber: Array },
    components: { Cropper }
  };
</script>

<style lang="less" scoped>
  .ivu-input-wrapper,
  .ivu-select {
    width: auto;
    min-width: 200px;
  }
  .ivu-form-item {
    margin: 5px 0;
  }
  .form {
    background-color: #f7f9fd;
    padding: 30px 60px;
    width: auto;
    max-width: 600px;
  }
  @btn-color: #19be6b;
  .upload-btn {
    // margin-left: 80px;
    border: 1px solid @btn-color;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    font-size: 14px;
    display: inline-block;
    cursor: pointer;
    color: @btn-color;
    &:hover {
      color: #fff;
      background-color: @btn-color;
    }
  }
</style>
