<template>
  <div class="page-config">
    <div class="page-preview">
      <header>效果预览</header>
      <previewWindow v-if="setting.bus" :value="setting" :pageType="pageType" @on-change="handleChange"></previewWindow>
    </div>
    <Setting v-if="setting.bus" :value="setting" :pageType.sync="pageType" class="setting" @on-add-module="handleAddModule" @on-custom-type-change="handleCustomTypeChange" @on-save="handleSave" @on-restore="handleRestore"></Setting>
  </div>
</template>

<script>
  import Setting from './components/setting.vue';
  import previewWindow from './components/previewWindow.vue';
  import defaultSettings, { customizedModule } from './default.js';

  export default {
    name: 'config',
    components: { previewWindow, Setting },
    data() {
      return {
        setting: {},
        pageType: 'bus',
        keepLinks: false
      };
    },
    created() {
      this.getInfo();
    },
    methods: {
      handleAddModule() {
        this.setting[this.pageType].modules.push({
          ...customizedModule.init,
          ...customizedModule.moduleTypes.image()
        });
      },
      handleRestore() {
        this.$Modal.confirm({
          title: '恢复默认',
          content: '点击确定恢复到默认模板',
          onOk: () => {
            this.$store.commit('SET_LOADING', true);
            this.$store.commit('SET_LOADING_TEXT', '般若波罗蜜...');
            setTimeout(() => {
              this.keepLinks = true;
              const index = this.$route.query.group;
              this.setting = defaultSettings[index]();
              this.$nextTick(() => {
                this.keepLinks = false;
                this.$store.commit('SET_LOADING', false);
                this.$store.commit('SET_LOADING_TEXT', '');
                this.$Message.success('已为你恢复到默认模板');
              });
            }, 1000);
          }
        });
      },
      handleCustomTypeChange(type, index) {
        if (this.keepLinks) return;
        let modules = [...this.setting[this.pageType].modules];
        modules[index] = {
          ...customizedModule.init,
          ...customizedModule.moduleTypes[type](),
          ...{ moduleName: modules[index].moduleName }
        };
        this.setting[this.pageType].modules = modules;
      },
      handleChange(modules) {
        this.keepLinks = true;
        this.setting[this.pageType].modules = modules;
        this.$nextTick(() => {
          this.keepLinks = false;
        });
      },
      getInfo() {
        const url = '/web/pageDiy/getInfo';
        this.$service
          .post(url, { group_id: this.$route.query.group })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (!data.bus.modules) data.bus.modules = [];
              this.setting = data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSave() {
        const url = '/web/pageDiy/save';
        const postData = {
          group_id: this.$route.query.group,
          diy_data: this.setting
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .page-config {
    display: flex;

    .page-preview {
      padding-right: 128px;
      // position: sticky;
      // top: 0;
      // align-self: flex-start;
      // height: calc(~"100vh - 170px");
      // overflow-y: scroll;
      // overscroll-behavior: contain;
      header {
        text-align: center;
        font-size: 14px;
        padding: 5px 0;
      }
    }

    .setting {
      flex: 1;
      overflow-x: scroll;
    }
  }
</style>
