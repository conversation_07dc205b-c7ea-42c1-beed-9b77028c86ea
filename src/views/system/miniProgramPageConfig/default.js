export const sideName = {
  banner: '场馆展示',
  menus: '宫格导航',
  split: '间隔',
  navigator: '区块导航',
  dynamic: '会员动态',
  rank: '排行榜',
  custom: '自定义模块',
  image: '个性图片',
  magnet: '磁贴导航'
};

export const customizedModule = {
  init: {
    name: 'custom',
    moduleName: sideName['custom']
  },
  moduleTypes: {
    image: () => {
      return {
        moduleType: 'image',
        imageUrl: '',
        linkUrl: ''
      };
    },
    banner: () => {
      return {
        moduleType: 'banner',
        type: '0'
      };
    },
    menus: () => {
      return {
        moduleType: 'menus',
        type: '0',
        backgroundColor: '#ffffff',
        color: '#333',
        links: [
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
            title: '预约课程',
            url: '/pages/bus/class/list?switchValue=0'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-2.png',
            title: '教练介绍',
            url: '/pages/bus/coachList'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-3.png',
            title: '场内活动',
            url: '/pages/bus/activityList'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-4.png',
            title: '场馆介绍',
            url: '/pages/bus/busDetail'
          }
        ]
      };
    },
    magnet: () => {
      return {
        moduleName: '磁贴导航',
        moduleType: 'magnet',
        type: '0',
        backgroundColor: '#ffffff',
        splitColor: '#FFFFFF',
        color: '#333',
        gap: '5',
        links: [
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
            title: '预约课程',
            url: '/pages/bus/class/list?switchValue=0',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/bd3488bf958c816d30912ded1a6ce887.png',
            backgroundColor: '#FFFFFF',
            color: '#51456e'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-6.png',
            title: '购卡购课',
            url: '/pages/getCard',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/97b26d38c3bccb6ed01577601d32f42c.png',
            backgroundColor: '#FFFFFF',
            color: '#405480'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-2.png',
            title: '教练介绍',
            url: '/pages/bus/coachList',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/1b7222fe6e95cb16fd11562ec1793f5c.png',
            backgroundColor: '#FFFFFF',
            color: '#3a6c6d'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-4.png',
            title: '场馆介绍',
            url: '/pages/bus/busDetail',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/8aff4ce4a0743add76b558c0406f0e71.png',
            backgroundColor: '#FFFFFF',
            color: '#1d5273'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-8.png',
            title: '积分商城',
            url: '/pages/bus/goods',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/2e817fd85e07c7a47b51716089910f3f.png',
            color: '#744e4a',
            backgroundColor: '#fff'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-3.png',
            title: '场内活动',
            url: '/pages/bus/activityList',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/dfbec1f78b5b4f3a106a8e77274d5ec1.png',
            color: '#6d4b33',
            backgroundColor: '#FFFFFF'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-7.png',
            title: '积分',
            url: '/pages/bus/charmValue',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/1c5fde018b4cc3a30470cec2e88f86ee.png',
            backgroundColor: '#FFFFFF',
            color: '#5f4068'
          },
          {
            icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-5.png',
            title: '留言咨询',
            url: '/pages/feedback',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/7b77895771ea0d1662f70498d1d1d745.png',
            backgroundColor: '#FFFFFF',
            color: '#6e3c52'
          }
        ],
        showIcon: '0',
        padding: '10'
      };
    },
    split: () => {
      return {
        moduleType: 'split',
        backgroundColor: '#f3f5f7',
        height: 6
      };
    },
    navigator: () => {
      return {
        moduleType: 'navigator',
        type: '0',
        padding: 7,
        gap: 4,
        splitColor: '#fff',
        links: [
          {
            title: '场内活动',
            description: '火速报名中',
            url: '/pages/bus/activityList',
            color: '#333',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/ed873ae7a7178aa8b61daffc3da97e93.png'
          },
          {
            title: '教练介绍',
            description: '跟着教练燥起来',
            url: '/pages/bus/coachList',
            color: '#333',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/44527f614dc5cee1fc946ff4540a8d84.png'
          },
          {
            title: '场馆介绍',
            description: '超棒场馆了解下',
            url: '/pages/bus/busDetail',
            color: '#333',
            backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/7d0acb03415516d6b06ae94a8f5d1d05.png'
          }
        ]
      };
    },
    dynamic: () => {
      return {
        moduleType: 'dynamic'
      };
    },
    rank: () => {
      return {
        moduleType: 'rank'
      };
    }
  }
};

export const pathUrls = [
  {
    title: '课程预约',
    url: '/pages/bus/class/list?switchValue=0'
  },
  {
    title: '私教预约',
    url: '/pages/bus/class/list?switchValue=1'
  },
  {
    title: '教练介绍',
    url: '/pages/bus/coachList'
  },
  {
    title: '场内活动',
    url: '/pages/bus/activityList'
  },
  {
    title: '场馆介绍',
    url: '/pages/bus/busDetail'
  },
  {
    title: '留言咨询',
    url: '/pages/feedback'
  },
  {
    title: '购卡购课',
    url: '/pages/getCard'
  },
  {
    title: '我的积分',
    url: '/pages/bus/charmValue'
  },
  {
    title: '积分商城',
    url: '/pages/bus/goods'
  }
];

export default {
  0: () => {
    return {
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#0a0a0a'
        },
        modules: [
          {
            moduleType: 'banner',
            type: '0',
            name: 'banner',
            moduleName: '场馆展示'
          },
          {
            moduleType: 'menus',
            type: '3',
            backgroundColor: '#ffffff',
            color: '#333',
            links: [
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
                title: '预约课程',
                url: '/pages/bus/class/list?switchValue=0'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-2.png',
                title: '教练介绍',
                url: '/pages/bus/coachList'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-3.png',
                title: '场内活动',
                url: '/pages/bus/activityList'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-4.png',
                title: '场馆介绍',
                url: '/pages/bus/busDetail'
              },
              {
                title: '留言咨询',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-5.png',
                url: '/pages/feedback'
              },
              {
                title: '购卡购课',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-6.png',
                url: '/pages/getCard'
              },
              {
                title: '我的积分',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-7.png',
                url: '/pages/bus/charmValue'
              },
              {
                title: '积分商城',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-8.png',
                url: '/pages/bus/goods'
              }
            ],
            name: 'menus',
            moduleName: '宫格导航'
          },
          {
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6',
            name: 'split',
            moduleName: '间隔'
          },
          {
            moduleType: 'dynamic',
            name: 'dynamic',
            moduleName: '会员动态'
          },
          {
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6',
            name: 'split',
            moduleName: '间隔'
          },
          {
            moduleType: 'rank',
            name: 'rank',
            moduleName: '排行榜'
          }
        ]
      },
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#0a0a0a'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/cc2b773785717b5db67dbd62d923e9de.png'
      }
    };
  },
  1: () => {
    return {
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#85d7f8'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/3437937b6140ed585f63e278edaaeb8f.png'
      },
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#85d7f8'
        },
        modules: [
          {
            name: 'banner',
            moduleName: '场馆展示',
            moduleType: 'banner',
            type: '0'
          },
          {
            name: 'magnet',
            moduleName: '磁贴导航',
            moduleType: 'magnet',
            type: '0',
            backgroundColor: '#ffffff',
            splitColor: '#FFFFFF',
            color: '#333',
            gap: '5',
            links: [
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
                title: '预约课程',
                url: '/pages/bus/class/list?switchValue=0',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/bd3488bf958c816d30912ded1a6ce887.png',
                backgroundColor: '#FFFFFF',
                color: '#51456e'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-6.png',
                title: '购卡购课',
                url: '/pages/getCard',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/97b26d38c3bccb6ed01577601d32f42c.png',
                backgroundColor: '#FFFFFF',
                color: '#405480'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-2.png',
                title: '教练介绍',
                url: '/pages/bus/coachList',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/1b7222fe6e95cb16fd11562ec1793f5c.png',
                backgroundColor: '#FFFFFF',
                color: '#3a6c6d'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-4.png',
                title: '场馆介绍',
                url: '/pages/bus/busDetail',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/8aff4ce4a0743add76b558c0406f0e71.png',
                backgroundColor: '#FFFFFF',
                color: '#1d5273'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-8.png',
                title: '积分商城',
                url: '/pages/bus/goods',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/2e817fd85e07c7a47b51716089910f3f.png',
                color: '#744e4a',
                backgroundColor: '#fff'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-3.png',
                title: '场内活动',
                url: '/pages/bus/activityList',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/dfbec1f78b5b4f3a106a8e77274d5ec1.png',
                color: '#6d4b33',
                backgroundColor: '#FFFFFF'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-7.png',
                title: '积分',
                url: '/pages/bus/charmValue',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/1c5fde018b4cc3a30470cec2e88f86ee.png',
                backgroundColor: '#FFFFFF',
                color: '#5f4068'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-5.png',
                title: '留言咨询',
                url: '/pages/feedback',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/7b77895771ea0d1662f70498d1d1d745.png',
                backgroundColor: '#FFFFFF',
                color: '#6e3c52'
              }
            ],
            showIcon: '0',
            padding: '10'
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#f5f7f9',
            height: '6'
          },
          {
            name: 'dynamic',
            moduleName: '会员动态',
            moduleType: 'dynamic'
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6'
          },
          {
            name: 'rank',
            moduleName: '排行榜',
            moduleType: 'rank'
          }
        ]
      }
    };
  },
  2: () => {
    return {
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#e6ab45'
        },
        modules: [
          {
            name: 'banner',
            moduleName: '场馆展示',
            type: '0'
          },
          {
            name: 'magnet',
            moduleName: '磁贴导航',
            type: '1',
            backgroundColor: '#fff',
            color: '#333',
            splitColor: '#FFFFFF',
            gap: '2',
            links: [
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/0d606f8389cbdbfa1bd54779df3e1dc2.png',
                title: '预约课程',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/class/list?switchValue=0',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/198f8d2e9d05f35f6f568648cd69ac74.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/63a1b505a10d26819030ab386202080b.png',
                title: '教练介绍',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/coachList',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/a83327914192ce68f252137ab7a10309.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/8665a7746f472381284c616e906e710b.png',
                title: '场内活动',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/activityList',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/5ac6549de397b700ef405466e12e3bd5.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/41ac4e712aeccc6142ea79d74883726a.png',
                title: '场馆介绍',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/busDetail',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/6487035faf06584b2794965d08139d08.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/f6c4e8708fbbcd243325f8ba2fb602b7.png',
                title: '留言咨询',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/feedback',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/8b06bd7115b73d31c53f7ceb5061026d.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/c66ebe36f889961ef03b515a18af9171.png',
                title: '购卡购课',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/getCard',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/c94c35dca9d8bb27d2fbc35f146cabb7.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/38bdc3886fa70b28fd186fbf39bd329a.png',
                title: '我的积分',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/charmValue',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/8a38c8d70fc7ab83777365026f65e18f.png@70q_1pr'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/test/image/91efe3015f03b46320a6c1b94a0003f9.png',
                title: '积分商城',
                backgroundColor: '#fff',
                color: '#333',
                url: '/pages/bus/goods',
                backgroundImage:
                  'https://imagecdn.rocketbird.cn/test/image/eb63afef23b5d62a2ad0a808113af60c.png@70q_1pr'
              }
            ],
            showIcon: '1',
            padding: '5'
          },
          {
            name: 'dynamic',
            moduleName: '会员动态',
            moduleType: 'dynamic'
          },
          {
            name: 'rank',
            moduleName: '排行榜',
            moduleType: 'rank'
          }
        ]
      },
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#e6ab45'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/1cae985815b9eb3bd68ba23748725634.png'
      }
    };
  },
  3: () => {
    return {
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#698cf4'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/7c483f508890a4300e4472273caf7dcd.png'
      },
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#698cf4'
        },
        modules: [
          {
            name: 'banner',
            moduleName: '场馆展示',
            moduleType: 'banner',
            type: '0'
          },
          {
            name: 'menus',
            moduleName: '宫格导航',
            type: '1',
            backgroundColor: '#FFFFFF',
            color: '#333444',
            links: [
              {
                title: '课程预约',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-fresh-course.png',
                url: '/pages/bus/class/list?switchValue=0'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-fresh-buy.png',
                title: '购卡购课',
                url: '/pages/getCard'
              },
              {
                title: '留言咨询',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-fresh-advisory.png',
                url: '/pages/feedback'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-fresh-charm.png',
                title: '我的积分',
                url: '/pages/bus/charmValue'
              },
              {
                title: '积分商城',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-fresh-mall.png',
                url: '/pages/bus/goods'
              }
            ]
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6'
          },
          {
            name: 'navigator',
            moduleName: '区块导航',
            moduleType: 'navigator',
            type: '0',
            padding: '12',
            gap: '4',
            splitColor: '#FFFFFF',
            links: [
              {
                title: '精彩活动',
                description: '赶快来报名哟',
                url: '/pages/bus/activityList',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/373fd6cbc523d2123200953b5bee9075.png'
              },
              {
                title: '教练介绍',
                description: '跟着教练燥起来',
                url: '/pages/bus/coachList',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/3e39b75d17ac7abd3b19d2fcf6e500d9.png'
              },
              {
                title: '场馆介绍',
                description: '超棒场馆了解下',
                url: '/pages/bus/busDetail',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/99b6f53531732a31019db62f1097597c.png'
              }
            ]
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6'
          },
          {
            moduleType: 'dynamic',
            name: 'dynamic',
            moduleName: '会员动态'
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#f3f5f7',
            height: '6'
          },
          {
            moduleType: 'rank',
            name: 'rank',
            moduleName: '排行榜'
          }
        ]
      }
    };
  },
  4: () => {
    return {
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#393a3a'
        },
        modules: [
          {
            name: 'banner',
            moduleName: '场馆展示',
            type: '0'
          },
          {
            name: 'menus',
            moduleName: '宫格导航',
            type: '0',
            backgroundColor: '#FFFFFF',
            color: '#333',
            links: [
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-paint-course.png',
                title: '预约课程',
                url: '/pages/bus/class/list?switchValue=0'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-paint-advisory.png',
                title: '留言咨询',
                url: '/pages/feedback'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-paint-charm.png',
                title: '我的积分',
                url: '/pages/bus/charmValue'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-paint-mall.png',
                title: '积分商城',
                url: '/pages/bus/goods'
              }
            ]
          },
          {
            name: 'navigator',
            moduleName: '区块导航',
            moduleType: 'navigator',
            type: '0',
            links: [
              {
                title: '场内活动',
                description: '火速报名中',
                url: '/pages/bus/activityList',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/0bf1699c54434efcb52d9e3feca0727b.png'
              },
              {
                title: '教练介绍',
                description: '跟着教练燥起来',
                url: '/pages/bus/coachList',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/c6946f0d0271ebfb077854a8be9c56e5.png'
              },
              {
                title: '场馆介绍',
                description: '超棒场馆了解下',
                url: '/pages/bus/busDetail',
                color: '#333',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/3d0f07742bdb29bbf1548bf23dc87990.png'
              }
            ],
            padding: '12',
            gap: '6',
            splitColor: '#FFFFFF'
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#FFFFFF',
            height: '3'
          },
          {
            name: 'custom',
            moduleName: '购卡购课',
            moduleType: 'image',
            imageUrl: 'https://imagecdn.rocketbird.cn/test/image/c21acf8cff92be6dd41737a1ae70df30.png',
            linkUrl: '/pages/getCard'
          },
          {
            name: 'split',
            moduleName: '间隔',
            moduleType: 'split',
            backgroundColor: '#FFFFFF',
            height: '10'
          },
          {
            name: 'dynamic',
            moduleName: '会员动态',
            moduleType: 'dynamic'
          },
          {
            name: 'rank',
            moduleName: '排行榜',
            moduleType: 'rank'
          }
        ]
      },
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#393a3a'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/7a57adea163098c6f5267c1a8cd00e12.png'
      }
    };
  },
  5: () => {
    return {
      index: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#0a0a0a'
        },
        trainingBg: 'https://imagecdn.rocketbird.cn/test/image/52506afbd89e8a960b47f72a111d6b4e.png'
      },
      bus: {
        navigationBarColor: {
          frontColor: '#ffffff',
          backgroundColor: '#0a0a0a'
        },
        modules: [
          {
            name: 'banner',
            moduleName: '场馆展示',
            type: '0'
          },
          {
            name: 'menus',
            moduleName: '宫格导航',
            type: '1',
            backgroundColor: '#000000',
            color: '#FFFFFF',
            links: [
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-hotel-course.png',
                title: '预约课程',
                url: '/pages/bus/class/list?switchValue=0'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-hotel-advisory.png',
                title: '留言咨询',
                url: '/pages/feedback'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-hotel-buy.png',
                title: '购卡购课',
                url: '/pages/getCard'
              },
              {
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-hotel-charm.png',
                title: '我的积分',
                url: '/pages/bus/charmValue'
              },
              {
                title: '积分商城',
                icon: 'https://imagecdn.rocketbird.cn/minprogram/member/theme/icon-hotel-mall.png',
                url: '/pages/bus/goods'
              }
            ]
          },
          {
            name: 'navigator',
            moduleName: '区块导航',
            moduleType: 'navigator',
            type: '0',
            links: [
              {
                title: '场内活动',
                description: '火速报名中',
                url: '/pages/bus/activityList',
                color: '#FFFFFF',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/d91269a24452a703fa9ea40515762234.png'
              },
              {
                title: '教练介绍',
                description: '跟着教练燥起来',
                url: '/pages/bus/coachList',
                color: '#FFFFFF',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/2952d19e82c334051a0f624067feca50.png'
              },
              {
                title: '场馆介绍',
                description: '超棒场馆了解下',
                url: '/pages/bus/busDetail',
                color: '#FFFFFF',
                backgroundImage: 'https://imagecdn.rocketbird.cn/test/image/7767ce78911b599b8cfe5afbf9928b9f.png'
              }
            ],
            splitColor: '#000000',
            padding: '12',
            gap: '4'
          },
          {
            name: 'dynamic',
            moduleName: '会员动态'
          },
          {
            name: 'split',
            moduleName: '间隔',
            backgroundColor: '#f3f5f7',
            height: '6'
          },
          {
            name: 'rank',
            moduleName: '排行榜'
          }
        ]
      }
    };
  }
};
