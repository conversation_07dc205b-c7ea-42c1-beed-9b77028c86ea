<template>
    <div class="box">
        <div class="box-head">教练评价设置</div>
        <div class="box-body">
            <Alert type="warning" style="margin:20px;width:500px;" show-icon>教练评价开启后，会员每次私教消课后都可以在会员端完成【教练评价】</Alert>
            <Form :model="formValidate" :rules="ruleValidate" :label-width="120">
                <FormItem label="教练评价开关" prop="commentFlag">
                    <i-switch v-model="formValidate.commentFlag" />
                </FormItem>
                <FormItem label="评分类型设置" prop="commentCategory">
                    <div class="tag-box">
                        <div v-for="(item, index) in formValidate.commentCategory" :key="index">
                            <Tag v-if="!item.deleteFlag" :name="index" @on-close="handleCloseCategory" closable @on-change="handleUpdateCategory" :checked="item.checkFlag" checkable>{{item.value}}</Tag>
                        </div>
                        <div>
                            <Input v-if="categoryInputBoxFlag" size="small" :maxlength="10" placeholder="请输入类型名称..." v-model="categoryInputTemp" style="width:140px;" />
                            <Button v-if="categoryInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveCategory"></Button>
                            <Button v-if="categoryInputBoxFlag" size="small" icon="md-close"  @click="handleToggleCategory"></Button>
                        </div>
                    </div>
                    <Button icon="ios-add" type="dashed" size="small" @click="handleToggleCategory">添加类型</Button>
                </FormItem>
                <FormItem label="评价标签设置" prop="commentTag">
                    <div class="tag-box">
                        <div v-for="(item, index) in formValidate.commentTag" :key="index">
                            <Tag v-if="!item.deleteFlag" :name="index" @on-close="handleCloseTag" closable @on-change="handleUpdateTag" :checked="item.checkFlag" checkable>{{item.value}}</Tag>
                        </div>
                        <div>
                            <Input v-if="tagInputBoxFlag" size="small" :maxlength="10" placeholder="请输入标签名称..." v-model="tagInputTemp" style="width:140px;" />
                            <Button v-if="tagInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveTag"></Button>
                            <Button v-if="tagInputBoxFlag" size="small" icon="md-close"  @click="handleToggleTag"></Button>
                        </div>
                    </div>
                    <Button icon="ios-add" type="dashed" size="small" @click="handleToggleTag">添加标签</Button>
                </FormItem>
                <FormItem label="其他意见反馈" prop="commentFeedback">
                    <i-switch v-model="formValidate.commentFeedback" />
                </FormItem>
                <FormItem label="评价推送时间" prop="commentMinutes">
                    私教消课后，<InputNumber :max="999" :min="1" v-model="formValidate.commentMinutes"></InputNumber> 分钟再推送评价消息
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="handleSaveClick">保存</Button>
                    <Button style="margin-left: 8px" @click="handleResetClick">重置</Button>
                </FormItem>
            </Form>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formValidate: {
                commentFlag: false,
                commentCategory: [],
                commentTag: [],
                commentFeedback: false,
                commentMinutes: 120
            },
            ruleValidate: {},

            categoryInputBoxFlag: false,
            categoryInputTempIdx: -1,
            categoryInputTemp: '',

            tagInputBoxFlag: false,
            tagInputTempIdx: -1,
            tagInputTemp: ''
        }
    },
    methods: {
        getSettings() {
            return this.$service.post('/Web/CourseComment/setting').then(res => {
                if (res.data.errorcode == 0) {
                    const settings = res.data.data;

                    if (Array.isArray(settings.set_type)) {
                        settings.set_type.forEach(item => {
                          item.deleteFlag = false;
                          item.checkFlag = false;
                        });
                    } else {
                        settings.set_type = [];
                    }

                    if (Array.isArray(settings.set_tag)) {
                        settings.set_tag.forEach(item => {
                          item.deleteFlag = false;
                          item.checkFlag = false;
                        });
                    } else {
                        settings.set_tag = [];
                    }

                    this.formValidate = {
                        commentFlag: settings.set_open.value == 1,
                        commentCategory: settings.set_type,
                        commentTag: settings.set_tag,
                        commentFeedback: settings.set_advice.value == 1,
                        commentMinutes: parseInt(settings.set_time.value)
                    };
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        handleSaveCategory() {
            const temp = this.categoryInputTemp.trim();
            if (!!temp) {
                if (this.categoryInputTempIdx === -1) {
                    const noneCategory = {value: temp, deleteFlag: false, checkFlag: false};
                    this.formValidate.commentCategory.push(noneCategory);
                } else {
                    let tempCategory = this.formValidate.commentCategory[this.categoryInputTempIdx];
                    tempCategory.value = temp;
                    tempCategory.deleteFlag = false;
                    tempCategory.checkFlag = false;
                }
                this.categoryInputTemp = "";
                this.categoryInputTempIdx = -1;
                this.categoryInputBoxFlag = false;
            }
        },
        handleUpdateCategory(checked, idx) {
            this.formValidate.commentCategory.forEach((item, index) => {
                if (index == idx) {
                    item.checkFlag = true;
                } else {
                    item.checkFlag = false;
                }
            });
            if (checked) {
                const item = this.formValidate.commentCategory[idx];
                this.categoryInputBoxFlag = true;
                this.categoryInputTemp = item.value;
                this.categoryInputTempIdx = idx;
            } else {
                this.categoryInputBoxFlag = false;
                this.categoryInputTemp = '';
                this.categoryInputTempIdx = -1;
            }
        },
        handleToggleCategory() {
            this.categoryInputBoxFlag = !this.categoryInputBoxFlag;
            this.categoryInputTemp = "";
            this.formValidate.commentCategory.forEach(item => {
                item.checkFlag = false;
            });
        },
        handleCloseCategory(event, index) {
            const idx = parseInt(index);
            if (typeof(idx) === "number") {
                this.formValidate.commentCategory[idx].deleteFlag = true;
            }
        },
        handleSaveTag() {
            const temp = this.tagInputTemp.trim();
            if (!!temp) {
                if (this.tagInputTempIdx === -1) {
                    const noneTag = {value: temp, deleteFlag: false, checkFlag: false};
                    this.formValidate.commentTag.push(noneTag);
                } else {
                    let tempTag = this.formValidate.commentTag[this.tagInputTempIdx];
                    tempTag.value = temp;
                    tempTag.deleteFlag = false;
                    tempTag.checkFlag = false;
                }
                this.tagInputTemp = "";
                this.tagInputTempIdx = -1;
                this.tagInputBoxFlag = false;
            }
        },
        handleUpdateTag(checked, idx) {
            this.formValidate.commentTag.forEach((item, index) => {
                if (index == idx) {
                    item.checkFlag = true;
                } else {
                    item.checkFlag = false;
                }
            });
            console.log(this.formValidate.commentTag);
            if (checked) {
                const item = this.formValidate.commentTag[idx];
                this.tagInputBoxFlag = true;
                this.tagInputTemp = item.value;
                this.tagInputTempIdx = idx;
            } else {
                this.tagInputBoxFlag = false;
                this.tagInputTemp = '';
                this.tagInputTempIdx = -1;
            }
        },
        handleToggleTag() {
            this.tagInputBoxFlag = !this.tagInputBoxFlag;
            this.tagInputTemp = "";
            this.formValidate.commentTag.forEach(item => {
                item.checkFlag = false;
            });
        },
        handleCloseTag(event, index) {
            const idx = parseInt(index);
            if (typeof(idx) === "number") {
                this.formValidate.commentTag[idx].deleteFlag = true;
            }
        },
        handleSaveClick() {
            let categoryList = this.formValidate.commentCategory.filter(item=>(!(!item.id && item.deleteFlag)));
            let tagList = this.formValidate.commentTag.filter(item=>(!(!item.id && item.deleteFlag)));
            categoryList.forEach(item => {
              item.deleted = item.deleteFlag?1:0;
            });
            tagList.forEach(item => {
              item.deleted = item.deleteFlag?1:0;
            });
            return this.$service.post('/Web/CourseComment/setSave', {
                set_open: this.formValidate.commentFlag?1:0,
                set_type: categoryList,
                set_tag: tagList,
                set_advice: this.formValidate.commentFeedback?1:0,
                set_time: this.formValidate.commentMinutes
            }).then(res => {
                if (res.data.errorcode == 0) {
                    this.getSettings();
                    this.$Message.success("保存成功！");
                } else {
                    this.$Message.error(res.data.errormsg);
                }
            });
        },
        handleResetClick() {
            this.getSettings();
        }
    },
    mounted() {
        this.getSettings();
    }
}
</script>

<style lang="less" scoped>
.box {
    border: 1px solid gray;
    background: white;

    .box-head {
        height: 50px;
        display: flex;
        align-items: center;
        padding-left: 20px;
        font-size: 24px;
        font-weight: bold;
        border-bottom: 1px solid gray;
    }

    .box-body {
        .tag-box {
            margin-bottom: 10px;
            display: flex;
            flex-direction: row;
            align-items: flex-end;
        }
    }
}
</style>
