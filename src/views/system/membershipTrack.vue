<template>
  <div class="addupinn2">
    <div class="addupinn2-content">
      <!-- <div class="addupinn2-t">
        <h2>参数设置</h2>
      </div> -->
      <div class="addupinn2-b">
        <div class="lay2">
          <p class="lay-title2 lay-title4">公海设置-非会员</p>
          <div class="lay-content2 lay_content4">
            <p class="day2">跟进有效时长</p>
            <input type="text" v-model="nonmemfollowUpTime" @input='inputnonmemTime($event)' class="form-control day-input2 mg202" :disabled="!isEdit">
            <p class="day_test3">天</p>
            <span class="nodes2">注：会籍名下<em>非会员</em>需要多少天未进行跟进将客户信息转入公海；默认0天，0天表示不启用；</span>
          </div>
          <p class="lay2_line2 lay-title4">-会员</p>
          <div class="lay-content2 lay_content4">
            <p class="day2 day2_line2">跟进有效时长</p>
            <input type="text" v-model="followUpTime" @input='inputTime($event)' class="form-control day-input2 mg202 mg202_line2" :disabled="!isEdit">
            <p class="day_test3">天</p>
            <span class="nodes2 nodes2_line2">注：会籍名下<em>会员</em>需要多少天未进行跟进将客户信息转入公海；默认0天，0天表示不启用；</span>
          </div>
          <p class="lay2_line2 lay-title4" style="padding-left: 70px">
            <i-switch v-model="groupEye" size="large" :disabled="!isEdit"></i-switch>
          </p>
          <div class="lay-content2 lay_content4 group-eye">
            <p>只能看到自己组内的公海会员数据</p>
          </div>
        </div>
        <div class="lay2" style="height: 100px;">
          <p class="lay-title2">预约设置</p>
          <div class="lay-content2">
            <!-- <p class="day2">预约有效期</p>
            <input type="text" v-model="reserTime" class="form-control day-input2 mg202" :disabled="!isEdit">
            <p class="day_test3">天</p>
            <span class="nodes2">注：客户加入预约列表的保护期，默认0天；0天不启用；</span> -->
            <p class="day2">最多预约人数</p>
            <input v-model='reserPersonMax' type="text" class="form-control day-input2 mg202" :disabled="!isEdit">
            <p class="day_test3">人</p>
            <span class="nodes2">注：一名会籍人员最多添加到预约列表中的人数；默认20人，0人不启用；</span>
          </div>
        </div>
        <div class="lay2 lay3">
          <p class="lay-title2 lay-title3" style="padding-left: 20px;">收定金设置</p>
          <Form class="lay-content2 lay-content3">
            <Form-item label="定金金额">
              <RadioGroup v-model="depositSet.deposit">
                <Radio label="1" :disabled="!isEdit">会籍决定收取金额</Radio>
                <Radio label="2" :disabled="!isEdit">固定金额</Radio>
              </RadioGroup>
              <span v-if="depositSet.deposit==2"><InputNumber v-model="depositSet.money" class="day-input3" :disabled="!isEdit"/>元</span>
            </Form-item>
          </Form>
        </div>
        <div class="lay2 lay3">
          <p class="lay-title2 lay-title3" style="padding-left: 20px;">体验卡领取限制</p>
          <div class="lay-content2 lay-content3">
            <RadioGroup v-model="receiveCardSetting">
              <Radio label="1" :disabled="!isEdit">一个客户只能从会籍端领取任意一种体验卡，且只能领一次（从PC端添加不受限制）</Radio>
              <Radio label="0" :disabled="!isEdit">不设限制</Radio>
            </RadioGroup>
          </div>
        </div>
        <!-- <div class="lay2 lay3">
          <p class="lay-title2 lay-title3">跟进标签设置</p>
          <div class="lay-content2 lay-content3">
            <div class="tag-wrap">
              <template v-for="(tag, index) in followSpecList">
                <i class="tag-con tag-default" v-show="followspecState[index].isDel != 1" :class="{'tag-selected':followspecState[index].isSel,'tag-del':isfollowSpecDel}" @click="changfollowSpec(index)" :key="index">{{tag.tag_name}}</i>
              </template>
              <a href="javascript:void(0)" v-if="!isfollowSpecDel && isEdit" class="tag-link" @click="addfollowSpec">添加</a>
              <a href="javascript:void(0)" v-if="followSpecList.length>0 && !isfollowSpecDel && isEdit" class="tag-link" @click="isfollowSpecDel=true">删除</a>
              <a href="javascript:void(0)" v-if="isfollowSpecDel && isEdit" class="tag-link" @click="savefollowSpecDel">保存</a>
              <div class="add-wrap" v-show="isfollowSpecAddShow && isEdit">
                <input type="text" v-model='followSpecName' class="form-control pull-left jobtest" :disabled="!isEdit">
                <a href="javascript:void(0)" class="tag-link" @click="savefollowSpecAdd">保存</a>
              </div>
            </div>
          </div>
        </div> -->
        <div class="lay2 lay3">
          <p class="lay-title2 lay-title3">会籍开单<p>
          <div class="lay_content2 lay_content3">
            <div style="margin-top:20px;">
              是否启用会籍开单
              <Tooltip>
                <div slot="content"
                     style="white-space: normal; width: 200px">设置是否允许会籍开单、续卡、升卡</div>
                <Icon size="16"
                      type="ios-help-circle"
                      style="padding-right: 5px"
                      color="#ffcf05"></Icon>
              </Tooltip>
              <i-switch v-model="preOrder" :disabled="!isEdit"></i-switch>
              <Checkbox  style="margin-left: 8px" true-value="1" false-value="0" v-model="depositSet.can_edit_fields" :disabled="!isEdit">可修改卡售价、赠送、有效期等</Checkbox>
              <Checkbox  style="margin-left: 8px" true-value="1" false-value="0" v-model="can_edit_create_time" :disabled="!isEdit">可修改开卡时间</Checkbox>
            </div>
            <div style="margin-top:10px;">
              是否启用开单核查
              <Tooltip>
                <div slot="content"
                     style="white-space: normal; width: 200px">设置是否需要前台审核会籍开单、续卡、升卡信息</div>
                <Icon size="16"
                      type="ios-help-circle"
                      style="padding-right: 5px"
                      color="#ffcf05"></Icon>
              </Tooltip>
              <i-switch v-model="checkOrder" :disabled="!isEdit"></i-switch>
            </div>
          </div>
        </div>
        <Row class="row-lh">
					<Button v-if="!isEdit" type="success" style="background:#5fb75d;" @click="isEdit=true">编辑</Button>
					<Button v-if="isEdit" type="success" style="background:#5fb75d;" @click="saveParam">保存</Button>
					<Button v-if="isEdit" style="margin-left: 8px" @click="handleCancelEdit">取消</Button>
				</Row>
      </div>
    </div>
  </div>
</template>
<script>
  // import radioGroups from '../../components/form/radioGroup.vue';
  export default {
    mounted() {
      this.getParam();
      this.getSpec();
    },
    data() {
      return {
        isEdit: false,
        groupEye: true,
        cardList: [],
        depositSet: {
          deposit: '1',
          can_edit_fields: '0',
          money: ''
        },
        followUpTime: "", //跟进有效时间
        nonmemfollowUpTime: "",
        reserTime: "0", //预约有效时间
        reserPersonMax: "20", //预约最大人数
        followSpecName: '',
        isfollowSpecAddShow: false,
        isfollowSpecDel: false, //特长删除样式是否隐藏状态
        followspecState: [], //特长状态
        followspecStateNum: 0, //特长选中个数
        followSpecList: {}, //特长列表
        receiveCardSetting: '1',
        cusSpecName: '',
        iscusSpecAddShow: false,
        iscusSpecDel: false, //特长删除样式是否隐藏状态
        cusspecState: [], //特长状态
        cusspecStateNum: 0, //特长选中个数
        cusSpecList: {}, //特长列表
        can_edit_create_time: '0', // 是否可修改开卡时间
        preOrder: false,
        checkOrder: false
      }
    },
    // components: {
    //   radioGroups
    // },
    methods: {
      handleCancelEdit() {
        this.isEdit = false;
        this.getParam();
      },
      inputTime(e) {
        var inputval = e.target.value;
        inputval=inputval.replace(/\s+/g,"");
        this.followUpTime = inputval;
        if(inputval){
          var re=/\d+$/;
          var t=re.test(inputval);
          if(!t||t<0){
            this.followUpTime = 0;
          }
        }
      },
      inputnonmemTime(e) {
        var inputval = e.target.value;
        inputval=inputval.replace(/\s+/g,"");
        this.nonmemfollowUpTime = inputval;
        if(inputval){
          var re=/\d+$/;
          var t=re.test(inputval);
          if(!t||t<0){
            this.nonmemfollowUpTime = 0;
          }
        }
      },
      savefollowSpecAdd() {
        if(!this.followSpecName || this.followSpecName.length>10){
          this.$Message.error('没有填写标签或超过十个字符');
          this.isfollowSpecAddShow = false;
          this.followSpecName = '';
          return false;
        }
        var postData = {
          'tag_name': this.followSpecName,
          'tag_type': 2
        }
        this.$service.post('/Web/MembershipTags/add_bus_tag', postData)
        .then((response) => {
          if (response.data.errorcode == 0) {
            this.getSpec();
            this.isfollowSpecAddShow = false;
            this.followSpecName = '';
          } else {
            this.$Message.error(response.data.errormsg);
            this.isfollowSpecAddShow = false;
            this.followSpecName = '';
          }
        }).catch(function(error) {
          console.log(error)
        });
      },
      getCardList() {
        return this.$service.post('/Web/Param/get_bus_card_list')
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.cardList = res.data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      savecusSpecAdd() {
        if(!this.cusSpecName || this.cusSpecName.length>10){
          this.$Message.error('没有填写标签或超过十个字符');
          this.iscusSpecAddShow = false;
          this.cusSpecName = '';
          return false;
        }
        this.$service.post('/Web/MembershipTags/add_bus_tag', {
          'tag_name': this.cusSpecName,
          'tag_type': 1
        })
        .then((response) => {
          if (response.data.errorcode == 0) {
            this.getSpec();
            this.iscusSpecAddShow = false;
            this.cusSpecName = '';
          } else {
            this.$Message.error(response.data.errormsg);
            this.iscusSpecAddShow = false;
            this.cusSpecName = '';
          }
        })
      },
      addfollowSpec() {
        this.isfollowSpecAddShow = true;
      },
      addcusSpec() {
        this.iscusSpecAddShow = true;
      },
      savefollowSpecDel(){
        let ids = [];
        for (let i = this.followSpecList.length - 1; i >= 0; i--) {
          if (this.followspecState[i].isDel == 1) {
            ids.push(this.followSpecList[i].id);
          };
        }
        if(ids.length === 0){
          this.isfollowSpecDel = false;
          return false;
        }
        this.$service.post('/Web/MembershipTags/del_bus_tag', {
            'id': ids.join(','),
            'tag_type': 2
          })
          .then((response) => {
            if (response.data.errorcode == 0) {
              this.getSpec();
              this.isfollowSpecDel = false;
            } else {
              this.$Message.error(response.data.errormsg);
              for (let i = this.followSpecList.length - 1; i >= 0; i--) {
                if (this.followspecState[i].isDel == 1) {
                  this.followspecState[i].isDel = 0
                };
              }
              this.isfollowSpecDel = false;
            }
          }).catch(function(error) {
            for (let i = this.followSpecList.length - 1; i >= 0; i--) {
              if (this.followspecState[i].isDel == 1) {
                this.followspecState[i].isDel = 0
              };
            }
            console.log(error)
          });
      },
      savecusSpecDel(){
        let ids = [];
        for (let i = this.cusSpecList.length - 1; i >= 0; i--) {
          if (this.cusspecState[i].isDel == 1) {
            ids.push(this.cusSpecList[i].id);
          };
        }
        if(ids.length === 0){
          this.iscusSpecDel = false;
          return false;
        }
        this.$service.post('/Web/MembershipTags/del_bus_tag', {
            'id': ids.join(','),
            'tag_type': 1
          })
          .then((response) => {
            if (response.data.errorcode == 0) {
              this.getSpec();
              this.iscusSpecDel = false;
            } else {
              this.$Message.error(response.data.errormsg);
              for (let i = this.cusSpecList.length - 1; i >= 0; i--) {
                if (this.cusspecState[i].isDel == 1) {
                  this.cusspecState[i].isDel = 0
                };
              }
              this.iscusSpecDel = false;
            }
          }).catch(function(error) {
            for (let i = this.cusSpecList.length - 1; i >= 0; i--) {
              if (this.cusspecState[i].isDel == 1) {
                this.cusspecState[i].isDel = 0
              };
            }
            console.log(error)
          });
      },
      changfollowSpec(index) {
        let specStateObj = this.followspecState;
        if (this.isfollowSpecDel === true) {
          this.followspecState[index].isDel = 1;
        } else {
          if (this.followspecState[index].isSel === 1) {
            this.followspecStateNum = this.followspecStateNum - 1;
          } else {
            this.followspecStateNum = this.followspecStateNum + 1;
          }
          this.followspecState[index].isSel = this.followspecState[index].isSel === 1 ? 0 : 1;
        }
      },
      changcusSpec(index) {
        let specStateObj = this.cusspecState;
        if (this.iscusSpecDel === true) {
          this.cusspecState[index].isDel = 1;
        } else {
          if (this.cusspecState[index].isSel === 1) {
            this.cusspecStateNum = this.cusspecStateNum - 1;
          } else {
            this.cusspecStateNum = this.cusspecStateNum + 1;
          }
          this.cusspecState[index].isSel = this.cusspecState[index].isSel === 1 ? 0 : 1;
        }
      },
      getSpec() {
        this.$service.get('/Web/MembershipTags/get_bus_tags')
          .then((response) => {
            if (response.data.errorcode == 0) {
              this.followSpecList = response.data.data.follow.all_tags;
              this.followselected = response.data.data.follow.real_tags;
              this.followspecState = [];
              this.followspecStateNum = 0;
              this.followSpecName='';
              this.isfollowSpecAddShow=false;
              this.isfollowSpecDel=false; //特长删除样式是否隐藏状态
              for (let i = 0; i < this.followSpecList.length; i++) {
                let arrObj = {
                  isDel: 0,
                  isSel: 0
                }; //isSel 0为默认状态 1为选中
                this.followspecState.push(arrObj);
                if (this.followselected.split(',').indexOf(this.followSpecList[i].id) >= 0) {
                  this.followspecState[i].isSel = 1;
                  this.followspecStateNum = this.followspecStateNum + 1;
                }
              }

              this.cusSpecList = response.data.data.customer.all_tags;
              this.cusselected = response.data.data.customer.real_tags;
              this.cusspecState = [];
              this.cusspecStateNum = 0;
              this.cusSpecName='';
              this.iscusSpecAddShow=false;
              this.iscusSpecDel=false;
              for (let i = 0; i < this.cusSpecList.length; i++) {
                let arrObj = {
                  isDel: 0,
                  isSel: 0
                }; //isSel 0为默认状态 1为选中
                this.cusspecState.push(arrObj);
                if (this.cusselected.split(',').indexOf(this.cusSpecList[i].id) >= 0) {
                  this.cusspecState[i].isSel = 1;
                  this.cusspecStateNum = this.cusspecStateNum + 1;
                }
              }
            } else {
              this.$Message.error(response.data.errormsg)
            }
          }).catch(function(error) {
            console.log(error)
          });
      },

      getParam: function() {
        this.$service.get('/Web/Param/get_param', '')
          .then((response) => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                let info = response.data.data.info;
                this.depositSet.deposit = info.deposit || '1'
                this.depositSet.can_edit_fields = info.can_edit_fields
                this.depositSet.money =  info.money=== '0.00' || info.money=== '0' || !info.money ? 100 : +info.money
                if(info.follow_up_valid_time){
                  this.followUpTime = info.follow_up_valid_time;
                }
                if(info.follow_up_valid_time){
                  this.nonmemfollowUpTime = info.nonmember_follow_up_valid_time;
                }
                // this.reserTime = info.reservation_valid_time;
                this.reserTime = 0;
                this.reserPersonMax = info.reservation_preson_max;
                this.receiveCardSetting = info.receive_card_setting;

                if (info.group_public_sea_member == 1) {
                  this.groupEye = true;
                } else {
                  this.groupEye = false;
                }
                this.can_edit_create_time = info.can_edit_create_time;
                this.preOrder = info.membership_billed == 1;
                this.checkOrder = info.membership_audit == 1;
              }
            }
          }, response => {
            console.log('请求出错', response);
          });
      },
      saveParam: function() {
        // if (this.followspecStateNum === 0||this.cusspecStateNum === 0) {
        //   this.$Message.error("请选择擅长");
        //   return;
        // }
        let arr = [];
        for (let i = 0; i < this.followSpecList.length; i++) {
          if (this.followspecState[i].isSel === 1) {
            arr.push(this.followSpecList[i].id);
          }
        }
        // let nonmemarr = [];
        // for (let i = 0; i < this.cusSpecList.length; i++) {
        //   if (this.cusspecState[i].isSel === 1) {
        //     nonmemarr.push(this.cusSpecList[i].id);
        //   }
        // }
        let postData = {
          follow_up_valid_time: this.followUpTime,
          nonmember_follow_up_valid_time: this.nonmemfollowUpTime,
          reservation_valid_time: this.reserTime,
          reservation_preson_max: this.reserPersonMax,
          follow_tag_ids: arr.join(','),
          // customer_tag_ids: nonmemarr.join(','),
          receive_card_setting: this.receiveCardSetting,
          group_public_sea_member: this.groupEye?1:0,
          membership_billed: this.preOrder?1:0,
          membership_audit: this.checkOrder?1:0,
          can_edit_create_time: this.can_edit_create_time,
        };
        if (this.depositSet.deposit==2 && !/^[1-9]\d*$/.test(this.depositSet.money)) {
          this.$Message.error('定金必须为正整数！');
          return false;
        }
        Object.assign(postData, this.depositSet)
        this.$service.post('/Web/Param/add_param', postData)
          .then((response) => {
            if (response.status == 200) {
              this.$Message.success(response.data.errormsg);
              this.getParam();
              this.getSpec();
              const self = this;
              setTimeout(() => {
                self.isEdit = false;
              }, 1000);
            }
          }, response => {
            this.$Message.error('服务器忙，请稍后再试！');
          });
      },
    },
  }
</script>
<style>
.lay-content2 .ivu-form-item-label{
  line-height: inherit;
}
</style>
<style scoped>
  .group-eye {
    display: flex;
    flex-direction: row;
    height: 38px;
    align-items: flex-end;
  }

  .form-control {
    padding: 6px 12px;
  }

  .row-lh {
		height: 52px;
		display: flex;
		align-items: center;
    margin-top: 30px;
	}

  .addupinn2-content {
    width: 100%;
    /* background: #ececec;
    padding: 40px 48px; */
  }

  .addupinn2-t {
    width: 100%;
    background: #f7f7f7;
    height: 37px;
    padding: 0 20px;
    overflow: hidden;
    border: 1px solid #dcdcdc;
  }

  .addupinn2-t h2 {
    margin: 0;
    font-size: 14px;
    margin-top: 12px;
    font-weight: bold;
  }

  .addupinn2-b {
    width: 100%;
    /* border: 1px solid #dcdcdc; */
    background: #fff;
    padding: 30px 35px 56px 35px;
  }

  .lay2 {
    width: 930px;
    height: 200px;
    border: 1px solid #dedede;
  }
  .lay3 {
    height: auto;
    overflow: auto;
    padding-bottom: 20px;
    border-top: 0;
    border-bottom: 1px solid #dedede;
  }
  .lay2:nth-child(2) {
    border-top: none;
  }

  .lay-title2 {
    width: 116px;
    padding-left: 35px;
    font-size: 14px;
    color: #333333;
    padding-top: 35px;
    float: left;
  }
  .lay2_line2 {
    width: 116px;
    padding-left: 92px;
    font-size: 14px;
    color: #333333;
    padding-top: 15px;
    float: left;
  }
  .lay-title3 {
    width: 126px;
  }
  .lay-title4 {
    width: 146px;
  }
  .lay-content2 {
    float: left;
    width: 810px;

  }

  .lay-content3 {
    width: 800px;
    padding-top: 27px;
    padding-right: 250px;
  }
  .lay_content4 {
    width: 780px;
  }
  .day2 {
    line-height: 88px;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
    width: 90px;
  }
  .day2_line2 {
    line-height: 48px;
  }
  .days {
    clear: both;
    line-height: 88px;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
  }

  .day_test3 {
    display: inline-block;
    color: #666666;
    font-size: 14px;
  }

  .nodes2 {
    display: inline-block;
    color: #999999;
    font-size: 14px;
    line-height: 88px;
    margin-left: 20px;
  }
  .nodes2 em {
     font-style: normal;
     color: #d9534f;
  }
  .nodes2_line2 {
    line-height: 48px;
  }
  .day-input2 {
    display: block;
    float: left;
    width: 54px;
    margin-right: 7px;
  }
  .day-input3 {
    display: inline-block;
    width: 54px;
    margin-right: 7px;
  }
  .day-input4 {
    display: inline-block;
    width: 120px;
  }
  .openOcean2 {
    font-size: 14px;
    color: #666666;
    float: left;
    margin: 0;
  }

  .check {
    float: left;
    margin-left: 48px;
  }

  .people {
    clear: both;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
    line-height: 35px;
    width: 90px;
    margin-top: 100px;
  }

  .people_test2 {
    display: inline-block;
    color: #999999;
    font-size: 14px;
    margin-left: 20px;
    line-height: 35px;
  }

  .mg202 {
    margin-top: 27px;
  }
  .mg202_line2 {
    margin-top: 7px;
  }
  .day_pp {
    display: block;
    float: left;
    line-height: 35px;
  }

  .addupinn2-on {
    overflow: hidden;
    clear: both;
    width: 70px;
    height: 30px;
    line-height: 28px;
    background-color: #5fb75d;
    color: #fff;
    border: none;
    border-radius: 4px;
    margin-left: 346px;
    margin-top: 56px;
  }

  .addupinn2-on:hover {
    opacity: 0.7;
  }

  .addupinn2-off {
    width: 70px;
    height: 30px;
    line-height: 28px;
    background: #fff;
    color: #5fb75d;
    border: 1px solid #5fb75d;
    border-radius: 4px;
    margin-left: 72px;
    margin-top: 56px;
  }

  .addupinn2-off:hover {
    background-color: #5fb75d;
    color: #fff;
  }
  .tag-wrap {
    float: left;
    margin-left: 26px;
    width: 550px;
    position: relative;
  }
  .tag-con {
    position: relative;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    padding: 0 25px 0 12px;
    background-color: #f1f1f1;
    color: #999;
    font-style: normal;
    margin: 0 10px 10px 0;
    cursor: pointer;
    vertical-align: middle;
  }

.tag-con::after {
  display: block;
  position: absolute;
  top: 50%;
  right: 5px;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  content: ' ';
  background: url("../../assets/img/ico-tag.png") no-repeat;
}
.tag-default::after {
  background-position: 0 -22px;
}
.tag-selected {
  background-color: #5cb85c;
  color: #fff;
}

.tag-selected::after {
  background-position: 0 0;
}

.tag-link {
  display: inline-block;
  color: #4ca0e0;
  line-height: 30px;
  margin: 0 15px 10px 15px;
  vertical-align: middle;
}

.tag-del {
  height: 28px;
  padding: 0 24px 0 11px;
  line-height: 28px;
  border: 1px solid #d9534f;
  background-color: #fff;
  color: #d9534f;
}
.tag-del::after {
  background-position: 0 -44px;
}

.tag-wrap .jobtest {
  margin-left: 0;
}
/*.tag-wrap .add-wrap{
  position: absolute;
}*/
.jobtest {
  width: 150px;
  margin-left: 26px;
}
.tag-wrap .jobtest {
  margin-left: 0;
}
/*.tag-wrap .add-wrap{
  position: absolute;
}*/
</style>
