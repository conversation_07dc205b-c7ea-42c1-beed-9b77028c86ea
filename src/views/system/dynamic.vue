
<style lang="less" scoped>
.dynamic {
  background-color: #fff;
}
.ivu-tabs /deep/ .ivu-tabs-nav {
  .ivu-tabs-ink-bar {
    top: 0;
    bottom: auto;
    background-color: #6cbf6c;
    width: 1 / 2 * 100% !important;
  }
  .tabs-active-1 {
    transform: translate3d(100%, 0, 0) !important;
  }
  .tabs-active-2 {
    transform: translate3d(200%, 0, 0) !important;
  }
}
</style>

<template>
  <div class="dynamic customized-tabs">
    <Tabs @on-click="clickTabs">
      <TabPane label="会员动态列表">
        <List v-if="activeIndex == 0" />
      </TabPane>
      <TabPane label="禁言会员列表">
        <Abandon v-if="activeIndex == 1" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import List from './components/list';
import Abandon from './components/abandonList';
export default {
  name: 'dynamic',
  components: { List, Abandon },
  data() {
    return {
      activeIndex: 0,
      activated: [0]
    };
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
      active.setAttribute('class', className);
      if (!this.activated.includes(index)) {
        this.activated.push(index);
      }
    }
  }
};
</script>
