<template>
  <div class="msg-box-help">
    <div class="msg-left-help" ref="scrollWrap">
      <div>
        <Icon class="md-help" type="md-help" />
        <h2 style="display:inline-block;vertical-align:middle;margin-left:4px;">常见问题</h2>
      </div>
       <Scroll :on-reach-bottom="handleReachEdge" :height="scrollHeight" :distance-to-edge="-50">
         <ul class="msg-short-help" v-if="helpList && helpList.length>0">
            <li style="list-style: none;" v-for="(item, index) in helpList"  @click="showCurMsg(item, index)" :key="item.id">
              <h4 class="help-title" :style="{color: item.id == curMsg.id ? 'lightgrey !important' : '#52A4EA !important'}">
                {{item.title}}
              </h4>
            </li>
            <li v-show="isEnd" class="nomore-help">没有更多了</li>
          </ul>
          <div class="nodata-help" v-else>
            暂无相关帮助信息
          </div>
      </Scroll>     
    </div>
    <div class="msg-rig-help">
      <div class="rig-con" ref="content">
         <div class="main-help" v-if="curMsg" v-html="curMsg.content"></div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'msgCenter',
  data() {
    return {
      chosen: false,
      helpList: [],
      postData: {
        page_no: 1,
        page_size: 20
      },
      isEnd: false,
      curMsg: '',
      scrollHeight: 500
    }
  },
  created() {
    this.getHelp();
  },
  mounted() {
    this.scrollHeight = this.$refs.scrollWrap.offsetHeight - 87 - 64
  },
  methods: {
    getMsgImg(type) {
      return `/static/img/notice-${type}.png`
    },
    handleReachEdge() {
      this.postData.page_no += 1
      return this.getHelp(true)
    },
    showCurMsg(msgInfo,index) {
      this.$refs.content.scrollTop = 0;
      msgInfo = {
        ...msgInfo,
        index: +index
      }
      this.curMsg = msgInfo
    },
    setCur(index){
      this.curMsg = {
        ...this.helpList[index],
        index: index >= this.helpList.length || index <= 0 ? index : index
      }
      if (this.curMsg.read_time == 0) {
      }
    },
    getHelp(isScroll) {
      if(this.isEnd && isScroll) {
        return false;
      }
      return this.$service
        .get('/Web/Index/getFaqList', {
          params: { ...this.postData }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            if (isScroll) {
              this.helpList = this.helpList.concat(resData);
              this.isEnd = resData.length < this.postData.page_size;
            } else {
              this.helpList = resData;
              this.setCur(0);
              this.isEnd = false;
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>
<style lang="less">
    .main-help {
      padding: 28px;
      color: #303030;
      font-size: 16px;
      line-height: 1.5;
      width: 100%;
      word-break: break-all;
      white-space: pre-wrap;
      img {
        display: block;
        max-width: 100%;
      }
      >p, >span, >strong {
        word-break: break-all;
        white-space: pre-wrap;
      }
      ul li {
        list-style: disc outside none;
        max-width: 100%;
      }
      ol li {
        list-style: decimal outside none;
        max-width: 100%;
      }
    }
</style>
<style lang="less" scoped>
.msg-button-help {
  border: 1px solid rgb(15, 16, 17);
  background-color: #fff;
  border-radius: 2px;
  color: #979faf;
  text-align: center;
  box-shadow: 0px 3px 5px 0px rgba(53, 73, 93, 0.1);
  height: 34px;
  line-height: 34px;
  overflow: hidden;
}
.md-help {
  margin: 4px 0px;
  font-size: 34px;
  border-radius: 50%;
  border: 2px solid #F9F9FA;
  box-sizing: border-box;
}
.msg-box-help {
  overflow: hidden;
  width: 100%;
  height: 100%;
  .msg-left-help {
    background-color: #fff;
    box-shadow: 0px 3px 20px 0px rgba(44, 57, 69, 0.1);
    position: absolute;
    width: 360px;
    height: 100%-20px;
    padding: 32px 20px;
    margin-bottom: 20px;
    font-size: 14px;
    box-sizing: border-box;
    float: left;
  }
}
.msg-tags-help {
  .msg-button-help;
  width: 100%;
  margin-bottom: 21px;
  span {
    width: 25%;
    cursor: pointer;
    float: left;
    &.cur,
    &:hover {
      color: #fff;
      background: #52a4ea;
    }
  }
}
.msg-short-help {
  color: #979faf;
  margin-bottom: 50px;
  .top {
    margin-bottom: 10px;
  }
  h4 {
    color: #303030;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 24px;
      height: 24px;
    }
  }
  .rig {
    float: right;
    font-size: 12px;
  }
  li {
    padding: 15px 18px;
    border-bottom: 1px solid #e5e9f4;
    position: relative;
    cursor: pointer;
    &.point-before::before {
      content: ' ';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #e60012;
      position: absolute;
      left: 0;
      top: 50%;
      transform: scaleY(-50%);
    }
  }
  p {
    height: 42px;
    overflow: hidden;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
.msg-rig-help {
  margin-left: 395px;
  // width: 100%;
  height: 100%;
  .rig-con {
    overflow-y: scroll;
    background: #fff;
    height: calc(100%);
    margin-right: 35px;
    width: 100%;
  }
  .tit {
    color: #303030;
    font-size: 26px;
    width: 100%;
    box-sizing: border-box;
    padding: 40px 28px;
    border-bottom: 1px solid #e5e9f4;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 24px;
      height: 24px;
      margin-right: 18px;
    }
    span {
      margin-left: 20px;
      font-size: 18px;
      color: #979faf;
    }
  }
}
.nodata-help,.nomore-help {
  color: #979faf;
  text-align: center;
  line-height: 1.5;
  border-bottom: none !important;
}
.help-title {
  width: 100%;
  color: #52A4EA !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: no-wrap;
}
</style>