<template>
  <div class="customized-tabs">
    <Tabs @on-click="clickTabs" v-model="activeIndex" :animated="true">
      <TabPane label="数据导入">
        <UpDataList />
      </TabPane>
      <TabPane label="导入历史">
        <Storage v-if="activated.includes(1)"/>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
  import Storage from './components/UpLogList';
  import UpDataList from './components/UpDataList';
  import { mapState } from 'vuex';
  export default {
    name: 'UpData',
    components: {
      UpDataList,
      Storage
    },
    data() {
      return {
        activeIndex: 0,
        activated: [0]
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    created() {
     
    },
    methods: {
      clickTabs(index) {
        this.activeIndex = index;
        const active = document.querySelector('.ivu-tabs-ink-bar');
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
        active.setAttribute('class', className);
        if (!this.activated.includes(index)) {
          this.activated.push(index);
        }
      }
    }
  };
</script>

