<template>
  <div class="msg-box">
    <div class="msg-left">
     <ul class="con-list">
       <li v-for="(item, index) in templateList" :key="item.id" @click="templateChange(item,index)" :class="curIndex===index && !hasAddTemplate?'cur':''" >
         <span>{{item.name}}</span> 
         <img @click="modifyName(item)" src="~assets/img/icon-edit-min.png" title="重命名" alt="edit"/>
         <img @click="delTemplate(item)"  v-if="item.can_delete" src="~assets/img/icon-del-min.png" title="删除" alt="del"/>
        </li>
        <li v-if="hasAddTemplate" :class="hasAddTemplate?'cur':''" >
         <span>{{addTemplateForm.name}}</span> 
        </li>
     </ul>
     <Button v-show="!hasAddTemplate"  class="contract-button" type="primary" icon="plus" @click="addTemplateShow">添加模板</Button>
    </div>
    <div class="msg-rig">
      <div class="rig-top">
        <span class="rig-button cur" @click="saveTemplate">保存</span>
        <span class="rig-button" @click="$router.back()">取消</span>
      </div>
      <div class="rig-con">
        <PrintEditor v-model="curEditContent.detail" :fields="curEditContent.fields" @pos-update="imgPosUpdate"/>
      </div>
    </div>
    <Modal :mask-closable="false" v-model="showAddTemplate" title="创建新模板">
      <Form ref="templateForm" :model="addTemplateForm" class="modal-form" style="padding: 0 30px" :label-width="80">
        <Form-item label="模板类型" prop="type" :rules="{required: true, message: '请选择模板类型'}">
          <Select v-model="addTemplateForm.type" placeholder="请选择">
            <Option v-for="item in typeList" :key="item.id" :value="+item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="模板名称" prop="name" :rules="{required: true, message: '请填写模板名称'}">
          <Input v-model="addTemplateForm.name" />
        </Form-item>
        <Form-item>
          <Checkbox v-model="addTemplateForm.default" :true-value="1" :false-value="0">将此模板设置成该类合同的默认打印模板</Checkbox>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="addTemplate">确定</Button>
        <Button @click="showAddTemplate = false">取消</Button>
      </div>
    </Modal>
    <Modal :mask-closable="false" v-model="showEditName" title="创建新模板">
      <Form ref="modifyForm" :model="modifyForm" class="modal-form" style="padding: 0 30px" :label-width="80">
        <Form-item label="模板名称" prop="name" :rules="{required: true, message: '请填写模板名称'}">
          <Input v-model="modifyForm.name" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveModifyName">确定</Button>
        <Button @click="showEditName = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import PrintEditor from 'components/form/PrintEditor'
export default {
  name: 'contractPrint',
  mounted() {},
  data() {
    return {
      showAddTemplate: false,
      showEditName: false,
      contratForm: {
        id: '',//新增不传，修改传
        name: '',//名称
        detail: '',//模板详情
        type: 1,//模板类型
        default: 1,//是否作为默认
        //用户签名位置
      },
      curEditContent: {
        detail: '',
        content: '',
        fields: [],
        userPosi: {
          x: '',
          y: '',
          pageNo: ''
        },
        busPosi: {
          x: '',
          y: '',
          pageNo: ''
        }
      },
      addTemplateForm: {
        id: '',
        name: '',
        detail: '',
        type: 1,
        default: 1,
      },
      modifyForm: {
        id: '',
        name: ''
      },
      templateList: [],
      typeList: [],
      curIndex: 0,
      hasAddTemplate: false
    }
  },
  computed: {
  },
  components: {
    PrintEditor
  },
  created() {
    this.getTemplateList();
    this.getTypeAndfields();
  },

  methods: {
    imgPosUpdate(obj){
      Object.assign(this.curEditContent, obj)
    },
    addTemplateShow() {
      this.showAddTemplate = true
      this.addTemplateForm = {
        id: '',
        name: '',
        detail: '',
        type: 1,
        default: 1,
      }
    },
    addTemplate() {
      this.$refs.templateForm.validate(valid => {
        if(valid){
          this.showAddTemplate = false
          this.hasAddTemplate = true
          this.curEditContent.detail = ''
          this.typeList.forEach((item)=>{
            if(item.id == this.addTemplateForm.type) {
              this.curEditContent.fields = item.fields
            }
          });
        }
      });
      
    },
    templateChange(item, index) {
      if(this.hasAddTemplate) {
        this.$Modal.confirm({
          title: '提示',
          content: '有未保存的新加模板,是否继续?',
          onOk: () => {
            this.setItemVal(item)
            this.curIndex = index
            this.hasAddTemplate = false
          },
          onCancel() {}
        });
      } else {
        this.setItemVal(item)
        this.curIndex = index
      }
    },
    setItemVal(item) {
      this.curEditContent.detail = item.detail
      this.curEditContent.fields = item.fields
      this.contratForm.id = item.id
      this.contratForm.default = item.default
      this.contratForm.name = item.name
      this.contratForm.type = parseInt(item.type)
    },
    saveModifyName() {
      return this.$service
        .post('/Web/contractTemplate/modifyName',this.modifyForm)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.showEditName = false
            this.templateList.forEach((item)=>{
              if(item.id == this.modifyForm.id) {
                item.name = this.modifyForm.name
                this.contratForm.name = this.modifyForm.name
                item.id = res.data.data.id //默认模板ID可能会在更新后改变
              }
            });
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    saveTemplate() {
      let postData = this.contratForm
      if(this.hasAddTemplate) {
        postData = this.addTemplateForm
      }
      postData.detail = this.curEditContent.detail
      postData.userPosi = this.curEditContent.userPosi
      postData.busPosi = this.curEditContent.busPosi
      return this.$service
        .post('/Web/contractTemplate/save',postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getTemplateList();
            this.hasAddTemplate = false;
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    modifyName(item) {
      this.showEditName = true
      this.modifyForm.id = item.id
      this.modifyForm.name = item.name
    },
    delTemplate(item) {
      return this.$service
        .post('/Web/contractTemplate/delete',{id: item.id})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.getTemplateList()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTemplateList() {
      return this.$service
        .get('/Web/contractTemplate/templateList')
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.templateList = resData.list
            this.setItemVal(resData.list[0])
            this.hasAddTemplate = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTypeAndfields() {
      return this.$service
        .get('/Web/contractTemplate/typeAndfields')
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.typeList = resData
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
<style lang="less" scoped>
.con-list {
  border-top: 1px solid #f5f7fb;
  color: #313131;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 35px;
  li {
    height: 62px;
    line-height: 62px;
    position: relative;
    padding-left: 50px;
    border-bottom: 1px solid #f5f7fb;
    cursor: pointer;
    span {
      display: inline-block;
      vertical-align: middle;
      width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 5px;
    }
    img {
      display: none;
      cursor: pointer;
      margin-right: 20px;
      vertical-align: middle;
    }
    &::after{
      background: #8dd1ca;
      content: '';
      width: 4px;
      height: 4px;
      border-radius: 50%;
      position: absolute;
      left: 36px;
      top: 50%;
      transform: translateY(-50%)
    }
     &.cur,
    &:hover {
      color: #52a4ea;
    }
    &:hover {
      img{
        display: inline-block;
      }
    }
  }
}
.contract-button{
  background: #7fccc4;
  border: 1px solid #7fccc4;
  margin-left: 36px;
}
.msg-button {
  border: 1px solid rgb(241, 243, 247);
  background-color: #fff;
  border-radius: 2px;
  color: #979faf;
  text-align: center;
  box-shadow: 0px 3px 5px 0px rgba(53, 73, 93, 0.1);
  height: 34px;
  line-height: 34px;
  overflow: hidden;
}
.msg-box {
  width: 100%;
  height: 100%;
  .msg-left {
    overflow-y: scroll;
    background-color: #fff;
    box-shadow: 0px 3px 20px 0px rgba(44, 57, 69, 0.1);
    position: absolute;
    width: 360px;
    height: 100%;
    padding: 32px 0;
    font-size: 14px;
    box-sizing: border-box;
    float: left;
  }
}

.msg-rig {
  margin-left: 395px;
  .rig-top {
    height: 90px;
    padding-top: 32px;
    .rig-button {
      .msg-button;
      display: inline-block;
      width: 98px;
      margin-right: 18px;
      cursor: pointer;
      &.cur,
      &:hover {
        color: #fff;
        background: #52a4ea;
      }
    }
  }
  .rig-con {
    margin-right: 35px;
    padding-bottom: 30px;
  }

}
</style>
