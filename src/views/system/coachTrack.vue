<template>
  <div class="addupinn3">
    <div class="addupinn3-content">
      <!-- <div class="addupinn3-t">
        <h2>参数设置</h2>
      </div> -->
      <div class="addupinn3-b">
        <div class="lay3 clearfix">
          <p class="lay-title3">排课设置</p>
          <div class="lay_content3">
            <p class="day3">排课超过</p>
            <input type="text" v-model="outDateTime" class="form-control day_input3 mg203" :disabled="!isEdit">
            <p class="day_test2">天，自动删除</p>
          </div>
          <div class="sub">
            <span class="nodes3">注：是指排课后，一直未上课，超过上课时间的天数后自动删除，默认为0天表示不删除；</span>
          </div>
        </div>
        <div class="public-sea">
          <div class="sea-title">公海设置</div>
          <div class="sea-settings">
            <span class="sea-label">对非私教会员，教练跟进有效时长</span>
            <span class="sea-value">
              <input class="sea-value-ipt" type="text" v-model="coach_followup_unconverted" :disabled="!isEdit"> 天
            </span>
            <span class="sea-desc nodes3 nodenext">注：教练多少天未跟进名下非私教会员，将会员调入公海；默认0天，0表示不启用。</span>
          </div>
          <div class="sea-settings">
            <span class="sea-label">对名下私教会员，教练跟进有效时长</span>
            <span class="sea-value">
              <input class="sea-value-ipt" type="text" v-model="coach_followup_converted" :disabled="!isEdit"> 天
            </span>
            <span class="sea-desc nodes3 nodenext">注：教练多少天未跟进名下私教会员，将会员调入公海；默认0天，0表示不启用。</span>
          </div>
          <div class="sea-settings">
            <span class="sea-label">教练在有效跟进期内，由其他教练成单，对该会员的归属设置</span>
            <span class="sea-value">
              <input class="sea-value-rdo" type="radio" name="affect" id="normal" v-model="coach_followup_change" value="0" :disabled="!isEdit">
              <label for="normal">保持原有跟进教练不变</label>
              <input class="sea-value-rdo" type="radio" name="affect" id="public" v-model="coach_followup_change" value="1" :disabled="!isEdit">
              <label for="public">将会员调入公海</label>
            </span>
          </div>
        </div>
        <div class="public-sea">
          <div class="sea-title">约课设置</div>
          <div class="sea-settings">
            <!-- <span class="sea-label">普通教练（名下权限）教练取消约课的权限</span> -->
            <span class="sea-label">普通教练是否可以取消已排私教课</span>
            <span class="sea-value">
              <input class="sea-value-rdo" type="radio" name="state" id="start" v-model="coach_cancel_pt_auth" value="1" :disabled="!isEdit">
              <label for="start">不可取消</label>
              <input class="sea-value-rdo" type="radio" name="state" id="disable" v-model="coach_cancel_pt_auth" value="0" :disabled="!isEdit">
              <label for="disable">可以取消</label>
            </span>
          </div>
        </div>
        <div class="lay3">
          <p style="padding-left: 35px; padding-bottom: 20px">是否在教练端启用教练排课的功能</p>
          <div class="sub">
            <!-- <radio-groups :radios="openCoach"></radio-groups> -->
            <input class="sea-value-rdo" type="radio" name="openCoach" id="start2b" v-model="is_set_coach_reservation_user" value="1" :disabled="!isEdit">
            <label for="start">是</label>
            <input class="sea-value-rdo" type="radio" name="openCoach" id="disable2b" v-model="is_set_coach_reservation_user" value="0" :disabled="!isEdit">
            <label for="disable">否</label>
          </div>
        </div>
        <div class="lay3">
          <p style="padding-left: 35px; padding-bottom: 20px">教练开单<p>
          <div class="sub">
            <div>
              是否启用教练开单
              <Tooltip>
                <div slot="content"
                     style="white-space: normal; width: 200px">设置是否允许教练开单、续卡、升卡</div>
                <Icon size="16"
                      type="ios-help-circle"
                      style="padding-right: 5px"
                      color="#ffcf05"></Icon>
              </Tooltip>
              <i-switch v-model="preOrder" :disabled="!isEdit"></i-switch>
              <Checkbox  style="margin-left: 8px" true-value="1" false-value="0" v-model="canEditFilds" :disabled="!isEdit">可修改卡售价、赠送、有效期等</Checkbox>
              <Checkbox  style="margin-left: 8px" true-value="1" false-value="0" v-model="can_edit_create_time" :disabled="!isEdit">可修改开卡时间</Checkbox>
            </div>
            <div style="margin-top:10px;">
              是否启用开单核查
              <Tooltip>
                <div slot="content"
                     style="white-space: normal; width: 200px">设置是否需要前台审核教练开单、续卡、升卡信息</div>
                <Icon size="16"
                      type="ios-help-circle"
                      style="padding-right: 5px"
                      color="#ffcf05"></Icon>
              </Tooltip>
              <i-switch v-model="checkOrder" :disabled="!isEdit"></i-switch>
            </div>
          </div>
        </div>
        <Row class="row-lh">
					<Col span="22">
					<Button v-if="!isEdit" type="success" style="background:#5fb75d;" @click="isEdit=true">编辑</Button>
					<Button v-if="isEdit" type="success" style="background:#5fb75d;" @click="saveParam">保存</Button>
					<Button v-if="isEdit" style="margin-left: 8px" @click="handleCancelEdit">取消</Button>
					</Col>
				</Row>
      </div>
    </div>
  </div>
</template>
<script>
  // import radioGroups from '../../components/form/radioGroup.vue';

  export default {
    mounted() {
      this.getParam()
    },
    data() {
      return {
        isEdit: false,
        outDateTime: "0", // 排课默认过期时间， 0 表示过期删除
        // openCoach: [{
        //   value: "是",
        //   checked: true
        // }, {
        //   value: "否",
        //   checked: false
        // }],
        is_set_coach_reservation_user: '0',
        busList: [],
        coach_followup_unconverted: '0',
        coach_followup_converted: '0',
        coach_followup_change: '0',
        coach_cancel_pt_auth: '0',
        preOrder: false,
        canEditFilds: '0',
        can_edit_create_time: '0',
        checkOrder: false
      }
    },
    // components: {
    //   radioGroups
    // },
    computed: {
      isOpenCoach: function () {
        return this.openCoach[0].checked ? 1 : 0;
      },
    },
    methods: {
      handleCancelEdit() {
        this.isEdit = false;
        this.getParam();
      },
      getParam() {
        const url = '/Web/SetClient/get_coach_setting_bus';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            let data = res.data.data;
            this.outDateTime = data.no_operate_day;
            // this.openCoach[0].checked = data.is_set_coach_reservation_user === '1';
            // this.openCoach[1].checked = data.is_set_coach_reservation_user === '0';
            this.is_set_coach_reservation_user = data.is_set_coach_reservation_user;
            this.coach_followup_unconverted = data.coach_followup_unconverted;
            this.coach_followup_converted = data.coach_followup_converted;
            this.coach_followup_change = data.coach_followup_change;
            this.coach_cancel_pt_auth = data.coach_cancel_pt_auth;
            this.preOrder = data.coach_billed == 1;
            this.canEditFilds = data.can_edit_fields;
            this.can_edit_create_time = data.can_edit_create_time;
            this.checkOrder = data.coach_audit == 1;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          console.error(err)
        })
      },

      saveParam: function () {

        if (!new RegExp("^[0-9]*$").test(this.coach_followup_unconverted)) {
          this.$Message.error("请输入数字！");
          $('.sea-value-ipt:first').focus();
          return false;
        }
        if (!new RegExp("^[0-9]*$").test(this.coach_followup_converted)) {
          this.$Message.error("请输入数字！");
          $('.sea-value-ipt:second').focus();
          return false;
        }

        let postData = {
          no_operate_day: this.outDateTime,
          is_set_coach_reservation_user: this.is_set_coach_reservation_user,
          coach_followup_unconverted: this.coach_followup_unconverted,
          coach_followup_converted: this.coach_followup_converted,
          coach_followup_change: this.coach_followup_change,
          coach_cancel_pt_auth: this.coach_cancel_pt_auth,
          coach_billed: this.preOrder?1:0,
          can_edit_fields: this.canEditFilds,
          can_edit_create_time: this.can_edit_create_time,
          coach_audit: this.checkOrder?1:0
        };
        this.$service.post('/Web/SetClient/coach_setting_bus', postData)
          .then(response => {
            if (response.status === 200) {
              this.$Message.success(response.data.errormsg);
              const self = this;
              setTimeout(() => {
                self.isEdit = false;
              }, 1000);
            }
          }, () => {
            this.$Message.error('服务器忙，请稍后再试！');
          });
      },

      getBusList: function () {
        let url = '/Mobile/Public/get_all_bus';
        this.$service.post(url, {}).then(res => {
          if (res.status === 200) {
            this.busList = res.data.data
          }
        })
      }
    },
  }
</script>
<style scoped>
  .form-control {
    padding: 6px 12px;
  }

  .row-lh {
		height: 52px;
		display: flex;
		align-items: center;
    margin-top: 30px;
	}

  .addupinn3-content {
    width: 100%;
    /* background: #ececec;
    padding: 40px 48px; */
  }

  .sub {
    padding-left: 35px;
  }

  .addupinn3-t {
    width: 100%;
    background: #f7f7f7;
    height: 37px;
    padding: 0 20px;
    overflow: hidden;
    border: 1px solid #dcdcdc;
  }

  .addupinn3-t h2 {
    margin: 0;
    font-size: 14px;
    margin-top: 12px;
    font-weight: bold;
  }

  .addupinn3-b {
    width: 100%;
    /* border: 1px solid #dcdcdc; */
    background: #fff;
    padding: 30px 35px 56px 35px;
  }

  .lay3 {
    width: 100%;
    padding: 35px;
    border: 1px solid #dedede;
  }

  .lay:nth-child(2) {
    border-top: none;
  }

  .lay-title3 {
    width: 116px;
    font-size: 14px;
    color: #333333;
    padding-top: 35px;
    padding-left: 35px;
    float: left;
  }

  .lay_content3 {
    float: left;
    margin-right: 30px;
  }

  .day3 {
    line-height: 88px;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
    padding-right: 10px;
  }

  .days {
    clear: both;
    line-height: 88px;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
  }

  .day_test2 {
    line-height: 88px;
    display: inline-block;
    color: #666666;
    font-size: 14px;
  }

  .nodes3 {
    display: inline-block;
    color: #999999;
    font-size: 14px;
    line-height: 88px;
  }
  .nodenext {
    width: 100%;
    /* line-height: 35px;
    margin-bottom: 30px;
    display: inline-block;
    color: #999999;
    font-size: 14px; */
  }

  .day_input3 {
    display: inline-block;
    float: left;
    width: 54px;
    margin-right: 7px;
  }

  .openOcean {
    font-size: 14px;
    color: #666666;
    float: left;
    margin: 0;
  }

  .check {
    float: left;
    margin-left: 48px;
  }

  .people {
    clear: both;
    color: #666666;
    font-size: 14px;
    margin: 0;
    float: left;
    line-height: 35px;
  }

  .people_test {
    display: inline-block;
    color: #999999;
    font-size: 14px;
    margin-left: 20px;
    line-height: 35px;
  }

  .mg203 {
    margin-top: 27px;
  }

  .day_pp {
    display: block;
    float: left;
    line-height: 35px;
  }

  .addupinn3-on {
    overflow: hidden;
    clear: both;
    width: 70px;
    height: 30px;
    line-height: 28px;
    background-color: #5fb75d;
    color: #fff;
    border: none;
    border-radius: 4px;
    margin-left: 346px;
    margin-top: 56px;
  }

  .addupinn3-on:hover {
    opacity: 0.7;
  }

  .addupinn3-off {
    width: 70px;
    height: 30px;
    line-height: 28px;
    background: #fff;
    color: #5fb75d;
    border: 1px solid #5fb75d;
    border-radius: 4px;
    margin-left: 72px;
    margin-top: 56px;
  }

  .addupinn3-off:hover {
    background-color: #5fb75d;
    color: #fff;
  }

  .public-sea {
    border: 1px solid #dedede;
    padding: 20px 20px 20px 60px;
  }

  .sea-settings {
    margin-top: 10px;
  }

  .sea-value-ipt  {
    border: 1px solid #ccc;
    width: 54px;
    padding: 6px 12px;
  }

  .sea-desc {
    margin-left: 30px;
  }

  .sea-value-rdo {
    margin-left: 30px;
  }
</style>
