<template>
  <div class="diy-box">
    <PrivateTabs :index="curTab" :list="filterTabList" @on-click="handleChange">
      <component
        v-if="filterTabList[curTab].componentName"
        :is="filterTabList[curTab].componentName"
        @change-showTips="handleChangeShowTips"
        @save-mode="handleSaveMode"
      ></component>
    </PrivateTabs>
  </div>
</template>

<script>
import { computed } from 'vue';
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import PrivateTabs from 'components/tabs'
import DiyPageMode from './components/diy/DiyPageMode'
import DiyPageColor from './components/diy/DiyPageColor'
import DiyPageLogin from './components/diy/DiyPageLogin'
import DiyPageIndex from './components/diy/DiyPageIndex'
import DiyPageReserve from './components/diy/DiyPageReserve'
import DiyPageTabBar from './components/diy/DiyPageTabBar'
import DiyPageOther from './components/diy/DiyPageOther'
import DiyPageGuide from './components/diy/DiyPageGuide'
import DiyPageCard from './components/diy/DiyPageCard'
import EventBus from 'components/EventBus.js';
import 'swiper/css/swiper.css'
import 'src/styles/theme.less'
import 'src/styles/iconfont.css'
export default {
  name: 'diyNew',
  components: {
    PrivateTabs,
    DiyPageMode,
    DiyPageColor,
    DiyPageLogin,
    DiyPageReserve,
    DiyPageGuide,
    DiyPageTabBar,
    DiyPageOther,
    DiyPageCard,
    DiyPageIndex
  },

  provide() {
    return {
      IS_BRAND_SITE: computed(() => !!window.IS_BRAND_SITE)
    }
  },

  data() {
    return {
      curTab: 0,
      showTips: false,
      // save_type  后端接口在保存会员端装修设置的时候定义的save_type值
      tabList: [
        {
          name: '运营模式',
          componentName: 'DiyPageMode',
          icon: 'color.png',
          iconSel: 'color-sel.png',
          save_type: 9
        },
        {
          name: '外观颜色',
          componentName: 'DiyPageColor',
          icon: 'color.png',
          iconSel: 'color-sel.png',
          save_type: 1
        },
        {
          name: '首页装修',
          icon: 'home.png',
          componentName: 'DiyPageIndex',
          iconSel: 'home-sel.png',
          save_type: 2
        },
        {
          name: '预约页面',
          componentName: 'DiyPageReserve',
          icon: 'reserve.png',
          iconSel: 'reserve-sel.png',
          save_type: 3
        },
        {
          name: '卡课列表',
          componentName: 'DiyPageCard',
          icon: 'cardpage.png',
          iconSel: 'cardpage-sel.png',
          save_type: 8
        },
        {
          name: '底部导航',
          componentName: 'DiyPageTabBar',
          icon: 'tabbar.png',
          iconSel: 'tabbar-sel.png',
          save_type: 5
        },{
          name: '进店指引',
          componentName: 'DiyPageGuide',
          icon: 'tabbar.png',
          iconSel: 'tabbar-sel.png',
          save_type: 5
        },{
          name: '其它设置',
          componentName: 'DiyPageOther',
          icon: 'other.png',
          iconSel: 'other-sel.png',
          save_type: ''
        }
      ],

      mode: '0' // 1 综合体育场版 0 健身瑜伽版
    }
  },
  computed: {
    ...mapState(['busId', 'busIdEncode', 'merchantId']),
    ...mapState('diy', ['selectMerBusId', 'firstBusIdEncode', 'theme9']),
    ...mapGetters('diy', ['isMerchantMode']),
    filterTabList() {
      const list = [...this.tabList]
      // 如果不是商家端则不展示运营模式
      if (!window.IS_BRAND_SITE) {
        list.splice(0, 1)
      } else {
        // 如果是商家端并且是综合馆模式
        if (this.mode === '1') {
          list.splice(list.findIndex(v => v.componentName === 'DiyPageCard'), 1)
        }
      }
      return list
    }
  },

  async created() {
    const unwatch = this.$watch('theme9', function() {
      this.handleSaveMode(this.theme9 ? this.theme9.mode : '0')
      unwatch()
    });

    this.selectMerBusId || this.SET_SELECT_MER_BUS_ID({
      id: window.IS_BRAND_SITE ? 'm' + this.merchantId : this.busId,
      encodeId: window.IS_BRAND_SITE ? this.firstBusIdEncode : this.busIdEncode
    })

    await this.getMerchantsBusList()
    this.linkListInit(this.firstBusIdEncode || this.busIdEncode)
  },
  beforeDestroy() {
    this.$store.dispatch('diy/RESET_LINKS')
  },


  methods: {
    ...mapActions('diy', ['linkListInit', 'getMerchantsBusList']),
    ...mapMutations('diy', ['SET_SELECT_MER_BUS_ID']),
    getTabLabel(index) {
      return h => {
        const imgPath =
          index == this.curTab
            ? this.filterTabList[index].iconSel
            : this.filterTabList[index].icon
        return (
          <div class="tab-nav">
            <img src={require('../../assets/img/diy/' + imgPath)} />
            {this.filterTabList[index].name}
          </div>
        )
      }
    },
    // 切换tab事件
    handleChange(key) {
      // 点击当前tab
      if (key === this.curTab) {
        return null
      }
      const item = this.filterTabList[this.curTab]
      EventBus.$emit('on-diy-tab-change', {
        type: item && item.name === '其它设置' ? this.$store.state.diy.otherSelectType : this.filterTabList[this.curTab].save_type,
        success: ()=> {
          this.curTab = key
          const item = this.filterTabList[this.curTab]
          this.$store.commit('diy/UPDATE_INDEX_LEFT_SELECTED', {
            value: 0,
            type: item && item.name === '其它设置'
              ? this.$store.state.diy.otherSelectType
              : this.filterTabList[this.curTab].save_type
          })
        }
      })
    },
    handleChangeShowTips(bool) {
      this.showTips = bool
    },

    handleSaveMode(val) {
      this.mode = val
    }
  }
}
</script>
<style lang="less">
.diy-box .info-wrap {
  width: 100%;
  height: 100%;
}
.diy-box > .ivu-tabs .ivu-tabs-ink-bar {
  display: none;
}
.diy-box > .ivu-tabs > .ivu-tabs-content > .ivu-tabs-tabpane {
  background: #fff;
  padding: 16px;
}

.diy-box > .ivu-tabs > .ivu-tabs-bar .ivu-tabs-tab-active {
  color: #515a6e;
}
.diy-box .tab-nav {
  margin-left: 32px;
  display: flex;
  align-items: center;
}
.diy-box .tab-nav img {
  width: 30px;
  height: 30px;
}

.bot-wrap {
  margin: 20px;
  padding: 20px 20px 0;
  background: #ffffff;
  border-radius: 10px;
  overflow: hidden;
}
.box-tit {
  position: relative;
  font-size: 36px;
  font-weight: bold;
  line-height: 100px;
  padding-left: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.box-tit .rig {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 400;
}
.box-tit::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: ' ';
  width: 6px;
  height: 32px;
  background: var(--THEME-COLOR);
  border-radius: 3px;
}
.arrow-right {
  width: 10px;
  height: 10px;
  border-top: 1px solid #03080e;
  border-right: 1px solid #03080e;
  transform: rotate(45deg);
}
.diy-box {
  width: 100%;
  // height: 100%;
  background: #fff;
  //无数据
  .nodata {
    width: 100%;
    height: 100%;
    position: relative;
    text-align: center;
    line-height: 50px;
    min-width: 286px;
    min-height: 289px;
    box-sizing: border-box;
    padding-top: 250px;
    margin: 0 auto;
    font-size: 24px;
  }
  .nodata-min {
    width: auto;
    margin-top: 150px;
    font-size: 14px;
  }
  .nodata::before {
    position: absolute;
    width: 103px;
    height: 144px;
    content: ' ';
    top: 144px;
    left: 50%;
    transform: translate(-50%, -50%);
    background: url('https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png')
      left top no-repeat;
    background-size: cover;
  }
}
</style>
