<template>
  <div class="source">
    <div class="source-content">
      <div class="source-b">
        <div class="source_b_list">
          <div class="source_b_l">
            <p class="source_nav">获客来源</p>
          </div>
          <div class="source_b_r">
            <ul>
              <li v-for="(item1, index) in type1" @mouseover="isshow(index,1)" @mouseout="ishide(1)">
                <div class="testchange" v-if="emit1!==item1.source_id">
                  <p class="sourcetest" :class="{'testcolor':is1===index&&item1.can_deleted==0,'huicolor':item1.can_deleted!=0}" v-html="blank(item1.names)"></p>
                  <i class="testemit" v-if="isEdit1" v-show="item1.can_deleted==0&&is1===index" @click="emitfn(item1.source_id,index,item1.type)"></i>
                  <i class="testdel" v-if="isEdit1" v-show="item1.can_deleted==0&&is1===index" @click="delfn(item1.source_id,item1.type,index)"></i>
                </div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="item1.name" id="type1emit" maxlength="10">
                  <i class="modifyy" @click="agreefn(item1.source_id,index,item1.type)"></i>
                  <i class="modifyn" @click="candelfn(index,item1.type)"></i>
                </div>
              </li>
              <li v-if="isEdit1">
                <div class="addsource" v-if="addlist1" @click="addlist1fn(1)">添加</div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="itme1name" id="type1add" maxlength="10">
                  <i class="modifyy" @click="itme1add(1)"></i>
                  <i class="modifyn" @click="item1candel(1)"></i>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="source_b_list">
          <div class="source_b_l">
            <p class="source_nav">会籍成交方式</p>
          </div>
          <div class="source_b_r">
            <ul>
              <li v-for="(item2, index) in type2" @mouseover="isshow(index,2)" @mouseout="ishide(2)">
                <div class="testchange" v-if="emit2!==item2.source_id">
                  <p class="sourcetest" :class="{'testcolor':is2===index&&item2.can_deleted==0}" v-html="blank(item2.names)"></p>
                  <i class="testemit" v-if="isEdit1" v-show="item2.can_deleted==0&&is2===index" @click="emitfn(item2.source_id,index,item2.type)"></i>
                  <i class="testdel" v-if="isEdit1" v-show="item2.can_deleted==0&&is2===index" @click="delfn(item2.source_id,item2.type,index)"></i>
                </div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="item2.name" id="type2emit" maxlength="10">
                  <i class="modifyy" @click="agreefn(item2.source_id,index,item2.type)"></i>
                  <i class="modifyn" @click="candelfn(index,item2.type)"></i>
                </div>
              </li>
              <li v-if="isEdit1">
                <div class="addsource" v-if="addlist2" @click="addlist1fn(2)">添加</div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="itme2name" id="type2add" maxlength="10">
                  <i class="modifyy" @click="itme1add(2)"></i>
                  <i class="modifyn" @click="item1candel(2)"></i>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="source_b_list">
          <div class="source_b_l">
            <p class="source_nav">私教成交方式</p>
          </div>
          <div class="source_b_r">
            <ul>
              <li v-for="(item3, index) in type3" @mouseover="isshow(index,3)" @mouseout="ishide(3)">
                <div class="testchange" v-if="emit3!==item3.source_id">
                  <p class="sourcetest" :class="{'testcolor':is3===index&&item3.can_deleted==0}" v-html="blank(item3.name)"></p>
                  <i class="testemit" v-if="isEdit1" v-show="item3.can_deleted==0&&is3===index" @click="emitfn(item3.source_id,index,item3.type)"></i>
                  <i class="testdel" v-if="isEdit1" v-show="item3.can_deleted==0&&is3===index" @click="delfn(item3.source_id,item3.type,index)"></i>
                </div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="item3.name" id="type3emit" maxlength="10">
                  <i class="modifyy" @click="agreefn(item3.source_id,index,item3.type)"></i>
                  <i class="modifyn" @click="candelfn(index,item3.type)"></i>
                </div>
              </li>
              <li v-if="isEdit1">
                <div class="addsource" v-if="addlist3" @click="addlist1fn(3)">添加</div>
                <div class="modifybox" v-else>
                  <input type="text" class="form-control modify" v-model="itme3name" id="type3add" maxlength="10">
                  <i class="modifyy" @click="itme1add(3)"></i>
                  <i class="modifyn" @click="item1candel(3)"></i>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <Row class="row-lh">
					<Col span="22" offset="1">
					<Button v-if="!isEdit1" type="success" style="background:#5fb75d;" @click="isEdit1=true">编辑</Button>
					<Button v-if="isEdit1" style="margin-left: 8px" @click="isEdit1=false">取消</Button>
					</Col>
				</Row>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        isEdit1: false,
        type1: "",
        type2: "",
        type3: "",
        isemit: false, //默认为不编辑状态
        // 1
        is1: -1,
        emit1: -1,
        itme1name: "",
        addlist1: true,
        // 2
        is2: -1,
        emit2: -1,
        itme2name: "",
        addlist2: true,
        //3
        is3: -1,
        emit3: -1,
        itme3name: "",
        addlist3: true
      };
    },
    mounted() {
      this.get_sources();
    },
    // filters: {
    //   blank: function(value, blank) {
    //     return value.replace(/ /g, "&nbsp;");
    //   }
    // },
    methods: {
      blank: function(value) {
        return value.replace(/ /g, "&nbsp;");
      },
      get_sources: function() {
        let url = "/Web/Source/get_sources";
        let postdata = {};
        this.$service
          .post(url, {})
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                this.type1 = response.data.data.type1;
                for (var i = 0; i < this.type1.length; i++) {
                  this.type1[i].names = this.type1[i].name;
                }
                this.type2 = response.data.data.type2;
                for (var j = 0; j < this.type2.length; j++) {
                  this.type2[j].names = this.type2[j].name;
                }
                this.type3 = response.data.data.type3;
                for (var k = 0; k < this.type3.length; k++) {
                  this.type3[k].names = this.type3[k].name;
                }
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            this.$Message.error("网络错误");
            console.error(response);
          });
      },
      isshow: function(id, index) {
        if (index == 1) {
          this.is1 = id;
        } else if (index == 2) {
          this.is2 = id;
        } else if (index == 3) {
          this.is3 = id;
        }
      },
      ishide: function(index) {
        if (index == 1) {
          this.is1 = -1;
        } else if (index == 2) {
          this.is2 = -1;
        } else if (index == 3) {
          this.is3 = -1;
        }
      },
      // 编辑
      emitfn: function(id, index, type) {
        if (type == 1) {
          this.emit1 = id;
          this.$nextTick(function() {
            document.getElementById("type1emit").focus();
          });
        } else if (type == 2) {
          this.emit2 = id;
          this.$nextTick(function() {
            document.getElementById("type2emit").focus();
          });
        } else if (type == 3) {
          this.emit3 = id;
          this.$nextTick(function() {
            document.getElementById("type3emit").focus();
          });
        }
      },
      // 删除
      delfn: function(id, type, index) {
        console.log(id);
        let deldata = {};
        deldata.source_id = id;
        deldata.source_type = type;
        this.set_sources(type, deldata, 3, index);
      },
      // 提交
      agreefn: function(id, index, type) {
        // 有name有id为编辑,有name无id时为添加,有id没name时为删除
        let emitdata = {};
        if (type == 1) {
          emitdata.source_name = this.type1[index].name;
        } else if (type == 2) {
          emitdata.source_name = this.type2[index].name;
        } else if (type == 3) {
          emitdata.source_name = this.type3[index].name;
        }
        emitdata.source_id = id;
        emitdata.source_type = type;
        this.set_sources(type, emitdata, 1, index);
      },
      // 取消
      candelfn: function(index, type) {
        if (type == 1) {
          this.emit1 = -1;
          this.type1[index].name = this.type1[index].names;
        } else if (type == 2) {
          this.emit2 = -1;
          this.type2[index].name = this.type2[index].names;
        } else if (type == 3) {
          this.emit3 = -1;
          this.type3[index].name = this.type3[index].names;
        }
      },
      // 添加
      addlist1fn: function(type) {
        if (type == 1) {
          this.addlist1 = false;
          this.$nextTick(function() {
            document.getElementById("type1add").focus();
          });
        } else if (type == 2) {
          this.addlist2 = false;
          this.$nextTick(function() {
            document.getElementById("type2add").focus();
          });
        } else if (type == 3) {
          this.addlist3 = false;
          this.$nextTick(function() {
            document.getElementById("type3add").focus();
          });
        }
      },
      itme1add: function(type) {
        let adddata = {};
        if (type == 1) {
          adddata.source_name = this.itme1name;
          adddata.source_type = 1;
          this.set_sources(type, adddata, 2);
          this.addlist1 = true;
          return;
        } else if (type == 2) {
          adddata.source_name = this.itme2name;
          adddata.source_type = 2;
          this.set_sources(type, adddata, 2);
          this.addlist2 = true;
          return;
        } else if (type == 3) {
          adddata.source_name = this.itme3name;
          adddata.source_type = 3;
          this.set_sources(type, adddata, 2);
          this.addlist3 = true;
          return;
        }
      },
      item1candel: function(type) {
        if (type == 1) {
          this.itme1name = "";
          this.addlist1 = true;
        } else if (type == 2) {
          this.itme2name = "";
          this.addlist2 = true;
        } else if (type == 3) {
          this.itme3name = "";
          this.addlist3 = true;
        }
      },
      // 提交函数
      set_sources: function(type, postdata, num, index) {
        if (postdata.source_name === "") {
          this.$Message.error('请输入名称!');
          return false;
        }
        // type为类型，num为提交类型 1为编辑 2为添加 3为删除
        let url = "/Web/Source/set_sources";
        this.$service
          .post(url, postdata)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                console.log("成功编辑");
                if (type == 1) {
                  this.emit1 = -1;
                  if (num == 1) {
                    // 编辑
                    this.type1[index].names = this.type1[index].name;
                    return;
                  } else if (num == 2) {
                    // 添加
                    this.get_sources();
                    this.itme1name = "";
                  } else {
                    // 删除
                    this.type1.splice(index, 1);
                  }
                } else if (type == 2) {
                  this.emit2 = -1;
                  if (num == 1) {
                    // 编辑
                    this.type2[index].names = this.type2[index].name;
                    return;
                  } else if (num == 2) {
                    // 添加
                    console.log(2);
                    this.get_sources();
                    this.itme2name = "";
                  } else {
                    // 删除
                    this.type2.splice(index, 1);
                  }
                } else if (type == 3) {
                  this.emit3 = -1;
                  if (num == 1) {
                    // 编辑
                    this.type3[index].names = this.type3[index].name;
                    return;
                  } else if (num == 2) {
                    // 添加
                    console.log(2);
                    this.get_sources();
                    this.itme3name = "";
                  } else {
                    // 删除
                    this.type3.splice(index, 1);
                  }
                }
              } else {
                this.$Message.error(response.data.errormsg);
                if (type == 1) {
                  if (num == 2) {
                    this.itme1name = "";
                  }
                } else if (type == 2) {
                  if (num == 2) {
                    this.itme2name = "";
                  }
                } else if (type == 3) {
                  if (num == 2) {
                    this.itme3name = "";
                  }
                }
              }
            }
          })
          .catch(function(response) {
            this.$Message.error("网络错误");
            console.error(response);
          });
      }
    }
  };
</script>
<style scoped>
  .row-lh {
		height: 52px;
		display: flex;
		align-items: center;
    margin-top: 30px;
	}

  .source {
    flex: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    position: relative;
    z-index: 10;
    height: 100%;
  }

  .source-content {
    width: 100%;
    /* background: #ececec; */
    /* padding: 40px 48px; */
  }
  .source-t {
    width: 100%;
    background: #f7f7f7;
    height: 37px;
    padding: 0 20px;
    overflow: hidden;
    border: 1px solid #dcdcdc;
  }
  .source-t h2 {
    margin: 0;
    font-size: 14px;
    margin-top: 12px;
    font-weight: bold;
  }
  .source-b {
    width: 100%;
    /* border: 1px solid #dcdcdc; */
    background: #fff;
    /* padding: 40px; */
    padding-top: 20px;
    overflow: hidden;
  }
  .source_b_list {
    clear: both;
    width: 100%;
    margin-top: 40px;
    overflow: hidden;
    position: relative;
  }
  .source_b_list:nth-child(1) {
    margin-top: 0;
  }
  .source_b_l {
    width: 12%;
    height: 100%;
    background: red;
    float: left;
    clear: both;
  }
  .source_nav {
    display: block;
    text-align: right;
    position: absolute;
    width: 9%;
    height: 100%;
    margin: 0;
    top: 90%; /*偏移*/
    transform: translateY(-50%);
    font-weight: 600;
  }
  .source_b_r {
    float: right;
    min-height: 70px;
    width: 88%;
    border: 1px solid #dedede;
  }
  .source_b_r ul {
    overflow: hidden;
    padding: 0;
    margin: 0;
    height: 100%;
  }
  /*.source_b_r ul li{
      	overflow: hidden;
      	display: block;
      	float: left;
      	width: 282px;
      	height: 67px;
      }*/
  @media screen and (min-width: 1601px) {
    .source_b_r ul li {
      overflow: hidden;
      display: block;
      float: left;
      width: 20%;
      height: 67px;
    }
  }
  @media screen and (min-width: 1201px) and (max-width: 1600px) {
    .source_b_r ul li {
      overflow: hidden;
      display: block;
      float: left;
      width: 33.3%;
      height: 67px;
    }
  }
  @media screen and (max-width: 1200px) {
    .source_b_r ul li {
      overflow: hidden;
      display: block;
      float: left;
      width: 33.3%;
      height: 67px;
    }
  }

  .testchange {
    position: relative;
  }
  .sourcetest {
    text-align: center;
    line-height: 67px;
    margin: 0;
  }
  .modifybox {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 67px;
  }
  .modify {
    width: 108px;
    height: 32px;
    /* margin: 0 auto; */
    margin-top: 17px;
    margin-left: 100px;
  }
  .addsource {
    display: block;
    width: 68px;
    height: 30px;
    overflow: hidden;
    margin: 0 auto;
    margin-top: 20px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    color: #5cb85c;
    font-size: 14px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #5cb85c;
    box-shadow: inset 1px 1px 0px #e6e5e5;
  }
  .addsource:hover {
    opacity: 0.7;
  }
  .testemit {
    position: absolute;
    right: 13%;
    bottom: 24px;
    display: block;
    width: 16px;
    height: 16px;
    background: url(../../assets/img/1234_1.png) no-repeat;
    background-position: 0 0;
    cursor: pointer;
  }
  .testemit:hover {
    opacity: 0.7;
    transition: 0.4s ease-out;
  }
  .testdel {
    position: absolute;
    right: 3%;
    bottom: 22px;
    display: block;
    width: 18px;
    height: 18px;
    background: url(../../assets/img/1234_1.png) no-repeat;
    background-position: -29px 0;
    cursor: pointer;
  }
  .testdel:hover {
    opacity: 0.7;
    transition: 0.4s ease-out;
  }
  .modifyy {
    position: absolute;
    right: 12%;
    bottom: 24px;
    display: block;
    width: 18px;
    height: 18px;
    background: url(../../assets/img/1234_1.png) no-repeat;
    background-position: 0 -41px;
    cursor: pointer;
  }
  .modifyy:hover {
    opacity: 0.7;
    transition: 0.4s ease-out;
  }
  .modifyn {
    position: absolute;
    right: 3%;
    bottom: 22px;
    display: block;
    width: 19px;
    height: 20px;
    background: url(../../assets/img/1234_1.png) no-repeat;
    background-position: -27px -41px;
    cursor: pointer;
  }
  .modifyn:hover {
    opacity: 0.7;
    transition: 0.4s ease-out;
  }
  .testcolor {
    color: #5cb85c;
    cursor: pointer;
  }
  .huicolor {
    color: #999;
  }
</style>