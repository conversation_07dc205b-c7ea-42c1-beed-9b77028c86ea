
<style lang="less">

</style>

<template>
  <div class="table-wrap abandon-list">
    <header style="border-bottom: 0">
      <Input v-model="search" placeholder="会员名称/电话" @on-enter="getList" />
      <Button type="success" @click="getList">搜索</Button>
    </header>
    <Table :columns="columns" :data="tableData" disabled-hover @on-selection-change="handleSelect" />
    <footer>
      <div class="left">
        <Button type="success" @click="handleRestore">批量恢复</Button>
      </div>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
import Image from 'assets/img/code.jpg';
import Avatar from 'assets/img/malecoach.png';
import { formatDate } from 'utils';

import pager from 'mixins/pager';
export default {
  name: 'abandonList',
  mixins: [pager],
  data() {
    return {
      search: '',
      selection: [],
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '头像',
          key: 'avatar',
          width: 80,
          render: (h, params) => {
            const item = params.row;
            return <img style="width: 44px; height: 44px" src={item.avatar} />;
          }
        },
        {
          title: '会员名称',
          key: 'username'
        },
        {
          title: '禁言操作时间',
          key: 'not_send_dynamic_time'
        },
        {
          title: '操作账号',
          key: 'not_send_dynamic_admin_name'
        },
        {
          title: '操作',
          key: 'operation',
          render: (h, params) => {
            const item = params.row;
            return (
              <i-button
                type="text"
                onClick={() => {
                  this.handleClickTableRestore(item.user_id);
                }}>
                恢复
              </i-button>
            );
          }
        }
      ],
      tableData: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClickTableRestore(user_id) {
      this.selection = [user_id]
      this.handleRestore()
    },
    handleRestore() {
      const url = '/Web/UserDynamic/restore_not_send_dynamic';
      this.$service
        .post(url, { user_ids: this.selection })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleSelect(selection) {
      this.selection = selection.map(item => item.user_id);
    },
    getList() {
      const url = '/Web/UserDynamic/not_send_dynamic_list';
      const { search, page: page_no, pageSize: page_size } = this;
      this.$service
        .post(url, { search, page_no, page_size })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list.map(item => {
              return {
                ...item,
                ...{
                  not_send_dynamic_time: formatDate(new Date(item.not_send_dynamic_time * 1000), 'MM-dd HH:mm')
                }
              };
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
