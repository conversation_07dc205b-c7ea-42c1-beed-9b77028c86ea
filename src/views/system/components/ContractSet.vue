<template>
  <div class="contract-set">
    <div class="box">
      <Form :label-width="160" class="modal-form">
        <FormItem label="会员端查看电子合同">
          <i-switch :disabled="!isEdit" true-value="1" false-value="0" v-model="postData.see_status"></i-switch>
        </FormItem>
      </Form>
    </div>
    <div class="buttons">
      <Button @click="isEdit = true" v-show="!isEdit" type="success">编辑</Button>
      <template v-if="isEdit">
        <Button type="success" @click="saveEdit">保存</Button>
        <Button @click="cancelEdit">取消</Button>
      </template>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'contractSet',
    data() {
      return {
        postData: {
          see_status: '1'
        },
        isEdit: false
      };
    },
    created() {
      this.getSetting();
    },
    methods: {
      cancelEdit() {
        this.getSetting()
        this.isEdit = false
      },
      getSetting() {
        this.$service
          .get('/web/ContractSetting/info')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.postData = res.data.info
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      saveEdit() {
        this.$service
          .post('web/ContractSetting/setting', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.isEdit = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .contract-set {
    padding: 30px;
    .box {
      border: 1px solid #eee;
      padding: 20px 20px 0;
    }
    .button {
      margin-top: 30px;
      margin-left: 30px;
    }
  }
</style>