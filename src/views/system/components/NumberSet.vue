<template>
  <div slot="content" class="panel">
    <Form :model="exchangeSet" :label-width="120">
      <FormItem label="积分兑换确认" prop="point_confirm">
        <RadioGroup v-model="exchangeSet.point_confirm">
          <Radio :disabled="!isEdit" :label="0">禁用</Radio>
          <Radio :disabled="!isEdit" :label="1">启用</Radio>
        </RadioGroup>
        <div>开启后使用积分兑换商品时，需会员短信验证码确认。</div>
      </FormItem>
      <FormItem>
        <Button v-show="!isEdit" type="success" @click="isEdit = true">编辑</Button>
        <Button v-show="isEdit" type="success" @click="saveEdit">保存</Button>
        <Button v-show="isEdit" style="margin-left: 8px" @click="cancelEdit">取消</Button>
      </FormItem>
    </Form>
    <Form :model="clearSet" :label-width="120">
      <form-item label="清除类型" prop="type">
        <radio-group v-model="clearSet.type">
          <radio :label="1">积分</radio>
          <!-- <radio :label="2">魅力值</radio> -->
        </radio-group>
      </form-item>
      <form-item label="选择时间" prop="s_date">
        <Date-picker
          v-model="clearSet.selected_date"
          format="yyyy-MM-dd"
          :editable="false"
          :options="disableDayAfter"
          type="date"
          style="width:300px;"
          placeholder="选择日期"
          @on-change="handleDateChange"
        />
        <div>
          清除所选日期及以前产生且未消耗的消费积分
        </div>
      </form-item>
      <form-item>
        <Button
          type="success"
          style="background:#5fb75d;"
          @click="clearData"
        >
          清除
        </Button>
        <Export ref="export" />
      </form-item>
    </Form>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Export from "src/components/Export";
export default {
  name: "NumberSet",
  components: {
    Export
  },
  data() {
    return {
      /* 积分兑换相关 */
      isEdit: false,
      exchangeSet: {
        point_confirm: 0, // 积分兑换确认 1启用 0禁用
      },

      /* 清除积分相关 */
      disableDayAfter: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      clearSet: {
        type: 1, // 1积分 2魅力值
        selected_date: ""
      },
    };
  },
  computed: {
    ...mapState(["busId"])
  },
  created() {
    this.getSettingData()
  },
  methods: {
    // 获取积分兑换确认设置
    getSettingData() {
      this.$service.post("/Web/PointSetting/getPintSetting").then(res => {
        if (res.data.errorcode === 0) {
         this.exchangeSet.point_confirm = +res.data.data.point_confirm;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },

      getSetting() {
        this.$service
          .get('/web/ContractSetting/info')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.postData = res.data.info
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      saveEdit() {
        const { point_confirm } = this.exchangeSet;
        this.$service.post('/Web/PointSetting/upPintSetting', { point_confirm }).then(res => {
            if (res.data.errorcode === 0) {
              this.isEdit = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      cancelEdit() {
        this.getSetting()
        this.isEdit = false
      },
    handleDateChange(val) {
      this.clearSet.selected_date = val;
    },
    clearData() {
      const { selected_date } = this.clearSet;
      if (!selected_date) {
        return this.$Message.error('请选择日期')
      }
      this.$Modal.confirm({
        title: "清除",
        content: `确认清除本门店下的会员${this.clearSet.type === 1 ? '积分'  : '魅力值'}？`,
        onOk: () => {
          const params = {
            date: selected_date,
          };
          this.$service
            .post("/Web/Point/PointCleanup", params)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        }
      });
    }
  }
};
</script>
