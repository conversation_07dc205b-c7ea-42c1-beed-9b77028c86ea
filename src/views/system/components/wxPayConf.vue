<template>
  <div>
    <div style="padding: 20px 60px 50px; border: 1px solid #f1f3f7; border-radius: 5px">
      <h3>说明</h3>
      <ul>
        <li>1、完成配置，“会员端购卡”等线上支付的款项可<span style="color: #f00;">直接入到商家账上，无需再提现</span>，如图：</li>
        <li style="padding: 30px 0">
          <figure style="display: inline-block">
            <img src="~assets/img/weixin-01.png" alt="">
            <figcaption style="text-align: center">图一 支付流程</figcaption>
          </figure>
          <figure style="display: inline-block; margin-left: 60px">
            <img src="~assets/img/weixin-02.png" alt="">
            <figcaption style="text-align: center">图二 支付页面</figcaption>
          </figure>
        </li>
        <li>2、请谨慎配置，一经保存，商家无法取消配置，若配置信息有误，会员将<span style="color: #f00;">无法支付</span></li>
        <li>3、
          <router-link :to="{ name: '使用帮助', query: { active: 3 } }">了解更多配置说明</router-link>
        </li>
        <li>
          <Button v-if="!hasSetting" type="success" @click="hasSetting = true" style="margin-top: 30px">配置</Button>
        </li>
      </ul>
    </div>
    <div v-if="hasSetting" class="contract-set">
      <div class="box">
        <Form :label-width="160" class="modal-form">
          <FormItem label="商户名称">
            <Input v-model="postData.sub_mch_name" :disabled="!isEdit"/>
          </FormItem>
          <FormItem label="商户号">
            <Input v-model="postData.sub_mch_id" :disabled="!isEdit"/>
          </FormItem>
        </Form>
      </div>
      <div class="buttons">
        <Button @click="isEdit = true" v-show="!isEdit" type="success">编辑</Button>
        <template v-if="isEdit">
          <Button type="success" @click="handleSave">保存</Button>
          <Button @click="cancelEdit">取消</Button>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'contractSet',
    data() {
      return {
        isFirstSetting: false,
        hasSetting: false,
        postData: {
          sub_mch_id: '',
          sub_mch_name: ''
        },
        isEdit: false
      };
    },
    created() {
      this.getSetting();
    },
    watch: {},
    methods: {
      cancelEdit() {
        this.getSetting();
        this.isEdit = false;
      },
      getSetting() {
        this.$service
          .get('/web/WxpaysubmchConf/get_setting_info')
          .then(res => {
            if (res.data.errorcode === 0) {
              if (!res.data.info.sub_mch_id) {
                this.isFirstSetting = true;
              } else {
                this.postData = res.data.info;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      handleSave() {
        if (this.isFirstSetting) {
          this.$Modal.confirm({
            title: '保存配置',
            content: '一经保存将无法取消配置，确定保存？',
            onOk: () => this.saveEdit()
          });
        } else {
          this.saveEdit();
        }
      },
      saveEdit() {
        if (!this.postData.sub_mch_id || !this.postData.sub_mch_name) return this.$Message.error('请填写商户信息');
        this.$service
          .post('/web/WxpaysubmchConf/save_setting_info', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.isEdit = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .contract-set {
    padding: 30px;
    .box {
      border: 1px solid #eee;
      padding: 20px;
    }
    .button {
      margin-top: 30px;
      margin-left: 30px;
    }
  }
</style>
