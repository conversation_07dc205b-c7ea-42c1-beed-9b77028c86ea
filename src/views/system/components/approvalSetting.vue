<template>
  <div class="approval">
    <Alert type="warning" show-icon style="width:426px;margin-bottom:15px;">
      开启审批后需添加审批人，若无审批人则无法查看审批数据
    </Alert>
    <div class="box">
      <Table
        :columns="column"
        :data="list"
        disabled-hover
      />
    </div>
    <!-- 修改接口是单个修改的，所以不需要编辑提交按钮了 -->
    <!-- <div class="buttons">
      <Button v-show="!isEdit" type="success" @click="isEdit = true">编辑</Button>
      <Button v-show="isEdit" type="success" @click="saveEdit">保存</Button>
      <Button v-show="isEdit" @click="cancelEdit">取消</Button>
    </div> -->
    <ApprovalEditModal
      :show.sync="showEditModal"
      :itemData="itemData"
      :readonly="readonly"
      @refresh="getSetting"
    />
  </div>
</template>

<script>
  import ApprovalEditModal from './approvalEditModal'

  const taskNames = {
    1: '购卡',
    2: '跨店购卡',
    3: '续卡',
    4: '升卡',
    5: '销卡',
    6: '拆分',
    7: '请假',
    8: '转卡',
    9: '补卡',
   10: '编辑会员卡',
   11: '赠体验卡/课',
   12: '会员信息',
   13: '会员头像',
   14: '折扣券赠送',
   15: '积分赠送', // 积分/金币赠送
  }

  export default {
    name: 'ApprovalSetting',
    components: {
      ApprovalEditModal
    },

    data() {
      return {
        isEdit: true, // 显示编辑按钮后取false
        showEditModal: false, // 审批人弹窗
        readonly: true, // 审批人弹窗是否只读
        itemData: {}, // 审批人弹窗修改数据
        list: [],
        column: [
          // { title: '序号', type: 'index', width: '80' },
          { title: '序号', key: 'approve_type', width: '80' },
          { title: '审批任务', key: 'taskName' },
          {
            title: '状态',
            key: 'status',
            render: (h, params) =>
              <i-switch
                value={ params.row.status }
                disabled={ !this.isEdit }
                true-value="1"
                false-value="0"
                on-on-change={ () => this.handleSwitchValue(params.row, 'status' /* key */) }
              />
          },
          {
            key: 'auto',
            renderHeader: (h, params) => (
              <div>
                自动审批
                <Tooltip placement="bottom" max-width="200">
                  <Icon type="ios-help-circle" style="color: #f4a627;font-size:18px;"></Icon>
                  <div slot="content">
                    关联购卡/续卡规则设置的浮动范围。范围内自动审批，超出范围推送至审批人。
                  </div>
                </Tooltip>
              </div>
            ),
            render: (h, params) => {
              const isShowCheck = ['1', '2', '3'].includes(params.row.approve_type)
              return isShowCheck ?
                <Checkbox
                  value={ params.row.auto_approve }
                  true-value="1"
                  false-value="0"
                  disabled={ !this.isEdit || params.row.status === '0' }
                  on-on-change={ () => this.handleSwitchValue(params.row, 'auto_approve' /* key */) }
                >自动审批</Checkbox>
                : <span>暂不支持</span>
            }

          },
          {
            title: '审批人数',
            key: 'num',
            render: (h, params) => {
              const { approve_admin_num: num } = params.row;
              return <Button
                type="text"
                disabled={ !this.isEdit || num == 0 }
                on-click={ () => this.handleShowEditModal(params.row) }
              >{ num }</Button>
            }
            },
          {
            title: '操作',
            render: (h, params) => (
              <i-button
                type="text"
                disabled={ !this.isEdit }
                onClick={ () => this.handleShowEditModal(params.row, false /* readonly */) }
              >编辑审批人</i-button>
            )
          },
        ],
      };
    },
    created() {
      this.getSetting();
    },
    methods: {
      /* 获取配置 */
      getSetting() {
        this.$service.get('Web/ApproveSet/get_approve_set').then(res => {
          if (res.data.errorcode === 0) {
            // 需后端返回
            this.list = res.data.data.map(v => {
              v.taskName = taskNames[v.approve_type] + '审批';
              return v
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      /* 修改配置 */
      putSetting(params, index, newItem) {
        this.$service.post('/Web/ApproveSet/set_approve_rule', params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.$set(this.list, index, newItem)
          } else {
            this.$Message.error(res.data.errormsg);
            this.getSetting()
          }
        }).catch(this.getSetting)
      },
      cancelEdit() {
        this.getSetting();
        this.isEdit = false;
      },
      cancelEditApprover() {
        this.showEditModal = false;
      },
      // 切换状态值/是否自动审批
      handleSwitchValue(item, key /* status、 auto_approve */) {
        const { approve_type, status, auto_approve, approve_admin_ids,  _index } = item;
        const params = {
          approve_type, // 审批类型 1购卡 2跨店购卡 3续卡 4升卡 5销卡 6拆分 7请假 8转卡 9补卡 10编辑会员卡 11增体验卡/课 12会员信息 13会员头像
          status,       // 审批状态 0关闭 1开启
          auto_approve, // 自动审批 0关闭/不支持 1开启
          approve_admins: approve_admin_ids.map(v => v.id) // 审批人ids
        }
        /* 判断新值并赋值 */
        const newVal = this.list[_index][key] === '1' ? '0' : '1';
        params[key] = newVal;
        /* 如果关闭对应审批，则自动审批同时关闭 */
        key === 'status' && newVal === '0' && (params['auto_approve'] = '0');

        const newItem = {
          ...this.list[_index],
          ...params,
        }
        this.putSetting(params, _index, newItem)
      },
      // 打开审批人弹窗
      handleShowEditModal(item, readonly = true) {
        this.readonly = readonly;
        this.itemData = item;
        this.showEditModal = true;
      },

    }
  };
</script>

<style lang="less" scoped>
  .ivu-form {
    display: flex;
    flex-wrap: wrap;
  }
  .ivu-form-item {
    width: 50%;
  }
  .modal-checkbox {
    display: flex;
    flex-wrap: wrap;
    padding: 0 30px;
    .people-item {
      width: 40%;
    }
  }
  .people-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 20px;
    .avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      margin-right: 10px;
    }
    .modal-avatar {
      margin-left: 20px;
    }
  }
  .approval {
    padding: 30px;
    padding-top: 15px;
    .box {
      border: 1px solid #eee;
      padding: 20px;
    }
    .people {
      padding: 20px 0;
      display: flex;
      align-items: flex-start;
      .title {
        padding: 0 35px;
        line-height: 44px;
      }
      .items {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
      }
    }
    .button {
      margin-top: 30px;
      margin-left: 30px;
    }
  }
</style>
