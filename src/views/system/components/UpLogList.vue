<template>
  <div class="table-wrap storage-list" v-if="tableData">
    <Table ref="table" :data="tableData" :columns="columns"></Table>
    <footer>
      <Pager :post-data="postData" :total="total" @on-change="pageChange" />
    </footer>
  </div>
</template>

<script>
import { formatDate } from "utils";
import Pager from "components/pager";
import { getBaseUrl } from "utils/config";
export default {
  name: "UpLogList",
  components: { Pager },
  data() {
    return {
      total: 0,
      postData: {
        page_no: 1,
        page_size: 10
      },
      tableData: [],
      columns: [
        {
          title: "导入时间",
          key: "create_time",
          render: (h, param) => {
            return (
              <div>
                {formatDate(
                  new Date(param.row.create_time * 1000),
                  "yyyy-MM-dd HH:mm"
                )}
              </div>
            );
          }
        },
        {
          title: "导入模块",
          key: "model"
        },
        {
          title: "操作人",
          key: "username"
        },
        {
          title: "成功条数",
          key: "exec_count"
        },
        {
          title: "失败条数",
          key: "error_count"
        },
        {
          title: "操作",
          render: (h, param) => {
            const item = param.row;
            const errMsg = (info) => {
              this.$Message.info({
                content: info.msg,
                duration: 5,
                closable: true
              });
            };
            const downDetail = (info) => {
              window.open(info.error_url);
            };
            const downBtn = (
              <i-button
                type="text"
                style={{ color: "#ff696a", minWidth: "0", marginRight: "5px" }}
                onClick={() => {
                  downDetail(item)
                }}
              >
                失败数据 
              </i-button>
            );
            const msgBtn = (
              <i-button
                type="text"
                style={{ color: item.status===3?"#ff696a":"#52a4ea", minWidth: "0", marginRight: "5px" }}
                onClick={() => {
                  errMsg(item)
                }}
              >
                {item.status==='3'?'导入失败':'导入成功'}
              </i-button>
            );
            return (
              <div>
                {item.status === "1" && downBtn}
                {(item.status === "3" || item.status === "2") && msgBtn}
              </div>
            );
          }
        }
      ]
    };
  },
  created() {},
  methods: {
    pageChange(postData) {
      this.postData = { ...postData };
      this.getList();
    },
    getList() {
      this.$service
        .post("/Web/Excel/getHistory", this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.tableData = data.list;
            this.total = data.count;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(e => {
          throw new Error(e);
        });
    }
  }
};
</script>

<style scoped>
.storage-list {
  border-top: 0;
}
</style>
