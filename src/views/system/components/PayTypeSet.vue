<template>
    <div class="pay-type-box">
      <Alert type="warning" show-icon>注意修改支付渠道名称已经产生的支付数据会一并受到影响</Alert>
      <div>
        <CheckboxGroup v-model="ids">
          <Checkbox v-for="pay in payTyes"
                    class="checkbox"
                    :key="pay.id"
                    :disabled="pay.can_close === '0'"
                    :label="pay.id">{{pay.pay_type_name}}
            <div class="pay-edit">
              <FaIcon name="edit"
                      size="18"
                      color="#5fb75d"
                      title="编辑"
                      v-if="pay.can_edit==='1'"
                      style="marginRight: 5px;"
                      @click.native.prevent="editPay(pay)"></FaIcon>
              <FaIcon name="trash-o"
                      size="18"
                      color="#ccc"
                      v-if="pay.can_del==='1'"
                      title="删除"
                      @click.native.prevent="deletePay(pay.id)"></FaIcon>
            </div>
          </Checkbox>
        </CheckboxGroup>
        <div class="add-pay">
          <Button type="success"
                  @click="clickAddPay">新增支付渠道
          </Button>
          <div class="add-pay-input"
               v-if="addingPay">
            <Input v-model="payName"
                   ref="payInput"
                   :maxlength="10"
                   @on-enter="payOnEnter"
                   style="width: 180px"
                   placeholder="支付方式名称"></Input>
            <Icon v-if="isEditPay"
                  type="ios-checkmark-circle-outline"
                  @click.native="editConfirm"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="确认"></Icon>
            <Icon v-else
                  type="ios-add-circle-outline"
                  @click.native="addConfirm"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="添加"></Icon>
            <Icon type="ios-close-circle-outline"
                  @click.native="addingPay = isEditPay = false"
                  size="24"
                  color="#d9544f"
                  style="cursor: pointer"
                  title="取消"></Icon>
          </div>
        </div>
        <Form :label-width="160" class="payment-wrap">
          <Alert style="margin:0 10px;" type="warning" show-icon>修改时尽量选择用户没有正在支付的时候，以防止支付出错！（小程序开单由上方勾选控制支付方式）</Alert>
          <FormItem label="微信小程序支付渠道">
            <RadioGroup v-model="payment_option" @on-change="setPaymentOption">
                <Radio label="1">微信特约商户</Radio>
                <Radio label="2">收钱吧</Radio>
                <Radio label="3">杉德</Radio>
                <Radio label="4">工行</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
    </div>

</template>

<script>
import { mapActions } from 'vuex'
import Export from 'src/components/Export'
export default {
  name: 'PayTypeSet',
  components: {
    Export
  },
  data() {
    return {
      payName: '',
      initPaymentOption:'1',
      payment_option:'1',
      initialName: '',
      isEditPay: false,
      payId: '',
      addingPay: false,
      coachId: '',
      ids: [],
      payTyes: []
    }
  },
  computed: {
  },
  watch: {
    ids(val, oldVal) {
      if (
        oldVal &&
        oldVal.length &&
        JSON.stringify(val) != JSON.stringify(oldVal)
      ) {
        this.$service
          .post('/Web/PaymentManage/switchPayType', { ids: val })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.setPayTypesInit()
            } else {
              this.getData()
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
  },
  created() {
    this.getData()
    this.getPaymentOption()
  },
  methods: {
    ...mapActions('pay', ['setPayTypesInit']),
    getPaymentOption() {
      return this.$service
        .post('/Web/PaymentManage/getPaymentOption')
        .then(res => {
          if (res.data.errorcode == 0) {
            const data = res.data.data
            this.initPaymentOption = data.info.payment_option
            this.payment_option = data.info.payment_option
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    setPaymentOption(payment_option) {
      return this.$service
        .post('/Web/PaymentManage/setPaymentOption', { payment_option })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.initPaymentOption = payment_option
            this.$Message.success(res.data.errormsg)
          } else {
             this.payment_option = this.initPaymentOption
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getData() {
      return this.$service
        .post('/Web/PaymentManage/getBusAllPayType')
        .then(res => {
          if (res.data.errorcode == 0) {
            const data = res.data.data
            this.payTyes = res.data.data
            this.ids = this.payTyes
              .filter(item => item.is_open === '1')
              .map(item => item.id)
            return data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    editPay({ id, pay_type_name }) {
      this.payName = this.initialName = pay_type_name
      this.payId = id
      this.addingPay = this.isEditPay = true
      this.$nextTick(() => {
        this.$refs.payInput.$refs.input.focus()
      })
    },
    clickAddPay() {
      this.isEditPay = false
      this.addingPay = true
      this.payId = ''
      this.payName = ''
      this.$nextTick(() => {
        this.$refs.payInput.$refs.input.focus()
      })
    },
    editConfirm() {
      if (this.payName === this.initialName) {
        this.addingPay = false
        return
      }
      if(!this.payName) {
        this.$Message.error('请先输入')
        return false
      }
      this.$service
        .post('/Web/PaymentManage/editPayType', {
          id: this.payId,
          name: this.payName
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.addingPay = false
            this.getData()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          throw new Error(err)
        })
    },
    deletePay(id) {
      this.$Modal.confirm({
        title: '删除',
        content: '确认删除该支付方式吗？',
        onOk: () => {
          this.$service
            .post('/Web/PaymentManage/delPayType', { id })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.getData()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              throw new Error(err)
            })
        }
      })
    },
    addConfirm() {
      if(!this.payName) {
        this.$Message.error('请先输入')
        return false
      }
      this.$service
        .post('/Web/PaymentManage/addBusPayType', { name: this.payName })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getData()
            this.addingPay = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    payOnEnter() {
      if (this.isEditPay) {
        this.editConfirm()
      } else {
        this.addConfirm()
      }
    }
  }
}
</script>
<style lang="less">
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pay-edit {
  display: none;
  padding-left: 15px;
}

.pay-type-box {
  font-size: 14px;
  .ivu-form .ivu-form-item-label {
    font-size: 14px;
    padding-right: 20px;
  }
  .input {
    width: 300px;
  }

  .checkbox {
    width: 30%;

    &:hover {
      .pay-edit {
        display: inline-block;
      }
    }
  }

  .add-pay {
    margin-top: 20px;
    display: flex;
  }

  .add-pay-input {
    display: flex;
    align-items: center;
    margin-left: 30px;
  }
  .payment-wrap {
    margin-top: 24px;
    padding-top: 24px;
    border: 1px solid #eee;
  }
}
</style>
