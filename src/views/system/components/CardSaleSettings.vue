<template>
  <div class="checkbox-box">
    <Form>
      <FormItem label="会籍允许售卖">
        <CheckboxGroup v-model="membership_set">
          <Checkbox label="1" :disabled="!isEdit">会籍卡</Checkbox>
          <Checkbox label="2" :disabled="!isEdit">私教课</Checkbox>
          <Checkbox label="3" :disabled="!isEdit">泳教课</Checkbox>
          <Checkbox label="4" :disabled="!isEdit">套餐包</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="私教允许售卖">
        <CheckboxGroup v-model="coach_pt_set">
          <Checkbox label="1" :disabled="!isEdit">会籍卡</Checkbox>
          <Checkbox label="2" :disabled="!isEdit">私教课</Checkbox>
          <Checkbox label="3" :disabled="!isEdit">泳教课</Checkbox>
          <Checkbox label="4" :disabled="!isEdit">套餐包</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="泳教允许售卖">
        <CheckboxGroup v-model="coach_swim_set">
          <Checkbox label="1" :disabled="!isEdit">会籍卡</Checkbox>
          <Checkbox label="2" :disabled="!isEdit">私教课</Checkbox>
          <Checkbox label="3" :disabled="!isEdit">泳教课</Checkbox>
          <Checkbox label="4" :disabled="!isEdit">套餐包</Checkbox>
        </CheckboxGroup>
      </FormItem>
    </Form>
    <div class="buttons">
      <Button @click="isEdit = true" v-show="!isEdit" type="success">编辑</Button>
      <template v-if="isEdit">
        <Button type="success" @click="saveEdit">保存</Button>
        <Button @click="cancelEdit">取消</Button>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      membership_set: [],
      coach_pt_set: [],
      coach_swim_set: [],
      isEdit: false
    }
  },
  methods: {
    getInformation() {
      this.$service.get('/Web/package/getSaleSetting').then(res => {
        if (res.data.errorcode === 0) {
          const { membership_set, coach_pt_set, coach_swim_set } = res.data.data
          this.membership_set = membership_set
          this.coach_pt_set = coach_pt_set
          this.coach_swim_set = coach_swim_set
        }
      })
    },
    saveEdit() {
      this.$service.post('/Web/package/saleSetting', {
        membership_set: this.membership_set,
        coach_pt_set: this.coach_pt_set,
        coach_swim_set: this.coach_swim_set
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.$Message.success('编辑成功！')
          this.isEdit = false
        }
      })
    },
    cancelEdit() {
      this.isEdit = false
      this.getInformation()
    }
  },
  created() {
    this.getInformation()
  }
}
</script>

<style lang="less" scoped>
.checkbox-box {
  padding: 20px;
}
</style>
