<template>
  <div class="pt-list-wrap" v-if="privateList && privateList.length">
      <div
        class="res-space"
        v-for="item in privateList"
        :key="item.id"
      >
        <MobileSpaceItem :info="item"></MobileSpaceItem>
      </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import MobileSpaceItem from './MobileSpaceItem'
export default {
  name: 'Field',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  components: {
    MobileSpaceItem
  },
  data() {
    return {
      privateList: []
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_schedule_mock_data', {
          type: 4
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.privateList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
</style>
