<template>
  <div class="diy-left">
    <h3 class="tit">其它设置</h3>
    <ul class="list-group">
        <li
          class="list-group-item"
          :class="selectedIndex===index?'item-selected' : ''"
          v-for="(item, index) in filterList"
          :key="`list-diy-left-${index}`"
          @click="handleItem(item, index)"
        >
          <div class="item-left">
            <div class="item-name">
              {{ item.name }}
            </div>
          </div>
           <!-- <div class="item-rig" v-if="item.save_type === 4">
            <div
              class="rig-icon-wrap"
              @click.stop="turnItem"
              :title="theme4.display==1?'点击隐藏':'点击显示'"
            >
              <Icon  v-if="theme4.display!=1" type="md-eye" />
              <Icon v-else type="md-eye-off" />
            </div>
          </div> -->
        </li>
        </ul>
  </div>
</template>

<script>
import EventBus from 'components/EventBus.js';
 import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiySelectOther',
  components: {
  },

  props: {

  },
  data() {
    return {
      list: [{
        name: '登录页面',
        save_type: 6,
        componentName: 'DiyPageLogin'
      },{
        name: '教练称谓',
        save_type: 7,
        componentName: 'DiyPageCoach'
      },{
        name: '我的页面',
        save_type: 4,
        componentName: 'DiyPageMy'
      },{
        name: '积分商城',
        save_type: 10,
        componentName: 'DiyPagePoint'
      }],
      selectedIndex: 0,
    }
  },

  computed: {
    ...mapState('diy', ['theme4']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
    filterList() {
      // 如果是综合模式并且当前选择的不是商家，则隐藏4、10
      return this.list.filter(v => {
        return !(
          this.isMerchantMode && !this.isSelectMerchant
          ? [4, 10].includes(v.save_type)
          : !this.isMerchantMode && v.save_type === 10
        )
      })
    }
  },

  watch: {
    isSelectMerchant() {
      // 切换商家/场馆后，如果筛选后的列表未对应，则切换到0
      const item = this.list[this.selectedIndex]
      const filterItem = this.filterList[this.selectedIndex]
      if (!filterItem || filterItem.save_type !== item.save_type) {
        this.handleItem(this.list[0], 0)
      }
    }
  },

  created() {
    this.handleItem(this.list[0], 0)
  },
  methods: {
    handleItem(item, index) {
      EventBus.$emit('on-diy-tab-change', {
        type: this.list[this.selectedIndex].save_type,
        success: ()=> {
          this.selectedIndex = index
          this.$store.commit('diy/UPDATE_OTHER_SELECT_TYPE', item.save_type)
          this.$emit('on-click-item', item)
        }
      })
    },
    turnItem() {
      this.$store.commit('diy/UPDATE_DIY_THEME', {
        value: {
          ...this.theme4,
          display: this.theme4.display == 1 ? 0 : 1
        },
        type: 4
       })
    }
  },
}
</script>

<style lang="less" scoped>
.diy-left {
  width: 300px;
  box-sizing: border-box;
  padding: 15px;
  height: 854px;
  background: #f6f6f8;
  margin-right: 30px;
  margin-top: 52px;
  .tit {
    font-size: 16px;
    font-weight: bold;
    color: #313131;
    margin-bottom: 15px;
  }
  .des {
    font-size: 14px;
    color: #7d7d7d;
    margin-bottom: 15px;
  }
}

.list-group {
  min-height: 20px;
}

.list-group-item {
  // width: 270px;
  height: 70px;
  box-sizing: border-box;
  padding: 0 10px;
  background: #ffffff;
  border: 1px dashed #cccccc;
  border-radius: 6px;
  margin-bottom: 10px;
  display: flex;
  font-size: 14px;
}
.item-selected {
  border-color:  #47CB89;
  background: #EEFBF5;
}
.list-group-item {
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #ccc;
  .pointer {
    cursor: pointer;
  }
  .item-name {
    color: #313131;
    margin-left: 5px;
  }
  .item-btn {
    min-width: 40px;
    width: 40px;
  }
  .item-left {
    display: flex;
    align-items: center;
  }
}
.rig-icon-wrap {
    cursor: pointer;
    width: 22px;
    height: 22px;
    background: #ffd8d9;
    border-radius: 50%;
    color: #ff7a80;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
  }
</style>
