<template>
    <div v-show="config.isHide" class="img-wrap">
        <img :src="config.img_url" alt="">
    </div>
</template>

<script>
export default {
    name: 'PictureInfo',
    props: {
        config: {
            type: Object
        }
    },
}
</script>

<style lang="less" scoped>
.img-wrap {
  width: 710px;
  height: 280px;
  margin: 20px;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>