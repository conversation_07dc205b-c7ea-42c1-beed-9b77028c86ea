<template>
    <div class="bot-wrap theme-bg">
    <div class="box-tit">
      {{config.name || '场地推荐'}}
      <div
        class="rig"
        v-if="list && list.length"
      >
        查看更多
        <div class="arrow-right"></div>
      </div>
    </div>
    <div class="bot-con class-wrap">
      <div
        class="index-ticket-item"
        v-for="(item, key) in list"
        :key="`card_list_${key}`"
      >
        <MobileSpaceItem :info="item" />
      </div>
      <div v-if="list && list.length == 0" class="nodata">暂未设置推荐场地</div>
    </div>
    </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import MobileSpaceItem from './MobileSpaceItem'
export default {
  name: 'IndexSpaceInfo',
  mixins: [memberPageIndex],
  components: {
    MobileSpaceItem
  },
  props: {
    config: {
      type: Object
    }
  },
  data() {
    return {
    }
  },
  computed: {
    list() {
      return this.config.recommend_type==2 ? this.config.recommend_space_detail : this.initList
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(6, val)
    }
  },

  created() {
    this.getInitList(6,  this.config.bus_id)
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.class-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
