<template>
 <div class="tag-view" v-if="tagList && tagList.length>1">
  <div v-for="item in tagList">{{item.title}}</div>
 </div>
</template>

<script>

export default {
  name: 'MobileCardTags',
  props: {
    type: Number
  },
  data() {
    return {
      tagList: [],
    }
  },
  computed: {
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$service
        .post('/Web/CardGroup/getCardGroupListByBusId', {
          type: this.type,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            let arr = [{title: '全部', id: ''}]
            this.tagList = arr.concat(res.data.data)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.tag-view {
  margin-top: -20px;
  margin-bottom: 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  div {
    background-color: #f5f5f5;
    margin: 10px 20px 10px 0;
    padding: 5px 10px;
    border: 1px #DCDFE6 solid;
    border-radius: 6px;
    font-size: 26px;
    color: #666;
    &:first-child {
      border-color: var(--THEME-COLOR);
      background-color: var(--THEME-COLOR);
      color: #fff;
    }
  }
}
</style>