<template>
<div class="style-box">
  <div class="style-item" :class="curStyle[name]===item.type?'item-checked':''" @click="setStyle(item)" v-for="(item, index) in list" :key="index">
    <div class="checked-tag">
      <img src="~src/assets/img/diy/used.png"/>
    </div>
    <div class="img-wrap">
      <img :src="item.url"/>
    </div>
    <div class="tit">{{item.name}}</div>
  </div>
 </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'StyleSet',
  data () {
    return {
    }
  },
  props: {
    value: [Object, String],
    list: {
      type: Array,
      default: []
    },
    name: {
      type: String,
      default: 'background_color',
    },
  },
  computed: {
    ...mapState('diy', ['theme1']),
    curStyle: {
      get () {
        return this.theme1
      },
      set (val) {
        if (this.name === 'background_color') {
          this.$emit('input', {
            ...this.theme1,
            background_color: val
          })
        } else {
          this.$emit('input', {
            ...this.theme1,
            fashion_color: val
          })
        }
      }
    }
  },
  watch: {
  },
  created () {
  },
  methods: {
    setStyle(info) {
      if(info.name === '深色(适配中，敬请期待...)') {
        return false
      }
      this.curStyle = info.type
    },
  }
}
</script>
<style lang="less" scoped>
.style-box {
  display: flex;
  align-items: center;
}
.style-item {
  margin: 0 20px 20px 0;
  position: relative;
  cursor: pointer;
  .img-wrap {
    border: 1px dashed #CCCCCC;
    border-radius: 6px;
  }

  .tit {
    text-align: center;
    margin-top: 9px;
    font-size: 12px;
    color: #313131;
  }
  .checked-tag {
    display: none;
  }
}
.item-checked {
  .img-wrap {
    border-color:#47CB89
  }
  .checked-tag {
    position: absolute;
    right: -10px;
    top: -12px;
    display: block;
    z-index: 1;
  }
}
</style>

