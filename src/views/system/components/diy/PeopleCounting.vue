<template>
  <div class="bot-wrap people-counting">
    <div>当前 <span class="people-counting-num">{{ peopleNum }}</span> 人正在健身</div>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';

const props = defineProps({
  config: Object
})

const peopleNum = ref(999)


</script>
<style lang="less">
@import '~@/styles/themeVar.less';
.people-counting {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  line-height: 80px;
  font-size: 24px;
  padding: 0 20px !important;
}
.people-counting-num {
  color: @theme-text-color-other;
}
</style>
