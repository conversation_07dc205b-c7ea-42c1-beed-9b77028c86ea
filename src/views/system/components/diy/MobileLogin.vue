<template>
  <MobileBox class="diy-color">
    <swiper class="swiper-box" ref="swiperBox" :options="swiperOptions">
      <swiper-slide>
        <div class="login-wrap agreement-wrap" :style="{'backgroundImage': `url(${loginBg})`,'backgroundSize':'cover'}">
          <div class="logo">
            <img class="custom-logo" :src="busLogo" alt="logo">
          </div>
          <div class="agreement-text">
            根据相关政策，在您使用之前我们诚恳的告诉您，为了确保会员服务的正常运行，我们需要使用您的部分信息，请查看并同意软件<a class="link-text">《最终用户许可协议》</a>及<a class="link-text">《隐私政策》</a>。
          </div>
          <div>
            <div class="normal-btn login-btn" style="background: var(--THEME-COLOR)">我已阅读并同意</div>
            <div class="code-login">不同意</div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide>
        <div class="login-wrap" :style="{'backgroundImage': `url(${loginBg})`,'backgroundSize':'cover'}">
          <div class="logo">
            <img class="custom-logo" :src="busLogo" alt="logo">
          </div>
          <div class="login-main">
            <div>
              <div class="normal-btn login-btn" style="background: var(--THEME-COLOR)">一键登录</div>
              <div class="code-login">验证码登录</div>
            </div>
          </div>
            <Checkbox-group class="diy-login-checkboxgroup">
            <Checkbox class="cb-transform" checked> 我已阅读并同意
            <a class="link-text">《隐私政策》</a>、<a class="link-text">《最终用户许可协议》</a> 、<a class="link-text">《场馆服务协议》</a>
            </Checkbox>
          </Checkbox-group>
        </div>
      </swiper-slide>
      <swiper-slide>
        <div class="login-wrap" :style="{'backgroundImage': `url(${loginBg})`,'backgroundSize':'cover'}">
          <div class="logo align">
            <img class="custom-logo" :src="busLogo" alt="logo">
          </div>
          <div class="login-main">
            <div>
              <div class="input-wrap">
                <div class="input">请输入手机号码</div>
              </div>
              <div class="input-wrap">
                <div class="input code-in">请输入验证码</div>
                <div class="btn-code">获取验证码</div>
              </div>
              <div class="normal-btn login-btn" style="background: var(--THEME-COLOR)">登 录</div>
              <div class="code-login">授权手机号登录</div>
            </div>
          </div>
          <Checkbox-group class="diy-login-checkboxgroup">
            <Checkbox class="cb-transform" checked> 我已阅读并同意
            <a class="link-text">《隐私政策》</a>、<a class="link-text">《最终用户许可协议》</a> 、<a class="link-text">《场馆服务协议》</a>
            </Checkbox>
          </Checkbox-group>
        </div>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>
  </MobileBox>
</template>

<script>
  import {
    Swiper,
    SwiperSlide
  } from 'vue-awesome-swiper'
  import { mapState } from 'vuex'
  import MobileBox from './MobileBox'
  export default {
    name: 'MobileLogin',
    props: {
      config: Object
    },
    components: {
      Swiper,
      MobileBox,
      SwiperSlide,
    },
    data() {
      return {
        swiperOptions: {
          pagination: {
            el: '.swiper-pagination',
            clickable: true,
          },
          autoplay: {
            delay: 5000,
            disableOnInteraction: false,
          },
          // Some Swiper option/callback...
        },
      }
    },
    watch: {},
    computed: {
      ...mapState(['busLogo']),
      swiper() {
        return this.$refs.swiperBox.$swiper
      },
      loginBg() {
        const defaultImg = 'https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg'
        return (this.config.background_type == 1? defaultImg : this.config.background_img) || defaultImg
      }
    },
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped>
  .diy-color {
    margin-right: 30px;
  }
  .swiper-box {
    height: 100%;
    font-size: 50px;
  }
  .swiper-box /deep/ .swiper-slide {
    height: auto;
  }
  .diy-login-checkboxgroup {
    position: absolute;
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
    bottom: 60px;
    text-align: center;
    color: #fff;
    zoom:2;
  }
  .agreement-wrap {
    .code-login {
      text-align: center;
    }
  }
  .agreement-text {
    width: 620px;
    margin: 0 auto;
    color: #fff;
    line-height: 1.75;
  }
  .under-white {
    display: inline-block;
    text-decoration: underline;
  }
  .under-text {
    display: inline-block;
    color: dodgerblue;
    text-decoration: underline;
  }
  .logo {
    display: block;
    width: 150px;
    height: 150px;
    margin: 300px auto 88px;
    img {
      width: 100%;
      height: 100%;
      // border-radius: 50%;
    }
  }
  .align {
    margin-left: 86px;
  }
  .login-wrap {
    width: 100%;
    height: 100%;
    background-image: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-size: 24px;
    overflow: hidden;
  }
  .login-btn {
    width: 580px;
    border-radius: 40px;
    margin: 80px auto 0;
  }
  .code-login {
    text-align: right;
    color: #fff;
    font-size: 26px;
    margin: 25px 30px 0 0;
  }
  .login-main {
    width: 600px;
    margin: 88px auto;
  }
  .input-wrap {
    position: relative;
    width: 100%;
    border-bottom: 1px solid var(--THEME-COLOR);
    margin: 0 auto;
  }
  .input-wrap .input {
    width: 100%;
    height: 106px;
    line-height: 106px;
    color: #fff;
    text-indent: 3px;
  }
  .input-pla {
    color: #bfbfc1;
  }
  .btn-code {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 182px;
    height: 62px;
    line-height: 62px;
    text-align: center;
    font-size: 26px;
    color: #fff;
    background: transparent;
    border: 1px solid var(--THEME-COLOR);
    border-radius: 30px;
  }
  .input-wrap .code-in {
    width: 255px;
  }
</style>
