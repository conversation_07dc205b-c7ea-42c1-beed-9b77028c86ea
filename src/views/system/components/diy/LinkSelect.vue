<template>
  <div>
  <!-- <Cascader @on-change="linkChanged"
          v-if="linkTree && linkTree.length"
          :placeholder="placeholder"
          clearable
          filterable
          change-on-select
          :value="linkUrlArr"
          :data="linkTree"
          :disabled="disabled">
  </Cascader> -->

  <Input
    :value="formatLabel"
    class="select-input"
    readonly
    clearable
    :placeholder="placeholder"
    @on-focus="handleShowSelectModal"
    @on-clear="handleClear"/>

  <LinkSelectModal
    v-if="hasModal"
    :show.sync="showSelectModal"
    :selected="linkUrlArr"
    :currentBusId="postData.bus_id"
    @on-confirm="linkChanged"
    @update:show="$emit('update:show', $event)"
  />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import LinkSelectModal from './LinkSelectModal.vue'
import { getQueryString, getQueryStringByName } from "@/utils/index"

export default {
  name: 'LinkSelect',
  components: {
    LinkSelectModal
  },
  data () {
    return {
      linkUrlArr: [],
      formatLabel: '',
      showSelectModal: false,
      hasModal: false,
    }
  },
  props: {
    value: {
      type: [String, Number]
    },
    selectBusId: {
      type: String || Number,
      default: ''
    },
    postData: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请选择链接'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['busIdEncode']),
    ...mapState('diy', ['links', 'merBusList', 'firstBusIdEncode', 'selectMerBusIdEncode']),
    ...mapGetters('diy', ['isSelectMerchant']),
    // linkTree() {
    //   const valueArr = this.value.split('&bus_id=')
    //   const encodeBusId = valueArr[valueArr.length - 1]
    //   return encodeBusId
    //     ? this.links[encodeBusId] ? this.links[encodeBusId].linkTree : []
    //     : this.links[this.busIdEncode] ? this.links[this.busIdEncode].linkTree : []
    // },
    linkList() {
      const busId = getQueryStringByName(this.value, 'bus_id')
      const encodeBusId = busId || this.postData.bus_id || (this.isSelectMerchant ? this.firstBusIdEncode : this.selectMerBusIdEncode)
      return this.links[encodeBusId] ? this.links[encodeBusId].linkList : []
    },
    watchData() {
      return {
        val: this.value,
        list: this.linkList
      }
    }
  },
  watch: {
    watchData: {
      handler({ val, list }, oldData) {
        // 展示数据
        // if(val && !(oldData && oldData.val)) {
        if(val) {
          const { item, formatLabel, linkUrlArr } = this.userValue(val)

          if (item) {
            this.$nextTick(()=> {
              this.formatLabel = formatLabel
              this.linkUrlArr = linkUrlArr
            })
          } else {
            this.$nextTick(()=> {
              this.formatLabel = ''
            })
          }
        }
      },
      immediate: true,
      deep: true
    },
  },
  created () {
    const valueArr = this.value.split(decodeURIComponent(this.value) === this.value ? 'bus_id=' : 'bus_id%3D')
    const encodeBusId = valueArr.length !== 1 ? valueArr[valueArr.length - 1] : this.postData.bus_id || this.busIdEncode
    if (!this.links[encodeBusId]) {
      this.linkListInit(encodeBusId)
    }
  },
  methods: {
    ...mapActions('diy', ['linkListInit']),
    linkChanged (linkArr) {
      if(!linkArr.length) {
        this.handleClear()
        return;
      }
      let expInfo = {}
      const lastUrl = linkArr[linkArr.length - 1]
      const { item, formatLabel, linkUrlArr } = this.userValue(lastUrl)

      if (item) {
        expInfo = {
          ...item,
          value: lastUrl
        }
        this.formatLabel = formatLabel
        this.linkUrlArr = linkUrlArr
      }

      this.$emit('input', lastUrl)
      this.$emit('on-change', {
        img_name: expInfo.label,
        img_link: expInfo.value
      })
    },

    userValue(path) {
      // console.log('path: ', path)
      if (path.includes('%3A') || path === 'miniProgram') {
        const item = this.linkList.find(item => item.value === 'miniProgram')
        // const originalPath = decodeURIComponent(path)
        // item.appid = originalPath.split('%3A')[0]
        // item.path = originalPath.split('%3A')[1]
        // item.decodeValue = decodeURIComponent(path)
        item.decodeValue = 'miniProgram'
        return {
          item,
          formatLabel: '外部小程序链接',
          linkUrlArr: [path]
        }
      }

      const valueArr = decodeURIComponent(path) === path ? path.split('bus_id=') : path.split('bus_id%3D')
      const urlBusId = getQueryStringByName(path, 'bus_id')
      const url = urlBusId
        ? valueArr[0].slice(0, valueArr[0].length - (decodeURIComponent(valueArr[0]) === valueArr[0] ? 1 : 3))
        : valueArr[0]
      const decodeValue = decodeURIComponent(url)

      const item = this.linkList.find((item) => {
        return (item.decodeValue || item.value) === decodeValue
      })

      let formatLabel = '', linkUrlArr = null
      if (item) {
        linkUrlArr = item.pValue ? [item.pValue, path] : [path]
        formatLabel = item.pLabel ? `${item.pLabel} / ${item.label}` : item.label

        const busIdEncode = urlBusId || (this.isSelectMerchant ? this.firstBusIdEncode : '')

        if (this.isSelectMerchant && busIdEncode) {
          const busItem = this.merBusList.find(v => {
            return v.encode_bus_id === busIdEncode
          })

          if (busItem) {
            if (/ \/ /.test(formatLabel)) {
              const textArr = formatLabel.split(' / ')
              textArr.splice(1, 0, busItem.bus_name)
              formatLabel = textArr.join(' / ')
            } else {
              formatLabel += ` / ${busItem.bus_name}`
            }
          }
        }
      }

      return {
        item,
        formatLabel,
        linkUrlArr
      }
    },

    handleClear() {
      this.formatLabel = ''
      this.linkUrlArr = []
      this.$emit('input', '')
      this.$emit('on-change', {
        img_name: '',
        img_link: ''
      })
    },

    handleShowSelectModal() {
      if (!this.hasModal) {
        this.hasModal = true
        setTimeout(() => {
          this.showSelectModal = true
        }, 0);
      } else {
        this.showSelectModal = true
      }
    },
  }
}
</script>

<style lang="less" scoped>
  .select-input {
    /deep/.ivu-input {
      cursor: pointer;
    }
  }
</style>
