<template>
   <div class="bus-box" :class="config.type==2?'style2':''">
    <div class="banner-box">
      <swiper
        class="swiper"
        v-if="imgs && imgs.length"
        :options="swiperOptions"
      >
        <swiper-slide
          v-for="(item,index) in imgs"
          v-if="item.img_url"
          :key='index'
        >
          <img
            class="bg-img"
            :src="item.img_url"
          />
        </swiper-slide>
      </swiper>
      <div class="swiper-page">
        <!-- <uni-icons
          type="img"
          size="12"
          color="#fff"
        ></uni-icons> -->
        <div class="swiper-page-text">
          {{(curImgIndex+1)+'/'+(imgs && imgs.length?imgs.length:0)}}
        </div>
      </div>
    </div>

    <div class="bus-wrap theme-bg">
      <div class="bus-info">
         <img
            class="logo"
            :src="busInfo.thumb"
          />
        <div class="bus-info-rig">
          <div>{{busInfo.name}}</div>
          <div class="arrow-right"></div>
        </div>
      </div>
      <div class="bus-des">
        <div
          class="address-info"
        >

          <div>{{busInfo.address}}</div>
        </div>
        <div class="act-list">
          <div v-if="config.toggle_bus === '1'" class="act-wrap">
            <ThemeIcon type="t-icon-shouye-qiehuan" :size="36" style="margin-bottom: 10px;" />
            切换场馆
          </div>
          <div class="act-wrap">
            <ThemeIcon type="t-icon-shouye-dianhua" :size="36" style="margin-bottom: 10px;" />
            电话
          </div>
        </div>
      </div>
    </div>
    <div
      class="scroll-box"
      v-if="imgs && imgs.length"
    >
      <img
        v-for="(item,index) in imgs"
        :key='index'
        class="scroll-img"
        :src="item.img_url"
      />
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { gymInfo } from '@/service/getData'
import { mapState } from 'vuex';
import ThemeIcon from './ThemeIcon';
export default {
  name: 'BusInfo',
  components: {
    ThemeIcon,
    Swiper,
    SwiperSlide
  },
  inject: ['IS_BRAND_SITE'],
  props: {
    config: {
      type: Object
    }
  },
  computed: {
    ...mapState(['busId']),
    ...mapState('diy', ['selectMerBusId', 'theme2', 'indexLeftSelected2', 'merBusList']),
    imgs() {
      return this.config.imgs && this.config.imgs.length?this.config.imgs.filter(item=>item.img_url):[]
    }
  },
  data() {
    return {
      curImgIndex: 0,
      swiperOptions: {
         autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
      },
      busInfo: {
        address: '',
        bus_id: '',
        lat: '',
        lng: '',
        name: '',
        phone: '',
        pid: '',
        thumb: ''
      }
    }
  },
  created() {
    this.init(this.config.bus_id)
  },

  watch: {
    'config.bus_id'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.init(newValue)
      }
    },
  },

  methods: {
    init(value) {
      if (this.IS_BRAND_SITE) {
        const item = this.merBusList.find((v) => {
          return v.encode_bus_id === value
        })
        if (item) {
          this.getGymInfo(item.bus_id)
        }
      } else {
        this.getGymInfo(this.busId)
      }
    },
    getGymInfo(busId) {
      gymInfo(busId).then(res => {
        const { errorcode, data } = res.data
        if (errorcode == 0) {
          this.busInfo = data[0]
        }else {
          this.$Message.error(res.data.errormsg);
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.t-icon-shouye-dianhua:before {
  content: "\e644";
}

.swiper {
  height: 460px;
}
.banner-box {
  position: relative;
  min-height: 100px;
}
.swiper-page {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 16px;
  right: 20px;
  width: 113px;
  height: 36px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 18px;
  z-index: 1;
}
.bg-img {
  width: 750px;
  height: 460px;
}
.swiper-page-text {
  margin-left: 10px;
  font-size: 20px;
  font-weight: 800;
  color: #ffffff;
}
.bus-box {
  width: 100%;
  position: relative;
  .scroll-box {
    white-space: nowrap;
    padding-left: 20px;
    box-sizing: border-box;
		width: 100%;
    display: none;
  }
  .scroll-img {
    width: 275px;
    height: 168px;
    border-radius: 10px;
    margin-right: 20px;
  }
}
.style2 {
  margin-top: 70px;
  .banner-box {
    display: none;
  }
  .scroll-box {
    display: block;
  }
}

.bus-wrap {
  position: relative;
  margin: 20px;
  padding: 108px 20px 0;
}
.bus-info {
  width: 100%;
  position: absolute;
  top: -40px;
  left: 20px;
  display: flex;
  align-items: flex-end;

  font-size: 36px;
  font-weight: bold;
  z-index: 1;
}
.bus-info-rig {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  .arrow-right {
    margin-right: 40px;
  }
}
.bus-info .logo {
  width: 112px;
  height: 112px;
  margin-right: 14px;
  border-radius: 20px;
}
.bus-des {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 30px;
}
.address-info {
  display: flex;
  align-items: flex-start;
  width: 420px;
  .icon {
    margin-top: 6px;
  }
}
.address-info .icon {
  line-height: 36px;
  width: 22px;
  height: 22px;
}
.address-info div {
  flex: 1;
}
.act-list {
  display: flex;
  align-items: center;
  font-size: 20px;
}
.act-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 50px
}
</style>
