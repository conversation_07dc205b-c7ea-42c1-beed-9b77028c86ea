<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="活动名称：">
        <Select v-model="postData.activity_id" filterable class="w250" @on-change="actChange">
          <Option
            v-for="(item) in actList"
            :key="item.id"
            :value="item.id"
            :label="item.name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="会员卡：">
        {{selectInfo.card_name}}
      </FormItem>
      <FormItem label="原价：">
        {{selectInfo.ori_cost}} 元
      </FormItem>
      <FormItem label="活动价：">
        {{selectInfo.curr_cost}} 元
      </FormItem>
      <FormItem v-if="selectInfo.refund_condition === '1'" label="退款条件：">
        首次入场不满{{selectInfo.refund_time}}分钟可退款
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyActivityRefund',
  components: {
    RightBox,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  watch: {
    'postData.bus_id':{
      handler() {
        this.getList()
      },
      immediate: true
    },
  },
  data() {
    return {
      actList: [],
      selectInfo: {
        activity_id: '',
        card_name: '',
        name: '',
        curr_cost: '',
        ori_cost: '',
        refund_condition: '',
        refund_time: '',
      }
    }
  },
  methods: {
    getList() {
      this.$service
        .post('/Web/Activity/ExperienceTemplateList', { bus_id: this.selectBusId || '' })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.actList = res.data.data
            if(!this.actList.length) {
              this.postData.activity_id = '';
              this.selectInfo = {
                activity_id: '',
                card_name: '',    
                name: '',
                curr_cost: '',
                ori_cost: '',
                refund_condition: '',
                refund_time: '',
              }
              return;
            }
            if (this.actList.length && !this.postData.activity_id) {
              this.$set(this.postData, 'activity_id', this.actList[0].id)
              this.actChange(this.postData.activity_id)
            } else if(this.postData.activity_id) {
              this.actChange(this.postData.activity_id)
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    actChange(id) {
      const item = this.actList.find(v => v.id == id)
      if (item) {
        this.postData.activity_id = item.id
        Object.assign(this.selectInfo, item)
      }
    }
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
