<template>
  <div class="diy-member">
    <div class="mobile-box" data-theme="default">
      <swiper class="swiper-box" ref="swiperBox" :options="swiperOptions">
        <swiper-slide>
          <img :src="`${pathBase}${color}/page-${theme==2?'dark-':''}1.jpg`" alt="">
        </swiper-slide>
        <swiper-slide>
          <img :src="`${pathBase}${color}/page-${theme==2?'dark-':''}2.jpg`" alt="">
        </swiper-slide>
        <swiper-slide>
          <img :src="`${pathBase}${color}/page-${theme==2?'dark-':''}3.jpg`" alt="">
        </swiper-slide>
        <div class="swiper-pagination" slot="pagination"></div>
      </swiper>
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'

export default {
  name: 'MobileColor',
  props: {
    color: String,
    theme: String
  },
  components: {
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      pathBase: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/theme-page/',
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
        // Some Swiper option/callback...
      },
    }
  },
  watch: {
  },
  computed: {
    swiper() {
      return this.$refs.swiperBox.$swiper
    }
  },
  created() {
  },
  methods: {
    
  }
}
</script>

<style lang="less" scoped>
.diy-member {
  background-image: url(~assets/img/diy/xsmax.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 30px;
  width: 419px;
  height: 804px;
}
.mobile-box {
  margin: 30px 44px;
  width: 750px;
  height: 1520px;
  border-radius: 44px;
  overflow: hidden;
  zoom: 0.5;
}
.swiper-box {
  height: 100%;
  font-size: 50px;
}
.swiper-box /deep/ .swiper-slide{
  height: auto;
}
.slide-selected{
  position: relative;
}
.slide-selected::before{
  position: absolute;
  top: 0;
  left: 0;
  content: ' ';
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 4px dashed #47CB89;
  z-index: 1;
}
</style>