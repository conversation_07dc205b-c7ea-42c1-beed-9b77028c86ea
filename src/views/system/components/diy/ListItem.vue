<template>
  <div class="info-item-wrap">
    <div class="info-item add-wrap" :style="{width:(imgWidth+30)+'px', height: (imgHeight+45)+'px'}" @click="addItem()">
      <img class="add-img" src="~src/assets/img/diy/up.png"/>
      <div class="add-txt">{{addTips}}</div>
    </div>
    <div class="info-item" :style="{width:(imgWidth+30)+'px', height: (imgHeight+45)+'px'}" v-for="(item, index) in itemList" :key="item.id">
      <img class="del-img" @click="delItem(index)" src="~src/assets/img/diy/close.png"/>
      <slot>
        <img v-if="showImg" class="item-img" :style="{width:imgWidth+'px', height: imgHeight+'px'}" :src="(Array.isArray(item[avatarKey]) ? item[avatarKey][0] || 'https://imagecdn.rocketbird.cn/minprogram/member/image/category-1.png' : item[avatarKey]) || avatarSrc"/>
        <div class="item-txt">{{item.class_name||item.name||item.card_name}}</div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ListItem',
  props: {
    value: {
      type: Array,
      default: ()=>[]
    },
    addTips: {
      type: String,
      default: '请选择'
    },
    showImg: {
      type: Boolean,
      default: true
    },
    imgWidth: {
      type: Number,
      default: 130
    },
    imgHeight: {
      type: Number,
      default: 130
    },
    avatarSrc: {
      type: String,
      default: ''
    },
    avatarKey: {
      type: String,
      default: 'avatar'
    },
    nameKey: {
      type: String,
      default: 'name'
    }
  },
  computed: {
    itemList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  components: {
  },
  data() {
    return {
    }
  },
  watch: {
  },
  methods: {
    addItem() {
      this.$emit('on-add', this.itemList)
    },
    delItem(index) {
      this.itemList.splice(index, 1)
    }
  },
}
</script>

<style lang="less" scoped>
.info-item-wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.info-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 15px 15px 5px;
  margin-right: 20px;
  margin-bottom: 30px;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 6px;
  &:hover .del-img {
    display: block;
  }
}
.add-wrap {
  border: 1px dashed #CCC;
  cursor: pointer;
}
.add-txt {
  margin-top: 10px;
}
.item-txt {
  width: 100%;
  overflow: hidden;
  text-align: center;
  height: 20px;
  font-size: 14px;
  color: #313131;
  line-height: 20px;
  margin-top: 5px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.del-img {
  cursor: pointer;
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  top: -10px;
  right: -10px;
}
.mb10 {
  margin-bottom: 10px;
}
</style>