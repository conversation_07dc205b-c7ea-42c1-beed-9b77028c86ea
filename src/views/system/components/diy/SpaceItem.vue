<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      add-tips="选择场地类型"
      :img-width="138"
      :img-height="52"
      avatar-key="pic_url"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <SpaceTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import SpaceTable from './SpaceTable'
export default {
  name: 'SpaceItem',
  components: {
    SpaceTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {},
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      if(this.itemList.length+info.selection.length>3) {
        this.$Message.error('选择的场地类型不超过3种')
        return false
      }
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
