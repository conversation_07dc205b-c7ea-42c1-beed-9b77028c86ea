<template>
  <Modal
    v-model="showAdd"
    title="活动选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.search"
          @on-enter="getList"
          placeholder="活动名称"
        ></Input>
        <Select
          style="width: 120px"
          v-model="searchData.type"
          placeholder="活动类型"
          clearable
        >
          <Option value="1">一般活动</Option>
          <Option value="2">付费活动</Option>
          <Option value="3">会员卡抢购</Option>
          <!-- <Option value="4">购卡赠积分</Option> -->
        </Select>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          class="avatar-zoom"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import Selection from 'mixins/selection'
export default {
  name: 'ActivityTable',
  mixins: [Selection],
  data() {
    return {
      pageTotal: 0,
      searchData: {
        search: '',
        from_member_setting: 1,
        type: '',
        page_size: 10,
      },
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
            title: '活动名称',
            key: 'name',
            render: (h, params) => {
              const item = params.row;
              let isTop =
                item.is_top == 1 ? (
                  <tag color="red" style="border-radius: 4px; margin-left: 5px; cursor: auto">
                    置顶
                  </tag>
                ) : (
                  ''
                );
              return (
                <div>
                  <span>{item.name}</span>
                  {isTop}
                </div>
              );
            }
          },
          {
            title: '活动类型',
            key: 'type',
            render: (h, params) => {
              let item = params.row;
              return (
                <div>
                  {item.type == 1 ? '普通活动' : item.type == 2 ? '付费活动' : '会员卡抢购'}
                </div>
              );
            }
          },
          {
            title: '活动时间',
            key: 'date',
            render: (h, params) => {
              let item = params.row;
              return (
                <div>
                  {item.beg_time} ~ {item.end_time}
                </div>
              );
            }
          },
          {
            title: '报名截止时间',
            key: 'cutoff_time'
          },
          {
            title: '已报名/可报名人数',
            key: 'number',
            render: (h, params) => {
              const item = params.row;
               let dom = (
                <span>
                  {item.signcount}/{item.about_number}
                </span>
              );
              if (item.type == 3) {
                dom = <span>-</span>;
              }
              return dom;
            }
          }
      ],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    selectBusId: String || Number
  },

  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
  },
  components: {},
  created() {
    this.getList()
  },
  methods: {
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection,
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/Activity/pc_get_activity_list', { ...this.searchData, bus_id: this.selectBusId })
        .then((res) => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            const list = data.activity_list
            this.tableData = list.map((item) => {
              return {
                ...item,
                _checked: this.selectionId.includes(item.id)
              }
            })
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
}
</script>

