<template>
  <div class="icon-picker">
    <img class="icon" :src="value" alt="">
      <Button @click="showPop=true">更换图标</Button>
    <Modal v-model="showPop" class="hover-icon" width="400" transfer>
      <div style="width: 350px">
        <Tabs type="card" v-if="showPop" :animated="false" v-model="tabIndex">
          <TabPane label="推荐图标" name="suggest">
            <div class="icons">
              <div v-for="(item, index) in icons" :key="index" @click="selected = item" @dblclick="handleSave" :class="{'active': selected === item}"><img :src="item" alt=""></div>
            </div>
          </TabPane>
          <TabPane label="自定义图标" name="custom">
            <div class="image-description">
              <p class="label">自定义图标</p>
              <p class="tip">图片最佳尺寸: 100X100</p>
              <p class="tip">推荐图片大小: &lt;100kb</p>
              <p class="tip">格式限制: jpg、png</p>
            </div>
            <Cropper v-model="selected" width="100px" height="100px" :ratio="1" class="cropper-wrap">
              <div slot="result" slot-scope="{ onCropping }" v-if="!onCropping">
                <img style="width: 100px; height: 100px" v-if="!icons.includes(selected)" :src="selected">
              </div>
              <label slot="picker" slot-scope="slotProps" v-if="!slotProps.imgSelected" class="upload">
                上传图标
                <input type="file" style="display: none" @change="slotProps.onChange" />
              </label>
              <div slot="save" slot-scope="{ save, cancel }" style="padding-top: 15px; display: flex; justify-content: space-around">
                <Button type="success" @click="onUpload(save)">保存</Button>
                <Button style="margin-left: 30px" @click="cancel">取消</Button>
              </div>
            </Cropper>
          </TabPane>
        </Tabs>
        
      </div>
      <div slot="footer" class="modal-buttons">
          <div v-if="tabIndex === 'suggest'">
              <Button type="success" @click="handleSave">保存</Button>
            <Button style="margin-left: 30px" @click="showPop = false">取消</Button>
          </div>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Cropper from 'components/form/cropperPlus';
  const CDNURL = 'https://imagecdn.rocketbird.cn/minprogram';
  export default {
    name: 'iconPicker',
    props: { value: {} },
    components: { Cropper },
    data() {
      return {
        tabIndex: 'suggest',
        showPop: false,
        selected: '',
        icons: [
          ...['fun-icon-1', 'fun-icon-2', 'fun-icon-3', 'fun-icon-4', 'fun-icon-5'].map(icon => `${CDNURL}/uni-member/${icon}.png`),
          ...['tie-icon-1', 'tie-icon-2', 'tie-icon-3', 'tie-icon-4', 'tie-icon-5', 'tie-icon-6', 'tie-icon-7'].map(icon => `${CDNURL}/uni-member/${icon}.png`),
          ...['links-1', 'links-2', 'links-3', 'links-4', 'links-5', 'links-6', 'links-7', 'links-8'].map(icon => `${CDNURL}/member/theme/${icon}.png`),
          ...['activity', 'course', 'coach', 'intro', 'advisory', 'buy', 'charm', 'mall'].map(icon => `${CDNURL}/member/theme/icon-fresh-${icon}.png`),
          ...['activity', 'course', 'coach', 'intro', 'advisory', 'buy', 'charm', 'mall'].map(icon => `${CDNURL}/member/theme/icon-paint-${icon}.png`),
          ...['activity', 'course', 'coach', 'intro', 'advisory', 'buy', 'charm', 'mall'].map(icon => `${CDNURL}/member/theme/icon-hotel-${icon}.png`),
          ...['activity', 'course', 'coach', 'intro', 'advisory', 'buy', 'charm', 'mall'].map(icon => `${CDNURL}/member/theme/icon-bussiness-${icon}.png`)
        ]
      };
    },
    created() {
      this.selected = this.value;
    },
    methods: {
      handleSave() {
        this.$emit('on-change', this.selected);
        this.showPop = false;
      },
      onUpload(save) {
        save().then(() => this.handleSave());
      }
    }
  };
</script>

<style lang="less" scoped>
  .upload {
    padding: 0 10px;
    height: 32px;
    background-color: #19be6b;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    line-height: 32px;
    font-size: 14px;
    user-select: none;
    &:hover {
      opacity: 0.8;
    }
  }
  .cropper-wrap {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .icons {
      display: grid;
      // grid-template-rows: 1fr 1fr 1fr;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
      grid-gap: 1px;
      background-color: #ccc;
      padding: 1px;
      margin: 15px 0;
      > div {
        background-color: #fff;
        text-align: center;
        padding: 10px 0;
        box-sizing: border-box;
        &.active {
          box-shadow: 0 0 0 2px inset hsl(200, 100%, 50%);
        }
        img {
          // width: 44px;
          height: 36px;
        }
      }
    }
  .icon-picker {
    display: flex;
    flex-direction: column;
    align-items: center;
    .icon {
      width: 44px;
      height: 44px;
      margin-bottom: 10px;
    }
    
    .button {
      display: flex;
      justify-content: center;
      padding: 5px 0 15px;
    }
  }
</style>
