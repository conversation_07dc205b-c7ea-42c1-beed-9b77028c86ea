<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">{{postData.temp_type==2?'私教课':'泳教课'}}标签</Checkbox>
        </Checkbox-group>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import { mapState } from 'vuex'
export default {
  name: 'DiyCardList',
  components: {
    RightBox,
  },
  computed: {
    ...mapState('diy', ['theme8','indexLeftSelected8']),
    postData: {
      get() {
        return this.theme8.list[this.indexLeftSelected8]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 8
        })
      },
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>