<template>
  <Modal
    v-model="showAdd"
    title="教练选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.param"
          @on-enter="getList"
          placeholder="姓名/电话"
        ></Input>
        <Select
          style="width: 120px"
          v-if="coachPosition && coachPosition.length"
          v-model="searchData.position_id"
          clearable
          placeholder="职务"
        >
          <Option
            v-for="position in coachPosition"
            :key="position.id"
            :value="position.id"
          >{{position.name}}</Option>
        </Select>
        <Select
          style="width: 120px"
          v-model="searchData.coach_type"
          placeholder="教练类型"
          clearable
        >
          <Option value="私教教练">私教教练</Option>
          <Option value="操课教练">操课教练</Option>
          <Option value="游泳教练">游泳教练</Option>
        </Select>
        <Select
          style="width: 120px"
          v-model="searchData.id"
          placeholder="组别"
          clearable
        >
          <Option
            v-for="group in coachGroup"
            :key="group.id"
            :value="group.id"
          >{{group.name}}
          </Option>
        </Select>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          class="avatar-zoom"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import Selection from 'mixins/selection'
import { coachGroup, coachPosition } from 'src/service/getData';

export default {
  name: 'CoachTable',
  mixins: [Selection],
  data() {
    return {
      pageTotal: 0,
      searchData: {
        param: '',
        position_id: '',
        id: '', //分组id
        coach_type: '',
        page_no: 1,
        page_size: 10,
      },
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
          title: '形象照',
          key: 'avatar',
          className: 'avatar-wrap',
          render: (h, params) => {
            let url = params.row.avatar
            return (
              <img
                src={url}
                class="avatar"
                style={{ width: '30px', height: '30px' }}
              />
            )
          },
        },
        {
          title: '姓名',
          key: 'name',
        },
        {
          title: '电话',
          key: 'phone',
        },
        {
          title: '职务',
          key: 'position',
        },
        {
          title: '类型',
          key: 'coach_type',
          render: (h, params) => {
            const coachType = params.row.coach_type.split(',')
            let renderDom = []
            coachType.forEach((item) => {
              if (item === '私教教练') {
                renderDom.push(<tag color="blue">私</tag>)
              } else if (item === '游泳教练') {
                renderDom.push(<tag color="gold">泳</tag>)
              } else {
                renderDom.push(<tag color="green">操</tag>)
              }
            })
            return renderDom
          },
        },
        {
          title: '分组',
          key: 'coach_group_name'
        }
      ],

      coachGroup: [],
      coachPosition: [],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    selectBusId: String || Number
  },

  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
  },
  components: {},
  created() {
    this.getCoachGroup()
    this.getCoachPosition()
    this.getList()
  },
  methods: {
    getCoachGroup() {
      coachGroup(this.selectBusId).then(res => {
        if (res.data.errorcode === 0) {
          this.coachGroup = res.data.data
        }
      })
    },
    getCoachPosition() {
      coachPosition(this.selectBusId).then(res => {
        if (res.data.errorcode === 0) {
          this.coachPosition = res.data.data
        }
      })
    },
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection,
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/Coach/get_bus_coach_list', { ...this.searchData, bus_id: this.selectBusId })
        .then((res) => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            const list = data.list
            this.tableData = list.map((item) => {
              return { ...item, _checked: this.selectionId.includes(item.id) }
            })
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
  },
}
</script>

