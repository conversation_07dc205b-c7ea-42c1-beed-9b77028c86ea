<template>
  <Modal
    class="link-select-modal"
    title="链接选择"
    :value="show"
    :width="50"
    :mask-closable="false"
    @on-visible-change="handleClose">
    <!-- <div class="modal-header">
      <Input v-model.trim="state.search" :disabled="!filterLinkTree.length" placeholder="搜索"></Input>
    </div> -->

    <div v-show="filterLinkTree.length" class="modal-content">
      <div class="select-box">
        <div class="select-title">链接页面名称</div>
        <ul class="select-ul" @click.prevent="handleClick($event, 'left')">
          <li
            class="radio-li"
            v-for="(v, i) in filterLinkTree"
            :key="i"
            :data-index="i">
            <i class="radio-box-inner" :class="{ 'checked': state.leftIndex === i }" />
            <span
              class="check-box-text text_overflow"
              :title="v.label.length > 10 ? v.label : null">
              {{ v.label }}
            </span>
          </li>
        </ul>
      </div>
      <div v-if="IS_BRAND_SITE && isMerchantMode && isSelectMerchant && linkTree[state.leftIndex].value !== 'miniProgram'" class="select-box">
        <div class="select-title">展示数据门店</div>
        <ul class="select-ul" @click.stop="handleClick($event, 'center')">
          <li
            class="radio-li"
            v-for="(v, i) in merBusList"
            :key="i"
            :data-index="i">
            <i class="radio-box-inner" :class="{ 'checked': state.centerIndex === i }" />
            <span
              class="check-box-text text_overflow"
              :title="v.bus_name.length > 10 ? v.bus_name : null">
              {{ v.bus_name }}
            </span>
          </li>
        </ul>
      </div>
      <div class="select-box">
        <div class="select-title">数据默认分类</div>
        <div class="input-box select-ul" v-if="linkTree[state.leftIndex].value === 'miniProgram'">
          <div class="input-line" v-for="(son, i) in linkTree[state.leftIndex].children" :key="i">
            <div class="label">{{ son.label }}:</div>
            <Input v-model.trim="son.value" :placeholder="son.example" />
          </div>
        </div>
        <ul class="select-ul" v-else @click="handleClick($event, 'right')">
          <li
            class="radio-li"
            v-for="(v, i) in linkChildren"
            :key="i"
            :data-index="i">
            <i class="radio-box-inner" :class="{ 'checked': state.rightIndex === i }" />
            <span
              class="check-box-text text_overflow"
              :title="v.label.length > 10 ? v.label : null">
              {{ v.label }}
            </span>
          </li>
        </ul>
      </div>
    </div>
    <div class="modal-content">
      <div v-show="!filterLinkTree.length" class="nodata">
        无可选数据
      </div>
    </div>

    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleConfirm" :disabled="!linkTree.length">确定</Button>
      <Button @click="handleClose(false)">取消</Button>
      <!-- <Button @click="handleReset">重置</Button> -->
    </div>
  </Modal>
</template>

<script setup>
  import { useState, useGetters, useActions } from 'vuex-composition-helpers'
  import { computed, watch, reactive, inject, nextTick } from 'vue'
  import { getQueryString, getQueryStringByName } from "@/utils/index"
  import { Message } from 'iview'

  const emit = defineEmits(['update:show'])
  const IS_BRAND_SITE = inject('IS_BRAND_SITE')
  const { busIdEncode } = useState(['busIdEncode'])
  const { links, merBusList, firstBusIdEncode, selectMerBusIdEncode } = useState('diy', ['links', 'merBusList', 'firstBusIdEncode', 'selectMerBusIdEncode'])
  const { isMerchantMode, isSelectMerchant } = useGetters('diy', ['isMerchantMode', 'isSelectMerchant'])
  const { linkListInit } = useActions('diy', ['linkListInit'])
  // const { busList } = useState(['busList'])

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    selected: {
      type: Array,
      default: () => []
    },
    currentBusId: {
       type: String,
      default: ''
    }
  })

  const initialState = () => ({
    search: '',
    leftIndex: 0,
    centerIndex: 0,
    rightIndex: null,
  })
  const state = reactive(initialState())

  const linkTree = computed(() => {
    const busId = merBusList.value[state.centerIndex]
    ? merBusList.value[state.centerIndex].encode_bus_id
    : props.currentBusId || (isSelectMerchant.value ? firstBusIdEncode.value : selectMerBusIdEncode.value)
    return links.value[busId] ? links.value[busId].linkTree : []
  })
  // 是否展示 链接其他门店，弹窗选择框里面判断
  const filterLinkTree = computed(() => {
    return linkTree.value.filter(v => {
      if (v.label === '链接其他门店' && !(isMerchantMode.value && isSelectMerchant.value)) {
        return false
      }
      return true
    });
  })

  const useValue = (path) => {
    const valueArr = path.split(decodeURIComponent(path) === path ? 'bus_id=' : 'bus_id%3D')
    const urlBusId = getQueryStringByName(path, 'bus_id')
    const url = urlBusId
      ? valueArr[0].slice(0, valueArr[0].length - (decodeURIComponent(valueArr[0]) === valueArr[0] ? 1 : 3))
      : valueArr[0]
    const decodeValue = decodeURIComponent(url)

    return {
      valueArr,
      urlBusId,
      url,
      decodeValue
    }
  }

  watch(
    () => props.show,
    (val) => {
      if (val) {
        Object.assign(state, initialState())
        if (props.selected.length) {
          const firstUrl = props.selected[0]
          const { decodeValue } = useValue(firstUrl)

          const index = linkTree.value.findIndex(v => {
            return (v.decodeValue || v.value) === decodeValue
          })
          if (index !== -1) {
            state.leftIndex = index
            // if(IS_BRAND_SITE.value) {
              const lastUrl = props.selected[props.selected.length - 1]
              const { urlBusId, decodeValue } = useValue(lastUrl)
              const busId = urlBusId || props.currentBusId || (isSelectMerchant.value ? firstBusIdEncode.value : selectMerBusIdEncode.value)
              if (busId) {
                const centerIndex = merBusList.value.findIndex(v => v.encode_bus_id === busId)

                if (centerIndex !== -1) {
                  state.centerIndex = centerIndex
                }
              }
            // }

            if (props.selected.length === 2) {
              const parent = linkTree.value[index]
              const subIndex = (parent.children || []).findIndex(v => {
                return (v.decodeValue || v.value) === decodeValue
              })

              state.rightIndex = subIndex !== -1 ? subIndex : null
            }
          }

          // console.log('decodeValue: ', decodeValue)
          if (decodeValue.includes(':')) {
            const appid = decodeValue.split(':')[0]
            const path = decodeValue.split(':')[1]
            const nodeIndex = linkTree.value.findIndex(v => v.value === 'miniProgram')
            state.leftIndex = nodeIndex
            const node = linkTree.value[nodeIndex]
            // console.log('node: ', node)
            node.children[0].value = appid
            node.children[1].value = path
          } else {
            const miniProgram = linkTree.value.find(v => v.value === 'miniProgram')
            miniProgram.children[0].value = ''
            miniProgram.children[1].value = ''
          }

        } else {
          const busId = props.currentBusId || (isSelectMerchant.value ? firstBusIdEncode.value : selectMerBusIdEncode.value)
          if (busId) {
            const centerIndex = merBusList.value.findIndex(v => v.encode_bus_id === busId)
            if (centerIndex !== -1) {
              state.centerIndex = centerIndex
                const list = links.value[busId] ? links.value[busId].linkTree : []
                if (!list.length) {
                  linkListInit(busId)
                    .catch(() => {
                      state.centerIndex = 0
                      state.rightIndex = null
                    });
                }
            }
          }

          const miniProgram = linkTree.value.find(v => v.value === 'miniProgram')
          miniProgram.children[0].value = ''
          miniProgram.children[1].value = ''
        }
      }
    },
    {
      immediate: true
    }
  )

  const querySelections = () => {
    let selections = [];
    function getSelections (arr, label, value) {
        for (let i = 0; i < arr.length; i++) {
            let item = arr[i];
            item.__label = label ? label + ' / ' + item.label : item.label;
            item.__value = value ? value + ',' + item.value : item.value;

            if (item.children && item.children.length) {
                getSelections(item.children, item.__label, item.__value);
                delete item.__label;
                delete item.__value;
            } else {
                selections.push({
                    label: item.__label,
                    value: item.__value,
                    display: item.__label,
                    item: item,
                    disabled: !!item.disabled
                });
            }
        }
    }
    getSelections(this.data);
    selections = selections.filter(item => {
        return item.label ? item.label.indexOf(this.query) > -1 : false;
    }).map(item => {
        item.display = item.display.replace(new RegExp(this.query, 'g'), `<span>${this.query}</span>`);
        return item;
    });
    return selections;
  }

  const linkChildren = computed(() => {
    const link = linkTree.value[state.leftIndex]
    return  link && link.children ? link.children : []
  })

  const handleClick = ({ target }, type) => {
    const li = [target, target.parentElement].find(v => v.tagName === 'LI')

    if (li && li.dataset.index) {
      const key = type + 'Index'
      const index = +li.dataset.index

      switch (key) {
        case 'leftIndex':
          state[key] = index
          state.rightIndex = null
          break;

        case 'centerIndex': {
          // const busId = merBusList.value[index] ? merBusList.value[index].encode_bus_id : props.currentBusId || (isSelectMerchant.value ? firstBusIdEncode.value : selectMerBusIdEncode.value)
          const busId = merBusList.value[index].encode_bus_id
          const list = links.value[busId] ? links.value[busId].linkTree : []

          if (!list.length) {
            linkListInit(merBusList.value[index].encode_bus_id)
              .then(() => {
                state[key] = index
                state.rightIndex = null
              }).catch(() => {
                state[key] = index
                state.rightIndex = null
              });
          } else {
            state[key] = index
            state.rightIndex = null
          }
          break;
        }

        case 'rightIndex':
          if (state[key] === index) {
            state.rightIndex = null
          } else {
            state.rightIndex = index
          }
          break;
      }
    }
  }

  const handleConfirm = () => {
    const link = linkTree.value[state.leftIndex]

    const urls = [link.value]
    
    if (link.value === 'miniProgram') {
      const appid = link.children[0].value
      const path = link.children[1].value
      link.decodeValue = `${appid}:${path}`
      // link.decodeValue = 'miniProgram'
      urls.push(encodeURIComponent(link.decodeValue))
      // console.log('urls', urls)
      if (!appid) {
        return Message.error('请输入小程序appid')
      }
      if (!path) {
        return Message.error('请输入小程序路径')
      } else if (path.indexOf(':') !== -1) {
        return Message.error('请输入正确的小程序路径, 如：pages/index/index')
      }
    }

    if (linkChildren.value.length && state.rightIndex !== null) {
      urls.push(linkChildren.value[state.rightIndex].value)
    }
    // 如果是商家端 并且 当前为综合体育场模式 并且 当前是在设置商家端数据 则需要带上门店id
    if (IS_BRAND_SITE.value && isMerchantMode.value && isSelectMerchant.value) {
      const bus = merBusList.value[state.centerIndex]
      const hasQuery = getQueryString(urls[urls.length - 1]).length
      const isEncode = decodeURIComponent(urls[urls.length - 1]) !== urls[urls.length - 1]
      const middle = isEncode
        ? hasQuery ? '%26' : '%3F'
        : hasQuery ? '&' : '?'
      const key = isEncode ? 'bus_id%3D' : 'bus_id='
      const url = `${urls[urls.length - 1]}${middle}${key}${bus.encode_bus_id}`
      urls[urls.length - 1] = isEncode ? url : encodeURIComponent(url)
    }

    emit('on-confirm', urls)
    handleClose(false)
  }

  const handleClose = (val = false) => {
    if (val === false) {
      emit('update:show', val)
    }
  }
</script>

<style lang="less" scoped>
  .modal-content {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    max-height: 55vh;

    .select-box {
      padding-right: 20px;
      flex: 1;
      min-width: 33.3%;
      font-size: 14px;
      &:last-child {
        padding-right: 0;
      }

      .select-title {
        height: 44px;
        line-height: 44px;
        font-size: 15px;
        font-weight: bold;
      }

      .select-ul {
        overflow-y: auto;
        padding: 10px;
        height: calc(100% - 44px);
        border: 1px solid #e0e3e9;
        border-radius: 5px;
        box-sizing: border-box;
      }

      .radio-li {
        display: flex;
        align-items: center;
        margin: 5px 0;
        width: 100%;
        height: 30px;
        white-space: nowrap;
        cursor: pointer;

        .check-box-text {
          display: inline-block;
          width: 100%;
        }
      }
    }
  }

  .radio-box-inner {
    position: relative;
    display: inline-block;
    margin-right: 6px;
    min-width: 14px;
    min-height: 14px;
    border: 1px solid #dcdee2;
    border-radius: 50%;
    background-color: #fff;
    transition: border-color .2s ease-in-out;
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 4px;
      height: 4px;
      transition: opacity .2s ease-in-out;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      opacity: 0;
      box-sizing: border-box;
      background-color: #2d8cf0;
    }

    &.checked {
      border-color: #2d8cf0;
      &::after {
        opacity: 1;
        width: 7px;
        height: 7px;
      }
    }
  }

  .nodata {
    width: 100%;
    height: 100%;
    position: relative;
    text-align: center;
    line-height: 50px;
    min-width: 286px;
    min-height: 289px;
    box-sizing: border-box;
    padding-top: 250px;
    margin: 0 auto;
    font-size: 24px;
  }
  .nodata::before {
    position: absolute;
    width: 103px;
    height: 144px;
    content: ' ';
    top: 144px;
    left: 50%;
    transform: translate(-50%, -50%);
    background: url('https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png')
      left top no-repeat;
    background-size: cover;
  }

  .input-box {
    .input-line {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 10px;

      .label {
        width: 140px;
        text-align: right;
        margin-right: 10px;
      }
    }
  }
</style>
