<template>
  <PageBox :type="8">
    <template slot-scope="slotProps">
      <diy-left title="卡课列表" :type="8" :data="slotProps.init.list"></diy-left>
      <mobile-card></mobile-card>
      <DiyCardListSetting v-show="indexLeftSelected8 === 'setting'" :type="8" />
      <component v-show="!(theme8.list[indexLeftSelected8] && theme8.list[indexLeftSelected8].isHide)" :type="8" :is="getComponentName()" :title="theme8.list[indexLeftSelected8] && theme8.list[indexLeftSelected8].name"></component>
    </template>
  </PageBox>
</template>

<script>
import PageBox from './PageBox'
import DiyLeft from './DiyLeft.vue'
import DiyCardListSetting from './DiyCardListSetting.vue'
import DiyCardList from './DiyCardList.vue'
import MobileCard from './MobileCard.vue'
import { mapState } from 'vuex';
export default {
  name: 'DiyPageCard',
  components: {
    PageBox,
    DiyLeft,
    DiyCardListSetting,
    DiyCardList,
    MobileCard,
  },
  computed: {
    ...mapState('diy', ['theme8','indexLeftSelected8'])
  },
  watch: {
  },
  data() {
    return {
      componentsName: ['','DiyCardList','DiyCardList',''],
    }
  },
  created() {
  },
  methods: {
    getComponentName() {
      if(typeof this.indexLeftSelected8 === 'number') {
        const type = this.theme8.list[this.indexLeftSelected8].temp_type - 1
        return this.componentsName[type]
      } 
      return ''
    }
  },
}
</script>

