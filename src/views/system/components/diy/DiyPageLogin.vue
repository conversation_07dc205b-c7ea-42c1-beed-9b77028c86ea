<template>
<PageBox :type="6">
 <MobileLogin :color="postData.fashion_color" :config="postData" />
 <RightBox>
    <div slot="title">登录页</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="背景设置：">
        <RadioGroup v-model="postData.background_type">
          <Radio label="1">默认背景</Radio>
          <Radio label="2">自定义</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem  v-show="postData.background_type == 2">
          <div class="tips">
            建议图片尺寸 750*1334px 推荐大小：100kb 
          </div>
         <upload-img-item des="尺寸：750*1334" :options="{aspectRatio: 750/1334}" class="mb10" v-model="postData.background_img"></upload-img-item>
      </FormItem>
    </Form>
  </RightBox>
</PageBox>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import { mapState } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
import MobileLogin from './MobileLogin'
export default {
  name: 'DiyPageLogin',
  components: {
    UploadImgItem,
    MobileLogin,
    PageBox,
    RightBox
  },
  computed: {
    ...mapState('diy', ['theme6']),
    postData: {
      get() {
        return this.theme6
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 6
        })
      }
    },
  },
  data() {
    return {
    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}
</style>

