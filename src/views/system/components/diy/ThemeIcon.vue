<template>
  <div
    class="iconfont themeicon"
    :class="type"
    :style="{color: color||'var(--THEME-COLOR)',fontSize: size+'px',lineHeight:size+'px'}"
  >
  </div>
</template>

<script>
export default {
  name: 'ThemeIcon',
  props: {
    size: {
      type: Number,
      default: 20
    },
    type: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    }
  }
}
</script>
