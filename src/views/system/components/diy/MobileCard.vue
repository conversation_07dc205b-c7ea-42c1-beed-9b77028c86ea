<template>
  <MobileBox class="diy-reserve">
      <Tabs
       v-if="theme8.setting.type === '1'"
      name="mobileCardTab"
      :animated="false"
      v-model="curTab"
    >
      <TabPane
        tab="mobileCardTab"
        v-for="(item, index) in theme8.list"
        :key="item.temp_type"
        :label="item.name"
        :disabled="true"
        :index="item.temp_type"
        :name="index+''"
      >
        <div class="nodata" v-show="item.isHide">
          模块已隐藏，会员端将不显示{{item.name}}
          <div>可点击左侧模块显示按钮显示</div>
        </div>
        <MobileMonthPayList style="padding-top:30px" v-show="!item.isHide" v-if="item.temp_type==5" />
        <MobileCardList v-show="!item.isHide && item.temp_type != 5" :type="item.temp_type" :config="item"/>
      </TabPane>
      </Tabs>
      <div class="cardlist-page" ref="cardlistPage" v-if="theme8.setting.type === '2'">
        <div class="theme-bg" v-for="(item, index) in theme8.list" :key="item.temp_type" :id="'mobileCard'+index">
          <div class="box-tit">{{ item.name }}</div>
          <div class="nodata" v-show="item.isHide">
            模块已隐藏，会员端将不显示{{item.name}}
            <div>可点击左侧模块显示按钮显示</div>
          </div>
          <MobileMonthPayList v-show="!item.isHide"  v-if="item.temp_type==5" />
          <MobileCardList
            v-show="!item.isHide && item.temp_type != 5"
            :type="item.temp_type"
            :config="item"
          />
        </div>
      </div>
    </MobileBox>
</template>

<script>
import { mapState } from 'vuex'
import MobileCardList from './MobileCardList'
import MobileMonthPayList from './MobileMonthPayList'
import MobileBox from './MobileBox'
export default {
  name: 'MobileCard',
  components: {
    MobileCardList,
    MobileMonthPayList,
    MobileBox
  },
  data() {
    return {
      curTab: '0',
      themeStyleVars: '--THEME-COLOR:#82dae4;--THEME-RGB:130,218,228;',
    }
  },
  watch: {
    indexLeftSelected8(val) {
      if(val === 'setting') return;
      this.curTab = val + ''
      this.$nextTick(() => {
        this.scrollToMobileCard('mobileCard'+val)
      })
    },
  },
  computed: {
    ...mapState('diy', ['theme8', 'indexLeftSelected8'])
  },
  created() {},
  methods: {
    scrollToMobileCard(id) {
      const cardlistPage = this.$refs.cardlistPage;
      const mobileCard = document.getElementById(id);
      if (cardlistPage && mobileCard) {
        const offsetTop = mobileCard.offsetTop;
        cardlistPage.style.transform = `translate3d(0,-${offsetTop}px,0)`;
      }
    }
  },
}
    
</script>
<style lang="less">
@import '~@/styles/themeVar.less';
.cardlist-page {
  :deep(.card-list-wrap) {
    padding: 0 30px 10px;
  }
  .box-tit {
    margin: 10px 30px;
  }
}
.mobile-box {
  font-size: 24px;
  .ivu-tabs-bar {
    border: 0 none;
    padding-top: 30px;
    background: #fff;
  }
  .ivu-tabs-ink-bar {
    display: none;
  }
  .ivu-tabs .ivu-tabs-nav .ivu-tabs-tab {
    font-size: 30px;
    padding: 8px 20px;
    color: #000;
  }
  .ivu-tabs .ivu-tabs-nav .ivu-tabs-tab-active {
    position: relative;
    font-size: 36px;
    font-weight: bold;
  }
  .ivu-tabs-nav .ivu-tabs-tab-active::before {
    display: block;
    content: " ";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 6px;
    background: var(--THEME-COLOR);
  }
  .pt-list-wrap {
    height: 100%;
  }
  .switch-wrap {
  padding: 10px;
}
.date-switch {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 22px 20px 0;
  overflow: hidden;
}
.date-switch .date {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
  width: 86px;
  margin-bottom: 35px;
}
.time-limit {
  margin-bottom: 0;
  font-size: 26px;
  margin-bottom: 10px;
}
.pt-list-wrap {
  height: 100%;
}
.date-switch .date .day {
  width: 66px;
  height: 66px;
  font-size: 32px;
  font-weight: bold;
  color: #313131;
  text-align: center;
  line-height: 66px;
}
.date-switch .date .week {
  margin-bottom: 10px;
  font-size: 24px;
}
.date-switch .active .day {
  background: var(--THEME-COLOR);
  border-radius: 50%;
}

.private-list {
  box-sizing: border-box;
}
.private-list .course {
  box-sizing: border-box;
  width: 690px;
  height: 190px;
  border: 1px solid var(--THEME-COLOR);
  border-radius: 20px;
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  margin: 25px auto 0;
  .lef-tag {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 20px 0 20px 0;
    width: 147px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(var(--THEME-RGB), 0.4);
    font-size: 20px;
  }
}
.private-list .course .left {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  .time {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-weight: bold;
    margin-right: 66px;
    .time-line {
      width: 2px;
      height: 16px;
      background-color: var(--THEME-COLOR);
      margin: 6px 0;
    }
  }
   .time-bg {
    margin-right: 10px;
    padding: 0 10px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: var(--THEME-COLOR);
    border-radius: 15px;
    font-weight: bold;
  }
}
.coach-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin-right: 26px;
}
.field-avatar {
  border-radius: 12%;
}
.surplus-num {
  width: 100px;
  text-align: center;
  font-size: 22px;
  color: @theme-text-color-grey;
}
.coach-info {
  display: flex;
  flex: 1;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;
  .name {
    display: flex;
    align-items: center;
    font-size: 30px;
    margin-right: 28px;
    margin-bottom: 15px;
    font-weight: bold;
    > text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.1;
    }
    .name-content{
      display: flex;
      align-items: center;
      font-size: 30px;
      margin-right: 28px;
      margin-bottom: 15px;
      font-weight: bold;
      span+span{
        color: rgba(#000, 0.5)
      }
    }
  }
  .item {
    display: flex;
    align-items: center;
  }
  .coach {
    margin-bottom: 16px;
  }
  .coach-classes {
    display: flex;
    flex-wrap: wrap;
    max-height: 80px;
    overflow: hidden;
    font-size: 20px;
  }
  .class {
    display: inline-block;
    line-height: 32px;
    padding: 0 15px;
    height: 32px;
    box-sizing: border-box;
    background: rgba(var(--THEME-RGB), 0.2);
    border-radius: 6px;
    margin-right: 7px;
    margin-bottom: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.reserve-btn {
  width: 94px;
  height: 94px;
  background: var(--THEME-COLOR);
  border-radius: 50%;
  text-align: center;
  line-height: 94px;
  position: relative;
  font-size: 26px;
  font-weight: bold;
  color: #03080e;
}
.reserve-btn::after {
  position: absolute;
  left: -8px;
  top: -8px;
  width: 110px;
  height: 110px;
  content: ' ';
  border: 4px solid var(--THEME-COLOR);
  border-radius: 50%;
  border-right: 4px solid transparent;
  transform: rotate(-45deg);
}
.reserve-btn.circle::after {
  border: 4px solid var(--THEME-COLOR);
}
.reserve-btn.disabled {
  background-color: #dedede;
  color: @theme-text-color-grey;
}
.reserve-btn.disabled::after {
  border: 4px solid #dedede;
}
.reserve-btn.waiting {
  background-color: @theme-text-color-other;
  color: #fff;
}
.reserve-btn.waiting::after {
  border: 4px solid @theme-text-color-other;
}
.reserve-btn.waiting-circle {
  background-color: transparent;
  color: @theme-text-color-other;
}
.reserve-btn.waiting-circle::after {
  border: 4px solid @theme-text-color-other;
}
}
</style>

<style lang="less" scoped>
.diy-reserve {
  margin-right: 30px;
  margin-top: 50px;
  /deep/ .mobile-box {
    overflow-y: scroll ;
  }
}
</style>