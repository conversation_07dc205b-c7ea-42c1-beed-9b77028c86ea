<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      avatar-key="thumb"
      add-tips="选择套餐包"
      :img-width="138"
      :img-height="80"
      name-key="card_name"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <PackageTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import PackageTable from './PackageTable'
export default {
  name: 'PackageItem',
  components: {
    PackageTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    src: {
      type: String
    },
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
