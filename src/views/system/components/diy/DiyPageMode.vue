<template>
<PageBox :type="9" ref="pageBoxRef" v-on="$listeners">
  <template slot-scope="{ init }">
    <div class="main-right">
      <div class="mode-wrap">
        <Card
          class="mode-item"
          :class="{ active: isMerchantMode }"
          dis-hover
          @click.native="handleClick('1')">
          <div style="text-align:center">
            <Badge :text="init && init.mode === '1' ? '使用中' : ''" type="primary">
              <div class="mode-title">综合体育场版</div>
            </Badge>
          </div>
          <div class="mode-info">
            <Alert type="warning">体育场下有多业态运动项目，每个项目作为子门店单独运营，用户进入小程序看到体育馆的主页面</Alert>
            <p>小程序有专门商家主页，商家主页上可以推荐展示各个门店相关数据</p>
            <p>用户注册登录时全部门店都会添加会员信息</p>
          </div>
        </Card>
        <Card
          class="mode-item"
          :class="{ active: !isMerchantMode }"
          dis-hover
          @click.native="handleClick('0')">
            <div style="text-align:center">
            <Badge :text="init && init.mode === '0' ? '使用中' : ''" type="primary">
              <div class="mode-title">健身瑜伽版</div>
            </Badge>
          </div>
          <div class="mode-info">
            <Alert type="warning">健身瑜伽连锁，门店分布较广，单店各自独立维护经营会员</Alert>
            <p>用户访问时小程序时根据地理位置自动切换到最近门店</p>
            <p>用户注册登录时只在某个单一门店留下信息，不予其他门店共享会员信息</p>
          </div>
        </Card>
      </div>
    </div>
  </template>
</PageBox>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import PageBox from './PageBox'

export default {
  name: 'DiyPageMode',
  components: {
    PageBox,
  },

  data() {
    return {
      // mode: '0'
    }
  },
  computed: {
    // ...mapState(['merchantId']),
    ...mapState('diy', ['theme9']),
    ...mapGetters('diy', ['isMerchantMode']),
    postData: {
      get() {
        return this.theme9
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 9
        })
      }
    },
  },

  // watch: {
  //   'theme9.mode'(val) {
  //     if (val) {
  //       this.mode = val
  //     }
  //   }
  // },
  beforeDestroy() {
    const vm = this.$refs.pageBoxRef
    if (vm) {
      this.$store.commit('diy/UPDATE_DIY_THEME', {
        type: 9,
        value: {
          mode: vm.initData.mode
        }
      })
    }
  },

  methods: {
    handleClick(val) {
      // this.mode = val
      this.postData = {
        mode: val
      }
    },
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: px solid #E8E8E8;
}

.mode-wrap {
  display: flex;
  height: 610px;

  .mode-item {
    width: 400px;
    height: 300px;
    border: 2px solid #dcdee2;
    cursor: pointer;
    &:first-child {
      margin-right: 42px;
    }

    &.active {
      border-color: #52a4ea;
      .mode-title {
        border-color: #52a4ea;
      }
    }

    .mode-info {
      margin-top: 20px;

      .ivu-alert {
        margin-bottom: 20px;
      }
    }

    .mode-title {
      padding: 10px;
      width: 180px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #313131;
      text-align: center;
      margin-bottom: 15px;
      border: 2px solid #dcdee2;
      border-radius: 3px;
      transition: border-color 0.2s ease-in-out;
    }
  }
}
</style>

