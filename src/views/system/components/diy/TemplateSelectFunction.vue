<template>
  <Modal
    style="visibility: hidden;"
    v-model="showAdd"
    title="添加模块"
    :mask-closable="false"
    width="600px"
  >
    <link-select
      ref="linkSelectRef"
      v-if="showAdd"
      v-model="name"
      @on-change="itemChanged($event)"
      @update:show="handleUpdateLinkShow"></link-select>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="doConfirm">确定</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import LinkSelect from './LinkSelect'
export default {
  name: 'TemplateSelectFunction',
  components: {
    LinkSelect,
  },
  data() {
    return {
      curItem: '',
      name: '',
    }
  },
  props: {
    value: {
      type: Boolean,
    },
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },

  watch: {
    showAdd(val) {
      if (!val) {
        this.curItem = ''
        this.name = ''
      } else {
        this.$nextTick(() => {
          const ref = this.$refs.linkSelectRef
          if (ref) {
            ref.handleShowSelectModal()
          }
        })
      }
    }
  },

  methods: {
    itemChanged(info) {
      let obj = {
        title: info.img_name,
        url: info.img_link,
      }
      this.curItem = obj

      this.doConfirm()
    },
    doConfirm() {
      if (!this.name) {
        this.$Message.error('请先选择')
        return false
      }
      this.$emit('on-confirm', {
        name: this.curItem.title,
        icon:'https://imagecdn.rocketbird.cn/minprogram/member/theme/links-1.png',
        link: this.curItem.url
      })
      // this.showAdd = false
      setTimeout(() => {
        // 平滑关闭
        this.showAdd = false
      }, 300);
    },

    handleUpdateLinkShow(val) {
      if (!val) {
        setTimeout(() => {
          this.showAdd = false
        }, 300);
      }
    }
  },
}
</script>
