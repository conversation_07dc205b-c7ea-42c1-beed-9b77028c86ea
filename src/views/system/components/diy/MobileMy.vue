<template>
  <MobileBox class="mobile-my">
    <div class="my-wrap">
      <img class="top-img" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/invitation/my-top.jpg " />
      <div class="bot-wrap theme-bg" v-if="theme4.my_interest.length">
      <div class="area-tit">
        <img class="area-img" src="/static/img/vip.png" />
        我的权益
      </div>
      <div class="nav-wrap">
        <div
          v-for="item in iconInfo.my_interest"
          v-show="getIsShowIcon(item.name, 'my_interest')"
          :key="item.name"
          class="bot-item bot-my"
          hover-class="none"
        >
          <div class="icon-wrap">
            <ThemeIcon :size="42" :color="themeIconColor" class="item-icon" :type="item.icon" />
          </div>
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- 邀请有礼 -->
    <div
      v-if="theme4.display === 1"
      class="invitation-wrap"
    >
      <img class="invitation-img" :src="theme4.background_img" />
    </div>
    <div class="bot-wrap theme-bg" v-if="theme4.service_item.length">
      <div class="area-tit">
        <img class="area-img" src="/static/img/fuwu.png" />
        服务项目
      </div>
      <div class="nav-wrap">
        <div
          v-for="item in iconInfo.service_item"
          v-show="getIsShowIcon(item.name, 'service_item')"
          :key="item.name"
          class="bot-item"
          hover-class="none"
        >
          <div class="icon-wrap">
            <ThemeIcon :size="42" :color="themeIconColor" class="item-icon" :type="item.icon" />
          </div>
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="bot-wrap theme-bg" v-if="theme4.personal_data.length">
      <div class="area-tit">
        <img class="area-img" src="/static/img/shuju.png" />
        个人数据
      </div>
      <div class="nav-wrap">
        <div
          v-for="item in iconInfo.personal_data"
          v-show="getIsShowIcon(item.name, 'personal_data')"
          :key="item.name"
          class="bot-item"
          hover-class="none"
        >
          <div class="icon-wrap">
            <ThemeIcon :size="42" :color="themeIconColor" class="item-icon" :type="item.icon" />
          </div>
          {{ item.name }}
        </div>
      </div>
    </div>
      
    </div>
  </MobileBox>
</template>

<script>
  import { mapState } from 'vuex'
  import MobileBox from './MobileBox'
  import ThemeIcon from './ThemeIcon';
  export default {
    name: 'MobileMy',
    props: {
      config: Object
    },
    components: {
      MobileBox,
      ThemeIcon
    },
    data() {
      return {
       themeIconColor: '#000',
       iconInfo:{
          my_interest: [
            { icon: 't-icon-wode-huiyuanka', name: '会员卡课' },
            { icon: 't-icon-wode-ruchangpingzheng', name: '入场凭证' },
            { icon: 't-icon-wode-yuyuejilu', name: '预约记录' },
            { icon: 't-icon-wode-zhekoujuan', name: '我的折扣券' },
            { icon: 't-icon-wode-wodehetong', name: '我的合同' },
          ],
          personal_data: [
            { icon: 't-icon-wode-qiandaojilu', name: '运动记录' },
            { icon: 't-icon-wode-lishitice', name: '历史体测' },
          ],
          service_item: [
            { icon: 't-icon-wode-wodefapiao', name: '我的发票' },
            { icon: 't-icon-wode-kaiguimima', name: '开柜密码' },
            { icon: 't-icon-wode-liuyanzixun', name: '留言咨询' },
            { icon: 't-icon-wode-renlianshibie', name: '人脸识别' },
            { icon: 't-icon-wode-qingjiajilu2', name: '请假记录' },
            { icon: 't-icon-wode-zuguijilu', name: '租柜记录' },
            { icon: 't-icon-wode-wodehongbao', name: '红包记录' },
            { icon: 't-icon-wode-bangzhuzhongxin', name: '帮助中心' },
            { icon: 't-icon-wode-juanmaduihuan', name: '券码兑换' },
            { icon: 't-icon-wode-zaixiankefu', name: '在线客服' },
          ],
        }
      }
    },
    watch: {},
    computed: {
     ...mapState('diy', ['theme4'])
    },
    created() {},
    methods: {
      getIsShowIcon(name, type) {
        return this.theme4[type]?this.theme4[type].includes(name):false
      }
    },
  }
</script>

<style lang="less" scoped>
.mobile-my {
  margin-right: 30px;
  position: relative;
}
.my-wrap {
  height: 100%;
  overflow-y: scroll;
}
.top-img {
  width: 750px;
  height: 483px;
}
.area-tit {
  line-height: 50px;
  font-size: 30px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  .area-img {
    margin-right: 15px;
    width: 24px;
    height: 24px;
  }
}
.invitation-wrap {
  margin: 0 20px;
  img {
    width: 100%;
  }
}
.bot-wrap {
  margin: 30px 20px 20px;
  padding: 20px;
  border-radius: 10px;
}
.nav-wrap {
  display: flex;
  flex-wrap: wrap;
}
.bot-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  padding: 0;
  min-width: 25%;
  font-size: 22px;
  .red-dot {
    position: absolute;
    right: 0;
    top: -10px;
    width: 30px;
    height: 30px;
    background: #ff0000;
    border-radius: 50%;
    font-size: 22px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
  }
  .long-dot {
    width: auto;
    padding: 0 10px;
    border-radius: 6px;
    white-space: nowrap;
  }
  .icon-wrap {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14px;
    border: 1px solid #ff7427;
    border-radius: 50%;
    width: 80px;
    height: 80px;
  }
}
.bot-item:last-child {
  border-bottom: 0 none;
}
.bot-my {
  flex:1;
  min-width: 20%;
}
.style2 {
  display: block;
  .bot-item {
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f4f3f8;
    width: 100%;
  }
  .item-icon {
    margin-right: 20px;
  }
  .icon-wrap {
    margin-bottom: 0;
  }
}

.payscore {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  background: linear-gradient(91deg, #f6daae, #ebcaa1);
  border-radius: 20px;
  padding: 0 28px;
  margin-top: 30px;

  .photo {
    width: 91.4px;
    height: 44.8px;
  }

  .message {
    font-size: 24px;
    font-weight: bold;
    color: #000000;
  }

  .label {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 92px;
    height: 40px;
    background: #c59d6c;
    border-radius: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
  }
}
</style>
