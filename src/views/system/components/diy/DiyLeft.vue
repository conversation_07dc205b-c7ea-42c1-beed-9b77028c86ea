<template>
  <div class="diy-left">
    <div v-if="this.type==2 || this.type==8" :class="selectedIndex==='setting'?'item-selected' : ''" class="list-group-item set-wrap" @click="handleSetting('setting')">
      {{this.type==2?'首页':'卡课列表'}}设置
    </div>
    <h3 class="tit">{{title}}</h3>
    <div class="des">模块排版样式支持自定义，拖动模块可排序</div>
    <div @click="addTemplate" class="group-add" v-if="canAdd">
      <Icon
        type="md-add"
        color="#47CB89"
      />
      <div class="add-after">添加模块</div>
    </div>
    <Draggable
      class="list-group"
      tag="ul"
      v-model="list"
      v-bind="dragOptions"
      @start="drag = true"
      @end="drag = false"
    >
      <transition-group type="transition">
        <li
          class="list-group-item"
          :class="selectedIndex===index?'item-selected' : item.isHide ? 'disabled' : ''"
          v-for="(item, index) in list"
          :key="`list-diy-left-${index}`"
          @click.self="handleItem(item, index)"
        >
          <div class="item-left">
            <Icon
              class="icon-move"
              type="ios-move"
            />
            <div
              class="item-input"
              v-if="item.isEdit"
            >
              <Input
                v-model="list[index].editedName"
                :maxlength="10"
                placeholder="最多10个字"
              />
              <span
                class="input-rig-num"
                slot="suffix"
              >{{list[index].editedName.length}}/10</span>
              </Input>
            </div>
            <div
              class="item-name"
              v-else
            >
              {{ item.name }}
            </div>
            <Icon
              @click.self.stop="showEdit(index)"
              v-if="!item.isEdit"
              class="icon-edit pointer"
              type="md-create"
            />
            <Button
              type="text"
              class="item-btn"
              @click="saveInfo(index)"
              v-if="item.isEdit"
            >保存
            </Button>
            <Icon @click="cancelInfo(index)" class="icon-close pointer" v-if="item.isEdit" type="ios-close-circle-outline" />
          </div>
          <div class="item-rig" v-if="!item.isEdit">
            <div
              class="rig-icon-wrap"
              :class="{ show: item.isHide }"
              @click="turnItem(index)"
              :title="item.isHide?'点击显示':'点击隐藏'"
            >
              <Icon  v-if="!item.isHide" type="md-eye-off" />
              <Icon v-else type="md-eye" />
            </div>
            <div
              v-if="canDelete"
              class="rig-icon-wrap"
              title="删除"
              @click="delItem(index)"
            >
              <Icon type="md-trash" />
            </div>
          </div>
        </li>
      </transition-group>
    </Draggable>
    <TemplateSelectModal @on-confirm="confirmAddTemplate" v-model="showTemplateSelect" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Draggable from 'vuedraggable'
import TemplateSelectModal from './TemplateSelectModal'
export default {
  name: 'DiyLeft',
  components: {
    Draggable,
    TemplateSelectModal
  },
  computed: {
    ...mapState('diy', ['selectMerBusId']),
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  props: {
    title: {
      type: String,
      default: '首页模块'
    },
    canAdd: {
      type: Boolean,
      default: false
    },
    canDelete: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array
    },
    type: {
      type: Number,
      default: 2,
    },
  },
  watch: {
    selectMerBusId() {
      this.list = []
      this.handleItem(undefined, 0)
    },
    list: {
      deep:true,
      handler(value) {
        if([2, 8].includes(this.type)) {
          value = {
            list: value,
            setting: this.$store.state.diy['theme' + this.type].setting
          }
        }
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value: value,
          type: this.type
        })
      }
    },
    data: {
      handler(val) {
        if(val && val.length) {
          this.list = val.map((item, index) => {
            return {
              ...item,
              editedName: item.name,
              isEdit: false,
              isHide: item.isHide === 'true' || item.isHide === true ? true : false
            }
          })
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      list: [],
      selectedIndex: 0,
      showTemplateSelect: false,
      drag: false
    }
  },
  created() {
  },
  methods: {
    confirmAddTemplate(obj) {
      const info = obj.item
      this.list.push({
        ...info,
        order: this.list.length + 1,
        editedName: info.name,
        isEdit: false,
        isHide: false
      })
    },
    handleSetting(key) {
      this.selectedIndex = key
      this.$store.commit('diy/UPDATE_INDEX_LEFT_SELECTED', {value: key, type: this.type})
    },
    handleItem(item, index) {
      this.selectedIndex = index
      this.$store.commit('diy/UPDATE_INDEX_LEFT_SELECTED', {value: index, type: this.type})
    },
    addTemplate() {
      this.showTemplateSelect = true
    },
    showEdit(index) {
      this.$set(this.list, index, {
        ...this.list[index],
        isEdit:true
      })
    },
    saveInfo(index) {
      if(this.list[index].editedName) {
        this.list[index].name = this.list[index].editedName
        this.list[index].isEdit = false
      }
    },
    cancelInfo(index) {
      this.list[index].editedName = this.list[index].name
      this.list[index].isEdit = false
    },
    turnItem(index) {
      this.list[index].isHide = !this.list[index].isHide
    },
    delItem(index) {
      this.$Modal.confirm({
        title: '删除',
        content: '删除后无法恢复，确定删除？',
        onOk: () => {
          this.list.splice(index, 1)
          if(this.selectedIndex > this.list.length-1) {
            this.selectedIndex = this.list.length -1
            this.$store.commit('diy/UPDATE_INDEX_LEFT_SELECTED', {value: this.selectedIndex, type: this.type})
          }
        }
      });
    }
  },
}
</script>

<style lang="less" scoped>
.diy-left {
  width: 300px;
  flex-grow: 0;
  flex-shrink: 0;
  box-sizing: border-box;
  padding: 15px;
  height: 854px;
  overflow-y: auto;
  background: #f6f6f8;
  margin-right: 30px;
  .tit {
    font-size: 16px;
    font-weight: bold;
    color: #313131;
    margin-bottom: 15px;
  }
  .des {
    font-size: 14px;
    color: #7d7d7d;
    margin-bottom: 15px;
  }
}

.button {
  margin-top: 35px;
}

.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.list-group {
  min-height: 20px;
}

.group-add,
.list-group-item {
  // width: 270px;
  height: 70px;
  box-sizing: border-box;
  padding: 0 10px;
  background: #ffffff;
  border: 1px dashed #cccccc;
  border-radius: 6px;
  margin-bottom: 10px;
  display: flex;
  font-size: 14px;

  &.disabled {
    background: #f1f1f1;
    .item-name {
      color: #7d7d7d;
    }
  }
}

.item-selected {
  border-color:  #47CB89;
  background: #EEFBF5;
}
.group-add {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: #313131;
}
.add-after {
  margin-left: 5px;
}
.list-group-item {
  cursor: move;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #ccc;
  .pointer {
    cursor: pointer;
  }
  .item-name {
    color: #313131;
    margin-left: 5px;
  }
  .item-input {
    display: flex;
    width: 168px;
    justify-content: space-between;
    position: relative;
  }
  .input-rig-num {
    height: 32px;
    line-height: 32px;
    text-align: center;
    position: absolute;
    right: 5px;
    top: 0;
    z-index: 1;
    color: #ccc;
    font-size: 12px;
  }
  .item-btn {
    min-width: 40px;
    width: 40px;
  }
  .icon-close {
    font-size: 16px;
  }
  .icon-edit {
    margin-left: 5px;
  }
  .item-left {
    display: flex;
    align-items: center;
  }
  .item-rig {
    display: flex;
    align-items: center;
  }
  &:hover .item-rig .rig-icon-wrap {
    display: flex;
  }
  .rig-icon-wrap {
    cursor: pointer;
    width: 22px;
    height: 22px;
    background: #ffd8d9;
    border-radius: 50%;
    color: #ff7a80;
    display: none;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    &.show {
      display: flex;
    }
  }
}
.set-wrap  {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: #313131;
  font-weight: normal;
  height: 45px;
}
</style>
<style lang="less">
.diy-left .item-input .ivu-input {
  padding-right: 30px;
}
</style>
