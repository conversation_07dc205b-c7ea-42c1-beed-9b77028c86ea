<template>
   <div class="bot-wrap theme-bg" :class="config.type==2?'style2':''">
    <div class="box-tit">
      {{config.name || '明星教练'}}
      <div
        class="rig"
      >
        查看更多
        <div class="arrow-right"></div>
      </div>
    </div>
     <div class="bot-con coach-wrap">
      <div
        class="coach-item"
        v-for="item in coachList"
        :key="item.coach_id"
      >
        <img
          class="coach-img"
          :src="item.avatar"
        />
        <div class="bot">
          <div class="name">{{item.name || item.coach_name}}</div>
          <div class="pos" v-if="arrIndexOf(config.show_content, '3')">{{item.position}}</div>
          <div class="des" v-if="arrIndexOf(config.show_content, '1')">{{item.specialty}}</div>
        </div>
      </div>
      <div v-if="coachList && coachList.length == 0" class="nodata">暂未设置教练</div>
    </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
export default {
  name: 'CoachInfo',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  components: {
  },
  data() {
    return {
    }
  },
  computed: {
    coachList() {
      return this.config.recommend_type==2 ? this.config.recommend_coach_detail : this.initList
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(1, val)
    }
  },

  created() {
    this.getInitList(1, this.config.bus_id)
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.coach-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.coach-item {
  width: 207px;
  margin-right: 20px;
  font-size: 24px;
  margin-bottom: 30px;
  .pos {
    font-size: 22rx;
    margin-bottom: 10px;
    color: @theme-text-color-grey;
  }
  .name {
    font-weight: bold;
    margin-bottom: 12px;
  }
  .des,.name,.pos {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .bot {
    text-align: center;
  }
}
.coach-img {
  width: 205px;
  height: 205px;
  border: 1px solid #f6f6f8;
  border-radius: 20px;
  margin-bottom: 20px;
}
.style2 {
  .bot-con {
    flex-direction: column;
    align-items: flex-start;
  }
  .coach-item {
    display: flex;
    width: 100%;
    margin-right: 20px;
    justify-items: flex-start;
    align-items: center;
    .bot {
      text-align: left;
    }
  }
  .coach-img {
    width: 150px;
    height: 150px;
    margin-right: 30px;
    margin-bottom: 0;
  }
}
</style>
