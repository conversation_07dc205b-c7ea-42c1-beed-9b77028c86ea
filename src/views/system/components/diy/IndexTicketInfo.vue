<template>
    <div class="bot-wrap theme-bg">
    <div class="box-tit">
      {{config.name || '票务推荐'}}
      <div
        class="rig"
        v-if="list && list.length"
      >
        查看更多
        <div class="arrow-right"></div>
      </div>
    </div>
    <div class="bot-con class-wrap">
      <div
        class="index-ticket-item"
        v-for="(item, key) in list"
        :key="`card_list_${key}`"
      >
        <MobileTicketItem :info="item" />
      </div>
      <div v-if="list && list.length == 0" class="nodata">暂未设置推荐票务</div>
    </div>
    </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import MobileTicketItem from './MobileTicketItem'
export default {
  name: 'IndexTicketInfo',
  mixins: [memberPageIndex],
  components: {
    MobileTicketItem
  },
  props: {
    config: {
      type: Object
    }
  },
  data() {
    return {
    }
  },
  computed: {
    list() {
      return this.config.recommend_type==2 ? this.config.recommend_ticket_detail : this.initList
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(5, val)
    }
  },

  created() {
    this.getInitList(5, this.config.bus_id)
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.class-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
