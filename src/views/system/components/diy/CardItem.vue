<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      add-tips="选择卡课"
      :img-width="138"
      :img-height="80"
      :avatar-src="src"
      avatarKey="thumb"
      name-key="card_name"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <CardTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import CardTable from './CardTable'
export default {
  name: 'CardItem',
  components: {
    CardTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    src: {
      type: String
    },
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
