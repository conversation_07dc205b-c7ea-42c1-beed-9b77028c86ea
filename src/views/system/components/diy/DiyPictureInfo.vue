<template>
<RightBox>
  <div slot="title">{{postData.name}}</div>
  <div class="min-tit">
    <div class="tips">
      建议图片尺寸 710*280px 推荐大小：100kb
    </div>
  </div>
  <div class="link-item">
    <UploadImgItem :options="{aspectRatio: 71/28}" class="mb10" v-model="postData.img_url"></UploadImgItem>
    <LinkSelect v-model="postData.link_url" :postData="postData" @on-change="onLinkChange"></LinkSelect>
  </div>

</RightBox>
</template>

<script>
import RightBox from './RightBox'
import UploadImgItem from './UploadImgItem'
import LinkSelect from './LinkSelect'
import { mapState } from 'vuex'
import { getQueryStringByName } from "@/utils"


export default {
  name: 'DiyPictureInfo',
  components: {
    RightBox,
    UploadImgItem,
    LinkSelect
  },
  props: {
    title: {
      type: String,
      default: '场馆介绍'
    }
  },
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
  },
  data() {
    return {
    }
  },
  methods: {
    onLinkChange(info) {
      const bus_id = getQueryStringByName(info.img_link, 'bus_id')
      if (bus_id) {
        this.postData.bus_id = bus_id
      }
    },
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
.link-item {
  width: 167px;
}
</style>
