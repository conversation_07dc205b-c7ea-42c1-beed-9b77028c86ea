<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="排序规则：">
        <RadioGroup v-model="postData.recommend_type">
          <Radio label="1">创建时间倒序</Radio>
          <Radio label="2">自定义添加</Radio>
        </RadioGroup>
      </FormItem>
       <div v-show="postData.recommend_type == 2">
        <div class="tips">选择的场地类型不超过3种</div>
        <SpaceItem v-model="postData.recommend_space_detail" :selectBusId="selectBusId" />
      </div>
    </Form>

  </RightBox>
</template>

<script>
import SpaceItem from './SpaceItem'
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyIndexSpace',
  components: {
    RightBox,
    SpaceItem,
  },
   watch: {
    'postData.recommend_space_detail'(val) {
      const ids = val.map(item => item.id)
      if(JSON.stringify(ids) === JSON.stringify(this.postData.recommend_space)) {
        return
      }
      if(val && val.length) {
        this.postData.recommend_space = ids
      } else {
        this.postData.recommend_space = []
      }
    },
    'postData.bus_id'() {
      this.postData.recommend_space_detail = []
    }
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
