<template>
<div class="tabbar-item">
  <div
    class="rig-icon-wrap"
    v-if="[1, 2].includes(index)"
    @click="turnItem(index)"
    :title="postData.display !== 1 ?'点击显示':'点击隐藏'"
  >
    <Icon  v-if="postData.display === 1" :size="20" type="md-eye" />
    <Icon v-else :size="20" type="md-eye-off" />
  </div>
  <img :src="`https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/tabicons/${tabKeyArr[index]}.png`" />
  <div>
     <Input
        style="width: 150px"
        v-model="postData.name"
        placeholder="请填写名称"
        :maxlength="5"
      >
        <span slot="append">{{postData.name.length}}/5</span>
      </Input>
  </div>
</div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'DiyPageTabBarItem',
  components: {
  },
  props: {
    index: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapState('diy', ['theme5', 'indexLeftSelected5']),
    postData: {
      get() {
        return this.theme5[this.index]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 5,
          curIndex: this.index
        })
      },
    },
  },
  data() {
    return {
      tabKeyArr: ['home', 'reserve', 'mall', 'my']
    }
  },
  methods: {
    turnItem(index) {
      this.postData.display = this.postData.display === 1 ? 0 : 1
    }
  },
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}

.tabbar-item {
  position: relative;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 6px;
  box-sizing: border-box;
  background: #fff;
  border: 1px dashed #ccc;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    margin-bottom: 20px;
  }
  .rig-icon-wrap {
    cursor: pointer;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: #ffd8d9;
    border-radius: 50%;
    color: #ff7a80;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
  }
}
</style>

