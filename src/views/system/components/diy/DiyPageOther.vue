<template>
  <div class="diy-page-other">
    <DiySeletOther @on-click-item="changeComponent" />
    <component class="other-wrap" :is="curItem.componentName"></component>
  </div>
</template>

<script>
import DiySeletOther from './DiySeletOther'
import DiyPageCoach from './DiyPageCoach'
import DiyPageMy from './DiyPageMy'
import DiyPageLogin from './DiyPageLogin'
import DiyPagePoint from './DiyPagePoint'
export default {
  name: 'DiyPageOther',
  components: {
    DiyPageLogin,
    DiyPageCoach,
    DiyPageMy,
    DiyPagePoint,
    DiySeletOther
  },
  computed: {
  },
  data() {
    return {
      curItem: {
        componentName: ''
      }
    }
  },
  methods: {
    changeComponent(item) {
      this.curItem = item
    }
  },
}
</script>
<style lang="less" scoped>
.diy-page-other {
  display: flex;
  overflow: scroll;
}
.other-wrap {
  flex: 1;
}
</style>

