<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <Form
      :model="postData"
      :label-width="100"
    >
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="推荐套餐包：">
        <RadioGroup v-model="postData.recommend_type">
          <Radio :label="'1'">按创建时间倒序</Radio>
          <Radio :label="'2'">自定义添加</Radio>
        </RadioGroup>
      </FormItem>
      <div v-show="+postData.recommend_type == 2">
        <div class="tips ">建议添加的套餐包不超过3种</div>
        <PackageItem v-model="postData.recommend_package_detail" :selectBusId="selectBusId" />
      </div>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import PackageItem from './PackageItem'
import StyleSet from './StyleSet'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyPackageInfo',
  components: {
    RightBox,
    StyleSet,
    PackageItem,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-style-2.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-style-1.png',
          name: '横向',
          type: '2',
        },
      ],
    }
  },

  watch: {
    'postData.bus_id'() {
      this.postData.recommend_package_detail = []
    }
  },

  methods: {
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
.card-min {
  width: 138px;
  height: 80px;
}
</style>
