<template>
  <RightBox>
    <div slot="title">首页设置</div>
     <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
import StyleSet from './StyleSet'
export default {
  name: 'DiyCardListSetting',
  components: {
    RightBox,
    StyleSet,
  },
  computed: {
    ...mapState('diy', ['theme8','indexLeftSelected8']),
    postData: {
      get() {
        return this.theme8.setting
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_THEME_BY_KEY',  {
          value: val,
          type: 8,
          key: 'setting'
        })
      },
    },
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/cardlist-style-1.png',
          name: '抽屉样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/cardlist-style-2.png',
          name: '平铺样式',
          type: '2',
        }
      ]
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
