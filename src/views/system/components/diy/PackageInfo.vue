<template>
    <div class="bot-wrap theme-bg" :class="config.type==2?'style2':''">
    <div class="box-tit">
      {{config.name || '会员卡'}}
      <div
        class="rig"
        v-if="list && list.length"
      >
        查看更多
        <div class="arrow-right"></div>
      </div>
    </div>
    <div class="bot-con">
      <div
        class="card-item"
        v-for="item in list"
        :key="item.card_id"
      >
        <img class="card-img" :src="config.card_type || 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png'" mode="scaleToFill" />
        <div class="lef">
          <div class="tit">{{item.card_name||item.name}}</div>
          <div class="des">
            {{item.contend}}
          </div>
          <div class="price">{{item.amount ? `￥${item.amount}` : '价格面议'}}</div>
        </div>
      </div>
      <div v-if="list && list.length == 0" class="nodata">暂未设置会员卡</div>
    </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
export default {
  name: 'PackageInfo',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  data() {
    return {
    }
  },
  computed: {
    list() {
      return this.config.recommend_type==2 ? this.config.recommend_package_detail : this.initList
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(7, val)
    }
  },

  created() {
    this.getInitList(7,  this.config.bus_id)
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.card-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
   .lef {
    flex: 1;
  }
}
.card-item .tit {
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 13px;
}
.card-item .des {
  font-size: 24px;
  font-weight: 400;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: @theme-text-color-grey;
}
.card-item .price {
  margin-top: 20px;
  font-size: 30px;
  font-weight: bold;
  color: @theme-text-color-other;
}

.card-img{
  width: 258px;
  height: 134px;
  margin-right: 24px;
}
.style2 {
  .bot-con {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .card-item {
    width: 322px;
    box-sizing: border-box;
    border: 1px solid #E8E8E8;
    border-radius: 10px;
    padding: 31px;
    flex-direction: column;
    align-items: flex-start;
  }
  .card-img {
    margin-bottom: 10px;
  }
}
</style>
