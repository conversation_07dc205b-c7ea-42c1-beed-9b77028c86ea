<template>
  <div class="cropper">
    <label class="upload-btn">
      <template v-if="!cropImg">
         <img class="add-img" src="~src/assets/img/diy/up.png"/>
         <div>上传图片<span v-if="des" class="des">({{des}})</span></div>
      </template>
      <template v-else>
        <img :src="cropImg" class="upload-image" alt="图片">
        <div class="hover-div">
          重新上传
        </div>
      </template>
      <form id="myForm">
        <input type="file" :id="refName" name="image" :files="filedetail" style="font-size: 1.2em; padding: 10px 0; display: none" @change="setImage" ref="uploadInput" icon="ios-upload-outline" />
      </form>
    </label>
    <Modal v-model="showCropperModal" title="裁剪" width="700">
    
    <div class="image-wrapper" v-if="(imgSrc || cropImg) && !multiLoaded">
      
      <div id="preview" v-if="!nopreview&&!cropImg"></div>
      <VueCropper :ref="refName" v-show="imgSrc && !cropImg" preview="#preview" :view-mode="2" :aspectRatio="aspectRatio" :auto-crop-area="1" :min-container-width="500" :min-container-height="300" :src="imgSrc" alt="原图片" :img-style="{ width: '400px', height: '300px' }">
      </VueCropper>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="doUpload" v-if="imgSrc && !cropImg">确定</Button>
      <Button @click="cancelUpload" v-if="imgSrc && !cropImg">取消</Button>
    </div>
   </Modal>
  </div>
</template>
<script>
  // readme: 取消编辑右方的预览，父组件中传入nopreview, 默认是false
  //取消保存图片后的图片展示，父组件中传入multiple，默认为false
  //编辑图片时需要隐藏父组件中的图片显示，父组件中添加监听emit事件v-on:noshowpic

  import VueCropper from 'vue-cropperjs';
  export default {
    name: 'UploadImgItem',
    components: {
      VueCropper
    },
    data() {
      //imgsrc为生成的图片数据，cropimg为上传后生成的图片地址
      //multiloaded为图片展示，showmodelpic为选择模版图片
      return {
        imgSrc: '',
        showCropperModal: false,
        multiLoaded: true,
        showmodelPic: false,
        filedetail: []
      };
    },
    props: {
      value: {},
      compress: {
        type: Boolean,
        default: false
      },
      des: {
        type: String
      },
      userId: {
        type: [String, Number]
      },
      outputWidth: { type: Number, default: 0 }, // 输出图片宽度, 默认 0 不限制
      outputHeight: { type: Number, default: 0 }, // 输出图片高度, 如果不传默认为: 输出宽度 * 比例
      outputType: { type: String, default: 'png' }, // 输出图片格式, 默认为 png, 可选: jpeg, webp 等, 注: 上传后服务器返回的都是 png 格式, 但是 jpeg 会比 png 小很多
      options: {},
      refName: {
        default: 'cropper'
      },
      nopreview: {
        type: Boolean,
        default: false
      },
      maxSize: {
        type: Number,
        default: 1
      }
    },
    computed: {
      // 截取比例: 长 / 宽, 默认 1/1
      aspectRatio() {
        return (this.options && this.options.aspectRatio !== undefined)? this.options.aspectRatio : 1;
      },
      cropImg: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
    },
    methods: {
      cancelUpload() {
        this.showCropperModal = false
        this.cropImg = '';
        this.imgSrc = '';
        this.multiLoaded = true;
        this.$emit('noshowpic', false);
      },
      setImage(e) {
        this.cropImg = '';
        this.imgSrc = '';
        const file = e.target.files[0];
        if (this.maxSize && file.size > this.maxSize * 1024 * 1024) {
          this.$Message.error(`图片过大，超过${this.maxSize}M`);
          return;
        }
        if (file && !file.type.includes('image/')) {
          this.$Message.error('请选择图片文件');
          return;
        }
        this.showCropperModal = true
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = event => {
            this.multiLoaded = false;
            this.cropImg = this.uploadedImg = '';
            this.imgSrc = event.target.result;
            this.$nextTick(() => {
              this.$refs[this.refName].replace(event.target.result);
            });
            let imgform = document.getElementById('myForm');
            imgform.reset();
            if (this.imgSrc) {
              this.$emit('noshowpic', true);
            }
          };
        } else {
          this.$Message.error('您的浏览器版本过低，无法上传图片');
        }
      },
      cropImage() {
        return this.$refs[this.refName]
          .getCroppedCanvas({
            height: this.outputHeight,
            width: this.outputWidth,
            fillColor: '#fff'
          })
          .toDataURL(`image/${this.outputType}`);
      },
      doUpload() {
        let cropImg = this.cropImage();
        const url = '/Admin/Public/upload_image';
        let postData = {
          image_data: cropImg,
          _type: 'platform'
        };
        if (this.userId) {
          postData.userid = this.userId;
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.status === 1) {
              this.cropImg = cropImg;
              this.showCropperModal = false
              let path = `${res.data.path}${this.compress ? '@70q_1pr' : ''}`;
              this.$emit('input', path);
              this.$emit('on-success', { type: res.data.type });
              this.$emit('noshowpic', false);
            } else {
              this.$Message.error(res.data.info);
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      selectPic(picaddr) {
        this.cropImg = picaddr;
        this.imgSrc = '';
        this.$emit('input', picaddr);
      }
    }
  };
</script>
<style lang="less" scoped>
  @btn-color: #19be6b;
  .des {
    font-size: 12px;
    color: #7D7D7D;
  }
  .upload-btn {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 167px;
    height: 103px;
    background: #F6F6F8;
    border: 1px dashed #CCCCCC;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    color: #313131;
    text-align: center;
    cursor: pointer;
    .add-img {
      width: 28px;
      height: 28px;
      display: block;
      margin-bottom: 10px;
    }
    &:hover .hover-div{
      display: block;
    }
  }
.hover-div {
  position: absolute;
  left: 0;
  bottom: 0;
  display: none;
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  text-align: center;
  color: #fff;
}
.upload-image {
  max-height: 100%;
  max-width: 100%;
}
  .cropper {
    #preview {
      width: 300px;
      height: 300px;
      overflow: hidden;
      position: absolute;
      right: -320px;
      border: 1px solid #dcdcdc;
      top: 0;
      background-color: #ccc;
    }
    .button {
      display: flex;
      align-items: center;
      
    }
    .image-wrapper {
      display: flex;
      justify-content: center;
      // width: 500px;
      height: 300px;
      border: 2px solid #dcdcdc;
      margin-bottom: 30px;
      position: relative;
      
    }
  }
</style>
