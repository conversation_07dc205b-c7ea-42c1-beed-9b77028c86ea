<template>
  <RightBox>
    <div slot="title">连续包月服务</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="背景设置：">
        <RadioGroup v-model="postData.background_type">
          <Radio label="1">默认背景</Radio>
          <Radio label="2">自定义</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem  v-show="postData.background_type == 2">
        <div class="tips">
          建议图片尺寸 710*180px 推荐大小：100kb
        </div>
         <upload-img-item des="尺寸：710*180" :options="{aspectRatio: 71/18}" class="mb10" v-model="postData.background_img"></upload-img-item>
      </FormItem>
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyPayscore',
  components: {
    RightBox,
    UploadImgItem,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
