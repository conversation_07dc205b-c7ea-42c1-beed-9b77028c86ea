<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">活动时间</Checkbox>
          <Checkbox label="2">报名截止时间</Checkbox>
          <Checkbox label="3">已报名人数</Checkbox>
          <Checkbox label="4">可报名人数</Checkbox>
        </Checkbox-group>
      </FormItem>
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="推荐活动：">
        <RadioGroup v-model="postData.recommend_type">
          <Radio label="1">按创建时间倒序，置顶活动优先展示</Radio>
          <Radio label="2">自定义添加</Radio>
        </RadioGroup>
      </FormItem>
      <div v-show="postData.recommend_type == 2">
        <div class="tips">建议选择的活动不超过3个</div>
        <ActivityItem
        v-model="postData.recommend_activity_detail" :selectBusId="selectBusId" />
      </div>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import ActivityItem from './ActivityItem'
import StyleSet from './StyleSet'
import { mapGetters, mapState } from 'vuex'
export default {
  name: 'DiyActivityInfo',
  components: {
    RightBox,
    StyleSet,
    ActivityItem,
  },
  props: {
    title: {
      type: String,
      default: '场馆介绍',
    },
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM', {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  watch: {
    'postData.recommend_activity_detail'(val) {
      const ids = val.map(item => item.id)
      if(JSON.stringify(ids) === JSON.stringify(this.postData.recommend_activity)) {
        return
      }
      this.postData.recommend_activity = ids
    },
    'postData.bus_id'() {
      this.postData.recommend_activity_detail = []
    }
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/activity-style-2.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/activity-style-1.png',
          name: '纵向',
          type: '2',
        },
      ],
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
