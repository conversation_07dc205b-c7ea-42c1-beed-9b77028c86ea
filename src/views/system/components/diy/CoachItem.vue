<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      add-tips="选择教练"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <CoachTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import CoachTable from './CoachTable'
export default {
  name: 'CoachItem',
  components: {
    CoachTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {},
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
