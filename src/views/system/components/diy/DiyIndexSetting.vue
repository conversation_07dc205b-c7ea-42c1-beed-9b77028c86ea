<template>
  <RightBox>
    <div slot="title">首页设置</div>
    <Form
      :model="postData"
      :label-width="120"
    >
      <FormItem label="顶部快捷导航：">
        <Checkbox v-model="postData.enter_voucher" true-value="1" false-value="0">入场凭证</Checkbox>
        <Checkbox v-model="postData.toggle_bus" true-value="1" false-value="0">切换场馆</Checkbox>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyIndexSetting',
  components: {
    RightBox,
  },
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.setting
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_THEME_BY_KEY',  {
          value: val,
          type: 2,
          key: 'setting'
        })
      },
    },
  },
  data() {
    return {
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
