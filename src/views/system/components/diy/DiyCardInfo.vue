<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">有效期</Checkbox>
          <Checkbox label="2">赠送</Checkbox>
        </Checkbox-group>
      </FormItem>
      <!-- <FormItem label="卡课样式：">
        <RadioGroup v-model="postData.card_type">
          <Radio label="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png">
            <img class="card-min" src="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png" />
          </Radio>
          <Radio label="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-2.png">
            <img class="card-min" src="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-2.png" />
          </Radio>
          <Radio label="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-3.png">
            <img class="card-min" src="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-3.png" />
          </Radio>
          <Radio label="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-4.png">
            <img class="card-min" src="https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-4.png" />
          </Radio>
        </RadioGroup>
      </FormItem> -->
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250" @on-change="busIdChange">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="推荐卡课：">
        <RadioGroup v-model="postData.recommend_type">
          <Radio :label="'1'">按创建时间倒序</Radio>
          <Radio :label="'2'">自定义添加</Radio>
        </RadioGroup>
      </FormItem>
      <div v-show="+postData.recommend_type == 2">
        <div class="tips ">建议添加的卡课不超过3张</div>
        <CardItem v-model="postData.recommend_card_detail" :src="postData.card_type" :selectBusId="selectBusId" />
      </div>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import CardItem from './CardItem'
import StyleSet from './StyleSet'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyCardInfo',
  components: {
    RightBox,
    StyleSet,
    CardItem,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-style-2.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-style-1.png',
          name: '横向',
          type: '2',
        },
      ],
    }
  },

  watch: {
    // 注释原因:  引发23892BUG 因此注释
    // 'postData.bus_id'() {
    //   this.postData.recommend_card_detail = []
    // }
  },

  methods: {
    busIdChange() {
      this.postData.recommend_card_detail = []
    }
  },
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
.card-min {
  width: 138px;
  height: 80px;
}
</style>
