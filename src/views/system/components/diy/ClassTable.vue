<template>
  <Modal
    v-model="showAdd"
    title="课程选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.class_name"
          @on-enter="getList"
          placeholder="课程名称"
        ></Input>
        <Select
          style="width: 120px"
          v-model="searchData.class_type"
          placeholder="课种"
          clearable
        >
          <Option value="1">团课</Option>
          <Option value="2">操课</Option>
        </Select>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          class="avatar-zoom"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.p"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import Selection from 'mixins/selection'
export default {
  name: 'ClassTable',
  mixins: [Selection],
  data() {
    return {
      pageTotal: 0,
      searchData: {
        class_name: '',
        class_type: '',
        p: 1,
        page_size: 10
      },
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60
        },
        {
          title: '名称',
          key: 'class_name'
        },
        {
          title: '类型',
          key: 'is_free',
          render: (h, param) => {
            return h('span', param.row.is_free == 0 ? '团课' : '操课')
          }
        },
        {
          title: '单节时长',
          key: 'class_hour',
          render: (h, param) => {
            return h('span', param.row.class_hour + '分钟')
          }
        },
        {
          title: '最少开课人数',
          key: 'min_number'
        },
        {
          title: '最大报名人数',
          key: 'reserve_number'
        }
      ]
    }
  },
  props: {
    value: {
      type: Boolean
    },
    selectBusId: String || Number
  },

  computed: {
    ...mapState(['busId']),
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {},
  components: {},
  created() {
    this.getList()
  },
  methods: {
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.p = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      let obj={}
      if(this.selectBusId || this.busId) {
        obj.region_bus = `${this.selectBusId || this.busId}_2`
      }
      let postData = Object.assign({...this.searchData,...obj})
      this.$service
        .post('/Web/OpenClass/get_open_class_list', postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            const list = data.list
            this.tableData = list.map(item => {
              return {
                ...item,
                _checked: this.selectionId.includes(item.id)
              }
            })
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
