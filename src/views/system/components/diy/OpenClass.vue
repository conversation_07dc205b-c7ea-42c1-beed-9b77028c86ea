<template>
  <div class="pt-list-wrap">
    <DateSwitch />
    <div
      v-if="filterList && filterList.length"
      class="private-list"
      scroll-y="true"
    >
      <div
        class="course theme-bg"
        :style="{'backgroundImage':config.background_type==2?`url(${config.background})`:'',backgroundSize:'cover'}"
        v-for="item in filterList"
        :class="config.type==2?'style2':''"
        :key="item.id"
      >
        <div
          v-if="item.seats_num"
          class="lef-tag"
        >
          座位 {{item.seats_num}}</div>
        <div
          v-else-if="item.teamclass_type == 1"
          class="lef-tag"
        >
          限班级成员参加
        </div>
        <div
          v-else-if="item.min_number && arrIndexOf(config.show_content, '4')"
          class="lef-tag"
        >
          满{{item.min_number}}人开课
        </div>
        <div class="left">
          <img
            class="coach-avatar"
            :src="item.avatar"
            v-if="config.type==2"
          />
          <div
            class="time"
            v-if="config.type==1"
          >
            <span>{{item.begin_time}}</span>
            <div class="time-line"></div>
            <span>{{item.end_time}}</span>
          </div>
          <div class="coach-info">
            <div class="name">
              <span :style="item.class_level? 'maxWidth:210rpx':'maxWidth:320rpx'">{{item.class_name}}</span>
              <div
                class="star-wrap"
                v-if="item.class_level && arrIndexOf(config.show_content, '5')"
              >
                <Rate
                  class="rate"
                  :disabled="true"
                  :value="item.class_level"
                  :count="item.class_level"
                  :size="12"
                />
              </div>
            </div>
            <div
              class="item coach"
              v-if="arrIndexOf(config.show_content, '1')"
            >
              <span
                v-if="config.type==2"
                class="time-bg"
              >{{item.begin_time}}-{{item.end_time}}</span>
              <ThemeIcon
                v-if="config.type==1"
                class="icon-mr"
                type="t-icon-jiaolianrenwu"
              />
              <span>{{item.coach_name}}</span>
            </div>
            <div class="item">
              <ThemeIcon
                v-if="arrIndexOf(config.show_content, '2')"
                class="icon-mr"
                type="t-icon-jiaoshi1"
              />
              <span v-if="arrIndexOf(config.show_content, '2')">{{item.classroom_name || '无'}}</span>
              <span
                class="cut-up"
                v-if="arrIndexOf(config.show_content, '2') && arrIndexOf(config.show_content, '3')"
              >|</span>
              
              <span v-if="arrIndexOf(config.show_content, '3') && config.remaining_seats == 1">空余<span class="theme-color-other ">{{item.surplus}}</span>位</span>
              <span v-if="arrIndexOf(config.show_content, '3') && config.remaining_seats == 2">当前预约 <span class="theme-color-other ">{{item.total_num}}</span>/{{item.reserve_number}}</span>
            </div>
          </div>
        </div>
        <div
          class="reserve"
          v-if="item.class_category == 0"
        >
          <div
            v-if="item.checkmark == 0 && item.can_mark_time"
            class="time-limit theme-color-other"
          >
            {{item.can_mark_time}}可约
          </div>
          <div
            v-if="item.checkmark == 0 && !item.can_mark_time"
            class="reserve-btn"
          >预约</div>
          <div
            v-if="item.checkmark == 1"
            class="reserve-btn waiting-circle"
          >已约</div>
          <div
            v-if="item.checkmark == 2"
            class="reserve-btn disabled"
          >已完成</div>
          <div
            v-if="item.checkmark == 3 && item.waitting_enable_status != 1"
            class="reserve-btn disabled"
          >已满</div>
          <div
            v-if="item.checkmark == 3 && item.waitting_enable_status == 1"
            class="reserve-btn circle"
          >候补</div>
          <div
            v-if="item.checkmark == 5||item.checkmark==8||item.checkmark==9"
            class="reserve-btn disabled"
          >{{item.checkmark==8?'已开始':item.checkmark==9?'已结束':'预约中'}}</div>
          <div
            v-if="item.checkmark == 6"
            class="reserve-btn waiting"
          >候补中</div>
          <div
            v-if="item.checkmark == 7"
            class="reserve-btn waiting"
          >待确认</div>
        </div>
        <div
          class="theme-color-other"
          v-else
        >
          无需预约
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import DateSwitch from './DateSwitch'
import ThemeIcon from './ThemeIcon';
export default {
  name: 'OpenClass',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  components: {
    DateSwitch,
    ThemeIcon
  },
  data() {
    return {
      privateList: [],
    }
  },
  computed: {
    filterList() {
      return this.privateList.filter((item) => {
        if((item.checkmark === 8 && this.arrIndexOf(this.config.show_content, '6')) || (item.checkmark === 9 && this.arrIndexOf(this.config.show_content, '7'))) {
          return true
        } else if (item.checkmark !== 8 && item.checkmark !== 9) {
          return true
        }
        return false
      })
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_schedule_mock_data', {
          type: 1,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.privateList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
}
</script>

<style lang="less" scoped>
</style>