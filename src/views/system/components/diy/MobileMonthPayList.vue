<template>
  <div class="monthpay-list theme-bg">
    <div class="item" v-for="(item, index) in cardList" :key="index" @click="handleDetail(item.id)">
        <div class="album">
          <img class="photo" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-card-bg.png" />
          <div class="card-type" > {{ getCardTypeName(item) }}</div>
        </div>
        <div class="info">
          <div class="title">{{ item.name || item.card_name }}</div>
          <div class="desc">期限1个月</div>
        </div>
        <div class="price">
          <div class="amount">¥0.01</div>
        </div>
      </div>
    <div v-if="!cardList.length" class="nodata">暂无数据</div>
  </div>
</template>

<script>

export default {
  name: 'MobileCardList',
  props: {
    type: [Number, String],
    config: Object,
  },
  data() {
    return {
      cardList: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_card_class_mock_data', {
          type: this.type,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.cardList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getCardTypeName(info) {
      const typeName =
        info.card_type_id == 1
          ? '期限卡'
          : info.card_type_id == 2
          ? '次卡'
          : info.card_type_id == 3
          ? '储值卡'
          : info.card_type_id == 4
          ? '私教'
          : info.card_type_id == 5 ? '泳教':'健身卡'
      const timeName = info.is_pt_time_limit_card ? '包月' : ''
      return typeName + timeName
    },
  }
}
</script>
<style lang="less" scoped>
@import '~@/styles/themeVar.less';

.monthpay-list {
    padding: 0 30px 30px;
    .item {
      padding: 30px;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 10px;
      margin-bottom: 20px;
      display: flex;
      flex-direction: row;

      .album {
        position: relative;
        width: 161px;
        height: 94px;

        .photo {
          width: 161px;
          height: 94px;
        }

        .card-type {
          font-size: 20px;
          font-weight: 800;
          color: #795733;
          position: absolute;
          margin-top: -60px;
          margin-left: 13px;
        }
      }

      .info {
        margin-left: 20px;

        .title {
          font-size: 26px;
          font-weight: bold;
          color: #000000;
          width: 333px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 8px;
        }

        .desc {
          font-size: 24px;
          font-weight: 400;
          color: #7d7d7d;
          margin-top: 10px;
        }
      }

      .price {
        margin-left: auto;
        display: flex;
        justify-content: center;
        align-items: center;

        .amount {
          font-size: 36px;
          font-weight: bold;
          color: @theme-text-color-other;
        }
      }
    }
  }
</style>