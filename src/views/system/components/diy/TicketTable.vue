<template>
  <Modal
    v-model="showAdd"
    title="选择票务"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.card_name"
          @on-enter="getList"
          placeholder="票名称"
        ></Input>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          class="avatar-zoom"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import Selection from 'mixins/selection'
export default {
  name: 'TicketTable',
  mixins: [Selection],
  data() {
    return {
      unitList: ['小时', '分钟'],
      pageTotal: 0,
      searchData: {
        card_name: '',
        is_show_member: 1,
        page_no: 1,
        page_size: 10
      },
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60
        },
        {
          title: '票名称',
          key: 'card_name',
        },
        {
          title: '基础时长',
          key: 'base_duration',
          render: (h, params) => {
            return h('div', params.row.base_duration + ' ' + (params.row.duration_unit == 1 ? '小时' : '分钟'))
          },
        },
        {
          title: '费用',
          key: 'base_fee',
          render: (h, params) => {
            return h('div', params.row.base_fee + ' 元')
          },
        },
        {
          title: '核销有效期',
          key: 'max_valid_time',
          render: (h, params) => {
            return h('div', params.row.max_valid_time + ' 天')
          },
        },
        {
          title: '适用场地',
          key: 'can_use_space_ids_copy',
          render: (h, params) => {
            if (Array.isArray(params.row.can_use_space_ids_copy) && params.row.can_use_space_ids_copy.length > 0) {
              const names = params.row.can_use_space_ids_copy.join(',')
              let sub_names = ''
              if (names.length > 10) {
                sub_names = names.substring(0, 10) + '...'
                return (
                  <Tooltip max-width="300" content={names} transfer>
                    {sub_names}
                  </Tooltip>
                )
              } else {
                return (<span>{names}</span>)
              }

            } else {
              return (<span>入场</span>)
            }
          }
        }
      ]
    }
  },
  props: {
    value: {
      type: Boolean
    },
    selectBusId: String || Number
  },

  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {},
  components: {},
  created() {
    this.getList()
  },
  methods: {
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/SanRule/getList', { ...this.searchData, bus_id: this.selectBusId })
        .then(res => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            const list = data.list
            this.tableData = list.map(item => {
              return {
                ...item,
                _checked: this.selectionId.includes(item.id)
              }
            })
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
