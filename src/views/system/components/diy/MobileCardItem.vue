<template>
  <div class="card-item">
    <div class="lef">
      <div class="img-view">
        <img class="card-img" :src="item.thumb" />
      </div>
      <div class="lef-rig">
        <div class="tit">
          <span v-if="isShowTags && (item.card_type_id == 4 || item.card_type_id == 5) && item.card_group_title"
            >[{{ item.card_group_title }}]</span
          >
          {{ item.name || item.card_name }}
        </div>
          <div v-if="item.card_type_id != 6" class="des">
            {{ getCardTypeName(item) }}
            <span>| {{ item.universal_card == 1 ? '多店' : '单店' }}</span>
            <span> | {{ item.end_time ? '有效期' + item.end_time + '天' : '永久有效' }}</span>
          </div>
          <div v-if="item.card_type_id == 4 || item.card_type_id == 5" class="item">
            时长: {{ item.class_duration }}分钟
          </div>
          <div v-if="item.card_type_id != 4 && item.card_type_id != 5" class="bot">
            <span class="price">{{
              item.current_price ? `￥${item.current_price}` : '价格面议'
            }}</span>
            <span v-if="item.card_type_id != 6" class="tag">
              {{ item.card_type_id == 1 ? item.end_time : item.number }}
              {{ getCardTypeUnit(item.card_type_id) }}
              <span v-if="item.gift_number">
                +赠送{{ item.gift_number }}{{ getCardTypeUnit(item.card_type_id) }}
              </span>
            </span>
          </div>
      </div>
    </div>
      <div v-if="item.card_type_id == 4 || item.card_type_id == 5" class="rig">
        <div v-if="!item.is_pt_time_limit_card && item.single_price" class="price">
          <span>￥{{ item.single_price }}/</span> <span class="unit">节</span>
        </div>
        <div v-else-if="item.is_pt_time_limit_card && item.current_price" class="price">
          <span>￥{{ item.current_price }}</span>
        </div>
        <div v-else class="price">价格面议</div>
        <div v-if="item.is_pt_time_limit_card && item.mc_gift_number" class="tag">
          赠送{{ item.mc_gift_number }}天
        </div>
      </div>
  </div>
</template>

<script>

export default {
  name: 'MobileCardItem',
  props: {
    item: Object,
    isShowTags: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
    }
  },
  created() {},
  methods: {
    getCardTypeName(info) {
      const typeName =
        info.card_type_id == 1
          ? '期限卡'
          : info.card_type_id == 2
          ? '次卡'
          : info.card_type_id == 3
          ? '储值卡'
          : info.card_type_id == 4
          ? '私教'
          : '泳教'
      const timeName = info.is_pt_time_limit_card ? '包月' : ''
      return typeName + timeName
    },
    getCardTypeUnit(card_type_id) {
      return card_type_id == 1 ? '天' : card_type_id == 2 ? '次' : card_type_id == 3 ? '元' : '节'
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.card-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 30px 20px;
  border: 1px solid var(--THEME-COLOR);
  border-radius: 20px;
  .bot {
    display: flex;
    align-items: center;
    .tag {
      margin-left: 10px;
    }
  }
  .lef {
    display: flex;
    overflow: hidden;
    align-items: flex-start;
    flex: 1;
    .lef-rig {
      flex: 1;
      min-width: 0;
    }
  }
  .tag {
    padding: 0 14px;
    border: 1px solid @theme-text-color-other;
    border-radius: 14px;
    height: 29px;
    line-height: 29px;
    text-align: center;
    font-size: 20px;
    color: @theme-text-color-other;
  }
  .rig {
    .tag {
      margin-top: 12px;
    }
  }
  .tit {
    overflow: hidden;
    margin-bottom: 13px;
    text-overflow: ellipsis;
    font-weight: bold;
    font-size: 30px;
    white-space: nowrap;
  }
  .des {
    margin-bottom: 13px;
    font-weight: 400;
    font-size: 24px;
    color: @theme-text-color-grey;
  }
  .price {
    font-weight: bold;
    font-size: 30px;
    color: @theme-text-color-other;
    .unit {
      font-weight: normal;
      font-size: 26px;
    }
  }
  .img-view {
    flex-shrink: 0;
    margin-right: 20px;
    width: 161px;
    .pic-bot {
      font-size: 20px;
      margin-top: 15px;
      color: @theme-text-color-other;
    }
    .card-img {
      width: 161px;
      height: 94px;
    }
  }
}
</style>