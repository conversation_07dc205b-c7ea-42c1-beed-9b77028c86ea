<template>
  <RightBox>
    <div slot="title">{{ postData.name }}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <div class="diyFunctionInfo">
      <div
        @click="handleAddTemplate"
        class="group-add"
      >
        <Icon type="md-add" color="#47CB89" />
        <div class="add-after">添加模块</div>
      </div>
      <div class="des">模块排版样式支持自定义，拖动模块可排序</div>
      <Draggable
        tag="ul"
        class="list-group"
        v-model="postData.icon_content"
        v-bind="dragOptions"
        @start="drag = true"
        @end="drag = false"
      >
        <transition-group type="transition">
          <!-- key为index时候删除元素造成后续元素渲染错误 为link时会造成组件重新渲染-->
          <li
            class="list-group-item"
            v-for="(item, key) in postData.icon_content"
            :key="item.uniqueId"
          >
            <img
              src="~assets/img/yuan_03.png"
              class="list-group-close-item"
              @click="handleDelete(key)"
            />
            <DiyFunctionItem v-model="postData.icon_content[key]" />
          </li>
        </transition-group>
      </Draggable>
      <TemplateSelectFunction
        @on-confirm="confirmAddTemplate"
        v-model="showTemplateSelect"
      />
    </div>
  </RightBox>
</template>

<script>
import { mapState } from 'vuex'
import StyleSet from './StyleSet'
import RightBox from './RightBox'
import Draggable from 'vuedraggable'
import DiyFunctionItem from './DiyFunctionItem'
import TemplateSelectFunction from './TemplateSelectFunction'
export default {
  name: 'DiyFunctionInfo',
  components: {
    RightBox,
    Draggable,
    StyleSet,
    DiyFunctionItem,
    TemplateSelectFunction
  },
  computed: {
    ...mapState('diy', ['theme2', 'indexLeftSelected2']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        if (val && val.icon_content) {
          val.icon_content = val.icon_content.map(icon => ({
            ...icon,
            uniqueId: icon.uniqueId || this.getKeyId()
          }));
        }
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM', {
          value: val,
          type: 2
        })
      }
    },
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  data() {
    return {
      showTemplateSelect: false,
      drag: false,
      styleList:[
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/f-style-1.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/f-style-2.png',
          name: '样式一',
          type: '2',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/f-style-3.png',
          name: '样式二',
          type: '3',
        },
      ],
      keyId: 0
    }
  },
  methods: {
    getKeyId() {
      return URL.createObjectURL(new Blob()).slice(-36)
    },
    // 打开添加模块的他创
    handleAddTemplate() {
      this.showTemplateSelect = true
    },
    // 添加模块的确认事件
    confirmAddTemplate(obj) {
      this.postData.icon_content.push({
        ...obj,
        uniqueId: obj.uniqueId || this.getKeyId()
      })
    },
    // 删除某个下标的模块
    handleDelete(index) {
      this.postData.icon_content.splice(index, 1)
    }
  }
}
</script>

<style lang="less" scoped>
.diyFunctionInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.des {
  width: 100%;
  text-align: left;
  font-size: 14px;
  color: #7d7d7d;
  margin-bottom: 15px;
}
.group-add {
  width: 100%;
  height: 70px;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 10px;
  box-sizing: border-box;
  background: #ffffff;
  border: 1px dashed #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
  .add-after {
    font-size: 14px;
    margin-left: 5px;
    color: #313131;
  }
}
.list-group {
  width: 100%;
}
.list-group-item {
  cursor: move;
  width: 100%;
  height: 140px;
  border-radius: 6px;
  box-sizing: border-box;
  background: #ffffff;
  border: 1px dashed #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.list-group-item + .list-group-item {
  margin-top: 10px;
}
.list-group-close-item {
  position: absolute;
  z-index: 999;
  right: -6px;
  top: -6px;
  width: auto;
  height: 16px;
  cursor: pointer;
}
</style>
