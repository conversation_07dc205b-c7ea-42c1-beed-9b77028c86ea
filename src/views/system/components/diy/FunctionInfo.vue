<template>
  <div
    class="nav-wrap"
    :class="{'style-1': config.type == 1 || !config.type, 'style-2': config.type == 2, 'style-3': config.type == 3 }"
  >
    <div
      class="nav-item"
      v-for="(item, key) in config.icon_content"
      :key="key"
    >
      <img :src="item.icon" class="nav-img" />
      <p class="bot">{{ item.name }}</p>
    </div>
    <div
      v-if="config.icon_content && config.icon_content.length == 0"
      class="nodata"
    >
      暂未设置功能区
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import memberPageIndex from 'mixins/memberPageIndex'
export default {
  name: 'FunctionInfo',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object,
    },
  },
  data() {
    return {
      // 背景颜色
      background: '#ffffff',
      fontColor: '#333444',
    }
  },
  created() {},
  methods: {},
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.nav-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  overflow: hidden;
  margin: 20px;

 
  .nav-item {
    display: flex;
    background: #fff;
    box-sizing: border-box;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 22px;
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
  .nav-img {
    width: 82px;
    height: 82px;
    margin-bottom: 20px;
  }
  .bot {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &.style-1{
    border-radius: 10px;
    background: #fff;
    .nav-item {
      min-width: 20%;
      &:nth-child(n+6) {
        flex-basis: 20%;
        flex-grow: 0;
      }
    }
  }
}
.nav-wrap.style-2 {
  .nav-item {
    position: relative;
    flex-basis: 50% !important;
    &:first-child, &:nth-child(2) {
      height: 240px;
      text-align: left;
      font-size: 30px;
      font-weight: bold;
      .nav-img {
        position: absolute;
        right: 0;
        bottom: 8px;
        width: 190px;
        height: 190px;
      }
      .bot {
        position: absolute;
        left: 20px;
        top: 10px;
        font-weight: bold;
        font-size: 30px;
        color: #000000;
        line-height: 64px;
      }
    }
    &:first-child {
      padding-left: 20px;
      border-radius: 10px 0 0 10px;
      flex-grow: 0;
      .nav-img {
        right: 20px;
      }
      &::after {
        content: '';
        position: absolute;
        top: 30px;
        right: 0;
        bottom: 0;
        width: 1px;
        height: 180px;
        background: #D9D9D9;
      }
    }
    &:nth-child(2) {
      border-radius: 0 10px 10px 0;
    }
    &:nth-child(n+3) {
      flex-grow: 0 !important;
      flex-shrink: 0 !important;
      flex-basis: calc(33.3% - 14px) !important;
      font-size: 24px;
      font-weight: bold;
      height: 210px;
      margin-right: 20px;
      margin-top: 20px;
      border-radius: 10px;
      .nav-img {
        width: 120px;
        height: 120px;
      }
    }
    &:nth-child(3n+2) {
      margin-right: 0;
    }
  }
}
.nav-wrap.style-3 {
  .nav-item {
    position: relative;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: calc(33.33% - 20px) !important;
    font-size: 26px;
    font-weight: bold;
    height: 222px;
    border-radius: 10px;
    margin-right: 20px;
    .nav-img {
      width: 130px;
      height: 130px;
    }
    &:nth-child(3) {
      margin-right: 0;
    }
    &:nth-child(n+4) {
      font-size: 20px;
      margin-right: 0;
      margin-top: 20px;
      flex-basis: 33.33% !important;
      height: 190px;
      .nav-img {
        width: 100px;
        height: 100px;
      }
    }
    &:nth-child(3n+4) {
      border-radius: 10px 0 0 10px;
    }
    &:nth-child(3n+5) {
      border-radius: 0;
    }
    &:nth-child(3n+6) {
      border-radius: 0 10px 10px 0;
    }
  }
}
</style>
