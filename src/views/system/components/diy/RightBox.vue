<template>
  <div class="diy-right">
    <div class="diy-right-tit">
      <slot name="title"></slot>
    </div>
    <div class="diy-right-main">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RightBox',
  components: {
  },
  data() {
    return {
      
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
.diy-right {
  box-sizing: border-box;
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.diy-right-tit {
  font-size: 16px;
  font-weight: bold;
  color: #313131;
  padding: 10px 0;
}
.diy-right-main {
  background: #f6f6f8;
  height: 810px;
  overflow-y: scroll;
  padding: 15px;
}
</style>