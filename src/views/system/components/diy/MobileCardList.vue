<template>
  <div class="card-list-wrap theme-bg">
    <CardTags v-if="(type == 2 || type == 3) && arrIndexOf(config.show_content, '1')" :type="type == 3 ? 3 : 1" />
    <div class="card-list">
      <div v-for="(item, index) in cardList" :key="index">
        <CardItem :item="item" :isShowTags="arrIndexOf(config.show_content, '1')"/>
      </div>
    </div>
    <div v-if="!cardList.length" class="nodata">暂无数据</div>
  </div>
</template>

<script>
import CardTags from './MobileCardTags'
import CardItem from './MobileCardItem'

export default {
  name: 'MobileCardList',
  props: {
    type: [Number, String],
    config: Object,
  },
  components: {
    CardTags,
    CardItem
  },
  data() {
    return {
      cardList: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    arrIndexOf(arr, value) {
      if (!Array.isArray(arr)) {
        return false
      }
      if (arr.indexOf(value) < 0) {
        return false
      } else {
        return true
      }
    },
    getList() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_card_class_mock_data', {
          type: this.type,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.cardList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  },
}
</script>
<style lang="less" scoped>
.card-list-wrap {
  min-height: 100%;
  box-sizing: border-box;
  padding: 30px;
}
</style>