<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">上课教练</Checkbox>
          <Checkbox label="2">上课教室/场地</Checkbox>
          <Checkbox label="3">学员数量</Checkbox>
          <Checkbox label="4">已开始课程</Checkbox>
          <Checkbox label="5">已结束课程</Checkbox>
        </Checkbox-group>
      </FormItem>
      <FormItem label="背景设置：">
        <RadioGroup v-model="postData.background_type">
          <Radio label="1">无</Radio>
          <Radio label="2">自定义</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem  v-show="postData.background_type == 2">
        <div class="tips">
          建议图片尺寸 690*190px 推荐大小：100kb
        </div>
         <upload-img-item des="尺寸：690*190" :options="{aspectRatio: 69/19}" class="mb10" v-model="postData.background"></upload-img-item>
      </FormItem>
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import RightBox from './RightBox'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyTeamClass',
  components: {
    RightBox,
    UploadImgItem,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme3','indexLeftSelected3', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme3[this.indexLeftSelected3]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 3
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
