<template>
  <MobileBox class="diy-index">
      <NavBar :show-bus-switch="setting.toggle_bus === '1'" :show-qr-code="setting.enter_voucher === '1'"  />
      <swiper class="swiper-box" ref="swiperBox" :options="swiperOptions">
        <swiper-slide :class="index==indexLeftSelected2?'slide-selected':''" v-for="(item, index) in theme2.list" :key="index">
          <component v-show="!item.isHide" :config="item" :is="componentsName[+item.temp_type]"></component>
        </swiper-slide>
      </swiper>
  </MobileBox>
</template>

<script>
import BusInfo from './BusInfo'
import CoachInfo from './CoachInfo'
import NavBar from './NavBar'
import CardInfo from './CardInfo'
import PackageInfo from './PackageInfo'
import MobileBox from './MobileBox'
import ActivityInfo from './ActivityInfo'
import IndexOpenClassInfo from './IndexOpenClassInfo'
import IndexTicketInfo from './IndexTicketInfo'
import IndexSpaceInfo from './IndexSpaceInfo'
import InvitationPictureInfo from './InvitationPictureInfo'
import PayscorePictureInfo from './PayscorePictureInfo'
import PeopleCounting from './PeopleCounting'
import PictureInfo from './PictureInfo'
import FunctionInfo from './FunctionInfo'
import ActivityRefund from './ActivityRefund'
import { mapState } from 'vuex'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'

export default {
  name: 'MobileIndex',
  props: {
    draggableList: Array
  },
  components: {
    ActivityInfo,
    BusInfo,
    CoachInfo,
    NavBar,
    CardInfo,
    PackageInfo,
    IndexTicketInfo,
    IndexSpaceInfo,
    IndexOpenClassInfo,
    InvitationPictureInfo,
    PayscorePictureInfo,
    MobileBox,
    FunctionInfo,
    PeopleCounting,
    PictureInfo,
    ActivityRefund,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      //1场馆介绍，2明星教练，3会员卡推荐，4热门活动，5图片卡，6功能区，7团课推荐，8红包，9邀请有礼，10票务，11场地推荐
      componentsName: {
        1: 'BusInfo',
        2: 'CoachInfo',
        3: 'CardInfo',
        4: 'ActivityInfo',
        5: 'PictureInfo',
        6: 'FunctionInfo',
        7: 'IndexOpenClassInfo',
        9: 'InvitationPictureInfo',
        10: 'IndexTicketInfo',
        11: 'IndexSpaceInfo',
        12: 'PackageInfo',
        13: 'PayscorePictureInfo',
        14: 'PeopleCounting',
        15: 'ActivityRefund',
      },
      selectedIndex: 0,
      swiperOptions: {
        direction: 'vertical',
        slidesPerView: 'auto',
        freeMode: true,
        autoHeight: true, // 自适应高度
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
      }
    }
  },
  watch: {
    indexLeftSelected2(val) {
      if(this.swiper &&  typeof val === 'number') {
        this.swiper.slideTo(val, 500, false)
      }
    }
  },
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2']),
    setting() {
      return this.theme2.setting || {}
    },
    swiper() {
      return this.$refs.swiperBox.$swiper
    }
  },
  created() {
  },
  methods: {
    
  }
}
</script>

<style lang="less" scoped>
.diy-index {
  margin-right: 30px;
  margin-top: 50px;
  position: relative;
}
.img-wrap {
  width: 710px;
  height: 280px;
  margin: 0 auto 20px;
  img {
    width: 100%;
    height: 100%;
  }
}
.swiper-box {
  height: 100%;
  font-size: 50px;
}
.swiper-box /deep/ .swiper-slide{
  height: auto;
}
.slide-selected{
  position: relative;
}
.slide-selected::before{
  position: absolute;
  top: 0;
  left: 0;
  content: ' ';
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 4px dashed #47CB89;
  z-index: 1;
}
</style>