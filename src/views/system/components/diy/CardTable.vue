<template>
  <Modal
    v-model="showAdd"
    title="卡课选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.card_name"
          @on-enter="getList"
          placeholder="卡课名称"
        ></Input>
        <Select
          style="width: 120px"
          v-model="searchData.card_type_id"
          placeholder="卡类型"
          clearable
        >
          <Option value="1">期限卡</Option>
          <Option value="2">次卡</Option>
          <Option value="3">储值卡</Option>
          <Option value="4">私教课</Option>
          <Option value="5">泳教课</Option>
          <!-- <Option value="6">套餐包</Option> -->
        </Select>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
export default {
  name: 'CardTable',
  data() {
    return {
      pageTotal: 0,
      searchData: {
        card_name: '',
        card_type_id: '',
        page_size: 10,
      },
      selection: [],
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
          title: '会员卡名称',
          key: 'card_name'
        },
        {
          title: '卡类型',
          key: 'cardTypeName',
        },
        {
          title: '售价',
          key: 'cardPrice',
        }
      ],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    selectBusId: String || Number
  },
  computed: {
    selectionId() {
      return this.selection.map(item => item.card_id);
    },
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
  },
  components: {},
  created() {
    this.getList()
  },
  methods: {
     onSelectionChange(selection) {
      if (selection.length === 0) {
        this.tableData.forEach(item => this.onSelectCancel(selection, item));
      }
    },
    onSelectCancel(selection, row) {
      const index = this.selection.findIndex(item => item.card_id === row.card_id);
      this.selection.splice(index, 1);
    },
    onSelect(selection, row) {
      if (!this.selectionId.includes(row.card_id)) {
        this.selection.push(row);
      }
    },
    onSelectAll(selection) {
      selection.forEach(item => this.onSelect(selection, item));
    },
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection,
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_cards_all', { ...this.searchData, bus_id: this.selectBusId })
        .then((res) => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            const list = data.list
            this.tableData = list.map((item) => {
              return {
                ...item,
                _checked: this.selectionId.includes(item.id),
                cardTypeName: `${item.universal_card == 1 ? '多店通用' : ''}${item.experience_card == 1 ? '体验卡' : ''}${item.card_type_id == 1 ? '期限卡' : item.card_type_id == 2 ? '次卡' : item.card_type_id == 3 ? '储值卡' : item.is_pt_time_limit_card == 1? '私教包月' : item.card_type_id == 4 ?  '私教课':'泳教课'}`,
                cardPrice: `${item.is_pt_time_limit_card != 1 && (item.card_type_id == 4 || item.card_type_id == 5 || item.experience_card == 1) ? '-' : item.current_price == 0?'价格面议':item.current_price}`
              }
            })
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
}
</script>

