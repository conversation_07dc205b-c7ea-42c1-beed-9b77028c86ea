<template>
  <PageBox :type="2">
    <template slot-scope="slotProps">
      <!-- 左侧菜单 -->
      <diy-left canAdd canDelete :type="2" :data="slotProps.init.list"></diy-left>
      <!-- 中间模块 -->
      <mobile-index></mobile-index>
      <div class="nodata nodata-min" v-if="currentItem && currentItem.isHide">
        该模块已隐藏，可点击左侧模块显示按钮显示
      </div>
      <DiyIndexSetting v-show="indexLeftSelected2 === 'setting'" :type="2" />
      <!-- 右侧添加区 -->
      <component
        v-show="!(currentItem && currentItem.isHide)"
        :type="2"
        :is="getComponentName()"
        :title="currentItem ? currentItem.name : ''"
      ></component>
    </template>
  </PageBox>
</template>

<script>
import PageBox from './PageBox'
import DiyLeft from './DiyLeft.vue'
import DiyIndexSetting from './DiyIndexSetting'
import DiyBusInfo from './DiyBusInfo'
import DiyCoachInfo from './DiyCoachInfo'
import DiyCardInfo from './DiyCardInfo'
import DiyPackageInfo from './DiyPackageInfo'
import DiyActivityInfo from './DiyActivityInfo'
import DiyPictureInfo from './DiyPictureInfo'
import DiyActivityRefund from './DiyActivityRefund'
import DiyFunctionInfo from './DiyFunctionInfo'
import DiyInvitation from './DiyInvitation'
import DiyPayscore from './DiyPayscore.vue'
import DiyIndexOpenClass from './DiyIndexOpenClass'
import DiyIndexTickt from './DiyIndexTickt'
import DiyIndexSpace from './DiyIndexSpace'
import MobileIndex from './MobileIndex.vue'
import { mapState } from 'vuex'
export default {
  name: 'DiyPageIndex',
  components: {
    PageBox,
    DiyLeft,
    DiyBusInfo,
    DiyCoachInfo,
    DiyIndexSetting,
    DiyCardInfo,
    DiyActivityInfo,
    DiyPictureInfo,
    DiyIndexOpenClass,
    DiyIndexTickt,
    DiyIndexSpace,
    DiyFunctionInfo,
    DiyInvitation,
    DiyPayscore,
    MobileIndex,
    DiyPackageInfo,
    DiyActivityRefund,
  },
  computed: {
    ...mapState('diy', ['theme2', 'indexLeftSelected2']),
    currentItem() {
      return this.theme2.list[this.indexLeftSelected2]
    }
  },
  watch: {},
  data() {
    return {
      // 组件名数组
      originalComponentsName: [
        { tempType: 1, name: 'DiyBusInfo' },
        { tempType: 2, name: 'DiyCoachInfo' },
        { tempType: 3, name: 'DiyCardInfo' },
        { tempType: 4, name: 'DiyActivityInfo' },
        { tempType: 5, name: 'DiyPictureInfo' },
        { tempType: 6, name: 'DiyFunctionInfo' },
        { tempType: 7, name: 'DiyIndexOpenClass' },
        { tempType: 9, name: 'DiyInvitation' },
        { tempType: 10, name: 'DiyIndexTickt' },
        { tempType: 11, name: 'DiyIndexSpace' },
        { tempType: 12, name: 'DiyPackageInfo' },
        { tempType: 13, name: 'DiyPayscore' },
        { tempType: 15, name: 'DiyActivityRefund' },
      ],
      list: [],
    }
  },
  created() {},
  methods: {
    getComponentName() {
      if(this.indexLeftSelected2 === 'setting') {
        return undefined
      }
      const nameItem = this.originalComponentsName.find(item => +item.tempType === +this.currentItem.temp_type)
      return nameItem ? nameItem.name : undefined
    },
  },
}
</script>
<style lang="less" scoped>

</style>
