<template>
   <div v-if="info && info.type" class="space-item" @tap="goDetail(info)">
    <img
      class="category-bg"
      :src="info.pic_url[0]||'https://imagecdn.rocketbird.cn/minprogram/member/image/category-1.png'"
    />
    <div class="category-pin">
      {{ info.name }}
    </div>
    <div class="category-tag">
      <span v-if="info.type.includes('1')" class="tag">订场</span>
      <span v-if="info.type.includes('2')" class="tag">散场</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileSpaceItem',
  data() {
    return {
    }
  },
  props: {
    info: {
      type: Object
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.space-item {
  width: 690px;
  height: 260px;
  border-radius: 20px;
  margin: 30px auto 0;
  position: relative;

  .category-tag {
    position: absolute;
    right: 28px;
    top: 20px;

    .tag {
      display: inline-block;
      line-height: 30px;
      text-align: center;
      width: 70px;
      height: 30px;
      background: var(--THEME-COLOR);
      font-size: 20px;
      font-weight: bold;
      color: #000;
      margin-left: 10px;
    }
  }

  .category-bg {
    width: 690px;
    height: 260px;
    border-radius: 20px;
  }

  .category-pin {
    width: 690px;
    height: 70px;
    line-height: 70px;
    background: rgba(0, 0, 0, 0.5);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 30px;
    font-weight: bold;
    color: #ffffff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-indent: 25px;
  }
}
</style>