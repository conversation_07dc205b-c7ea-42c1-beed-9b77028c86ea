<template>
  <div class="diy-box-main">
    <div class="diy-btn-wrap">
    <!-- 判断并且当前是商家 并且不是 9运营模式 -->
      <Select
        v-if="IS_BRAND_SITE && ![9].includes(type)"
        :class="[selectClass, [4, 6, 7, 10].includes(type) ? 'position-select': null ]"
        :value="selectMerBusId"
        filterable
        @on-change="handleChangeSelect">
        <Option
          v-for="(item) in selectOptions"
          :key="item.bus_id"
          :value="item.bus_id"
          :label="item.bus_name + (item.isMerchant ? '【商家】' : '')">
        </Option>
      </Select>
      <slot name="save">
        <Button type="success" @click="handleSave">保存</Button>
      </slot>
    </div>
    <div class="diy-main">
        <slot v-bind:init="initData"></slot>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { mapState, mapGetters, mapMutations } from 'vuex'
import EventBus from 'components/EventBus.js';
import { isEqual } from 'lodash-es'
export default {
  name: 'PageBox',
  props: {
    //1外观颜色 2首页装修 3预约页面装修 4我的页面装修 5底部导航 8卡课列表 9运营模式
    type: {
      type: Number,
      default: 1
    },
    value: {
      type: [Array, Object]
    },
  },

  inject: ['IS_BRAND_SITE'],

  data () {
    return {
      initData: [],
      options: [],
    }
  },

  computed: {
    ...mapState(['busId', 'busIdEncode', 'merchantId', 'merchantName']),
    ...mapState('diy', ['selectMerBusId', 'selectMerBusIdEncode', 'firstBusIdEncode', 'merBusList']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
    selectClass() {
      return [2, 3, 4, 6, 7, 8, 10].includes(this.type) ? 'w300' : 'w419'
    },
    selectOptions() {
      return this.IS_BRAND_SITE && this.isMerchantMode
        ? [
          {
            bus_id: 'm' + this.merchantId,
            bus_name: this.merchantName,
            merchants_id: this.merchantId,
            encode_bus_id: this.firstBusIdEncode,
            isMerchant: true,
          },
          ...this.merBusList
        ]
        : this.merBusList
    }
  },

  created() {
    // 门店端获取运营模式
    if (!this.IS_BRAND_SITE && this.type === 1) {
      this.getSet(9, true)
    }

    this.getSet()
    // 防止tab页多次注册
    EventBus.$off("on-diy-tab-change")
    EventBus.$on("on-diy-tab-change", info => {
      if(this.type === info.type) {
        this.equalData(info)
      } else {
        info.success()
      }
    })
  },
  methods: {
    ...mapMutations('diy', ['SET_SELECT_MER_BUS_ID']),
    equalData(info) {
      // 处理比较数据不存在情况
      if (
        !this.$store.state.diy[`theme${info.type}`] ||
        !this.initData
      ) {
        info.success()
        return
      }

      let bool = isEqual(
        this.$store.state.diy[`theme${info.type}`],
        this.initData
      )
      if (!bool) {
        this.$Modal.confirm({
          title: '提示',
          content: '您有信息尚未保存，确认离开吗?',
          okText: '离开',
          onOk: () => {
            info.success()
          },
          onCancel: () => {}
        })
        return
      } else {
        info.success()
      }
    },
    handleSave() {
      let url = '';
      if (this.isMerchantMode) {
        url = '/Merchant/NewMemberApplet/getSetting';
      } else {
        url = '/Web/NewMemberTemplateSetting/get_bus_setting';
      }
      this.$service.post(url, {
        get_type: this.type,
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.saveSet()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    saveSet() {
      const statePath = 'theme'+this.type
      // 首页装修 处理发送数据
      let params = JSON.parse(JSON.stringify(this.$store.state.diy[statePath]))
      let hasActivityIdNull = false
      if(Object.prototype.toString.call(params.list) === '[object Array]' && this.type === 2) { // 首页装修
        const paramsList=params.list
        params.list.forEach((item, index) => {
          if(+item.temp_type===3 && item.recommend_card_detail){ // 会员卡推荐
            paramsList[index].recommend_card=[]
            item.recommend_card_detail.forEach(content => {
              paramsList[index].recommend_card.push(content.card_id)
            })
          } else if(+item.temp_type === 7 && item.recommend_class_detail){ // 团课推荐
            paramsList[index].recommend_class=[]
            item.recommend_class_detail.forEach(content => {
              paramsList[index].recommend_class.push(content.id)
            })
          } else if(+item.temp_type === 12 && item.recommend_package_detail){ // 热销套餐包
            paramsList[index].recommend_package=[]
            item.recommend_package_detail.forEach(content => {
              paramsList[index].recommend_package.push(content.id)
            })
          } else if(+item.temp_type === 15 && !item.activity_id){ // 门店体验活动 需要点击一下对应左侧模块才会初始化一个活动id 
            hasActivityIdNull = true;
          }
          // 防止传递给后端的字段过多 导致请求出问题
          delete paramsList[index].recommend_coach_detail
          delete paramsList[index].recommend_card_detail
          delete paramsList[index].recommend_activity_detail
          delete paramsList[index].recommend_space_detail
          delete paramsList[index].recommend_ticket_detail
          delete paramsList[index].recommend_class_detail
          delete paramsList[index].recommend_package_detail
        })
      }
      if(hasActivityIdNull) {
        this.$Message.error('请确认门店体验活动内容！');
        return
      }
      this.$service
        // .post('Web/NewMemberTemplateSetting/save_bus_setting', {
        .post('/Merchant/NewMemberApplet/saveSetting', {
          [this.type == 9 || this.isSelectMerchant ? 'm_id' : 'bus_id']:
            this.type == 9 ? this.merchantId : this.selectMerBusId.replace('m', ''),
          save_type: this.type,
          decorate_detail: JSON.stringify(params)
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.initData = JSON.parse(JSON.stringify(this.$store.state.diy[statePath]))
            if (this.type === 9) {
              // 切换模式之后，需要重置当前选中的商家/门店
              this.SET_SELECT_MER_BUS_ID({
                id: this.initData.mode === '1' ? 'm' + this.merchantId :  this.busId,
                encodeId: this.initData.mode === '1' ? this.firstBusIdEncode : this.busIdEncode
              })
              const fn = this.$listeners['save-mode']
              typeof fn === 'function' && fn(this.initData.mode)
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getSet(type, isBrand) {
      const currentType = type !== undefined ? type : this.type
      // isBrandSite 仅用于处理请求数据
      // const isBrandSite = isBrand !== undefined ? isBrand : this.IS_BRAND_SITE
      const url = this.IS_BRAND_SITE ? 'Merchant/NewMemberApplet/getSetting' : 'Web/NewMemberTemplateSetting/get_bus_setting'
      const params = {
        get_type: currentType,
      }
      if (this.IS_BRAND_SITE) {
        params[currentType == 9 || this.isSelectMerchant ? 'm_id' : 'bus_id'] =
          currentType == 9 ? this.merchantId : this.selectMerBusId.replace('m', '')
      }

      this.$service
        .post(url, params)
        .then(res => {
          if (res.data.errorcode === 0) {
            let data = res.data.data;
            // 有拖拽时 防止初始信息尚未保存提示
            if([2, 3, 8].includes(currentType)) {
              const dataList = currentType === 2 || currentType===8 ? data.list : data
              const mapData = dataList.map((item) => {
                const newItem = {
                  ...item,
                  editedName: item.name,
                  isEdit: false,
                  isHide: item.isHide==='true' || item.isHide===true ? true : false,
                  bus_id: item.bus_id || this.getDefaultBusId(res.data.bus_id),
                }
                if (currentType === 2 && newItem && newItem.icon_content) {
                  // 设置uid 防止key为index时候删除元素造成后续元素渲染错误 为link时会造成组件重新渲染
                  newItem.icon_content = newItem.icon_content.map(icon => ({
                    ...icon,
                    uniqueId: icon.uniqueId || URL.createObjectURL(new Blob()).slice(-36)
                  }));
                  newItem.type = newItem.type || '1'
                }
                return newItem
              })
              data = currentType === 2 || currentType===8 ?  {...data, list: mapData} : mapData
            } else if (currentType === 9) {
              // 如果是健身瑜伽运营模式，则重置selectId, 避免是商家id匹配不上
              if (data && data.mode === '0' && this.IS_BRAND_SITE) {
                this.SET_SELECT_MER_BUS_ID({
                  id: this.busId,
                  encodeId: this.busIdEncode
                })
              }
            } else {
              if (data && !data.bus_id) {
                // 如果获取到的值没有bus_id，则为其赋初始值
                data.bus_id = this.getDefaultBusId(res.data.bus_id)
              }
            }

            // 赋值init，仅门店端获取运营模式时不赋值（会覆盖）
            if (!(isBrand && type === 9)) {
              this.initData = JSON.parse(JSON.stringify(data))
            }
            this.$store.commit('diy/UPDATE_DIY_THEME', {value:data, type: currentType})
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getDefaultBusId(busIdFromResponse) {
      return this.IS_BRAND_SITE
        ? this.isSelectMerchant
          ? busIdFromResponse || this.firstBusIdEncode
          : busIdFromResponse || this.selectMerBusIdEncode
        : this.busIdEncode;
    },
    handleChangeSelect(val) {
      if(val !== this.selectMerBusId) {
        this.equalData({
          type: this.type,
          success: () => {
            const item = this.selectOptions.find(v => v.bus_id === val)
            this.SET_SELECT_MER_BUS_ID({
              id: val,
              encodeId: item ? item.encode_bus_id : this.firstBusIdEncode
            })
            if (item) {
              this.getSet()
            }
          }
        })
      }
    }
  },
}
</script>
<style lang="less" scoped>
.diy-box-main {
  .diy-main {
    display: flex;
    overflow: scroll;
  }
  .diy-btn-wrap {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;

    .w300 {
      width: 300px;
    }

    .w419 {
      width: 419px;
    }

    .ivu-btn {
      margin-left: auto;
    }

    .position-select {
      position: absolute;
      top: 0;
      left: -330px;
    }
  }
}

</style>


