<template>
    <div class="navbar-left">
      <div v-if="showBusSwitch || showQrCode" class="navbar-items">
        <div v-if="showQrCode" class="navbar-item" >
          <img
            class="switch-img icon-mr"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/qrcode-dark.png"
          />
          入场凭证
        </div>
        <div v-if="showBusSwitch" class="navbar-item">
          <ThemeIcon class="icon-mr" type="t-icon-mendianqiehuan-01" :size="30" color="#000" />
          门店名称
        </div>
      </div>
    </div>
</template>

<script setup>
import ThemeIcon from './ThemeIcon';

const props = defineProps({
  showBusSwitch: {
    type: Boolean,
    default: false,
  },
  showQrCode: {
    type: Boolean,
    default: false,
  },
})

</script>

<style lang="less" scoped>
.navbar-left {
  display: flex;
  align-items: center;
  position: absolute;
  top: 44px;
  left: 60px;
  z-index: 99;
}
.navbar-items {
  border-radius: 31px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  .navbar-item {
    position: relative;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    &:last-child::after {
      display: none;
    }
  }
}
.switch-img {
  width: 36px;
  height: 36px;
}
</style>
