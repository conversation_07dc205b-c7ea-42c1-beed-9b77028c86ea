<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      add-tips="选择票务"
      :img-width="104"
      :img-height="88"
      avatar-key="thumb"
      :show-img="false"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <TicketTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import TicketTable from './TicketTable'
export default {
  name: 'TicketItem',
  components: {
    TicketTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {},
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      if(this.itemList.length+info.selection.length>3) {
        this.$Message.error('选择的票务不超过3种')
        return false
      }
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
