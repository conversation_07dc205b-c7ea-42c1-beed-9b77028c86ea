<template>
  <div class="config-menus-item">
    <div class="block">
      <IconPicker
        class="picker"
        @on-change="(icon) => (info.icon = icon)"
        v-model="info.icon"
      ></IconPicker>
      <Form class="form" :label-width="60">
        <FormItem label="名称">
          <Input v-model="info.name" />
        </FormItem>
        <FormItem label="链接">
          <link-select
            v-model="info.link"
            :postData="info"
            @on-change="onLinkChange"
          ></link-select>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
import IconPicker from './icon-picker'
import LinkSelect from './LinkSelect'
import <PERSON>ropper from 'components/form/cropperPlus'
import { getQueryStringByName } from "@/utils"

export default {
  name: 'diyFunctionItem',
  props: {
    value: {
      type: Object
    },
    index: { type: Number },
    from: {},
  },
  computed: {
    info: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  components: { IconPicker, Cropper, LinkSelect },
  methods: {
    onLinkChange(info) {
      this.info.name = info.img_name
      const bus_id = getQueryStringByName(info.img_link, 'bus_id')
      if (bus_id) {
        this.info.bus_id = bus_id
      }
    },
  },
}
</script>

<style lang="less" scoped>
.ivu-input-wrapper,
.ivu-select {
  width: auto;
  min-width: 200px;
}
.ivu-form-item {
  margin: 5px 0;
}
.config-menus-item {
  display: flex;
  align-items: center;
  .icon-buttons {
    // width: 80px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  .block {
    display: flex;
    justify-content: center;
    align-items: center;
    // flex: 1;
    // background-color: #f7f9fd;
    // padding: 30px 60px;
  }
  .picker {
    flex: 0;
  }
  .form {
    // width: 350px;

    margin-left: 20px;
  }
}
@btn-color: #19be6b;
.upload-btn {
  // margin-left: 80px;
  border: 1px solid @btn-color;
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
  color: @btn-color;
  white-space: nowrap;
  &:hover {
    color: #fff;
    background-color: @btn-color;
  }
}
</style>
