<template>
  <div class="bot-wrap theme-bg" :class="config.type==2?'style2':''">
    <div class="box-tit tit-align-left">
      {{config.name || '热门活动'}}
      <img class="hot-icon" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/hot.png" />
    </div>
      <swiper v-if="config.type!=2" class="swiper-box" ref="swiperBox" :options="swiperOptions" :style="{height:!arrIndexOf(config.show_content, '1')&&!arrIndexOf(config.show_content, '2')?'530px':arrIndexOf(config.show_content, '1')&&arrIndexOf(config.show_content, '2')?'610px':'570px'}">
        <swiper-slide v-for="(item, index) in list" :key="index">
          <div class="swiper-item" :class="'swiper-item' + index">
            <img
            class="act-img"
            mode="scaleToFill"
            :src="item.thumb"
            />
            <div class="act-con">
              <div class="act-con-lef">
                <div class="name">{{item.name}}</div>
                <div class="time" v-if="arrIndexOf(config.show_content, '1')">活动时间 {{item.beg_time}}~{{item.end_time}}</div>
                <div class="time" v-if="arrIndexOf(config.show_content, '2')">报名截止时间 {{item.cutoff_time}}</div>
              </div>
              <div class="act-con-rig" v-if="arrIndexOf(config.show_content, '3')||arrIndexOf(config.show_content, '4')">
                <span v-if="arrIndexOf(config.show_content, '3')">
                  已报：
                  <span>
                    {{item.signcount}}
                  </span>
                </span>
                <span v-if="arrIndexOf(config.show_content, '3') && arrIndexOf(config.show_content, '4')">/</span>
                <span v-if="arrIndexOf(config.show_content, '4')">
                  {{item.about_number}}
                </span>
              </div>
            </div>
          </div>
        </swiper-slide>
        <div class="swiper-pagination" slot="pagination"></div>
      </swiper>
     <div v-else class="list-item" v-for="(item, index) in list" :key="index">
        <img
        class="act-img"
        mode="scaleToFill"
        :src="item.thumb"
        />
        <div class="act-con">
            <div class="name">{{item.name}}</div>
            <div class="time" v-if="arrIndexOf(config.show_content, '1')">活动时间 {{item.beg_time}}~{{item.end_time}}</div>
            <div class="time" v-if="arrIndexOf(config.show_content, '2')">报名截止时间 {{item.cutoff_time}}</div>
            <div class="time">
              <span v-if="arrIndexOf(config.show_content, '3')">
                已报人数
                <span class="theme-color-other">
                  {{item.signcount}}
                </span>
              </span>
              <span v-if="arrIndexOf(config.show_content, '3') && arrIndexOf(config.show_content, '4')">/</span>
              <span v-if="arrIndexOf(config.show_content, '4')" class="theme-color-other">
                {{item.about_number}}
              </span>
            </div>
        </div>
      </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import memberPageIndex from 'mixins/memberPageIndex'
export default {
  name: 'ActivityInfo',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  components: {
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        }
        // Some Swiper option/callback...
      }
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(3, val)
    }
  },

  created() {
    this.getInitList(3, this.config.bus_id)
  },
  computed: {
    list() {
      return this.config.recommend_type==2 ? this.config.recommend_activity_detail : this.initList
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.tit-align-left {
  justify-content: flex-start;
}
.hot-icon {
  margin-left: 10px;
  width: 62px;
  height: 22px;
}
.act-img {
  width: 670px;
  height: 387px;
}
.swiper-box {
  height: 610px;
}
.act-con {
  display: flex;
  justify-content: space-between;
  font-size: 24px;
  .name {
    font-size: 30px;
    font-weight: bold;
    margin-top: 20px;
  }
  .time {
    margin-top: 15px;
    color: @theme-text-color-grey;
  }
  .act-con-rig {
    margin-top: 20px;
    height: 41px;
    line-height: 41px;
    padding: 0 18px;
    border-radius: 21px;
    color: #535353;
    font-size: 22px;
    background: rgba(var(--THEME-RGB), 0.2);
  }
}
.list-item {
  display: flex;
  padding: 20px 0;
  align-items: flex-start;
  border-bottom: 1px solid #E8E8E8;
  &:last-child {
    border-bottom: none;
  }
  .act-con {
    display: block;
    .name {
      margin-top: 0;
    }
  }
  .act-img {
    width: 182px;
    height: 105px;
    margin-right: 23px;
  }
}
</style>
