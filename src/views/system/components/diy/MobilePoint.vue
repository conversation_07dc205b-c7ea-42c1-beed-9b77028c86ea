<template>
  <MobileBox class="mobile-my">
    <div class="my-wrap">
      <img class="bot-img" src="https://imagecdn.rocketbird.cn/mainsite-fe/diy/points-store-page.png" />
    </div>
  </MobileBox>
</template>

<script>
  import { mapState } from 'vuex'
  import MobileBox from './MobileBox'
  export default {
    name: 'MobileMy',
    props: {
      config: Object
    },
    components: {
      MobileBox
    },
    data() {
      return {
      }
    },
    watch: {},
    computed: {
     ...mapState('diy', ['theme4'])
    },
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped>
.mobile-my {
  margin-right: 30px;
  position: relative;
}
.top-img {
  width: 750px;
  height: 483px;
}
.bot-wrap {
  margin: 30px 20px 20px;
  padding: 20px;
  border-radius: 10px;
  background: #fff;
}
.invitation-img {
  width: 100%;
  margin-bottom: 20px;
}
</style>
