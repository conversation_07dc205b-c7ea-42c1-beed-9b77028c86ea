<template>
<PageBox :type="11">
 <MobileGuide />
 <RightBox style="margin-left: 30px;" v-show="!isMerchantMode || isSelectMerchant">
    <div slot="title">进店指引</div>
    <div class="min-tit" style="margin-bottom: 32px;">
      <div class="tips">
        <p>上传进店指引长图，引导新用户使用微信小程序时，查看购卡、核销、入场流程和注意事项。</p>
        <p>配置方式：通过功能区或者图片卡添加“进店指引”跳转链接，用户在会员端点击后即可查看指引说明。</p>
      </div>
    </div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="上传长图：">
    <div class="tips">
      建议图片宽度 750px 推荐大小：&lt; 5MB
    </div>
    <div class="up-flex-box">
      <upload-img-item :options="{aspectRatio: 0}" :maxSize="5" class="mb10" v-model="postData.image_url"></upload-img-item>
      <a class="preview-wrap" href="https://imagecdn.rocketbird.cn/mainsite-fe/diy/ex-guide.png" target="_blank">
        <img src="https://imagecdn.rocketbird.cn/mainsite-fe/guide-min.png" alt="示例图" />
        <img class="big" src="https://imagecdn.rocketbird.cn/mainsite-fe/ico-fada.png" alt="放大" />
        <p>示例图</p>
      </a>
    </div>
         
      </FormItem>
    </Form>
  </RightBox>
</PageBox>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
import MobileGuide from './MobileGuide'
import UploadImgItem from './UploadImgItem'
export default {
  name: 'DiyPageGuide',
  components: {
    RightBox,
    PageBox,
    MobileGuide,
    UploadImgItem,
  },
  computed: {
    ...mapState('diy', ['theme11']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
    postData: {
      get() {
        return this.theme11
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 11
        })
      }
    },
  },
  data() {
    return {

    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}
.up-flex-box {
  display: flex;
  .preview-wrap {
    position: relative;
    width: 167px;
    margin-left: 20px;
    color: #000;
  }
  .big {
    width: 24px;
    height: 24px;
    position: absolute;
    bottom: 28px;
    left: 5px;
  }
  img {
    width: 167px;
    height: 103px;
  }
  p {
    line-height: 23px;
    text-align: center;
  }
}
</style>

