<template>
  <div class="pt-list-wrap">
 <DateSwitch />
    <div
      class="private-list"
      scroll-y="true"
    >
      <div
        class="course theme-bg"
        v-for="item in privateList"
        :key="item.id"
      >
        <div class="left">
          <img
            class="light-shadow coach-avatar"
            :src="item.avatar"
          />
          <div class="coach-info">
            <div class="name">{{item.coach_name}}</div>
            <div class="coach-classes">
              <div
                class="class"
                v-for="name in item.permitted_class"
                :key="name"
              >{{name}}</div>
            </div>
          </div>
          <div class="surplus-num" v-if="arrIndexOf(config.show_content, '1')">余{{item.surplus}}席</div>
        </div>
        <div class="reserve">
          <div
            v-if="item.surplus > 0"
            class="reserve-btn"
          >预约</div>
          <div
            v-else
            class="reserve-btn disabled"
          >已满</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import DateSwitch from './DateSwitch'
export default {
  name: 'PtClass',
  components: {
    DateSwitch
  },
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    },
    isSwim: {
      type: Boolean,
      default:false
    }
  },
  data() {
    return {
      privateList: []
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
       this.$service
        .post('/Web/NewMemberTemplateSetting/get_schedule_mock_data', { type: this.isSwim?3:2 })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.privateList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>

</style>