<template>
  <MobileBox class="diy-tabbar">
    <div class="nodata" v-if="!isSelectMerchant && isMerchantMode">
      综合体育场版模式下，场馆不显示底部菜单
    </div>
    <template v-else>
      <div class="tab-tips">
        手机端实际位置悬浮在页面底部
      </div>
      <div class="tab-bar" :style="{backgroundColor: theme == 2 ? '#000000' : '#ffffff'}">
        <div class="tab-bar-border"></div>
        <template v-for="(item, index) in theme5">
          <div v-if="item.display === 1" :key="index" class="tab-bar-item" @click="switchToTab(index)">
            <img :src="getImgPath(index)" />
            <div :style="{color: selected === index ? (theme == 2 ? '#ffffff' : '#0E0E0E') : (theme == 2 ? '#7d7d7d' : '#0E0E0E')}">{{item.name}}</div>
          </div>
        </template>
      </div>
    </template>
  </MobileBox>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import MobileBox from './MobileBox'

export default {
  name: 'MobileTabbar',
  components: {
    MobileBox,
  },

  inject: ['IS_BRAND_SITE'],
  data() {
    return {
      selected: 0,
      tabKeyArr: ['home', 'reserve', 'reserve', 'my']
    }
  },
  computed: {
    ...mapState('diy', ['theme5','theme1']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
    color() {
      return this.theme1.fashion_color || 'a1ea2b'
    },
    theme() {
      return this.theme1.background_color || 1
    },
  },
  created() {
  },
  methods: {
    // ...mapMutations('diy', ['SET_SELECT_MER_BUS_ID']),
    getImgPath(index) {
      let path = `${this.tabKeyArr[index]}.png`
      if(this.selected === index) {
        path = `${this.color}/${this.tabKeyArr[index]}-selected${ this.theme == 2 ? '' : '-dark' }.png`
      }
      return `https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/tabicons/${path}`
    },
    switchToTab(index) {
      this.selected = index
    }
  },
}
</script>
<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.mobile-box {
  font-size: 24px;
}
.diy-tabbar {
  margin-right: 30px;
  position: relative;
}
.tab-tips {
  text-align: center;
}
.tab-bar {
  position: absolute;
  margin: 70px 44px;
  top: 0;
  left: 0;
  right: 0;
  height: 96px;
  background: white;
  display: flex;
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 2px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-size: 20px;
}

.tab-bar-item img {
  width: 44px;
  height: 44px;
  margin-bottom: 4px;
}
</style>
