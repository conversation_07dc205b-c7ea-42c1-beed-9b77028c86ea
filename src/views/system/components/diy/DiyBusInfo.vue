<template>
<RightBox>
  <div slot="title">{{postData.name}}</div>
  <div class="min-tit">
    模块样式
  </div>
 <StyleSet v-model="postData.type" :list="styleList" />
 <template v-if="IS_BRAND_SITE">
  <div class="min-tit">
    <div>场馆：</div>
  </div>
  <div style="margin-bottom:20px;">
    <Select v-model="postData.bus_id" filterable class="w250">
      <Option
        v-for="(item) in filterBusList"
        :key="item.bus_id"
        :value="item.encode_bus_id"
        :label="item.bus_name">
      </Option>
    </Select>
  </div>
 </template>
  <div class="min-tit">
    添加轮播图片：
    <div class="tips">
      最多添加8个广告 <br />
      建议图片尺寸 750*460px 推荐大小：100kb
    </div>
  </div>
  <LinkItem v-model="postData.imgs" :selectBusId="selectBusId" />
  <div class="min-tit">
    <div>切换场馆按钮：</div>
    <i-switch v-model="postData.toggle_bus" true-value="1" false-value="0" />
  </div>
</RightBox>
</template>

<script>
import RightBox from './RightBox'
import LinkItem from './LinkItem'
import StyleSet from './StyleSet'
import { mapGetters, mapState } from 'vuex'
export default {
  name: 'DiyBusInfo',
  components: {
    RightBox,
    StyleSet,
    LinkItem
  },
  props: {
    title: {
      type: String,
      default: '场馆介绍'
    }
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      styleList: [{
        url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/gym-style-1.png',
        name: '默认样式',
        type: '1'
      },{
        url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/gym-style-2.png',
        name: '样式二',
        type: '2'
      }]
    }
  },

  watch: {
    selectMerBusId: {
      handler(val) {
        if (!this.postData.bus_id) {
          if (this.isSelectMerchant) {
            this.$set(this.postData, 'bus_id', this.firstBusIdEncode || '')
          } else {
            this.$set(this.postData, 'bus_id', this.merBusList.find(v => this.selectMerBusId == v.bus_id).encode_bus_id)
          }
        }
      }
    },
    immediate: true
  },

  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
