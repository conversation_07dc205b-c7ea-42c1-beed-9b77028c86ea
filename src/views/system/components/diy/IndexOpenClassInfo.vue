<template>
    <div class="bot-wrap theme-bg" :class="config.type==2?'style2':''">
    <div class="box-tit">
      {{config.name || '推荐团课'}}
      <div
        class="rig"
        v-if="list && list.length"
      >
        查看更多
        <div class="arrow-right"></div>
      </div>
    </div>
    <div class="bot-con class-wrap">
      <div
        class="class-item"
        v-for="(item, key) in list"
        :key="`card_list_${key}`"
      >
        <div class="img-wrap">
          <img
            class="item-img"
            mode="aspectFill"
            :src="item.thumb"
          />
          <div class="img-con">
            <div class="con-span" v-if="item.min_number && arrIndexOf(config.show_content, '4')">满{{item.min_number}}人开课</div>
            <Rate
              v-if="arrIndexOf(config.show_content, '3')"
              class="rate"
              :disabled="true"
              :value="+item.class_level"
              :count="+item.class_level"
              :size="12"
            />
          </div>
        </div>
        <div class="bot">
          <div class="name">{{item.class_name}}</div>
          <div class="des" v-if="item.schedule_info">
            <span class="item time">上课时间：{{item.schedule_info.beg_time}}-{{item.schedule_info.end_time}}</span>
            <span class="item" v-if="arrIndexOf(config.show_content, '2')">{{item.schedule_info.classroom_name}}</span>
            <span class="item" v-if="arrIndexOf(config.show_content, '1')">{{item.schedule_info.coach_name}}</span>
          </div>
          <div class="des" v-else>
            <span class="item time">今日暂无排课信息</span>
            <span class="item">&nbsp;</span>
          </div>
        </div>
      </div>
      <div v-if="list && list.length == 0" class="nodata">暂未设置推荐团课</div>
    </div>
    </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
export default {
  name: 'IndexOpenClassInfo',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    }
  },
  data() {
    return {
    }
  },
  computed: {
    list() {
      return this.config.recommend_type==2 ? this.config.recommend_class_detail : this.initList
    }
  },

  watch: {
    'config.bus_id'(val) {
      this.getInitList(4, val)
    }
  },

  created() {
    this.getInitList(4, this.config.bus_id)
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.class-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.class-item {
  width: 330px;
  border: 1px solid #E8E8E8;
  border-radius: 20px;
  overflow: hidden;
  font-size: 24px;
  margin-bottom: 30px;
  &:first-child {
    width: 670px;
    .time {
      width: 100%;
    }
    .item-img {
      height: 388px;
    }
  }
  .item-img {
    width: 100%;
    height: 190px;
  }
  .img-wrap {
    position: relative;
    color: #fff;
    .img-con {
      position: absolute;
      left: 0;
      bottom: 20px;
      width: 100%;
      box-sizing: border-box;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .con-span {
      text-shadow: 0px 1px 4px rgba(0,0,0,0.3);
      font-weight: bold;
      color: #FFF;
    }
  }
  .des {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .item {
      margin-right: 10px;
    }
  }

  .bot {
    padding: 0 20px 20px;
    line-height: 1.75;

    .name {
      font-size: 30px;
      font-weight: bold;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.style2 {
  .class-wrap {
    width: 100%;
    overflow-x: scroll;
    white-space: nowrap;
    flex-wrap: nowrap;
    .class-item {
      width: 450px;
      flex-shrink: 0;
      margin-right: 20px;
      .item-img {
        width: 100%;
        height: 261px;
      }
      .time {
        width: 100%;
      }
    }
  }
}
</style>
