<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <Form
      :model="postData"
      :label-width="120"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">上课教练</Checkbox>
          <Checkbox label="2">上课教室</Checkbox>
          <Checkbox label="3">空位</Checkbox>
          <Checkbox label="4">最小预约人数</Checkbox>
          <Checkbox label="5">课程强度</Checkbox>
          <Checkbox label="6">已开始课程</Checkbox>
          <Checkbox label="7">已结束课程</Checkbox>
        </Checkbox-group>
      </FormItem>
      <FormItem v-show="postData.show_content && postData.show_content.includes('3')" label="会员约课人数显示：">
        <RadioGroup v-model="postData.remaining_seats">
          <Radio :label="1">显示剩余人数</Radio>
          <Radio :label="2">显示已约和总数</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="背景设置：">
        <RadioGroup v-model="postData.background_type">
          <Radio label="1">无</Radio>
          <Radio label="2">自定义</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem  v-show="postData.background_type == 2">
        <div class="tips">
          建议图片尺寸 690*190px 推荐大小：100kb
        </div>
         <upload-img-item des="尺寸：690*190" :options="{aspectRatio: 69/19}" class="mb10" v-model="postData.background"></upload-img-item>
      </FormItem>
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import RightBox from './RightBox'
import CoachItem from './CoachItem'
import StyleSet from './StyleSet'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyOpenClass',
  components: {
    RightBox,
    UploadImgItem,
    StyleSet,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme3','indexLeftSelected3', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme3[this.indexLeftSelected3]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 3
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/openclass-style-1.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/openclass-style-2.png',
          name: '纵向',
          type: '2',
        }
      ]
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
