<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <!-- <div class="min-tit">
      模块样式
    </div> -->
    <!-- <StyleSet
      v-model="postData.type"
      :list="styleList"
      :showText="false"
    /> -->
    <Form
      :model="postData"
      :label-width="80"
    >
      <!-- <FormItem label="显示内容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">已满场地</Checkbox>
          <Checkbox label="2">场地最低价格</Checkbox>
        </Checkbox-group>
      </FormItem>
      <FormItem label="显示维度：">
        <RadioGroup v-model="postData.background_type">
          <Radio label="1">场地类型</Radio>
          <Radio label="2">场地</Radio>
        </RadioGroup>
      </FormItem> -->
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
    </Form>

  </RightBox>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import RightBox from './RightBox'
// import StyleSet from './StyleSet'
export default {
  name: 'DiyField',
  components: {
    RightBox,
    // StyleSet
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme3', 'indexLeftSelected3', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme3[this.indexLeftSelected3]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM', {
          value: val,
          type: 3
        })
      }
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      // 模块样式
      styleList: [
        {
          url:
            'https://imagecdn.rocketbird.cn/mainsite-fe/diy/openclass-style-2.png',
          name: '默认样式',
          type: '1'
        },
        {
          url:
            'https://imagecdn.rocketbird.cn/mainsite-fe/diy/openclass-style-1.png',
          name: '纵向',
          type: '2'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
