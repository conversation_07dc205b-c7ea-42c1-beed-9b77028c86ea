<template>
  <div v-show="config.isHide" class="refund-box">
    <img class="boximg" mode="scaleToFill" :src="activityInfo.thumb" />
    <div class="refund-info">
      <div class="info-lef">
        <div class="info-tit">{{ activityInfo.name }}</div>
        <div class="info-des" v-if="activityInfo.refund_condition == 1">
          [首次入场不满{{ activityInfo.refund_time }}分钟可退款]
        </div>
      </div>
      <div class="info-rig">
        <div class="refund-btn">¥{{ activityInfo.curr_cost }} 购买</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityRefund',
  props: {
    config: {
      type: Object,
    },
  },
  data() {
    return {
      activityInfo: {
        activity_id: '',
        card_name: '',
        name: '',
        curr_cost: '',
        ori_cost: '',
        refund_condition: '',
        refund_time: '',
      },
    }
  },
  created() {
    // this.getInfo(this.config.activity_id)
  },
  watch: {
    'config.activity_id': {
      handler(val) {
        console.log(val)
        if (val) {
          this.getInfo(val)
        } else {
          this.activityInfo = {
            activity_id: '',
            card_name: '',
            name: '',
            curr_cost: '',
            ori_cost: '',
            refund_condition: '',
            refund_time: '',
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    getInfo(id) {
      this.$service.post('/Web/Activity/get_activity_info', { id }).then((res) => {
        if (res.data.errorcode === 0) {
          const { info } = res.data.data
          this.activityInfo = info
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.refund-box {
  width: 710px;
  height: 170px;
  margin: 20px auto;
  position: relative;
  .boximg {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
  .refund-info {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 30px 35px 0;
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.6);
    top: 0;
    left: 0;
  }
  .info-tit {
    font-size: 30px;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .info-lef {
    flex: 1;
    overflow: hidden;
  }
  .info-rig {
    width: 170px;
    flex-shrink: 0;
    text-align: center;
  }
  .info-des {
    margin-top: 12px;
  }
  .code-img {
    width: 74px;
    height: 74px;
  }
  .qrcode-wrap {
    margin-top: 10px;
  }
  .refund-btn {
    width: 170px;
    height: 53px;
    margin-top: 36px;
    background: #92dfe8;
    border-radius: 0px 0px 0px 0px;
    font-weight: bold;
    font-size: 26px;
    color: #000000;
    line-height: 53px;
  }
}
</style>
