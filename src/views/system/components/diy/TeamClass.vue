<template>
  <div class="pt-list-wrap">
    <DateSwitch />
    <div
      v-if="filterList && filterList.length"
      class="private-list"
      scroll-y="true"
    >
      <div
        class="course theme-bg"
        :style="{'backgroundImage':config.background_type==2?`url(${config.background})`:'',backgroundSize:'cover'}"
        v-for="item in filterList"
        :key="item.id"
      >
       
        <div class="lef-tag">
          限班级成员参加
        </div>
        <div class="left">
          <img
            class="coach-avatar"
            :src="item.avatar"
          />
          <div class="coach-info">
            <div class="name">
              {{item.class_name}}
            </div>
            <div
              class="item coach"
            >
              <span class="time-bg">{{item.begin_time}}-{{item.end_time}}</span>
              <span v-if="arrIndexOf(config.show_content, '1')">{{item.coach_name}}</span>
            </div>
            <div class="item">
              <ThemeIcon
                v-if="arrIndexOf(config.show_content, '2')"
                class="icon-mr"
                type="t-icon-jiaoshi1"
              />
              <span v-if="arrIndexOf(config.show_content, '2')">{{item.classroom_name || '无'}}</span>
              <span
                class="cut-up"
                v-if="arrIndexOf(config.show_content, '2') && arrIndexOf(config.show_content, '3')"
              >|</span>
              <span v-if="arrIndexOf(config.show_content, '3')">共<span class="theme-color-other ">{{item.student_num}}</span>名学员</span>
            </div>
          </div>
        </div>
        <div
          class="theme-color-other"
        >
          {{ item.status }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import memberPageIndex from 'mixins/memberPageIndex'
import DateSwitch from './DateSwitch'
import ThemeIcon from './ThemeIcon';
export default {
  name: 'TeamClass',
  mixins: [memberPageIndex],
  props: {
    config: {
      type: Object
    },
    isSwim: {
      type: Boolean,
      default:false
    }
  },
  components: {
    DateSwitch,
    ThemeIcon
  },
  data() {
    return {
      privateList: [],
    }
  },
  created() {
    this.getInfo()
  },
  computed: {
    filterList() {
      return this.privateList.filter((item) => {
        if((item.status === '已开始' && this.arrIndexOf(this.config.show_content, '4')) || (item.status === '已结束' && this.arrIndexOf(this.config.show_content, '5'))) {
          return true
        } else if (item.status !== '已开始' && item.status !== '已结束') {
          return true
        }
        return false
      })
    }
  },
  methods: {
    getInfo() {
      this.$service
        .post('/Web/NewMemberTemplateSetting/get_schedule_mock_data', {
          type: this.isSwim ? 6 : 5,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.privateList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
}
</script>

<style lang="less" scoped>
</style>