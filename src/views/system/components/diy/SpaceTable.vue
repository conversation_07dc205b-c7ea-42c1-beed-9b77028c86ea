<template>
  <Modal
    v-model="showAdd"
    title="场地类型选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.name"
          @on-enter="getList"
          placeholder="名称"
        ></Input>
         <Select
          style="width: 120px"
          v-model="searchData.type"
          placeholder="经营方式"
          clearable
        >
          <Option value="1">订场</Option>
          <Option value="2">散场</Option>
        </Select>
        <Button
          type="success"
          @click="getList"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          class="avatar-zoom"
          stripe
          disabled-hover
        ></Table>
      </main>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import Selection from 'mixins/selection'
export default {
  name: 'SpaceTable',
  mixins: [Selection],
  data() {
    return {
      unitList: ['小时', '分钟'],
      searchData: {
        name: '',
        type: '',
      },
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60
        },
        {
          title: '场地类型名称',
          key: 'name',
        },
        {
          title: '经营方式',
          key: 'type',
          render: (h, params) => {
            return (<div>{params.row.type.includes('1') || params.row.type.includes(1)?'订场':''}{params.row.type.length>=2?'、':''}{params.row.type.includes('2') || params.row.type.includes(2)?'散场':''}</div>)
          },
        },
        {
          title: '订场团队人数限制',
          key: 'max_join_people',
          render: (h, params) => {
            return (<div>{params.row.max_join_people}人</div>)
          },
        }
      ]
    }
  },
  props: {
    value: {
      type: Boolean
    },
    selectBusId: String || Number
  },

  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {},
  components: {},
  created() {
    this.getList()
  },
  methods: {
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection
      })
      this.showAdd = false
    },
    getList() {
      //type
      this.$service
        .post('/Web/Space/getTypes', { ...this.searchData, bus_id: this.selectBusId })
        .then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data
            this.tableData = list.map(item => {
              return {
                ...item,
                _checked: this.selectionId.includes(item.id)
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
