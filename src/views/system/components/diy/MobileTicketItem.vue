<template>
  <div class="ticket-card" v-if="info">
    <div class="card-left">
      <div class="label">{{ info.name || info.card_name }}</div>
      <div class="desc">
        {{ info.max_valid_time }}天内使用，进场后可停留 {{ info.base_duration }}
        {{ info.duration_unit == 1 ? '小时' : '分钟' }}
      </div>
      <div class="desc desc-name">适用场地：{{ info.space_name && info.space_name.length? info.space_name : '入场' }}</div>
    </div>
    <div class="card-right">
      <div class="price"> <span>￥</span>{{ info.base_fee }}</div>
      <div class="ticket-btn">立即购买</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileTicketItem',
  data() {
    return {
    }
  },
  props: {
    info: {
      type: Object
    }
  },
  methods: {
  },
}
</script>

<style lang="less" scoped>
@import '~@/styles/themeVar.less';
.ticket-card {
  width: 693px;
  height: 230px;
  margin: 0 auto 26px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/stadium/ticket-bg.png');
  background-size: 100% 100%;
  .card-left {
    width: 468px;
    padding: 30px;
    box-sizing: border-box;
    .label {
      font-size: 32px;
      font-weight: bold;
      color: #313131;
      line-height: 36px;
    }
    .desc {
      font-size: 22px;
      color: #898989;
      line-height: 30px;
      margin-top: 20px;
    }
    .desc-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .price {
    color: @theme-text-color-other;
    font-size: 44px;
    font-weight: bold;
    margin-bottom: 30px;
    text {
      font-size: 26px;
    }
  }
  .ticket-btn {
    width: 157px;
    height: 60px;
    line-height: 60px;
    background: @theme-text-color-other;
    border-radius: 30px;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
  }
  .card-right {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
</style>