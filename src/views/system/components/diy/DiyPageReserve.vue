<template>
  <PageBox :type="3">
    <template slot-scope="slotProps">
      <diy-left title="预约" :type="3" :data="slotProps.init"></diy-left>
      <mobile-reserve></mobile-reserve>
      <component v-show="!theme3[indexLeftSelected3].isHide" :type="3" :is="componentsName[+theme3[indexLeftSelected3].temp_type-1]" :title="theme3[indexLeftSelected3].name"></component>
    </template>
  </PageBox>
</template>

<script>
import PageBox from './PageBox'
import DiyLeft from './DiyLeft.vue'
import DiyField from './DiyField.vue'
import DiyPtClass from './DiyPtClass'
import DiyOpenClass from './DiyOpenClass'
import DiyTeamClass from './DiyTeamClass'
import MobileReserve from './MobileReserve.vue'
import { mapState } from 'vuex';
export default {
  name: 'DiyPageReserve',
  components: {
    PageBox,
    DiyLeft,
    DiyField,
    DiyPtClass,
    DiyOpenClass,
    MobileReserve,
    DiyTeamClass,
  },
  computed: {
    ...mapState('diy', ['theme3','indexLeftSelected3'])
  },
  watch: {
  },
  data() {
    return {
      componentsName: ['DiyOpenClass','DiyPtClass','DiyField','DiyPtClass', 'DiyTeamClass', 'DiyTeamClass'],
      list: []
    }
  },
  created() {
  },
  methods: {
  },
}
</script>

