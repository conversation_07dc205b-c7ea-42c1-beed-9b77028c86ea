<template>
  <div class="info-wrap">
    <ListItem
      v-model="itemList"
      add-tips="选择活动"
      :img-width="104"
      :img-height="88"
      avatar-key="thumb"
      @on-add="addItem"
    />
    <div v-if="showAddModal">
      <ActivityTable
        v-model="showAddModal"
        :selectBusId="selectBusId"
        @on-confirm="itemConfirm"
      />
    </div>
  </div>
</template>

<script>
import ListItem from './ListItem'
import ActivityTable from './ActivityTable'
export default {
  name: 'ActivityItem',
  components: {
    ActivityTable,
    ListItem,
  },
  data() {
    return {
      showAddModal: false
    }
  },
  props: {
    value: {
      type: Array,
      default: ()=>[]
    },
    selectBusId: String || Number
  },
  computed: {
    itemList: {
      get() {
        return this.value || []
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {},
  methods: {
    addItem() {
      this.showAddModal = true
    },
    itemConfirm(info) {
      this.itemList = this.itemList.concat(info.selection)
      this.$emit('on-confirm', this.itemList)
    },
  },
}
</script>

<style lang="less" scoped>
</style>
