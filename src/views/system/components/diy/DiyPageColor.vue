<template>
<PageBox :type="1">
 <MobileColor :color="postData.fashion_color" :theme="postData.background_color" />
  <div class="main-right">
    <div v-if="!isSelectMerchant" class="main-right-tit">会员端开放使用</div>
    <RadioGroup v-if="!isSelectMerchant" v-model="postData.member_open" style="margin-bottom: 20px;">
      <Radio label="1">开放会员使用</Radio>
      <Radio label="0">禁止使用（适用于闭店或者门店装修期间 让会员无法使用本门店会员端）</Radio>
    </RadioGroup>
    <!-- <div class="main-right-tit">外观颜色</div>
    <AppearanceStyleSets v-model="postData" name="background_color" :list="styleList" /> -->
    <div class="main-right-bottom">
      <div class="main-right-tit">独特配色</div>
      <AppearanceStyleSets v-model="postData" name="fashion_color" :list="colorList" />
    </div>
  </div>
</PageBox>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
const themeImgPath = 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/'
import MobileColor from './MobileColor'
import PageBox from './PageBox'
import AppearanceStyleSets from './AppearanceStyleSets'
export default {
  name: 'DiyPageColor',
  components: {
    MobileColor,
    PageBox,
    AppearanceStyleSets
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme1']),
    ...mapGetters('diy', ['isSelectMerchant']),
    postData: {
      get() {
        return this.theme1
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type:1
        })
      }
    },
  },
  data() {
    return {
      styleList: [{
        url: `${themeImgPath}theme-default.png`,
        name: '浅色',
        type: '1'
      },{
        url: `${themeImgPath}theme-dark.png`,
        name: '深色(适配中，敬请期待...)',
        type: '2'
      }],
      colorList: [{
        url: `${themeImgPath}color-a1ea2b.png`,
        name: '活力绿',
        type: 'a1ea2b'
      },{
        url: `${themeImgPath}color-82dae4.png`,
        name: '清新蓝',
        type: '82dae4'
      },{
        url: `${themeImgPath}color-00b5b6.png`,
        name: '英东蓝',
        type: '00b5b6'
      },{
        url: `${themeImgPath}color-06befc.png`,
        name: '夏日蓝',
        type: '06befc'
      },{
        url: `${themeImgPath}color-ffd066.png`,
        name: '阳光黄',
        type: 'ffd066'
      },{
        url: `${themeImgPath}color-f72c6c.png`,
        name: '甜酷粉',
        type: 'f72c6c'
      }]
    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  padding-top: 30px;
  width: 539px;
}
</style>

