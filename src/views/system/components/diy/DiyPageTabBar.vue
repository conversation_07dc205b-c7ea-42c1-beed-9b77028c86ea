<template>
<PageBox :type="5">
 <MobileTabbar />
 <RightBox v-show="!isMerchantMode || isSelectMerchant">
    <div slot="title">底部导航</div>
    <div class="list">
      <template v-for="(item, index) in theme5">
        <DiyPageTabBarItem :index="index" :key="index" />
      </template>
    </div>
  </RightBox>
  <span v-if="isMerchantMode && !isSelectMerchant" slot="save"></span>
</PageBox>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
import DiyPageTabBarItem from './DiyPageTabBarItem'
import MobileTabbar from './MobileTabbar'
export default {
  name: 'DiyPageTabBar',
  components: {
    RightBox,
    PageBox,
    MobileTabbar,
    DiyPageTabBarItem
  },
  computed: {
    ...mapState('diy', ['theme5', 'indexLeftSelected5']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
  },
  data() {
    return {

    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}
</style>

