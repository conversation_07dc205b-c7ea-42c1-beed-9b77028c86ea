<template>
<div class="style-box">
  <div class="style-item" :class="curStyle===item.type?'item-checked':''" @click="setStyle(item)" v-for="(item, index) in list" :key="index">
    <div class="checked-tag">
      <img src="~src/assets/img/diy/used.png"/>
    </div>
    <div class="img-wrap">
      <img :src="item.url"/>
    </div>
    <div class="tit" v-if="showText">{{item.name}}</div>
  </div>
 </div>
</template>

<script>
export default {
  name: 'StyleSet',
  data () {
    return {
    }
  },
  props: {
    value: {
      type: [String, Number]
    },
    list: {
      type: Array,
      default: []
    },
    showText: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    curStyle: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
  },
  created () {
  },
  methods: {
    setStyle(info) {
      this.curStyle = info.type
    },
  }
}
</script>
<style lang="less" scoped>
.style-box {
  display: flex;
  align-items: center;
}
.style-item {
  margin: 0 20px 20px 0;
  position: relative;
  cursor: pointer;
  .img-wrap {
    border: 1px dashed #CCCCCC;
    border-radius: 6px;
  }
  
  .tit {
    text-align: center;
    margin-top: 9px;
    font-size: 12px;
    color: #313131;
  }
  .checked-tag {
    display: none;
  }
}
.item-checked {
  .img-wrap {
    border-color:#47CB89
  }
  .checked-tag {
    position: absolute;
    right: -10px;
    top: -12px;
    display: block;
    z-index: 1;
  }
}
</style>

