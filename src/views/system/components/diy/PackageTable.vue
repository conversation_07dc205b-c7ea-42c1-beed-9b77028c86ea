<template>
  <Modal
    v-model="showAdd"
    title="套餐包选择"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <header>
        <Input
          style="width: 150px"
          v-model="searchData.name"
          @on-enter="getList"
          placeholder="套餐包名称"
        ></Input>
        <Button
          type="success"
          @click="doSearch"
        >搜索</Button>
      </header>
      <main>
        <Table
          @on-selection-change="onSelectionChange"
          @on-select="onSelect"
          @on-select-all="onSelectAll"
          @on-select-cancel="onSelectCancel"
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doAdd"
      >保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
export default {
  name: 'PackageTable',
  data() {
    return {
      pageTotal: 0,
      searchData: {
        name: '',
        status: 1,
        is_member: 1,
        page_size: 10,
      },
      selection: [],
      tableData: [],
      columns: [
        {
          type: 'selection',
          width: 60,
        },
        {
          title: '套餐包名称',
          key: 'name'
        },
        {
          title: '内容',
          key: 'contend',
        },
        {
          title: '售价',
          key: 'amount',
        }
      ],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    selectBusId: String || Number
  },
  computed: {
    selectionId() {
      return this.selection.map(item => item.id);
    },
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
  },
  components: {},
  created() {
    this.getList()
  },
  methods: {
     onSelectionChange(selection) {
      if (selection.length === 0) {
        this.tableData.forEach(item => this.onSelectCancel(selection, item));
      }
    },
    onSelectCancel(selection, row) {
      const index = this.selection.findIndex(item => item.id === row.id);
      this.selection.splice(index, 1);
    },
    onSelect(selection, row) {
      if (!this.selectionId.includes(row.id)) {
        this.selection.push(row);
      }
    },
    onSelectAll(selection) {
      selection.forEach(item => this.onSelect(selection, item));
    },
    doAdd() {
      this.$emit('on-confirm', {
        selectionId: this.selectionId,
        selection: this.selection,
      })
      this.showAdd = false
    },
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/package/getPackageList', {
          ...this.searchData,
          bus_id: this.selectBusId || this.$store.state.busId
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.list
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
  },
}
</script>

