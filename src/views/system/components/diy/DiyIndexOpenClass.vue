<template>
  <RightBox>
    <div slot="title">{{postData.name}}</div>
    <div class="min-tit">
      模块样式
    </div>
    <StyleSet
      v-model="postData.type"
      :list="styleList"
    />
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="显示类容：">
        <Checkbox-group v-model="postData.show_content">
          <Checkbox label="1">上课教练</Checkbox>
          <Checkbox label="2">上课教室</Checkbox>
          <Checkbox label="3">课程强度</Checkbox>
          <Checkbox label="4">最小开课人数</Checkbox>
        </Checkbox-group>
      </FormItem>
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="排序规则：">
        <RadioGroup v-model="postData.recommend_type">
          <Radio label="1">创建时间倒序</Radio>
          <Radio label="2">自定义添加</Radio>
        </RadioGroup>
      </FormItem>
       <div v-show="postData.recommend_type == 2">
        <div class="tips">选择的课程不超过3门</div>
        <ClassItem v-model="postData.recommend_class_detail" :selectBusId="selectBusId" />
      </div>
    </Form>

  </RightBox>
</template>

<script>
import RightBox from './RightBox'
import ClassItem from './ClassItem'
import StyleSet from './StyleSet'
import { mapState, mapGetters } from 'vuex'
export default {
  name: 'DiyOpenClass',
  components: {
    RightBox,
    ClassItem,
    StyleSet,
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme2','indexLeftSelected2', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme2.list[this.indexLeftSelected2]
      },
      set(val) {
        this.$store.commit('diy/UPDATE_DIY_CUR_ITEM',  {
          value: val,
          type: 2
        })
      },
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
      styleList: [
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/indexclass-style-1.png',
          name: '默认样式',
          type: '1',
        },
        {
          url: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/indexclass-style-2.png',
          name: '纵向',
          type: '2',
        }
      ]
    }
  },

  watch: {
    'postData.bus_id'() {
      this.postData.recommend_class_detail = []
    }
  },

  methods: {
  }
}
</script>

<style lang="less" scoped>
.min-tit {
  display: flex;
  font-size: 14px;
  color: #000000;
  margin-bottom: 15px;
  align-items: flex-start;
}
.tips {
  font-size: 12px;
  color: #7d7d7d;
  margin-bottom: 10px;
}
</style>
