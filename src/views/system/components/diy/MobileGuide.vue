<template>
  <MobileBox>
    <div class="guide-wrap">
      <img class="guide-img" :src="theme11.image_url" />
    </div>
  </MobileBox>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import MobileBox from './MobileBox'

export default {
  name: 'MobileGuide',
  components: {
    MobileBox,
  },
  inject: ['IS_BRAND_SITE'],
  data() {
    return {
    }
  },
  computed: {
    ...mapState('diy', ['theme11']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
  },
  created() {
  },
}
</script>
<style lang="less" scoped>
.guide-wrap {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: scroll;
  .guide-img {
    display: block;
    width: 100%;
  }
}
</style>
