<template>
<PageBox :type="4">
 <MobileMy />

 <RightBox>
    <div slot="title">我的页面</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="我的权益：">
        <Select v-model="postData.my_interest" multiple filterable class="w250">
          <Option v-for="item in iconInfo.my_interest" :key="item" :value="item">{{ item }}</Option>
        </Select>
      </FormItem>
      <FormItem label="邀请有礼：">
        <RadioGroup v-model="postData.display">
          <Radio :label="1">显示</Radio>
          <Radio :label="0">隐藏</Radio>
        </RadioGroup>
        <div>
          <RadioGroup v-model="postData.background_type">
            <Radio label="1">默认背景</Radio>
            <Radio label="2">自定义</Radio>
          </RadioGroup>
        </div>
        <div  v-show="postData.background_type == 2">
          <div class="tips">
            建议图片尺寸 710*180px 推荐大小：100kb
          </div>
          <upload-img-item des="尺寸：710*180" :options="{aspectRatio: 71/18}" class="mb10" v-model="postData.background_img"></upload-img-item>
        </div>
        <div v-if="IS_BRAND_SITE && isSelectMerchant">
          <Select v-model="postData.bus_id" filterable class="w250" placeholder="请选择场馆">
            <Option
              v-for="(item) in filterBusList"
              :key="item.bus_id"
              :value="item.encode_bus_id"
              :label="item.bus_name">
            </Option>
          </Select>
        </div>
      </FormItem>
      <FormItem label="服务项目：">
        <Select v-model="postData.service_item" multiple filterable class="w250">
          <Option v-for="item in iconInfo.service_item" :key="item" :value="item">{{ item }}</Option>
        </Select>
      </FormItem>
      <FormItem label="个人数据：">
        <Select v-model="postData.personal_data" multiple filterable class="w250">
          <Option v-for="item in iconInfo.personal_data" :key="item" :value="item">{{ item }}</Option>
        </Select>
      </FormItem>
    </Form>
  </RightBox>
</PageBox>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import { mapState, mapGetters } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
import MobileMy from './MobileMy'
export default {
  name: 'DiyPageMy',
  components: {
    UploadImgItem,
    MobileMy,
    PageBox,
    RightBox
  },

  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme4', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme4
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 4
        })
      }
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
  },
  data() {
    return {
      iconInfo: {
        my_interest: ['会员卡课', '入场凭证', '预约记录', '我的折扣券', '我的合同'],
        personal_data: ['运动记录', '历史体测'],
        service_item: ['我的发票','开柜密码', '留言咨询', '人脸识别', '请假记录', '租柜记录', '红包记录', '券码兑换', '帮助中心', '在线客服'],
      }
    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}
</style>

