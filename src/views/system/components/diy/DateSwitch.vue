<template>
 <div class="date-switch theme-bg">
  <div
    class="date"
    :class="today == item.date ? 'inset-shadow active' : ''"
    v-for="item in dateList"
    :key="item.day"
  >
    <span class="week">{{item.week}}</span>
    <span class="day">{{item.day}}</span>
  </div>
</div>
</template>

<script>
export default {
  name: 'DateSwitch',
  components: {
  },
  data() {
    return {
      weekList: ['日', '一', '二', '三', '四', '五', '六'],
      dayList: ['今天', '明天', '后天'],
      today: '',
      dates: [],
      dateList: [],
    }
  },
  created() {
    this.setDateList(7)
  },
  methods: {
    setDateList(dayNum) {
      const dateNow = new Date()
      const yearNow = dateNow.getFullYear()
      const monthNow = dateNow.getMonth()
      const dayNow = dateNow.getDate()
      this.today= `${yearNow}-${monthNow + 1}-${dayNow}`
      let num = 0
      let dateListArr = []
      let datesArr = []
      while (num < dayNum) {
        const date = new Date(yearNow, monthNow, dayNow + num)
        datesArr.push(date)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const week = date.getDay()
        const day = date.getDate()
        dateListArr.push({
          day: day,
          week: this.dayList[num] || `周${this.weekList[week]}`,
          date: `${year}-${month}-${day}`,
        })
        num++
      }
      this.dates = datesArr
      this.dateList = dateListArr
    }
  }
}
</script>

<style lang="less" scoped>

  .date-switch {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 22px 20px 0;
    overflow: hidden;
  }
  .date-switch .date {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 86px;
    margin-bottom: 35px;
  }
  
  .date-switch .date .day {
    width: 66px;
    height: 66px;
    font-size: 32px;
    font-weight: bold;
    color: #313131;
    text-align: center;
    line-height: 66px;
  }
  .date-switch .date .week {
    margin-bottom: 10px;
    font-size: 24px;
  }
  .date-switch .active .day {
    background: var(--THEME-COLOR);
    border-radius: 50%;
  }
</style>