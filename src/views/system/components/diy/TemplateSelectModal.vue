<template>
  <Modal
    v-model="showAdd"
    title="添加模块"
    :mask-closable="false"
    width="600px"
  >
    <Select
      v-model="curId"
      @on-change="itemChanged"
      placeholder="请选择"
      clearable
      filterable
    >
      <Option
        v-for="item in list"
        :key="item.temp_type"
        :label="item.name"
        :value="item.temp_type"
      >
      </Option>
    </Select>
    <div
      slot="footer"
      class="modal-buttons"
    >
      <Button
        type="success"
        @click="doConfirm"
      >确定</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { cloneDeep } from 'lodash-es';

export default {
  name: 'TemplateSelectModal',
  data() {
    return {
      curItem: '',
      curId: '',
      list: [
        {
          name: '场馆介绍',
          temp_type: 1,
          type: '1',
          imgs: [{
            img_url: '',
            img_link: '',
            img_name: ''
          }]
        },
        {
          name: '明星教练',
          temp_type: 2,
          type: '1',
          recommend_type: '1',
          recommend_coach: [],
          show_content: ['1']
        },
        {
          name: '会员卡推荐',
          temp_type: 3,
          type: '1',
          card_type: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
          show_content: ['1'],
          recommend_type: '1',
          recommend_card: []
        },
        {
          name: '热门活动',
          temp_type: 4,
          type: '1',
          recommend_type: '1',
          show_content: ['1','2','3']
        },
        {
          name: '图片卡',
          temp_type: 5,
          img_url: '',
          link_url: ''
        },
        {
          name: '功能区',
          temp_type: 6,
          type: '1',
          icon_content: [
            {icon: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/fun-icon-1.png', name: '场内活动', link: '/pages/activity/index', uniqueId: this.getKeyId()},
            {icon: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/fun-icon-2.png', name: '教练介绍', link: '/pages/coach/list', uniqueId: this.getKeyId()},
            {icon: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/fun-icon-3.png', name: '购卡购课', link: '/pages/card/list', uniqueId: this.getKeyId()},
            {icon: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/fun-icon-4.png', name: '留言咨询', link: '/pages/bus/feedback', uniqueId: this.getKeyId()},
            {icon: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/fun-icon-5.png', name: '场馆介绍', link: '/pages/bus/detail', uniqueId: this.getKeyId()},
          ],
        },
        {
          name: '团课推荐',
          temp_type: 7,
          type:'1',
          show_content: ['1','2','3'],
          recommend_type: '1',
          recommend_class_detail: [],
        },
        {
          name: '邀请有礼',
          temp_type: 9,
          background_type: '1',
          background_img: '',
        },
        {
          name: '票务推荐',
          temp_type: 10,
          recommend_type: '1',
          recommend_ticket: [],
        },
        {
          name: '场地推荐',
          temp_type: 11,
          recommend_type: '1',
          recommend_space: [],
        },
        {
          name: '热销套餐包',
          temp_type: 12,
          type: '1',
          card_type: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
          show_content: ['1'],
          recommend_type: '1',
          recommend_package: []
        },
        {
          name: '连续包月服务',
          temp_type: 13,
          // img_url: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-banner.jpg',
          // link_url: '/pages/payscore/list',
          background_type: '1',
          background_img: '',
        },
        {
          name: '门店体验活动',
          temp_type: 15,
        },
      ],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
  },

  computed: {
    ...mapState(['busIdEncode']),
    ...mapState('diy', ['selectMerBusIdEncode', 'firstBusIdEncode']),
    ...mapGetters('diy', ['isSelectMerchant']),
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.curItem = ''
        this.curId = ''
      } else {
        const peopleCounting = {
          name: '实时人数',
          temp_type: 14,
        }
        if (this.isSelectMerchant) {
          this.list = this.list.filter(item => item.temp_type !== 14)
        } else if (!this.isSelectMerchant && !this.list.find(item => item.temp_type === 14)){
          this.list.splice(4, 0, peopleCounting)
        }
      }
    }
  },
  components: {},
  created() {},
  methods: {
    getKeyId() {
      return URL.createObjectURL(new Blob()).slice(-36)
    },
    itemChanged(type) {
      for (const item of this.list) {
        if (item.temp_type === type) {
          this.curItem = { ...item }
          if ([1, 2, 3, 4, 7, 10, 11, 12, 9, 13, 15].includes(type)) {
            this.curItem.bus_id = window.IS_BRAND_SITE
            ? this.isSelectMerchant ? this.firstBusIdEncode : this.selectMerBusIdEncode
            : this.busIdEncode
          }
          break
        }
      }
    },
    doConfirm() {
      if(!this.curId) {
        this.$Message.error('请先选择')
        return false;
      }
      const item = cloneDeep(this.curItem)
      // 功能区
      if (this.curId === 6) {
        const busId = window.IS_BRAND_SITE
          ? this.isSelectMerchant ? this.firstBusIdEncode : this.selectMerBusIdEncode
          : this.busIdEncode

        item.icon_content.forEach(v => {
          v.link = encodeURIComponent(`${v.link}?bus_id=${busId}`)
        })
      }

      this.$emit('on-confirm', {
        id: this.curId,
        item: item
      })
      this.showAdd = false
    },
  },
}
</script>

