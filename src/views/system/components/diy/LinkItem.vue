<template>
  <div class="link-item-wrap">
    <div class="link-item" v-for="(item, index) in linkList" :key="getKeyId()">
     <img class="del-img" v-if="item.img_url" @click="delItem(index)" src="~src/assets/img/diy/close.png"/>
      <upload-img-item :options="{aspectRatio: 75/46}" class="mb10" v-model="linkList[index].img_url"></upload-img-item>
      <link-select v-model="linkList[index].img_link" :selectBusId="selectBusId" :postData="linkList[index]" @on-change="setLinkList($event, index)"></link-select>
    </div>
  </div>
</template>

<script>
import UploadImgItem from './UploadImgItem'
import LinkSelect from './LinkSelect'
import { getQueryStringByName } from "@/utils/index"

export default {
  name: 'LinkItem',
  components: {
    UploadImgItem,
    LinkSelect
  },
  props: {
    value: {
      type: Array,
      default: ()=> [{
        img_url: '',
        img_link: '',
        img_name: ''
      }]
    },
    selectBusId: String || Number
  },
  data() {
    return {
      max: 8,
    }
  },
  computed: {
    linkList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    linkList: {
      deep: true,
      immediate: true,
      handler: function (val){
        this.$emit('on-change', val)
        if(!val.length || (val && val.length && (val.length < this.max) && val[0].img_url)) {
          this.linkList.unshift({
            img_url: '',
            img_link: '',
            img_name: ''
          })
        }
      }
    }
  },
  methods: {
    getKeyId() {
      return URL.createObjectURL(new Blob()).slice(-36)
    },
    delItem(index) {
      if(this.linkList.length === 1) {
        this.$Message.error('至少保留一项')
      } else {
        this.linkList.splice(index, 1)
      }
    },
    setLinkList(info, index) {
      // this.linkList[index].img_link = info.img_link
      // this.linkList[index].img_name = info.img_name
      const bus_id = getQueryStringByName(info.img_link, 'bus_id')
      if (bus_id) {
        this.linkList[index].bus_id = bus_id
      }
    }
  },
}
</script>

<style lang="less" scoped>
.link-item-wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.link-item {
  position: relative;
  width: 200px;
  padding: 16px;
  margin-right: 20px;
  margin-bottom: 30px;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 6px;
  &:hover .del-img {
    display: block;
  }
}
.del-img {
  cursor: pointer;
  display: none;
  width: 20px;
  height: 20px;
  position: absolute;
  top: -10px;
  right: -10px;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
