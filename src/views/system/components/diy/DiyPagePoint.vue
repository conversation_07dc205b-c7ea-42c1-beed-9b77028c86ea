<template>
<PageBox :type="10">
 <MobilePoint />

 <RightBox>
    <div slot="title">积分商城</div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem v-if="IS_BRAND_SITE" label="场馆：">
        <Select v-model="postData.bus_id" filterable class="w250">
          <Option
            v-for="(item) in filterBusList"
            :key="item.bus_id"
            :value="item.encode_bus_id"
            :label="item.bus_name">
          </Option>
        </Select>
      </FormItem>
    </Form>
  </RightBox>
</PageBox>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
import MobilePoint from './MobilePoint'
export default {
  name: 'DiyPagePoint',
  components: {
    MobilePoint,
    PageBox,
    RightBox
  },
  inject: ['IS_BRAND_SITE'],
  computed: {
    ...mapState('diy', ['theme10', 'merBusList', 'selectMerBusId']),
    ...mapGetters('diy', ['isSelectMerchant', 'isMerchantMode']),
    postData: {
      get() {
        return this.theme10
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 10
        })
      }
    },
    filterBusList() {
      const { isMerchantMode, isSelectMerchant, selectMerBusId, merBusList } = this
      return isMerchantMode && isSelectMerchant ? merBusList : merBusList.filter(v => v.bus_id == selectMerBusId)
    },
    selectBusId() {
      const bus_id = this.postData.bus_id
      const item = this.merBusList.find(v => v.encode_bus_id == bus_id)
      if (item) {
        return item.bus_id
      }
      return undefined
    }
  },
  data() {
    return {
    }
  }
}
</script>
<style lang="less" scoped>
.main-right {
  padding-left: 25px;
}
.main-right-tit {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #313131;
  margin-bottom: 15px;
}
.main-right-bottom {
  margin-top: 30px;
  padding-top: 30px;
  width: 539px;
  border-top: 1px solid #E8E8E8;
}
</style>

