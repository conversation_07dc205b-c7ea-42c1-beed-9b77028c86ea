<template>
  <div class="diy-member">
    <div class="mobile-box" :data-theme="dataTheme" :style="themeStyleVars">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'MobileBox',
  props: {
  },
  components: {
  },
  data() {
    return {
      dataTheme: 'default',
      themeStyleVars: '--THEME-COLOR:#A1EA2B'
    }
  },
  watch: {
    theme1: {
      handler(val) {
        this.setIno(val)
      },
      immediate: true
    }
  },
  computed: {
    ...mapState('diy', ['theme1'])
  },
  created() {
  },
  methods: {
    setIno(info) {
      const rgbInfo = this.getRGB(info.fashion_color)
      this.dataTheme = info.background_color == 2 ? 'dark' : 'default'
      this.themeStyleVars = `--THEME-COLOR:#${info.fashion_color};--THEME-RGB:${rgbInfo.r},${rgbInfo.g},${rgbInfo.b};`
    },
    getRGB(color){
      let sColor = '#' + (color || 'a1ea2b').toLowerCase()
      //十六进制颜色值的正则表达式
      const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
      // 如果是16进制颜色
      if (sColor && reg.test(sColor)) {
        if (sColor.length === 4) {
          let sColorNew = '#'
          for (let i = 1; i < 4; i += 1) {
            sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
          }
          sColor = sColorNew
        }
        //处理六位的颜色值
        let sColorChange = []
        for (let i = 1; i < 7; i += 2) {
          sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
        }
        return {
          r: sColorChange[0],
          g: sColorChange[1],
          b: sColorChange[2],
        }
      }
    },
  }
}
</script>

<style lang="less" scoped>
.diy-member {
  background-image: url(~assets/img/diy/xsmax.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 419px;
  height: 804px;
}
.mobile-box {
  margin: 30px 44px;
  width: 750px;
  height: 1520px;
  border-radius: 44px;
  overflow: hidden;
  zoom: 0.5;
}
</style>