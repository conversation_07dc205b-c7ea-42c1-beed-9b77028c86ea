<template>
<PageBox :type="7">
 <RightBox>
    <div slot="title">教练称谓</div>
    <div class="coach-tips">
      修改后影响会员端教练列表页面顶部导航栏的称谓显示
    </div>
    <Form
      :model="postData"
      :label-width="80"
    >
      <FormItem label="教练称谓：">
       <Input
        style="width: 150px"
        v-model="postData.appellation"
        placeholder="请填写名称"
        :maxlength="5"
      >
        <span slot="append">{{postData.appellation.length}}/5</span>
      </Input>
      </FormItem>
    </Form>
  </RightBox>
</PageBox>
</template>

<script>
import { mapState } from 'vuex';
import RightBox from './RightBox'
import PageBox from './PageBox'
export default {
  name: 'DiyPageCoach',
  components: {
    PageBox,
    RightBox
  },
  computed: {
    ...mapState('diy', ['theme7']),
    postData: {
      get() {
        return this.theme7
      },
      set(value) {
        this.$store.commit('diy/UPDATE_DIY_THEME', {
          value,
          type: 7
        })
      }
    },
  },
  data() {
    return {
    }
  }
}
</script>
<style lang="less" scoped>
.coach-tips {
  color: #F59A23;
  margin-bottom: 20px;
  font-size: 14px;
}
</style>

