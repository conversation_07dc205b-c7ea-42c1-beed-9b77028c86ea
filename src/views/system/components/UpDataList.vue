<template>
  <div class="table-wrap">
    <div class="con-box-wrap">
      <Alert class="con-info top" type="warning">
        <div>尊敬的客户，数据导入功能开放的时间为：20:00-08:00， 13:00-15:00。</div>
        <div>您在使用数据导入功能时，请严格按照我们的数据格式标准准备数据，如果您对数据准备标准和数据导入功能有不清楚的地方，请寻求勤鸟客服的帮助。</div>
        <div>如果您需要我们提供批量数据处理和批量数据清除等服务，我们将按1000元/每次/每店的标准收取技术服务费。</div>
      </Alert>
      <h4 class="con-box-title">基础数据导入</h4>
      <Alert class="con-info" type="warning">导入前请先创建好所有门店</Alert>
      <div class="con-box">

        <div class="bus-box">
          <p class="box-tit">单店卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=singleCard" :data="{function: 'singleCard'}"/>
        </div>

        <div class="bus-box">
          <p class="box-tit">通卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=uniCard" :data="{function: 'uniCard'}"/>
        </div>



        <div class="bus-box">
          <p class="box-tit">工作人员</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=staff" :data="{function: 'staff'}"/>
        </div>

        <div class="bus-box">
          <p class="box-tit">会员</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=user" :data="{function: 'user'}"/>
        </div>



      </div>
      <h4 class="con-box-title">进阶数据导入</h4>
      <Alert class="con-info" type="warning">导入前请先确保基础数据已导入</Alert>
      <div class="con-box">

        <div class="bus-box">
          <p class="box-tit">签到</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=sign" :data="{function: 'sign'}"/>
        </div>

        <div class="bus-box">
          <p class="box-tit">私教消课</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=ptSign" :data="{function: 'ptSign'}"/>
        </div>

         <div class="bus-box">
          <p class="box-tit">单店购卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=buySingleCard" :data="{function: 'buySingleCard'}"/>
        </div>

        <div class="bus-box">
          <p class="box-tit">会籍通卡购卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=buyUniCard" :data="{function: 'buyUniCard'}"/>
        </div>
        <div class="bus-box">
          <p class="box-tit">私教购卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=buyPtCard" :data="{function: 'buyPtCard'}"/>
        </div>
        <div class="bus-box">
          <p class="box-tit">私教包月购卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=buyPtTimeCard" :data="{function: 'buyPtTimeCard'}"/>
        </div>
        <div class="bus-box">
          <p class="box-tit">私/泳教通卡购卡</p>
           <UpExcel template-path="/Web/Excel/getTpl?type=buyPtUniCard" :data="{function: 'buyPtUniCard'}"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBaseUrl } from 'utils/config'
import UpExcel from './UpExcel'
  export default {
    name: 'UpDataList',
    data() {
      return {
        total: 0,
        uploadUrl: getBaseUrl() + '/Admin/Public/upload',
        postData: {
          search: '',
          inventoryCode: '',
          type: '',
          page_no: 1,
          page_size: 10
        }
      };
    },
    components: {
      UpExcel
    },
    computed: {},
    created() {
    },
    methods: {

    },
  };
</script>

<style lang="less" scoped>
.con-box-wrap {
  width: 100%;
  margin-top: 8px;
  font-size: 14px;
  .con-info {
    width: 300px;
    margin-left: 40px;
    &.top {
      width: 952px;
      line-height: 1.4;
    }
  }
  .con-box-title {
    padding-left: 20px;
    line-height: 40px;
  }
  .con-box {
    padding: 16px 40px 0;
    margin-bottom: 8px;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
  }
  .bus-box {
    position: relative;
    width: 220px;
    height: 150px;
    padding: 8px 16px;
    border: 2px solid rgba(207, 246, 244, 1);
    border-radius: 6px;
    margin-bottom: 16px;
    margin-right: 24px;
    text-align: center;
  }
  .box-tit {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 16px;
    margin-bottom: 16px;
    text-align: left;
  }


}
</style>
