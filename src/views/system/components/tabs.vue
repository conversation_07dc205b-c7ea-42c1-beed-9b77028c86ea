<template>
    <div class="tabs-content">
        <!-- 顶部菜单列表 -->
        <div class="tabs-title">
            <div v-for="(item,index) in list" :key="`tabs-${index}`">
                <img :src="active===index?item.iconSel:item.icon" />
                <span>{{item.name}}</span>
            </div>
        </div>
        <!-- 预留插槽:自定义内容 -->
        <div class="tabs-slot">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
  name: 'tabsTitle',
  props: {
    list: {
      type: Array,
      default: []
    },
    active: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {}
  },
  created() { },
  mounted() { },
  methods: { },
}
</script>
<style lang="less">
    .tabs-content{
        width: 100%;
        height: 100%;
    }
    .tabs-slot{
        padding: 20px;
    }
</style>