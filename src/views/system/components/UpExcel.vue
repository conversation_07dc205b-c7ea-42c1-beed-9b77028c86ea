<template>
  <div class="up-excel">
    <div v-if="percent === -1 || percent>=100">
      <Button class="mb" icon="md-download" @click="handleDownload">下载模板</Button>
      <Upload
        id="uploader"
        :action="uploadUrl"
        :data="data"
        :show-upload-list="false"
        :format="['xls','xlsx']"
        accept=".xls, .xlsx"
        ref="upload"
        :with-credentials = "true"
        :max-size="100*1024"
        :disabled="disabled"
        :before-upload="handleBeforeUpload"
        :on-success="handleSuccess"
        :on-format-error="handleFormatError"
        :on-exceeded-size="handleMaxSize"
      >
        <Button icon="ios-cloud-upload-outline" :loading="disabled">上传数据</Button>
      </Upload>
    </div>
    <i-circle v-else :percent="percent" :stroke-color="color" :size="90">
      <Icon v-if="percent == 100" type="ios-checkmark" size="60" style="color:#5cb85c"></Icon>
      <span v-else style="font-size:24px">{{ percent }}%</span>
    </i-circle>
  </div>
</template>
<script>
import { getBaseUrl } from "utils/config";
export default {
  name: "UpExcel",
  data() {
    return {
      uploadUrl: getBaseUrl() + "/Web/ExcelOptimize/newImportExcel",
      btnLoading: false,
      disabled: false,
      percent: -1,
      timeOut: null
    };
  },
  props: {
    templatePath: {
      type: String
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    color() {
      let color = "#2db7f5";
      if (this.percent == 100) {
        color = "#5cb85c";
      }
      return color;
    }
  },
  mounted() {},
  beforeDestroy() {
    this.timeOutClear();
  },
  methods: {
    handleSuccess(res, file) {
      this.disabled = false
      if (res.errorcode === 10001) {
        this.$Modal.confirm({
          title: "提示",
          content: res.errormsg,
          onOk: () => {
            window.open(res.data.error_url);
          },
          onCancel() {}
        });
      } else if (res.errorcode !== 0) {
        this.$Message.error(res.errormsg);
      } else {
        this.getUploadResult({
          key: res.data.key,
          batch_no: res.data.batch_no
        });
      }
    },
    getUploadResult(info) {
      this.percent = 0
      this.timeOut = setInterval(() => {
        this.$service
          .post("/Web/Excel/getResult", info, {loading: false})
          .then(res => {
            if (res.data.errorcode === 0) {
              const resData = res.data.data
              // excel进程执行情况，3程序异常，2-执行成功，1-写入数据库失败，0执行中
              if (resData.status == 0) {
                this.percent = +(+resData.exec_count/+resData.total_count*100).toFixed(2)
              } else if (resData.status == 2) {
                this.percent = 100
                setTimeout(() => {
                  this.$Message.success('导入数据成功！');
                }, 500);
                this.timeOut()
              } else if (resData.status == 1){
                this.timeOutClear()
                this.$Modal.confirm({
                  title: "提示",
                  content: resData.msg,
                  onOk: () => {
                    window.open(resData.error_url);
                  },
                  onCancel() {}
                });
              } else {
                this.$Message.error(resData.msg);
                this.timeOutClear()
              }
            } else {
              this.$Message.error(res.data.errormsg);
              this.timeOutClear()
            }
          })
          .catch(err => {
            this.timeOutClear();
          });
      }, 2000);
    },
    timeOutClear() {
      clearInterval(this.timeOut);
      this.timeOut = null
      this.percent = -1
    },
    handleFormatError() {
      this.disabled = false
      this.$Message.error("文件格式不正确，请上传xls、xlsx文件");
    },
    handleMaxSize() {
      this.disabled = false
      this.$Message.error("文件太大，不能超过 100M。");
    },
    handleBeforeUpload() {
      this.disabled = true
    },
    handleDownload() {
      window.open(getBaseUrl() + this.templatePath);
    }
  },
  watch: {}
};
</script>

<style lang='less' scoped>
.up-excel {
  width: 100%;
  height: 90px;
}
.mb {
  margin-bottom: 16px;
}
</style>
