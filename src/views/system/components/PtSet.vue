<template>
   <div class="panel" slot="content">
        <Row class="row-lh">
          <Col span="" offset="1">
            <Checkbox :true-value="1" :false-value="0" v-model="card_reservation" :disabled="!isEditState">无有效会籍卡，会员不能正常预约{{isSwim?'泳教':'私教'}}课</Checkbox>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="" offset="1">
            <RadioGroup v-model="assignCoach">
              <Radio label="0" :disabled="!isEditState">会员可预约所有能教授课程的教练</Radio>
              <Radio label="1" :disabled="!isEditState">会员仅可预约指定的上课教练</Radio>
            </RadioGroup>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="" offset="1"> 可约
          <InputNumber size="small" :max="5" :min="1" v-model="appointmentDays" :disabled="!isEditState"></InputNumber>天内的{{isSwim?'泳教':'私教'}}课（最多5天） 需提前
          <InputNumber size="small" :max="9999" :min="1" v-model="advanceAppointmentMinutes" :disabled="!isEditState"></InputNumber>分钟进行预约 开课前
          <InputNumber size="small" :max="180" :min="1" v-model="stopCancelAppointmentMinutes" :disabled="!isEditState"></InputNumber>分钟内不能进行取消预约
          </Col>
        </Row>
        <Row class="row-lh">
          <Col offset="1"> 每天
          <TimePicker
            v-model="schedule_begin_time"
            style="width: 120px"
            :editable="false"
            :disabled="!isEditState"
            :clearable="false"
            placement="top"
            format="HH:mm"
            placeholder="请选择" />
          点后可以约课
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <Checkbox v-model="payFee" :disabled="!isEditState">会员爽约依然扣除{{isSwim?'泳教':'私教'}}费用</Checkbox>
          <Checkbox v-if="payFee" v-model="is_miss_toll_private"  :true-value="1" :false-value="0" :disabled="!isEditState">爽约课时不计入教练课时</Checkbox>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="" offset="1"> 教练每日预约上限
          <InputNumber size="small" :max="20" :min="1" v-model="upperLimitPerDay" :disabled="!isEditState"></InputNumber>节数 教练的每种课程对于同一会员可约
          <InputNumber size="small" :max="20" :min="1" v-model="upperLimitOfSamePerson" :disabled="!isEditState"></InputNumber>节
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="" offset="1">
            <span>预约最小时间单位</span>
            <RadioGroup v-model="schedule_time_type">
              <Radio label="1" :disabled="!isEditState">10分钟</Radio>
              <Radio label="2" :disabled="!isEditState">15分钟</Radio>
              <Radio label="3" :disabled="!isEditState">20分钟</Radio>
              <Radio label="0" :disabled="!isEditState">30分钟</Radio>
            </RadioGroup>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1"> 工作时间
          <Button type="text" icon="md-add" @click="handleAddTimeClick" :disabled="!isEditState">新增</Button>
          </Col>
        </Row>
        <Row class="row-lh" v-for="(time, index) in timeList" :key="index">
          <Col span="22" offset="1">
          <TimePicker :value="time" :editable="false" @on-change="handleTimeChange" @on-ok="handleTimeOK(index)" @on-clear="handleTimeClear(index)" @on-open-change="handleTimeOpenOrClose(index)" type="timerange" placement="top-start" placeholder="选择时间" format="HH:mm" :steps="[1, 10]" :disabled="!isEditState" style="width: 168px" confirm></TimePicker>
          <Button type="text" icon="ios-trash" @click="handleDeleteTimeClick(index)" :disabled="!isEditState">删除</Button>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <Checkbox v-model="sendMsg" :disabled="!isEditState">预约成功信息推送中隐藏会员的电话号码</Checkbox>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <Checkbox v-model="stopAppointment" :disabled="!isEditState">暂停所有教练约课（暂停期间会员无法在会员端预约{{isSwim?'泳教':'私教'}}课）</Checkbox>
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <Button v-if="!isEditState" type="success" style="background:#5fb75d;" @click="isEditState=true">编辑</Button>
          <Button v-if="isEditState" type="success" style="background:#5fb75d;" @click="saveCoachSettings">保存</Button>
          <Button v-if="isEditState" style="margin-left: 8px" @click="handleCancelEdit">取消</Button>
          </Col>
        </Row>
      </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'PtSet',
  components: {},
  data() {
    return {
      tempTime: [],
      timeList: [[]],
      isEditState: false,
      card_reservation: 0,
      is_miss_toll_private: 0,
      schedule_begin_time: '00:00',
      assignCoach: '0',
      schedule_time_type: '0',
      appointmentDays: 3,
      advanceAppointmentMinutes: 30,
      stopCancelAppointmentMinutes: 30,
      payFee: true,
      upperLimitPerDay: 5,
      upperLimitOfSamePerson: 2,
      sendMsg: true,
      stopAppointment: false
    }
  },
  props: {
    isSwim: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  created() {
    this.getCoachSettings()
  },
  methods: {
    addOneHour(hhmm) {
      const hm = hhmm.split(':')
      let hour = hm[0]
      hour = parseInt(hour) + 1
      if (hour < 24) {
        return this.getDoubleBit(hour) + ':' + hm[1]
      } else {
        return '23:59'
      }
    },
    getDoubleBit(num) {
      if (num < 10) {
        return '0' + num
      } else {
        return num
      }
    },
    getDoubleDate(index) {
      if (index === -1 || this.timeList[index].length === 0) {
        const today = new Date()
        const hour = today.getHours()
        const minute = today.getMinutes()
        const date = this.getDoubleBit(hour) + ':' + this.getDoubleBit(minute)
        this.timeList.push([date, date])
      } else {
        const beginDate = this.timeList[index][1]
        let endDate = this.timeList[index][1]
        endDate = this.addOneHour(endDate)
        this.timeList.push([beginDate, endDate])
      }
    },
    handleAddTimeClick() {
      if (this.timeList.length < 5) {
        const idx = this.timeList.length - 1
        this.getDoubleDate(idx)
      } else {
        this.$Message.error('最多添加5条!')
      }
    },
    handleDeleteTimeClick(index) {
      this.timeList.splice(index, 1)
    },
    handleTimeChange(valArr) {
      this.tempTime = valArr
    },
    handleTimeOK(index) {
      this.timeList[index] = this.tempTime
    },
    handleTimeClear(index) {
      this.timeList[index] = []
    },
    handleTimeOpenOrClose(index) {
      if (this.tempTime.length === 0) {
        // start to.
        // nothing to do.
      } else if (this.tempTime == this.timeList[index]) {
        // click the ok button over.
        this.tempTime = []
      } else if (this.timeList[index].length === 0) {
        // click the cancel button over
        this.tempTime = []
      } else {
        // don't have click any other button.
        this.timeList[index] = this.tempTime
        this.tempTime = []
      }
    },
    handleCancelEdit() {
      this.isEditState = false
      this.getCoachSettings()
    },
    getCoachSettings() {
      return this.$service
        .post('/Web/Business/get_bus_coach_setting', {
          is_swim: this.isSwim ? 1 : 0
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data.info
            this.card_reservation = Number(resData.card_reservation)
            this.is_miss_toll_private = Number(resData.is_miss_toll_private)
            this.schedule_begin_time = resData.schedule_begin_time || '00:00'
            this.assignCoach = resData.member_reservation_class
            this.schedule_time_type = resData.schedule_time_type
            this.appointmentDays = parseInt(resData.reservation_prior_days) || ''
            this.advanceAppointmentMinutes = parseInt(
              resData.reservation_prior_minute
            ) || ''
            this.stopCancelAppointmentMinutes = parseInt(
              resData.stop_prior_minute
            ) || ''
            this.payFee = resData.is_miss_toll == 1
            this.upperLimitPerDay = parseInt(resData.coach_reservation_max) || ''
            this.upperLimitOfSamePerson = parseInt(
              resData.reservation_class_num
            ) || ''
            this.sendMsg = resData.user_phone_hide == 1
            this.stopAppointment = resData.user_suspend_reservation_class == 1
            this.timeList = []
            if (
              Array.isArray(resData.coach_working_hours) &&
              resData.coach_working_hours.length > 0
            ) {
              const self = this
              setTimeout(() => {
                self.timeList = resData.coach_working_hours
              }, 300)
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    saveCoachSettings() {
      const url = this.isSwim
        ? '/Web/Business/update_bus_swim_coach_setting'
        : '/Web/Business/update_bus_coach_setting'
      return this.$service
        .post(url, {
          is_swim: this.isSwim ? 1 : 0,
          card_reservation: this.card_reservation,
          schedule_begin_time: this.schedule_begin_time,
          member_reservation_class: this.assignCoach,
          schedule_time_type: this.schedule_time_type,
          reservation_prior_days: this.appointmentDays,
          reservation_prior_minute: this.advanceAppointmentMinutes,
          stop_prior_minute: this.stopCancelAppointmentMinutes,
          is_miss_toll: this.payFee ? '1' : '0',
          is_miss_toll_private: this.is_miss_toll_private,
          coach_reservation_max: this.upperLimitPerDay,
          reservation_class_num: this.upperLimitOfSamePerson,
          user_suspend_reservation_class: this.stopAppointment ? '1' : '0',
          user_phone_hide: this.sendMsg ? '1' : '0',
          coach_working_hours: this.timeList
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            const self = this
            setTimeout(() => {
              self.isEditState = false
            }, 1000)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.row-lh {
  height: 52px;
  display: flex;
  align-items: center;
  .ivu-col {
    display: flex;
    align-items: center;
  }
}
</style>
