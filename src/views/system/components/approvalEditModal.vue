<template>
  <Modal
    :title="`${ readonly ? '查看' : '编辑' }审批人`"
    :value="show"
    :width="readonly ? 410 : 800"
    :mask-closable="readonly"
    @on-cancel="handleClose"
  >
    <div class="modal-body">
      <div v-show="!readonly" class="left-wrapper">
        <div class="top-row">
          <Input
            v-model.trim="search"
            style="margin-right: 15px"
            placeholder="姓名/手机号"
            clearable
            @on-clear="(search = leftState.search = '', handleSearch())"
            @on-enter="handleSearch" />
          <!-- 获取当前场馆账号列表需要拿到对应的region_bus -->
          <AdminRegion v-show="false" :multiple="false" :shouldDefault="1" v-model="leftState.region_bus" />
          <Button type="success" @click="handleSearch">搜索</Button>
        </div>
        <Scroll
          ref="testRef"
          :on-reach-bottom="leftState.isEnd ? undefined : handleReachEdge"
          :height="400"
          :distance-to-edge="-150">
          <ul class="list-box" >
            <li class="item-row head">
              <span class="th" style="width:34px;">#</span>
              <span class="th">姓名</span>
              <span class="th">手机号</span>
            </li>
            <template v-if="leftState.list.length">
              <li
                class="item-row"
                v-for="item in leftState.list" :key="item.admin_id"
                @click="handleSelectUser(item)">
                <Checkbox
                  :value="item.isChecked" style="margin:0 10px;"/>
                <div class="td">
                  <img v-if="item.is_open_boss == '1'" src="../../../assets/img/sd_03.png" class="boss-icon"/>
                  <span class="name text_overflow" :title="item.realname">{{ item.realname }}</span>
                </div>
                <span class="td phone">{{ item.phone }}</span>
              </li>
              <li v-show="leftState.isEnd" class="item-row no-more">
                <span style="flex:1;">没有更多了</span>
              </li>
            </template>
            <li v-else class="item-row no-data">
              <span style="flex:1;">暂无数据</span>
            </li>
          </ul>
        </Scroll>
      </div>
      <div class="right-wrapper">
        <div class="top-row">
          <img class="added-img" src="../../../assets/img/approval-edit.png" alt=""/>
          <span style="font-weight:bold;font-size:15px;">已有审批账号</span>
        </div>
        <ul class="list-box" style="height:400px;overflow-y:scroll;">
          <li class="item-row head">
            <span class="th">姓名</span>
            <span class="th">手机号</span>
            <span v-if="!readonly" class="th" style="width:52px;text-align:center;">操作</span>
          </li>
          <template v-if="rightState.list.length">
            <li class="item-row" v-for="(item, index) in rightState.list" :key="item.admin_id">
              <div class="td">
                <img v-if="item.is_open_boss == '1'" src="../../../assets/img/sd_03.png" class="boss-icon"/>
                <span class="name text_overflow" :title="item.realname">{{ item.realname }}</span>
              </div>
              <span class="td phone">{{ item.phone }}</span>
              <span v-if="!readonly" class="del-btn" @click="handleDelUser(item, index)" >删除</span>
            </li>
          </template>
          <li v-else class="item-row no-data">
            <span style="flex:1;">暂无数据</span>
          </li>
        </ul>
      </div>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button v-show="!readonly" type="success" @click="hansleConfirm">确定</Button>
      <Button @click="handleClose">{{ !readonly ? '取消' : '关闭' }}</Button>
    </div>
  </Modal>
</template>

<script>
  import AdminRegion from 'components/form/adminRegion.vue';
  const icon = require('../../../assets/img/sd_03.png')

  export default {
    name: 'approvalEditModal',
    components: {
      AdminRegion
    },

    props: {
      show: {
        type: Boolean,
        required: true
      },
      itemData: {
        type: Object,
        required: true
      },
      readonly: {
        type: Boolean,
        default: true
      },
    },
    data() {
      return {
        search: '',
        leftState: {
          list: [],
          search: '',
          region_bus: '',
          page_no: 1,
          page_size: 12,
          total: 0,
          isEnd: false,
        },
        rightState: {
          list: [],
        },
        addedIds: [],
      };
    },

    watch: {
      show(val) {
        if (val) {
          !this.readonly && this.getAdminList()
          const list = [];
          const ids = [];
          this.itemData.approve_admin_ids.forEach(({ id, ...rest }) => {
            list.push({ admin_id: id, isChecked: true, ...rest });
            ids.push(id)
          });
          this.rightState.list = list;
          this.addedIds = ids;
          /*
          const { testRef } = this.$refs
          const { scrollContainer } = testRef.$refs
          scrollContainer.addEventListener('scroll', () => {
            console.log('scroll');
          })
          scrollContainer.addEventListener('wheel', (event) => {
            const wheelDelta = event.wheelDelta ? event.wheelDelta : -(event.detail || event.deltaY);
            console.log('wheel', wheelDelta);
          })
          scrollContainer.addEventListener('touchstart', () => {
            console.log('touchstart');
          }) */
        }else {
          this.search = '';
          this.leftState.list = [];
          this.leftState.search = '';
          this.leftState.page_no = 1;
          this.leftState.isEnd = false;
        }
      },
    },

    methods: {
      getAdminList() {
        const { search, region_bus, page_no, page_size } = this.leftState;
        const params = {
          search, region_bus, page_no, page_size
        }
        /* 该账号列表获取接口，不受是否有账号权限控制 */
        return this.$service.post('/Web/Admin/get_admin_list_approve', params).then(res => {
          if (res.data.errorcode === 0) {
            const { list: resList } = res.data.data;
            const {
              leftState: { list },
              rightState: { list: rightList },
              addedIds
            } = this;
            const dataList = resList.filter(v => {
              /* v1 过滤掉关闭的账号 */
              // const closed = v.status != 0;
              /* v1 过滤掉已添加 */
              // const added = rightList.some(k => v.admin_id == k.admin_id);
              /* v3 过滤掉rightList和addedIds 共有的 */
              return !rightList.some(k => v.admin_id == k.admin_id) || !addedIds.includes(v.admin_id);
            })
            dataList.forEach(v => {
              /* v2 选中已添加 */
              v.isChecked = rightList.some(k => v.admin_id === k.admin_id)
            });

            /* 列表赋值 */
            const startIndex = page_no === 1 ? 0 : list.length;
            list.splice(startIndex, 0, ...dataList);
            this.leftState.isEnd = resList.length < page_size;
            /* 获取足够量的初始列表。因为过滤后的列表高度不确定，高度不足会导致onWheel无法触发Scroll组件的onCallback方法 */
            if (list.length <= page_size && !this.leftState.isEnd) {
              this.handleReachEdge()
            }
            // this.leftState.total = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err);
          this.leftState.page_no !== 1 && (this.leftState.page_no -= 1);
        })
      },
      /* 获取下一页数据 */
      handleReachEdge() {
        this.leftState.page_no += 1
        return this.getAdminList()
      },
      handleSearch() {
        this.leftState.search = this.search;
        this.leftState.list = [];
        this.leftState.isEnd = false;
        this.leftState.page_no = 1;
        this.getAdminList()
      },
      handleSelectUser(item) {
        /* v2 */
        const { list } = this.rightState;
        if (item.isChecked) {
          item.isChecked = false;
          const index = list.findIndex(v => v.admin_id == item.admin_id);
          index !== -1 && list.splice(index, 1)
        } else {
          item.isChecked = true;
          list.unshift(item)
        }
        /* v1 */
        // this.leftState.list.splice(index, 1)
        // this.rightState.list.unshift(item)
        // this.leftState.list.length < 15 && !this.leftState.isEnd && this.handleReachEdge()
      },
      /* handleSelectAll() {
        this.rightState.list.unshift(...this.leftState.list);
        this.leftState.list = [];
        !this.leftState.isEnd && this.handleReachEdge()
      }, */
      handleDelUser(item, i) {
        this.rightState.list.splice(i, 1)
        const { list } = this.leftState;
        const index = list.findIndex(v => v.admin_id == item.admin_id)
        /* v3... */
        index === -1 ? list.unshift({ ...item, isChecked: false }) : (list[index].isChecked = false)
        /* v2 */
        // index !== -1 && (list[index].isChecked = false)
        /* v1 */
        // this.leftState.list.unshift(item)
      },
      hansleConfirm() {
        const { approve_type, status, auto_approve } = this.itemData;
        /* 因为和外面修改状态是同一个接口，且所有参数都是必传 */
        const params = {
          approve_type, // 审批类型 1购卡 2跨店购卡 3续卡 4升卡 5销卡 6拆分 7请假 8转卡 9补卡 10编辑会员卡 11增体验卡/课 12会员信息 13会员头像
          status,       // 审批状态 0关闭 1开启
          auto_approve, // 自动审批 0关闭/不支持 1开启
          approve_admins: this.rightState.list.map(v => v.admin_id) // 审批人ids
        };
        this.$service.post('/Web/ApproveSet/set_approve_rule', params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.handleClose()
            this.$emit('refresh')
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })

      },
      handleClose() {
        this.$emit('update:show', false)
      }
    }
  };
</script>

<style lang="less" scoped>
.modal-body {
  display: flex;
  justify-content: space-between;

  .left-wrapper, .right-wrapper {
    position: relative;
    padding: 20px;
    flex: 1;
    border-radius: 5px;
    background-color: #fafaff;
  }
  .left-wrapper {
    margin-right: 20px;
    .item-row:first-child {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 324px;
      background-color: #f7f7f7;
    }
    .item-row {
      cursor: pointer;
    }
  }
  .right-wrapper .item-row:first-child {
    position: absolute;
    top: 68px;
    left: 20px;
    width: 324px;
    background-color: #f7f7f7;
  }

  .top-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    min-height: 32px;
    .added-img {
      margin: 0 8px;
      width: 30px;
      border-radius: 50%;
    }
  }

  .pagination-row {
    margin-top: 20px;
    height: 40px;
    text-align: right;
  }
  /deep/.ivu-scroll-loader {
    display: none;
  }
}

.list-box {
  padding-top: 40px;
  .item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    text-align: center;
    background-color: #fff;
    border-bottom: 1px solid #e8eaec;
    user-select: none;

    .th, .td {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 42%;
      font-size: 13px;
    }
    .th {
      font-weight: bold;
      font-size: 15px;
    }
    &.head {
      z-index: 1;
    }
    &.no-more {
      color: #979faf;
      background-color: transparent;
      border-bottom: none;
    }
    .no-data {
      font-size: 14px;
    }
    .del-btn {
      margin: 0 9px;
      width: 34px;
      color: #d9544f;
      cursor: pointer;
    }
  }

  // .checkbox-inner {
  //   margin: 0 10px;
  //   width: 14px;
  //   height: 14px;
  //   border: 1px solid #dcdee2;
  //   border-radius: 2px;
  //   background-color: #fff;
  //   cursor: pointer;
  //   &:hover {
  //     border-color: #bcbcbc;
  //   }
  // }
  .boss-icon {
    margin-right: 8px;
    width: 14px;
  }

  .name {
    max-width: 110px;
  }
}

</style>
