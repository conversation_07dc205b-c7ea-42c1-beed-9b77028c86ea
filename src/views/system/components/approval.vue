
<style lang="less" scoped>
  .ivu-form {
    display: flex;
    flex-wrap: wrap;
  }
  .ivu-form-item {
    width: 50%;
  }
  .modal-checkbox {
    display: flex;
    flex-wrap: wrap;
    padding: 0 30px;
    .people-item {
      width: 40%;
    }
  }
  .people-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 20px;
    .avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      margin-right: 10px;
    }
    .modal-avatar {
      margin-left: 20px;
    }
  }
  .approval {
    padding: 30px;
    .box {
      border: 1px solid #eee;
      padding: 20px;
    }
    .people {
      padding: 20px 0;
      display: flex;
      align-items: flex-start;
      .title {
        padding: 0 35px;
        line-height: 44px;
      }
      .items {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
      }
    }
    .button {
      margin-top: 30px;
      margin-left: 30px;
    }
  }
</style>

<template>
  <div class="approval">
    <div class="box">
      <Form :label-width="120" class="modal-form">
        <FormItem v-for="(item, index) in items" :key="item.key" :label="item.name + '审批'">
          <i-switch :disabled="!isEdit" true-value="1" false-value="0" v-model="item.value"></i-switch>
        </FormItem>
      </Form>
      <div class="people">
        <div class="title">审批人</div>
        <div class="items">
          <div v-for="(item, index) in people" :key="index" class="people-item">
            <img class="avatar" :src="item.avatar" alt="">
            <div>{{ item.realname || item.username || item.nickname}}</div>
          </div>
          <div class="people-item">
            <Button v-if="isEdit" @click="handleEditApprover" type="primary">编辑审批人</Button>
          </div>
        </div>
      </div>
      <p style="color: red">审批人需要使用BOSS端并且和本场馆进行绑定才能进行审批</p>
    </div>
    <div class="buttons">
      <Button @click="isEdit = true" v-show="!isEdit" type="success">编辑</Button>
      <template v-if="isEdit">
        <Button type="success" @click="saveEdit">保存</Button>
        <Button @click="cancelEdit">取消</Button>
      </template>
    </div>
    <Modal title="编辑审批人" v-model="editApprover">
      <CheckboxGroup v-model="selectApprover" class="modal-checkbox">
        <Checkbox v-for="item in approvers" :key="item.openid" :label="item.openid" class="people-item">
          <img class="avatar modal-avatar" :src="item.avatar" alt="">
          <div>{{item.realname || item.username || item.nickname}}</div>
        </Checkbox>
      </CheckboxGroup>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveEditApprover">确定</Button>
        <Button @click="cancelEditApprover">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import avatar from '../../../assets/img/femalecoach.png';
  const ID = 'openid';
  export default {
    name: 'approvalSetting',
    data() {
      return {
        items: [
          { name: '购卡', key: 'card_buy', value: '1' },
          { name: '跨店购卡', key: 'card_help_sale', value: '1' },
          { name: '续卡', key: 'card_renew', value: '1' },
          { name: '升卡', key: 'card_change', value: '1' },
          { name: '销卡', key: 'card_delete', value: '1' },
          { name: '拆分', key: 'card_split', value: '1' },
          { name: '请假', key: 'card_pause', value: '1' },
          { name: '转卡', key: 'card_trans', value: '1' },
          { name: '编辑会员卡', key: 'card_edit', value: '0' },
          { name: '补卡', key: 'card_makeup', value: '0' },
          { name: '赠体验卡/课', key: 'card_experience_class', value: '0' }
        ],
        isEdit: false,
        settings: {
          card_buy: '0',
          card_help_sale: '0',
          card_renew: '0',
          card_change: '0',
          card_split: '0',
          card_delete: '0',
          card_pause: '0',
          card_trans: '0',
          card_edit: '0',
          card_experience_class: '0',
          card_makeup: '0'
        },
        people: [],
        approvers: [],
        editApprover: false,
        selectApprover: []
      };
    },
    created() {
      this.getSetting();
    },
    methods: {
      async handleEditApprover() {
        await this.getApprovers();
        this.selectApprover = this.people.map(item => item[ID]);
        this.editApprover = true;
      },
      calItems() {
        this.items.forEach(item => {
          item.value = this.settings[item.key];
        });
      },
      saveEditApprover() {
        this.people = this.approvers.filter(item => this.selectApprover.includes(item[ID]));
        this.editApprover = false;
      },
      cancelEdit() {
        this.getSetting();
        this.isEdit = false;
      },
      cancelEditApprover() {
        this.editApprover = false;
      },
      getSetting() {
        const url = '/Web/approve/get_setting_info';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.settings = data.switchs;
              this.people = data.approvers;
              this.calItems();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getApprovers() {
        const url = '/Web/approve/get_can_approvers';
        return this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.approvers = data.list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      saveEdit() {
        const url = '/Web/approve/save_approve_setting';
        const postData = {
          approver_ids: this.people.reduce((ids, item) => ids + item[ID] + ',', '')
        };
        this.items.forEach(item => {
          postData[item.key] = item.value;
        });
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.isEdit = false;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
