<template>
  <div class="panel" slot="content">
    <Form ref="goodsSettingForm" :model="invoiceSet" :label-width="60">
      <form-item>
        <radio-group v-model="invoiceSet.bill_type">
          <radio label="1">仅可开普票</radio>
          <radio label="2">可开专票与普票</radio>
        </radio-group>
      </form-item>
      <form-item>
         <Checkbox v-model="invoiceSet.is_edit" true-value="1" false-value="0">允许修改开票人</Checkbox>
      </form-item>
      <form-item>
        <Button type="success" @click="setData">保存</Button>
      </form-item>
    </Form>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Export from "src/components/Export";
export default {
  name: "NumberSet",
  components: {
    Export
  },
  data() {
    return {
      invoiceSet: {
        bill_type: '2',
        is_edit: '0'
      }
    };
  },
  computed: {
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      return this.$service
        .post("/Web/invoice/getSetting")
        .then(res => {
          if (res.data.errorcode == 0) {
            this.invoiceSet = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    setData() {
      return this.$service
        .post("/Web/invoice/setting", this.invoiceSet)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    }
  }
};
</script>
