
<style lang="less" scoped>
.dynamic-detail {
  .user {
    display: flex;
    align-items: center;
    .detail-avatar {
      width: 60px;
      height: 60px;
      border: 1px solid #dcdcdc;
      background-color: #ccc;
      border-radius: 50%;
      overflow: hidden;
    }
    > p {
      font-size: 14px;
      color: #333;
      padding-left: 20px;
    }
  }
  > p {
    padding: 10px;
    word-wrap: break-word;
  }
  .dynamic-img {
    padding: 20px 10px;
    min-height: 100px;
  }
}
</style>

<template>
  <div class="table-wrap dynamic-list" style="border-top: 0">
    <header style="border-bottom: 0">
      <Input v-model="search" placeholder="会员名称/电话" @on-enter="getList" />
      <Button type="success" @click="getList">搜索</Button>
    </header>
    <Table :columns="columns" :data="tableData" disabled-hover @on-selection-change="handleSelect" />
    <footer>
      <div class="left">
        <Button type="error" @click="handleDelete">批量删除</Button>
        <Button style="margin-left: 15px" @click="handleAbandon">会员禁言</Button>
      </div>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
    <Modal title="会员禁言" v-model="showAbandon">
      <Form :label-width="100" label-position="right" class="modal-form" style="padding-right: 40px">
        <FormItem label="搜索会员">
          <userSearch ref="userSearch" v-model="searchUserId" placeholder="姓名/电话" url="/Web/Member/search_all_member" />
        </FormItem>
      </Form>
      <footer slot="footer" class="modal-buttons">
        <Button type="success" @click="doAbandon(searchUserId, 1)" :disabled="!searchUserId">确定</Button>
        <Button @click="showAbandon = false">取消</Button>
      </footer>
    </Modal>
    <Modal title="动态详情" v-model="showDetail">
      <div class="dynamic-detail">
        <div class="user">
          <div class="detail-avatar">
            <img :src="detail.avatar" alt="" style="width: 100%; height: 100%">
          </div>
          <p>{{detail.username}}</p>
        </div>
        <p>{{detail.content}}</p>
        <div class="dynamic-img">
          <img style="max-width: 100%" :src="detail.image" alt="">
        </div>
      </div>
      <footer slot="footer"></footer>
    </Modal>
  </div>
</template>

<script>
import Image from 'assets/img/code.jpg';
import Avatar from 'assets/img/malecoach.png';
import { formatDate } from 'utils';
import userSearch from 'components/user/userSearch';

import pager from 'mixins/pager';
export default {
  name: 'dynamicList',
  mixins: [pager],
  components: { userSearch },
  data() {
    return {
      searchUserId: '',
      search: '',
      showAbandon: false,
      showDetail: false,
      selectedItem: [],
      selection: [],
      detail: {},
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '头像',
          key: 'avatar',
          width: 80,
          render: (h, params) => {
            const item = params.row;
            return <img style="width: 44px; height: 44px" src={item.avatar} />;
          }
        },
        {
          title: '会员名称',
          key: 'username'
        },
        {
          title: '发布时间',
          key: 'create_time'
        },
        {
          title: '动态内容',
          key: 'content',
          ellipsis: true,
          render: (h, params) => {
            const item = params.row;
            return (
              <div
                onClick={() => {
                  this.handleShowDetail(item.dynamic_id);
                }}
                class="link"
                style="overflow: hidden; text-overflow: ellipsis"
                title={item.content}>
                {item.content}
              </div>
            );
          }
        },
        {
          title: '动态图片',
          key: 'image',
          render: (h, params) => {
            const item = params.row;
            return (
              <div
                onClick={() => {
                  this.handleShowDetail(item.dynamic_id);
                }}
                style="height: 50px">
                <img style="max-width: 100%; max-height: 100%" src={item.image} />
              </div>
            );
          }
        },
        {
          title: '操作',
          key: 'operation',
          render: (h, params) => {
            const item = params.row;
            return (
              <div>
                <i-button
                  type="text"
                  disabled={item.not_send_dynamic == 1}
                  onClick={() => {
                    this.doAbandon(item.user_id);
                  }}>
                  禁言
                </i-button>
                <i-button
                  type="text"
                  class="button-text-red"
                  onClick={() => {
                    this.singleDelete(item.dynamic_id);
                  }}>
                  删除
                </i-button>
              </div>
            );
          }
        }
      ],
      tableData: []
    };
  },
  watch: {
    showAbandon(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.userSearch.$refs.signSelect.$refs.input.focus();
          this.$refs.userSearch.$refs.signSelect.$refs.input.click();
        });
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    handleAbandon() {
      this.showAbandon = true;
    },
    doAbandon(user_id, formSearch) {
      const url = '/Web/UserDynamic/set_not_send_dynamic';
      this.$service
        .post(url, { user_id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$Message.success(res.data.errormsg);
            this.showAbandon = false;
            if (formSearch) {
              this.searchUserId = '';
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    singleDelete(dynamic_id) {
      this.selection = [dynamic_id];
      this.handleDelete();
    },
    handleDelete() {
      const url = '/Web/UserDynamic/deleted_user_dynamic';
      this.$service
        .post(url, { dynamic_ids: this.selection })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleShowDetail(id) {
      this.detail = this.tableData.find(item => item.dynamic_id === id);
      this.showDetail = true;
    },
    handleSelect(selection) {
      this.selectedItem = selection;
      this.selection = selection.map(item => item.dynamic_id);
    },
    getList() {
      const url = '/Web/UserDynamic/get_user_dynamic_list';
      const { search, page: page_no, pageSize: page_size } = this;
      this.$service
        .post(url, { search, page_no, page_size })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list.map(item => {
              return {
                ...item,
                ...{
                  create_time: formatDate(new Date(item.create_time * 1000), 'MM-dd HH:mm')
                }
              };
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
