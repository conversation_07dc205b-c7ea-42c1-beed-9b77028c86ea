<template>
  <div class="table-wrap">
    <Modal :title="title" v-model="showModal">
      <Form :label-width="100" label-position="right" style="padding: 0 50px 0 20px" class="modal-form">
        <FormItem label="教室">
          <Input v-model="device.terminal_name" placeholder="请输入设备所在的教室" />
        </FormItem>
        <FormItem label="终端设备号">
          <Input v-model="device.terminal_id" placeholder="请输入终端设备号" />
        </FormItem>
      </Form>
      <footer slot="footer" class="modal-buttons">
        <Button type="success" @click="handleAdd('')">确定</Button>
        <Button @click="showModal = false">取消</Button>
      </footer>
    </Modal>
    <Table :columns="columns" :data="tableData" disabled-hover />
    <footer>
      <Button type="success" @click="clickAdd">添加设备</Button>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
import pager from 'mixins/pager';
export default {
  name: 'heartRateDevice',
  mixins: [pager],
  data() {
    return {
      device: {
        terminal_name: '',
        terminal_id: ''
      },
      showModal: false,
      title: '添加设备',
      columns: [
        {
          title: '教室',
          key: 'terminal_name'
        },
        {
          title: '终端设备号',
          key: 'terminal_id'
        },
        {
          title: '操作',
          key: 'room',
          render: (h, params) => {
            const item = params.row;
            return (
              <div>
                <i-button
                  type="text"
                  class="button-text-red"
                  onClick={() => {
                    this.handleAdd(item.id);
                  }}>
                  删除
                </i-button>
              </div>
            );
          }
        }
      ],
      tableData: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleEdit(id) {
      this.device = this.tableData.find(item => item.terminal_id === id);
      this.title = '编辑设备';
      this.showModal = true;
    },
    clickAdd() {
      this.title = '添加设备';
      this.showModal = true;
    },
    handleAdd(id = '') {
      const { terminal_name, terminal_id } = this.device;
      if (!id) {
        if (!terminal_name) {
          this.$Message.error('请填写教室');
          return false;
        }
        if (!terminal_id) {
          this.$Message.error('请填写设备编号');
          return false;
        }
      }
      const url = '/Web/GymsmartTerminal/set_bus_terminal';
      this.$service
        .post(url, { terminal_name, terminal_id, id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.getList();
            this.showModal = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getList() {
      const url = '/Web/GymsmartTerminal/get_bus_terminal';
      const postData = {
        page_no: this.page,
        page_size: this.pageSize
      }
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>

<style scoped>

</style>
