<template>
  <Collapse class="b-box" :value="collapseValue" @on-change="handleCollapseClick" accordion>
    <Panel name="cardSaleSettings" v-if="cardSaleSettingsPanel">
      卡课售卖设置
      <card-sale-settings slot="content" v-if="cardSaleSettingsShow"></card-sale-settings>
    </Panel>
    <Panel name="teamCourse" v-if="teamCourseShow">
      团操课设置
      <Form slot="content" :label-width="170" label-position="left" style="margin-left: 4%">
        <Alert type="warning" style="width:520px;" show-icon>爽约惩罚开启后，会员预约后必须要进行课程签到，第二天仍未签到皆视为爽约</Alert>
        <FormItem label="爽约惩罚">
          <i-switch v-model="missReservation" size="large" :disabled="!isEditState1"></i-switch>
        </FormItem>
        <FormItem label="爽约计算周期">
          <InputNumber :max="90" :min="1" v-model="lifecycle" :disabled="!missReservation||!isEditState1"></InputNumber>
          <span> 天</span>
        </FormItem>
        <FormItem v-for="(item, index) in teamList" :key="index">
          <p slot="label">{{lifecycle}}天内爽约 {{index+1}} 次禁止约课</p>
          <InputNumber class="shitIpt" :max="30" :min="1" :value="parseInt(item)" :disabled="!missReservation||!isEditState1"></InputNumber>
          <span> 天
            <Button v-if="isEditState1 && teamList.length>1" type="text" @click="handleDeleteListClick(index)">删除</Button>
          </span>
        </FormItem>
        <Button v-if="isEditState1 && missReservation" style="margin-bottom:10px;" type="primary" @click="handleAddListClick">添加</Button>
        <FormItem :label-width="0">
          <Checkbox v-model="courseState.is_limit_card" :true-value="1" :false-value="0" :disabled="!isEditState1">单张期限卡预约团课次数限制，</Checkbox>
          <InputNumber :max="9999" :min="1" v-model="courseState.limit_card_set.days" :disabled="!isEditState1" @on-blur="handleBlurCourse($event, courseState.limit_card_set, 'days')"/>
          <span> 天最多可预约 </span>
          <InputNumber :max="9999" :min="1" v-model="courseState.limit_card_set.times" :disabled="!isEditState1" @on-blur="handleBlurCourse($event, courseState.limit_card_set, 'times')"/>
          <span> 次</span>
        </FormItem>
        <FormItem :label-width="0">
          <Checkbox v-model="courseState.is_waitting_limit" :true-value="1" :false-value="0" :disabled="!isEditState1">候补团课次数限制，</Checkbox>
          <InputNumber :max="9999" :min="1" v-model="courseState.limit_times.days" :disabled="!isEditState1" @on-blur="handleBlurCourse($event, courseState.limit_times, 'days')"/>
          <span> 天最多可候补 </span>
          <InputNumber :max="9999" :min="1" v-model="courseState.limit_times.times" :disabled="!isEditState1" @on-blur="handleBlurCourse($event, courseState.limit_times, 'times')"/>
          <span> 次</span>
        </FormItem>
        <FormItem :label-width="0">
          <Checkbox v-model="courseState.is_confirm" :true-value="1" :false-value="0" :disabled="!isEditState1">候补确认，排队成功后会员需在</Checkbox>
          <InputNumber :max="9999" :min="1" v-model="courseState.confirm_time" :disabled="!isEditState1" @on-blur="handleBlurCourse($event, courseState, 'confirm_time')"/>
          <span> 分钟内确认 </span>
          <template v-if="waitting_count > 0">
            <Icon  type="ios-alert" size="16" color="#F59A23" style="margin:0 4px 0 10px;" />
            <span>当前有会员正在候补排队，不建议修改</span>
          </template>
        </FormItem>
        <FormItem :label-width="0">
          <Checkbox v-model="courseState.is_stop" :true-value="1" :false-value="0" :disabled="!isEditState1">候补暂停时间：</Checkbox>
          <TimePicker
            style="width: 168px"
            v-model="courseState.stop_time"
            format="HH:mm"
            type="timerange"
            placeholder="请选择"
            :editable="false"
            :disabled="!isEditState1"
            :clearable="false"
          />
          <template v-if="waitting_count > 0">
            <Icon  type="ios-alert" size="16" color="#F59A23" style="margin:0 4px 0 10px;" />
            <span>当前有会员正在候补排队，不建议修改</span>
          </template>
        </FormItem>
        <FormItem :label-width="0">
          <Checkbox v-model="courseState.is_send_msg" :true-value="1" :false-value="0" :disabled="!isEditState1">短信通知会员确认候补</Checkbox>
        </FormItem>
        <FormItem>
          <Button v-if="!isEditState1" type="success" style="background:#5fb75d;" @click="isEditState1=true">编辑</Button>
          <Button v-if="isEditState1" type="success" style="background:#5fb75d;" @click="saveTeamSettings">保存</Button>
          <Button v-if="isEditState1" style="margin-left: 8px" @click="handleCancelEdit1">取消</Button>
        </FormItem>
      </Form>
    </Panel>
    <Panel name="coachCourse" v-if="showCoach">
      私教课设置
      <PtSet slot="content" v-if="coachCourseShow" />
    </Panel>
    <Panel name="swimCoachCourse" v-if="showSwimCoach">
      泳教课设置
      <PtSet slot="content" v-if="swimCoachCourseShow" isSwim />
    </Panel>
    <Panel name="sign" v-if="signShow">
      签到设置
      <div class="panel" slot="content">
        <Row class="row-lh">
          <Col span="22" offset="1">
          <i-switch v-model="enterGym" size="large" :disabled="!isEditState3"></i-switch>
          储值卡可直接扫码扣费入场
          </Col>
        </Row>
        <Row v-if="enterGym" class="row-lh">
          <Col span="22" offset="1"> 储值卡单次进场扣费
          <InputNumber size="small" :max="999" :min="0" :step="0.1" v-model="enterFee" :disabled="!isEditState3"></InputNumber>
          元/次
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <i-switch v-model="autoStart" size="large" :disabled="!isEditState3"></i-switch>
          会员签到时可自行启用“请假或未激活”的会员卡
          </Col>
        </Row>
        <Row v-if="aroundGymShow" class="row-lh">
          <Col span="22" offset="1">
          <i-switch v-model="aroundGym" size="large" :disabled="!isEditState3"></i-switch>
          签退和扫码签到必须在场馆周围
          </Col>
        </Row>
        <Row v-if="aroundGym && aroundGymShow" class="row-lh">
          <Col span="22" offset="1">
          <Alert type="warning" style="width:520px;" show-icon>请确保场馆地图位置定位准确，地图位置可在“场馆”中设置</Alert>
          </Col>
        </Row>
        <Row v-if="aroundGym && aroundGymShow" class="row-lh">
          <Col span="22" offset="1"> 扫码签到范围限定在场馆定位半径
          <InputNumber :active-change="false" size="small" :max="3000" :min="200" v-model="aroundGymMeter" :disabled="!isEditState3"></InputNumber>
          米内（200米~3000米）
          </Col>
        </Row>
        <Row class="row-lh">
          <Col span="22" offset="1">
          <Button v-if="!isEditState3" type="success" style="background:#5fb75d;" @click="isEditState3=true">编辑</Button>
          <Button v-if="isEditState3" type="success" style="background:#5fb75d;" @click="saveSignSettings">保存</Button>
          <Button v-if="isEditState3" style="margin-left: 8px" @click="handleCancelEdit3">取消</Button>
          </Col>
        </Row>
      </div>
    </Panel>
    <Panel name="statistics" v-if="statisticsShow">
      获客来源设置
      <get-cust-res slot="content"></get-cust-res>
    </Panel>
    <Panel name="membershipTrack" v-if="membershipTrackShow">
      会籍端设置
      <membership-track slot="content"></membership-track>
    </Panel>
    <Panel name="coachTrack" v-if="coachTrackShow">
      教练端设置
      <coach-track slot="content"></coach-track>
    </Panel>
    <Panel name="tweet" v-if="memberLogShow">
      会员端设置
      <Form slot="content" :label-width="180">
        <FormItem label="会员动态发布和展示">
          <i-switch v-model="memberSetting.info.is_open_member_dynamic" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
        </FormItem>
        <FormItem label="我的积分">
          <i-switch v-model="memberSetting.info.is_open_member_point" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
        </FormItem>
        <FormItem label="我的历史体测">
          <i-switch v-model="memberSetting.info.is_open_stamina" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
        </FormItem>
        <FormItem label="查看合同">
          <i-switch :disabled="!isEditState4" true-value="1" false-value="0" v-model="memberSetting.info.see_status"></i-switch>
        </FormItem>
        <FormItem label="电子合同签署" v-if="memberSetting.info.see_status === '1'">
          <i-switch :disabled="!isEditState4" true-value="1" false-value="0" v-model="memberSetting.info.electronic_status" @on-change="electronicChange"></i-switch>
          <Alert type="warning" style="marginTop:20px;width:400px;">功能开启以前的合同无需再进行签署</Alert>
        </FormItem>
        <FormItem label="会员实名认证机会" v-if="memberSetting.info.see_status === '1'">
          <InputNumber :min="0" v-model="memberSetting.info.user_auth_max" :disabled="!isEditState4"></InputNumber> 次/人
          <Alert type="warning" style="marginTop:20px;width:400px;">实名认证不通过次数超过设置值后只能进行纸打合同签署</Alert>
        </FormItem>
        <FormItem label="认证规则">
          <RadioGroup v-model="memberSetting.info.factors_person_auth">
            <Radio :label="2" :disabled="!isEditState4">二要素识别</Radio>
            <Radio :label="3" :disabled="!isEditState4">三要素识别</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="会员端显示购卡协议">
          <i-switch v-model="memberSetting.info.is_open_buycard_protocol" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
        </FormItem>
        <FormItem label="协议内容" v-if="isEditState4 && memberSetting.info.is_open_buycard_protocol == 1">
          <ProtocolEdit />
        </FormItem>
        <FormItem label="会员端显示团课预约协议">
          <i-switch v-model="memberSetting.info.is_open_reservation_protocol" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
        </FormItem>
        <FormItem label="会员端开票">
            <i-switch v-model="memberSetting.info.is_open_invoice" true-value="1" false-value="0" :disabled="!isEditState4" />
        </FormItem>
        <FormItem label="教练评价开关" prop="commentFlag">
            <i-switch v-model="memberSetting.assessment.commentFlag" :disabled="!isEditState4" />
        </FormItem>
        <div v-if="memberSetting.assessment.commentFlag" style="background-color:#f1f3f7;padding:1px 0;margin-bottom:10px;">
          <Alert v-if="memberSetting.assessment.commentFlag" type="warning" style="margin:20px;width:500px;" show-icon>教练评价开启后，会员每次教练消课后都可以在会员端进行教练评价</Alert>
          <FormItem v-if="memberSetting.assessment.commentFlag" label="评分类型设置" prop="commentCategory" :show-message="false" required>
              <div class="tag-box">
                  <div v-for="(item, index) in memberSetting.assessment.commentCategory" :key="index">
                      <Tag v-if="!item.deleteFlag" :name="index" @on-close="handleCloseCategory" :closable="isEditState4" @on-change="handleUpdateCategory" :checked="item.checkFlag" :checkable="isEditState4">{{item.value}}</Tag>
                  </div>
                  <div>
                      <Input v-if="categoryInputBoxFlag" size="small" :maxlength="10" placeholder="请输入类型名称..." v-model="categoryInputTemp" style="width:140px;" />
                      <Button v-if="categoryInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveCategory"></Button>
                      <Button v-if="categoryInputBoxFlag" size="small" icon="md-close"  @click="handleToggleCategory"></Button>
                  </div>
              </div>
              <Button icon="ios-add" type="dashed" size="small" @click="handleToggleCategory" :disabled="!isEditState4">添加类型</Button>
          </FormItem>
          <FormItem label="评分设置" prop="star">
                <radio-group v-model="memberSetting.assessment.commentStar">
                    <radio :disabled="!isEditState4" label="3">1～3分</radio>
                    <radio :disabled="!isEditState4" label="5">1～5分</radio>
                    <radio :disabled="!isEditState4" label="10">1～10分</radio>
                </radio-group>
                <div>
                <span style="color:#AAA;">注：设置针对维度评价的打分范围</span>
                </div>
          </FormItem>
          <FormItem v-if="memberSetting.assessment.commentFlag" label="评价标签设置" prop="commentTag">
              <div class="tag-box">
                  <div v-for="(item, index) in memberSetting.assessment.commentTag" :key="index">
                      <Tag v-if="!item.deleteFlag" :name="index" @on-close="handleCloseTag" :closable="isEditState4" @on-change="handleUpdateTag" :checked="item.checkFlag" :checkable="isEditState4">{{item.value}}</Tag>
                  </div>
                  <div>
                      <Input v-if="tagInputBoxFlag" size="small" :maxlength="10" placeholder="请输入标签名称..." v-model="tagInputTemp" style="width:140px;" />
                      <Button v-if="tagInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveTag"></Button>
                      <Button v-if="tagInputBoxFlag" size="small" icon="md-close"  @click="handleToggleTag"></Button>
                  </div>
              </div>
              <Button icon="ios-add" type="dashed" size="small" @click="handleToggleTag" :disabled="!isEditState4">添加标签</Button>
          </FormItem>
          <FormItem v-if="memberSetting.assessment.commentFlag" label="其他意见反馈" prop="commentFeedback">
              <i-switch v-model="memberSetting.assessment.commentFeedback" :disabled="!isEditState4" />
          </FormItem>
          <FormItem v-if="memberSetting.assessment.commentFlag" label="评价推送时间" prop="commentMinutes">
              教练消课后，<InputNumber :max="999" :min="0" v-model="memberSetting.assessment.commentMinutes" :disabled="!isEditState4"></InputNumber> 分钟再推送评价消息
          </FormItem>
          <FormItem v-if="memberSetting.assessment.commentFlag" label="评价时间设置" prop="commentDays">
              <div style="margin-top:10px">消课<InputNumber style="margin-left:10px" :max="30" :min="0" v-model="memberSetting.assessment.commentDays" :disabled="!isEditState4"></InputNumber> 天后，不可再进行评价<span style="color:#AAA;margin-left:10px;">注：输入0，即不限制;最多30天</span></div>
          </FormItem>
            <FormItem v-if="memberSetting.assessment.commentFlag" label="是否开启追评" prop="commentFeedback">
              <i-switch v-model="memberSetting.assessment.commentFollowUp" :disabled="!isEditState4" /><span style="color:#AAA;margin-left:10px;">注：开启后，会员评价后可再次追加评论，追评时仅限输入文字</span>
              <div v-if="memberSetting.assessment.commentFollowUp">第一次评价<InputNumber :max="30" :min="0" v-model="memberSetting.assessment.commentFollowUpDays" :disabled="!isEditState4"></InputNumber> 天后，不可进行追评<span style="color:#AAA;margin-left:10px;">注：输入0，即不限制;最多30天</span></div>
          </FormItem>
        </div>
        <FormItem label="人脸上传">
          <i-switch v-model="memberSetting.info.member_is_upload_face" true-value="1" false-value="0" :disabled="!isEditState4"></i-switch>
          <Select :disabled="!isEditState4" v-if="memberSetting.info.member_is_upload_face == 1" style="width:90px;marginLeft:10px;" v-model="memberSetting.info.member_upload_face_limit">
            <Option value="0">不限制</Option>
            <Option value="7">7天</Option>
            <Option value="15">15天</Option>
            <Option value="30">30天</Option>
            <Option value="90">90天</Option>
            <Option value="-1">每次授权</Option>
          </Select>
          <Tooltip v-if="memberSetting.info.member_is_upload_face == 1">
            <Icon type="ios-help-circle"
                  style="color: #f4a627;fontSize:18px;"></Icon>
            <div slot="content">
              <div style="max-width: 200px; white-space: normal">对购卡时间进行限制，禁止超过购卡时间的用户绑定人脸，（需要前台进行授权后再操作）</div>
            </div>
          </Tooltip>
        </FormItem>
         <FormItem label="团课评价开关" prop="commentFlag">
            <i-switch v-model="memberSetting.groupClassEvalution.commentFlag" :disabled="!isEditState4" />
        </FormItem>
        <div v-if="memberSetting.groupClassEvalution.commentFlag" style="background-color:#f1f3f7;padding:1px 0;margin-bottom:10px;">
          <Alert v-if="memberSetting.groupClassEvalution.commentFlag" type="warning" style="margin:20px;width:500px;" show-icon>课程结束后，所有签到的会员都可在“会员端-我的-预约记录”里评价</Alert>
          <FormItem v-if="memberSetting.groupClassEvalution.commentFlag" label="评分维度设置" prop="commentCategory" :show-message="false" required>
              <div class="tag-box">
                  <div v-for="(groupItem, index) in memberSetting.groupClassEvalution.commentCategory" :key="index">
                      <Tag v-if="!groupItem.deleteFlag" :name="index" @on-close="handleCloseGroupClassCategory" :closable="index!==0 && isEditState4" @on-change="handleUpdateGroupClassCategory" :checked="groupItem.checkFlag" :checkable="index!==0 &&isEditState4">{{groupItem.value}}</Tag>
                  </div>
                  <div>
                      <Input v-if="categoryGroupClassInputBoxFlag" size="small" :maxlength="10" placeholder="请输入类型名称..." v-model="categoryGroupClassInputTemp" style="width:140px;" />
                      <Button v-if="categoryGroupClassInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveGroupClassCategory"></Button>
                      <Button v-if="categoryGroupClassInputBoxFlag" size="small" icon="md-close"  @click="handleToggleGroupClassCategory"></Button>
                  </div>
              </div>
              <Button icon="ios-add" type="dashed" size="small" @click="handleToggleGroupClassCategory" :disabled="!isEditState4">添加类型</Button>
          </FormItem>
          <FormItem label="打分星级设置" prop="star">
                <radio-group v-model="memberSetting.groupClassEvalution.commentStar">
                    <radio :disabled="!isEditState4" label="3">1～3星</radio>
                    <radio :disabled="!isEditState4" label="5">1～5星</radio>
                    <radio :disabled="!isEditState4" label="10">1～10星</radio>
                </radio-group>
                <div>
                <span style="color:#AAA;">注：设置针对维度评价的打分范围</span>
                </div>
          </FormItem>
          <FormItem v-if="memberSetting.groupClassEvalution.commentFlag" label="评价标签设置" prop="commentTag">
              <div class="tag-box">
                  <div v-for="(item, index) in memberSetting.groupClassEvalution.commentTag" :key="index">
                      <Tag v-if="!item.deleteFlag" :name="index" @on-close="handleCloseGroupClassTag" :closable="isEditState4" @on-change="handleUpdateGroupClassTag" :checked="item.checkFlag" :checkable="isEditState4">{{item.value}}</Tag>
                  </div>
                  <div>
                      <Input v-if="tagGroupClassInputBoxFlag" size="small" :maxlength="10" placeholder="请输入标签名称..." v-model="tagGroupClassInputTemp" style="width:140px;" />
                      <Button v-if="tagGroupClassInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveGroupClassTag"></Button>
                      <Button v-if="tagGroupClassInputBoxFlag" size="small" icon="md-close"  @click="handleToggleGroupClassTag"></Button>
                  </div>
              </div>
              <Button icon="ios-add" type="dashed" size="small" @click="handleToggleGroupClassTag" :disabled="!isEditState4">添加标签</Button>
          </FormItem>
          <FormItem v-if="memberSetting.groupClassEvalution.commentFlag" label="其他意见反馈" prop="commentFeedback">
              <i-switch v-model="memberSetting.groupClassEvalution.commentFeedback" :disabled="!isEditState4" /> <span style="color:#AAA;margin-left:10px;">注：开启后，可以文字形式输入意见</span>
          </FormItem>
          <FormItem v-if="memberSetting.groupClassEvalution.commentFlag" label="评价时间设置" prop="commentMinutes">
              <div>课程结束后<InputNumber :max="60" :min="0" v-model="memberSetting.groupClassEvalution.commentMinutes" :disabled="!isEditState4"></InputNumber> 分钟，会员可进行评价<span style="color:#AAA;margin-left:10px;">注：输入0，即课程结束后就可评价;最多60分钟。</span></div>
              <div style="margin-top:10px">课程结束<InputNumber style="margin-left:10px" :max="30" :min="0" v-model="memberSetting.groupClassEvalution.commentDays" :disabled="!isEditState4"></InputNumber> 天后，不可再进行评价<span style="color:#AAA;margin-left:10px;">注：输入0，即不限制;最多30天</span></div>
          </FormItem>
            <FormItem v-if="memberSetting.groupClassEvalution.commentFlag" label="是否开启追评" prop="commentFeedback">
              <i-switch v-model="memberSetting.groupClassEvalution.commentFollowUp" :disabled="!isEditState4" /><span style="color:#AAA;margin-left:10px;">注：开启后，会员评价后可再次追加评论，追评时仅限输入文字</span>
              <div>第一次评价<InputNumber :max="30" :min="0" v-model="memberSetting.groupClassEvalution.commentFollowUpDays" :disabled="!isEditState4"></InputNumber> 天后，不可进行追评<span style="color:#AAA;margin-left:10px;">注：输入0，即不限制;最多30天</span></div>
          </FormItem>
        </div>
        <FormItem>
          <Button v-if="!isEditState4" type="success" style="background:#5fb75d;" @click="isEditState4=true">编辑</Button>
          <Button v-if="isEditState4" type="success" style="background:#5fb75d;" @click="saveTweetSettings">保存</Button>
          <Button v-if="isEditState4" style="margin-left: 8px" @click="handleCancelEdit4">取消</Button>
        </FormItem>
      </Form>
    </Panel>
    <Panel name="approval" v-if="settingAuth.approve_get_setting_info == 1">
      合同审批设置
      <Approval v-if="showApproval" slot="content"></Approval>
    </Panel>
    <Panel name="suspendSetting" v-if="suspendShow">
      请假设置
      <div class="panel" slot="content">
        <Form>
          <span style="margin-right:20px;">会员卡请假权益计算规则</span>
          <FormItem class="form-box">
            <span style="margin-right:20px;">期限卡</span>
            <RadioGroup v-model="suspendSetting.suspend_rule">
              <Radio label="1" checked :disabled="!isEditState5">会员卡有效期内，会员<b>每年</b>有权请假一定次数和天数</Radio>
              <Tooltip placement="right" :max-width="200">
                <Icon type="ios-help-circle" style="color: #f4a627;font-size:18px;"></Icon>
                <div slot="content">
                  <p>用户购买一张两年卡（设置允许请假的次数为2次），则会员在此卡有效期内每年有权请2次假，即总共可请假4次；</p>
                  <p>天数同理</p>
                </div>
              </Tooltip>
              <br />
              <Radio label="2" checked :disabled="!isEditState5">会员卡有效期内，会员<b>共计</b>有权请假一定次数和天数</Radio>
              <Tooltip placement="right" :max-width="200">
                <Icon type="ios-help-circle" style="color: #f4a627;font-size:18px;"></Icon>
                <div slot="content">
                  <p>用户购买一张两年卡（设置允许请假的次数为2次），则会员在此卡有效期内总共有权请2次假；</p>
                  <p>天数同理</p>
                </div>
              </Tooltip>
            </RadioGroup>
             <Alert type="warning" style="width:500px;margin-top:20px;">注：次卡、储值卡、私教卡、泳教卡执行第二个请假权益规则</Alert>
          </FormItem>
            <FormItem label="是否开启请假限制">
            <i-switch :true-value="1" :false-value="0" @on-change="handleSwitchLeave" v-model="suspendSetting.suspend_limit" size="large" :disabled="!isEditState5"></i-switch>
          </FormItem>
          <FormItem class="form-box">
            <span class="span-block">开启限制后，请假权益使用完后，不能请假</span>
            <span class="span-block">权益内请假收费规则</span>
            <RadioGroup v-model="suspendSetting.charging_type">
              <Radio label="1" checked :disabled="!isEditState5 || !suspendSetting.suspend_limit">按请假次数收费，每次请假收取&nbsp;
                <Input-number style="width:80px" :disabled="!isEditState5 || !suspendSetting.suspend_limit" v-model="suspendSetting.number_price" size="small" :precision="2" :min="0" :active-change="false" />&nbsp;元
              </Radio><br />
              <Radio label="2" checked :disabled="!isEditState5 || !suspendSetting.suspend_limit">按请假时间收费，请假&nbsp;
                <Input-number style="width:80px" :disabled="!isEditState5 || !suspendSetting.suspend_limit" v-model="suspendSetting.month" size="small" :min="1" :precision="0" />&nbsp;月，收取&nbsp;
                <Input-number style="width:80px" size="small" :disabled="!isEditState5 || !suspendSetting.suspend_limit" v-model="suspendSetting.price" :min="0" :precision="2" :active-change="false" />&nbsp;元
              </Radio>
            </RadioGroup>
            <Alert type="warning" style="width:500px;margin-top:20px;">注：按<b>请假时间</b>收费，请假时间不足1月时按1月计费</Alert>
          </FormItem>
           <FormItem label="是否开启会员端请假">
            <i-switch :true-value="1" :false-value="0" @on-change="handleSwichMemberLeave" v-model="suspendSetting.is_open_member_suspend" size="large" :disabled="!isEditState5"></i-switch>
          </FormItem>
          <FormItem class="form-box">
            <span class="span-block">允许请假方式</span>
           <RadioGroup v-model="suspendSetting.member_suspend_rule.suspend_method">
              <Radio label="1" checked :disabled="!isEditState5 || !suspendSetting.is_open_member_suspend"><b>以会员</b>请假，即可以对名下<b>所有卡种一并</b>请假</Radio>
              <br />
              <Radio label="2" checked :disabled="!isEditState5 || !suspendSetting.is_open_member_suspend"><b>以卡种</b>请假，即只能对名下<b>某一张卡</b>请假</Radio>
            </RadioGroup>
            <span class="span-block">请假时间范围</span>
            <span class="span-block">单次请假时间最长不超过&nbsp;
                <Input-number style="width:80px" :disabled="!isEditState5 || !suspendSetting.is_open_member_suspend" v-model="suspendSetting.member_suspend_rule.each_longest_day" size="small" :precision="2" :min="0" :active-change="false" :formatter="value => `${parseInt(value)}`" />&nbsp;天（输入0，即为不限制时间）</span>
          </FormItem>
          <FormItem>
            <Button v-if="!isEditState5" type="success" style="background:#5fb75d;" @click="isEditState5=true">编辑</Button>
            <Button v-if="isEditState5" type="success" style="background:#5fb75d;" @click="saveSuspendSettings">保存</Button>
            <Button v-if="isEditState5" style="margin-left: 8px" @click="handleCancelEdit5">取消</Button>
          </FormItem>
        </Form>
      </div>
    </Panel>
    <Panel name="goodsConsumeSetting" v-if="commodity_settlement_setting">
      储值卡设置
      <div class="panel" slot="content">
        <Form ref="goodsSettingForm" :model="goodsConsumeSettingData" :rules="goodsSettingRules" :label-width="140">
          <form-item label="储值卡消费确认" prop="open_confirm">
            <radio-group v-model="goodsConsumeSettingData.open_confirm">
              <radio :disabled="!isEditState6" label="1">开启</radio>
              <radio :disabled="!isEditState6" label="0">禁用</radio>
            </radio-group>
            <div>
              <span style="color:#AAA;">开启后会员使用储值卡消费时，需会员进行确认。（确认方式：一体机/短信验证码）</span>
            </div>
          </form-item>
          <form-item label="储值卡与其他支付方式混合支付" prop="stored_value_blend">
            <radio-group v-model="goodsConsumeSettingData.stored_value_blend">
              <radio :disabled="!isEditState6" label="1">开启</radio>
              <radio :disabled="!isEditState6" label="0">禁用</radio>
            </radio-group>
            <div>
              <span style="color:#AAA;">开启后支付时可以用储值卡和微信、支付宝等方式分摊付款金额</span>
            </div>
          </form-item>
          <form-item label="储值卡会员端购卡购课" prop="stored_card_member_pay">
            <radio-group v-model="goodsConsumeSettingData.stored_card_member_pay">
              <radio :disabled="!isEditState6" label="1">开启</radio>
              <radio :disabled="!isEditState6" label="0">禁用</radio>
            </radio-group>
            <div>
              <span style="color:#AAA;">开启后会员可以在会员端使用储值卡进行购卡、购课、付费团课支付</span>
            </div>
          </form-item>
          <template>
            <form-item label="商品可用储值卡" prop="card_use_type">
              <radio-group v-model="goodsConsumeSettingData.card_use_type">
                <radio :disabled="!isEditState6" label="1">所有卡可用</radio>
                <radio :disabled="!isEditState6" label="2">部分卡可用</radio>
              </radio-group>
            </form-item>
            <form-item prop="card_id_list" v-if="goodsConsumeSettingData.card_use_type == 2">
              <Select :disabled="!isEditState6" style="width:300px;" v-model="goodsConsumeSettingData.card_id_list" multiple clearable filterable>
                <Option v-for="card in nonMemberCardList" :key="card.id" :value="card.id">{{ card.name }}</Option>
              </Select>
            </form-item>
          </template>
          <FormItem>
            <Button v-if="!isEditState6" type="success" style="background:#5fb75d;" @click="isEditState6=true">编辑</Button>
            <Button v-if="isEditState6" type="success" style="background:#5fb75d;" @click="handleGoodsConsumeSetting('post')">保存</Button>
            <Button v-if="isEditState6" style="margin-left: 8px" @click="handleCancelEdit6">取消</Button>
          </FormItem>
        </Form>
      </div>
    </Panel>
    <Panel name="numberSet">
      积分设置
      <NumberSet v-if="showNumberSet" slot="content"></NumberSet>
    </Panel>
    <Panel name="reserveStadiumSetting" v-if="spaceOrderShow">
      订场设置
      <div class="panel" slot="content">
        <Alert type="warning" style="width:500px;" show-icon>订场设置只针对会员端订场有限制</Alert>
        <Form :model="reserveStadiumData" :label-width="150">
          <form-item label="订场用户: " prop="user_range">
            <radio-group v-model="reserveStadiumData.user_range">
              <radio :disabled="!isEditState7" :label="1">会员、潜客都可订场</radio>
              <radio :disabled="!isEditState7" :label="2">仅会员可订场</radio>
            </radio-group>
          </form-item>
          <form-item label="支付方式: " prop="pay_type">
            <checkbox-group v-model="reserveStadiumData.pay_type" @on-change="handlePayMethodChange">
              <checkbox :disabled="!isEditState7" :label="1">微信支付</checkbox>
              <checkbox :disabled="!isEditState7" :label="2">储值卡支付</checkbox>
            </checkbox-group>
          </form-item>
          <form-item label="订场时间: " prop="booking_time">
            <radio-group v-model="reserveStadiumData.booking_time">
              <radio :disabled="!isEditState7" :label="1">
                <span>可预约未来</span>
                <Input-number :disabled="!isEditState7||reserveStadiumData.booking_time!=1" v-model="reserveStadiumData.booking_future_days" size="small" :min="1" :max="6" :precision="0" />
                <span>天的场地</span></radio>
              <radio :disabled="!isEditState7" :label="2">只能预订当日的场地</radio>
            </radio-group>
          </form-item>
          <form-item label="允许订场时段" prop="allow_time">
            <span>
              每天
              <TimePicker v-model="reserveStadiumData.allow_time" format="HH:mm" placeholder="允许时段" :disabled="!isEditState7" :editable="false" :clearable="false" style="width:120px" transfer></TimePicker>
              后开始进行场地预订
            </span>
          </form-item>
          <form-item label="订场次数: " prop="booking_num_set">
            <radio-group v-model="reserveStadiumData.booking_num_set" @on-change="handleBookNumChange">
              <radio :disabled="!isEditState7" :label="0">用户每日订场次数无限制</radio>
              <radio :disabled="!isEditState7" :label="1">
                用户每日订场次数不能大于
                <Input-number :disabled="!isEditState7" v-model="reserveStadiumData.booking_num" size="small" :min="1" :precision="0" />
                次
              </radio>
            </radio-group>
          </form-item>
          <form-item label="取消订场时间: ">
            预订超过
            <Input-number :disabled="!isEditState7" v-model="reserveStadiumData.cancel_booking_after_order" size="small" :min="1" :precision="0" />
            小时不能取消订场；
            且场地预订起始时间前
            <Input-number :disabled="!isEditState7" v-model="reserveStadiumData.cancel_booking_before_start" size="small" :min="1" :precision="0" />
            分钟不能取消预订；
          </form-item>
          <form-item label="订场邀约活动: ">
            仅能邀约预订未来
            <Input-number :disabled="!isEditState7" v-model="reserveStadiumData.able_mark_limit_time" size="small" :min="1" :precision="0" />
            小时以后的场地
            <div>
               订场邀约活动成团的截止时间不晚于开场前
              <Input-number :disabled="!isEditState7" v-model="reserveStadiumData.mark_group_cutoff_time" size="small" :min="1" :precision="0" />
              小时
            </div>
          </form-item>
          <!-- <form-item label="会员端订场显示订场须知: " prop="booking_notice_swtich">
            <i-switch :disabled="!isEditState7" v-model="reserveStadiumData.booking_notice_swtich" :true-value="1" :false-value="0" />
          </form-item>
          <form-item v-if="reserveStadiumData.booking_notice_swtich === 1" label="订场须知: ">
            <Editor v-model="reserveStadiumData.booking_notice" :height="200"></Editor>
          </form-item> -->
          <FormItem>
            <Button v-if="!isEditState7" type="success" style="background:#5fb75d;" @click="isEditState7=true">编辑</Button>
            <Button v-if="isEditState7" type="success" style="background:#5fb75d;" @click="setReserveStadiumSettings">保存</Button>
            <Button v-if="isEditState7" style="margin-left: 8px" @click="handleCancelEdit7">取消</Button>
          </FormItem>
        </Form>
      </div>
    </Panel>
    <Panel name="wxPayConf" v-if="settingAuth.WxpaysubmchConf_save_setting_info == 1">
      微信支付特约商户配置
      <WxPayConf v-if="showWxPayConf" slot="content"></WxPayConf>
    </Panel>
    <Panel name="payTypeSet">
      支付方式设置
      <PayTypeSet v-if="showPayTypeSet" slot="content"></PayTypeSet>
    </Panel>
    <Panel name="invoiceSet">
      电子发票设置
      <InvoiceSet v-if="showInvoiceSet" slot="content"></InvoiceSet>
    </Panel>
    <Panel name="ticketSet" v-if="san_setting">
      散场票设置
      <div class="panel" slot="content">
        <Form :model="ticketData" :label-width="150">
          <form-item label="会员端退票: " prop="cancel_member">
            <radio-group v-model="ticketData.cancel_member">
              <radio :disabled="!isEditTicket" :label="1">开启</radio>
              <radio :disabled="!isEditTicket" :label="0">关闭</radio>
            </radio-group>
          </form-item>
          <FormItem>
            <Button v-if="!isEditTicket" type="success" style="background:#5fb75d;" @click="isEditTicket=true">编辑</Button>
            <Button v-if="isEditTicket" type="success" style="background:#5fb75d;" @click="setTicketSettings">保存</Button>
            <Button v-if="isEditTicket" style="margin-left: 8px" @click="handleCancelEditTicket">取消</Button>
          </FormItem>
        </Form>
      </div>
    </Panel>
    <Panel name="thirdPartySettings" v-if="hasThirdParty">
      三方平台对接设置
      <Form slot="content" :label-width="180">
        <FormItem label="美团ID">
          <Input v-model="thirdPartySettings[0].shop_id" placeholder="请输入美团ID" :disabled="!isEditThirdParty" style="max-width: 300px;" />
        </FormItem>
        <FormItem label="大众点评ID">
          <Input v-model="thirdPartySettings[1].shop_id" placeholder="请输入大众点评ID" :disabled="!isEditThirdParty" style="max-width: 300px;" />
        </FormItem>
        <FormItem label="抖音ID">
          <Input v-model="thirdPartySettings[2].shop_id" placeholder="请输入抖音ID" :disabled="!isEditThirdParty" style="max-width: 300px;" />
        </FormItem>
        <FormItem label="抖音商家ID">
          <Input v-model="thirdPartySettings[2].account_id" placeholder="请输入抖音商家ID" :disabled="!isEditThirdParty" style="max-width: 300px;" />
        </FormItem>
        <FormItem>
          <Button v-if="!isEditThirdParty" type="success" style="background:#5fb75d;" @click="isEditThirdParty=true">编辑</Button>
          <Button v-if="isEditThirdParty" type="success" style="background:#5fb75d;" @click="handleSaveThirdPartySettings">保存</Button>
          <Button v-if="isEditThirdParty" style="margin-left: 8px" @click="handleCancelThirdPartySettings">取消</Button>
        </FormItem>
      </Form>
    </Panel>

    <Modal
        v-model="leaveModalFlag"
        :closable="false">
        <p slot="header">
            <Icon type="ios-information-circle" style="color:#ff9900;"></Icon>
            <span style="margin-left:10px;font-size:16px;">请先设置会员卡允许请假次数</span>
        </p>
        <div class="btnnn-box">
          <router-link :to="{name: '会员卡管理'}">
            <Button type="primary">去设置</Button>
          </router-link>
          <Button @click="handleLeaveModalCancel">设置好了</Button>
        </div>
        <div slot="footer"></div>
    </Modal>
     <Modal
        v-model="leavePayFlag"
        :closable="false">
        <p slot="header">
            <Icon type="ios-information-circle" style="color:#ff9900;"></Icon>
            <span style="margin-left:10px;font-size:16px;">您设置了请假收费，但检测到您还未设置小程序支付渠道</span>
        </p>
        <div class="btnnn-box">
          <Button type="primary" @click="handleSetPaySet">去配置</Button>
          <Button @click="leavePayFlag=false;">取消</Button>
        </div>
        <div slot="footer"></div>
    </Modal>
    <Modal
        v-model="electronicSatusModal">
        <p style="margin:50px 20px;textAlign:center;fontSize:16px">请先开通电子合同功能</p>
        <div class="btnnn-box">
          <router-link :to="{name: '电子合同签署流程'}">
            <Button type="primary">查看如何开通</Button>
          </router-link>
        </div>
        <div slot="footer"></div>
    </Modal>
  </Collapse>
</template>

<script>
import GetCustRes from './getCustRes'
import MembershipTrack from './membershipTrack'
import CoachTrack from './coachTrack'
// import Approval from './components/approval.vue' // old
import Approval from './components/approvalSetting.vue' // new
import NumberSet from './components/NumberSet.vue'
import PayTypeSet from './components/PayTypeSet.vue'
import InvoiceSet from './components/InvoiceSet.vue'
import WxPayConf from './components/wxPayConf.vue'
import PtSet from './components/PtSet.vue'
import CardSaleSettings from './components/CardSaleSettings.vue'
import ProtocolEdit from 'src/views/gym/protocolEdit.vue'
import Editor from 'src/components/form/Editor'
import { mapState } from 'vuex'

const NONE_THIRD_PARTY_SETTINGS = [
  { platform_id: 1, shop_id: '' },
  { platform_id: 2, shop_id: '' },
  { platform_id: 3, shop_id: '', account_id: '' }
]

export default {
  components: {
    GetCustRes,
    MembershipTrack,
    CoachTrack,
    WxPayConf,
    Approval,
    NumberSet,
    PayTypeSet,
    InvoiceSet,
    PtSet,
    CardSaleSettings,
    ProtocolEdit,
    Editor
  },
  computed: {
    ...mapState(['adminInfo'])
  },
  data() {
    return {
      payment_option: '1',
      electronicSatusModal: false,
      leaveModalFlag: false,
      leavePayFlag: false,
      collapseValue: 'teamCourse',
      goodsConsumeSettingData: {
        open_confirm: undefined,
        stored_value_blend: undefined,
        stored_card_member_pay: undefined,
        card_use_type: undefined,
        card_id_list: []
      },
      goodsSettingRules: {
        card_id_list: [{ required: true, message: '请选择储值卡' }]
      },
      isEditState6: false,
      nonMemberCardList: [],
      isEditState5: false,
      is_open_applet_pay: false,
      suspendSetting: {
        suspend_rule: 1,
        charging_type: 1,
        number_price: 220,
        month: 2,
        price: 200,
        suspend_limit: 0,
        is_open_member_suspend: 0,
        member_suspend_rule: {
          suspend_method: 1,
          each_longest_day: 0
        }
      },
      settingAuth: {},
      teamCourseShow: false,
      spaceOrderShow: false,
      suspendShow: false,
      commodity_settlement_setting: false, // 储值卡设置
      showCoach: true,
      showSwimCoach: true,
      coachCourseShow: false,
      swimCoachCourseShow: false,
      signShow: false,
      statisticsShow: false,
      membershipTrackShow: false,
      coachTrackShow: false,
      aroundGymShow: false,
      cardSaleSettingsPanel: true,
      cardSaleSettingsShow: false,
      // team course.
      isEditState1: false, // 是否编辑状态
      missReservation: true, // 是否开启爽约惩罚
      lifecycle: 30,
      teamList: [],
      courseState: {
        is_limit_card: 0, // 是否开启期限卡预约限制0否1是
        limit_card_set: {
          days: 1, // 期限卡限制天数
          times: 3 // 期限卡限制次数
        },
        is_waitting_limit: 0, // 是否开启团课候补限制0否1是
        limit_times: {
          days: 1, // 候补限制天数
          times: 5 // 候补限制次数
        },
        is_confirm: 0, // 是否开启会员确认0否1是
        confirm_time: 30, // 候补确认时间（分钟）
        is_stop: 0, // 是否开启候补暂停
        stop_time: ['01:00', '05:00'], // 暂停候补开始结束时间
        is_send_msg: 0 // 是否开启短信通知确认0否1是
      },
      waitting_count: 0, // 候补会员人数
      // sign.
      enterGym: true,
      enterFee: 2.5,
      autoStart: false,
      aroundGym: true,
      aroundGymMeter: 300,
      isEditState3: false,
      showApproval: false,
      showNumberSet: false,
      showPayTypeSet: true,
      showInvoiceSet: true,
      showWxPayConf: false,
      // tweet
      isEditState4: false,
      courseCommentSetting:{},
      memberSetting: {
        info: {
          is_open_member_dynamic: '1',
          member_is_upload_face: '1',
          member_upload_face_limit: '0',
          is_open_member_point: '1',
          is_open_stamina: '1',
          is_open_buycard_protocol: '0',
          is_open_reservation_protocol: '0',
          is_open_invoice: '0',
          see_status: '0',
          electronic_status: '0',
          user_auth_max: 1,
          factors_person_auth: 2,
        },
        assessment: {
          commentFlag: false,
          commentCategory: [],
           commentStar:'5',
          commentTag: [],
          commentFeedback: false,
           commentDays: 0,
           commentFollowUp: false,
            commentFollowUpDays: 0,
          commentMinutes: 120
        },
        groupClassEvalution: {
            commentFlag: false,
            commentCategory: [],
            commentStar:'5',
            commentTag: [],
            commentFeedback: false,
            commentMinutes: 0,
            commentDays: 0,
            commentFollowUp: false,
            commentFollowUpDays: 0,
        }
        // course_comment: {
        //     COACH_RATING_SWITCH: {},//教练-评价开关
        //     COACH_RATING_TYPE: [],//教练-评分类型
        //     COACH_APPRAISE_TAGS: [],//教练-评价标签
        //     COACH_FEEDBACK_SWITCH: {},//教练-其他意见反馈开关
        //     COACH_PUSH_TIME: {},//教练-推送时间
        // }
      },
      categoryInputBoxFlag: false,
      categoryInputTempIdx: -1,
      categoryInputTemp: '',

     categoryGroupClassInputBoxFlag: false,
      categoryGroupClassInputTempIdx: -1,
      categoryGroupClassInputTemp: '',

      tagInputBoxFlag: false,
      tagInputTempIdx: -1,
      tagInputTemp: '',

      tagGroupClassInputBoxFlag: false,
      tagGroupClassInputTempIdx: -1,
      tagGroupClassInputTemp: '',

      memberLogShow: false,

      isEditState7: false,
      reserveStadiumData: {
        user_range: 1,
        pay_type: [2],
        booking_time: 1,
        booking_future_days: 6,
        booking_num: 0,
        booking_num_set: 0,
        cancel_booking_after_order: 2,
        cancel_booking_before_start: 30,
        able_mark_limit_time: 24,
        mark_group_cutoff_time: 8,
        booking_notice_swtich: 1,
        booking_notice: '',
        allow_time: '00:00'
      },
      isWxAccount: false,

      // 是否显示散场票设置
      san_setting: false,
      // 散场票设置
      isEditTicket: false,
      // 散场票
      ticketData: {
        cancel_member: 0
      },
      
      // third party settings
      hasThirdParty: false,
      isEditThirdParty: false,
      thirdPartySettings: NONE_THIRD_PARTY_SETTINGS
    }
  },
  methods: {
    electronicChange(val) {
      if (val && this.adminInfo.esign_status != 1) {
        this.memberSetting.info.electronic_status = '0'
        this.electronicSatusModal = true
      }
    },
    checkWxAcount() {
      return this.$service.get('/Web/Groupbuy/check_bus_pay_conf', {
        params: {
          is_pay_conf: 1
        }
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.isWxAccount = res.data.data === 1
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    async handlePayMethodChange() {
      if(!this.reserveStadiumData.pay_type.some(item => item === 1)) {
        return false
      }
      await this.checkWxAcount()
      if (
        !this.isWxAccount
      ) {
        if (this.reserveStadiumData.pay_type.some(item => item === 2)) {
          this.reserveStadiumData.pay_type = [2]
        } else {
          this.reserveStadiumData.pay_type = []
        }
        this.$Message.error('请配置微信支付特约商户')
      }
    },
    handleBookNumChange(val) {
      if (val === 0) {
        this.reserveStadiumData.booking_num = 0
      } else if (val === 1) {
        this.reserveStadiumData.booking_num = 4
      }
    },
    handleSwitchLeave(val) {
      if (val === 1) {
        this.leaveModalFlag = true
      }
    },
    handleLeaveModalCancel() {
      this.leaveModalFlag = false
      if (
        this.suspendSetting.is_open_member_suspend &&
        !this.is_open_applet_pay
      ) {
        this.leavePayFlag = true
      }
    },
    handleSwichMemberLeave(val) {
      if (
        val === 1 &&
        this.suspendSetting.suspend_limit &&
        !this.is_open_applet_pay
      ) {
        this.leavePayFlag = true
      }
    },
    handleSetPaySet() {
      this.leavePayFlag = false
      this.collapseValue = 'payTypeSet'
    },
    handleCancelEdit1() {
      this.isEditState1 = false
      this.getTeamSettings()
    },
    handleCancelEdit3() {
      this.isEditState3 = false
      this.getSignSettings()
    },
    handleCancelEdit4() {
      this.isEditState4 = false
      this.getTweetSettings()
    },
    handleCancelEdit5() {
      this.isEditState5 = false
      this.getSuspendSetting()
    },
    handleCancelEdit6() {
      this.isEditState6 = false
      this.handleGoodsConsumeSetting('get')
    },
    handleCancelEdit7() {
      this.isEditState7 = false
      this.getReserveStadiumSettings()
    },
    handleGoodsConsumeSetting(method) {
      if (method === 'get') {
        return this.$service
          .get('/Web/Commodity/get_setting')
          .then(res => {
            if (res.data.errorcode == 0) {
              this.goodsConsumeSettingData = res.data.data
              this.goodsConsumeSettingData.open_confirm = String(
                res.data.data.open_confirm
              )
              this.goodsConsumeSettingData.stored_value_blend = String(
                res.data.data.stored_value_blend
              )
              this.goodsConsumeSettingData.stored_card_member_pay = String(
                res.data.data.stored_card_member_pay
              )
              this.goodsConsumeSettingData.card_use_type = String(
                res.data.data.card_use_type
              )
              if (res.data.data.card_use_type == 2) {
                this.goodsConsumeSettingData.card_id_list = this.goodsConsumeSettingData.card_id_list.split(
                  ','
                )
              } else {
                this.goodsConsumeSettingData.card_id_list = []
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      } else {
        this.$refs.goodsSettingForm.validate(res => {
          if (res) {
            let postdata = { ...this.goodsConsumeSettingData }
            if (this.goodsConsumeSettingData.card_use_type == 1) {
              postdata.card_id_list = ''
            } else {
              postdata.card_id_list = this.goodsConsumeSettingData.card_id_list.join(
                ','
              )
            }
            return this.$service
              .post('/Web/Commodity/save_setting', postdata)
              .then(res => {
                if (res.data.errorcode == 0) {
                  this.$Message.success(res.data.errormsg)
                  this.isEditState6 = false
                } else {
                  this.$Message.error(res.data.errormsg)
                  this.handleGoodsConsumeSetting('get')
                }
              })
              .catch(err => {
                console.error(err)
              })
          }
        })
      }
    },
    handleAddListClick() {
      if (this.teamList.length < 5) {
        this.teamList.push(1)
      } else {
        this.$Message.error('最多设置5条!')
      }
    },
    handleDeleteListClick(index) {
      this.teamList.splice(index, 1)
    },
    // 获取所有储值卡
    getNonMemberCardList() {
      const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
      const postdata = {
        bus_id: this.$store.state.busId,
        page_size: 9999,
        card_type: 1
      }
      return this.$service
        .post(url, postdata)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.nonMemberCardList = res.data.data.list.filter(
              item => item.card_type_id === '3'
            )
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getSuspendSetting() {
      const url = '/Web/SuspendSetting/get_user_suspend_setting'
      this.$service
        .post(url)
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.is_open_applet_pay = res.data.data.is_open_applet_pay
              let config = res.data.data.info
              Object.assign(this.suspendSetting, config)
              this.suspendSetting.number_price = Number(
                config.charging_rule.number_price
              )
              this.suspendSetting.month =
                Number(config.charging_rule.time_price.month) || 1
              this.suspendSetting.price = Number(
                config.charging_rule.time_price.price
              )
              this.suspendSetting.suspend_rule = config.suspend_rule
              this.suspendSetting.charging_type = config.charging_type
              this.suspendSetting.suspend_limit = Number(config.suspend_limit)
              this.suspendSetting.is_open_member_suspend = Number(
                config.is_open_member_suspend
              )
              this.suspendSetting.member_suspend_rule.suspend_method =
                config.member_suspend_rule.suspend_method
              this.suspendSetting.member_suspend_rule.each_longest_day = Number(
                config.member_suspend_rule.each_longest_day
              )
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error('服务器扑街！')
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    saveSuspendSettings() {
      const url = '/Web/SuspendSetting/update_user_suspend_setting'
      this.$service
        .post(url, this.suspendSetting)
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              const self = this
              setTimeout(() => {
                self.isEditState5 = false
              }, 1000)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error('服务器扑街！')
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleCollapseClick(keyArr) {
      const key = keyArr[0]
      switch (key) {
        case 'teamCourse':
          this.getTeamSettings()
          break
        case 'coachCourse':
          this.coachCourseShow = true
          break
        case 'swimCoachCourse':
          this.swimCoachCourseShow = true
          break
        case 'sign':
          this.getSignSettings()
          break
        case 'approval':
          this.showApproval = true
          break
        case 'numberSet':
          this.showNumberSet = true
          break
        case 'payTypeSet':
          this.showPayTypeSet = true
          break
        case 'invoiceSet':
          this.showInvoiceSet = true
          break
        case 'wxPayConf':
          this.showWxPayConf = true
          this.checkWxAcount()
          break
        case 'cardSaleSettings':
          this.cardSaleSettingsShow = true
          break
        case 'tweet':
          this.getTweetSettings()
          break
        case 'suspendSetting':
          this.getSuspendSetting()
          break
        case 'goodsConsumeSetting':
          this.getNonMemberCardList()
          this.handleGoodsConsumeSetting('get')
          break
        case 'reserveStadiumSetting':
          this.getReserveStadiumSettings()
          break
        case 'ticketSet':
          this.getTicketSet()
          break
        case 'thirdPartySettings':
          this.getThirdPartySettings()
          break
        default:
          break
      }
    },
    getAuthority() {
      return this.$service
        .post('/Web/business/check_admin_bus_setting')
        .then(res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.settingAuth = resData
            this.teamCourseShow = resData.set_business_miss_setting == 1
            this.spaceOrderShow = resData.space_order_settlement_setting !== 0
            this.showCoach = resData.update_bus_coach_setting == 1
            this.showSwimCoach = resData.update_bus_swim_coach_setting == 1
            this.signShow = resData.update_sign_setting == 1
            this.statisticsShow = resData.set_sources == 1
            this.membershipTrackShow = resData.add_membership_param == 1
            this.coachTrackShow = resData.coach_setting_bus == 1
            this.aroundGymShow = resData.location_sign_auth == 1
            this.memberLogShow = resData.member_setting == 1
            this.suspendShow = resData.user_suspend_setting == 1
            this.commodity_settlement_setting = resData.commodity_settlement_setting == 1; // 储值卡设置
            this.san_setting = resData.san_setting
            this.hasThirdParty = resData.third_party_setting == 1 // third party settings
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getSignSettings() {
      return this.$service
        .post('/Web/SignSetting/get_sign_setting')
        .then(res => {
          if (res.data.errorcode == 0) {
            const info = res.data.data.info
            this.enterGym = info.is_value_card_sign == 1
            this.enterFee = parseFloat(info.value_card_sign_price)
            this.autoStart = info.allow_user_active_card == 1
            this.aroundGym = info.is_bus_location_sign == 1
            this.aroundGymMeter = parseInt(info.bus_location_sign_range)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    saveSignSettings() {
      return this.$service
        .post('/Web/SignSetting/update_sign_setting', {
          is_value_card_sign: this.enterGym ? '1' : '0',
          value_card_sign_price: this.enterFee,
          allow_user_active_card: this.autoStart ? '1' : '0',
          is_bus_location_sign: this.aroundGym ? '1' : '0',
          bus_location_sign_range: this.aroundGymMeter
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success('设置成功!')
            const self = this
            setTimeout(() => {
              self.isEditState3 = false
            }, 1000)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    // 获取团操课设置
    getTeamSettings() {
      return this.$service
        .post('/Web/BusinessMissSetting/get_business_miss_setting')
        .then(res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.missReservation = resData.miss_turn_on == 1
            this.teamList = []
            const self = this
            setTimeout(() => {
              if (
                Array.isArray(resData.prohibits) &&
                resData.prohibits.length > 0
              ) {
                self.teamList = resData.prohibits
              } else {
                self.teamList = [1]
              }
              if (resData.miss_cycle == 0) {
                self.lifecycle = 30
              } else {
                self.lifecycle = parseInt(resData.miss_cycle)
              }

              // 处理团课候补相关数据
              const {
                id,
                bus_id,
                miss_turn_on,
                miss_cycle,
                prohibits,
                waitting_count,
                stop_time,
                ...rest
              } = resData
              for (const [key, val] of Object.entries(rest)) {
                if (typeof val === 'string' && val) {
                  this.courseState[key] = Number(val)
                } else if (
                  Object.prototype.toString.call(val) === '[object Object]'
                ) {
                  Object.keys(val).forEach(k => {
                    this.courseState[key][k] = Number(val[k])
                  })
                }
              }
              this.courseState['stop_time'] = JSON.parse(stop_time)
              this.waitting_count = +waitting_count
            }, 300)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTweetSettings() {
      return this.$service.post('/Web/MemberSetting/get_setting').then(res => {
        if (res.data.errorcode == 0) {
          this.memberSetting.info = {
            ...res.data.data.info,
            user_auth_max: parseInt(res.data.data.info.user_auth_max),
            factors_person_auth: parseInt(res.data.data.info.factors_person_auth)
          }

          const settings = res.data.data.course_comment
          this.courseCommentSetting = res.data.data.course_comment

          if (Array.isArray(settings.COACH_RATING_TYPE_LIST)) {
            settings.COACH_RATING_TYPE_LIST.forEach(item => {
              item.deleteFlag = false
              item.checkFlag = false
            })
          } else {
            settings.COACH_RATING_TYPE_LIST = []
          }
          if (Array.isArray(settings.COACH_APPRAISE_TAGS_LIST)) {
            settings.COACH_APPRAISE_TAGS_LIST.forEach(item => {
              item.deleteFlag = false
              item.checkFlag = false
            })
          } else {
            settings.COACH_APPRAISE_TAGS_LIST = []
          }

           if (Array.isArray(settings.GROUP_CLASS_GRADING_TYPE_LIST)) {
            settings.GROUP_CLASS_GRADING_TYPE_LIST.forEach(item => {
              item.deleteFlag = false
              item.checkFlag = false
            })
          } else {
            settings.GROUP_CLASS_GRADING_TYPE_LIST = []
          }
          if (Array.isArray(settings.GROUP_CLASS_LABEL_SETTINGS_LIST)) {
            settings.GROUP_CLASS_LABEL_SETTINGS_LIST.forEach(item => {
              item.deleteFlag = false
              item.checkFlag = false
            })
          } else {
            settings.GROUP_CLASS_LABEL_SETTINGS_LIST = []
          }
          this.memberSetting.assessment = {
            commentFlag: settings.COACH_RATING_SWITCH.value == 1,//教练-评价开关
            commentCategory: settings.COACH_RATING_TYPE_LIST,//教练-评分类型
            commentStar: settings.COACH_MAXIMUM_SCORE_VALUE.value.toString(),//教练-打分星级设置
            commentTag: settings.COACH_APPRAISE_TAGS_LIST,//教练-评价标签
            commentFeedback: settings.COACH_FEEDBACK_SWITCH.value == 1,//教练-其他意见反馈开关
            commentMinutes: parseInt(settings.COACH_PUSH_TIME.value),//教练-推送时间
            commentDays: parseInt(settings.COACH_MAXIMUM_EVALUABLE_TIME.value),//教练-课程结束后就最大可评价时间(天)[0则不限制]
            commentFollowUp: settings.COACH_WHETHER_TO_TURN_ON_THE_REVIEW.value == 1,//教练-追评开关
            commentFollowUpDays: parseInt(settings.COACH_MAXIMUM_REVIEW_TIME.value)//教练-最大可追评时间(天)[0则不限制]
          }
          this.memberSetting.groupClassEvalution = {
            commentFlag: settings.GROUP_CLASS_EVALUATION_SWITCH.value == 1,//团课-评价开关
            commentCategory: settings.GROUP_CLASS_GRADING_TYPE_LIST,//团课-评分类型
            commentTag: settings.GROUP_CLASS_LABEL_SETTINGS_LIST,//团课-评价标签
            commentStar: settings.GROUP_CLASS_MAXIMUM_SCORE_VALUE.value.toString(),//团课-打分星级设置
            commentFeedback: settings.GROUP_CLASS_FEEDBACK_SWITCH.value == 1,//团课-其他意见反馈开关
            commentMinutes: parseInt(settings.GROUP_CLASS_MINIMUM_ASSESSABLE_TIME_AFTER_THE_END_OF_THE_CLASS.value),//团课-课程结束后就最小可评价时间(分钟)[0则不限制]
            commentDays: parseInt(settings.GROUP_CLASS_THE_MAXIMUM_EVALUATION_TIME_AFTER_THE_COURSE.value),//团课-课程结束后就最大可评价时间(天)[0则不限制]
            commentFollowUp: settings.GROUP_CLASS_FOLLOW_UP_SWITCH.value == 1,//团课-追评开关
            commentFollowUpDays: parseInt(settings.GROUP_CLASS_COURSE_MAXIMUM_FOLLOW_UP_ASSESSMENT_TIME.value)//团课-最大可追评时间(天)[0则不限制]
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 保存团操课设置
    saveTeamSettings() {
      const ipts = document.querySelectorAll('.shitIpt input')
      if (!!ipts && ipts.length > 0) {
        ipts.forEach((item, index) => {
          this.teamList[index] = item.value
        })
      }

      return this.$service
        .post('/Web/BusinessMissSetting/set_business_miss_setting', {
          miss_turn_on: this.missReservation ? '1' : '0',
          miss_cycle: this.lifecycle,
          prohibits: this.teamList,
          ...this.courseState
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            const self = this
            setTimeout(() => {
              self.isEditState1 = false
            }, 1000)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    saveTweetSettings() {
      let categoryList = this.memberSetting.assessment.commentCategory.filter(
        item => !(!item.id && item.deleteFlag)
      )
      let tagList = this.memberSetting.assessment.commentTag.filter(
        item => !(!item.id && item.deleteFlag)
      )
      categoryList.forEach(item => {
        item.deleted = item.deleteFlag ? 1 : 0
      })
      tagList.forEach(item => {
        item.deleted = item.deleteFlag ? 1 : 0
      })

      if (
        Array.isArray(categoryList) &&
        categoryList.filter(item => item.deleted === 0).length === 0
      ) {
        this.$Message.error('评分类型不能为空！')
        return false
      }

      let categoryGroupClassList = this.memberSetting.groupClassEvalution.commentCategory.filter(
        item => !(!item.id && item.deleteFlag)
      )
      let tagGroupClassList = this.memberSetting.groupClassEvalution.commentTag.filter(
        item => !(!item.id && item.deleteFlag)
      )
      categoryGroupClassList.forEach(item => {
        item.deleted = item.deleteFlag ? 1 : 0
      })
      tagGroupClassList.forEach(item => {
        item.deleted = item.deleteFlag ? 1 : 0
      })

      let courseComment = {
        //--教练评价--
        COACH_RATING_SWITCH: {
            id:this.courseCommentSetting.COACH_RATING_SWITCH.id,
            value:this.memberSetting.assessment.commentFlag ? 1 : 0
        },
        COACH_RATING_TYPE_LIST: categoryList,
        COACH_APPRAISE_TAGS_LIST: tagList,
        COACH_FEEDBACK_SWITCH: {
            id:this.courseCommentSetting.COACH_FEEDBACK_SWITCH.id,
            value: this.memberSetting.assessment.commentFeedback ? 1 : 0
        },
        COACH_PUSH_TIME: {
            id:this.courseCommentSetting.COACH_PUSH_TIME.id,
            value: this.memberSetting.assessment.commentMinutes
        },

        COACH_MAXIMUM_SCORE_VALUE: {
            id:this.courseCommentSetting.COACH_MAXIMUM_SCORE_VALUE.id,
            value:  this.memberSetting.assessment.commentStar
        },
        COACH_MAXIMUM_EVALUABLE_TIME: {
            id:this.courseCommentSetting.COACH_MAXIMUM_EVALUABLE_TIME.id,
            value: this.memberSetting.assessment.commentDays
        },
        COACH_WHETHER_TO_TURN_ON_THE_REVIEW: {
            id:this.courseCommentSetting.COACH_WHETHER_TO_TURN_ON_THE_REVIEW.id,
            value: this.memberSetting.assessment.commentFollowUp ? 1 : 0
        },
        COACH_MAXIMUM_REVIEW_TIME: {
            id:this.courseCommentSetting.COACH_MAXIMUM_REVIEW_TIME.id,
            value: this.memberSetting.assessment.commentFollowUpDays
        },

        //--团课评价--
        GROUP_CLASS_EVALUATION_SWITCH: {
            id:this.courseCommentSetting.GROUP_CLASS_EVALUATION_SWITCH.id,
            value:this.memberSetting.groupClassEvalution.commentFlag ? 1 : 0
        },
        GROUP_CLASS_GRADING_TYPE_LIST: categoryGroupClassList,
        GROUP_CLASS_LABEL_SETTINGS_LIST: tagGroupClassList,
        GROUP_CLASS_MAXIMUM_SCORE_VALUE: {
            id:this.courseCommentSetting.GROUP_CLASS_MAXIMUM_SCORE_VALUE.id,
            value: this.memberSetting.groupClassEvalution.commentStar
        },
        GROUP_CLASS_FEEDBACK_SWITCH: {
            id:this.courseCommentSetting.GROUP_CLASS_FEEDBACK_SWITCH.id,
            value: this.memberSetting.groupClassEvalution.commentFeedback ? 1 : 0
        },
        GROUP_CLASS_MINIMUM_ASSESSABLE_TIME_AFTER_THE_END_OF_THE_CLASS: {
            id:this.courseCommentSetting.GROUP_CLASS_MINIMUM_ASSESSABLE_TIME_AFTER_THE_END_OF_THE_CLASS.id,
            value: this.memberSetting.groupClassEvalution.commentMinutes
        },
        GROUP_CLASS_THE_MAXIMUM_EVALUATION_TIME_AFTER_THE_COURSE: {
            id:this.courseCommentSetting.GROUP_CLASS_THE_MAXIMUM_EVALUATION_TIME_AFTER_THE_COURSE.id,
            value: this.memberSetting.groupClassEvalution.commentDays
        },
        GROUP_CLASS_FOLLOW_UP_SWITCH: {
            id:this.courseCommentSetting.GROUP_CLASS_FOLLOW_UP_SWITCH.id,
            value: this.memberSetting.groupClassEvalution.commentFollowUp ? 1 : 0
        },
        GROUP_CLASS_COURSE_MAXIMUM_FOLLOW_UP_ASSESSMENT_TIME: {
            id:this.courseCommentSetting.GROUP_CLASS_COURSE_MAXIMUM_FOLLOW_UP_ASSESSMENT_TIME.id,
            value: this.memberSetting.groupClassEvalution.commentFollowUpDays
        },
      }

      return this.$service
        .post('/Web/MemberSetting/update_setting', {
          ...this.memberSetting.info,
        //   set_open: this.memberSetting.assessment.commentFlag ? 1 : 0,
        //   set_type: categoryList,
        //   set_tag: tagList,
        //   set_advice: this.memberSetting.assessment.commentFeedback ? 1 : 0,
        //   set_time: this.memberSetting.assessment.commentMinutes
        courseComment: courseComment
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            const self = this
            setTimeout(() => {
              self.getTweetSettings()
              self.isEditState4 = false
            }, 1000)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSaveCategory() {
      const temp = this.categoryInputTemp.trim()
      if (!!temp) {
        if (this.categoryInputTempIdx === -1) {
          const noneCategory = {
            value: temp,
            deleteFlag: false,
            checkFlag: false
          }
          this.memberSetting.assessment.commentCategory.push(noneCategory)
        } else {
          let tempCategory = this.memberSetting.assessment.commentCategory[
            this.categoryInputTempIdx
          ]
          tempCategory.value = temp
          tempCategory.deleteFlag = false
          tempCategory.checkFlag = false
        }
        this.categoryInputTemp = ''
        this.categoryInputTempIdx = -1
        this.categoryInputBoxFlag = false
      }
    },
    handleSaveGroupClassCategory() {
      const temp = this.categoryGroupClassInputTemp.trim()
      if (!!temp) {
        if (this.categoryGroupClassInputTempIdx === -1) {
          const noneCategory = {
            value: temp,
            deleteFlag: false,
            checkFlag: false
          }
          this.memberSetting.groupClassEvalution.commentCategory.push(noneCategory)
        } else {
          let tempCategory = this.memberSetting.groupClassEvalution.commentCategory[
            this.categoryGroupClassInputTempIdx
          ]
          tempCategory.value = temp
          tempCategory.deleteFlag = false
          tempCategory.checkFlag = false
        }
        this.categoryGroupClassInputTemp = ''
        this.categoryGroupClassInputTempIdx = -1
        this.categoryGroupClassInputBoxFlag = false
      }
    },
    handleUpdateCategory(checked, idx) {
      this.memberSetting.assessment.commentCategory.forEach((item, index) => {
        if (index == idx) {
          item.checkFlag = true
        } else {
          item.checkFlag = false
        }
      })
      if (checked) {
        const item = this.memberSetting.assessment.commentCategory[idx]
        this.categoryInputBoxFlag = true
        this.categoryInputTemp = item.value
        this.categoryInputTempIdx = idx
      } else {
        this.categoryInputBoxFlag = false
        this.categoryInputTemp = ''
        this.categoryInputTempIdx = -1
      }
    },
    handleUpdateGroupClassCategory(checked, idx) {
      this.memberSetting.groupClassEvalution.commentCategory.forEach((item, index) => {
        if (index == idx) {
          item.checkFlag = true
        } else {
          item.checkFlag = false
        }
      })
      if (checked) {
        const item = this.memberSetting.groupClassEvalution.commentCategory[idx]
        this.categoryGroupClassInputBoxFlag = true
        this.categoryGroupClassInputTemp = item.value
        this.categoryGroupClassInputTempIdx = idx
      } else {
        this.categoryGroupClassInputBoxFlag = false
        this.categoryGroupClassInputTemp = ''
        this.categoryGroupClassInputTempIdx = -1
      }
    },
    handleToggleCategory() {
      this.categoryInputBoxFlag = !this.categoryInputBoxFlag
      this.categoryInputTemp = ''
      this.categoryInputTempIdx = -1
      this.memberSetting.assessment.commentCategory.forEach(item => {
        item.checkFlag = false
      })
    },
    handleToggleGroupClassCategory() {
      this.categoryGroupClassInputBoxFlag = !this.categoryGroupClassInputBoxFlag
      this.categoryGroupClassInputTemp = ''
      this.categoryGroupClassInputTempIdx = -1
      this.memberSetting.groupClassEvalution.commentCategory.forEach(item => {
        item.checkFlag = false
      })
    },
    handleCloseCategory(event, index) {
      const idx = parseInt(index)
      if (typeof idx === 'number') {
        this.memberSetting.assessment.commentCategory[idx].deleteFlag = true
      }
      this.$forceUpdate()
    },
    handleCloseGroupClassCategory(event, index) {
      const idx = parseInt(index)
      if (typeof idx === 'number') {
        this.memberSetting.groupClassEvalution.commentCategory[idx].deleteFlag = true
      }
      this.$forceUpdate()
    },
    handleSaveTag() {
      const temp = this.tagInputTemp.trim()
      if (!!temp) {
        if (this.tagInputTempIdx === -1) {
          const noneTag = { value: temp, deleteFlag: false, checkFlag: false }
          this.memberSetting.assessment.commentTag.push(noneTag)
        } else {
          let tempTag = this.memberSetting.assessment.commentTag[
            this.tagInputTempIdx
          ]
          tempTag.value = temp
          tempTag.deleteFlag = false
          tempTag.checkFlag = false
        }
        this.tagInputTemp = ''
        this.tagInputTempIdx = -1
        this.tagInputBoxFlag = false
      }
    },
    handleSaveGroupClassTag() {
      const temp = this.tagGroupClassInputTemp.trim()
      if (!!temp) {
        if (this.tagGroupClassInputTempIdx === -1) {
          const noneTag = { value: temp, deleteFlag: false, checkFlag: false }
          this.memberSetting.groupClassEvalution.commentTag.push(noneTag)
        } else {
          let tempTag = this.memberSetting.groupClassEvalution.commentTag[
            this.tagGroupClassInputTempIdx
          ]
          tempTag.value = temp
          tempTag.deleteFlag = false
          tempTag.checkFlag = false
        }
        this.tagGroupClassInputTemp = ''
        this.tagGroupClassInputTempIdx = -1
        this.tagGroupClassInputBoxFlag = false
      }
    },
    handleUpdateTag(checked, idx) {
      this.memberSetting.assessment.commentTag.forEach((item, index) => {
        if (index == idx) {
          item.checkFlag = true
        } else {
          item.checkFlag = false
        }
      })
      console.log(this.memberSetting.assessment.commentTag)
      if (checked) {
        const item = this.memberSetting.assessment.commentTag[idx]
        this.tagInputBoxFlag = true
        this.tagInputTemp = item.value
        this.tagInputTempIdx = idx
      } else {
        this.tagInputBoxFlag = false
        this.tagInputTemp = ''
        this.tagInputTempIdx = -1
      }
    },
    handleUpdateGroupClassTag(checked, idx) {
      this.memberSetting.groupClassEvalution.commentTag.forEach((item, index) => {
        if (index == idx) {
          item.checkFlag = true
        } else {
          item.checkFlag = false
        }
      })
      console.log(this.memberSetting.groupClassEvalution.commentTag)
      if (checked) {
        const item = this.memberSetting.groupClassEvalution.commentTag[idx]
        this.tagGroupClassInputBoxFlag = true
        this.tagGroupClassInputTemp = item.value
        this.tagGroupClassInputTempIdx = idx
      } else {
        this.tagGroupClassInputBoxFlag = false
        this.tagGroupClassInputTemp = ''
        this.tagGroupClassInputTempIdx = -1
      }
    },
    handleToggleTag() {
      this.tagInputBoxFlag = !this.tagInputBoxFlag
      this.tagInputTemp = ''
      this.tagInputTempIdx = -1
      this.memberSetting.assessment.commentTag.forEach(item => {
        item.checkFlag = false
      })
    },
    handleToggleGroupClassTag() {
      this.tagGroupClassInputBoxFlag = !this.tagGroupClassInputBoxFlag
      this.tagGroupClassInputTemp = ''
       this.tagGroupClassInputTempIdx = -1
      this.memberSetting.groupClassEvalution.commentTag.forEach(item => {
        item.checkFlag = false
      })
    },
    handleCloseTag(event, index) {
      const idx = parseInt(index)
      if (typeof idx === 'number') {
        this.memberSetting.assessment.commentTag[idx].deleteFlag = true
      }
      this.$forceUpdate()
    },
    handleCloseGroupClassTag(event, index) {
      const idx = parseInt(index)
      if (typeof idx === 'number') {
        this.memberSetting.groupClassEvalution.commentTag[idx].deleteFlag = true
      }
      this.$forceUpdate()
    },
    getReserveStadiumSettings() {
      return this.$service.post('/Web/Space/getSetting').then(res => {
        if (res.data.errorcode == 0) {
          if (res.data.data) {
            this.reserveStadiumData = {
              user_range: res.data.data.user_range
                ? res.data.data.user_range
                : 1,
              pay_type: res.data.data.pay_type ? res.data.data.pay_type : [2],
              booking_time: res.data.data.booking_time
                ? res.data.data.booking_time
                : 1,
              booking_future_days: res.data.data.booking_future_days
                ? res.data.data.booking_future_days
                : 6,
              booking_num: res.data.data.booking_num
                ? res.data.data.booking_num
                : 0,
              cancel_booking_after_order: res.data.data
                .cancel_booking_after_order
                ? res.data.data.cancel_booking_after_order
                : 2,
              cancel_booking_before_start: res.data.data
                .cancel_booking_before_start
                ? res.data.data.cancel_booking_before_start
                : 30,
              able_mark_limit_time: res.data.data.able_mark_limit_time || 24,
              mark_group_cutoff_time: res.data.data.mark_group_cutoff_time || 8,
              booking_notice_swtich: res.data.data.booking_notice_swtich
                ? res.data.data.booking_notice_swtich
                : 1,
              booking_notice: res.data.data.booking_notice
                ? res.data.data.booking_notice
                : '',
              allow_time: res.data.data.allow_time
            }
            if (this.reserveStadiumData.booking_num === 0) {
              this.reserveStadiumData.booking_num_set = 0
            } else {
              this.reserveStadiumData.booking_num_set = 1
            }
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    setReserveStadiumSettings() {
      return this.$service
        .post('/Web/Space/saveSetting', this.reserveStadiumData)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.isEditState7 = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    // 处理空值和小数
    handleBlurCourse(e, target, key) {
      this.$nextTick(() => {
        if (!target[key]) {
          if (key === 'confirm_time') {
            target[key] = 30
          } else {
            target[key] = 1
          }
        } else {
          target[key] = parseInt(target[key])
        }
      })
    },

    // 散场票设置-保存
    setTicketSettings(){
      const params = {
        cancel_member: this.ticketData.cancel_member
      }
      this.$service
        .post('/Web/San/update_san_setting', params)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.isEditTicket = false
          } else {
            this.getTicketSet()
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    // 散场票设置-取消
    handleCancelEditTicket() {
      this.isEditTicket = false
      this.getTicketSet()
    },
    // 散场票设置-获取
    getTicketSet() {
      return this.$service.post('/Web/San/get_san_setting').then(res => {
        if (res.data.errorcode == 0) {
          if (res.data.data) {
            this.ticketData = {
              cancel_member: res.data.data.cancel_member ? 1 : 0
            }
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // third party settings
    getThirdPartySettings() {
      return this.$service.get('/Web/SanGroup/getThirdPlatformShopId').then(res => {
        if (res.data.errorcode == 0) {
          const list = res.data.data
          if (Array.isArray(list) && list.length === 3) {
            list.sort((a, b) => {
              return Number(a.platform_id) - Number(b.platform_id)
            })
            this.thirdPartySettings = list
          } else {
            this.thirdPartySettings = NONE_THIRD_PARTY_SETTINGS
          }
        } else {
          this.thirdPartySettings = NONE_THIRD_PARTY_SETTINGS
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSaveThirdPartySettings() {
      return this.$service.post('/Web/SanGroup/updateThirdPlatformShopId', {
        list: this.thirdPartySettings
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.isEditThirdParty = false
          this.$Message.success(res.data.errormsg)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(() => {
        this.handleCancelThirdPartySettings()
        this.$Message.error('保存失败')
      })
    },
    handleCancelThirdPartySettings() {
      this.isEditThirdParty = false
      this.getThirdPartySettings()
    }
  },
  mounted() {
    this.getAuthority()
  }
}
</script>

<style lang="less" scoped>
.tag-box {
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  flex-wrap: wrap;
}

.b-box {
  font-size: 14px;
}

.row-lh {
  height: 52px;
  display: flex;
  align-items: center;
}

.btnnn-box {
  width: 70%;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

.form-box {
  margin-top: 20px;
  border: 1px solid #d7dde4;
  background: white;
  padding: 10px;
}

.span-block {
  display: block;
}
</style>
