<template>
  <div class="coach-info">
    <div class="card">
      <div class="avatar">
        <img :src="data.avatar" alt="">
      </div>
      <div class="intro">
        <h3>{{ data.name }}</h3>
        <div class="list">
          <div class="item">
            <div class="key">职务</div>
            <div class="value">{{ data.position }}</div>
          </div>
          <div class="item">
            <div class="key">擅长</div>
            <div class="tags">
              <div class="tag" v-for="(item, index) in data.specialty.split(',')">{{ item }}</div>
            </div>
          </div>
          <div class="item">
            <div class="key">课程</div>
            <div class="value">{{ data.courses_content }}</div>
          </div>
          <div class="item">
            <div class="key">荣誉</div>
            <div class="value" v-html="data.aptitude">{{ data.aptitude }}</div>
          </div>
        </div>
        <div class="rich-text" v-html="data.other_intr"></div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'coachInfo',
    props: {
      data: {}
    }
  };
</script>

<style lang="less">
  .rich-text {
    font-size: 12px;
    color: #898989;
    line-height: 1.4;
    padding-top: 16px;
    padding-bottom: 25px;
    img {
      max-width: 100%;
    }
  }
</style>

<style lang="less" scoped>
  .coach-info {
    background: #f5f7f9 url(https://imagecdn.rocketbird.cn/minprogram/member/image/coach-bg.png) no-repeat center top /
      contain;
    width: 375px;
    padding: 16px 35px 40px;
    .card {
      background-color: #fff;
      box-shadow: 0 4px 5px 0 rgba(65, 92, 145, 0.15);
      padding-top: 23px;
    }
    .avatar {
      margin: 0 auto;
      height: 193px;
      width: 193px;
      background-color: #ccc;
      img {
        width: 100%;
        height: 100%;
        box-shadow: 0 4px 5px 0 rgba(65, 92, 145, 0.15);
      }
    }
    .intro {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 20px 30px;
      background-color: #fff;
      h3 {
        font-size: 15px;
        color: #313131;
        border-bottom: 2px solid #ca2e53;
        padding-bottom: 7px;
        margin-bottom: 25px;
      }
      .list {
        align-self: stretch;
        .item {
          display: flex;
          margin-bottom: 20px;
          .key {
            color: #b3b3b3;
            flex: 0;
            margin-right: 18px;
            min-width: 25px;
          }
          .value {
            flex: 1;
          }
          .tags {
            display: flex;
            flex-wrap: wrap;
            .tag {
              display: flex;
              justify-content: center;
              align-items: center;
              box-sizing: border-box;
              font-size: 10px;
              min-width: 45px;
              padding: 0 7px;
              height: 16px;
              color: #8abcc3;
              border-radius: 2px;
              margin-right: 3px;
              margin-bottom: 3px;
              border: 1px solid #e7e7e7;
            }
          }
        }
      }
    }
  }
</style>
