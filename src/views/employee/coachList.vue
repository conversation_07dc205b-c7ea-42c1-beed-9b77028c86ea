<style lang="less" scoped>
  .coach-intro /deep/ .ivu-modal-body {
    padding: 0;
  }
  .coach-intro /deep/ .ivu-modal-footer {
    padding: 0;
  }

  .coach-list {
    width: 100%;
    display: flex;
    overflow: hidden;
    .tree {
      width: 240px;
    }
    .table-wrap {
      flex: 1;
    }
    .hide-tree {
      display: none;
    }
    .group-switch {
      min-width: 40px;
      cursor: pointer;
    }

  }

  .delete-coach-content {
    .delete-coach-word {
      font-size: 14px;
    }
    .delete-coach-word + .delete-coach-word {
      margin-top: 12px;
    }
  }
</style>
<style lang="less">
.coach-list {
  .call-wrap {
    position: relative;
    width: 80px;
    height: 50px;
    .ico-phone-notuse, .ico-phone-use {
      display: block;
      margin: 20px auto 0;
    }
  }
  .pos-tag {
    position: absolute;
    font-size: 10px;
    right: 15px;
    top: -8px;
  }
  .green {
    color: green;
  }
  .blue {
    color: #1890ff;
  }
}
</style>
<template>
  <div>
    <router-view v-if="$route.name !== '教练'"></router-view>
    <div class="coach-list" v-else>
      <Modal v-model="showCoachInfo" :width="375" class="coach-intro">
        <CoachInfo :data="coachInfoData" v-if="showCoachInfo"></CoachInfo>
        <div slot="footer"></div>
      </Modal>
      <Modal title="指派教练" v-model="showDelModal">
        <div style="display: flex; flex-direction: column; align-items: center">
          <span style="color: red; font-size: 14px">该教练名下的私教课学员指派给其他教练后才能删除</span>
          <Form v-if="delData" style="width: 80%; padding-top: 20px" label-position="left">
            <FormItem v-for="(user, index) in delData.card_user_list" :key="index" style="display: flex; align-items: center; justify-content: space-between" :label="`${user.username}【${user.last_nums}】`">
              <Select v-model="user.coach_id" style="width: 130px;" transfer>
                <Option v-for="coach in delData.coach_list" v-if="coach.is_swim?user.card_type_id === '5' :  user.card_type_id !== '5'" :key="coach.coach_id" :value="coach.coach_id">{{coach.coach_name}}</Option>
              </Select>
            </FormItem>
          </Form>
        </div>
        <div slot="footer" class="modal-buttons">
          <Button type="success" @click="assign">确定</Button>
          <Button @click="showDelModal = false">取消</Button>
        </div>
      </Modal>
      <div class="tree" :class="{'hide-tree': !searchData.showTree}">
        <CoachNav @groupChange="groupChanged"></CoachNav>
      </div>
      <div class="table-wrap">
        <header>
          <Input style="width: 150px" v-model="searchData.param" @on-enter="getList" placeholder="姓名/电话"></Input>
          <Select style="width: 120px" v-if="positionList && positionList.length" v-model="searchData.position_id" clearable placeholder="职务">
            <Option v-for="position in positionList" :key="position.id" :value="position.id">{{position.name}}</Option>
          </Select>
          <Select style="width: 120px" v-model="searchData.coach_type" placeholder="教练类型" clearable>
            <Option value="私教教练">私教教练</Option>
            <Option value="操课教练">操课教练</Option>
            <Option value="游泳教练">游泳教练</Option>
          </Select>
          <Select style="width: 120px" v-model="searchData.use_coach_manage" placeholder="教练端" clearable>
            <Option value="1">已开通</Option>
            <Option value="0">未开通</Option>
          </Select>
          <Button type="success" @click="doSearch">搜索</Button>
        </header>
        <main>
          <Table @on-selection-change="onSelectionChange" @on-select="onSelect" @on-select-all="onSelectAll" @on-select-cancel="onSelectCancel" v-if="tableData" :columns="columns" :data="tableData" ref="table" class="avatar-zoom" stripe disabled-hover></Table>
        </main>
        <footer>
          <router-link to="/employee/coach/add">
            <Button type="success" style="margin-right: 30px">添加
            </Button>
          </router-link>
          <Dropdown placement="top" @on-click="otherCase" v-if="notExport">
            <Button>其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="0">导出excel</DropdownItem>
              <DropdownItem name="1">批量设置允许教授课程</DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <Pager :total="pageTotal" :postData="searchData" @on-change="pageChange"></Pager>
        </footer>
      </div>
    </div>
    <div v-if="showImgUpload">
      <ImgUploadWithMedia v-model="showImgUpload" @on-success="upImgSuccess" />
    </div>
    <rfid v-model="showRfid" :rfid-id="RFID" :beforeBind="beforeBind"></rfid>
    <div v-if="showCourseModal">
      <SelectGroupeData v-model="showCourseModal" @on-confirm="confirmSelect" :checked="selectionId" :is-swim-coach="hasSwimCoach"/>
    </div>
    <!-- 弹窗-删除教练 -->
    <Modal v-model="deleteCoachModal" :title="deleteCoachTitle" width="500">
      <div class="delete-coach-content">
        <div class="delete-coach-word">
          <template v-if="memberNum">
            <span>该教练名下有上课会员</span>
            <b style="color: red">{{ memberNum }}</b>
            <span>名，</span>
          </template>
          <template v-if="followupNum">
            <span>跟进会员</span>
            <b style="color: red">{{ followupNum }}</b>
            <span>名，</span>
          </template>
          <template v-if="!followupNum && !memberNum">
            <span>确定要删除教练</span>
            <b style="color: red">{{ coachInfo.name }}</b>
            <span>?</span>
          </template>
        </div>
        <div class="delete-coach-word">
          <span v-if="memberNum || followupNum">请将会员指派给其他教练。选择“直接删除教练”后，这些会员会自动解除和该教练的跟进关系</span>
        </div>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button  v-if="!followupNum && !memberNum" @click="handleDeleteCoach">删除</Button>
        <Button v-if="!followupNum && !memberNum" @click="handleDeleteCancel">取消</Button>
        <Button v-if="memberNum || followupNum" @click="handleForceDeleteCoach">直接删除教练</Button>
        <Button type="success" v-if="memberNum || followupNum" @click="handleCoachAssign">指派给其他教练</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import CoachNav from '../../components/employeeNav/coach.vue';
  import CoachInfo from './components/coachInfo.vue';
  import Unbind from 'mixins/unbind';
  import Pager from 'components/pager';
  import SelectGroupeData from "components/form/SelectGroupeData";
  import ImgUploadWithMedia from 'src/components/form/ImgUploadWithMedia.vue';
  import rfid from 'src/views/member/components/AddRfid.vue';
  import Selection from 'mixins/selection';

  export default {
    name: 'coachList',
    mixins: [Unbind, Selection],
    components: {
      CoachNav,
      CoachInfo,
      Pager,
      SelectGroupeData,
      ImgUploadWithMedia,
      rfid
    },
    data() {
      return {
        RFID: '',
        showRfid: false,
        showImgUpload: false,
        showDelModal: false,
        hasSwimCoach: false,
        hasPtCoach: false,
        allPrivate: [],
        delData: null,
        searchData: {
          showTree: true,
          param: '',
          position_id: '',
          id: '', //分组id
          coach_type: '',
          use_coach_manage: '',
          page_no: 1,
          page_size: 10
        },
        showCoachInfo: false,
        showCourseModal: false,
        coachInfoData: null,
        notExport: true,
        pageTotal: 0,
        exportData: null,
        tableData: [],
        columns: [
          {
            type: 'selection',
            width: 60
          },
          {
            title: '形象照',
            key: 'avatar',
            className: 'avatar-wrap',
            render: (h, params) => {
              let url = params.row.avatar;
              if (url) {
                return <img src={url} class="avatar" style={{ width: '30px', height: '30px' }} />;
              } else {
                return <span> - </span>;
              }
            }
          },
          {
            title: '头像',
            key: 'face',
            className: 'avatar-wrap',
            render: (h, params) => {
              const item = params.row;
              return item.face_avatar ? (
                <img
                  src={item.face_avatar}
                  class="avatar"
                  style={{ width: '30px', height: '30px', cursor: 'pointer' }}
                  onClick={() => {
                    this.unbind({ coach_id: item.id, bind_type: 1 });
                  }}
                />
              ) : (
                ''
              );
            }
          },
          {
            title: '姓名',
            key: 'name',
            render: (h, params) => {
              const cb = ({ id, type }) => {
                if (type === 'rfid') {
                  this.unbind({ coach_id: id, bind_type: 2 });
                }
              };
              return this.nameRender(params, 2, cb);
            }
          },
          {
            title: '电话',
            key: 'phone'
          },
          {
            title: '职务',
            key: 'position'
          },
          {
            title: '教练类型',
            key: 'coach_type',
            render: (h, params) => {
              const coachType = params.row.coach_type.split(",");
              let renderDom = []
              coachType.forEach(item =>{
                if (item === '私教教练') {
                  renderDom.push(<tag color="blue">私</tag>)
                } else if (item === '游泳教练') {
                  renderDom.push(<tag color="gold">泳</tag>)
                } else {
                  renderDom.push(<tag color="green">操</tag>)
                }
              })
              return renderDom;
            }

          },
          {
            title: '授课/跟进会员',
            key: 'member',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <router-link to={{ path: '/member', query: { class_coach_id: item.id, curMenu: 'search' } }}>
                    {item.member}
                  </router-link>{' '}
                  /{' '}
                  <router-link to={{ path: '/member', query: { followup_coach_id: item.id, curMenu: 'search' } }}>
                    {item.followup}
                  </router-link>
                </div>
              );
            }
          },
          {
            title: '私教端',
            key: 'use_coach_manage',
            width: 80,
            render: (h, params) => {
              const use = params.row.use_coach_manage;
              const auth = params.row.data_authority;
              return (
                <div class="call-wrap">
                 <div class={'pos-tag ' + (auth == '组长'?'blue':auth == '经理'?'green':'')}>{auth != '教练' && auth}</div>
                 <div class={+use === 1 ? 'ico-phone-use' : 'ico-phone-notuse'} title={+use === 1 ? '已开通' : '未开通'} />
                </div>
              );
            }
          },
          {
            title: '教练介绍',
            key: 'id',
            render: (h, params) => {
              let id = params.row.id;
              return (
                <i-button
                  onClick={() => {
                    this.clickCoachInfo(id);
                  }}
                  type="text">
                  详情
                </i-button>
              );
            }
          },
          {
            title: '操作',
            key: 'id',
            render: (h, params) => {
              let id = params.row.id;
              const index = this.tableData.findIndex(item => item.id === id);
              const placement = index < this.tableData.length - 1 ? 'bottom' : 'top';

              return (
                <dropdown
                  placement={placement}
                  on-on-click={name => {
                    this.dropdownClick(name, id, params.row);
                  }}>
                  <i-button >
                    操作
                    <icon style="margin-left: 4px; margin-right: -4px" type="md-arrow-dropdown" />
                  </i-button>
                  <dropdown-menu slot="list">
                    <dropdown-item name="edit">编辑</dropdown-item>
                    <dropdown-item name="del">删除</dropdown-item>
                    <dropdown-item name="avatar">传头像</dropdown-item>
                    <dropdown-item name="rfid">绑RFID</dropdown-item>
                  </dropdown-menu>
                </dropdown>
              );
            }
          }
        ],
        deleteCoachModal: false,
        deleteCoachTitle: '',
        followupNum: 0,
        memberNum: 0,
        coachInfo: {}
      };
    },
    created() {
      this.getCoachPosition();
    },
    computed: {
      ...mapState('employee', {
        positionList: 'coachPosition'
      })
    },
    methods: {
      confirmSelect(ids) {
        this.$service.post('/Web/Coach/batch_set_permitted_class', {
          is_swim: this.hasSwimCoach?1:0,
          coach_ids: this.selectionId,
          permitted_class: ids
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.selection = []
            this.tableData = this.tableData.map(item => {
              return { ...item, _checked: false }
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      ...mapActions('employee', ['getCoachPosition']),
      // bind_type: 1 人脸 2 rfid
      handleBind({ bind_type, bind_data }) {
        const url = '/Web/Coach/bind_property';
        return this.$service
          .post(url, { coach_id: this.bindCoachId, bind_type, bind_data })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      unbind({ bind_type, coach_id }) {
        const title = ['解绑头像', '解绑RFID'];
        const content = ['点击确定解绑人脸头像', '点击确定解绑RFID'];
        this.$Modal.confirm({
          title: title[bind_type - 1],
          content: content[bind_type - 1],
          onOk: () => {
            const url = '/Web/Coach/unbind_property';
            this.$service
              .post(url, { bind_type, coach_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success('解绑成功');
                  this.getList();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        });
      },
      upImgSuccess({ imgUrl }) {
        this.handleBind({ bind_type: 1, bind_data: imgUrl });
      },
      beforeBind(rfid) {
        return this.handleBind({ bind_type: 2, bind_data: rfid });
      },
      handleAvatarUpload(imgData) {
        let postData = {
          image_data: imgData,
          _type: 'platform'
        };
        return this.$service
          .post('/Admin/Public/upload_image', postData)
          .then(response => {
            if (response.status === 200) {
              resolve(response.data.path);
            } else {
              this.$Message.error(response.data.statusText);
            }
          })
          .catch(() => {
            this.$Message.error('网络错误');
          });
      },
      dropdownClick(name, coachId, info) {
        this.bindCoachId = coachId;
        const event = {
          edit: () => {
            this.$router.push({ path: '/employee/coach/add', query: { id: coachId } });
          },
          del: () => this.delCoach(info),
          avatar: () => {
            this.showImgUpload = true;
          },
          rfid: () => {
            this.RFID = this.tableData.find(item => item.id === coachId).rfid;
            this.showRfid = true;
          }
        };
        event[name]();
      },
      pageChange(postData) {
        this.searchData = { ...this.searchData, ...postData };
        this.getList();
      },
      doSearch() {
        this.searchData.page_no = 1;
        this.getList();
      },
      getList() {
        const url = '/Web/Coach/get_bus_coach_list';
        this.$service
          .post(url, this.searchData)
          .then(res => {
            if (res.data.errorcode === 0) {
              let data = res.data.data;
              const list = data.list;
              this.tableData = list.map(item => {
                return { ...item, _checked: this.selectionId.includes(item.id) }
              });
              this.pageTotal = data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      assign() {
        let flag = false;
        this.delData.card_user_list.forEach(item => {
          if (!item.coach_id) {
            this.$Message.error('还有未指派的用户');
            flag = true;
            return false;
          }
          let coach = this.delData.coach_list.find(coach => coach.coach_id === item.coach_id);
          item.coach_name = coach.coach_name;
        });

        if (flag) return false;

        let url = '/Web/Coach/update_card_user_coach';
        let postData = {
          old_coach_id: this.delData.id,
          card_user_list: this.delData.card_user_list
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showDelModal = false;
              this.confirmDel(this.delData.id);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      confirmDel(id, type) {
        let url = '/Web/Coach/del_bus_coach';
        let params = { id }
        if(type) {
          params.type = type
        }
        this.$service
          .post(url, params)
          .then(res => {
            if (res.data.errorcode === 0) {
              if (res.data.data) {
                this.delData = res.data.data;
                this.delData.id = id;
                this.showDelModal = true;
              } else {
                this.$Message.success('删除成功');
                this.getList();
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.handleDeleteCancel()
          })
          .catch(err => {
            console.error(err);
          });
      },
      delCoach(info) {
        this.followupNum = +info.followup
        this.memberNum = +info.member
        this.coachInfo = info
        this.deleteCoachModal = true
        this.deleteCoachTitle = `确定要删除教练 ${info.name}?`
      },
      groupChanged(id) {
        if (id === this.searchData.id) return;
        this.searchData.page_no = 1;
        this.searchData.id = id;
        this.getList();
      },
      clickCoachInfo(id) {
        this.coachInfoData = this.tableData.find(item => item.id === id);
        this.showCoachInfo = true;
      },
      getExportList(isExport) {
        const url = '/Web/Coach/get_bus_coach_list';
        let postData = {
          ...this.searchData,
          ...{
            p: 1,
            page_size: this.pageTotal
          }
        };
        return this.$service
          .post(url, postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              let resData = res.data.data;
              this.exportData = resData.list.map(item => {
                return {
                  ...item,
                  ...{
                    coach_type: item.coach_type.split(',').join('、'),
                    position: item.position.split(',').join('、'),
                    use_coach_manage: item.use_coach_manage == 1 ? '已开通' : '未开通',
                    member: `\u0009${item.member} / ${item.followup}`
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
              return false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async otherCase(val) {
        if (val == '0') {
          await this.getExportList(true);
          if (!this.exportData) return false;

          this.notExport = false;
          this.$refs.table.exportCsv({
            filename: '教练列表',
            columns: this.columns.filter((col, index) => index < 9 && index > 0 && index !== 6),
            data: this.exportData
          });
          setTimeout(() => {
            this.notExport = true;
          }, 100);
        } else {
          this.setClass()
        }
      },
      setClass() {
        if (this.selectionId.length < 1) {
          this.$Message.error('请先勾选需要设置的教练!');
          return;
        }
        this.hasPtCoach = false;
        this.hasSwimCoach = false;
        this.selection.forEach(item => {
          if (item.coach_type.indexOf('私教教练') != -1) {
            this.hasPtCoach = true;
          }
          if (item.coach_type.indexOf('游泳教练') != -1) {
            this.hasSwimCoach = true;
          }
        });
        if (this.hasSwimCoach && this.hasPtCoach) {
          this.$Message.error('批量设置不能同时选中泳教和私教!');
          return;
        }
        this.showCourseModal = true
      },
      handleForceDeleteCoach(){
        console.log(this.coachInfo)
        {/* 直接删除教练 */}
        this.confirmDel(this.coachInfo.id, 1);
      },
      handleDeleteCoach(){
        {/* 直删除教练 */}
        this.confirmDel(this.coachInfo.id);
      },
      handleDeleteCancel(){
        {/* 取消 */}
        this.deleteCoachModal = false;
      },
      handleCoachAssign(){
        {/* 去指派 */}
        this.$router.push({ path: '/member', query: { followup_coach_id: this.coachInfo.id, curMenu: 'search' }})
        this.handleDeleteCancel()
      }
    }
  };
</script>
