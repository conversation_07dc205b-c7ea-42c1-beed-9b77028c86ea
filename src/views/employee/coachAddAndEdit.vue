<style lang="less">
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .position-edit {
    display: none;
    padding-left: 15px;
  }

  .coachAddEdit {
    * {
      font-size: 14px;
    }

    .ivu-form .ivu-form-item-label {
      font-size: 14px;
      padding-right: 20px;
    }

    .input {
      width: 300px;
    }

    .checkbox {
      width: 30%;

      &:hover {
        .position-edit {
          display: inline-block;
        }
      }
    }

    .form {
      .photo {
        .flex-center;
        border: 1px solid #dcdcdc;
        width: 150px;
        height: 150px;
        box-sizing: border-box;
        padding: 2px;
        margin-bottom: 20px;

        > img {
          width: 100%;
          height: 100%;
        }
      }

      .add-position {
        margin-top: 20px;
        display: flex;
      }

      .add-position-input {
        display: flex;
        align-items: center;
        margin-left: 30px;
      }

      .buttons {
        padding: 20px 20px 50px;
        max-width: 100%;
        display: flex;

        button {
          min-width: 80px;
        }
      }
    }
  }
</style>

<template>
  <div class="container coachAddEdit">
    <header>
      <h3>{{title}}</h3>
    </header>
    <Form label-position="right"
          ref="form"
          :model="formItem"
          :rules="formRules"
          class="form"
          :label-width="180">
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">形象照</p>
          <p class="tip">图片最佳尺寸: 100X100</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="photo">
          <img :src="formItem.avatar">
        </div>
        <imgUploader v-model="formItem.avatar"
          :options="{aspectRatio: 1/1}"
          multiple></imgUploader>
      </FormItem>
      <FormItem label="姓名"
                prop="name">
        <Input v-model="formItem.name"
               autofocus
               class="input"></Input>
      </FormItem>
      <FormItem label="性别">
        <RadioGroup v-model="formItem.sex">
          <Radio label="1">男</Radio>
          <Radio label="2">女</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="手机号码"
                prop="phone">
        <Input v-model="formItem.phone"
               class="input"></Input>
      </FormItem>
      <FormItem label="所在组别"
                prop="group_id">
        <Select v-model="formItem.group_id"
                filterable
                class="input">
          <Option v-for="group in coachGroup"
                  :key="group.id"
                  :value="group.id">{{group.name}}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="教练类型"
                prop="coach_type">
        <CheckboxGroup v-model="formItem.coach_type">
          <Checkbox label="私教教练" :disabled="isSwimCoach"></Checkbox>
          <Checkbox label="游泳教练" :disabled="isPrivateCoach"></Checkbox>
          <Checkbox label="团操课教练"></Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem v-show="isPrivateCoach || isSwimCoach" :rules="{ required: isPrivateCoach || isSwimCoach, message: '请选择教授课程' }" label="允许教授课程"
                prop="permitted_class">
        <Button style="max-width: 300px; overflow: hidden; text-overflow: ellipsis"
                @click="handleCourseSelect">{{courseNames}}
        </Button>
      </FormItem>
      <!-- coach_type 选择了 私教教练 或者 游泳教练 时, 才显示 -->
      <FormItem
        v-if="formItem.coach_type.includes('私教教练') || formItem.coach_type.includes('游泳教练')"
        label="单节付费课方案"
        prop="pt_charge_plan_id">
        <template #label>
          <div class="flex-center" style="justify-content: flex-end">
            <div>单节付费课方案</div>
            <Tooltip content="会员直接付费预约教练上课，不需提前购课，不同课程不同教练每次支付费用不同" transfer>
              <Icon type="md-help-circle" size="16" />
            </Tooltip>
          </div>
        </template>
        <Select v-model="formItem.pt_charge_plan_id"
          class="input"
          filterable
          clearable>
          <Option v-for="plan in planList"
            :key="plan.id"
            :value="plan.id">
            {{plan.name}}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="擅长"
                prop="specialty_id">
        <CheckboxTag style="width: 400px;"
                     v-model="formItem.specialty_id"
                     @tagAdded="getCoachSpecialty"
                     :data.sync="specialtyList"></CheckboxTag>
      </FormItem>
      <FormItem label="职务"
                prop="position_id">
        <CheckboxGroup v-model="formItem.position_id">
          <Checkbox v-for="position in coachPosition"
                    class="checkbox"
                    :key="position.id"
                    :label="position.id">{{position.name}}
            <div class="position-edit">
              <FaIcon name="edit"
                      size="18"
                      color="#5fb75d"
                      title="编辑"
                      v-if="position.bus_id!=='0'"
                      @click.native.prevent="editPosition(position)"></FaIcon>
              <FaIcon name="trash-o"
                      size="18"
                      color="#ccc"
                      title="删除"
                      v-if="position.bus_id!=='0'"
                      @click.native.prevent="deletePosition(position.id)"></FaIcon>
            </div>
          </Checkbox>
        </CheckboxGroup>
        <div class="add-position">
          <Button type="success"
                  @click="clickAddPosition">添加职务
          </Button>
          <div class="add-position-input"
               v-if="addingPosition">
            <Input v-model="positionName"
                   ref="position"
                   :maxlength="10"
                   @on-enter="positionOnEnter"
                   style="width: 180px"
                   placeholder="例如: 高级教练"></Input>
            <Icon v-if="isEditPosition"
                  type="ios-checkmark-circle-outline"
                  @click.native="editCoachPosition"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="确认"></Icon>
            <Icon v-else
                  type="ios-add-circle-outline"
                  @click.native="addCoachPosition"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="添加"></Icon>
            <Icon type="ios-close-circle-outline"
                  @click.native="addingPosition = isEditPosition = false"
                  size="24"
                  color="#d9544f"
                  style="cursor: pointer"
                  title="取消"></Icon>
          </div>
        </div>
      </FormItem>
      <FormItem label="课程项目">
        <Input v-model="formItem.courses_content"
               placeholder="例如: 普拉提机械康复、动感单车"
               class="input"></Input>
      </FormItem>
      <FormItem label="荣誉资质">
        <Input v-model="formItem.aptitude"
               placeholder="例如: 健美协会全能私人教练认证"
               type="textarea" :autosize="{minRows: 4, maxRows: 8}"></Input>
      </FormItem>
      <FormItem label="其他介绍">
        <FormEditor v-model="formItem.other_intr"/>
      </FormItem>
      <FormItem label="向会员展示">
        <RadioGroup v-model="formItem.display">
          <Radio label="1">展示</Radio>
          <Radio label="0">不展示</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="展示排位"
                v-if="formItem.display == 1">
        <Input v-model="formItem.sort"
               placeholder="数值越小，会员端排位越靠前"
               class="input"></Input>
      </FormItem>
      <FormItem label="教练端使用">
        <RadioGroup v-model="formItem.use_coach_manage">
          <Radio label="1">允许使用</Radio>
          <Radio label="0">不允许使用</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="教练端权限">
        <Select v-model="formItem.data_authority"
                class="input">
          <Option value="1">教练（仅管理自己会员）</Option>
          <Option value="2">教练组长（查看组别内会员）</Option>
          <Option value="3">教练经理（管理全部会员）</Option>
        </Select>
      </FormItem>
      <formItem v-if="formItem.coach_type.includes('团操课教练')" label="团操教练端权限">
        <Select v-model="formItem.class_data_authority" class="input">
          <Option value="1">团操教练（查看团操课安排）</Option>
          <Option value="2">团操经理（管理全部团操课）</Option>
        </Select>
      </formItem>
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">宣传照</p>
          <p class="tip">图片最佳尺寸: 660X660</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="photo">
          <img :src="formItem.promotional_photo">
        </div>
        <imgUploader v-model="formItem.promotional_photo"
          :options="{aspectRatio: 1/1}"
          multiple></imgUploader>
      </FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="success"
                  @click="addCoach">保存
          </Button>
          <Button
                  @click="$router.back()">取消
          </Button>
        </div>
      </FormItem>
    </Form>
      <div v-if="showCourseModal || (formItem.permitted_class && formItem.permitted_class.length)">
        <SelectGroupeData  v-model="showCourseModal" @on-confirm="confirmSelect" :checked="formItem.permitted_class" :is-swim-coach="isSwimCoach" @on-edit="confirmSelect" />
      </div>
  </div>
</template>

<script>
  import { mapActions, mapState } from "vuex";
  import CheckboxTag from "components/form/checkboxTag";
  import imgUploader from "components/form/cropper";
  import SelectGroupeData from "components/form/SelectGroupeData";
  import { unescapeHTML } from "utils";
  import FormEditor from "components/form/Editor";

  export default {
    name: "coachAddEdit",
    components: {
      CheckboxTag,
      imgUploader,
      SelectGroupeData,
      FormEditor
    },
    data() {
      return {
        title: "添加",
        courseNames: "选择课程",
        showCourseModal: false,
        indeterminate: false,
        courseSortId: '0',
        sortedCourse: [],
        courseSortData: [],
        editInitClass: [],
        formItem: {
          name: "",
          sex: "1",
          phone: "",
          coach_type: ['私教教练'],
          permitted_class: [],
          group_id: "",
          specialty_id: [],
          position_id: [],
          courses_content: "",
          aptitude: "",
          other_intr: "",
          avatar: "",
          display: 1,
          sort: "",
          use_coach_manage: 0,
          data_authority: "1",
          class_data_authority: "1",
          promotional_photo: '',
          pt_charge_plan_id: '',
        },
        formRules: {
          name: [{ required: true, message: "请填写教练姓名", trigger: "blur" }],
          phone: [
            { required: true, message: "请输入手机号码", trigger: "blur" },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: "手机号码错误",
              trigger: "change"
            }
          ],
          group_id: [
            { required: true, message: "请选择所在组别", trigger: "change" }
          ],
          coach_type: [{ required: true, message: "请选择教练类型" }],
          specialty_id: [{ required: true, message: "请选择教练擅长" }],
          position_id: [{ required: true, message: "请选择教练职务" }]
        },
        allPrivate: [],
        specialtyList: null,
        positionName: "",
        initialName: "",
        isEditPosition: false,
        positionId: "",
        addingPosition: false,
        coachId: "",
        planList: []
      };
    },
    created() {
      let id = this.$route.query.id;
      if (id) {
        this.coachId = id;
        this.title = "编辑";
        this.$route.meta.breadText = "编辑教练";
        this.getCoachInfo(this.$route.query.id).then(res => {
          this.getAllPlanList();
        })
      } else {
        this.title = "添加";
        this.$route.meta.breadText = "添加教练";
        this.getAllPlanList();
      }
      this.getCoachGroup();
      this.getCoachSpecialty();
      this.defaultPosition();
    },
    computed: {
      ...mapState("employee", {
        coachPosition: "coachPosition",
        coachGroup: "coachGroup"
      }),
      isPrivateCoach() {
        return this.formItem.coach_type.indexOf("私教教练") > -1
      },
      isSwimCoach() {
        return this.formItem.coach_type.indexOf("游泳教练") > -1;
      }
    },
    watch: {
      addingPosition(val) {
        if (!val) {
          this.positionName = "";
          this.isEditPosition = false;
        }
      },
      'formItem.coach_type'(val, oldVal) {
        // if (this.editInitClass !== val && oldVal != val) {
        // fix 12215 如果未勾选私教或泳教，才清空允许教授课程
        if (!(this.isPrivateCoach || this.isSwimCoach)) {
          this.formItem.permitted_class = []
          this.courseNames = '选择课程'
        }
        this.getAllPlanList()
      }
    },
    methods: {
      ...mapActions("employee", ["getCoachPosition", "getCoachGroup"]),
      handleCourseSelect() {
        this.showCourseModal = true;
      },
      confirmSelect(info, checkedArr) {
        this.formItem.permitted_class = info
        this.courseNames = checkedArr.reduce((names, item) => names + `${item.card_name}、`, '') || '选择课程';
      },
      async defaultPosition() {
        if (!this.coachPosition) {
          await this.getCoachPosition();
        }
        this.formItem.position_id = this.coachPosition.filter(item => item.name === '教练').map(item => item.id);
      },
      positionOnEnter() {
        if (this.isEditPosition) {
          this.editCoachPosition();
        } else {
          this.addCoachPosition();
        }
      },
      editPosition({ id, name }) {
        this.positionName = this.initialName = name;
        this.positionId = id;
        this.addingPosition = this.isEditPosition = true;
        this.$nextTick(() => {
          this.$refs.position.$refs.input.focus();
        });
      },
      clickAddPosition() {
        this.addingPosition = true;
        this.$nextTick(() => {
          this.$refs.position.$refs.input.focus();
        });
      },
      editCoachPosition() {
        if (this.positionName === this.initialName) {
          this.addingPosition = false;
          return;
        }
        const url = "/Web/Coach/update_coach_posi";
        const postData = {
          id: this.positionId,
          name: this.positionName
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.addingPosition = false;
              this.getCoachPosition();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            throw new Error(err);
          });
      },
      deletePosition(id) {
        this.$Modal.confirm({
          title: "删除职务",
          content: "确认删除该职务吗？",
          onOk: () => {
            const url = "/Web/Coach/del_coach_posi";
            this.$service
              .post(url, { id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getCoachPosition();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                throw new Error(err);
              });
          }
        });
      },
      getCoachInfo(id) {
        const url = "/Web/Coach/get_coach";
        return this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              let data = res.data.data;
              this.editInitClass = data.coach_type.split(",")
              this.formItem = {
                ...data,
                ...{
                  position_id: data.position_id.split(","),
                  specialty_id: data.specialty_id.split(","),
                  coach_type: this.editInitClass,
                  aptitude: data.aptitude.replace(new RegExp(/(<br \/>)/g), "\n"),
                  other_intr: unescapeHTML(data.other_intr)
                }
              };
            } else {
              this.$Message.error(res.data.errormsg);
            }
            return res;
          })
          .catch(err => {
            throw new Error(err);
          });
      },
      addCoach() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error("请完成信息填写");
            return false;
          }
          let form = this.formItem;
          Reflect.deleteProperty(form, 'specialty');
          Reflect.deleteProperty(form, 'position');
          let url = "/Web/Coach/add_coach";
          let postData = {
            ...form,
            position_id: form.position_id.join(","),
            specialty_id: form.specialty_id.join(","),
            coach_type: form.coach_type.join(","),
            aptitude: form.aptitude.replace(new RegExp(/(\n)/g), "<br />"),
            other_intr: form.other_intr.replace(new RegExp(/(\n)/g), "<br />")
          };
          if (this.coachId) {
            url = "/Web/Coach/update_coach_info";
            postData.id = this.coachId;
          }
          this.$service
            .post(url, postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back();
              }
              this.$Message.success(res.data.errormsg);
            })
            .catch(err => {
              throw new Error(err);
            });
        });
      },
      getCoachSpecialty() {
        const url = "/Web/Coach/get_coach_spec";
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            this.specialtyList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      addCoachPosition() {
        let url = "/Web/Coach/add_coach_posi";
        let postData = {
          name: this.positionName
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.getCoachPosition();
            this.addingPosition = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getAllPlanList() {
        let type = '';
        if (this.formItem.coach_type.includes('私教教练') && this.formItem.coach_type.includes('游泳教练')) {
          type = 0;
        } else if (this.formItem.coach_type.includes('私教教练')) {
          type = 1;
        } else if (this.formItem.coach_type.includes('游泳教练')) {
          type = 2;
        }
        this.$service.get("/Web/PtChargePlan/get_pt_charge_plan_all_list", { params: { type } }).then(res => {
          if (res.data.errorcode === 0) {
            this.planList = res.data.data.list
          }
        })
      }
    }
  };
</script>
