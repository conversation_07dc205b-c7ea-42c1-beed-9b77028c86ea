<template>
    <div class="box">
        <div class="header">
            <DatePicker v-model="statDate" @on-change="handleSearchStat" type="date" placeholder="选择时间" style="width: 200px"></DatePicker>
        </div>
        <div class="buddy">
            <div class="percent-circle-box">
                <div class="percent-circle">
                    <div class="title">教练出勤:</div>
                    <div class="circle">
                        <i-circle :size="214"
                            :trail-width="4"
                            :stroke-width="5"
                            :percent="statCoach.total.circleProgress"
                            stroke-linecap="square"
                            stroke-color="#52a4ea">
                                <div class="label">签到人数/总人数</div>
                                <div class="value">{{statCoach.total.attendanceCount}} / {{statCoach.total.totalCount}}</div>
                                <Button type="text" @click="handleDetail('coach')">出勤明细></Button>
                        </i-circle>
                    </div>
                    <div class="percent-box">
                        <div class="percent-item" v-for="(item, index) in statCoach.detail" :key="index">
                            <div class="label">{{item.groupName}}:</div>
                            <div class="percent-value">
                                <Progress class="percent" stroke-color="#52a4ea" :stroke-width="16" :percent="item.progress" hide-info />
                                <div class="value">{{item.attendanceCount}}/{{item.totalCount}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="percent-circle" style="margin-left:35px;">
                    <div class="title">会籍出勤:</div>
                    <div class="circle">
                        <i-circle :size="214"
                            :trail-width="4"
                            :stroke-width="5"
                            :percent="statMembership.total.circleProgress"
                            stroke-linecap="square"
                            stroke-color="#52a4ea">
                                <div class="label">签到人数/总人数</div>
                                <div class="value">{{statMembership.total.attendanceCount}} / {{statMembership.total.totalCount}}</div>
                                <Button type="text" @click="handleDetail('membership')">出勤明细></Button>
                        </i-circle>
                    </div>
                    <div class="percent-box">
                        <div class="percent-item" v-for="(item, index) in statMembership.detail" :key="index">
                            <div class="label">{{item.groupName}}:</div>
                            <div class="percent-value">
                                <Progress class="percent" stroke-color="#52a4ea" :stroke-width="16" :percent="item.progress" hide-info />
                                <div class="value">{{item.attendanceCount}}/{{item.totalCount}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Row class="search-box">
                <Col span="24">
                    <Input v-model="postData.search" placeholder="姓名/电话" class="search-item" />
                    <DatePicker v-model="initTime" :options="dateOptions" @on-change="handleDateChange" type="daterange" placement="bottom-end" placeholder="时间" class="search-item"></DatePicker>
                    <Select v-model="postData.user_type" class="search-item" placeholder="职务" clearable>
                        <Option value="2" key="2">教练</Option>
                        <Option value="1" key="1">会籍</Option>
                    </Select>
                    <Select v-model="postData.action" class="search-item" placeholder="动作" clearable>
                        <Option value="签到" key="1">签到</Option>
                        <Option value="进场" key="2">进场</Option>
                        <Option value="出场" key="3">出场</Option>
                        <Option value="租柜" key="4">租柜</Option>
                        <Option value="退柜" key="5">退柜</Option>
                        <!-- <Option value="上课" key="6">上课</Option>
                        <Option value="下课" key="7">下课</Option> -->
                        <Option value="门禁" key="8">门禁</Option>
                        <Option value="水控" key="9">水控</Option>
                        <Option value="私教" key="10">私教</Option>
                    </Select>
                    <Button type="success" class="search-btn" @click="handleSearchList">搜索</Button>
                </Col>
            </Row>
            <Row class="table-wrap">
                <Col span="24">
                    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
                </Col>
            </Row>
            <Row class="table-foot">
                <Col span="2">
                    <Button type="success" style="margin-left:10px;" @click="handleExport">导出Excel</Button>
                </Col>
                <Col span="22" class="table-page">
                    <Page @on-change="handlePage" :total="total" :current="postData.page_no" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
                </Col>
            </Row>
        </div>
        <Modal v-model="detailModal" :title="detailModalTitle[detailModalStatus]">
            <Tabs value="done">
                <TabPane :label="dialogLabelOne" name="done">
                    <Table height="400" ref="table" stripe :columns="colsDetail" :data="list1" disabled-hover></Table>
                </TabPane>
                <TabPane :label="dialogLabelTwo" name="donot">
                    <Table height="400" ref="table" stripe :columns="colsDetail" :data="list2" disabled-hover></Table>
                </TabPane>
            </Tabs>
            <div slot="footer"></div>
        </Modal>
    </div>
</template>

<script>
import { formatDate } from "../../utils/index";

export default {
    data() {
        return {
            columns: [
                {title: '时间', key: 'createTime'},
                {title: '员工姓名', key: 'username'},
                {title: '手机号码', key: 'phone'},
                {title: '职务', key: 'userType'},
                {title: '工作组', key: 'groupName'},
                {title: '动作', key: 'action'},
                {title: '方式', key: 'from_type'},
                {title: '设备', key: 'device_name'}
            ],
            total: 0,
            list: [],
            initTime: [],
            postData: {
                search: '',
                time: [],
                user_type: '',
                action: '',
                page_no: 1,
                page_size: 10
            },
            statDate: '',
            statCoach: {
                total: {},
                detail: []
            },
            statMembership: {
                total: {},
                detail: []
            },
            detailModal: false,
            detailModalTitle: {
                coach: '教练-出勤明细',
                membership: '会籍-出勤明细'
            },
            detailModalStatus: '',
            dialogLabelOne: '',
            dialogLabelTwo: '',
            colsDetail: [
                {title: '员工姓名', key: 'userName'},
                {title: '工作组', key: 'groupName'},
                {title: '时间', key: 'AttendanceTime'}
            ],
            list1: [],
            list2: [],
            dateOptions: {
                disabledDate (date) {
                    return date.getTime() > Date.now();
                }
            }
        }
    },
    methods: {
        getList() {
            this.$service.post('/Web/Staff/getVeinLogList', this.postData).then(res => {
                if (res.data.errorcode == 0) {
                    this.list = res.data.data.list;
                    this.total = parseInt(res.data.data.count);
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        handlePage(val) {
            this.postData.page_no = val;
            this.getList();
        },
        pageSizeChanged(size) {
            this.postData.page_no = 1;
            this.postData.page_size = size;
            this.getList();
        },
        handleExport() {
            let copyPostData = {};
            Object.assign(copyPostData, this.postData);
            copyPostData.page_no = 1;
            copyPostData.page_size = this.total;
            this.$service.post('/Web/Staff/getVeinLogList', copyPostData).then(res => {
                if (res.data.errorcode == 0) {
                    const list = res.data.data.list;
                    if (Array.isArray(list) && list.length > 0) {
                        this.$refs.table.exportCsv({
                            filename: `员工出勤-${this.postData.time[0]}-${this.postData.time[1]}`,
                            columns: this.columns,
                            data: list
                        });
                    } else {
                        this.$Message.error('没有数据!');
                        return false;
                    }
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        getStat() {
            this.$service.post('/Web/Staff/getStaffAttendance', {
                date: this.statDate
            }).then(res => {
                if (res.data.errorcode == 0) {
                    this.statCoach = res.data.data.coach;
                    this.statCoach.total.circleProgress = parseInt(this.statCoach.total.attendanceCount) * 100 / parseInt(this.statCoach.total.totalCount);
                    this.statCoach.detail.forEach(item => {
                      item.progress = parseInt(item.attendanceCount) * 100 / parseInt(item.totalCount);
                    });
                    this.statMembership = res.data.data.marketers;
                    this.statMembership.total.circleProgress = parseInt(this.statMembership.total.attendanceCount) * 100 / parseInt(this.statMembership.total.totalCount);
                    this.statMembership.detail.forEach(item => {
                      item.progress = parseInt(item.attendanceCount) * 100 / parseInt(item.totalCount);
                    });
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        getDetail(user_type) {
            this.$service.post('/Web/Staff/getStaffAttendanceDetail', {
                date: this.statDate,
                user_type
            }).then(res => {
                if (res.data.errorcode == 0) {
                    this.list1 = res.data.data.hasAttendance;
                    this.list2 = res.data.data.notAttendance;
                    this.dialogLabelOne = `已出勤 (${this.list1.length})    `
                    this.dialogLabelTwo = `未出勤 (${this.list2.length})    `
                    this.detailModal = true;
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        handleDetail(status) {
            const user_type = status==='coach'?2:1;
            this.detailModalStatus = status;

            this.getDetail(user_type);
        },
        handleSearchList() {
            this.postData.page_no = 1;
            this.getList();
        },
        handleSearchStat(val) {
            this.statDate = val;
            this.getStat();
        },
        handleDateChange(val) {
            this.postData.time = val;
        }
    },
    mounted() {
        const today = new Date();
        this.statDate = formatDate(today, "yyyy-MM-dd");
        this.postData.time[0] = this.statDate;
        this.postData.time[1] = this.statDate;
        this.initTime[0] = this.statDate;
        this.initTime[1] = this.statDate;
        this.getStat();
        this.getList();
    }
}
</script>

<style lang="less" scoped>
    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .box {
        background-color: white;
        padding: 20px 40px;

        .header {
            display: flex;
            align-items: center;
            height: 60px;
        }

        .buddy {
            .percent-circle-box {
                display: flex;
                flex-direction: row;

                .percent-circle {
                    background-color: #f2f6f9;
                    border-radius: 10px;
                    padding: 30px;
                    width: 548px;

                    .title {
                        font-size: 18px;
                        color: #333333;
                        margin-bottom: 20px;
                    }

                    .circle {
                        .center;
                        width: 100%;

                        .label {
                            font-size: 16px;
                            color: #333333;
                        }

                        .value {
                            font-size: 34px;
                            font-weight: bold;
                            color: #333333;
                            margin: 15px 0 10px 0;
                        }
                    }

                    .percent-box {
                        margin-top: 50px;

                        .percent-item {
                            width: 100%;

                            .label {
                                font-size: 16px;
                                color: #333333;
                                display: block;
                                width: 160px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .percent-value {
                                display: flex;
                                flex-direction: row;

                                .percent {
                                    max-width: 460px;
                                }

                                .value {
                                    font-size: 14px;
                                    color: #333333;
                                    margin-left: 16px;
                                }
                            }
                        }
                    }
                }
            }

            .search-box {
                height: 80px;
                display: flex;
                flex-direction: row;
                align-items: center;

                .search-item {
                    margin-left: 20px;
                    width: 200px;
                }

                .search-item:first-child {
                    margin: 0;
                }

                .search-btn {
                    float: right;
                    margin-right: 20px;
                }
            }

            .table-foot {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                height: 60px;

                .table-page {
                    display: flex;
                    justify-content: flex-end;
                }
            }
        }
    }
</style>