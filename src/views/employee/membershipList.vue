<style lang="less" scoped>
  .membership-list {
    width: 100%;
    display: flex;
    overflow: hidden;
    .tree {
      width: 240px;
    }
    .table-wrap {
      flex: 1;
    }
    .hide-tree {
      display: none;
    }
    .group-switch {
      min-width: 40px;
      cursor: pointer;
    }

    /deep/.call-wrap {
      position: relative;
      width: 80px;
      height: 50px;
      .ico-phone-notuse, .ico-phone-use {
        display: block;
        margin: 20px auto 0;
      }
      .pos-tag {
        position: absolute;
        font-size: 10px;
        right: 15px;
        top: -8px;
      }
      .green {
        color: green;
      }
      .blue {
        color: #1890ff;
      }
    }
  }
</style>

<template>
  <div>
    <router-view v-if="$route.name !== '工作人员'"></router-view>
    <div class="membership-list" v-else>
      <div class="tree" :class="{'hide-tree': !searchData.showTree}">
        <MembershipNav @groupChange="groupChanged"></MembershipNav>
      </div>
      <div class="table-wrap">
        <header>
          <!-- <div v-show="!searchData.showTree" title="显示分组" @click="searchData.showTree = true" class="group-switch">
            <Icon type="md-list-box" color="#5cb85c" size="30" class="group-switch"></Icon>
          </div>
          <div class="group-switch" title="隐藏分组" v-show="searchData.showTree" @click="searchData.showTree = false">
            <Icon type="md-arrow-round-back" color="#5cb85c" size="30" class="group-switch"></Icon>
          </div> -->
          <Input style="width: 150px" v-model="searchData.search" @on-enter="getList" placeholder="姓名/电话"></Input>
          <Select style="width: 120px" v-if="positionList && positionList.length" v-model="searchData.mak_type_id"
                  clearable placeholder="职务">
            <Option v-for="position in positionList" :key="position.id" :value="position.id">
              {{position.marketers_name}}
            </Option>
          </Select>
          <Select style="width: 120px" v-model="searchData.use_membership_client" placeholder="会籍端" clearable>
            <Option value="1">已开通</Option>
            <Option value="0">未开通</Option>
          </Select>
          <Button type="success" @click="doSearch">搜索</Button>
        </header>
        <main>
          <Table v-if="tableData" :columns="columns" :data="tableData" ref="table" class="avatar-zoom" stripe
                 disabled-hover></Table>
        </main>
        <footer>
          <router-link to="/employee/membership/add">
            <Button type="success" style="margin-right: 30px">添加
            </Button>
          </router-link>
          <Dropdown placement="top" @on-click="otherCase" v-if="notExport">
            <Button>其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="0">导出excel</DropdownItem>
            </DropdownMenu>
          </Dropdown>
          <Pager :total="pageTotal" :postData="searchData" @on-change="pageChange"></Pager>
        </footer>
      </div>
    </div>
    <div v-if="showImgUpload">
      <ImgUploadWithMedia v-model="showImgUpload" @on-success="upImgSuccess" :beforeUpload="handleAvatarUpload"/>
    </div>
    <rfid v-model="showRfid" :rfid-id="RFID" :beforeBind="beforeBind"></rfid>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import MembershipNav from '../../components/employeeNav/membership.vue';
  import Unbind from 'mixins/unbind';
  import Pager from 'components/pager';
  import ImgUploadWithMedia from 'src/components/form/ImgUploadWithMedia.vue';
  import rfid from 'src/views/member/components/AddRfid.vue';

  const MOBILE_AUTH = {
    auth: ['名下数据权限', '组别数据权限', '场馆数据权限'],
    name: ['教练', '组长', '经理'],
  }

  export default {
    name: 'membershipList',
    mixins: [Unbind],
    components: {
      MembershipNav,
      Pager,
      ImgUploadWithMedia,
      rfid
    },
    data() {
      return {
        RFID: '',
        showRfid: false,
        showImgUpload: false,
        showDelModal: false,
        delData: null,
        searchData: {
          showTree: true,
          search: '',
          mak_type_id: '', // 职务
          group_id: '', //分组id
          coach_type: '',
          use_membership_client: '',
          page_no: 1,
          page_size: 10
        },
        showCoachInfo: false,
        coachInfoData: null,
        notExport: true,
        pageTotal: 0,
        exportData: null,
        tableData: null,
        columns: [
          {
            title: '头像',
            key: 'face_avatar',
            className: 'avatar-wrap',
            render: (h, params) => {
              const item = params.row;
              return item.face_avatar ? (
                <img
                  src={item.face_avatar}
                  class="avatar"
                  style={{ width: '30px', height: '30px', cursor: 'pointer' }}
                  onClick={() => {
                    this.unbind({ marketers_id: item.id, bind_type: 1 });
                  }}
                />
              ) : (
                       ''
                     );
            }
          },
          {
            title: '姓名',
            key: 'name',
            render: (h, params) => {
              const cb = ({ id, type }) => {
                if (type === 'rfid') {
                  this.unbind({ marketers_id: id, bind_type: 2 });
                }
              };
              return this.nameRender(params, 1, cb);
            }
          },
          {
            title: '电话',
            key: 'phone'
          },
          {
            title: '组别',
            key: 'group_name'
          },
          {
            title: '职务',
            key: 'mak_type_name'
          },
          {
            title: '名下会员数',
            key: 'member_count',
            render: (h, params) => {
              let { count, id } = params.row.member_count;
              return count > 0 ? (
                <router-link
                  title="查看名下会员"
                  to={{ path: '/member', query: { curMenu: 'search', marketers_id: id } }}>
                  {count}
                </router-link>
              ) : (
                       <span>0</span>
                     );
            }
          },
          {
            title: '会籍端',
            key: 'use_membership_client',
            render: (h, params) => {
              const use = params.row.use_membership_client;
              const auth = MOBILE_AUTH.auth.findIndex(v => v === params.row.data_authority) + 1;
              return (
                <div class="call-wrap">
                  {
                    auth != 1 ?
                      <div class={ `pos-tag${auth == 2 ?' blue' : auth == 3 ? ' green' :''}` }>
                        {  MOBILE_AUTH.name[auth - 1] }
                      </div> : null
                  }
                  <div class={+use === 1 ? 'ico-phone-use' : 'ico-phone-notuse'} title={+use === 1 ? '已开通' : '未开通'} />
                </div>
              );
            }
          },
          {
            title: '权限',
            key: 'data_authority'
          },
          {
            title: '操作',
            key: 'id',
            render: (h, params) => {
              let id = params.row.id;
              const index = this.tableData.findIndex(item => item.id === id);
              const placement = index < this.tableData.length - 1 ? 'bottom' : 'top';

              return (
                <dropdown
                  placement={placement}
                  on-on-click={name => {
                    this.dropdownClick(name, id);
                  }}>
                  <i-button >
                    操作
                    <icon style="margin-left: 4px; margin-right: -4px" type="md-arrow-dropdown"/>
                  </i-button>
                  <dropdown-menu slot="list">
                    <dropdown-item name="edit">编辑</dropdown-item>
                    <dropdown-item name="del">删除</dropdown-item>
                    <dropdown-item name="avatar">传头像</dropdown-item>
                    <dropdown-item name="rfid">绑RFID</dropdown-item>
                  </dropdown-menu>
                </dropdown>
              );
            }
          }
        ]
      };
    },
    created() {
      this.getMembershipPosition();
    },
    computed: {
      ...mapState('employee', {
        positionList: 'membershipPosition'
      })
    },
    methods: {
      ...mapActions('employee', ['getMembershipPosition']),
      // bind_type: 1 人脸 2 rfid
      handleBind({ bind_type, bind_data }) {
        const url = '/Web/Marketers/bind_property';
        return this.$service
          .post(url, { marketers_id: this.bindId, bind_type, bind_data })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      unbind({ bind_type, marketers_id }) {
        const title = ['解绑头像', '解绑RFID'];
        const content = ['点击确定解绑人脸头像', '点击确定解绑RFID'];
        this.$Modal.confirm({
          title: title[bind_type - 1],
          content: content[bind_type - 1],
          onOk: () => {
            const url = '/Web/Marketers/unbind_property';
            this.$service
              .post(url, { bind_type, marketers_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success('解绑成功');
                  this.getList();
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        });
      },
      upImgSuccess({ imgUrl }) {
        this.handleBind({ bind_type: 1, bind_data: imgUrl });
      },
      beforeBind(rfid) {
        return this.handleBind({ bind_type: 2, bind_data: rfid });
      },
      handleAvatarUpload(imgData) {
        let postData = {
          image_data: imgData,
          _type: 'platform'
        };
        return this.$service
          .post('/Admin/Public/upload_image', postData)
          .then(response => {
            if (response.status === 200) {
              resolve(response.data.path);
            } else {
              this.$Message.error(response.data.statusText);
            }
          })
          .catch(() => {
            this.$Message.error('网络错误');
          });
      },
      dropdownClick(name, id) {
        this.bindId = id;
        const event = {
          edit: () => {
            this.$router.push({ path: '/employee/membership/add', query: { id: id } });
          },
          del: () => this.delMembership(id),
          avatar: () => {
            this.showImgUpload = true;
          },
          rfid: () => {
            this.RFID = this.tableData.find(item => item.id === id).rfid;
            this.showRfid = true;
          }
        };
        event[name]();
      },
      pageChange(postData) {
        this.searchData = { ...this.searchData, ...postData };
        this.getList();
      },
      getList() {
        const url = '/Web/Marketers/get_marketers_list';
        this.$service
          .post(url, this.searchData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list.map(item => {
                return {
                  ...item,
                  ...{
                    member_count: {
                      count: item.member_count,
                      id: item.id
                    }
                  }
                };
              });
              this.pageTotal = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      doSearch() {
        this.searchData.page_no = 1;
        this.getList();
      },
      confirmDel(id) {
        let url = '/Web/Marketers/delete_marketers_info';
        this.$service
          .post(url, { ids: [id] })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList();
              this.$Message.success('删除成功');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      delMembership(id) {
        let membership = this.tableData.filter(item => {
          return item.id === id;
        });
        this.$Modal.confirm({
          title: '删除会籍',
          content: `确定要删除会籍 <b style="color: red">${membership[0].name}</b> ?`,
          onOk: () => {
            this.confirmDel(id);
          }
        });
      },
      groupChanged(id) {
        if (id === this.searchData.group_id) return;
        this.searchData.page_no = 1;
        this.searchData.group_id = id;
        this.getList();
      },
      pageSizeChanged(size) {
        this.searchData.page_no = 1;
        this.searchData.page_size = size;
        this.getList();
      },
      getExportList(isExport) {
        const url = '/Web/Marketers/get_marketers_list';
        let postData = {
          ...this.searchData,
          ...{
            page_no: 1,
            page_size: this.pageTotal
          }
        };
        return this.$service
          .post(url, postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              let resData = res.data.data;
              this.exportData = resData.list.map(item => {
                return {
                  ...item,
                  ...{
                    mak_type_name: item.mak_type_name.split(',').join('、'),
                    use_membership_client: item.use_membership_client == 1 ? '已开通' : '未开通'
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
              return false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async otherCase(val) {
        if (val == '0') {
          await this.getExportList(true);
          if (!this.exportData) return false;

          this.notExport = false;
          this.$refs.table.exportCsv({
            filename: '会籍列表',
            columns: this.columns.filter((col, index) => index < 7 && index !== 5),
            data: this.exportData
          });
          setTimeout(() => {
            this.notExport = true;
          }, 100);
        }
      }
    }
  };
</script>
