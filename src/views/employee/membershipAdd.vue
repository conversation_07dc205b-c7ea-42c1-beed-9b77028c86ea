<style lang="less">
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .position-edit {
    display: none;
    padding-left: 15px;
  }

  .membership-add {
    * {
      font-size: 14px;
    }
    .ivu-form .ivu-form-item-label {
      font-size: 14px;
      padding-right: 20px;
    }
    .input {
      width: 300px;
    }
    .checkbox {
      width: 40%;
      &:hover {
        .position-edit {
          display: inline-block;
        }
      }
    }
    .form {
      padding: 20px 300px 0 0;
      .add-position {
        margin-top: 20px;
        display: flex;
      }
      .add-position-input {
        display: flex;
        align-items: center;
        margin-left: 30px;
      }
      .buttons {
        padding: 20px 20px 50px;
        max-width: 100%;
        display: flex;
        button {
          min-width: 80px;
        }
      }
    }
  }
</style>

<template>
  <div class="container membership-add">
    <header>
      <h3>添加</h3>
    </header>
    <Form label-position="right"
          ref="form"
          :model="formItem"
          :rules="formRules"
          class="form"
          :label-width="140">
      <FormItem label="姓名"
                prop="name">
        <Input v-model="formItem.name"
               autofocus
               class="input"></Input>
      </FormItem>
      <FormItem label="性别">
        <RadioGroup v-model="formItem.sex">
          <Radio label="1">男</Radio>
          <Radio label="2">女</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="手机号码"
                prop="phone">
        <Input v-model="formItem.phone"
               class="input"></Input>
      </FormItem>
      <FormItem label="是否会籍人员">
        <RadioGroup v-model="formItem.is_membership">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="所在组别"
                prop="marketers_group_id">
        <Select v-model="formItem.marketers_group_id"
                filterable
                class="input">
          <Option v-for="group in membershipGroup"
                  :key="group.group_id"
                  :value="group.group_id">{{group.name}}</Option>
        </Select>
      </FormItem>
      <FormItem label="职务"
                prop="mak_type_id">
        <CheckboxGroup v-model="formItem.mak_type_id">
          <Checkbox v-for="position in membershipPosition"
                    class="checkbox"
                    :key="position.mrk_type_id"
                    :label="position.mrk_type_id">{{position.marketers_name}}
            <div class="position-edit">
              <FaIcon name="edit"
                      size="18"
                      color="#5fb75d"
                      title="编辑"
                      @click.native.prevent="clickEditPosition(position)"></FaIcon>
              <FaIcon name="trash-o"
                      size="18"
                      color="#ccc"
                      title="删除"
                      @click.native.prevent="deletePosition(position.mrk_type_id)"></FaIcon>
            </div>
          </Checkbox>
        </CheckboxGroup>
        <div class="add-position">
          <Button type="success"
                  @click="clickAddPosition">添加职务</Button>
          <div class="add-position-input"
               v-if="addingPosition">
            <Input v-model="positionName"
                   ref="position"
                   @on-enter="positionOnEnter"
                   style="width: 180px"
                   placeholder="例如: 会籍经理"></Input>
            <Icon v-if="isEditPosition"
                  type="ios-checkmark-circle-outline"
                  @click.native="doEditPosition"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="确认"></Icon>
            <Icon v-else
                  type="ios-add-circle-outline"
                  @click.native="addPosition"
                  size="24"
                  color="#5cb85c"
                  style="margin: 0 10px; cursor: pointer"
                  title="添加"></Icon>
            <Icon type="ios-close-circle-outline"
                  @click.native="addingPosition = isEditPosition = false"
                  size="24"
                  color="#d9544f"
                  style="cursor: pointer"
                  title="取消"></Icon>
          </div>
        </div>
      </FormItem>
      <FormItem label="是否开通会籍端">
        <RadioGroup v-model="formItem.use_membership_client">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="数据权限">
        <Select v-model="formItem.data_authority"
                class="input">
          <Option value="1">名下数据权限</Option>
          <Option value="2">组别数据权限</Option>
          <Option value="3">场馆数据权限</Option>
        </Select>
      </FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="success"
                  @click="addMembership">保存</Button>
          <Button
                  @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  export default {
    name: 'membershipAdd',
    data() {
      return {
        formItem: {
          name: '',
          sex: '1',
          phone: '',
          is_membership: '1',
          marketers_group_id: '',
          mak_type_id: [],
          use_membership_client: 0,
          data_authority: '1',
          is_change: 0
        },
        formRules: {
          name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号码', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号码错误', trigger: 'change' }
          ],
          marketers_group_id: [{ required: true, message: '请选择所在组别', trigger: 'change' }],
          mak_type_id: [{ required: true, message: '请选择职务' }]
        },
        positionName: '',
        initialName: '',
        isEditPosition: false,
        positionId: '',
        addingPosition: false,
        membershipId: '',
        membershipGroup: [],
        membershipPosition: []
      }
    },
    created() {
      let id = this.$route.query.id
      if (id) {
        this.membershipId = id
        this.getMembershipInfo(id)
      }
      this.getPosition(id)
      this.getGroupList()
    },
    watch: {
      addingPosition(val) {
        if (!val) {
          this.positionName = ''
          this.isEditPosition = false
        }
      }
    },
    methods: {
      getGroupList() {
        const url = '/Web/MarketersGroup/get_marketers_group_list'
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.membershipGroup = res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      positionOnEnter() {
        if (this.isEditPosition) {
          this.doEditPosition()
        } else {
          this.addPosition()
        }
      },
      clickAddPosition() {
        this.addingPosition = true
        this.$nextTick(() => {
          this.$refs.position.$refs.input.focus()
        })
      },
      clickEditPosition({ mrk_type_id, marketers_name }) {
        this.positionName = this.initialName = marketers_name
        this.positionId = mrk_type_id
        this.addingPosition = this.isEditPosition = true
        this.$nextTick(() => {
          this.$refs.position.$refs.input.focus()
        })
      },
      doEditPosition() {
        if (this.positionName === this.initialName) {
          this.addingPosition = false
          return
        }
        const url = '/Web/Marketers/set_mrk_type'
        const postData = {
          mrk_type_id: this.positionId,
          marketers_name: this.positionName
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.addingPosition = false
              this.getPosition()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      deletePosition(mrk_type_id) {
        this.$Modal.confirm({
          title: '删除职务',
          content: '确认删除该职务吗？',
          onOk: () => {
            const url = '/Web/Marketers/set_mrk_type'
            this.$service
              .post(url, { mrk_type_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getPosition()
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              })
              .catch(err => {
                console.error(err)
              })
          }
        })
      },
      getMembershipInfo(id) {
        const url = '/Web/Marketers/get_marketers_info'
        this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              let data = res.data.data
              this.formItem = { ...this.formItem, ...data }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getPosition(id) {
        const url = '/Web/Marketers/get_mrk_type'
        let postData = {
          marketers_id: id
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.membershipPosition = res.data.data
              res.data.data.forEach(item => {
                if (item.is_default === 1) {
                  this.formItem.mak_type_id.push(item.mrk_type_id)
                }
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      addMembership() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写')
            return false
          }
          let form = this.formItem
          let url = '/Web/Marketers/add_marketers_info'
          let postData = {
            ...form,
            ...{
              mak_type_id: form.mak_type_id.join(',')
            }
          }
          if (this.membershipId) {
            url = '/Web/Marketers/update_marketers_info'
            postData.marketers_id = this.membershipId
          }
          this.$service
            .post(url, postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back()
              }
              this.$Message.success(res.data.errormsg)
            })
            .catch(err => {
              console.error(err)
            })
        })
      },
      addPosition() {
        const url = '/Web/Marketers/set_mrk_type'
        let postData = {
          marketers_name: this.positionName
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.getPosition()
            this.addingPosition = false
          } else {
            console.error(res.data.errormsg)
          }
        })
      }
    }
  }
</script>
