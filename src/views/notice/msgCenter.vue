<template>
  <div class="msg-box">
    <div class="msg-left" ref="scrollWrap">
      <div class="msg-tags">
        <span :class="postData.msg_type==index?'cur':''" v-for="(item,index) in typeList" :key="index" @click="handleClickTag(index)">{{item}}</span>
      </div>
       <Scroll :on-reach-bottom="handleReachEdge" :height="scrollHeight" :distance-to-edge="-50">
         <ul class="msg-short" v-if="msgList && msgList.length>0">
            <li :class="item.read_time==0?'point-before':''" v-for="(item, index) in msgList"  @click="showCurMsg(item, index)" :key="item.id">
              <div class="top">
                <span class="rig">{{item.create_time}}</span>
                <h4><img :src="getMsgImg(item.msg_type)" alt="消息类型" /> {{item.title}}</h4>
              </div>
              <p>
                {{item.sub_content}}
              </p>
            </li>
            <li v-show="isEnd" class="nomore">没有更多了</li>
          </ul>
          <div class="nodata" v-else>
            暂无相关消息
          </div>
      </Scroll>

    </div>
    <div class="msg-rig">
      <div class="rig-top">
        <span class="rig-button" @click="readNext(false)">上一条</span>
        <span class="rig-button" @click="readNext(true)">下一条</span>
        <span class="rig-button cur" @click="readMsg('')">全部已读</span>
        <!-- <span class="rig-button">接收设置</span> -->
      </div>
      <div class="rig-con">
         <h3 class="tit" v-if="curMsg"><img :src="getMsgImg(curMsg.msg_type)" alt="" />{{curMsg.title}} <span>{{curMsg.create_time}}</span></h3>
         <div class="main" v-if="curMsg" v-html="curMsg.content"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { EventBus } from 'components/EventBus.js'
import { getBusHost } from 'utils/config';
export default {
  name: 'msgCenter',
  data() {
    return {
      typeList: ['全部', '提醒', '预约', '系统'],
      msgList: [],
      count: '',
      postData: {
        page_no: 1,
        page_size: 10,
        msg_type: 0
      },
      isEnd: false,
      curMsg: '',
      scrollHeight: 500
    }
  },
  computed: {
    ...mapState('websocket', ['socketMsgCount'])
  },
  created() {
    this.getMsgs()
    EventBus.$on('newSocketMsgIn', info => {
      this.newMsgInit(info)
    });
  },
  mounted() {
    this.scrollHeight = this.$refs.scrollWrap.offsetHeight - 87 - 64
  },
  methods: {
    getMsgImg(type) {
      return `/static/img/notice-${type}.png`
    },
    handleClickTag(type) {
      this.postData.msg_type = type
      this.postData.page_no = 1
      this.isEnd = false
      this.getMsgs()
    },
    handleReachEdge() {
      this.postData.page_no += 1
      return this.getMsgs(true)
    },
    showCurMsg(msgInfo,index) {
      msgInfo = {
        ...msgInfo,
        content: msgInfo.content.replaceAll('#/', `${getBusHost()}/v2/`),
        index: +index
      }
      this.curMsg = msgInfo
      this.readMsg(msgInfo)
    },
    readNext(isPrev){
      let cur = this.curMsg;
      if (!cur && this.msgList && this.msgList.length>0) {
        this.setCur(0)
        return;
      }
      if (!isPrev) {
        if (cur.index > 0) {
          this.setCur(cur.index-1)
        }
      } else {
        if (cur.index < this.msgList.length-1) {
          this.setCur(cur.index+1)
        } else if (!this.isEnd) {
          this.postData.page_no += 1
          this.getMsgs(true).then(()=>{
            this.setCur(cur.index+1)
          })
        }
      }
    },
    setCur(index){
      this.curMsg = {
        ...this.msgList[index],
        index: index >= this.msgList.length || index <= 0 ? index : index
      }
      if (this.curMsg.read_time == 0) {
        this.readMsg(this.curMsg)
      }
    },
    readMsg(info) {
      let id = info.id || info
      if (info.read_time == 0 || info === '') {
        return this.$service
          .post('/Web/Msg/read_msg', { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              if (info === '') {
                this.$Message.success('设置成功！')
                this.$store.commit('websocket/SET_SOCKET_MSG_COUNT', 0)
                 this.msgList = this.msgList.map(item => {
                  item.read_time = Date.now()
                  return item
                })
              } else {
                let socketMsgNum = this.socketMsgCount - 1
                this.$store.commit(
                  'websocket/SET_SOCKET_MSG_COUNT',
                  socketMsgNum
                )
                localStorage.setItem('socketMsgCount',  socketMsgNum)
                this.msgList = this.msgList.map(item => {
                  if (item.id === id) {
                    item.read_time = Date.now()
                  }
                  return item
                })
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    },
    newMsgInit(info) {
      return this.$service
        .post('/Web/Msg/msg_list', {
          page_no: 1,
          page_size: info.msg_count,
          msg_type: 0
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            let newMsgs = res.data.data.list
            let curTypeMsgs = newMsgs.filter(item => {
              let findIsIn = this.msgList.find((cur, i) =>cur.id == item.id)
              return !findIsIn && (item.msg_type === this.postData.msg_type || this.postData.msg_type == 0)
            })
            this.msgList = curTypeMsgs.concat(this.msgList)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getMsgs(isScroll) {
      if(this.isEnd && isScroll) {
        return false;
      }
      return this.$service
        .post('/Web/Msg/msg_list', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            if (isScroll) {
              this.msgList = this.msgList.concat(resData.list)
              this.isEnd = resData.list.length < this.postData.page_size;
            } else {
              this.msgList = resData.list
              this.isEnd = false;
            }
            this.count = resData.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>
<style lang="less">
.msg-button {
  border: 1px solid rgb(241, 243, 247);
  background-color: #fff;
  border-radius: 2px;
  color: #979faf;
  text-align: center;
  box-shadow: 0px 3px 5px 0px rgba(53, 73, 93, 0.1);
  height: 34px;
  line-height: 34px;
  overflow: hidden;
}
.msg-box {
  width: 100%;
  height: 100%;
  .msg-left {
    overflow-y: scroll;
    background-color: #fff;
    box-shadow: 0px 3px 20px 0px rgba(44, 57, 69, 0.1);
    position: absolute;
    width: 360px;
    height: 100%;
    padding: 32px 20px;
    font-size: 14px;
    box-sizing: border-box;
    float: left;
  }
}
.msg-tags {
  .msg-button;
  width: 100%;
  margin-bottom: 21px;
  span {
    width: 25%;
    cursor: pointer;
    float: left;
    &.cur,
    &:hover {
      color: #fff;
      background: #52a4ea;
    }
  }
}
.msg-short {
  color: #979faf;
  margin-bottom: 50px;
  .top {
    margin-bottom: 10px;
  }
  h4 {
    color: #303030;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 24px;
      height: 24px;
    }
  }
  .rig {
    float: right;
    font-size: 12px;
  }
  li {
    padding: 15px 18px;
    list-style: none;
    border-bottom: 1px solid #e5e9f4;
    position: relative;
    cursor: pointer;
    &.point-before::before {
      content: ' ';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #e60012;
      position: absolute;
      left: 0;
      top: 50%;
      transform: scaleY(-50%);
    }
  }
  p {
    height: 42px;
    overflow: hidden;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
.msg-rig {
  margin-left: 395px;
  height: 100%;
  .rig-top {
    height: 90px;
    padding-top: 32px;
    margin-bottom: 21px;
    .rig-button {
      .msg-button;
      display: inline-block;
      width: 98px;
      margin-right: 18px;
      cursor: pointer;
      &.cur,
      &:hover {
        color: #fff;
        background: #52a4ea;
      }
    }
  }
  .rig-con {
    background: #fff;
    height: calc(100% - 130px);
    overflow-y: scroll;
    margin-right: 35px;
  }
  .tit {
    color: #303030;
    font-size: 26px;
    width: 100%;
    box-sizing: border-box;
    padding: 40px 28px;
    border-bottom: 1px solid #e5e9f4;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 24px;
      height: 24px;
      margin-right: 18px;
    }
    span {
      margin-left: 20px;
      font-size: 18px;
      color: #979faf;
    }
  }
  .main {
    padding: 28px;
    color: #303030;
    font-size: 16px;
    line-height: 1.5;
    img {
      max-width: 100%;
    }
  }
}
.nodata,.nomore {
  color: #979faf;
  text-align: center;
  line-height: 1.5;
  border-bottom: none !important;
}
</style>
