
<style lang="less" scoped>
.word-length-error {
  .ivu-input {
    border-color: red;
    box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.1);
  }
}
.modalicon-wrap {
  display: flex;
  align-items: center;
}
</style>

<template>
  <div class="table-wrap notice-list">
    <header>
      <Input style="width: 200px"
             placeholder="主题名称"
             @on-enter="getNoticeList"
             v-model="keyword" />
      <DatePicker type="daterange"
                  v-model="dateRange"
                  style="width: 250px"
                  format="yyyy年MM月dd日" />
      <Button type="success"
              @click="getNoticeList">搜索</Button>
    </header>
    <Table :columns="columns"
           :data="tableData"
           disabled-hover />
    <footer>
      <Button type="success"
              @click="showSendNotice = true">发布通知</Button>
      <Page :total="total"
            :current.sync="currentPage"
            show-total
            show-sizer
            placement="top"
            @on-change="getNoticeList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <Modal title="通知详情"
           width="800"
           v-model="showNoticeDetail">
      <Form class="modal-form"
            :label-width="80"
            label-position="left">
        <FormItem label="接收对象">
          <div>
            <Tag v-for="member in detailData.receiver" style="cursor: auto"
                 :key="member.id">{{member}}</Tag>
          </div>
        </FormItem>
        <FormItem label="主题">
          <div type="text"
               style="font-size: 14px; padding-top: 2px">{{detailData.title}}</div>
        </FormItem>
        <div style="border: 1px solid #dddee1; border-radius: 4px; padding: 10px; font-size: 14px; min-height: 200px; word-break: break-all"
             v-html="detailData.content && detailData.content.replace(/\n/g, '<br>').replace('，拒收请回复R', '')"></div>
      </Form>
      <div slot="footer"></div>
    </Modal>
    <Modal title="发布通知"
           width="800"
           :maskClosable="false"
           v-model="showSendNotice">
      <Form class="modal-form"
            :model="sendObj"
            :label-width="80"
            label-position="left">
        <FormItem label="接收对象" v-if="initLabel && userList">
          <Select  v-if="!isUserId"
                  ref="multipleSelect"
                  multiple
                  filterable
                  remote
                  :label="initLabel"
                  placeholder="请选择或搜索会员姓名、电话号码"
                  :loading="searchingUser"
                  :remote-method="getUserList"
                  v-model="sendObj.send_object">
            <Option v-for="user in userList"
                    :key="user.user_id"
                    :value="user.user_id"
                    :label="user.phone ? `${user.username}(${user.phone})` : user.username"></Option>
          </Select>
          <Select ref="singleSelect"
                  v-else
                  filterable
                  clearable
                  :remote="isRemote"
                  :disabled="singleSelectDisabled"
                  :loading="searchingUser"
                  :remote-method="getUserList"
                  v-model="searchUserId">
            <Option v-for="user in userList"
                    :key="user.user_id"
                    :value="user.user_id"
                    :label="user.phone ? `${user.username}(${user.phone})` : user.username"></Option>
          </Select>
        </FormItem>
        <FormItem label="短信条数">
          <div style="font-size: 14px; padding-top: 2px; display: flex">
            <p style="padding-right: 5px">当前剩余
              <span style="color: red">{{adminInfo.sms_number}}</span>
              条
            </p>
            <Button type="text"
                    @click="showBuySms = true">充值短信</Button>
          </div>
        </FormItem>
        <FormItem label="短信签名"
                  prop="sign"
                  :rules="[{required: true, message: '短信签名为必填项', trigger: 'change'}]">
          <div class="modalicon-wrap">
            <Input v-model="sendObj.sign" disabled/>
            <!-- <Input class="w200" v-model="sendObj.sign" disabled/>
            <FaIcon name="edit" style="margin-left: 10px;" size="24" color="#5fb75d" title="编辑" @click.native.prevent="onEditSign"></FaIcon>
              <span>&nbsp;小提示：30天内仅允许编辑一次</span> -->
          </div>
        </FormItem>
        <FormItem label="主题">
          <Input v-model="sendObj.title"
                 :maxlength="20"
                 placeholder="短信主题最多20字" />
        </FormItem>
        <Input type="textarea"
               :class="{'word-length-error': wordLeft < 0}"
               v-model="sendObj.content"
               :autosize="{minRows: 10, maxRows: 15}" />
        <div style="display: flex; justify-content: space-between; padding-top: 3px">
          <p>{{singleWords}}个字算作1条短信，一次发送最多{{singleWords * 4}}个字(4条)</p>
          <p>标题
            <span style="color: red">{{sendObj.title.length}}</span>个字，签名
            <span style="color: red">{{sendObj.sign.length}}</span>个字，内容
            <span style="color: red">{{sendObj.content.length}}</span>个字，还可输入
            <span style="color: red">{{wordLeft}}</span>个字</p>
        </div>
      </Form>
      <div slot="footer"
           class="modal-buttons"
           style="padding-top: 30px;">
        <Button type="success"
                :disabled="wordLeft < 0 || sendObj.sign.length <= 0"
                @click="checkSmsNumber">发送</Button>
        <Button
                @click="showSendNotice = false">取消</Button>
      </div>
    </Modal>
    <Modal title="短信购买"
           width="800"
           class="buy-sms"
           v-model="showBuySms">
      <BuySMS v-if="showBuySms"
              @paySuccess="onPaySuccess"></BuySMS>
      <div slot="footer"></div>
    </Modal>
    <Modal title="签名编辑"
           width="500"
           class="buy-sms"
           v-model="showEditSign">
            <Input v-model="editSign" />
            <div style="padding-top: 10px;">
              上次编辑时间：{{editTime}}
            </div>
      <div slot="footer"
           class="modal-buttons">
        <Button type="success" @click="confirmEditSign">确认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import BuySMS from 'components/onlinePay/buySMS';
import { mapState } from 'vuex';
export default {
  name: 'noticeList',
  components: {
    BuySMS
  },
  data() {
    return {
      editSign: '',
      editTime: '',
      sizer: 10,
      currentPage: 1,
      total: 0,
      selectedMembers: this.$route.params.selectedMembers,
      selectedMembersDes: '',
      singleWords: 56,
      keyword: '',
      isRemote: true,
      isUserId: false,
      singleSelectDisabled: false,
      searchUserId: '',
      searchingUser: false,
      showNoticeDetail: false,
      showSendNotice: false,
      showEditSign: false,
      showBuySms: false,
      dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
      sendMembers: [],
      selectedUser: [],
      initLabel: [],
      userList: [],
      defaultUserList: [],
      sendObj: {
        title: this.$store.state.busName,
        send_object: [],
        content: '',
        sign: '勤鸟运动',
        from: ''
      },
      detailData: {},
      columns: [
        {
          title: '时间',
          key: 'send_time'
        },
        {
          title: '通知对象',
          key: 'receiver',
          ellipsis: true,
          render: (h, params) => {
            return (
              <div style="overflow: hidden; text-overflow: ellipsis" title={params.row.receiver}>
                {params.row.receiver}
              </div>
            );
          }
        },
        {
          title: '内容',
          key: 'content',
          ellipsis: true,
          align: 'center',
          render: (h, params) => {
            return (
              <div style="width: 100%; overflow: hidden; text-overflow: ellipsis">
                <span style="padding-right: 15px; font-weight: bold">{params.row.title}</span>
                <span style="color: #666" title={params.row.content.replace('，拒收请回复R', '')}>
                  {params.row.content.replace('，拒收请回复R', '')}
                </span>
              </div>
            );
          }
        },
        {
          title: '操作账号',
          key: 'sender'
        },
        {
          title: '操作',
          key: 'operation',
          render: (h, params) => {
            return (
              <i-button
                type="text"
                onClick={() => {
                  this.noticeDetail(params.row.msg_id);
                }}>
                详情
              </i-button>
            );
          }
        }
      ],
      tableData: []
    };
  },
  async created() {
    this.sendObj.from = this.$route.params.from;
    this.selectedMembers = this.$route.params.selectedMembers;
    this.$route.params.selectedMembers = '';
    this.getSign();
    this.getNoticeList();
    await this.getDefaultUserGroup();
    if (this.$route.query.userId) {
      this.isRemote = true;
      this.isUserId = true;
      this.showSendNotice = true;
      this.$nextTick(() => {
        this.$refs.singleSelect.setQuery(this.$route.query.userId);
      });
    }
    if (this.selectedMembers) {
      let desObj = this.nameTips(this.selectedMembers);
      this.defaultUserList.push(desObj);
      this.userList = this.defaultUserList;
      this.sendObj.send_object = ['selectedMembers'];
      this.initLabel = [desObj.username];
      if(this.$route.params.from==='invoice') {
        this.sendObj.title = this.$route.params.title
        this.sendObj.content = this.$route.params.content
      }
      this.showSendNotice = true;
    }
  },
  beforeDestroy() {
    this.delSelectedMembersArr(this.defaultUserList);
    this.isUserId = false;
    this.showSendNotice = false;
    this.initLabel = [];
    this.selectedMembers = '';
    this.selectedMembersDes = '';
  },
  computed: {
    ...mapState(['adminInfo']),
    wordLeft() {
      return this.singleWords * 4 - this.sendObj.title.length - this.sendObj.content.length - this.sendObj.sign.length;
    }
  },
  watch: {
    'sendObj.send_object'(val) {
      if (val.length) {
        const id = val[val.length - 1];
        const selectUser = this.userList.find(user => user.user_id === id);
        if (selectUser) {
          this.selectedUser.push(selectUser);
        }
      }
    },
    showSendNotice(val) {
      if (this.$refs.multipleSelect) {
        this.$refs.multipleSelect.lastQuery = '';
        this.$refs.multipleSelect.setQuery('');
        this.$refs.multipleSelect.selectedMultiple = [];
      }
      if (!val) {
        this.sendObj = {
          title: '',
          send_object: [],
          content: '',
          sign: this.sendObj.sign || '勤鸟运动'
        };
        this.userList = this.defaultUserList;
        this.selectedUser = [];
        this.isUserId = false;
      }
    }
  },
  methods: {
    delSelectedMembersArr(arr) {
      arr.forEach((user, index) => {
        if (user.user_id == 'selectedMembers') {
          arr.splice(index, 1);
        }
      });
    },
    nameTips(selectedMembers) {
      let tips = '';
      let sendObj = {};
      let length = selectedMembers.length;
      selectedMembers.forEach((user, index) => {
        sendObj[user.user_id] = user.username;
        if (index == length - 1) {
          tips = tips + user.username;
        } else {
          tips = tips + user.username + '、';
        }
      });
      this.selectedMembersDes = sendObj;
      if (length > 3) {
        tips =
          selectedMembers[0].username +
          '、' +
          selectedMembers[1].username +
          '、' +
          selectedMembers[2].username +
          '...等' +
          length +
          '位用户';
      }
      let desObj = {
        user_id: 'selectedMembers',
        username: tips
      };
      return desObj;
    },
    onPaySuccess() {
      this.$store.dispatch('getAdminInfo').then(() => {
        this.showBuySms = false;
      });
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.sizer = size;
      this.getNoticeList();
    },
    noticeDetail(msg_id) {
      this.showNoticeDetail = true;
      const url = '/MsgCenter/Msg/getMsgInfo';
      this.$service
        .post(url, { msg_id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.detailData = data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getUserList(keyword) {
      if (!keyword || keyword == 0 || keyword === `${this.userList[0].username}(${this.userList[0].phone})`) return;
      const url = '/MsgCenter/Msg/searchMember';
      let postData = { keyword };
      if (this.isUserId && this.$route.query.userId) {
        postData = { user_id: this.$route.query.userId };
      }
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            if (data.length) {
              if (this.isUserId) {
                this.userList = data;
                this.searchUserId = data[0].user_id;
                this.sendObj.send_object = [data[0].user_id];
                this.$nextTick(() => {
                  // this.$refs.singleSelect.lastQuery = `${data[0].username}(${data[0].phone})`;
                  // this.$refs.singleSelect.setQuery(`${data[0].username}(${data[0].phone})`);
                  this.$refs.singleSelect.hideMenu();
                  this.isRemote = false;
                  this.singleSelectDisabled = true;
                  window.location.replace('#/notice/list');
                });
              } else {
                this.userList = [...data,...this.defaultUserList];
              }
            } else {
              if (this.isUserId) {
                this.userList = [];
              } else {
                this.userList = this.defaultUserList;
              }
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    calSendObject() {
      let arr = [];
      let hasSelectedMembers = false;
      this.sendObj.send_object.forEach(id => {
        const user = this.selectedUser.find(user => {
          if (id === 'selectedMembers') {
            hasSelectedMembers = true;
            return false;
          }
          return user.user_id === id;
        });
        user && arr.push(user);
      });
      let obj = {};
      arr.length > 0 &&
        arr.forEach(user => {
          obj[user.user_id] = user.username;
        });
      hasSelectedMembers && Object.assign(obj, this.selectedMembersDes);
      return obj;
    },
    checkSend() {
      if (!this.sendObj.send_object.length) {
        this.$Message.error('请选择接收对象');
        return false;
      }
      if (!this.sendObj.content) {
        this.$Message.error('请输入内容');
        return false;
      }
      return true;
    },
    checkSmsNumber() {
      if (!this.checkSend()) return;
      const url = '/MsgCenter/Msg/getInfoBeforeSend';
      const postData = {
        ...this.sendObj,
        ...{
          send_object: JSON.stringify(this.calSendObject()),
          // 恢复 不是错别字，用来通过短信敏感字检测，回复 是敏感字，但是可以发送出去
          content: `${this.sendObj.content}，拒收请恢复R`
        }
      };
      this.$service
        .post(url, postData)
        .then(res => {
          const data = res.data.data;
          if (res.data.errorcode === 49012) {
            this.messageNumberChecked(data.consume_sms);
          } else if (res.data.errorcode === 49010) {
            this.pleaseRecharge(data.consume_sms - data.surplus_sms);
          } else if (res.data.errorcode === 49022) {
            this.hasAbandonWord(res.data.data);
          } else if (res.data.errorcode === 49025) {
            this.hasAbandonWord(res.data.data.join(', '), true);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    hasAbandonWord(words, isErr) {
      let errTip = isErr ? '以下词为敏感词，可能被运营商拦截导致短信发送失败：' : '以下内容被定义为广告，可能被运营商拦截导致短信发送失败：'
      let con = (
            <div style="display: flex; align-items: center; padding: 20px 0 0">
              <icon type="alert" color="#ff9900" size="36" />
              <p style="padding-left: 20px; font-size: 14px">
               {errTip}<span style="color: red">{words}</span>，请修改
              </p>
            </div>
          )
      if (isErr) {
        this.$Modal.error({
          title: '内容错误',
          render: () => {
            return con
          },
          okText: '返回修改'
        });
      } else {

        this.$Modal.confirm({
          title: '内容错误',
          render: () => {
            return con
          },
          okText: '返回修改',
          cancelText: '仍要发送',
          onCancel: () => {
            this.doSend();
          }
        });
      }

    },
    pleaseRecharge(missNumber) {
      this.$Modal.confirm({
        title: '发送确认',
        render: () => {
          return (
            <div style="display: flex; align-items: center; padding: 20px 0 0">
              <icon type="ios-help-circle" color="#ff9900" size="36" />
              <p style="padding-left: 10px; font-size: 14px">
                短信条数不足，差<span style="color: red">{missNumber}</span>条请充值
              </p>
            </div>
          );
        },
        okText: '充值',
        onOk: () => {
          this.showBuySms = true;
        }
      });
    },
    messageNumberChecked(number) {
      this.$Modal.confirm({
        title: '发送确认',
        render: () => {
          return (
            <div style="display: flex; align-items: center; padding: 20px 0 0">
              <icon type="ios-help-circle" color="#ff9900" size="36" />
              <p style="padding-left: 10px; font-size: 14px">
                本次发送将消耗<span style="color: red">{number}</span>条短信，是否发送？
              </p>
            </div>
          );
        },
        okText: '发送',
        onOk: () => {
          this.doSend();
        }
      });
    },
    doSend() {
      const url = '/Web/SendMessage/sendMsg';
      const postData = {
        ...this.sendObj,
        ...{
          send_object: JSON.stringify(this.calSendObject()),
          content: `${this.sendObj.content}，拒收请回复R`
        }
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$store.dispatch('getAdminInfo');
            this.getNoticeList();
            this.showSendNotice = false;
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    dealDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${month}-${day}`;
    },
    getDefaultUserGroup() {
     return this.$service
        .post('/MsgCenter/Msg/targets')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.defaultUserList = res.data.data;
            this.userList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getSign() {
      this.$service
        .post('/MsgCenter/Msg/getSign')
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            this.showEditSign = false
            this.sendObj.sign = resData.title;
            this.editTime = resData.update_time;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    confirmEditSign() {
      this.$service
        .post('/MsgCenter/Msg/setSign', {
          title: this.editSign
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            this.showEditSign = false
            this.sendObj.sign = resData.title;
            this.editTime = resData.update_time
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    onEditSign() {
      this.showEditSign = true
      this.editSign = this.sendObj.sign
    },
    getNoticeList() {
      const url = '/Web/SendMessage/getMsgList';
      const postData = {
        keyword: this.keyword,
        begin_date: this.dealDate(this.dateRange[0]),
        end_date: this.dealDate(this.dateRange[1]),
        page_size: this.sizer,
        page_no: this.currentPage
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    }
  }
};
</script>
