<template>
  <div class="box">
    <div v-if="isCheckPage">
      <Row>
        <Col :xs="{span: 24}" class="pay-alert">
        <Icon type="ios-bell-outline" color="#fea04c"></Icon>
        <label>为确保收取的定金和您的账户绑定，请验证您的手机！</label>
        </Col>
      </Row>
      <Row>
        <Col :xs="{span: 22, offset:1}" :class="{'pay-item': true, 'hvr-buzz-out': !phoneFlag}">
        <div class="pay-label">
          <img src="../../assets/img/mobile.png" />
          <input v-model="phone" class="pay-input" placeholder="请输入您的电话号码" @change="handleValidatePhone" />
        </div>
        </Col>
      </Row>
      <Row>
        <Col :xs="{span: 22, offset:1}" :class="{'pay-item': true, 'hvr-buzz-out': !codeFlag}">
        <div class="pay-label">
          <img src="../../assets/img/check.png" />
          <input v-model="code" class="pay-input" style="width:8.8rem;" placeholder="请输入验证码" />
        </div>
        <button class="pay-check" @click="handleGetCode" :style="{opacity: codeBtnFlag?'1':'.4'}">获取验证码({{codeTimer}})</button>
        </Col>
      </Row>
      <Row>
        <Col :xs="{span: 20, offset:2}">
        <button class="pay-button" @click="handleCheckCode">完成验证，去付款</button>
        </Col>
      </Row>
    </div>
    <div v-else>
      <Row>
        <Col :xs="{span: 22, offset:1}" class="title">健身房信息</Col>
      </Row>
      <Row>
        <Col :xs="{span: 22, offset:1}" class="gym">
        <div class="gym-name">{{information.bus_name}}</div>
        <div class="gym-info">
          <div class="gym-info-item">
            <img src="../../assets/img/map.png" /> {{information.bus_address}}
          </div>
          <div class="gym-info-item" style="margin-top: .8rem;">
            <img src="../../assets/img/mobile.png" /> {{information.bus_phone}}
          </div>
        </div>
        </Col>
      </Row>
      <Row>
        <Col :xs="{span: 22, offset:1}" class="title">会籍信息</Col>
      </Row>
      <Row>
        <Col :xs="{span: 22, offset:1}" class="membership">
        <div class="name">
          <img src="../../assets/img/membership.png" /> {{information.ms_name}}
        </div>
        <div class="phone">
          {{information.ms_phone}}
        </div>
        </Col>
      </Row>
      <Row>
        <Col :xs="{span: 20, offset:2}">
        <button v-if="!isOver" class="pay-button" @click="handlePayPal">支付定金（¥{{information.amount}}）</button>
        </Col>
      </Row>
    </div>
  </div>
</template>
<script>
  var coverYourEyes = null;

  export default {
    data () {
      return {
        phone: '',
        phoneFlag: true,
        code: '',
        codeFlag: true,
        codeBtnFlag: true,
        codeTimer: 60,
        isCheckPage: true,
        isOver: false,
        information: {
          amount: 0,
          bus_address: "",
          bus_name: "",
          bus_phone: "",
          ms_name: "",
          ms_phone: ""
        },
        payPreInfo: {
          appId: "",
          nonceStr: "",
          package: "",
          paySign: "",
          signType: "MD5",
          timeStamp: ""
        }
      };
    },
    methods: {
      instance (type, msg) {
        switch (type) {
          case 'success':
            const self = this;
            self.$Modal.success({
              title: '支付成功',
              content: `已成功向商家 <b>"${this.information.bus_name}"</b> 支付 <b style='color:orange;'>${this.information.amount}</b> 元人民币。`,
              onOk () {
                self.isOver = true;
              }
            });
            break;
          case 'error':
            this.$Modal.error({
              title: '支付失败',
              content: msg
            });
            break;
        }
      },
      onBridgeReady () {
        const self = this;
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest', {
            "appId": this.payPreInfo.appId,
            "timeStamp": this.payPreInfo.timeStamp,
            "nonceStr": this.payPreInfo.nonceStr,
            "package": this.payPreInfo.package,
            "signType": this.payPreInfo.signType,
            "paySign": this.payPreInfo.paySign
          },
          function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
              self.instance('success');
            } else {
              self.instance('error', res.err_msg);
            }
          }
        );
      },
      handlePayPal () {
        if (typeof WeixinJSBridge == "undefined") {
          if (document.addEventListener) {
            document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(), false);
          } else if (document.attachEvent) {
            document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady());
            document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady());
          }
        } else {
          this.onBridgeReady();
        }
      },
      handleValidatePhone () {
        if (!(/^1[3456789]\d{9}$/.test(this.phone))) {
          this.phoneFlag = false;
        } else {
          this.phoneFlag = true;
        }
      },
      handleGetCode () {
        if (this.phone.length === 0) {
          this.phoneFlag = false;
          this.$Notice.error({
            title: '请输入手机号！'
          });
        } else if (this.phoneFlag) {
          if (!this.codeBtnFlag) {
            return false;
          }
          this.$service.post('/MembershipV2/Marketers/send_user_sms', {
            phone: this.phone
          }).then(res => {
            if (res.status === 200) {
              if (res.data.errorcode == 0) {
                // count timer.
                let self = this;
                self.codeTimer = 60;
                self.codeBtnFlag = false;
                coverYourEyes = setInterval(()=>{
                  if (self.codeTimer > 0) {
                    self.codeTimer--;
                  } else {
                    self.codeTimer = 60;
                    self.codeBtnFlag = true;
                    clearInterval(coverYourEyes);
                  }
                }, 1000);
              } else {
                let msg = res.data.errormsg;
                msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
                this.$Notice.error({
                  title: msg
                });
              }
            } else {
              this.$Notice.error({
                title: '网络不稳定，请摇一摇显示器再重试！',
                desc: `服务器返回代码：${res.status}`
              });
            }
          });
        } else {
          this.phoneFlag = false;
          this.$Notice.error({
            title: '请输入正确的手机号！'
          });
        }
      },
      handleCheckCode () {
        if (this.code.length > 0) {
          this.$service.post('/MembershipV2/Marketers/add_deposit_order', {
            verify_code: this.code,
            phone: this.phone,
            scene_id: this.$route.query.scene_id,
            openid: this.$route.query.openid
          }).then(res => {
            if (res.status === 200) {
              if (res.data.errorcode == 0) {
                this.information = res.data.data.order_info;
                this.payPreInfo = res.data.data.ajax_data;
                this.codeFlag = true;
                this.isCheckPage = false;
              } else {
                this.codeFlag = false;
                this.isCheckPage = true;
                let msg = res.data.errormsg;
                msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
                this.$Notice.error({
                  title: msg
                });
              }
            } else {
              this.$Notice.error({
                title: '网络不稳定，请摇一摇显示器再重试！',
                desc: `服务器返回代码：${res.status}`
              });
            }
          });
        } else {
          this.codeFlag = false;
          this.$Notice.error({
            title: '请输入验证码！'
          });
        }
      }
    },
    mounted () {
      let mm = document.createElement('meta');
      mm.name = "viewport";
      mm.content = "width=device-width, initial-scale=1";
      document.head.appendChild(mm);
    }
  }
</script>

<style lang="less">
  html {
    font-size: 10px;
  }

  .ivu-notice {
    width: 20rem;
  }

  .box {
    background-color: #f5f5f5;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .pay-alert {
      background-color: #fffccc;
      min-width: 3.5rem;
      line-height: 3.5rem;
      text-align: center;
      vertical-align: middle;
      margin-bottom: .2rem;

      label {
        color: #fea04c;
        font-size: 1.2rem;
      }
    }

    .pay-item {
      background-color: #fff;
      height: 5rem;
      border-radius: .4rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: .7rem;
      padding: 0 1.3rem;
      // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .2), 0 1px 5px 0 rgba(0, 0, 0, .12);

      .pay-label {
        display: flex;
        align-items: center;
      }

      img {
        height: 1.5rem;
        width: 1.5rem;
      }

      .pay-input {
        color: #b5b5b5;
        font-size: 1.4rem;
        margin-left: .7rem;
        border: none;
        outline: none;
      }

      .pay-check {
        color: #fff;
        background-color: #abdef9;
        font-size: 1.4rem;
        height: 3.2rem;
        min-width: 9rem;
        border-radius: .4rem;
      }
    }

    .pay-button {
      height: 4rem;
      width: 100%;
      background-color: #74a9ff;
      font-size: 1.7rem;
      color: #fff;
      margin-top: 3.9rem;
      border-radius: .4rem;
    }

    button {
      // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .2), 0 1px 5px 0 rgba(0, 0, 0, .12);
      border: none;
      border-radius: 2px;
      position: relative;
      margin: 0;
      padding: 0 16px;
      display: inline-block;
      text-transform: uppercase;
      letter-spacing: 0;
      overflow: hidden;
      will-change: box-shadow;
      outline: none;
      cursor: pointer;
      text-decoration: none;
    }

    .title {
      font-size: 1.4rem;
      color: #636c7e;
      height: 3.5rem;
      display: flex;
      align-items: center;
    }

    .gym {
      background-color: #fff;
      // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .2), 0 1px 5px 0 rgba(0, 0, 0, .12);
      border-radius: .2rem;

      .gym-name {
        font-size: 1.6rem;
        font-weight: bold;
        color: #202a3c;
        height: 4.8rem;
        display: flex;
        align-items: center;
        margin: 0 1.3rem;
        border-bottom: .05rem solid #e6e9ed;
      }

      .gym-info {
        height: 7.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin: 0 1.3rem;

        .gym-info-item {
          display: flex;
          align-items: center;
          font-size: 1.5rem;
          color: #636c7e; // margin-top: 1rem;
          img {
            height: 1.5rem;
            width: 1.5rem;
            margin-right: .7rem;
          }
        }
      }
    }

    .membership {
      background-color: #fff;
      // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .2), 0 1px 5px 0 rgba(0, 0, 0, .12);
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 1.3rem;
      height: 4.8rem;
      border-radius: .2rem;

      .name {
        font-size: 1.6rem;
        font-weight: bold;
        color: #202a3c;
        display: flex;
        align-items: center;

        img {
          height: 1.5rem;
          width: 1.5rem;
          margin-right: .7rem;
        }
      }

      .phone {
        font-size: 1.6rem;
        color: #636c7e;
      }
    }
  }

  @-webkit-keyframes hvr-buzz-out {
    10% {
      -webkit-transform: translateX(3px) rotate(2deg);
      transform: translateX(3px) rotate(2deg);
    }
    20% {
      -webkit-transform: translateX(-3px) rotate(-2deg);
      transform: translateX(-3px) rotate(-2deg);
    }
    30% {
      -webkit-transform: translateX(3px) rotate(2deg);
      transform: translateX(3px) rotate(2deg);
    }
    40% {
      -webkit-transform: translateX(-3px) rotate(-2deg);
      transform: translateX(-3px) rotate(-2deg);
    }
    50% {
      -webkit-transform: translateX(2px) rotate(1deg);
      transform: translateX(2px) rotate(1deg);
    }
    60% {
      -webkit-transform: translateX(-2px) rotate(-1deg);
      transform: translateX(-2px) rotate(-1deg);
    }
    70% {
      -webkit-transform: translateX(2px) rotate(1deg);
      transform: translateX(2px) rotate(1deg);
    }
    80% {
      -webkit-transform: translateX(-2px) rotate(-1deg);
      transform: translateX(-2px) rotate(-1deg);
    }
    90% {
      -webkit-transform: translateX(1px) rotate(0);
      transform: translateX(1px) rotate(0);
    }
    100% {
      -webkit-transform: translateX(-1px) rotate(0);
      transform: translateX(-1px) rotate(0);
    }
  }

  @keyframes hvr-buzz-out {
    10% {
      -webkit-transform: translateX(3px) rotate(2deg);
      transform: translateX(3px) rotate(2deg);
    }
    20% {
      -webkit-transform: translateX(-3px) rotate(-2deg);
      transform: translateX(-3px) rotate(-2deg);
    }
    30% {
      -webkit-transform: translateX(3px) rotate(2deg);
      transform: translateX(3px) rotate(2deg);
    }
    40% {
      -webkit-transform: translateX(-3px) rotate(-2deg);
      transform: translateX(-3px) rotate(-2deg);
    }
    50% {
      -webkit-transform: translateX(2px) rotate(1deg);
      transform: translateX(2px) rotate(1deg);
    }
    60% {
      -webkit-transform: translateX(-2px) rotate(-1deg);
      transform: translateX(-2px) rotate(-1deg);
    }
    70% {
      -webkit-transform: translateX(2px) rotate(1deg);
      transform: translateX(2px) rotate(1deg);
    }
    80% {
      -webkit-transform: translateX(-2px) rotate(-1deg);
      transform: translateX(-2px) rotate(-1deg);
    }
    90% {
      -webkit-transform: translateX(1px) rotate(0);
      transform: translateX(1px) rotate(0);
    }
    100% {
      -webkit-transform: translateX(-1px) rotate(0);
      transform: translateX(-1px) rotate(0);
    }
  }

  .hvr-buzz-out {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;

    -webkit-animation-name: hvr-buzz-out;
    animation-name: hvr-buzz-out;
    -webkit-animation-duration: 0.75s;
    animation-duration: 0.75s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;

    border: .05rem solid #d50000;
  }
</style>
