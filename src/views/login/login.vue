<template>
<div class="wrap-box">
  <div class="loading-box" v-show="showLoginLoad">
    <div class="ani-loading">
      <img v-if="loadingImg" :src="loadingImg" alt="勤鸟运动" class="logo">
      <img v-else alt="勤鸟运动" src="~assets/img/new-logo.png" class="logo">
      <div class="dots animate">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
    </div>
  </div>
  <div v-show="!showLoginLoad" :class="`wrap ${loginUrlStatus==1?'wrap1':loginUrlStatus==2?'wrap2':''}`">
    <a :href="browserDownloadUrl" download class="suggest-browser">推荐浏览器下载</a>
    <div class="container" v-if="hasErrMsg">
      <img v-if="loadingImg" :src="loadingImg" alt="勤鸟运动" class="logo">
      <img v-else src="~assets/img/login-logo.png" alt="勤鸟运动" class="logo">
      <p class="error-message">{{errorMessage}}
        <span>联系电话: 400-160-7266</span>
      </p>
    </div>
    <div :class="`container ${loginUrlStatus==1?'container1':loginUrlStatus==2?'container2':''}`" v-else>
      <img v-if="loadingImg" :src="loadingImg" alt="勤鸟运动" class="logo">
      <img v-else :src="LOGINLOGOS[loginUrlStatus==0?0:1]" alt="勤鸟运动"  class="logo">
      <form>
        <label class="name">
          <span>账号
            <b>NAME</b>
          </span>
          <input v-focus="usernameErr" @keydown.enter="doLogin" :class="{'invalidate': usernameErr}" :autofocus="focus == 'username'" v-model="username">
          <Tag v-show="usernameErr" color="error">{{errMsg}}</Tag>
        </label>
        <label class="password">
          <span>密码
            <b>PASSWORD</b>
          </span>
          <input v-focus="pwdErr" @keydown.enter="doLogin" :class="{'invalidate': pwdErr}" v-model="password" :autofocus="focus == 'password'" type="password">
          <Tag v-show="pwdErr" color="error">{{errMsg}}</Tag>
        </label>
        <label class="verify-code" v-if="hasVerify">
          <span>请输入验证码</span>
          <label class="verify">
            <input v-focus="verifyErr" @keydown.enter="doLogin" :class="{'invalidate': verifyErr}" v-model="verifyCode">
            <img @click="getVerifyImg" :src="verifyImg" title="换一张">
            <a href="javascript:;" @click="getVerifyImg">换一张</a>
            <Tag v-show="verifyErr" class="verify-err-tag" color="error">{{errMsg}}</Tag>
          </label>
        </label>
      </form>
      <Button @click="doLogin" type="error" size="large" long :disabled="forbidSub" :loading="submitting">
        <span class="login-word">登录</span>
      </Button>
      <div>
        <a :href="shortCut">生成桌面图标</a>
        <i>400-160-7266</i>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import { getBaseUrl, apis } from 'utils/config'
import CryptoJS from 'crypto-js'

export default {
  name: 'login',
  data() {
    return {
      focus: 'username',
      username: '',
      password: '',
      verifyCode: '', // 验证码
      verifyImg: '', // 验证码图片
      errMsg: '', // 错误提示信息
      hasVerify: false, // 显示验证码
      showLoginLoad: 0, // 展示加载动画
      loadingImg: '', // 展示加载动画
      submitting: false, // 正在登录
      usernameErr: false, // 用户名错误
      hasErrMsg: false, // 过期或其他提示
      errorMessage: '',
      pwdErr: false, // 密码错误
      verifyErr: false, // 验证码错误
      timeOut: null, // 登录超时, 网络错误
      loginUrlStatus: 0, // 登陆来源 0 vip 正常页 1 vip1 球场 2 vip2 瑜伽馆
      LOGINLOGOS: [
        require('@/assets/img/login-logo.png'),
        require('@/assets/img/login-logo1.png')
      ],
      shortCut: getBaseUrl() + '/Web/Public/create_shortcut' // 生成桌面图标
    }
  },
  created() {
    const host = window.location.host
    const subDomain = host.split('.')[0]
    if (subDomain == apis[1]) {
      this.loginUrlStatus = 1
    } else if (subDomain == apis[2]) {
      this.loginUrlStatus = 2
    }
    this.showLoginLoad = this.$route.query.loading == 1
    this.loadingImg = this.$route.query.img
    if (window.localStorage.errorCount >= 3) {
      this.hasVerify = true
    }
    if (this.showLoginLoad) {
      setTimeout(() => {
        this.showLoginLoad = false
      }, 3300)
    }
  },
  methods: {
    doLogin() {
      if (this.forbidSub || !this.checkedSub()) {
        return false
      }
      let url = '/Web/Public/check_login'
      let postData = {
        username: this.username,
        password: this.encrypt(this.password),
        vCode: this.verifyCode
      }
      return this.$service
        .post(url, postData, { loading: false })
        .then(res => {
          clearTimeout(this.timeOut)
          if (res.data.errorcode === 0) {
            window.localStorage.errorCount = 0
            sessionStorage.setItem('hasPopupNotice', false)
            if (
              window.location.hostname == 'localhost' ||
              window.location.hostname == 'fe.rocketbird.cn'
            ) {
              if (this.$route.params.from) {
                window.location.hash = this.$route.params.from
              } else {
                this.$router.push('/index')
              }
            } else {
              // 防止跳转到无权限的地址 后端返回地址
              window.location.href = res.data.data.url
            }
          } else {
            this.errMsg = res.data.errormsg
            this.submitting = false
            this.hasErrCode(res.data)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getVerifyImg() {
      let timestamp = Date.parse(new Date())
      this.verifyImg = getBaseUrl() + '/Web/Public/captcha?' + timestamp
    },
    checkedSub() {
      if (!this.username) {
        this.usernameErr = true
        this.pwdErr = this.verifyErr = false
        this.errMsg = '请输入用户名'
        return false
      }
      if (!this.password) {
        this.pwdErr = true
        this.verifyErr = false
        this.errMsg = '请输入密码'
        return false
      }
      if (this.hasVerify && !this.verifyCode) {
        this.verifyErr = true
        this.errMsg = '请输入验证码'
        return false
      }
      this.submitting = true
      this.timeOut = setTimeout(() => {
        this.submitting = false
        this.$Message.error('网络错误')
      }, 10000)
      return true
    },
    hasErrCode(resData) {
      switch (resData.errorcode) {
        case 43004:
          this.verifyErr = this.hasVerify = true
          this.pwdErr = this.usernameErr = false
          break
        case 40027:
          window.location.href = resData.data.url
          break
        case 40028:
          this.usernameErr = true
          this.verifyErr = this.pwdErr = false
          break
        case 40033:
          this.pwdErr = true
          this.verifyErr = false
          if (resData.data.error_number >= 3) {
            window.localStorage.errorCount = resData.data.error_number
            this.hasVerify = true
          }
          break
        case 49007:
          this.hasErrMsg = true
          this.errorMessage = '您好，系统服务已经过期，请联系运营人员进行续费'
          break
        case 40029:
        case 40030:
        case 40031:
        case 40032:
        default:
          this.$Message.error(this.errMsg)
      }
    },
    encrypt(word) {
      const KEY = 'rocketbird@2017!'
      const IV = 'jSsGUiDSEyG33jV6'
      let key = CryptoJS.enc.Utf8.parse(KEY)
      let iv = CryptoJS.enc.Utf8.parse(IV)
      let secret = CryptoJS.enc.Utf8.parse(word)
      let encrypted = CryptoJS.AES.encrypt(secret, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.ZeroPadding
      })
      return encrypted.toString()
    }
  },
  watch: {
    username() {
      this.usernameErr = false
    },
    password() {
      this.pwdErr = false
    },
    verifyCode() {
      this.verifyErr = false
    },
    hasVerify(val) {
      val && this.getVerifyImg()
    },
    usernameErr(val) {
      val && this.getVerifyImg()
    },
    verifyErr(val) {
      val && this.getVerifyImg()
    },
    pwdErr(val) {
      val && this.hasVerify && this.getVerifyImg()
    }
  },
  computed: {
    forbidSub() {
      return (
        this.usernameErr || this.pwdErr || this.verifyErr || this.submitting
      )
    },
    browserDownloadUrl() {
      if (navigator.platform === 'MacIntel') {
        return 'https://imagecdn.rocketbird.cn/chrome.dmg'
      }
      return 'https://imagecdn.rocketbird.cn/chrome.exe'
    }
  },
  directives: {
    focus(el, binding) {
      if (binding.value) {
        el.focus()
        el.select()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrap-box {
  width: 100%;
  height: 100%;
}
.wrap {
  overflow: hidden;
  color: #1b1b1b;
  width: 100%;
  height: 100%;
  background: url('~assets/img/login-bg.jpg') no-repeat center top / cover;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Microsoft YaHei', sans-serif;
  position: relative;

  .suggest-browser {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 14px;
  }
  .logo {
    width: 105px;
  }
}

.wrap1 {
  background: url('~assets/img/login-bg1.jpg') no-repeat center top / cover;
}

.wrap2 {
  background: url('~assets/img/login-bg2.jpg') no-repeat center top / cover;
}

.container {
  // min-height: 400px;
  box-sizing: content-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 0 50px 5px rgba(0, 0, 0, 0.2);
  width: 418px;
  padding: 30px 75px 80px;
  form {
    padding-top: 20px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  label {
    display: flex;
    flex-direction: column;
    position: relative;
    span {
      font-size: 16px;
      padding: 6px 0 2px;
    }
    b {
      font-size: 12px;
      font-weight: normal;
      font-family: Arial, serif;
    }
  }

  input {
    width: 416px;
    border-radius: 1px;
    height: 45px;
    border: 1px solid #e1e1e1;
    padding: 0 10px;
    font-size: 18px;
  }
  .ivu-tag {
    position: absolute;
    border-radius: 3px;
    right: 10px;
    bottom: 10px;
    cursor: text;
  }
  .invalidate {
    outline: none;
    box-shadow: 0 0 0 1px red;
  }
  .verify-code {
    padding-top: 10px;
    span {
      font-size: 14px;
    }
  }
  .verify {
    flex-direction: row;
    align-items: center;
    input {
      width: 220px;
      height: 40px;
      margin-right: 10px;
    }
    img {
      cursor: pointer;
    }
    a {
      text-decoration: underline;
      padding-left: 20px;
    }
    .verify-err-tag {
      right: 205px;
      bottom: 7px;
    }
  }
  .ivu-btn {
    font-size: 18px;
    margin-top: 25px;
    line-height: 2;
  }
  .login-word {
    letter-spacing: 1em;
  }
  > div {
    width: 100%;
    padding-top: 15px;
    white-space: nowrap;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    a {
      color: #1b1b1b;
    }
    a:hover {
      color: #f63538;
    }
    i {
      font-style: normal;
      float: right;
      font-family: PingFang, sans-serif;
    }
  }
}

.container1 {
  width: 530px;
  background: rgba(255, 255, 255, 0.4);
  border: 0px;
  padding: 60px 100px 70px;
  .ivu-btn-error {
    background: #202833;
    border-color: #202833;
  }

  .name {
    margin-bottom: 50px;
  }
  .password {
    margin-bottom: 20px;
  }
  input:focus {
    outline: none;
    border: none;
    box-shadow: 0 0 0 2px transparent;
    border-bottom: 1px solid #202833;
  }
  input:-internal-autofill-previewed,
  input:-internal-autofill-selected {
    // -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }
  input {
    width: 530px;
    border: none;
    background: transparent;
    border-bottom: 1px solid #202833;
  }

  //   .ivu-input:focus{
  //       outline: none;
  //       border: none;
  //       box-shadow: 0 0 0 2px transparent;
  //   }
}

.container2 {
  margin-left: 40%;
  background: rgba(255, 255, 255, 0.7);
  border: 0px;
  .ivu-btn-error {
    background: #986f5c;
    border-color: #986f5c;
  }
  .name {
    margin-bottom: 50px;
  }
  .password {
    margin-bottom: 20px;
  }
  input:focus {
    outline: none;
    border: none;
    box-shadow: 0 0 0 2px transparent;
    border-bottom: 1px solid #986f5c;
  }
  input:-internal-autofill-previewed,
  input:-internal-autofill-selected {
    // -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }
  input {
    background: transparent;
    border: 0;
    border-bottom: 1px solid #986f5c;
  }
}
.error-message {
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
  font-size: 16px;
  padding: 60px 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  text-align: center;
}

.loading-box {
  width: 100%;
  height: 100%;
  background: -webkit-linear-gradient(
    45deg,
    #6fc7b5 0%,
    #13bdce 20%,
    #0094d9 40%,
    #5a3694 60%,
    #ee4d74 80%,
    #fff 100%
  );
  background: linear-gradient(
    45deg,
    #6fc7b5 0%,
    #13bdce 20%,
    #0094d9 40%,
    #5a3694 60%,
    #ee4d74 80%,
    #fff 100%
  );
  background-size: 600%;
  background-position: 0% 100%;
  -webkit-animation: gradient 7.5s ease-in-out infinite;
  animation: gradient 7.5s ease-in-out infinite;
  position: absolute;
  left: 0;
}

.ani-loading {
  width: 100%;
  height: 100%;
}
.ani-loading * {
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0) rotate(0deg);
  transform: translate3d(-50%, -50%, 0) rotate(0deg);
}
.ani-loading .logo {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
}
.ani-loading .dots.animate .dot {
  -webkit-animation: ani-loading-block 2.5s ease-in-out 1;
  animation: ani-loading-block 2.5s ease-in-out 1;
}
.ani-loading .dots.animate .dot:after {
  -webkit-animation: ani-loading-dot 2.5s ease-in-out 1;
  animation: ani-loading-dot 2.5s ease-in-out 1;
}
.ani-loading .dots .dot {
  width: 300px;
  height: 300px;
}
.ani-loading .dots .dot:after {
  content: '';
  display: inline-block;
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255);
  opacity: 0;
  border-radius: 50%;
  position: absolute;
  -webkit-transform: scale(0.17);
  transform: scale(0.17);
}
.ani-loading .dots .dot:nth-child(1) {
  top: 119px;
  left: -209px;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.ani-loading .dots .dot:nth-child(1):after {
  -webkit-transform-origin: top right;
  transform-origin: top right;
  top: 0;
  right: 0;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.ani-loading .dots .dot:nth-child(2) {
  top: -161px;
  left: -194px;
  -webkit-animation-delay: 0.25s;
  animation-delay: 0.25s;
}
.ani-loading .dots .dot:nth-child(2):after {
  -webkit-transform-origin: bottom right;
  transform-origin: bottom right;
  bottom: 0;
  right: 0;
  -webkit-animation-delay: 0.25s;
  animation-delay: 0.25s;
}
.ani-loading .dots .dot:nth-child(3) {
  top: -161px;
  left: -101px;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.ani-loading .dots .dot:nth-child(3):after {
  -webkit-transform-origin: bottom right;
  transform-origin: bottom right;
  bottom: 0;
  right: 0;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.ani-loading .dots .dot:nth-child(4) {
  top: 116px;
  left: 200px;
  -webkit-animation-delay: 0.75s;
  animation-delay: 0.75s;
}
.ani-loading .dots .dot:nth-child(4):after {
  -webkit-transform-origin: top left;
  transform-origin: top left;
  top: 0;
  left: 0;
  -webkit-animation-delay: 0.75s;
  animation-delay: 0.75s;
}
.ani-loading .dots .dot:nth-child(5) {
  top: -161px;
  left: 214px;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.ani-loading .dots .dot:nth-child(5):after {
  -webkit-transform-origin: bottom left;
  transform-origin: bottom left;
  bottom: 0;
  left: 0;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

@-webkit-keyframes ani-loading-block {
  100% {
    -webkit-transform: translate3d(-50%, -50%, 0) rotate(360deg);
    transform: translate3d(-50%, -50%, 0) rotate(360deg);
  }
}

@keyframes ani-loading-block {
  100% {
    -webkit-transform: translate3d(-50%, -50%, 0) rotate(360deg);
    transform: translate3d(-50%, -50%, 0) rotate(360deg);
  }
}
@-webkit-keyframes ani-loading-dot {
  50% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.5;
  }
}
@keyframes ani-loading-dot {
  50% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.5;
  }
}
@-webkit-keyframes gradient {
  50% {
    background-position: 100% 0%;
  }
}
@keyframes gradient {
  50% {
    background-position: 100% 0%;
  }
}
</style>
