<template>
  <div class="table-wrap">
    <div class="top-wrap" v-show="step===1">
      <Button type="primary" @click="addOrder">添加开票业务单</Button>
      <Table ref="table" class="invoice-table" :columns="columns" no-data-text="先添加业务单再填写发票抬头"  :data="postData.orders" stripe />
    </div>
    <div class="bot-wrap customized-tabs" v-show="step===1 && postData.orders && postData.orders.length">
      <Tabs @click="clickTabs" v-model="postData.is_qr">
        <TabPane label="人工开票" name="0">
          <Form ref="personForm"
            :model="postData"
            class="modal-form"
            style="padding: 0 30px"
            :label-width="110">
        <Form-item :label-width="0">
          <RadioGroup v-model="postData.is_enterprise" @on-change="enterpriseChange">
            <Radio :label="1">
                企业单位开票
            </Radio>
            <Radio :label="0">
               个人/非企业单位开票
            </Radio>
          </RadioGroup>
        </Form-item>
        <div class="invoce-formiitem">
          <Form-item label="发票类型" prop="type">
            <Select v-model="postData.type" v-if="invoiceSet.bill_type !== '1' && postData.is_enterprise === 1 && !hasMoneyCardOrder">
              <Option :value="2">普通发票（电子）</Option>
              <Option :value="1">专用发票（纸质）</Option>
            </Select>
            <div v-else>普通发票（电子）</div>
            <div v-if="hasMoneyCardOrder" class="tips">所选业务包含储值不可开专票</div>
          </Form-item>
          <Form-item label="发票抬头" prop="bill_title" :rules="{required: true, message: '请填写'}">
            <TaxInfoSearch v-model="postData.auth_id" :initLabel="postData.bill_title" @on-change="billTitleChange" />
          </Form-item>
          <Form-item label="电话" prop="deliver_type">
            <Input :placeholder="postData.type === 1?'必填':'非必填'" v-model="postData.tel_number"/>
          </Form-item>
          
        </div>
        <div class="invoce-formiitem">
          <Form-item v-if="postData.deliver_type == 1" label="电子邮箱" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,message: '邮箱格式不正确'}]">
            <RadioGroup v-model="postData.deliver_type">
              <Radio :label="1">
                电子邮件
              </Radio>
              <Radio :label="2">
                手机
              </Radio>
            </RadioGroup>
            <Input placeholder="用于接收开出的电子发票" v-model="postData.email"/>
          </Form-item>
          <Form-item v-else label="手机" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^1[********]\d{9}$/,message: '手机号格式不正确'}]">
            <RadioGroup v-model="postData.deliver_type">
              <Radio :label="1">
                电子邮件
              </Radio>
              <Radio :label="2">
                手机
              </Radio>
            </RadioGroup>
            <Input placeholder="用于接收开出的电子发票" v-model="postData.email" />
          </Form-item>
          <Form-item label="开户行与账号" prop="bank_and_account" :rules="{required: postData.type === 1?true:false, message: '请填写'}" class="max-formiitem">
            <Input :placeholder="postData.type === 1?'必填':'非必填'" v-model="postData.bank_and_account"/>
          </Form-item>
        </div>
        <div class="invoce-formiitem">
          <Form-item :label="postData.is_enterprise==0?'身份证':'税号'" prop="tax_identity_number" :rules="{required: postData.is_enterprise==0?false:true, message: '请填写'}">
            <Input :placeholder="postData.is_enterprise === 0?'非必填':'必填'" v-model="postData.tax_identity_number"/>
          </Form-item>
          <Form-item label="地址" prop="address" class="max-formiitem" :rules="{required: postData.type === 1?true:false, message: '请填写'}">
            <Input :placeholder="postData.type === 1?'必填':'非必填'" v-model="postData.address"/>
          </Form-item>
        </div>
        <div class="modal-buttons">
          <Button type="success" @click="nextStep">下一步</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
          </Form>
        </TabPane>
        <TabPane label="二维码开票" name="1">
          <div class="qritem-wrap">
          <Form ref="qrForm"
            :model="qrData"
            class="modal-form"
            style="padding: 0 30px"
            :label-width="80">
          <Form-item :label-width="0">
          <RadioGroup v-model="postData.is_enterprise" @on-change="enterpriseChange">
            <Radio :label="1">
                企业单位开票
            </Radio>
            <Radio :label="0">
               个人/非企业单位开票
            </Radio>
          </RadioGroup>
        </Form-item>
        <div class="invoce-formiitem">
          <Form-item label="发票类型" prop="type">
            <Select v-model="postData.type" v-if="invoiceSet.bill_type !== '1' && postData.is_enterprise === 1 && !hasMoneyCardOrder">
              <Option :value="2">普通发票（电子）</Option>
              <Option :value="1">专用发票（纸质）</Option>
            </Select>
            <div v-else >普通发票（电子）</div>
            <div v-if="hasMoneyCardOrder" class="tips">所选业务包含储值不可开专票</div>
          </Form-item>
           <Form-item label="开票方" prop="id" :rules="{required: true, message: '请填写'}">
              <TaxNumSelect v-model="qrData.id" :type="postData.type" @on-change="qrTaxNumSelect" />
            </Form-item>
          </div>
          <div class="invoce-formiitem">
             <Form-item label="开票人" prop="opener" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^[\u4e00-\u9fa5]+$/, message: '开票人仅可以为中文'}]">
              <Input v-if="invoiceSet.is_edit==='1'" v-model="qrData.opener"/>
              <div v-else>{{qrData.opener}}</div>
            </Form-item>
             <Form-item label="复核人" prop="email">
              <Input v-model="qrData.checker" />
            </Form-item>
            
          </div>
          <div class="invoce-formiitem">
            <Form-item label="收款人" prop="payee">
              <Input placeholder="非必填" v-model="qrData.payee"/>
            </Form-item>
          </div>
          <div class="modal-buttons">
            <Button type="success" @click="getQrcode">生成二维码</Button>
          </div>
          </Form>
          <div class="qritem-rig">
            <faIcon name="qrcode" color="#1B1B1B" size="36"/>
            <div class="qritem-rig-con">
              <div>扫描生成的二维码后客户可自助填写发票抬头</div>
              <div class="red">友情提示：您生成开票二维码后将不可再操作人工开票，将二维码作废后才可继续操作。</div>
            </div>
          </div>
           </div>
        </TabPane>
      </Tabs>

    </div>
    <div class="next-wrap" v-show="step === 2">
      <div class="top-header">
        <div>总金额 <span class="red">{{totalAmount}}元</span></div>
        <div>发票数量 <span class="red">{{postData.bill_detail && postData.bill_detail.length}}张</span></div>
      </div>
      <div class="detail-list">
        <div class="detail-item" v-for="(item, index) in postData.bill_detail">
          <Icon v-if="index>0"
              @click="deleteItem(index)"
              class="item-delete"
              type="ios-close-circle"></Icon>
          <Form 
          :ref="'billForm'+index"
            :model="postData.bill_detail[index]"
            class="modal-form"
            style="padding: 0 30px"
            :label-width="80">
           <Form-item label="发票内容">
              <div class="detail-item-text" v-if="orderDes && orderDes.length<18">{{orderDes}}</div>
              <Tooltip v-else>
                <div class="detail-item-text">{{orderDes}}</div>
                <div slot="content" style="white-space:normal">
                  {{orderDes}}
                </div>
              </Tooltip>
            </Form-item>
           <Form-item label="开票方" prop="id" :rules="{required: true, message: '请填写'}">
              <TaxNumSelect v-model="postData.bill_detail[index].id" :type="postData.type" @on-change="qrTaxNumSelect($event, index)" />
            </Form-item>
           <Form-item label="开票金额" prop="amount" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/,message: '金额必须大于0且最多只能保留两位小数'}]">
              <Input v-if="postData.bill_detail.length>1" v-model="postData.bill_detail[index].amount" @on-change="handleAmountChange(index, $event)"/>
              <div class="detail-item-text" v-else>{{postData.bill_detail[index].amount}}元</div>
            </Form-item>
             <Form-item label="开票人" prop="opener" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^[\u4e00-\u9fa5]+$/, message: '开票员姓名仅可以为中文'}]">
              <Input v-if="invoiceSet.is_edit==='1'" v-model="postData.bill_detail[index].opener"/>
              <div class="detail-item-text" v-else>{{postData.bill_detail[index].opener}}</div>
            </Form-item>
             <Form-item label="复核人" prop="email">
              <Input v-model="postData.bill_detail[index].checker" />
            </Form-item>
            <Form-item label="收款人" prop="payee">
              <Input placeholder="非必填" v-model="postData.bill_detail[index].payee"/>
            </Form-item>
          </Form>
        </div>
        <div class="detail-add" @click="addItem" v-show="postData.orders && postData.orders.length === 1 && postData.bill_detail && postData.bill_detail.length<=6">
          <Icon type="md-add" color="#959595" size="20"></Icon>
          <div>
            添加发票
          </div>
        </div>
      </div>
      <div class="modal-buttons">
          <Button type="primary" @click="step = 1">上一步</Button>
          <Button @click="$router.back()">取消</Button>
          <Button type="success" @click="submitInvoice">提交</Button>
        </div>
    </div>
    <QrModal v-model="showQrModal" :data="qrInfo" />
    <TipsModal v-model="showTipsModal" />
    <PrintTipsModal v-model="showPrintTipsModal" />
    <InvoiceOrderAdd :ids="selectedSn" v-model="showOrderModal" @on-success="orderAdded"/>
  </div>
</template>

<script>
import Pager from 'components/pager'
import InvoiceOrderAdd from './components/InvoiceOrderAdd'
import TaxNumSelect from './components/TaxNumSelect'
import TaxInfoSearch from './components/TaxInfoSearch'
import QrModal from './components/QrModal'
import TipsModal from './components/TipsModal'
import PrintTipsModal from './components/PrintTipsModal'
import { mapState } from 'vuex'
export default {
  name: 'InvoiceAdd',
  components: {
    Pager,
    PrintTipsModal,
    TipsModal,
    QrModal,
    TaxNumSelect,
    TaxInfoSearch,
    InvoiceOrderAdd
  },
  computed: {
    ...mapState(['adminInfo'])
  },
  data() {
    return {
      showPrintTipsModal: false,
      showTipsModal: false,
      showQrModal: false,
      showOrderModal: false,
      totalAmount: 0,
      orderDes: '',
      step: 1,
      columns: [
        {
          title: '类型',
          key: 'type'
        },
        {
          title: '税务分类编码',
          key: 'code',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-input
                  class="count"
                  value={item.code}
                  on-on-blur={e => {
                    this.postData.orders[item._index].code = e.target.value
                  }}
                  style="width: 80px; height: 32px; margin: 0 5px"
                />
              </div>
            )
          }
        },
        {
          title: '业务单号',
          key: 'flow_sn'
        },
        {
          title: '税率',
          key: 'rate',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-input
                  class="count"
                  value={item.rate}
                  on-on-blur={e => {
                    this.postData.orders[item._index].rate = e.target.value
                  }}
                  style="width: 42px; height: 32px; margin: 0 5px"
                />%
              </div>
            )
          }
        },
        {
          title: '时间',
          key: 'deal_time'
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '事项',
          key: 'operate_type'
        },
        {
          title: '描述',
          key: 'description'
        },
        {
          title: '应收金额',
          key: 'amount'
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-button
                  onClick={() => this.deleteOrder(item._index)}
                  type="text"
                  class="button-text-red"
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      listCount: 0,
      qrInfo: null,
      hasMoneyCardOrder: false,
      invoiceSet: {
        bill_type: '2',
        is_edit: '0'
      },
      selectedSn: [],
      billFlow: '',
      qrData: {
        id: '',
        enterprise_name: '',
        amount: '',
        opener: '',
        checker: '',
        payee: '',
        address: '',
        tel_number: '',
        tax_identity_number: '',
        bank_and_account: ''
      },
      curTime: '',
      postData: {
        is_qr: '0',
        type: 2,
        deliver_type: 1,
        is_enterprise: 1,
        bill_title: '',
        auth_id: '',
        tax_identity_number: '',
        email: '',
        tel_number: '',
        address: '',
        bank_and_account: '',
        orders: [],
        bill_detail: [
          {
            id: '',
            enterprise_name: '',
            amount: 0,
            opener: '',
            checker: '',
            payee: '',
            address: '',
            tel_number: '',
            tax_identity_number: '',
            bank_and_account: ''
          }
        ]
      }
    }
  },
  created() {
    this.getInvoiceSet()
  },
  watch: {
    adminInfo: {
      handler(val) {
        const realName = this.adminInfo && this.adminInfo.account_realname
        this.postData.bill_detail[0].opener = realName
        this.qrData.opener = realName
      },
      immediate: true
    },
    'postData.orders'(val) {
      this.totalAmount = 0
      this.orderDes = ''
      this.hasMoneyCardOrder = false
      this.selectedSn = [] 
      if (Array.isArray(val) && val.length) {
        val.forEach(item => {
          this.totalAmount = (
            Number(this.totalAmount) + Number(item.amount)
          ).toFixed(2)
          this.selectedSn.push(item.flow_sn)
          this.orderDes = this.orderDes + `${item.type}*${item.operate_type} `
          if(item.type === '储值') {
            this.hasMoneyCardOrder = true
          }
        })
        this.qrData.amount = this.postData.bill_detail[0].amount = this.totalAmount
        if (val.length > 1 && this.postData.bill_detail.length > 1) {
          this.postData.bill_detail = [this.postData.bill_detail[0]]
        }
      }
    },
    'postData.bill_detail'(val) {
      if(val && val.length === 1) {
        this.postData.bill_detail[0].amount = this.totalAmount
      }
    }
  },
  methods: {
    handleAmountChange(index, ev) {
      let amount = ev.target ? ev.target.value : ev;
      if (Number.isNaN(+amount)) {
        this.$Message.error('请输入数字');
      }
      if (this.postData.bill_detail.length === 2) {
        this.postData.bill_detail[index==1?0:1].amount = Math.max(this.totalAmount - amount, 0).toFixed(2);
      }
    },
    getInvoiceSet() {
      return this.$service.post('/Web/invoice/getSetting').then(res => {
        if (res.data.errorcode == 0) {
          this.invoiceSet = res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getStatus() {
      this.handleSpinCustom()
      this.curTime = new Date().getTime()
      this.getBillResult()
    },
    getBillResult() {
      if (new Date().getTime() - this.curTime > 3000) {
        this.showTipsModal = true
        return false
      }
      this.$service
        .post('/Web/invoice/getBillResult', {
          bil_flow: this.billFlow
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            if (resData.status === 1) {
              this.showPrintTipsModal = true
            } else {
              setTimeout(() => {
                this.getBillResult()
              }, 1000)
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSpinCustom() {
      this.$Spin.show({
        render: h => {
          return (
            <div class="invoice-loading-div">
              <icon type="ios-loading" class="demo-spin-icon-load" />
              <div>
                <p>系统自动开票中，</p>
                <p>开具成功后自动开启打印窗口。</p>
              </div>
            </div>
          )
        }
      })
      setTimeout(() => {
        this.$Spin.hide()
      }, 3000)
    },
    addItem() {
      const formRef = 'billForm' + (this.postData.bill_detail.length - 1)
      this.$refs[formRef][0].validate(valid => {
        if (!valid) {
          this.$Message.error('请完成信息填写')
          return false
        }
        this.postData.bill_detail.push({
          id: '',
          enterprise_name: '',
          amount: '',
          opener: this.adminInfo.account_realname,
          checker: '',
          payee: '',
          address: '',
          tel_number: '',
          tax_identity_number: '',
          bank_and_account: ''
        })
      })
    },
    billTitleChange(info) {
      if (!info) return
      this.postData.bill_title = info.bill_title
      this.postData.tax_identity_number = info.tax_identity_number
      this.postData.email = info.email
      this.postData.deliver_type = +info.deliver_type || 1
      this.postData.tel_number = info.tel_number
      this.postData.bank_and_account = info.bank_and_account
      this.postData.address = info.address
    },
    qrTaxNumSelect(info, index) {
      let toData =
        index !== undefined ? this.postData.bill_detail[index] : this.qrData
      toData.enterprise_name = info.enterprise_name
      toData.id = info.id
      toData.checker = info.checker
      toData.address = info.address
      toData.tel_number = info.tel_number
      toData.tax_identity_number = info.tax_identity_number
      toData.bank_and_account = info.bank_and_account
    },
    getQrcode() {
      if (this.validOrderData()) {
        this.$refs.qrForm.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写')
            return false
          }
          this.$service
            .post('/Web/invoice/openBill', {
              ...this.postData,
              bill_detail: [this.qrData]
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.qrInfo = res.data.data
                this.showQrModal = true
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        })
      }
    },
    nextStep() {
      if (this.validOrderData()) {
        this.$refs.personForm.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写')
            return false
          }
          this.step = 2
        })
      }
    },
    validOrderData() {
      const orders = this.postData.orders
      let result = true
      if (!orders || orders.length === 0) {
        this.$Message.error('请先添加开票业务单')
        result = false
      } else {
        for (const item of orders) {
          if (!item.code || item.rate === '') {
            result = false
            this.$Message.error('请先到税务编码页设置')
            break
          }
          if (!/^[+-]*(\d)*(\.\d{0,2})*$/.test(item.rate)) {
            result = false
            this.$Message.error('税率必须为数字且只能保留两位小数')
            break
          }
        }
      }
      return result
    },
    async submitInvoice() {
      const billDetail = this.postData.bill_detail
      let result = true
      let totalAmount = 0
      for (let [index, item] of billDetail.entries()) {
        const formRef = 'billForm' + index
        totalAmount = (Number(totalAmount) + Number(item.amount)).toFixed(2)
        await this.$refs[formRef][0].validate(valid => {
          result = valid
        })
        if (!result) {
          break
        }
      }
      if (result && totalAmount !== this.totalAmount) {
        result = false
        this.$Message.error('发票金额之和与总金额不等，请检查后重新填写。')
      }
      if (result) {
        this.$service.post('/Web/invoice/openBill', this.postData).then(res => {
          if (res.data.errorcode === 0) {
            this.billFlow = res.data.data.bill_flow
            this.getStatus()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      }
    },
    enterpriseChange(val) {
      if (val === 0) {
        this.postData.type = 2
      }
    },
    orderAdded(list) {
      this.postData.orders = this.postData.orders.concat(list)
    },
    deleteOrder(index) {
      this.postData.orders.splice(index, 1)
    },
    deleteItem(index) {
      this.postData.bill_detail.splice(index, 1)
    },
    addOrder() {
      this.showOrderModal = true
    },
    clickTabs(index) {
      const active = document.querySelector('.ivu-tabs-ink-bar')
      const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`
      active.setAttribute('class', className)
    },
    getList() {
      this.$service
        .post('/Web/invoice/invoiceInfoList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
<style lang="less">
.text-pre {
  font-size: 14px;
  margin-right: 5px;
}
.invoice-loading-div {
  width: 380px;
  height: 110px;
  display: flex;
  background: #fff;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #666;
  font-weight: bold;
  text-align: left;
  .demo-spin-icon-load {
    color: #2d8cf0;
    font-size: 30px;
    animation: ani-spin 1s linear infinite;
    margin-right: 15px;
  }
}
.ivu-spin-fullscreen .ivu-spin-fix {
  background: rgba(0, 0, 0, 0.3) !important;
}
</style>
<style lang="less" scoped>
.top-wrap {
  padding: 35px;
  border-bottom: 1px solid #f1f3f7;
}
.bot-wrap {
  padding: 35px;
}
.invoice-table {
  margin-top: 38px;
}
.customized-tabs /deep/ .ivu-tabs-bar {
  width: 300px;
}
.customized-tabs /deep/ .ivu-tabs-tabpane {
  border: 1px solid #f1f3f7;
  padding-top: 10px;
}
.invoce-formiitem {
  display: flex;
  align-items: flex-start;
  justify-content: left;
  /deep/ .ivu-form-item {
    width: 30%;
    margin-right: 3%;
  }
  .max-formiitem {
    width: 63%;
  }
}
.next-wrap {
  padding: 35px;
  .top-header {
    width: 460px;
    height: 63px;
    padding: 15px 40px;
    box-sizing: border-box;
    background: #f1f3f7;
    display: flex;
    font-size: 16px;
    color: #434343;
    line-height: 33px;
    div {
      flex: 1;
    }
  }
  .red {
    color: #e60012;
  }
}
.detail-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
.detail-item {
  position: relative;
  width: 460px;
  border: 1px solid #ececec;
  padding: 20px;
  margin: 30px 40px 0 0;
  box-sizing: border-box;
}
.detail-item-text {
  font-size: 14px;
  font-weight: bold;
  color: #434343;
  width: 280px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.detail-add {
  width: 166px;
  height: 150px;
  padding: 50px;
  margin: 30px 40px 0 0;
  box-sizing: border-box;
  border: 1px solid #ececec;
  text-align: center;
  cursor: pointer;
}
.qritem-wrap {
  position: relative;
}
.qritem-rig {
  background: #f1f3f7;
  position: absolute;
  left: 66%;
  width: 33%;
  max-width: 622px;
  padding: 0 32px;
  box-sizing: border-box;
  height: 103px;
  top: 52px;
  display: flex;
  align-items: center;
  font-size: 12px;
  .red {
    color: #e60012;
  }
  .qritem-rig-con {
    margin-left: 15px;
  }
}
.tips {
  color: #ed4014;
}
.item-delete {
  position: absolute;
  right: -10px;
  top: -10px;
  font-size: 25px;
  color: #c6c6c6;
  cursor: pointer;
}
</style>
