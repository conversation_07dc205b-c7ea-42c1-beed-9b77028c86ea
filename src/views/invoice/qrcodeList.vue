<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.qr_status" placeholder="开票状态" clearable>
        <Option value="1">已开票</Option>
        <Option value="0">未开票</Option>
        <Option value="2">提交失败</Option>
        <Option value="3">已作废</Option>
      </Select>
      <DatePicker type="daterange" placeholder="时间" v-model="dateRange" @on-change="dateChange"></DatePicker>
      <Input class="w180" v-model="postData.search" @on-enter="doSearch" placeholder="业务单号、发票抬头" />
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" @on-selection-change="handleSelect" :data="tableData" stripe />
    <footer>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <QrModal v-model="showQrModal" :data="curQrInfo" :showBtn="false" />
    <QrInvoiceInfo :data="curQrInvoiceInfo" v-model="showEditModal" @on-success="getList" />
  </div>
</template>

<script>
import Pager from 'components/pager'
import QrInvoiceInfo from './components/QrInvoiceInfo'
import QrModal from './components/QrModal'
import { formatDate } from 'src/utils'
export default {
  name: 'QrcodeList',
  components: {
    Pager,
    QrModal,
    QrInvoiceInfo
  },
  data() {
    return {
      showQrModal: false,
      showEditModal: false,
      curQrInfo: null,
      curQrInvoiceInfo: null,
      selection: [],
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      columns: [
        {
          title: '生成时间',
          key: 'create_time'
        },
        {
          title: '业务单号',
          key: 'flow_sn'
        },
        {
          title: '含税金额',
          key: 'amount_tax'
        },
        {
          title: '发票抬头',
          key: 'bill_title',
          render: (h, params) => {
            return <div>{params.row.bill_title || '-'}</div>
          }
        },
        {
          title: '状态',
          key: 'bank_and_account',
          render: (h, params) => {
            let dom = ''
            if (params.row.qr_status === '2') {
              dom = (
                <tooltip>
                  <badge status="error" text="提交失败" />
                  <icon
                    type="ios-help-circle"
                    style="color: #f4a627;margin-left:3px"
                  />
                  <div slot="content" style="white-space:normal">
                    {params.row.qrcode_msg}
                  </div>
                </tooltip>
              )
            } else {
              dom = <div>{params.row.status_str}</div>
            }
            return dom
          }
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            let dom = ''
            if(item.qr_status === '3') {
              dom = (<div>-</div>)
            } else {
              dom = (
                <div>
                  { item.qr_status === '2' || item.qr_status === '0' ? <i-button
                    style="margin-right: 15px"
                    type="text"
                    onClick={() => { 
                      this.curQrInvoiceInfo = {
                        id: item.id,
                        type: item.type,
                        is_enterprise: item.is_enterprise,
                        auth_id: item.auth_id||'-1',
                        bill_title: item.bill_title,
                        tax_identity_number: item.tax_identity_number,
                        tel_number: item.tel_number,
                        bank_and_account: item.bank_and_account,
                        address: item.address,
                        email: item.deliver_email
                      }
                      this.showEditModal = true
                    }}
                  >
                    开票
                  </i-button> : ''}
                  <i-button
                    style="margin-right: 15px"
                    type="text"
                    onClick={() => {
                      this.curQrInfo = {
                        id: item.id,
                        enterprise_name: item.enterprise_name,
                        type: item.type,
                        url: item.qrcode_imgurl,
                        qr_url: item.qrcode_url,
                        bill_order_no: item.flow_sn,
                        amount: item.amount_tax,
                        create_time: item.qrcode_starttime,
                        expiration_time: item.qrcode_endtime
                      }
                      this.showQrModal = true 
                    }}
                  >
                    二维码
                  </i-button>
                  { item.qr_status === '2' || item.qr_status === '0' ?<i-button
                    onClick={() => this.handleDelete(item.id)}
                    type="text"
                    style="color:#d9544f"
                  >
                    作废
                  </i-button> : ''}
                </div>
              )
            }
            return dom
          }
        }
      ],
      tableData: [],
      listCount: 0,
      postData: {
        begin_date: formatDate(new Date(), 'yyyy-MM-dd'),
        end_date: formatDate(new Date(), 'yyyy-MM-dd'),
        search: '',
        qr_status: '',
        page_no: 1,
        page_size: 10
      }
    }
  },
  created() {},
  methods: {
    dateChange([s, e]) {
      this.postData.begin_date = s
      this.postData.end_date = e
    },
    handleAddInfo() {
      this.showEditModal = true
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(postData) {
      const { page_no, page_size } = postData;
      this.postData = { ...this.postData, page_no, page_size };
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/invoiceQr/qrInvoiceList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleSelect(selection) {
      this.selection = selection.map(item => item.coupon_id).join(',')
    },
    handleDelete(id) {
      this.$Modal.confirm({
        title: '作废',
        content: '确定要作废吗?',
        onOk: () => {
          this.$service
            .post('/Web/invoiceQr/updateQrInvoice', {
              id: id,
              qr_status: 3
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
