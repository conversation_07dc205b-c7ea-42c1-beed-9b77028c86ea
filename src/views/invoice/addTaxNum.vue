<template>
  <div class="container">
    <header>
      <h3>{{id?'编辑':'新增'}}税号</h3>
    </header>
    <Form label-position="right" ref="form" :model="formItem" class="form" :label-width="140">
      <FormItem label="企业名称" prop="enterprise_name" :rules="[{ required: true, message: '请填写企业名称' }]">
        <Input v-model="formItem.enterprise_name" class="input" placeholder="必填" />
      </FormItem>
      <FormItem label="税号" prop="tax_identity_number" :rules="[{ required: true, message: '请填写税号' }]">
        <Input v-model="formItem.tax_identity_number" :disabled="formItem.is_auth==='1'" class="input" placeholder="必填" />
      </FormItem>
      <FormItem label="发票打印密钥" prop="app_secret" :rules="[{ required: true, message: '请填写发票打印密钥' }]">
        <Input v-model="formItem.app_secret" class="input" placeholder="必填" />
      </FormItem>
      <FormItem label="电话" prop="tel_number">
        <Input v-model="formItem.tel_number" class="input" placeholder="非必填" />
      </FormItem>
      <FormItem label="默认复核人" prop="checker">
        <Input v-model="formItem.checker" class="input" placeholder="非必填" />
      </FormItem>
      <FormItem label="地址" prop="address">
        <Input v-model="formItem.address" placeholder="非必填" type="textarea" :autosize="{minRows: 4, maxRows: 8}" />
      </FormItem>
      <FormItem label="开户行与账号" prop="bank_and_account">
        <Input v-model="formItem.bank_and_account" placeholder="非必填" type="textarea" :autosize="{minRows: 4, maxRows: 8}" />
      </FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="success" @click="addTaxNum">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'AddTaxNum',
  components: {},
  data() {
    return {
      id: this.$route.query.id,
      formItem: {
        enterprise_name: '',
        is_auth: '',
        tax_identity_number: '',
        address: '',
        app_secret: '',
        bank_and_account: '',
        checker: '',
        tel_number: ''
      },
      charmSet: '',
      firstChage: true,
      expCardList: [],
      showImgUpload: false
    }
  },
  created() {
    if (this.id) {
      this.getTaxNumInfo()
    }
  },
  computed: {},
  watch: {},
  methods: {
    getTaxNumInfo() {
      this.$service
        .post('/Web/invoice/getInvoiceById', { id: this.id })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.formItem = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    addTaxNum() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.$Message.error('请完成信息填写')
          return false
        }
        let url = '/Web/invoice/addInvoiceTax'
        let postData = this.formItem
        if (this.id) {
          url = '/Web/invoice/editInvoiceTax'
          postData.id = this.id
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back()
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      })
    }
  }
}
</script>
<style lang="less">
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.photo {
  .flex-center;
  border: 1px solid #dcdcdc;
  width: 200px;
  height: 200px;
  box-sizing: border-box;
  padding: 2px;
  margin-bottom: 20px;
  > img {
    width: 100%;
    height: 100%;
  }
}

.container .form {
  padding-bottom: 44px;
}
.goods-card {
  margin-top: 15px;
  font-size: 12px;

  ul {
    li {
      float: left;
      width: 50%;
      color: #999;
    }
  }
  .bot {
    color: red;
  }
  .ivu-card-head {
    padding-top: 8px;
    padding-bottom: 8px;
    p {
      font-size: 12px;
      font-weight: normal;
    }
  }
}
</style>
