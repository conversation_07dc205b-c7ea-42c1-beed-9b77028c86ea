<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" @on-enter="doSearch" placeholder="企业名称、税号、地址电话、开户行账号" />
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Alert class="tax-warn" type="warning" show-icon>温馨提示：请保证授权账号的税号与授权税号一致，否则无法成功开票</Alert>
    <Table ref="table" :columns="columns" :data="tableData" stripe />
    <footer>
      <Button style="margin-right: 15px" type="success" @click="handleAddInfo()">新增税号</Button>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <SelectTaxNumBus :ids="curBusIds" :id="curId" v-model="showSetBusModal" @on-success="getList" />
  </div>
</template>

<script>
import Pager from 'components/pager'
import SelectTaxNumBus from './components/SelectTaxNumBus'

export default {
  name: 'TaxNum',
  components: {
    SelectTaxNumBus,
    Pager
  },
  data() {
    return {
      showSetBusModal: false,
      curBusIds: [],
      curId: '',
      columns: [
        {
          title: '企业名称',
          key: 'enterprise_name'
        },
        {
          title: '税号',
          key: 'tax_identity_number'
        },
        {
          title: '地址、电话',
          key: 'address',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.address}
                {item.tel_number}
              </div>
            )
          }
        },
        {
          title: '开户行及账号',
          key: 'bank_and_account'
        },
        {
          title: '发票打印密钥',
          key: 'app_secret'
        },
        {
          title: '可用场馆',
          key: 'bus_auth',
          width: 200,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '默认复核人',
          key: 'checker'
        },
        {
          title: '禁用/启用',
          key: 'tel_number',
          render: (h, params) => {
            const item = params.row
            return (
              <i-switch
                value={item.status == 1}
                on-on-change={() => {
                  this.handleSwitchChange(
                    { status: item.status, id: item.id },
                    params.row._index
                  )
                }}
              />
            )
          }
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.is_auth!=='1'?<i-button style="margin-right: 15px" type="text" onClick={() => this.getAuth(item.id)}>
                  授权
                </i-button>:''}
                <i-button style="margin-right: 15px" type="text" onClick={()=> {
                  this.curBusIds = item.auth_bus_ids || []
                  this.curId = item.id
                  this.showSetBusModal = true
                }}>
                  可用场馆
                </i-button>
                <i-button
                  style="margin-right: 15px"
                  type="text"
                  onClick={() => this.handleAddInfo(item.id)}
                >
                  编辑
                </i-button>
                <i-button
                  onClick={() => this.handleDelete(item.id)}
                  type="text"
                  style="color:#d9544f"
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      listCount: 0,
      postData: {
        search: '',
        is_use: 0,
        page_no: 1,
        page_size: 10
      }
    }
  },
  created() {},
  methods: {
    getAuth() {
      this.$service
        .post('/Web/invoice/taxAuth')
        .then(res => {
          if (res.data.errorcode === 0) {
            window.open(res.data.data,'_blank')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSwitchChange(infoObj, index) {
      infoObj.status = this.tableData[index].status == 1 ? 0 : 1
      this.tableData[index].status =
        this.tableData[index].status == 1 ? 0 : 1
      this.$service
        .post('/Web/invoice/switchTax', infoObj)
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.tableData[index].status =
              this.tableData[index].status == 1 ? 1 : 0
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(function(error) {
          this.tableData[index].status =
            this.tableData[index].status == 1 ? 1 : 0
          this.$Message.error(error)
        })
    },
    handleAddInfo(id) {
      const query = id ? `?id=${id}` : ''
      this.$router.push({ path: '/invoice/addTaxNum' + query })
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(postData) {
      const { page_no, page_size } = postData;
      this.postData = { ...this.postData, page_no, page_size };
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/invoice/invoiceAuthList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleDelete(id) {
      this.$Modal.confirm({
        title: '删除',
        content: '确定要删除吗?',
        onOk: () => {
          this.$service.post('/Web/invoice/delInvoiceTax', { id }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.tax-warn {
  margin: 15px 30px;
}
</style>
