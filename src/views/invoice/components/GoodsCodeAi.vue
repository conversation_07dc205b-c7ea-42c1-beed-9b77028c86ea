<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="800"
         title="智能编码">
    <div>
      <div class="taxform-header">
        <Input v-model="search" class="w150 mr5" @on-enter="getList" />
        <Button type="success" @click="getList">搜索</Button>
      </div>
      <Table ref="table" :columns="modalColumns" :data="tableData" stripe />
    </div>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'
export default {
  name: 'GoodsCodeAi',
  props: {
    id: {
      type: [String, Number]
    },
    name: {
      type: String
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      modalColumns: [
        {
          title: '分类编码',
          key: 'u_code'
        },
        {
          title: '商品名称',
          key: 'u_name'
        },
        {
          title: '税率',
          key: 'u_rate'
        },
        {
          title: '用户选择比例',
          key: 'u_recommend_score'
        },
        {
          title: '建议',
          key: 'u_recommend_score',
          render: (h, params) => {
            return <div>{params.row.u_flag === 1 ? '推荐' : '-'}</div>
          }
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            return (
              <div>
                <i-button
                  type="text"
                  onClick={() => {
                    this.addGoods(params.row)
                  }}
                >
                  选择
                </i-button>
              </div>
            )
          }
        }
      ],
      search: this.name,
      tableData: []
    }
  },
  computed: {
    showModal: {
      get() {
        if (this.value) {
        }
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$service
        .post('/Web/invoice/aiTaxCode', { name: this.search })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.result
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    addGoods(info) {
      this.$service
        .post('/Web/invoice/taxCodeEdit', {
          id: this.id,
          code: info.u_code,
          rate: info.u_rate
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$emit('on-success')
            this.$Message.success(res.data.errormsg)
            this.showModal = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style scoped>
.modal-page {
  text-align: right;
  margin: 15px 0;
}
.taxform-header {
  display: flex;
  padding-bottom: 30px;
}
</style>
