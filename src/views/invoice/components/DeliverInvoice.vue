<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="交付">
    
    <Form ref="invoiceForm"
          :model="invoiceForm"
          class="modal-form"
          :label-width="80">
          <Form-item>
            <RadioGroup v-model="invoiceForm.deliver_type">
              <Radio :label="1">
                电子邮件
              </Radio>
              <Radio :label="2">
                手机
              </Radio>
            </RadioGroup>
          </Form-item>
          <Form-item v-if="invoiceForm.deliver_type == 1" label="电子邮件" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,message: '邮箱格式不正确'}]">
            <Input v-model="invoiceForm.email" />
          </Form-item>
           <Form-item v-else label="手机" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^1[23456789]\d{9}$/,message: '手机号格式不正确'}]">
            <Input v-model="invoiceForm.email" />
          </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="deliverInvoice">交付</Button>
      <Button 
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'DeliverInvoice',
  props: {
    id: {
      type: [String, Number]
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      invoiceForm: {
        deliver_type: 1,
        email: ''
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.$refs.invoiceForm.resetFields()
      }
    }
  },
  created() {},
  methods: {
    deliverInvoice(info) {
      this.$refs.invoiceForm.validate(val => {
        if (!val) {
          return false
        }
        this.$service
          .post('/Web/invoice/deliverInvoice', {
            id: this.id,
            ...this.invoiceForm
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$emit('on-success')
              this.showModal = false
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      })
    }
  }
}
</script>

<style scoped>
</style>
