<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="填写抬头信息">
    <Form ref="invoiceForm"
          :model="invoiceForm"
          class="modal-form"
          :label-width="120">
          <Form-item label="发票类型" prop="type" :rules="{required: true, message: '请选择'}">
            <!-- <Select v-model="invoiceForm.type">
              <Option :value="2">普通发票（电子）</Option>
              <Option :value="1">专用发票（纸质）</Option>
            </Select> -->
            <div>{{invoiceForm.type == 1?'专用发票（纸质）':'普通发票（电子）'}}</div>
          </Form-item>
          <Form-item label="发票抬头" prop="bill_title" :rules="{required: true, message: '请填写'}">
            <TaxInfoSearch v-if="showModal" :initLabel="invoiceForm.bill_title" v-model="invoiceForm.auth_id" @on-change="billTitleChange" />
          </Form-item>
          <Form-item :label="invoiceForm.is_enterprise==0?'身份证':'税号'" prop="tax_identity_number" :rules="[{ required: invoiceForm.is_enterprise==0?false:true, message: '请填写' }]">
            <Input v-model="invoiceForm.tax_identity_number" />
          </Form-item>
          <Form-item label="电话" prop="tel_number" :rules="{required: invoiceForm.type == 1?true:false, message: '请填写'}">
            <Input v-model="invoiceForm.tel_number" />
          </Form-item>
          <Form-item label="开户行及账号" prop="bank_and_account" :rules="{required: invoiceForm.type == 1?true:false, message: '请填写'}">
            <textarea rows="3" maxlength="90" v-model="invoiceForm.bank_and_account"></textarea>
          </Form-item>
          <Form-item label="地址" prop="address" :rules="{required: invoiceForm.type == 1?true:false, message: '请填写'}">
            <textarea rows="3" maxlength="90" v-model="invoiceForm.address"></textarea>
          </Form-item>
          <Form-item>
            <RadioGroup v-model="invoiceForm.deliver_type">
              <Radio :label="1">
                电子邮件
              </Radio>
              <Radio :label="2">
                手机
              </Radio>
            </RadioGroup>
          </Form-item>
          <Form-item v-if="invoiceForm.deliver_type == 1" label="邮箱地址" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,message: '邮箱格式不正确'}]">
            <Input v-model="invoiceForm.email" />
          </Form-item>
           <Form-item v-else label="手机" prop="email" :rules="[{required: true, message: '请填写'},{type: 'string',pattern: /^1[23456789]\d{9}$/,message: '手机号格式不正确'}]">
            <Input v-model="invoiceForm.email" />
          </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success" @click="handleSubmit(true)">提交</Button>
      <Button @click="handleSubmit()">仅保存</Button>
      <Button @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import TaxInfoSearch from './TaxInfoSearch'
export default {
  name: 'QrInvoiceInfo',
  components: {
    TaxInfoSearch
  },
  props: {
    value: {
      type: Boolean
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      invoiceForm: {
        id: '',
        is_qr: 2,
        deliver_type: 1,
        type: 2,
        is_enterprise: 1,
        auth_id: '',
        bill_title: '',
        tax_identity_number: '',
        address: '',
        tel_number: '',
        bank_and_account: '',
        email: ''
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    data: {
      handler(obj) {
        this.invoiceForm = {
          ...this.invoiceForm,
          ...obj
        }
      },
      immediate: true
    },
    showModal(val) {
      if (!val) {
        this.invoiceForm = {
          id: '',
          is_qr: 2,
          type: 2,
          deliver_type: 1,
          auth_id: '',
          bill_title: '',
          tax_identity_number: '',
          address: '',
          tel_number: '',
          bank_and_account: '',
          email: ''
        }
        this.$refs.invoiceForm.resetFields()
      }
    }
  },
  created() {},
  methods: {
    billTitleChange(info) {
      if (!info) return
      this.invoiceForm.bill_title = info.bill_title
      this.invoiceForm.tax_identity_number = info.tax_identity_number
      this.invoiceForm.email = info.email
      this.invoiceForm.tel_number = info.tel_number
      this.invoiceForm.deliver_type = +info.deliver_type || 1
      this.invoiceForm.bank_and_account = info.bank_and_account
      this.invoiceForm.address = info.address
    },
    handleSubmit(isSubmit) {
      this.$refs.invoiceForm.validate(val => {
        if (!val) {
          return false
        }
        const url = isSubmit ? '/Web/invoice/openQrBill' : '/Web/invoiceQr/editInvoiceInfo'
        this.$service
          .post(url, this.invoiceForm)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showModal = false
              if(isSubmit) {
                this.$router.push('/invoice/record')
              } else {
                this.$emit('on-success')
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      })
    }
  }
}
</script>

<style scoped>
</style>
