<template>
<div>
  <Select v-model="curID"
          @on-change="idChanged"
          :placeholder="placeholder"
          clearable
          filterable
          :disabled="this.disabled">
    <slot></slot>
    <Option v-for="item in dataList"
            :key="item.id"
            :label="item.enterprise_name"
            :value="item.id">
    </Option>
  </Select>
  <div v-if="curInfo" class="num-tips">
    <Icon type="ios-information-circle-outline" color="red" size="16"/>
    <span>企业发票余量{{curInfo.remainNum[type].num}}张</span>
  </div>
</div>
  
</template>

<script>
export default {
  name: 'TaxNumSelect',
  data () {
    return {
      curInfo: null,
      dataList: [],
    }
  },
  props: {
    type: {
      type: Number,
      default: 2
    },
    value: {
      type: [String, Number]
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    curID: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
  },
  created () {
    this.initData()
  },
  methods: {
    idChanged (curID) {
      let info = ''
      this.dataList.forEach((item, index) => {
        if (item.id === curID) {
          info = item
        }
      })
      this.curInfo = info
      this.$emit('on-change', info)
    },
    initData () {
      this.dataList = []
      this.$service
        .post('/Web/invoice/invoiceAuthList', { page_no: 1, is_use: 1 })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.dataList = res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
<style lang="less" scoped>
.num-tips {
  color: #E60012;
  font-size: 12px;
  span {
    display: inline-block;
    margin-left: 5px;
    vertical-align: middle;
  }
}
</style>
