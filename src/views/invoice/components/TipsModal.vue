<template>
  <Modal :mask-closable="false"
         v-model="showModal">
    <div class="modal-tips">
      <Icon type="ios-alert-outline" size="30" color="#ffd77a" />
      <div>
        <p>该发票暂未开具完成，</p>
        <p>请稍后前往【开票记录】打印发票</p>
        <p>将在 <span>{{closeTime}}</span> 秒后关闭。</p>
      </div>
    </div>
    <div slot="footer"
         class="modal-buttons">
      <Button @click="showModal=false">开票记录</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TipsModal',
  props: {
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      closeTime: 3,
      timer: ''
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.goRecord()
      } else {
        this.setTime()
      }
    }
  },
  created() {},
  methods: {
    setTime() {
      if(this.closeTime <= 0) {
        this.showModal = false
      }
      this.timer = setTimeout(() => {
        this.closeTime -= 1
        this.setTime()
      }, 1000);
    },
    goRecord(info) {
      this.timer && clearTimeout(this.timer)
      this.$router.push('/invoice/record')
    }
  }
}
</script>

<style lang="less" scoped>
.modal-tips {
  display: flex;
  font-size: 14px;
  margin: 10px 0 0 10px;
  div {
    margin-left: 15px;
  }
  p {
    line-height: 1.75;
    span {
      color: #2d8cf0;
    }
  }
}
</style>
