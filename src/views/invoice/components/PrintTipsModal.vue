<template>
  <Modal :mask-closable="false"
         v-model="showModal">
    <div class="maintips-wrap">
       <div class="print-modal">
     <p class="tips-top">
       请认真核对即将打印的纸质发票的（种类、代码、号码）信息是否与下方信息一致。如一致，请执行打印操作；如不一致，请予以更换。
      </p>
     <div class="center-con" v-if="info">
       <p>发票种类：{{info.type}}</p>
       <p>发票代码：{{info.bill_code}}</p>
       <p>发票号码：{{info.bill_sn}}</p>
      </div>
      <p class="print-des">为确保您正常打印发票，请先下载安装<a href="https://www.nuonuo.com/nuonuo/web/mainbody/special/index.html?guid=3dedd2bf999b4de2a5d50e35ac7a2771" target="_blank">打印助手</a></p>
    </div>
    </div>
  
    <div slot="footer" class="modal-buttons">
      <a :href="`webprint:0,${printCode}`">
        <Button>发票打印</Button>
      </a>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'PrintTipsModal',
  props: {
    info: {
      type: Object
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      printCode: ''
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val) {
      } else {
      }
    },
    info: {
      handler(val) {
        if(val && val.id) {
          this.getPrintCode()
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    getPrintCode(info) {
      this.$service
        .post('/Web/invoice/getPrintData', {
          id: this.info.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.printCode = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.maintips-wrap {
  margin: 10px;
}
.tips-top,.print-des {
  margin-bottom: 10px;
  color: #969696;
  line-height: 20px;
}
.center-con {
  line-height: 24px;
}
</style>
