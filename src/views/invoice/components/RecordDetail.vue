<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="600"
         title="发票详情">
    <div class="item-wrap">
      <div class="item">类型：{{info.is_qrcode === '1' && info.status !=='0' ? '-' : info.type === '1' ? '专票' : '普票'}}</div>
      <div class="item">创建时间：{{info.create_time}}</div>
      <div class="item">业务单号：{{info.flow_sn}}</div>
      <div class="item">发票代码：{{info.bill_code}}</div>
      <div class="item">会员姓名：{{info.usernames}}</div>
      <div class="item">描述：{{info.flow_desc}}</div>
      <div class="item">发票号码：{{info.bill_sn}}</div>
      <div class="item">发票抬头：{{info.bill_title}}</div>
      <div class="item">{{info.is_enterprise==0?'身份证':'税号'}}：{{info.tax_identity_number}}</div>
      <div class="item">含税金额：{{info.amount_tax}}</div>
      <div class="item">{{info.deliver_type==2?'交付手机':'交付邮箱'}}：{{info.deliver_email}}</div>
      <div class="item">地址：{{info.address}}</div>
      <div class="item maxitem">开户行与账号：{{info.bank_and_account}}</div>
    </div>
    <div slot="footer"
         class="modal-buttons">
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'RecordDetail',
  props: {
    info: {
      type: Object
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      invoiceForm: {
        email: ''
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
  },
  created() {},
  methods: {
  }
}
</script>

<style lang="less" scoped>
.item-wrap {
  display: flex;
  flex-wrap: wrap;
  line-height: 2.25;
  font-size: 14px;
  .item {
    width: 50%;
  }
  .maxitem {
    width: 100%;
  }

}
</style>
