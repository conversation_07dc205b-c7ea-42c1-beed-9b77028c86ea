<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="900"
         title="发票预览">
    <div style="height:600px">
      <iframe id="iframe" :src="pdfUrl" style="width: 100%; height: 100%;"></iframe>
    </div>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'
export default {
  name: 'PriewModal',
  props: {
    id: {
      type: [String, Number]
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      pdfUrl: ''
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    id(val) {
      if(val) {
        this.getPdf()
      }
    }
  },
  created() {
  },
  methods: {
    getPdf() {
      this.$service
        .post('/Web/invoice/view', {id: this.id})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.pdfUrl = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style scoped>
.modal-page {
  text-align: right;
  margin: 15px 0;
}
.taxform-header {
  display: flex;
  padding-bottom: 30px;
}
</style>
