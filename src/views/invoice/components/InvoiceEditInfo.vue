<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="基础信息">
    <Form ref="invoiceForm"
          :model="invoiceForm"
          class="modal-form"
          :label-width="120">
          <Form-item label="发票抬头" prop="bill_title" :rules="[{ required: true, message: '请填写发票抬头' }]">
           <Input v-model="invoiceForm.bill_title" />
          </Form-item>
          <Form-item label="税号/身份证" prop="tax_identity_number" :rules="[{ required: true, message: '请填写税号/身份证' }]">
            <Input v-model="invoiceForm.tax_identity_number" />
          </Form-item>
          <Form-item label="电话" prop="tel_number">
            <Input v-model="invoiceForm.tel_number" />
          </Form-item>
          <Form-item label="开户行及账号" prop="bank_and_account">
            <textarea rows="3" maxlength="90" v-model="invoiceForm.bank_and_account"></textarea>
          </Form-item>
          <Form-item label="地址" prop="address">
            <textarea rows="3" maxlength="90" v-model="invoiceForm.address"></textarea>
          </Form-item>
          <Form-item label="邮箱地址" prop="email" :rules="[{type: 'string',pattern: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,message: '邮箱格式不正确'}]">
            <Input v-model="invoiceForm.email" />
          </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success" @click="infoConfirm">确定</Button>
      <Button @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
import { formatDate } from 'utils'
export default {
  name: 'InvoiceEditInfo',
  props: {
    value: {
      type: Boolean
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      invoiceForm: {
        bill_title: '',
        tax_identity_number: '',
        address: '',
        tel_number: '',
        bank_and_account: '',
        email: ''
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.invoiceForm = {
          bill_title: '',
          tax_identity_number: '',
          address: '',
          tel_number: '',
          bank_and_account: '',
          email: ''
        }
        this.$refs.invoiceForm.resetFields()
      } else {
        if (this.data && typeof this.data === 'object') {
          let {id, bill_title, tax_identity_number, address, tel_number, bank_and_account, email} = this.data
          this.invoiceForm = {id, bill_title, tax_identity_number, address, tel_number, bank_and_account, email}
        }
      }
    }
  },
  created() {},
  methods: {
    infoConfirm() {
      this.$refs.invoiceForm.validate(val => {
        if (!val) {
          return false
        }
        const url = this.invoiceForm.id?'/Web/invoice/editInvoiceInfo' : '/Web/invoice/addInvoiceInfo'
        this.$service
          .post(url, this.invoiceForm)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.$emit('on-success')
              this.showModal = false
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      })
    }
  }
}
</script>

<style scoped>
</style>
