<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="800">
    <div>
      <div class="taxform-header">
        <Select class="w120 mr5" v-model="postData.type" @on-change="handleSearch" placeholder="类型" clearable>
          <Option :value="1">服务</Option>
          <Option :value="2">储值</Option>
          <Option :value="3">转卡与补卡</Option>
          <Option :value="4">商品</Option>
        </Select>
        <DatePicker class="w200 mr5" type="daterange" placeholder="时间" v-model="dateRange" @on-change="dateChange"></DatePicker>
        <Input v-model="postData.search" class="w150 mr5" @on-enter="getList" placeholder="业务单号、会员、会员电话" />
        <Button type="success" @click="getList">搜索</Button>
      </div>
      <Table ref="table" @on-selection-change="handleSelect" :columns="modalColumns" :data="tableData" stripe />
      <Page :total="+total"
            class="modal-page"
            :current.sync="postData.page_no"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success"
              @click="handleConfirm">确定</Button>
      <Button 
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'
export default {
  name: 'InvoiceOrderAdd',
  props: {
    value: {
      type: Boolean
    },
    ids: {
      type: Array
    }
  },
  data() {
    return {
      selection: [],
      modalColumns: [
        {
          type: 'selection',
          width: 80
        },
        {
          title: '业务单号',
          key: 'flow_sn'
        },
        {
          title: '时间',
          key: 'deal_time'
        },
        {
          title: '事项',
          key: 'operate_type'
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '应收金额',
          key: 'amount'
        }
      ],
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      tableData: [],
      total: 0,
      postData: {
        begin_date: formatDate(new Date(), 'yyyy-MM-dd'),
        end_date: formatDate(new Date(), 'yyyy-MM-dd'),
        type: 1,
        search: '',
        page_no: 1,
        page_size: 10
      },
      lockerList: []
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            _checked: false
          }
        })
        this.selection = []
      } else {
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            _disabled: this.ids.indexOf(item.flow_sn) !== -1 ? true : false
          }
        })
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleConfirm() {
      if (!this.selection.length) return this.$Message.error('请先勾选业务单')
      this.$emit('on-success', this.selection)
      this.showModal = false
    },
    handleSelect(selection) {
      this.selection = selection
    },
    dateChange([s, e]) {
      this.postData.begin_date = s
      this.postData.end_date = e
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageSizeChanged(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/invoice/getOrderList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.total = data.count
            this.tableData = data.list.map(item => {
              return {
                ...item,
                _disabled: this.ids.indexOf(item.flow_sn) !== -1 ? true : false
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    addGoods(name) {
      this.$service.post('/Web/invoice/addGoodsTax', { name }).then(res => {
        if (res.data.errorcode === 0) {
          this.$emit('on-success')
          this.showModal = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>

<style scoped>
.modal-page {
  text-align: right;
  margin: 15px 0;
}
.taxform-header {
  display: flex;
  padding-bottom: 30px;
}
</style>
