<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="添加商品"
         width="600">
    <div>
      <div class="taxform-header">
        <Input v-model="postData.search" class="w150 mr5" @on-enter="handleSearch" placeholder="商品名称" />
        <Button type="success" @click="handleSearch">搜索</Button>
      </div>
      <Table ref="table" :columns="modalColumns" :data="tableData" stripe />
      <Page :total="+total"
            class="modal-page"
            :current.sync="postData.page_no"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </div>
    <div slot="footer" class="modal-buttons"></div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'
export default {
  name: 'InvoiceGoodsSelect',
  props: {
    userId: {
      type: [String, Number]
    },
    from: {
      type: String
    },
    value: {
      type: Boolean
    },
    data: ''
  },
  data() {
    return {
      modalColumns: [
        {
          title: '商品名称',
          key: 'commodity_name'
        },
        {
          title: '单价',
          key: 'commodity_price'
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-button type="text" onClick={() => {
                  this.addGoods(params.row.commodity_name)
                }}>添加</i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      total: 0,
      postData: {
        search: '',
        page_no: 1,
        page_size: 10
      },
      lockerList: []
    }
  },
  computed: {
    showModal: {
      get() {
        if (this.value) {
        }
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageSizeChanged(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/invoice/get_commodity_list', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.total = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    addGoods(name) {
      this.$service
        .post('/Web/invoice/addGoodsTax', { name })
        .then(res => {
          if (res.data.errorcode === 0) {
             this.$emit('on-success')
            this.showModal = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style scoped>
.modal-page {
  text-align: right;
  margin: 15px 0;
}
.taxform-header {
  display: flex;
  padding-bottom: 30px;
}
</style>
