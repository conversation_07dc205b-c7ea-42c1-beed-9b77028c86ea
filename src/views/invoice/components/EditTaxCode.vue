<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="基础信息">
    <Form ref="invoiceForm"
          :model="invoiceForm"
          class="modal-form"
          :label-width="120">
          <Form-item label="税务分类编码" prop="code" :rules="[{ required: true, message: '请填写税务分类编码' }]">
           <Input v-model="invoiceForm.code" />
          </Form-item>
          <Form-item label="税率" prop="rate" :rules="[{ required: true, message: '请填写税率' }]">
            <Input v-model="invoiceForm.rate" />
          </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="taxCodeEdit">确定</Button>
      <Button 
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'
export default {
  name: 'EditTaxCode',
  props: {
    info: {
      type: Object
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {
      invoiceForm: {
        id: '',
        code: '',
        rate: ''
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    info: {
      handler(obj) {
        this.invoiceForm = {
          ...this.invoiceForm,
          ...obj
        }
      },
      immediate: true
    },
    showModal(val) {
      if (!val) {
        this.$refs.invoiceForm.resetFields()
      }
    }
  },
  created() {},
  methods: {
    taxCodeEdit() {
      this.$refs.invoiceForm.validate(val => {
        if (!val) {
          return false
        }
        this.$service
          .post('/Web/invoice/taxCodeEdit', this.invoiceForm)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$emit('on-success')
              this.$Message.success(res.data.errormsg)
              this.showModal = false
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      })
    }
  }
}
</script>

<style scoped>
</style>
