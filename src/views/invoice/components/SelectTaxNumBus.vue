<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="基础信息">
    <CheckboxGroup v-model="checkedIds" class="modal-checkgroup">
        <Checkbox v-for="item in adminBusList" :label="item.id" :key="item.id">{{item.name}}</Checkbox>
    </CheckboxGroup>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success" @click="infoConfirm">确定</Button>
      <Button @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'SelectTaxNumBus',
  props: {
    value: {
      type: Boolean
    },
    id: {
      type: [String, Number]
    },
    ids: {
      type: Array
    }
  },
  data() {
    return {
      checkedIds: []
    }
  },
  computed: {
    ...mapState(['adminBusList']),
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    ids(val) {
      this.checkedIds = val
    }
  },
  created() {
    !this.adminBusList && this.$store.dispatch('getAdminBusList')
  },
  methods: {
    infoConfirm() {
      this.$service
        .post('/Web/invoice/authBus', {
          bus_ids: this.checkedIds,
          id: this.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$emit('on-success')
            this.showModal = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.modal-checkgroup /deep/ .ivu-checkbox-wrapper{
  width: 30%;
  margin: 0 8px 8px 0;
}
</style>
