<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="二维码">
    <div class="act-wrap">
      <div class="act-item" @click="printContent()">
        <Icon class="mr5" type="ios-print-outline" />
        <span>打印二维码</span>
      </div>
      <div class="act-item" @click="copyUrl()">
        <Icon class="mr5" type="ios-paper-outline" />
        <span>复制链接</span>
      </div>
      <div class="act-item" @click="sendMsg()">
        <Icon class="mr5" type="ios-send-outline" />
        <span>发送到手机</span>
      </div>
    </div>
    <div class="con-wrap" ref="printElement" v-if="data">
      <div class="qr-wrap">
        <div class="tit">
          {{data.enterprise_name}}
        </div>
        <img :src="data.url" />
        <div class="normal-wrap">
          <span class="normal-text">开票金额</span> {{data.amount}}
        </div>
        <div class="normal-wrap">
          <span class="normal-text">业务单号</span> {{data.bill_order_no}}
        </div>
      </div>
      <div class="qr-tips">
        <p>生成于 {{data.create_time}} 请于 {{data.expiration_time}} 前扫码开票，否则二维码将失效</p>
        <p class="mt10">1.使用支付宝或微信扫码</p>
        <p>2.输入开票信息后提交申请</p>
        <p>3.支付宝交付到发票管家，微信交付到卡包</p>
        <p>4.如需纸质发票，可自行打印</p>
      </div>
    </div>
    <div slot="footer"
         class="modal-buttons" v-show="showBtn">
      <Button type="primary" @click="delQr">作废二维码</Button>
      <!-- <Button type="success" @click="showModal = false">二维码管理</Button> -->
    </div>

    <PrintView v-model="printHtml" />
  </Modal>
</template>

<script>
import PrintView from './PrintView'
export default {
  name: 'QrModal',
  props: {
    value: {
      type: Boolean
    },
    showBtn: {
      type: Boolean,
      default: true
    },
    data: {
      type: Object
    }
  },
  components: {
    PrintView
  },
  data() {
    return {
      printHtml: '',
      isDelQr: false
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showModal(val) {
      if (!val && !this.isDelQr) {
        this.goRecord()
      } else if (val) {
        this.isDelQr = false
      }
    }
  },
  created() {},
  methods: {
    sendMsg() {
      this.$service
        .post('/Web/invoice/getSendQrSmsInfo', {
          id: this.data.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            this.$router.push({
              name: '消息推送',
              params: {
                from: 'invoice',
                activeIndex: '2',
                selectedMembers: resData.users,
                title: '手机开票',
                content: resData.content
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    goRecord() {
      this.$router.push('/invoice/record')
    },
    copyUrl() {
      let oInput = document.createElement('input')
      oInput.value = this.data.qr_url
      document.body.appendChild(oInput)
      oInput.select()
      document.execCommand('Copy')
      this.$Message.success('复制成功')
      oInput.remove()
    },
    printContent() {
      this.printLogUpload()
      this.printHtml = this.$refs.printElement.innerHTML
    },
    printLogUpload() {
      this.$service.post('/Web/invoiceQr/printLog', {
        id: this.data.id
      })
    },
    delQr() {
      this.$service
        .post('/Web/invoiceQr/updateQrInvoice', {
          id: this.data.id,
          qrcode_status: 3
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.isDelQr = true
            this.showModal = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.con-wrap,
.act-wrap {
  display: block;
  page-break-before: always;
  width: 58mm;
  margin: 0 auto;
  font-family: simhei,fangsong,simsun,serif;
}
.act-wrap {
  width: 300px;
  display: flex;
  font-size: 14px;
  color: #2b8df2;
  text-align: center;
  margin-bottom: 20px;
  .act-item {
    cursor: pointer;
    flex: 1;
    border-right: 1px solid #979faf;
    &:last-child {
      border-right: 0 none;
    }
  }
}
.qr-wrap {
  display: block;
  width: 100%;
  margin: 0 auto;
  font-size: 14px;
  font-weight: bold;
  color: #1b1b1b;
  background: #f1f3f7;
  text-align: center;
  padding: 30px 0;
  box-sizing: border-box;
  .normal-wrap {
    word-break: break-all;
  }
  .normal-text {
    font-weight: normal;
  }
  img {
    width: 200px;
    margin-top: 15px;
  }
}
.qr-tips {
  width: 200px;
  margin: 20px auto 0;
}
.mt10 {
  margin-top: 10px;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 56mm auto;
    margin: 0;
    padding: 0;
  }
  .qr-tips {
    font-size:4mm !important;
    color: #000 !important;
  }
}
</style>
