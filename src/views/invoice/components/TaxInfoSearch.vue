<template>
  <Select filterable
          :clearable="false"
          remote
          class="taxinfo-select"
          ref="signSelect"
          :remote-method="getSearchList"
          v-model="curId"
          @on-change="itemSelected"
          :loading="searching"
          :placeholder="holder">
    <Option v-for="item in list" :key="item.id" :value="item.id" :label="item.bill_title"></Option>
  </Select>
</template>

<script>
import { debounce } from 'lodash-es'

export default {
  name: 'TaxInfoSearch',
  data() {
    return {
      curInfo: null,
      timer: '',
      curId: '',
      searching: false,
      isFromOut: false,
      list: []
    }
  },
  props: {
    value: {
      type: [String, Number]
    },
    initLabel: {
      type: String
    },
    clearable: {
      type: Boolean
    },
    placeholder: {
      type: String
    }
  },
  watch: {
    value(val, oldVal) {
      if (val != oldVal) {
        this.curId = this.value
      }
    },
    initLabel: {
      handler(val) {
        if (val) {
          this.list = [
            {
              bill_title: val,
              id: this.curId || '-1',
              tax_identity_number: '',
              email: '',
              tel_number: '',
              address: '',
              bank_and_account: ''
            }
          ]
          this.curId = this.list[0].id
          this.$refs.signSelect && this.$refs.signSelect.hideMenu()
        }
      },
      immediate: true
    }
  },
  computed: {
    holder() {
      return this.placeholder || '输入企业名称搜索'
    }
  },
  mounted() {},
  methods: {
    itemSelected(curId) {
      let info = ''
      this.list.forEach((item, index) => {
        if (item.id === curId) {
          info = item
        }
      })
      this.curInfo = info
      this.$emit('input', curId)
      this.$emit('on-change', info)
    },
    getSearchList(search) {
      search = search && search.trim()
      if (search === '') {
        this.list = []
        return
      }
      if (this.curId === search) {
        return
      }
      this.searching = true
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        return this.$service
          .post(
            '/Web/invoice/invoiceInfoList',
            {
              search: search
            },
            { loading: false }
          )
          .then(res => {
            const firstItem = [
              {
                bill_title: search,
                id: this.isFromOut ? this.curId || '-1' : '-1',
                tax_identity_number: '',
                email: '',
                tel_number: '',
                address: '',
                bank_and_account: ''
              }
            ]
            let list = []
            if (res.data.errorcode === 0) {
              this.searching = false
              const data = res.data.data
              if (
                Array.isArray(res.data.data.list) &&
                res.data.data.list.length
              ) {
                list = res.data.data.list
              }
              let hasSearhWords = false
              list.forEach(item => {
                if (item.bill_title === search) {
                  hasSearhWords = true
                }
              })
              this.list = hasSearhWords ? list : firstItem.concat(list)
            } else {
              this.list = firstItem
            }
          })
      }, 100)
    }
  }
}
</script>
