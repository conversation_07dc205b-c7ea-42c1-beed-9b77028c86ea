<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" @on-enter="doSearch" placeholder="搜索名称、税务分类编码" />
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" :data="tableData" stripe />
    <footer>
      <Button style="margin-right: 15px" type="success" @click="handleAddInfo">添加商品</Button>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <div v-if="showGoodsModal">
      <InvoiceGoodsSelect v-model="showGoodsModal" @on-success="getList()"/>
    </div>
    <div v-if="showAiModal">
      <GoodsCodeAi v-model="showAiModal" :id="curAiInfo.id" :name="curAiInfo.name" @on-success="getList()"/>
    </div>
    <div v-if="showEditModal">
      <EditTaxCode v-model="showEditModal" :info="curEditInfo" @on-success="getList()"/>
    </div>
  </div>
</template>

<script>
import Pager from 'components/pager'
import InvoiceGoodsSelect from './components/InvoiceGoodsSelect'
import GoodsCodeAi from './components/GoodsCodeAi'
import EditTaxCode from './components/EditTaxCode'

export default {
  name: 'TaxCode',
  components: {
    Pager,
    GoodsCodeAi,
    EditTaxCode,
    InvoiceGoodsSelect
  },
  data() {
    return {
      showGoodsModal: false,
      showEditModal: false,
      showAiModal: false,
      selection: [],
      curAiInfo: {
        id: '',
        name: ''
      },
      curEditInfo: null,
      columns: [
        {
          title: '类别',
          key: 'type',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.type === '1' ? '服务' : item.type === '2' ? '储值' : item.type === '3' ? '转卡与补卡' : '商品'}
              </div>
            )
          }
        },
        {
          title: '名称',
          key: 'name',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.name || '-'}
              </div>
            )
          }
        },
        {
          title: '描述',
          key: 'description',
          render: (h, params) => {
            return (
              <div>
                {params.row.description || '-'}
              </div>
            )
          }
        },
        {
          title: '税务分类编码',
          key: 'code',
          render: (h, params) => {
            return (
              <div>
                {params.row.code || '-'}
              </div>
            )
          }
        },
        {
          title: '税率',
          key: 'rate',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.rate}%
              </div>
            )
          }
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-button style="margin-right: 15px" type="text" onClick={() => this.editCode(item)}>
                  编辑
                </i-button>
               {item.type === '4'? <i-button style="margin-right: 15px" type="text" onClick={() => {
                  this.curAiInfo = {
                    id: item.id,
                    name: item.name
                  }
                  this.showAiModal=true
                }}>
                  智能编码
                </i-button> : ''}
                {item.type === '4'?<i-button
                  onClick={() => this.clickDeleteBtn(item.id)}
                  type="text"
                  style="color:#d9544f"
                >
                  删除
                </i-button>: ''}
              </div>
            )
          }
        }
      ],
      tableData: [],
      listCount: 0,
      postData: {
        search: '',
        page_no: 1,
        page_size: 10
      }
    }
  },
  created() {},
  methods: {
    handleAddInfo() {
      this.showGoodsModal = true
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(postData) {
      const { page_no, page_size } = postData;
      this.postData = { ...this.postData, page_no, page_size };
      this.getList();
    },
    editCode(info) {
      this.curEditInfo = {
        id: info.id,
        code: info.code,
        rate: info.rate
      }
      this.showEditModal = true
    },
    getList() {
      this.$service
        .post('/Web/invoice/taxCodeList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    clickDeleteBtn(id) {
      this.$Modal.confirm({
        title: '删除',
        content: '确定要删除吗?',
        onOk: () => {
          this.$service
            .post('/Web/invoice/delTaxCode', { id })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              console.error(err)
            })
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
