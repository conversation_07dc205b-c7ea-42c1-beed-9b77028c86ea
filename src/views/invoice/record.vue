<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.type" placeholder="类型" clearable>
        <Option value="2">普通发票（电子）</Option>
        <Option value="1">专用发票（纸质）</Option>
      </Select>
      <Select v-model="postData.status" placeholder="开票状态" clearable>
        <Option value="0">开票完成</Option>
        <Option value="1">开票中</Option>
        <Option value="2">开票失败</Option>
        <Option value="3">重试中</Option>
        <Option value="4">已作废</Option>
        <Option value="5">已提交冲红</Option>
        <Option value="6">已放弃</Option>
      </Select>
      <Select v-model="postData.is_qrcode" placeholder="开票方式" clearable>
        <Option value="1">二维码开票</Option>
        <Option value="0">人工开票</Option>
      </Select>
      <DatePicker type="daterange" placeholder="时间" v-model="dateRange" @on-change="dateChange"></DatePicker>
      <Input class="w180" v-model="postData.search" @on-enter="doSearch" placeholder="搜索业务单号、发票代码发票号 、抬头、邮箱" />
      <Button type="success" @click="doSearch">搜索</Button>
      <Button type="success" @click="gotaxCode">税务编码</Button>
      <Button type="success" @click="gotaxNum">场馆税号</Button>
    </header>
    <Table ref="table" :columns="columns" @on-selection-change="handleSelect" :data="tableData" stripe />
     <div class="box-body-total" v-if="statics">
      <div class="t-label">
        总计
        <Tooltip class="total-wrap">
          <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
          <div slot="content">
            <p>成功开票数量: 包含普票与专票开票完成、已提交冲红、已作废状态的数量</p>
            <p>普通发票数量: 包含普票开票完成、已提交冲红状态的数量</p>
            <p>专用发票数量: 包含专票开票完成、已作废状态的数量</p>
            <p>成功开票金额: 仅包含普票专票开票完成状态且金额为正的数据</p>
            <p>普通发票金额: 仅包含普票开票完成状态且金额为正的数据</p>
            <p>专用发票金额: 仅包含专票开票完成状态且金额为正的数据</p>
          </div>
        </Tooltip>
      </div>
      <div class="t-desc">
        <div>成功开票数量: {{statics.success_num}}张</div>
        <div>普通发票数量: {{statics.general_num}}张</div>
        <div>专用发票数量: {{statics.special_num}}张</div>
        <div>成功开票金额: {{statics.success_amount}}元</div>
        <div>普通发票金额: {{statics.general_amount}}元</div>
        <div>专用发票金额: {{statics.special_amount}}元</div>
      </div>
    </div>
    <footer>
      <Button style="margin-right: 15px" type="success" @click="handleAddInfo">开发票</Button>
      <Button @click="handleExcel">导出Excel</Button>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <PrintTipsModal v-model="showPrintModal" :info="curPrintInfo"/>
    <PriewModal :id="curId" v-model="showPriewModal" />
    <RecordDetail :info="curInfo" v-model="showDetail" />
    <DeliverInvoice :id="curId" v-model="showDeliverInvoice" />
    <QrModal v-model="showQrModal" :data="curQrInfo" :showBtn="false" />
    <QrInvoiceInfo :data="curQrInvoiceInfo" v-model="showEditModal" @on-success="getList" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Pager from 'components/pager'
import PriewModal from './components/PriewModal'
import RecordDetail from './components/RecordDetail'
import DeliverInvoice from './components/DeliverInvoice'
import PrintTipsModal from './components/PrintTipsModal'
import { formatDate } from 'src/utils'
import QrInvoiceInfo from './components/QrInvoiceInfo'
import QrModal from './components/QrModal'
export default {
  name: 'InvoiceRecord',
  components: {
    Pager,
    PrintTipsModal,
    PriewModal,
    QrInvoiceInfo,
    QrModal,
    RecordDetail,
    DeliverInvoice
  },
  data() {
    return {
      showQrModal: false,
      showEditModal: false,
      curQrInfo: null,
      curQrInvoiceInfo: null,
      showDetail: false,
      showPrintModal: false,
      showPriewModal: false,
      showDeliverInvoice: false,
      curId: '',
      curInfo: {},
      curPrintInfo: null,
      selection: [],
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      columns: [
        {
          title: '类型',
          key: 'type',
          render: (h, params) => {
            let dom = ''
            if (params.row.is_qrcode === '1' && params.row.status !=='0') {
              dom = <div>-</div>
            } else if (params.row.type === '1') {
              dom = <div style="color:dodgerblue">专</div>
            } else {
              dom = <div style="color:#19be6b">普</div>
            }
            return dom
          }
        },
        {
          title: '创建时间',
          key: 'create_time'
        },
        {
          title: '业务单号',
          key: 'flow_sn'
        },
        {
          title: '会员姓名',
          key: 'usernames'
        },
        {
          title: '描述',
          key: 'flow_desc'
        },
        {
          title: '发票代码',
          key: 'bill_code',
          render: (h, params) => {
            return (
              <div>
               {params.row.bill_code || '-'}
              </div>
            )
          }
        },
        {
          title: '发票号码',
          key: 'bill_sn',
          render: (h, params) => {
            return (
              <div>
               {params.row.bill_sn || '-'}
              </div>
            )
          }
        },
        {
          title: '发票抬头',
          key: 'bill_title',
          render: (h, params) => {
            return (
              <div>
               {params.row.bill_title || '-'}
              </div>
            )
          }
        },
        {
          title: '含税金额',
          key: 'amount_tax'
        },
        {
          title: '开票方式',
          key: 'is_qrcode_text'
        },
        // {
        //   title: '交付邮箱',
        //   key: 'deliver_email'
        // },
        // {
        //   title: '备注',
        //   key: 'address',
        //   render: (h, params) => {
        //     return (
        //       <div>
        //         地址与电话：{params.row.address} {params.row.tel_number}{' '}
        //         开户行与账号：{params.row.bank_and_account}
        //       </div>
        //     )
        //   }
        // },
        {
          title: '开票状态',
          key: 'tax_identity_number',
          render: (h, params) => {
            const status = params.row.status
            let dom = ''
            if (status === '0') {
              dom = <badge status="success" text="开票完成" />
            } else if (status === '2') {
              dom = (
                <tooltip>
                  <badge status="error" text="开票失败" />
                  <icon
                    type="ios-help-circle"
                    style="color: #f4a627;margin-left:3px"
                  />
                  <div slot="content" style="white-space:normal">
                    {params.row.fail_msg}
                  </div>
                </tooltip>
              )
            } else {
              dom = (
                <div>
                  {status === '1'
                    ? '开票中'
                    : status === '3'
                      ? '重试中'
                      : status === '4'
                        ? '已作废'
                        : status === '5'
                          ? '已提交冲红'
                          : '已放弃'}
                </div>
              )
            }
            return dom
          }
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            //0开票完成 1开票中 2开票失败 3重试中 4已作废 5已提交冲红 6已放弃
            const item = params.row
            return (
              <div>
                {item.status === '0' ||
                item.status === '4' ||
                item.status === '5' ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.curId = item.id
                      this.showPriewModal = true
                    }}
                  >
                    预览
                  </i-button>
                ) : (
                  ''
                )}
                {item.type === '2' &&
                (item.status === '0' || item.status === '5') ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.deliverInvoice(item.id)
                    }}
                  >
                    交付
                  </i-button>
                ) : (
                  ''
                )}
                {item.type === '2' && item.status === '0' ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.invoiceAct('redInvoice', item.id)
                    }}
                  >
                    冲红
                  </i-button>
                ) : (
                  ''
                )}
                {item.status === '2' ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.invoiceAct('reopenInvoice', item.id)
                    }}
                  >
                    重开
                  </i-button>
                ) : (
                  ''
                )}
                {item.status === '2' ? (
                  <i-button
                    class="mr5"
                    style="color:#d9544f"
                    type="text"
                    onClick={() => {
                      this.invoiceAct('giveUpInvoice', item.id)
                    }}
                  >
                    放弃
                  </i-button>
                ) : (
                  ''
                )}
                {item.type === '1' &&
                (item.status === '4' || item.status === '0') ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.curPrintInfo = {
                        id: item.id,
                        bill_code: item.bill_code,
                        bill_sn: item.bill_sn,
                        type: item.type
                      }
                      this.showPrintModal = true
                    }}
                  >
                    打印
                  </i-button>
                ) : (
                  ''
                )}
                {item.is_qrcode === '1' && item.status === '1' ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.curQrInvoiceInfo = {
                        id: item.id,
                        type: item.type,
                        deliver_type: +item.deliver_type,
                        is_enterprise: item.is_enterprise,
                        auth_id: item.auth_id || '-1',
                        bill_title: item.bill_title,
                        tax_identity_number: item.tax_identity_number,
                        tel_number: item.tel_number,
                        bank_and_account: item.bank_and_account,
                        address: item.address,
                        email: item.deliver_email
                      }
                      this.showEditModal = true
                    }}
                  >
                    代填
                  </i-button>
                ) : (
                  ''
                )}
                {item.is_qrcode === '1' && item.status === '1' ? (
                  <i-button
                    class="mr5"
                    type="text"
                    onClick={() => {
                      this.curQrInfo = {
                        id: item.id,
                        is_enterprise: item.is_enterprise,
                        enterprise_name: item.enterprise_name,
                        type: item.type,
                        url: item.qrcode_imgurl,
                        qr_url: item.qrcode_url,
                        bill_order_no: item.flow_sn,
                        amount: item.amount_tax,
                        create_time: item.qrcode_starttime,
                        expiration_time: item.qrcode_endtime
                      }
                      this.showQrModal = true
                    }}
                  >
                    二维码
                  </i-button>
                ) : (
                  ''
                )}
                {(item.type === '1' && item.status === '0') || (item.is_qrcode === '1' && item.status === '1') ? (
                  <i-button
                    class="mr5"
                    onClick={() => {
                      if(item.is_qrcode === '1') {
                        this.handleQrDelete(item.id)
                      } else {
                        this.invoiceAct('cancelInvoice', item.id)
                      }
                    }}
                    type="text"
                    style="color:#d9544f"
                  >
                    作废
                  </i-button>
                ) : (
                  ''
                )}
                {item.status !== '1' ? (
                  <i-button
                    onClick={() => {
                      this.curInfo = item
                      this.showDetail = true
                    }}
                    type="text"
                  >
                    详情
                  </i-button>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ],
      tableData: [],
      listCount: 0,
      statics: '',
      postData: {
        begin_date: formatDate(new Date(), 'yyyy-MM-dd'),
        end_date: formatDate(new Date(), 'yyyy-MM-dd'),
        search: '',
        status: '',
        type: '',
        is_qrcode: '',
        page_no: 1,
        page_size: 10
      }
    }
  },
  created() {},
  computed: {
    ...mapState(['busName'])
  },
  methods: {
    deliverInvoice(id) {
      this.curId = id
      this.showDeliverInvoice = true
    },
    handleQrDelete(id) {
      this.$Modal.confirm({
        title: '作废',
        content: '确定要作废吗?',
        onOk: () => {
          this.$service
            .post('/Web/invoiceQr/updateQrInvoice', {
              id: id,
              qr_status: 3
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.getList()
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      })
    },
    invoiceAct(path, id) {
      this.$service.post(`/Web/invoice/${path}`, { id }).then(res => {
        if (res.data.errorcode === 0) {
          this.getList()
          this.$Message.success(res.data.errormsg)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    dateChange([s, e]) {
      this.postData.begin_date = s
      this.postData.end_date = e
    },
    handleAddInfo() {
      this.$router.push('/invoice/add')
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    gotaxCode() {
      this.$router.push('/invoice/taxCode')
    },
    gotaxNum() {
      this.$router.push('/invoice/taxNum')
    },
    pageChange(postData) {
      const { page_no, page_size } = postData
      this.postData = { ...this.postData, page_no, page_size }
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/invoice/InvoiceList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.statics = data.statics
            this.tableData = data.list.map(item => {
              return {
                ...item,
                is_qrcode_text:
                  item.is_qrcode === '1' ? '二维码开票' : '人工开票'
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleExcel() {
      this.$service
        .post('/Web/invoice/InvoiceListExport', {
          ...this.postData,
          page_no: 1,
          page_size: this.listCount
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data && res.data.data.list)) {
              return false
            }
            this.$refs.table.exportCsv({
              filename: '发票填开',
              columns: [
                {
                  title: '门店名称',
                  key: 'bus_name'
                },
                {
                  title: '类型',
                  key: 'type_text'
                },
                {
                  title: '创建时间',
                  key: 'create_time'
                },
                {
                  title: '业务单号',
                  key: 'flow_sn'
                },
                {
                  title: '会员姓名',
                  key: 'usernames'
                },
                {
                  title: '描述',
                  key: 'flow_desc'
                },
                {
                  title: '发票代码',
                  key: 'bill_code'
                },
                {
                  title: '发票号码',
                  key: 'bill_sn'
                },
                {
                  title: '发票抬头',
                  key: 'bill_title'
                },
                {
                  title: '税号',
                  key: 'tax_identity_number'
                },
                {
                  title: '含税金额',
                  key: 'amount_tax'
                },
                {
                  title: '交付邮箱',
                  key: 'deliver_email'
                },
                {
                  title: '地址',
                  key: 'address'
                },
                {
                  title: '开户行与账号：',
                  key: 'bank_and_account'
                }
              ],
              data: res.data.data.list.map(item => {
                return {
                  ...item,
                  flow_sn: '\t' + item.flow_sn,
                  bill_code: '\t' + item.bill_code,
                  tax_identity_number: '\t' + item.tax_identity_number,
                  type_text: item.is_qrcode === '1' && item.status !=='0' ? '-' : item.type === '1' ? '专票' : '普票',
                  bus_name: this.busName,
                  is_qrcode_text: item.is_qrcode === '1' ? '二维码开票' : '人工开票'
                }
              })
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSelect(selection) {
      this.selection = selection.map(item => item.coupon_id).join(',')
    },
    clickDeleteBtn(id) {
      this.selection = [id]
      this.deleteList()
    },
    handleDelete() {
      if (!this.selection.length) return this.$Message.error('请勾选发票抬头')
      this.$Modal.confirm({
        title: '删除',
        content: '确定要删除吗?',
        onOk: () => {
          this.$service
            .post('/Web/invoice/delInvoiceInfo', { ids: ids })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              console.error(err)
            })
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
  .box-body-total {
    border: 1px solid #dddee1;
    border-top: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

      .t-label {
        width: 200px;
        padding: 0 40px;
        font-size: 16px;
        font-weight: bold;
      }

      .t-desc {
        width: 70%;
        max-width: 650px;
        font-size: 16px;
        display: flex;
        flex-wrap: wrap;
        div {
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          width: 33%;
          display: flex;
        }
      }
  }
  .total-wrap /deep/ .ivu-tooltip-inner{
    max-width: 450px;
    p {
      line-height: 2;
    }
  }
</style>
