<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" @on-enter="doSearch" placeholder="搜索企业名称、税号/身份证号" />
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :columns="columns" @on-selection-change="handleSelect" :data="tableData" stripe />
    <footer>
      <Button style="margin-right: 15px" type="success" @click="handleAddInfo">新增</Button>
      <Button style="margin-right: 15px" @click="handleDelete()" type="success">批量删除</Button>
      <Pager :total="listCount" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <InvoiceEditInfo v-model="showEditModal" :data="curEditInfo" @on-success="getList"/>
  </div>
</template>

<script>
import Pager from 'components/pager'
import InvoiceEditInfo from './components/InvoiceEditInfo'

export default {
  name: 'TitleList',
  components: {
    Pager,
    InvoiceEditInfo
  },
  data() {
    return {
      showEditModal: false,
      curEditInfo: null,
      selection: [],
      columns: [
        {
          type: 'selection',
          width: 80
        },
        {
          title: '发票抬头',
          key: 'bill_title'
        },
        {
          title: '税号/身份证号',
          key: 'tax_identity_number'
        },
        {
          title: '地址、电话',
          key: 'address',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                {item.address}
                {item.tel_number}
              </div>
            )
          }
        },
        {
          title: '开户行及账号',
          key: 'bank_and_account'
        },
        {
          title: '邮箱地址',
          key: 'email'
        },
        {
          title: '操作',
          key: '',
          render: (h, params) => {
            const item = params.row
            return (
              <div>
                <i-button style="margin-right: 15px" type="text" onClick={() => {
                  this.curEditInfo = item
                  this.showEditModal = true
                }}>
                  编辑
                </i-button>
                <i-button
                  onClick={() => this.clickDeleteBtn(item.id)}
                  type="text"
                  style="color:#d9544f"
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      tableData: [],
      listCount: 0,
      postData: {
        search: '',
        page_no: 1,
        page_size: 10
      }
    }
  },
  created() {},
  methods: {
    handleAddInfo() {
      this.curEditInfo = null
      this.showEditModal = true
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(postData) {
      const { page_no, page_size } = postData;
      this.postData = { ...this.postData, page_no, page_size };
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/invoice/invoiceInfoList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.listCount = data.count
            this.tableData = data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    handleSelect(selection) {
      this.selection = selection.map(item => item.id)
    },
    clickDeleteBtn(id) {
      this.selection = [id]
      this.handleDelete()
    },
    handleDelete() {
      if (!this.selection.length) return this.$Message.error('请勾选发票抬头')
      this.$Modal.confirm({
        title: '删除',
        content: '确定要删除吗?',
        onOk: () => {
          this.$service
            .post('/Web/invoice/delInvoiceInfo', { ids: this.selection })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              console.error(err)
            })
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
