<template>
  <div class="commodity customized-tabs">
    <Tabs @on-click="clickTabs" v-model="activeIndex" :animated="true">
      <TabPane label="商品列表">
        <Goods ref="goodsList" v-if="activated.includes(0)"/>
      </TabPane>
      <TabPane label="入出库记录">
        <Storage v-if="activated.includes(1)"/>
      </TabPane>
      <TabPane label="消费记录">
        <Consume v-if="activated.includes(2)"/>
      </TabPane>
    </Tabs>
    <div class="total-bar" v-if="activeIndex === 0">
      <div style="flex: 1; display: flex; align-items: center; margin-left: 300px;">
        <Button type="text" @click="handleVerifyPickupClick">核销提货</Button>
      </div>
      <Poptip placement="top" trigger="hover" width="500">
        <p class="total-bar-item">已选购
          <span class="count">{{count}}</span> 件商品</p>
        <div slot="content" class="cart-list">
          <Table :data="list" :columns="cartColumns" :showHeader="false" disabledHover></Table>
        </div>
      </Poptip>
      <p>共计
        <span style="color: red">{{total}}</span> 元</p>
      <router-link to="/commodity/list/check">
        <Button :disabled="list.length === 0 || inventoryCode !== busId + '-1'" type="error" size="large" class="check">去结算</Button>
      </router-link>
    </div>

    <!-- 商品核销提货弹窗 -->
    <Modal v-model="showVerifyModal" title="商品核销提货" :mask-closable="false" width="600">
      <Form ref="verifyFormRef" :model="verifyForm" :rules="verifyFormRules" :label-width="80">
        <FormItem label="商品凭证" prop="voucher">
          <Input ref="voucherInput" v-model="verifyForm.voucher" placeholder="支持扫码枪识别" @on-change="handleVoucherInputChange" @keydown.enter.native.prevent />
          <div v-if="voucherErrorMsg" style="color: #ed4014; font-size: 13px; margin-top: 4px;">{{ voucherErrorMsg }}</div>
        </FormItem>
      </Form>
      
      <!-- 凭证信息表格 -->
      <div v-if="voucherData.length > 0" style="margin: 20px auto; text-align: center;">
        <h4 style="margin-bottom: 10px;">凭证信息</h4>
        <Table :data="voucherData" :columns="voucherColumns" :loading="voucherLoading" size="small" style="margin: 0 auto;"></Table>
      </div>
      
      <div slot="footer" class="modal-buttons">
        <Button @click="showVerifyModal = false">取消</Button>
        <Button type="primary" :loading="verifyLoading" :disabled="voucherData.length === 0" @click="handleVerifyPickup">核销</Button>
      </div>
    </Modal>

    <Modal v-model="showReceipt" width="380">
      <div style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
        <Icon type="ios-checkmark-circle-outline" size="32" color="#19be6b"></Icon>
        <h3 style="margin-top: 10px;">核销成功！</h3>
      </div>
      <div slot="footer" class="modal-buttons">
        <router-link 
          style="margin-right:20px;color: #fff" 
          target="_blank"
          :to="{ path: '/commodityPrint', query: {type: 4, user_id: receiptInfo.user_id, order_sn: receiptInfo.order_sn, oper_type: 'buygoods'}}">
          <Button type="success">打印小票</Button>
        </router-link>
        <Button @click="showReceipt = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Goods from './components/goodsList';
  import Consume from './components/consumeRecords';
  import Storage from './components/storageList';
  import { mapGetters, mapMutations, mapState } from 'vuex';
  import * as Types from 'types';

  export default {
    name: 'goodsTabs',
    components: {
      Goods,
      Consume,
      Storage
    },
    data() {
      return {
        activeIndex: 0,
        activated: [0],
        showVerifyModal: false,
        keyEnterNum: '',
        verifyForm: {
          voucher: ''
        },
        verifyFormRules: {
          voucher: [
            { required: true, message: '请输入商品凭证号', trigger: 'blur' },
            { len: 12, message: '凭证号必须是12位字符', trigger: 'blur' },
            { pattern: /^[0-9]+$/, message: '凭证号只能是数字', trigger: 'blur' }
          ]
        },
        verifyLoading: false,
        voucherData: [],
        voucherLoading: false,
        voucherErrorMsg: '',
        voucherColumns: [
          {
            title: '购买时间',
            key: 'pay_time',
          },
          {
            title: '商品名称',
            key: 'commodity_name',
          },
          {
            title: '单价',
            key: 'commodity_price',
            width: 100,
            render: (h, params) => {
              return h('span', {
                style: { color: '#ed4014', fontWeight: 'bold' }
              }, `¥${params.row.commodity_price}`);
            }
          },
          {
            title: '数量',
            key: 'purchase_count',
            width: 80
          },
        ],
        cartColumns: [
          {
            key: 'commodity_img',
            title: ' ',
            render: (h, params) => {
              const item = params.row;
              return (
                <a>
                  <img style="width: 30px; height: 30px" src={item.commodity_img}/>
                </a>
              );
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '购买数量',
            key: 'count',
            render: (h, params) => {
              const item = params.row;
              const btnStyle = {
                width: '32px',
                height: '32px',
                minWidth: 'auto',
                boxShadow: 'none'
              };
              const handleTotal = count => {
                item.discountPrice = item.totalPrice = (item.commodity_price * count).toFixed(2);
                this[Types.ADD_GOODS_ITEM](item);
              };
              return (
                <div style="display: flex; justify-content: center; align-items: center">
                  <i-button
                    
                    style={btnStyle}
                    disabled={item.count <= 1}
                    onClick={() => {
                      item.count--;
                      handleTotal(item.count);
                    }}>
                    -
                  </i-button>
                  <i-input
                    class="count"
                    value={item.count}
                    disabled
                    on-on-blur={e => {
                      if (item.count >= item.stock_balance) {
                        this.$Message.error('库存不足');
                        item.count = item.stock_balance;
                        handleTotal(item.count);
                      }
                    }}
                    on-on-change={e => {
                      item.count = e.data;
                      handleTotal(item.count);
                    }}
                    style="width: 42px; height: 32px; margin: 0 5px"
                  />
                  <i-button
                    
                    style={btnStyle}
                    disabled={item.count >= item.stock_balance}
                    onClick={() => {
                      item.count++;
                      handleTotal(item.count);
                    }}>
                    +
                  </i-button>
                </div>
              );
            }
          },
          {
            title: '总计',
            key: 'totalPrice',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  ￥ <span style="color: red">{item.discountPrice}</span>
                </div>
              );
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <i-button
                    type="text"
                    style="color: #d9544f"
                    onClick={() => {
                      this[Types.DELETE_GOODS_ITEM](item.id);
                    }}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        showReceipt: false,
        receiptInfo: {
          user_id: '',
          order_sn: ''
        }
      };
    },
    computed: {
      ...mapGetters('goods', ['list', 'total', 'count']),
      ...mapState(['busId']),
      ...mapState('goods', ['inventoryCode'])
    },
    watch: {
      showVerifyModal(val) {
        if (!val) {
          // 关闭弹窗时重置表单
          this.verifyForm.voucher = '';
          this.verifyLoading = false;
          this.voucherData = [];
          this.voucherLoading = false;
          this.$nextTick(() => {
            this.$refs.verifyFormRef && this.$refs.verifyFormRef.resetFields();
          });
        } else {
          // 弹窗打开时聚焦输入框
          this.$nextTick(() => {
            if (this.$refs.voucherInput && this.$refs.voucherInput.focus) {
              this.$refs.voucherInput.focus();
            }
          });
        }
      }
    },
    created() {
      const tab = +this.$route.query.tab;
      if (tab) {
        this.activated = [tab];
        this.$nextTick(() => this.clickTabs(tab));
        window.location.hash = '#/commodity/list'
      }
    },
    mounted() {
      document.addEventListener('keydown', this.handleKeyDown);
    },
    beforeDestroy() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },
    activated() {
      document.addEventListener('keydown', this.handleKeyDown);
    },
    deactivated() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },
    methods: {
      ...mapMutations('goods', [Types.ADD_GOODS_ITEM, Types.DELETE_GOODS_ITEM]),
      clickTabs(index) {
        this.activeIndex = index;
        const active = document.querySelector('.ivu-tabs-ink-bar');
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
        active.setAttribute('class', className);
        if (!this.activated.includes(index)) {
          this.activated.push(index);
        }
      },
      handleVerifyPickupClick() {
        // 检查是否有选中的商品
        // const goodsList = this.$refs.goodsList;
        // if (!goodsList || !goodsList.selection || goodsList.selection.length === 0) {
        //   this.$Message.warning('请先选择要核销的商品');
        //   return;
        // }
        // 有选中商品，显示弹窗
        this.voucherErrorMsg = '';
        this.showVerifyModal = true;
      },
      handleVoucherInputChange(event) {
        const value = event.target.value;
        console.log('input:', value);
        const numericValue = value.replace(/[^0-9]/g, '');
        console.log('numericValue:', numericValue);
        this.verifyForm.voucher = numericValue;
        
        if (numericValue.length === 12) {
          this.fetchVoucherData(numericValue);
        } else {
          this.voucherData = [];
        }
      },
      handleKeyDown(e) {
        const event = window.event || e;
        let key = event.key;
        
        // Only handle uppercase 'G' key in parent component
        if (event.target.localName !== 'input' && event.target.localName !== 'textarea') {
          // Handle numeric keys and 'G' for barcode/voucher scanning
          if (/^[0-9G]*$/.test(key)) {
            this.keyEnterNum += key;
          } else if (key === 'Enter') {
            if (this.keyEnterNum.startsWith('G')) {
              this.handleVoucherInput(this.keyEnterNum.slice(1));
            } else {
              this.handleGetCommodity(this.keyEnterNum);
            }
            this.keyEnterNum = '';

            if (this.showVerifyModal) {
              // 阻止默认行为和事件冒泡
              event.preventDefault && event.preventDefault();
              event.stopPropagation && event.stopPropagation();
            }
          }
        }
      },
      handleVoucherInput(voucherCode) {
        if (voucherCode.length === 12) {
          this.verifyForm.voucher = voucherCode;
          this.showVerifyModal = true;
          this.fetchVoucherData(voucherCode);
        } else {
          this.$Message.error('凭证码格式不正确，应为12位字符');
        }
      },
      handleGetCommodity(barcode) {
        // This method handles non-G prefixed barcodes for commodity lookup
        // For now, delegate to the goodsList component
        if (this.$refs.goodsList && this.$refs.goodsList.handleGetCommodity) {
          this.$refs.goodsList.handleGetCommodity('body', barcode);
        } else {
          console.log('Commodity barcode scanned:', barcode);
          this.$Message.info('商品条码: ' + barcode);
        }
      },
      fetchVoucherData(voucher) {
        this.voucherLoading = true;
        this.voucherData = [];
        this.voucherErrorMsg = '';
        const url = '/Web/ConsumptionLog/prepare';
        this.$service
          .post(url, { consume_sn: voucher })
          .then(res => {
            if (res.data.errorcode === 0) {
              if (Array.isArray(res.data.data)) {
                this.voucherData = res.data.data || [];
              } else {
                this.voucherData = [res.data.data]
              }

              this.voucherErrorMsg = '';
            } else {
              this.voucherErrorMsg = res.data.errormsg || '获取凭证信息失败';
              this.$Message.error(res.data.errormsg || '获取凭证信息失败');
            }
          })
          .catch(() => {
            this.voucherErrorMsg = '获取凭证信息失败，请重试';
            this.$Message.error('获取凭证信息失败，请重试');
          })
          .finally(() => {
            this.voucherLoading = false;
          });
      },
      handleVerifyPickup() {
        // 表单验证
        this.$refs.verifyFormRef.validate((valid) => {
          if (!valid) {
            return false;
          }
          
          this.verifyLoading = true;
          
          // 获取选中的商品
          // const info = this.voucherData[0]

          // 处理核销提货逻辑
          const url = '/Web/ConsumptionLog/verify';
          this.$service
            .post(url, {
              // id: info.id,
              consume_sn: this.verifyForm.voucher
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success('核销成功');
                this.showVerifyModal = false;
                this.verifyForm.voucher = '';
                // 打印小票
                this.showReceipt = true;
                this.receiptInfo = {
                  user_id: res.data.data.user_id || '',
                  order_sn: res.data.data.order_sn || '',
                }
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(() => {
              this.$Message.error('核销失败，请重试');
            })
            .finally(() => {
              this.verifyLoading = false;
            });
        });
      }
    }
  };
</script>

<style lang="less">
.commodity {
    background-color: #fff;

    .count {
      .ivu-input {
        text-align: center;
      }
    }

    .ivu-table td {
      border-bottom: 0;
}

.cart-list {
      max-height: 400px;
      overflow-y: scroll;
    }

    .ivu-tabs-nav {
      .ivu-tabs-ink-bar {
        width: 1 / 3 * 100% !important;
}

      .tabs-active-1 {
        transform: translate3d(100%, 0, 0) !important;
      }

      .tabs-active-2 {
        transform: translate3d(200%, 0, 0) !important;
      }
    }

    .total-bar {
      height: 76px;
      width: 100%;
      position: fixed;
      z-index: 1;
      bottom: 0;
      left: 0;
      background-color: #dcdcdc;

  display: flex;
  justify-content: flex-end;
      align-items: center;
      padding-right: 60px;

      .ivu-table:after,
      .ivu-table:before {
        width: 0;
}

      .check {
        height: 36px;
        width: 110px;
}

      > p,
      .total-bar-item {
        padding-right: 30px;
        font-size: 16px;
        color: #666;

        > span,
        .count {
          font-size: 22px;
  font-weight: bold;
}
      }
    }
}
</style>
