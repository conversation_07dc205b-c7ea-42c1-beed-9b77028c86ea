<template>
  <div class="commodity customized-tabs">
    <Tabs @on-click="clickTabs" v-model="activeIndex" :animated="true">
      <TabPane label="商品列表">
        <Goods v-if="activated.includes(0)"/>
      </TabPane>
      <TabPane label="入出库记录">
        <Storage v-if="activated.includes(1)"/>
      </TabPane>
      <TabPane label="消费记录">
        <Consume v-if="activated.includes(2)"/>
      </TabPane>
    </Tabs>
    <div class="total-bar" v-if="activeIndex === 0">
      <Poptip placement="top" trigger="hover" width="500">
        <p class="total-bar-item">已选购
          <span class="count">{{count}}</span> 件商品</p>
        <div slot="content" class="cart-list">
          <Table :data="list" :columns="cartColumns" :showHeader="false" disabledHover></Table>
        </div>
      </Poptip>
      <p>共计
        <span style="color: red">{{total}}</span> 元</p>
      <router-link to="/commodity/list/check">
        <Button :disabled="list.length === 0 || inventoryCode !== busId + '-1'" type="error" size="large" class="check">去结算</Button>
      </router-link>
    </div>
  </div>
</template>

<script>
  import Goods from './components/goodsList';
  import Consume from './components/consumeRecords';
  import Storage from './components/storageList';
  import { mapGetters, mapMutations, mapState } from 'vuex';
  import * as Types from 'types';

  export default {
    name: 'goodsTabs',
    components: {
      Goods,
      Consume,
      Storage
    },
    data() {
      return {
        activeIndex: 0,
        activated: [0],
        cartColumns: [
          {
            key: 'commodity_img',
            title: ' ',
            render: (h, params) => {
              const item = params.row;
              return (
                <a>
                  <img style="width: 30px; height: 30px" src={item.commodity_img}/>
                </a>
              );
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '购买数量',
            key: 'count',
            render: (h, params) => {
              const item = params.row;
              const btnStyle = {
                width: '32px',
                height: '32px',
                minWidth: 'auto',
                boxShadow: 'none'
              };
              const handleTotal = count => {
                item.discountPrice = item.totalPrice = (item.commodity_price * count).toFixed(2);
                this[Types.ADD_GOODS_ITEM](item);
              };
              return (
                <div style="display: flex; justify-content: center; align-items: center">
                  <i-button
                    
                    style={btnStyle}
                    disabled={item.count <= 1}
                    onClick={() => {
                      item.count--;
                      handleTotal(item.count);
                    }}>
                    -
                  </i-button>
                  <i-input
                    class="count"
                    value={item.count}
                    disabled
                    on-on-blur={e => {
                      if (item.count >= item.stock_balance) {
                        this.$Message.error('库存不足');
                        item.count = item.stock_balance;
                        handleTotal(item.count);
                      }
                    }}
                    on-on-change={e => {
                      item.count = e.data;
                      handleTotal(item.count);
                    }}
                    style="width: 42px; height: 32px; margin: 0 5px"
                  />
                  <i-button
                    
                    style={btnStyle}
                    disabled={item.count >= item.stock_balance}
                    onClick={() => {
                      item.count++;
                      handleTotal(item.count);
                    }}>
                    +
                  </i-button>
                </div>
              );
            }
          },
          {
            title: '总计',
            key: 'totalPrice',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  ￥ <span style="color: red">{item.discountPrice}</span>
                </div>
              );
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <i-button
                    type="text"
                    style="color: #d9544f"
                    onClick={() => {
                      this[Types.DELETE_GOODS_ITEM](item.id);
                    }}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ]
      };
    },
    computed: {
      ...mapGetters('goods', ['list', 'total', 'count']),
      ...mapState(['busId']),
      ...mapState('goods', ['inventoryCode'])
    },
    created() {
      const tab = +this.$route.query.tab;
      if (tab) {
        this.activated = [tab];
        this.$nextTick(() => this.clickTabs(tab));
        window.location.hash = '#/commodity/list'
      }
    },
    methods: {
      ...mapMutations('goods', [Types.ADD_GOODS_ITEM, Types.DELETE_GOODS_ITEM]),
      clickTabs(index) {
        this.activeIndex = index;
        const active = document.querySelector('.ivu-tabs-ink-bar');
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
        active.setAttribute('class', className);
        if (!this.activated.includes(index)) {
          this.activated.push(index);
        }
      }
    }
  };
</script>

<style lang="less">
  .commodity {
    background-color: #fff;

    .count {
      .ivu-input {
        text-align: center;
      }
    }

    .ivu-table td {
      border-bottom: 0;
    }

    .cart-list {
      max-height: 400px;
      overflow-y: scroll;
    }

    .ivu-tabs-nav {
      .ivu-tabs-ink-bar {
        width: 1 / 3 * 100% !important;
      }

      .tabs-active-1 {
        transform: translate3d(100%, 0, 0) !important;
      }

      .tabs-active-2 {
        transform: translate3d(200%, 0, 0) !important;
      }
    }

    .total-bar {
      height: 76px;
      width: 100%;
      position: fixed;
      z-index: 1;
      bottom: 0;
      left: 0;
      background-color: #dcdcdc;

      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 60px;

      .ivu-table:after,
      .ivu-table:before {
        width: 0;
      }

      .check {
        height: 36px;
        width: 110px;
      }

      > p,
      .total-bar-item {
        padding-right: 30px;
        font-size: 16px;
        color: #666;

        > span,
        .count {
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
</style>
