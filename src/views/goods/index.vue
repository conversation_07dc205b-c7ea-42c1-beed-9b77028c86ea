<template>
  <router-view
    v-if="$route.name !== '会员端商城' && $route.meta.breadText !== '会员端商城'"
  />
  <PointList v-else />
  <!-- <div v-else class="tab-table-wrap customized-tabs">
    <Tabs :value="activeIndex" @on-click="clickTabs">
      <TabPane label="魅力值商城" name="0">
        <CharmList />
      </TabPane>
      <TabPane label="积分商城" name="1">
        <PointList v-if="activated.includes('1')" />
      </TabPane>
      <TabPane label="金币商城" name="2">
        <CoinsList v-if="activated.includes('2')" />
      </TabPane>
    </Tabs>
  </div> -->
</template>

<script>
// import CharmList from './charmList'
import PointList from './pointList'
// import CoinsList from './coinsList'
import tabsMixins from 'mixins/tabs'

export default {
  name: 'GoodsManagement',
  components: {
    // CharmList,
    PointList,
    // CoinsList
  },

  mixins: [tabsMixins],
}
</script>

<style lang="less" scoped>
/* .tab-table-wrap {
  height: 100%;
  .ivu-tabs {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    /deep/.ivu-tabs-content {
      flex: 1;
    }
  }
} */
.alert-row {
  padding: 10px 15px;
  background-color: #fff;
  .ivu-alert {
    margin-bottom: 0;
  }
}
.table-wrap {
  padding: 20px 28px;
  // min-height: 100%;
}
</style>
