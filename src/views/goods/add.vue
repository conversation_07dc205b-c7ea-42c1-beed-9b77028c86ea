<template>
  <div class="container">
    <header>
      <h3>商品添加</h3>
    </header>
    <Form
      ref="form"
      label-position="right"
      :model="formItem"
      :rules="formRules"
      class="form"
      :label-width="180">
      <FormItem prop="goods_image">
        <div slot="label" class="image-description image-description-required">
          <p class="label">商品图片</p>
          <p class="tip">图片最佳尺寸: 500X500</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="photo">
          <img :src="formItem.goods_image">
        </div>
        <Input v-model="formItem.goods_image" class="input" style="display:none" />
        <Button @click="showImgUpload=true">更换图片</Button>
      </FormItem>
      <Form-item label="商品类型" prop="goods_type">
        <Select v-model="formItem.goods_type">
          <Option :value="1">体验卡</Option>
          <Option :value="3">体验课</Option>
          <Option :value="2">折扣券</Option>
          <Option :value="0">其他</Option>
        </Select>
        <div>注：体验卡/课商品，用户在兑换后，会自动进入发放；其他类型，在用户点击申请后需在前台兑换</div>
      </Form-item>
      <Form-item v-if="formItem.goods_type == 1" label="商品名称" prop="card_id">
        <Select
          v-if="expCardList && expCardList.length>0"
          v-model="formItem.card_id"
          label-in-value
          @on-change="cardChange">
          <Option v-for="item in expCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
        </Select>
      </Form-item>
      <Form-item v-if="formItem.goods_type == 3" label="商品名称" prop="card_id">
        <ExpClassSelect v-model="formItem.card_id" @on-change="classChange" />
      </Form-item>
      <Form-item v-if="formItem.goods_type == 2" label="折扣券" prop="card_id">
        <busDiscount v-model="formItem.card_id" @on-change="({label}) => { formItem.goods_name = label }"></busDiscount>
      </Form-item>
      <FormItem v-else label="商品名称" prop="goods_name">
        <Input v-model="formItem.goods_name" class="input" placeholder="限10个字" />
      </FormItem>
      <FormItem v-if="!goodsId" label="商品库存" prop="total_volume">
        <Input v-model="formItem.total_volume" class="input" />
      </FormItem>
      <FormItem v-else label="商品库存" prop="last_volume">
        总量{{ formItem.total_volume }}，剩余
        <Input-number
          v-model="formItem.last_volume"
          :min="0"
          :step="1"
          style="margin-left:5px" />
      </FormItem>
      <FormItem label="商品单价" prop="single_price">
        <Input v-model="formItem.single_price" class="input" placeholder="魅力值" />
        <Card class="goods-card" dis-hover>
          <p slot="title">魅力值获取规则</p>
          <ul>
            <li>每天签到：魅力值+{{ charmSet.first_sign }}（每天限一次）</li>
            <li>上团课：魅力值+{{ charmSet.class_sign }} 每节</li>
            <li>上私教课：魅力值+{{ charmSet.private_sign }} 每节</li>
            <li>发布动态：魅力值+{{ charmSet.release_dynamic }} 每条（每天限3次）</li>
            <li>点赞他人动态：魅力值+{{ charmSet.dynamic_praise }} 每赞</li>
            <li>转发朋友贡献昨日步数：魅力值+1 每2500步</li>
          </ul>
          <p class="bot">以一个用户一周3天签到计算，一周大概能获取{{ weekAll }}魅力值</p>
        </Card>
      </FormItem>
      <FormItem label="商品状态">
        <RadioGroup v-model="formItem.status">
          <Radio label="1">启用</Radio>
          <Radio label="0">禁用</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="商品说明">
        <Input
          v-model="formItem.remark"
          placeholder="限100个字"
          type="textarea"
          :autosize="{minRows: 4, maxRows: 8}" />
      </FormItem>
      <FormItem>
        <div class="buttons">
          <Button type="success" @click="addGoods">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
    <ImgUploadWithBase v-model="showImgUpload" @on-change="imgChange" />
  </div>
</template>

<script>
  import ImgUploadWithBase from 'components/form/ImgUploadWithBase';
  import ExpClassSelect from 'components/form/ExpClassSelect';
  import busDiscount from 'components/form/busDiscount';
  export default {
    name: 'GoodsAdd',
    components: {
      ImgUploadWithBase,
      ExpClassSelect,
      busDiscount
    },
    data() {
      return {
        goodsId: this.$route.params.goodsId,
        formItem: {
          bus_id: this.$store.state.busId,
          goods_name: '',
          goods_type: 1,
          goods_image: '',
          card_id: '',
          total_volume: '',
          last_volume: 0,
          single_price: '',
          status: '1',
          remark: ''
        },
        weekAll: 0,
        charmSet: '',
        firstChage: true,
        expCardList: [],
        showImgUpload: false,
        formRules: {
          goods_image: [{ required: true, message: '请选择商品图片' }],
          goods_name: [{ required: true, message: '请填写商品名称' }],
          card_id: [{ required: true, message: '请选择体验卡' }],
          total_volume: [
            { required: true, message: '请正确填写商品库存', type: 'string', pattern: /^\d+$/, trigger: 'blur' }
          ],
          single_price: [
            { required: true, type: 'string', pattern: /^\d+$/, message: '商品单价必须为非负整数', trigger: 'blur' }
          ]
        }
      };
    },
    computed: {},
    watch: {
      'formItem.last_volume'(val, oldVal) {
        val = parseInt(val);
        oldVal = parseInt(oldVal);
        if (this.goodsId && this.firstChage) {
          this.firstChage = false;
        } else if (!this.firstChage && this.goodsId) {
          let cha = val - oldVal;
          this.formItem.total_volume = parseInt(this.formItem.total_volume) + cha;
        }
      }
    },
    created() {
      this.getExpCard();
      this.getCharmSet();
      if (this.goodsId) {
        this.getGoodsInfo();
      }
    },
    methods: {
      getExpCard() {
        this.$service.get('/Web/Goods/get_experience_list').then(res => {
          if (res.data.errorcode == 0) {
            this.expCardList = res.data.data.list;
          } else {
            this.expCardList = [];
            this.$Message.error(res.data.errormsg);
          }
        });
      },

      getCharmSet() {
        this.$service.get('/Web/Goods/get_charm_value_setting').then(res => {
          if (res.data.errorcode == 0) {
            let charmSet = res.data.data.info;
            this.charmSet = charmSet;
            this.weekAll =
              parseInt(charmSet.private_sign) +
              parseInt(charmSet.class_sign) +
              3 * parseInt(charmSet.first_sign) +
              parseInt(charmSet.release_dynamic) * 2 +
              parseInt(charmSet.dynamic_praise) * 3 +
              6;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },

      getGoodsInfo() {
        this.$service.post('/Web/Goods/get_goods_info', { goods_id: this.goodsId }).then(res => {
          if (res.data.errorcode == 0) {
            let resData = res.data.data;
            this.formItem = resData.info;
            this.formItem.last_volume = parseInt(resData.info.last_volume);
            this.formItem.total_volume = parseInt(resData.info.total_volume);
            this.formItem.goods_type = parseInt(resData.info.goods_type);
            if (this.formItem.goods_type === 3) {
              this.formItem.card_id = parseInt(resData.info.card_id);
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      cardChange(res) {
        this.formItem.card_id = res.value;
        this.formItem.goods_name = res.label;
      },
      classChange(res) {
        this.formItem.card_id = res.card_id;
        this.formItem.goods_name = res.card_name;
      },
      imgChange(path) {
        this.formItem.goods_image = path;
      },
      addGoods() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            this.$Message.error('请完成信息填写');
            return false;
          }
          let url = '/Web/Goods/add_goods';
          let postData = this.formItem;
          if (this.goodsId) {
            url = '/Web/Goods/update_goods_info';
            postData.goods_id = this.goodsId;
          }
          this.$service
            .post(url, postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$router.back();
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.log(err);
            });
        });
      }
    }
  };
</script>
<style lang="less">
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .photo {
    .flex-center;
    border: 1px solid #dcdcdc;
    width: 200px;
    height: 200px;
    box-sizing: border-box;
    padding: 2px;
    margin-bottom: 20px;
    > img {
      width: 100%;
      height: 100%;
    }
  }

  .container .form {
    padding-bottom: 44px;
  }
  .goods-card {
    margin-top: 15px;
    font-size: 12px;

    ul {
      li {
        float: left;
        width: 50%;
        color: #999;
      }
    }
    .bot {
      color: red;
    }
    .ivu-card-head {
      padding-top: 8px;
      padding-bottom: 8px;
      p {
        font-size: 12px;
        font-weight: normal;
      }
    }
  }
</style>
