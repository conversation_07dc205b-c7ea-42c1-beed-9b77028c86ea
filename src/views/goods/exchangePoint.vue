<template>
  <div>
    <div class="table-wrap">
      <header>
        <DatePickerWithButton
          :days.sync="days"
          clearable
          @on-change="dateChanged" />
        <Select
          v-model="postData.goods_id"
          class="w120"
          placeholder="选择商品"
          filterable
          clearable>
          <Option v-for="item in goodsList" :key="item.id" :value="item.goods_id">{{ item.goods_name }}</Option>
        </Select>
        <Input
          v-model="postData.search"
          class="w120"
          placeholder="请输入手机号码"
          @on-enter="search" />
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>

      <main>
        <Table
          ref="table"
          :columns="columns"
          :data="tableData"
          stripe
          disabled-hover />
      </main>

      <footer>
        <Button type="success" style="margin-right:30px" @click="goAdd">兑换商品</Button>
        <Page
          class="page"
          :total="totalCount"
          :page-size="postData.page_size"
          :current.sync="postData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="pageChanged"
          @on-page-size-change="pageSizeChanged" />
      </footer>
    </div>

    <Modal
      v-model="showExchangeModal"
      title="兑换商品"
      width="600"
      class="sign_modal"
      :mask-closable="false"
      :transfer="false"
      @on-cancel="closeExchangeCallBack">
      <Select
        v-model="selectedUserId"
        filterable
        clearable
        remote
        :remote-method="getUserExchangeInfo"
        :loading="searching"
        placeholder="用户姓名、昵称、电话">
        <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">{{ user.username }}</Option>
      </Select>
      <div v-if="userInfo" class="user-box">
        <div class="flex-center">
          <img class="user-avatar" :src="userInfo.avatar">
          <div>
            <h4 class="mb10">{{ userInfo.username }}</h4>
            <p>{{ userInfo.phone }}</p>
          </div>
        </div>
        <div>
          可用积分 <span class="userinfo-txt">{{ userInfo.charm_value }}</span>
        </div>
      </div>

      <div v-if="userInfo" class="goods-list">
        <div>
          <div v-if="userInfo.goods_list && userInfo.goods_list.length">
            <div>
              <div style="padding-top: 10px">
                <RadioGroup v-model="modalData.goods_id">
                  <Radio
                    v-for="goods in userInfo.goods_list"
                    :key="goods.goods_id"
                    :label="goods.goods_id"
                    :disabled="goods.point > userInfo.charm_value">
                    <h5>{{ goods.goods_name }}</h5>
                    <p>需要积分 {{ goods.point }}</p>
                  </Radio>
                </RadioGroup>
              </div>
            </div>
          </div>
          <p v-else class="nodata">
            暂无可兑换商品信息
          </p>
        </div>
      </div>

      <!-- <div class="exchange-glod-input-row">
        <Row type="flex" align="middle">
          <Col span="4">兑换金币数量</Col>
          <Col span="20">
            <Input-number
              v-model="coinsNum"
              :max="999999999"
              :min="1"
              :precision="0"
              :active-change="false" />
          </Col>
        </Row>
        <div>需要积分{{}}</div>
      </div> -->

      <Input
        v-if="userInfo && userInfo.goods_list && userInfo.goods_list.length"
        v-model="modalData.remark"
        type="textarea"
        :autosize="{minRows: 4,maxRows: 5}"
        placeholder="备注（可不填）" />

      <div slot="footer" class="modal-buttons">
        <Button type="success" :disabled="!modalData.user_id" @click="handleConfirmExchange">兑换商品</Button>
        <Button @click="closeExchangeCallBack">取消</Button>
      </div>

      <Modal
        v-model="showVerifySmsModal"
        title="请输入会员收到的短信验证码"
        :closable="false"
        :mask-closable="false">
        <i-input v-model="modalData.code" placeholder="短信验证码" @on-enter="addExchange">
          <Button slot="append" :disabled="!!verifySec" @click="handleResendVerifyCode">{{ resendVerifyCode }}</Button>
        </i-input>
        <div slot="footer" style="text-align:center;">
          <div>
            <Button @click="handleShowVerifyModal(false)">关闭</Button>
            <Button type="success" @click="addExchange">确认</Button>
          </div>
          <!-- <div style="margin-top:16px;">
            <span style="color:#AAA;">5分钟后未确认会取消该订单</span>
          </div> -->
        </div>
      </Modal>
    </Modal>
  </div>
</template>

<script>
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';

let countDownTimer = null;
let closeTimer = null;

export default {
  name: 'exchangePoint',
  components: {
    DatePickerWithButton,
  },

  data() {
    return {
      days: [Date.now() - 365 * 24 * 60 * 60 * 1000, Date.now()], // 上年今天到今天
      tableData: [], // 兑换记录列表
      userList: [], // 搜索用户数据
      goodsList: [], // 所有积分商品
      postData: {
        s_date: '',
        e_date: '',
        search: '',
        goods_id: this.$route.params.id || '', // 传积分商品列表的id
        page_size: 10,
        page_no: 1
      },
      totalCount: 0,
      columns: [
        {
          title: '兑换时间',
          key: 'create_time'
        },
        {
          title: '兑换商品',
          key: 'goods_name'
        },
        {
          title: '兑换人姓名',
          key: 'username'
        },
        {
          title: '兑换人电话',
          key: 'phone'
        },
        {
          title: '体验卡转赠接收人',
          key: 'recipient_username'
        },
        {
          title: '备注',
          key: 'remark'
        }
      ],
      // 商品兑换弹窗相关数据
      showExchangeModal: false,
      selectedUserId: '',
      userInfo: '',
      modalData: {
        user_id: '',
        goods_id: '', // 传积分商品列表的id
        remark: '',
        code: '' // 验证码
      },
      needVerifyCode: false, // 是否需要验证码
      showVerifySmsModal: false, // 输入验证码弹窗
      verifySec: 0, // 验证码发送倒计时
      searching: false, // 搜索用户loading

    };
  },
  computed: {
    resendVerifyCode() {
      return this.verifySec > 0 ? `再次发送(${this.verifySec}s)` : '发送';
    },
  },

  watch: {
    selectedUserId(val) {
      if (!val) {
        this.userInfo = null
        return
      }
      let selectedInfo = this.userList.filter(user => {
        return user.user_id === val
      })
      this.userInfo = selectedInfo[0];
      this.modalData.goods_id = selectedInfo[0] && selectedInfo[0].goods_list && selectedInfo[0].goods_list[0] ? selectedInfo[0].goods_list[0].goods_id : '';
      this.modalData.user_id = val
    }
  },

  created() {
    this.getPointGoodsList();
    this.getExchangeList();
  },

  methods: {
    closeExchangeCallBack() {
      this.userInfo = null;
      this.selectedUserId = '';
      this.modalData.user_id = '';
      this.modalData.remark = '';
      this.modalData.goods_id = '';
      this.modalData.code = '';
      this.showExchangeModal = false;
    },
    goAdd() {
      this.showExchangeModal = true;
      this.getSettingData()
    },
    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getExchangeList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getExchangeList();
    },
    search() {
      this.postData.page_no = 1;
      this.getExchangeList();
    },
    // 获取兑换列表数据
    getExchangeList() {
      this.$service
        .post('/Web/PointGoods/exchange_list', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    // 获取全部积分商品列表
    getPointGoodsList() {
      this.$service
        .post('/Web/PointGoods/all_goods_list')
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.goodsList = data.list;
            this.modalData.goods_id = data.list[0].goods_id;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    // 搜索兑换用户信息
    getUserExchangeInfo(search) {
      if (search !== '') {
        this.searching = true;
        this.$service
        .post('/Web/PointGoods/SearchExchangeUser', { search })
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.userList = data.list;
            this.searching = false;
          } else {
            this.searching = false;
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          this.searching = false;
          console.error(err);
        });
      }
    },
    // 关闭订单
    handleShutDownOrder(isAuto) {
      this.$service.post(
        '/Web/Commodity/close_commodity_settlement',
        {  }
      ).then(res => {
        if (res.data.errorcode == 0) {
          this.handleShowVerifyModal(); // 关闭验证码弹窗

          clearTimeout(countDownTimer) // 清除验证码倒计时
          countDownTimer = null;
          clearTimeout(closeTimer) // 清除自动关闭倒计时
          closeTimer = null;

          if (isAuto) {
            this.$Modal.info({
              title: '提示',
              content: '已自动关闭订单',
              okText: '确定'
            })
          } else {
            this.$Message.success(res.data.errormsg)
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 获取积分兑换确认设置
    getSettingData() {
      this.$service.post("/Web/PointSetting/getPintSetting").then(res => {
        if (res.data.errorcode === 0) {
          this.needVerifyCode = res.data.data.point_confirm == 1; // 积分兑换验证码确认 1启用 0禁用
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    // 兑换请求
    addExchange() {
      const params = {
        ...this.modalData
      }
      this.$service
        .post('/web/PointGoods/ExchangePointGoods', params)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getExchangeList();
            this.handleShowVerifyModal() // 关闭验证码弹窗
            this.closeExchangeCallBack() // 重置表单;
            this.$Message.success(res.data.errormsg);
          }else { // 40001 验证码为空
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 发送验证码
    handleResendVerifyCode() {
      if (this.verifySec > 0) return;
      const { userInfo, modalData } = this;
      const params = {
        user_id: userInfo.user_id,
        goods_id: modalData.goods_id,
        loading: true
      }
      return this.$service.post('/web/PointGoods/ExchangePointGoodsSendMsg', params).then(res => {
        if (Number(res.data.errorcode) == 0) {
          this.$Message.success(res.data.errormsg)
          this.handleSendCountDown() // 开始验证码发送倒计时处理
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },

    // 兑换确认
    handleConfirmExchange() {
      // 判断是否需要验证 验证码
      if (this.needVerifyCode) {
        this.handleShowVerifyModal(true)
      } else {
        this.addExchange() // 请求
      }
    },
    // 短信验证码确认提交 兑换
    // handleConfirmSuccess() {
    //   clearTimeout(closeTimer)
    //   closeTimer = null;

    //   const params = {
    //     loading: true,
    //   }

    //   this.$service.post('/Web/Commodity/submit_commodity_settlement', params).then(res => {
    //     if (res.data.errorcode == 0) {
    //       this.handleShowVerifyModal() // 关闭验证码弹窗
    //       this.closeExchangeCallBack() // 重置表单
    //       this.$Message.success(res.data.errormsg)
    //     } else {
    //       this.$Message.error(res.data.errormsg)
    //     }
    //   }).catch(err => {
    //     console.error(err)
    //   })
    // },

    // 显示/隐藏短信验证码弹窗
    handleShowVerifyModal(isShow = false) {
      this.showVerifySmsModal = isShow;
      if (isShow) {
        // this.handleAutoShutDownSms() // 开始自动关闭订单倒计时
        this.handleResendVerifyCode() //  发送验证码
      } else {
        this.modalData.code = '';
      }
    },
    // 处理验证码验证倒计时，倒计时结束，自动关闭订单
    // handleAutoShutDownSms() {
    //   closeTimer = setTimeout(() => {
    //     this.handleShutDownOrder(true) // true 自动关闭
    //   }, 5 * 60 * 1000)
    //   // }, 10 * 1000)
    // },
    // 验证码发送倒计时
    handleSendCountDown() {
      this.verifySec = 30;
      countDownTimer = setInterval(() => {
        this.verifySec--;
        if (this.verifySec === 0) {
          clearInterval(countDownTimer)
          countDownTimer = null;
        }
      }, 1000)
    },

  }
};
</script>

<style>
.goods-list .ivu-radio{
  position: absolute;
  right: 0;
  bottom: 15px;
}
</style>

<style lang="less" scoped>
.flex-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.user-box {
  padding: 10px;
  .flex-center;
  margin-top: 20px;
  border: 1px solid #dddee1;
  .user-avatar{
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
  }
}
.userinfo-txt{
  font-size: 16px;
  color: #d8321f;;
}
.mb10 {
  margin-bottom: 10px;
}
.goods-list{
  margin-bottom: 20px;
  .nodata{
    line-height: 50px;
    text-align: center;
    margin-top: 10px;
  }
  .ivu-radio-wrapper{
    width: 140px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(95, 183, 93);
    padding: 15px 30px 15px 10px;
    margin-right: 10px;
    position: relative;
    h5 {
      margin-bottom: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.exchange-glod-input-row {
  margin-bottom: 20px;
  .ivu-input-number {
    width: 100%;
  }
}
</style>
