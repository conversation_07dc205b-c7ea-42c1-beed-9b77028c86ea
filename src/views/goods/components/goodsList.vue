<style lang="less" scoped>
  .goods-list {
    margin-bottom: 40px;
    display: flex;
    height: 100%;
  }
</style>

<style>
  .ivu-tabs .ivu-table .ivu-table-body {
    min-height: 40vh;
  }

  .disabled-row td {
    background-color: #eee;
  }
</style>

<template>
  <div class="goods-list">
    <GoodsNav
      style="width: 240px; border-left: 1px solid #e0e3e9; border-top: 1px solid #e0e3e9;"
      @groupChange="groupChange"
      @inventoryChange="getList(true)"></GoodsNav>
    <div class="table-wrap" style="border-top: 0;">
      <header style="border-bottom: 0">
        <Input v-model="postData.search" placeholder="商品名称" @on-enter="getList(1)" />
        <Select v-model="postData.online" placeholder="商品状态" clearable>
          <Option value="1" label="上架"></Option>
          <Option value="0" label="下架"></Option>
        </Select>
        <Button type="success" @click="getList(1)">搜索</Button>
      </header>
      <Table
        ref="table"
        :row-class-name="modalRowClassName"
        :columns="columns"
        :data="tableData"
        class="avatar-zoom"
        @on-selection-change="onSelectionChange"
        @on-select="onSelect"
        @on-select-all="onSelectAll"
        @on-select-cancel="onSelectCancel" />
      <footer>
        <div class="left">
          <router-link
            v-if="adminInfo.commodity_inventory.add_commodity"
            :to="{ path: '/commodity/list/detail', query: { inventoryCode: this.inventoryCode } }">
            <Button type="success" icon="plus">添加商品</Button>
          </router-link>
          <Dropdown style="margin-left: 15px;" placement="top" @on-click="otherCase">
            <Button>
              其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <template v-if="adminInfo.commodity_inventory.in">
                <DropdownItem name="show">会员端展示</DropdownItem>
                <DropdownItem name="hide">会员端不展示</DropdownItem>
              </template>
              <DropdownItem v-if="adminInfo.commodity_inventory.export_excel" name="excel">导出excel</DropdownItem>
              <!--<DropdownItem name="import">导入商品</DropdownItem>-->
              <template v-if="adminInfo.commodity_inventory.in">
                <DropdownItem name="storage">批量入库</DropdownItem>
                <DropdownItem name="damaged">批量报损</DropdownItem>
                <DropdownItem name="transfer">批量调拨</DropdownItem>
                <DropdownItem name="confirm">调入确认</DropdownItem>
              </template>
            </DropdownMenu>
          </Dropdown>
        </div>
        <Pager
          name="goodsList"
          :total="total"
          :postData="postData"
          :history="false"
          @on-change="pageChange" />
      </footer>
    </div>
    <Modal v-model="showModal" title="库存记录" width="800">
      <div style="display: flex; align-items: center; padding: 10px 5px;">
        <img style="max-width: 100px; max-width: 100px" :src="modalInfo.commodity_img" alt="">
        <p style="font-size: 16px; padding: 0 15px">{{ modalInfo.commodity_name }}</p>
        <p style="font-size: 16px; padding: 0 15px">
          库存
          <span>{{ modalInfo.stock_balance }}</span> {{ modalInfo.unit }}
        </p>
        <p style="font-size: 16px; padding: 0 15px">
          单价
          <span>{{ modalInfo.commodity_price }}</span> 元
        </p>
      </div>
      <Table :data="modalTableData" :columns="modalColumns" disabledHover></Table>
      <div style="display: flex; justify-content: flex-end; padding-top: 20px">
        <Pager
          :history="false"
          :postData="modalPostData"
          :total="modalTotal"
          @on-change="handleModalChange"></Pager>
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import userSearch from 'components/user/userSearch';
  import { mapGetters, mapMutations, mapState } from 'vuex';
  import * as Types from 'types';
  import GoodsNav from './goodsNav';
  import Pager from 'components/pager';
  import Selection from 'mixins/selection';

  export default {
    name: 'GoodsList',
    components: { userSearch, Pager, GoodsNav },
    mixins: [Selection],
    data() {
      return {
        showModal: false,
        modalPostData: {
          page_no: 1,
          page_size: 10,
          commodity_id: ''
        },
        postData: {
          page_no: 1,
          page_size: 10,
          search: '',
          online: '',
          inventoryCode: '',
          cate_id: ''
        },
        searchUserId: '',
        showAbandon: false,
        showDetail: false,
        detail: {},
        total: 0,
        modalTotal: 0,
        modalTableData: [],
        modalInfo: {},
        modalColumns: [
          {
            title: '时间',
            key: 'create_time_date',
          },
          {
            title: '库存变更',
            key: 'stock_balance',
          },
          {
            title: ' ',
            key: 'type',
            render: (h, params) => {
              const item = params.row;
              return item.operation_mode == 0 ? (
                item.type == 0 ? (
                  <tag color="green">入库</tag>
                ) : (
                  <tag color="red">出库</tag>
                )
              ) : (
                       ''
                     );
            }
          },
          {
            title: '操作方式',
            key: 'operation'
          },
          {
            title: '操作账号',
            key: 'marketers_name'
          }
        ],
        columns: [
          {
            type: 'selection',
            width: 60
          },
          {
            key: 'commodity_img',
            title: ' ',
            className: 'avatar-wrap',
            render: (h, params) => {
              const item = params.row;
              return (
                <a>
                  <img class="avatar" src={item.commodity_img}/>
                </a>
              );
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '库存',
            key: 'stock'
          },
          {
            title: '单价',
            key: 'singlePrice'
          },
          {
            title: '购买数量',
            key: 'count',
            render: (h, params) => {
              const item = params.row;
              const btnStyle = {
                width: '32px',
                height: '32px',
                minWidth: 'auto',
                boxShadow: 'none'
              };
              const handleTotal = count => {
                item.discountPrice = item.totalPrice = (item.commodity_price * count).toFixed(2);
                this[Types.ADD_GOODS_ITEM](item);
              };
              const sale = this.adminInfo.commodity_inventory.commodity_settlement;
              const disabled = !sale || this.inventoryCode !== `${this.busId}-1`;
              return (
                <div style="display: flex; justify-content: center; align-items: center">
                  <i-button
                    style={btnStyle}
                    disabled={item.count <= 0 || item.unsale || disabled}
                    onClick={() => {
                      item.count--;
                      handleTotal(item.count);
                    }}>
                    -
                  </i-button>
                  <i-input
                    class="count"
                    value={item.count}
                    disabled={item.stock_balance == 0 || item.unsale || disabled}
                    on-on-blur={e => {
                      const value = e.target.value;
                      if (Number.isNaN(+value)) return this.$Message.error('请输入数字');
                      item.count = Math.max(value, 0)
                      if (item.count > item.stock_balance) {
                        this.$Message.error('库存不足');
                        item.count = item.stock_balance;
                      }
                      handleTotal(item.count);
                    }}
                    style="width: 42px; height: 32px; margin: 0 5px"
                  />
                  <i-button
                    style={btnStyle}
                    disabled={item.count >= item.stock_balance || item.unsale || disabled}
                    onClick={() => {
                      item.count++;
                      handleTotal(item.count);
                    }}>
                    +
                  </i-button>
                </div>
              );
            }
          },
          {
            title: '总计',
            key: 'totalPrice',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  ￥ <span style="color: red">{item.totalPrice}</span>
                </div>
              );
            }
          },
          {
            title: '会员端展示',
            key: 'if_member',
            render: (h, { row }) => {
              return (
                <i-switch
                  value={ row.if_member }
                  true-value={ 1 }
                  false-value={ 0 }
                  on-on-change={ (val) => { this.updateIsShowGoods(val, row.id) } }
                />
              );
            }
          },
          {
            title: '操作',
            render: (h, params) => {
              const item = params.row;
              const index = params.index;
              const placement = index < this.tableData.length - 4 || index < 3 ? 'bottom' : 'top';
              let drop = <dropdown-item name="obtained">下架</dropdown-item>;

              if (item.unsale) {
                drop = <dropdown-item name="shelf">上架</dropdown-item>;
              }

              const edit = this.adminInfo.commodity_inventory.add_commodity;
              const inVentory = this.adminInfo.commodity_inventory.in;

              return (
                <dropdown
                  placement={placement}
                  on-on-click={name => {
                    this.dropdownClick(name, item);
                  }}>
                  <i-button >
                    操作
                    <icon style="margin-left: 4px; margin-right: -4px" type="md-arrow-dropdown"/>
                  </i-button>
                  <dropdown-menu slot="list">
                    {drop}
                    {inVentory && <dropdown-item name="storage">入库</dropdown-item>}
                    {inVentory && <dropdown-item name="damaged">报损</dropdown-item>}
                    {inVentory && <dropdown-item name="transfer">调拨</dropdown-item>}
                    <dropdown-item name="turnover">流水</dropdown-item>
                    {edit && <dropdown-item name="edit">编辑</dropdown-item>}
                    {edit && <dropdown-item name="delete">删除</dropdown-item>}
                  </dropdown-menu>
                </dropdown>
              );
            }
          }
        ],
        hasEnterNum: '',
        authCode: ''
      };
    },
    created () {
      /**
       * 扫码枪逻辑梳理(识别到条形码)
       * 1.监控数字键盘和enter按下事件 以enter为结束标识
       * 2.调用查询商品是否存在,存在则走3,4,不存在则走5
       * 3.购物车为空的时候直接加入该商品
       * 4.购物车不为空时,查询购物车是否已经有该商品,有则需要在原来的基础上数量+1, 如果没有则直接加入该商品
       * 5.弹出提示
       */
      document.addEventListener('keydown', this.handleKeyDown)
    },
    computed: {
      ...mapGetters('goods', ['tableData', 'list']),
      ...mapState('goods', ['inventoryCode']),
      ...mapState(['busId', 'adminInfo'])
    },
    methods: {
      ...mapMutations('goods', [Types.ADD_GOODS_ITEM, Types.SET_GOODS_LIST]),
      handleModal(commodity_id) {
        this.modalPostData.commodity_id = commodity_id;
        this.showModal = true;
        this.getModalList();
      },
      modalRowClassName(item) {
        if (item.unsale) {
          return 'disabled-row';
        }
      },
      // 设置取消签到 tr 的已取消 title
      setDisabledTitle() {
        this.$nextTick(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled-row');
          // let disabledTrArr = Array.prototype.slice.call(disabledTr);
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已下架');
          });
        });
      },
      groupChange(id) {
        if (id === this.postData.cate_id) return;
        this.postData.cate_id = id;
        this.getList(true);
      },
      handleDelete(commodity_id) {
        this.$Modal.confirm({
          title: '删除商品',
          content: '点击确定删除该商品',
          onOk: () => {
            const url = '/Web/Commodity/delete_commodity';
            this.$service
              .post(url, { commodity_id })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getList();
                  this.$Message.success(res.data.errormsg);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        });
      },
      getModalList() {
        const url = '/Web/Commodity/commodity_log_list';
        this.$service
          .post(url, this.modalPostData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.modalInfo = data.info;
              this.modalTableData = data.list.map(item => {
                return {
                  ...item,
                  stock_balance: `库存由 ${item.beg_stock_balance} 变更为 ${item.end_stock_balance}`,
                  operation: item.operation_mode == 0 ? '手动操作' : item.operation_mode == 1 ? '售出' : '取消售出'
                };
              });
              this.modalTotal = data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleModalChange(postData) {
        this.modalPostData = postData;
        this.getModalList();
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      getList(isSearch) {
        if (isSearch) {
          this.postData.page_no = 1;
        }
        this.postData.inventoryCode = this.inventoryCode;
        const url = '/Web/Commodity/get_commodity_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              const list = data.list.map(item => {
                return { ...item, _checked: this.selectionId.includes(item.id), unsale: item.online === 0 };
              });
              this[Types.SET_GOODS_LIST](list);
              this.setDisabledTitle();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/Web/Commodity/export_excel';
        return this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              return res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      singleCheck({ hasInitCost, type, id, items }) {
        if (type !== 'storage' && items[0].stock_balance === 0) return this.$Message.error('该商品已无库存');
        if (hasInitCost) {
          this.$router.push({
            name: '商品入库',
            query: { type },
            params: { items, total: this.total }
          });
        } else {
          this.$Modal.confirm({
            title: '无期初成本',
            content: '该商品尚未填写期初成本',
            okText: '去填写',
            cancelText: '继续',
            onOk: () => {
              this.$router.push({ path: '/commodity/list/detail', query: { id, type: 'edit' } });
            },
            onCancel: () => {
              this.$router.push({
                name: '商品入库',
                query: { type },
                params: { items, total: this.total }
              });
            }
          });
        }
      },
      dropdownClick(name, item) {
        const { id, inoutList, opening_cost: openingCost } = item;
        const event = {
          edit: () => {
            this.$router.push({ path: '/commodity/list/detail', query: { id, type: 'edit' } });
          },
          storage: () => {
            this.$router.push({
              name: '商品入库',
              query: { type: 'storage' },
              params: { items: [item], total: this.total }
            });
          },
          damaged: () => {
            const hasInitCost = this.checkInitCost(inoutList, openingCost);
            this.singleCheck({ hasInitCost, type: 'damaged', id, items: [item] });
          },
          transfer: () => {
            const hasInitCost = this.checkInitCost(inoutList, openingCost);
            this.singleCheck({ hasInitCost, type: 'transfer', id, items: [item] });
          },
          turnover: () => {
            this.$router.push({
              name: '商品流水',
              params: { inventoryCode: this.inventoryCode, search: item.commodity_name }
            });
          },
          delete: () => {
            if (item.stock_balance > 0) return this.$Message.error('该商品有库存，请先将商品出库');
            this.handleDelete(id);
          },
          obtained: () => {
            this.handleShelf(id, 0);
          },
          shelf: () => {
            this.handleShelf(id, 1);
          }
        };
        event[name]();
      },
      checkInitCost(list, openingCost) {
        return !(list.length && +openingCost === 0 && list[0].num > 0);
      },
      checkCostList(selection) {
        for (let item of selection) {
          const hasInitCost = this.checkInitCost(item.inoutList, item.opening_cost);
          if (!hasInitCost) return { value: false, item };
        }
        return { value: true };
      },
      handleShelf(id, online = 0) {
        const url = '/web/commodity/lineStatus';
        const postData = {
          idStr: id,
          online
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            const index = this.tableData.findIndex(item => item.id === id);
            this.tableData[index].unsale = !this.tableData[index].unsale;
            this.setDisabledTitle();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      multiCheck({ hasInitCost, type }) {
        const item = this.selection.find(item => item.stock_balance === 0);
        if (item) return this.$Message.error(`商品【${item.commodity_name}】已无库存`);
        if (hasInitCost.value) {
          this.$router.push({
            name: '商品入库',
            query: { type },
            params: { items: this.selection, total: this.total }
          });
        } else {
          this.$Modal.confirm({
            title: '无期初成本',
            content: `商品【${hasInitCost.item.commodity_name}】未填写期初成本`,
            okText: '去填写',
            cancelText: '继续',
            onOk: () => {
              this.$router.push({ path: '/commodity/list/detail', query: { id: hasInitCost.item.id, type: 'edit' } });
            },
            onCancel: () => {
              this.$router.push({
                name: '商品入库',
                query: { type },
                params: { items: this.selection, total: this.total }
              });
            }
          });
        }
      },
      otherCase(name) {
        const event = {
          excel: () => {
            this.exportCsv();
          },
          import: () => {
          },
          show: () => {
            if (!this.selection.length) return this.$Message.error('请选择商品');
            this.updateIsShowGoods(1);
          },
          hide: () => {
            if (!this.selection.length) return this.$Message.error('请选择商品');
            this.updateIsShowGoods(0);
          },
          storage: () => {
            if (!this.selection.length) return this.$Message.error('请选择商品');
            this.$router.push({
              name: '商品入库',
              query: { type: 'storage' },
              params: { items: this.selection, total: this.total }
            });
          },
          damaged: () => {
            if (!this.selection.length) return this.$Message.error('请选择商品');
            const hasInitCost = this.checkCostList(this.selection);
            this.multiCheck({ hasInitCost, type: 'damaged' });
          },
          transfer: () => {
            if (!this.selection.length) return this.$Message.error('请选择商品');
            const hasInitCost = this.checkCostList(this.selection);
            this.multiCheck({ hasInitCost, type: 'transfer' });
          },
          confirm: () => {
            this.$router.push({ name: '调入确认列表' });
          }
        };
        event[name]();
      },
      async exportCsv() {
        const data = await this.getExportData();
        if (!data) return false;
        const columns = [
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '类别',
            key: 'cate_name'
          },
          {
            title: '单位',
            key: 'unit'
          },
          {
            title: '售价',
            key: 'commodity_price'
          },
          {
            title: '库存合计',
            key: 'total'
          },
        ];
        for (let bus of data.bus_list) {
          columns.push({
            title: bus.name,
            key: bus.id
          });
        }
        for (let item of data.list) {
          item.total = 0;
          for (let bus of item.list) {
            item[bus.id] = bus.num;
            item.total += bus.num;
          }
        }
        this.$refs.table.exportCsv({
          filename: '商品列表',
          columns,
          data: data.list
        });
      },
      // 更新会员端是否展示
      updateIsShowGoods(if_member, id) {
        const params = {
          if_member, // 1展示 0不展示
          idStr: id || this.selectionId.join() // 有id 单个，没有 批量
        };

        this.$service.post('/Web/Commodity/show_member', params).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }

        }).catch(e => {
          throw new Error(e);
        }).finally(this.getList)
      },
      handleKeyDown(e) {
        let event = e || window.event;
        let key = event.key;
        if (event.target.localName != 'input' && /^[0-9]*$/.test(key)) {
          this.hasEnterNum += key
        }
        if (key === 'Enter') {
          this.authCode = this.hasEnterNum
          this.hasEnterNum = ''
          this.handleGetCommodity(event.target.localName, this.authCode)
        }
      },
      handleGetCommodity(name, scan) {
        let code = JSON.parse(JSON.stringify(scan))
        let url= '/Web/Commodity/get_commodity'
        let params= {
          barcode_number: code,
          bus_id: this.$store.state.busId
        }
        if(name == 'input') {
          return false;
        }
        if(!scan) {
          this.$Message.error('未检测到条形码!');
          return false;
        }
        this.$service.post(url, params).then(res => {
          if (res.data.errorcode === 0) {
            if(Object.prototype.toString.call(res.data.data) == '[object Array]') {
              this.$Message.error('未知商品!');
            } else if(res.data.data.online == 0) {
              this.$Message.error('商品已下架!');
            } else if(res.data.data.stock_balance > 0) {
              this.handleTotal(res.data.data)
            } else {
              this.$Message.error('商品库存不足!');
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        })
      },
      handleTotal(item) {
        /**
         * 1.拿到购物车的数据list
         * 2.利用id对比购物车是否含有该商品
         * 3.无则直接+1
         * 4.有并且库存>已添加在购物车的数量则添加,否则不添加并且提示
         */
        let commodity = JSON.parse(JSON.stringify(item))
        let list = JSON.parse(JSON.stringify(this.list))

        // 购物车是否包含 (false不包含/true包含)
        let only = false

        // 是否可以添加  (false不可以/true可以)
        let bool = false

        // 设置默认数量
        commodity.count = 1

        for(let i = 0; i < list.length; i++) {
          if (list[i].id == commodity.id) {
            // 购物车包含该商品
            only = true
            if(commodity.stock_balance > list[i].count) {
              bool = true
              // 设置实际数量
              commodity.count = list[i].count + 1
            }
            //break跳出整个循环
            break;
          }
        }

        // 设置实际单商品价格*数量
        commodity.discountPrice = commodity.totalPrice = (commodity.commodity_price * commodity.count).toFixed(2);
        commodity.stock = `${commodity.stock_balance}（${commodity.unit}）`
        commodity.singlePrice = `￥ ${commodity.commodity_price}`,
        commodity.totalPrice = commodity.totalPrice || '0.00'

        if(only) {
          if(bool) {
            // 购物车包含并且数量允许添加
            this[Types.ADD_GOODS_ITEM](commodity);
          } else {
            // 购物车包含但是数量无法添加
            this.$Message.error('商品库存不足!');
          }
        } else {
          if(commodity.stock_balance > 0) {
            // 购物车不包含并且数量允许添加
            this[Types.ADD_GOODS_ITEM](commodity);
          } else {
            // 商品本身没有库存
            this.$Message.error('商品库存不足!');
          }
        }
      }
    },
    // 解绑事件
    beforeDestroy() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },
  };
</script>
