<template>
  <Select v-model="catId" placeholder="请选择商品分类">
    <Option v-for="item in catList" :key="item.id" :value="item.id" :label="item.name"></Option>
  </Select>
</template>

<script>

  export default {
    name: 'catSelect',
    props: {
      value: {}
    },
    data() {
      return {
        catList: [],
      };
    },
    created() {
      this.getList();
    },
    computed: {
      catId: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    methods: {
      getList() {
        const url = '/web/comCate/lists';
        this.$service.post(url, { inventoryCode: this.$store.state.goods.inventoryCode || `${this.$store.state.busId}-1` }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.catList = data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      }
    },
  };
</script>

<style scoped>

</style>
