<template>
  <Select v-model="inventoryCode" @on-change="onChange" :clearable="clearable" :disabled="disabled" filterable
          :placeholder="placeholder">
    <Option v-for="item in inventoryList" :key="item.inventory_code" :value="item.inventory_code"
            :label="item.name"></Option>
  </Select>
</template>

<script>
  import { mapState, mapActions } from 'vuex';

  export default {
    name: 'inventorySelect',
    props: {
      value: {},
      clearable: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      all: {
        type: Number,
        default: 0
      },
      placeholder: {
        type: String,
        default: '仓库'
      }
    },
    data() {
      return {};
    },
    computed: {
      ...mapState('goods', ['inventoryList']),
      inventoryCode: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    created() {
      this.getInventoryList({ all: this.all });
    },
    methods: {
      ...mapActions('goods', ['getInventoryList']),
      onChange(id) {
        this.$emit('on-change', id);
      }
    },
  };
</script>

<style scoped>

</style>
