<template>
  <div class="container">
    <header><h3>{{title}}</h3></header>
    <GoodsAdd></GoodsAdd>
  </div>
</template>

<script>
  import GoodsAdd from './goodsAdd';

  export default {
    name: 'goodsDetail',
    components: {
      GoodsAdd
    },
    data() {
      return {
        title: '商品添加',
      };
    },
    created() {
      const id = this.$route.query.id;
      if (id) {
        this.title = '商品编辑';
      } else {
        this.title = '商品添加';
      }
    },
  };
</script>
