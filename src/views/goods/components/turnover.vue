<template>
  <div class="container turnover">
    <header><h3>商品流水</h3></header>
    <div class="table-wrap">
      <header>
        <Input v-model="postData.search" @on-enter="doSearch" placeholder="商品名称"/>
        <InventorySelect v-model="postData.inventoryCode" :clearable="false"></InventorySelect>
        <Select placeholder="入出库类型" v-model="postData.type" clearable>
          <Option v-for="item in transferType" :key="item.key" :value="item.key" :label="item.value"></Option>
        </Select>
        <DatePicker type="daterange" v-model="dateRange" @on-change="dateChange"></DatePicker>
        <Button type="success" @click="doSearch">搜索</Button>
      </header>
      <Table ref="table" :data="tableData" :columns="columns"></Table>
      <footer>
        <Button v-if="$store.state.adminInfo.commodity_inventory.comLog_export" @click="exportCsv">导出Excel</Button>
        <Pager :total="total" @on-change="pageChange" :history="false" :post-data="postData"></Pager>
      </footer>
    </div>
    <StorageModal v-model="showModal" :inout_id="inout_id" :type="storageType"/>
  </div>
</template>

<script>
  import { formatDate } from 'utils';
  import Pager from 'components/pager';
  import Columns from './storageModalColumns';
  import StorageModal from './storageModal';
  import { TRANSFER_TYPE } from 'src/store/constants';
  import InventorySelect from './inventorySelect';

  export default {
    name: 'turnover',
    components: { Pager, StorageModal, InventorySelect },
    data() {
      return {
        total: 0,
        inout_id: '',
        transferType: Object.entries(TRANSFER_TYPE).map(([key, value]) => ({ key, value })),
        postData: {
          inventoryCode: '',
          search: '',
          type: '',
          beg_time: '',
          end_time: '',
          page_no: 1,
          page_size: 10
        },
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd'),],
        tableData: [],
        columns: [
          ...Columns.common,
          {
            title: '入出库类型',
            key: 'type_name'
          },
          {
            title: '单据编号',
            key: 'order_sn',
            width: 200,
            render: (h, param) => {
              const item = param.row;
              return <i-button type="text" onClick={() => this.handleDetail(item)}>{item.order_sn}</i-button>;
            }
          },
          {
            title: '时间',
            key: 'date'
          },
          {
            title: '数量',
            key: 'amount'
          },
          {
            title: '总成本',
            key: 'total_cost'
          },
          {
            title: '销售额',
            key: 'sale'
          },
        ],
        storageType: 1,
        showModal: false
      };
    },
    created() {
      const { search, inventoryCode } = this.$route.params;
      this.postData.beg_time = this.dateRange[0];
      this.postData.end_time = this.dateRange[1];
      this.postData.search = search;
      this.postData.inventoryCode = inventoryCode;
      this.getList();
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      pageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getList();
      },
      dateChange([s, e]) {
        this.postData.beg_time = s;
        this.postData.end_time = e;
      },
      handleDetail({ inout_id, type_name }) {
        this.inout_id = inout_id;
        this.storageType = type_name;
        this.showModal = true;
      },
      getList() {
        const url = '/web/inventory/comLog';
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      getExportData() {
        const url = '/web/inventory/comLog';
        return this.$service.post(url, { ...this.postData, page_no: 1, page_size: this.total }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            return data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      async exportCsv() {
        const data = await this.getExportData();
        this.$refs.table.exportCsv({
          filename: '商品流水',
          data,
          columns: this.columns
        });
      }
    },
  };
</script>

<style scoped>

</style>
