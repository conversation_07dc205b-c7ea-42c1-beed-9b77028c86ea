<template>
  <div class="table-wrap storage-list">
    <header>
      <Input placeholder="单据编号" v-model="postData.search" @on-enter="doSearch"/>
      <InventorySelect v-model="postData.inventoryCode" :clearable="false" @on-change="onInventoryChange"/>
      <Select placeholder="入出库类型" v-model="postData.type" clearable>
        <Option value="0" label="全部"></Option>
        <Option value="1" label="入库"></Option>
        <Option value="2" label="调入"></Option>
        <Option value="4" label="调出"></Option>
        <Option value="3" label="报损"></Option>
      </Select>
      <DatePicker type="daterange" v-model="dateRange" @on-change="dateChange"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns"></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export"/>
      <Pager :post-data="postData" :total="total" @on-change="pageChange"/>
    </footer>
    <StorageModal v-model="showModal" :type="storageType" :inout_id="inout_id" :inventoryCode="postData.inventoryCode"/>
  </div>
</template>

<script>
  import { formatDate } from 'utils';
  import Pager from 'components/pager';
  import StorageModal from './storageModal';
  import InventorySelect from './inventorySelect';
  import { mapMutations } from 'vuex';
  import { SET_INVENTORY_CODE } from 'src/store/mutationTypes';
  import Export from "src/components/Export";

  export default {
    name: 'storageList',
    components: { Pager, StorageModal, InventorySelect, Export },
    data() {
      return {
        total: 0,
        showModal: false,
        inout_id: '',
        storageType: '',
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        postData: {
          search: '',
          inventoryCode: '',
          type: '',
          beg_time: formatDate(new Date(), 'yyyy-MM-dd'),
          end_time: formatDate(new Date(), 'yyyy-MM-dd'),
          page_no: 1,
          page_size: 10
        },
        tableData: [],
        columns: [
          {
            title: '单据编号',
            key: 'orderSn'
          },
          {
            title: '入出库类型',
            key: 'type_name'
          },
          {
            title: '操作账号',
            key: 'op_name'
          },
          {
            title: '时间',
            key: 'date'
          },
          {
            title: '调出仓库',
            key: 'out_name'
          },
          {
            title: '调入仓库',
            key: 'in_name'
          },
          {
            title: '总成本',
            key: 'total_cost'
          },
          {
            title: '操作',
            render: (h, param) => {
              const item = param.row;
              return <i-button type="text" onClick={() => {
                this.clickDetail(item);
              }}>查看明细</i-button>;
            }
          },
        ],
      };
    },
    computed: {},
    created() {
      const inventoryCode = this.$store.state.goods.inventoryCode || `${this.$store.state.busId}-1`;
      this.onInventoryChange(inventoryCode);
    },
    methods: {
      ...mapMutations('goods', [SET_INVENTORY_CODE]),
      onInventoryChange(id) {
        this.postData.inventoryCode = id;
        this[SET_INVENTORY_CODE](id);
      },
      pageChange(postData) {
        this.postData = {...this.postData, ...postData, inventoryCode: this.$store.state.goods.inventoryCode };
        this.getList();
      },
      dateChange([s, e]) {
        this.postData.beg_time = s;
        this.postData.end_time = e;
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      clickDetail({ inout_id, type_name }) {
        this.inout_id = inout_id;
        this.storageType = type_name;
        this.showModal = true;
      },
      getList() {
        const url = '/web/inventory/inoutLog';
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.tableData = data.list;
            this.total = data.count;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      getExportList() {
        const url = '/web/inventory/inoutLog';
        return this.$service.post(url, { ...this.postData, page_no: 1, page_size: this.total, isExport: true }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            return data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      formatKey(item, key) {
        if (item.comList) {
          return item.comList.map(subItem => {
              return subItem[key] || ""
          });
        } else {
          return "";
        }
      },
      async exportCsv() {
        let data = await this.getExportList();
        data = data.map(item => {
          return {
            ...item,
            detail_commodity_name: this.formatKey(item, "commodity_name"),
            detail_cate_name: this.formatKey(item, "cate_name"),
            detail_unit: this.formatKey(item, "unit"),
            detail_cost: this.formatKey(item, "cost"),
            detail_amount: this.formatKey(item, "amount"),
            detail_total_cost: this.formatKey(item, "total_cost"),
            detail_status_text: this.formatKey(item, "status_text"),
            detail_in_amount: this.formatKey(item, "in_amount"),
            detail_return_amount: this.formatKey(item, "return_amount")
          }
        })
        const exportColumns =[{
            title: '商品名称',
            key: 'detail_commodity_name',
          },
          {
            title: '商品类别',
            key: 'detail_cate_name'
          },
          {
            title: '商品单位',
            key: 'detail_unit'
          },
          {
            title: '单位成本',
            key: 'detail_cost'
          },
          {
            title: '数量',
            key: 'detail_amount'
          },
          {
            title: '成本',
            key: 'detail_total_cost'
          },
          {
            title: '状态',
            key: 'detail_status_text'
          },
          {
            title: '调入数量',
            key: 'detail_in_amount'
          },
          {
            title: '退货数量',
            key: 'detail_return_amount'
          }]
        let columns = this.columns.concat(exportColumns)
        columns.splice(7,1)
        this.$refs.export.export({
          columns: columns,
          data,
          filename: "入出库记录"
        });
      }
    },
  };
</script>

<style scoped>
  .storage-list {
    border-top: 0;
  }
</style>
