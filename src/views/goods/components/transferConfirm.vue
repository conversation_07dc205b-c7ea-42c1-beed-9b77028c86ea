<template>
  <div class="container transfer-confirm">
    <header><h3>商品调入确认</h3></header>
    <Form ref="form" class="form" :label-width="140">
      <FormItem label="调拨单号">
        {{postData.order_sn}}
      </FormItem>
      <FormItem label="调拨日期">
        {{postData.date}}
      </FormItem>
      <FormItem label="调出场馆">
        {{postData.from_bus_name}}
      </FormItem>
      <FormItem label="调入场馆">
        {{postData.to_bus_name}}
      </FormItem>
      <FormItem v-if="postData.status === 0">
        <Card style="width: 60vw; min-width: 800px" dis-hover>
          <p slot="title">商品列表</p>
          <Table :data="tableData" :columns="columns"></Table>
          <div class="total">
            <p>总计</p>
            <p>调拨量: {{totalAmount}}</p>
            <p>总成本: {{totalCost}}</p>
          </div>
        </Card>
      </FormItem>
      <FormItem v-else>
        <div style="display: flex; width: 70vw; justify-content: space-between">
          <Card style="width: 35vw" dis-hover>
            <p slot="title">调拨商品信息</p>
            <Table :data="tableData" :columns="incomeColumns"></Table>
          </Card>
          <div style="display: flex; flex-direction: column; justify-content: center">
            <div>----></div>
            <div>----></div>
            <div>----></div>
          </div>
          <Card style="width: 30vw" dis-hover>
            <p slot="title">调入商品信息</p>
            <Table :data="inTableData" :columns="confirmColumns"></Table>
          </Card>
        </div>
      </FormItem>
      <FormItem v-if="postData.status === 0">
        <div class="form-bottom-buttons">
          <Button type="success" @click="transferAll">全部入库</Button>
          <Button @click="transferSome">部分入库</Button>
          <Button @click="transferNone" type="error">全部退回</Button>
        </div>
      </FormItem>
    </Form>
    <Modal v-model="showModal" :title="modalTitle" width="60vw" :mask-closable="false">
      <Table v-if="transferType !== 'none'" :data="tableData" :columns="modalColumns"></Table>
      <Form class="modal-form" style="margin-top: 20px;" v-if="transferType !== 'all' && hasRemark">
        <FormItem label="退货原因" required>
          <Input v-model="remark" type="textarea" style="width: 80%" placeholder="请输入退货原因..."/>
        </FormItem>
        <FormItem v-if="transferType === 'some'">
          <p style="color: red; text-align: center; font-size: 14px">未入库的商品默认为退回</p>
        </FormItem>
      </Form>
      <div class="modal-buttons" slot="footer">
        <Button type="success" @click="handleConfirm" :disabled="disabledSubmit">确认</Button>
        <Button @click="showModal = false">取消</Button>
      </div>
    </Modal>
    <GoodsPicker v-if="postData.status === 0" v-model="showPickerModal" :inventory-code="$route.query.code" radio @on-change="onPickerChange"></GoodsPicker>
  </div>
</template>

<script>
  import storageColumns from './storageModalColumns';
  import GoodsPicker from './goodsPicker';

  export default {
    name: 'transferConfirm',
    components: { GoodsPicker },
    data() {
      return {
        hasRemark: false,
        disabledSubmit: false,
        pickerId: '',
        remark: '',
        showPickerModal: false,
        postData: {},
        showModal: false,
        transferType: '',
        incomeColumns: [
          ...storageColumns.common,
          {
            title: '数量',
            key: 'amount',
          },
        ],
        confirmColumns: [
          {
            title: '商品名称',
            key: 'commodity_name',
            width: 200,
          },
          {
            title: '商品类别',
            key: 'cate_name'
          },
          {
            title: '商品单位',
            key: 'unit'
          },
          {
            title: '数量',
            key: 'amount',
          },
        ],
        modalTableData: [],
        tableData: [],
        inTableData: [],
        columns: [
          ...storageColumns.common,
          {
            title: '调拨量',
            key: 'amount',
          },
          {
            title: '总成本',
            key: 'total_cost'
          },],
        modalColumns: [],
        initModalColumns: [
          ...storageColumns.common,
          {
            title: '调拨数量',
            key: 'amount'
          },
          {
            title: '对应本场馆商品',
            render: (h, param) => {
              const item = param.row;
              return <i-button onClick={() => {
                this.pickerId = item.iidId;
                this.showPickerModal = true;
              }}>
                <span>{ item.t_commodity_name }</span>
              </i-button>
            }
          },
        ]
      };
    },
    computed: {
      totalAmount() {
        return this.tableData.reduce((total, item) => total + Number(item.amount), 0)
      },
      totalCost() {
        return this.tableData.reduce((total, item) => total + Number(item.total_cost), 0).toFixed(2)
      },
      modalTitle() {
        const title = {
          all: '全部入库',
          some: '部分入库',
          none: '全部退回'
        };
        return title[this.transferType];
      }
    },
    created() {
      this.getInfo();
    },
    methods: {
      onPickerChange([data]) {
        const item = this.tableData.find(item => item.iidId === this.pickerId);
        item.t_commodity_name = data.commodity_name;
        item.t_comId = data.id;
      },
      getInfo() {
        const url = '/web/inventory/allotInDetail';
        const postData = {
          inout_id: this.$route.query.id,
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData = data;
            this.tableData = data.comList;
            this.inTableData = data.tComList || [];
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      checkTableData() {
        for (let item of this.tableData) {
          if (!item.t_comId && item.in_amount > 0) {
            this.$Message.error('请选择对应本场馆商品');
            return false;
          }
        }
        return true
      },
      handleConfirm() {
        if (!this.checkTableData()) return false;
        const url = '/web/inventory/allotIn';
        const postData = {
          inoutId: this.$route.query.id,
          iidList: this.tableData,
          remark: this.remark
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.$router.push({ path: '/commodity/list', query: { tab: 1 } });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      transferAll() {
        this.tableData.forEach(item => item.in_amount = item.amount);
        this.modalColumns = this.initModalColumns;
        this.transferType = 'all';
        this.showModal = true;
      },
      transferSome() {
        this.tableData.forEach(item => item.in_amount = item.amount);
        this.modalColumns = [...this.initModalColumns, {
          title: '调入数量', render: (h, param) => {
            const item = param.row;
            const index = param.index;
            return <i-input on-on-focus={() => this.disabledSubmit = true} on-on-blur={e => {
              let value = +e.target.value;
              this.hasRemark = false;
              if (!Number.isInteger(value)) {
                this.$Message.error('请输入整数');
                value = Math.round(value)
              }
              if (value > item.amount) {
                this.$Message.error('调入数量不能大于调拨数量');
                value = item.amount;
              } else if (value < item.amount) {
                this.hasRemark = true;
              }
              item.in_amount = value;
              this.$set(this.tableData, index, item);
              this.disabledSubmit = false;
            }} value={+item.in_amount}/>;
          }
        }];
        this.transferType = 'some';
        this.showModal = true;
      },
      transferNone() {
        this.tableData.forEach(item => item.in_amount = 0);
        this.transferType = 'none';
        this.hasRemark = true;
        this.showModal = true;
      }
    },
  };
</script>

<style lang="less">
  .transfer-confirm {
    .ivu-card-body {
      padding: 0;
    }
  }
</style>

<style lang="less" scoped>
  .total {
    display: flex;
    justify-content: flex-end;
    height: 40px;
    align-items: center;
    padding-right: 30px;
    p {
      margin-right: 25px;
    }
  }
</style>
