<template>
  <div class="goods-picker">
    <Modal title="选择商品" v-model="showPickerModal" width="60vw" :mask-closable="false">
      <div class="table-wrap">
        <header>
          <Input placeholder="商品名称" v-model="modalPostData.search" @on-enter="getGoodsList"/>
          <catSelect v-model="modalPostData.cate_id"></catSelect>
          <Button type="success" @click="getGoodsList">查询</Button>
        </header>
        <Table @on-selection-change="onSelectionChange" @on-row-click="handleSelectionClick"
               :row-class-name="modalRowClassName" :data="modalGoodsData"
               :columns="modalGoodsColumns" class="modal-table" style="max-height: 60vh;"/>
      </div>
      <div class="modal-buttons" slot="footer">
        <template v-if="modalGoodsData.length">
          <Button type="success" @click="confirmAdd">确定</Button>
          <Button @click="showPickerModal = false">取消</Button>
        </template>
        <Button type="primary" v-else @click="showAddGoods = true">添加商品</Button>
      </div>
    </Modal>
    <Modal v-model="showAddGoods" title="添加商品" width="50vw">
      <div class="container" style="padding-right: 100px; padding-bottom: 0">
        <GoodsAdd v-if="showAddGoods" :on-add-success="onAddSuccess"></GoodsAdd>
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import GoodsAdd from './goodsAdd';
  import catSelect from './catSelect';

  export default {
    name: 'goodsPicker',
    props: {
      value: {
        type: Boolean,
        default: false
      },
      ids: {
        type: Array,
        default: () => ([])
      },
      radio: {
        type: Boolean,
        default: false
      },
      inventoryCode: {},
      disabledSame: {
        type: Boolean,
        default: false
      }
    },
    components: { GoodsAdd, catSelect },
    data() {
      return {
        showAddGoods: false,
        selection: [],
        modalGoodsData: [],
        modalPostData: {
          search: '',
          cate_id: ''
        },
        modalGoodsColumns: [
          {
            type: 'selection',
            width: 60
          },
          {
            title: '商品图片',
            render: (h, param) => {
              const item = param.row;
              return <img style="width: 48px; height: 48px" src={item.commodity_img} alt=""/>;
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name',
          },
          {
            title: '商品类别',
            key: 'cate_name'
          },
          {
            title: '商品单位',
            key: 'unit'
          },
          {
            title: '商品售价',
            key: 'commodity_price'
          }
        ],
      };
    },
    created() {
      this.getGoodsList();
    },
    computed: {
      showPickerModal: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      busId() {
        return this.$store.state.busId;
      }
    },
    watch: {
      value(val) {
        if (val) {
          this.getGoodsList();
        }
      }
    },
    methods: {
      modalRowClassName(item) {
        if (item._disabled) {
          return 'disabled-row';
        }
      },
      getGoodsList() {
        const url = '/Web/Commodity/get_commodity_list';
        const { search, cate_id } = this.modalPostData;
        return this.$service
          .post(url, {
            search,
            cate_id,
            page_no: 1,
            page_size: '',
            inventoryCode: this.inventoryCode || `${this.busId}-1`
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.modalGoodsData = data.list.map(item => {
                return {
                  ...item,
                  stock: `${item.stock_balance}${item.unit}`,
                  singlePrice: `${item.commodity_price}元`,
                  totalCount: `${item.total_count}${item.unit}`,
                  _disabled: this.ids.includes(item.id) && !this.radio && this.disabledSame
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onAddSuccess() {
        this.getGoodsList();
        this.showAddGoods = false;
      },
      handleSelectionClick(item, index) {
        if (item._disabled) return false;
        if (this.radio) {
          this.onSelectionChange([item]);
          this.modalGoodsData.forEach(item => item._checked = false);
          item._checked = true;
          this.$set(this.modalGoodsData, index, item);
        } else {
          item._checked = !item._checked;
          this.$set(this.modalGoodsData, index, item);
          if (item._checked) {
            this.onSelectionChange([...this.selection, item]);
          } else {
            const selection = this.selection;
            const idx = selection.findIndex(item => item.id === item.id);
            selection.splice(idx, 1);
            this.onSelectionChange(selection);
          }
        }
      },
      checkInitCost(list, openingCost) {
        return !(list.length && +openingCost === 0 && list[0].num > 0);
      },
      checkCostList(selection) {
        for (let item of selection) {
          const hasInitCost = this.checkInitCost(item.inoutList, item.opening_cost);
          if (!hasInitCost) return { value: false, item };
        }
        return { value: true };
      },
      onSelectionChange(selection) {
        if (this.radio && selection.length > 1) {
          selection = [selection[0]];
          this.$Message.error('请选择一种商品');
        }
        const valid = this.checkCostList(selection);
        if (!valid.value) {
          this.$Modal.confirm({
            title: '无期初成本',
            content: `商品【${valid.item.commodity_name}】未填写期初成本`,
            okText: '去填写',
            onOk: () => {
              this.$router.replace({ path: '/commodity/list/detail', query: { id: valid.item.id, type: 'edit' } });
            }
          });
        }
        this.selection = selection;
      },
      confirmAdd() {
        if (!this.selection.length) return this.$Message.error('请选择要添加的商品');
        const selection = this.selection.map(item => {
          return { ...item, amount: 0, total_cost: 0 };
        });
        this.$emit('on-change', selection);
        this.selection = [];
        this.modalGoodsData.forEach(item => item._checked = false);
        this.showPickerModal = false;
      },
    },
  };
</script>

<style lang="less">
  .modal-table {
    .ivu-table {
      max-height: 60vh;
      overflow-y: scroll;
      overflow-x: hidden;
    }
  }
</style>

