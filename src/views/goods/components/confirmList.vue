<template>
  <div class="table-wrap">
    <header>
      <InventorySelect placeholder="调拨仓库" v-model="postData.fromInventoryCode" :all="1"></InventorySelect>
      <!--<InventorySelect placeholder="调入仓库" v-model="postData.inventoryCode" :clearable="false"></InventorySelect>-->
      <DatePicker type="daterange" v-model="dateRange" @on-change="dateChange"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns"></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager :total="total" :post-data="postData" @on-change="pageChange" :history="false"></Pager>
    </footer>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import InventorySelect from './inventorySelect';
  import { formatDate } from 'utils';

  export default {
    name: 'confirmList',
    components: { Pager, InventorySelect },
    data() {
      return {
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        columns: [
          {
            title: '调拨仓库',
            key: 'name'
          },
          {
            title: '调拨时间',
            key: 'date'
          },
          {
            title: '调拨总成本',
            key: 'total_cost'
          },
          {
            title: '单据编号',
            key: 'order_sn'
          },
          {
            title: '状态',
            key: 'status_text',
            render: (h, param) => {
              const item = param.row;
              const color = item.status ? 'green' : 'red';
              return <tag color={color}>{item.status_text}</tag>
            }
          },
          {
            title: '操作',
            render: (h, param) => {
              const item = param.row;
              if (item.status === 0) {
                return <router-link
                  to={{ name: '商品调入确认', query: { id: item.id, code: item.code } }}>前往处理</router-link>;
              } else {
                return <router-link
                  to={{ name: '商品调入确认', query: { id: item.id, code: item.code } }}>查看</router-link>;
              }
            }
          },
        ],
        tableData: [],
        total: 0,
        postData: {
          inventoryCode: this.$store.state.goods.inventoryCode || `${this.$store.state.busId}-1`,
          fromInventoryCode: '',
          page_no: 1,
          page_size: 10,
          beg_time: formatDate(new Date(), 'yyyy-MM-dd'),
          end_time: formatDate(new Date(), 'yyyy-MM-dd')
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      dateChange(postData) {
        const [s, e] = postData;
        this.postData.beg_time = s;
        this.postData.end_time = e;
      },
      getList() {
        const url = '/web/inventory/allotList';
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list.map(item => {
              return { ...item, total_cost: Number(item.total_cost).toFixed(2) };
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      getExportList() {
        const url = '/web/inventory/allotList';
        return this.$service.post(url, { ...this.postData, page_no: 1, page_size: this.total }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            return data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      pageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getList();
      },
      async exportCsv() {
        const data = await this.getExportList();
        const columns = this.columns.filter(item => item.key);
        this.$refs.table.exportCsv({
          filename: '调入列表',
          data,
          columns
        });
      }
    },
  };
</script>

<style scoped>

</style>
