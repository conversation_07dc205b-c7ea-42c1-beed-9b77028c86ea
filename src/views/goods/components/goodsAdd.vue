<template>
  <Form ref="form" :model="postData" class="form" :label-width="180">
    <!--<FormItem label="仓库名称" prop="inventoryCode">-->
      <!--&lt;!&ndash;<InventorySelect v-model="postData.inventory_code" :clearable="false" />&ndash;&gt;-->
      <!--{{$store.getters['goods/inventoryName']}}-->
    <!--</FormItem>-->
    <FormItem label="商品名称" prop="commodity_name" :rules="{required: true, message: '请输入商品名称'}">
      <Input v-model="postData.commodity_name"></Input>
    </FormItem>
    <FormItem label="商品单位">
      <RadioGroup v-model="postData.unit">
        <Radio v-for="(item, index) in units" :key="index" :value="item" :label="item"/>
      </RadioGroup>
    </FormItem>
    <FormItem>
      <div class="image-description" slot="label">
        <p class="label">商品图片(可选)</p>
        <p class="tip">图片最佳尺寸: 200X200</p>
        <p class="tip">推荐图片大小: &lt;100kb</p>
        <p class="tip">格式限制: jpg、png</p>
      </div>
      <div class="goods-image"><img :src="postData.commodity_img"></div>
      <Cropper :output-width="200" :output-height="200" v-model="postData.commodity_img" multiple></Cropper>
    </FormItem>
    <FormItem label="商品类别" prop="cate_id" :rules="[{ required: true, message: '请选择商品类别' }]">
      <catSelect v-model="postData.cate_id"></catSelect>
    </FormItem>
    <FormItem label="条形码编号" prop="barcode_number">
      <Input v-model="postData.barcode_number"></Input>
    </FormItem>
    <FormItem label="单价" prop="commodity_price"
              :rules="[{required: true, message: '请输入商品价格'}, { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '保留两位小数' }]">
      <Input v-model="postData.commodity_price"/>
    </FormItem>
    <FormItem>
      <span slot="label">
        <span>期初库存</span>
        <HelpCircle value="期初库存一经保存无法直接修改数据，请通过入库、报损等操作更改库存；商品报损、调出时将优先处理期初库存"></HelpCircle>
      </span>
      <InputNumber :disabled="disabledInventory" v-model="postData.inventory" :min="0" :step="1" :precision="0"/>
    </FormItem>
    <FormItem prop="cost"
              :rules="[{required: true, message: '请输入期初单位成本'}, { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '保留两位小数' }]">
      <span slot="label">
        <span>期初单位成本</span>
        <HelpCircle value="期初单位成本指本商品入出库操作前盘点时的平均单价,请谨慎编辑，一经保存无法修改"></HelpCircle>
      </span>
      <Input :disabled="disabledCost" v-model="postData.cost"/>
    </FormItem>
    <FormItem>
      <div class="form-bottom-buttons">
        <Button type="success"
                v-if="$route.query.type === 'edit'"
                @click="handleUpdate">保存
        </Button>
        <Button type="success"
                v-else
                @click="handleAdd">添加
        </Button>
        <Button 
                @click="$router.back()">取消
        </Button>
      </div>
    </FormItem>
  </Form>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import Cropper from 'components/form/cropper';
  import HelpCircle from 'components/form/helpCircle';
  import catSelect from './catSelect';
  import InventorySelect from './inventorySelect'

  export default {
    name: 'goodsDetail',
    components: {
      Cropper,
      HelpCircle,
      catSelect,
      InventorySelect
    },
    props: {
      onAddSuccess: Function
    },
    data() {
      return {
        disabledCost: false,
        disabledInventory: false,
        units: ['瓶', '件', '个', '听', '盒', '箱', '条', '份', '杯'],
        postData: {
          unit: '瓶',
          inventory_code: this.$store.state.goods.inventoryCode,
          inventory: 0,
          commodity_img: '',
          commodity_price: '',
          commodity_name: '',
          cate_id: '',
          cost: '',
          barcode_number: '',
        }
      };
    },
    created() {
      if (!this.catList.length) this.getCatList({ inventoryCode: this.inventoryCode });
      const id = this.$route.query.id;
      const type = this.$route.query.type;
      // this.postData.inventoryCode = this.$route.query.inventoryCode;
      if (id && type === 'edit') {
        this.getDetail(id);
      }
    },
    computed: {
      ...mapState('goods', ['catList', 'inventoryCode'])
    },
    methods: {
      ...mapActions('goods', ['getCatList']),
      handlePrice(val) {
        this.postData.commodity_price = +this.postData.commodity_price.toFixed(2);
      },
      goBack() {
        if (this.onAddSuccess) {
          this.onAddSuccess();
        } else {
          setTimeout(() => {
            this.$router.back();
          }, 1000);
        }
      },
      checkForm() {
        if (this.postData.cate_id === '0' || this.postData.cate_id === '') {
          this.$Message.error('请选择商品类别');
          return false;
        }
        return true;
      },
      async handleAdd() {
        let valid = this.checkForm();
        if (!valid) return false;
        valid = await this.$refs.form.validate();
        if (!valid) return false;
        const url = '/Web/Commodity/add_commodity';
        this.postData.inventoryCode = this.$store.state.goods.inventoryCode;
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.goBack();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async handleUpdate() {
        let valid = this.checkForm();
        if (!valid) return false;
        valid = await this.$refs.form.validate();
        if (!valid) return false;
        const url = '/Web/Commodity/update_commodity';
        this.$service
          .post(url, {
            ...this.postData,
            ...{
              commodity_id: this.postData.id,
              end_stock_balance: this.postData.stock_balance,
              beg_stock_balance: this.beg_stock_balance
            }
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.goBack();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getDetail(commodity_id) {
        const url = '/Web/Commodity/get_commodity';
        this.$service
          .post(url, { commodity_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.beg_stock_balance = data.stock_balance;
              if (+data.inventory > 0) this.disabledInventory = true;
              if (+data.cost > 0) this.disabledCost = true;
              this.postData = {
                ...data,
                ...{
                  commodity_price: Number(data.commodity_price),
                  stock_balance: Number(data.stock_balance),
                  inventory: +data.inventory,
                  cost: +data.cost
                }
              };
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less" scoped>
  .goods-image {
    height: 100px;
    width: 100px;
    margin-bottom: 10px;
    border: 2px solid #ccc;

    img {
      max-height: 100%;
      min-height: 100%;
    }
  }
</style>
