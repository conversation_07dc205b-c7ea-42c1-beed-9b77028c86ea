<template>
  <div class="container goods-change">
    <header><h3>商品{{breadText}}</h3></header>
    <Form ref="form" :model="postData" class="form" :label-width="140">
      <FormItem :label="breadText + '单号'">
        {{postData.orderSn}}
      </FormItem>
      <FormItem :label="breadText + '日期'" prop="date">
        <DatePicker style="width: 240px" v-model="date" @on-change="dateChange" :clearable="false"
                    :editable="false" :options="datePickerOptions"></DatePicker>
      </FormItem>
      <FormItem :label="breadText + '仓库'" v-if="$route.query.type !== 'transfer'" prop="inventoryCode"
                :rules="{ required: true, message: '请选择仓库' }">
        <!--<InventorySelect style="width: 240px" v-model="postData.inventoryCode" disabled/>-->
        {{inventoryName}}
      </FormItem>
      <template v-else>
        <FormItem label="调出仓库" prop="fromInventoryCode" :rules="{ required: true, message: '请选择仓库' }">
          <!--<InventorySelect style="width: 240px" v-model="postData.fromInventoryCode" disabled/>-->
          {{inventoryName}}
        </FormItem>
        <FormItem label="调入仓库" prop="toInventoryCode" :rules="{ required: true, message: '请选择仓库' }">
          <InventorySelect style="width: 240px" v-model="postData.toInventoryCode" :all="1"></InventorySelect>
        </FormItem>
      </template>
      <FormItem>
        <Card style="width: 60vw; min-width: 800px" dis-hover>
          <p slot="title">商品列表</p>
          <Button @click="handlePicker" type="text" icon="plus-circled" slot="extra">添加商品</Button>
          <Table :data="tableData" :columns="columns"></Table>
          <Table :data="tableDataTotal" :columns="totalColumns" :show-header="false"></Table>
        </Card>
      </FormItem>
      <FormItem>
        <div class="form-bottom-buttons">
          <Button type="success"
                  v-if="$route.query.id"
                  @click="handleUpdate">保存
          </Button>
          <Button type="success"
                  v-else
                  :disabled="disabledSubmit"
                  @click="handleAdd">{{breadText}}
          </Button>
          <Button 
                  @click="$router.back()">取消
          </Button>
        </div>
      </FormItem>
    </Form>
    <GoodsPicker v-model="showPickerModal" :ids="ids" :inventory-code="inventoryCode"
                 :disabled-same="type !== 'storage'" @on-change="onPickerChange"></GoodsPicker>
  </div>
</template>

<script>
  import GoodsPicker from './goodsPicker';
  import InventorySelect from './inventorySelect';
  import { formatDate } from 'utils';

  const title = {
    storage: '入库',
    transfer: '调拨',
    damaged: '报损'
  };

  export default {
    name: 'goodsChange',
    components: {
      GoodsPicker,
      InventorySelect
    },
    data() {
      return {
        datePickerOptions: {
          disabledDate(date) {
            return date - Date.now() > 0;
          }
        },
        disabledSubmit: false,
        ids: [],
        type: '',
        date: new Date(),
        postData: {
          date: formatDate(new Date(), 'yyyy-MM-dd'),
          inventoryCode: this.$store.state.goods.inventoryCode,
          toInventoryCode: '',
          fromInventoryCode: this.$store.state.goods.inventoryCode,
          orderSn: '',
          comList: []
        },
        tableData: [],
        columns: [],
        initColumns: [
          {
            title: '操作',
            render: (h, param) => {
              const index = param.index;
              const style = {
                cursor: 'pointer'
              };
              return <div>
                <span onClick={() => this.handleDelete(index)}><icon title="删除" style={style}
                                                                     type="ios-trash" color="#d8321f"
                                                                     size="20"/></span>
              </div>;
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '商品类别',
            key: 'cate_name'
          },
          {
            title: '商品单位',
            key: 'unit'
          },
          {
            title: '库存量',
            key: 'stock_balance'
          },
          {
            title: '单位成本',
            key: 'cost',
            render: (h, param) => {
              const item = param.row;
              return <InputNumber 
                precision={4}
                active-change={false}
                min={0}
                on-on-focus={() => this.disabledSubmit = true}
                on-on-change={() => {
                  this.disabledSubmit = false;
                  item.total_cost = this.calTotalCost(item.amount, item);
                  this.$set(this.tableData, param.index, item);
                }}
                v-model={item.cost}
              />;
            }
          },
          {
            title: `入库量`,
            key: 'amount',
            render: (h, param) => {
              const item = param.row;
              return <InputNumber
                precision={0}
                min={0}
                on-on-focus={() => this.disabledSubmit = true}
                on-on-change={value => {
                  this.disabledSubmit = false;
                  if (value > +item.stock_balance && this.type !== 'storage') {
                    value = +item.stock_balance;
                    this.$Message.error('数量不能大于库存');
                  }
                  item.amount = value;
                  item.total_cost = this.calTotalCost(item.amount, item);
                  this.$set(this.tableData, param.index, item);
                }}
                v-model={item.amount}
              />;
            }
          },
          {
            title: '总成本',
            key: 'total_cost',
            render: (h, param) => {
              const item = param.row;
              return <div>{item.total_cost}</div>;
            }
          },],
        showPickerModal: false,
        hasEnterNum: '',
        authCode: ''
      };
    },
    computed: {
      inventoryCode() {
        if (this.type === 'transfer') {
          return this.postData.fromInventoryCode;
        } else {
          return this.postData.inventoryCode;
        }
      },
      inventoryName() {
        return this.$store.getters['goods/inventoryName'];
      },
      breadText() {
        return this.$route.meta.breadText;
      },
      totalColumns() {
        const [first, name, type, unit, storage, cost, amount, totalAmount] = this.initColumns;
        let change = [{ key: 'cost' }, { key: 'amount' }];
        if (this.$route.query.type !== 'storage') {
          change = [{ key: 'amount' }];
        }
        return [{ key: 'title' }, name, type, unit, storage, ...change, totalAmount];
      },
      tableDataTotal() {
        const amount = this.tableData.reduce((total, item) => total + Number(item.amount), 0);
        const total_cost = Number(this.tableData.reduce((total, item) => total + Number(item.total_cost), 0).toFixed(4));
        return [{
          title: '总计',
          amount,
          total_cost
        }];
      },
      title() {
        return title[this.$route.query.type];
      }
    },
    beforeRouteEnter(to, from, next) {
      to.meta.breadText = title[to.query.type];
      next();
    },
    created() {
      const [first, name, amountType, unit, storage, amount, transfer, totalAmount] = this.initColumns;
      const type = this.$route.query.type;
      this.type = type;
      if (type === 'storage') {
        this.columns = this.initColumns;
      } else if (type === 'damaged') {
        this.columns = [first, name, amountType, unit, storage, { ...transfer, title: '报损量' }, totalAmount];
      } else if (type === 'transfer') {
        this.columns = [first, name, amountType, unit, storage, { ...transfer, title: '调拨量' }, totalAmount];
      }
      this.tableData = this.$route.params.items.map(item => {
        return { ...item, amount: 0, total_cost: 0, cost: 0 };
      });
      this.getOrderSn();
      /**
       * 监听键盘按下事件
       */
      document.addEventListener('keydown', this.handleKeyDown)
    },
    // 解绑事件
    beforeDestroy() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },
    methods: {
      dateChange(e) {
        this.postData.date = e;
      },
      calTotalCost(value, item) {
        let num = value;
        let total = 0;
        if (this.type === 'storage') {
          total = num * item.cost;
        } else {
          const { inoutList } = item;
          for (let item of inoutList) {
            num -= item.num;
            if (num <= 0) {
              total += (item.num + num) * item.cost;
              break;
            } else {
              total += item.num * item.cost;
            }
          }
        }
        return Number(total.toFixed(4));
      },
      onPickerChange(data) {
        this.tableData = [...this.tableData, ...[{ ...data[0], cost: 0 }]];
      },
      getOrderSn() {
        const url = '/web/inventory/orderSn';
        const type = this.type === 'storage' ? 1 : this.type === 'transfer' ? 2 : 3;
        this.$service.post(url, { type }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData.orderSn = data.orderSn;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleDelete(index) {
        this.tableData.splice(index, 1);
      },
      handlePicker() {
        this.ids = this.tableData.map(item => item.id);
        this.showPickerModal = true;
      },
      handlePrice(val) {
        this.postData.commodity_price = +this.postData.commodity_price.toFixed(2);
      },
      checkTableData() {
        for (let item of this.tableData) {
          if (+item.amount === 0) {
            this.$Message.error('数量不能为0');
            return false
          }
        }
        return true
      },
      async handleAdd() {
        const { fromInventoryCode, toInventoryCode } = this.postData;
        if (toInventoryCode && toInventoryCode === fromInventoryCode) return this.$Message.error('调入调出仓库不能相同');

        let valid = this.checkTableData();
        if (!valid) return false;
        valid = await this.$refs.form.validate();
        if (!valid) return this.$Message.error('表单错误');
        this.postData.comList = this.tableData;
        let url = '';
        if (this.type === 'storage') {
          url = '/web/inventory/in';
        } else if (this.type === 'damaged') {
          url = '/web/inventory/checkOut';
        } else {
          url = '/web/inventory/allotOut';
        }
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.$router.push({ path: '/commodity/list', query: { tab: 1 } });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleKeyDown(e) {
        if(this.pay_type == 20) {
          return false;
        }
        let event = e || window.event;
        let key = event.key;
        if (event.target.localName != 'input' && /^[0-9]*$/.test(key)) {
          this.hasEnterNum += key
        }
        if (key === 'Enter') {
          this.authCode = this.hasEnterNum
          this.hasEnterNum = ''
          this.handleGetCommodity(event.target.localName, this.authCode)
        }
      },
      handleGetCommodity(name, scan) {
        let code = JSON.parse(JSON.stringify(scan))
        let url= '/Web/Commodity/get_commodity'
        let params= {
          barcode_number: code,
          type : 1,
          bus_id: this.$store.state.busId
        }
        if(name == 'input') {
          return false;
        }
        if(!scan) {
          this.$Message.error('未检测到条形码!');
          return false;
        }
        this.$service.post(url, params).then(res => {
          if (res.data.errorcode === 0) {
            if(Object.prototype.toString.call(res.data.data) == '[object Array]') {
              this.$Message.error('未知商品!');
            } else {
              this.handleTotal(res.data.data)
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        })
      },
      handleTotal(item) {
        /**
         * 1.拿到购物车的数据list
         * 2.利用id对比购物车是否含有该商品
         * 3.无则直接+1
         * 4.有并且库存>已添加在购物车的数量则添加,否则不添加并且提示
         */
        let commodity = JSON.parse(JSON.stringify(item))
        let list = JSON.parse(JSON.stringify(this.tableData))

        // 是否已经加入 (false未加入过/true已加入)
        let add = false;
        let addIndex = '';
        
        for(let i = 0; i < list.length; i++) {
          if (list[i].id == commodity.id) {
            add = true
            addIndex = i
          }
        }

        this.classification(add, addIndex, 1, commodity)
      },
      /**
       * 改变tableDate事件
       * @param {Booleam} add 是否已添加
       * @param {Number || ''} addIndex 已添加的情况是是下标,反之是空字符串
       * @param {Number} count 需要添加的数量
       * 
       * 入库
       * 校验是否已加入
       * 校验库存是否(>或者=)需要改变的数量
       * 
       * 出库/调拨/报损
       * 校验是否已加入
       * 校验库存是否(>或者=)需要改变的数量
       */
      classification(add, addIndex, count, item) {
        let commodity = JSON.parse(JSON.stringify(item))
        let list = JSON.parse(JSON.stringify(this.tableData))
        let type = JSON.parse(JSON.stringify(this.type))
        
        let number = add ? list[addIndex].amount + 1 : count
        let total = add ? list[addIndex].amount : 0
        
        if(type == 'storage') {
          if(add) {
            list[addIndex].amount = number
          } else {
            list = [...list, { ...commodity, amount: number, total_cost: 0, cost: 0 }]
          }
        } else {
          if(commodity.stock_balance > total) {
            if(add) {
              list[addIndex].amount = number
            } else {
              list = [...list, { ...commodity, amount: number, total_cost: 0, cost: 0 }]
            }
          } else {
            this.$Message.error('商品库存不足!');
            return false;
          }
        }
        this.tableData = list
      }
    }
  };
</script>

<style lang="less" scoped>
  .ivu-card-body {
    padding: 0
  }

  .goods-image {
    height: 100px;
    width: 100px;
    margin-bottom: 10px;
    border: 2px solid #ccc;

    img {
      max-height: 100%;
      min-height: 100%;
    }
  }
</style>
