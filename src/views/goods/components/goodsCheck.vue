<style lang="less">
  .goods-check {
    .count {
      .ivu-input {
        text-align: center;
      }
    }
    .discount-price {
      width: 80px;
      .ivu-input {
        color: red;
        font-size: 14px;
      }
    }
    .total-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30px;
      p {
        font-size: 16px;
        padding: 10px 0;
        span {
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
    .tabs {
      padding: 50px 50px 30px;
    }
    .pay-form {
      padding: 50px 0;
    }
  }
</style>

<template>
  <div class="table-wrap goods-check">
    <Table :data="list" :columns="cartColumns" disabledHover></Table>
    <div class="total-bar">
      <p>合计
        <span style="color: red">{{total}}</span> 元</p>
      <div>
        <router-link :to="{path: '/commodity/list'}">
          <Button type="primary">继续添加</Button>
        </router-link>
        <Button type="error" @click="handleClear">清空所有商品</Button>
      </div>
    </div>
    <div class="tabs">
      <Tabs @on-click="clickTabs" type="card" style="overflow:unset" :animated="false">
        <TabPane label="现场支付">
          <Form ref="livePaymentForm" :labelWidth="100" :model="livePaymentForm" :rules="livePaymentRules" class="modal-form pay-form">
            <FormItem label="支付方式">
              <PayTypeTags showCardPay v-model="pay_type" :describe="commodityDes" :amount="total" @on-dragonfly-confirm="onDragonflyConfirm" :userId="-2" />
            </FormItem>

            <form-item v-if="Number(pay_type) === 8 && Number(consumeSetting.open_confirm) === 1" prop="confirm_type" label="确认方式">
              <radio-group v-model="livePaymentForm.confirm_type">
                <radio :label="1">短信验证码确认</radio>
                <radio :label="2">一体机确认</radio>
              </radio-group>
              <Tooltip placement="right" :max-width="200">
                <Icon type="ios-help-circle" style="color: #f4a627;font-size:18px;"></Icon>
                <div slot="content">
                  <span style="vertical-align:middle;">使用储值卡消费时，需会员确认</span>
                </div>
              </Tooltip>
            </form-item>

            <div
              v-show="pay_type == 8 && ((Number(livePaymentForm.confirm_type) === 1 && Number(consumeSetting.open_confirm) === 1) || Number(consumeSetting.open_confirm) === 0)">
              <FormItem label="会员">
                <UserSearch style="width: 320px" url="/Web/Commodity/search_user" v-model="pay_user_id" @on-change="getUserCard" :focus="true"/>
              </FormItem>
              <FormItem label="会员卡">
                <Select style="width: 320px" v-model="card_user_id" @on-change="handleCardChange">
                  <Option
                    v-for="item in userCardList"
                    :key="item.card_user_id"
                    :value="item.card_user_id">{{item.card_name}} ({{item.amount}})</Option>
                </Select>
                 <Checkbox v-if="isShowDiscount" style="margin-left: 20px;" @on-change="handleDiscount" v-model="sign_discount">启用储值卡折扣 {{curCardInfo.sign_discount}}折</Checkbox>
              </FormItem>
            </div>

            <FormItem label="销售人员">
                <Select style="width: 320px" placeholder="请选择销售人员" v-model="marketers_id" filterable>
                    <!-- <Option label="无销售人员" value="0"></Option> -->
                    <Option v-for="item in salersList" :key="item.marketers_id" :label="item.sale_name" :value="item.marketers_id">
                        <span>{{item.sale_name}}</span>
                      </Option>
                </Select>
            </FormItem>
            <FormItem label="备注">
              <Input style="width: 320px" placeholder="选填" v-model="mark" />
            </FormItem>
            <FormItem>
              <div class="buttons">
                <Button type="success" @click.native="handlePay(0)">完成</Button>
                <Button @click="handleCancel">取消</Button>
              </div>
            </FormItem>
          </Form>
        </TabPane>
        <TabPane label="挂账">
          <Form :labelWidth="100" class="modal-form pay-form">
            <FormItem label="说明:">
              <p style="font-size: 14px">会员详情中会有挂账的特殊提醒</p>
            </FormItem>
            <FormItem label="会员">
              <UserSearch url="/Web/Commodity/search_user" v-model="user_id" style="width: 320px" placeholder="姓名/电话/实体卡号" />
            </FormItem>
            <FormItem label="销售人员">
                <Select style="width: 320px" placeholder="请选择销售人员" v-model="marketers_id" filterable>
                    <!-- <Option label="无销售人员" value="0"></Option> -->
                    <Option v-for="item in salersList" :key="item.marketers_id" :label="item.sale_name" :value="item.marketers_id">
                        <span>{{item.sale_name}}</span>
                        <!-- <span v-if="item.is_membership" style="float:right;color:red">*会籍</span> -->
                      </Option>
                </Select>
            </FormItem>
            <FormItem label="备注">
              <Input v-model="accountMark" style="width: 320px" placeholder="选填" />
            </FormItem>
            <FormItem>
              <div class="buttons">
                <Button type="success" @click.native="handlePay(1)">完成</Button>
                <Button @click="handleCancel">取消</Button>
              </div>
            </FormItem>
          </Form>
        </TabPane>
      </Tabs>
    </div>
    <receipt-modal v-model="showPrint" :to-path='toPath' @on-receiptupdate="receiptUpdate" />
    <Modal v-model="showVerifySmsModal" title="请输入会员收到的短信验证码" :closable="false" :mask-closable="false">
      <Input v-model="verifyCode" @on-enter="handleConfirmSuccess" placeholder="短信验证码">
        <Button slot="append" :disabled="!!verifySec" @click="handleResendVerifyCode">{{ resendVerifyCode }}</Button>
      </Input>
      <div slot="footer" style="text-align:center;">
        <div>
          <Button @click="handleShutDownOrder(false)">关闭</Button>
          <Button type="success" @click="handleConfirmSuccess">确认</Button>
        </div>
        <div style="margin-top:16px;">
          <span style="color:#AAA;">5分钟后未确认会取消该订单</span>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { mapGetters, mapMutations, mapState } from 'vuex';
  import * as Types from 'types';
  import PayTypeTags from 'components/form/PayTypeTags';
  import UserSearch from 'components/user/userSearch';
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js';
  import { EventBus } from 'components/EventBus.js';
  export default {
    name: 'goodsCheck',
    components: { PayTypeTags, UserSearch, receiptModal },
    mixins: [receipt],
    data() {
      return {
        consumeSetting: {
          open_confirm: undefined
        },
        livePaymentForm: {
          confirm_type: undefined
        },
        livePaymentRules: {
          confirm_type: [
            { required: true, message: '请选择确认方式' }
          ]
        },
        timer: null,
        timerTwo: null,
        verifyCode: undefined,
        currentPreId: undefined,
        verifySec: 30,
        showVerifySmsModal: false,
        marketers_id: '',
        salersList: [],
        userCardList: [],
        activeIndex: 0,
        activated: [0],
        pay_type: 3,
        user_id: '',
        pay_user_id: '',
        pay_order_ids: [],
        mark: '',
        card_user_id: '',
        curCardInfo: '',
        sign_discount: '',
        accountMark: '',
        cartColumns: [
          {
            key: 'commodity_img',
            title: ' ',
            render: (h, params) => {
              const item = params.row;
              return (
                <a>
                  <img style="width: 30px; height: 30px" src={item.commodity_img} />
                </a>
              );
            }
          },
          {
            title: '商品名称',
            key: 'commodity_name'
          },
          {
            title: '单价',
            key: 'singlePrice'
          },
          {
            title: '购买数量',
            key: 'count',
            render: (h, params) => {
              const item = params.row;
              const btnStyle = {
                width: '32px',
                height: '32px',
                minWidth: 'auto',
                boxShadow: 'none'
              };
              const handleTotal = count => {
                if(this.sign_discount && this.curCardInfo && this.curCardInfo.sign_discount!=='10.0') {
                  item.totalPrice = (item.commodity_price * count).toFixed(2);
                  item.discountPrice = (item.commodity_price * count * this.curCardInfo.sign_discount/10).toFixed(2);
                } else {
                  item.discountPrice = item.totalPrice = (item.commodity_price * count).toFixed(2);
                }
                this[Types.ADD_GOODS_ITEM](item);
                this.$nextTick(() => {
                  this.$forceUpdate();
                });
              };
              return (
                <div style="display: flex; justify-content: center; align-items: center">
                  <i-button

                    style={btnStyle}
                    disabled={item.count <= 1}
                    onClick={() => {
                      item.count--;
                      handleTotal(item.count);
                    }}>
                    -
                  </i-button>
                  <i-input
                    class="count"
                    value={item.count}
                    spellcheck={true}
                    on-on-blur={e => {
                      const value = e.target.value;
                      if (Number.isNaN(+value)) return this.$Message.error('请输入数字');
                      item.count = Math.max(value, 0)
                      if (item.count > item.stock_balance) {
                        this.$Message.error('库存不足');
                        item.count = item.stock_balance;
                      }
                      handleTotal(item.count);
                    }}
                    style="width: 42px; height: 32px; margin: 0 5px"
                  />
                  <i-button

                    style={btnStyle}
                    disabled={item.count >= item.stock_balance}
                    onClick={() => {
                      item.count++;
                      handleTotal(item.count);
                    }}>
                    +
                  </i-button>
                </div>
              );
            }
          },
          {
            title: '总计',
            key: 'totalPrice',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  ￥ <span style="color: red">{item.totalPrice}</span>
                </div>
              );
            }
          },
          {
            title: '打折后',
            key: 'discountPrice',
            render: (h, params) => {
              const item = params.row;
              return (
                <div style="display: flex; align-items: center; justify-content: center">
                  <span style="margin-right: 10px">￥</span>
                  <i-input
                    class="count discount-price"
                    value={item.discountPrice}
                    disabled={this.sign_discount === true}
                    on-on-blur={e => {
                      const value = e.target.value;
                      if (Number.isNaN(+value)) return this.$Message.error('请输入数字');
                      item.discountPrice = Number(value);
                      this[Types.ADD_GOODS_ITEM](item);
                    }}
                  />
                </div>
              );
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              const item = params.row;
              return (
                <div>
                  <i-button
                    type="text"
                    style="color: #d9544f"
                    onClick={() => {
                      this[Types.DELETE_GOODS_ITEM](item.id);
                    }}>
                    删除
                  </i-button>
                </div>
              );
            }
          }
        ],
        hasEnterNum: '',
        authCode: ''
      };
    },
    computed: {
      ...mapGetters('goods', ['list', 'total', 'count']),
      ...mapState(['busId']),
      resendVerifyCode() {
        if (this.verifySec > 0) {
          return `再次发送(${this.verifySec}S)`
        } else {
          return `再次发送`
        }
      },
      commodityDes() {
        let des = ''
        this.list.forEach((item)=>{
          des += `${item.commodity_name}*${item.count} `
        })
        return des
      },
      isShowDiscount() {
        return this.activeIndex === 0 && this.pay_type == 8 && ((Number(this.livePaymentForm.confirm_type) === 1 && Number(this.consumeSetting.open_confirm) === 1) || Number(this.consumeSetting.open_confirm) === 0) && (this.curCardInfo && this.curCardInfo.sign_discount && this.curCardInfo.sign_discount!=='10.0')
      }
    },
    watch: {
      'curCardInfo.sign_discount'(val, oldVal) {
        if(this.sign_discount) {
          this.setItemDisAmount(val/10)
        } else {
          this.setItemDisAmount(1)
        }
      },
      'list.length'(val) {
        if (val === 0) {
          this.$router.back();
        }
      },
      isShowDiscount(val, oldVal) {
        if(!val && oldVal && this.sign_discount) {
          this.sign_discount = false
          this.setItemDisAmount(1)
        }
      },
      showVerifySmsModal(val) {
        if (!val) {
          this.verifyCode = undefined
        }
      }
    },
    created() {
      this.getBusConsumeSetting()
      this.getSalersList()
      EventBus.$on('clearCargo', () => {
        this[Types.CLEAR_GOODS_CART]();
      });
      /**
       * 扫码枪逻辑梳理(识别到条形码)
       * 1.监控数字键盘和enter按下事件 以enter为结束标识
       * 2.调用查询商品是否存在,存在则走3,4,不存在则走5
       * 3.购物车为空的时候直接加入该商品
       * 4.购物车不为空时,查询购物车是否已经有该商品,有则需要在原来的基础上数量+1, 如果没有则直接加入该商品
       * 5.弹出提示
       */
      document.addEventListener('keydown', this.handleKeyDown)
    },
    mounted() {
      this.killOrder()
      let preId = localStorage.getItem('preId')
      preId && this.violenteShutDownOrder(preId)
    },
    // 解绑事件
    beforeDestroy() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },
    methods: {
      onDragonflyConfirm(info) {
        this.pay_order_ids = info.pay_order_ids
      },
      handleCardChange(val) {
        if(!val) {
          this.curCardInfo = null
          return
        }
        for (const iterator of this.userCardList) {
          if(val === iterator.card_user_id) {
            this.curCardInfo = iterator
          }
        }

      },
      handleDiscount(val) {
        if(val) {
          let curDis = this.curCardInfo.sign_discount
          this.setItemDisAmount(curDis/10)
        } else {
          this.setItemDisAmount(1)
        }
      },
      setItemDisAmount(discount) {
        this.list.forEach((item, index)=> {
          item.discountPrice = (item.totalPrice * discount).toFixed(2)
          this[Types.ADD_GOODS_ITEM](item);
        })
      },
      killOrder() {
        window.addEventListener('beforeunload', event => {
          localStorage.setItem('preId', this.currentPreId)
        })
      },
      getBusConsumeSetting() {
        return this.$service.get('/Web/Commodity/get_setting').then(res => {
          if (res.data.errorcode == 0) {
            if (res.data.data.open_confirm == 1) {
              this.consumeSetting = res.data.data
              this.livePaymentForm.confirm_type = Number(res.data.data.last_open_confirm_type)
              this.consumeSetting.card_id_list = res.data.data.card_id_list.split(',')
            } else {
              this.consumeSetting = res.data.data
            }
          } else {
            this.$Message.error(res.data.message)
          }
        })
      },
      getSalersList() {
          let isIncludeMembership = {
            include_membership: 0
          }
          this.$service.post('Web/Marketers/get_marketers', isIncludeMembership).then((res) => {
              if(res.data.errorcode === 0) {
                this.salersList = res.data.data
              } else {
                  this.$Message.error(res.data.errormsg);
                }
          }).catch(err => console.error(err))
      },
      ...mapMutations('goods', [Types.ADD_GOODS_ITEM, Types.DELETE_GOODS_ITEM, Types.CLEAR_GOODS_CART]),
      receiptUpdate() {
        this[Types.CLEAR_GOODS_CART]();
      },
      handleClear() {
        const that = this;
        this.$Modal.confirm({
          title: '清空所有',
          content: '要清空所有商品吗?',
          onOk() {
            that[Types.CLEAR_GOODS_CART]();
          }
        });
      },
      dataValidate() {
        if (this.pay_type == 8 && ((Number(this.livePaymentForm.confirm_type) === 1 && Number(this.consumeSetting.open_confirm) === 1) || Number(this.consumeSetting.open_confirm) === 0)) {
          if (!this.pay_user_id) {
            return { res: false, message: '请选择会员' }
          } else if (!this.card_user_id) {
            return { res: false, message: '请选择储值卡' }
          } else {
            return { res: true }
          }
        }
        return { res: true }
      },
      getUserCard(user_id) {
        if (!user_id) return;
        this.card_user_id = undefined
        const url = 'Web/Commodity/get_user_debit_card';
        this.$service
          .post(url, { user_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (this.consumeSetting.card_use_type == 2) {
                this.userCardList = Array.isArray(data.list) && data.list.filter(item => {
                  return this.consumeSetting.card_id_list.includes(item.card_id)
                })
              } else {
                this.userCardList = data.list
              }
              if (!this.userCardList.length) {
                this.$Message.error('该会员没有储值卡')
                return
              }
              this.card_user_id = this.userCardList[0].card_user_id
              this.handleCardChange(this.card_user_id)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleCancel() {
        this[Types.CLEAR_GOODS_CART]();
      },
      async handlePay(isAccount) {
        if (this.pay_type == 8 && this.consumeSetting.open_confirm == 1) {
          const checkForm = await this.$refs.livePaymentForm.validate()
          if (!checkForm) return
        }

        let vldt = this.dataValidate()
        if (!vldt.res) {
          this.$Message.error(vldt.message)
          return
        }
        const url = '/Web/Commodity/commodity_settlement';
        let postData = {
          confirm_type: this.livePaymentForm.confirm_type,
          marketers_id: this.marketers_id,
          pay_type: this.pay_type,
          checkout_price: this.total,
          mark: this.mark,
          pay_order_ids: this.pay_order_ids,
          user_id: this.pay_type == 8 ? this.pay_user_id : '',
          card_user_id: this.pay_type == 8 ? this.card_user_id : '',

          commodity_list: this.list.map(item => {
            return {
              commodity_id: item.id,
              purchase_count: item.count,
              commodity_price: item.commodity_price,
              commodity_name: item.commodity_name,
              commodity_unit: item.unit,
              commodity_img: item.commodity_img,
              total_price: item.discountPrice
            };
          })
        };
        if (isAccount) { // 挂帐
          postData = {
            ...postData,
            ...{
              pay_order_ids: '',
              user_id: this.user_id,
              pay_status: 2,
              pay_type: 0,
              mark: this.accountMark
            }
          };
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              if (this.activeIndex == 1) {
                this.$Message.success(res.data.errormsg);
                setTimeout(() => {
                  this[Types.CLEAR_GOODS_CART]();
                }, 1000);
              } else {
                // 现场支付
                if (Number(postData.pay_type) === 8 && !!res.data.pre_id) {
                  // 有pre_id 消费确认的话
                  this.handleConsumeConfirm(res.data.pre_id, postData.confirm_type)
                } else {
                  // 没消费确认  直接成功
                  this.commodityComplete(postData.user_id, res.data.order_sn, 'buygoods')
                }
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleConsumeConfirm(pre_id, confirmType) {
        this.currentPreId = pre_id
        if (Number(confirmType) === 2) {
          this.loopCheckPreStatus()
          this.$Modal.info({
            title: '请等待会员完成确认',
            content: '5分钟后未确认自动取消该订单',
            okText: '关闭订单',
            onOk: () => {
              this.handleShutDownOrder()
            }
          })
        } else if (Number(confirmType) === 1) {
          this.showVerifySmsModal = true
          this.handleAutoShutDownSms()
          this.doCountDown()
        }
      },
      handleAutoShutDownSms() {
        this.timerTwo = setTimeout(() => {
          this.handleShutDownOrder(true)
        }, 300000)
      },
      violenteShutDownOrder(preId) {
        this.$service.post('Web/Commodity/get_pre_status', { pre_id: preId || this.currentPreId }, { loading: false }).then(res => {
          if (res.data.errorcode == 0) {
            if (res.data.data.status == 3) {
              this.handleShutDownOrder(false, preId)
            }
          }
        })
      },
      loopCheckPreStatus() {
        this.$service.post('Web/Commodity/get_pre_status', { pre_id: this.currentPreId }, { loading: false }).then(res => {
          if (res.data.errorcode == 0) {
            if (res.data.data.status == 3) {
              this.timer = setTimeout(() => {
                this.loopCheckPreStatus()
              }, 3000)
            } else if (res.data.data.status == 1) {
              this.$Modal.remove()
              // this.$Message.success(res.data.errormsg)
              this.commodityComplete(this.pay_user_id, res.data.data.order_sn, 'buygoods')
            } else if (res.data.data.status == 2 || res.data.data.status == 0) {
              this.$Modal.remove()
              this.handleMachineRes(res.data.errormsg)
            } else if (res.data.data.status == 4) {
              this.handleMachineRes('已自动关闭订单')
            }
          } else {
            this.$Modal.remove()
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      handleMachineRes(content) {
        this.$Modal.info({
          title: '提示',
          content,
          okText: '确定'
        })
      },
      handleConfirmSuccess() {
        clearTimeout(this.timerTwo)
        let postdata = {
          confirm_type: this.livePaymentForm.confirm_type,
          pre_id: this.currentPreId
        }
        if (postdata.confirm_type == 1) {
          postdata.code = this.verifyCode
        } else if (postdata.confirm_type == 2) {
          postdata.card_user_ids = this.card_user_id
          user_id: this.pay_user_id
        }

        return this.$service.post('/Web/Commodity/submit_commodity_settlement', postdata).then(res => {
          if (res.data.errorcode == 0) {
            this.showVerifySmsModal = this.showVerifySmsModal && false
            // this.$Message.success(res.data.errormsg)
            this.commodityComplete(this.pay_user_id, res.data.order_sn, 'buygoods')
          } else if (res.data.errorcode == 40022) {
            this.showVerifySmsModal = this.showVerifySmsModal && false
            this.$Message.error(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          console.error(err)
        })
      },
      doCountDown() {
        this.verifySec = 30
        let timer = setInterval(() => {
          if (this.verifySec === 0) {
            clearInterval(timer)
          } else {
            this.verifySec--
          }
        }, 1000)
      },
      handleResendVerifyCode() {
        return this.$service.post('/Web/Commodity/commodity_settlement_send_msg', { pre_id: this.currentPreId }).then(res => {
          if (Number(res.data.errorcode) == 0) {
            this.$Message.success(res.data.errormsg)
            this.doCountDown()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      handleShutDownOrder(auto = false, preId) {
        return this.$service.post('/Web/Commodity/close_commodity_settlement', { pre_id: preId || this.currentPreId }).then(res => {
          if (res.data.errorcode == 0) {
            this.showVerifySmsModal = this.showVerifySmsModal && false
            clearTimeout(this.timer)
            clearTimeout(this.timerTwo)
            if (!auto) {
              this.$Message.success(res.data.errormsg)
            } else {
              this.handleMachineRes('已自动关闭订单')
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      clickTabs(index) {
        this.activeIndex = index;
        const active = document.querySelector('.ivu-tabs-ink-bar');
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
        active.setAttribute('class', className);
        if (!this.activated.includes(index)) {
          this.activated.push(index);
        }
      },
      handleKeyDown(e) {
        if(this.pay_type == 20) {
          return false;
        }
        let event = e || window.event;
        let key = event.key;
        if (event.target.localName != 'input' && /^[0-9]*$/.test(key)) {
          this.hasEnterNum += key
        }
        if (key === 'Enter') {
          this.authCode = this.hasEnterNum
          this.hasEnterNum = ''
          this.handleGetCommodity(event.target.localName, this.authCode)
        }
      },
      handleGetCommodity(name, scan) {
        let code = JSON.parse(JSON.stringify(scan))
        let url= '/Web/Commodity/get_commodity'
        let params= {
          barcode_number: code,
          bus_id: this.$store.state.busId
        }
        if(name == 'input') {
          return false;
        }
        if(!scan) {
          this.$Message.error('未检测到条形码!');
          return false;
        }
        this.$service.post(url, params).then(res => {
          if (res.data.errorcode === 0) {
            // online
            if(Object.prototype.toString.call(res.data.data) == '[object Array]') {
              this.$Message.error('未知商品!');
            } else if(res.data.data.online == 0) {
              this.$Message.error('商品已下架!');
            } else if(res.data.data.stock_balance > 0) {
              this.handleTotal(res.data.data)
            } else {
              this.$Message.error('商品库存不足!');
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        })
      },
      handleTotal(item) {
        /**
         * 1.拿到购物车的数据list
         * 2.利用id对比购物车是否含有该商品
         * 3.无则直接+1
         * 4.有并且库存>已添加在购物车的数量则添加,否则不添加并且提示
         */
        let commodity = JSON.parse(JSON.stringify(item))
        let list = JSON.parse(JSON.stringify(this.list))

        // 购物车是否包含 (false不包含/true包含)
        let only = false

        // 是否可以添加  (false不可以/true可以)
        let bool = false

        // 设置默认数量
        commodity.count = 1
        
        for(let i = 0; i < list.length; i++) {
          if (list[i].id == commodity.id) {
            // 购物车包含该商品
            only = true
            if(commodity.stock_balance > list[i].count) {
              bool = true
              // 设置实际数量
              commodity.count = list[i].count + 1
            }
            //break跳出整个循环
            break;
          }
        }

        // 设置实际单商品价格*数量
        commodity.discountPrice = commodity.totalPrice = (commodity.commodity_price * commodity.count).toFixed(2);

        if(only) {
          if(bool) {
            // 购物车包含并且数量允许添加
            this[Types.ADD_GOODS_ITEM](commodity);
          } else {
            // 购物车包含但是数量无法添加
            this.$Message.error('商品库存不足!');
          }
        } else {
          if(commodity.stock_balance > 0) {
            // 购物车不包含并且数量允许添加
            this[Types.ADD_GOODS_ITEM](commodity);
          } else {
            // 商品本身没有库存
            this.$Message.error('商品库存不足!');
          }
        }
      }
    },
    beforeDestory() {
      this.violenteShutDownOrder()
      clearTimeout(this.timer)
      clearTimeout(this.timerTwo)
      this.$Modal.remove()
    },
    beforeRouteLeave(to, from, next) {
      clearTimeout(this.timer)
      clearTimeout(this.timerTwo)
      this.$Modal.remove()
      this.violenteShutDownOrder()
      next()
    }
  };
</script>
