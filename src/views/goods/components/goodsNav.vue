<template>
  <div style="height: 100%">
    <Modal v-model="isEdit" :title="editTitle">
      <Form class="modal-form">
        <FormItem label="类别名称" style="padding-left: 50px; padding-top: 30px">
          <Input v-model="editData.name" style="width: 300px"/>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="editConfirm(editData)">确定</Button>
        <Button @click="isEdit = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="isDelete" title="删除商品类别" width="600px">
      <Form class="modal-form" style="padding-left: 50px">
        <FormItem style="color: red">删除商品分类将同时删除子分类，请将该分类下的商品转移至其它类别中</FormItem>
        <FormItem label="将商品转移到">
          <catSelect v-if="isDelete" v-model="toId" style="width: 300px"></catSelect>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="delConfirm(delData)">确定</Button>
        <Button @click="isDelete = false">取消</Button>
      </div>
    </Modal>
    <div style="padding: 20px 20px 0">
      <InventorySelect v-model="inventoryCode" @on-change="onInventoryChange" :clearable="false" />
    </div>
    <TreeMenu style="min-width: 280px; border: 0;" :data="groupList" :disabledEdit="!adminInfo.commodity_inventory.add"
              :options="{titleKey: 'name', idKey: 'id', levelKey: 'level', level: 3, levelTop: 1, addTitle: '添加分类', editTitle: '编辑类名', deleteTitle: '删除分类', disabledKeys: [0]}"
              @on-edit="data => addGroup(data, true)" @on-add="data => addGroup(data)"
              @on-delete="data => delGroup(data)" @on-select="groupId => toggleTree(groupId)"></TreeMenu>
  </div>
</template>
<script>
  import { mapMutations, mapActions, mapState } from 'vuex';
  import { SET_INVENTORY_CODE } from 'src/store/mutationTypes'
  import TreeMenu from 'components/treeMenu';
  import InventorySelect from './inventorySelect'
  import catSelect from './catSelect'

  export default {
    name: 'goodsNav',
    components: { TreeMenu, InventorySelect, catSelect },
    data() {
      return {
        inventoryCode: '',
        toId: '',
        busList: [],
        groupList: [],
        editData: {},
        editModal: {
          show: false,
          title: '组别编辑'
        },
        delData: {},
        delModal: {
          show: false,
          title: '组别删除'
        },
        currentId: '',
        delParentId: '',
        showTree: true,
        showListIndex: [],

        isEdit: false,
        editTitle: '',
        isDelete: false,
        resGroupList: []
      };
    },
    computed: {
      ...mapState(['adminInfo'])
    },
    created() {
      const inventoryCode = this.$store.state.goods.inventoryCode || `${this.$store.state.busId}-1`
      this.onInventoryChange(inventoryCode);
      this.getGroup();
    },
    methods: {
      ...mapMutations('goods', [SET_INVENTORY_CODE]),
      ...mapActions('goods', ['getCatList']),
      onInventoryChange(id) {
        this.inventoryCode = id;
        this[SET_INVENTORY_CODE](id);
        this.$emit('inventoryChange');
        this.getGroup();
      },
      async getGroup() {
        const list = await this.getCatList({ inventoryCode: this.inventoryCode });
        this.groupList = list.map(item => {
          return {
            ...item,
            ...{
              expand: true,
              children:
                item.children &&
                item.children.map(item => {
                  return {
                    ...item,
                    ...{
                      expand: true
                    }
                  };
                })
            }
          };
        });
      },
      addGroup(data, isEdit) {
        let dataObj = {
          group_level: data.group_level,
          name: data.name,
          pid: data.id,
          id: data.id
        };
        this.editTitle = isEdit ? '类别编辑' : '类别创建';
        this.editData = dataObj;
        this.editData.name = isEdit ? dataObj.name : '';
        this.editData.isEdit = isEdit;
        this.isEdit = true;
      },
      delGroup(data) {
        this.delData = {
          name: data.name,
          group_level: data.group_level,
          id: data.id
        };
        this.isDelete = true;
      },
      toggleTree(id, level, index) {
        this.currentId = id;
        this.$emit('groupChange', id);
      },
      toggleGroup(index) {
        let findIndex = this.showListIndex.findIndex(item => item === index);
        if (findIndex === -1) {
          this.showListIndex.push(index);
        } else {
          this.showListIndex.splice(findIndex, 1);
        }
      },
      editConfirm(data) {
        if (!data.name.trim()) return this.$Message.error('请输入分类名称');
        let postData = {
          ...data,
          inventoryCode: this.inventoryCode
        };
        let url = '/web/comCate/add';
        if (data.isEdit === true) {
          url = '/web/comCate/edit';
          postData = {
            name: data.name,
            id: data.id
          };
        }
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.data.errorcode === 0) {
              this.getGroup();
              this.isEdit = false;
              this.$Message.success(response.data.errormsg);
            } else {
              this.$Message.error(response.data.errormsg);
            }
          })
          .catch(error => {
            console.error(error);
          });
      },
      delConfirm(data) {
        let postData = {
          id: data.id,
          toId: this.toId,
          inventoryCode: this.inventoryCode
        };
        let url = '/web/comCate/del';
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                this.getGroup();
                this.$emit('groupChange', data.pid);
                this.isDelete = false;
                this.$Message.success(response.data.errormsg);
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(error => {
            console.error(error);
          });
      }
    }
  };
</script>
<style scoped>
  h2 {
    font-weight: normal;
    user-select: none;
  }

  .tree {
    width: 100%;
    height: 100%;
    background: #fff;
    border: 1px solid #dcdcdc;
    padding: 10px 0;
  }

  .tree_t {
    width: 100%;
    height: 30px;
  }

  .tree_t_l {
    width: 27px;
    height: 27px;
    float: left;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: 4px -1px;
    margin-left: 10px;
  }

  .tree_t_c {
    float: left;
    color: #666666;
    font-size: 18px;
    margin: 0;
    line-height: 30px;
    margin-left: 6px;
    max-width: 60%;
  }

  .tree_t_c,
  .treeli_t_h2,
  .list_c {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .isfocus .tree_t_c,
  .isfocus .treeli_t_h2,
  .isfocus .list_c {
    color: #5cb85c;
  }

  .tree_t_r {
    width: 27px;
    height: 27px;
    float: right;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -287px 0px;
    margin-right: 16px;
    cursor: pointer;
  }

  .tree_t_r:hover {
    opacity: 0.7;
  }

  .treeli_t {
    height: 30px;
  }

  .treeli_t span,
  .list_t span {
    display: none;
  }

  .treeli_t:hover span,
  .list_t:hover span {
    display: block;
  }

  .treeli_t_l {
    width: 27px;
    height: 27px;
    float: left;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -34px -4px;
    margin-left: 30px;
  }

  .no-children {
    background-position: -70px -4px;
  }

  .treeli_t_h2 {
    margin: 0;
    float: left;
    color: #666666;
    font-size: 16px;
    line-height: 30px;
    height: 30px;
    max-width: 45%;
  }

  @media screen and (max-width: 1280px) {
    .treeli_t_h2 {
      max-width: 35%;
    }
  }

  .list_t {
    height: 30px;
  }

  .list_l {
    clear: both;
    width: 27px;
    height: 27px;
    float: left;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -70px -4px;
    margin-left: 50px;
  }

  .list_c {
    margin: 0;
    float: left;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    line-height: 30px;
    max-width: 40%;
  }

  .ullist li {
    clear: both;
    margin-left: 90px;
    color: #666666;
    font-size: 12px;
    line-height: 20px;
  }

  .dellist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -180px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .dellist:hover {
    opacity: 0.7;
  }

  .emitlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -109px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .emitlist:hover {
    opacity: 0.7;
  }

  .addlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -145px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .addlist:hover {
    opacity: 0.7;
  }

  .hjdpic {
    width: 30px;
    height: 30px;
    background: url('~assets/img/yg_pic.png') no-repeat;
    background-position: -248px 2px;
    margin: 0 auto;
  }

  .togglegreen {
    background-position: -212px 2px;
  }
</style>

