<template>
  <Modal :title="type + '明细'" v-model="showModal" width="1000px">
    <div style="padding: 0 30px">
      <Form style="display: flex; flex-wrap: wrap" class="modal-form">
        <template v-if="type === '调入' || type === '调出'">
          <FormItem label="调拨单号">{{modalData.order_sn}}</FormItem>
          <!--<FormItem label="调拨类型">{{modalData.op_type }}</FormItem>-->
          <FormItem label="调出场馆">{{modalData.from_bus_name}}</FormItem>
          <FormItem label="调入场馆">{{modalData.to_bus_name}}</FormItem>
        </template>
        <template v-else>
          <FormItem :label="type + '单号'">{{ modalData.order_sn }}</FormItem>
          <FormItem :label="type + '场馆'">{{modalData.bus_name}}</FormItem>
        </template>
        <FormItem label="操作账号">{{modalData.op_name}}</FormItem>
        <FormItem label="操作时间">{{modalData.date}}</FormItem>
      </Form>
      <Table :columns="modalColumns" :data="modalTableData"></Table>
      <Table :columns="modalColumns" :data="modalTotalData" :show-header="false"></Table>
      <div style="margin-top: 30px;" v-if="modalData.remark">
        <h3 style="display: inline-block">退货原因:</h3>
        <span style="font-size: 14px;">{{modalData.remark}}</span>
      </div>
    </div>
    <div slot="footer"></div>
  </Modal>
</template>

<script>
  import modalColumns from './storageModalColumns';

  export default {
    name: 'storageModal',
    props: {
      value: {
        type: Boolean,
        default: false
      },
      type: {},
      inout_id: {},
      inventoryCode: {}
    },
    data() {
      return {
        modalColumns: [],
        modalTableData: [],
        modalData: {}
      };
    },
    computed: {
      modalTotalData() {
        const formatNumber = (num) => {
          if (Number.isInteger(num)) {
            return num;
          } else {
            return Number(num.toFixed(4));
          }
        }

        const totalNum = this.modalTableData.reduce((total, item) => total + Number(item.amount), 0);
        const totalNumIn = this.modalTableData.reduce((total, item) => total + Number(item.in_amount), 0);
        const totalNumOut = this.modalTableData.reduce((total, item) => total + Number(item.return_amount), 0);
        const totalAmount = this.modalTableData.reduce((total, item) => total + Number(item.total_cost), 0);

        return [{
          commodity_name: '总计',
          amount: formatNumber(totalNum),
          in_amount: formatNumber(totalNumIn || 0),
          return_amount: formatNumber(totalNumOut || 0),
          total_cost: formatNumber(totalAmount),
        }];
      },
      showModal: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      type(val) {
        if (val === '调出' || val === '调入') {
          this.modalColumns = [...modalColumns.common, ...modalColumns.transfer, { title: `${val}成本`, key: 'total_cost' }];
        } else {
          this.modalColumns = [...modalColumns.common,
            {
              title: `${val}数量`,
              key: 'amount'
            },
            {
              title: `${val}成本`,
              key: 'total_cost'
            },];
        }
      },
      value(val) {
        val && this.getList();
      }
    },
    methods: {
      getList() {
        const url = '/web/inventory/inoutDetail';
        const postData = {
          inout_id: this.inout_id,
          inventoryCode: this.inventoryCode
        };
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.modalTableData = data.comList;
            this.modalData = data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      }
    },
  }
  ;
</script>

<style>
  .ivu-form-item-content {
    font-size: 14px;
    line-height: 34px;
  }
</style>

<style scoped>
  .modal-form .ivu-form-item {
    margin-bottom: 8px;
    width: 35%
  }
</style>
