<template>
  <div class="table-wrap">
    <header>
      <DatePickerWithButton
        :days.sync="days"
        clearable
        @on-change="dateChanged" />
      <Select
        v-model="postData.goods_type"
        class="w120"
        filterable
        clearable
        placeholder="商品类型">
        <Option value="1">体验卡</Option>
        <Option value="2">体验课</Option>
        <Option value="3">折扣券</Option>
        <!-- <Option value="4">金币</Option> -->
        <Option value="5">其它</Option>
      </Select>
      <Select
        v-model="postData.status"
        class="w120"
        filterable
        clearable
        placeholder="状态">
        <Option value="0">禁用</Option>
        <Option value="1">启用</Option>
      </Select>
      <Input
        v-model="postData.search"
        class="w120"
        clearable
        placeholder="商品名称"
        @on-enter="search" />
      <Button type="success" class="search" @click="search">搜索</Button>

      <Button
        type="text"
        style="margin-left: auto;"
        :title="exchangePointAuth ? null : '没有兑换记录权限'"
        :disabled="!exchangePointAuth"
        @click="$router.push('/goods/exchangePoint')">
        兑换记录
      </Button>
    </header>

    <main>
      <Table
        ref="table"
        class="avatar-zoom"
        :columns="columns"
        :data="tableData"
        stripe
        disabled-hover />
    </main>

    <footer>
      <Button type="success" style="margin-right:30px" @click="goAdd">添加商品</Button>
      <Button type="primary" @click="handleShowExchangeModal(true)">魅力值兑换积分</Button>
      <Page
        class="page"
        :total="totalCount"
        :page-size="postData.page_size"
        :current.sync="postData.page_no"
        placement="top"
        show-total
        show-sizer
        @on-change="pageChanged"
        @on-page-size-change="pageSizeChanged" />
    </footer>

    <Modal
      v-model="showExchangeModal"
      title="魅力值兑换"
      :closable="false"
      :mask-closable="false">
      <div class="exchange-modal-body">
        <span class="tips">将本门店全部会员的魅力值兑换成积分</span>
        <span style="margin: 40px 0 20px;">魅力值 积分兑换比例</span>
        <div class="input-row">
          魅力值&nbsp;
          <Input-number
            v-model="num"
            class="input-number"
            :max="100000000"
            :min="1"
            :precision="0"
            :active-change="false"
          />
          <span class="point-text">&nbsp;= 1&nbsp;</span>
          积分
        </div>
        <span style="margin: 20px 0 40px;">(兑换不足1积分按四舍五入计算)</span>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleConfirmExchange">确认</Button>
        <Button @click="handleShowExchangeModal(false)">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';
import { formatDate } from 'utils';

const GOODS_TYPE = {
  '1': '体验卡',
  '2': '体验课',
  '3': '折扣卷',
  '4': '金币',
  '5': '其它',
}

export default {
  name: 'PointList',
  components: {
    DatePickerWithButton
  },

  data() {
    return {
      totalCount: 0,
      days: [Date.now() - 365 * 24 * 60 * 60 * 1000, Date.now()],
      tableData: [],
      cardList: [],
      postData: {
        s_date: formatDate(Date.now() - 365 * 24 * 60 * 60 * 1000, 'yyyy-MM-dd'),
        e_date: formatDate(new Date(), 'yyyy-MM-dd'),
        search: '',
        goods_type: '', // 商品类型 1—体验卡 2—体验课 3—折扣卷 4—金币 5—其他
        status: '', // 0禁用 1正常
        page_size: 10,
        page_no: 1
      },
      columns: [
        {
          title: '商品名称',
          key: 'goods_name',
          ellipsis: true
        },
        {
          title: '商品类型',
          key: 'goods_type_text',
        },
        {
          title: '所需积分',
          key: 'point'
        },
        {
          title: '库存总量/剩余',
          key: 'volume_text',
        },
        // { title: '可兑换人群', key: '', tooltip: true },
        { title: '可兑换数量', key: 'barter_limit_rule_text' },
        {
          title: '状态',
          key: 'status_text',
        },
        {
          title: '操作',
          width: 210,
          render: (h, { row }) => {
            return (
              <div>
                <i-button
                  class="mr5"
                  type="text"
                  onClick={() => {
                    this.$router.push({
                      path: '/goods/addPointGoods',
                      name: '积分商品添加',
                      params: {
                        id: row.id,
                        goodsData: row
                      }
                    });
                  }}>
                  编辑
                </i-button>
                <i-button
                  type="text"
                  class="mr5"
                  title={this.exchangePointAuth ? null : '没有兑换记录权限'}
                  disabled={!this.exchangePointAuth}
                  onClick={() => {
                    this.$router.push({
                      path: '/goods/exchangePoint',
                      name: '积分兑换记录',
                      params: {
                        id: row.id,
                        goodsId: row.goods_Id
                      }
                    });
                  }}>
                  兑换记录
                </i-button>
                <i-button
                  type="text"
                  style="color: #ff696a;"
                  onClick={() => {
                    this.handleDelete(row.id);
                  }}>
                  删除
                </i-button>
              </div>
            );
          }
        }
      ],
      exchangePointAuth: false, // 积分兑换记录权限
      /* 魅力值兑换 */
      showExchangeModal: false,
      num: null
    };
  },

  created() {
    this.getList()
    this.getExchangePointAuth()
  },

  methods: {
    handleDelete(goods_id) {
      let _this = this;
      this.$Modal.confirm({
        title: '',
        content: '确定要删除吗？',
        onOk() {
          this.$service.post('/Web/PointGoods/delPointGoods', { id: goods_id }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              _this.getList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    },
    goAdd() {
      this.$router.push({
        path: '/goods/addPointGoods',
        name: '积分商品添加',
      });
    },
    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    // 获取积分商城列表
    getList() {
      const params = {
        ...this.postData
      }
      this.$service
        .post('/Web/PointGoods/getPointGoodsList', params)
        .then((res) => {
          const { errorcode, errormsg, data } = res.data;
          if (errorcode == 0) {
            const types = {
              'day': '每天',
              'week': '每周',
              'month': '每月',
              'season': '每季',
              'year': '每年'
            }

            data.list.forEach((v, i) => {
              v.goods_type_text = GOODS_TYPE[v.goods_type]; // 商品类型
              v.volume_text = `${v.goods_volume}/${v.last_volume}`; // 库存总量/剩余
              v.status_text = v.status == 1 ? '启用' : '禁用'; // 状态
              // 处理兑换限制说明文本
              let text = '无限制';
              if (v.barter_limit == 1 && v.barter_limit_rule) { // barter_limit 0不限制 1限制, barter_limit_rule限制规则
                v.barter_limit_rule = JSON.parse( v.barter_limit_rule.replace(/&quot;/g, '"'));
                const rule = v.barter_limit_rule;
                const limit = rule.type == 1 ? '累积' : types[Object.keys(rule.rule)[0]];
                const num = rule.type == 1 ? rule.rule : Object.values(rule.rule)[0];
                text = `${limit}${num}件`;
              }
              v.barter_limit_rule_text = text;
            });
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    // 获取积分兑换记录权限，从积分兑换列表接口
    getExchangePointAuth() {
      this.$service
        .post('/Web/PointGoods/exchange_list', {page_no: 1, page_size: 1})
        .then(res => {
          // 40014 没有兑换记录权限
          if (res.data.errorcode == 0) {
            this.exchangePointAuth = true
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleShowExchangeModal(isShow) {
      this.showExchangeModal = isShow;
      !isShow && (this.num = null);
    },
    handleConfirmExchange() {
      // if (xx) return this.$Message.info('魅力值已经兑换完毕')
      if (this.num == null) return this.$Message.error('请输入魅力值')

      this.$Modal.confirm({
          title: '一经确认兑换，操作不能再撤销',
          okText: '兑换',
          onOk: () => {
            const params = {
              num: this.num,
              loading: true
            }
            this.$service.post('/Web/Point/CharmValueTurnPoint', params).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.handleShowExchangeModal(false)
              } else {
                this.$Message.error(res.data.errormsg);
              }
            }).catch(err => {
              this.$Message.error(err);
            })
          }
      });
    },
  }
};
</script>

<style lang="less" scoped>
.brand-box {
  width: 365px;
  position: absolute;
  right: 55px;
  top: 0;
  padding: 115px 15px 15px;
  height: 100%;
  max-height: 100%;
  box-sizing: border-box;
  background: #fff;
  z-index: 4;
  box-shadow: -3px 0 3px rgba(0, 0, 0, 0.15);
  .brand-main {
    width: 100%;
    height: 100%;
    position: relative;
    overflow-y: auto;
  }
  .close {
    position: absolute;
    left: -16px;
    top: 50%;
    cursor: pointer;
    transform: translateY(-50%);
  }

  .tips {
    color: #434343;
    font-size: 14px;
    margin: 15px 0;
    span {
      color: #3976db;
    }
  }
}
.side-right {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background: #1c2b36;
  width: 55px;
  min-height: 500px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 16px;
  color: #fff;

  .con {
    width: 55px;
    text-align: center;
    padding: 20px 13.5px;
    position: relative;
    &::after {
      display: block;
      width: 26px;
      height: 1px;
      content: ' ';
      background-color: #3999db;
      position: absolute;
      left: 50%;
      margin-left: -13px;
      bottom: 0;
    }
    &:last-child::after {
      display: none;
    }
  }
  .cursor {
    cursor: pointer;
  }
  .sign-ico::before {
    display: block;
    width: 26px;
    height: 26px;
    content: ' ';
    margin-bottom: 10px;
    background: url('../../assets/img/signin_ico.png') no-repeat;
  }

  .isactive {
    color: #788289;
    &::before {
      background-position: 0 -75px;
    }
  }

  .ico-singnin::before {
    background-position: 0 -38px;
  }

  .badge {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 40px;
    left: 30px;
    background: #ff0000;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 20px;
  }
}
.scanqr {
  z-index: 9;
  position: absolute;
  top: 50%;
  right: 55px;
  width: 360px;
  height: 100px;
  border-radius: 10px 0 0 10px;
  background: #1c2b36;
  transform: translateY(-50%);
}

.scanqr .scanwords {
  width: 228px;
  display: block;
  float: left;
}

.scanwords .lineone {
  font-family: 微软雅黑;
  font-size: 24px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  margin: 26px 0 0;
}

.scanwords .linetwo {
  font-family: 微软雅黑;
  font-size: 14px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  margin: 12px 0 0;
}
.detailurl {
  float: right;
  display: block;
  width: 110px;
  height: 40px;
  font-size: 13px;
  color: #fff;
  text-align: center;
  border: 1px solid #5fb75d;
  border-radius: 4px;
  line-height: 40px;
  margin: 30px 0 0;
  cursor: pointer;
  margin-right: 20px;
}
.newsign-lang {
  overflow: hidden;
  clear: both;
  /*padding: 0 30px;*/
  background: #fff;
  border: 1px solid #eee;
  border-top: none;
  background: #fff;
  margin-bottom: 40px;
  /*min-height: 100px;*/
}
.newsign-lang-b {
  table {
    width: 100%;
    margin: 0 auto;
    font-size: 14px;
    color: #313131;
    td,
    th {
      height: 30px;
      font-weight: normal;
      text-align: center;
      word-break: break-all;
      .red {
        color: #e60012;
      }
      .green {
        color: #5fb75d;
      }
    }
  }
}
.newsign-lang-t {
  width: 100%;
  background: #fff;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  border: 1px solid #eee;
  text-align: center;
  border-left: none;
  border-right: none;
  font-size: 14px;
  font-weight: bold;
}
.num-box {
  overflow-y: auto;
  span {
    display: inline-block;
    color: #636363;
    font-size: 12px;
    margin-right: 5px;
    padding: 5px 12px;
    border: 1px solid #bfbfbf;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      background-color: #51a4ea;
      color: #fff;
      border-color: #51a4ea;
    }
  }
}
.voice-wrap {
  position: absolute;
  top: 15px;
  left: 135px;
  span {
    vertical-align: middle;
  }
}
.ivu-switch-checked {
  border-color: #5fb75d;
  background-color: #5fb75d;
}

.set-form .min-input {
  width: 50px;
  margin-right: 5px;
}
.set-form .ivu-switch {
  margin-right: 10px;
}

.exchange-modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
  .input-row {
    display: flex;
    align-items: center;

    .input-number {
      width: 100px;
      font-size: 14px;
    }
    .point-text {
      font-size: 22px;
      font-weight: bold;
    }
  }

}
</style>
