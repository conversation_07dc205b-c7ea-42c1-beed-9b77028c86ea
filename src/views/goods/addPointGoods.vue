<template>
  <div class="container">
    <header>
      <h3>{{ id ? '商品编辑' : '商品添加' }}</h3>
    </header>
    <Form
      ref="form"
      label-position="right"
      :model="formItem"
      :rules="formRules"
      class="form"
      :label-width="180">
      <FormItem prop="goods_image">
        <div slot="label" class="image-description image-description-required">
          <p class="label">商品图片</p>
          <p class="tip">图片最佳尺寸: 500X500</p>
          <p class="tip">图片推荐大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="photo">
          <img :src="formItem.goods_image">
        </div>
        <Input v-model="formItem.goods_image" style="display:none" />
        <Button @click="showImgUpload=true">更换图片</Button>
      </FormItem>

      <Form-item label="商品类型" prop="goods_type">
        <Select v-model="formItem.goods_type" @on-change="handleChangeType">
          <Option :value="1">体验卡</Option>
          <Option :value="2">体验课</Option>
          <Option :value="3">折扣券</Option>
          <!-- <Option :value="4">金币</Option> -->
          <Option :value="5">其它</Option>
        </Select>
        <div>注：体验卡/课、折扣券、积分兑换后会自动进入发放；其他类型，在用户兑换后需在前台出码兑换</div>
      </Form-item>

      <Form-item v-if="formItem.goods_type != 5" label="商品" prop="goods_id">
        <Select
          v-if="formItem.goods_type == 1"
          v-model="formItem.goods_id"
          label-in-value
          @on-change="cardChange">
          <Option v-for="item in expCardList" :key="item.card_id" :value="item.card_id">{{ item.card_name }}</Option>
        </Select>
        <ExpClassSelect
          v-if="formItem.goods_type == 2"
          v-model="formItem.goods_id"
          @on-change="classChange"
        />
        <BusDiscount
          v-if="formItem.goods_type == 3"
          v-model="formItem.goods_id"
          @on-change="({label}) => { formItem.goods_name = label }"
        />
      </Form-item>

      <FormItem label="商品名称" prop="goods_name">
        <Input
          v-model="formItem.goods_name"
          :maxlength="30"
          placeholder="请填写" />
      </FormItem>
      <FormItem v-if="!id" label="商品库存" prop="goods_volume">
        <Input v-model="formItem.goods_volume" />
      </FormItem>

      <FormItem v-else label="商品库存" prop="last_volume">
        总量{{ formItem.goods_volume }}，剩余
        <Input-number
          v-model="formItem.last_volume"
          :min="0"
          :step="1"
          :precision="0"
          :active-change="false"
          style="margin-left:5px"
          @on-change="handleChangeLastVolume"
        />
      </FormItem>

      <FormItem label="兑换所需积分" prop="point">
        <Input v-model="formItem.point" placeholder="请填写" />
      </FormItem>

      <FormItem
        v-if="[1, 2].includes(formItem.goods_type)"
        label="使用限制"
        prop="use_limit">
        <CheckboxGroup v-model="formItem.use_limit">
          <Checkbox label="1">本人使用</Checkbox>
          <Checkbox label="2">转赠他人</Checkbox>
        </CheckboxGroup>
      </FormItem>

      <FormItem label="兑换限制" required>
        <RadioGroup v-model="formItem.barter_limit">
          <Radio label="0">不限制</Radio>
          <Radio label="1">限制兑换</Radio>
        </RadioGroup>
      </FormItem>

      <FormItem v-if="formItem.barter_limit == '1'">
        <!-- <FormItem prop="barter_limit_rule.user_group" style="margin-bottom: 24px;">
          <Select v-model="formItem.barter_limit_rule.user_group" multiple>
            <Option :value="1">todo</Option>
          </Select>
        </FormItem> -->

        <RadioGroup v-model="formItem.barter_limit_rule.type" @on-change="handleChangeLimitType">
          <div class="exchange-limit-flex" style="margin-bottom: 24px;">
            <Radio label="1">累积兑换限制</Radio>
            <FormItem
              ref="exchangeLimitValue1Ref"
              prop="barter_limit_rule.value_1"
              :rules="{ required: formItem.barter_limit_rule.type == '1', type: 'number', message: '请填写数量', trigger: 'blur' }">
              <Input-number
                v-model="formItem.barter_limit_rule.value_1"
                :disabled="formItem.barter_limit_rule.type != '1'"
                :max="99999"
                :min="1"
                :precision="0"
                :active-change="false" />
            </FormItem>
            &nbsp;件
          </div>
          <div class="exchange-limit-flex">
            <Radio label="2" style="min-width:102px;">按周期限制</Radio>
            <Select
              v-model="formItem.barter_limit_rule.period"
              :disabled="formItem.barter_limit_rule.type != '2'"
              style="margin-right: 16px;width: 100px;">
              <Option value="day">每天</Option>
              <Option value="week">每周</Option>
              <Option value="month">每月</Option>
              <Option value="season">每季度</Option>
              <Option value="year">每年</Option>
            </Select>
            <FormItem
              ref="exchangeLimitValue0Ref"
              prop="barter_limit_rule.value_2"
              :rules="{ required: formItem.barter_limit_rule.type == '2', type: 'number', message: '请填写数量', trigger: 'blur' }">
              <Input-number
                v-model="formItem.barter_limit_rule.value_2"
                :disabled="formItem.barter_limit_rule.type != '2'"
                :max="999999999"
                :min="1"
                :precision="0"
                :active-change="false" />
            </FormItem>
            &nbsp;件
          </div>
        </RadioGroup>
      </FormItem>

      <FormItem label="商品状态">
        <RadioGroup v-model="formItem.status">
          <Radio label="1">启用</Radio>
          <Radio label="0">禁用</Radio>
        </RadioGroup>
      </FormItem>

      <FormItem label="商品说明">
        <Input
          v-model="formItem.remark"
          placeholder="限100个字"
          type="textarea"
          :autosize="{minRows: 4, maxRows: 8}" />
      </FormItem>

      <FormItem>
        <div class="buttons">
          <Button type="success" @click="handleSubmit">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>

    <ImgUploadWithBase v-model="showImgUpload" @on-change="imgChange" />
  </div>
</template>

<script>
  import ImgUploadWithBase from 'components/form/ImgUploadWithBase';
  import ExpClassSelect from 'components/form/ExpClassSelect';
  import BusDiscount from 'components/form/busDiscount';

  export default {
    name: 'AddPointGoods',
    components: {
      ImgUploadWithBase,
      ExpClassSelect,
      BusDiscount
    },

    data() {
      return {
        id: this.$route.params.id,
        formItem: {
          bus_id: this.$store.state.busId,
          goods_name: '', // 商品名称
          goods_type: 1, // 商品类型 1体验卡 2体验课 3折扣券 0其他； !!!魅力值等部分地方是 3体验课 2折扣券 这里要注意
          goods_image: '', // 商品图片
          goods_id: '', // 商品id
          goods_volume: '', // 商品库存 总量
          // last_volume: 0, // 商品库存 剩余
          point: '', // 兑换所需积分
          use_limit: ['1'], // 使用限制 1本人使用 2转赠他人
          barter_limit: '1', // 兑换限制 0不限制 1限制
          barter_limit_rule: {
            // user_group: [],
            type: '1',
            value_1: null, // 累计限制的值
            period: 'day',
            value_2: null, // 周期限制的值
          },
          status: '1',
          remark: ''
        },
        formRules: {
          goods_image: [{ required: true, message: '请选择商品图片' }],
          goods_name: [{ required: true, message: '请填写商品名称' }],
          goods_id: [{ required: true, message: '请选择商品' }],
          goods_volume: [
            { required: true, message: '请正确填写商品库存', type: 'string', pattern: /^\d+$/, trigger: 'blur' }
          ],
          point: [
            { required: true, type: 'string', pattern: /^[1-9]\d*$/, message: '请填写正整数', trigger: 'blur' }
          ],
          use_limit: [
            { required: true, type: 'array', min: 1, message: '至少选择一个', trigger: 'change' },
          ],
          // 'barter_limit_rule.user_group': [
          //   { required: true, type: 'array', min: 1, message: '请选择可兑换人群', trigger: 'change' }
          // ],
        },
        expCardList: [],
        showImgUpload: false,
      };
    },

    created() {
      this.getExpCard();
      if (this.id) {
        this.getGoodsInfo();
      }
    },
    methods: {
      // 获取体验卡列表
      getExpCard() {
        this.$service.get('/Web/Goods/get_experience_list').then(res => {
          if (res.data.errorcode == 0) {
            this.expCardList = res.data.data.list;
          } else {
            this.expCardList = [];
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      // 获取积分商品详情，从列表路由传参
      getGoodsInfo() {
        const { goodsData: data } = this.$route.params;
        if (data) {
        const rule = data.barter_limit_rule;

          this.formItem = {
            id: this.id,
            bus_id: data.bus_id,
            goods_id: data.goods_type == 2 ? +data.goods_id : data.goods_id, // 商品id
            goods_name: data.goods_name, // 商品名称
            goods_type: +data.goods_type, // 商品类型 1体验卡 2体验课 3折扣券 0其他； !!!魅力值等部分地方是 3体验课 2折扣券 这里要注意
            goods_image: data.goods_image, // 商品图片
            goods_volume: data.goods_volume, // 商品库存
            last_volume: +data.last_volume || 0, // 剩余库存
            point: data.point, // 兑换所需积分
            use_limit: data.use_limit ? data.use_limit.split(',') : ['1'], // 使用限制 [1本人使用, 2转赠他人]
            barter_limit: data.barter_limit, // 兑换限制 0不限制 1限制
            barter_limit_rule: data.barter_limit == 1
              ? {
                type: rule.type,
                value_1: rule.type == 1 ? rule.rule : null, // 累计限制的值
                period: rule.type == 2 ? Object.keys(rule.rule)[0] : 'day', // 周期限制的类型 day 天 week周 month 月 season季 year 年
                value_2: rule.type == 2 ? Object.values(rule.rule)[0] : null, // 周期限制的值
              }
              : {
                type: '1',
                value_1: null, // 累计限制的值
                period: 'day',
                value_2: null, // 周期限制的值
              },
            status: data.status,
            remark: data.remark
          }
        }
      },
      // 请求添加/编辑商品
      addGoods(url, params) {
        this.$service.post(url, params).then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.log(err);
        });
      },
      // 处理商品类型变更
      handleChangeType(val) {
        const { formItem } = this
        formItem.goods_id = ''
        switch (val) {
          case 1: // 体验卡
          case 2: // 体验课
            formItem.use_limit = ['1'];
            break;
          case 3: // 折扣券
            formItem.use_limit = ['1', '2'];
            break;
          case 4: // 金币
          case 5: // 其他
            formItem.use_limit = ['1'];
            break;
        }
      },
      // 处理剩余库存变更后的总库存
      handleChangeLastVolume(val) {
        const { goods_volume, last_volume } = this.$route.params.goodsData;
        let cha = val - last_volume;
        this.formItem.goods_volume = parseInt(goods_volume) + cha;
      },

      cardChange(res) {
        this.formItem.goods_id = res.value;
        this.formItem.goods_name = res.label;
      },
      classChange(res) {
        this.formItem.goods_id = res.card_id;
        this.formItem.goods_name = res.card_name;
      },
      imgChange(path) {
        this.formItem.goods_image = path;
      },
      handleChangeLimitType() {
        const {
          exchangeLimitValue1Ref: ref_1,
          exchangeLimitValue0Ref: ref_0
        } = this.$refs;
        ref_1 && ref_1.resetField();
        ref_0 && ref_0.resetField();
      },
      handleSubmit() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            return this.$Message.error('请完成信息填写');
          }

          const { id, formItem } = this;
          const url = id ? '/Web/PointGoods/editPointGoods' : '/Web/PointGoods/addPointGoods';
          const {
            goods_name,
            goods_volume,
            use_limit,
            barter_limit: limit,
            barter_limit_rule: rule,
            ...rest
          } = formItem;

          const params = {
            ...rest,
            goods_name: goods_name.trim(),
            // [id ? 'last_volume' : 'goods_volume']: goods_volume,
            goods_volume,
            use_limit: use_limit.join(),
            barter_limit: limit, // 兑换限制 0不限制 1限制
            barter_limit_rule: limit == 1
              ? JSON.stringify({
                type: rule.type,
                rule: rule.type == 1 // 1累计限制 2周期限制   累计限制rule直接传值，周期限制 rule传对象key是周期类型，value是值
                  ? rule.value_1
                  : { [rule.period]: rule.value_2 } // rule.period， day 天 week周 month季 season季 year 年
              })
              : '',
          }

          this.addGoods(url, params)
        });

      }
    }
  };
</script>
<style lang="less">
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .exchange-limit-flex {
    display: flex;
    align-items: center;
  }

  .photo {
    .flex-center;
    border: 1px solid #dcdcdc;
    width: 200px;
    height: 200px;
    box-sizing: border-box;
    padding: 2px;
    margin-bottom: 20px;
    > img {
      width: 100%;
      height: 100%;
    }
  }

  .container .form {
    padding-bottom: 44px;
  }
  .goods-card {
    margin-top: 15px;
    font-size: 12px;

    ul {
      li {
        float: left;
        width: 50%;
        color: #999;
      }
    }
    .bot {
      color: red;
    }
    .ivu-card-head {
      padding-top: 8px;
      padding-bottom: 8px;
      p {
        font-size: 12px;
        font-weight: normal;
      }
    }
  }
</style>
