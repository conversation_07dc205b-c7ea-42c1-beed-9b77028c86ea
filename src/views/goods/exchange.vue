<template>
  <div>
    <div class="table-wrap">
      <header>
        <DatePickerWithButton
          :days.sync="days"
          clearable
          @on-change="dateChanged" />
        <Select
          v-model="postData.goods_id"
          class="w120"
          placeholder="选择商品"
          filterable
          clearable>
          <Option v-for="item in goodsList" :key="item.goods_id" :value="item.goods_id">{{ item.goods_name }}</Option>
        </Select>
        <Input
          v-model="postData.search"
          class="w120"
          placeholder="请输入手机号码"
          @on-enter="search" />
        <Button type="success" class="search" @click="search">搜索</Button>
      </header>

      <main>
        <Table
          ref="table"
          :columns="columns"
          :data="tableData"
          stripe
          disabled-hover />
      </main>

      <footer>
        <Button type="success" style="margin-right:30px" @click="goAdd">兑换商品</Button>
        <Page
          class="page"
          :total="totalCount"
          :page-size="postData.page_size"
          :current.sync="postData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="pageChanged"
          @on-page-size-change="pageSizeChanged" />
      </footer>
    </div>

    <Modal
      v-model="showExchangeModal"
      title="兑换商品"
      width="600"
      class="sign_modal"
      :mask-closable="false"
      :transfer="false"
      @on-cancel="closeExchangeCallBack">
      <Select
        v-model="selectedUserId"
        filterable
        clearable
        remote
        :remote-method="getUserExchangeInfo"
        :loading="searching"
        placeholder="用户姓名、昵称、电话">
        <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">{{ user.username }}</Option>
      </Select>

      <div v-if="userInfo" class="user-box">
        <div class="flex-center">
          <img class="user-avatar" :src="userInfo.avatar">
          <div>
            <h4 class="mb10">{{ userInfo.username }}</h4>
            <p>{{ userInfo.phone }}</p>
          </div>
        </div>
        <div>
          可用魅力值 <span class="userinfo-txt">{{ userInfo.charm_value }}</span>
        </div>
      </div>

      <div v-if="userInfo" class="goods-list">
        <div>
          <div v-if="userInfo.goods_list && userInfo.goods_list.length>0">
            <div>
              <div style="padding-top: 10px">
                <RadioGroup v-model="modalData.goods_id">
                  <Radio v-for="goods in userInfo.goods_list" :key="goods.goods_id" :label="goods.goods_id">
                    <h5>{{ goods.goods_name }}</h5>
                    <p>需要魅力值 {{ goods.single_price }}</p>
                  </Radio>
                </RadioGroup>
              </div>
            </div>
          </div>
          <p v-else class="nodata">
            暂无可兑换商品信息
          </p>
        </div>
      </div>
      <Input
        v-if="userInfo && userInfo.goods_list && userInfo.goods_list.length>0"
        v-model="modalData.remark"
        type="textarea"
        :autosize="{minRows: 4,maxRows: 5}"
        placeholder="备注（可不填）" />

      <div slot="footer" class="modal-buttons">
        <Button type="success" :disabled="!modalData.user_id" @click="handleConfirmExchange">兑换商品</Button>
        <Button @click="closeExchangeCallBack">取消</Button>
      </div>

      <Modal
        v-model="showVerifySmsModal"
        title="请输入会员收到的短信验证码"
        :closable="false"
        :mask-closable="false">
        <i-input v-model="verifyCode" placeholder="短信验证码" @on-enter="handleConfirmSuccess">
          <Button slot="append" :disabled="!!verifySec" @click="handleResendVerifyCode">{{ resendVerifyCode }}</Button>
        </i-input>
        <div slot="footer" style="text-align:center;">
          <div>
            <Button @click="handleShutDownOrder(false)">关闭</Button>
            <Button type="success" @click="handleConfirmSuccess">确认</Button>
          </div>
          <div style="margin-top:16px;">
            <span style="color:#AAA;">5分钟后未确认会取消该订单</span>
          </div>
        </div>
      </Modal>
    </Modal>
  </div>
</template>

<script>
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';

let countDownTimer = null;
let closeTimer = null;

export default {
  name: 'GoodsExchange',
  components: {
    DatePickerWithButton
  },

  data() {
    return {
      totalCount: 0,
      days: [Date.now()-365 * 24 * 60 * 60 * 1000, Date.now()],
      tableData: [],
      userList: [],
      goodsList: [],
      postData: {
        s_date: '',
        e_date: '',
        search: '',
        goods_id: this.$route.query.goodsId,
        page_size: 10,
        page_no: 1
      },
      modalData: {
        user_id: '',
        goods_id: '',
        remark: ''
      },
      selectedUserId: '',
      userInfo: '',
      showExchangeModal: false,
      needverifyCode: false, // 是否需要验证码
      showVerifySmsModal: false, // 输入验证码弹窗
      verifyCode: undefined, // 验证码
      verifySec: 30,
      searching: false,
      columns: [
        {
          title: '兑换时间',
          key: 'create_time'
        },
        {
          title: '兑换商品',
          key: 'goods_name'
        },
        {
          title: '兑换人姓名',
          key: 'username'
        },
        {
          title: '兑换人电话',
          key: 'phone'
        },
        {
          title: '体验卡转赠接收人',
          key: 'recipient_username'
        },
        {
          title: '备注',
          key: 'remark'
        }
      ]
    };
  },
  computed: {
    resendVerifyCode() {
      return this.verifySec > 0 ? `再次发送(${this.verifySec}s)` : '再次发送';
    },
  },

  watch: {
    selectedUserId(val) {
      if (!val) {
        this.userInfo = null
        return
      }
      let selectedInfo = this.userList.filter(user => {
        return user.user_id === val
      })
      this.userInfo = selectedInfo[0];
      this.modalData.user_id = val
    }
  },

  created() {
    this.getGoodsList();
    this.getList();
  },

  methods: {
    closeExchangeCallBack() {
      this.userInfo = null;
      this.selectedUserId = '';
      this.modalData.user_id = '';
      this.modalData.remark = '';
      this.modalData.goods_id = this.goodsList && this.goodsList[0].goods_id;
      this.showExchangeModal = false;
    },
    goAdd() {
      this.showExchangeModal = true;
    },
    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/Exchange/exchange_list', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getGoodsList() {
      this.$service
        .post('/Web/Goods/all_goods_list')
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.goodsList = data.list;
            this.modalData.goods_id = data.list[0].goods_id;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getUserExchangeInfo(search) {
      if (search !== '') {
        this.searching = true;
        this.$service
        .post('/Web/Exchange/search_user', {search})
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.userList = data.list;
            this.searching = false;
          } else {
            this.searching = false;
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          this.searching = false;
          console.error(err);
        });
      }
    },

    handleShutDownOrder(isAuto) {
      this.$service.post(
        '/Web/Commodity/close_commodity_settlement',
        {  }
      ).then(res => {
        if (res.data.errorcode == 0) {
          this.handleShowVerifyModal();

          clearTimeout(countDownTimer)
          countDownTimer = null;
          clearTimeout(closeTimer)
          closeTimer = null;

          if (isAuto) {
            this.handleMachineRes('已自动关闭订单')
          } else {
            this.$Message.success(res.data.errormsg)
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 兑换请求
    addExchange() {
      this.$service
        .post('/Web/Exchange/exchange_goods', this.modalData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.closeExchangeCallBack();
            this.$Message.success(res.data.errormsg);
          }else{
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 发送验证码
    handleResendVerifyCode() {
      if (this.verifySec > 0) return;

      return this.$service.post('/Web/Commodity/commodity_settlement_send_msg', { pre_id: this.currentPreId }).then(res => {
        if (Number(res.data.errorcode) == 0) {
          this.$Message.success(res.data.errormsg)
          this.handleSendCountDown()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },

    // 兑换确认
    handleConfirmExchange() {
      // 判断是否需要验证 验证码
      if (this.needverifyCode) {
        this.handleShowVerifyModal(true)
      } else {
        this.addExchange() // 请求
      }
    },

    handleConfirmSuccess() {
      clearTimeout(closeTimer)
      closeTimer = null;

      const params= {
        loading: true,
      }

      this.$service.post('/Web/Commodity/submit_commodity_settlement', params).then(res => {
        if (res.data.errorcode == 0) {
          this.handleShowVerifyModal()
          this.closeExchangeCallBack()
          this.$Message.success(res.data.errormsg)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    },

    handleShowVerifyModal(isShow = false) {
      this.showVerifySmsModal = isShow;
      if (isShow) {
        this.handleAutoShutDownSms()
        this.handleSendCountDown()
      } else {
        this.verifyCode = undefined;
      }
    },

    handleAutoShutDownSms() {
      closeTimer = setTimeout(() => {
        this.handleShutDownOrder(true)
      }, 5 * 60 * 1000)
      // }, 10 * 1000)
    },
    handleSendCountDown() {
      this.verifySec = 30;
      countDownTimer = setInterval(() => {
        this.verifySec--;
        if (this.verifySec === 0) {
          clearInterval(countDownTimer)
          countDownTimer = null;
        }
      }, 1000)
    },

  }
};
</script>

<style>
.goods-list .ivu-radio{
  position: absolute;
  right: 0;
  bottom: 15px;
}
</style>

<style lang="less" scoped>
.flex-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.user-box {
  padding: 10px;
  .flex-center;
  margin-top: 20px;
  border: 1px solid #dddee1;
  .user-avatar{
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
  }
}
.userinfo-txt{
  font-size: 16px;
  color: #d8321f;;
}
.mb10 {
  margin-bottom: 10px;
}
.goods-list{
  margin-bottom: 20px;
  .nodata{
    line-height: 50px;
    text-align: center;
    margin-top: 10px;
  }
  .ivu-radio-wrapper{
    width: 140px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(95, 183, 93);
    padding: 15px 30px 15px 10px;
    margin-right: 10px;
    position: relative;
    h5 {
      margin-bottom: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.exchange-glod-input-row {
  margin-bottom: 20px;
  .ivu-input-number {
    width: 100%;
  }
}
</style>
