<template>
  <div class="table-wrap">
    <header>
      <DatePickerWithButton :days.sync="days" :clearable="true" @on-change="dateChanged" />
      <Select
        v-model="postData.goods_type"
        class="w120"
        filterable
        placeholder="商品类型">
        <Option value="">商品类型</Option>
        <Option value="1">体验卡</Option>
        <Option value="3">体验课</Option>
        <Option value="2">折扣券</Option>
        <Option value="0">其它</Option>
      </Select>
      <Select
        v-model="postData.status"
        class="w120"
        filterable
        placeholder="状态">
        <Option value="">状态</Option>
        <Option value="0">禁用</Option>
        <Option value="1">启用</Option>
      </Select>
      <Input
        v-model="postData.search"
        class="w120"
        placeholder="商品名称"
        @on-enter="search" />
      <Button type="success" class="search" @click="search">搜索</Button>

      <Button
        type="text"
        style="margin-left: auto;"
        :title="exchangeCharmAuth ? null : '没有兑换记录权限'"
        :disabled="!exchangeCharmAuth"
        @click="$router.push('/goods/exchange')">
        兑换记录
      </Button>
    </header>
    <main>
      <Table
        ref="table"
        class="avatar-zoom"
        :columns="columns"
        :data="tableData"
        stripe
        disabled-hover />
    </main>
    <footer>
      <Button type="success" style="margin-right:30px" @click="goAdd">添加商品</Button>
      <Page
        class="page"
        :total="totalCount"
        :page-size="postData.page_size"
        :current.sync="postData.page_no"
        placement="top"
        show-total
        show-sizer
        @on-change="pageChanged"
        @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';

export default {
  name: 'CharmList',
  components: {
    DatePickerWithButton
  },

  data() {
    return {
      totalCount: 0,
      days: [Date.now() - 365 * 24 * 60 * 60 * 1000, Date.now()],
      tableData: [],
      cardList: [],
      postData: {
        s_date: '',
        e_date: '',
        search: '',
        goods_type: '',
        status: '',
        page_size: 10,
        page_no: 1
      },
      columns: [
        {
          title: '商品名称',
          key: 'goods_name',
          ellipsis: true
        },
        {
          title: '商品类型',
          key: 'goods_type',
          render: (h, params) => {
            return h('span', params.row.goods_type == 1 ? '体验卡' : params.row.goods_type == 2 ? '折扣券' : params.row.goods_type == 3 ? '体验课' : '其它');
          }
        },
        {
          title: '单价（魅力值）',
          key: 'single_price'
        },
        {
          title: '库存总量/剩余',
          key: 'class_name',
          render: (h, params) => {
            return h('span', `${params.row.total_volume}/${params.row.last_volume}`);
          }
        },
        {
          title: '创建时间',
          key: 'create_time'
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', params.row.status == 1 ? '启用' : '禁用');
          }
        },
        {
          title: '操作',
          width: 210,
          render: (h, params) => {
            return (
              <div>
                <i-button
                  class="mr5"
                  type="text"
                  onClick={() => {
                    this.$router.push({
                      path: '/goods/add',
                      name: '商品添加',
                      params: {
                        goodsId: params.row.goods_id,
                      }
                    });
                  }}>
                  编辑
                </i-button>
                <i-button
                  class="mr5"
                  type="text"
                  title={this.exchangeCharmAuth ? null : '没有兑换记录权限'}
                  disabled={!this.exchangeCharmAuth}
                  onClick={() => {
                    this.$router.push(`/goods/exchange/?goodsId=${params.row.goods_id}`);
                  }}>
                  兑换记录
                </i-button>
                <i-button
                type="text"
                  style="color: #ff696a;"
                  onClick={() => {
                    this.handleDelete(params.row.goods_id);
                  }}>
                  删除
                </i-button>
              </div>
            );
          }
        }
      ],
      exchangeCharmAuth: false, // 魅力值兑换记录权限
    };
  },

  created() {
    this.getList()
    this.getExchangeCharmAuth()
  },

  methods: {
    handleDelete(delId) {
      let _this = this;
      this.$Modal.confirm({
        title: '',
        content: '确定要删除吗？',
        onOk() {
          this.$service.post('/Web/Goods/delete_goods', { goods_id: delId }).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              _this.getList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    },
    goAdd() {
      this.$router.push('/goods/add');
    },

    dateChanged(val) {
      this.postData.s_date = val[0];
      this.postData.e_date = val[1];
    },
    pageChanged(page) {
      this.postData.page_no = page;
      this.getList();
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service
        .post('/Web/Goods/goods_list', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let data = res.data.data;
            this.tableData = data.list;
            this.totalCount = parseInt(data.count);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    // 获取魅力值兑换记录权限，从魅力值兑换列表接口
    getExchangeCharmAuth() {
      this.$service
        .post('/Web/Exchange/exchange_list', {page_size: 1, page_no: 1})
        .then(res => {
          // 40014 没有兑换记录权限
          if (res.data.errorcode == 0) {
            this.exchangeCharmAuth = true
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
  }
};
</script>

<style lang="less" scoped>
.brand-box {
  width: 365px;
  position: absolute;
  right: 55px;
  top: 0;
  padding: 115px 15px 15px;
  height: 100%;
  max-height: 100%;
  box-sizing: border-box;
  background: #fff;
  z-index: 4;
  box-shadow: -3px 0 3px rgba(0, 0, 0, 0.15);
  .brand-main {
    width: 100%;
    height: 100%;
    position: relative;
    overflow-y: auto;
  }
  .close {
    position: absolute;
    left: -16px;
    top: 50%;
    cursor: pointer;
    transform: translateY(-50%);
  }

  .tips {
    color: #434343;
    font-size: 14px;
    margin: 15px 0;
    span {
      color: #3976db;
    }
  }
}
.side-right {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background: #1c2b36;
  width: 55px;
  min-height: 500px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 16px;
  color: #fff;

  .con {
    width: 55px;
    text-align: center;
    padding: 20px 13.5px;
    position: relative;
    &::after {
      display: block;
      width: 26px;
      height: 1px;
      content: ' ';
      background-color: #3999db;
      position: absolute;
      left: 50%;
      margin-left: -13px;
      bottom: 0;
    }
    &:last-child::after {
      display: none;
    }
  }
  .cursor {
    cursor: pointer;
  }
  .sign-ico::before {
    display: block;
    width: 26px;
    height: 26px;
    content: ' ';
    margin-bottom: 10px;
    background: url('../../assets/img/signin_ico.png') no-repeat;
  }

  .isactive {
    color: #788289;
    &::before {
      background-position: 0 -75px;
    }
  }

  .ico-singnin::before {
    background-position: 0 -38px;
  }

  .badge {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 40px;
    left: 30px;
    background: #ff0000;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 20px;
  }
}
.scanqr {
  z-index: 9;
  position: absolute;
  top: 50%;
  right: 55px;
  width: 360px;
  height: 100px;
  border-radius: 10px 0 0 10px;
  background: #1c2b36;
  transform: translateY(-50%);
}

.scanqr .scanwords {
  width: 228px;
  display: block;
  float: left;
}

.scanwords .lineone {
  font-family: 微软雅黑;
  font-size: 24px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  margin: 26px 0 0;
}

.scanwords .linetwo {
  font-family: 微软雅黑;
  font-size: 14px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  margin: 12px 0 0;
}
.detailurl {
  float: right;
  display: block;
  width: 110px;
  height: 40px;
  font-size: 13px;
  color: #fff;
  text-align: center;
  border: 1px solid #5fb75d;
  border-radius: 4px;
  line-height: 40px;
  margin: 30px 0 0;
  cursor: pointer;
  margin-right: 20px;
}
.newsign-lang {
  overflow: hidden;
  clear: both;
  /*padding: 0 30px;*/
  background: #fff;
  border: 1px solid #eee;
  border-top: none;
  background: #fff;
  margin-bottom: 40px;
  /*min-height: 100px;*/
}
.newsign-lang-b {
  table {
    width: 100%;
    margin: 0 auto;
    font-size: 14px;
    color: #313131;
    td,
    th {
      height: 30px;
      font-weight: normal;
      text-align: center;
      word-break: break-all;
      .red {
        color: #e60012;
      }
      .green {
        color: #5fb75d;
      }
    }
  }
}
.newsign-lang-t {
  width: 100%;
  background: #fff;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  border: 1px solid #eee;
  text-align: center;
  border-left: none;
  border-right: none;
  font-size: 14px;
  font-weight: bold;
}
.num-box {
  overflow-y: auto;
  span {
    display: inline-block;
    color: #636363;
    font-size: 12px;
    margin-right: 5px;
    padding: 5px 12px;
    border: 1px solid #bfbfbf;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      background-color: #51a4ea;
      color: #fff;
      border-color: #51a4ea;
    }
  }
}
.voice-wrap {
  position: absolute;
  top: 15px;
  left: 135px;
  span {
    vertical-align: middle;
  }
}
.ivu-switch-checked {
  border-color: #5fb75d;
  background-color: #5fb75d;
}

.set-form .min-input {
  width: 50px;
  margin-right: 5px;
}
.set-form .ivu-switch {
  margin-right: 10px;
}

</style>
