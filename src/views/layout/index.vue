<template>
  <div class="app-wrapper">
    <sidebar v-if="!isPoweredByQIANKUN && !$route.meta.hideSidebar" />
    <div class="con-wrapper">
      <div v-if="$store.state.adminInfo.is_trial_version" class="top-layer" @click="showTips"></div>
      <header-Top  v-if="!isPoweredByQIANKUN && !$route.meta.hideHeader" />
      <bread-crumb v-if="!$route.meta.hideBread" />
      <app-Main v-if="busId" id="vipMainCon" />
    </div>
    <QrSignNotice />
    <template v-if="modalList && modalList.length">
    <Modal v-for="(info, index) in modalList" :key="info.id" :title="info.title" width="800" v-model="showModalNotice[index]" :mask-closable="false">
      <div class="ql-snow">
       <div class="modal-notice ql-editor" v-html="info.content"></div>
      </div>
      <div slot="footer"></div>
    </Modal>
  </template>
  </div>
</template>
<script>
  import headerTop from 'views/layout/headerTop';
  import sidebar from 'views/layout/sidebar';
  import appMain from 'views/layout/appMain';
  import breadCrumb from './breadCrumb.vue';
  import { mapActions, mapState } from 'vuex'
  import { formatDate, dateDiff } from 'utils/index.js'
  import QrSignNotice from 'components/member/qrSignNotice';

  export default {
    name: 'layOut',
    components: {
      headerTop,
      sidebar,
      appMain,
      QrSignNotice,
      breadCrumb
    },
    computed: {
      ...mapState(['adminInfo', 'busName', 'busId']),
    },
    // watch: {
    //   'adminInfo.expire_date'(val, oldVal) {
    //     if (val != oldVal) {
    //       let cha = dateDiff(val, formatDate(new Date(), 'yyyy-MM-dd'))
    //       if (cha <= 30) {
    //         this.$Notice.warning({
    //           title: "到期提醒",
    //           duration: 0,
    //           desc: `系统将于${val}到期，请及时续费以免影响使用`,
    //           onClose() {
    //           }
    //         })
    //       }
    //     }
    //   },
    //   'adminInfo.bus_no_money'(val, oldVal) {
    //     if (val !== oldVal && Number(val) > 0) {
    //       this.$Notice.warning({
    //         title: "缴费提醒",
    //         duration: 0,
    //         desc: `当前欠费${val}元，请及时缴费以免影响使用`,
    //         onClose() {
    //         }
    //       })
    //     }
    //   },
    // },
    data() {
      return {
        hasShowTrialVersion: false,
        modalList: [],
        showModalNotice: [false],
        isPoweredByQIANKUN: window.__POWERED_BY_QIANKUN__ || false,
      }
    },
    created() {
      this.getBusInfo().then(() => {
        document.title = `${this.busName}_${this.$route.name}`
      })
      if(!(sessionStorage.getItem('hasPopupNotice')==='true')) {
        this.getPopupNotice()
        sessionStorage.setItem('hasPopupNotice', true)
      }
      this.getAdminInfo().then(res => {
        this.setPayTypesInit()
      })
      this.getFinanceDays()
      this.getReceiptAuth()
    },
    methods: {
      ...mapActions('pay', ['setPayTypesInit']),
      ...mapActions(['getAdminInfo', 'getReceiptAuth', 'getBusInfo']),
      // 运营后台发布的弹窗通知
       getPopupNotice() {
        this.$service
          .post('/web/PopupNotice/get_bus_popup_notice_list')
          .then(res => {
              if (res.data.errorcode == 0) {
                const modalList = res.data.data.list
                if(Array.isArray(modalList) && modalList.length) {
                  Promise.all([import('quill/dist/quill.core.css'), import('quill/dist/quill.snow.css')]).then(() => {
                    this.modalList = modalList
                    this.showModalNotice = Array(modalList.length).fill(true)
                  })
                } else {
                  this.modalList = []
                }
              }
          })
      },
      // 对应角色在财务板块查询时，日期范围要根据角色权限中的设定来限制
      getFinanceDays() {
        this.$service
          .post('/web/admin/check_days')
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$store.commit('SET_FINACE_CHECK_DAYS', res.data.data.check_days)
            }
          })
      },
      showTips() {
        if(this.hasShowTrialVersion) {
          this.$Notice.close('is_trial_version')
        }
        this.hasShowTrialVersion = true
        this.$Notice.warning({
          title: "版本提醒",
          duration: 0,
          name: 'is_trial_version',
          desc: '暂无权限，请联系商务人员开通！联系电话：4001-6072-66',
          onClose() {
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .app-wrapper {
    display: flex;
    height: 100%;
    width: 100%;
  }

  .con-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .top-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 901;
  }
</style>
