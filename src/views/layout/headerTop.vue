<template>
<div>
  <div class="header-top">
    <div class="search-box">
      <img @click="handleSearch" src="../../assets/img/search.png" alt="搜索会员" title="搜索">
      <input v-model="search" placeholder="会员名/电话/卡号/身份证" class="search" @keydown.enter="handleSearch" />
    </div>
    <div class="right">
      <div class="icons">
        <div class="hover-icon" @click="$router.push('/stat/menus/exportLogTable')">
          <Tooltip content="下载中心">
            <Icon type="md-download" size="27" color="rgb(178 187 206)" />
          </Tooltip>
        </div>
        <div class="hover-icon" @click="goMsgCenter">
          <div class="info-wrap">
            <Badge :count="+socketMsgCount" v-if="socketMsgCount"></Badge>
            <img src="../../assets/img/header-bell.png" alt="消息中心">
          </div>
        </div>
        <Poptip trigger="hover" placement="bottom" class="hover-icon" width="300" @on-popper-show="showCode = true">
          <img src="../../assets/img/header-phone.png" alt="使用手机端">
          <div slot="content" v-if="showCode">
            <div class="sign-help">
              <p class="help-title">微信扫一扫，即可使用手机端</p>
              <div class="url-wrap">
                <a class="signurl" :href="helpUrl">
                  <p class="help-text">使用帮助</p>
                  <p class="help-ico"></p>
                </a>
              </div>
              <ul>
                <li class="sign-imgbox" v-if="$store.state.is_qn_j == 1">
                  <img class="sign-img" :src="busQrCodeTwo">
                  <p class="sign-bottomtext">会员端</p>
                </li>
                <li class="sign-imgbox" v-else>
                  <img class="sign-img" :src="busQrCode">
                  <p class="sign-bottomtext">会员端</p>
                </li>
              </ul>
            </div>
          </div>
        </Poptip>

      </div>
      <div class="switch" @click="handleSwitchBus">
        <img src="../../assets/img/switch-bus-icon.png" alt="">
        <span>{{busName}}</span>
        <img src="../../assets/img/dropdown-white.png" alt="">
      </div>
      <Poptip placement="bottom" trigger="hover" class="user" width="250">
        <div class="user">
          <img src="../../assets/img/login-user.png" alt="">
          <span>{{adminName}}</span>
          <img src="../../assets/img/dropdown-blue.png" alt="">
        </div>
        <div class="admin-pop" slot="content">
          <div class="thumb">
            <img :src="adminInfo.logo || 'https://imagecdn.rocketbird.cn/default/business_logo.jpg'">
            <span>{{adminName}}</span>
          </div>
          <ul class="info">
            <li>
              <span>类型：</span>
              <p class="user-type">{{adminInfo.version}}</p>
            </li>
            <li>
              <span>到期：</span>
              <p class="end-date">{{adminInfo.expire_date}}</p>
            </li>
            <li>
              <span>短信：</span>
              <p class="message">剩余
                <span style="color: #ff696a">{{adminInfo.sms_number}}</span>条
                <Button type="text" @click="showBuySms = true">充值</Button>
              </p>
            </li>
            <li class="more-wrap">
              <span>电子<br/>合同：</span>
              <div>
                <p>{{adminInfo.esign_status==1?'已开通':'未开通'}}{{adminInfo.esign_status!==1?'':adminInfo.esign_vip_status==1?'-使用中':'-暂停使用'}}</p>
                <p v-if="adminInfo.esign_status==1">余额 <span style="color: #ff696a">{{adminInfo.contract_number}}</span></p>
                <!-- <p v-if="adminInfo.esign_status==1 && !!adminInfo.contract_expiry_date">{{adminInfo.contract_expiry_date}}到期</p> -->
              </div>
              <Button type="text" @click="showBuyElectronic = true" v-if="adminInfo.esign_status==1">充值</Button>
              <router-link :to="{name: '电子合同签署流程'}" v-else>
                <Button type="text">开通</Button>
              </router-link>
            </li>
          </ul>
          <div class="bottom">
            <Button type="text" @click="editPwd">修改密码</Button>
            <span class="line"></span>
            <Button type="text" @click="clickLogout">退出登录</Button>
          </div>
        </div>
      </Poptip>
    </div>

    <Modal v-model="switchBusModal" title="场馆选择" width="555">
      <div class="search-bus">
        <Input v-model="searchBusName" placeholder="请输入场馆名称..." width="333"></Input>
      </div>
      <div style="height: 333px;">
        <div class="list-bus">
          <div class="tag" :class="bus.version_name === '试用版' || bus.version_expired ? 'hastag' : ''" v-for="bus in filterBusName" :key="bus.id" @click="handleSwitchBusTag(bus)">
              <div class="name-wrap" :title="bus.name">
                {{bus.name}}
              </div>
              <span class="version-tag" v-if="bus.version_name === '试用版'">试</span>
              <span class="version-tag tag-red" v-if="bus.version_expired">过</span>
          </div>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal title="短信购买" width="800" class="buy-sms" v-model="showBuySms">
      <BuySMS v-if="showBuySms" @paySuccess="onPaySuccess"></BuySMS>
      <div slot="footer"></div>
    </Modal>
    <Modal title="电子合同充值" width="800" class="buy-sms" v-model="showBuyElectronic">
      <BuySMS v-if="showBuyElectronic" @paySuccess="onPaySuccess" type="2"></BuySMS>
      <div slot="footer"></div>
    </Modal>
  </div>
  <!-- <QrSignNotice /> -->
 

</div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { getBaseUrl, getNewHost } from 'utils/config';
import BuySMS from 'components/onlinePay/buySMS';
// import QrSignNotice from 'components/member/qrSignNotice';

export default {
  name: 'headerTop',
  components: {
    BuySMS,
    // QrSignNotice
  },

  data() {
    return {
      showCode: false,
      search: '',
      timer: null,
      select: '用户',
      searchPla: '姓名/电话/实体卡号',
      signOutUrl: getBaseUrl() + '/Web/Public/logout',
      switchBusModal: false,
      searchBusName: '',
      showNoticeModal: false,
      showBuyElectronic: false,
      showBuySms: false,
      busQrCode: getBaseUrl() + '/Web/Business/getBusQrCode/',
      busQrCodeTwo: getBaseUrl() + '/Web/Applet/qn_plus_qrcode_index',
    };
  },
  created() {
    if (this.$route.query.operation === 'showBuySms') {
      this.showBuySms = true;
      window.location.hash = '#/index';
    }
  },
  computed: {
    ...mapState(['adminInfo', 'busName', 'busId', 'busList', 'adminName', 'adminId']),
    ...mapState('websocket', ['socketMsgCount']),
    // busQrCode: () => getBaseUrl() + '/Web/Business/getBusQrCode/',
    helpUrl: () => '#/signin/manual',
    filterBusName() {
      const self = this;
      if (self.busList) {
        return self.busList.filter(item => item.name.indexOf(self.searchBusName) !== -1);
      } else {
        return [];
      }
    }
  },
  watch:{
    busId () {
      // #13208 将二维码换成我的页面的二维码
      this.$service.get('/Web/Applet/get_applet_menu').then(res => {
        if (res.data.errorcode === 0) {
          const item = res.data.data.list.find(v => v.name === '我的');
          if (item) {
            this.busQrCode = `${getBaseUrl()}/Web/Applet/download_applet_qrcode?filename=我的&path=${encodeURIComponent(item.pagepath)}`
          }
        }
      })
      .catch(err => {
        console.log(err);
      });
    }
  },
  methods: {
    goMsgCenter() {
      window.open(`/v2/notice/msgCenter`)
    },
    onPaySuccess() {
      this.showBuySms = false;
      this.$store.dispatch('getAdminInfo').then(res => {
        this.showBuySms = false;
      });
    },
    clickLogout() {
      if (window.location.hostname == 'localhost') {
        this.$router.push({path: '/login', name: '登录', params: { from: window.location.hash }})
      } else {
        window.location.href = this.signOutUrl;
      }
    },
    editPwd() {
      this.$router.push(`/admin/add?from=pass&id=${this.adminId}`)
    },
    handleSwitchBus(event) {
      this.switchBusModal = true;
      this.searchBusName = '';
    },
    handleSwitchBusTag(bus) {
      this.$service
        .post('/Admin/Cutover/ajax_cutover', {
          bus_id: bus.id
        })
        .then(res => {
          if (res.status === 200) {
            if (res.data.status == 1) {
              // 初始化搜索标签
              sessionStorage.setItem('class_id', '0');
              sessionStorage.setItem('card_id', '0');
              // 初始化卡课管理tab
              sessionStorage.setItem('cardListActive', '0');
              // fix: 11791 切换场馆时重置，以获取通知
              sessionStorage.setItem('hasPopupNotice', false)
              this.$Notice.success({
                title: '场馆切换',
                duration: 1.2,
                desc: '切换中...',
                onClose() {
                  if (window.location.hostname == 'localhost') {
                    window.location.reload();
                  } else {
                    window.location.href =
                      getBaseUrl() + '/Admin/Index/index' + '/create_time/' + Date.parse(new Date())+'?url='+encodeURIComponent(getNewHost());
                  }
                }
              });
            } else {
              this.$Message.error(res.data.info);
            }
          } else {
            console.log(res.data.info);
            this.$Notice.error({
              title: '场馆切换失败！',
              desc: res.data.info
            });
          }
        });
    },
    handleSearch() {
      if (this.search === '') {
        this.$Message.error('请输入查询内容。');
        return false;
      }
      if (this.select === '用户') {
        this.$router.push({
          name: '会员管理',
          query: { curMenu: 'search', search: this.search }
        });
      } else {
        this.$router.push({
          name: '会员签到',
          query: { brand_number: this.search }
        });
      }
      this.search = '';
    },
    
  }
};
</script>
<style lang="less" scoped>
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-notice {
  width: 100%;
  img {
    max-width: 100%;
  }
}
.admin-pop {
  color: #1b1b1b;
  font-size: 14px;
  .thumb {
    height: 114px;
    .flex-center;
    flex-direction: column;
    img {
      height: 74px;
      width: 74px;
      border: 2px solid #ececec;
      border-radius: 50%;
    }
    span {
      font-weight: bold;
      font-size: 16px;
    }
  }
  .info {
    background-color: #f4f4f4;
    padding: 8px 14px;
    li {
      .flex-center;
      justify-content: flex-start;
      height: 36px;
      .user-type {
        .flex-center;
        background-color: #fff;
        height: 24px;
        border-radius: 12px;
        padding: 0 14px;
      }
      .end-date {
        font-weight: bold;
      }
      .message {
        .flex-center;
      }
    }
  }
  .bottom {
    padding: 16px 0 20px;
    .flex-center;
    .line {
      width: 1px;
      height: 13px;
      background-color: #d2d2d2;
    }
    button {
      color: #1b1b1b;
      font-size: 12px;
      &:hover {
        color: #e7280e;
      }
    }
  }
}

.header-top {
  height: 70px;
  background-color: #fff;
  border-bottom: 2px solid #e0e3e9;
  z-index: 20;

  padding-left: 90px;
  padding-right: 48px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 998;
  position: relative;
}
.search-box {
  width: 528px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f1f3f7;

  > img {
    width: 22px;
    height: 22px;
    margin-left: 22px;
    margin-right: 11px;
    cursor: pointer;
  }
  .search {
    flex: 1;
    background-color: transparent;
    border: 0;
    outline: 0;
    font-size: 14px;
    color: #35495d;
  }
  .search::placeholder {
    color: #b2bbce;
  }
}
.right {
  display: flex;
  align-items: center;
  height: 100%;
  .icons {
    height: 100%;
    border-left: 1px solid #f1f3f7;
    margin-right: 45px;
    display: flex;
    .hover-icon {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 70px;
      height: 100%;
      border-right: 1px solid #f1f3f7;
    }
  }
  .switch {
    background-color: #52a4ea;
    color: #fff;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 21px 0 16px;
    border-radius: 6px;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }

    > span {
      margin-left: 20px;
      margin-right: 24px;
    }
  }
  .user {
    .switch;
    &:hover {
      opacity: 1;
    }
    background-color: #fff;
    color: #35495d;
  }
}
.sign-help {
  // width: 300px;
  width: 100%;
  // height: 160px;
  background: #fff;
  border-radius: 6px;
  .help-title {
    font-size: 12px;
    color: #35495d;
    margin: 0;
    margin-left: 14px;
    line-height: 40px;
    float: left;
  }

  .url-wrap {
    float: right;
    margin-right: 15px;
  }

  .help-text {
    display: block;
    float: left;
    color: #3598db;
    font-size: 12px;
    margin: 0;
    line-height: 40px;
  }

  .help-text:hover {
    color: red;
  }

  .help-ico {
    font-style: normal;
    display: inline-block;
    width: 12px;
    height: 12px;
    cursor: pointer;
    background: url(../../assets/img/blue_03.png) no-repeat;
    background-size: cover;
    margin-top: 15px;
    margin-left: 4px;
  }

  .signurl {
    display: block;
    width: 100%;
    height: 100%;
  }

  .sign-img {
    overflow: hidden;
    display: block;
    // width: 87px;
    // height: 87px;
    width: 215px;
    height: 215px;
    margin-top: 20px;
    margin: 0 auto;
    transition: all .3s;
  }

  .sign-imgbox {
    overflow: hidden;
    clear: both;
    // width: 87px;
    margin: 0 auto;
    // &:hover .sign-img {
    //   width: 215px;
    //   height: 215px;
    // }
  }

  .sign-bottomtext {
    color: #35495d;
    font-size: 12px;
    text-align: center;
    margin-top: 6px;
  }
}
/*场馆选择弹出框样式 begin*/
.search-bus {
  height: 60px;
}
.list-bus {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  max-height: 333px;
  overflow: auto;
  .tag {
    background: #fff;
    border-radius: 4px;
    font-size: 16px;
    position: relative;
    transition: all 0.2s ease-in-out;
    transform: translate3d(0, -2px, 0);
    border: 1px solid #e9eaec;
    cursor: pointer;
    padding: 5px 10px;
    margin: 5px;
    width: 47%;
    height: 36px;
  }
  .tag:hover {
    /*box-shadow: -1px 5px 25px -5px rgba(0,0,0,0.8);*/
    /*box-shadow: -1px 5px 5px -5px rgba(0,0,0,0.8);*/
    background-color: #52a4ea;
    color: #fff;
    .version-tag {
      background: #fff;
      color: #52a4ea;
    }
  }
  .name-wrap {
    width: 100%;
    float: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .hastag .name-wrap {
    width: 70%;
  }
}
.version-tag {
  float: right;
  margin-top: 3px;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 18px;
  font-size: 12px;
  background: #1bd4c9;
  margin-right: 8px;
  font-family: 'sans-serif';
}
.tag-red {
  background: #e8403d;
}
.more-wrap {
  align-items: flex-start;
  margin: 15px 0;
}
</style>
<style lang="less">
.info-wrap {
  position: relative;
  width: 20px;
  height: 20px;
  .ivu-badge {
    position: absolute;
    left: 10px;
    top: -10px;
  }
}
</style>
