<template>
  <section class="app-main" :class="$route.meta.hidePadding?'nopadding':''" >
    <transition name="fade" mode="out-in">
      <keep-alive :include="keepAliveList">
        <router-view></router-view>
      </keep-alive>
    </transition>
  </section>
</template>
<script>
  export default {
    name: 'appMain',
    data() {
      return {
        keepAliveList: ['MemberIndex']
      }
    },
    computed: {
      key() {
        return this.$route.path;
      }
    },
    watch: {
      $route: {
        handler(to, from) {
          let curComponentName = to.matched && to.matched[to.matched.length-1].components.default.name
          if(to.meta.keepAlive) {
            this.keepAliveList = Array.from(new Set([...this.keepAliveList, curComponentName]))
          }
          //会员列表的缓存 从会员列表到不是会员详情的页面时需要清除
          if((from && from.name==='会员管理' && to.name!=='会员详情'))  {
            this.keepAliveList = this.keepAliveList.filter((item) => item !== 'MemberIndex')
          }
        },
        immediate: true
      }
    }
  };
</script>
<style scoped>
  .app-main {
    flex: 1;
    overflow-y: scroll;
    background: #f1f3f7;
    box-sizing: border-box;
    padding: 40px 48px;
  }
  .nopadding {
    padding: 0;
  }
</style>
