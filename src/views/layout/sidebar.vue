<style lang="less">
.sidebar {
  width: 100px;
  background: #35495d;
  position: relative;
  z-index: 999;

  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  height: 100%;
  font-size: 14px;
  color: #b2bbce;
  .ivu-poptip-inner {
    box-shadow: 1px 1px 15px rgba(0, 0, 0, 0.3);
  }
  .ivu-poptip-popper[x-placement='right-start'] .ivu-poptip-arrow {
    top: 28px !important;
  }
  .ivu-poptip-popper[x-placement='right-end'] .ivu-poptip-arrow {
    bottom: 27px;
  }
  .menu-pop {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #35495d;

    .link {
      display: block;
      line-height: 36px;
      font-size: 14px;
      width: 100%;
      text-align: center;
      &.ac {
        color: #d8321f;
      }
    }
  }

  .logo {
    display: flex;
    height: 70px;
    justify-content: center;
    align-items: center;
  }
  .custom-logo {
    width: 60px;
    height: 60px;
    margin-top: 10px;
  }
  .menus {
    width: 100%;
    margin-top: 27px;
    padding-bottom: 60px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: scroll;
    .menu-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      flex: 0;

      width: 70px;
      height: 65px;
      color: #b2bbce;
      border-radius: 6px;
      cursor: pointer;
      &:hover {
        background-color: #2c3945;
      }

      .sub-icon {
        position: absolute;
        right: 2px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .active {
      color: #fff;
      background-color: #2c3945;
    }
  }

}
</style>


<template>
  <div class="sidebar">
    <div v-if="showCustomLogo">
      <img class="custom-logo" :src="busLogo" :title="$store.getters.gatedLaunch ? '灰度' : '勤鸟运动'" alt="logo">
    </div>
    <a
      v-else
      :title="$store.getters.gatedLaunch ? '灰度' : '勤鸟运动'"
      class="logo"
      href="https://www.rocketbird.cn"
      target="_blank">
      <img src="~assets/img/rocketbird.png" alt="logo">
    </a>
    <div id="sidebarMenus" class="menus">
      <div v-for="(menu, index) in menus" :key="menu.id">
        <Poptip
          v-if="menu.node"
          :placement="placement(index, menu)"
          trigger="hover">
          <div :class="{'menu-item': true, 'active': menu.name == activeName}">
            <img v-if="menu.name == activeName" :src="menu.iconCur" :alt="menu.name">
            <img v-else :src="menu.icon" :alt="menu.name">
            <span>{{ menu.name }}</span>
            <img class="sub-icon" src="~assets/img/menu-submenu.png" alt="has-sub-menu">
          </div>
          <div slot="content" class="menu-pop">
            <a
              v-for="link in menu.node"
              :key="link.id"
              class="link"
              :class="{ 'ac': link.name === ($route.meta.breadText || $route.name) }"
              :target="link.name == '新签到提醒' ? '_blank' : ''"
              :href="link._url">{{ link.name }}</a>
          </div>
        </Poptip>
        <a
          v-else
          :class="{'menu-item': true, 'active': menu.name == activeName}"
          :href="menu._url">
          <img v-if="menu.name == activeName" :src="menu.iconCur" alt="">
          <img v-else :src="menu.icon" alt="">
          <span>{{ menu.name }}</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { getLeftNav } from 'src/service/getData';
import { getBaseUrl, getHost } from 'utils/config';
import { mapState } from 'vuex';

export default {
  name: 'Sidebar',
  data() {
    return {
      menuList: null,
      activeName: '',
      menus: ''
    };
  },
  computed: {
    ...mapState(['busLogo', 'showCustomLogo'])
  },

  watch: {
    $route() {
      this.update();
    }
  },
  created() {
    this.getMenuList();
  },

  methods: {
    placement(index, menu) {
      return menu.name==='财务' || menu.name==='营销'  ? 'right' : index >= this.menus.length - Math.ceil(this.menus.length / 3) && this.menus.length >= 7
        ? 'right-end'
        : 'right-start';
    },
    getMenuList() {
      getLeftNav()
        .then(res => {
          if (res.data.errorcode === 0) {
            // const child = res.data.data.find(item => item.name == '管理')
            // if (child) {
            //   child.node.push({
            //     id: "23333",
            //     name: "积分管理",
            //     url: "/Web/Point/getPointList",
            //     version: 3
            //   })
            // }

            this.menuList = res.data.data;
            this.dealMenu(res.data.data);
            this.$nextTick(() => {
              this.update();
            });
          } else {
            this.$Message.error('你还没有登录噢！');
            this.$router.push({path: '/login', name: '登录', params: { from: window.location.hash }});
          }
        })
        .catch(err => {
          this.$router.push({path: '/login', name: '登录', params: { from: window.location.hash }});
        });
    },
    dealMenu(menu) {
      this.menus = menu.map(item => {
        return {
          ...item,
          ...{
            icon: `/static/img/menu-icon/${item.flag}.png`,
            iconCur: `/static/img/menu-icon/${item.flag}-cur.png`,
            _url:
              item.version == 3
                ? `${item.url}`
                : item.version == 2 ? `${getBaseUrl()}${item.url}` : `${getHost()}/#${item.url}`,
            node:
              item.node &&
              item.node.map(link => {
                return {
                  ...link,
                  ...{
                    _url:
                      link.version == 3
                        ? `${link.url}`
                        : link.version == 2 ? `${getBaseUrl()}${link.url}` : `${getHost()}/#${link.url}`
                  }
                };
              })
          }
        };
      });
      this.$nextTick(() => {
        const menus = document.getElementById('sidebarMenus');
        const menusItems = document.querySelectorAll('.menu-item');
        const itemHeight = menusItems[0].clientHeight;
        const itemTotalH = itemHeight * menusItems.length;
        if (menus.offsetHeight >= itemTotalH) {
          menus.style.overflowY = 'auto';
        } else {
          menus.style.overflowY = 'scroll';
        }
        window.addEventListener('resize', this.handleResize);
      });
    },
    handleResize() {
      this.dealMenu(this.menuList);
      window.removeEventListener('resize', this.handleResize);
    },
    //更新展开的子目录和当前选择项
    update() {
      this.activeName = this.$route.meta.parentName || this.$route.name;
    }
  }
};
</script>
