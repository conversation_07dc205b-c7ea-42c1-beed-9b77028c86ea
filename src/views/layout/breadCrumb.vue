<template>
  <div class="bc-wrap">
    <a v-if="levelList.length >= 3 || needBack" href="javascript:history.back(-1);" class="back">返回</a>
    <Breadcrumb separator=">">
      <template v-for="(item,index) in levelList">
        <Breadcrumb-item v-if='index==levelList.length-1 || index==0' :key="index">{{item.meta && item.meta.breadText ? item.meta.breadText : item.name}}</Breadcrumb-item>
        <template v-else>
          <Breadcrumb-item :to="item.path" v-if="!item.meta.version && !(item.meta.brandPath && IS_BRAND_SITE)" :key="index">{{item.meta && item.meta.breadText ? item.meta.breadText : item.name}}</Breadcrumb-item>
          <Breadcrumb-item v-else :key="index" ><a @click="handleTo(item.path)">{{item.meta && item.meta.breadText ? item.meta.breadText : item.name}}</a></Breadcrumb-item>
        </template>
      </template>
    </Breadcrumb>
  </div>
</template>
<script>
  import {
    getHost, getBrandHost
  } from "utils/config"

  export default {
    created() {
      this.getBreadcrumb()
    },
    data() {
      return {
        IS_BRAND_SITE: window.IS_BRAND_SITE,
        levelList: null,
        breadNotShow: false,
      }
    },
    computed: {
      needBack() {
        const list = this.levelList
        const current = list && list[list.length - 1]
        return current.meta && current.meta.needBack
      }
    },

    methods: {
      getBreadcrumb() {
        let matched = this.$route.matched.filter(item => item.meta.breadText || item.name);
        const last = matched[matched.length - 1];
        if (last.meta.breadNotShow) {
          this.breadNotShow = true;
          return;
        } else {
          this.breadNotShow = false;
        }
        this.levelList = matched.map((item) => {
          const newItem = { ...item }
          newItem.path = item.meta.breadNotLink === true
            ? ''
            : item.meta.version
              ? `${getHost()}/#${item.meta.version}`
              : item.meta.brandPath && this.IS_BRAND_SITE
              ? getBrandHost() + item.meta.brandPath
              : item.path
          return newItem
        });
      },

      handleTo(path) {
        window.open(path, '_self')
      }
    },
    watch: {
      $route() {
        this.getBreadcrumb();
      }
    }
  }
</script>
<style scoped>
  .back {
    display: inline-block;
    float: left;
    color: #3598db;
    padding-right: 10px;
    line-height: 17px;
  }

  .bc-wrap {
    /* position: fixed;
    top: 70px;
    z-index: 999; */
    padding: 12px 20px 10px;
    width: 100%;
    background: #fff;
    height: 40px;
    border-bottom: 1px solid #e0e3e9;
    line-height: 17px;
  }

  .ivu-breadcrumb > span:first-child {
    font-weight: bold;
    padding-left: 10px;
    border-left: 4px solid #d9534f;
    color: #333;
  }

  .ivu-breadcrumb > span:last-child {
    font-weight: normal;
    color: #999;
  }
</style>
