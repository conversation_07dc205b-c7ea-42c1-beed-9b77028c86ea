<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs" :value="activeIndex">
      <TabPane label="充值记录" name="0">
          <PayRecordList />
      </TabPane>
      <TabPane label="消费记录" name="1">
          <ConsumptionList />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import PayRecordList from './components/PayRecordList.vue'
import ConsumptionList from './components/ConsumptionList.vue'
export default {
  name: 'RechargeRecord',
  components: {
    PayRecordList,
    ConsumptionList
  },
  data () {
    return {
      cardSettingAuth: {
        expCard: false,
        multiCard: false,
        multiPtCard: false,
        singleCard: false,
        swimCard: false
      },
      activeIndex: '0'
    };
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
    }
  },
  created() {
  }
};
</script>

<style lang="less">
  .tab-table-wrap .ivu-tabs {
    min-height: 0;
  }
</style>
