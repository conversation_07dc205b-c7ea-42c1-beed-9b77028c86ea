
<template>
  <div class="container">
    <header>
      <h3>薪资设置</h3>
    </header>
    <Form label-position="right" class="form" :label-width="140">
      <FormItem>
        正在为<span v-if="nameTips" class="tips">{{ nameTips }}</span>等<span class="tips">{{ selectedMembers.length }}</span>位人员设置薪资规则
      </FormItem>
      <Card class="bonus-card bonus-card-table" dis-hover>
        <p slot="title">底薪</p>
        <i-button
          slot="extra"
          type="text"
          :disabled="rule.salary_rule.length > 0"
          @click.prevent="salaryData = null;showSalaryModal = true">
          <Icon type="plus-circled" /> 添加底薪
        </i-button>
        <Table
          ref="table"
          :columns="salaryColumns"
          :data="rule.salary_rule"
          disabled-hover></Table>
      </Card>
      <Card class="bonus-card bonus-card-table" dis-hover>
        <p slot="title">团操课时费</p>
        <a slot="extra" href="#" @click.prevent="showAdd = true">
          <Icon type="plus-circled" /> 添加课程
        </a>
        <Table
          ref="table"
          :columns="classColumns"
          :data="rule.tk_rule"
          disabled-hover></Table>
      </Card>
      <Card class="bonus-card bonus-card-table" dis-hover>
        <p slot="title">销售业绩提成</p>
        <i-button
          slot="extra"
          type="text"
          shape="circle"
          size="small"
          :disabled="rule.sale_rule.length>0 && !rule.sale_rule[rule.sale_rule.length-1].max"
          @click.prevent="showSaleAdd = true">
          <Icon type="plus-circled" /> 添加梯度
        </i-button>
        <Table
          ref="table"
          :columns="saleColumns"
          :data="rule.sale_rule"
          disabled-hover></Table>
      </Card>
      <Card class="bonus-card bonus-card-table" dis-hover>
        <p slot="title">私教/泳教上课提成方式</p>
        <a slot="extra" href="#" @click.prevent="ptModalData = null;showPtAdd = true">
          <Icon type="plus-circled" /> 添加提成规则
        </a>
        <Table
          ref="table"
          :columns="priclassColumns"
          :data="rule.priclass_rule"
          disabled-hover></Table>
      </Card>
      <FormItem>
        <div class="buttons">
          <Button type="primary" @click="addPay">确定</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>

    <!-- 底薪设置弹窗 -->
    <SalaryEditSalaryModal
      :show.sync="showSalaryModal"
      :editData="salaryData"
      @save="saveSalaryModal" />

    <Modal v-model="showAdd" :mask-closable="false" title="团操课时费">
      <Form
        v-if="showAdd"
        ref="addForm"
        :model="tkModalData"
        class="modal-form"
        :label-width="80">
        <Form-item
          label="课程种类"
          prop="tkid"
          :rules="{required: true, message: '请选择课种', trigger: 'change'}"
          filterable>
          <Select v-model="tkModalData.tkid" label-in-value @on-change="openClassChange">
            <Option
              v-for="item in openClassList"
              :key="item.class_id"
              :value="item.class_id"
              :label="item.class_name"
              :disabled="tkidArray.indexOf(item.class_id) !== -1"></Option>
          </Select>
        </Form-item>
        <Form-item label="单节费用" prop="price" :rules="{required: true,type: 'string',pattern: /^[0-9]+(.[0-9]{1,2})?$/,message: '必须为数字且最多只能保留两位小数', trigger: 'blur'}">
          <Input v-model="tkModalData.price" placeholder="元" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveClassModal">保存</Button>
        <Button @click="showAdd = false">取消</Button>
      </div>
    </Modal>

    <Modal v-model="showSaleAdd" :mask-closable="false" title="销售业绩提成方式">
      <Form
        v-if="showSaleAdd"
        ref="saleForm"
        :model="saleModalData"
        class="modal-form"
        :label-width="80">
        <FormItem label="提成范围">
          <Row>
            <Col span="5">
            <FormItem prop="min" :rules="{required: true, message: '请填写范围初始值'}">
              <InputNumber
                v-model="saleModalData.min"
                :min="0"
                :active-change="false"
                :disabled="rule.sale_rule.length>0 && saleModalData._index != 0"></InputNumber>
            </FormItem>
            </Col>
            <Col span="2" style="text-align: center">~</Col>
            <Col span="6">
            <FormItem prop="max">
              <InputNumber v-model="saleModalData.max" :active-change="false" :min="saleModalData.min + 1"></InputNumber> 元
            </FormItem>
            </Col>
          </Row>
        </FormItem>
        <Form-item label="提成比例" prop="ratio" :rules="{required: true, message: '请填写比例'}">
          <InputNumber
            v-model="saleModalData.ratio"
            :max="100"></InputNumber> %
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveSaleModal">保存</Button>
        <Button @click="showSaleAdd = false">取消</Button>
      </div>
    </Modal>

    <!-- 私教泳教课提 设置弹窗 -->
    <SalaryEditPtModal
      :show.sync="showPtAdd"
      :editData="ptModalData"
      :ptClassList="ptClassList"
      :groupClassList="groupClassList"
      @save="savePtModal" />
  </div>
</template>

<script>
import SalaryEditPtModal from './components/SalaryEditPtModal'
import SalaryEditSalaryModal from './components/SalaryEditSalaryModal'

export default {
  name: 'SalaryEdit',
  components: {
    SalaryEditPtModal,
    SalaryEditSalaryModal
  },

  data() {
    return {
      selectedMembers: this.$route.params.selectedMembers || [],
      editData: this.$route.params.editData,
      isEdit: this.$route.params.isEdit,
      // payId: this.$route.params.payId,
      // postData: {
      //   ids: '',
      //   salary_rule: '', // 底薪规则
      //   commission_rule: '', // 提成规则
      //   bus_id: ''
      // },
      rule: {
        salary_rule: [],
        tk_rule: [],
        sale_rule: [],
        priclass_rule: []
      },
      ptClassList: [],   // 私教泳教课列表
      openClassList: [], // 团操课选项列表
      groupClassList: [], // 分组私教泳教课

      showSalaryModal: false, // 控制底薪设置弹窗
      showAdd: false,
      showSaleAdd: false,
      showPtAdd: false, // 控制私教/泳教上课提成弹窗

      /* 暂存的4项修改数据 */
      salaryData: null, // 底薪
      tkModalData: { // 团操课
        tkid: '',
        name: '',
        price: ''
      },
      saleModalData: {
        min: null,
        max: null,
        ratio: null
      },
      ptModalData: null, // 私教/泳教课提成

      /* Table渲染结构 */
      salaryColumns: [
        {
          title: '条件',
          render: (_, { row }) => {
            if (!row || row.type == 1) {
              return <div>-</div>
            }
            const { rule: salaryRule } = row;
            const rules = salaryRule.map(({ achievement, class_hour }) => {
              let achievementDes, hourDes, des;
              if (achievement) {
                if (achievement.min && achievement.max) {
                  achievementDes = `业绩${achievement.min}~${achievement.max}元`
                } else if (achievement.min) {
                  achievementDes = `业绩>=${achievement.min}元`
                } else if (achievement.max) {
                  achievementDes = `业绩<=${achievement.max}元`
                }
              }

             if (class_hour) {
                if (class_hour.min && class_hour.max) {
                  hourDes = `课时${class_hour.min}~${class_hour.max}节`
                } else if (class_hour.min) {
                  hourDes = `课时>=${class_hour.min}节`
                } else if (class_hour.max) {
                  hourDes = `课时<=${class_hour.max}节`
                }
             }

              if (achievementDes && hourDes) {
                des = `${achievementDes} & ${hourDes}`
              } else if (achievementDes || hourDes){
                des = achievementDes || hourDes
              }

              return <div>{des}</div>
            });

            return <div>{rules}</div>
          }
        },
        {
          title: '底薪',
          render: (_, { row }) => {
            if (!row) {
              return <div>-</div>
            }

            const { rule: salaryRule } = row;
            const rules = salaryRule.map(({ base_salary }) => {
              return <div>{base_salary}元</div>
            })

            return <div>{rules}</div>
          }
        },
        {
          title: '操作',
          render: (h, param) => {
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  onClick={() => {
                    this.showSalaryModal = true;
                    this.salaryData = param.row;
                  }}
                >
                  修改
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  class="button-text-red"
                  onClick={() => {
                    this.rule.salary_rule = [];
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      classColumns: [
        {
          title: '课程种类',
          key: 'name'
        },
        {
          title: '单节费用',
          render: (h, param) => {
            return <span>￥{param.row.price}</span>
          }
        },
        {
          title: '操作',
          render: (h, param) => {
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  onClick={() => {
                    this.showAdd = true;
                    this.tkModalData = JSON.parse(JSON.stringify(param.row));
                  }}
                >
                  修改
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  class="button-text-red"
                  onClick={() => {
                    this.rule.tk_rule.splice(param.index, 1)
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      saleColumns: [
        {
          title: '业绩范围',
          render: (h, param) => {
            return <span>{param.row.min}~{param.row.max}元</span>
          }
        },
        {
          title: '提成比例',
          render: (h, param) => {
            return <span>{param.row.ratio}%</span>
          }
        },
        {
          title: '操作',
          render: (h, param) => {
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  disabled={this.rule.sale_rule && (this.rule.sale_rule.length-1>param.row._index)}
                  onClick={() => {
                    this.showSaleAdd = true;
                    this.saleModalData = JSON.parse(JSON.stringify(param.row));
                  }}
                >
                  修改
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  class="button-text-red"
                  disabled={this.rule.sale_rule && (this.rule.sale_rule.length-1>param.row._index)}
                  onClick={() => {
                    this.rule.sale_rule.splice(param.index, 1)
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      priclassColumns: [
        {
          title: '条件',
          render: (h, param) => {
            let item = param.row
            let priceDes, classDes, des;
            if (item.achievement.min && item.achievement.max) {
              priceDes = `${item.achievement.min}~${item.achievement.max}元`
            } else if (item.achievement.min) {
              priceDes = `>=${item.achievement.min}元`
            }  else if (item.achievement.max) {
              priceDes = `<=${item.achievement.max}元`
            }

            if (item.class.min && item.class.max) {
              classDes = `消课总数${item.class.min}~${item.class.max}节`
            } else if (item.class.min) {
              classDes = `消课总数>=${item.class.min}节`
            } else if (item.class.max) {
              classDes = `消课总数<=${item.class.max}节`
            }
            if (priceDes && classDes) {
              des = `${priceDes} & ${classDes}`
            } else if (priceDes || classDes){
              des = priceDes || classDes
            }
            return <span>{des}</span>
          }
        },
        {
          title: '提成',
          render: (h, { row }) => {
            let curDes = [];
            if (row.method.classtype == 1) {
              curDes.push(<div>{ `提 消课价值 ${row.method.ratio}%` }</div>)
              if (row.method.pt_month == 1) {
                curDes.push(<div>{ `包月课程 独立按结算单价提成` }</div>)
              } else if(row.method.pt_month == 2)  {
                curDes.push(<div>{ `包月课程提 ${row.method.pt_sign_cost_pro}%` }</div>)
              }
            }

            if (row.method.classtype == 2) {
              row.method.classrule.forEach(v => {
                if (v.price || v.price === 0) {
                  curDes.push(<div>{ `${v.name} 提 ${v.price}元` }</div>)
                }
                if (v.sign_pro || v.sign_pro === 0) {
                  curDes.push(<div>{ `${v.name} 提 ${v.sign_pro}%` }</div>)
                }
                if (v.is_pt_time_limit_card == 1 && v.cost_type == 1) {
                  curDes.push(<div>{ `${v.name} 按课程结算单价` }</div>)
                }
              });
            }

            return <div>{ curDes }</div>
          }
        },
        {
          title: '操作',
          render: (h, param) => {
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  onClick={() => {
                    // this.ptModalData = JSON.parse(JSON.stringify(param.row));
                    this.ptModalData = param.row;
                    this.showPtAdd = true;
                  }}
                >
                  修改
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  class="button-text-red"
                  onClick={() => {
                    this.rule.priclass_rule.splice(param.index, 1)
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
    }
  },

  computed: {
    tkidArray() {
      let ids = []
      this.rule.tk_rule.forEach(item => {
        ids.push(item.tkid)
      })
      return ids
    },
    selectedUserIds() {
      let userIds = []
      this.selectedMembers.forEach(user => {
        userIds.push(user.id)
      })
      return userIds
    },
    nameTips() {
      let tips = '';
      let length = this.selectedMembers.length;
      if (length > 3) {
        tips =
          this.selectedMembers[0].name +
          '、' +
          this.selectedMembers[1].name +
          '、' +
          this.selectedMembers[2].name +
          '...' +
          length +
          '位用户';
      } else {
        this.selectedMembers.forEach((user, index) => {
          if (index == length - 1) {
            tips = tips + this.selectedMembers[index].name;
          } else {
            tips = tips + this.selectedMembers[index].name + '、';
          }
        });
      }
      return tips;
    }
  },

  watch: {
    showAdd(val) {
      if (!val) {
        this.tkModalData = {
          tkid: '',
          name: '',
          price: ''
        }
      }
    },
    showSaleAdd(val) {
      if (!val) {
        this.saleModalData = {
          min: null,
          max: null,
          ratio: null
        }
      } else if (this.rule.sale_rule && this.rule.sale_rule.length>0 && this.saleModalData._index == undefined){
        this.saleModalData.min = this.rule.sale_rule[this.rule.sale_rule.length-1].max + 1
      }
    },
  },

  created() {
    if (this.selectedMembers.length == 0) {
      this.$router.push({
        name: '薪资设定'
      })
    } else {
      this.getAllCourses();
      this.getAllClass();
      this.getAllGroup();
      if (this.isEdit) {
        // this.editData.commission_rule = this.editData.commission_rule
        // this.postData = this.editData
        const { commission_rule, salary_rule } = this.editData;
        const target = {
          salary_rule: salary_rule ? [salary_rule] : [],
          ...(commission_rule || { tk_rule: [], sale_rule: [], priclass_rule: [] }),
        }
        const rule = JSON.parse(JSON.stringify(target).replace(/""/g, null))
        this.rule = rule || this.rule
      }
    }
  },

  methods: {
    getAllCourses() {
      this.$service
        .post('/Web/PtSchedule/pt_user_all_card', {
          type: 0,
          // no_pt_time_limit_card: 1
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data;
            if(Array.isArray(list)) {
              this.ptClassList = list.map(({id, name, is_pt_time_limit_card}) => ({
                id,
                name,
                is_pt_time_limit_card, // 1是包月  0就是正常卡
                cost_type: 1,    // 1: 固定结算 2: 价值比例
                // price: "",    // 固定结算传这个 2选1
                // sign_pro: "", // 价值比例传这个
                value: null      // 用于前端存储以上2值，请求时修改为对应key
              }))
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getAllClass() {
      this.$service
        .post('/Web/OpenClass/get_all_class')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.openClassList = res.data.data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getAllGroup() {
      this.$service
        .post('/Web/PtSchedule/pt_user_group_all_card', { type: 0 })
        .then(res => {
          if (res.data.errorcode === 0) {
            const list = [];
            for (const [key, val] of Object.entries(res.data.data)) {
              const parentId =  val[0].card_group_id || key;
              list.push({
                id: parentId,
                name: key,
                check: false,
                show: true,
                disabled: false,
                children: val.map(({ ...rest }) => ({
                  ...rest,
                  parentId,
                  check: false,
                  show: true,
                  disabled: false,
                }))
              })
            }
            this.groupClassList = list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },

    openClassChange(info) {
      this.tkModalData.tkid = info.value
      this.tkModalData.name = info.label
    },

    /* 暂存四个弹窗修改的数据 */
    saveSalaryModal(rule) {
      if (this.salaryData && this.salaryData._index !== undefined) {
        this.rule.salary_rule.splice(this.salaryData._index, 1, rule)
      } else {
        this.rule.salary_rule.push(rule)
      }
    },
    saveClassModal() {
      this.$refs.addForm.validate(val => {
        if (!val) return false
        if (this.tkModalData._index !== undefined) {
          this.rule.tk_rule.splice(this.tkModalData._index, 1, this.tkModalData)
        } else {
          this.rule.tk_rule.push(this.tkModalData)
        }
        this.$nextTick(() => {
          this.showAdd = false
        })
      })
    },
    saveSaleModal() {
      this.$refs.saleForm.validate(val => {
        if (!val) return false
        if (this.saleModalData._index !== undefined) {
          this.rule.sale_rule.splice(this.saleModalData._index, 1, this.saleModalData)
        } else {
          this.rule.sale_rule.push(this.saleModalData)
        }
        this.$nextTick(() => {
          this.showSaleAdd = false
        })
      })
    },
    savePtModal(data) {
      if (this.ptModalData && this.ptModalData._index !== undefined) {
        this.rule.priclass_rule.splice(this.ptModalData._index, 1, data)
      } else {
        this.rule.priclass_rule.push(data)
      }
    },

    addPay() {
      const { salary_rule, ...rest } = this.rule;
      let s_rule, commission_rule, params
      try {
        s_rule =  JSON.stringify(salary_rule[0] || "").replace(/null/g, '""')
      } catch (error) {
        console.error(error)
      }
      try {
        commission_rule = JSON.stringify(rest).replace(/null/g, '""')
      } catch (error) {
        console.error(error)
      }
      try {
        params = {
          "bus_id": this.$route.params.busId,
          "ids": this.selectedUserIds.join(','),
          "salary_rule": s_rule,
          "commission_rule": commission_rule,
        }
      } catch (error) {
        console.error(error)
      }
      console.log('/Web/SalaryRule/deal_salary_rule', params);

      this.$service
        .post('/Web/SalaryRule/deal_salary_rule', params)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.$router.back()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(this.$Message.error)
    }
  }
}
</script>

<style>
.bonus-card-table .ivu-card-body {
  padding: 0;
}
.pt-item .ivu-form-item-label {
  text-align: left;
}
</style>

<style lang="less" scoped>
.tips {
  color: red;
  font-size: 14px;
}
.card-tips {
  text-align: right;
  font-size: 12px;
  line-height: 35px;
  margin-right: 15px;
  color: #888;
}
.bonus-card {
  margin: 0 0 24px 50px;
}
.container .form {
  padding-bottom: 44px;
}

.ivu-table-wrapper {
  overflow: unset;
  .ivu-table {
    overflow: unset;
  }
}
</style>
