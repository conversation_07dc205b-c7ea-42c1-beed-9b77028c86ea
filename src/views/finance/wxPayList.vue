<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs" :value="activeIndex">
      <TabPane label="微信" name="0">
          <WxPayRecord key="WxPayRecord" />
      </TabPane>
      <TabPane label="支付宝" name="1">
          <AlipayRecord key="AlipayRecord" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import WxPayRecord from './components/WxPayRecord.vue'
import AlipayRecord from './components/AlipayRecord.vue'
export default {
  name: 'wxPayList',
  components: {
    WxPayRecord,
    AlipayRecord
  },
  data () {
    return {
      activeIndex: '0'
    };
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
    }
  },
  created() {
  }
};
</script>

<style lang="less">
  .tab-table-wrap .ivu-tabs {
    min-height: 0;
  }
</style>
