<template>
  <Modal
    title="底薪设置"
    :value="show"
    :mask-closable="false"
    width="620"
    @on-visible-change="$emit('update:show', $event)"
  >
    <Form
      v-if="show"
      ref="basicForm"
      class="modal-form"
      :label-width="80"
      :model="basicData">
      <Form-item label="薪资方式" prop="type">
        <RadioGroup v-model="basicData.type">
          <Radio label="1" style="margin-right: 30px;">固定底薪</Radio>
          <Radio label="2">浮动底薪</Radio>
        </RadioGroup>
      </Form-item>
      <FormItem
        v-show="basicData.type == '1'"
        label="底薪(元)"
        prop="rule.0.base_salary"
        :rules="basicData.type == '1' ? { required: true, type: 'number', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '底薪必须为数字且最多只能保留两位小数', trigger: 'blur'} : null">
        <InputNumber
          style="width: 200px;"
          v-model="basicData.rule[0].base_salary"
          :min="0"
          :max="99999999"
          :precision="2"
          :active-change="false"/>
      </FormItem>
      <div v-show="basicData.type === '2'">
        <ul>
          <li class="salary-conditions-row">
            <span style="width: 180px;">业绩条件</span>
            <span style="width: 180px;">
              课时条件
              <Tooltip>
                <div slot="content">不包含包月课时</div>
                <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
              </Tooltip>
            </span>
            <span class="basic-text">底薪</span>
            <span style="width: 34px;"></span>
          </li>
          <li
            class="salary-conditions-row"
            v-for="(item, index) in basicData.rule"
            :key="index">
            <div>
              <InputNumber
                v-model="item.achievement.min"
                :min="0"
                :max="item.achievement.max === null ? 999999998 : item.achievement.max - 1"
                :precision="2"
                :active-change="false">
                <!-- @on-change="item.achievement.max <= $event + 1 && (item.achievement.max = $event + 1)" -->
              </InputNumber>
              <span class="text">~</span>
              <InputNumber
                v-model="item.achievement.max"
                :min="item.achievement.min + 1"
                :max="99999999"
                :precision="2"
                :active-change="false">
              </InputNumber>
              <span class="text">元</span>
            </div>
            <div>
              <InputNumber
                v-model="item.class_hour.min"
                :min="0"
                :max="item.class_hour.max === null ? 999999998 : item.class_hour.max - 1"
                :precision="0"
                :active-change="false">
              </InputNumber>
              <span class="text">~</span>
              <InputNumber
                v-model="item.class_hour.max"
                :min="item.class_hour.min + 1"
                :max="99999999"
                :precision="0"
                :active-change="false">
              </InputNumber>
              <span class="text">节</span>
            </div>
            <div>
              <InputNumber v-model="item.base_salary" :min="0" :max="99999999" :precision="2" :active-change="false"></InputNumber>
              <span class="text">元</span>
            </div>
            <div  class="delete-btn" @click="handleDeleteBasicRule(index)">
              <FaIcon name="trash-o" size="16" color="#d9544f"/>
            </div>
          </li>
          <li>
            <Button icon="md-add" type="primary" ghost @click="handleAddBasicRule">添加条件</Button>
          </li>
        </ul>
      </div>

    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="saveBasicModal">保存</Button>
      <Button @click="$emit('update:show', false)">取消</Button>
    </div>
  </Modal>
</template>

<script>
  const itemData = {
    achievement: {
      min: null,
      max: null
    },
    class_hour: {
      min: null,
      max: null
    },
    base_salary: null
  }

  export default {
    name: 'SalaryEditBasicModal',

    props: {
      show: {
        type: Boolean,
        required: true
      },
      editData: {
        type: Object,
        default: null
      }
    },

    data() {
      return {
        basicData: {
          type: '1', // 薪酬类型 1 固定 2 浮动
          rule: [{ // 浮动条件
            achievement: {
              min: null,
              max: null
            },
            class_hour: {
              min: null,
              max: null
            },
            base_salary: 0
          }],
        },
      }
    },

    watch: {
      show(val) {
        if (val) {
          const { editData } = this;
          if (editData) {
            const rule = editData.rule.map(v => Object.assign({}, itemData, { ...v, base_salary: +v.base_salary }))
            this.basicData = JSON.parse(JSON.stringify({
              type: editData.type + '',
              rule
            }));

          } else {
            Object.assign(this.$data, this.$options.data())
          }
        }
      }
    },

    methods: {
      /* 添加底薪浮动规则 */
      handleAddBasicRule() {
        const { rule } = this.basicData;
        if (rule.length >= 10) return this.$Message.warning('规则条数已达上限');
        rule.push({
          achievement: {
            min: null,
            max: null
          },
          class_hour: {
            min: null,
            max: null
          },
          base_salary: null
        })
      },

      handleDeleteBasicRule(index) {
        const { rule } = this.basicData;
        if (rule.length === 1) return this.$Message.warning('至少需要一条规则');
        rule.splice(index, 1)
      },

      saveBasicModal() {
        this.$refs.basicForm.validate(val => {
          if (!val) return false;

          const { type, rule } = this.basicData;
          let newRule = [];

          if (type == '2') {
            if (!rule.length) return this.$Message.warning('至少需要一条规则')

            // const hasValue = rule.some(({ achievement, class_hour }) => [achievement.min, achievement.max, class_hour.min, class_hour.max].some(v => v === 0 || v))
            const hasValue = !rule.some(({ achievement, class_hour }) => !achievement.min && !achievement.max && !class_hour.min && !class_hour.max)
            if (!hasValue) return this.$Message.warning('每行条件至少填写一个')

            const hasBaseSalary = rule.every(v => v.base_salary || v.base_salary === 0)
            if (!hasBaseSalary) return this.$Message.warning('每行底薪必填')
            newRule = rule;
          }

          if (type == '1') {
            newRule = [{ base_salary: rule[0].base_salary }]
          }

          this.$emit('save', { type, rule: newRule })
          this.$emit('update:show', false)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.salary-label-row {
  /deep/.ivu-col {
    margin-bottom: 10px;
    text-align: center;
  }
}

.salary-conditions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  margin-bottom: 16px;
  height: 36px;
  line-height: 34px;
  text-align: center;
  &:first-child {
    height: auto;
    line-height: unset;
    .basic-text {
      width: 80px;
      &::before {
        content: '*';
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-family: SimSun;
        font-size: 12px;
        color: #ed4014;
      }
    }
  }
  .text {
    display: inline-block;
    width: 18px;
  }
  .delete-btn {
    width: 34px;
    cursor: pointer;
    &:hover {
      opacity: .83;
    }
  }
}
</style>
