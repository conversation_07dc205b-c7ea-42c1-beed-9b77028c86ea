<template>
  <Modal
    title="私教/泳教上课提成方式"
    :value="show"
    :mask-closable="false"
    @on-visible-change="$emit('update:show', $event)"
  >
    <Form
      v-if="show"
      ref="ptForm"
      :model="ptModalData"
      class="modal-form"
      :label-width="120">
      <h3 class="pt-tit">条件 <span class="red">(两个条件至少需要填写一个, 消课条件不包含包月课时数)</span></h3>
      <FormItem label="业绩条件">
        <Row>
          <Col span="5">
          <FormItem prop="min">
            <InputNumber
              v-model="ptModalData.achievement.min"
              :min="0"
              :max="ptModalData.achievement.max == null ? 999999998 : ptModalData.achievement.max-1"
              :precision="2"
              :active-change="false"
            ></InputNumber>
          </FormItem>
          </Col>
          <Col span="2" style="text-align: center">~</Col>
          <Col span="8">
          <FormItem prop="max">
            <InputNumber
              v-model="ptModalData.achievement.max"
              :min="ptModalData.achievement.min+1"
              :max="99999999"
              :precision="2"
              :active-change="false"
            ></InputNumber> 元
          </FormItem>
          </Col>
        </Row>
      </FormItem>
      <FormItem label="消课条件">
        <!-- <div slot="label">
          <Tooltip>
            <div slot="content">不包含包月课时</div>
            <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
          </Tooltip>
          <span>消课条件</span>
        </div> -->
        <Row>
          <Col span="5">
          <FormItem prop="min">
            <InputNumber
              v-model="ptModalData.class.min"
              :min="0"
              :max="ptModalData.class.max == null ? 999999998 : ptModalData.class.max-1"
              :precision="0"
              :active-change="false"
            ></InputNumber>
          </FormItem>
          </Col>
          <Col span="2" style="text-align: center">~</Col>
          <Col span="8">
          <FormItem prop="max">
            <InputNumber
              v-model="ptModalData.class.max"
              :min="ptModalData.class.min+1"
              :max="99999999"
              :precision="0"
              :active-change="false"
            ></InputNumber> 节
          </FormItem>
          </Col>
        </Row>
      </FormItem>
      <h3 class="pt-tit">提成<!-- <span>（私教包月课程独立计按结算单价计算）</span> --></h3>
      <Form-item label="提成方式" prop="method.classtype">
        <Radio-group v-model="ptModalData.method.classtype" @on-change="ptModalData.method.ratio=null">
          <Radio :label="1">按消课价值比例提成</Radio>
          <Radio :label="2">按课种提成</Radio>
        </Radio-group>
      </Form-item>
      <div v-show="ptModalData.method.classtype == 2" class="setting-btn-row">
        <span class="setting-btn" @click="handleShowSetting">批量设置</span>
      </div>
      <div v-show="ptModalData.method.classtype == 1">
        <Form-item
          label="提成比例"
          prop="method.ratio">
          <InputNumber
            v-model="ptModalData.method.ratio"
            :min="0"
            :max="100"
            :precision="2"
            :active-change="false"
          ></InputNumber> %
        </Form-item>
        <Form-item label="包月私教" prop="method.pt_month">
          <!-- <Select v-model="ptModalData.method.pt_month" style="width:200px;">
            <Option :value="1" label="独立按结算单价计算提成"></Option>
            <Option :value="2" label="按照消课价值比例计算提成"></Option>
          </Select> -->
          <Radio-group v-model="ptModalData.method.pt_month" @on-change="ptModalData.method.pt_sign_cost_pro=null">
            <Radio :label="1">独立按结算单价提成</Radio>
            <Radio :label="2">按消课价值比例提成</Radio>
          </Radio-group>
        </Form-item>
        <Form-item
          v-if="ptModalData.method.pt_month == 2"
          label="包月提成比例"
          prop="method.pt_sign_cost_pro">
          <InputNumber
            v-model="ptModalData.method.pt_sign_cost_pro"
            :min="0"
            :max="100"
            :precision="2"
            :active-change="false"
          ></InputNumber> %
        </Form-item>
      </div>
      <ul v-show="ptModalData.method.classtype == 2" class="ov-wrap">
        <li v-for="(item, index) in ptModalData.method.classrule" :key="index" class="pt-item">
          <p class="pt-item-label text_overflow" :title="item.name">{{ item.name }}</p>
          <Select v-model="item.cost_type" class="pt-item-select" @on-change="item.value=null">
            <Option :value="1" label="固定费用"></Option>
            <Option :value="2" label="消课价值比例"></Option>
          </Select>
          <div style="min-width:115px;">
            <span v-if="item.is_pt_time_limit_card==1" v-show="item.cost_type==1">按课程结算单价</span>
            <div v-show="item.is_pt_time_limit_card!=1 || item.cost_type==2">
              <InputNumber
                v-model="item.value"
                :min="0"
                :max="item.cost_type == 2 ? 100 : 99999999"
                :precision="2"
                :active-change="false"
              ></InputNumber>
              <span class="pt-item-unit">{{ item.cost_type == '1' ? '元/节' : '%' }}</span>
            </div>
          </div>
        </li>
      </ul>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="savePtModal">保存</Button>
      <Button @click="$emit('update:show', false)">取消</Button>
    </div>

    <Modal
      v-model="showSetting"
      title="课提批量设置"
      :mask-closable="false"
      width="500"
    >
      <Form
        ref="batchForm"
        :model="settingFormData"
        class="modal-form"
        :label-width="80">
        <Form-item label="课提方式" prop="type">
          <Select v-model="settingFormData.type">
            <Option :value="1" label="固定费用"></Option>
            <Option :value="2" label="消课价值比例"></Option>
          </Select>
        </Form-item>
        <Form-item
          :label="settingFormData.type == '1' ? '单节费用' : '价值比例'"
          prop="value"
        >
          <!-- :rules="{ required: true,  message: (settingFormData.type == '1' ? '单节费用' : '价值比例') + '必填', trigger: 'blur'}" -->
          <InputNumber
            v-model="settingFormData.value"
            style="width:100%"
            :min="0"
            :max="settingFormData.type == '1' ? 99999999 : 100"
            :precision="2"
            :active-change="false"
            :placeholder="settingFormData.type == '1' ? '元' : '%'" />
        </Form-item>
        <Form-item label="课程">
          <CheckPtClass
            v-if="showSetting"
            :treeList="settingFormData.list"
            placeholder="搜索课名称"
          />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button :disabled="!settingFormData.list.length" type="success" @click="handleSaveBatch">保存</Button>
        <Button @click="showSetting = false">取消</Button>
      </div>
    </Modal>
  </Modal>
</template>

<script>
  import CheckPtClass from 'components/form/checkPtClass';

  export default {
    name: 'SalaryEditPtModal',
    components: {
      CheckPtClass
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      editData: {
        type: Object,
        default: null
      },
      ptClassList: {
        type: Array,
        default: () => []
      },
      groupClassList: {
        type: Array,
        default: () => []
      },
    },

    data() {
      return {
        /* 私教/泳教上课提成方式相关数据 */
        ptModalData: {
          achievement: {
            min: null,
            max: null
          },
          class: {
            min: null,
            max: null
          },
          method: {
            classtype: 1, // 提成方式 1 按消课价值比例, 2 按课种
            ratio: null, // 消课价值比例提成比例
            pt_month: 1, // 包月提成类型 1 独立按结算单价 2 消课价值比例
            pt_sign_cost_pro: null, // 包月消课价值比例提成比例
            classrule: [
              // {
              //   id:”151”,
              //   name:”私教课”,
              //   is_pt_time_limit_card // 1是包月  0就是正常卡
              //   cost_type: 1,     // 按课种提成方式 1:固定结算 2: 价值比例
              //   price: null,      // 固定结算传这个 2选1
              //   sign_pro: null,   // 价值比例传这个
              //   value: null       // 用于前端存储以上2值，请求时修改为对应key
              // }
            ] // 按课种提成比例
          }
        },
        /* 批量设置使用数据 */
        showSetting: false,
        settingFormData: {
          type: 1,
          value: null,
          list: [],
        },
        filterText: '',
        isCheckAll: false,
        isFound: false
      }
    },

    watch: {
      show(val) {
        if (val) {
          if (this.editData == null) {
            this.ptModalData.method.classrule = JSON.parse(JSON.stringify(this.ptClassList))
          } else {
            const { achievement, class: classObj, method } = this.editData;
            const { classrule, ...rest } = method;
            const newRules = classrule.map(({ cost_type, price, sign_pro, ...rest }) => (
              {
                cost_type: cost_type || 1,
                value: (cost_type && cost_type == 2 ? sign_pro : price) || null,
                ...rest
              }
            ))
            const target = {
              achievement,
              class: classObj,
              method: {
                classrule: newRules,
                ...rest
              }
            }
            this.ptModalData = JSON.parse(JSON.stringify(target))
          }
        }else {
          Object.assign(this.$data, this.$options.data())
        }
      },
      showSetting(val) {
        if (val) {
          this.settingFormData.type = 1;
          this.settingFormData.value = null;
          this.settingFormData.list = JSON.parse(JSON.stringify(this.groupClassList))
        }
      }
    },

    methods: {
      handleShowSetting() {
        this.showSetting = true
      },

      /* 保存批量设置 */
      handleSaveBatch() {
        // this.$refs.batchForm.validate(val => {
          // if (val) {

            const { type, value, list } = this.settingFormData;
            const { classrule } = this.ptModalData.method;
            if (!value && value !== 0) return this.$Message.warning(`请填写${ type == '1' ? '单节费用' : '价值比例' }`)

            const fn = (v) => ({
              id: v.id,
              is_pt_time_limit_card: v.is_pt_time_limit_card,
              name: v.name,
              cost_type: type,
              value: v.is_pt_time_limit_card == 1 && type == 1 ? null : value
            })

            list.map(v => v.children).flat().forEach(v => {
              if (!v.check) return;

              const index = classrule.findIndex(k => k.id == v.id);
              if (index == -1) {
                classrule.push(fn(v))
              } else {
                this.$set(classrule, index, fn(v))
              }
            })

            this.showSetting = false;
          // }
        // })
      },

      savePtModal() {
        this.$refs.ptForm.validate(val => {
          if(val) {
            const { achievement, class: classNumObj, method } = this.ptModalData;

            // if (![achievement.min, achievement.max, classNumObj.min, classNumObj.max].some(v => v === 0 || v)) {
            if ((!achievement.min && !achievement.max &&!classNumObj.min && !classNumObj.max)) {
              return this.$Message.error('请填写条件')
            }

            if (method.classtype == 1) {
              if (method.ratio === null) {
                return this.$Message.error('请填写提成比例')
              }
              if (method.pt_month == 2 && method.pt_sign_cost_pro === null) {
                return this.$Message.error('请填写包月提成比例')
              }
              method.classrule.forEach(v => {
                v.cost_type = 1;
                v.value = null;
              })
              this.ptModalEdit()
            }

            if(method.classtype == 2) {
              const hasData = method.classrule.some(({ value }) => value || value === 0)
              hasData ?
                this.ptModalEdit() :
                this.$Message.error('至少填写一种课种的提成')
            }
          }
        })
      },

      ptModalEdit() {
        const { method, ...rest } = this.ptModalData;

        const newRules = method.classrule.map(({ cost_type, value, ...rest }) => ({
          cost_type,
          [cost_type == 2 ? 'sign_pro' : 'price']: value,
          ...rest
        }))

        const target = {
          ...rest,
          method: {
            ...method,
            classrule: newRules
          }
        }


        this.$emit('save', target)
        this.$emit('update:show', false)
      },
    },
  }
</script>

<style lang="less" scoped>

.pt-tit {
  padding-bottom: 25px;
  .red {
    color: red;
    font-size: 12px;
    font-weight: normal
  }
}

.setting-btn-row {
  margin-bottom: 16px;
  padding-left: 10px;
  .setting-btn {
    font-size: 14px;
    color: #2D8cF0;
    cursor: pointer;
    &:hover {
      color: #57a3f3;
    }
  }
}

.ov-wrap {
  max-height: 300px;
  overflow-y: scroll;
}
.pt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding: 0 10px;
  .pt-item-label {
    width: 180px;
    font-size: 14px;
  }
  .pt-item-select {
    width: 120px;
  }
  .pt-item-unit {
    display: inline-block;
    min-width: 35px;
    text-align: center;
    font-size: 14px;
  }
}

.check-container {
  // overflow: hidden;
  padding: 6px 0;
  // height: auto;
  // height: 47px;
  // transition: all 1s;
  border-top: 1px solid #f2f2f2;
}

.checkbox-group {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-left: 20px;
  padding-bottom: 8px;
}

.check-box {
  display: flex;
  align-items: center;
  margin: 6px 10px 6px 0;
  padding: 2px 0 2px 5px;
  width: 45%;
  line-height: 1.5;
  border-radius: 3px;
  transition: background-color .3s;
  cursor: pointer;
  &:hover {
    background-color: #f8f8f9;
  }
}

.checkbox-inner {
  display: inline-block;
  margin: 2px;
  min-width: 14px;
  min-height: 14px;
  border: 1px solid #dcdee2;
  border-radius: 2px;
  background-color: #fff;
  // cursor: pointer;
  &:hover {
    border-color: #bcbcbc;
  }
}
.check-content {
  padding-left: 2px;
}
</style>
