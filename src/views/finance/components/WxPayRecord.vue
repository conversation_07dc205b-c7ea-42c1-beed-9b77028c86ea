<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input v-model="postData.phone" class="w120" placeholder="手机" />
      <DatePicker type="daterange" :value="dateRange" :options="dateOptions" @on-change="dateChange" placeholder="时间段" style="width: 200px" :clearable="false"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <div style="min-height:200px;">
      <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    </div>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager name="WxPayRecord" :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from 'utils'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'WxPayRecord',
  components: {
    Pager
  },
  data() {
    return {
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
       dateOptions: {
        disabledDate: date => {
          const days = this.financeCheckDays
          return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
        }
      },
      postData: {
        s_date: formatDate(new Date(), 'yyyy-MM-dd'),
        e_date: formatDate(new Date(), 'yyyy-MM-dd'),
        phone: '',
        page_no: 1,
        page_size: 10,
        bus_id: ''
      },
      total: 0,
      tableData: [],
      columns: [
        {
          key: 'create_time',
          title: '下单时间'
        },
        {
          key: 'order_sn',
          title: '订单号'
        },
        {
          key: 'Consumption_type',
          title: '类型'
        },
        {
          key: 'username',
          title: '会员',
          render: (h, params) => {
            const item = params.row
            // const url = `/#/member/detail/${item.user_id}`
            // return <a href={url}>{item.username}</a>
            return <a onClick={() => {
              this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
            }}>{item.username}</a>
          }
        },
        {
          key: 'phone',
          title: '手机'
        },
        {
          key: 'amount',
          title: '总金额'
        },
        {
          key: 'sub_mch_id',
          title: '子商户号'
        },
        {
          key: 'transaction_id',
          title: '微信支付订单号'
        },
        {
          key: 'card_name',
          title: '备注'
        }
        // {
        //   key: 'online_pay_status',
        //   title: '支付状态'
        // },
        // {
        //   key: 'order_status',
        //   title: '订单状态'
        // }
        // {
        //   key: 'description',
        //   title: '备注',
        //   width: '15%',
        //   render: (h, params) => {
        //     const item = params.row
        //     return <div title={item.description}>{item.description}</div>
        //   }
        // }
      ],
      storeList: []
    }
  },
  computed: {
    ...mapState(['busId','financeCheckDays'])
  },
  created() {
    this.postData.bus_id = this.busId
    this.getStoreList()
  },
  methods: {
    ...mapActions(["getAdminInfo", "getReceiptAuth"]),
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    dateChange([s_date, e_date]) {
      this.postData = { ...this.postData, ...{ s_date, e_date } }
    },
    pageChange(postData) {
      const { s_date, e_date } = postData
      this.dateRange = [s_date, e_date]
      this.postData = { ...this.postData, ...postData }
      this.getList()
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/Finance/wxreceiptdetails', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.list
            this.total = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getExportData() {
      return this.$service
        .post('/Web/Finance/wxreceiptdetails', {
          ...this.postData,
          ...{ page_size: this.total, page_no: 1 }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            return res.data.data.list.map(item => {
              return {
                ...item,
                ...{
                  order_sn: '\u0009' + item.order_sn,
                  transaction_id: '\u0009' + item.transaction_id
                }
              }
            });
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    async exportCsv() {
      const exportData = await this.getExportData()
      this.$refs.table.exportCsv({
        filename: '支付对账单',
        // columns: this.columns.filter((col, index) => col.key != 'create_time'),
        columns: this.columns,
        data: exportData
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>
