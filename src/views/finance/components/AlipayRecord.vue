<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input v-model="postData.phone" class="w120" placeholder="手机" />
      <DatePicker type="daterange" :value="dateRange" :options="dateOptions" @on-change="dateChange" placeholder="时间段" style="width: 200px" :clearable="false"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <div style="min-height:200px;">
      <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    </div>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Pager name="AlipayRecord" :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from 'utils'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'AlipayRecord',
  components: {
    Pager
  },
  data() {
    return {
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      dateOptions: {
        disabledDate: date => {
          const days = this.financeCheckDays
          return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
        }
      },
      postData: {
        s_date: formatDate(new Date(), 'yyyy-MM-dd'),
        e_date: formatDate(new Date(), 'yyyy-MM-dd'),
        phone: '',
        type: 2,
        page_no: 1,
        page_size: 10,
        bus_id: ''
      },
      total: 0,
      tableData: [],
      columns: [
        {
          key: 'pay_time',
          title: '支付时间'
        },
        {
          key: 'create_time',
          title: '下单时间'
        },
        {
          key: 'order_sn',
          title: '订单号'
        },
        {
          key: 'order_type',
          title: '类型',
          render: (h, params) => {
            const item = params.row
            return <div>{item.order_type==2?'买单':'购卡'}</div>
          }
        },
        {
          key: 'status_txt',
          title: '状态'
        },
        {
          key: 'card_name',
          title: '卡课'
        },
        {
          key: 'username',
          title: '会员',
          render: (h, params) => {
            const item = params.row
            // const url = `/#/member/detail/${item.user_id}`
            // return <a href={url}>{item.username}</a>
            return <a onClick={() => {
              this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
            }}>{item.username}</a>
          }
        },
        {
          key: 'phone',
          title: '手机'
        },
        {
          key: 'order_amount',
          title: '订单金额'
        },
        {
          key: 'discount_type',
          title: '优惠方式'
        },
        {
          key: 'discount_amount',
          title: '优惠金额'
        },
        {
          key: 'amount',
          title: '支付金额'
        },
        {
          key: 'trade_no',
          title: '支付订单号'
        },
        {
          key: 'alipay_user_id',
          title: '收款商户号'
        },
        {
          key: 'remark',
          title: '备注'
        }

      ],
      storeList: []
    }
  },
  computed: {
    ...mapState(['busId','financeCheckDays'])
  },
  created() {
    this.postData.bus_id = this.busId
    this.getStoreList()
  },
  methods: {
    ...mapActions(["getAdminInfo", "getReceiptAuth"]),
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    dateChange([s_date, e_date]) {
      this.postData = { ...this.postData, ...{ s_date, e_date } }
    },
    pageChange(postData) {
      const { s_date, e_date } = postData
      this.dateRange = [s_date, e_date]
      this.postData = { ...this.postData, ...postData }
      this.getList()
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.$service
        .post('/Web/Finance/wxreceiptdetails', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.list
            this.total = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getExportData() {
      return this.$service
        .post('/Web/Finance/wxreceiptdetails', {
          ...this.postData,
          ...{ page_size: this.total, page_no: 1 }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            return res.data.data.list.map(item => {
              return {
                ...item,
                ...{
                  order_sn: '\u0009' + item.order_sn,
                  alipay_user_id: '\u0009' + item.alipay_user_id,
                  trade_no: '\u0009' + item.trade_no,
                  order_type: item.order_type==2?'买单':'购卡'
                }
              }
            });
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    async exportCsv() {
      const exportData = await this.getExportData()
      this.$refs.table.exportCsv({
        filename: '支付对账单',
        // columns: this.columns.filter((col, index) => col.key != 'create_time'),
        columns: this.columns,
        data: exportData
      })
    }
  }
}
</script>

<style scoped lang="less">
</style>
