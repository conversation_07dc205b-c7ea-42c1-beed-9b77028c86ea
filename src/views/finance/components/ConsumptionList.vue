<template>
    <div class="table-wrap">
        <main>
          <Menu mode="horizontal" :active-name="activeIndex" @on-select="selectMenu">
            <MenuItem :name="1">
                短信消费
            </MenuItem>
            <MenuItem :name="2">
                电子合同消费
            </MenuItem>
          </Menu>
          <NoticeList v-show="activeIndex === 1"/>
          <ElectronicList v-show="activeIndex === 2"/>
        </main>
    </div>
</template>
<script>
import NoticeList from './NoticeList.vue'
import ElectronicList from './ElectronicList.vue'
export default {
  name: 'ConsumptionList',
   components: {
    NoticeList,
    ElectronicList
  },
  data() {
    return {
      activated: [1],
      activeIndex: 1
    }
  },
  methods: {
    selectMenu(index) {
      this.activeIndex = index;
      if (!this.activated.includes(index)) {
        this.activated.push(index);
      }
    }
  }
}
</script>
