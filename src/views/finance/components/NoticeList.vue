<template>
  <div class="table-wrap notice-list">
    <header>
      <Input style="width: 200px"
             placeholder="主题名称"
             @on-enter="searchList"
             v-model="keyword" />
      <DatePicker type="daterange"
                  v-model="dateRange"
                  style="width: 250px"
                  format="yyyy-MM-dd" />
      <Button type="success"
              @click="searchList">搜索</Button>
    </header>
    <Table :columns="columns"
           :data="tableData"
           disabled-hover />
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Page :total="total"
            :current.sync="currentPage"
            show-total
            show-sizer
            placement="top"
            @on-change="getNoticeList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
   
  </div>
</template>

<script>
import Export from 'src/components/Export';
import { mapState } from 'vuex';
export default {
  name: 'noticeList',
  components: {
    Export
  },
  data() {
    return {
      dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
      keyword: '',
      sizer: 10,
      currentPage: 1,
      total: 0,
      columns: [
        {
          title: '时间',
          key: 'send_time'
        },
        {
          title: '通知对象',
          key: 'receiver',
          ellipsis: true,
          render: (h, params) => {
            return (
              <div style="overflow: hidden; text-overflow: ellipsis" title={params.row.receiver}>
                {params.row.receiver}
              </div>
            );
          }
        },
        {
          title: '消耗短信条数',
          key: 'sms_num'
        },
        {
          title: '内容',
          key: 'content',
          ellipsis: true,
          align: 'center',
          render: (h, params) => {
            return (
              <div style="width: 100%; overflow: hidden; text-overflow: ellipsis">
                <span style="padding-right: 15px; font-weight: bold">{params.row.title}</span>
                <span style="color: #666" title={params.row.content.replace('，拒收请回复R', '')}>
                  {params.row.content.replace('，拒收请回复R', '')}
                </span>
              </div>
            );
          }
        },
        {
          title: '操作账号',
          key: 'sender'
        }
      ],
      tableData: []
    };
  },
  created() {
    this.getNoticeList();
  },
  methods: {
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.sizer = size;
      this.getNoticeList();
    },
    dealDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${month}-${day}`;
    },
    searchList() {
      this.currentPage = 1;
      this.getNoticeList()
    },
    getNoticeList() {
      // const url = '/Web/SendMessage/getMsgList';
      const url = '/Web/SendMessage/sendMsgList'
      const postData = {
        keyword: this.keyword,
        begin_date: this.dealDate(this.dateRange[0]),
        end_date: this.dealDate(this.dateRange[1]),
        page_size: this.sizer,
        page_no: this.currentPage
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getExportData() {
      return this.$service
        // .post('/Web/SendMessage/getMsgList', {
        .post('/Web/SendMessage/sendMsgList', {
          keyword: this.keyword,
          begin_date: this.dealDate(this.dateRange[0]),
          end_date: this.dealDate(this.dateRange[1]),
          page_size: this.total, 
          page_no: 1 
        }, { isExport: true })
        .then(res => {
          if (res.data.errorcode === 0) {
            return res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    async exportCsv() {
      const exportData = await this.getExportData();
      this.$refs.export.export({
        filename: '短信消费记录',
        columns: this.columns,
        data: exportData
      });
    }
  }
};
</script>
