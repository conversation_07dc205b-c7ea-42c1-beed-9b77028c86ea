<template>
  <div class="table-wrap notice-list">
    <header>
      <DatePicker type="daterange"
                  v-model="dateRange"
                  style="width: 250px"
                  format="yyyy-MM-dd" 
                  @on-change="dateChange"/>
      <Select v-model="postData.item_type" placeholder="事项" clearable>
          <Option value="1">实名认证</Option>
          <Option value="2">签署</Option>
        </Select>
      <Button type="success"
              @click="searchList">搜索</Button>
    </header>
    <Table :columns="columns"
           :data="tableData"
           disabled-hover />
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Page :total="total"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            @on-change="getNoticeList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
   
  </div>
</template>

<script>
import Export from 'src/components/Export'
import { formatDate } from 'utils'
export default {
  name: 'ElectronicList',
  components: {
    Export
  },
  data() {
    return {
      dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
      postData: {
        item_type: '',
        begin_date: formatDate(new Date(Date.now() - 30 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
        end_date: formatDate(new Date(), 'yyyy-MM-dd'),
        page_no: 1,
        page_size: 10
      },
      total: 0,
      columns: [
        {
          title: '时间',
          key: 'create_time'
        },
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '合同订单',
          key: 'order_sn'
        },
        {
          title: '事项',
          key: 'item_type',
          render(h, {row}) {
            return <span>{row.item_type==='1'?'实名认证':'签署'}</span>
          },
        },
        {
          title: '数量',
          key: 'use_contract_number'
        }
      ],
      tableData: []
    };
  },
  created() {
    this.getNoticeList();
  },
  methods: {
    dateChange([s, e]) {
      this.postData.begin_date = s;
      this.postData.end_date = e;
    },
    searchList() {
      this.postData.page_no = 1;
      this.getNoticeList()
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.postData.page_no = 1;
      this.getNoticeList();
    },
    dealDate(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${month}-${day}`;
    },
    getNoticeList() {
      this.$service
        .post('Web/consumption_record/get_contract_record', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            this.tableData = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getExportData() {
      return this.$service
        .post('/Web/consumption_record/get_contract_record', {
          ...this.postData,
          page_size: this.total,
          page_no: 1 
        }, { isExport: true })
        .then(res => {
          if (res.data.errorcode === 0) {
            return res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    async exportCsv() {
      const exportData = await this.getExportData();
      const data = exportData.map(item => {
        return { 
          ...item,
          item_type: item.item_type==='1'?'实名认证':'签署'
        };
      });
      this.$refs.export.export({
        filename: '电子合同消费记录',
        columns: this.columns,
        data
      });
    }
  }
};
</script>
