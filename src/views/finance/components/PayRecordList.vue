<template>
    <div class="table-wrap">
        <header>
            <DatePicker type="daterange" :value="dateRange" @on-change="dateChange" placeholder="时间段"></DatePicker>
            <Button type="success"
                    @click="handleSearch">搜索</Button>
        </header>
        <main>
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="tableData"
                   ref="table"></Table>
        </main>
        <footer>
            <Pager :total="total" :postData="postData" @on-change="handleWhichPage" />
        </footer>

    </div>
</template>
<script>
import { formatDate } from 'utils'
import Pager from 'components/pager'
export default {
  name: 'PayRecordList',
  components: {
    Pager
  },
  data() {
    return {
      tableData: [],
      dateRange: [formatDate(new Date(Date.now() - 30 * 24 * 3600 * 1000), 'yyyy-MM-dd'),formatDate(new Date(), 'yyyy-MM-dd')],
      total: 0,
      postData: {
        begin_date: formatDate(new Date(Date.now() - 30 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
        end_date: formatDate(new Date(), 'yyyy-MM-dd'),
        page_no: 1,
        page_size: 10
      },
      cardColumns: [
        {
          title: '时间',
          align: 'center',
          key: 'create_time'
        },
        {
          title: '事项',
          align: 'center',
          key: 'type'
        },
        {
          title: '充值金额',
          align: 'center',
          key: 'amount'
        }
      ]
    }
  },
  methods: {
    dateChange([begin_date, end_date]) {
      this.postData.begin_date = begin_date
      this.postData.end_date = end_date
    },
    getCardList() {
      return this.$service
        .post('/Web/consumption_record/get_recharge_record', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data
            this.tableData = resData.list
            this.total = parseInt(resData.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleWhichPage(postData) {
      this.postData = { ...this.postData, ...postData }
      this.getCardList()
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getCardList()
    }
  }
}
</script>
