<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Select v-model="postData.bus_id" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input style="width: 180px" v-model="postData.search" class="option-select" placeholder="业务单号/姓名/电话" />
      <DatePicker v-model="duringDate" :options="dateOptions" type="daterange" placement="bottom-start" placeholder="选择日期" class="option-select" style="width: 220px" format="yyyy-MM-dd"></DatePicker>
      <Select v-model="postData.type" class="option-select" placeholder="支出/收入">
        <Option v-for="item in flowTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="postData.pay_status" class="option-select" placeholder="线上/线下">
        <Option v-for="item in onlineTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      
      <PayTypeSelect v-model="postData.pay_type"
                      style="width: 180px;max-width: none; margin-right: 20px;"
                     :showCardPay="true"
                     :busId="postData.bus_id"
                      placeholder="支付方式" :needShowDragonFly="false">
        <Option value="-1">全部</Option>
      </PayTypeSelect>
      <Select v-model="postData.related" class="option-select" placeholder="业务单关联">
        <Option v-for="item in relatedList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-body-total">
      <Col span="24" class="box-body-total-col">
      <div class="t-label">总计</div>
      <div class="t-desc">
        <div v-for="item in totalStat" :key="item.name">{{item.name}}:{{item.amount}}</div>
      </div>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <!-- <Button @click="handleExcel">导出Excel</Button> -->
        <Button @click="exportOne">导出Excel</Button>
        <Export ref="export">导出Excel</Export>
      </div>
      <Page @on-change="handlePage" :total="total" :current="postData.page_no" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
   
  </div>
</template>
<script>
import Export from 'src/components/Export'
import PayTypeSelect from 'components/form/PayTypeSelect'
import { mapState } from 'vuex'
export default {
  name: 'ReceiveLog',
  components: { Export, PayTypeSelect },
  data() {
    return {
      postData: {
        search: '',
        begin_date: '',
        end_date: '',
        page_no: 1,
        page_size: 10,
        type: '',
        pay_type: '',
        pay_status: '',
        related: '',
        bus_id: ''
      },
      isLoading: false,
      total: 0,
      duringDate: [new Date(), new Date()],
      flowTypeList: [
        { value: '', label: '全部' },
        { value: 0, label: '收入' },
        { value: 1, label: '支出' }
      ],
      onlineTypeList: [
        { value: '', label: '全部' },
        { value: 1, label: '线上' },
        { value: 0, label: '线下' }
      ],
      relatedList: [
        { value: '', label: '全部' },
        { value: 1, label: '已关联' },
        { value: 0, label: '未关联' }
      ],
      columns: [
        { title: '流水单号', key: 'serial_number' },
        {
          title: '渠道单号',
          key: 'order_no',
          render: (h, params) => {
            return (
              <span>{params.row.order_no ? params.row.order_no : '-'}</span>
            )
          }
        },
        { title: '时间', key: 'create_time' },
        {
          title: '金额',
          key: 'amount',
          render: (h, params) => {
            return <span>{params.row.amount}</span>
          }
        },
        {
          title: '线上/线下',
          key: 'is_online',
          render: (h, params) => {
            return <span>{params.row.is_online === '1' ? '线上' : '线下'}</span>
          }
        },
        { title: '支付方式', key: 'pay_type_name' },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <span>{params.row.username ? params.row.username : '-'}</span>
            )
          }
        },
        {
          title: '关联业务单号',
          key: 'flow_sn',
          render: (h, params) => {
            let flowSn = params.row.flow_sn.split(',')
            let snDom = <span>-</span>
            if (Array.isArray(flowSn) && flowSn.length > 1) {
              const shortStride = flowSn.slice(0, 4).join(',')
              const singleStride = (search) => {
                return (<i-button type="text" style="display: block; margin: 10px 0" onClick={() => {
                window.history.pushState({}, '', `/finance/inventory?beginTime=${this.postData.begin_date}&endTime=${this.postData.end_date}&busId=${this.jumpBusId}&search=${search}`)
              }} size="small">
                {search}
              </i-button>)
              }
              snDom = (<Poptip title="关联业务单号" trigger="hover" transfer>
                <span>{shortStride}...</span>
                <div slot="content" style="height: 300px; overflow: auto">
                  {flowSn.map(singleStride)}
                </div>
              </Poptip>)
            } else {
              const search = params.row.flow_sn
              snDom = (<i-button type="text" style="white-space: normal" onClick={() => {
                window.history.pushState({}, '', `/finance/inventory?beginTime=${this.postData.begin_date}&endTime=${this.postData.end_date}&busId=${this.jumpBusId}&search=${search}`)
              }} size="small">
                <span>{search}</span>
              </i-button>)
            }
            return snDom
          }
        }
      ],
      list: [],
      dateOptions: {
        disabledDate: date => {
          const days = this.financeCheckDays
          return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date()
              const start = new Date()
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return [start, end]
            }
          },
          {
            text: '一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            }
          },
          {
            text: '一个月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            }
          }
        ]
      },
      totalStat: [],
      curTotal: '',
      allTotal: '',
      storeList: [],
      jumpBusId: ''
    }
  },
  computed: {
    ...mapState(['busId','financeCheckDays'])
  },
  methods: {
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getDateString(date) {
      if (date) {
        date = new Date(date)
        const year = date.getFullYear()
        let month = date.getMonth() + 1
        month = month < 10 ? '0' + month : month
        let day = date.getDate()
        day = day < 10 ? '0' + day : day
        return `${year}-${month}-${day}`
      } else {
        return ''
      }
    },
    handleSearch() {
      this.postData.page_no = 1
      if (this.duringDate.length === 2) {
        const start = this.duringDate[0]
        const end = this.duringDate[1]
        this.duringDate = [this.getDateString(start), this.getDateString(end)]
      }
      this.getList()
    },
    handlePage(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList() {
      if (this.duringDate.length !== 2 || !this.duringDate[0]) {
        this.$Message.error('请选择日期！')
        return false
      } else {
        this.postData.begin_date = this.getDateString(this.duringDate[0])
        this.postData.end_date = this.getDateString(this.duringDate[1])
      }
      return this.$service
        .post('/Web/statistics/ReceiveLogList', this.postData, {
          timeout: 30000
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            this.list = res.data.data.list
            this.total = parseInt(res.data.data.count)
            this.totalStat = res.data.data.total_stat
            this.jumpBusId = this.postData.bus_id
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    exportOne() {
      this.$service
        .post('/Web/statistics/ReceiveLogList', {
          ...this.postData,
          page_no: 1,
          page_size: this.total
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            let arr = res.data.data.list
            arr.forEach(item => {
              item.is_online = item.is_online === '1' ? '线上' : '线下'
              // item.amount = item.amount
            })
            this.$refs.export.export({
              filename: '收银流水',
              columns: this.columns,
              data: arr,
              quoted: true
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    // handleExcel() {
    //   let bbdate = this.duringDate[0],
    //     eedate = this.duringDate[1]
    //   if (typeof bbdate != 'string') {
    //     bbdate = this.getDateString(bbdate)
    //     eedate = this.getDateString(eedate)
    //   }
    //   this.exportOne(bbdate, eedate)
    // },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    }
  },
  created() {
    this.postData.bus_id = this.busId
    this.jumpBusId = this.busId
    this.getStoreList()
    this.getList()
  }
}
</script>
<style lang="less">
.warn-sj {
  font-size: 14px;
  color: red;
  margin-left: 20px;
}

.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-body-total {
    border: 1px solid #dddee1;
    border-top: none;

    .box-body-total-col {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .t-label {
        width: 200px;
        padding: 0 40px;
        font-size: 16px;
        font-weight: bold;
      }

      .t-desc {
        width: 70%;
        max-width: 650px;
        font-size: 16px;
        display: flex;
        flex-wrap: wrap;
        div {
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          width: 25%;
          display: flex;
        }
      }
    }
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
    }
  }
}
</style>
