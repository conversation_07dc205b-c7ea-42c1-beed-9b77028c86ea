<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <Input style="width:150px" v-model="postData.search" class="option-select" placeholder="姓名/电话/合同单号" />
      <cardList v-model="postData.card_id" :busId="postData.bus_id" @on-change="cardChange" placeholder="卡/课类型" class="select" returnCardInfo multiple showPackage></cardList>
      <PayTypeSelect v-model="postData.pay_type" :busId="postData.bus_id" class="select" :needShowDragonFly="false"></PayTypeSelect>
      <Select v-model="postData.from_type" class="select" placeholder="来源" clearable>
        <Option value="1">线下</Option>
        <Option value="2">线上</Option>
      </Select>
      <salesSelect v-model="postData.marketers_id" :belongBusId="postData.bus_id" @on-change="saleChange" labelInValue isCoach class="select" placeholder="业绩归属"></salesSelect>
      <!-- <Select v-model="postData.order_sign_status" class="select" placeholder="会员签字" clearable>
        <Option value="0">未签</Option>
        <Option value="1">已签</Option>
        <Option value="9">免签</Option>
      </Select> -->
      <Select class="select" v-model="postData.consumption_type" placeholder="消费类型" clearable>
        <Option value="">全部</Option>
        <Option value="购卡">购卡</Option>
        <Option value="升卡">升卡</Option>
        <Option value="续卡">续卡</Option>
        <Option value="购私教">购私教</Option>
        <Option value="续私教">续私教</Option>
        <Option value="购泳教">购泳教</Option>
        <Option value="续泳教">续泳教</Option>
        <Option value="转卡">转卡</Option>
        <Option value="补卡">补卡</Option>
        <Option value="请假">请假</Option>
        <Option value="销卡">销卡</Option>
        <Option value="拆分">拆分</Option>
        <Option value="租柜">租柜</Option>
        <Option value="跨店购卡">跨店购卡</Option>
        <Option value="在线购课">在线购课</Option>
      </Select>
      <DatePicker type="daterange" :value="dateRange" @on-change="dateChange" :options="dateOptions" placement="bottom-end" placeholder="时间段" style="width: 200px"></DatePicker>
      <Select class="select" v-model="postData.approve_status" placeholder="审批状态" clearable>
        <Option value="">全部</Option>
        <Option value="1">待审</Option>
        <Option value="2">不通过</Option>
        <Option value="3">通过</Option>
      </Select>
      <Select v-model="postData.contract_type" class="select" placeholder="合同类型" clearable>
        <Option value="">全部</Option>
        <Option value="1">会籍合同</Option>
        <Option value="2">私教合同</Option>
        <Option value="4">泳教合同</Option>
        <Option value="5">套餐包合同</Option>
        <Option value="3">其他</Option>
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    <div class="total">
      <p>当页小计:
        <span>{{amount}}</span> 元</p>
      <p>总计:
        <span>{{totalAmount}}</span> 元</p>
    </div>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <Modal :title="modalTitle" v-model="showModal">
      <Table ref="modalTable" :columns="modalColumns" :data="modalTableData" disabledHover></Table>
      <div slot="footer"></div>
    </Modal>
    <Modal v-model="showRecord" :title="recordDetail.custom_order_sn">
      <div v-if="recordDetail.custom_type == 1">
        <Table ref="packageTable" :data="recordDetail.sub_detail" :columns="packageColumns" disabledHover></Table>
      </div>
      <div style="padding: 10px;fontSize: 14px;lineHeight: 2;" v-else>
        {{recordDetail.new_description}}
      </div>
        <div slot="footer"></div>
      </Modal>
  </div>
</template>

<script>
import cardList from 'components/card/cardListForOrderList'
import PayTypeSelect from 'components/form/PayTypeSelect'
import salesSelect from 'components/membership/salesSelect'
import Pager from 'components/pager'
import { formatDate } from 'utils'
import DateOptions from 'components/picker/quickPicker'
import Export from 'src/components/Export'
import { mapState, mapActions } from 'vuex'

const AMOUNT_TYPE = {
  front_money: '定金',
  coupon_amount: '折扣',
  income_amount: '收款',
  debt_card_amount: '储值卡'
}
const MODAL_TITLE = ['金额明细', '支付方式', '业绩归属']
const COLUMNS = {
  0: [
    {
      title: '类型',
      key: 'name'
    },
    {
      title: '涉及金额',
      key: 'value'
    }
  ],
  1: [
    {
      title: '支付方式',
      key: 'payType'
    },
    {
      title: '支付金额',
      key: 'amount'
    }
  ],
  2: [
    {
      title: '姓名',
      key: 'marketers_name'
    },
    {
      title: '归属/协助',
      key: 'isMain'
    },
    {
      title: '贡献占比',
      key: 'percent'
    },
    {
      title: '业绩金额',
      key: 'amount'
    }
  ]
}

export default {
  name: 'orderList',
  components: {
    cardList,
    PayTypeSelect,
    salesSelect,
    Pager,
    Export
  },
  data() {
    return {
      modalColumns: [],
      modalTableData: [],
      packageColumns: [{
        title: '子合同编号',
        key: 'custom_order_sn'
      }, {
        title: '名称',
        key: 'name'
      }, {
        title: '总共',
        key: 'overplus'
      }, {
        title: '价值',
        key: 'amount'
      }, {
        title: '到期时间',
        key: 'end_time'
      }],
      modalTitle: '',
      recordDetail: '',
      showModal: false,
      showRecord: false,
      card: '',
      sale: '',
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      dateOptions: {
        disabledDate: date => {
          const days = this.financeCheckDays
          return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
        },
        shortcuts: [
          {
            text: '一周',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return [start, end];
            }
          },
          {
            text: '一个月',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              return [start, end];
            }
          },
          {
            text: '三个月',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              return [start, end];
            }
          }
        ]
      },
      postData: {
        search: '',
        pay_type: '',
        from_type: '',
        order_sign_status: '',
        consumption_type: '',
        s_date: formatDate(new Date(), 'yyyy-MM-dd'),
        e_date: formatDate(new Date(), 'yyyy-MM-dd'),
        card_id: [],
        card_name: [],
        marketers_id: '',
        marketers_name: '',
        approve_status: '',
        contract_type: '',
        page_no: 1,
        page_size: 10,
        bus_id: ''
      },
      storeList: [],
      tableData: [],
      amount: '',
      totalAmount: '',
      total: '',
      columns: [
        {
          key: 'deal_time',
          title: '成交时间'
        },
        {
          key: 'username',
          title: '购买会员',
          render: (h, params) => {
            const item = params.row
            // const url = `/#/member/detail/${item.user_id}`
            // return <a href={url}>{item.username}</a>
            return <a onClick={() => {
              this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
            }}>{item.username}</a>
          }
        },
        {
          key: 'contract_type',
          title: '合同类型'
        },
        {
          key: 'custom_order_sn',
          title: '合同单号',
          render: (h, params) => {

            return (
              <a
                onClick={() => {
                  this.clickRecordNum(params.row);
                }}>
                {params.row.custom_order_sn}
              </a>
            );
          }
        },
        {
          key: 'cardname',
          title: '会员卡'
        },
        {
          key: 'amount',
          title: '金额',
          render: (h, params) => {
            const item = params.row
            const text = item.from_type == '线上' ? ` [${item.from_type}]` : ''
            return (
              <div>
                <i-button
                  onClick={() => {
                    this.handleDetail(0, item.card_order_id)
                  }}
                  type="text"
                >
                  {item.amount}
                </i-button>
                {text}
              </div>
            )
          }
        },
        {
          key: 'Consumption_type',
          width: 80,
          title: '消费类型'
        },
        {
          key: 'pay_type',
          title: '支付方式',
          render: (h, params) => {
            const item = params.row
            if (item.pay_type.split(',').length > 1) {
              return (
                <i-button
                  onClick={() => {
                    this.handleDetail(1, item.card_order_id)
                  }}
                  type="text"
                >
                  {item.pay_type}
                </i-button>
              )
            } else {
              return <div>{item.pay_type}</div>
            }
          }
        },
        {
          title: '审批状态',
          key: 'approve_status',
          width: 80,
          render: (h, params) => {
            const item = params.row
            return item.approve_status == 1 ? (
              <div>待审</div>
            ) : item.approve_status == 2 ? (
              <div style="color: #d9534f">不通过</div>
            ) : (
              <div style="color: #19be6b">通过</div>
            )
          }
        },
        {
          title: '会员签字',
          key: 'order_sign_status',
          width: 80,
          render: (h, params) => {
            const item = params.row;
            return item.order_sign_status == 0 ? (
              <div>未签</div>
            ) : item.order_sign_status == 1 ? (
              <div>已签</div>
            ) : (
              <div>-</div>
            );
          }
        },
        {
          key: 'sale_name',
          title: '业绩归属',
          render: (h, params) => {
            const item = params.row
            if (item.sale_name.split(',').length > 1) {
              return (
                <i-button
                  onClick={() => {
                    this.handleDetail(2, item.card_order_id)
                  }}
                  type="text"
                >
                  <div
                    title={item.sale_name}
                    style="width: 100px; overflow: hidden; text-overflow: ellipsis"
                  >
                    {item.sale_name}
                  </div>
                </i-button>
              )
            } else {
              return <div>{item.sale_name}</div>
            }
          }
        },
        {
          key: 'description',
          title: '备注',
          render: (h, params) => {
            const item = params.row
            return <div title={item.description}>{item.description}</div>
          }
        }
      ]
    }
  },
  computed: {
    ...mapState(['busId', 'financeCheckDays'])
  },
  created(){
    if (this.$route.query.busId) {
      this.postData.bus_id = this.$route.query.busId
    } else {
      this.postData.bus_id = this.busId
    }
    this.getStoreList()
  },
  methods: {
    ...mapActions(["getAdminInfo", "getReceiptAuth"]),
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    clickRecordNum(item) {
      this.recordDetail = item
      this.showRecord = true;
    },
    handleDetail(index, id) {
      this.modalTitle = MODAL_TITLE[index]
      this.modalColumns = COLUMNS[index] || []
      this.getModalData(index, id)
      this.showModal = true
    },
    getModalData(type, card_order_id) {
      const url = '/Web/Statistics/statistics_card_order_info'
      this.$service
        .post(url, { type, card_order_id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            if (type == 0) {
              this.modalTableData = Object.keys(data)
                .sort()
                .map(key => {
                  return {
                    name: AMOUNT_TYPE[key],
                    value: Number(data[key])
                  }
                })
                .filter(item => item.value != 0)
              return false
            }
            this.modalTableData = data.map(item => {
              if (type == 1) {
                return {
                  ...item,
                  ...{
                    payType: this.$store.getters['pay/getPayNameById'](item.pay_type),
                    amount: `￥${item.amount}`
                  }
                }
              } else {
                return {
                  ...item,
                  ...{
                    isMain: item.is_main === '1' ? '主归属' : '协助',
                    percent: `${item.proportion}%`,
                    amount: `￥${item.amount}`
                  }
                }
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    cardChange(arr) {
      let card_id = [],
        card_name = []
      arr.forEach(item => {
        card_id.push(item.card_id)
        card_name.push(item.card_name)
      })
      this.postData = { ...this.postData, ...{ card_id, card_name } }
    },
    saleChange({ value: marketers_id, label: marketers_name }) {
      this.postData = { ...this.postData, ...{ marketers_id, marketers_name } }
    },
    dateChange([s_date, e_date]) {
      this.postData = { ...this.postData, ...{ s_date, e_date } }
    },
    pageChange(postData) {
      if (this.$route.query.id) {
         const params = this.$route.query
        this.dateRange = [ params.s_date, params.e_date]
        this.postData = {
          ...this.postData,
          ...{
            s_date: params.s_date,
            e_date: params.e_date,
            marketers_id: params.id,
            marketers_name: params.name
          }
        }
        this.$route.query.id = ''
      } else {
        const { s_date, e_date } = postData
        this.dateRange = [s_date, e_date]
        this.postData = { ...this.postData, ...postData }
      }
      this.getList()
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      let curData = Object.assign({}, this.postData)
      curData.card_id = curData.card_id.join(',')
      curData.card_name = curData.card_name.join(',')
      const url = '/Web/Statistics/cardOrderList'
      this.$service
        .post(url, curData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.tableData = data.OrderList || []
            if (!data.OrderList || !data.OrderList.length) {
              this.$Message.error('未查询到数据')
              this.amount = 0
              this.totalAmount = 0
              this.total = 0
              return
            }
            this.calPageAmount()
            this.totalAmount = data.amount
            this.total = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    calPageAmount() {
      this.amount = this.tableData
        .reduce((total, item) => {
          if (item.approve_status === '2') {
            return total
          } else {
            return total + Number(item.amount)
          }
        }, 0)
        .toFixed(2)
    },
    exportCsv() {
      return this.$service
        .post('/Web/Statistics/cardOrderListExport', {
          ...this.postData,
          ...{
            card_id: this.postData.card_id.join(','),
            card_name: this.postData.card_name.join(','),
            page_size: this.total,
            _export: 1,
            page_no: 1
          }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success({
              content:'导出任务运行中，请稍后到消息中心下载',
              duration: 3
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>

<style scoped lang="less">
.select {
  width: 120px;
}
.total {
  font-size: 16px;
  padding: 20px 20px 0;
  display: flex;
  justify-content: flex-end;
  > p {
    padding-left: 60px;
    padding-right: 20px;
    span {
      font-weight: bold;
    }
  }
}
</style>
