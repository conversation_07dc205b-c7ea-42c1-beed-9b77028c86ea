<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" style="margin-right:20px;width:200px" @on-change="handleStoreChange" filterable>
        <Option
          v-for="item in adminBusList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input class="w120" v-model="postData.name" placeholder="姓名" />
      <!-- <Select v-model="postData.del" style="margin-right:20px;width:160px">
        <Option value="0" >正常</Option>
        <Option value="1" >删除</Option>
      </Select> -->
      <JobSelect v-model="postData.job_staff" :busId="postData.bus_id"></JobSelect>
      <DatePicker class="w120" type="month" :value="postData.m_date" @on-change="dateChange" placeholder="选择月份"></DatePicker>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { formatDate } from 'utils';
  import JobSelect from 'components/form/jobSelect';
  import Export from 'src/components/Export';
  import { mapState, mapGetters } from 'vuex'

  export default {
    name: 'salaryList', // 薪资报表
    components: {
      JobSelect,
      Pager,
      Export
    },
    data() {
      return {
        postData: {
          bus_id: '',
          name: '',
          // del: '0', // 0正常 1删除
          job_staff: '',
          m_date: '',
          page_no: 1,
          page_size: 10,
        },
        tableData: [],
        total: '',
        columns: [
          {
            key: 'name',
            title: '姓名'
          },
          {
            key: 'job_names',
            title: '职务'
          },
          {
            key: 'salemoney',
            title: '销售业绩'
          },
          {
            key: 'sale_ratio',
            title: '业绩提成比例'
          },
          {
            key: 'count_openclass',
            title: '团操课节数'
          },
          {
            key: 'count_private_class',
            title: '私教课节数'
          },
          {
            key: 'count_swimming_class',
            title: '泳教课节数'
          },
          {
            key: 'basic_salary',
            title: '底薪'
          },
          {
            key: 'cost_openclass',
            title: '团操课时费'
          },
          {
            key: 'sale_p_money',
            title: '业绩提成'
          },
          {
            key: 'cost_private_class',
            title: '私教上课提成'
          },
          {
            key: 'cost_swimming_class',
            title: '泳教上课提成'
          },
          {
            key: 'total_money',
            title: '总计'
          }
        ],
        storeList: []
      };
    },
    computed: {
      ...mapState(['busId']),
    ...mapGetters(['adminBusList']),
    },
    created() {
      this.postData.bus_id = this.busId
      this.getStoreList()
      this.postData.m_date = this.getLastMonth()
    },
    methods: {
      handleStoreChange() {

      },
      getStoreList() {
        !this.adminBusList && this.$store.dispatch('getAdminBusList');
        // return this.$service
        //   .get('/Web/Business/get_bus_list')
        //   .then(res => {
        //     if (res.data.errorcode === 0) {
        //       this.storeList = res.data.data.bus_list
        //     }
        //   })
      },
      getLastMonth() {
        let curTime = new Date(), month, lastMonth, year, lastYear, dateStr;
         month= curTime.getMonth();
         year = curTime.getFullYear();
         lastMonth = month==0 ? 12 : month;
         lastYear = month==0 ? year-1 : year
         dateStr = `${lastYear}-${lastMonth}`
         return dateStr
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        console.log(info)
        this.postData.m_date = info
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        this.$service
          .post('/Web/SalaryRule/get_salary_list', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data;
              this.total = res.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        return this.$service
          .post('/Web/SalaryRule/get_salary_list', { ...this.postData, ...{ page_size: this.total, page_no: 1 } })
          .then(res => {
            if (res.data.errorcode === 0) {
              return res.data.data
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const exportData = await this.getExportData();
        this.$refs.export.export({
          filename: `薪资报表(${this.postData.m_date})`,
          columns: this.columns,
          data: exportData
        });
      }
    }
  };
</script>

<style scoped lang="less">
  .select {
    width: 120px;
  }
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
</style>
