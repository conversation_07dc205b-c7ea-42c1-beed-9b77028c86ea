<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input class="w120" v-model="postData.name" @on-enter="doSearch" placeholder="姓名" />
      <JobSelect v-model="postData.job_staff" :busId="postData.bus_id"></JobSelect>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table @on-select="selectMember" @on-selection-change="selectMemberChange" @on-select-all="selectMemberAll" @on-select-cancel="selectMemberCancel" ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    <footer>
      <Button type="success" @click="salarySet" :disabled="postData.bus_id!==busId">批量设置薪资</Button>
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>

    <Modal v-model="showModal" title="复制" :mask-closable="false" @on-cancel="showModal=false">
      <div class="name-tips">
        将<span>{{modalInfo.copy_name}}</span>的薪资规则复制给
      </div>
      <Form ref="assignForm" class="modal-form" style="padding: 0 10px" :label-width="50">
        <FormItem label="人员">
          <Select v-model="modalInfo.to_ids" placeholder="选择人员" filterable multiple>
            <Option v-for="item in employeeList" :key="item.id" :label="item.name" :value="item.id">
            </Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="doCopy">确定</Button>
        <Button @click="showModal=false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { Poptip } from 'iview';
  import JobSelect from 'components/form/jobSelect';
  import { mapState } from 'vuex'
  export default {
    name: 'salarySetList',
    components: {
      JobSelect,
      Pager
    },
    data() {
      return {
        showModal: false,
        modalInfo: {
          copy_id: '',
          copy_name: '',
          to_ids: ''
        },
        selectedMembers: [],
        employeeList: [],
        postData: {
          name: '',
          job_staff: '',
          page_no: 1,
          page_size: 10,
          bus_id: ''
        },
        jumpBusId: '',
        storeList: [],
        tableData: [],
        total: '',
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            key: 'name',
            title: '姓名'
          },
          {
            key: 'job_names',
            title: '职务'
          },
          {
            key: 'basic_salary',
            title: '底薪',
            render: (h, { row }) => {
              const { basic_salary, salary_rule } = row;

              if (!salary_rule || !salary_rule.rule.length) {
                return <div>{basic_salary || 0}元</div>
              }
              const { type, rule: list } = salary_rule;
              if (type == 1) { // 固定
                return <div>{list[0].base_salary || 0}元</div>
              }

              /* 浮动 */
              let divCount = 0, divContent;
              const rules = list.map(({ achievement, class_hour, base_salary }) => {
                let achievementDes, hourDes, des;
                if (achievement) {
                  if (achievement.min && achievement.max) {
                    achievementDes = `业绩${achievement.min}~${achievement.max}元`
                  } else if (achievement.min) {
                    achievementDes = `业绩>=${achievement.min}元`
                  } else if (achievement.max) {
                    achievementDes = `业绩<=${achievement.max}元`
                  }
                }

                if (class_hour) {
                    if (class_hour.min && class_hour.max) {
                      hourDes = `课时${class_hour.min}~${class_hour.max}节`
                    } else if (class_hour.min) {
                      hourDes = `课时>=${class_hour.min}节`
                    } else if (class_hour.max) {
                      hourDes = `课时<=${class_hour.max}节`
                    }
                }

                if (achievementDes && hourDes) {
                  des = `${achievementDes} & ${hourDes}`
                  divCount += 2
                } else if (achievementDes || hourDes){
                  des = achievementDes || hourDes
                  divCount += 1
                }

                return <div>{ `${des} ${base_salary}元` }</div>
              });
              if(divCount > 5) {
                divContent = (
                  <Poptip trigger="hover" placement="left">
                  <div style="height: 105px;overflow:hidden;">{rules}</div><div>...</div>
                  <div slot="content" style="max-height: 300px; overflow-y: auto">
                    <div>{rules}</div>
                  </div>
                  </Poptip>
                )
              } else {
                  divContent = (<div style="max-height: 105px;">{rules}</div>)
              }
              return divContent
            }
          },
          {
            title: '团操课时费',
            render: (h, param) => {
              const tkRule = param.row.commission_rule.tk_rule
              if (!tkRule || !tkRule.length) {
                return <div>-</div>
              }
              const rules = tkRule.map((data) =>
                <div>{data.name} {data.price}元/节</div>
              );

              let divContent;
              if(tkRule && tkRule.length >= 5) {
               divContent = (
                 <Poptip trigger="hover" placement="left">
                 <div style="height: 105px;overflow:hidden;">{rules}</div><div>...</div>
                  <div slot="content" style="max-height: 300px; overflow-y: auto">
                    <div>{rules}</div>
                  </div>
                 </Poptip>
                 )
              } else {
                 divContent = (<div style="max-height: 105px;">{rules}</div>)
              }

              return divContent
            }
          },
          {
            title: '销售提成',
            render: (h, param) => {
              const saleRule = param.row.commission_rule.sale_rule
              if (!saleRule || !saleRule.length) {
                return <div>-</div>
              }
              const rules = saleRule.map((data) =>
                <div>{data.min}~{data.max} 提{data.ratio}%</div>
              );
              return <div>{rules}</div>
            }
          },
          {
            title: '私教/泳教课课时提成',
            width: 250,
            render: (h, param) => {
              const priclassRule = param.row.commission_rule.priclass_rule
              if (!priclassRule || !priclassRule.length) {
                return <div>-</div>
              }
              let divCount = 0, divContent;
              const rules = priclassRule.map((item) => {
                let priceDes, classDes, des;
                if (item.achievement.min && item.achievement.max) {
                  priceDes = `${item.achievement.min}~${item.achievement.max}元`
                } else if (item.achievement.min) {
                  priceDes = `>=${item.achievement.min}元`
                }  else if (item.achievement.max) {
                  priceDes = `<=${item.achievement.max}元`
                }

                if (item.class.min && item.class.max) {
                  classDes = `消课总数${item.class.min}~${item.class.max}节`
                } else if (item.class.min) {
                  classDes = `消课总数>=${item.class.min}节`
                } else if (item.class.max) {
                  classDes = `消课总数<=${item.class.max}节`
                }
                if (priceDes && classDes) {
                  des = `${priceDes} & ${classDes}`
                  divCount += 1
                } else if (priceDes || classDes){
                  des = priceDes || classDes
                  divCount += 1
                }

                let curDes = [];
                if (item.method.classtype == 1) {
                  curDes.push(<div>{ `消课价值提 ${item.method.ratio}%` }</div>)
                  divCount += 1
                  if (item.method.pt_month == 1) {
                    curDes.push(<div>{ `包月课程 独立按结算单价提成` }</div>)
                    divCount += 1
                  } else if(item.method.pt_month == 2)  {
                    curDes.push(<div>{ `包月课程提 ${item.method.pt_sign_cost_pro}%` }</div>)
                    divCount += 1
                  }
                }
                if (item.method.classtype == 2) {
                  item.method.classrule.forEach(data => {
                    if (data.price || data.price === 0) {
                      divCount += 1
                      curDes.push(<div>{data.name} {data.price}元/节</div>)
                    }
                    if (data.sign_pro || data.sign_pro === 0) {
                       divCount += 1
                       curDes.push(<div>{data.name} {data.sign_pro}%</div>)
                    }
                    if (data.is_pt_time_limit_card == 1 && data.cost_type == 1) {
                      divCount += 1
                      curDes.push(<div>{ `${data.name} 按课程结算单价` }</div>)
                    }
                  });
                }
                return <div style="margin-top: 15px">{des} <br /> {curDes}</div>
              });
              if(divCount >= 5) {
               divContent = (
                 <Poptip trigger="hover" placement="left">
                  <div style="height: 116px;overflow:hidden;">{rules}</div><div>...</div>
                  <div slot="content" style="max-height: 300px; overflow-y: auto">
                    <div>{rules}</div>
                  </div>
                 </Poptip>
                 )
              } else {
                 divContent = (<div style="max-height: 116px;">{rules}</div>)
              }
              return divContent
            }
          },
          {
          title: '操作',
          render: (h, param) => {
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  disabled={this.jumpBusId!==this.busId}
                  onClick={() => {
                    this.$router.push({
                      name: '薪资设置',
                      params: {
                        selectedMembers: [{
                          id: param.row.id,
                          name: param.row.name
                        }],
                        editData: param.row,
                        isEdit: true,
                        busId: this.jumpBusId
                      }
                    })
                  }}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                  size="small"
                  style="margin-left: 10px"
                  disabled={!param.row.commission_rule||this.jumpBusId!==this.busId}
                  onClick={() => {
                    this.modalInfo.copy_id = param.row.id
                    this.modalInfo.copy_name = param.row.name
                    this.modalInfo.to_ids = []
                    this.showModal = true
                  }}
                >
                  复制
                </i-button>
              </div>
            )
          }
        }
        ]
      };
    },
    computed: {
      ...mapState(['busId']),
      selectedUserIds() {
        let userIds = []
        this.selectedMembers.forEach(user => {
          userIds.push(user.id)
        })
        return userIds
      }
    },
    created() {
      this.postData.bus_id = this.busId
      this.jumpBusId = this.busId
      this.getStoreList()
      this.getEmployeeList();
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      doCopy() {
        const { to_ids, ...rest } = this.modalInfo;
        if(to_ids.length > 0) {
          const params = {
            to_ids: to_ids.join(','),
            ...rest
          }
          this.$service
            .post('/Web/SalaryRule/copy_salary_rule', params)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showModal = false
                this.$Message.success(res.data.errormsg);
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(this.$Message.error)
        }
      },
      salarySet() {
        if(this.selectedMembers.length === 0) {
          this.$Message.error('请先选择人员');
          return false;
        }
        this.$router.push({
          name: '薪资设置',
          params: { selectedMembers: this.selectedMembers, busId: this.jumpBusId }
        })
      },
      selectMember(selection, member) {
        let selUserIds = this.selectedUserIds
        if (selUserIds.indexOf(member.id) === -1) {
          this.selectedMembers.push(member)
        }
      },
      selectMemberCancel(selection, member) {
        this.selectedMembers.forEach((user, index) => {
          if (user.id == member.id) {
            this.selectedMembers.splice(index, 1)
          }
        })
      },
      selectMemberChange(selection) {
        if (selection.length == 0) {
          this.tableData.forEach(member => {
            this.selectMemberCancel(selection, member)
          })
        }
      },
      selectMemberAll(selection) {
        if (selection.length > 0) {
          selection.forEach(member => {
            this.selectMember(selection, member)
          })
        }
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange([s_date, e_date]) {
        this.postData = { ...this.postData, ...{ s_date, e_date } };
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
        this.getEmployeeList()
      },
      getList() {
        this.$service
          .post('/Web/SalaryRule/get_salary_rule_list', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data;
              this.total = res.data.count;
              this.tableData.forEach((user, index) => {
                if (this.selectedUserIds.indexOf(user.id) >= 0) {
                  user._checked = true
                }
              })
              this.jumpBusId = this.postData.bus_id
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getEmployeeList() {
        this.$service
          .post('/Web/SalaryRule/get_employee_list', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.employeeList = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style scoped lang="less">
  .select {
    width: 120px;
  }
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
   .name-tips {
    text-align: center;
    margin-bottom: 35px;
    font-size: 14px;
    span {
      color: #d9534f;
    }
  }

  .ivu-table-wrapper {
    overflow: unset;
    .ivu-table {
      overflow: unset;
    }
  }
</style>
