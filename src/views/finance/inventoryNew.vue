<template>
<div>
  <Alert type="warning" show-icon class="inventory-alert" closable>
      <span>由于技术更新,2021-1-1日之前的数据需要到旧版本中查询</span>
      <router-link to="/finance/inventory" target="_blank">
        查询历史数据
      </router-link>
    </Alert>

  <div class="box">
    
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
      <Select v-model="selectBusId" style="width: 200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="业务单号/姓名/电话" />
      <DatePicker v-model="duringDate" :options="dateOptions" type="daterange" placement="bottom-start" placeholder="选择日期" class="option-select" style="width: 220px"></DatePicker>
      <Select v-model="flowType" class="option-select" placeholder="支出/收入">
        <Option v-for="item in flowTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="operateType" class="option-select" placeholder="全部事项" filterable>
        <Option v-for="item in operateTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <!-- <Select v-model="saleId" class="option-select" placeholder="销售人员">
        <Option v-for="item in saleList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select> -->
      <AdminRegion 
        v-model="saleList" url="/Web/Statistics/getGroupSales" 
        :busId="selectBusId" 
        :multiple="false" 
        placeholder="销售人员" 
        style="width: 180px;max-width: none; margin-right: 20px;"
        :hasStore="true"/>
      <PayTypeSelect v-model="payType"
                      style="width: 180px;max-width: none; margin-right: 20px;"
                     :showCardPay="true"
                     :busId="selectBusId"
                      placeholder="付费方式" :needShowDragonFly="false">
        <Option value="-1">全部</Option>
      </PayTypeSelect>
      <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <div class="box-body-total" v-if="totalStat && totalStat.length">
      <div class="t-label">总计</div>
      <div class="t-desc">
        <div v-for="item in totalStat" :key="item.pay_name">{{item.pay_name}}:{{item.pay_amount}}</div>
      </div>
    </div>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button v-if="hasExportAuth" @click="handleExcel">导出Excel</Button>
        <Export ref="export">导出Excel</Export>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>

    <Modal v-model="orderDetailModal" title="成单明细">
      <Table ref="table" class="avatar-zoom" stripe :columns="orderDetailCols" :data="orderDetailList" disabled-hover></Table>
      <div slot="footer"></div>
    </Modal>
    <Modal v-model="payDetailModal" title="支付方式">
      <Table ref="table" class="avatar-zoom" stripe :columns="payDetailCols" :data="payDetailList" disabled-hover></Table>
      <div slot="footer"></div>
    </Modal>
  </div>
</div>
</template>
<script>
import { mapState } from 'vuex'
import Export from 'src/components/Export'
import AdminRegion from 'components/form/adminRegion.vue'
import PayTypeSelect from 'components/form/PayTypeSelect'
export default {
  components: { Export, AdminRegion, PayTypeSelect },
  data() {
    return {
      orderDetailModal: false,
      hasAmountPayTypeIds: [],
      orderDetailCols: [
        { title: '姓名', key: 'name' },
        { title: '充当角色', key: 'role' },
        { title: '贡献占比', key: 'percent' },
        { title: '业绩金额', key: 'amount' }
      ],
      orderDetailList: [],
      payDetailModal: false,
      payDetailCols: [
        { title: '支付方式', key: 'pay_type' },
        { title: '支付金额', key: 'amount' }
      ],
      payDetailList: [],
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      duringDate: [],
      flowType: '',
      flowTypeList: [
        { value: -1, label: '全部' },
        { value: 1, label: '收入' },
        { value: 2, label: '支出' }
      ],
      operateType: '',
      operateTypeList: [
        { value: -1, label: '全部' },
        { value: 0, label: '购卡' },
        { value: 1, label: '续卡' },
        { value: 21, label: '购私教' },
        { value: 22, label: '续私教' },
        { value: 23, label: '购泳教' },
        { value: 24, label: '续泳教' },
        { value: 2, label: '升卡' },
        { value: 3, label: '转入卡' },
        { value: 5, label: '请假' },
        { value: 6, label: '销卡' },
        { value: 7, label: '租柜' },
        { value: 8, label: '商品售卖' },
        { value: 27, label: '商品退款' },
        { value: 9, label: '定金' },
        { value: 10, label: '退定金' },
        { value: 11, label: '押金' },
        { value: 12, label: '退押金' },
        { value: 13, label: '在线购课' },
        { value: 15, label: '补卡' },
        { value: 16, label: '付费活动' },
        { value: 17, label: '拆分' },
        { value: 18, label: '跨店购卡' },
        { value: 19, label: '场地预订' },
        { value: 20, label: '场地退订' },
        { value: 29, label: '票务' }
      ],
      payType: '',
      saleId: '',
      saleList: [],
      totalStat: [],
      recorderList: [],
      columns: [
        { title: '业务单号', key: 'flow_sn' },
        { title: '时间', key: 'date' },
        { title: '事项', key: 'thing' },
        {
          title: '会员',
          key: 'membership',
          render: (h, params) => {
            // let name = params.row.membership
            // if (Number(params.row.userId) <= 0) {
            //   return <div>{name}</div>
            // } else {
            //   return h(
            //     'a',
            //     {
            //       on: {
            //         click: () => {
            //           this.$router.push({
            //             path: `/member/detail/${params.row.userId}`
            //           })
            //         }
            //       }
            //     },
            //     name
            //   )
            // }
            const item = params.row
            if (item.is_real_user) {
              return <a onClick={() => {
                this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
              }}>{item.username}</a>
            } else {
              return <span>{item.username}</span>
            }
          }
        },
        { title: '应收金额', key: 'planAmount' },
        { title: '定金抵扣', key: 'preAmount' },
        { title: '实收', key: 'finalAmount' },
        { title: '描述', key: 'desc' },
        {
          title: '销售人员',
          key: 'sale',
          render: (h, params) => {
            return h(
              'a',
              {
                on: {
                  click: () => {
                    this.orderDetailModal = true
                    this.orderDetailList = params.row.marketers_detail
                  }
                }
              },
              params.row.sale
            )
          }
        },
        {
          title: '付费方式',
          key: 'payType',
          render: (h, params) => {
            return h(
              'a',
              {
                on: {
                  click: () => {
                    this.payDetailModal = true
                    this.payDetailList = params.row.pay_detail
                  }
                }
              },
              params.row.payType
            )
          }
        }
      ],
      list: [],
      searchTxt: '',
      hasExportAuth: true,
      dateOptions: {
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date()
              const start = new Date()
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              return [start, end]
            }
          },
          {
            text: '一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            }
          },
          {
            text: '一个月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            }
          }
        ],
        disabledDate: date => {
          const days = this.financeCheckDays
          return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
        }
      },
      curTotal: '',
      allTotal: '',
      saleCount: 0,
      storeList: [],
      selectBusId: ''
    }
  },
  watch: {
    saleList: function(val, old) {
      if (val === undefined) {
        //以前点击select框的清除按钮时不能清除，只有这么搞了
        this.saleId = ''
        this.getList()
        return
      }
      val = String(val)
      const saleIdArr = val.split('_')
      if (Array.isArray(saleIdArr) && saleIdArr.length === 3) {
        this.saleId = saleIdArr[0]
        this.getList()
        //   this.saleList = [];
      }
    }
  },
  computed: {
    ...mapState('pay', ['payTypes']),
    ...mapState(['busId','financeCheckDays'])
  },
  methods: {
    // ...mapActions(["getAdminInfo", "getReceiptAuth"]),
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getDateString(date) {
      if (!!date) {
        date = new Date(date)
        const year = date.getFullYear()
        let month = date.getMonth() + 1
        month = month < 10 ? '0' + month : month
        let day = date.getDate()
        day = day < 10 ? '0' + day : day
        return `${year}-${month}-${day}`
      } else {
        return ''
      }
    },
    packRow(item, isExp) {
      let saleArr = {}
      if (item.marketers_detail && (item.marketers_detail.length > this.saleCount)) {
        this.saleCount = item.marketers_detail.length
      }
      let sale = ''
      if(item.marketers_detail) {
        item.marketers_detail.forEach((m, i) => {
          saleArr['sale' + i] = m.name
          saleArr['saleRate' + i] = m.percent
          saleArr['saleAmount' + i] = m.amount
        })
        item.marketers_detail.forEach((m, i) => {
          sale += m.name + m.percent
          if (i + 1 !== item.marketers_detail.length) {
            sale += '; '
          }
        })
      }
      
      let payType = ''
      if(isExp) {
        this.payTypes.forEach((p, i) => {
          saleArr[Number(p.pay_type_id)] = 0
          item.pay_detail.forEach((subP, subI) => {
            if(this.hasAmountPayTypeIds.indexOf(parseInt(subP.pay_type_id)) === -1 && subP.pay_type_id) {
              this.hasAmountPayTypeIds.push(parseInt(subP.pay_type_id))
            }
            if(Number(p.pay_type_id) === Number(subP.pay_type_id)) {
              saleArr[Number(p.pay_type_id)] = subP.amount
            }
          })
        })
      } else {
        item.pay_detail.forEach((p, i) => {
          saleArr[Number(p.pay_type_id)] = p.amount || 0
          payType += p.pay_type + p.amount
          if (i + 1 !== item.pay_detail.length) {
            payType += '; '
          }
        })
      }
      return {
        ...item,
        rowId: item.id,
        userId: item.user_id,
        date: item.deal_time,
        billType: item.flow_type,
        thing: item.operate_type,
        membership: item.username,
        planAmount: item.amount,
        preAmount: item.pre_payment,
        finalAmount: item.income_amount,
        desc: item.description,
        payType: payType,
        ...saleArr,
        sale
      }
    },
    // getSaleList() {
    //   return this.$service.get('/Web/statistics/getSales').then(res => {
    //     if (res.data.errorcode == 0) {
    //       this.saleList = [{ id: -1, name: '全部' }].concat(res.data.data);
    //     } else {
    //       this.$Message.error(res.data.errormsg);
    //     }
    //   });
    // },
    handleSearch() {
      this.currentPage = 1
      if (this.duringDate.length === 2) {
        const start = this.duringDate[0]
        const end = this.duringDate[1]
        this.duringDate = [this.getDateString(start), this.getDateString(end)]
      }
      this.getList()
    },
    handlePage(val) {
      this.currentPage = val
      this.getList()
    },
    getList() {
      if (this.duringDate.length !== 2 || !this.duringDate[0]) {
        this.$Message.error('请选择日期！')
        return false
      } else {
        const start = this.duringDate[0]
        const end = this.duringDate[1]
        this.duringDate = [this.getDateString(start), this.getDateString(end)]
      }
      return this.$service
        .post(
          '/Web/statistics/getFinancialFlowNew',
          {
            search: this.searchTxt,
            begin_date: this.duringDate[0],
            end_date: this.duringDate[1],
            page_no: this.currentPage,
            page_size: this.pageSize,
            flow_type: this.flowType == -1 ? '' : this.flowType,
            pay_type: this.payType == -1 ? '' : this.payType,
            sale_id: this.saleId == -1 ? '' : this.saleId,
            operate_type: this.operateType == -1 ? '' : this.operateType,
            bus_id: this.selectBusId
          },
          { timeout: 30000 }
        )
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            let arr = []
            res.data.data.list.forEach(item => {
              arr.push(this.packRow(item))
            })
            this.list = arr
            this.total = parseInt(res.data.data.count)
            this.totalStat = res.data.data.total_stat
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    exportOne(bbdate, eedate) {
      this.$service
        // .post('/Web/statistics/getFinancialFlowNew', {
        // ID=9139 业务流水导出调用接口地址变更：/Web/statistics/exportFlow
        .post('/Web/statistics/exportFlow', {
          search: this.searchTxt,
          begin_date: bbdate,
          end_date: eedate,
          page_no: 1,
          page_size: this.total,
          flow_type: this.flowType == -1 ? '' : this.flowType,
          pay_type: this.payType == -1 ? '' : this.payType,
          sale_id: this.saleId == -1 ? '' : this.saleId,
          operate_type: this.operateType == -1 ? '' : this.operateType,
          bus_id: this.selectBusId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (!Array.isArray(res.data.data.list)) {
              this.list = []
              return false
            }
            let arr = []
            res.data.data.list.forEach(item => {
              if (item.flow_category == '定金') {
                item.marketers_detail.forEach(little => {
                  little.percent = ''
                  little.amount = ''
                })
              }
              arr.push(this.packRow(item, true))
            })

            const all = res.data.data.total_stat
            let totalObject= {}
            
            all.forEach(item=> {
              if(item.pay_type_id) {
                totalObject[item.pay_type_id] = item.pay_amount
              }
            })
            arr.push({})
            arr.push({
              ...totalObject,
              date: '总计',
              billType:
                '实收:' +
                all[0].pay_amount +
                '; ' +
                '支出:' +
                all[1].pay_amount,
              finalAmount: all[0].pay_amount
            })
            let exColumns = [
              { title: '时间', key: 'date' },
              { title: '业务单号', key: 'flow_sn' },
              { title: '流水类型', key: 'flow_category' },
              { title: '合同编号', key: 'custom_order_sn' },
              { title: '支出/收入', key: 'billType' },
              { title: '事项', key: 'thing' },
              { title: '卡种', key: 'card_name' },
              { title: '会员', key: 'membership' },
              { title: '应收金额', key: 'planAmount' },
              { title: '定金抵扣', key: 'preAmount' },
              { title: '实收', key: 'finalAmount' },
              { title: '描述', key: 'desc' },
              { title: '备注', key: 'remark' },
            ]
            this.payTypes.forEach((item)=>{
              if(item.usable !== 0 || this.hasAmountPayTypeIds.indexOf(parseInt(item.pay_type_id)) !== -1) {
                exColumns.push({ title: item.pay_type_name, key: item.pay_type_id })
              }
            })

            if (this.saleCount > 0) {
              for (let i = 0; i < this.saleCount; i++) {
                exColumns.push({ title: '销售人员', key: 'sale' + i })
                exColumns.push({ title: '比例', key: 'saleRate' + i })
                exColumns.push({ title: '金额', key: 'saleAmount' + i })
              }
            }

            this.$refs.export.export({
              filename: `日报流水-${this.getDateString(new Date())}`,
              columns: exColumns,
              data: arr,
              quoted: true
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    exportTwo(bbdate, eedate) {
      this.$service
        .post('/Web/Statistics/tableTwo', {
          search: this.searchTxt,
          begin_date: bbdate,
          end_date: eedate,
          // page_no: 1,
          // page_size: this.total,
          flow_type: this.flowType == -1 ? '' : this.flowType,
          pay_type: this.payType == -1 ? '' : this.payType,
          sale_id: this.saleId == -1 ? '' : this.saleId,
          operate_type: this.operateType == -1 ? '' : this.operateType,
          bus_id: this.selectBusId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            let list = []
            if (!Array.isArray(res.data.data)) {
              return false
            } else {
              const curBetweenDate = bbdate + '至' + eedate
              const summary = res.data.total
              res.data.data.forEach(item => {
                item.curBetweenDate = curBetweenDate
                list.push(this.packRow(item, true))
              })
              list.push({})
              const all = res.data.total.pay_type_amount
              let totalObject= {}
              all.forEach(item=> {
                if(item.pay_type_id) {
                  totalObject[item.pay_type_id] = item.amount
                }
              })
              list.push({
                ...res.data.total,
                ...totalObject,
                curBetweenDate: '总计'
              })
            }
            let exColumns = [
              { title: '日期', key: 'curBetweenDate' },
              { title: '事项', key: 'name' },
              { title: '会员卡', key: 'card_name' },
              { title: '数量', key: 'count' },
              { title: '应收', key: 'amount' },
              { title: '定金抵扣', key: 'pre_payment' },
              { title: '实收', key: 'income_amount' }
            ]
            this.payTypes.forEach((item)=>{
              if(item.usable !== 0 || this.hasAmountPayTypeIds.indexOf(parseInt(item.pay_type_id)) !== -1) {
                exColumns.push({ title: item.pay_type_name, key: item.pay_type_id })
              }
            })

            this.$refs.export.export({
              filename: `上海交大-日报流水-${this.getDateString(new Date())}`,
              columns: exColumns,
              data: list,
              quoted: true
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleExcel() {
      let bbdate = this.duringDate[0],
        eedate = this.duringDate[1]
      if (typeof bbdate != 'string') {
        bbdate = this.getDateString(bbdate)
        eedate = this.getDateString(eedate)
      }
      this.exportOne(bbdate, eedate)

      if (this.$store.state.busName.indexOf('上海交通大学') !== -1) {
        this.exportTwo(bbdate, eedate)
      }
    },
    // getExcelAuth() {
    //   this.$service.get('').then(res => {
    //     if (res.data.errorcode === 40014) {
    //       this.hasExportAuth = false;
    //     }
    //   });
    // },
    pageSizeChanged(size) {
      this.currentPage = 1
      this.pageSize = size
      this.getList()
    }
  },
  created() {
    const time = this.$route.query.time || ''
    const end = time?new Date(time) : new Date()
    const start = time?new Date(time) : new Date()
    this.searchTxt = this.$route.query.search || ''

    const { beginTime, endTime, busId } = this.$route.params
    if (beginTime && endTime) {
      this.duringDate = [beginTime, endTime]
    } else {
      this.duringDate = [this.getDateString(start), this.getDateString(end)]
    }
    if (busId) {
      this.selectBusId = busId
    } else {
      this.selectBusId = this.busId
    }

    this.getStoreList()
    this.getList()
    // this.getSaleList();
    // this.getExcelAuth();
  }
}
</script>
<style lang="less">
.inventory-alert {
  margin-bottom: 15px;
  .ivu-alert-message {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
.warn-sj {
  font-size: 14px;
  color: red;
  margin-left: 20px;
}

.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-body-total {
    border: 1px solid #dddee1;
    border-top: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

      .t-label {
        width: 200px;
        padding: 0 40px;
        font-size: 16px;
        font-weight: bold;
      }

      .t-desc {
        width: 70%;
        max-width: 650px;
        font-size: 16px;
        display: flex;
        flex-wrap: wrap;
        div {
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          width: 25%;
          display: flex;
        }
      }
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
    }
  }
}
</style>
