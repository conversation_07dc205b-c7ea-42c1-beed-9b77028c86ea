<style lang="less">
  .class-stat-modal .coach-info {
    display: flex;
    align-items: center;
    padding-bottom: 24px;
    font-size: 14px;
    font-weight: bold;

    img {
      width: 70px;
      height: 70px;
      margin-right: 10px;
      border-radius: 50%;
      overflow: hidden;
    }
  }

  .class-stat {
    .no-padding {
      padding: 0;
    }
  }

  .group-select {
    width: 200px;
    margin-left: 30px;

    .group {
      font-size: 14px !important;
      font-weight: bold;
    }

    .ivu-select-item-selected,
    .ivu-select-item-selected:hover {
      color: #fff;
      background: rgba(45, 140, 240, 0.9);
    }

    .ivu-select-group-title {
      display: none;
    }
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<template>
  <div class="table-wrap class-stat">
    <header>
      <DatePickerWithButton select="今天" :options="options" @on-change="dateChange"></DatePickerWithButton>
      <Select v-model="busId" @on-change="handleBusChange" style="margin-left:130px;">
        <Option v-for="bus in adminBusList" :value="bus.id" :key="bus.id">{{bus.name}}</Option>
      </Select>
    </header>
    <Table ref="table" :data="tableData" class="avatar-zoom" :columns="tableCols" border></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export"></Export>
      <Button @click="editTableFlag = true" style="margin-left: 20px;">更改表格样式</Button>
    </footer>
    <Modal v-model="editTableFlag" title="更改表格样式" width="800" @on-ok="handleSaveClick">
        <Table :columns="defaultRowColumn" :data="showRowData"></Table>
        <Button type="primary" style="margin: 20px 0;" @click="handleAddRowClick">添加</Button>
        <Table :columns="defaultColColumn" :data="showColData"></Table>
        <Button type="primary" style="margin: 20px 0;" @click="handleAddColClick">添加</Button>
    </Modal>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import DatePickerWithButton from 'components/picker/datePickerWithButton';
  import Export from 'src/components/Export';

  const TYPE_EXPORT_COLS = ['exportOpen', 'exportTeam', 'exportPrivate'];
  const LI_HEIGHT = 20;

  export default {
    name: 'totalOfFlowWater',
    components: {
      DatePickerWithButton,
      Export
    },
    computed: {
      ...mapState(['adminBusList','financeCheckDays']),
    },
    data() {
      return {
        options: {
          disabledDate: date => {
            const days = this.financeCheckDays
            return (days ? (date.getTime() - Date.now() < days * -24 * 60 * 60 * 1000) : false)|| (date.valueOf() > Date.now())
          }
        },
        dateRange: [],
        exportColumns: [],
        exportTableData: [],

        busId: '',
        busList: [],
        tableData: [],
        tableCols: [{title: '类型 \\ 支付', key: -1}],

        // update table construct.
        editTableFlag: false,
        rowArr: [],
        colArr: [],
        defaultRowColumn: [
            {
                title: '类型名称',
                key: 'name',
                render: (h, params) => {
                    const handleUpdateTypeName = event => {
                        params.row.group_name = event.target.value;
                    };
                    if (params.row.editFlag) {
                        return (<div>
                            <Input value={params.row.group_name} onOn-change={handleUpdateTypeName} />
                        </div>);
                    } else {
                        return (<div>
                            <label>{params.row.group_name}</label>
                        </div>);
                    }
                }
            },
            {
                title: '统计项',
                key: 'category',
                render: (h, params) => {
                    if (params.row.editFlag) {
                        const options = this.rowArr.map(item => {
                            return (<Option value={item.id} key={item.id} disabled={!!item.disFlag}>{item.name}</Option>);
                        });
                        const selectedCategoryIds = params.row.ids;
                        const handleUpdateCategory = (idxArr) => {
                            params.row.ids = [];
                            let categoryArr = [];
                            idxArr.forEach(id => {
                                const item = this.rowArr.find(row => (row.id == id));
                                params.row.ids.push(item.id);
                                categoryArr.push(item.name);
                            });
                            params.row.value = categoryArr.join('、');
                            this.showRowData[params.index] = params.row;
                        };
                        return (<div>
                            <Select value={selectedCategoryIds} onOn-change={handleUpdateCategory} multiple transfer>{options}</Select>
                        </div>);
                    } else {
                        return (<div>
                            <label>{params.row.value}</label>
                        </div>);
                    }
                }
            },
            {
                title: '操作',
                key: 'option',
                width: 150,
                align: 'center',
                render: (h, params) => {
                    const btnName = !!params.row.editFlag?'完成':'编辑';
                    const handleEditClick = () => {
                        if (params.row.editFlag) {
                            if (!params.row.group_name) {
                                this.$Message.error('请输入类型名称！');
                                return false;
                            }
                            if (Array.isArray(params.row.ids) && params.row.ids.length === 0) {
                                this.$Message.error('请选择统计项！');
                                return false;
                            }

                            // id 定位在新增的情况下有同为空id问题，改完 entityId 定位，初始化自定义顺序标识。
                            const itemIdx = this.updateRowList.findIndex(item => (item.entityId == params.row.entityId));
                            if (itemIdx === -1) {
                                this.updateRowList.push(params.row);
                            } else {
                                this.updateRowList[itemIdx] = params.row;
                            }
                        } else {
                            let optionSet = new Set();
                            this.showRowData.forEach(item => {
                              item.ids.forEach(id => {
                                optionSet.add(id);
                              });
                            });
                            // all of the options that should be disabled.
                            // console.log(optionSet);
                            params.row.ids.forEach(id => {
                              optionSet.delete(id);
                            });
                            // console.log(optionSet);
                            this.rowArr.forEach(row => {
                                if (optionSet.has(row.id)) {
                                    row.disFlag = true;
                                } else {
                                    row.disFlag = false;
                                }
                            });
                        }
                        params.row.editFlag = !params.row.editFlag;
                        this.showRowData[params.index] = params.row;
                    };
                    const handleDeleteRowClick = () => {
                        let delItem = this.showRowData.splice(params.index, 1);
                        delItem = delItem[0];
                        this.deleteRowList.push({
                            ...delItem,
                            is_del: 1
                        });
                    };
                    return (<div>
                        <Button type="primary" size="small" onClick={handleEditClick}>{btnName}</Button>
                        <Button type="error" size="small" style="margin-left: 5px;" onClick={handleDeleteRowClick}>删除</Button>
                    </div>);
                }
            }
        ],
        defaultColColumn: [
            {
                title: '类型名称',
                key: 'name',
                render: (h, params) => {
                    const handleUpdateTypeName = event => {
                        params.row.group_name = event.target.value;
                    };
                    if (params.row.editFlag) {
                        return (<div>
                            <Input value={params.row.group_name} onOn-change={handleUpdateTypeName} />
                        </div>);
                    } else {
                        return (<div>
                            <label>{params.row.group_name}</label>
                        </div>);
                    }
                }
            },
            {
                title: '统计项',
                key: 'category',
                render: (h, params) => {
                    if (params.row.editFlag) {
                        const options = this.colArr.map(item => {
                            return (<Option value={item.id} key={item.id} disabled={!!item.disFlag}>{item.name}</Option>);
                        });
                        const selectedCategoryIds = params.row.ids;
                        const handleUpdateCategory = (idxArr) => {
                            params.row.ids = [];
                            let categoryArr = [];
                            idxArr.forEach(id => {
                                const item = this.colArr.find(row => (row.id == id));
                                params.row.ids.push(item.id);
                                categoryArr.push(item.name);
                            });
                            params.row.value = categoryArr.join('、');
                            this.showColData[params.index] = params.row;
                        };
                        return (<div>
                            <Select value={selectedCategoryIds} onOn-change={handleUpdateCategory} multiple transfer>{options}</Select>
                        </div>);
                    } else {
                        return (<div>
                            <label>{params.row.value}</label>
                        </div>);
                    }
                }
            },
            {
                title: '规则',
                key: 'is_sum',
                render: (h, params) => {
                    if (params.row.editFlag) {
                        const handleUpdateRule = rule => {
                            params.row.is_sum = rule
                            this.showColData[params.index] = params.row
                        }
                        return (<div>
                            <Select value={params.row.is_sum} onOn-change={handleUpdateRule} transfer>
                                <Option value="1">汇总</Option>
                                <Option value="0">不汇总</Option>
                            </Select>
                        </div>)
                    } else {
                        return (<div>
                            <label>{params.row.is_sum==1?'汇总':'不汇总'}</label>
                        </div>);
                    }
                }
            },
            {
                title: '操作',
                key: 'option',
                width: 150,
                align: 'center',
                render: (h, params) => {
                    const btnName = !!params.row.editFlag?'完成':'编辑';
                    const handleEditClick = () => {
                        if (params.row.editFlag) {
                            if (!params.row.group_name) {
                                this.$Message.error('请输入类型名称！');
                                return false;
                            }
                            if (Array.isArray(params.row.ids) && params.row.ids.length === 0) {
                                this.$Message.error('请选择统计项！');
                                return false;
                            }
                            const itemIdx = this.updateColList.findIndex(item => (item.entityId == params.row.entityId));
                            if (itemIdx === -1) {
                                this.updateColList.push(params.row);
                            } else {
                                this.updateColList[itemIdx] = params.row;
                            }
                        } else {
                            let optionSet = new Set();
                            this.showColData.forEach(item => {
                                item.ids.forEach(id => {
                                    optionSet.add(id);
                                });
                            });
                            params.row.ids.forEach(id => {
                                optionSet.delete(id);
                            });
                            this.colArr.forEach(row => {
                                if (optionSet.has(row.id)) {
                                    row.disFlag = true;
                                } else {
                                    row.disFlag = false;
                                }
                            });
                        }
                        params.row.editFlag = !params.row.editFlag;
                        this.showColData[params.index] = params.row;
                    };
                    const handleDeleteColClick = () => {
                        let delItem = this.showColData.splice(params.index, 1);
                        delItem = delItem[0];
                        this.deleteColList.push({
                            ...delItem,
                            is_del: 1
                        });
                    };
                    return (<div>
                        <Button type="primary" size="small" onClick={handleEditClick}>{btnName}</Button>
                        <Button type="error" size="small" style="margin-left: 5px;" onClick={handleDeleteColClick}>删除</Button>
                    </div>);
                }
            }
        ],
        showRowData: [],
        showColData: [],
        deleteRowList: [],
        deleteColList: [],
        updateRowList: [],
        updateColList: []
      };
    },
    mounted() {
      this.busId = this.$store.state.busId;
      this.getBusList();
      // update table contruct.
      this.getHeadArr(1)
        .then(this.getHeadArr(2))
        .then(this.getDefineArr);
    },
    methods: {
      getList() {
        const [start_time, end_time] = this.dateRange;
        return this.$service.post('/Web/Summary/getSummaryList', {
          bus_id: this.busId,
          start_time,
          end_time
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getBusList() {
          !this.adminBusList && this.$store.dispatch('getAdminBusList');
        // return this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
        //   if (res.data.errorcode == 0) {
        //     this.busList = res.data.data;
        //   } else {this.$Message.error(res.data.errormsg);}
        // });
      },
      handleBusChange() {
        this.getHeadArr(1)
            .then(this.getHeadArr(2))
            .then(this.getDefineArr)
            .then(this.getList());
      },
      dateChange(dateRange) {
        this.dateRange = dateRange;
        this.getList();
      },
      async exportCsv() {
        //导出
        await this.getList();
        this.$refs.export.export({
          columns: this.tableCols,
          data: this.tableData,
          filename: `流水汇总表(${this.dateRange[0]}~${this.dateRange[1]})`
        });
      },

      // update table construct.
      getHeadArr(tp) {
          return this.$service.post('/Web/Summary/getGroupList', {
            bus_id: this.busId,
            type: tp
          }).then(res => {
              if (res.data.errorcode == 0) {
                  if (tp === 1) {
                      this.rowArr = res.data.data;
                  } else {
                      this.colArr = res.data.data;
                  }
              } else {
                  this.$Message.error(res.data.errormsg);
              }
          });
      },
      setOptionPack(item, index) {
          return {
              ...item,
              editFlag: false,
              entityId: index + 1
          };
      },
      getDefineArr() {
          return this.$service.post('/Web/Summary/getSummaryGroupList', {bus_id: this.busId}).then(res => {
              if (res.data.errorcode == 0) {
                  const defineRowArr = res.data.data.operate_type_list;
                  const defineColArr = res.data.data.pay_type_list;

                  this.updateRowList = [];
                  this.updateColList = [];
                  this.deleteRowList = [];
                  this.deleteColList = [];

                  this.showRowData = [];
                  this.showColData = [];
                  this.tableCols = [{title: '类型 \\ 支付', key: -1}];

                  if (Array.isArray(defineRowArr)) {
                      defineRowArr.forEach((item, index) => {
                          this.showRowData.push(this.setOptionPack(item, index));
                      });
                  }
                  if (Array.isArray(defineColArr)) {
                      defineColArr.forEach((item, index) => {
                          this.showColData.push(this.setOptionPack(item, index));
                          this.tableCols.push({
                            title: item.group_name,
                            key: item.group_id
                          });
                      });
                      this.tableCols.push({title: '汇总', key: -2});
                  }
              } else {
                  this.$Message.error(res.data.errormsg);
              }
          });
      },
      handleSaveClick() {
          let type1arr = this.updateRowList.concat(this.deleteRowList);
          let type2arr = this.updateColList.concat(this.deleteColList);
          type1arr = type1arr.filter(item=>(!!item.group_name));
          type2arr = type2arr.filter(item=>(!!item.group_name));

          if (type1arr.length === 0 && type2arr.length ===0) {
            return false;
          }

          return this.$service.post('/Web/Summary/groupOperate', {
              bus_id: this.busId,
              operate_type_list: type1arr,
              pay_type_list: type2arr
          }).then(res => {
              if (res.data.errorcode == 0) {
                  this.$Message.info('表格样式编辑成功！');
                  this.updateRowList = [];
                  this.updateColList = [];
                  this.deleteRowList = [];
                  this.deleteColList = [];

                  this.showRowData = [];
                  this.showColData = [];
                  this.tableCols = [{title: '类型 \\ 支付', key: -1}];

                  this.getDefineArr().then(this.getList());
              } else {
                  this.$Message.error(res.data.errormsg);
              }
          });
      },
      handleAddRowClick() {
        let optionSet = new Set();
        this.showRowData.forEach(item => {
            item.ids.forEach(id => {
                optionSet.add(id);
            });
        });
        // all of the options that should be disabled.
        // console.log(optionSet);
        this.rowArr.forEach(row => {
            if (optionSet.has(row.id)) {
                row.disFlag = true;
            } else {
                row.disFlag = false;
            }
        });

        let lastEntityId = 0;
        if (Array.isArray(this.showRowData) && this.showRowData.length > 0) {
            lastEntityId = this.showRowData[this.showRowData.length - 1].entityId;
        }
        this.showRowData.push({
            group_id: '',
            group_name: '',
            ids: [],
            editFlag: true,
            entityId: lastEntityId + 1
        });
      },
      handleAddColClick() {
        let optionSet = new Set();
        this.showColData.forEach(item => {
            item.ids.forEach(id => {
                optionSet.add(id);
            });
        });
        this.colArr.forEach(row => {
            if (optionSet.has(row.id)) {
                row.disFlag = true;
            } else {
                row.disFlag = false;
            }
        });

        let lastEntityId = 0;
        if (Array.isArray(this.showColData) && this.showColData.length > 0) {
            lastEntityId = this.showColData[this.showColData.length - 1].entityId;
        }
        this.showColData.push({
            group_id: '',
            group_name: '',
            ids: [],
            editFlag: true,
            entityId: lastEntityId + 1,
            is_sum: '0'
        });
      },
    }
  };
</script>
