<template>
  <div class="super-box">
    <div class="terminal">
      <div class="term-panel">
        <div class="panel-item">
          <div class="panel-value" style="color:#02e4d6;">{{revedetail.general_income==''?'--':Number(revedetail.general_income).toFixed(2)}}</div>
          <div class="panel-name">总收入（元）</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value" style="color:#f4fc03;">{{revedetail.buycard==''?'--':Number(revedetail.buycard).toFixed(2)}}</div>
          <div class="panel-name">新购会员卡（元）</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value" style="color:#5a80e8;">{{revedetail.renewcard==''?'--':Number(revedetail.renewcard).toFixed(2)}}</div>
          <div class="panel-name">续卡（元）</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value" style="color:#ff9481;">{{revedetail.pt_buycard==''?'--':Number(revedetail.pt_buycard).toFixed(2)}}</div>
          <div class="panel-name">私教新开（元）</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value" style="color:#f5509e;">{{revedetail.pt_changecard==''?'--':Number(revedetail.pt_changecard).toFixed(2)}}</div>
          <div class="panel-name">私教续课（元）</div>
        </div>
      </div>
    </div>
    <div class="statistics">
      <div class="stat-box">
        <div class="circle-stat">
          <div id="circle"></div>
          <div class="circle-legend">
            <ul style="list-style-type: none;">
              <li>
                <p id="buy_card">0</p>
              </li>
              <li>
                <p id="private_card">0</p>
              </li>
              <li>
                <p id="renew_card">0</p>
              </li>
              <li>
                <p id="renew_private_card">0</p>
              </li>
              <li>
                <p id="change_card">0</p>
              </li>
              <li>
                <p id="deliver_card">0</p>
              </li>
              <li>
                <p id="leave_card">0</p>
              </li>
              <li>
                <p id="goods_total">0</p>
              </li>
              <li>
                <p id="rent_cupboard">0</p>
              </li>
              <li>
                <p id="cancellation_card">0</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="stat-box">
        <div id="rectangle"></div>
      </div>
    </div>
  </div>
</template>

<script>
  var echarts = require("echarts");
  let showcount = 3,
    robit = null,
    trendChart = null;
  const SHOW_COUNT = 3,
    SHOW_TIME = 1000,
    RESET_TIME = 6000;

  export default {
    data() {
      return {
        businessChart: null,
        beginDate: "2018-03-01",
        endDate: "2018-03-10",
        revedetail: {},
        dddList: {},
        daysBusinessData: {
          day: 0,
          month: 0,
          current: 0
        },
        businessOption: {
          title: {
            top: 20,
            left: 20,
            text: "收入占比",
            textStyle: {
              color: "#a29eae"
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b}: {c} ({d}%)"
          },
          legend: {
            orient: "vertical",
            top: "middle",
            right: 0,
            formatter: "{name}",
            data: [
              "购卡",
              "私教",
              "续卡",
              "续私教",
              "升卡",
              "转卡",
              "请假",
              "商品销售",
              "租柜",
              "销卡"
            ],
            textStyle: {
              color: "#fff"
            }
            // itemHeight: 50,
            // itemWidth: 50
          },
          color: [
            "#1abbde",
            "#a76de8",
            "#ff6969",
            "#1bd4c9",
            "#b5db4f",
            "#ff7b44",
            "#80df89",
            "#f4ed28",
            "#ff85b3",
            "gray"
          ],
          series: [
            {
              name: "业绩统计",
              type: "pie",
              radius: ["0%", "60%"],
              center: ["37%", "50%"],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: "center"
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: "30",
                    fontWeight: "bold"
                  }
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: [
                { value: 0, name: "购卡", code: "buy_card" },
                { value: 0, name: "私教", code: "private_card" },
                { value: 0, name: "续卡", code: "renew_card" },
                { value: 0, name: "续私教", code: "renew_private_card" },
                { value: 0, name: "升卡", code: "change_card" },
                { value: 0, name: "转卡", code: "deliver_card" },
                { value: 0, name: "请假", code: "leave_card" },
                { value: 0, name: "商品销售", code: "goods_total" },
                { value: 0, name: "租柜", code: "rent_cupboard" },
                { value: 0, name: "销卡", code: "cancellation_card" }
              ]
            }
          ]
        },
        trenddata: [],
        trendOption: {
          title: {
            top: 20,
            left: 20,
            text: "收入走势",
            textStyle: {
              color: "#a29eae"
            }
          },
          textStyle: {
            color: "#666666"
          },
          backgroundColor: "#302f33",
          grid: {
            top: "15%",
            left: "3%",
            right: "7%",
            bottom: "7%",
            containLabel: true
          },
          xAxis: {
            show: true,
            position: "bottom",
            type: "time",
            nameLocation: "end",
            splitLine: {
              show: false
            }
          },
          yAxis: {
            show: true,
            position: "left",
            type: "value",
            // name: '亿',
            nameLocation: "end",
            splitLine: {
              show: false
            }
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985"
              }
            }
          },
          series: [
            {
              name: "场馆总收入",
              type: "line",
              // center:['55%','50%'],
              label: {
                normal: {
                  show: false
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              coordinateSystem: "cartesian2d",
              smooth: true,
              lineStyle: {
                normal: {
                  width: 1,
                  color: "#09c7bc"
                }
              },
              itemStyle: {
                normal: {
                  color: "#09c7bc"
                }
              },
              areaStyle: {
                normal: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: "#09c7bc" // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "#302f33" // 100% 处的颜色
                      }
                    ],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              symbolSize: "6",
              data: []
            }
          ]
        }
      };
    },
    methods: {
      getBusinessDB() {
        this.$service
          .post(
            "/Web/Index/achievement",
            {
              type: "month"
            },
            { loading: false }
          )
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                const businessData = response.data.data;

                // 勒两天的业绩
                this.daysBusinessData = {
                  day: businessData.day,
                  month: businessData.month,
                  current:
                    this.bizStatVal === "today"
                      ? businessData.day
                      : businessData.month
                };

                // 不需要画图了
                // if (this.daysBusinessData.current === 0) return;

                // 业绩饼图
                this.dddList = businessData.list;
                this.setDdd();
              } else {
                console.log(response.data.errormsg);
              }
            } else {
              console.log("服务器扑街！");
            }
          });
      },
      setDdd() {
        this.businessChart.clear();
        const list = this.dddList;
        for (const key in list) {
          if (list.hasOwnProperty(key)) {
            this.businessOption.series[0].data.forEach(function(item) {
              if (item.code == key) {
                if (item.code !== "cancellation_card") {
                  item.value = list[key];
                }
                setTimeout(function() {
                  document.querySelector("#" + key).innerHTML = list[key];
                }, 800);
              }
            });
          }
        }
        const self = this;
        setTimeout(() => {
          self.businessChart.setOption(self.businessOption);
        }, 1000);
      },
      getreveDetail(index = 1) {
        let url = "/Web/Statistics/get_bus_income_details";
        let _this = this;
        this.$service
          .post(
            url,
            {
              beg_date: this.beginDate,
              end_date: this.endDate
            },
            { loading: false }
          )
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                if (index == "1") {
                  _this.revedetail = response.data.data;
                  // _this.getPiedata();
                  _this.reve_detail = _this.revedetail;
                  // this.getDates(1);
                } else if (index == "2") {
                  _this.reve_detail = response.data.data;
                  _this.getComparison(3);
                } else {
                  _this.compreve_detail = response.data.data;
                  _this.compCal();
                }
              } else {
                _this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.log(response);
          });
      },
      getreveTrend() {
        var postData = {
          beg_date: this.beginDate,
          end_date: this.endDate
        };
        let url = "/Web/Statistics/get_bus_general_income";
        let _this = this;
        this.$service
          .post(url, postData, { loading: false })
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                let resdata = response.data.data;
                this.trenddata = [];
                for (let i = 0; i < resdata.length; i++) {
                  let trenditem = [resdata[i].date, resdata[i].general_income];
                  this.trenddata.push(trenditem);
                }
                trendChart = echarts.init(document.querySelector("#rectangle"));

                if (this.trenddata.length > showcount) {
                  const self = this;
                  this.dynamicLine(showcount);
                  showcount++;
                  robit = setInterval(() => {
                    self.dynamicLine(showcount);
                    if (showcount === self.trenddata.length) {
                      if (!!robit) {
                        clearInterval(robit);
                        robit = null;
                        setTimeout(() => {
                          showcount = SHOW_COUNT;
                          this.getBusinessDB();
                          this.getreveTrend();
                          this.getreveDetail();
                        }, RESET_TIME);
                      }
                    }
                    showcount++;
                  }, SHOW_TIME);
                }
              } else {
                _this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.log(response);
          });
      },
      getDateString() {
        const date = new Date();
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? "0" + month : month;
        let day = date.getDate();
        day = day < 10 ? "0" + day : day;
        return `${year}-${month}-${day}`;
      },
      dynamicLine(lens) {
        this.trendOption.series[0].data = this.trenddata.slice(0, lens);
        trendChart.setOption(this.trendOption);
      }
    },
    mounted() {
      this.businessChart = echarts.init(document.querySelector("#circle"));
      this.endDate = this.getDateString();
      this.getBusinessDB();
      this.getreveTrend();
      this.getreveDetail();
    }
  };
</script>

<style lang="less">
  html,
  body {
    height: 100%;
  }

  @media screen and (min-height: 1300px) and (max-height: 1700px) {
    html,
    body {
      font-size: 42px;
    }
  }

  @media screen and (min-height: 930px) and (max-height: 1300px) {
    html,
    body {
      font-size: 30px;
    }
  }

  @media screen and (min-height: 810px) and (max-height: 930px) {
    html,
    body {
      font-size: 25px;
    }
  }

  @media screen and (max-height: 810px) {
    html,
    body {
      font-size: 20px;
    }
  }
</style>

<style lang="less">
  .box-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .super-box {
    height: 100%;
    background-image: url("../../assets/img/screen-three.png");
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    flex-direction: column;
    .box-center;

    .terminal {
      height: 7.3rem;
      width: 47.3rem;
      border-radius: 0.5rem;
      border: 0.1rem solid #f4fc03;
      padding: 0.7rem 0.6rem;

      .term-panel {
        height: 100%;
        background-color: rgba(48, 47, 51, 0.45);
        .box-center;
        flex-direction: row;

        .panel-item {
          width: 11.1rem;
          .box-center;
          flex-direction: column;

          .panel-value {
            font-family: DIN;
            font-size: 1.6rem;
          }

          .panel-name {
            font-size: 0.7rem;
            color: #eeeeee;
          }
        }

        .line {
          height: 2.9rem;
          width: 0.05rem;
          background-color: #3c3c3c;
        }
      }
    }

    .statistics {
      display: flex;
      flex-direction: row;
      margin-top: 3.8rem;

      .stat-box:first-child {
        margin-right: 1.1rem;
      }

      .stat-box {
        height: 19.7rem;
        width: 23.3rem;
        border-radius: 1rem;
        background-color: rgba(29, 28, 29, 0.45);
        padding: 1rem 1rem 1.85rem 1rem;

        .circle-stat {
          display: flex;
          flex-direction: row;
          border-radius: 0.5rem;
          overflow: hidden;
          background-color: rgba(48, 47, 51, 0.45);
          height: 100%;

          #circle {
            height: 100%;
            width: 16.5rem;
          }

          .circle-legend {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            color: white;
            // line-height: 18px;
          }
        }

        #rectangle {
          height: 100%;
          width: 21.3rem;
          border-radius: 0.5rem;
          overflow: hidden;
          background-color: rgba(48, 47, 51, 0.45);
        }
      }
    }
  }
</style>
