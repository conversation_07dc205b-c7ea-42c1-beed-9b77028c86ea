<template>
  <div class="rock-box">
    <!-- <h1>图表最大值展示为绿色 bus_id=499</h1> -->
    <div class="rock-card who">
      <div class="glove">
        <div class="personal panel">
          <div class="glad">欢迎光临，{{user.bus_name}}</div>
          <div class="avatar">
            <img v-if="!!user.avatar" :src="user.avatar" alt="avatar">
            <img v-else src="https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKv7eKSxjJIAkUojCdB9O9q6dhxgaUw6NHeHCkT3m4aDekibmYNX3fhzxKhLh1hnUdOqSicrRojypnw/0" alt="avatar">
          </div>
        </div>
        <div class="information">
          <div class="username">
            <img v-if="user.sex==1" src="../../assets/img/screen-male.png" alt="male">
            <img v-else src="../../assets/img/screen-female.png" alt="female">
            <span>{{user.username}}</span>
          </div>
          <div class="info-items">
            <div class="info-item panel">
              <div class="item-value hvr-pulse-grow">{{user.sign_num_count}}</div>
              <div class="item-name">健身次数</div>
            </div>
            <div class="info-item panel">
              <div class="item-value hvr-pulse-grow">{{user.charm_value}}</div>
              <div class="item-name">魅力值</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rock-card sports">
      <div class="stat-title panel">
        恭喜您已经消耗
        <span class="kcal hvr-pulse-grow">{{kcal}}</span> cal，
        <span class="hvr-icon-wobble-horizontal">{{coca}} 杯可乐</span>
      </div>
      <div class="stat-tops">
        <div class="today-coach-recommend">
          <div class="tops-title">训练推荐</div>
          <div class="tops panel">
            <div class="recommend-item">
              <img class="logo hvr-pulse-grow" src="../../assets/img/stretching.png" alt="recommend">
              <span class="name">全身拉伸</span>
              <span class="value">10</span>
              <span class="unit">分钟</span>
            </div>
            <div class="recommend-item">
              <img class="logo hvr-pulse-grow" src="../../assets/img/aerobic.png" alt="recommend">
              <span class="name">有氧运动</span>
              <span class="value">20</span>
              <span class="unit">分钟</span>
            </div>
            <div class="recommend-item">
              <img class="logo hvr-pulse-grow" src="../../assets/img/legs-day.png" alt="recommend">
              <span class="name">下肢训练</span>
              <span class="value">50</span>
              <span class="unit">分钟</span>
            </div>
            <div class="recommend-item">
              <img class="logo hvr-pulse-grow" src="../../assets/img/six-pack-abs.png" alt="recommend">
              <span class="name">腹部运动</span>
              <span class="value">10</span>
              <span class="unit">分钟</span>
            </div>
          </div>
        </div>
        <div class="glamour">
          <div class="tops-title">魅力周榜</div>
          <div class="tops panel">
            <div class="glamour-item" v-for="(item, index) in user.charm_value_row">
              <img v-if="index===0" class="logo hvr-pulse-grow" src="../../assets/img/tops-gold.png" alt="glamour">
              <img v-else-if="index===1" class="logo hvr-pulse-grow" src="../../assets/img/tops-silver.png" alt="glamour">
              <img v-else-if="index===2" class="logo hvr-pulse-grow" src="../../assets/img/tops-copper.png" alt="glamour">
              <span v-else class="logo">{{index+1}}</span>
              <img class="avatar" :src="item.avatar" alt="avatar">
              <span class="name">{{item.username}}</span>
              <span class="value">{{item.charm_value}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getSocketUrl } from "utils/config";

  export default {
    data() {
      return {
        websock: "",
        socketCloseTimer: "",
        heartBeatTimer: "",
        user: {},
        kcal: 0,
        coca: 0,
        busId: 499,
        userId: "1129181"
      };
    },
    methods: {
      getUser() {
        return this.$service
          .post("/Web/member/user_info_for_screen", {
            bus_id: this.busId,
            user_id: this.userId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              const resdata = res.data.data;
              this.user = resdata;
              this.kcal = parseInt(resdata.sign_num_count) * 390;
              this.coca = (this.kcal / 90).toFixed(1);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      //连接websocket
      websocketAction() {
        if (this.websock && this.websock.readyState === this.websock.OPEN) {
          this.websocketsend(agentData);
        } else if (
          this.websock &&
          this.websock.readyState === this.websock.CONNECTING
        ) {
          // 若是正在开启状态
        } else {
          // 若未开启
          this.initWebSocket();
        }
      },
      initWebSocket() {
        const wsuri = getSocketUrl();
        this.websock = new WebSocket(wsuri);
        //心跳验证 60秒一次
        this.heartBeatTimer = setInterval(this.websocketHeartbeat, 1000 * 60);
        this.websock.onopen = this.websocketononopen;
        this.websock.onmessage = this.websocketonmessage;
        this.websock.onerror = this.websocketonerror;
        this.websock.onclose = this.websocketonclose;
      },
      websocketononopen() {
        //参数
        this.websocketsend(`{"action":"sign_init","bus_id":${this.busId}}`);
        if (this.socketCloseTimer) {
          clearInterval(this.socketCloseTimer);
          this.socketCloseTimer = "";
        }
      },
      //websocket心跳验证
      websocketHeartbeat() {
        this.websocketsend('{"action":"check_heart","msg":"@heart|end|"}');
      },
      websocketonmessage(e) {
        const resdata = JSON.parse(e.data);
        if (resdata.user_id) {
          // this.noticeArray.push(resdata);
          this.userId = resdata.user_id;
          // this.commitItem.sign_log_id = this.noticeArray[0].sign_log_id;
          // this.commitItem.symbol = this.noticeArray[0].symbol;
          // this.newNoticeIn();
          this.getUser();
        } else if (resdata.action == "clean_sign") {
          // this.noticeOut(resdata.sign_log_id);
        } else {
          // this.clear();
        }
      },
      websocketsend(agentData) {
        this.websock.send(agentData);
      },
      closeWebSocket() {
        this.websock && this.websock.close();
      },
      websocketonclose(e) {
        this.heartBeatTimer && clearInterval(this.heartBeatTimer);
        if (!e.wasClean && !this.socketCloseTimer) {
          this.socketCloseTimer = setInterval(this.websocketAction, 1000 * 30);
        }
      },
      websocketonerror() {
        console.log("websocketonerror");
      }
    },
    mounted() {
      this.websocketAction();
      this.getUser();

      let index = 0,
        glamourList = [];
      setTimeout(() => {
        glamourList = document.querySelectorAll(".glamour-item");
      }, 1000);
      setInterval(() => {
        // glamourList.map(item => (item.className = "glamour-item"));
        if (glamourList.length > 0) {
          glamourList.forEach(item => {
            item.className = "glamour-item";
          });
          glamourList[index].className =
            "glamour-item hvr-wobble-to-bottom-right";
          index++;
          if (index === glamourList.length) {
            index = 0;
          }
        }
      }, 3000);
    }
  };
</script>

<style lang="less">
  html,
  body {
    height: 100%;
  }

  @media screen and (min-height: 1300px) and (max-height: 1700px) {
    html,
    body {
      font-size: 42px;
    }
  }

  @media screen and (min-height: 930px) and (max-height: 1300px) {
    html,
    body {
      font-size: 30px;
    }
  }

  @media screen and (min-height: 810px) and (max-height: 930px) {
    html,
    body {
      font-size: 25px;
    }
  }

  @media screen and (max-height: 810px) {
    html,
    body {
      font-size: 20px;
    }
  }
</style>

<style lang="less" scoped>
  /* Icon Wobble Horizontal */
  @-webkit-keyframes hvr-icon-wobble-horizontal {
    16.65% {
      -webkit-transform: translateX(6px);
      transform: translateX(6px);
    }
    33.3% {
      -webkit-transform: translateX(-5px);
      transform: translateX(-5px);
    }
    49.95% {
      -webkit-transform: translateX(4px);
      transform: translateX(4px);
    }
    66.6% {
      -webkit-transform: translateX(-2px);
      transform: translateX(-2px);
    }
    83.25% {
      -webkit-transform: translateX(1px);
      transform: translateX(1px);
    }
    100% {
      -webkit-transform: translateX(0);
      transform: translateX(0);
    }
  }
  @keyframes hvr-icon-wobble-horizontal {
    16.65% {
      -webkit-transform: translateX(6px);
      transform: translateX(6px);
    }
    33.3% {
      -webkit-transform: translateX(-5px);
      transform: translateX(-5px);
    }
    49.95% {
      -webkit-transform: translateX(4px);
      transform: translateX(4px);
    }
    66.6% {
      -webkit-transform: translateX(-2px);
      transform: translateX(-2px);
    }
    83.25% {
      -webkit-transform: translateX(1px);
      transform: translateX(1px);
    }
    100% {
      -webkit-transform: translateX(0);
      transform: translateX(0);
    }
  }
  .hvr-icon-wobble-horizontal {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;
    position: relative;
    padding-right: 2.2em;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    margin-left: 2rem;
  }
  .hvr-icon-wobble-horizontal:before {
    content: "\f061";
    position: absolute;
    left: -1.5rem;
    padding: 0 1px;
    font-family: FontAwesome;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  .hvr-icon-wobble-horizontal:before,
  .hvr-icon-wobble-horizontal:focus:before,
  .hvr-icon-wobble-horizontal:active:before {
    -webkit-animation-name: hvr-icon-wobble-horizontal;
    animation-name: hvr-icon-wobble-horizontal;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
  }

  /* Pulse Grow */
  @-webkit-keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  @keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  .hvr-pulse-grow {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;
  }
  .hvr-pulse-grow,
  .hvr-pulse-grow:focus,
  .hvr-pulse-grow:active {
    -webkit-animation-name: hvr-pulse-grow;
    animation-name: hvr-pulse-grow;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
  }

  /* Wobble To Bottom Right */
  @-webkit-keyframes hvr-wobble-to-bottom-right {
    16.65% {
      -webkit-transform: translate(8px, 8px);
      transform: translate(8px, 8px);
    }
    33.3% {
      -webkit-transform: translate(-6px, -6px);
      transform: translate(-6px, -6px);
    }
    49.95% {
      -webkit-transform: translate(4px, 4px);
      transform: translate(4px, 4px);
    }
    66.6% {
      -webkit-transform: translate(-2px, -2px);
      transform: translate(-2px, -2px);
    }
    83.25% {
      -webkit-transform: translate(1px, 1px);
      transform: translate(1px, 1px);
    }
    100% {
      -webkit-transform: translate(0, 0);
      transform: translate(0, 0);
    }
  }
  @keyframes hvr-wobble-to-bottom-right {
    16.65% {
      -webkit-transform: translate(8px, 8px);
      transform: translate(8px, 8px);
    }
    33.3% {
      -webkit-transform: translate(-6px, -6px);
      transform: translate(-6px, -6px);
    }
    49.95% {
      -webkit-transform: translate(4px, 4px);
      transform: translate(4px, 4px);
    }
    66.6% {
      -webkit-transform: translate(-2px, -2px);
      transform: translate(-2px, -2px);
    }
    83.25% {
      -webkit-transform: translate(1px, 1px);
      transform: translate(1px, 1px);
    }
    100% {
      -webkit-transform: translate(0, 0);
      transform: translate(0, 0);
    }
  }
  .hvr-wobble-to-bottom-right {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;
  }
  .hvr-wobble-to-bottom-right,
  .hvr-wobble-to-bottom-right:focus,
  .hvr-wobble-to-bottom-right:active {
    -webkit-animation-name: hvr-wobble-to-bottom-right;
    animation-name: hvr-wobble-to-bottom-right;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
  }

  .rock-box {
    height: 100%;
    background-image: url("../../assets/img/screen-one.png");
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    .who {
      height: 30.8rem;
      width: 16rem;
      border: 0.1rem solid #f4fc03;
    }

    .sports {
      height: 30.8rem;
      width: 32rem;
      margin-left: 0.9rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      // background-color: #1e1d1e;
      background-color: rgba(30, 29, 30, 0.7);
    }

    .rock-card {
      border-radius: 0.5rem;
      padding: 1rem 0.6rem;

      .glove {
        height: 100%;
        background-color: rgba(32, 32, 32, 0.7);
        border-radius: 0.5rem;
      }

      .panel {
        background-color: #302f33;
        border-radius: 0.5rem;
      }

      .personal {
        height: 13.8rem;
        width: 14.7rem;
        display: flex;
        flex-direction: column;
        align-items: center;

        .glad {
          font-size: 1rem;
          color: #f4fc03;
          margin-top: 2.35rem;
        }

        .avatar {
          height: 6.3rem;
          width: 6.3rem;
          margin-top: 2.5rem;

          img {
            height: 100%;
            width: 100%;
            border-radius: 50%;
            border: 0.05rem solid #f4fc03;
            overflow: hidden;
            padding: 0.4rem;
          }
        }
      }

      .information {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .username {
          font-size: 1.1rem;
          font-weight: bold;
          color: white;
          margin-top: 1.5rem;

          span {
            margin-left: 0.5rem;
          }
        }

        .info-items {
          display: flex;
          flex-direction: row;
          margin-top: 2.1rem;

          .info-item {
            height: 7.3rem;
            width: 5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(48, 47, 51, 0.45);

            .item-value {
              font-family: DIN;
              font-size: 1.7rem;
              color: #ff9481;
            }

            .item-name {
              font-size: 0.7rem;
              color: white;
            }
          }

          .info-item:last-child {
            margin-left: 1.3rem;

            .item-value {
              color: #02e4d6;
            }
          }
        }
      }

      .stat-title {
        height: 3.1rem;
        width: 28.5rem;
        border: 0.1rem solid #f4fc03;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: DIN;
        font-size: 1.2rem;
        color: #eeeeee;
        margin-top: 1.4rem;

        .kcal {
          color: #f4fc03;
          margin: 0 0.4rem;
        }
      }

      .stat-tops {
        display: flex;
        flex-direction: row;
        margin-top: 2.6rem;

        .tops-title {
          height: 2rem;
          width: 7rem;
          background-color: #eeeeee;
          font-size: 0.9rem;
          font-weight: bold;
          color: #1b1b1b;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 0.5rem;
        }

        .today-coach-recommend {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .tops {
            height: 18.3rem;
            width: 13rem;
            margin-top: 1.6rem;

            .recommend-item {
              height: 2rem;
              margin: 2rem 0.7rem;
              display: flex;
              flex-direction: row;
              align-items: center;

              .logo {
                height: 2rem;
                width: 2rem;
              }

              .name {
                font-size: 1rem;
                font-weight: bold;
                color: #eeeeee;
                margin-left: 0.6rem;
              }

              .value {
                font-family: DIN;
                font-size: 1.6rem;
                color: #f4fc03;
                margin-left: 1rem;
              }

              .unit {
                font-size: 1rem;
                color: #eeeeee;
                margin-left: 0.3rem;
              }
            }
          }
        }

        .glamour {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-left: 0.7rem;

          .tops {
            height: 18.3rem;
            width: 16.7rem;
            margin-top: 1.6rem;

            .glamour-item:first-child {
              margin-top: 2rem;
            }

            .glamour-item {
              height: 2.1rem;
              margin: 1rem 0.8rem 1rem 1.1rem;
              display: flex;
              flex-direction: row;
              align-items: center;

              .logo {
                height: 1rem;
                width: 0.8rem;
                font-family: DIN;
                font-size: 0.9rem;
                font-weight: bold;
                color: #eeeeee;
              }

              .avatar {
                height: 2.1rem;
                width: 2.1rem;
                border-radius: 50%;
                border: 0.1rem solid #706f72;
                overflow: hidden;
                margin-left: 0.7rem;
              }

              .name {
                font-size: 1rem;
                font-weight: bold;
                color: #eeeeee;
                margin-left: 0.7rem;
                width: 6.4rem;
              }

              .value {
                height: 1.7rem;
                width: 4.1rem;
                font-size: 1.3rem;
                color: #101011;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #f4fc03;
                border-radius: 0.4rem;
              }
            }
          }
        }
      }
    }
  }

  @font-face {
    font-family: "DIN";
    font-weight: normal;
    font-style: normal;
    src: url("data:application/x-font-woff;charset=utf-8;base64,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")
      format("woff");
  }
</style>
