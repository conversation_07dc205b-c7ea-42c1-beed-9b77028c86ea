<template>
  <div class="bird-box">
    <div class="terminal">
      <div class="term-item">
        <div class="term-glove">
          <div class="term-title">今日客流(人)：
            <span class="hvr-pulse-grow">{{todayFlowData.count}}</span>
          </div>
          <div class="term-panel">
            <div class="panel-item">
              <div class="panel-value hvr-pulse-grow" style="color:#02e4d6">{{todayFlowData.other_class}}</div>
              <div class="panel-name">训练 (人)</div>
            </div>
            <div class="panel-item">
              <div class="panel-value hvr-pulse-grow" style="color:#f4fc03">{{todayFlowData.private_class}}</div>
              <div class="panel-name">私教 (人)</div>
            </div>
            <div class="panel-item">
              <div class="panel-value hvr-pulse-grow" style="color:#ff696a">{{todayFlowData.to_visit_list}}</div>
              <div class="panel-name">到店体验 (人)</div>
            </div>
          </div>
        </div>
      </div>
      <div class="term-item">
        <div class="term-glove">
          <div class="term-title">
            <div class="box-center">
              今日新售卡(张)：
              <span class="hvr-pulse-grow">{{daysSaleData.today.card_count}}</span>
            </div>
            <div class="box-center" style="margin-left:.7rem;">
              今日新售私教(节)：
              <span class="hvr-pulse-grow">{{daysSaleData.today.private_count}}</span>
            </div>
          </div>
          <div class="term-panel">
            <div class="panel-item">
              <div class="panel-value hvr-pulse-grow" style="color:#990099">{{daysSaleData.all.card_count}}</div>
              <div class="panel-name">本月新售卡</div>
            </div>
            <div class="panel-item">
              <div class="panel-value hvr-pulse-grow" style="color:#858ddf">{{daysSaleData.all.private_count}}</div>
              <div class="panel-name">本月新售私教</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="statistics">
      <div id="flowStat"></div>
    </div>
  </div>
</template>
<script>
  var echarts = require("echarts");
  let showcount = 7,
    robit = null;
  const SHOW_COUNT = 7,
    SHOW_TIME = 1000,
    RESET_TIME = 6000;

  export default {
    data() {
      return {
        flowChart: null,
        dateList: [],
        inStoreList: [],
        trainList: [],
        coachList: [],
        todayFlowData: {
          count: 0,
          other_class: 0,
          private_class: 0,
          to_visit_list: 0
        },
        flowOption: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985"
              }
            }
          },
          textStyle: {
            color: "#666666"
          },
          color: ["#02e4d6", "#f4fc03", "#ff696a"],
          // backgroundColor: 'rgb(48, 147, 51, .45)',
          backgroundColor: "#302f33",
          legend: {
            data: ["训练", "私教", "到店体验"],
            textStyle: {
              color: "#a29eae"
            },
            top: 16
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              boundaryGap: false,
              data: []
            }
          ],
          yAxis: [
            {
              type: "value",
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: "训练",
              type: "line",
              lineStyle: {
                normal: {
                  color: "#02e4d6",
                  opacity: 0
                }
              },
              areaStyle: {
                normal: {
                  color: "#02e4d6",
                  opacity: 0.7
                }
              },
              data: []
            },
            {
              name: "私教",
              type: "line",
              lineStyle: {
                normal: {
                  color: "#f4fc03",
                  opacity: 0
                }
              },
              areaStyle: {
                normal: {
                  color: "#f4fc03",
                  opacity: 0.7
                }
              },
              data: []
            },
            {
              name: "到店体验",
              type: "line",
              lineStyle: {
                normal: {
                  color: "#ff696a",
                  opacity: 0
                }
              },
              areaStyle: {
                normal: {
                  color: "#ff696a",
                  opacity: 0.7
                }
              },
              data: []
            }
          ]
        },
        daysSaleData: {
          today: {
            card_count: 0,
            private_count: 0
          },
          all: {
            card_count: 0,
            private_count: 0
          }
        }
      };
    },
    methods: {
      dynamicFlow(lens) {
        this.flowOption.xAxis[0].data = this.dateList.slice(0, lens);
        this.flowOption.series[0].data = this.trainList.slice(0, lens);
        this.flowOption.series[1].data = this.coachList.slice(0, lens);
        this.flowOption.series[2].data = this.inStoreList.slice(0, lens);
        this.flowChart.setOption(this.flowOption);
      },
      getFlowDB() {
        this.$service
          .post("/Web/Index/get_flow_list", {}, { loading: false })
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                const flowData = response.data.data;
                // 今日客流量显示
                this.todayFlowData = flowData.today;

                // 近30天客流量统计图
                this.dateList = [];
                this.inStoreList = [];
                this.trainList = [];
                this.coachList = [];
                for (const key in flowData) {
                  if (key === "today") continue;
                  this.dateList.push(key);
                  if (flowData.hasOwnProperty(key)) {
                    this.inStoreList.push(flowData[key].to_visit_list);
                    this.trainList.push(flowData[key].other_class);
                    this.coachList.push(flowData[key].private_class);
                  }
                }

                if (this.dateList.length > showcount) {
                  const self = this;
                  this.dynamicFlow(showcount);
                  showcount++;
                  robit = setInterval(() => {
                    self.dynamicFlow(showcount);
                    if (showcount === self.dateList.length) {
                      if (!!robit) {
                        clearInterval(robit);
                        robit = null;
                        setTimeout(() => {
                          showcount = SHOW_COUNT;
                          self.getFlowDB();
                          self.getSaleDB();
                        }, RESET_TIME);
                      }
                    }
                    showcount++;
                  }, SHOW_TIME);
                } else {
                  this.dynamicFlow(this.dateList.length);
                }
              } else {
                console.log(response.data.errormsg);
              }
            } else {
              console.log("服务器扑街！");
            }
          });
      },
      getSaleDB() {
        this.$service.post("/Web/Index/new_card", {}, {loading: false}).then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              const saleData = response.data.data;

              // 勒两天的销售情况
              this.daysSaleData = saleData.total;

              // 会员购卡数据
              this.membershipData = saleData.list;
              // saleData.list.forEach(currentItem => {
              //   currentItem.amount = '￥' + currentItem.amount;
              //   this.membershipData.push(currentItem);
              // });
            } else {
              console.log(response.data.errormsg);
            }
          } else {
            console.log("服务器扑街！");
          }
        });
      }
    },
    mounted() {
      this.flowChart = echarts.init(document.querySelector("#flowStat"));
    },
    created() {
      this.getFlowDB();
      this.getSaleDB();
    }
  };
</script>

<style lang="less">
  html,
  body {
    height: 100%;
  }

  @media screen and (min-height: 1300px) and (max-height: 1700px) {
    html,
    body {
      font-size: 42px;
    }
  }

  @media screen and (min-height: 930px) and (max-height: 1300px) {
    html,
    body {
      font-size: 30px;
    }
  }

  @media screen and (min-height: 810px) and (max-height: 930px) {
    html,
    body {
      font-size: 25px;
    }
  }

  @media screen and (max-height: 810px) {
    html,
    body {
      font-size: 20px;
    }
  }
</style>

<style lang="less" scoped>
  /* Pulse Grow */
  @-webkit-keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  @keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  .hvr-pulse-grow {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;
  }
  .hvr-pulse-grow,
  .hvr-pulse-grow:focus,
  .hvr-pulse-grow:active {
    -webkit-animation-name: hvr-pulse-grow;
    animation-name: hvr-pulse-grow;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
  }

  .box-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .bird-box {
    height: 100%;
    background-image: url("../../assets/img/screen-two.png");
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    flex-direction: column;
    .box-center;

    .terminal {
      display: flex;
      flex-direction: row;

      .term-item {
        height: 9.9rem;
        width: 23.9rem;
        border: 0.1 solid #f4fc03;
        border-radius: 1rem;
        padding: 1rem 0.8rem;
        // background-color: #191819;
        background-color: rgba(25, 24, 25, 0.45);

        .term-glove {
          height: 100%;
          border-radius: 0.5rem;
          // background-color: #1d1c1d;
          background-color: rgba(29, 28, 29, 0.45);

          .term-title {
            font-size: 0.9rem;
            font-weight: bold;
            color: white;
            margin-left: 1.2rem;
            height: 2.7rem;
            display: flex;
            flex-direction: row;
            align-items: center;

            span {
              font-family: DIN;
              font-size: 1.5rem;
              color: white;
            }
          }

          .term-panel {
            height: 5rem;
            background-color: rgba(48, 47, 51, 0.45);
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            border-bottom-left-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;

            .panel-item {
              flex-direction: column;
              .box-center;

              .panel-value {
                font-family: DIN;
                font-size: 1.5rem;
              }

              .panel-name {
                font-size: 0.7rem;
                color: #eeeeee;
              }
            }
          }
        }
      }

      .term-item:last-child {
        margin-left: 0.8rem;
      }
    }

    .statistics {
      height: 19.6rem;
      width: 48.6rem;
      border-radius: 1rem;
      // background-color: #1d1d1e;
      background-color: rgba(29, 28, 29, 0.45);
      margin-top: 1.3rem;
      .box-center;

      #flowStat {
        height: 17.1rem;
        width: 46.3rem;
        border-radius: 0.5rem;
        overflow: hidden;
        // background-color: #302f33;
        // background-color: rgba(48, 47, 51, 0.45);
      }
    }
  }
</style>
