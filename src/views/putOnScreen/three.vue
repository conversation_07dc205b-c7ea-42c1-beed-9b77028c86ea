<template>
  <div class="super-box">
    <div class="terminal">
      <div class="term-panel">
        <div class="panel-item">
          <div class="panel-value hvr-pulse-grow" style="color:#f4fc03;">{{tipData.availability}}</div>
          <div class="panel-name">有效会员 (人)</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value hvr-pulse-grow" style="color:#02e4d6;">{{tipData.not_buy_card}}</div>
          <div class="panel-name">潜在会员 (人)</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value hvr-pulse-grow" style="color:#f5509e;">{{tipData.overdue}}</div>
          <div class="panel-name">过期会员 (人)</div>
        </div>
        <div class="line"></div>
        <div class="panel-item">
          <div class="panel-value">
            <span class="hvr-pulse-grow" style="color:#00b7ee;">{{male}}</span> :
            <span class="hvr-pulse-grow" style="color:#ff9481;">{{female}}</span>
          </div>
          <div class="panel-name">男女比列</div>
        </div>
      </div>
    </div>
    <div class="statistics">
      <div class="stat-box">
        <div id="circle"></div>
      </div>
      <div class="stat-box">
        <div id="rectangle"></div>
      </div>
    </div>
  </div>
</template>

<script>
  var echarts = require("echarts");
  const AGE_GROUP = [
    "0-14",
    "15-20",
    "21-30",
    "31-40",
    "41-50",
    "51-60",
    "61-70",
    "71-∞",
    "未设置年龄"
  ];

  let showcount = 5,
    robit = null;
  const SHOW_COUNT = 5,
    SHOW_TIME = 1000,
    RESET_TIME = 6000;

  export default {
    data() {
      return {
        male: 0,
        female: 0,
        tipData: {
          availability: 0,
          overdue: 0,
          not_buy_card: 0,
          normal: 0,
          private_card: 0
        },
        maleArr: [],
        femaleArr: [],
        xField: [],
        $ageChart: null,
        $baoziChart: null,
        ageOption: {
          title: {
            top: 20,
            left: 20,
            text: "年龄占比",
            textStyle: {
              color: "#a29eae"
            }
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          grid: {
            left: "3%",
            right: "6%",
            bottom: "3%",
            containLabel: true
          },
          textStyle: {
            color: "#666666"
          },
          backgroundColor: "#302f33",
          xAxis: [
            {
              type: "category",
              data: AGE_GROUP
            }
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value}"
              },
              minInterval: 1,
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: "男",
              type: "bar",
              stack: "勤鸟会员",
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#81cfef"]
            },
            {
              name: "女",
              type: "bar",
              stack: "勤鸟会员",
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#f88397"]
            }
          ]
        },
        baoziOption: {
          title: {
            top: 20,
            left: 20,
            text: "会员种类",
            textStyle: {
              color: "#a29eae"
            }
          },
          textStyle: {
            color: "#666666"
          },
          backgroundColor: "#302f33",
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b}: {c} ({d}%)"
          },
          series: [
            {
              name: "访问来源",
              type: "pie",
              selectedMode: "single",
              radius: [0, "30%"],

              label: {
                normal: {
                  position: "inner"
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: [
                { value: 0, name: "有效会员" },
                { value: 0, name: "潜在会员" },
                { value: 0, name: "过期会员" }
              ]
            },
            {
              name: "访问来源",
              type: "pie",
              radius: ["40%", "55%"],
              label: {
                normal: {
                  formatter: "{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ",
                  backgroundColor: "#eee",
                  borderColor: "#aaa",
                  borderWidth: 1,
                  borderRadius: 4,
                  rich: {
                    a: {
                      color: "#999",
                      lineHeight: 22,
                      align: "center"
                    },
                    hr: {
                      borderColor: "#aaa",
                      width: "100%",
                      borderWidth: 0.5,
                      height: 0
                    },
                    b: {
                      fontSize: 12,
                      lineHeight: 33
                    },
                    per: {
                      color: "#eee",
                      backgroundColor: "#334455",
                      padding: [2, 4],
                      borderRadius: 2
                    }
                  }
                }
              },
              data: [
                { value: 0, name: "普通会员" },
                { value: 0, name: "私教会员" },
                { value: 0, name: "潜在会员" },
                { value: 0, name: "过期会员" }
              ]
            }
          ]
        }
      };
    },
    methods: {
      setTipDB() {
        this.$baoziChart.clear();
        this.baoziOption.series[0].data[0].value = parseInt(
          this.tipData.availability
        );
        this.baoziOption.series[0].data[1].value = parseInt(
          this.tipData.not_buy_card
        );
        this.baoziOption.series[0].data[2].value = parseInt(this.tipData.overdue);
        this.baoziOption.series[1].data[0].value = parseInt(this.tipData.normal);
        this.baoziOption.series[1].data[1].value = parseInt(
          this.tipData.private_card
        );
        this.baoziOption.series[1].data[2].value = parseInt(
          this.tipData.not_buy_card
        );
        this.baoziOption.series[1].data[3].value = parseInt(this.tipData.overdue);
        this.$baoziChart.setOption(this.baoziOption);
      },
      getTipDB() {
        this.$service
          .post("/Web/Index/user_statistics", {}, { loading: false })
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                this.tipData = response.data.data;
                this.setTipDB();
              } else if (response.data.errorcode === 40005) {
                this.$Message.error(response.data.errormsg);
              } else {
                console.log(response.data.errormsg);
              }
            } else {
              console.log("服务器扑街！");
            }
          });
      },
      dynamicRectangle(lens) {
        this.ageOption.series[0].data = this.maleArr.slice(0, lens);
        this.ageOption.series[1].data = this.femaleArr.slice(0, lens);
        this.ageOption.xAxis[0].data = this.xField.slice(0, lens);

        this.$ageChart.setOption(this.ageOption);
      },
      getAgeAndGenderRatioDB() {
        this.$service
          .post(
            "/Web/Statistics/user_sex_age_info",
            {
              age: [1, 2, 3, 4, 5, 6, 7, 8, 9],
              sex: 3
            },
            { loading: false }
          )
          .then(res => {
            if (res.status === 200) {
              if (res.data.errorcode == 0) {
                const originOption = res.data.data;

                this.male = originOption.sex1;
                this.female = originOption.sex2;

                // 画年龄柱状图
                this.maleArr = [];
                this.femaleArr = [];
                this.xField = [];
                let ageVal = [1, 2, 3, 4, 5, 6, 7, 8, 9];
                ageVal.forEach(item => {
                  if (item === 1) {
                    this.maleArr.push(originOption.age014sex1);
                    this.femaleArr.push(originOption.age014sex2);
                    this.xField.push(AGE_GROUP[0]);
                  } else if (item === 2) {
                    this.maleArr.push(originOption.age1520sex1);
                    this.femaleArr.push(originOption.age1520sex2);
                    this.xField.push(AGE_GROUP[1]);
                  } else if (item === 3) {
                    this.maleArr.push(originOption.age2130sex1);
                    this.femaleArr.push(originOption.age2130sex2);
                    this.xField.push(AGE_GROUP[2]);
                  } else if (item === 4) {
                    this.maleArr.push(originOption.age3140sex1);
                    this.femaleArr.push(originOption.age3140sex2);
                    this.xField.push(AGE_GROUP[3]);
                  } else if (item === 5) {
                    this.maleArr.push(originOption.age4150sex1);
                    this.femaleArr.push(originOption.age4150sex2);
                    this.xField.push(AGE_GROUP[4]);
                  } else if (item === 6) {
                    this.maleArr.push(originOption.age5160sex1);
                    this.femaleArr.push(originOption.age5160sex2);
                    this.xField.push(AGE_GROUP[5]);
                  } else if (item === 7) {
                    this.maleArr.push(originOption.age6170sex1);
                    this.femaleArr.push(originOption.age6170sex2);
                    this.xField.push(AGE_GROUP[6]);
                  } else if (item === 8) {
                    this.maleArr.push(originOption.age71sex1);
                    this.femaleArr.push(originOption.age71sex2);
                    this.xField.push(AGE_GROUP[7]);
                  } else if (item === 9) {
                    this.maleArr.push(originOption.not_set_age_sex1);
                    this.femaleArr.push(originOption.not_set_age_sex2);
                    this.xField.push(AGE_GROUP[8]);
                  }
                });

                if (this.xField.length > showcount) {
                  const self = this;
                  this.dynamicRectangle(showcount);
                  showcount++;
                  robit = setInterval(() => {
                    self.dynamicRectangle(showcount);
                    if (showcount === self.xField.length) {
                      if (!!robit) {
                        clearInterval(robit);
                        robit = null;
                        setTimeout(() => {
                          showcount = SHOW_COUNT;
                          this.getAgeAndGenderRatioDB();
                          this.getTipDB();
                        }, RESET_TIME);
                      }
                    }
                    showcount++;
                  }, SHOW_TIME);
                } else {
                  this.dynamicRectangle(this.xField.length);
                }
              } else {
                let msg = res.data.errormsg;
                msg = msg ? msg : "卧槽，谁把代码删了！(╯▔皿▔)╯";
                this.$Notice.error({
                  title: msg
                });
              }
            } else {
              this.$Notice.error({
                title: "网络不稳定，请摇一摇显示器再重试！",
                desc: `服务器返回代码：${res.status}`
              });
            }
          });
      }
    },
    mounted() {
      this.$ageChart = echarts.init(document.querySelector("#rectangle"));
      this.$baoziChart = echarts.init(document.querySelector("#circle"));
      this.getAgeAndGenderRatioDB();
      this.getTipDB();
    }
  };
</script>

<style lang="less">
  html,
  body {
    height: 100%;
  }

  @media screen and (min-height: 1300px) and (max-height: 1700px) {
    html,
    body {
      font-size: 42px;
    }
  }

  @media screen and (min-height: 930px) and (max-height: 1300px) {
    html,
    body {
      font-size: 30px;
    }
  }

  @media screen and (min-height: 810px) and (max-height: 930px) {
    html,
    body {
      font-size: 25px;
    }
  }

  @media screen and (max-height: 810px) {
    html,
    body {
      font-size: 20px;
    }
  }
</style>

<style lang="less">
  /* Pulse Grow */
  @-webkit-keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  @keyframes hvr-pulse-grow {
    to {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  .hvr-pulse-grow {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px transparent;
  }
  .hvr-pulse-grow,
  .hvr-pulse-grow:focus,
  .hvr-pulse-grow:active {
    -webkit-animation-name: hvr-pulse-grow;
    animation-name: hvr-pulse-grow;
    -webkit-animation-duration: 0.3s;
    animation-duration: 0.3s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
  }

  .box-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .super-box {
    height: 100%;
    background-image: url("../../assets/img/screen-three.png");
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    flex-direction: column;
    .box-center;

    .terminal {
      height: 7.3rem;
      width: 47.3rem;
      border-radius: 0.5rem;
      border: 0.1rem solid #f4fc03;
      padding: 0.7rem 0.6rem;

      .term-panel {
        height: 100%;
        background-color: rgba(48, 47, 51, 0.45);
        .box-center;
        flex-direction: row;

        .panel-item {
          width: 11.1rem;
          .box-center;
          flex-direction: column;

          .panel-value {
            font-family: DIN;
            font-size: 1.6rem;
          }

          .panel-name {
            font-size: 0.7rem;
            color: #eeeeee;
          }
        }

        .line {
          height: 2.9rem;
          width: 0.05rem;
          background-color: #3c3c3c;
        }
      }
    }

    .statistics {
      display: flex;
      flex-direction: row;
      margin-top: 3.8rem;

      .stat-box:first-child {
        margin-right: 1.1rem;
      }

      .stat-box {
        height: 19.7rem;
        width: 23.3rem;
        border-radius: 1rem;
        background-color: rgba(29, 28, 29, 0.45);
        padding: 1rem 1rem 1.85rem 1rem;

        #circle,
        #rectangle {
          height: 100%;
          width: 21.3rem;
          border-radius: 0.5rem;
          overflow: hidden;
          background-color: rgba(48, 47, 51, 0.45);
        }
      }
    }
  }
</style>
