<template>
  <div class="table-wrap">
    <div>
      <Table 
        ref="table" 
        :data="tableData" 
        stripe
        :columns="columns" />
    </div>
    <footer>
      <Button type="success" @click="newDevice">添加设备</Button>
      <Pager :total="totalCount" :postData="postData" @on-change="pageChange" />
    </footer>
    <Modal v-model="showAdd" :mask-closable="false" :title="isEdit ? '编辑' : '添加设备'">
      <div>
        <Form ref="addForm" :model="addForm" :label-width="80">
          <Form-item label="品牌" prop="brand">
            <Select v-model="addForm.brand" placeholder="请选择设备品牌" :disabled="isEdit">
              <Option v-for="(item, index) in brandList" :value="item" :key="index">{{item}}</Option>
            </Select>
          </Form-item>
          <Form-item label="设备编号" prop="device_sn" :rules="{required: true, message: '请输入设备编号'}">
            <Input placeholder="请输入设备编号" v-model="addForm.device_sn" />
          </Form-item>
        </Form>
      </div>
      <div class="modal-buttons" slot="footer">
        <Button type="success" @click="handleSubmit" :loading="loading">确定</Button>
        <Button @click="showAdd=false;">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import Pager from "src/components/pager";

export default {
  name: "youjiu",
  components: {
    Pager
  },
  data() {
    return {
      isEdit: false,
      showAdd: false,
      loading: false,
      addForm: {
        device_sn: '',
        brand: ''
      },
      brandList: [],
      totalCount: 0,
      postData: {
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      columns: [
        {
          title: '设备编号',
          key: 'device_sn',
        },
        {
          title: '品牌',
          key: 'brand',
        },
        {
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  ghost: true,
                },
                style: {
                  marginRight: '12px',
                  color: 'red',
                },
                on: {
                  'click': async () => {
                    await this.deleteDevice(params.row.id, params.row.brand);
                    await this.getList();
                  }
                }
              }, "删除"),
              h('Button', {
                props: {
                  type: 'text',
                  ghost: true,
                },
                on: {
                  'click': () => {
                    this.showAdd = true;
                    this.isEdit = true;
                    this.addForm = { ...params.row };
                  }
                }
              }, "编辑"),              
            ])
          }
        },
      ]
    }
  },
  created() {
    this.getList();
  },
  watch: {
    showAdd(val) {
      val && this.getBrands();
    }
  },
  methods: {
    async handleSubmit() {
      let checkForm = await this.$refs.addForm.validate();
      this.loading = checkForm;
      if (!checkForm) return;
      if (this.isEdit) {
        await this.updateDevice(this.addForm);
        this.isEdit = false;
      } else {
        await this.submitAddYoujiu();
      }
      await this.getList();
      this.showAdd = false;
      this.loading = false;
    },
    newDevice() {
      for (let item in this.addForm) {
        this.addForm[item] = '';
      }
      this.isEdit = false;
      this.showAdd = true;
    },
    pageChange(val) {
      this.postData = val;
      this.getList();
    },
    getList() {
      const url = '/Web/Youjiu/getList';
      return this.$service.get(url, {
        params: { ...this.postData }
      }).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.totalCount = res.data.data.count;
            this.tableData = res.data.data.list;
          } else {
            console.log(res.data.errormsg);
          }
        } else {
          console.error("服务器扑街！");
        }
      }).catch(err => {
        console.error(err);
      })
    },
    getBrands() {
      const url = '/Web/Youjiu/getDeviceBrand';
      return this.$service.get(url).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.brandList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        } else {
          console.error("服务器扑街！");
        }
      }).catch(err => {
        console.error(err);
      })
    },
    deleteDevice(id, brand) {
      const url = '/Web/Youjiu/postDelete';
      return this.$service.post(url, { id, brand }).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.$Message.success("删除成功！");
          } else {
            this.$Message.error(res.data.errormsg);
          }
        } else {
          console.error("服务器扑街！");
        }
      }).catch(err => {
        console.error(err);
      })
    },
    updateDevice(id) {
      const url = '/Web/Youjiu/postUpdate';
      return this.$service.post(url, this.addForm, { loading: false }).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.$Message.success("修改成功！");
          } else {
            this.$Message.error(res.data.errormsg);
          }
        } else {
          console.error("服务器扑街！");
        }
      }).catch(err => {
        console.error(err);
      })
    },
    submitAddYoujiu() {
      const url = '/Web/Youjiu/postCreate';
      let postdata = Object.create(null);
      Object.assign(postdata, this.addForm);
      return this.$service.post(url, postdata, { loading: false }).then(res => {
        if(res.status === 200) {
          if(res.data.errorcode == 0) {
            this.$Message.success("设备添加成功！")
          } else {
            this.$Message.error(res.data.errormsg)
          }
        } else {
          this.$Message.error("网络有问题！")
        }
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
</script>

