<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>版本权限</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="140">
        <Form-item label="请选择场馆">
          <Select clearable
                  filterable
                  v-model="bus_id"
                  placeholder="请选择场馆"
                  style="width: 360px"
                  v-if="busList">
            <Option v-for="item in busList"
                    :key="item.id"
                    :value="item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="签到方式"
                   v-if="bus_id">
          <Radio-group v-model="signType">
            <Radio v-for="(label, index) in signLabels"
                      :key="index"
                      style="font-size: 14px; margin-right: 16px"
                      :label="label"></Radio>
          </Radio-group>
        </Form-item>
        <Form-item label="系统版本"
                   v-if="bus_id">
          <Radio-group v-model="versionType">
            <Radio v-for="(label, index) in versionLabels"
                      :key="index"
                      style="font-size: 14px; margin-right: 16px"
                      :label="label"></Radio>
          </Radio-group>
        </Form-item>
        <Form-item label="会员端私教签到"
                   v-if="bus_id">
          <Radio-group v-model="memberSignType">
            <Radio v-for="(label, index) in memberSignLabels"
                      :key="index"
                      style="font-size: 14px; margin-right: 16px"
                      :label="label"></Radio>
          </Radio-group>
        </Form-item>
        <Form-item v-if="bus_id">
          <div class="buttons">
            <Button type="primary"
                    @click="submit">保存</Button>
            <Button >重置</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
  export default {
    name: 'versionCtrl',
    data() {
      return {
        bus_id: '',
        checked: [],
        signType: '普通',
        signLabels: ['普通', '指纹', '指静脉'],
        versionType: '未指定',
        versionLabels: ['未指定', '工作室版', '俱乐部版', '智能版'],
        memberSignType: '开启',
        memberSignLabels: ['开启', '关闭']
      }
    },
    created () {
      this.bus_id = this.$store.getters.busId
    },
    watch: {
      'bus_id'(val) {
        this.getBusSetting(val)
      }
    },
    computed: {
      ...mapGetters(['busList'])
    },
    methods: {
      getBusSetting(bus_id) {
        const url = '/Web/Business/get_bus_setting'
        this.$service.post(url, { bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data.info
            this.signType = this.signLabels[resData.support_fingerprint]
            this.versionType = this.versionLabels[resData.edition]
            this.memberSignType = this.memberSignLabels[resData.is_hidden_private_training]
          }
        })
      },
      submit() {
        const url = '/Web/Business/update_bus_setting'
        let postData = {
          bus_id: this.bus_id,
          support_fingerprint: this.signLabels.indexOf(this.signType),
          edition: this.versionLabels.indexOf(this.versionType),
          is_hidden_private_training: this.memberSignLabels.indexOf(this.memberSignType)
        }
        this.$service.post(url, postData).then(res => {
          this.$Message.success(res.data.errormsg)
        }).catch(err => {
          this.$Message.error(err)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .form-box .ivu-checkbox-group,
  .buttons {
    width: 100%;
  }

  .form-box-con {
    padding-top: 40px;
  }
</style>
