<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>账号数量设置</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="140">
        <Form-item label="请选择场馆">
          <Select clearable
                  filterable
                  v-model="bus_id"
                  placeholder="请选择场馆"
                  style="width: 360px"
                  v-if="busList">
            <Option v-for="item in busList"
                    :key="item.id"
                    :value="item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <template v-if="bus_id">
          <Form-item label="会员端开通人数">
                  <Input style="width: 360px" v-model="settings.member_num"></Input>
          </Form-item>
          <Form-item label="会籍端开通人数">
                  <Input style="width: 360px" v-model="settings.membership_num"></Input>
          </Form-item>
          <Form-item label="教练端开通人数">
                  <Input style="width: 360px" v-model="settings.coach_num"></Input>
          </Form-item>
          <Form-item label="BOSS端开通人数">
                  <Input style="width: 360px" v-model="settings.boss_num"></Input>
          </Form-item>
          <Form-item>
            <div class="buttons">
              <Button type="primary"
                      @click="submit">保存</Button>
              <Button >重置</Button>
            </div>
          </Form-item>
        </template>
      </Form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'numberSetting',
  data() {
    return {
      bus_id: '',
      settings: {
        boss_num: '',
        member_num: '',
        membership_num: '',
        coach_num: ''
      }
    };
  },
  created() {
    this.bus_id = this.$store.state.busId;
  },
  watch: {
    bus_id(val) {
      this.getSetting(val);
    }
  },
  computed: {
    ...mapGetters(['busList'])
  },
  methods: {
    getSetting(bus_id) {
      const url = '/Web/AccountNumber/get_account_number';
      this.$service.post(url, { bus_id }).then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data;
          this.settings = data.info;
        }
      });
    },
    submit() {
      const url = '/Web/AccountNumber/update_account_number';
      const { bus_id } = this;
      const { boss_num, member_num, membership_num, coach_num } = this.settings;
      this.$service
        .post(url, { bus_id, boss_num, member_num, membership_num, coach_num })
        .then(res => {
          this.$Message.success(res.data.errormsg);
        })
        .catch(err => {
          this.$Message.error(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
.form-box .ivu-checkbox-group,
.buttons {
  width: 100%;
}

.form-box-con {
  padding-top: 40px;
}
</style>
