<template>
  <div class="table-wrap">
    <Table ref="table" :data="tableData" :columns="columns"></Table>
    <footer>
      <Pager :total="total" :post-data="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import { formatDate } from 'src/utils';
  import Pager from 'components/pager';

  export default {
    name: 'padComment',
    components: { Pager },
    data() {
      return {
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        postData: {
          page_no: 1,
          page_size: 10
        },
        total: 0,
        columns: [
          {
            title: '时间',
            key: 'create_time',
          },
          {
            title: '场馆',
            key: 'bus_name',
          },
          {
            title: '提交人',
            key: 'coach_name',
          },
          {
            title: '电话',
            key: 'phone',
          },
          {
            title: '描述',
            key: 'content',
          },
        ],
        tableData: []
      };
    },
    methods: {
      dateChange([s, e]) {
        this.postData.begin_time = s;
        this.postData.end_time = e;
      },
      pageChange(postData) {
        const { page_no, page_size } = postData
        this.postData = { ...this.postData, page_no, page_size }
        this.getList()
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList(isExport) {
        const url = '/Web/Comment/ivep_comment';
        const postData = isExport ? { ...this.postData, page_no: 1, page_size: this.total } : this.postData;
        return this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            this.tableData = data.list;
            return data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      async exportCsv() {
        const list = await this.getList(true);
        const columns = this.columns
        const data = list.map(item => {
          return { ...item, }
        });
        this.$refs.table.exportCsv({
          filename: '导出列表',
          data,
          columns
        })
      }
    },
  };
</script>

<style scoped>

</style>
