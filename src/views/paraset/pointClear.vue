<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>积分清零</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="140">
        <Form-item label="请选择商家">
          <Select v-model="merchants_id" clearable filterable placeholder="请选择商家" style="width: 360px">
            <Option v-for="item in businessList" :key="item.id" :value="item.id" :label="item.mer_name"/>
          </Select>
        </Form-item>
        <Form-item>
          <Button type="success" @click="submit" style="margin-top: 30px">清零</Button>
        </Form-item>
      </Form>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'pointClear',
    data() {
      return {
        businessList: [],
        merchants_id: '',
      };
    },
    created() {
      this.getBusinessList();
    },
    methods: {
      getBusinessList() {
        const url = '/Web/Business/is_admin';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.businessList = res.data.data;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      submit() {
        const url = '/Web/MemberPoint/setPointZero';
        const { merchants_id } = this;
        if (!merchants_id) return false;
        const name = this.businessList.find(item => item.id === merchants_id).mer_name;

        this.$Modal.confirm({
          title: '积分清零',
          content: `点击确定清空 <b style="color: red">${name}</b> 的积分`,
          onOk: () => {
            this.$service
              .post(url, { merchants_id })
              .then(res => {
                this.$Message.success(res.data.errormsg);
              })
              .catch(err => {
                throw new Error(err);
              });
          },
        });
      }
    }
  };
</script>

<style lang="less" scoped>
  .form-box-con {
    padding-top: 40px;
  }
</style>
