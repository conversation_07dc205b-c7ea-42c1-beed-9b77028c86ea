<template>
  <div class="gray_set form-box">
    <div class="form-box-title">
      <h2>灰度环境设置</h2>
    </div>
    <div class="form-box-con">
      <Form :label-width="120">
        <Form-item label="灰度环境开关">
          <i-switch size="large"
                    v-model="switchValue"
                    @on-change="switchChanged">
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </Form-item>
        <Form-item label="请选择场馆"
                   v-if="switchValue">
          <Select clearable
                  filterable
                  v-model="bus_id"
                  @on-change="busChange"
                  placeholder="请选择场馆"
                  style="width: 360px"
                  v-if="busList">
            <Option v-for="item in busList.filter(info=>!info.version_expired)"
                    :key="item.id"
                    :value="item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="灰度环境"
                   v-if="bus_id && switchValue">
          <Checkbox-group v-model="checked">
            <Checkbox v-for="(label, index) in labels"
                      :key="index"
                      style="font-size: 14px"
                      :label="label"></Checkbox>
          </Checkbox-group>
        </Form-item>
        <Form-item v-if="bus_id && switchValue">
          <div class="buttons">
            <Button type="primary"
                    @click="submit">保存</Button>
            <Button 
                    @click="checked = []">重置</Button>
          </div>
        </Form-item>
      </Form>
    </div>
    <Table :columns="columns"
           :data="busTableData"
           border
           style="margin: 30px 0"></Table>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  export default {
    name: 'graySet',
    data () {
      return {
        bus_id: '',
        switchValue: false,
        checked: [],
        labels: [],
        resList: null,
        settingString: '',
        columns: [
          {
            title: '场馆名称',
            key: 'bus_name'
          },
          {
            title: '已开通灰度',
            key: 'string'
          },
        ],
        busTableData: []
      }
    },
    created () {
      this.getSwitch()
    },
    computed: {
      ...mapGetters(['busList'])
    },
    watch: {
      'checked' (val) {
        this.resList.forEach(item => {
          item.Switch = 'false'
        })
        val.forEach(checkedItem => {
          this.resList.forEach(resItem => {
            if (resItem.Alias === checkedItem) {
              resItem.Switch = 'true'
            }
          })
        })
        let strArr = this.resList.map(item => {
          return `${item.Name}|${item.Switch === 'true'}`
        })
        this.settingString = strArr.join(',')
      }
    },
    methods: {
      busChange (bus_id) {
        if (!bus_id) return
        this.getSettingList(bus_id)
      },
      submit () {
        const url = '/Web/Business/bus_set_gatedlaunch_conf'
        let postData = {
          bus_id: this.bus_id,
          gatedlaunch_str: this.settingString
        }
        this.$service.post(url, postData).then(res => {
          this.$Message.success(res.data.errormsg)
          this.getSwitch()
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getSwitch () {
        const url = '/Web/Business/get_gated_launch'
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.switchValue = resData.switch === 'on'
            if (!resData.list) {
              this.busTableData = []
              return
            }
            this.busTableData = resData.list.map(bus => {
              let gray = bus.choose_array.filter(item => item.Switch === 'true')
              let string = gray.reduce((str, item) => str + `${item.Alias}、`, '')
              return {
                bus_name: bus.bus_name,
                string: string
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          console.error(err)
        })
      },
      switchChanged (val) {
        const url = '/Web/Business/set_gated_launch'
        let postData = {
          switch: this.switchValue ? 'on' : 'off'
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode !== 0) {
            this.$Message.error(res.data.errormsg)
          }
          this.getSwitch()
        }).catch(err => {
          this.$Message.error(err)
        })
      },
      getSettingList (bus_id) {
        const url = '/Web/Business/bus_get_gatedlaunch_conf'
        this.$service.post(url, { bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data
            this.resList = list
            this.labels = list.map(item => {
              return item.Alias
            })
            let checkedItem = list.filter(item => {
              return item.Switch === 'true'
            })
            this.checked = checkedItem.map(item => {
              return item.Alias
            })
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .form-box .ivu-checkbox-group,
  .buttons {
    width: 100%;
  }

  .form-box-con {
    padding-top: 40px;
    padding-bottom: 40px;
  }
</style>
