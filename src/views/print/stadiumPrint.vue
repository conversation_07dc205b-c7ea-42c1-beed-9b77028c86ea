<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>{{infoData.title}}</header>
      <Form label-position="left"
          v-if="infoData">
        <FormItem label="场馆:" class="space-bet-row w-48">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:" class="space-bet-row w-48">
          <div>{{ infoData.username }}</div>
        </FormItem>
        <FormItem label="电话:" class="space-bet-row w-48">
          <div>{{ infoData.phone }}</div>
        </FormItem>
        <FormItem label="下单时间:">
          <div>{{ infoData.pay_time }}</div>
        </FormItem>

        <div class="line"></div>

        <FormItem label="订单编号:" class="space-bet-row w-48">

        </FormItem>
        <div style="margin-left:20px;">{{ infoData.order_sn }}</div>
        <FormItem label="场地类型:" class="space-bet-row w-48">
          <div>{{ infoData.space_type_name }}</div>
        </FormItem>
        <FormItem label="场地:" class="space-bet-row w-48">
          <div>{{ infoData.space_name }}</div>
        </FormItem>
        <FormItem v-if="infoData.card_name" label="课程名称:" class="space-bet-row w-48">
          <div>{{ infoData.card_name }}</div>
        </FormItem>
        <FormItem v-if="infoData.coach_name" label="教练名称:" class="space-bet-row w-48">
          <div>{{ infoData.coach_name }}</div>
        </FormItem>
        <FormItem label="预订时间:" class="space-bet-row w-48">

        </FormItem>
        <div style="margin-left:20px;" v-for="(time, index) in infoData.reserve_time" :key="index">{{ time }}</div>

        <div class="line"></div>

        <FormItem label="总计:" class="space-bet-row w-48">
          <div>{{ infoData.amount }}</div>
        </FormItem>

        <FormItem label="支付方式:" class='space-bet-row w-48'></FormItem>
        <FormItem :label="item.pay_type" class="space-bet-row w-48 m-b-5" v-for="item in infoData.pay_type" :key="item.pay_type">
          <div>{{ item.amount }}元</div>
        </FormItem>

        <div class="line"></div>

        <FormItem label="打印时间:">
          <div>{{ infoData.print_time }}</div>
        </FormItem>
        <FormItem label="打印账号:" class="space-bet-row w-48">
          <div>{{ infoData.print_admin }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'stadiumPrint',
    data () {
      return {
        userId: 1,
        orderCode: '',
        infoData: {}
      }
    },
    created () {
        this.userId = this.$route.query.user_id;
        this.orderCode = this.$route.query.order_sn;
        this.getInfo();
    },
    methods: {
      getInfo () {
        return this.$service.post('/Web/SmallTicket/printTicket', {
          type: 7,
          user_id: this.userId,
          order_sn: this.orderCode
        }).then(res => {
            if (res.data.errorcode == 0) {
                this.infoData = res.data.data;
                this.$nextTick(() => {
                  window.addEventListener('afterprint', (event) => {
                    localStorage.setItem('receipt',true)
                    window.close();
                  });
                  // 处理打印区域
                  const v2container = document.getElementById("v2container")
                  if (v2container) {
                    let inHTML = v2container.innerHTML
                    inHTML = inHTML.replace(/qiankun-head/g, 'head');
                    document.body.innerHTML = inHTML;
                    setTimeout(() => {
                      // setTimeout 处理打印页面loading未消失的问题
                      window.print();
                    }, 600);
                  } else {
                    window.print();
                  }
                });
            } else {this.$Message.error(res.data.errormsg);}
        });
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}
</style>
