<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>{{infoData.title}}</header>
      <Form label-position="left"
          v-if="infoData">
        <FormItem label="场馆:">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:">
          <div>{{ infoData.user_name }}</div>
        </FormItem>
        <FormItem label="电话:">
          <div>{{ infoData.user_phone }}</div>
        </FormItem>
        <FormItem label="下单时间:">
          <div>{{ infoData.create_time }}</div>
        </FormItem>

        <div class="line"></div>

        <FormItem label="基础票价:" class="space-bet-row w-48">
          <div>{{ infoData.buy_san_amount }}</div>
        </FormItem>
        <FormItem label="押金:" class="space-bet-row w-48">
          <div>{{ infoData.pre_amount }}</div>
        </FormItem>
        <FormItem label="超时费用:" class="space-bet-row w-48">
          <div>{{ infoData.over_fee }}</div>
        </FormItem>
        <FormItem label="应退:" class="space-bet-row w-48">
            <div>{{ infoData.should_refund_amount }}</div>
          </FormItem>
          <FormItem label="应补:" class="space-bet-row w-48">
            <div>{{ infoData.should_repre_amount }}</div>
          </FormItem>

        <div class="line"></div>

        <FormItem label="实退:" class="space-bet-row w-48">
          <div>{{ infoData.refund_amount }}</div>
        </FormItem>
        <FormItem label="实补:" class="space-bet-row w-48">
          <div>{{ infoData.repre_amount }}</div>
        </FormItem>
        <!-- <FormItem label="支付方式:" class='space-bet-row w-48'></FormItem>
        <FormItem :label="item.pay_type" class="space-bet-row w-48 m-b-5" v-for="item in infoData.pay_type" :key="item.pay_type">
          <div>{{ item.amount }}元</div>
        </FormItem> -->

        <div class="line"></div>

        <FormItem label="打印时间:">
          <div>{{ infoData.print_time }}</div>
        </FormItem>
        <FormItem label="打印账号:">
          <div>{{ infoData.print_account }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'nonMemberPrint',
    data () {
      return {
        logId: 1,
        infoData: {}
      }
    },
    created () {
        this.logId = this.$route.query.logId;
        this.getInfo();
    },
    methods: {
      getInfo () {
        return this.$service.get(`Web/San/getSpaceOrderSmallTicket?san_log_id=${this.logId}`).then(res => {
            if (res.data.errorcode == 0) {
                this.infoData = res.data.data;
                this.$nextTick(() => {
                  window.addEventListener('afterprint', (event) => {
                    localStorage.setItem('receipt',true)
                    window.close();
                  });
                  // 处理打印区域
                  const v2container = document.getElementById("v2container")
                  if (v2container) {
                    let inHTML = v2container.innerHTML
                    inHTML = inHTML.replace(/qiankun-head/g, 'head');
                    document.body.innerHTML = inHTML;
                    setTimeout(() => {
                      // setTimeout 处理打印页面loading未消失的问题
                      window.print();
                    }, 600);
                  } else {
                    window.print();
                  }
                });
            } else {this.$Message.error(res.data.errormsg);}
        });
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}
</style>
