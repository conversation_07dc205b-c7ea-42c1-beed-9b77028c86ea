<template>
  <div id="printBody" class="sign-print">
    <div class="sign_print_item">
      <header>合同签订</header>
      <Form label-position="left"
          v-if="infoData">
        <FormItem label="场馆:">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:">
          <div class="length-lim-40">{{ infoData.username }}</div>
        </FormItem>
        <FormItem label="电话:">
          <div>{{ infoData.phone }}</div>
        </FormItem>
        <FormItem label="签订时间:">
          <div>{{ infoData.deal_time }}</div>
        </FormItem>
        <div class="line"></div>
        <FormItem label="类型:">
          <div>{{ infoData.type }}</div>
        </FormItem>
        <FormItem label="详情:" v-if="infoData.type!='租柜' && infoData.info!='会员请假'">
          <div class="length-lim-40">{{ infoData.info }}</div>
        </FormItem>
        <FormItem label="可用门店:" v-if="infoData.user_card_bus_str">
          <div>{{ infoData.user_card_bus_str }}</div>
        </FormItem>
        <div class="remark">
          <div>备注：</div>
          <div class="length-lim-48">{{ infoData.remark }}</div>
        </div>
        <div class="line"></div>
        <FormItem label="合同金额:" class="space-bet-row w-48">
          <div>{{ infoData.amount }}元</div>
        </FormItem>
        <FormItem label="定金抵扣:" class="space-bet-row w-48">
          <div>{{ infoData.front_money }}元</div>
        </FormItem>

        <div class="line"></div>
        <FormItem label="实收:" class="space-bet-row w-48">
          <div>{{ infoData.income_amount }}元</div>
        </FormItem>
        <FormItem label="支付方式:" class='m-b-5'></FormItem>
        <FormItem :label="item.pay_type" class="space-bet-row w-48 m-b-5" v-for="item in infoData.pay_type" :key="item.id">
          <div>{{item.amount}}元</div>
        </FormItem>
        <div class="line pay-type-line"></div>
        <FormItem label="打印时间:">
          <div>{{ date }}</div>
        </FormItem>
        <FormItem label="打印账号:">
          <div class="length-lim-27">{{ infoData.print_admin }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  import { formatDate } from 'utils'
  export default {
    name: 'contractPrint',
    data () {
      return {
        infoData: {},
        date: formatDate(new Date(), 'yyyy-MM-dd HH:mm')
      }
    },
    created () {
      setTimeout(() => {
        this.getInfo(this.$route.query.type, this.$route.query.user_id, this.$route.query.card_order_info_id, this.$route.query.oper_type)
      }, 1);
    },
    methods: {
      getInfo (type,user_id,card_order_info_id,oper_type) {
        const url = '/Web/SmallTicket/printTicket'
        this.$service.post(url, { type: type, user_id: user_id, card_order_info_id: card_order_info_id }).then(res => {
          if (res.data.errorcode === 0) {
            this.infoData = res.data.data;
            this.$nextTick(() => {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });

              // 处理打印区域
              const v2container = document.getElementById("v2container")
              if (v2container) {
                let inHTML = v2container.innerHTML
                inHTML = inHTML.replace(/qiankun-head/g, 'head');
                document.body.innerHTML = inHTML;
                setTimeout(() => {
                  // setTimeout 处理打印页面loading未消失的问题
                  window.print();
                }, 600);
              } else {
                window.print();
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err);
          setTimeout(() => {
            window.close();
          }, 1000)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-40 {
  width: 40mm;
  word-break: break-all;
}
.length-lim-27 {
  width: 27mm;
  word-break: break-all;
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}
.m-b-5 {
  margin-bottom: 5pt;
}
.pay-type-line {
  margin-top: 15pt;
}
</style>
