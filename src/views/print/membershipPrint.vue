/*
 * @Author: l<PERSON>hu<PERSON>q
 * @Date: 2017-08-23 11:17:17
 * @Last Modified by: linghucq
 * @Last Modified time: 2017-11-06 14:05:45
 */
<template>
  <div class="membership_print">
    <div class="paper_page">
      <header>
        <img :src="info.thumb"
             alt="">
        <div class="order_sn">合同号:
          <span>{{ info.order_sn }}</span>
        </div>
      </header>
      <div class="main">
        <div class="title">
          <h1>会员协议</h1>
          <span>Membership Agreement</span>
        </div>
        <div class="block">
          <h2>
            <img src="../../assets/img/print_item.png">
            <span>会员资料</span>
          </h2>
          <Row class-name="row">
            <Col span="8">
            <Item name="姓名"
                  eng="Name"
                  align="center"
                  :value="info.username"></Item>
            </Col>
            <Col class="mgl"
                 span="6">
            <Item name="性别"
                  eng="Sex"
                  align="center"
                  :value="info.sex"></Item>
            </Col>
            <Col class="mgl"
                 span="10">
            <Item name="年龄"
                  eng="Age"
                  align="center"
                  :value="info.age"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col span="14">
            <Item name="证件号码"
                  eng="ID Number"
                  align="center"
                  :value="info.id_code"></Item>
            </Col>
            <Col class="mgl"
                 span="10">
            <Item name="电话"
                  eng="Tel"
                  align="center"
                  :value="info.phone"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="住址"
                  eng="Address"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="紧急联系电话"
                  eng="Emergency Contact"
                  value=""></Item>
            </Col>
          </Row>
        </div>
        <div class="block">
          <h2>
            <img src="../../assets/img/print_item.png">
            <span>服务内容</span>
          </h2>
          <Row class-name="row">
            <Col>
            <Item name="业务种类"
                  eng="Business Categories"
                  :checkbox="true"
                  :checked="catChecked"
                  :checkboxLabels="catLabels"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="会员卡类型"
                  eng="Card Type"
                  :value="info.card_name"></Item>
            </Col>
          </Row>
          <Row class-name="row"
               v-if="info.universal_card == 1">
            <Col>
            <Item name="可用门店"
                  eng="Support Stores"
                  :longString="isLongString(info.bus_name)"
                  :value="info.bus_name"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="会员卡号"
                  eng="Card Number"
                  :value="info.card_sn"></Item>
            </Col>
          </Row>
          <Row class-name="row"
               v-if="info.all_days">
            <Col>
            <Item name="使用有效期"
                  eng="Period Of Validity"
                  :value="info.all_days"></Item>
            </Col>
          </Row>
          <Row class-name="row"
               v-if="info.card_type_id === '2' || info.card_type_id === '4'">
            <Col>
            <Item name="可使用次数"
                  eng="Usage Count"
                  :value="info.all_num"></Item>
            </Col>
          </Row>
          <Row class-name="row"
               v-if="info.card_type_id === '3'">
            <Col>
            <Item name="可用金额"
                  eng="Available Balance"
                  :value="info.all_num"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="启用时间"
                  eng="Commissioning Date"
                  :value="info.active_time"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="到期时间"
                  eng="Due Date"
                  :value="info.end_time_date"></Item>
            </Col>
          </Row>
        </div>
        <div class="block">
          <h2>
            <img src="../../assets/img/print_item.png">
            <span>费用金额</span>
          </h2>
          <Row class-name="row">
            <Col>
            <Item name="收费金额"
                  eng="Payment Amount"
                  :value="info.amount"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="支付方式"
                  eng="Payment Method"
                  :checkbox="true"
                  :checked="payTypeChecked"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="经办人"
                  eng="Transactor"
                  :value="info.marketer_name"></Item>
            </Col>
          </Row>
          <Row class-name="row">
            <Col>
            <Item name="备注"
                  eng="Remark"
                  :longString="isLongString(info.remark)"
                  :value="info.remark"></Item>
            </Col>
          </Row>
        </div>
        <div class="block">
          <Row class-name="row"
               style="padding-top: 0"
               type="flex"
               justify="end">
            <Item name="会员署名"
                  eng="Member`s Signature"
                  width="20"></Item>
          </Row>
          <Row class-name="row"
               type="flex"
               justify="end">
            <Item name="会籍署名"
                  eng="Membership`s Signature"
                  width="20"
                  value=""></Item>
          </Row>
          <Row class-name="row"
               type="flex"
               justify="end">
            <Item name="主管署名"
                  eng="Head`s Signature"
                  width="20"></Item>
          </Row>
          <Row class-name="row"
               type="flex"
               justify="end">
            <Item name="签订时间"
                  eng="Signing Time"
                  width="20"
                  :value="info.deal_time"></Item>
          </Row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import Item from './printItem'
  export default {
    name: 'membershipPrint',
    data () {
      return {
        catLabels: ['购卡', '升卡', '续卡', '请假', '租柜', '销卡'],
        catChecked: [],
        payTypeChecked: [],
        info: {}
      }
    },
    components: {
      Item
    },
    created () {
      this.getPrintInfo()
    },
    methods: {
      isLongString(val) {
        return val && val.length > 20
      },
      calAllDays (info) {
        if (info.card_type_id === '1') {
          if (info.gift_volume === '0') {
            return `${info.all_days}天`
          } else {
            return `${info.all_days}天（含${info.gift_volume}天赠送）`
          }
        } else {
          if (info.all_days && info.all_days !== '永久') {
            return `${info.all_days}天`
          } else {
            return false
          }
        }
      },
      calAllNum (info) {
        if (info.card_type_id === '2' || info.card_type_id === '4') {
          if (info.gift_volume === '0') {
            return `${info.all_num}次`
          } else {
            return `${info.all_num}次（含${info.gift_volume}次赠送）`
          }
        } else {
          if (info.gift_volume === '0') {
            return `${info.all_num}元`
          } else {
            return `${info.all_num}元（含${info.gift_volume}元赠送）`
          }
        }
      },
      getPrintInfo () {
        const url = '/Web/Member/print_order';
        let postData = {
          cardorder_info_id: this.$route.params.card_order_info_id
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            let info = res.data.data.info
            let thumb = info.thumb
            let arr = thumb.split('?')
            thumb = arr[0] + '@100w_100h_90q_1pr?' + arr[1]
            this.info = Object.assign({}, info, {
              amount: `￥${info.amount}`,
              all_days: this.calAllDays(info),
              all_num: this.calAllNum(info),
              bus_name: info.bus_name.split(',').join('、'),
              end_time_date: info.end_time_date === '未知' ? `启用以后的${info.all_days}天` : info.end_time_date,
              thumb
            });
            this.catChecked = [info.card_type];
            this.payTypeChecked = [info.pay_type];
            this.$nextTick(() => {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });
              // 处理打印区域
              const v2container = document.getElementById("v2container")
              if (v2container) {
                let inHTML = v2container.innerHTML
                inHTML = inHTML.replace(/qiankun-head/g, 'head');
                document.body.innerHTML = inHTML;
                setTimeout(() => {
                  // setTimeout 处理打印页面loading未消失的问题
                  window.print();
                }, 600);
              } else {
                window.print();
              }
            })
          } else {
            throw new Error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error({
            content: err || '网络或服务器错误',
            duration: 1.5,
            onClose () {
              setTimeout(() => {
                window.close()
              }, 500);
            }
          })
        })
      }
    }
  }
</script>

<style lang="less">
  .flex-center {
    display: flex;
    align-items: center;
  }

  @media print {
    @page {
      size: 210mm 297mm;
      margin: 0;
      padding: 0;
    }
  }

  .membership_print {
    * {
      margin: 0;
      padding: 0;
      line-height: 1;
    }
    .ivu-checkbox-checked .ivu-checkbox-inner {
      border-color: #e72a0d;
      background-color: #e72a0d;
    }
    font-size: 4.04mm;
    color: #000;

    .paper_page {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 5mm 10.5mm 0;
      background-color: #fff;

      header {
        .flex-center;
        justify-content: space-between;
        height: 24.6mm;
        width: 100%;
        border-bottom: 1pt solid #d2d2d2;
        font-size: 4.23mm;

        img {
          height: 18.5mm;
          margin-top: 2mm;
        }
      }

      .main {
        width: 100%;
        .title {
          .flex-center;
          flex-direction: column;
          padding-bottom: 5mm;
          h1 {
            font-size: 6.35mm;
            padding: 3.8mm 0 1.9mm;
          }
          span {
            font-size: 2.82mm;
            font-family: Didot;
            color: #e72a0d;
          }
        }

        .block {
          width: 100%;
          padding-bottom: 7.5mm;

          h2 {
            .flex-center;
            font-size: 5.14mm;
            justify-content: flex-start;
            width: 100%;

            span {
              padding-left: 2.2mm;
            }
          }

          .row {
            padding-top: 4.7mm;
            .mgl {
              padding-left: 1.5mm;
            }
          }
        }
      }
    }
  }
</style>




