<template>
  <div class="sign_print_item">
    <header>教练上课</header>
    <Form label-position="left"
          v-if="info">
      <FormItem label="场馆:">
        <div>{{ info.bus_name }}</div>
      </FormItem>
      <FormItem label="会员:">
        <div>{{ info.username }}</div>
      </FormItem>
      <FormItem label="电话:">
        <div>{{ info.phone }}</div>
      </FormItem>
      <FormItem label="上课时间:">
        <div>{{ info.create_time }}</div>
      </FormItem>
      <div class="line"></div>
      <FormItem label="课程:">
        <div>{{ info.class_name }}</div>
      </FormItem>
      <FormItem label="上课教练:">
        <div>{{ info.coach_name }}</div>
      </FormItem>
      <FormItem label="总购买课时:">
        <div>{{ info.buy_all_class }}{{ info.is_pt_time_limit_card!=1?'节':'天' }}</div>
      </FormItem>
      <FormItem v-if="info.user_card_surplus_display" label="剩余课时:">
        <div>{{ info.surplus_class }}{{ info.is_pt_time_limit_card!=1?'节':'天' }}</div>
      </FormItem>
      <FormItem label="本次扣费:">
        <div>{{ info.this_time }}节</div>
      </FormItem>
      <FormItem v-if="info.user_card_surplus_display" label="消费后剩余:">
        <div>{{ info.consume_after }}{{ info.is_pt_time_limit_card!=1?'节':'天' }}</div>
      </FormItem>
      <div class="line"></div>
      <FormItem label="打印时间:">
        <div>{{ date }}</div>
      </FormItem>
      <FormItem label="打印账号:">
        <div>{{ info.print_admin }}</div>
      </FormItem>
    </Form>
    <footer>
      <div v-if="type == 0"
           class="footer-line">
        <p class="type">会员保留</p>
      </div>
      <div v-else
          class="signline">
        <h3 class="signat">会员签字:</h3>
        <p class="type">教练保留</p>
      </div> 
    </footer>
  </div>
</template>

<script>
  export default {
    name: 'signPrintItem',
    data () {
      return {

      }
    },
    props: {
      type: {},
      info: {},
      date: {}
    },
  }
</script>

<style lang="less">
  @media print {
    @page {
      margin: 0;
      padding: 0;
    }
  }  
</style>
