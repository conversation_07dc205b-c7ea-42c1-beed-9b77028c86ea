<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>{{infoData.title}}</header>
      <Form label-position="left"
          v-if="infoData">
        <FormItem label="场馆:">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:">
          <div>{{ infoData.username }}</div>
        </FormItem>
        <FormItem label="电话:">
          <div>{{ infoData.phone }}</div>
        </FormItem>
        <FormItem :label="(infoData.type=='收定金'||infoData.type=='收押金') ? '缴纳时间:' : '退款时间'">
          <div v-if="infoData.type=='收定金'||infoData.type=='收押金'">{{ infoData.create_time }}</div>
          <div v-else>{{ infoData.refund_time }}</div>
        </FormItem>
        <div class="line"></div>

        <FormItem label="类型:">
          <div>{{ infoData.type }}</div>
        </FormItem>
        <FormItem :label="(infoData.type=='收定金'||infoData.type=='收押金') ? '实收:' : '实退:'" class="space-bet-row w-48">
          <div>{{ infoData.amount }}元</div>
        </FormItem>
        <template v-if="infoData.type=='收定金'||infoData.type=='收押金'">
          <FormItem label="支付方式:" class="space-bet-row"></FormItem>
          <div
            class="space-bet-row w-48 pay-type"
            v-for="(item, index) in infoData.new_pay_type"
            :key="index">
            <span>{{ item.pay_type_name || item.name }}</span>
            <span>{{ item.amount }}元</span>
          </div>
        </template>
        <div class="remark">
          <div>备注：</div>
          <div class="length-lim-48">{{ infoData.remark }}</div>
        </div>
        <div class="line"></div>
        <FormItem label="打印时间:">
          <div>{{ date }}</div>
        </FormItem>
        <FormItem label="打印账号:">
          <div>{{ infoData.print_admin }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  import { formatDate } from 'utils'
  export default {
    name: 'depositPrint',
    data () {
      return {
        infoData: {},
        date: formatDate(new Date(), 'yyyy-MM-dd HH:mm')
      }
    },
    created () {
      this.getInfo(this.$route.query.type, this.$route.query.user_id, this.$route.query.charge_id)
    },
    methods: {
      getInfo (type,user_id,charge_id) {
        const url = '/Web/SmallTicket/printTicket'
        this.$service.post(url, { type: type, user_id: user_id, charge_id: charge_id }).then(res => {
          if (res.data.errorcode === 0) {
            this.infoData = res.data.data;
            this.$nextTick(() => {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });

              // 处理打印区域
              const v2container = document.getElementById("v2container")
              if (v2container) {
                let inHTML = v2container.innerHTML
                inHTML = inHTML.replace(/qiankun-head/g, 'head');
                document.body.innerHTML = inHTML;
                setTimeout(() => {
                  // setTimeout 处理打印页面loading未消失的问题
                  window.print();
                }, 600);
              } else {
                window.print();
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err);
          setTimeout(() => {
            window.close();
          }, 1000)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row {
  /deep/.ivu-form-item-label {
    white-space: nowrap;
  }
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}

.pay-type {
  display: flex;
  justify-content: space-between;
  padding-left: 10pt;
  padding-bottom: 2pt;
}
</style>
