<template>
  <div class="print_item">
    <span class="name">{{ name }}</span>
    <div class="eng">
      <span>(</span>
      <span style="padding: 0 1.5mm">{{ eng }}</span>
      <span>)</span>
    </div>
    <div v-if="!checkbox"
         class="value"
         :style="valueStyle">
      {{ value }}
    </div>
    <div v-else style="flex: 1; margin-left: 4.5mm">
      <span v-for="(type, index) in checked" style="color: #666">{{ type }}</span>
    </div>
  </div>
</template>


<script>
  export default {
    name: 'printItem',
    data () {
      return {

      }
    },
    computed: {
      valueStyle() {
        return {
          width: this.width ? this.width + '%' : 'auto',
          flex: this.width ? 'none' : 1,
          textAlign: this.align === 'center' ? 'center' : '',
          paddingLeft: this.align === 'center' ? '' : '5mm',
          fontSize: this.longString ? '3mm' : '',
          whiteSpace: this.longString ? 'normal' : '',
          overflow: this.longString ? 'hidden' : '',
          textOverflow: this.longString ? 'hidden' : ''
        }
      }
    },
    props: {
      name: {},
      eng: {},
      value: {},
      width: {},
      align: {},
      checkbox: {
        type: Boolean,
        default: false
      },
      checked: {
        type: Array
      },
      longString: {
        type: Boolean,
        default: false
      }
    }
  }
</script>

<style lang="less" scoped>
  .print_item {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;

    .eng {
      display: flex;
      align-items: flex-start;
      font-size: 3.63mm;
      color: #898989;
      margin-left: 1.5mm;
    }

    .value {
      flex: 1;
      border-bottom: 1pt solid #d3d3d3;
      margin-left: 1.5mm;
      align-self: flex-end;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>


