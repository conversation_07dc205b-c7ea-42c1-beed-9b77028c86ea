<template>
  <div class="sign-print">
    <SignPrintItem type="0"
                   :info="signInfo"
                   :date="date"></SignPrintItem>
    <SignPrintItem type="1"
                   :info="signInfo"
                   :date="date"></SignPrintItem>
  </div>
</template>

<script>
  import SignPrintItem from './signPrintItem'
  import { formatDate } from 'utils'
  export default {
    name: 'signPrint',
    data () {
      return {
        date: formatDate(new Date(), 'yyyy-MM-dd HH:mm'),
        sign_log_id: this.$route.params.sign_id,
        signInfo: null,
      }
    },
    components: {
      SignPrintItem
    },
    created () {
      this.getInfo(this.$route.query.type, this.$route.query.user_id, this.$route.query.small_ticket_id)
    },
    methods: {
      getInfo (type,user_id,ticket_id) {
        const url = 'Web/SmallTicket/printTicket'
        this.$service.post(url, { type: type, user_id: user_id, small_ticket_id: ticket_id }).then(res => {
          if (res.data.errorcode === 0) {
            this.signInfo = res.data.data
            this.$nextTick(() => {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });
              // 处理打印区域
              const v2container = document.getElementById("v2container")
              if (v2container) {
                let inHTML = v2container.innerHTML
                inHTML = inHTML.replace(/qiankun-head/g, 'head');
                document.body.innerHTML = inHTML;
                setTimeout(() => {
                  // setTimeout 处理打印页面loading未消失的问题
                  window.print();
                }, 600);
              } else {
                window.print();
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err);
          setTimeout(() => {
            window.close();
          }, 1000)
        })
      }
    },
  }
</script>
<style>
.sign-print .ivu-form-item-content,.sign-print .ivu-form-item-label{
  font-size:4mm !important;color: #000 !important;
}
</style>
<style lang="less" scoped>
  .sign-print	{
    display: block;
    page-break-before: always;
    font-family: simhei,fangsong,simsun,serif;
    width: 58mm;
    margin: 0 auto;
    font-size:4mm !important;
    color: #000 !important;
  }

  @media print {
    -webkit-print-color-adjust: exact;
    * {
      margin: 0;
      padding: 0;
      line-height: 1;
    }
    @page {
      size: 48mm auto;
      margin: 0;
      padding: 0;
    }
  }
</style>
