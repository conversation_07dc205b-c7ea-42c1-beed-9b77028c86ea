<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>票凭证</header>
      <Form v-for="(item, index) in infoData" :key="index" :label-width="80">
        <div></div>
        <FormItem label="场馆:">
          <div>{{ item.bus_name }}</div>
        </FormItem>
        <FormItem label="票名称:">
          <div>{{ item.san_name }}</div>
        </FormItem>
        <FormItem label="有效时间:">
          <div>{{ item.validity_date }}</div>
          <div>{{ item.validity_time }}</div>
        </FormItem>
        <FormItem label="适用场地:">
          <div>{{ item.spaceNames }}</div>
        </FormItem>
        <FormItem label="核销时间:">
          <div>{{ item.sign_in_time_string }}</div>
        </FormItem>
        <div class="qrcode-box">
          <div class="number">{{  item.consumeSn }}</div>
          <img class="qrcode" :src="item.qrcode_url" alt="二维码" />
        </div>
        <div v-if="infoData.length>0 && index !== infoData.length-1" class="line"></div>
      </Form>
    </div>
  </div>
</template>

<script>
  import { formatDate } from '@/utils/index'

  export default {
    name: 'sanTicketPrint',
    data () {
      return {
        logId: 1,
        infoData: [],
        isMultiPrint: false,
      }
    },
    created () {
        this.logId = this.$route.query.logId;
        //是否批量购票打印
        this.isMultiPrint = this.$route.query.isMultiPrint
        this.getInfo();
    },
    methods: {
      getInfo() {
        const url = `/Web/San/${this.isMultiPrint ? '/getSmallTickeBatch' : '/getSmallTicket'}?san_log_id=${this.logId}`
        return this.$service.get(url).then(res => {
          if (res.data.errorcode !== 0) {
            this.$Message.error(res.data.errormsg)
            return
          }
          const resData = res.data.data
          this.infoData = (Array.isArray(resData)? resData : [resData]).map(item => {
            return {
              ...item,
              sign_in_time_string: formatDate( new Date(Number(item.sign_in_time) * 1000), 'yyyy-MM-dd'),
              consumeSn: item?.consume_sn.replace(/^(\w{4})(\w{4})(\w{4})$/, '$1  $2  $3'),
              spaceNames: Array.isArray(item.can_use_space_ids_copy) ? item.can_use_space_ids_copy.join(',') : '入场'
            }
          })
          this.$nextTick(() => {
            window.addEventListener('afterprint', () => {
              localStorage.setItem('receipt', true)
              window.close()
            })
            // 处理打印区域
            const v2container = document.getElementById("v2container")
            if (v2container) {
              let inHTML = v2container.innerHTML
              inHTML = inHTML.replace(/qiankun-head/g, 'head');
              document.body.innerHTML = inHTML;
              setTimeout(() => {
                // setTimeout 处理打印页面loading未消失的问题
                window.print();
              }, 600);
            } else {
              window.print();
            }
          })
        })
      }

    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}

// ::v-deep .ivu-form-item {
//   display: block !important;
// }

/deep/ .ivu-form-item {
  display: block !important;
}

// ::v-deep .ivu-form-item-label {
//   line-height: 32px;
// }

/deep/ .ivu-form-item-label {
  line-height: 32px;
}

.qrcode-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .number {
    font-size: 30px;
    font-weight: bold;
    color: #333333;
    width: 100%;
    text-align: center;
  }

  .qrcode {
    width: 200px;
    height: 200px;
  }
}
</style>
