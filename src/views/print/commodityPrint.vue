<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>商品购买</header>
      <Form label-position="left"
          v-if="infoData.consumption_log_info">
        <FormItem label="场馆:">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:">
          <div>{{ infoData.username }}</div>
        </FormItem>
        <FormItem label="电话:">
          <div>{{ infoData.phone }}</div>
        </FormItem>
        <FormItem label="下单时间:">
          <div>{{ infoData.create_time }}</div>
        </FormItem>
        <div class="line"></div>

        <FormItem :label="item.commodity_name" class="splitspace commodity" v-for="item in infoData.consumption_log_info" :key="item.id">
          <div class="flex-start-row">
            <span class="amount">×{{item.purchase_count}}</span>
            <span class="price">{{ item.total_price }}</span>
          </div>
        </FormItem>

        <div class="line"></div>
        <FormItem label="总计:" class="space-bet-row w-48">
          <div>{{ infoData.amount }}</div>
        </FormItem>
        <FormItem label="支付方式:" class="space-bet-row w-48">
          <div>{{ infoData.pay_type }}</div>
        </FormItem>
        <div class="line"></div>
        <FormItem label="打印时间:">
          <div>{{ date }}</div>
        </FormItem>
        <FormItem label="打印账号:">
          <div>{{ infoData.print_admin }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  import { formatDate } from 'utils'
  export default {
    name: 'commodityPrint',
    data () {
      return {
        infoData: {},
        date: formatDate(new Date(), 'yyyy-MM-dd HH:mm')
      }
    },
    created () {
      this.getInfo(this.$route.query.type, this.$route.query.user_id, this.$route.query.order_sn, this.$route.query.oper_type)
    },
    methods: {
      getInfo (type,user_id,order_sn) {
        const url = '/Web/SmallTicket/printTicket'
        this.$service.post(url, { type: type, user_id: user_id, order_sn: order_sn }).then(res => {
          if (res.data.errorcode === 0) {
            this.infoData = res.data.data;
            this.$nextTick(() => {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });
              // 处理打印区域
              const v2container = document.getElementById("v2container")
              if (v2container) {
                let inHTML = v2container.innerHTML
                inHTML = inHTML.replace(/qiankun-head/g, 'head');
                document.body.innerHTML = inHTML;
                setTimeout(() => {
                  // setTimeout 处理打印页面loading未消失的问题
                  window.print();
                }, 600);
              } else {
                window.print();
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err);
          setTimeout(() => {
            window.close();
          }, 1000)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.commodity {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
.commodity .amount {
  margin-right: 3mm;
  width: 8mm;
}
.commodity .price {
  width: 17mm;
}
.commodity /deep/ .ivu-form-item-label{
  width: 25mm;
  padding-top: 0;
  padding-bottom: 0;
}
.commodity /deep/ .ivu-form-item-content {
  width: 27mm;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
</style>
