<template>
  <Modal
    v-model="visible"
    :loading="loading"
    title="合同模版批量复制到其他场馆"
    @on-ok="handleSubmit"
    @on-cancel="handleCancel"
    width="666"
  >
    <div v-if="submitting" class="loading-container">
      <div class="loading-icon">
        <Icon type="ios-sync" size="60" class="spin-icon"/>
      </div>
      <div class="loading-text">正在复制，请稍后...</div>
    </div>
    <template v-else>
      <Alert type="warning">温馨提示：合同复制前后，请仔细核对合同内容，确保模板准确无误。</Alert>
      <Form v-if="visible" ref="formRef" :model="formData" :rules="formRules" :label-width="120">
      <FormItem label="复制到目标场馆" prop="bus_ids">
        <Select v-model="formData.bus_ids" filterable multiple style="width: 100%" placeholder="请选择">
          <Option v-for="item in shopList" :key="item.id" :value="item.id" :disabled="item.id == busId">
            {{ item.name }}
          </Option>
        </Select>
      </FormItem>

      <FormItem label="选择模版" prop="ids">
        <Transfer
          :data="transferData"
          :target-keys="formData.ids"
          :titles="['可选模版', '已选模版']"
          :render-format="renderFormat"
          :list-style="{
            width: '220px',
            height: '400px'
          }"
          @on-change="handleTempsChange"
          filterable
          :filter-method="filterMethod"
        ></Transfer>
      </FormItem>
      </Form>
    </template>
  </Modal>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick, getCurrentInstance } from 'vue';
import service from '@/service';
import { Message, Alert } from 'iview';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  copyTemplateList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:show']);

const ins = getCurrentInstance();
const busId = ref(ins.proxy.$store.state.busId);

const visible = ref(false);
const loading = ref(true);
const submitting = ref(false);
const formRef = ref(null);
const shopList = ref([]);

// Form data and validation rules
const formData = reactive({
  bus_ids: [],
  ids: [],
});

// fetch shop list
const getShopList = () => {
  return service.get('/Web/contractTemplate/get_copy_template_bus_list').then((res) => {
    if (res.data.errorcode === 0) {
      shopList.value = res.data.data.list;
    }
  });
}

getShopList();

// Transfer component data
const transferData = computed(() => {
  return props.copyTemplateList.map((item) => ({
    key: item.id,
    label: item.name,
    disabled: false,
  }));
});

// Render format for Transfer component
const renderFormat = (item) => {
  return item.label;
}

// Handle Transfer component change
const handleTempsChange = (newTargetKeys) => {
  formData.ids = newTargetKeys;
}

const formRules = {
  bus_ids: [{ required: true, message: '请选择目标场馆', trigger: 'change', type: 'array' }],
  ids: [{ required: true, message: '请选择模版', trigger: 'change', type: 'array' }],
}

// Watch props
watch(
  () => props.show,
  (newVal) => {
    visible.value = newVal;

    if (newVal) {
      loading.value = true;
    }
  }
);

const resetLoading = () => {
  loading.value = false;
  nextTick(() => {
    loading.value = true;
  });
}

// Form submit handler
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // Show loading animation
      // submitting.value = true;

      // Add your submit logic here
      service.post('/Web/contractTemplate/copy_template', formData, { loading: true }).then((res) => {
        submitting.value = false;
        if (res.data.errorcode === 0) {
          formData.ids = [];
          formData.bus_ids = [];
          emit('update:show', false);
          Message.success('合同复制成功，请到对应场馆下查看。');
        } else if (res.data.errorcode === 4250) {
          resetLoading();
          Message.error('正在执行批量复制合同模版任务，请稍后再试。');
        } else {
          resetLoading();
          Message.error('合同复制失败，请重新复制。');
        }
      }).catch(() => {
        submitting.value = false;
        resetLoading();
        Message.error('提交失败，请稍后重试');
      })
    } else {
      resetLoading();
    }
  });
};

const handleCancel = () => {
  emit('update:show', false);
};

// Filter method for Transfer component
const filterMethod = (data, query) => {
  return data.label.toLowerCase().includes(query.toLowerCase());
}
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loading-icon {
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #333;
}

.spin-icon {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
