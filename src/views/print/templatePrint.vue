<template>
  <div class="order-print ">
    <div class="paper-page">
      <div class="has-break" v-if="status==1">合同已作废</div>
      <div v-html="html" class="ql-editor"></div>
    </div>
  </div>
</template>
<script>
import 'quill/dist/quill.snow.css'
import 'src/styles/quill-table.less'
export default {
  name: 'membershipPrint',
  data() {
    return {
      status: 0, //1作废  0没有作废
      html: '',
      isEnd: false
    }
  },
  components: {},
  created() {
    this.getPrintInfo()
  },
  methods: {
    saveDoc(hemlInfo, htmlPic) {
      if (!window.Blob) {
        this.$Message.error({
          content: '浏览器版本过低，不支持导出为word'
        })
        return
      }
      let fileContent, cssInfo, link, blob, url, filename;
      cssInfo = "body{line-height:1.5}p{line-height:1.42}h1{font-size:2em;margin:.67em 0}*{box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}*:before,*:after{box-sizing:border-box}body{font-family:'Microsoft YaHei','\\5FAE\\8F6F\\96C5\\9ED1',Helvetica,sans-serif,Arial;font-size:12px;line-height:1.5}body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,form,fieldset,legend,input,textarea,p,blockquote,th,td,hr,button,article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{margin:0;padding:0}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}input::-ms-clear,input::-ms-reveal{display:none}a{color:#2d8cf0;background:transparent;text-decoration:none;outline:0;cursor:pointer;transition:color .2s ease}a:hover{color:#57a3f3}a:active{color:#2b85e4}a:active,a:hover{outline:0;text-decoration:none}a[disabled]{color:#ccc;cursor:not-allowed;pointer-events:none}code,kbd,pre,samp{font-family:Consolas,Menlo,Courier,monospace}.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li::before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{box-sizing:border-box;line-height:1.42;height:100%;outline:0;overflow-y:auto;padding:12px 15px;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word}.ql-editor>*{cursor:text}.ql-editor p,.ql-editor ol,.ql-editor ul,.ql-editor pre,.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6{margin:0;padding:0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul[data-checked=true],.ql-editor ul[data-checked=false]{pointer-events:none}.ql-editor ul[data-checked=true]>li *,.ql-editor ul[data-checked=false]>li *{pointer-events:all}.ql-editor ul[data-checked=true]>li::before,.ql-editor ul[data-checked=false]>li::before{color:#777;cursor:pointer;pointer-events:all}.ql-editor li::before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl)::before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl::before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) '. '}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) '. '}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) '. '}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) '. '}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) '. '}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) '. '}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) '. '}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) '. '}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) '. '}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) '. '}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank::before{color:rgba(0,0,0,0.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}"
      fileContent = `<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><title>rocketbird</title> <xml><w:worddocument xmlns:w="#unknown"><w:zoom>50</w:zoom></w:worddocument></xml><style><!--@page{size:21cm 29.7cmt;/* A4 */margin:1cm 1cm 1cm 1cm;/* Margins: 2.5 cm on each side */mso-page-orientation: portrait;}@page Section1 {}div.Section1 {page:Section1;}${cssInfo}--></style></head><body><div class='Section1' style='white-space: nowrap !important;'>${hemlInfo}</div></body></html>`
      blob =  new Blob([fileContent], {
        type: "application/msword;charset=utf-8"
      });
      url = URL.createObjectURL(blob)
      link = document.createElement('a')
      link.href = url
      filename = '合同'+this.getDate()+'.doc'
      // Set default file name.
      // Word will append file extension - do not add an extension here.
      link.download = filename
      document.body.appendChild(link)
      if (navigator.msSaveOrOpenBlob) {
        navigator.msSaveOrOpenBlob(blob, filename) // IE10-11
      } else {
        link.click() // other browsers
      }
      document.body.removeChild(link)
      setTimeout(() => {
        window.close();
      }, 500)
    },
    getDate() {
      let date = new Date();
      let nowMonth = date.getMonth() + 1;
      let strDate = date.getDate();
      let seperator = "-";
      if (nowMonth >= 1 && nowMonth <= 9) {
        nowMonth = "0" + nowMonth;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      return date.getFullYear() + seperator + nowMonth + seperator + strDate;
    },
    getPrintInfo() {
      this.$service
        .post(
          '/Web/member/print_order_new',
          {
            cardorder_info_id: this.$route.query.cardorder_info_id,
            template_id: this.$route.query.template_id
          }
        )
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.status = resData.status
            this.html = resData.html
            if (this.$route.query.printerType == 2) {
              // let resHtml = resData.html.replace(/>([^<]+)/g, function ($0, $1) {
              //   return '>' + $1.replace(/ /g, '&nbsp;');
              // });
              this.$nextTick(() => {
                this.saveDoc(resData.html, resData.pic)
              })
            } else {
              window.addEventListener('afterprint', (event) => {
                localStorage.setItem('receipt',true)
                window.close();
              });

              this.$nextTick(() => {
                // 处理打印区域
                const v2container = document.getElementById("v2container")
                if (v2container) {
                  let inHTML = v2container.innerHTML
                  inHTML = inHTML.replace(/qiankun-head/g, 'head');
                  document.body.innerHTML = inHTML;

                  this.handlePrint()
                } else {
                  this.handlePrint()
                }

              })
            }
          } else {
            throw new Error(res.data.errormsg)
          }
        })
        .catch(err => {
          this.$Message.error({
            content: err || '网络或服务器错误',
            duration: 1.5,
            onClose() {
              setTimeout(() => {
                window.close()
              }, 500);
            }
          })
        })
    },
    handlePrint() {
      let flag = 0
      let imgs = document.querySelectorAll('img')

      if (imgs.length) {
        imgs.forEach((img, index) => {
          img.onload = () => {
            flag++
            if(flag === imgs.length) {
              setTimeout(() => {
                // setTimeout 处理打印页面loading未消失的问题
                window.print();
              }, 600);
            }
          }
        });
      } else {
        setTimeout(() => {
          window.print()
        }, 600);
      }
    }
  }
}
</script>

<style lang="less">
@media print {
  @page {
    size: 210mm 297mm;
    margin: 0;
    padding: 0;
  }
}
.has-break {
  position: absolute;
  right: 50px;
  top: 50px;
  width: 120px;
  height: 52px;
  line-height: 52px;
  border: 1px dashed #ff0000;
  color: #ff0000;
  text-align: center;
  font-size: 18px;
  transform: rotate(345deg);
  z-index: 1;
}
.paper-page {
  position: relative;
}
.order-print {
  width: 210mm;
  margin: 0 auto;
  .ql-editor {
    font-family: 'Microsoft YaHei', '微软雅黑', Helvetica, sans-serif, Arial;
    img {
      max-width: 100%;
    }
  }
}

.ql-snow .ql-picker.ql-font {
  .ql-picker-label[data-value=serif]::before,
  .ql-picker-item[data-value=serif]::before {
    content: '黑体'
  }

  .ql-picker-label[data-value=monospace]::before,
  .ql-picker-item[data-value=monospace]::before {
    content: '微软雅黑'
  }

  .ql-picker-item[data-value=serif]::before {
    font-family: SimHei
  }

  .ql-picker-item[data-value=monospace]::before {
    font-family: 'Microsoft YaHei'
  }
}

.ql-editor {
  .ql-font-serif {
    font-family: SimHei
  }

  .ql-font-monospace {
    font-family: 'Microsoft YaHei'
  }
}
</style>




