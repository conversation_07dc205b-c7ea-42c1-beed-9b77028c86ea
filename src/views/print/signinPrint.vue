<template>
  <div class="sign-print">
    <div class="sign_print_item">
      <header>{{infoData.title}}</header>
      <Form label-position="left"
          v-if="infoData">
        <FormItem label="场馆:">
          <div>{{ infoData.bus_name }}</div>
        </FormItem>
        <FormItem label="会员:">
          <div>{{ infoData.username }}</div>
        </FormItem>
        <FormItem label="电话:">
          <div>{{ infoData.phone }}</div>
        </FormItem>
        <FormItem label="签到时间:">
          <div>{{ infoData.sign_time }}</div>
        </FormItem>

        <div v-for="(item, index) in infoData.info" :key="index">
          <div class="line"></div>

          <FormItem label="签到用卡:" class="space-bet-row w-48">
            <div>{{ item.sign_card_name }}</div>
          </FormItem>
          <FormItem label="本次扣除:" class="space-bet-row w-48">
            <div>{{ item.use }}</div>
          </FormItem>
          <FormItem label="卡剩余量:" class="space-bet-row w-48">
            <div>{{ item.remain }}</div>
          </FormItem>
        </div>

        <!-- <div class="line"></div>

        <FormItem label="总计:" class="space-bet-row w-48">
          <div>{{ infoData.total_fee }}</div>
        </FormItem>
        <FormItem label="支付方式:" class="space-bet-row w-48">
          <div>{{ infoData.pay_type }}</div>
        </FormItem> -->

        <div class="line"></div>

        <FormItem label="打印时间:">
          <div>{{ infoData.print_time }}</div>
        </FormItem>
        <FormItem label="打印账号:">
          <div>{{ infoData.admin }}</div>
        </FormItem>
      </Form>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'signinPrint',
    data () {
      return {
        signinIds: [],
        infoData: {}
      }
    },
    created () {
        const arrStr = this.$route.query.signinIds;
        if (arrStr.indexOf(',') === -1) {
          this.signinIds.push(arrStr);
        } else {
          this.signinIds = arrStr.split(',');
        }
        this.getInfo();
    },
    methods: {
      getInfo () {
        return this.$service.post("/Web/Sign/print_small_ticket", {
          sign_ids: this.signinIds
        }).then(res => {
            if (res.data.errorcode == 0) {
                this.infoData = res.data.data;
                this.$nextTick(() => {
                  window.addEventListener('afterprint', (event) => {
                    localStorage.setItem('receipt',true)
                    window.close();
                  });
                  // 处理打印区域
                  const v2container = document.getElementById("v2container")
                  if (v2container) {
                    let inHTML = v2container.innerHTML
                    inHTML = inHTML.replace(/qiankun-head/g, 'head');
                    document.body.innerHTML = inHTML;
                    setTimeout(() => {
                      // setTimeout 处理打印页面loading未消失的问题
                      window.print();
                    }, 600);
                  } else {
                    window.print();
                  }
                });
            } else {this.$Message.error(res.data.errormsg);}
        });
      }
    }
  }
</script>

<style lang="less" scoped>
.remark {
  margin: 0 0 15pt;
}
.space-bet-row:before {
   content: none;
}
.space-bet-row:after {
   content: none;
}
@media print {
  -webkit-print-color-adjust: exact;
  * {
    margin: 0;
    padding: 0;
    line-height: 1;
  }
  @page {
    size: 48mm auto;
    margin: 0;
    padding: 0;
  }
}
.w-48 {
  width: 48mm
}
.length-lim-48 {
  width: 48mm;
  word-break: break-all;
  line-height: 1;
}
</style>
