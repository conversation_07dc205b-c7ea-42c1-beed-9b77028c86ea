<template>
  <div class="msg-box">
    <div class="msg-left">
     <ul class="con-list">
       <li v-for="(item, index) in templateList" :key="item.id" @click="templateChange(item,index)" :class="curIndex===index && !hasAddTemplate?'cur':''" >
         <span class="name">{{item.name}}<span class="name-type">[{{item.type_txt}}]</span></span> 
         <img @click="modifyName(item)" src="~assets/img/icon-edit-min.png" title="重命名" alt="edit"/>
         <img @click="handleCopyClick(item, index)" src="~assets/img/coach-reserve.png" title="复制模板" alt="copy"/>
         <img @click="delTemplate(item)"  v-if="item.can_delete" src="~assets/img/icon-del-min.png" title="删除" alt="del"/>
        </li>
        <li v-if="hasAddTemplate" :class="hasAddTemplate?'cur':''" >
         <span class="name">{{addTemplateForm.name}}</span> 
        </li>
     </ul>
     <Button v-show="!hasAddTemplate"  class="contract-button" type="primary" icon="plus" @click="addTemplateShow">添加模板</Button>
     <Button class="contract-button" type="primary" icon="copy" @click="handleCopyTo">批量复制到其他场馆</Button>
    </div>
    <div class="msg-rig">
      <div class="rig-top">
        <span class="rig-button cur" v-if="!isEdit" @click="isEdit = true">编辑</span>
        <span class="rig-button cur" v-if="isEdit"  @click="saveTemplate">保存</span>
        <span class="rig-button" v-if="isEdit"  @click="cancelSet">取消</span>
      </div>
      <div class="rig-con">
        <PrintEditor v-model="curEditContent.detail" :fields="curEditContent.fields" @pos-update="imgPosUpdate" :disabled="!isEdit"/>
      </div>
    </div>
    <Modal :mask-closable="false" v-model="showAddTemplate" title="创建新模板">
      <Form ref="templateForm" :model="addTemplateForm" class="modal-form" style="padding: 0 30px" :label-width="80">
        <Form-item label="模板类型" prop="type" :rules="{required: true, message: '请选择模板类型'}">
          <Select v-model="addTemplateForm.type" placeholder="请选择">
            <Option v-for="item in typeList" :key="item.id" :value="+item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="模板名称" prop="name" :rules="{required: true, message: '请填写模板名称'}">
          <Input v-model="addTemplateForm.name" />
        </Form-item>
        <Form-item>
          <Checkbox v-model="addTemplateForm.default" :true-value="1" :false-value="0">将此模板设置成该类合同的默认打印模板</Checkbox>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="addTemplate">确定</Button>
        <Button @click="showAddTemplate = false">取消</Button>
      </div>
    </Modal>
    <Modal :mask-closable="false" v-model="showEditName" title="编辑模板名称">
      <Form ref="modifyForm" :model="modifyForm" class="modal-form" style="padding: 0 30px" :label-width="80">
        <Form-item label="模板名称" prop="name" :rules="{required: true, message: '请填写模板名称'}">
          <Input v-model="modifyForm.name" />
        </Form-item>
        <Form-item>
          <Checkbox v-model="modifyForm.default" :true-value="1" :false-value="0">将此模板设置成该类合同的默认打印模板</Checkbox>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveModifyName">确定</Button>
        <Button @click="showEditName = false">取消</Button>
      </div>
    </Modal>

    <Modal :mask-closable="false" v-model="showCopyTemplate" title="复制模板">
      <Form ref="templateForm" :model="addTemplateForm" class="modal-form" style="padding: 0 30px" :label-width="120">
        <Form-item label="复制到类型" prop="type" :rules="{required: true, message: '请选择模板类型'}">
          <Select v-model="addTemplateForm.type" placeholder="请选择">
            <Option v-for="item in typeList" :key="item.id" :value="+item.id">{{item.name}}</Option>
          </Select>
        </Form-item>
        <Form-item label="复制后模板名称" prop="name" :rules="{required: true, message: '请填写模板名称'}">
          <Input v-model="addTemplateForm.name" />
        </Form-item>
        <Form-item>
          <Checkbox v-model="addTemplateForm.default" :true-value="1" :false-value="0">将此模板设置成该类合同的默认打印模板</Checkbox>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="copyTemplate">确定</Button>
        <Button @click="showCopyTemplate = false">取消</Button>
      </div>
    </Modal>

    <batch-copy :show.sync="showBatchCopy" :copy-template-list="copyTemplateList" />
  </div>
</template>

<script>
import PrintEditor from './components/PrintEditor'
import BatchCopy from './components/BatchCopy.vue'

export default {
  name: 'ContractTemplate',
  mounted() {},
  data() {
    return {
      showAddTemplate: false,
      showCopyTemplate: false,
      copyItemTemplate: null,
      showEditName: false,
      isEdit: false,
      contratForm: {
        id: '', //新增不传，修改传
        name: '', //名称
        detail: '', //模板详情
        type: 1, //模板类型
        default: 1 //是否作为默认
        //用户签名位置
      },
      curEditContent: {
        detail: '',
        content: '',
        fields: [],
        userPosi: {
          x: '',
          y: '',
          pageNo: ''
        },
        busPosi: {
          x: '',
          y: '',
          pageNo: ''
        }
      },
      addTemplateForm: {
        id: '',
        name: '',
        detail: '',
        type: 1,
        default: 1
      },
      modifyForm: {
        id: '',
        name: '',
        default: ''
      },
      templateList: [],
      typeList: [],
      curIndex: 0,
      hasAddTemplate: false,
      // batch copy
      showBatchCopy: false,
    }
  },
  computed: {
    copyTemplateList() {
      return this.templateList.filter(item => Number(item.bus_id) !== 0);
    }
  },
  components: {
    PrintEditor,
    BatchCopy,
  },
  created() {
    this.getTemplateList()
    this.getTypeAndfields()
  },

  methods: {
    cancelSet() {
      this.isEdit = false
      if (this.hasAddTemplate) {
        this.hasAddTemplate = false
        this.addTemplateForm = {
          id: '',
          name: '',
          detail: '',
          type: 1,
          default: 1
        }
        this.curEditContent.detail = this.templateList[this.curIndex].detail
      } else {
        this.curEditContent.detail = this.templateList[this.curIndex].detail
      }
    },
    imgPosUpdate(obj) {
      Object.assign(this.curEditContent, obj)
    },
    addTemplateShow() {
      this.isEdit = true
      this.showAddTemplate = true
      this.addTemplateForm = {
        id: '',
        name: '',
        detail: '',
        type: 1,
        default: 1
      }
    },
    handleCopyClick(item, index) {
      this.copyItemTemplate = item
      this.isEdit = false
      this.setItemVal(item)
      this.curIndex = index
      this.hasAddTemplate = false

      this.showCopyTemplate = true
      this.addTemplateForm = {
        id: '',
        name: '',
        detail: item.detail,
        type: Number(item.type),
        default: Number(item.default)
      }
    },
    addTemplate() {
      this.$refs.templateForm.validate(valid => {
        if (valid) {
          this.showAddTemplate = false
          this.hasAddTemplate = true
          this.curEditContent.detail = ''
          this.typeList.forEach(item => {
            if (item.id == this.addTemplateForm.type) {
              this.curEditContent.fields = item.fields
            }
          })
        }
      })
    },
    copyTemplate() {
      this.$refs.templateForm.validate(valid => {
        if (valid) {
          // this.templateList.push({
          //   ...this.copyItemTemplate,
          //   id: '',
          //   name: this.addTemplateForm.name,
          //   type: this.addTemplateForm.type,
          //   default: this.addTemplateForm.default
          // })
          this.showCopyTemplate = false
          this.hasAddTemplate = true
          this.curEditContent.detail = this.addTemplateForm.detail
          this.isEdit = true
          // this.typeList.forEach(item => {
          //   if (item.id == this.addTemplateForm.type) {
          //     this.curEditContent.fields = item.fields
          //   }
          // })
        }
      })
    },
    templateChange(item, index) {
      if (this.hasAddTemplate) {
        this.$Modal.confirm({
          title: '提示',
          content: '有未保存的新加模板,是否继续?',
          onOk: () => {
            this.setItemVal(item)
            this.curIndex = index
            this.hasAddTemplate = false
          },
          onCancel() {}
        })
      } else {
        if (this.isEdit) {
          this.$Modal.confirm({
            title: '提示',
            content: '有未保存的编辑模板,是否继续?',
            onOk: () => {
              this.setItemVal(item)
              this.curIndex = index
            },
            onCancel() {}
          })
        } else {
          this.setItemVal(item)
          this.curIndex = index
        }
      }
    },
    setItemVal(item) {
      this.curEditContent.detail = item.detail
      this.curEditContent.fields = item.fields
      this.contratForm.id = item.id
      this.contratForm.default = item.default
      this.contratForm.name = item.name
      this.contratForm.type = parseInt(item.type)
    },
    saveModifyName() {
      // 合同默认模板同类型下只能有1个，设置A模板后，原先B模板如果是默认模板就会被取消。
      if (this.modifyForm.default == 0) {
        // 不准取消
        const theSameType = this.templateList.findIndex(item => (item.default==1&&item.type==this.modifyForm.id))
        if (theSameType === -1) {
          this.$Message.error('合同默认模板同类型下必须要有一个，不能取消!')
          return false
        }
      }

      return this.$service
        .post('/Web/contractTemplate/modifyName', this.modifyForm)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.showEditName = false
            this.templateList.forEach(item => {
              if (item.id == this.modifyForm.id) {
                item.name = this.modifyForm.name
                this.contratForm.name = this.modifyForm.name
                this.getTemplateList()
                item.id = res.data.data.id //默认模板ID可能会在更新后改变
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    saveTemplate() {
      let postData = this.contratForm
      if (this.hasAddTemplate) {
        postData = this.addTemplateForm
      }
      let detail = this.curEditContent.detail
      postData.detail = detail
      postData.userPosi = this.curEditContent.userPosi
      postData.busPosi = this.curEditContent.busPosi
      return this.$service
        .post('/Web/contractTemplate/save', postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getTemplateList()
            this.hasAddTemplate = false
            this.isEdit = false
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    modifyName(item) {
      this.showEditName = true
      this.modifyForm.id = item.id
      this.modifyForm.name = item.name
      this.modifyForm.default = Number(item.default)
    },
    delTemplate(item) {
      return this.$service
        .post('/Web/contractTemplate/delete', { id: item.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.getTemplateList()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTemplateList() {
      return this.$service
        .get('/Web/contractTemplate/templateList')
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.templateList = resData.list
            this.setItemVal(resData.list[this.curIndex])
            this.isEdit = false
            this.hasAddTemplate = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTypeAndfields() {
      return this.$service
        .get('/Web/contractTemplate/typeAndfields')
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data;
            this.typeList = resData;
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleCopyTo() {
      this.showBatchCopy = true;
    }
  }
}
</script>

<style lang="less" scoped>
.con-list {
  border-top: 1px solid #f5f7fb;
  color: #313131;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 35px;
  max-height: 80%;
  overflow-y: scroll;
  li {
      overflow: hidden;
    height: 62px;
    line-height: 62px;
    position: relative;
    padding-left: 50px;
    border-bottom: 1px solid #f5f7fb;
    cursor: pointer;
    .name {
      display: inline-block;
      vertical-align: middle;
      width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 5px;
    }
    .name-type {
      font-size: 12px;
      color: #666;
    }
    img {
      display: none;
      cursor: pointer;
      margin-right: 10px;
      vertical-align: middle;
    }
    &::after {
      background: #8dd1ca;
      content: '';
      width: 4px;
      height: 4px;
      border-radius: 50%;
      position: absolute;
      left: 36px;
      top: 50%;
      transform: translateY(-50%);
    }
    &.cur,
    &:hover {
      color: #52a4ea;
    }
    &:hover {
      img {
        display: inline-block;
      }
    }
  }
}
.contract-button {
  background: #7fccc4;
  border: 1px solid #7fccc4;
  margin-left: 36px;
}
.msg-button {
  border: 1px solid rgb(241, 243, 247);
  background-color: #fff;
  border-radius: 2px;
  color: #979faf;
  text-align: center;
  box-shadow: 0px 3px 5px 0px rgba(53, 73, 93, 0.1);
  height: 34px;
  line-height: 34px;
  overflow: hidden;
}
.msg-box {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  .msg-left {
    overflow-y: scroll;
    background-color: #fff;
    box-shadow: 0px 3px 20px 0px rgba(44, 57, 69, 0.1);
    position: absolute;
    width: 360px;
    height: 100%;
    padding: 32px 0;
    font-size: 14px;
    box-sizing: border-box;
    float: left;
  }
}

.msg-rig {
  margin-left: 395px;
  .rig-top {
    height: 90px;
    padding-top: 32px;
    .rig-button {
      .msg-button;
      display: inline-block;
      width: 98px;
      margin-right: 18px;
      cursor: pointer;
      &.cur,
      &:hover {
        color: #fff;
        background: #52a4ea;
      }
    }
  }
  .rig-con {
    margin-right: 35px;
    padding-bottom: 30px;
  }
}
</style>
