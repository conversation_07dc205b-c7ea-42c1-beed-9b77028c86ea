<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
        <Input style="width: 180px" v-model="postData.order_sn" class="option-select" placeholder="订单编号" />
        <Input style="width: 180px" v-model="postData.nickname_or_phone" class="option-select" placeholder="购票人姓名/电话" />
        <DatePicker
          v-model="duringDate"
          @on-change="handleDateChange"
          type="daterange"
          placement="bottom-end"
          placeholder="入场时间"
          class="option-select"
          style="width: 220px"
          :clearable="false"
        ></DatePicker>
        <Select v-model="postData.buy_channel" class="option-select" placeholder="全部渠道">
          <Option value="">全部渠道</Option>
          <Option value="1">前台</Option>
          <Option value="2">会员端</Option>
          <Option value="3">票务机-刷掌购票</Option>
          <Option value="4">票务机-手机号购票</Option>
          <Option value="5">美团</Option>
          <Option value="6">大众点评</Option>
          <Option value="7">抖音</Option>
          <Option value="8">快诺优票务机</Option>
        </Select>
        <Select v-model="postData.status" class="option-select" placeholder="票状态">
          <Option value="">票状态</Option>
          <Option value="1">待核销</Option>
          <Option value="2">已核销</Option>
          <Option value="5">已过期</Option>
          <Option value="6">已退票</Option>
        </Select>
        <Input style="width: 180px" v-model="postData.operation_user" class="option-select" placeholder="购票/退票操作账号" clearable />
        <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
        <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-body-total">
      <Col span="24" class="box-body-total-col">
        <div class="t-label">总计</div>
        <div class="t-desc">
          <div v-for="item in totalStat" :key="item.name">{{ item.name }}: ￥{{ item.amount }}</div>
        </div>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
        <div class="option-ctrl">
          <Button style="margin-right: 30px" @click="handleExport">导出 Excel</Button>
        </div>
        <Page
          @on-change="handlePage"
          :total="total"
          :current="postData.page_no"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </Col>
    </Row>
    <!-- 退票弹窗 -->
    <Modal v-model="returnTicketModal" title="退票" width="800">
      <Form
        ref="returnTicketFrom"
        :model="returnTicket"
        :label-width="100"
      >
        <FormItem label="票名称">
          <div>{{ returnTicket.san_name }}</div>
        </FormItem>
        <FormItem label="购票金额">
          <div>{{ returnTicket.buy_san_amount }}</div>
        </FormItem>
        <FormItem label="退款金额">
          <InputNumber class="w120 mr15" v-model="returnTicket.refundAmount" :min="0" :max="returnTicket.buy_san_amount || 0" />
        </FormItem>
        <FormItem label="退款方式" v-if="returnTicketModal && returnTicket.status != 2 && isGoPay" prop="new_pay_type" :rules="{ required: true, message: '请选择退款方式!' }">
          <pay-type-list
            v-if="returnTicketModal"
            v-model="returnTicket.new_pay_type"
            :amount="Number(returnTicket.refundAmount)"
            :describe="`退票退款`"
            :userId="returnTicket.user_id"
            :showCardPay="true"
            :isRefundNeedSQB="true"
            :isRefund="true"
          ></pay-type-list>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleSureReturnTicket">确定</Button>
        <Button @click="handleCloseModal">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import Big from 'big.js';
import { formatDate } from '@/utils/index'
import PayTypeList from 'components/form/PayTypeList.vue'

export default {
  name: 'BoughtLogs',
  components: {
    PayTypeList,
  },
  data() {
    const resetOrder = order => {
      let obj = order
      obj.new_pay_type.forEach((item) => {
        item.pay_type = Number(item.pay_type)
      })
      this.isGoPay = false
      this.returnTicket = {
        status: obj.status,
        user_id: obj.user_id,
        san_log_id: obj.id,
        san_name: obj.san_name,
        buy_san_amount: Number(obj.buy_san_amount || 0),
        refundAmount: Number(obj.buy_san_amount || 0),
        new_pay_type: obj.new_pay_type || [],
      };
    };
    return {
      total: 0,
      duringDate: [],
      columns: [
        { title: '订单编号', key: 'order_sn', width: 200 },
        { title: '票名称', key: 'san_name' },
        {
          title: '购票渠道',
          key: 'buy_channel_copy',
        },
        {
          title: '购票时间',
          key: 'create_time',
          render: (h, params) => {
            return <div>{formatDate(new Date(Number(params.row.create_time) * 1000), 'yyyy-MM-dd HH:mm')}</div>
          }
        },
        {
          title: '有效期',
          key: 'valid_until',
          render: (h, params) => {
            return <div>{formatDate(new Date(Number(params.row.valid_until) * 1000), 'yyyy-MM-dd')}</div>
          }
        },
        {
          title: '退票时间',
          key: 'retreat_time',
          render: (h, params) => {
            if (!params.row.retreat_time || params.row.retreat_time == 0) {
              return <div>--</div>
            } else {
              return <div>{formatDate(new Date(Number(params.row.retreat_time) * 1000), 'yyyy-MM-dd HH:mm')}</div>
            }
          }
        },
        {
          title: '状态',
          key: 'status_copy',
          render: (h, params) => {
            if (params.row.status == 1) {
              return <span style="color:black">{params.row.status_copy}</span>
            } else if (params.row.status == 2) {
              return <span style="color:#19be6b">{params.row.status_copy}</span>
            } else if (params.row.status == 5) {
              return <span style="color:red">{params.row.status_copy}</span>
            } else if (params.row.status == 6) {
              return <span style="color:red">{params.row.status_copy}</span>
            }
          }
        },
        {
          title: '购票人',
          key: 'username',
          render: (h, params) => {
            const tipsName = `已赠与 ${params.row.san_username || ''} ${params.row.san_phone || ''}`
            if (params.row.is_real_user) {
              return (
                params.row.is_receive ? <Tooltip 
                  content={tipsName}
                  transfer
                >
                  <a
                    onClick={() => {
                      this.$router.push(`/member/detail/${params.row.user_id}/${params.row.bus_id}`)
                    }}
                  >
                    {params.row.username} (已赠)
                  </a>
                </Tooltip>
                : <a
                  onClick={() => {
                    this.$router.push(`/member/detail/${params.row.user_id}/${params.row.bus_id}`)
                  }}
                >
                  {params.row.username}
                </a>
              )
            } else {
              return params.row.is_receive ? <Tooltip 
                  content={tipsName}
                  transfer
                >
                <div>{params.row.username}</div>
              </Tooltip>
              : <div>{params.row.username}</div>
            }
          }
        },
        { title: '购票金额', key: 'buy_san_amount' },
        { title: '交纳押金', key: 'pre_amount' },
        {
          title: '消费金额',
          key: 'use_amount',
          renderHeader(h, params) {
            return <div>
              消费金额
              <Tooltip content="购票金额+超时费用" max-width="200" transfer>
                <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
              </Tooltip>
            </div>
          },
        },
        { title: '退款金额', key: 'refund_amount' },
        { title: '补交金额', key: 'repre_amount' },
        { title: '购票账号', key: 'buy_user' },
        { title: '退票账号', key: 'retreat_user' },
        { 
          title: '操作',
          key: 'operate',
          render: (h, params) => {
            return (
              <div>
                <i-button
                  type="text"
                  QE570-
                  shape="circle"
                  size="small"
                  disabled={ params.row.status != 1 }
                  style={{ color: params.row.status != 1 ? '#999999' : '#52a4ea', minWidth: '0' }}
                  onClick={$event => {
                    resetOrder(params.row);
                    this.handleReturnTicket();
                    this.returnTicketModal = true
                  }}
                >
                  退票
                </i-button>
              </div>
            )
          }
        }
      ],
      list: [],
      totalStat: [],
      postData: {
        page_no: 1,
        page_size: 10,
        is_export: 0,
        bus_id: this.$store.state.busId,
        is_end: 0,
        order_sn: '',
        nickname_or_phone: '',
        start_time: '',
        end_time: '',
        buy_channel: '',
        status: '',
        ticket_type: 1,
        order_create_time: 'desc',
        operation_user: '',
      },
      returnTicketModal: false,
      returnTicket: {
        san_log_id: '',
        san_name: '',
        buy_san_amount: 0,
        refundAmount: 0,
        new_pay_type: [],
      },
      isGoPay: false
    }
  },
  methods: {
    getList() {
      if (Array.isArray(this.duringDate) && this.duringDate.length === 2) {
        this.postData.start_time = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        this.postData.end_time = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }
      return this.$service.post('/Web/San/getSanList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.total = parseInt(res.data.data.count)
          this.list = []
          if (Array.isArray(res.data.data.list)) {
            this.list = res.data.data.list
          }

          const statistics = res.data.data.StatisticsMoney
          this.totalStat = []
          if (statistics.total_buy_san_amount) {
            this.totalStat.push({
              name: '购票',
              amount: statistics.total_buy_san_amount
            })
          }
          if (statistics.total_pre_amount) {
            this.totalStat.push({
              name: '交纳',
              amount: statistics.total_pre_amount
            })
          }
          if (statistics.total_use_amount) {
            this.totalStat.push({
              name: '消费',
              amount: statistics.total_use_amount
            })
          }
          if (statistics.total_refund_amount) {
            this.totalStat.push({
              name: '退款',
              amount: statistics.total_refund_amount
            })
          }
          if (statistics.total_repre_amount) {
            this.totalStat.push({
              name: '补交',
              amount: statistics.total_repre_amount
            })
          }
          if (statistics.total_amount) {
            this.totalStat.push({
              name: '合计收入',
              amount: statistics.total_amount
            })
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
    handleDateChange(val) {
      this.duringDate = val
    },
    handlePage(val) {
      this.postData.page_no = val
      this.getList()
    },
    showDate(datetime) {
      if (!datetime || datetime == 0) {
        return ''
      } else {
        return formatDate(new Date(Number(datetime) * 1000), 'yyyy-MM-dd')
      }
    },
    handleExport() {
      if (Array.isArray(this.duringDate) && this.duringDate.length === 2) {
        this.postData.start_time = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        this.postData.end_time = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }
      const copySearch = { ...this.postData, page_no: 1, page_size: this.total, is_export: 1 }

      // this.$service.post('/Web/San/getSanList', copySearch).then(res => {
      this.$service.post('/Web/San/exportListByOrder', copySearch).then(res => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
            list.forEach(item => {
              item.order_sn = '\u0009' + item.order_sn
              if(item.is_receive) {
                item.username = '\u0009' + item.username
                item.receive = item.san_username + ' ' + item.san_phone
              } else {
                item.username = '\u0009' + item.username
                item.receive = ''
              }
              // item.buy_channel = item.buy_channel == 1 ? '前台' : item.buy_channel == 2 ? '会员端' : item.buy_channel == 3 ? '票务机-刷掌购票' : '票务机-手机号购票'
              item.create_time = '\u0009' + this.showDate(item.create_time)
              item.retreat_time = '\u0009' + this.showDate(item.retreat_time)
              item.valid_until = '\u0009' + this.showDate(item.valid_until)
            })
          }
          let columnsCV = JSON.parse(JSON.stringify(this.columns.slice(0, this.columns.length - 1)))
          columnsCV.splice(8, 0, {title:'转赠', key: 'receive'})
          this.$refs.table.exportCsv({
            filename: `购票记录-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: columnsCV,
            data: list
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // 退票退款
    handleReturnTicket() {
      this.isGoPay = true
    },
    handleCloseModal() {
      this.isGoPay = false
      this.returnTicket = {
        status: '',
        user_id: '',
        san_log_id: '',
        san_name: '',
        buy_san_amount: 0,
        refundAmount: 0,
        new_pay_type: [],
      }
      this.returnTicketModal = false;
    },
    // 确认退款
    handleSureReturnTicket() {
      if(this.returnTicket.refundAmount > this.returnTicket.buy_san_amount) {
        this.$Message.error('实际退款金额不能大于退款金额!')
        return false;
      }
      let amount = new Big(0)
      this.returnTicket.new_pay_type.forEach(item => {
        amount = amount.plus(item.amount)
      })
      const refundAmount = new Big(this.returnTicket.refundAmount)
      if(!amount.eq(refundAmount)) {
        this.$Message.error('实际退款总支付金额必须等于退款金额!')
        return false;
      }
      let url = '/Web/San/cancel'
      this.$service.post(url, this.returnTicket).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg)
          this.handleCloseModal();
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
  created() {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
    this.duringDate = [start, end]

    this.getList()
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-body-total {
    border: 1px solid #dddee1;
    border-top: none;

    .box-body-total-col {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .t-label {
        width: 200px;
        padding: 0 40px;
        font-size: 16px;
        font-weight: bold;
      }

      .t-desc {
        width: 70%;
        max-width: 960px;
        font-size: 16px;
        display: flex;
        flex-wrap: wrap;
        div {
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          display: flex;
          margin-right: 15px;
        }
      }
    }
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }
    }
  }
}

.price-info-pay {
  margin-bottom: 15px;
  .label {
    margin-bottom: 15px;
  }
}
</style>
