<template>
  <Modal v-model="showModal" :mask-closable="false" title="美团门店授权">
    <div style="margin-bottom: 20px;">
      是否对当前门店进行"美团到店综合业务授权"
    </div>
    <Form
      ref="formRef"
      :model="formData"
      :label-width="105">
      
      <FormItem prop="is_auth_all" label="批量授权">
         <Checkbox v-model="formData.is_auth_all"></Checkbox>
        <div>对以下{{nameList.length}}家门店进行"美团到店综合业务授权"</div>
        <div>
          <span v-for="(item, index) in nameList" :key="index" v-show="index < 4 || isShowMore">{{item.name}}{{index === nameList.length-1 ? '' : '、'}}</span>
          <a class="show-more" v-if="nameList.length > 4 && !isShowMore" @click="isShowMore = !isShowMore">...【展示全部】</a>
        </div>
      </FormItem>
      <FormItem v-if="formData.is_auth_all" prop="account" label="美团开店宝账号" :rules="{ required: true, message: '请输入' }">
        <Input
          v-model="formData.account"
          clearable
          placeholder="美团开店宝账号"
        />
        <p>商家打开开店宝时，通过账号密码方式登录的账号。注意，不是手机号</p>
      </FormItem>
    </Form>
     <div
      slot="footer"
      class="modal-buttons">
      <Button type="success" @click="handleSubmit" :loading="isLoading">
        {{formData.is_auth_all?'批量授权':'单店授权'}}
      </Button>
     
      <Button @click="showModal=false">
        取消
      </Button>
    </div>
  </Modal>
</template>
<script setup>
import { ref, defineProps,watch, computed, defineEmits, reactive } from 'vue';
import { Message } from 'iview'
import service from 'src/service'

const emit = defineEmits(['input'])
const props = defineProps({
  value: {
    type: Boolean,
  },
  meituanAuthInfo: {
    type: Object,
  },
})
const formData = reactive({
  account: '',
  is_auth_all: true,
})
const isShowMore = ref(false)
const showModal = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('input', val)
  },
})
watch(() => showModal.value, (val) => {
  if(val) {
    getNames()
  }
})
const nameList = ref([])
function getNames() {
  service
    .get(`Web/sanGroup/getNeedAuthShops?platform_id=${props.meituanAuthInfo.platform_id}`)
    .then(res => {
      if (res.data.errorcode === 0) {
        nameList.value = res.data.data;
      }
    })
}

const formRef = ref()
const isLoading = ref(false)
function handleSubmit() {
  if(formData.is_auth_all){
  formRef.value.validate((valid) => {
    if (valid) {
      isLoading.value = true
      service
        .post('Web/sanGroup/authBatch', {
          ...formData,
          platform_id: props.meituanAuthInfo.platform_id
      })
        .then(res => {
          if (res.data.errorcode === 0) {
            showModal.value = false
            Message.success('操作成功，后台处理中！')
          } else {
            Message.error(res.data.errormsg)
          }
          isLoading.value = false
        })
    }
  })
  } else {
    showModal.value = false
    window.open(props.meituanAuthInfo.url, '_blank');
  }
}
</script>