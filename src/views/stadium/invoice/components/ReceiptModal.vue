<template>
  <Modal v-model="showReceipt" width="380">
    <div class="header">
      <Icon type="ios-checkmark-circle-outline" size="32"></Icon>
      <h3>成功！</h3>
    </div>
    <div slot="footer" class="modal-buttons">
      <router-link style="color: #fff; margin-right: 15px" target="_blank" :to="{ path: url, query: { logId: id, isMultiPrint: isMultiPrint } }">
        <Button type="success" v-if="url === '/nonMemberPrint'">打印小票</Button>
        <Button type="success" v-if="url === '/sanTicketPrint'">打印凭证</Button>
      </router-link>
      <Button @click="showReceipt = false">取消</Button>
    </div>
  </Modal>
  
</template>
<script>
export default {
  name: 'ReceiptModal',
  props: {
    value: {
      type: Boolean
    },
    isMultiPrint: {
      type: Boolean,
      default: false
    },
    id: {
      type: String
    },
    url: {
      type: String,
      default: '/nonMemberPrint'
    }
  },
  data() {
    return {
      keyEnterNum: '',
    }
  },
  computed: {
    showReceipt: {
      get() { 
        return this.value 
      },
      set(value) { 
        this.$emit('input', value) 
      }
    }
  },
  watch: {
  },
  methods: {
    eventFun(event) {
      if (event.key == 'receipt') {
        this.showReceipt = false
        localStorage.removeItem('receipt')
      }
    },
  },
  mounted() {
    window.addEventListener('storage', this.eventFun)
  },
  beforeDestroy() {
    window.removeEventListener('storage', this.eventFun)
  },
  activated() {
    window.addEventListener('storage', this.eventFun)
  },
  deactivated() {
    window.removeEventListener('storage', this.eventFun)
  }
}
</script>
<style lang="less" scoped>
.header {
  width: 100%;
  color: #19be6b;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;

  h3 {
    padding-left: 5px;
    font-weight: normal;
  }
}
</style>
