<template>
  <Modal v-model="showQRCode" width="800" :mask-closable="false" title="核销票据入场">
    <Form ref="qrcodeData" :model="qrcodeData" class="modal-form" :label-width="80">
      <!-- tiktok sale -->
      <Form-item label="购票渠道">
        <div class="ticket-box">
          <div v-for="(item, index) in ticketList"
            :key="index"
            @click="handleTicketClick(index)">
            <Card
              style="width:150px;cursor:pointer"
              :class="index === ticketIndex ? 'ticket-card-active' : ''">
              <div class="ticket-card">
                <img class="ticket-img" :src="item.icon">
                <span class="ticket-text">{{ item.name }}</span>
              </div>
            </Card>
          </div>
        </div>
      </Form-item>

      <!-- 会员端 -->
      <blockquote v-if="ticketIndex === 0">
        <Form-item label="凭证编号" prop="consume_sn" :rules=" { required: true, message: '请输入凭据编号!' }">
          <Input ref="qrcodeSn" name="consume_sn" v-model="qrcodeData.consume_sn" @on-change="handleSNChange" autofocus />
          <div v-if="consumeSnError" class="ivu-form-item-error-tip">{{ consumeSnError }}</div>
        </Form-item>
        <Form-item label="手机号码" prop="phone">
          <!-- <span v-if="qrcodeData.phone">{{ qrcodeData.username }} &nbsp; ({{ qrcodeData.phone }})</span> -->
          <user-search
            :busId="$store.state.busId"
            :hasSelfPhone="true"
            url="/Web/SpaceOrder/searchUserList"
            v-model="userSearchId"
            @on-change="userSelectedByAdd"
            placeholder="姓名/电话"
          ></user-search>
        </Form-item>
        <Form-item
          label="手环号"
          prop="enter_card_number"
        >
          <Input ref="qrcodeHandId" name="handId" v-model="qrcodeData.enter_card_number" />
        </Form-item>
        <Form-item label="交纳押金" prop="pre_amount" :rules="{ required: true, message: '请输入金额!' }">
          <InputNumber
            :precision="2"
            :max="99999"
            :min="0"
            :step="0.1"
            :active-change="false"
            v-model="qrcodeData.pre_amount"
            :disabled="qrcodeData.ticket_type == 2"
          />
        </Form-item>
        <Form-item label="支付方式" prop="new_pay_type" v-if="qrcodeData.pre_amount" :rules="{ required: true, message: '请选择支付方式!' }">
          <pay-type-list
            v-model="qrcodeData.new_pay_type"
            :amount="Number(qrcodeData.pre_amount)"
            :sqbOption="{ describe: `票据 [${qrcodeData.consume_sn}]`, serviceType: 2 }"
            :userId="qrcodeData.user_id ? qrcodeData.user_id : -1"
            :showCardPay="!!qrcodeData.user_id"
             @onDragonflyChange="handleDragonflyChange"
          />
        </Form-item>
      </blockquote>

      <!-- 第三方渠道 -->
      <blockquote v-else>
        <Form-item
          label="凭证编号"
          prop="consume_sn"
          :rules=" { required: true, message: '请输入凭据编号!' }">
          <Input ref="qrcodeSn" v-model="qrcodeData.consume_sn" @on-change="handleCodeChange" autofocus />
          <div class="ticket-box" v-show="ticketInfo.group_title">
            <Card style="width:100%;margin-top:10px">
              <div class="ticket-card">
                <img class="ticket-big-img" :src="ticketList[ticketIndex].icon">
                <div class="ticket-info">
                  <div class="ticket-text">{{ ticketInfo.group_title }} <em class="ticket-tip">对应系统 （{{ ticketInfo.qn_title }}）</em></div>
                  <div class="ticket-text">{{ ticketInfo.start_time }} {{ticketInfo.start_time?'~':''}} {{ ticketInfo.end_time }}</div>
                  <div class="ticket-text">{{ ticketInfo.group_price }}元</div>
                </div>
              </div>
            </Card>
          </div>
          <div v-if="consumeSnError" class="ivu-form-item-error-tip">{{ consumeSnError }}</div>
        </Form-item>
        <Form-item
          label="手机号码"
          prop="phone">
          <!-- <Input v-model="qrcodeData.phone" /> -->
          <user-search
            :busId="$store.state.busId"
            :hasSelfPhone="true"
            url="/Web/SpaceOrder/searchUserList"
            v-model="userSearchId"
            @on-change="userSelectedByAdd"
            placeholder="姓名/电话"
          ></user-search>
        </Form-item>
        <Form-item
          label="手环号"
          prop="enter_card_number">
          <Input v-model="qrcodeData.enter_card_number" />
        </Form-item>
        <Form-item
          label="交纳押金"
          prop="pre_amount"
          :rules="{ required: true, message: '请输入金额!' }">
          <InputNumber
            :precision="2"
            :max="99999"
            :min="0"
            :step="0.1"
            :active-change="false"
            v-model="qrcodeData.pre_amount"
            :disabled="qrcodeData.ticket_type == 2"
          />
        </Form-item>
        <Form-item label="支付方式" prop="new_pay_type" v-if="qrcodeData.pre_amount" :rules="{ required: true, message: '请选择支付方式!' }">
          <pay-type-list
            v-model="qrcodeData.new_pay_type"
            :amount="Number(qrcodeData.pre_amount)"
            :sqbOption="{ describe: `票据 [${qrcodeData.consume_sn}]`, serviceType: 2 }"
            :userId="qrcodeData.user_id ? qrcodeData.user_id : -1"
            :showCardPay="!!qrcodeData.user_id"
            @onDragonflyChange="handleDragonflyChange"
          />
        </Form-item>
      </blockquote>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleQRCodeSave">保存</Button>
      <Button @click="handleQRCodeCancel">取消</Button>
    </div>
    <MeituanAuth v-model="showMeituanAuth" :meituanAuthInfo="meituanAuthInfo" />
  </Modal>
</template>
<script>
import { debounce } from 'lodash-es'
import PayTypeList from '@/components/form/PayTypeList.vue'
import rocketbirdIcon from '@/assets/img/ticket/rocketbird.svg'
import meituanIcon from '@/assets/img/ticket/meituan.svg'
import dianpingIcon from '@/assets/img/ticket/dianping.svg'
import tiktokIcon from '@/assets/img/ticket/tiktok.svg'
import userSearch from '@/components/user/userSearch.vue'
import MeituanAuth from './MeituanAuth.vue'

export default {
  name: 'VerificationNumber',
  components: {
    PayTypeList,
    MeituanAuth,
    userSearch
  },
  props: {
    value: {
      type: Boolean,
    },
    consumeSn: {
      type: String,
      default: ''
    },
    platformId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyEnterNum: '',
      consumeSnError: '',
      // entrance: '手环入场',
      qrcodeData: {
        bus_id: this.$store.state.busId,
        consume_sn: '',
        user_id: '',
        phone: '',
        consume_type: '',
        enter_card_number: '',
        new_pay_type: [],
        pre_amount: 0,
        ticket_type: '',
        // tiktok sale
        san_rule_id: '',
        buy_channel: ''
      },
      punchCardProcess: false,
      // tiktok sale
      ticketList: [
        {
          name: '会员端',
          platformId: '0',
          icon: rocketbirdIcon,
        },
        {
          name: '美团',
          platformId: '1',
          icon: meituanIcon,
        },
        {
          name: '大众点评',
          platformId: '2',
          icon: dianpingIcon,
        },
        {
          name: '抖音',
          platformId: '3',
          icon: tiktokIcon,
        }
      ],
      // validatePhoneOrCard,
      ticketIndex: 0,
      ticketInfo: {
        qn_title: '',
        group_title: '',
        group_price: '',
        start_time: '',
        end_time: '',
        san_rule_id: '',
      },
      userSearchId: '',
      userInfo: {
        user_id: '',
        username: '',
        phone: '',
      },
      showMeituanAuth: false,
      meituanAuthInfo: {},

    }
  },
  computed: {
    showQRCode: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
  watch: {
    showQRCode(val) {
      if (!val) {
        this.resetQRCode()
      } else {
        if (this.consumeSn) {
          this.qrcodeData.consume_type = this.platformId
          this.ticketIndex = this.ticketList.findIndex(item => item.platformId == this.platformId)
          this.qrcodeData.consume_sn = this.consumeSn
          this.handleCodeChange()
        }
      }
    },
  },
  methods: {
    handleDragonflyChange(val) {
      if(val) {
        document.removeEventListener('keydown', this.handleVerificationKeyDown)
      } else {
        document.addEventListener('keydown', this.handleVerificationKeyDown)
      }
    },
    resetQRCode() {
      this.ticketIndex = 0
      this.keyEnterNum = ''
      this.consumeSnError = ''
      this.userSearchId = ''
      this.qrcodeData = {
        bus_id: this.$store.state.busId,
        consume_sn: '',
        user_id: '',
        phone: '',
        consume_type: '',
        enter_card_number: '',
        new_pay_type: [],
        pre_amount: 0,
        ticket_type: '',
        san_rule_id: '',
        buy_channel: ''
      }
      this.$refs.qrcodeData.resetFields()
    },
    handleQRCodeCancel() {
      this.keyEnterNum = ''
      // this.entrance = '手环入场'
      this.showQRCode = false
      this.consumeSnError = ''
    },
    // 产品逻辑调整  新购和核销在新购票tab页面完成，结算等继续保留在核销记录tab页面
    handleVerificationKeyDown(e) {
      const event = window.event || e
      let key = event.key
      if (event.target.localName !== 'input' && event.target.localName !== 'textarea') {
        // 数字、链接中的字符或特殊符号（抖音扫码是链接）
        if (/^[0-9a-zA-Z\-_@./#]+$/.test(key)) {
          this.keyEnterNum += key
        } else if (key === 'Enter') {
          if (this.showQRCode) {
            // 会员的二维码
            if (this.ticketIndex === 0) {
              if (this.keyEnterNum.length === 12) {
                this.$refs.qrcodeSn.focus()
                this.qrcodeData.consume_sn = this.keyEnterNum
                this.handleSNChange()
              } else { // 手环号
                this.$refs.qrcodeHandId.focus()
                this.qrcodeData.enter_card_number = this.keyEnterNum
              }
            } else if (this.ticketIndex === 1 || this.ticketIndex === 2  || this.ticketIndex === 3) {
              this.$refs.qrcodeSn.focus()
              this.qrcodeData.consume_sn = this.keyEnterNum
              this.handleCodeChange()
            }
          } else {
            this.getPrevious()
          }
          this.keyEnterNum = ''
        }
      }
    },
    getPrevious() {
      if (this.punchCardProcess || this.keyEnterNum.length <= 5) {
        return false
      }
      this.punchCardProcess = true
      const { keyEnterNum } = this
      this.$service
        .post('/Web/San/scene', {
          sn: keyEnterNum,
          bus_id: this.$store.state.busId,
        })
        .then((res) => {
          if (res.data.errorcode !== 0) {
            this.keyEnterNum = ''
            this.punchCardProcess = false
            this.$Message.error(res.data.errormsg)
            return
          }
          this.resetQRCode()
          // status
          // 1:结算散客票
          // 2:散客票核销
          // 3:定场成员票自动核销
          // 4:结算订场票
          const resData = res.data.data
          if (resData.status !== 2) {
            this.keyEnterNum = ''
            this.punchCardProcess = false
            return
          }
          const { ticket_type } = resData
          this.qrcodeData = { ...this.qrcodeData, consume_sn: keyEnterNum, consume_type: 1, ticket_type }
          this.handleSNChange()
          this.showQRCode = true
          this.punchCardProcess = false
        })
        .catch(() => {
          this.punchCardProcess = false
        })
    },
    handleSNChange: debounce(function () {
      this.qrcodeData.consume_sn = this.qrcodeData.consume_sn.replace(/\s+/g, '')
      const consume_sn = this.qrcodeData.consume_sn
      if (!consume_sn) {
        this.consumeSnError = ''
        this.qrcodeData.ticket_type = ''
        return {
          status: true,
        }
      }
      this.$service
        .post('/Web/San/checkStatusByConsumesn', {
          consume_sn,
          bus_id: this.$store.state.busId,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.qrcodeData.ticket_type = resData.ticket_type
            this.qrcodeData.user_id = resData.user_id || ''
            this.qrcodeData.phone = resData.user_phone || ''
            this.qrcodeData.username = resData.user_name || ''
            this.consumeSnError = resData.status ? '' : resData.msg
          } else {
            this.$Message.error(res.data.errormsg)
            this.consumeSnError = res.data.errormsg
          }
        })
    }, 500),
    handleQRCodeSave() {
      this.$refs.qrcodeData.validate((valid) => {
        if (valid && !this.consumeSnError) {
          this.qrcodeData.new_pay_type = this.qrcodeData.pre_amount == 0 ? [] : this.qrcodeData.new_pay_type
          this.qrcodeData.san_rule_id = this.ticketInfo.san_rule_id
          this.qrcodeData.buy_channel = this.ticketIndex
          this.qrcodeData.user_id = this.userInfo.user_id === 'self_id' ? '' : this.userInfo.user_id
          this.qrcodeData.phone = this.userInfo.phone
          this.$service.post('/Web/San/entryVerification', this.qrcodeData).then((res) => {
            if (res.data.errorcode == 0) {
              this.resetQRCode()
              // this.entrance = '手环入场'
              this.showQRCode = false
              this.$emit('on-success', {
                logId: res.data.data.id,
                logUrl: res.data.data.ticket_type == 1 ? '/nonMemberPrint' : '/sanTicketPrint',
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    // tiktok sale
    async handleTicketClick(index) {
      this.$refs.qrcodeData.resetFields()
      this.qrcodeData.consume_sn = ''
      this.qrcodeData.user_id = ''
      this.qrcodeData.phone = ''
      this.qrcodeData.consume_type = ''
      this.qrcodeData.enter_card_number = ''
      this.qrcodeData.new_pay_type = []
      this.qrcodeData.pre_amount = 0
      this.qrcodeData.ticket_type = ''
      this.qrcodeData.san_rule_id = ''
      this.qrcodeData.buy_channel = ''
      this.ticketInfo.group_title = ''
      this.consumeSnError = ''
      this.ticketIndex = index
      this.userSearchId = ''
      // this.$refs.qrcodeSn.focus()
      const authority = await this.getSaleAuthority();
      if (!authority.is_auth && authority.url) {
        // window.open(authority.url, '_blank');
        this.meituanAuthInfo = {...authority, platform_id: this.ticketIndex}
        this.showMeituanAuth = true
      }
    },
    getSaleAuthority() {
      const defaultResponse = {
        is_auth: false,
        url: '',
      }
      // if tiktok, return default
      if (this.ticketIndex === 3) {
        return defaultResponse
      }
      return this.$service.get(`/Web/SanGroup/auth?redirect_url=${window.location.href}&platform_id=${this.ticketIndex}`).then((res) => {
        if (res.data.errorcode == 0) {
          return res.data.data
        } else {
          this.$Message.error(res.data.errormsg)
          return defaultResponse
        }
      }).catch(() => {
        return defaultResponse
      })
    },
    handleCodeChange: debounce(function () {
      // remove all spaces in the string
      this.qrcodeData.consume_sn = this.qrcodeData.consume_sn.replace(/\s+/g, '')
      // if (this.qrcodeData.consume_sn.length === 12) {
      if (this.qrcodeData.consume_sn.length >= 8) {
        this.$service.post('/Web/SanGroup/getVoucher', {
          platform_id: this.ticketIndex,
          code: this.qrcodeData.consume_sn
        }).then(async (res) => {
          if (res.data.errorcode == 0) {
            this.ticketInfo = res.data.data
            this.qrcodeData.san_rule_id = res.data.data.rule_id
            this.consumeSnError = ''
          } else if (res.data.errorcode == 401012) {
            const authority = await this.getSaleAuthority();
            if (!authority.is_auth && authority.url) {
              window.open(authority.url, '_blank');
            }
            this.qrcodeData.san_rule_id = ''
            this.consumeSnError = res.data.errormsg
          } else {
            this.qrcodeData.san_rule_id = ''
            this.consumeSnError = res.data.errormsg
          }
        })
      } else {
        this.consumeSnError = ''
      }
    }, 500),
    resetUser() {
      Object.assign(this.userInfo, {
        user_id: '',
        phone: '',
        username: '',
      })
    },
    userSelectedByAdd(userId, list) {
      if (userId && list.length) {
        const user = list.find((item) => item.user_id === userId)
        Object.assign(this.userInfo, user)
        this.qrcodeData.user_id = userId
        this.qrcodeData.new_pay_type = []
        this.qrcodeData.pre_amount = 0
      } else {
        this.qrcodeData.new_pay_type = []
        this.qrcodeData.pre_amount = 0
        this.resetUser()
      }
    }
  },
  mounted() {
    document.addEventListener('keydown', this.handleVerificationKeyDown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleVerificationKeyDown)
  },
  activated() {
    document.addEventListener('keydown', this.handleVerificationKeyDown)
    const cn = this.$route.query.card_number
    if (cn) {
      this.keyEnterNum = cn
      this.getPrevious()
    }
  },
  deactivated() {
    document.removeEventListener('keydown', this.handleVerificationKeyDown)
  },
}
</script>

<style lang="less" scoped>
.ticket-box {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;

  .ticket-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;

    .ticket-img {
      width: 40px;
      height: 40px;
    }

    .ticket-big-img {
      width: 80px;
      height: 80px;
    }

    .ticket-text {
      font-size: 14px;
      line-height: 24px;
      color: #333;
      margin-left: 10px;
      display: flex;
      align-items: center;
    }

    .ticket-tip {
      font-size: 12px;
      color: #f4a627;
      margin-left: 10px;
    }

    .ticket-info {
      margin-left: 10px;
    }
  }

  .ticket-card-active {
    font-weight: bold;
    border-color: #f4a627;
    box-shadow: 0 0 10px #f4a627;
  }
}
</style>
