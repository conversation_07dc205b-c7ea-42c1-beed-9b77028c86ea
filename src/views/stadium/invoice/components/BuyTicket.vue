<template>
  <div class="table-wrap ticket-wrap">
    <div class="ticket-side left">
       <div class="ticket-title">
        票列表
        <div class="ticket-qrcode" @click="handleMobileVerify">
          <span title="二维码" class="act-qrcode" />
          移动端核销
        </div>
      </div>
      <div class="ticket-content">
        <div class="ticket-search">
          <Input v-model="search" class="w200" placeholder="票名称" prefix="ios-search" />
          <Button type="warning" @click="handleQRCodeTicket">核销凭证</Button>
        </div>
        <div class="ticket-list">
          <div class="ticket-item" v-for="item in computedSearchList" :key="item.san_rule_id" @click="selectTicket(item)">
            <div class="ticket-name">{{ item.card_name }}</div>
            <div class="ticket-price">
              售价
              <span class="price">¥{{ item.base_fee }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Form
      ref="ticketForm"
      :model="postData"
      :label-width="100"
      class="ticket-side right"
    >
      <div class="ticket-title">支付结算</div>
      <Alert v-if="!postData.san_batch_list.length" type="warning">请先从左侧选择需要购买的票</Alert>
      <div class="ticket-content-wrap">
      <div class="ticket-content">
        <div class="ticket-select-list">
          <FormItem :label-width="0" v-for="(item, index) in postData.san_batch_list" :key="index" :prop="'san_batch_list.' + index" :rules="{ required: true, validator: validateBatchItem}">
            <div class="ticket-select-item">
              <div class="ticket-select-name">{{ item.card_name }}</div>
              <div class="item-input-wrap">
                <span class="mr5">票价</span>
                <InputNumber v-model="item.pre_amount" class="amount w120" :min="0" :max="99999999.99"/>
                <span>元</span>
              </div>
              <div class="item-input-wrap">
                <span class="mr5">手环号</span>
                <Input ref="numInputRefs" v-model="item.enter_card_number" class="number w120" @on-enter="handleNextFocus($event, index)" />
              </div>
              <div class="item-input-wrap">
                <span class="mr5">手机号</span>
                <user-search
                  class="w200"
                  v-if="index === 0"
                  :busId="busId"
                  :hasSelfPhone="true"
                  url="/Web/SpaceOrder/searchUserList"
                  v-model="userSearchId"
                  @on-change="userSelectedByAdd"
                  placeholder="姓名/电话"
                ></user-search>
                <Input class="w200" disabled :value="item.username" v-else />
              </div>
              <Icon
                type="ios-close-circle-outline"
                @click.native="deleteSelectItem(index)"
                size="24"
                color="#d9544f"
                style="cursor: pointer"
                title="删除"
              ></Icon>
            </div>

          </FormItem>
        </div>
      </div>
      <template v-if="postData.san_batch_list.length">
        <FormItem :label-width="0">
        <div class="price-info">
          <div class="price-label">票价应收</div>
          <span class="price bold mr15">{{ computedTicketAmount }}</span>
          元
        </div>
        </FormItem>
        <FormItem :label-width="0" prop="deposit_unit_price" :rules="{ pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额最多只能保留两位小数' }">
          <div class="price-info">
            <div class="price-label">每张票押金</div>
            <InputNumber class="w120 mr15" v-model="postData.deposit_unit_price" :min="0" :max="99999999.99" />
            元/张 合计押金
            <span class="price bold">{{ computedDepositAmount }}</span>
            元
          </div>
        </FormItem>
        <FormItem :label-width="0">
        <div class="price-info">
          <div class="price-label red">总计收取</div>
          <span class="total-amount mr15">{{ computedTotalAmount }}</span>
          元
        </div>
        </FormItem>
        <FormItem v-if="computedTotalAmount" class="price-info-pay" :label-width="0" prop="new_pay_type" :rules="{ required: true, message: '请选择支付方式!' }">
          <div class="price-label">支付方式</div>
          <pay-type-list
            v-model="postData.new_pay_type"
            :amount="computedTotalAmount"
            :showCardPay="!!userInfo.user_id"
            :userId="userInfo.user_id || -1"
            :sqbOption="{ isEqual: false, serviceType: 2, describe: '购票' }"
          />
        </FormItem>
        </template>
        </div>
        <div class="ticket-btn" v-if="postData.san_batch_list.length">
          <Button type="primary" @click="handleOnConfirm">支付入场</Button>
        </div>
    </Form>
    <verification-number v-model="showQRModal" :consumeSn="consumeSn" :platformId="platformId" @on-success="onVerificationSuccess" />
    <receipt-modal v-model="showReceipt" :id="logId" :url="logUrl" :isMultiPrint="isMultiPrint" />
    <MobileVerifyModal v-model="showMobileVerifyModal" />
  </div>
</template>
<script setup>
import { ref, reactive, computed, watch, provide, nextTick, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router/composables'
import { Modal, Message } from 'iview'
import service from 'src/service'
import userSearch from '@/components/user/userSearch.vue'
import PayTypeList from 'components/form/PayTypeList.vue'
import VerificationNumber from './VerificationNumber.vue'
import ReceiptModal from './ReceiptModal.vue'
import MobileVerifyModal from './MobileVerifyModal.vue'
import { useState } from 'vuex-composition-helpers'

const { busId } = useState(['busId'])

const postData = reactive({
  san_batch_list: [],
  new_pay_type: [],
  amount: 0,
  deposit_unit_price: 0,
})
const validateBatchItem = (rule, value, callback) => {
  const index  = Number(rule.field.split('.')[1])
  // if (!value.enter_card_number && !postData.user_id) {
  //   callback('手环号和用户不能同时为空')
  // }
  if(!validateAmount(value.pre_amount)){
    callback('金额最多保留两位小数')
  }  else if (value.enter_card_number && checkDuplicateNumber(index, value.enter_card_number)) {
    callback('手环号不可重复')
  } else {
    callback()
  }
}
function validateAmount(amount) {
  const match = /^(?!0\d)(\d+|\d*\.\d{1,2})$/.test(amount)
  return match
}
function checkDuplicateNumber(index, number) {
  return postData.san_batch_list.findIndex((obj2, index2) => {
    return index2 !== index && number === obj2.enter_card_number
  }) !== -1
}

const isMultiPrint = ref(false)
const showReceipt = ref(false)
const logId = ref('')
const logUrl = ref('/sanTicketPrint')
const ticketList = ref([])
const userSearchId = ref('')
const search = ref('')
const computedSearchList = computed(() => {
  return ticketList.value.filter((item) => {
    return item.card_name.includes(search.value.trim())
  })
})
//票价应收
const computedTicketAmount = computed(() => {
  return Math.round((postData.san_batch_list.reduce((a, b) => a + b.pre_amount, 0)) * 100) / 100
})
// 合计押金
const computedDepositAmount = computed(() => {
  return (postData.deposit_unit_price * postData.san_batch_list.length).toFixed(2)
})
// 总计
const computedTotalAmount = computed(() => {
  return Math.round((computedTicketAmount.value + Number(computedDepositAmount.value)) * 100) / 100
})
// 总计金额变化，支付方式重置
watch(computedTotalAmount, () => {
  postData.new_pay_type = []
})

const numInputRefs = ref()
function selectTicket(item) {
  if (postData.san_batch_list.length >= 10) {
    Message.error('最多只能添加10个')
    return
  }
  postData.san_batch_list.push({
    card_name: item.card_name,
    san_rule_id: item.san_rule_id,
    pre_amount: Number(item.base_fee) || 0,
    user_id: userInfo.user_id,
    phone: userInfo.phone,
    username: userInfo.username,
    enter_card_number: '',
  })

  nextTick(() => {
    // 聚焦第一个手环号输入框
    if (numInputRefs.value && numInputRefs.value[0]) {
      numInputRefs.value[0].focus()
    }
    /*
      需求为第一次点击左侧列表票，默认选中上一次提交的单支付方式
      因为watch(computedTotalAmount会重置支付方式，所以放在nextTick再执行
    */
    if (postData.san_batch_list.length === 1) {
      const payTypeItem = localStorage.getItem('TICKET_NEW_PAY_TYPE')
      if (payTypeItem && !postData.new_pay_type.length) {
        postData.new_pay_type.push({
          ...JSON.parse(payTypeItem),
          amount: Number(item.base_fee) || 0
        })
      }
    }
  })
}
// 回车聚焦下一个手环号输入框
const handleNextFocus = (e, index) => {
  if (numInputRefs.value) {
    const inputRef = numInputRefs.value[index + 1]
    if (inputRef) {
      inputRef.focus()
    }
  }
}

function deleteSelectItem(index) {
  if(index === 0) {
    resetUser()
  }
  postData.san_batch_list.splice(index, 1)
}

const userInfo = reactive({
  user_id: '',
  username: '',
  phone: '',
})
provide(
  'sqbPhoneInfo',
  computed(() => ({
    phone: userInfo.phone,
    old_bus_id: busId.value
  }))
)
// 用户变化，支付方式重置
watch(() => userInfo.user_id, () => {
  postData.new_pay_type = []
})

function userSelectedByAdd(userId, list) {
  if (userId && list.length) {
    const user = list.find((item) => item.user_id === userId)
    Object.assign(userInfo, user)
  } else {
    resetUser()
  }
}
function resetUser() {
  Object.assign(userInfo, {
    user_id: '',
    phone: '',
    username: '',
  })
  userSearchId.value = ''
}

watch(userInfo, (value) => {
  if (!value.user_id) {
    postData.new_pay_type = []
  }
  postData.san_batch_list = postData.san_batch_list.map((item) => {
    return Object.assign(item, value)
  })
})

const checkUserLimitByType = (params) => {
  return service.post('/Web/UserBlacklist/checkUser', params,
  { headers: { 'Content-Type': 'application/json' } }).then(res => {
    return res.data.data
  })
}
const handleOnConfirm = async () => {
  const flag = await checkUserLimitByType({
    user_id: userInfo.user_id,
    bus_id: busId.value,
    member_rule: 5,
    loading: true
  })
  if(flag) {
    Modal.confirm({
      title: '确认购票?',
      content: '此会员已在门店黑名单中',
      okText: '仍要购买',
      onOk: () => {
        onConfirm()
      },
    });
  } else {
    onConfirm()
  }
}

const ticketForm = ref()
function onConfirm() {
  ticketForm.value.validate(valid => {
    if (!valid) {
      return Message.error('请正确填写信息')
    }
    const san_batch_list = postData.san_batch_list.map(v => ({ ...v }))
    if (san_batch_list.length === 1 && san_batch_list[0].user_id === 'self_id') {
      san_batch_list[0].user_id = ''
    }
    service
      .post('/Web/San/buyBatch', {
        new_pay_type: postData.new_pay_type,
        amount: computedTotalAmount.value,
        san_batch_list,
        deposit_unit_price: postData.deposit_unit_price,
      })
      .then((res) => {
        if (res.data.errorcode === 0) {
          // 如果该支付方式为单次并且不是收钱吧、杉德、储值卡，则缓存，下回首次点击票时默认选中
          if (postData.new_pay_type.length === 1 && ![8, 20, 21].includes(+postData.new_pay_type[0].pay_type)) {
            localStorage.setItem('TICKET_NEW_PAY_TYPE', JSON.stringify(postData.new_pay_type[0]))
          }

          const resDate = res.data.data
          postData.san_batch_list = []
          resetUser()
          logId.value = resDate.san_log_id
          logUrl.value = '/sanTicketPrint'
          isMultiPrint.value = true
          showReceipt.value = true
          Message.success('购买成功')
        } else {
          Message.error(res.data.errormsg)
        }
      })
  })
}
function getTicketList() {
  return service.post('/Web/San/getSanCard').then((res) => {
    if (res.data.errorcode === 0) {
      ticketList.value = res.data.data
    } else {
      Message.error(res.data.errormsg)
    }
  })
}
getTicketList()

// 凭证核销
const showQRModal = ref(false)

function handleQRCodeTicket() {
  showQRModal.value = true
}
function onVerificationSuccess(info) {
  logId.value = info.logId
  logUrl.value = info.logUrl
  isMultiPrint.value = false
  showReceipt.value = true
}
// 从团购商品核销填入散场票后跳转过来
const consumeSn = ref('')
const platformId = ref('')
const route = useRoute();
const router = useRouter();
onMounted(() => {
  consumeSn.value = route.query.consume_sn || ''
  platformId.value = route.query.platform_id || ''
  if (consumeSn.value && platformId.value) {
    handleQRCodeTicket()
    // 清空route.query 防止刷新页面后再次弹窗
    router.replace({ query: {} });
  }
})

// 移动端核销
const showMobileVerifyModal = ref(false)
function handleMobileVerify() {
  showMobileVerifyModal.value = true
}
</script>
<style lang="less" scoped>
.ticket-wrap {
  display: flex;
  box-sizing: border-box;
  justify-content: space-between;
  padding: 20px;
  height: 100%;
}

.ticket-side {
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  &.left {
    width: 33%;
    height: 100%;
    background: #f1f3f7;
    flex-shrink: 0;
  }

  &.right {
    position: relative;
    border: 1px solid #e1e3e9;
    flex: 1;
    margin-left: 20px;
    overflow: hidden;
  }
}
.act-qrcode {
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  width: 17px;
  height: 17px;
  background: url('~assets/img/qrcode.png') no-repeat;
  background-size: contain;
}
.ticket-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: bold;
  color: #2c3945;
  line-height: 36px;
  padding-left: 15px;

  &::before {
    position: absolute;
    left: 5px;
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #2b8df2;
    margin-right: 10px;
    margin-bottom: 2px;
  }
  .ticket-qrcode {
    cursor: pointer;
    font-size: 14px;
    cursor: pointer;
    color: #2b8df2;
  }
}
.ticket-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.ticket-search {
  display: flex;
  justify-content: space-between;
  padding: 20px 0;
}

.ticket-list {
  display: flex;
  flex-wrap: wrap;
  .ticket-item {
    position: relative;
    width: calc(33.3% - 8px);
    height: 100px;
    background: #fff;
    border: 1px solid #e1e3e9;
    margin-bottom: 15px;
    border-radius: 6px;
    box-sizing: border-box;
    padding: 10px;
    cursor: pointer;
    margin-right: 12px;
    overflow: hidden;

    &:nth-child(3n) {
      margin-right: 0;
    }
  }

  .ticket-name {
    font-size: 14px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .ticket-price {
    position: absolute;
    right: 10px;
    bottom: 10px;
    text-align: right;
    font-size: 12px;
    color: #18bf6a;
    line-height: 24px;
    margin-top: 10px;

    .price {
      font-size: 18px;
    }
  }
}
.ticket-select-list {
  margin-top: 20px;
}
.ticket-select-item {
  display: flex;
  align-items: center;
  .ticket-select-name {
    width: 150px;
    height: 40px;
    background: #ebfaef;
    border-radius: 4px;
    line-height: 40px;
    text-align: center;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-input-wrap {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
}
.price-info-pay {
  margin-bottom: 15px;
  .label {
    margin-bottom: 15px;
  }
}
.mr15 {
  margin-right: 15px;
}
.price-info {
  display: flex;
  align-items: center;
  .price-label {
    width: 100px;
  }
  .red {
    color: #f56c6c;
  }
  .bold {
    font-weight: bold;
  }
  .total-amount {
    font-size: 20px;
  }
}
.ticket-content-wrap {
  height: calc(100% - 60px);
  overflow-y: scroll;
}
.ticket-btn {
  width: 100%;
  padding: 10px 0;
  position: absolute;
  display: flex;
  justify-content: center;
  bottom: 0;
  right: 0;
  button {
    width: 210px;
  }
}
</style>
