<template>
  <Modal v-model="showModal" title="移动端核销" footer-hide>
    <div class="qrcode-box">
     <qrcode-vue :value="codeString" :size="size" level="H" />
     <div class="tips">微信扫码登录使用移动端核销</div>
    </div>
  </Modal>
</template>
<script setup>
import { ref, defineProps, computed, defineEmits } from 'vue';
import QrcodeVue from 'qrcode.vue'


const emit = defineEmits(['input'])
const props = defineProps({
  value: {
    type: Boolean,
  },
})
const codeString = ref(`${window.location.origin}/mobile/verify`)
const size = ref(300)
const showModal = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('input', val)
  },
})
</script>
<style lang="less" scoped>
.qrcode-box {
  text-align: center;
}
.tips {
  font-size: 14px;
  padding: 10px 0;
}
</style>