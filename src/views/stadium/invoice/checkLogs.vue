<template>
  <div class="box">
    <Row class="box-head">
      <Col offset="1" span="22" class="head-option">
        <Input style="width: 180px" v-model="postData.enter_card_number_or_phone" class="option-select" placeholder="手环号/电话" />
        <Input style="width: 180px" v-model="postData.consume_sn" class="option-select" placeholder="我的凭证" />
        <Select v-model="postData.buy_channel" class="option-select" placeholder="全部渠道">
          <Option value="">全部渠道</Option>
          <Option value="1">前台</Option>
          <Option value="2">会员端</Option>
          <Option value="3">票务机-刷掌购票</Option>
          <Option value="4">票务机-手机号购票</Option>
          <Option value="5">美团</Option>
          <Option value="6">大众点评</Option>
          <Option value="7">抖音</Option>
        </Select>
        <DatePicker
          v-model="duringDate"
          @on-change="handleDateChange"
          type="daterange"
          placement="bottom-end"
          placeholder="入场时间"
          class="option-select"
          style="width: 220px"
          :clearable="false"
        ></DatePicker>
        <Select v-model="postData.is_end" class="option-select" placeholder="结算状态">
          <Option value="0">结算状态</Option>
          <Option value="2">已结算</Option>
          <Option value="1">未结算</Option>
        </Select>
        <Button type="success" @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
        <Table ref="table" class="avatar-zoom" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
        <div class="option-ctrl">
          <Button style="margin-right: 20px" @click="handleExport">导出 Excel</Button>
        </div>
        <Page
          @on-change="handlePage"
          :total="total"
          :current="postData.page_no"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </Col>
    </Row>


    <Modal v-model="showEdit" :mask-closable="false" title="编辑" width="800">
      <Form ref="editData" :model="editData" class="modal-form" :rules="editRules" :label-width="80">
        <Form-item label="票据" prop="cardName">
          <span>{{ editData.cardName }}</span>
        </Form-item>
        <Form-item v-if="editData.phone" label="手机号" prop="phone">
          <span>{{ editData.phone }}</span>
        </Form-item>
        <Form-item label="手环号" prop="handId">
          <span>{{ editData.handId }}</span>
        </Form-item>
        <Form-item label="交纳押金" prop="deposit">
          <InputNumber
            :precision="2"
            :max="99999"
            :min="0"
            :active-change="false"
            :step="0.1"
            v-model="editData.deposit"
          />
        </Form-item>
        <Form-item v-if="editData.deposit != 0" label="支付方式" prop="new_pay_type">
          <pay-type-list
            v-model="editData.new_pay_type"
            placeholder="请选择"
            :sqbOption="{ describe: `票据 [${editData.cardName}]`, serviceType: 2 }"
            :amount="editData.deposit"
            :userId="editData.user_id || -1"
          />
        </Form-item>
        <Form-item v-if="editData.status == 4" label="运动时长" prop="exercise">
          <span>{{ editData.exercise }}</span>
        </Form-item>
        <Form-item v-if="editData.status == 4" label="购票金额" prop="baseFee">
          <span>{{ editData.baseFee }}</span>
        </Form-item>
        <Form-item v-if="editData.status == 4" label="超时费用" prop="overFee">
          <span>{{ editData.overFee }}</span>
        </Form-item>
        <Form-item v-if="editData.status == 4 && editData.isOvertime == 2" label="应补" prop="repair">
          <InputNumber
            :precision="2"
            :max="99999"
            :min="0"
            :step="0.1"
            v-model="editData.repair"
          />
        </Form-item>
        <Form-item v-else-if="editData.status == 4 && editData.isOvertime == 1" label="应退" prop="refund">
          <InputNumber
            :precision="2"
            :max="99999"
            :min="0"
            :step="0.1"
            :active-change="false"
            v-model="editData.refund" />
        </Form-item>
        <Form-item
          v-if="editData.status == 4 && editData.isOvertime == 2 && editData.repair != 0"
          label="支付方式"
          prop="new_repre_type"
        >
          <pay-type-list
            v-model="editData.new_repre_type"
            placeholder="请选择"
            :sqbOption="{ describe: `票据 [${editData.cardName}]`, serviceType: 2 }"
            :amount="editData.repair"
            :userId="editData.user_id || -1"
          />
        </Form-item>
        <Form-item
          v-else-if="editData.status == 4 && editData.isOvertime == 1 && editData.refund != 0"
          label="退款方式"
          prop="new_refund_type"
        >
          <pay-type-list
            v-model="editData.new_refund_type"
            :amount="editData.refund"
            :userId="editData.user_id || -1"
            isRefund
            isRefundNeedSQB />
        </Form-item>
        <Form-item label="编辑原因" prop="remark">
          <Input v-model="editData.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入..."></Input>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleNonMemberEditClick">保存</Button>
        <Button @click="showEdit = false">取消</Button>
      </div>
    </Modal>

    <Modal v-model="showCalc" :mask-closable="false" title="结算" width="800">
      <Form ref="calcData" :model="calcData" class="modal-form" :rules="calcRules" :label-width="80">
        <Form-item label="手环号" prop="handId">
          <span>{{ calcData.handId }}</span>
        </Form-item>
        <Form-item v-if="calcData.phone" label="手机号" prop="phone">
          <span>{{ calcData.phone }}</span>
        </Form-item>
        <Form-item label="交纳押金" prop="deposit">
          <span>{{ calcData.deposit }}</span>
        </Form-item>
        <Form-item label="运动时长" prop="exercise">
          <span>{{ calcData.exercise }}</span>
        </Form-item>
        <Form-item v-if="calcData.buy_channel==1" label="购票金额" prop="baseFee">
          <span>{{ calcData.baseFee }}</span>
        </Form-item>
        <Form-item label="超时费用" prop="overFee">
          <span>{{ calcData.overFee }}</span>
        </Form-item>
        <Form-item v-if="calcData.isOvertime == 2" label="应补" prop="repair">
          <InputNumber :precision="2" :active-change="false" :max="99999" :min="0" :step="0.1" v-model="calcData.repair" />
        </Form-item>
        <Form-item v-else label="应退" prop="refund">
          <InputNumber
            :precision="2"
            :active-change="false"
            :min="0"
            :step="0.1"
            v-model="calcData.refund"
          />
        </Form-item>
        <Form-item v-if="calcData.isOvertime == 2 && parseFloat(calcData.repair) != 0" label="支付方式" prop="new_repre_type">
          <pay-type-list
            v-model="calcData.new_repre_type"
            :sqbOption="{ describe: '结算补交', serviceType: 2 }"
            :amount="calcData.repair"
            :userId="calcData.user_id || -1"
          />
        </Form-item>
        <Form-item v-else-if="calcData.isOvertime == 1 && parseFloat(calcData.refund) != 0" label="退款方式" prop="new_refund_type">
          <pay-type-list
            :userId="calcData.user_id || -1"
            :amount="Number(calcData.refund)"
            v-model="calcData.new_refund_type"
            :sqbOption="{ isEqual: true, serviceType: 2, describe: '结算退款' }"
            :isRefundNeedSQB="true"
            isRefund/>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handlePayBillSaveClick">保存</Button>
        <Button @click="showCalc = false">取消</Button>
      </div>
    </Modal>

    <ReceiptModal v-model="showReceipt" :url="logUrl" :id="logId" />
    <Modal v-model="showRepair" width="380">
      <div class="repair-header">
        <Icon type="ios-information-circle" color="orange" size="32" />
        <h5>消费超支{{ this.calcData.repair }}，是否补收?</h5>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleRepairConfirm">收取</Button>
        <Button @click="handleRepairCancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import PayTypeList from '@/components/form/PayTypeList.vue'
import ReceiptModal from './components/ReceiptModal.vue'
export default {
  name: 'CheckLogs',
  components: {
    PayTypeList,
    ReceiptModal
  },
  data() {
    const validateRefund = (rule, value, callback) => {
      const info = this.showCalc ? this.calcData : this.editData
      if (info.isOvertime == 1) {
        if (value > (Number(info.deposit) + Number(info.baseFee || 0))) {
          callback('退款金额不能大于交纳金额加购票金额！')
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const validateRefundAmount = (rule, value, callback) => {
      const info = this.showCalc ? this.calcData : this.editData
      if (info.isOvertime == 1) {
        if (!(value && value.length)) {
          callback('请选择退款方式');
        } else if(value.reduce((sum, v) => sum + Number(v.amount), 0).toFixed(2) !== Number(info.refund).toFixed(2)) {
          callback('实退与应退应该相同');
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const showDate = (datetime) => {
      if (!datetime || datetime == 0) {
        return ''
      } else {
        return formatDate(new Date(Number(datetime) * 1000), 'yyyy-MM-dd HH:mm')
      }
    }
    return {
      punchCardProcess: false,
      keyEnterNum: '',
      showReceipt: false,
      logId: '',
      logUrl: '',
      isEnd: '',
      showEdit: false,
      showCalc: false,
      editData: {
        ruleId: '',
        cardName: '',
        phone: '',
        handId: '',
        deposit: 0,
        new_pay_type: [],
        exercise: '',
        baseFee: '',
        overFee: '',
        refund: 0,
        new_repre_type: [],
        new_refund_type: [],
        user_id: '',
        remark: '',
        repair: 0,
        isOvertime: 0
      },
      calcData: {
        id: '',
        handId: '',
        deposit: 0,
        exercise: '',
        baseFee: '',
        overFee: '',
        refund: 0,
        repair: 0,
        user_id: '',
        new_refund_type: [],
        new_repre_type: [],
        isOvertime: 0,
        phone: ''
      },
      editRules: {
        refund: [{ required: true, validator: validateRefund }],
        new_pay_type: [{ required: true, message: '请选择支付方式!' }],
        new_repre_type: [{ required: true, message: '请选择补收支付方式!' }],
        new_refund_type: [{ required: true, validator: validateRefundAmount }],
        remark: [{ required: true, message: '请输入编辑原因!', trigger: 'blur' }]
      },
      calcRules: {
        refund: [{ required: true, validator: validateRefund }],
        repair: [{ required: true, message: '请输入!'}],
        new_repre_type: [{ required: true, message: '请选择支付方式!' }],
        new_refund_type: [{ required: true, validator: validateRefundAmount }]
      },
      total: 0,
      duringDate: [],
      columns: [
        { title: '我的凭证', key: 'consume_sn', width: 200, render: (h, params) => {
          if (['5', '6', '7', 5, 6, 7].includes(params.row.buy_channel)) {
            return h('div', {
              attrs: {
                title: `${params.row.buy_channel_copy}凭证: ${params.row.channel_code}`
              },
              style: {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }
            }, [
              h('span', params.row.consume_sn),
              h('Icon', {
                props: {
                  type: 'ios-alert',
                  size: '15',
                  color: '#f5a627',
                },
                style: {
                  marginLeft: '5px',
                }
              }),
            ])
          } else {
            return h('span', params.row.consume_sn)
          }
        }},
        { title: '手环号/电话', key: 'enter_card_number',
          render: (h, params) => {
            if (params.row.enter_card_number) {
              return h('span', params.row.enter_card_number)
            } else {
              return h('span', params.row.san_phone)
            }
          }
        },
        { title: '票', key: 'san_name' },
        {
          title: '购票渠道',
          key: 'buy_channel_copy',
        },
        { title: '核销方式', key: 'consume_type_copy' },
        {
          title: '入场时间',
          width: 150,
          key: 'sign_in_time',
          render: (h, params) => {
            return <div>{showDate(params.row.sign_in_time)}</div>
          }
        },
        {
          title: '离场时间',
          width: 150,
          key: 'sign_out_time',
          render: (h, params) => {
            return <div>{showDate(params.row.sign_out_time)}</div>
          }
        },
        { title: '在场时长', key: 'play_duration' },
        { title: '购票金额', key: 'buy_san_amount' },
        { title: '交纳押金', key: 'pre_amount' },
        {
          title: '消费金额',
          key: 'use_amount',
          renderHeader(h, params) {
            return <div>
              消费金额
              <Tooltip content="购票金额+超时费用" max-width="200" transfer>
                <icon size="16" type="ios-help-circle" color="#f4a627" class="icon-tips" />
              </Tooltip>
            </div>
          },
        },
        { title: '退款金额', key: 'refund_amount' },
        { title: '补交金额', key: 'repre_amount' },
        {
          title: '操作',
          key: 'option',
          width: 250,
          render: (h, params) => {
            let calcLink = ''
            let cancelLink = ''
            let updateLink = ''
            if (params.row.ticket_type == 1) {
              calcLink = (
                <a
                  onClick={() => {
                    this.calcData.ticket_type = params.row.ticket_type
                    this.getInfo(params.row.id)
                  }}
                  disabled={Number(params.row.end_time) > 0}
                >
                  结算
                </a>
              )
              updateLink = (
                <a
                  onClick={() => {
                    this.getSan(params.row.id)
                  }}
                >
                  编辑
                </a>
              )
            } else {
              calcLink = (
                <a
                  onClick={() => {
                    // this.getInfo(params.row.id)
                    this.calcData = {
                      id: params.row.id,
                      handId: '',
                      deposit: 0,
                      exercise: '',
                      baseFee: '',
                      overFee: '',
                      refund: 0,
                      repair: 0,
                      user_id: '',
                      new_repre_type: [],
                      new_refund_type: [],
                      isOvertime: 2,
                      ticket_type: params.row.ticket_type
                    }
                    const self = this
                    this.$Modal.confirm({
                      title: '结算',
                      content: `结算后将自动归还手环！`,
                      onOk() {
                        self.postStopCard()
                      }
                    })
                  }}
                  disabled={Number(params.row.end_time) > 0}
                >
                  结算
                </a>
              )
            }
            // 前台购散场票不显示，其余的都要显示
            if (params.row.buy_channel == 1 && params.row.ticket_type == 1) {
              cancelLink = ''
            } else if (Number(params.row.end_time) > 0) {
              cancelLink = (<a disabled>取消核验</a>)
            // } else if (Number(params.row.sign_out_time) > 0) {
            //   cancelLink = (<a disabled>取消核验</a>)
            } else {
              cancelLink = (
                <a
                  onClick={() => {
                    this.handleCancelQRCodeTicket(params.row.consume_sn)
                  }}
                >
                  取消核验
                </a>
              )
            }

            let label = ''
            if (params.row.ticket_type == 1) {
              if (params.row.end_time == 0) {
                label = '打印凭证'
              } else {
                label = '打小票'
              }
            } else {
              label = '打印凭证'
            }
            return (
              <div style="display: flex;flex-direction: row;justify-content: space-around;">
                {calcLink}
                {cancelLink}
                {updateLink}
                <a
                  onClick={() => {
                    let routeUrl = null
                    if (params.row.ticket_type == 1) {
                      if (params.row.end_time == 0) {
                        routeUrl = this.$router.resolve({ path: '/sanTicketPrint', query: { logId: params.row.id } })
                      } else {
                        routeUrl = this.$router.resolve({ path: '/nonMemberPrint', query: { logId: params.row.id } })
                      }
                    } else {
                      routeUrl = this.$router.resolve({ path: '/sanTicketPrint', query: { logId: params.row.id } })
                    }
                    window.open(routeUrl.href, '_blank')
                  }}
                >
                  { label }
                </a>
              </div>
            )
          }
        }
      ],
      list: [],
      postData: {
        page_no: 1,
        page_size: 10,
        is_export: 0,
        bus_id: this.$store.state.busId,
        enter_card_number_or_phone: '',
        consume_sn: '',
        start_time: '',
        end_time: '',
        buy_channel: '',
        is_end: 0,
        status: 2,
        order_sign_in_time: 'desc'
      },
      showRepair: false,
    }
  },
  methods: {
    handleRepairConfirm() {
      this.calcData.new_repre_type = []
      this.calcData.isOvertime = 2
      this.showCalc = true
      this.showRepair = false
    },
    handleRepairCancel() {
      this.calcData.new_repre_type = []
      this.calcData.isOvertime = 1
      this.showCalc = true
      this.showRepair = false
    },
    handleKeyDown(e) {
      const event = window.event || e
      let key = event.key
      if (event.target.localName !== 'input' && event.target.localName !== 'textarea') {
        if (/^[0-9]*$/.test(key)) {
          this.keyEnterNum += key
        } else if (key === 'Enter') {
          this.getPrevious()
        }
      }
    },
     // 产品逻辑调整  新购和核销在新购票tab页面完成，结算等继续保留在当前页面
    getPrevious() {
      if (this.punchCardProcess) {
        return false
      } else {
        this.punchCardProcess = true
      }

      if (this.keyEnterNum.length > 5) {
        return this.$service
          .post('/Web/San/scene', {
            // card_number: this.keyEnterNum
            sn: this.keyEnterNum,
            bus_id: this.$store.state.busId
          })
          .then(res => {
            const cardNumber = this.keyEnterNum
            if (res.data.errorcode == 0) {
              const scene = res.data.data
              // status
              // 1:结算散客票
              // 2:散客票核销
              // 3:定场成员票自动核销
              // 4:结算订场票
               if (scene.status == 1) {
                // 结算
                this.showEdit = false
                this.$refs.calcData.resetFields()
                this.getInfo(scene.id)
                this.calcData.handId = cardNumber
                this.calcData.ticket_type = scene.ticket_type
              }  else if (scene.status == 4) {
                this.calcData = {
                  id: scene.id,
                  handId: '',
                  deposit: 0,
                  exercise: '',
                  baseFee: '',
                  overFee: '',
                  refund: 0,
                  repair: 0,
                  new_refund_type: [],
                  new_repre_type: [],
                  isOvertime: 2,
                  ticket_type: scene.ticket_type
                }
                const self = this
                this.$Modal.confirm({
                  title: '结算',
                  content: `结算后将自动归还手环！`,
                  onOk() {
                    self.postStopCard()
                  }
                })
              }
              this.keyEnterNum = ''
              this.punchCardProcess = false
            } else {
              this.keyEnterNum = ''
              this.punchCardProcess = false
              this.$Message.error(res.data.errormsg)
            }
          })
      } else {
        this.showCalc = false
        this.showEdit = false
        this.punchCardProcess = false
      }
    },
    handleDateChange(val) {
      this.duringDate = val
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    handleNonMemberEditClick() {
      this.$refs['editData'].validate(valid => {
        if (valid) {
          let postData = {
            id: this.editData.ruleId,
            pre_amount: this.editData.deposit,
            new_pay_type: this.editData.new_pay_type,
            reason: this.editData.remark,
            pay_status: this.editData.isOvertime,
            bus_id: this.$store.state.busId
          }
          if (this.editData.isOvertime == 2) {
            postData.repre_amount = this.editData.repair
            postData.new_repre_type = this.editData.new_repre_type
          } else if (this.editData.isOvertime == 1) {
            postData.refund_amount = this.editData.refund
            postData.new_refund_type = this.editData.new_refund_type
          }
          this.$service.post('/Web/San/editPost', postData).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.info(res.data.errormsg)
              this.showEdit = false
              this.editData = {
                ruleId: '',
                cardName: '',
                handId: '',
                deposit: 0,
                new_pay_type: [],
                exercise: '',
                baseFee: '',
                overFee: '',
                refund: 0,
                repair: 0,
                new_refund_type: [],
                new_repre_type: [],
                repairType: 0,
                remark: '',
                isOvertime: 0
              }
                this.$refs['editData'].resetFields()
              this.getList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    postStopCard() {
      let postData = {
        san_log_id: this.calcData.id,
        pay_status: this.calcData.isOvertime
      }
      if (this.calcData.isOvertime == 2) {
        postData.repre_amount = this.calcData.repair
        postData.new_repre_type = this.calcData.new_repre_type
      } else if (this.calcData.isOvertime == 1) {
        postData.refund_amount = this.calcData.refund
        postData.new_refund_type = this.calcData.refund ? this.calcData.new_refund_type :[]
      }
      this.$service.post('/Web/San/stopCard', postData).then(res => {
        if (res.data.errorcode == 0) {
          // this.$Message.info(res.data.errormsg);
          this.showCalc = false

          // 票务核销--订场票结算后，不提示打印
          // https://zentao.rocketbird.cn/index.php?m=bug&f=view&bugID=15875
          if (this.calcData.ticket_type == 1) {
            this.showReceipt = true
            this.logId = this.calcData.id
            this.logUrl = '/nonMemberPrint'
          }

          this.calcData = {
            id: '',
            handId: '',
            deposit: 0,
            exercise: '',
            baseFee: '',
            overFee: '',
            refund: 0,
            repair: 0,
            new_refund_type: [],
            new_repre_type: [],
            isOvertime: 0
          }

          this.$refs['calcData'].resetFields()

          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handlePayBillSaveClick() {
      this.$refs['calcData'].validate(valid => {
        if (valid) {
          this.postStopCard()
        }
      })
    },
    handlePage(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList() {
      if (Array.isArray(this.duringDate) && this.duringDate.length === 2) {
        this.postData.start_time = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        this.postData.end_time = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }
      return this.$service.post('/Web/San/getSanList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.total = parseInt(res.data.data.count)
          this.list = []
          if (Array.isArray(res.data.data.list)) {
            this.list = res.data.data.list
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    showDate(datetime) {
      if (!datetime || datetime == 0) {
        return ''
      } else {
        return formatDate(new Date(Number(datetime) * 1000), 'yyyy-MM-dd HH:mm')
      }
    },
    handleExport() {
      if (Array.isArray(this.duringDate) && this.duringDate.length === 2) {
        this.postData.start_time = formatDate(this.duringDate[0], 'yyyy-MM-dd')
        this.postData.end_time = formatDate(this.duringDate[1], 'yyyy-MM-dd')
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }
      const copySearch = { ...this.postData, page_no: 1, page_size: this.total, is_export: 1 }

      // this.$service.post('/Web/San/getSanList', copySearch).then(res => {
      this.$service.post('/Web/San/exportListByUse', copySearch).then(res => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
            list.forEach(item => {
              item.consume_sn = '\u0009' + item.consume_sn
              // item.enter_card_number = '\u0009' + item.enter_card_number
              {/* item.buy_channel = item.buy_channel == 1 ? '前台' : item.buy_channel == 2 ? '会员端' : item.buy_channel == 3 ? '票务机-刷掌购票' : '票务机-手机号购票' */}
              item.sign_in_time = '\u0009' + this.showDate(item.sign_in_time)
              item.sign_out_time = '\u0009' + this.showDate(item.sign_out_time)
              if (item.enter_card_number) {
                item.enter_card_number = '\u0009' + item.enter_card_number
              } else {
                item.enter_card_number = '\u0009' + item.san_phone
              }
              // tiktok sale
              if (item.channel_code) {
                item.channel_code = '\u0009' + item.channel_code
              }
            })
          }
          const len = this.columns.length
          // tiktok sale
          let columns = []
          // copy from this.columns
          columns = this.columns.slice(0, len - 1)
          // add a new field within index 1
          columns.splice(1, 0, {
            title: '原始凭证',
            key: 'channel_code',
          })
          this.$refs.table.exportCsv({
            filename: `票务核销-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns,
            data: list
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getInfo(id) {
      return this.$service
        .post('/Web/San/getStopInfo', {
          san_log_id: id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.calcData.id = id
            this.calcData.handId = resData.enter_card_number
            this.calcData.deposit = resData.pre_amount
            this.calcData.exercise = resData.run_time_desc
            this.calcData.baseFee = resData.base_use_amount
            this.calcData.overFee = resData.base_over_amount
            this.calcData.refund = resData.refund_amount
            this.calcData.repair = resData.repre_amount
            this.calcData.isOvertime = resData.pay_status
            // 结算需要退款的时候 后端会返回默认需要填充的退款方式
            this.calcData.new_refund_type = resData.new_pay_type 
            // 结算需要补收的时候 当新支付处理
            this.calcData.new_repre_type = []
            this.calcData.user_id = resData.user_id
            this.calcData.phone = resData.phone
            this.calcData.buy_channel = resData.buy_channel

            if (this.calcData.isOvertime == 2) {
              this.showRepair = true
            } else if (this.calcData.isOvertime == 1) {
              this.showCalc = true
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getSan(id) {
      return this.$service.post('/Web/san/edit', { id, bus_id: this.$store.state.busId }).then(res => {
        if (res.data.errorcode == 0) {
          this.$refs['editData'].resetFields()
          const resData = res.data.data
          this.editData.ruleId = resData.id
          this.editData.cardName = resData.card_name
          this.editData.handId = resData.enter_card_number
          this.editData.deposit = parseFloat(resData.pre_amount)
          this.editData.new_pay_type  = resData.new_pay_type || []
          this.editData.exercise = resData.run_time_desc
          this.editData.baseFee = resData.base_fee
          this.editData.overFee = resData.over_fee
          this.editData.refund = parseFloat(resData.refund_amount)
          this.editData.new_refund_type = resData.refund_pay_type || []
          this.editData.new_repre_type = resData.repre_pay_type || []
          this.editData.status = resData.status
          this.editData.user_id = resData.user_id
          this.editData.remark = ''
          this.editData.repair = parseFloat(resData.repre_amount)
          // this.editData.repair = 20;
          this.editData.isOvertime = resData.pay_status
          this.editData.phone = resData.phone

          this.showEdit = true
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
    handleCancelQRCodeTicket(consume_sn) {
      const self = this
      this.$Modal.confirm({
        title: '取消核验',
        content: `确认取消凭证编号${consume_sn}的核验吗？`,
        onOk() {
          self.$service.post('/Web/San/cancelEntry', {
            consume_sn,
            bus_id: self.$store.state.busId
          }).then(res => {
            if (res.data.errorcode == 0) {
              self.getList()
              self.$Message.success(res.data.errormsg)
            } else {
              self.$Message.error(res.data.errormsg)
            }
          })
        }
      });
    },

  },
  mounted() {
    const today = new Date()
    this.duringDate = [today, today]
    this.getList()
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  activated() {
    document.addEventListener('keydown', this.handleKeyDown)

    const cn = this.$route.query.card_number
    if (cn) {
      this.keyEnterNum = cn
      this.getPrevious()
    }
  },
  deactivated() {
    document.removeEventListener('keydown', this.handleKeyDown)
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }
    }
  }
}
.repair-header {
  width: 100%;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;

  h3 {
    padding-left: 5px;
    font-weight: normal;
  }
}
.modal-buttons .ivu-btn-ghost {
  margin-left: 60px;
}
</style>
