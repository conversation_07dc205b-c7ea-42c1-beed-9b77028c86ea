<template>
  <div class="box">
    <div class="header">
      <Input v-model="searchPost.card_name" placeholder="请输入票名称..." style="width: 200px; margin-right: 20px" @on-enter="handleSearch"></Input>
      <Button type="success" icon="ios-search" @click="handleSearch">搜索</Button>
    </div>
    <div class="buddy">
      <Table ref="table" :columns="tableCols" :data="tableData"></Table>
      <div class="page">
        <Button type="success" @click="handleSave">添加票</Button>
        <Page
          @on-change="handlePage"
          :total="total"
          :current="searchPost.page_no"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </div>
    </div>

    <!-- <Modal v-model="dialogFlag" width="360" @on-visible-change="handleModalChange">
      <p slot="header" style="color: #f60; text-align: center">
        <Icon type="ios-information-circle"></Icon>
        <span>提示</span>
      </p>
      <div style="text-align: center">
        <p>确认删除"{{ dialogName }}"？</p>
      </div>
      <div slot="footer">
        <Button type="error" size="large" long @click="handleDelete(dialogId)">删除</Button>
      </div>
    </Modal> -->
  </div>
</template>

<script>
import editIcon from '@/assets/img/booking/edit-blue.png'
import deleteIcon from '@/assets/img/booking/delete-blue.png'

export default {
  name: 'InvoiceList',
  data() {
    return {
      dialogId: '',
      dialogName: '',
      // dialogFlag: false,
      searchPost: {
        page_no: 1,
        page_size: 10,
        card_name: '',
      },
      tableCols: [
        {
          title: '票名称',
          key: 'card_name',
        },
        {
          title: '基础时长',
          key: 'base_duration',
          render: (h, params) => {
            return h('div', params.row.base_duration + ' ' + (params.row.duration_unit == 1 ? '小时' : '分钟'))
          },
        },
        {
          title: '费用',
          key: 'base_fee',
          render: (h, params) => {
            return h('div', params.row.base_fee + ' 元')
          },
        },
        {
          title: '核销有效期',
          key: 'max_valid_time',
          render: (h, params) => {
            return h('div', params.row.max_valid_time + ' 天')
          },
        },
        {
          title: '超时规则',
          key: 'rule',
          render: (h, params) => {
            if (Array.isArray(params.row.rule) && params.row.rule.length > 0) {
              let jsonString = ''
              const unit = params.row.duration_unit == 1 ? '小时' : '分钟'
              if (params.row.rule.length === 1 && params.row.rule[0].price == 0) {
                return ''
              } else {
                params.row.rule.forEach((item) => {
                  let label = ''
                  if (!item.end || item.end == 0) {
                    label = `大于${item.start}${unit}`
                    // label = '大于0分钟'
                  } else {
                    label = `${item.start}~${item.end}${unit}内`
                  }
                  jsonString += `${label}，每${item.count}${unit}，收${item.price}元\n`
                })
                return (
                  <Tooltip max-width="300" content={jsonString} transfer>
                    {jsonString.substring(0, 10)}...
                  </Tooltip>
                )
              }
            } else {
              return ''
            }
          },
        },
        {
          title: '智能结算',
          key: 'is_auto_bill',
          render: (h, params) => {
            return h('div', params.row.is_auto_bill == 1 ? '开启' : '关闭')
          },
        },
        {
          title: '适用场地',
          key: 'can_use_space_ids_copy',
          render: (h, params) => {
            if (Array.isArray(params.row.can_use_space_ids_copy) && params.row.can_use_space_ids_copy.length > 0) {
              const names = params.row.can_use_space_ids_copy.join(',')
              let sub_names = ''
              if (names.length > 10) {
                sub_names = names.substring(0, 10) + '...'
                return (
                  <Tooltip max-width="300" content={names} transfer>
                    {sub_names}
                  </Tooltip>
                )
              } else {
                return (<span>{names}</span>)
              }
              
            } else {
              return (<span>入场</span>)
            }
          },
        },
        {
          title: '售卖渠道',
          key: 'sale_channel',
          render: (h, params) => {
            let labelList = []
            if (params.row.is_show_member == 1) {
              labelList.push('会员端')
            }
            if (params.row.is_show_ticket_machine == 1) {
              labelList.push('票务机')
            }
            if (params.row.is_show_meituan == 1) {
              labelList.push('美团')
            }
            if (params.row.is_show_dianping == 1) {
              labelList.push('大众点评')
            }
            if (params.row.is_show_tiktok == 1) {
              labelList.push('抖音')
            }
            return (<span>{labelList.join(',')}</span>)
          }
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            const action = []
            action.push(
              h(
                'img',
                {
                  attrs: {
                    src: editIcon,
                    title: '编辑',
                  },
                  style: {
                    width: '14px',
                    height: '14px',
                    marginRight: '24px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.handleSave(params.row.id)
                    },
                  },
                },
                '复制'
              )
            )
            action.push(
              h(
                'img',
                {
                  attrs: {
                    src: deleteIcon,
                    title: '删除',
                  },
                  style: {
                    width: '14px',
                    height: '14px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.dialogId = params.row.id
                      this.dialogName = params.row.card_name
                      // this.dialogFlag = true
                      this.$Modal.confirm({
                        title: '提示',
                        content: `确认删除 "${this.dialogName}"`,
                        onOk: () => {
                          this.handleDelete()
                        },
                        onCancel() {
                          this.dialogId = ''
                          this.dialogName = ''
                        }
                      });
                    },
                  },
                },
                '删除'
              )
            )
            return h('div', action)
          },
        },
      ],
      tableData: [],
      total: 0,
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      return this.$service.post('/Web/SanRule/getList', this.searchPost).then((res) => {
        if (res.data.errorcode == 0) {
          this.total = Number(res.data.data.count)
          this.tableData = []
          if (Array.isArray(res.data.data.list)) {
            this.tableData = res.data.data.list
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handlePage(page) {
      this.searchPost.page_no = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.searchPost.page_no = 1
      this.searchPost.page_size = size
      this.getList()
    },
    handleSave(id) {
      if (typeof id !== 'string') {
        id = null
      }
      this.$router.push({
        name: '票详情',
        params: { id },
      })
    },
    handleModalChange(flag) {
      if (!flag) {
        this.dialogId = ''
      }
    },
    handleDelete() {
      return this.$service.post('/Web/SanRule/del', { id: this.dialogId }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success('删除成功')
          this.dialogId = ''
          this.dialogName = ''
          // this.dialogFlag = false
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
          this.dialogId = ''
          this.dialogName = ''
          // this.dialogFlag = false
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  .buddy {
    margin: 20px 0;

    .page {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
    }
  }
}
</style>
