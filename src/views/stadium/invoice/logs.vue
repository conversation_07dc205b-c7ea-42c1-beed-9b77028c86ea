<template>
  <div class="tab-table-wrap customized-tabs logs-tab-wrap">
    <Tabs v-model="activeIndex" @on-click="clickTabs">
      <TabPane name="0" label="新购票" v-if="authority">
        <buy-ticket v-if="activated.includes('0')" />
      </TabPane>
      <TabPane name="1" label="核销记录">
        <check-logs v-if="activated.includes('1') && activeIndex === '1'" />
      </TabPane>
      <TabPane name="2" label="购票记录">
        <bought-logs v-if="activated.includes('2') && activeIndex === '2'" ref="refBoughtLogs" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import checkLogs from './checkLogs.vue'
import BuyTicket from './components/BuyTicket.vue'
import boughtLogs from './boughtLogs.vue'

export default {
  components: {
    BuyTicket,
    checkLogs,
    boughtLogs,
  },
  data() {
    return {
      activeIndex: '0',
      authority: true,
      activated: ['0'],
    }
  },
  created() {
    this.getAuth()
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index
      const active = document.querySelector('.ivu-tabs-ink-bar')
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
      if (!this.activated.includes(index)) {
        this.activated.push(index)
      }
    },
    getAuth() {
      return this.$service.post("/Web/San/SanPowerNode").then(res => {
        if (res.data.errorcode == 0) {
          this.authority = res.data.data?.SanBuy
          if(!this.authority){
            this.activated = ['1']
            this.activeIndex = '1'
          }
        }
      })
    }
  },
}
</script>

<style lang="less">
.logs-tab-wrap {
  height: 100%;
  .ivu-tabs {
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    height: 100%;
  }
  .ivu-tabs-content {
    height: calc(100% - 40px);
  }
  .ivu-tabs-tabpane {
    height: 100%;
    overflow: auto;
  }
}
</style>
<style lang="less" scoped>
.tab-table-wrap {
  height: 100%;
}
</style>
