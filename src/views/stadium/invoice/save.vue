<template>
  <div class="box">
    <div class="header">{{ id ? '编辑' : '新增' }}</div>
    <div class="buddy">
      <Form ref="formRef" :label-width="120" :model="formPost" :rules="formRules">
        <Form-item label="入场票名称" prop="name">
          <Input v-model="formPost.name" placeholder="请输入入场票名称..." :disabled="!!id"></Input>
        </Form-item>
        <Form-item label="适用场地">
          <treeselect
            :limit="10"
            :limitText="(count) => `还有 ${count} 个`"
            v-model="treeChecked"
            :options="treeOptions"
            placeholder="请选择..."
            no-results-text="无数据"
            no-options-text="无数据"
            no-children-text="无数据"
            loading-text="加载中..."
            valueConsistsOf="LEAF_PRIORITY"
            :default-expand-level="Infinity"
            multiple
          />
        </Form-item>
        <Form-item>
          <Alert type="warning" show-icon>
            配合闸机或门禁可限制进入场地，若不设置适用场地则表示可以进入场馆但不能进入场地
          </Alert>
        </Form-item>
        <Form-item label="核验有效期" prop="max_valid_time">
          <Input-number v-model="formPost.max_valid_time" :min="1" />
          <span style="margin-left: 10px">天</span>
        </Form-item>
        <Form-item>
          <Alert type="warning" show-icon>核验后仅能使用一次</Alert>
        </Form-item>
        <Form-item label="基础时长" required>
          <Input-number v-model="formPost.base_duration" :min="1" :precision="0" />
          <Select v-model="formPost.duration_unit" style="width: 80px; margin-left: 10px">
            <Option value="1">小时</Option>
            <Option value="2">分钟</Option>
          </Select>
          <span style="margin-left: 10px">费用</span>
          <Input-number v-model="formPost.base_fee" :min="0" style="width: 80px; margin-left: 10px" />
          <span style="margin-left: 10px">元</span>
        </Form-item>
        <Form-item :label="index ? '' : '超时计费规则'" v-for="(date, index) in timeoutList" :key="index">
          <div class="timeout-box" :style="index > 0 ? 'width:95%;' : 'width:100%'">
            <Input-number v-model="date.start" :min="0" :precision="0" disabled size="small" style="width: 60px" />
            <span style="margin: 0 10px">~</span>
            <Input-number
              v-model="date.end"
              :min="date.start + 1"
              :precision="0"
              :active-change="false"
              size="small"
              style="width: 60px"
            />
            <span style="margin: 0 10px">{{ hourOrMinute }}内，每</span>
            <Input-number v-model="date.count" :min="1" :precision="0" size="small" style="width: 60px" />
            <span style="margin: 0 10px">{{ hourOrMinute }}，收</span>
            <Input-number v-model="date.price" :min="0" size="small" style="width: 60px" />
            <span style="margin: 0 10px">元</span>
            <Icon
              v-if="index > 0"
              type="ios-trash"
              :size="22"
              style="cursor: pointer"
              @click="timeoutList.splice(index, 1)"
            ></Icon>
          </div>
        </Form-item>
        <Form-item>
          <Button type="dashed" shape="circle" @click="handleTimeoutAdd">添加超时梯度</Button>
        </Form-item>
        <Form-item>
          <Alert type="warning" show-icon>超时计费规则仅适用于线下验票结算，设备智能结算不计超时费用</Alert>
        </Form-item>
        <Form-item label="会员端售卖" prop="is_show_member">
          <i-switch v-model="formPost.is_show_member" true-value="1" false-value="0">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </Form-item>
        <!-- tiktok sale field name begin -->
        <Form-item label="美团售卖" prop="is_show_meituan">
          <i-switch
            v-model="formPost.is_show_meituan"
            true-value="1"
            false-value="0"
            @on-change="handleTiktokChange('meituan', $event)"
          >
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
          <div class="relative" v-if="formPost.meituan_deal_id" @click="handleTiktokChange('meituan', '1')">
            关联美团套餐 id {{ formPost.meituan_deal_id }} 编辑
          </div>
        </Form-item>
        <Form-item label="大众点评售卖" prop="is_show_dianping">
          <i-switch
            v-model="formPost.is_show_dianping"
            true-value="1"
            false-value="0"
            @on-change="handleTiktokChange('dianping', $event)"
          >
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
          <div class="relative" v-if="formPost.dianping_deal_id" @click="handleTiktokChange('dianping', '1')">
            关联大众点评套餐 id {{ formPost.dianping_deal_id }} 编辑
          </div>
        </Form-item>
        <Form-item label="抖音售卖" prop="is_show_tiktok">
          <i-switch
            v-model="formPost.is_show_tiktok"
            true-value="1"
            false-value="0"
            @on-change="handleTiktokChange('tiktok', $event)"
          >
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
          <div class="relative" v-if="formPost.tiktok_deal_id" @click="handleTiktokChange('tiktok', '1')">
            关联抖音套餐 id {{ formPost.tiktok_deal_id }} 编辑
          </div>
        </Form-item>
        <!-- tiktok sale field name end -->
        <Form-item label="票务机售卖" prop="is_show_ticket_machine">
          <i-switch v-model="formPost.is_show_ticket_machine" true-value="1" false-value="0">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </Form-item>
        <Form-item prop="is_auto_bill">
          <div slot="label" style="display: flex; flex-direction: row; justify-content: flex-end; align-items: center">
            <span style="margin-right: 4px">智能结算</span>
            <Tooltip content="开启后离场出闸机自动结算费用，结算后不能再次入场" transfer>
              <div style="border: 1px solid #ff9900; border-radius: 50%">
                <Icon type="ios-help" :size="16" color="#ff9900"></Icon>
              </div>
            </Tooltip>
          </div>
          <i-switch v-model="formPost.is_auto_bill" true-value="1" false-value="0">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </Form-item>
        <form-item label="购票须知">
          <Editor v-model="formPost.notice" :height="200"></Editor>
        </form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit">提交</Button>
            <Button @click="handleReset">返回</Button>
          </div>
        </Form-item>
      </Form>
    </div>

    <!-- tiktok sale modal begin -->
    <Modal v-model="couponModel" :width="1020" @on-cancel="handleCancelCoupon">
      <div slot="header" style="text-align: center">
        <p style="font-size: 18px">请选择团购套餐</p>
        <p style="font-size: 14px; margin-top: 10px">需在第三方平台提前创建对应团购套餐</p>
      </div>
      <div>
        <Table
          ref="selection"
          :columns="couponColumns"
          :data="couponList"
          :row-class-name="rowClassName"
          @on-select="handleCouponSelect"
        ></Table>
        <!-- <Page
          @on-change="handlePageChange"
          :total="couponTotal"
          :current="couponSearchPost.page_no"
          @on-page-size-change="handlePageSizeChange"
          show-sizer
          style="margin-top: 10px; text-align: right"
        ></Page> -->
        <div style="margin-top: 10px; text-align: right">
          <Button @click="handlePrevPage" :disabled="couponSearchPost.page_no === 1">上一页</Button>
          <span style="margin: 0 10px">
            第
            <strong>{{ couponSearchPost.page_no }}</strong>
            页
          </span>
          <Button @click="handleNextPage" :disabled="!hasMoreCoupons">下一页</Button>
        </div>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleSubmitCoupon">确定</Button>
        <Button @click="handleCancelCoupon">取消</Button>
      </div>
    </Modal>
    <!-- tiktok sale modal end -->
    <MeituanAuth v-model="showMeituanAuth" :meituanAuthInfo="meituanAuthInfo" />
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Editor from 'src/components/form/Editor'
import MeituanAuth from './components/MeituanAuth.vue'
import { unescapeHTML } from 'src/utils'
import qs from 'qs'

// const ERROR_STADIUM = '请选择调价场地'

export default {
  name: 'StadiumInvoiceSave',
  components: { Treeselect, Editor, MeituanAuth },
  props: {
    id: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      treeOptionsIds: [],
      treeOptions: [],
      treeChecked: [],
      allChecked: [],
      treeError: null,
      timeoutList: [{ start: 0, end: null, count: 1, price: 0 }],
      formPost: {
        bus_id: this.$store.state.busId,
        rule: [],
        name: '',
        can_use_space_ids: [],
        max_valid_time: 30,
        base_duration: 1,
        duration_unit: '1',
        base_fee: 40,
        is_show_member: '0',
        is_show_ticket_machine: '0',
        is_auto_bill: '0',
        notice: '',
        // tiktok sale
        is_show_meituan: '0',
        meituan_deal_id: '',
        is_show_dianping: '0',
        dianping_deal_id: '',
        is_show_tiktok: '0',
        tiktok_deal_id: '',
      },
      formRules: {
        name: [{ required: true, message: '请输入入场票名称', trigger: 'blur' }],
        max_valid_time: [{ required: true, message: '请输入核验有效期' }],
      },
      // tiktok sale
      couponList: [],
      hasMoreCoupons: false,
      couponName: '',
      couponModel: false,
      hasSalePermission: false,
      couponSearchPost: {
        page_no: 1,
        page_size: 50,
        platform_id: 3,
      },
      oldCoupon: {
        meituan_deal_id: '',
        dianping_deal_id: '',
        tiktok_deal_id: '',
      },
      showMeituanAuth: false,
      meituanAuthInfo: {},
    }
  },
  computed: {
    hourOrMinute() {
      return this.formPost.duration_unit == 1 ? '小时' : '分钟'
    },
    isAddTimeout() {
      const len = this.timeoutList.length
      if (len > 0) {
        return !!this.timeoutList[len - 1].end
      } else {
        return false
      }
    },
    // tiktok sale
    platformId() {
      if (this.couponName === 'meituan') {
        return 1
      } else if (this.couponName === 'dianping') {
        return 2
      } else if (this.couponName === 'tiktok') {
        return 3
      } else {
        return 1
      }
    },
    couponColumns() {
      const columns = [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
      ]
      const chunkOne = [
        {
          title: '套餐id',
          key: 'deal_id',
        },
        {
          title: '团购id',
          key: 'group_id',
        },
      ]
      const chunkTwo = [
        {
          title: '商品id',
          key: 'sku_id',
          render: (h, params) => {
            if (params.row.not_belong_to) {
              return h(
                'Tooltip',
                {
                  props: {
                    content: '所选商品不属于当前门店',
                    transfer: true,
                  },
                },
                [h('div', { style: { textDecoration: 'underline' } }, params.row.sku_id)]
              )
            }
            return h('div', params.row.sku_id)
          },
        },
      ]
      const chunkOther = [
        {
          title: '套餈名祢',
          key: 'title',
        },
        {
          title: '套餐价格',
          key: 'price',
          render: (h, params) => {
            return h('div', params.row.price + ' 元')
          },
        },
        {
          title: '团购券服务开始时间',
          width: 160,
          key: 'begin_time',
        },
        {
          title: '团购券服务结束时间',
          width: 160,
          key: 'end_time',
        },
        {
          title: '售卖状态',
          key: 'status',
        },
      ]

      if (this.couponName === 'meituan') {
        columns.push(...chunkOne)
      } else if (this.couponName === 'dianping') {
        columns.push(...chunkOne)
      } else if (this.couponName === 'tiktok') {
        columns.push(...chunkTwo)
      }

      columns.push(...chunkOther)

      return columns
    },
  },
  // watch: {
  //   treeChecked(newValue, oldValue) {
  //     if (newValue.length > 0) {
  //       this.treeError = null
  //     } else {
  //       this.treeError = ERROR_STADIUM
  //     }
  //   },
  // },
  created() {
    this.getTree().then(() => {
      if (this.id) {
        this.getInfo()
      }
    })
  },
  methods: {
    packageTree(tree) {
      const result = []
      tree.forEach((node) => {
        this.treeOptionsIds.push(node.id)

        const obj = {
          ...node,
          children: this.packageTree(node.children),
        }
        if (node.children.length === 0) {
          obj.leaf = true
          obj.children = undefined
          obj.isDisabled = node.is_space === 0
          if (!obj.isDisabled) {
            this.allChecked.push(node.id)
          }
        }
        result.push(obj)
      })
      return result
    },
    getInfo() {
      return this.$service
        .post('/Web/SanRule/getDetail', {
          id: this.id,
          bus_id: this.$store.state.busId,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const info = res.data.data
            info.max_valid_time = Number(info.max_valid_time)
            info.base_duration = Number(info.base_duration)
            info.base_fee = Number(info.base_fee)
            info.notice = unescapeHTML(info.notice)
            if (Array.isArray(info.can_use_space_ids)) {
              // const ids = new Set(info.can_use_space_ids);
              // this.treeChecked = Array.from(this.treeOptionsIdSet.intersection(ids));
              const intersection = this.treeOptionsIds.filter((id) => info.can_use_space_ids.includes(id))
              if (Array.isArray(intersection)) {
                this.treeChecked = intersection
              }
            }
            if (Array.isArray(info.rule)) {
              this.timeoutList = info.rule
            }
            this.formPost = info

            // tiktok sale
            this.oldCoupon = {
              meituan_deal_id: info.meituan_deal_id,
              dianping_deal_id: info.dianping_deal_id,
              tiktok_deal_id: info.tiktok_deal_id,
            }
          }
        })
    },
    getTree() {
      return this.$service.get('/web/space/getSpaceTreeList').then((res) => {
        if (res.data.errorcode === 0) {
          this.treeOptions = this.packageTree(res.data.data)
        }
      })
    },
    handleTimeoutAdd() {
      if (this.isAddTimeout) {
        let start = 0
        const len = this.timeoutList.length
        if (len > 0) {
          start = this.timeoutList[len - 1].end
        }
        this.timeoutList.push({ start, end: null, count: 1, price: 0 })
      } else {
        this.$Message.error('超时计费规则，结束时间必填！')
      }
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // if (this.treeChecked.length === 0) {
          //   this.$Message.error(ERROR_STADIUM)
          //   return
          // }

          if (this.id) {
            this.formPost.id = this.id
          }
          this.formPost.rule = JSON.stringify(this.timeoutList)
          // this.formPost.can_use_space_ids = JSON.stringify(this.treeChecked)
          this.formPost.can_use_space_ids = this.treeChecked
          this.$service.post('/Web/SanRule/save', this.formPost).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success('设置成功')
              this.$router.back()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    handleReset() {
      this.$router.back()
    },
    // tiktok sale
    checkSalePermission() {
      if (this.hasSalePermission) {
        return true
      }
      return this.$service
        .get('/Web/SanRule/getSanSalesPermission')
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.hasSalePermission = true
          } else {
            this.$Message.error(res.data.errormsg)
            this.hasSalePermission = false
          }
          return this.hasSalePermission
        })
        .catch(() => {
          return false
        })
    },
    openWindowByResult(result) {
      if (!result.is_auth && result.url) {
        // window.open(result.url, '_blank');
        this.meituanAuthInfo = { ...result, platform_id: this.platformId }
        this.showMeituanAuth = true
      }
    },
    getSaleAuthority() {
      const defaultResponse = {
        is_auth: false,
        url: '',
      }
      // if tiktok, return default
      if (this.platformId === 3) {
        return defaultResponse
      }
      return this.$service
        .get(`/Web/SanGroup/auth?redirect_url=${window.location.href}&platform_id=${this.platformId}`)
        .then((res) => {
          if (res.data.errorcode == 0) {
            return res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
            return defaultResponse
          }
        })
        .catch(() => {
          return defaultResponse
        })
    },
    getCouponList() {
      this.couponList = []
      this.couponSearchPost.platform_id = this.platformId
      return this.$service
        .get(`/Web/SanGroup/getThirdSanList?${qs.stringify(this.couponSearchPost)}`)
        .then(async (res) => {
          if (res.data.errorcode == 0) {
            const list = res.data.data?.list
            if (Array.isArray(list)) {
              this.couponList = list.map((item) => {
                let _checked = false
                if (item.deal_id == this.formPost[this.couponName + '_deal_id']) {
                  _checked = true
                  item._disabled = false
                }
                return {
                  ...item,
                  id: item.deal_id,
                  _checked,
                }
              })
            }
            if (this.platformId === 3) {
              this.hasMoreCoupons = res.data.data?.has_more
            } else {
              this.hasMoreCoupons = this.couponList.length === this.couponSearchPost.page_size
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
          return res.data
        })
        .catch(() => {
          this.$Message.error('获取优惠券列表失败')
        })
    },
    async handleTiktokChange(name, val) {
      this.couponName = name
      // check permission, if not, set value back to old value
      const flag = await this.checkSalePermission()
      if (!flag) {
        this.formPost['is_show_' + name] = val === '1' ? '0' : '1'
      } else {
        // check authority, if return url, open a window to ask for authority
        if (val === '1') {
          const res = await this.getSaleAuthority()
          this.openWindowByResult(res)
          if (res.is_auth || this.platformId === 3) {
            // get coupon list, show in the dialog
            this.couponSearchPost.page_no = 1
            const couponRes = await this.getCouponList()
            // 因shopId非对应商户下门店需重新授权
            if (couponRes.errorcode === 4010141) {
              const authRes = await this.getSaleAuthority()
              this.openWindowByResult(authRes)
              this.formPost['is_show_' + name] = '0'
            } else {
              this.couponModel = true
            }
          }
        } else {
          this.formPost[this.couponName + '_deal_id'] = ''
        }
      }
    },
    handleCouponSelect(rowList, row) {
      this.couponList.forEach((item) => {
        if (item.id === row.id) {
          item._checked = true
        } else {
          item._checked = false
        }
      })
      this.$forceUpdate()
    },
    handleCancelCoupon() {
      if (this.oldCoupon[this.couponName + '_deal_id']) {
        this.formPost[this.couponName + '_deal_id'] = this.oldCoupon[this.couponName + '_deal_id']
      } else {
        this.formPost['is_show_' + this.couponName] = '0'
        this.formPost[this.couponName + '_deal_id'] = ''
      }
      this.couponModel = false
    },
    handleSubmitCoupon() {
      const rowList = this.$refs.selection.getSelection()
      if (rowList.length === 0) {
        this.formPost['is_show_' + this.couponName] = '0'
        this.formPost[this.couponName + '_deal_id'] = ''
        this.couponModel = false
      } else {
        this.formPost['is_show_' + this.couponName] = '1'
        this.formPost[this.couponName + '_deal_id'] = rowList[0].id
        this.oldCoupon[this.couponName + '_deal_id'] = rowList[0].id
        this.couponModel = false
      }
    },
    // handlePageChange(pageNo) {
    //   this.couponSearchPost.page_no = pageNo
    //   this.getCouponList()
    // },
    // handlePageSizeChange(pageSize) {
    //   this.couponSearchPost.page_no = 1
    //   this.couponSearchPost.page_size = pageSize
    //   this.getCouponList()
    // }
    handlePrevPage() {
      this.couponSearchPost.page_no -= 1
      this.getCouponList()
    },
    handleNextPage() {
      this.couponSearchPost.page_no += 1
      this.getCouponList()
    },
    rowClassName(row) {
      return row._disabled ? 'gray-row' : ''
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  min-height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;

  .header {
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    color: #333;
    background-color: #f7f7f7;
    width: 100%;
    background: #f7f7f7;
    height: 40px;
    padding: 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #e0e3e9;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  .buddy {
    width: 40%;
    min-width: 721px;
    height: 100%;
    padding: 20px 40px;
    box-sizing: border-box;

    .timeout-box {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .relative {
      font-size: 14px;
      color: rgb(38, 38, 123);
      cursor: pointer;
    }
  }
}

/deep/ .gray-row {
  background-color: #f5f5f5;
  td {
    background-color: #f5f5f5 !important;
  }
}
</style>
