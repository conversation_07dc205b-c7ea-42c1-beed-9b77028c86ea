<template>
  <div class="customized-tabs">
    <Tabs @on-click="clickTabs" v-model="activeIndex" :animated="true">
      <TabPane label="团操教室管理">
        <ClassRoom />
      </TabPane>
      <!-- <TabPane label="运动场管理">
        <SportList v-if="activated.includes(1)"/>
      </TabPane> -->
    </Tabs>
  </div>
</template>

<script>
  import ClassRoom from './components/ClassRoom';
  import SportList from './components/SportList';
  import { mapState } from 'vuex';
  export default {
    name: 'UpData',
    components: {
      SportList,
      ClassRoom
    },
    data() {
      return {
        activeIndex: 0,
        activated: [0]
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    created() {
     
    },
    methods: {
      clickTabs(index) {
        this.activeIndex = index;
        const active = document.querySelector('.ivu-tabs-ink-bar');
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`;
        active.setAttribute('class', className);
        if (!this.activated.includes(index)) {
          this.activated.push(index);
        }
      }
    }
  };
</script>