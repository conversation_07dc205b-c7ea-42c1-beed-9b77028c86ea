<template>
  <div class="box">
    <div class="header">
      <Input
        v-model="searchPost.name"
        placeholder="请输入方案名称..."
        style="width: 200px; margin-right: 20px"
        @keyup.enter="handleSearch"
      ></Input>
      <Button type="success" icon="ios-search" @click="handleSearch">搜索</Button>
    </div>
    <div class="buddy">
      <Table
        :columns="tableCols"
        :data="tableData"
        @on-selection-change="onSelectionChange"
        @on-select="onSelect"
        @on-select-all="onSelectAll"
        @on-select-cancel="onSelectCancel"
      ></Table>
      <div class="page">
        <Page
          @on-change="handlePage"
          :total="total"
          :current="searchPost.page"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </div>
    </div>
    <div class="footer">
      <Button type="success" @click="handleSave">新增排场&定价方案</Button>
      <Button :disabled="selection.length === 0" @click="dialogFlag = true">批量删除</Button>
      <!-- <Button type="success">导出excel</Button> -->
      <div style="width: 84px"></div>
    </div>

    <Modal v-model="dialogFlag" width="360" @on-visible-change="handleModalChange">
      <p slot="header" style="color: #f60; text-align: center">
        <Icon type="ios-information-circle"></Icon>
        <span>提示</span>
      </p>
      <div style="text-align: center">
        <p>确认删除？</p>
      </div>
      <div slot="footer">
        <Button type="error" size="large" long @click="handleDelete(dialogId)">删除</Button>
      </div>
    </Modal>

    <Modal v-model="detailModalShow" title="排场&场次方案详情" width="60%">
      <Form ref="formRef" :model="formPost" :label-width="120">
        <Form-item label="价格策略名称" prop="name">
          <Input v-model="formPost.name" placeholder="请输入..." style="width: 300px" readonly></Input>
        </Form-item>
        <Form-item label="订场限制" prop="max_hour">
          <span>用户单次订场时长不能大于</span>
          <Input-number
            v-model="formPost.max_hour"
            :min="0.5"
            :step="0.5"
            :precision="1"
            size="small"
            style="margin: 0 10px"
            readonly
          ></Input-number>
          <span>小时</span>
        </Form-item>
        <Form-item label="半场预订">
          <i-switch v-model="formPost.is_half" :true-value="1" :false-value="0" disabled>
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </Form-item>
        <Form-item label="场次安排以及定价">
          <Tabs v-model="tabName" type="card">
            <Tab-pane :label="monday" name="monday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="tuesday" name="tuesday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="wednesday" name="wednesday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="thursday" name="thursday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="friday" name="friday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="saturday" name="saturday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
            <Tab-pane :label="sunday" name="sunday">
              <div class="strategy-box">
                <Table
                  :columns="dialogTableCols"
                  :data="dialogTableData"
                  @on-selection-change="onSelectionChange"
                  @on-select="onSelect"
                  @on-select-all="onSelectAll"
                  @on-select-cancel="onSelectCancel"
                ></Table>
              </div>
            </Tab-pane>
          </Tabs>
        </Form-item>
      </Form>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import Selection from 'mixins/selection'
import { putWeek, getWeekJson } from '@/service/putOSS.js'
import editIcon from '@/assets/img/booking/edit-blue.png'
import deleteIcon from '@/assets/img/booking/delete-blue.png'

export default {
  mixins: [Selection],
  data() {
    return {
      dialogId: '',
      dialogFlag: false,
      total: 0,
      searchPost: {
        page: 1,
        per_page: 10,
        name: '',
      },
      tableCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        {
          title: '方案名称',
          key: 'name',
        },
        {
          title: '场次&定价',
          key: 'detail',
          render: (h, params) => {
            return h('div', [
              h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#1890ff',
                  },
                  attrs: {
                    title: '点击查看详情',
                  },
                  on: {
                    click: () => {
                      this.handleDetailDialog(params.row)
                    },
                  },
                },
                '查看详情'
              ),
            ])
          },
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            return h('div', [
              h(
                'img',
                {
                  attrs: {
                    src: editIcon,
                    title: '编辑',
                  },
                  style: {
                    width: '14px',
                    height: '14px',
                    marginRight: '24px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.handleSave(params.row.id)
                    },
                  },
                },
                '编辑'
              ),
              h(
                'img',
                {
                  attrs: {
                    src: deleteIcon,
                    title: '删除',
                  },
                  style: {
                    width: '14px',
                    height: '14px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      // this.handleDelete(params.row.id)
                      this.dialogId = params.row.id
                      this.dialogFlag = true
                    },
                  },
                },
                '删除'
              ),
            ])
          },
        },
      ],
      tableData: [],
      // dialog
      detailModalShow: false,
      formPost: {},
      tabName: 'monday',
      allCols: [
        {
          title: '场次时间',
          key: 'time',
        },
        {
          title: '半场价',
          key: 'half',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                  },
                  props: {
                    content: params.row.half,
                    transfer: true,
                  },
                },
                params.row.half
              ),
            ])
          },
        },
        {
          title: '全场价',
          key: 'all',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                  },
                  props: {
                    content: params.row.all,
                    transfer: true,
                  },
                },
                params.row.all
              ),
            ])
          },
        },
      ],
      noHalfCols: [
        {
          title: '场次时间',
          key: 'time',
        },
        {
          title: '全场价',
          key: 'all',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                  },
                  props: {
                    content: params.row.all,
                    transfer: true,
                  },
                },
                params.row.all
              ),
            ])
          },
        },
      ],
      dialogTableCols: [],
      dialogTableDataAll: [],
    }
  },
  computed: {
    monday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周一 (0)'
      return `周一 (${this.formPost.schedule_price.monday.list.length})`
    },
    tuesday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周二 (0)'
      return `周二 (${this.formPost.schedule_price.tuesday.list.length})`
    },
    wednesday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周三 (0)'
      return `周三 (${this.formPost.schedule_price.wednesday.list.length})`
    },
    thursday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周四 (0)'
      return `周四 (${this.formPost.schedule_price.thursday.list.length})`
    },
    friday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周五 (0)'
      return `周五 (${this.formPost.schedule_price.friday.list.length})`
    },
    saturday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周六 (0)'
      return `周六 (${this.formPost.schedule_price.saturday.list.length})`
    },
    sunday() {
      if (!this.formPost.schedule_price || !this.formPost.schedule_price.monday.list) return '周日 (0)'
      return `周日 (${this.formPost.schedule_price.sunday.list.length})`
    },
    dialogTableData() {
      return this.dialogTableDataAll.filter((item) => {
        return item.tabName === this.tabName
      })
    },
  },
  watch: {
    'formPost.is_half'(newValue, oldValue) {
      if (newValue === 0) {
        this.dialogTableCols = this.noHalfCols
      } else if (newValue === 1) {
        this.dialogTableCols = this.allCols
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      return this.$service.post('/Web/Space/getListSchedulePriceTemplate', this.searchPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.data.map((item) => {
            item._checked = this.selectionId.includes(item.id)
            return item
          })
          this.total = res.data.data.total
        }
      })
    },
    handleSearch() {
      this.searchPost.page = 1
      this.getList()
    },
    handlePage(page) {
      this.searchPost.page = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.searchPost.page = 1
      this.searchPost.per_page = size
      this.getList()
    },
    async handleDetailDialog(row) {
      row.max_hour = Number(row.max_hour)
      this.formPost = { ...row }
      this.dialogTableDataAll = []

      const strategy = { schedule_price: {} }
      for (const key in this.formPost.schedule_price) {
        if (Object.hasOwnProperty.call(this.formPost.schedule_price, key)) {
          const url = this.formPost.schedule_price[key]
          strategy.schedule_price[key] = await getWeekJson(url)
        }
      }
      this.formPost.schedule_price = strategy.schedule_price

      for (const key in strategy.schedule_price) {
        if (Object.hasOwnProperty.call(strategy.schedule_price, key)) {
          const day = strategy.schedule_price[key]
          day.list.forEach((item) => {
            let halfFlag = true
            let allFlag = true

            let half = `非会员:${item.no_member_price.half.common}元/${item.no_member_price.half.holiday}元 `
            if (Array.isArray(item.member_price)) {
              item.member_price.forEach((member, index) => {
                if (member.half.common === null || member.half.holiday === null) {
                  halfFlag = false
                }
                half += `会员价${index + 1}:${member.half.common}元/${member.half.holiday}元; `
              })
            }
            let all = `非会员:${item.no_member_price.all.common}元/${item.no_member_price.all.holiday}元 `
            if (Array.isArray(item.member_price)) {
              item.member_price.forEach((member, index) => {
                if (member.all.common === null || member.all.holiday === null) {
                  allFlag = false
                }
                all += `会员价${index + 1}:${member.all.common}元/${member.all.holiday}元; `
              })
            }

            if (item.no_member_price.half.common === null || item.no_member_price.half.holiday === null) {
              halfFlag = false
            }

            if (item.no_member_price.all.common === null || item.no_member_price.all.holiday === null) {
              allFlag = false
            }

            item.tabName = key
            item.time = `${item.start_time} ~ ${item.end_time}`
            item.half = halfFlag ? half : '未设置'
            item.all = allFlag ? all : '未设置'
          })
          this.dialogTableDataAll = [...this.dialogTableDataAll, ...day.list]
        }
      }
      this.detailModalShow = true
    },
    handleSave(id) {
      if (typeof id !== 'number') {
        id = null
      }
      this.$router.push({
        name: '场次&定价',
        params: { id },
      })
    },
    handleModalChange(flag) {
      if (!flag) {
        this.dialogId = ''
      }
    },
    handleDelete(id = '') {
      let ids = []
      if (typeof id === 'number') {
        ids.push(id)
      } else {
        ids = [...this.selectionId]
      }
      if (ids.length === 0) {
        this.$Message.error('请选择要删除的记录')
        return
      }
      return this.$service.post('/Web/Space/deleteSchedulePriceTemplate', { ids }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success('删除成功')
          this.dialogId = ''
          this.dialogFlag = false
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
  }

  .buddy {
    margin: 20px 0;

    .page {
      text-align: right;
      margin-top: 20px;
    }
  }

  .footer {
    width: 370px;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
