<template>
    <div class="box">
        <div class="header-ya">
            <div class="ya">{{$route.params.stadiumId==0?'新增':'编辑'}}</div>
        </div>
        <div class="buddy">
            <Form ref="stadium" :model="stadium" :rules="stadiumRule" class="modal-form" :label-width="180">
                <Form-item label="场地名称" prop="space_name">
                    <Input v-model="stadium.space_name" placeholder="请输入..." style="max-width:630px;"></Input>
                </Form-item>
                <Form-item label="场地类型" prop="space_type_id">
                    <div class="tag-box">
                        <Tag v-for="(item, index) in stadiumCategoryList" :key="'tag'+item.id" @on-change="handleTagClick" :name="index" :checked="item.check" @on-close="handleCloseCategory(item.id)" color="success" style="border:1px solid #e8eaec;" checkable closable>{{item.name}}</Tag>
                        <div>
                            <Input v-if="categoryInputBoxFlag" size="small" :maxlength="10" placeholder="请输入类型名称..." v-model="categoryInputTemp" style="width:140px;" />
                            <Button v-if="categoryInputBoxFlag" size="small" icon="md-checkmark"  @click="handleSaveCategory"></Button>
                            <Button v-if="categoryInputBoxFlag" size="small" icon="md-close"  @click="handleToggleCategory"></Button>
                        </div>
                    </div>
                    <Button icon="ios-add" type="dashed" size="small" @click="handleToggleCategory">添加</Button>
                    <!-- <Button v-if="categoryInputTempIdx!=-1" icon="ios-add" type="dashed" size="small" @click="categoryInputBoxFlag = true">编辑</Button> -->
                </Form-item>
                <Form-item label="营业时间" prop="beg_time">
                    <TimePicker v-model="bussinessTime" type="timerange" :steps="[1, 30]" :disabled-hours="[0,1,2,3,4,5,6,7]" format="HH:mm" :disabled="isEdit"
                        @on-change="handleDateChange" placement="bottom-start" placeholder="请选择时间段..." style="width: 200px;" :editable="false" :clearable="false"></TimePicker>
                </Form-item>
                <Form-item label="场地时长" required>
                    用户单次订场时长不能小于
                    <!-- <InputNumber :max="4" :min="0.5" :step="0.5" v-model="stadium.booking_time_min" :editable="false" :disabled="isEdit"></InputNumber> -->
                    <InputNumber
                      :min="0.5"
                      :max="4"
                      :step="0.5"
                      v-model="stadium.booking_time_min"
                      :editable="false"
                      :disabled="isEdit"
                      @on-change="handleChangeBookingTimeMin"/>
                    小时；不能大于
                    <!-- <InputNumber :min="0.5" :step="0.5" v-model="stadium.booking_time_max" :editable="false" :disabled="isEdit"></InputNumber> -->
                    <InputNumber
                      :min="0.5"
                      :step="stadium.booking_time_min"
                      v-model="stadium.booking_time_max"
                      :editable="false"
                      :disabled="isEdit"
                      @on-change="handleChangeBookingTimeMax"/>
                    小时
                    <Alert type="error" style="width:430px;margin-top:10px;">营业时间和订场时长一旦创建，不允许编辑</Alert>
                </Form-item>
                <Form-item label="适应卡种" required>
                    <RadioGroup v-model="cardScope" type="button" @on-change="handleFilterCard" >
                        <Radio :label="-1">全部</Radio>
                        <Radio :label="0">单店储值卡</Radio>
                        <Radio :label="1">多店通用储值卡</Radio>
                    </RadioGroup>
                    <Checkbox v-model="checkAll" @on-change="handleCheckAll" style="margin-left:40px;">全选</Checkbox>
                </Form-item>
                <Form-item>
                    <Select placeholder="请选择" v-if="filterCardList && filterCardList.length>0"
                        v-model="stadium.card_ids" @on-change="handleChooseCard"
                        style="max-width: 630px;" multiple filterable>
                        <Option v-for="item in filterCardList" :key="item.card_id" :value="item.card_id">{{item.card_name}}</Option>
                    </Select>
                    <span v-if="filterCardList && filterCardList.length == 0">该场馆暂无该卡种会员卡</span>
                </Form-item>
                <Form-item label="半场预订" prop="full_price">
                    <i-switch v-model="stadium.open_half" true-value="1" false-value="0">
                        <span slot="open">开</span>
                        <span slot="close">关</span>
                    </i-switch>
                    <div class="panel" v-if="stadium.open_half == 1">
                        <div class="panel-title">
                            <span>半场价格设置（元/小时）</span>
                            <span><Icon type="ios-information-circle-outline" style="margin-right:4px;color:orange;" />预订半小时则按照1小时价格的二分之一计算</span>
                        </div>
                        <div class="panel-body">
                            <div class="panel-item">
                                常规时间
                                <InputNumber v-model="stadium.half_price" :active-change="false" :precision="2" :max="99999" :min="0" :step="0.1" style="margin:0 10px;" />
                                元/小时
                            </div>
                            <div class="panel-item">
                                <div class="item-label">特殊时段</div>
                                <div class="item-multipe-line">
                                    <div class="item-line" v-for="(rule, index) in stadium.half_special_rule" :key="'rule'+rule.id">
                                        <span v-if="rule.status != 3" style="width:240px;">{{rule.weeksToString}}</span>
                                        <span v-if="rule.status != 3" style="width:70px;">{{rule.beg_time}}-{{rule.end_time}}</span>
                                        <span v-if="rule.status != 3" style="width:111px;">价格{{rule.price}}元/小时</span>
                                        <Button v-if="rule.status != 3" type="text" size="small" @click="handleShowModal(1, rule, index)" ghost>编辑</Button>
                                        <Button v-if="rule.status != 3" type="text" size="small" style="color:red;" @click="handleDeleteModal(1, rule.id, index)" ghost>删除</Button>
                                    </div>
                                    <Button style="margin-left:13px;" @click="handleShowModal(1)">添加</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel">
                        <div class="panel-title">
                            <span>全场价格设置（元/小时）</span>
                            <span><Icon type="ios-information-circle-outline" style="margin-right:4px;color:orange;" />预订半小时则按照1小时价格的二分之一计算</span>
                        </div>
                        <div class="panel-body">
                            <div class="panel-item">
                                常规时间
                                <InputNumber v-model="stadium.full_price" :active-change="false" :precision="2" :max="99999" :min="0" :step="0.1" style="margin:0 10px;" />
                                元/小时
                            </div>
                            <div class="panel-item">
                                <div class="item-label">特殊时段</div>
                                <div class="item-multipe-line">
                                    <div class="item-line" v-for="(rule, index) in stadium.full_special_rule" :key="'rule'+rule.id">
                                        <span v-if="rule.status != 3" style="width:240px;">{{rule.weeksToString}}</span>
                                        <span v-if="rule.status != 3" style="width:70px;">{{rule.beg_time}}-{{rule.end_time}}</span>
                                        <span v-if="rule.status != 3" style="width:111px;">价格{{rule.price}}元/小时</span>
                                        <Button v-if="rule.status != 3" type="text" size="small" @click="handleShowModal(0, rule, index)" ghost>编辑</Button>
                                        <Button v-if="rule.status != 3" type="text" size="small" style="color:red;" @click="handleDeleteModal(0, rule.id, index)" ghost>删除</Button>
                                    </div>
                                    <Button style="margin-left:13px;" @click="handleShowModal(0)">添加</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </Form-item>
                <!-- TODO: image description -->
                <Form-item required>
                    <div class="image-description image-description-required" slot="label">
                        <p class="label">场地图片</p>
                        <p class="tip">图片最佳尺寸: 1200X900</p>
                        <p class="tip">推荐图片大小: &lt;100kb</p>
                        <p class="tip">格式限制: jpg、png</p>
                    </div>
                    <!-- <div class="upload-box">
                        <Upload
                            ref="upload"
                            :show-upload-list="false"
                            :default-file-list="stadium.img_url"
                            :on-success="handleSuccess"
                            :format="['jpg','jpeg','png']"
                            :max-size="8192"
                            :on-format-error="handleFormatError"
                            :on-exceeded-size="handleMaxSize"
                            multiple
                            type="drag"
                            :data="{savePath: './Uploads/'}"
                            :action="getBaseUrl() + '/Admin/Public/upload'"
                            style="display: inline-block">
                            <Button type="success">选择本地图片</Button>
                        </Upload>
                    </div> -->
                    <image-crop-upload
                        refName="cropEverything"
                        v-model="imageTemporary"
                        :options="{aspectRatio: 4/3}"
                        multiple
                        style="max-width:630px"
                    ></image-crop-upload>
                    <div class="image-box">
                        <div class="photo-box" v-for="(url, $index) in stadium.img_url" :key="url">
                            <img class="photo" :src="url"></img>
                            <div class="delete-photo" @click="handlePhotoDelete($index)">
                                <Icon type="ios-close" size="45" />
                            </div>
                        </div>
                    </div>
                </Form-item>
                <Form-item>
                    <div class="buttons">
                        <Button type="primary" @click="handleSaveForm">保存</Button>
                        <Button @click="handleCancelForm">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>

        <Modal v-model="showSpecialDate" style="width:420px">
            <div class="modal-header">
                <h3>设置特殊时段价格</h3>
            </div>
            <div class="modal-buddy" style="margin-top:20px;">
                <CheckboxGroup v-model="dayInWeek">
                    <Checkbox :label="index + 1" v-for="(day, index) in dayOfWeek" :key="day">{{day}}</Checkbox>
                </CheckboxGroup>
                <TimePicker :value="modalTime" type="timerange" :steps="[1, 60]" :disabled-hours="disableTimeOption" :editable="false" format="HH:mm" @on-change="handleTimeChange" placement="bottom-start" placeholder="请选择时间段..." style="width: 200px;margin:20px 0;"></TimePicker>
                <div>
                    价格
                    <InputNumber v-model="price" :precision="2" :active-change="false" :max="99999" :min="0" :step="0.1" style="margin:0 10px;" />
                    元/小时
                </div>
            </div>
            <div slot="footer" class="modal-buttons">
                <Button type="success" @click="handleSaveModal">确定</Button>
                <Button @click="showSpecialDate = false">取消</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { getBaseUrl } from 'src/utils/config';
import ImageCropUpload from '@/components/form/cropper.vue'

export default {
    components: {
        ImageCropUpload,
    },
    watch: {
        imageTemporary(newValue, oldValue) {
            if (newValue) {
                this.stadium.img_url.push(newValue)
            }
        }
    },
    data() {
        const validateReservation = (rule, value, callback) => {
            if (this.stadium.open_half == 1) {
                if (this.stadium.half_price === null && this.stadium.full_price === null) {
                    callback(new Error('请配置全场和半场的价格!'));
                } else if (this.stadium.half_price === null) {
                    callback(new Error('请配置半场价格!'));
                } else if (this.stadium.full_price === null) {
                    callback(new Error('请配置全场价格!'));
                } else {
                    callback();
                }
            } else {
                if (value === null) {
                    callback(new Error('请配置全场价格!'));
                } else {
                    callback();
                }
            }
        };
        return {
            getBaseUrl,
            bussinessTime: ['08:00', '23:00'],
            dayOfWeek: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            imageTemporary: '',
            stadium: {
                space_id: '',
                space_name: '',
                space_type_id: '',
                beg_time: '08:00',
                end_time: '23:00',
                img_url: [],
                card_ids: [],
                booking_time_min: 1.0,
                booking_time_max: 4.0,
                open_half: '0',
                half_price: 0,
                full_price: 0,
                half_special_rule: [],
                full_special_rule: []
            },
            stadiumRule: {
                space_name: [{required: true, message: '请输入场地名称!', trigger: 'blur'}],
                space_type_id: [{required: true, message: '请选择场地类型!', type: 'number', trigger: 'change'}],
                beg_time: [{required: true, message: '请选择营业时间!', trigger: 'blur'}],
                full_price: [{required: true, validator: validateReservation, trigger: 'change'}]
            },
            categoryInputBoxFlag: false,
            categoryInputTempIdx: -1,
            categoryInputTemp: '',
            stadiumCategoryList: [],
            filterCardList: [],
            cardScope: -1,
            // modal
            showSpecialDate: false,
            isHalf: 0,
            dayInWeek: [],
            startTime: '',
            overTime: '',
            price: 0,
            modalTime: [],
            modalId: '',
            modalIdx: '',
            disableTimeOption: [],
            // delete list
            deleteFullList: [],
            deleteHalfList: [],
            isEdit: false,
            checkAll: false
        }
    },
    computed: {
        ...mapState(['busId', 'addCardList', 'globalBelongBusId'])
    },
    methods: {
        ...mapActions(['getAddCardList']),
        handleCheckAll() {
            if (this.checkAll) {
                this.stadium.card_ids = this.filterCardList.map(item=>item.card_id);
            }
        },
        handleChooseCard() {
            if (this.stadium.card_ids.length === this.filterCardList.length) {
                this.checkAll = true;
            } else {
                this.checkAll = false;
            }
        },
        // about category of stadium.
        handleToggleCategory() {
            this.categoryInputBoxFlag = !this.categoryInputBoxFlag;
            this.categoryInputTemp = "";
        },
        handleCloseCategory(id) {
            this.$Modal.confirm({
                title: '删除场地类型则该类型下的场地一并删除！',
                onOk: ()=>{
                    this.$service.post('/Web/Space/delSpaceType', {id}).then(res => {
                        if (res.data.errorcode == 0) {
                            this.$Message.info(res.data.errormsg);
                            this.getStadiumCategoryList();
                        } else {this.$Message.error(res.data.errormsg);}
                    });
                }
            });
        },
        handleSaveCategory() {
            const temp = this.categoryInputTemp.trim();
            if (!!temp) {
                if (this.categoryInputTempIdx === -1) {
                    const category = {value: temp, checkFlag: false};
                    this.stadiumCategoryList.push(category);
                } else {
                    let tempCategory = this.stadiumCategoryList[this.categoryInputTempIdx];
                    tempCategory.value = temp;
                    tempCategory.checkFlag = false;
                }

                this.$service.post('/Web/Space/saveSpaceType', {
                    name: this.categoryInputTemp
                }).then(res => {
                    if (res.data.errorcode == 0) {
                        this.$Message.info(res.data.errormsg);
                        this.categoryInputTemp = "";
                        this.categoryInputTempIdx = -1;
                        this.categoryInputBoxFlag = false;
                        this.getStadiumCategoryList();
                    } else {this.$Message.error(res.data.errormsg);}
                });
            }
        },
        getStadiumCategoryList() {
            return this.$service.post('/Web/Space/getTypes').then(res => {
                if (res.data.errorcode == 0) {
                    let arr = res.data.data;
                    arr.forEach(item => {
                        item.check = false;
                    });
                    this.stadiumCategoryList = arr;
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        handleTagClick(checked, name) {
            if (checked) {
                this.stadium.space_type_id = this.stadiumCategoryList[name].id;
            } else {
                this.stadium.space_type_id = '';
            }
            this.$refs['stadium'].validateField('space_type_id');
            this.stadiumCategoryList.forEach((item, index) => {
                if (index == name) {
                    item.check = checked;
                } else {
                    item.check = false;
                }
            });
        },

        // about date and time.
        handleDateChange(arr) {
            if (Array.isArray(arr)) {
                this.stadium.beg_time = arr[0];
                this.stadium.end_time = arr[1];
                if (this.stadium.end_time == '23:30') {
                    this.$Message.error('营业时间只能 8:00~23:00 !');
                }
            }
        },
        // 改变最小订场时长
        handleChangeBookingTimeMin(val) {
          const { booking_time_max } = this.stadium;
          /* max须为min的倍数以处理订场时长对应正确 */
          if (booking_time_max % val !== 0) {
            const num = Math.round(booking_time_max / val);
            this.$nextTick(() => {
              this.stadium.booking_time_max = val * num;
            })
          }
        },
        // 改变最大订场时长
        handleChangeBookingTimeMax(val) {
          const { booking_time_min } = this.stadium;
          /* max须为min的倍数以处理订场时长对应正确 */
          if (val % booking_time_min !== 0) {
            const num = Math.round(val / booking_time_min);
            this.$nextTick(() => {
              this.stadium.booking_time_max = booking_time_min * num;
            })
          }
        },
        handleTimeChange(arr) {
            if (Array.isArray(arr)) {
                this.startTime = arr[0];
                this.overTime = arr[1];
            }
        },

        // about upload photo.
        handleMaxSize(file) {
            this.$Notice.warning({
                title: '图片体积过大',
                desc: '图片 ' + file.name + ' 体积过大，不能超过 8M。'
            });
        },
        handleFormatError(file) {
            this.$Notice.warning({
                title: '图片格式错误',
                desc: '文件 ' + file.name + ' 格式错误，请选择 jpg 或 png 格式。'
            });
        },
        async handleSuccess(res, file, fileList) {
            const url = `${res.info}@70q_1pr`;
            if (url !== null && url.length > 0) {
                this.stadium.img_url.push(url)
            } else {
                this.$Message.error('上传图片失败');
            }
        },
        handlePhotoDelete(idx) {
            this.stadium.img_url.splice(idx, 1);
        },

        // about a modal.
        handleShowModal(isHalf, rule = false, idx = false) {
            this.disableTimeOption = [];
            const start = parseInt(this.bussinessTime[0].split(':')[0]);
            let end = parseInt(this.bussinessTime[1].split(':')[0]);
            if (this.bussinessTime[1].split(':')[1] === '30') {
                end++;
            }

            for (let index = 0; index < start; index++) {
                this.disableTimeOption.push(index);
            }
            for (let index = end+1; index < 24; index++) {
                this.disableTimeOption.push(index);
            }

            this.isHalf = isHalf;
            if (rule) {
                const arr = [];
                arr.push(rule.beg_time);
                arr.push(rule.end_time);
                this.modalId = rule.id;
                this.modalIdx = idx;
                this.dayInWeek = rule.weeks;
                this.startTime = rule.beg_time;
                this.overTime = rule.end_time;
                this.modalTime = arr;
                this.price = parseFloat(rule.price);
            } else {
                this.modalId = '';
                this.modalIdx = '';
                this.dayInWeek = [];
                this.modalTime = [];
                this.startTime = '';
                this.overTime = '';
                this.price = 0;
            }
            this.showSpecialDate = true;
        },
        handleSaveModal() {
            if (this.dayInWeek.length === 0) {
                this.$Message.error('请选择特殊时间段!');
                return false;
            } else if (!this.startTime) {
                this.$Message.error('请选择特殊时段!');
                return false;
            } else if (this.startTime == '00:00') {
                this.$Message.error('不在营业时间内!');
                return false;
            } else if (this.startTime == this.overTime) {
                this.$Message.error('特殊时段设置错误!');
                return false;
            } else if (this.price == null) {
                this.$Message.error('价格不能为空!');
                return false;
            }

            let specialDate = {};
            specialDate.status = 1;
            specialDate.id = '';
            specialDate.weeks = this.dayInWeek;
            specialDate.beg_time = this.startTime;
            specialDate.end_time = this.overTime;
            specialDate.price = this.price;

            let specialTarget = null;
            if (this.isHalf == 1) {
                specialTarget = this.stadium.half_special_rule;
            } else {
                specialTarget = this.stadium.full_special_rule;
            }

            if (this.modalId !== '') {
                specialDate.status = 2;
            } else {
                specialDate.status = 1;
            }

            if (this.modalIdx !== '') {
                specialDate.id = this.modalId;
                specialTarget[this.modalIdx] = specialDate;
                // specialTarget.forEach(item => {
                //     if (item.id === this.modalId) {
                //         Object.assign(item, specialDate);
                //     }
                // });
            } else {
                specialTarget.push(specialDate);
            }

            this.getWeeksToString(specialTarget);
            this.showSpecialDate = false;
        },
        handleDeleteModal(isHalf, id, ruleIdx) {
            if (isHalf == 1) {
                // const ruleIdx = this.stadium.half_special_rule.findIndex(item=>(item.id==id));
                if (ruleIdx !== -1) {
                    let rule = this.stadium.half_special_rule[ruleIdx];
                    if (rule.id != '') {
                        rule.status = 3;
                        this.deleteHalfList.push(rule);
                    }
                    this.stadium.half_special_rule.splice(ruleIdx, 1);
                }
            } else {
                // const ruleIdx = this.stadium.full_special_rule.findIndex(item=>(item.id==id));
                if (ruleIdx !== -1) {
                    let rule = this.stadium.full_special_rule[ruleIdx];
                    if (rule.id != '') {
                        rule.status = 3;
                        this.deleteFullList.push(rule);
                    }
                    this.stadium.full_special_rule.splice(ruleIdx, 1);
                }
            }
        },

        // about form submit.
        handleSaveForm() {
            this.$refs['stadium'].validate(valid => {
                if (this.stadium.end_time == '23:30') {
                    this.$Message.error('营业时间只能 8:00~23:00 !');
                    return false;
                } else if (this.stadium.end_time == this.stadium.beg_time) {
                    this.$Message.error('营业结束时间必须大于开始时间!');
                    return false;
                } else if (this.stadium.booking_time_min > this.stadium.booking_time_max) {
                    this.$Message.error('单次订场最小时长必须大最大时长!');
                    return false;
                } else if (this.stadium.img_url.length === 0) {
                    this.$Message.error('请上传场地图片!');
                    return false;
                }
                if (valid) {
                    this.$service.post('/Web/Space/save', {
                        ...this.stadium,
                        half_special_rule: this.stadium.half_special_rule.concat(this.deleteHalfList),
                        full_special_rule: this.stadium.full_special_rule.concat(this.deleteFullList)
                    }).then(res => {
                        if (res.data.errorcode == 0) {
                            this.$Message.info(res.data.errormsg);
                            this.$router.back();
                        } else {this.$Message.error(res.data.errormsg);}
                    });
                }
            });
        },
        handleCancelForm() {
            this.$router.back();
        },
        getWeeksToString(ruleList) {
            if (Array.isArray(ruleList) && ruleList.length > 0) {
                ruleList.forEach(rule => {
                    let weeksToString = '';
                    rule.weeks.sort();
                    rule.weeks.forEach((day, index) => {
                        weeksToString += this.dayOfWeek[day - 1];
                        if ((index + 1) !== rule.weeks.length) {
                            weeksToString += '、';
                        }
                    });
                    rule.weeksToString = weeksToString;
                });
            }
        },
        getInfo(space_id) {
            return this.$service.post('/Web/Space/getEditInfo', {space_id}).then(res => {
                if (res.data.errorcode == 0) {
                    const space = res.data.data;
                    this.stadium = {
                        ...space,
                        half_price: parseFloat(space.half_price),
                        full_price: parseFloat(space.full_price),
                        booking_time_min: parseFloat(space.booking_time_min),
                        booking_time_max: parseFloat(space.booking_time_max),
                        open_half: space.open_half+''
                    };

                    if (Array.isArray(this.stadiumCategoryList) && this.stadiumCategoryList.length > 0 && !!space.space_type_id) {
                        this.stadiumCategoryList.forEach(item => {
                            if (item.id == space.space_type_id) {
                                item.check = true;
                            }
                        });
                    }

                    let arr = [];
                    arr.push(space.beg_time);
                    arr.push(space.end_time);
                    this.bussinessTime = arr;

                    this.getWeeksToString(this.stadium.full_special_rule);
                    this.getWeeksToString(this.stadium.half_special_rule);
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        handleFilterCard() {
            let arr = [...this.addCardList].filter(item => item.card_type_id == 3);

            // single or multi.
            if (this.cardScope != -1) {
                arr = arr.filter(item => item.universal_card == this.cardScope);
            }
            this.filterCardList = arr;
            this.stadium.card_ids = [];
            this.checkAll = false;
        }
    },
    created() {
        const stadiumId = this.$route.params.stadiumId;
        if (stadiumId != 0) {
            this.stadium.space_id = stadiumId;
            this.isEdit = true;
        } else {
            this.stadium.space_id = '';
            this.isEdit = false;
        }

        this.stadium.card_ids = [];
        this.getStadiumCategoryList()
            .then(this.getAddCardList)
            .then(this.handleFilterCard)
            .then(res=>{
                if (stadiumId != 0) {
                    this.getInfo(stadiumId);
                }
            });
    }
}
</script>

<style lang="less" scoped>
.box {
    width: 100%;
    background-color: white;
    border: 1px solid #e0e3e9;

    .header-ya {
        width: 100%;
        background: #f7f7f7;
        height: 37px;
        padding: 0 20px;
        overflow: hidden;
        border-bottom: 1px solid #e0e3e9;

        .ya {
            font-size: 14px;
            line-height: 37px;
            font-weight: bold;
        }
    }

    .buddy {
        width: 100%;
        padding: 17px 44px 70px 44px;

        .tag-box {
            max-width: 630px;
            margin-bottom: 10px;
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .image-box {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;

            .photo-box {
                display: flex;
                flex-direction: row;

                .photo {
                    width: 400px;
                    height: 300px;
                    margin-top: 10px;
                }

                .delete-photo {
                    width: 45px;
                    height: 45px;
                    position: relative;
                    top: -10px;
                    left: -25px;
                    background-color: rgba(0, 0, 0, 0.4);
                    color: red;
                    border-radius: 50%;
                    overflow: hidden;
                    cursor: pointer;
                }
            }
        }


        .panel {
            max-width: 630px;
            border: 1px solid #e0e3e9;
            margin-top: 10px;

            .panel-title {
                border-bottom: 1px solid #e0e3e9;
                padding: 0 20px;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .panel-body {
                .panel-item {
                    margin: 10px 20px;
                    display: flex;
                    flex-direction: row;

                    .item-multipe-line {
                        .item-line {
                            display: flex;
                            flex-direction: row;

                            span {
                                margin-left: 10px;
                                display: inline-block;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
