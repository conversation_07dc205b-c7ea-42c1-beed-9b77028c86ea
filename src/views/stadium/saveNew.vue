<template>
  <div class="box">
    <div class="header">场地</div>
    <div class="buddy">
      <Form ref="formRef" :model="formPost" :rules="formRule" :label-width="120">
        <Form-item label="场地类型名称" prop="type_id">
          <Select v-model="formPost.type_id" style="max-width: 630px" :disabled="!!id" clearable filterable>
            <Option v-for="item in categoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </Form-item>
        <Form-item v-if="!!id" label="场地名称" prop="name">
          <Input v-model="formPost.name" placeholder="请输入..." style="max-width: 630px"></Input>
        </Form-item>
        <Form-item v-else label="场地" prop="names_string">
          <Input v-model="formPost.names_string" placeholder="请输入..." style="max-width: 630px"></Input>
          <Alert type="warning" style="margin-top: 13px; max-width: 630px" show-icon>
            多个场地，名称中间请用 , 进行分割，例如：“3号场地，4号场地”
          </Alert>
        </Form-item>
        <Form-item label="场次&定价">
          <Select v-model="strategyId" style="max-width: 630px" @on-change="dialogFlag = true" filterable>
            <Option v-for="item in strategyList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="订场限制" prop="max_hour">
          <span>用户单次订场时长不能大于</span>
          <Input-number
            v-model="formPost.max_hour"
            :min="0.5"
            :step="0.5"
            :precision="1"
            :active-change="false"
            size="small"
            style="margin: 0 10px"
          ></Input-number>
          <span>小时</span>
        </Form-item>
        <Form-item label="半场预订">
          <i-switch v-model="formPost.is_half" :true-value="1" :false-value="0">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </Form-item>
        <Form-item label="场次安排以及定价">
          <the-week-strategy
            ref="weekly"
            :planTimeSet.sync="planTimeSet"
            :tableDataAll.sync="tableDataAll"
            :hasHalf="formPost.is_half"
            :maxHours="formPost.max_hour"
            hasTemplate
            @emitSaveTemplate="handleSaveTemplate"
          ></the-week-strategy>
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit">提交</Button>
            <Button @click="handleCancel">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>

    <Modal v-model="dialogFlag" width="360">
      <p slot="header" style="color: #f60; text-align: center">
        <Icon type="ios-information-circle"></Icon>
        <span>切换方案</span>
      </p>
      <div style="text-align: center">
        <p>切换方案会覆盖场次和定价信息，</p>
        <p>确认切换？</p>
      </div>
      <div slot="footer" class="btn-box">
        <Button type="success" @click="handleStrategyChange">切换</Button>
        <Button @click="dialogFlag = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import TheWeekStrategy from './components/TheWeekStrategy.vue'
import axios from 'axios'
import { putWeek, getWeekJson } from '@/service/putOSS.js'
import { getOSSPath } from '@/utils/config.js'

export default {
  components: {
    TheWeekStrategy,
  },
  props: {
    id: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      formPost: {
        names: [],
        names_string: '',
        name: '',
        type_id: '',
        max_hour: 4,
        is_half: 0,
        schedule_price: {},
      },
      weekUrl: null,
      formRule: {
        type_id: [{ required: true, message: '请输选择场地类型!' }],
        name: [{ required: true, message: '请输入场地名称!', trigger: 'blur' }],
        max_hour: [{ required: true, message: '请输入订场限制!' }],
        names_string: [{ required: true, message: '请输入场地名称!', trigger: 'blur' }],
      },
      categoryList: [],
      strategyList: [],
      dialogFlag: false,
      strategyId: '',
      planTimeSet: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      },
      tableDataAll: [],
    }
  },
  async created() {
    this.getCategoryList()
    this.getStrategyList()
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    initSchedule() {
      this.formPost.schedule_price = {
        monday: {
          list: [],
        },
        tuesday: {
          list: [],
        },
        wednesday: {
          list: [],
        },
        thursday: {
          list: [],
        },
        friday: {
          list: [],
        },
        saturday: {
          list: [],
        },
        sunday: {
          list: [],
        },
      }
    },
    getCategoryList() {
      return this.$service.post('/Web/Space/getTypes').then((res) => {
        if (res.data.errorcode == 0) {
          this.categoryList = res.data.data.map((item) => {
            item.isChecked = false
            return item
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getStrategyList() {
      return this.$service
        .post('/Web/Space/getListSchedulePriceTemplate', {
          page: 1,
          page_size: 999,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.strategyList = res.data.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    setTableDataAll(strategy) {
      this.tableDataAll = []
      this.planTimeSet = {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      }
      if (typeof strategy.schedule_price === 'object') {
        for (const key in strategy.schedule_price) {
          if (strategy.schedule_price[key]) {
            const day = strategy.schedule_price[key]
            day.list.forEach((item, index) => {
              let halfFlag = true
              let allFlag = true

              let half = `非会员:${item.no_member_price.half.common}元/${item.no_member_price.half.holiday}元 `
              if (Array.isArray(item.member_price)) {
                item.member_price.forEach((member, index) => {
                  if (member.half.common === null || member.half.holiday === null) {
                    halfFlag = false
                  }
                  half += `会员价${index + 1}:${member.half.common}元/${member.half.holiday}元; `
                })
              }
              let all = `非会员:${item.no_member_price.all.common}元/${item.no_member_price.all.holiday}元 `
              if (Array.isArray(item.member_price)) {
                item.member_price.forEach((member, index) => {
                  if (member.all.common === null || member.all.holiday === null) {
                    allFlag = false
                  }
                  all += `会员价${index + 1}:${member.all.common}元/${member.all.holiday}元; `
                })
              }

              if (item.no_member_price.half.common === null || item.no_member_price.half.holiday === null) {
                halfFlag = false
              }

              if (item.no_member_price.all.common === null || item.no_member_price.all.holiday === null) {
                allFlag = false
              }

              item.id = key + '_' + index
              item.tabName = key
              item.time = `${item.start_time}~${item.end_time}`
              item.half = halfFlag ? half : '未设置'
              item.all = allFlag ? all : '未设置'
              item.noneMember = item.no_member_price
              item.member = item.member_price
              this.planTimeSet[key].push([item.start_time, item.end_time])
            })
            this.tableDataAll = [...this.tableDataAll, ...day.list]
          }
        }
      } else {
        this.initSchedule()
      }
    },
    async handleStrategyChange() {
      const option = this.strategyList.find((item) => item.id === this.strategyId)
      this.formPost.max_hour = option.max_hour
      this.formPost.is_half = option.is_half

      const strategy = { schedule_price: {} }
      for (const key in option.schedule_price) {
        if (Object.hasOwnProperty.call(option.schedule_price, key)) {
          const url = option.schedule_price[key]
          strategy.schedule_price[key] = await getWeekJson(url)
        }
      }

      this.setTableDataAll(strategy)
      this.dialogFlag = false
    },
    getInfo() {
      return this.$service.get('/Web/Space/getEditInfo?id=' + this.id).then(async (res) => {
        if (res.data.errorcode === 0) {
          this.formPost = {
            id: res.data.data.id,
            names: [],
            names_string: '',
            name: res.data.data.name,
            type_id: res.data.data.type_id,
            max_hour: Number(res.data.data.max_hour),
            is_half: res.data.data.is_half,
          }
          this.weekUrl = res.data.data.schedule_price

          const strategy = { schedule_price: {} }
          for (const key in this.weekUrl) {
            if (Object.hasOwnProperty.call(this.weekUrl, key)) {
              const url = this.weekUrl[key]
              strategy.schedule_price[key] = await getWeekJson(url)
            }
          }
          this.setTableDataAll(strategy)
        }
      })
    },
    handleSubmit() {
      if (!this.$refs.weekly.handleEditModalOk()) {
        return
      }
      if (this.tableDataAll.length === 0) {
        this.$Message.error('请设置场次安排以及定价!')
        return
      }
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.initSchedule()

          let flag = true
          this.tableDataAll.forEach((row) => {
            if (row.noneMember.all.common === null || row.noneMember.all.holiday === null) {
              flag = false
              return
            }
            if (this.formPost.is_half === 1 && (row.noneMember.half.common === null || row.noneMember.half.holiday === null)) {
              flag = false
              return
            }
            row.member.forEach((item) => {
              if (item.all.common === null || item.all.holiday === null) {
                flag = false
                return
              }
              if (this.formPost.is_half === 1 && (item.half.common === null || item.half.holiday === null)) {
                flag = false
                return
              }
            })
            this.formPost.schedule_price[row.tabName].list.push({
              start_time: row.time.split('~')[0],
              end_time: row.time.split('~')[1],
              no_member_price: row.noneMember,
              member_price: row.member,
            })
          })

          if (!flag) {
            this.$Message.error('请设置场次价格，不能为空!')
            return
          }

          let weekUrl = {}
          if (!this.id) {
            // if (this.formPost.names_string.indexOf('，') > 0) {
            //   this.formPost.names = this.formPost.names_string.split('，')
            // } else {
            //   this.formPost.names = this.formPost.names_string.split(',')
            // }
            const names = []
            const nameArrArr = this.formPost.names_string.split(',')
            nameArrArr.forEach((nameArr) => {
              names.push(...nameArr.split('，'))
            })
            this.formPost.names = names

            const camp = getOSSPath()
            weekUrl = {
              monday: `${camp}stadium_monday_${Date.now()}.json`,
              tuesday: `${camp}stadium_tuesday_${Date.now()}.json`,
              wednesday: `${camp}stadium_wednesday_${Date.now()}.json`,
              thursday: `${camp}stadium_thursday_${Date.now()}.json`,
              friday: `${camp}stadium_friday_${Date.now()}.json`,
              saturday: `${camp}stadium_saturday_${Date.now()}.json`,
              sunday: `${camp}stadium_sunday_${Date.now()}.json`,
            }
          } else {
            for (const key in this.weekUrl) {
              if (Object.hasOwnProperty.call(this.weekUrl, key)) {
                const url = this.weekUrl[key]
                weekUrl[key] = url.split('aliyuncs.com')[1]
              }
            }
          }

          const weekFullUrl = await putWeek(weekUrl, this.formPost.schedule_price)
          this.formPost.schedule_price = JSON.stringify(weekFullUrl)

          this.$service.post('/Web/Space/save', this.formPost).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.$router.back()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCancel() {
      this.$router.back()
    },
    async handleSaveTemplate(name) {
      const tempPost = {
        name,
        max_hour: this.formPost.max_hour,
        is_half: this.formPost.is_half,
        schedule_price: {
          monday: {
            list: [],
          },
          tuesday: {
            list: [],
          },
          wednesday: {
            list: [],
          },
          thursday: {
            list: [],
          },
          friday: {
            list: [],
          },
          saturday: {
            list: [],
          },
          sunday: {
            list: [],
          },
        },
      }
      this.tableDataAll.forEach((row) => {
        tempPost.schedule_price[row.tabName].list.push({
          start_time: row.time.split('~')[0],
          end_time: row.time.split('~')[1],
          no_member_price: row.noneMember,
          member_price: row.member,
        })
      })

      const camp = getOSSPath()
      const weekUrl = {
        monday: `${camp}strategy_monday_${Date.now()}.json`,
        tuesday: `${camp}strategy_tuesday_${Date.now()}.json`,
        wednesday: `${camp}strategy_wednesday_${Date.now()}.json`,
        thursday: `${camp}strategy_thursday_${Date.now()}.json`,
        friday: `${camp}strategy_friday_${Date.now()}.json`,
        saturday: `${camp}strategy_saturday_${Date.now()}.json`,
        sunday: `${camp}strategy_sunday_${Date.now()}.json`,
      }

      const weekFullUrl = await putWeek(weekUrl, tempPost.schedule_price)
      tempPost.schedule_price = JSON.stringify(weekFullUrl)

      this.$service.post('/Web/Space/addSchedulePriceTemplate', tempPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.getStrategyList()
          this.$Message.success(res.data.errormsg)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  min-height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;

  .header {
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    color: #333;
    background-color: #f7f7f7;
    width: 100%;
    background: #f7f7f7;
    height: 40px;
    padding: 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #e0e3e9;
  }

  .buddy {
    width: 80%;
    min-width: 721px;
    height: 100%;
    padding: 20px 40px;
    box-sizing: border-box;
  }
}

.btn-box {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
}
</style>
