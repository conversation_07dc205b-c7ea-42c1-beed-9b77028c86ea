<template>
  <Modal v-model="visible" width="500" @on-visible-change="handleChange">
    <div class="modal-buddy">
      <h2>本预订为长租预订, 共有 {{ props.count }} 场关联预订数据</h2>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleTodayCancel" :disabled="!props.modifyOne">仅取消本次预订</Button>
      <Button type="success" @click="handleToLongTermRentalCancel" :disabled="!props.modifyAll">批量取消预订</Button>
    </div>
  </Modal>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits } from 'vue';
import service from '@/service';
import { Message } from 'iview';

const props = defineProps({
  show: Boolean,
  count: Number,
  id: Number,
  modifyOne: Boolean,
  modifyAll: Boolean,
})
const emit = defineEmits(['update:show', 'on-today-cancel'])

// variables
const visible = ref(false)

// methods
const handleTodayCancel = () => {
  emit('update:show', false)
  emit('on-today-cancel')
}
const handleToLongTermRentalCancel = () => {
  service.post('/Web/SpaceOrderLong/checkSpaceLongLimit').then((res) => {
    if (res.data.errorcode === 0) {
      emit('update:show', false)
      const url = `${window.location.protocol}//${window.location.host}/booking/long-term-rental-cancel/${props.id}`
      window.open(url, '_self')
    } else {
      Message.error(res.data.errormsg)
    }
  })
}
const handleChange = (val) => {
  if (!val) {
    emit('update:show', false)
  }
}

watch(() => props.show, () => {
  if (props.show) {
    visible.value = true
  } else {
    visible.value = false
  }
}, { immediate: true })
</script>

<style lang="less" scoped>
.modal-buddy {
  text-align: center;
}
</style>