<template>
  <Modal v-model="visible" width="500" @on-visible-change="handleChange">
    <div class="modal-buddy">
      <h2>本预订有关联约课，请选择取消方式</h2>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleAllCancel">取消订场和约课</Button>
      <Button type="success" @click="handleOneTimePayCancel">仅取消约课</Button>
    </div>
  </Modal>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, getCurrentInstance } from 'vue';

const props = defineProps({
  show: Boolean,
  item: Object,
})
const emit = defineEmits(['update:show', 'on-all-cancel'])

// variables
const visible = ref(false)
const ins = getCurrentInstance()

// methods
const handleAllCancel = () => {
  emit('update:show', false)
  emit('on-all-cancel')
}
const handleOneTimePayCancel = () => {
  if (props.item.reservation_type === 1) {
    ins.proxy.$router.push({ name: '私教排课', params: { 
      courseId: props.item.pt_schedule_id,
      coachName: props.item.coach_name,
      username: props.item.username,
      cardName: props.item.card_name,
     } })
  } else {
    const url = `${window.location.protocol}//${window.location.host}/one-time-pay/cancel/${props.item.pt_schedule_id}/booking`
    window.open(url, '_self')
  }
  emit('update:show', false)
}
const handleChange = (val) => {
  if (!val) {
    emit('update:show', false)
  }
}

watch(() => props.show, () => {
  if (props.show) {
    visible.value = true
  } else {
    visible.value = false
  }
}, { immediate: true })
</script>

<style lang="less" scoped>
.modal-buddy {
  text-align: center;
}
</style>