<template>
  <div
    :class="[
      className,
      {
        'light-grid': isLight,
        'dark-grid': isDark,
      },
    ]"
    :style="grid"
    v-on="$listeners"
  >
    <Poptip v-model="visible" disabled width="150" style="width: 100%; height: 100%" transfer>
      <!-- <Poptip v-model="visible" :disabled="!isLight" width="150" style="width: 100%; height: 100%" transfer> -->
      <div style="width: 100%; height: 100%"></div>
      <div class="action" slot="content">
        <!-- <Tooltip content="预约" transfer> -->
        <div class="row-ti-box" @click="emitBookingShow">
          <img class="row-icon" src="@/assets/img/booking/booking.png" />
          <div class="row-tip">预约</div>
        </div>
        <!-- </Tooltip>
        <Tooltip content="休息" transfer> -->
        <div class="row-ti-box" @click="emitBookingRest">
          <img class="row-icon" src="@/assets/img/booking/rest.png" />
          <div class="row-tip">占用</div>
        </div>
        <!-- </Tooltip>
        <Tooltip content="重选" transfer> -->
        <div class="row-ti-box" @click="emitBookingReset">
          <img class="row-icon" src="@/assets/img/booking/reset.png" />
          <div class="row-tip">重选</div>
        </div>
        <!-- </Tooltip> -->
      </div>
    </Poptip>
  </div>
</template>

<script>
export default {
  props: {
    position: {
      type: String,
      required: true,
    },
    selectedGrids: {
      type: Object,
      required: true,
    },
    rowId: {
      type: Number,
      required: true,
    },
    grid: {
      type: String,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
    }
  },
  computed: {
    className() {
      if (this.position === 'all') {
        return 'row-other'
      } else if (this.position === 'up') {
        return 'row-other-up'
      } else if (this.position === 'down') {
        return 'row-other-down'
      }
    },
    isLight() {
      if (this.selectedGrids.start === this.index) {
        return true
      } else {
        return (
          this.selectedGrids.start !== null &&
          this.selectedGrids.site === this.position &&
          this.selectedGrids.rowId === this.rowId &&
          this.selectedGrids.start <= this.index &&
          this.index <= this.selectedGrids.end
        )
      }
    },
    isDark() {
      return (
        this.selectedGrids.start !== null &&
        (this.selectedGrids.site !== this.position ||
          this.selectedGrids.rowId !== this.rowId ||
          this.index < this.selectedGrids.start)
      )
    },
  },
  methods: {
    emitBookingShow() {
      this.visible = false
      this.$emit('emitBookingShow')
    },
    emitBookingRest() {
      this.visible = false
      this.$emit('emitBookingRest')
    },
    emitBookingReset() {
      this.visible = false
      this.$emit('emitBookingReset')
    },
  },
}
</script>

<style lang="less" scoped>
@regionHeight: 60px;

.row-other {
  border: 1px dotted #dddddd;
  margin: 1px;
  cursor: pointer;
}

.row-other-up {
  .row-other;
  height: calc(@regionHeight / 2 - 1px);
}

.row-other-down {
  .row-other;
  height: calc(@regionHeight / 2 - 1px);
  margin-top: calc(@regionHeight / 2 + 1px);
}

.light-grid {
  background-color: #e8fbf9;
  border: 1px dotted #7ae6df;

  /deep/ .ivu-poptip-rel {
    width: 100%;
    height: 100%;
  }
}

.dark-grid {
  background-color: #ececec;
  border: 1px dotted #eeeeee;
  cursor: not-allowed;
}

.action {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.row-ti-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 37px;
  cursor: pointer;

  .row-icon {
    width: 18px;
    height: 18px;
  }

  .row-tip {
    font-size: 12px;
    color: #1b1b1b;
    white-space: pre-wrap;
    text-align: center;
    margin-top: 7px;
  }
}
</style>
