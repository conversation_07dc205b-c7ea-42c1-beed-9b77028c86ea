<template>
  <div class="table-wrap">
    <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
    <footer class="box-foot">
        <div class="option-ctrl">
          <router-link style="color:white;" target="_self" to="/stadium/addClassRoom">
            <Button type="success" style="margin-right: 30px;">添加教室</Button>
          </router-link>
        </div>
        <Page
          @on-change="handlePage"
          :total="total"
          :current="currentPage"
          @on-page-size-change="pageSizeChanged"
          show-total
          placement="top"
          show-sizer
        ></Page>
     </footer>
  </div>
</template>
<script>
 import { formatDate } from 'utils';
export default {
  name: "ClassRoom",
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      columns: [
        { title: "教室名称", key: "classroom_name" },
        {
          title: "创建时间",
          key: "create_time",
          render: (h, params) => {
            return (
              <div>
                {formatDate(new Date(params.row.create_time*1000), 'yyyy-MM-dd HH:mm:ss')}
              </div>
            );
          }
        },
        {
          title: "选座功能",
          key: "seat_status",
          render: (h, params) => {
            return (
              <div>
                {params.row.seat_status === '1' ? '开启' : '不开启'}
              </div>
            );
          }
        },
        { title: "允许人数", key: "allow_number" },
        {
          title: "操作",
          key: "option",
          width: 150,
          render: (h, params) => {
            return (
              <div style="display: flex;flex-direction: row;justify-content: space-around;">
                <a
                  onClick={() => {
                    this.$router.push(
                      `/stadium/addClassRoom?id=${params.row.id}`
                    );
                  }}
                >
                  编辑
                </a>
                <a
                  style="color:red;"
                  onClick={() => {
                    this.deleteClassById(params.row.id);
                  }}
                >
                  删除
                </a>
              </div>
            );
          }
        }
      ],
      list: [],
      searchTxt: "",
      stadiumCategoryList: [],
      stadiumCategoryId: ""
    };
  },
  methods: {
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    },
    getList() {
      return this.$service
        .post("/Web/Classroom/get_list", {
          space_name: this.searchTxt,
          space_type_id: this.stadiumCategoryId,
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.total = parseInt(res.data.data.count);
            this.list = [];
            if (Array.isArray(res.data.data.list)) {
              this.list = res.data.data.list;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    deleteClassById(classroom_id) {
      this.$Modal.confirm({
        title: '提示',
        content: '确定删除教室?',
        onOk: () => {
           this.$service
            .post("/Web/Classroom/post_delete", {
              classroom_id
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success(res.data.errormsg);
                this.getList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
        onCancel() {}
      });
     
    }
  },
  mounted() {
    this.getList();
  }
};
</script>

<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }
    }
  }
}
</style>
