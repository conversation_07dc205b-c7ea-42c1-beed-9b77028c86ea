<template>
  <div class="long-term-rental-tasks">
    <Row class="box-head">
      <Col span="24" class="head-option">
      <Input v-model="searchParams.keyword" placeholder="会员姓名/电话" class="option-item" />
      <DatePicker v-model="dateRange" placeholder="预订时间" type="daterange" :clearable="false" transfer
        class="option-item" />
      <Select v-model="searchParams.space_type" placeholder="场地类型" clearable transfer class="option-item">
        <Option v-for="item in stadiumCategoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select v-model="searchParams.type" placeholder="动作" clearable transfer class="option-item">
        <Option v-for="(item, index) in actionList" :value="item.value" :key="index">{{ item.label }}</Option>
      </Select>
      <Select v-model="searchParams.status" placeholder="状态" clearable transfer class="option-item">
        <Option v-for="(item, index) in statusList" :value="item.value" :key="index">{{ item.label }}</Option>
      </Select>
      <Button icon="ios-search" type="primary" ghost @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="height-table" :columns="columns" :data="list" disabled-hover stripe></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col span="6" class="foot-option" style="justify-content: flex-start">
      <ExportButton url="/Web/SpaceOrderLong/getLongOrderRecords" :data="searchParams" />
      </Col>
      <Col span="18" class="foot-option">
      <Page @on-change="handlePage" :total="total" :current="searchParams.page_no"
        @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>

    <BigTableModal :big-visible.sync="numVisible" title="场次详情" :columns="numColumns" :list="numList" />
    <BigTableModal :big-visible.sync="errorVisible" title="失败原因" :columns="errorColumns" :list="errorList" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import service from '@/service';
import ExportButton from '@/components/form/ExportButton'
import { formatDate } from '@/utils'
import BigTableModal from './BigTableModal.vue'

const props = defineProps({
  clickCount: {
    type: Number,
    default: 0
  }
});

// variables
const dateRange = ref([]);
const searchParams = ref({
  type: null,
  keyword: '',
  space_type: null,
  start_time: '',
  end_time: '',
  status: null,
  page_no: 1,
  page_size: 10
});
const stadiumCategoryList = ref([]);
const actionList = ref([
  {
    value: 1,
    label: '长租'
  },
  {
    value: 2,
    label: '退订'
  }
]);
const statusList = ref([
  {
    value: 1,
    label: '数据处理中',
    color: '#2d8cf0'
  },
  {
    value: 2,
    label: '执行完成',
    color: '#19be6b'
  },
  {
    value: 3,
    label: '执行失败',
    color: '#ed4014'
  }
]);
const total = ref(0);
const list = ref([]);
const columns = ref([
  {
    title: '长租任务编号',
    key: 'batch_number',
  },
  {
    title: '操作时间',
    key: 'create_time',
  },
  {
    title: '会员/散客',
    key: 'username',
  },
  {
    title: '动作',
    key: 'action',
  },
  {
    title: '场地',
    key: 'space_name',
  },
  {
    title: '实付金额',
    key: 'amount',
  },
  {
    title: '场次数',
    key: 'number',
    render: (h, params) => {
      return h('a', {
        style: 'color: #2d8cf0',
        on: {
          click: () => {
            // const list = params.row.content
            // if (Array.isArray(list)) {
            //   numList.value = list.map(item => {
            //     return {
            //       date: item.date,
            //       time: `${item.start_time}-${item.end_time}`
            //     }
            //   })
            // }
            service.post('/Web/SpaceOrderLong/getLongOrderDetailLists', {
              batch_number: params.row.batch_number,
              type: params.row.type,
              id: params.row.relation_id
            }).then((res) => {
              if (res.data.errorcode === 0) {
                numList.value = res.data.data.map(item => {
                  return {
                    date: item.date,
                    time: `${item.start_time}-${item.end_time}`
                  }
                })
                numVisible.value = true
              }
            })
          }
        }
      }, params.row.number)
    }
  },
  {
    title: '备注',
    key: 'remark',
    render: (h, params) => {
      let shortRemark = params.row.remark || '';
      if (shortRemark.length > 15) {
        shortRemark = params.row.remark.substr(0, 20) + '...';
        return h('Tooltip', {
          props: {
            content: params.row.remark,
            transfer: true
          }
        }, shortRemark)
      } else {
        return h('div', shortRemark)
      }
    }
  },
  {
    title: '操作人',
    key: 'admin',
  },
  {
    title: '状态',
    width: 100,
    key: 'status_text',
    render: (h, params) => {
      const status = statusList.value.find(item => item.value == params.row.status)
      return h('span', {
        style: `color: ${status.color}`,
      }, status.label)
    }
  },
  {
    title: '操作',
    key: 'error',
    render: (h, params) => {
      if (params.row.status === 3) {
        return h('a', {
          on: {
            click: () => {
              const list = params.row.false_result;
              if (Array.isArray(list)) {
                errorList.value = list.map(item => {
                  return {
                    date: item.date,
                    time: `${item.start_time}-${item.end_time}`,
                    msg: item.msg
                  }
                })
              }
              errorVisible.value = true
            }
          }
        }, '失败原因')
      } else {
        return h('span', '-')
      }
    }
  },
]);

// methods
const getStadiumCategoryList = async () => {
  const res = await service.post('/Web/Space/getTypes')
  if (res.data.errorcode === 0) {
    stadiumCategoryList.value = res.data.data
  }
}
const getList = () => {
  if (Array.isArray(dateRange.value) && dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    searchParams.value.start_time = formatDate(dateRange.value[0], 'yyyy-MM-dd')
    searchParams.value.end_time = formatDate(dateRange.value[1], 'yyyy-MM-dd')
  } else {
    searchParams.value.start_time = ''
    searchParams.value.end_time = ''
  }
  service.post('/Web/SpaceOrderLong/getLongOrderRecords', searchParams.value).then((res) => {
    if (res.data.errorcode === 0) {
      list.value = res.data.data.list
      total.value = Number(res.data.data.count)
    }
  })
}
const handleSearch = () => {
  searchParams.value.page_no = 1
  getList()
}
const handlePage = (page) => {
  searchParams.value.page_no = page
  getList()
}
const pageSizeChanged = (pageSize) => {
  searchParams.value.page_no = 1
  searchParams.value.page_size = pageSize
  getList()
}

// modal
const numVisible = ref(false);
const numColumns = ref([
  {
    title: '日期',
    key: 'date',
  },
  {
    title: '场次时间',
    key: 'time',
  },
]);
const numList = ref([]);
const errorVisible = ref(false);
const errorColumns = ref([
  {
    title: '日期',
    key: 'date',
    width: 100
  },
  {
    title: '场次时间',
    key: 'time',
    width: 100
  },
  {
    title: '失败原因',
    key: 'msg',
    render: (h, params) => {
      return h('Tooltip', {
        props: {
          content: params.row.msg,
          transfer: true
        }, style: {
          width: '100%'
        }
      }, params.row.msg.substring(0, 30))
    }
  },
])
const errorList = ref([])

// created
// set dateRange to recent 7 days
dateRange.value = [
  new Date(new Date().getTime() - 3600 * 1000 * 24 * 6),
  new Date()
]
getStadiumCategoryList();
getList();

// watch
watch(() => props.clickCount, () => {
  searchParams.value.page_no = 1
  getList();
}, { immediate: true })
</script>

<style lang="less" scoped>
.long-term-rental-tasks {

  .box-head {
    border: none;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-item {
        width: 200px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border: none;
    height: calc(100vh - 400px);
    overflow-y: auto;

    .height-table {
      max-height: calc(100vh - 400px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }

  .box-foot {
    border: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.height-table ::v-deep(.ivu-table-overflowX) {
  overflow-x: hidden !important;
}
</style>