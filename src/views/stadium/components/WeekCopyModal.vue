<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="复制场次信息"
         :width="750">
    <Form ref="postForm"
          :model="postForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item label="复制" prop="from">
        <Select v-model="postForm.from">
            <Option v-for="item in weekList" :key="item.value" :label="item.name" :value="item.value">
            </Option>
        </Select>
      </Form-item>
      <Form-item label="粘贴到" prop="to">
        <Select v-model="postForm.to" multiple>
            <Option v-for="item in weekList" :key="item.value" :label="item.name" :value="item.value">
            </Option>
        </Select>
        <div>粘贴的周期内已有场次将被全部覆盖替换</div>
      </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="handleCopyConfirm">确定</Button>
      <Button
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
const WEEKLIST = [
  {
    name: '周一',
    value: 'monday',
  },
  {
    name: '周二',
    value: 'tuesday',
  },
  {
    name: '周三',
    value: 'wednesday',
  },
  {
    name: '周四',
    value: 'thursday',
  },
  {
    name: '周五',
    value: 'friday',
  },
  {
    name: '周六',
    value: 'saturday',
  },
  {
    name: '周日',
    value: 'sunday',
  },

]
  export default {
    name: 'WeekCopyModal',
    props: {
      value: {
        type: Boolean
      },
      from: {
        type: String,
        default: 'monday'
      }
    },
    data() {
      return {
        weekList: [...WEEKLIST],
        postForm: {
          from: '',
          to: []
        }
      }
    },
    computed: {
      showModal: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showModal(val) {
        if (!val) {
          this.$refs.postForm.resetFields()
        }
      },
      'from': {
        handler(val) {
          this.postForm.from = val
        },
        immediate: true
      }
    },
    created() {
    },
    methods: {
      handleCopyConfirm() {
        this.$emit('on-confirm', this.postForm)
      }
    }
  }
</script>

<style scoped>

</style>
