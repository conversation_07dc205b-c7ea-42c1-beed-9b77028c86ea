<template>
  <div class="booking-box">
    <div class="search-box">
      <DatePicker
        :value="searchPost.date"
        type="date"
        style="width: 300px; min-width: 200px"
        :clearable="false"
        @on-change="handleDateChange"
        transfer
      />
      <SaleSelect v-model="searchPost.coach_id" 
        placeholder="请选择教练" 
        isPtCoach 
        :isSwimCoach="false" 
        showCoachType
        :isMembership="false" 
        :label-in-value="true" 
        @on-change="changeSale" 
        style="margin-left: 10px; width: 300px"></SaleSelect>
      <div class="tag-box-scroll">
        <category-tag
          v-for="cat in categoryList"
          :key="'tag_' + cat.id"
          :cat="cat"
          style="margin: 0 10px"
          @click="handleTagClick(cat)"
          @fetchCategoryList="reloadCategoryList"
        />
      </div>
      <router-link to="/stadium/categorySave">
        <Button type="text">新增场地类型</Button>
      </router-link>
    </div>
    <div class="legend-box">
      <div class="legend">
        <div class="legend-color" style="background-color: #d2f6f4; border: 1px dotted #36d9cf"></div>
        <div class="legend-label">选中</div>
      </div>
      <div class="legend" v-for="(who, index) in [1, 2, 3]" :key="'legend_' + index">
        <div class="legend-color" :style="{ backgroundColor: statusColorList[index] }"></div>
        <div class="legend-label">{{ statusNameList[index] }}</div>
      </div>
      <div class="legend">
        <div class="legend-color" style="background-color: #eeeeee"></div>
        <div class="legend-label">场地休息不可订</div>
      </div>
      <div class="legend">
        <div class="legend-color" style="background-color: #70b603"></div>
        <div class="legend-label">灯光已开</div>
      </div>
      <div style="flex-grow: 1">
        <Poptip style="float: right" transfer>
          <div slot="content">
            <h3>订场信息显示</h3>
            <CheckboxGroup v-model="showLabelValue" @on-change="handleShowLabelChange" style="display: flex; flex-direction: column">
              <Checkbox v-for="(label, index) in showLabelList" :key="index" :label="index" :disabled="showLabelValue.length >= 2 && !showLabelValue.includes(index)">{{ label }}</Checkbox>
            </CheckboxGroup>
          </div>
          <Icon type="md-settings" size="16" style="cursor: pointer" />
        </Poptip>
      </div>
    </div>
    <div class="grid-box" @click.stop>
      <div class="grid-header-row">
        <div class="grid-header-one">
          <div class="date">时间</div>
          <div class="line"></div>
          <div class="region">场地</div>
        </div>
        <div class="grid-header" ref="costa" :style="gridHeaderStyle">
          <div></div>
          <template v-for="(time, index) in timeList">
            <div class="grid-header-other" v-if="index % 2 === 0" :key="'time_' + index">
              <!-- <div class="scratch">
                  <div :class="index?'':'small-line'"></div>
                  <div class="big-line"></div>
                  <div class='small-line'></div>
                </div> -->
              <div class="hour">
                <div :class="index === 0 ? 'time-box' : ''">
                </div>
                <div class="time-box">
                  <div class="long-line"></div>
                  <div>{{ time }}</div>
                </div>
                <div class="time-box" style="width: 2px">
                  <div :class="index !== timeList.length - 2 ? 'short-line' : ''"></div>
                  <div>{{ timeList[index + 1] }}</div>
                </div>
              </div>
            </div>
          </template>
          <div></div>
        </div>
      </div>
      <div class="grid-buddy-row">
        <div class="grid-buddy-one-box" ref="coffee">
          <div
            v-for="(row, index) in list"
            class="grid-buddy-one"
            :key="'row_' + row.id"
            :style="{
              gridArea: `${index + 1} / 1 / span 1 / span 1`,
            }"
          >
            <Poptip trigger="hover" width="200" offset="0" placement="right" transfer>
              <div class="label">{{ row.name }}</div>
              <div class="action" slot="content">
                <div class="row-ti-box" @click="handleLightBulbSwitch(row.id)">
                  <img class="row-icon" src="@/assets/img/booking/lightbulb.png" />
                  <div class="row-tip">灯光</div>
                </div>
                <!-- <Tooltip content="编辑" transfer> -->
                <!-- <router-link :to="`/stadium/saveNew/${row.id}`"> -->
                  <div class="row-ti-box" @click="handleToSaveStadium($event, row.id)">
                    <img class="row-icon" src="@/assets/img/booking/edit-blue.png" />
                    <div class="row-tip">编辑</div>
                  </div>
                <!-- </router-link> -->
                <!-- </Tooltip>
                <Tooltip content="休息" transfer> -->
                <div class="row-ti-box" @click="handleRestClick(row)">
                  <img class="row-icon" src="@/assets/img/booking/rest.png" />
                  <div class="row-tip">占用</div>
                </div>
                <!-- </Tooltip>
                <Tooltip content="节假日调价" transfer> -->
                <router-link :to="`/stadium/holydaySettings/fixed/${row.id}`">
                  <div class="row-ti-box">
                    <img class="row-icon" src="@/assets/img/booking/exchange.png" />
                    <div class="row-tip">临时调价</div>
                  </div>
                </router-link>
                <!-- </Tooltip>
                <Tooltip content="删除" transfer> -->
                <div class="row-ti-box" @click="handleStadiumDelete(row.id)">
                  <img class="row-icon" src="@/assets/img/booking/delete-blue.png" />
                  <div class="row-tip">删除</div>
                </div>
                <!-- </Tooltip> -->
              </div>
            </Poptip>
            <div v-if="row.lightBulb === 0" class="light-switch-all"></div>
            <div v-else-if="row.lightBulb === 1" class="light-switch-half"></div>
            <div v-else-if="row.lightBulb === 2" class="light-switch-half-down"></div>
            <template v-else-if="row.lightBulb === 3">
              <div class="light-switch-half"></div>
              <div class="light-switch-half"></div>
            </template>
          </div>
        </div>
        <div
          id="scrollBar"
          class="grid-buddy"
          @scroll="handleGridScroll"
          :style="{
            'grid-template-columns': `10px 10px repeat(${timeList.length}, minmax(50px, 1fr)) 20px`,
            'grid-template-rows': `repeat(${list.length}, 60px)`,
          }"
        >
          <div class="time-line" :style="timeLineStyle">
            <div class="line"></div>
          </div>
          <template v-for="(row, index) in list">
            <template v-if="row.hasHalf">
              <template v-for="(grid, gridIdx) in row.gridList">
                <the-row-other
                  :key="'grid_up_' + index + '_' + gridIdx"
                  position="up"
                  :selectedGrids="selectedGrids"
                  :rowId="row.id"
                  :grid="grid"
                  :index="gridIdx"
                  :ref="'row_other_up_' + row.id + '_' + gridIdx"
                  @click="handleGridClick(row.id, gridIdx, 'up')"
                  @mouseover="handleGridMove(row.id, gridIdx, 'up')"
                  @emitBookingShow="handleBookingShow"
                  @emitBookingRest="handleBookingRest"
                  @emitBookingReset="handleBookingReset"
                />
                <the-row-other
                  :key="'grid_down_' + index + '_' + gridIdx"
                  position="down"
                  :selectedGrids="selectedGrids"
                  :rowId="row.id"
                  :grid="grid"
                  :index="gridIdx"
                  :ref="'row_other_down_' + row.id + '_' + gridIdx"
                  @click="handleGridClick(row.id, gridIdx, 'down')"
                  @mouseover="handleGridMove(row.id, gridIdx, 'down')"
                  @emitBookingShow="handleBookingShow"
                  @emitBookingRest="handleBookingRest"
                  @emitBookingReset="handleBookingReset"
                />
              </template>
              <the-row-light
                v-for="(order, orderIdx) in row.orderList"
                :key="'order_' + index + '_' + orderIdx"
                :order="order"
                :showLongTermRentalTask="canShowLongTermTask(order)"
                @click="handleLightClick(order)"
                @emitBookingRefund="handleBookingRefund"
                :showLabelValue="showLabelValue"
              />
            </template>
            <template v-else>
              <the-row-other
                v-for="(grid, gridIdx) in row.gridList"
                :key="'grid_' + index + '_' + gridIdx"
                position="all"
                :selectedGrids="selectedGrids"
                :rowId="row.id"
                :grid="grid"
                :index="gridIdx"
                :ref="'row_other_' + row.id + '_' + gridIdx"
                @click="handleGridClick(row.id, gridIdx, 'all')"
                @mouseover="handleGridMove(row.id, gridIdx, 'all')"
                @emitBookingShow="handleBookingShow"
                @emitBookingRest="handleBookingRest"
                @emitBookingReset="handleBookingReset"
              />
              <the-row-light
                v-for="(order, orderIdx) in row.orderList"
                :key="'order_' + index + '_' + orderIdx"
                :order="order"
                :showLongTermRentalTask="canShowLongTermTask(order)"
                @click="handleLightClick(order)"
                @emitBookingRefund="handleBookingRefund"
                :showLabelValue="showLabelValue"
              />
            </template>
            <div
              class="disabled-time"
              :style="dis"
              v-for="(dis, disIdx) in row.disabledList"
              :key="'dis_' + index + '_' + disIdx"
            >
              <Tooltip v-if="!row.disabledPositionList[disIdx].expand" theme="light" style="width: 100%; height: 100%" transfer>
                <div class="tip" slot="content">
                  <div class="tip-box">
                    <div class="tip-info">
                      <div class="info-title">{{ row.disabledPositionList[disIdx].name }}</div>
                      <div class="info-label">
                        <Icon type="md-time" />
                        <span class="info-label-text">{{ row.disabledPositionList[disIdx].start_time }}~{{ row.disabledPositionList[disIdx].end_time }}</span>
                      </div>
                      <div class="info-label">
                        <Icon type="md-person" />
                        <span class="info-label-text">{{ row.disabledPositionList[disIdx].userinfo }}</span>
                      </div>
                      <div class="info-label">
                        <Icon type="md-bookmark" />
                        <span class="info-label-tag">{{ row.disabledPositionList[disIdx].type }}</span>
                      </div>
                      <div class="info-label">
                        <!-- <Icon type="logo-usd" /> -->
                        <img src="@/assets/img/booking/money.png" style="width: 10px; height: 10px" />
                        <span class="info-label-text">{{ row.disabledPositionList[disIdx].amount }} 元</span>
                      </div>
                    </div>
                    <div class="remark" v-if="row.disabledPositionList[disIdx].remarks">
                      <div class="tip-label">
                        <Icon class="icon-mr" type="ios-information-circle" />
                        备注
                      </div>
                      <div class="remark-con">{{ row.disabledPositionList[disIdx].remarks }}</div>
                    </div>
                  </div>
                  <div class="tip-btn">
                    <Button type="text" size="small" @click="handleRestCancel(row.id, row.disabledPositionList[disIdx])">
                      <span style="color: red">取消占用</span>
                    </Button>
                  </div>
                </div>
                <div class="rest-box" :style="{ height: row.disabledPositionList[disIdx].height }">
                  <div class="rest-left-top" v-if="row.disabledPositionList[disIdx].remarks">
                    <div class="icon-wrap">
                      <Icon type="ios-information-circle" />
                    </div>
                  </div>
                  <div style="width: 100%;">
                    {{ row.disabledPositionList[disIdx].type }}
                  </div>
                </div>
              </Tooltip>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="button-box">
      <!-- <router-link class="button-item" to="/stadium/saveNew"> -->
      <div class="button-item">
        <Button class="button-height" type="primary" @click="handleToSaveStadium">新增场地</Button>
      </div>
      <!-- </router-link> -->
      <div class="button-item" v-if="longTermAuthorities.show">
        <Button class="button-height" style="border: 1px solid #1cd4c9" @click="handleToLongTermRental">长租订场</Button>
      </div>
      <Button class="button-height" @click="goHolidaySetList()" style="margin-right: 16px; border: 1px solid #1cd4c9">临时调价</Button>
      <!-- <router-link class="button-item" to="/stadium/strategyList"> -->
      <div class="button-item" @click="handleToStrategyList">
        <Button class="button-height" style="border: 1px solid #3dabff">排场&定价方案</Button>
      </div>
      <div class="button-item" @click="handleToSetMemberPrice">
        <Button class="button-height" style="border: 1px solid #3dabff">设置场地会员价</Button>
      </div>
      <!-- </router-link> -->
      <Button class="button-height" style="border: 1px solid #f5b57d" @click="handleTipShow">订场须知</Button>
    </div>
    <!-- <Drawer :closable="false" :mask-closable="false" width="640" v-model="detailFlag"> -->
    <Modal
      v-model="detailFlag"
      :title="bookingModalTitle"
      width="1200"
      :mask-closable="false"
    >
      <!-- <div class="modal-header">
        <h3>{{ currentStadium.isDetail ? '预约详情' : '预约' }}</h3>
      </div> -->
      <div class="modal-buddy" v-if="detailFlag && !refundLabel">
        <!-- <h2 style="text-align: center; margin-bottom: 20px">{{ currentStadium.name }}</h2> -->
        <Form :model="currentStadium" class="modal-form" :label-width="120">
          <div class="modal-box">
            <div class="modal-divider">
              <Form-item label="场地名称: ">{{ currentStadium.name }}</Form-item>
              <Form-item v-if="currentStadium.isDetail" label="订单号: ">{{ currentStadium.order_sn }}</Form-item>
              <Form-item label="日期: ">{{ searchPost.date }} {{ currentStadium.between }}</Form-item>
              <Form-item label="场地大小: ">
                <RadioGroup v-model="currentStadium.isHalf" @on-change="changeIsHalf">
                  <Radio :label="0" :disabled="currentStadium.isDetail || !currentStadium.hasWhole">全场</Radio>
                  <Radio
                    :label="1"
                    :disabled="currentStadium.isDetail || currentStadium.openHalf === 0"
                    style="margin-left: 20px"
                  >
                    半场
                  </Radio>
                </RadioGroup>
              </Form-item>
              <Form-item label="客户类型: " prop="isMember">
                <RadioGroup v-model="currentStadium.isMember" @on-change="handleCustomerTypeChange">
                  <Radio :label="1" :disabled="currentStadium.isDetail">会员</Radio>
                  <Radio :label="2" style="margin-left: 20px" :disabled="currentStadium.isDetail">散客</Radio>
                </RadioGroup>
              </Form-item>
              <Form-item v-if="currentStadium.isMember == 1" label="会员: ">
                <UserPtSearchNew 
                  v-if="!currentStadium.isDetail"
                  ref="userSearchRef" 
                  @on-change="userSelected" 
                  @on-class-change="classChange" 
                  :isOneToMore="false"
                  :search="userSearch" 
                  :isUserId="isUserId" 
                  style="width: 300px"
                  @isUserId="(val)=> isUserId = val" ></UserPtSearchNew>
                <span v-else>{{ this.currentStadium.username }}</span>
              </Form-item>
              <Form-item v-if="currentStadium.isMember == 2" label="电话: ">
                <Input
                  v-if="!currentStadium.isDetail"
                  v-model="currentStadium.phone"
                  placeholder="请输入电话号码..."
                  style="width: 300px"
                />
                <span v-else>{{ this.currentStadium.phone }}</span>
              </Form-item>

              <Form-item label="场次: ">
                <div v-for="(price, $index) in currentStadium.schedule_price" :key="'price_' + $index">
                  {{ price.start_time }}~{{ price.end_time }} 价格 {{ Number(price.price).toFixed(2) }} 元
                </div>
              </Form-item>
              <Form-item label="折扣券抵扣: " v-if="currentStadium.isMember == '1' && currentStadium.type == 1">
                <div style="display: flex; align-items: center">
                  <span v-if="selectedDiscount && selectedDiscount.discount_amount">
                    {{ selectedDiscount.discount_amount }}
                  </span>
                  <span v-else>{{ discountText }}</span>
                  <Button
                    v-if="search && !currentStadium.isDetail"
                    type="text"
                    @click="
                      () => {
                        this.showDiscount = true
                      }
                    "
                  >
                    全部折扣券
                  </Button>
                </div>
              </Form-item>
              <!-- 总计金额是扣除优惠卷之后的金额 -->
              <Form-item label="订场费用合计: ">
                <span style="font-weight: bold; color: red">
                  {{ currentStadium.isDetail ? Number(currentStadium.total_price).toFixed(2) : Number(showPrice).toFixed(2) }}元
                </span>
              </Form-item>
              <Form-item>
                <template #label>
                  <div style="display: flex; justify-content: flex-end; align-items: center">
                    <span>教练</span>
                    <Tooltip trigger="hover" placement="top">
                      <Icon type="md-help-circle" size="16" style="margin: 0 5px" />
                      <template #content>
                        <span>若需要约课, 请选择教练</span>
                      </template>
                    </Tooltip>
                    <span>: </span>
                  </div>
                </template>
                <Select v-if="!currentStadium.isDetail" v-model="currentStadium.coach_id" style="width: 220px" @on-change="coachChanged" transfer clearable filterable>
                  <Option v-for="coach in filteredCoaches" :key="coach.coach_id" :value="coach.coach_id">{{coach.name}}</Option>
                </Select>
                <span style="line-height: 36px;" v-else>{{ this.currentStadium.ssl_status !== 1 ? this.currentStadium.coach_name : '无' }}</span>
                <Poptip trigger="hover" v-if="currentStadium.coach_id" width="300">
                  <Tag color="green" style="margin-left: 10px">空闲时间</Tag>
                  <div slot="content">
                    <h2 v-if="coachFreeTime.length" style="margin-bottom: 10px">空闲时间</h2>
                    <div v-else>暂无空闲时间</div>
                    <div style="display: flex; flex-wrap: wrap">
                      <!-- <Tag v-for="(time, index) in coachFreeTime" :key="index">{{ time[0] }}~{{ time[1] }}</Tag> -->
                      <span style="margin: 4px 10px" v-for="(time, index) in coachFreeTime" :key="index">{{ time[0] }}~{{ time[1] }}</span>
                    </div>
                  </div>
                </Poptip>
              </Form-item>
              
              <template v-if="currentStadium.isDetail && currentStadium.card_name && currentStadium.ssl_status !== 1">
                <Form-item label="预约: ">
                  <RadioGroup v-model="currentStadium.reservation_type">
                    <Radio :label="1" disabled>用卡预约</Radio>
                    <Radio :label="2" disabled>直接付费预约</Radio>
                  </RadioGroup>
                </Form-item>
                <Form-item label="课程: " class="selects">
                  <span>{{ currentStadium.card_name }}</span>
                </Form-item>
                <Form-item label="上课时间">
                  <span>{{ currentStadium.course_time }}</span>
                </Form-item>
              </template>
              <template v-else-if="currentStadium.coach_id">
                <Form-item label="预约: ">
                  <RadioGroup v-model="currentStadium.reservation_type" @on-change="handleTypeChanged">
                    <Radio :label="1">用卡预约</Radio>
                    <Radio :label="2">直接付费预约</Radio>
                  </RadioGroup>
                </Form-item>
                <Form-item label="课程: " class="selects" v-if="currentStadium.isMember == 1 && selectedUser && currentStadium.reservation_type === 1">
                  <Select v-if="selectedUser.card_list.length" v-model="currentStadium.card_index" style="width: 300px" @on-change="cardSelected" transfer>
                    <Option v-for="(card, index) in selectedUser.card_list" :key="index" :value="index">
                      {{card.name}}
                    </Option>
                  </Select>
                  <div v-else style="text-align: center">
                    <img src="~assets/img/stat_null.png" mode="widthFix" style="width: 208px" alt="暂无数据" />
                    <Alert type="error" style="margin-top: 20px;">您暂无对应课程, 无法预约!</Alert>
                  </div>
                </Form-item>
                <Form-item label="课程: " class="selects" v-if="currentStadium.reservation_type === 2">
                  <Select v-if="oneTimePayCourseList.length" v-model="currentStadium.card_index" style="width: 300px" @on-change="cardSelected" transfer>
                    <Option v-for="(course, index) in oneTimePayCourseList" :key="index" :value="index">
                      {{ course.name }} (时长{{ course.class_duration }}分钟, 标准价{{ course.single_price }}元)
                    </Option>
                  </Select>
                  <div v-else style="text-align: center">
                    <img src="~assets/img/stat_null.png" mode="widthFix" style="width: 208px" alt="暂无数据" />
                    <Alert type="error" style="margin-top: 20px;">此教练不支持单节付费课方案, 无法预约!</Alert>
                  </div>
                </Form-item>
                <Form-item label="上课时间: " v-if="radioTimeData">
                  <!-- <radioTime :date="new Date(searchPost.date)" :data="radioTimeData" :courseActiveTime="courseActiveTime" v-on:selectedTime="classTimeSelected"></radioTime> -->
                  <Select v-model="currentStadium.begin_date" style="width: 300px">
                    <Option v-for="(time, index) in radioTimeData" :key="index" :value="time.start">
                      {{ time.start }} ~ {{ time.end }}
                    </Option>
                  </Select>
                </Form-item>
              </template>

              <template v-if="currentStadium.reservation_type === 2 && oneTimePayCourseList.length && oneTimePayPrice">
                <!-- <Form-item label="标准价格">
                  <div class="price-box" style="font-size: 20px">{{ oneTimePayPrice?.original_amount }}</div>
                </Form-item> -->
                <Form-item label="持卡优惠: " v-if="oneTimePayPrice">
                  <div class="price-box">
                    <!-- <Tag v-if="oneTimePayPrice?.card_name" size="small">{{ oneTimePayPrice?.card_name }}</Tag> -->
                    <span style="color: red; font-size: 14px; margin-right: 10px">{{ oneTimePayPrice?.discount_amount }}</span>
                    <span v-if="oneTimePayPrice?.card_name" style="font-size: 14px; color: #999">{{ oneTimePayPrice?.card_name }}</span>
                  </div>
                </Form-item>
                <Form-item label="约课费用合计: ">
                  <div class="price-box" style="color: red; font-size: 16px;">{{ Number(oneTimePayPrice?.pay_amount).toFixed(2) }}</div>
                </Form-item>
              </template>
            </div>
            <div class="modal-divider"> 
              <Form-item label="订场实付: ">
                <Input-number
                  :min="0"
                  :precision="2"
                  :active-change="false"
                  :disabled="true"
                  style="width: 300px"
                  v-model="currentStadium.fee"
                  v-if="currentStadium.isDetail"
                />
                <Input-number
                  v-else
                  :min="0"
                  :precision="2"
                  :active-change="false"
                  v-model="allPrice"
                  style="width: 300px"
                  :disabled="currentStadium.isDetail || !reservePriceEditAuth"
                />
              </Form-item>
              <Form-item label="约课实付: " v-if="currentStadium.ssl_status !== 1 && currentStadium.reservation_type === 2">
                <Input-number
                  :disabled="currentStadium.isDetail"
                  :min="0"
                  :precision="2"
                  :active-change="false"
                  v-model="cptPrice"
                  style="width: 300px"
                />
              </Form-item>
              <Form-item label="总计: ">
                <span style="font-weight: bold; color: red">
                  {{ 
                    currentStadium.isDetail ? 
                    (currentStadium.ssl_status === 1 ? Number(currentStadium.total_price).toFixed(2) : Number(Number(currentStadium.total_price) + Number(cptPrice)).toFixed(2)) : 
                    Number(finalPrice).toFixed(2) 
                  }}元
                </span>
              </Form-item>
              <Form-item v-if="isRefund && currentStadium.type == 1" label="应退比例" prop="refund_rate" style="color: red;">{{(currentStadium.refund_rate*100).toFixed(2)}}%</Form-item>
              <template v-if="isRefund">
                <Form-item v-if="currentStadium.status != 2" :label="currentStadium.status == 0 ? '支付方式' : '退款方式'">
                  <pay-type-list
                    v-model="currentStadium.new_pay_type"
                    :amount="currentStadium.fee"
                    :sqbOption="{ describe: `订场[${currentStadium.name}]`, serviceType: 2, isEqual: false }"
                    :userId="currentStadium.isMember == 1 ? currentStadium.user_id : -3"
                    :showCardPay="currentStadium.isMember == 1"
                    :isRefundNeedSQB="currentStadium.isRefundNeedSQB"
                    :isRefund="true"
                    :isMaxAmount="currentStadium.from == 2"
                  />
                </Form-item>
              </template>

              <Form-item v-else label="支付方式: ">
                <pay-type-list
                  ref="payTypeListRef"
                  v-if="currentStadium.coach_id && currentStadium.reservation_type === 2"
                  v-model="oneTimePayType"
                  :amount="finalPrice"
                  :sqbOption="{ describe: `订场和私教课预约[${coachName}]`, serviceType: 2, isEqual: false }"
                  :userId="currentStadium.user_id"
                  :showCardPay="true"
                  :showOnlinePay="false"
                  :isMaxAmount="true"
                  style="width: 100%"
                  @on-card-change="handleCardChange"
                />
                <pay-type-list
                  v-else
                  ref="payTypeListRef2"
                  v-model="currentStadium.new_pay_type"
                  :amount="currentStadium.isDetail ? currentStadium.fee : allPrice"
                  :sqbOption="{ describe: `订场[${currentStadium.name}]`, serviceType: 2, isEqual: false }"
                  :userId="currentStadium.isMember == 1 ? currentStadium.user_id : -3"
                  :showCardPay="currentStadium.isMember == 1"
                  :showOnlinePay="false"
                  :disabled="currentStadium.isDetail"
                  :needShowDragonFly="!currentStadium.isDetail"
                />
              </Form-item>
              <Form-item label="备注: ">
                <Input
                  v-model.trim="currentStadium.remarks"
                  type="textarea"
                  :disabled="currentStadium.isDetail"
                  :maxlength="150"
                  style="width: 300px"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  :placeholder="currentStadium.isDetail ? '' : '150个字以内'"
                />
              </Form-item>

              <Form-item v-if="currentStadium.isDetail" label="状态: ">
                <span :style="{ color: statusColorList[currentStadium.status] }">
                  {{ statusNameList[currentStadium.status] }}
                </span>
              </Form-item>
              <FormItem v-if="!currentStadium.isDetail">
                <Button type="success" @click="handleBookingSubmit" :disabled="currentStadium.isDetail">支付</Button>
                <Button @click="handleBookingCancel" style="margin-left: 40px" :disabled="currentStadium.isDetail">
                  取消订场
                </Button>
              </FormItem>
              <FormItem v-else>
                <Button v-if="isRefund" type="success" style="margin-right: 40px" @click="handleRefund">
                  {{ refundLabel }}
                </Button>
                <Button @click="handleBookingCancel">关闭</Button>
              </FormItem>
            </div>
          </div>
        </Form>
      </div>
      <div class="modal-buddy" v-else>
        <Form :model="currentStadium" class="modal-form" :label-width="120">
          <div class="modal-box">
            <div class="modal-divider form-no-bottom">
              <Form-item label="客户姓名" prop="username">{{ currentStadium.username }}</Form-item>
              <Form-item label="联系方式" prop="phone">{{ currentStadium.phone }}</Form-item>
              <Form-item label="订单编号" prop="orderCode">{{ currentStadium.order_sn }}</Form-item>

              <Divider></Divider>

              <Form-item label="场地名称" prop="stadium">{{ currentStadium.name }}</Form-item>
              <Form-item label="场地类型" prop="stadiumCategory">{{ currentStadium.space_type_name }}</Form-item>
              <!-- <Form-item label="日期" prop="reservationDay"></Form-item> -->
              <Form-item label="时间" prop="reservationTime">{{ currentStadium.date }} {{ currentStadium.between }}</Form-item>
              <Form-item label="实收金额" prop="price">{{ Number(currentStadium.fee).toFixed(2) }}</Form-item>
              <!-- <Form-item label="支付状态" prop="payment">{{ currentStadium.payment }}</Form-item> -->
              <Form-item v-if="isRefund && currentStadium.type == 1" label="应退比例" prop="refund_rate" style="color: red;">{{(currentStadium.refund_rate*100).toFixed(2)}}%</Form-item>
              <Form-item label="应退金额" style="color: red;">{{ Number(currentStadium.should_refund_amount).toFixed(2) }}</Form-item> 

              
              <template v-if="currentStadium.ssl_status === 0">
                <Divider></Divider>

                <Form-item label="课程名称">{{ currentStadium.card_name }}</Form-item> 
                <Form-item label="教练名称">{{ currentStadium.coach_name }}</Form-item> 
                <Form-item label="上课时间">{{ currentStadium.course_time }}</Form-item>

                <template v-if="currentStadium.reservation_type === 2">
                  <Form-item label="实收金额">{{ Number(currentStadium.pt_income_amount).toFixed(2) }}</Form-item>
                  <Form-item v-if="isRefund && currentStadium.type == 1" label="应退比例" prop="refund_rate" style="color: red;">{{(currentStadium.pt_refund_rate*100).toFixed(2)}}%</Form-item>
                  <Form-item label="应退金额" style="color: red;">{{ Number(currentStadium.pt_refund_amount).toFixed(2) }}</Form-item> 
                </template>
              </template>
              
            </div>
            <div class="modal-divider">
              <Form-item label="订场实退: ">
                <Input-number
                  :min="0"
                  :precision="2"
                  :active-change="false"
                  style="width: 300px"
                  v-model="currentStadium.put_refund_amount"
                />
              </Form-item>
              <Form-item label="约课实退: " v-if="currentStadium.ssl_status !== 1 && currentStadium.reservation_type === 2">
                <Input-number
                  :min="0"
                  :precision="2"
                  :active-change="false"
                  v-model="currentStadium.put_pt_refund_amount"
                  style="width: 300px"
                />
              </Form-item>
              <Form-item label="总计: ">
                <span style="font-weight: bold; color: red">
                  {{ Number(finalRefundPrice).toFixed(2) }}元
                </span>
              </Form-item>
              <template v-if="isRefund">
                <Form-item v-if="currentStadium.status != 2" :label="currentStadium.status == 0 ? '支付方式' : '退款方式'" required>
                  <pay-type-list
                    v-model="currentStadium.new_pay_type"
                    :amount="finalRefundPrice"
                    :sqbOption="{ describe: `订场[${currentStadium.name}]`, serviceType: 2, isEqual: false }"
                    :userId="currentStadium.isMember == 1 ? currentStadium.user_id : -3"
                    :showCardPay="currentStadium.isMember == 1"
                    :isRefundNeedSQB="currentStadium.isRefundNeedSQB"
                    :isRefund="true"
                    :isMaxAmount="currentStadium.from == 2"
                  />
                </Form-item>
                <Form-item v-else label="支付方式: " required>
                  <pay-type-list
                    v-model="currentStadium.new_pay_type"
                    :amount="finalRefundPrice"
                    :sqbOption="{ describe: `订场[${currentStadium.name}]`, serviceType: 2, isEqual: false }"
                    :userId="currentStadium.isMember == 1 ? currentStadium.user_id : -3"
                    :showCardPay="currentStadium.isMember == 1"
                    :disabled="currentStadium.isDetail"
                    :needShowDragonFly="!currentStadium.isDetail"
                  />
                </Form-item>
              </template>
              <Form-item label="备注: ">
                <Input v-model.trim="currentStadium.remarks" type="textarea" :maxlength="150" style="width: 300px"
                  :autosize="{ minRows: 4, maxRows: 8 }" />
              </Form-item>
              <FormItem v-if="!currentStadium.isDetail">
                <Button type="success" @click="handleBookingSubmit" :disabled="currentStadium.isDetail">支付</Button>
                <Button @click="handleBookingCancel" style="margin-left: 40px" :disabled="currentStadium.isDetail">
                  取消订场
                </Button>
              </FormItem>
              <FormItem v-else>
                <Button v-if="isRefund" type="success" style="margin-right: 40px" @click="handleRefund">
                  {{ refundLabel }}
                </Button>
                <Button @click="handleBookingCancel">关闭</Button>
              </FormItem>
            </div>
          </div>
        </Form>
      </div>
      <div slot="footer"></div>
    </Modal>
    <!-- </Drawer> -->
    <Modal v-model="restDialogFlag" width="900" @on-visible-change="handleRestHide" :mask-closable="false">
      <div class="modal-header">
        <h3>休息</h3>
        <h1 style="text-align: center">{{ currentStadium.name }}</h1>
      </div>
      <div class="modal-buddy" style="margin-top: 20px">
        <Row :gutter="32">
          <Col span="24" class="demo-tabs-style1" style="background-color: #e3e8ee; padding: 16px">
            <div class="rest-time-box">
              <div class="rest-time-line" v-for="(rest, $index) in restDateList" :key="`rest_${$index}`">
                <DatePicker
                  v-model="rest.date"
                  @on-change="handleRestDateChange($event, $index)"
                  type="date"
                  placeholder="选择休息日期"
                  :options="dateOptions"
                  :clearable="false"
                ></DatePicker>
                <Checkbox @on-change="handleRestAll($index)" :value="rest.checkAll" :indeterminate="rest.indeterminate">
                  全选
                </Checkbox>
                <div class="rest-tag-box">
                  <Tag
                    v-for="(time, $timeIndex) in rest.timeList"
                    :key="`time_${$timeIndex}`"
                    :checked="time.checked"
                    @on-change="handleTimeClick($index, $timeIndex)"
                    checkable
                  >
                    {{ time.start_time }}~{{ time.end_time }}
                  </Tag>
                </div>
                <Button icon="ios-trash" size="small" type="error" @click="handleRestDelete($index)" ghost>删除</Button>
                <Button
                  icon="ios-checkmark"
                  size="small"
                  type="success"
                  :disabled="!rest.saveFlag"
                  @click="handleRestSubmit($index)"
                  ghost
                >
                  保存
                </Button>
              </div>
              <Button icon="ios-add" type="primary" class="rest-add-btn" @click="handleRestAdd">添加</Button>
            </div>
          </Col>
        </Row>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal v-model="tipDialogFlag" width="900" :mask-closable="false">
      <div class="modal-header">
        <h3>订场须知</h3>
      </div>
      <div class="modal-buddy" style="margin-top: 20px">
        <Form :model="tipDialogPost" :label-width="80">
          <Form-item label="会员端显示: " prop="booking_notice_swtich">
            <i-switch v-model="tipDialogPost.booking_notice_swtich" :true-value="1" :false-value="0" />
          </Form-item>
          <Form-item label="场地类型: " prop="id">
            <Select
              @on-change="tipCategoryChange"
              v-model="tipDialogPost.type_id"
              class="w120"
              placeholder="场地类型"
              clearable
              transfer
            >
              <Option v-for="item in categoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
          </Form-item>
          <Form-item v-if="tipDialogPost.booking_notice_swtich === 1" label="内容: ">
            <Editor v-model="tipDialogPost.booking_notice" :height="400"></Editor>
          </Form-item>
        </Form>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleTipSubmit">保存</Button>
        <Button @click="handleTipCancel">取消</Button>
      </div>
    </Modal>
    <Modal v-model="showReceipt" width="380">
      <div class="header">
        <Icon type="ios-checkmark-circle-outline" size="32"></Icon>
        <h3>支付成功！</h3>
      </div>
      <div slot="footer" class="modal-buttons">
        <!-- <router-link
          style="color: #fff"
          target="_blank"
          :to="{
            path: '/stadiumPrint',
            query: { user_id: this.currentStadium.user_id, order_sn: this.currentStadium.order_sn },
          }"
        > -->
          <Button type="success" @click="handleToPrint">打印小票</Button>
        <!-- </router-link> -->
        <Button @click="showReceipt = false" style="margin-left: 60px">取消</Button>
      </div>
    </Modal>

    <Modal v-model="lightFlag" title="灯光" width="444">
      <div class="modal-buddy">
        <Alert type="error" style="margin-bottom: 40px 0">注意：已预订场次到时间会自动开/关灯，无需手动控制</Alert>
        <div class="light-item" v-if="lightBulbList.length > 0">
          <!-- <strong>全部</strong>
          <i-switch @on-change="handleOpenAllLight" :disabled="lightBulbList.length === 0" size="large" style="margin-left: 10px">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch> -->
          <Button @click="handleOpenAllLight(true)" type="text" ghost>全部开启</Button>
          <Divider type="vertical" />
          <Button @click="handleOpenAllLight(false)" type="text" ghost>全部关闭</Button>
        </div>
        <template v-if="lightBulbList.length > 0">
          <div class="light-card" v-for="item in lightBulbList" :key="item.id">
            <div>{{ item.name }}</div>
            <div class="light-item">
              灯光
              <i-switch
                v-model="item.checked"
                @on-change="handleOpenLight(item)"
                size="small"
                style="margin-left: 10px"
              />
            </div>
          </div>
        </template>
        <div v-else class="none-lightbulb">场地未配置灯控数据，请先完成灯控配置！</div>
      </div>
      <div slot="footer" class="modal-buttons"></div>
    </Modal>
    <DiscountModal
      type="buy"
      :show.sync="showDiscount"
      :list="discountList"
      :selectIndex="selectDiscountIndex"
      :isPreOrder="false"
      @radio="handleSelectDiscount"
    />
    <RestModal 
      :post-data="restPostData"
      :type="restType"
      v-model="showRestModal"
      @on-success="restModalSuccess"
    />

    <!-- <Modal v-model="showLongTermRental" width="500">
      <div class="modal-buddy">
        <h2>本预订为长租预订, 后续还有 {{ longTermRentalOrder?.long_order?.num || 0 }} 关联预订数据</h2>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleTodayCancel">仅取消当天预订</Button>
        <Button type="success" @click="handleToLongTermRentalCancel">取消当天及以后日期预订</Button>
      </div>
    </Modal> -->
    <long-term-rental-modal 
      :show.sync="showLongTermRental" 
      :count="longTermRentalOrder?.long_order?.num || 0"
      :id="longTermRentalOrder?.long_order?.id || 0"
      :modify-one="longTermRentalLabel === '取消预订' ? authority.cancel === 1 : authority.refund === 1"
      :modify-all="longTermAuthorities.modifyAll"
      @on-today-cancel="handleTodayCancel" />

    <one-time-pay-modal :show.sync="showOneTimePay" :item="oneTimePayOrder" @on-all-cancel="handleAllCancel" />

    <occupy-modal
      :show.sync="showOccupyModal"
      :space-id="spaceId"
      :space-type-id="searchPost.type_id + ''"
      :date="occupyDate"
      :time="occupyTime"
      @on-success="() => {
        this.resetSelectedGrids()
        this.getList()
      }"
    />

    <!-- one time booking -->
    <set-price-modal
      :show.sync="showSetPriceModal"
      @on-success="() => {
        Message.success('设置成功')
      }"
    />
    <Modal
        v-model="noCancelVisible"
        title=""
        :mask-closable="false">
        <h2 style="text-align: center; margin-bottom: 40px;">场地关联的约课已签到或上课，无法取消订场</h2>
        <Form class="modal-form" :label-width="200">
          <Form-item label="会员名称">{{ noCancelOrder?.username }}</Form-item>
          <Form-item label="课程名称">{{ noCancelOrder?.card_name }}</Form-item>
          <Form-item label="教练名称">{{ noCancelOrder?.coach_name }}</Form-item>
          <Form-item label="上课时间">{{ noCancelOrder?.sign_date }}</Form-item>
        </Form>
        <div slot="footer" style="text-align: center; margin-bottom: 40px;">
            <Button @click="() => {
              this.noCancelVisible = false
              this.noCancelOrder = null
            }">关闭</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
import CategoryTag from './CategoryTag.vue'
import TheRowLight from './TheRowLight.vue'
import TheRowOther from './TheRowOther.vue'
import RestModal from './RestModal.vue'
import { throttle, cloneDeep } from 'lodash-es'
import PayTypeList from 'components/form/PayTypeList.vue'
import { formatDate } from 'utils'
import Editor from 'src/components/form/Editor'
import { getWeekJson } from '@/service/putOSS.js'
import { mapGetters } from 'vuex'
import SocketUtil from '@/mixins/socketUtil'
import DiscountModal from 'components/form/discountModal'
import { DISCOUNT_CARD_LIMIT, DISCOUNT_SCOPE_LIMIT } from 'store/constants'
import LongTermRentalModal from './LongTermRentalModal.vue'
import Big from 'big.js'
import OccupyModal from './OccupyModal.vue'
import SetPriceModal from './SetPriceModal.vue'
import SaleSelect from 'src/components/membership/salesSelect'
import UserPtSearchNew from 'src/components/form/UserPtSearchNew.vue';
// import radioTime from 'src/components/picker/radioTime.vue';
import _ from 'lodash-es';
import OneTimePayModal from './OneTimePayModal.vue'

let dealDiscountTimer = null

export default {
  mixins: [SocketUtil],
  components: {
    CategoryTag,
    RestModal,
    TheRowLight,
    TheRowOther,
    PayTypeList,
    Editor,
    DiscountModal,
    LongTermRentalModal,
    OccupyModal,
    SetPriceModal,
    SaleSelect,
    // radioTime,
    UserPtSearchNew,
    OneTimePayModal
  },
  props: {
    categoryList: {
      type: Array,
      default: () => [],
    },
    longTermAuthorities: {
      type: Object,
      default: () => ({})
    },
    authority: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const timeList = [
      '00:00',
      '00:30',
      '01:00',
      '01:30',
      '02:00',
      '02:30',
      '03:00',
      '03:30',
      '04:00',
      '04:30',
      '05:00',
      '05:30',
      '06:00',
      '06:30',
      '07:00',
      '07:30',
      '08:00',
      '08:30',
      '09:00',
      '09:30',
      '10:00',
      '10:30',
      '11:00',
      '11:30',
      '12:00',
      '12:30',
      '13:00',
      '13:30',
      '14:00',
      '14:30',
      '15:00',
      '15:30',
      '16:00',
      '16:30',
      '17:00',
      '17:30',
      '18:00',
      '18:30',
      '19:00',
      '19:30',
      '20:00',
      '20:30',
      '21:00',
      '21:30',
      '22:00',
      '22:30',
      '23:00',
      '23:30',
      '24:00',
    ]
    const restDatePickerFlag = (date) => {
      const dateStr = formatDate(date, 'yyyy-MM-dd')
      const flag = this.restDateList.find((item) => formatDate(item.date, 'yyyy-MM-dd') === dateStr)
      return flag
    }
    return {
      restPostData: {},
      lightFlag: false,
      restType: 0,
      showReceipt: false,
      showRestModal: false,
      movingListenerFlag: false,
      list: [],
      timeLineStyle: '',
      timeList,
      gridHeaderStyle: '',
      // gridBuddyStyle: '',
      statusColorList: ['#ff6969', '#1bd4c9', '#3dabff'],
      statusNameList: ['未支付', '已支付', '已到场'],
      // categoryList: [],
      checkedCategory: {},
      searchPost: {
        date: '',
        type_id: '',
        coach_id: '',
      },
      selectedGrids: {
        rowId: '',
        start: null,
        end: null,
        site: '', // up, down, all
      },
      detailFlag: false,
      isRefund: false,
      refundUrl: '',
      refundLabel: '',
      search: '',
      currentStadium: {
        id: '',
        name: '',
        date: '',
        between: '',
        isMember: 1,
        order_sn: '',
        username: '',
        phone: '',
        new_pay_type: [],
        openHalf: 1,
        isHalf: 0,
        hasWhole: true,
        all_price: 0,
        user_id: '',
        card_user_id: '',
        status: '',
        payment: '',
        schedule_price: [],
        fee: 0,
        refund_rate: 1,
        from: '', // 1-PC 2-微信小程序
        remarks: '',
        isDetail: false,
        // long term rental
        type: 1,
        // one time booking
        reservation_type: 1,
        card_index: '',
        card_name: '',
        coach_id: '',
        coach_name: '',
        begin_date: '',
        pid_id: '',
        start_time_format: '',
        course_time: '',
        pt_refund_rate: 1,
        pt_refund_amount: 0,
        put_pt_refund_amount: 0,
        pt_income_amount: 0,
        should_refund_amount: 0,
        pt_schedule_id: '',
        put_refund_amount: 0,
        ssl_status: 0,
        isRefundNeedSQB: false,
      },
      restDialogFlag: false,
      restDateList: [],
      dateOptions: {
        disabledDate(date) {
          return Date.now() - date > 24 * 3600 * 1000 || restDatePickerFlag(date)
        },
      },
      planTimeSet: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      },
      reserveNewAuth: false,
      reservePriceEditAuth: false,
      tipDialogFlag: false,
      tipDialogPost: {
        booking_notice_swtich: 0,
        booking_notice: '',
        type_id: '',
      },
      lightBulbList: [],
      selectDiscountIndex: -1, // 选择的折扣数据索引
      selectedDiscount: '', // 选择的折扣数据对象
      showDiscount: false, // 显示全部折扣券弹窗
      discountList: [],
      discountText: '请选择会员',
      computedFee: 0,
      showPrice: 0, // 总计金额
      allPrice: 0, //实际支付金额
      stateList: [
        { name: "未支付", code: 0 },
        { name: "已支付", code: 1 },
        { name: "已到场", code: 2 },
        { name: "已退款", code: 4 },
        { name: "已取消", code: 5 }
      ],
      // long term rental
      showLongTermRental: false,
      longTermRentalOrder: null,
      longTermRentalLabel: '',
      // occupy modal
      showOccupyModal: false,
      spaceId: '',
      occupyDate: '',
      occupyTime: [],
      // one time booking
      coachList: [],
      showSetPriceModal: false,
      showLabelList: ['会员名', '会员电话', '教练名称', '课程名称'],
      showLabelValue: [2, 3],
      designatedCoach: true,
      userSearch: '',
      isUserId: false,
      selectedUser: null,
      filteredCoaches: [],
      radioTimeData: null,
      // courseActiveTime: '',
      coachFreeTime: '',
      cptPrice: 0,
      // one time pay
      oneTimePayCourseList: [],
      oneTimePayPrice: null,
      oneTimePayType: [],
      oneTimePayNoCardPrice: null,
      oneTimePayCardUserId: '',
      card_order_info_id: '',
      showOneTimePay: false,
      oneTimePayOrder: null,
      oneTimePayLabel: '',
      noCancelVisible: false,
      noCancelOrder: null,
    }
  },
  watch: {
    categoryList(newValue, oldValue) {
      if (Array.isArray(this.categoryList) && this.categoryList.length > 0) {
        const today = new Date()
        this.searchPost.date = formatDate(today, 'yyyy-MM-dd')
        this.searchPost.type_id = this.categoryList[0].id
        this.getList()
        this.categoryList[0].isChecked = true
        this.checkedCategory = this.categoryList[0]
      }
    },
    // allPrice() {
    //   this.currentStadium.new_pay_type = []
    // },
    cardUserId(val) {
      if (this.currentStadium.coach_id && this.currentStadium.reservation_type === 2) {
        this.currentStadium.new_pay_type = [...this.oneTimePayType];
      }
      if (this.currentStadium.reservation_type === 1) {
        this.getPrice().then(() => {
          const index = this.currentStadium.new_pay_type.findIndex(item => item.pay_type === 8);
          if (index !== -1) {
            this.$nextTick(() => {
              this.$refs?.payTypeListRef2?.handleAmountChange(8, this.finalPrice);
            });
          }
        });
      }
    },
    oneTimePayType(val) {
      // if change pay type then get price
      if (Array.isArray(val) && typeof this.currentStadium.card_index === 'number') {
        let run = true;

        const index = val.findIndex(item => item.pay_type === 8);
        if (index !== -1) {
          const item = val[index];
          if (item.autoSelect) {
            run = false;
            item.autoSelect = false;
          }
          
          // const oldIndex = oldVal.findIndex(item => item.pay_type === 8);
          // if (oldIndex !== -1) {
          //   run = false;
          // }
          if (this.oneTimePayCardUserId === item.card_user_id) {
            run = false;
          }
        }

        if (run) {
          const course = this.oneTimePayCourseList[this.currentStadium.card_index];
          this.getOneTimePayPrice(course, index !== -1).then(() => {
            this.currentStadium.new_pay_type = this.oneTimePayType;
            if (index !== -1) {
              this.getPrice().then(() => {
                if (val[index]) {
                  this.$nextTick(() => {
                    this.$refs?.payTypeListRef?.handleAmountChange(val[index]?.pay_type, this.finalPrice);
                  });
                }
              });
            }
          });
        }
      }
    },
  },
  computed: {
    ...mapGetters(['busId', 'adminId', 'adminName']),
    cardUserId() {
      const cardPayItem = this.currentStadium.new_pay_type.find((item) => item.pay_type === 8)
      return cardPayItem ? cardPayItem.card_user_id : ''
    },
    bookingModalTitle() {
      if (this.currentStadium.isDetail) {
        return this.refundLabel || '预约详情';
      } else {
        return '场地预约';
      }
    },
    coachName() {
      const coach = this.filteredCoaches.find(item => item.coach_id === this.currentStadium.coach_id)
      return coach ? coach.name: ''
    },
    finalPrice() {
      if (this.currentStadium.coach_id && this.currentStadium.reservation_type === 2) {
        const stadium = new Big(this.allPrice || 0);
        const cpt = new Big(this.cptPrice || 0);
        return Number(stadium.add(cpt).toNumber().toFixed(2));
      } else {
        // return this.showPrice;
        return this.allPrice;
      }
    },
    finalRefundPrice() {
      const stadium = new Big(this.currentStadium.put_refund_amount || 0);
      const cpt = new Big(this.currentStadium.put_pt_refund_amount || 0);
      return Number(stadium.add(cpt).toNumber().toFixed(2));
    },
    canShowLongTermTask() {
      return (order) => {
        const isFutureOrder = order.endTime > Date.now();

        if (order.type === 2) {
          if (isFutureOrder) {
            return this.authority.cancel === 1 || this.longTermAuthorities.modifyAll;
          } else {
            return this.authority.refund === 1 || this.longTermAuthorities.modifyAll;
          }
        } else {
          if (isFutureOrder) {
            return this.authority.cancel === 1;
          } else {
            return this.authority.refund === 1;
          }
        }
      };
    }
  },
  methods: {
    handleToSaveStadium(event, id = '') {
      const url = `${window.location.protocol}//${window.location.host}/booking/stadium-save/${id}`
      window.open(url, '_self')
    },
    handleToStrategyList() {
      this.$service.get('/Web/Admin/check_plan_auth').then((res) => {
        if (res.data.errorcode === 0) {
          if (res.data.data.space) {
            const url = `${window.location.protocol}//${window.location.host}/booking/strategy-list`
            window.open(url, '_self')
          } else {
            this.$Message.error('没有排场&定价方案权限')
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleToSetMemberPrice() {
      this.showSetPriceModal = true
    },
    goHolidaySetList() {
      window.history.pushState({}, '', '/space/holiday-set-list');
    },
    handleGridScroll($event) {
      const { target } = $event
      const { costa, coffee } = this.$refs
      if (costa) costa.scrollLeft = target.scrollLeft
      if (coffee) coffee.scrollTop = target.scrollTop
    },
    eventFun(event) {
      if (event.key == 'receipt') {
        localStorage.removeItem('receipt')
        this.showReceipt = false
      }
    },
    handleDateChange(val) {
      this.searchPost.date = val
      this.resetDetail()
      this.resetSelectedGrids()
      this.getList()
      this.getMapCoachList()
    },
    tipCategoryChange(val) {
      this.tipDialogPost.type_id = val
      this.getTipInfo(val)
    },
    getReserveAuth() {
      this.$service.post('/Web/SpaceOrder/ReserveNodePower').then((res) => {
        if (res.data.errorcode == 0) {
          this.reserveNewAuth = res.data.data.reserveNew
          this.reservePriceEditAuth = res.data.data.reservePriceEdit
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getTipInfo(type_id) {
      this.$service.post('/Web/Space/getTypeList', { type_id }).then((res) => {
        if (res.data.errorcode == 0) {
          this.tipDialogPost.booking_notice_swtich = res.data.data.booking_notice_swtich
          this.tipDialogPost.booking_notice = res.data.data.booking_notice
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleTipShow() {
      this.tipDialogPost.type_id = this.categoryList[0].id
      this.getTipInfo(this.tipDialogPost.type_id)
      this.tipDialogFlag = true
    },
    handleTipCancel() {
      this.tipDialogFlag = false
    },
    handleTipSubmit() {
      this.$service.post('/Web/Space/saveBooKNotice', this.tipDialogPost).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success('保存成功')
          this.tipDialogFlag = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    reloadCategoryList(cat) {
      const flag = cat.isChecked
      const index = this.categoryList.findIndex((item) => item.isChecked)
      this.getCategoryList().then(() => {
        if (Array.isArray(this.categoryList) && this.categoryList.length > 0) {
          if (flag) {
            this.searchPost.type_id = this.categoryList[0].id
            this.getList()
            this.categoryList[0].isChecked = true
            this.checkedCategory = this.categoryList[0]
          } else {
            this.searchPost.type_id = this.categoryList[index].id
            this.getList()
            this.categoryList[index].isChecked = true
            this.checkedCategory = this.categoryList[index]
          }
        }
        this.resetDetail()
        this.resetSelectedGrids()
        this.$emit('update:categoryList', this.categoryList)
      })
    },
    getCategoryList() {
      return this.$service.post('/Web/Space/getTypes').then((res) => {
        if (res.data.errorcode == 0) {
          this.categoryList = res.data.data.map((item) => {
            item.isChecked = false
            return item
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleTagClick(cat) {
      this.categoryList.forEach((item) => {
        item.isChecked = false
      })
      cat.isChecked = true
      this.searchPost.type_id = cat.id
      this.checkedCategory = cat
      this.resetDetail()
      this.resetSelectedGrids()
      this.getList()
    },
    changeSale() {
      this.resetDetail()
      this.resetSelectedGrids()
      this.getList()
    },
    getList() {
      // this.resetDetail()
      // this.resetSelectedGrids()
      return this.$service
        .get('/Web/Space/getOrderAndSchedule', {
          params: this.searchPost,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            const bookingList = res.data.data
            const list = []
            if (Array.isArray(bookingList) && bookingList.length > 0) {
              bookingList.forEach((item, index) => {
                const orderList = []
                if (Array.isArray(item.space_list) && item.space_list.length > 0) {
                  item.space_list.forEach((order) => {
                    const start = this.timeList.findIndex((time) => time === order.start_time_format) + 4
                    if (order.end_time_format === '00:00') {
                      order.end_time_format = '24:00'
                    }
                    const end = this.timeList.findIndex((time) => time === order.end_time_format) + 4
                    const color = this.statusColorList[order.status]
                    const grid = `grid-area: ${index + 1} / ${start} / span 1 / ${end};background-color: ${color};`
                    let position = 'all' // 0:all, 1:up, 2:down
                    if (order.position === 1) {
                      position = 'up'
                    } else if (order.position === 2) {
                      position = 'down'
                    }
                    orderList.push({
                      id: order.space_id,
                      orderId: order.space_order_id,
                      name: order.space_name,
                      space_type_name: order.space_type_name,
                      username: order.username,
                      phone: order.phone,
                      // amount: Number(order.amount),
                      openHalf: order.is_half,
                      user_type: order.user_type,
                      user_id: order.user_id,
                      new_pay_type: order.new_pay_type,
                      order_sn: order.order_sn,
                      card_user_id: order.card_user_id,
                      status: order.status,
                      statusLabel: this.statusNameList[order.status],
                      payment: this.stateList[order.status].name,
                      color,
                      schedule_price: order.schedule_detail.schedule,
                      all_price: Number(order.schedule_detail.all_price),
                      fee: Number(order.amount),
                      from: order.from,
                      date: formatDate(order.end_time * 1000, 'yyyy-MM-dd'),
                      time: order.start_time_format + '~' + order.end_time_format,
                      endTime: Number(order.end_time) * 1000,
                      remarks: order.remarks,
                      grid,
                      refund_rate: order.refund_rate,
                      position,
                      start,
                      end,
                      coupon_amount: order.coupon_amount || '0',
                      type: order.type,
                      long_order: order.long_order,
                      batch_number: order.batch_number,
                      card_name: order.card_name,
                      coach_name: order.coach_name,
                      reservation_type: order.reservation_type,
                      start_time_format: order.start_time_format,
                      course_time: order.course_time,
                      ssl_status: order.ssl_status,
                      ssl_id: order.ssl_id,
                      pt_schedule_id: order.pt_schedule_id,
                      should_refund_amount: order.should_refund_amount,
                      put_refund_amount: order.should_refund_amount,
                      schedule_time: order.schedule_time,
                      schedule_amount: order.schedule_amount,
                    })
                  })
                }
                const gridList = []
                const gridPositionList = []
                if (Array.isArray(item.schedule_price) && item.schedule_price.length > 0) {
                  item.schedule_price.forEach((sch) => {
                    if (sch.end_time === '00:00') {
                      sch.end_time = '24:00'
                    }
                    const start = this.timeList.findIndex((time) => time === sch.start_time) + 4
                    const end = this.timeList.findIndex((time) => time === sch.end_time) + 4
                    // if (start !== 3 && end !== 3) {
                    const grid = `grid-area: ${index + 1} / ${start} / span 1 / ${end};`
                    gridList.push(grid)
                    gridPositionList.push({
                      start,
                      end,
                    })
                    // }
                  })
                }
                const disabledList = []
                const disabledPositionList = []
                if (Array.isArray(item.disable_space_list) && item.disable_space_list.length > 0) {
                  const disableExpandList = this.disableExpand(item.disable_space_list, item.schedule_price);

                  [...disableExpandList, ...item.disable_space_list].forEach((dis) => {
                    const start = this.timeList.findIndex((time) => time === dis.start_time) + 4
                    const end = this.timeList.findIndex((time) => time === (dis.end_time === '00:00' ? '24:00' : dis.end_time)) + 4
                    const gridArea = `${index + 1} / ${start} / span 1 / ${end};`
                    let height = '60px';
                    let marginTop = '0px';
                    let bgColor = '';
                    if (dis.position === 1) {
                      height = '29px';
                    } else if (dis.position === 2) {
                      height = '29px';
                      marginTop = '31px';
                    }
                    if (dis.expand) {
                      bgColor = 'background-color: #bdc9d696;';
                    }
                    disabledList.push(`grid-area: ${gridArea};height: ${height};margin-top: ${marginTop};${bgColor}`)

                    disabledPositionList.push({
                      start,
                      end,
                      ...dis,
                      height,
                    })
                  })
                }
                const booking = {
                  id: item.id,
                  name: item.name,
                  max: item.max_hour,
                  gridList,
                  gridPositionList,
                  orderList,
                  disabledList,
                  disabledPositionList,
                  hasHalf: !!item.is_half,
                  lightBulb: item.light_status,
                }
                list.push(booking)
              })
            }
            this.list = list

            const count = list.length
            const today = new Date()
            let currentTimeIdx = (today.getHours() - Number(this.timeList[0].split(':')[0])) * 2 + 4
            if (today.getMinutes() > 30) {
              currentTimeIdx++
            }
            this.timeLineStyle = `grid-area: 1 / ${currentTimeIdx} / span ${count} / span 1;`

            this.setScrollbar((currentTimeIdx / 2 - 9) * 100)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleCustomerTypeChange(val) {
      if (this.currentStadium.isDetail) {
        return
      }
      if (val === 2) {
        this.selectedUser = null
        this.radioTimeData = null
        this.oneTimePayNoCardPrice = null
        this.oneTimePayType = []
        this.currentStadium.user_id = ''
        this.currentStadium.new_pay_type = []
        // 清空折扣券数据
        this.selectedDiscount = ''
        this.discountList = []
        this.selectDiscountIndex = -1
      }
      this.getPrice()
    },
    getPrice() {
      if (this.selectedGrids.rowId) {
        const schedules = this.getSchedules()
        return this.$service
          .post('/Web/Space/getPrice', {
            space_id: this.currentStadium.id, // 场地id
            date: this.searchPost.date, // 2019-08-1
            user_id: this.currentStadium.user_id, // 用户id
            schedules,
            is_half: this.currentStadium.isHalf, // 1-半场 0-全场
            new_pay_type: this.currentStadium.new_pay_type,
          })
          .then((res) => {
            if (res.data.errorcode === 0) {
              this.currentStadium.all_price = Number(res.data.data.all_price)
              if (this.selectedDiscount && this.selectedDiscount.discount_amount) {
                let price = this.sub(res.data.data.all_price, this.selectedDiscount.discount_amount)
                this.showPrice = price < 0 ? 0 : price
                this.allPrice = price < 0 ? 0 : price
              } else {
                this.showPrice = Number(res.data.data.all_price)
                this.allPrice = Number(res.data.data.all_price)
              }
              this.currentStadium.fee = Number(res.data.data.all_price)
              this.currentStadium.schedule_price = res.data.data.schedule
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      } else {
        return Promise.resolve()
      }
    },
    userSelected(selectedUser) {
      this.currentStadium.user_id = ''
      this.currentStadium.new_pay_type = []
      this.currentStadium.card_index = '';
      this.currentStadium.reservation_type = 1;
      // this.$refs.userSearchRef.$refs.selectRef.values = [];
      // 每次选择会员都需要重置已选折扣券卷变量
      this.selectedDiscount = ''
      this.selectDiscountIndex = -1
      this.selectedUser = null;
      this.radioTimeData = null;
      this.userSearch = '';
      this.search = '';

      if (selectedUser) {
        this.selectedUser = selectedUser;
        this.currentStadium.user_id = selectedUser.user_id;
        this.getPrice()
        // 当选择会员之后请求该会员的折扣券数据
        this.getDiscountList(selectedUser.user_id)
        this.search = selectedUser.username
      }
    },
    resetDetail() {
      const s = this.currentStadium
      Object.assign(s, {
        id: '',
        name: '',
        between: '',
        isMember: 1,
        order_sn: '',
        username: '',
        phone: '',
        new_pay_type: [],
        openHalf: 1,
        isHalf: 0,
        hasWhole: true,
        all_price: 0,
        user_id: '',
        status: '',
        refund_rate: 1,
        schedule_price: [],
        fee: 0,
        from: '',
        remarks: '',
        isDetail: false,
        type: 1,
        reservation_type: 1,
        card_index: '',
        coach_id: '',
        begin_date: '',
        pid_id: '',
        start_time_format: '',
        course_time: '',
        pt_refund_rate: 1,
        pt_refund_amount: 0,
        put_pt_refund_amount: 0,
        pt_income_amount: 0,
        should_refund_amount: 0,
        put_refund_amount: 0,
        ssl_status: 0,
        isRefundNeedSQB: false,
      })
      Object.assign(this, {
        search: '',
        isRefund: false,
        refundUrl: '',
        refundLabel: '',
        showPrice: 0,
        allPrice: 0,
      })
    },
    resetSelectedGrids() {
      this.selectedGrids = {
        rowId: '',
        start: null,
        end: null,
        site: '',
      }
    },
    handleBookingCancel() {
      this.detailFlag = false
      this.resetSelectedGrids()
    },
    getSchedules() {
      const schedules = []
      const row = this.list.find((item) => item.id === this.selectedGrids.rowId)
      if (row) {
        for (let i = this.selectedGrids.start; i <= this.selectedGrids.end; i++) {
          const start = row.gridPositionList[i].start
          const end = row.gridPositionList[i].end
          const start_time = this.timeList[start - 4]
          let end_time = this.timeList[end - 4]
          if (end_time === '24:00') {
            end_time = '00:00'
          }
          schedules.push({
            start_time,
            end_time,
          })
        }
      }
      return schedules
    },
    checkUserLimitByType(params) {
      return this.$service.post('/Web/UserBlacklist/checkUser', params, 
      { headers: { 'Content-Type': 'application/json' } }).then(res => {
        return res.data.data
      })
    },
    async handleBookingSubmit() {
      const flag = await this.checkUserLimitByType({
        user_id: this.currentStadium.user_id,
        bus_id: this.$store.state.busId,
        member_rule: 6,
        loading: true
      })
      if(flag) {
        this.$Modal.confirm({
          title: '确认预约?',
          content: '此会员已在门店黑名单中',
          okText: '仍要预约',
          onOk: () => {
            this.handleBooking()
          },
        });
      } else {
        this.handleBooking()
      }
    },
    handleBooking() {
      let position = 0 // 0:all, 1:up, 2:down
      if (this.selectedGrids.site === 'up') {
        position = 1
      } else if (this.selectedGrids.site === 'down') {
        position = 2
      }
      if (this.currentStadium.isHalf === 0) {
        position = 0
      }
      const schedules = this.getSchedules()

      const params = {
        space_id: this.currentStadium.id, // 场地id
        date: this.searchPost.date, // '2019-08-1'; 日期
        user_type: this.currentStadium.isMember, //1-会员 2-散客
        is_half: this.currentStadium.isHalf, //1-半场 0-全场
        position, // 场地位置
        new_pay_type: this.currentStadium.new_pay_type, // 支付类型
        schedules,
        amount: this.allPrice, //金额  由fee->变更为all_price
        phone: this.currentStadium.phone,
        remarks: this.currentStadium.remarks,

        user_id: this.currentStadium.user_id,

        // 折扣卷 id
        receive_id: this.selectedDiscount.receive_id,
        // 折扣卷价格
        coupon_amount: this.selectedDiscount.discount_amount,
      }

      if (this.currentStadium.coach_id) {
        // validator
        if (!this.currentStadium.card_id) {
          this.$Message.error('请选择课程')
          return
        }
        if (this.currentStadium.card_id && !this.currentStadium.begin_date) {
          this.$Message.error('请选择上课时间')
          return
        }

        let new_pay_type = params.new_pay_type;
        if (this.currentStadium.reservation_type === 2) {
          new_pay_type = this.oneTimePayType;
        }

        const overPrice = new_pay_type.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
        if (this.finalPrice !== overPrice) {
          this.$Message.error('支付金额不等于总计金额!')
          return
        }

        return this.$service.post('/Web/SpaceOrder/scheduleAndBook', {
          ...params,
          coach_id: this.currentStadium.coach_id,
          coach_name: this.coachName,
          begin_date: this.searchPost.date + ' ' + this.currentStadium.begin_date,
          book_amount: this.allPrice,
          schedule_amount: this.cptPrice,
          card_id: this.currentStadium.card_id,
          card_user_id: this.currentStadium.card_user_id,
          card_type_id: 4,
          pt_charge_plan_detail_id: this.oneTimePayPrice?.pt_charge_plan_detail_id || '',
          reservation_type: this.currentStadium.reservation_type,
          amount: this.finalPrice,
          new_pay_type,
        }).then((res) => {
          if (res.data.errorcode == 0) {
            this.card_order_info_id = res.data.card_order_info_id
            // this.$Message.success(res.data.errormsg)
            this.detailFlag = false
            this.currentStadium.order_sn = res.data.order_sn
            this.showReceipt = true
            this.getList()
            this.resetSelectedGrids()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      } else {
        const overPrice = this.currentStadium.new_pay_type.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
        if (this.allPrice !== overPrice) {
          this.$Message.error('支付金额不等于总计金额!')
          return
        }
        return this.$service
          .post('/Web/SpaceOrder/reserveNew', params)
          .then((res) => {
            if (res.data.errorcode == 0) {
              // this.$Message.success(res.data.errormsg)
              this.detailFlag = false
              this.currentStadium.order_sn = res.data.order_sn
              this.showReceipt = true
              this.getList()
              this.resetSelectedGrids()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    },
    checkBooking() {
      const row = this.list.find((item) => item.id === this.selectedGrids.rowId)
      const first = row.gridPositionList[this.selectedGrids.start]
      const second = row.gridPositionList[this.selectedGrids.end]
      const hours = (second.end - first.start) / 2
      if (hours > row.max) {
        this.$Message.error(`单次订场限制${row.max}小时内，已超出！`)
        return false
      }

      let hasWhole = true
      let hasOrder = false
      row.orderList.forEach((order) => {
        if (first.start <= order.start && order.end <= second.end) {
          if (order.position === this.selectedGrids.site) {
            hasOrder = true
          } else {
            hasWhole = false
          }
        }
      })
      if (hasOrder) {
        this.$Message.error('该时间段已被预订！')
        return false
      }

      let hasRest = false
      
      row.disabledPositionList.forEach((position) => {
        let theSameSite = false;
        if (position.position === 0) {
          theSameSite = true
        } else if (position.position === 1) {
          theSameSite = this.selectedGrids.site === 'up'
        } else if (position.position === 2) {
          theSameSite = this.selectedGrids.site === 'down'
        }
        if (first.start <= position.start && position.end <= second.end && theSameSite) {
          hasRest = true
        }
      })
      if (hasRest) {
        this.$Message.error('该时段有场次占用！')
        return false
      }

      let hasNone = false
      const startPoint = first.start
      const endPoint = second.end
      let lastPoint = first
      row.gridPositionList.forEach((position) => {
        if (startPoint < position.start && position.end <= endPoint) {
          if (lastPoint.end !== position.start) {
            hasNone = true
          }
          lastPoint = position
        }
      })
      if (hasNone) {
        this.$Message.error('预约时间不连续！')
        return false
      }

      this.resetDetail()
      const start = row.gridPositionList[this.selectedGrids.start].start - 4
      const end = row.gridPositionList[this.selectedGrids.end].end - 4
      this.currentStadium.between = this.timeList[start] + '~' + this.timeList[end]
      this.currentStadium.id = row.id
      this.currentStadium.name = row.name
      this.currentStadium.openHalf = row.hasHalf ? 1 : 0
      this.currentStadium.isHalf = row.hasHalf ? 1 : 0
      this.currentStadium.hasWhole = hasWhole
      return true
    },
    handleBookingShow() {
      if (!this.reserveNewAuth && !this.currentStadium.isDetail) {
        this.$Message.error('权限不足，无预约权限！')
        return
      }
      if (this.checkBooking()) {
        this.getPrice().then((res) => {
          this.selectedDiscount = ''
          this.detailFlag = true
        })
      }
    },
    handleBookingRest() {
      // check if can occupy at this time
      let hasRest = false;
      const row = this.list.find((item) => item.id === this.selectedGrids.rowId) 
      const startCell = row.gridPositionList[this.selectedGrids.start]
      const endCell = row.gridPositionList[this.selectedGrids.end]

      try {
        row.disabledPositionList.forEach((position) => {
          let theSameSite = false;
          if (position.position === 0) {
            theSameSite = true
          } else if (position.position === 1) {
            theSameSite = this.selectedGrids.site === 'up'
          } else if (position.position === 2) {
            theSameSite = this.selectedGrids.site === 'down'
          }
          if (startCell.start <= position.start && position.end <= endCell.end && theSameSite) {
            hasRest = true
            throw new Error('该时段有场次占用！')
          }
        })
      } catch (error) {
        console.log(error)
      }
      if (hasRest) {
        this.$Message.error('该时段有场次占用！')
        return
      }

      const rest = this.getSchedules()
      let position = 0
      if (this.selectedGrids.site === 'up') {
        position = 1
      } else if (this.selectedGrids.site === 'down') {
        position = 2
      }
      this.spaceId = this.selectedGrids.rowId + '_' + position;
      this.occupyDate = this.searchPost.date;
      this.occupyTime = rest;
      this.showOccupyModal = true;
      // this.restType = 0
      // this.restPostData = {
      //   space_id: this.selectedGrids.rowId,
      //   date: this.searchPost.date,
      //   rest,
      // }
      // this.showRestModal = true
    },
    restModalSuccess() {
      this.getList()
      this.resetDetail()
      this.resetSelectedGrids()
    },
    handleCancelRest(space_id, position) {
      const start_time = this.timeList[position.start - 4]
      const end_time = this.timeList[position.end - 4]
      this.$service
        .post('/Web/Space/cancelRest', {
          space_id,
          date: this.searchPost.date,
          start_time,
          end_time: end_time === '24:00' ? '00:00' : end_time,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.resetDetail()
            this.resetSelectedGrids()
            this.getList()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleBookingReset() {
      this.resetDetail()
      this.resetSelectedGrids()
    },
    removeAllPop(id, index = -1, site = 'all') {
      for (const key in this.$refs) {
        if (Object.hasOwnProperty.call(this.$refs, key)) {
          const gridArr = this.$refs[key]
          if (Array.isArray(gridArr)) {
            const grid = gridArr[0]
            if (grid) {
              if (grid.rowId === id && grid.index === index && grid.position === site) {
                grid.visible = true
              } else {
                grid.visible = false
              }
            }
          }
        }
      }
    },
    handleGridClick(id, index, site) {
      // 有选择开始，则结束
      if (this.selectedGrids.start !== null) {
        // 同一行
        if (this.selectedGrids.rowId === id && this.selectedGrids.site === site) {
          // 之前的位置，重置
          if (this.selectedGrids.start > index) {
            this.$Message.error('已取消选择！')
            this.resetSelectedGrids()
            this.removeAllPop(id, -1, site)
            // 同一行，之后的位置
          } else {
            this.selectedGrids.end = index
            this.movingListenerFlag = false
            this.removeAllPop(id, index, site)
            // this.checkBooking()
          }
          // 不同行
        } else {
          this.$Message.error('请选择同一个场地！')
        }
        // 没有选择开始，则开始
      } else {
        this.selectedGrids.rowId = id
        this.selectedGrids.start = index
        this.selectedGrids.end = null
        this.selectedGrids.site = site
        this.movingListenerFlag = true
      }
    },
    getContent() {
      let price = 0;
      if (Array.isArray(this.currentStadium.new_pay_type) && this.currentStadium.new_pay_type.length > 0) {
        price = this.currentStadium.new_pay_type.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
      }
      return `请确认退款金额: ${price} 元`;
    },
    handleRefund() {
      const overPrice = this.currentStadium.new_pay_type.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
      if (this.finalRefundPrice !== overPrice) {
        this.$Message.error('支付金额不等于总计金额!')
        return
      }
      const content = this.getContent();
      this.$Modal.confirm({
        title: '提示',
        // content: '请核对退款金额和退款方式，一旦确认退款无法再修改数据?',
        content,
        onOk: () => {
          if (Number(this.currentStadium.type || 0) === 2) {
            this.postLongTermRentalRefund()
          } else {
            this.postSingleRefund()
          }
        },
      })
    },
    showSingleRefund(order, refundLabel, refundUrl) {
      this.handleLightClick(order, true)
      if (typeof order.new_pay_type === 'undefined') {
        order.new_pay_type = []
      }
      this.refundLabel = refundLabel
      this.refundUrl = refundUrl

      // if (order.reservation_type === 2) {
      //   this.$service.post('/Web/SpaceOrder/scheduleAndBookRefundInfo', {
      //     space_order_id: order.orderId,
      //     user_id: order.user_id,
      //   }).then(res => {
      //     if (res.data.errorcode == 0) {
      //       const info = res.data.data.pt_refund_info;
      //       this.currentStadium.course_time = info.course_time;
      //       this.currentStadium.card_name = info.class_name || info.card_name;
      //       this.currentStadium.pt_refund_rate = info.refund_rate;
      //       this.currentStadium.pt_refund_amount = info.refund_amount;
      //       this.currentStadium.put_pt_refund_amount = info.refund_amount;
      //       this.currentStadium.pt_income_amount = info.income_amount;
      //       // this.currentStadium.new_pay_type = info.pay_type;
      //       this.currentStadium.new_pay_type = res.data.data?.new_pay_type;

      //       // 退款类型中是否包收钱吧
      //       this.currentStadium.isRefundNeedSQB = res.data.data?.new_pay_type.findIndex(item => item.pay_type === 20) > -1;
      //     } else {
      //       this.$Message.error(res.data.errormsg)
      //     }
      //   })
      // }
    },
    postSingleRefund() {
      let schedule_amount = ''
      let pt_schedule_id = ''
      
      if (this.currentStadium.ssl_status === 0) {
        schedule_amount = this.currentStadium.put_pt_refund_amount
        pt_schedule_id = this.currentStadium.pt_schedule_id
      }
      this.$service
        .post(this.refundUrl, {
          space_order_id: this.currentStadium.orderId,
          new_pay_type: this.currentStadium.new_pay_type,
          refund_amount: this.finalRefundPrice,
          book_amount: this.currentStadium.put_refund_amount,
          schedule_amount,
          pt_schedule_id,
          sports_mark_order_id: this.currentStadium.sports_mark_order_id,
          remark: this.currentStadium.remarks,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.detailFlag = false
            this.resetDetail()
            this.resetSelectedGrids()
            this.getList()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    postLongTermRentalRefund() {
      // the amount equal new_pay_type amount sum
      let amount = 0
      if (Array.isArray(this.currentStadium.new_pay_type) && this.currentStadium.new_pay_type.length > 0) {
        amount = this.currentStadium.new_pay_type.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber()
      }
      return this.$service
        .post("/Web/SpaceOrderLong/refundSpaceLongItems", {
          user_id: this.currentStadium.user_id,
          batch_number: this.currentStadium.batch_number,
          ids: this.currentStadium.orderId,
          number: 1,
          amount,
          new_pay_type: this.currentStadium.new_pay_type,
          scene: this.longTermRentalLabel === '取消预订' ? 2 : 3,
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.detailFlag = false
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleBookingRefund({ order, refundLabel, refundUrl }, checkType = true) {
     // 单节付费, ssl_status = 0 约课且订场, ssl_status = 1 已经取消约课, ssl_status = 2 已到场上课
     if (order.ssl_status === 0 && checkType) {
        this.oneTimePayOrder = order
        this.oneTimePayLabel = refundLabel
        this.showOneTimePay = true
        return false
      } else if (order.ssl_status === 2 && checkType) {
        // this.$Message.error('场地关联的约课已签到或上课，无法取消订场')
        this.$service.post('/Web/SpaceOrder/get_space_schedule_info', {
          ssl_id: order.ssl_id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.noCancelOrder = res.data.data
            this.noCancelOrder.username = order.username
            this.noCancelVisible = true
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        return false
      }


      // long term rental
      if (order.type === 2 && checkType) {
        this.showLongTermRental = true
        this.longTermRentalOrder = order
        this.longTermRentalLabel = refundLabel
        return false
      }

      this.showSingleRefund(order, refundLabel, refundUrl)
    },
    async handleLightClick(order, isRefund = false) {
      this.resetDetail()
      this.isRefund = isRefund
      const { currentStadium, sub } = this
      let {
        id,
        orderId,
        name,
        openHalf,
        position,
        user_type,
        time,
        user_id,
        username,
        order_sn,
        phone,
        status,
        schedule_price,
        all_price,
        coupon_amount,
        fee,
        refund_rate,
        from,
        remarks,
        type,
        date,
        batch_number,
        space_type_name,
        payment,
        coach_name,
        card_name,
        reservation_type,
        start_time_format,
        course_time,
        should_refund_amount,
        put_refund_amount,
        pt_schedule_id,
        sports_mark_order_id,
        ssl_status,
      } = order

      let payTypeList = cloneDeep(order.new_pay_type)
      let total_price = Math.max(0, sub(all_price, coupon_amount))

      // 长租订单
      if (order.type === 2) {
        const res = await this.$service.post('/Web/SpaceOrderLong/getLongOrderDetail', { 
          id: order.long_order.id
        })
        payTypeList = cloneDeep(res?.data?.data?.new_pay_type || [])
        total_price = res?.data?.data?.amount || 0
      }

      this.oneTimePayPrice = null
      this.oneTimePayType = []

      // 退款类型中是否包收钱吧
      let isRefundNeedSQB = false
      const payTypeIndex = payTypeList.findIndex(item => [20, 21].includes(item.pay_type));
      if (payTypeIndex !== -1) {
        const item = payTypeList[payTypeIndex];
        if (!item?.is_not_show) {
          isRefundNeedSQB = true
        }
      }

      // 包含约课的
      this.cptPrice = 0;
      if (order.reservation_type === 2) {
        const res2 = await this.$service.post('/Web/SpaceOrder/scheduleAndBookRefundInfo', {
          space_order_id: order.orderId,
          user_id: order.user_id,
        })

        if (order.ssl_status !== 1) {
          const info = res2.data.data.pt_refund_info;
          course_time = info.course_time;
          card_name = info.class_name || info.card_name;
          this.currentStadium.pt_refund_rate = info.refund_rate;
          this.currentStadium.pt_refund_amount = info.refund_amount || 0;
          this.currentStadium.put_pt_refund_amount = info.refund_amount || 0;
          this.currentStadium.pt_income_amount = info.income_amount;
          this.cptPrice = info.income_amount || 0;
        }

        // 详情用 payment_method, 退款用 new_pay_type
        if (this.isRefund) {
          payTypeList = cloneDeep(res2?.data?.data?.new_pay_type || [])
        } else {
          payTypeList = cloneDeep(res2?.data?.data?.payment_method || [])
        }

        // 退款类型中是否包收钱吧
        const payTypeIndex = payTypeList.findIndex(item => [20, 21].includes(item.pay_type));
        if (payTypeIndex !== -1) {
          const item = payTypeList[payTypeIndex];
          if (!item?.is_not_show) {
            isRefundNeedSQB = true
          }
        }
      }

      // 退款的时候用 max_amount
      if(!this.isRefund) {
        payTypeList = payTypeList.map(item => {
          return {
            ...item,
            amount: Number(item?.max_amount || item.amount),
          }
        })
      }

      Object.assign(currentStadium, {
        id,
        orderId,
        name,
        openHalf,
        between: time,
        user_id,
        new_pay_type: payTypeList,
        order_sn,
        phone,
        status,
        isDetail: true,
        schedule_price,
        all_price,
        total_price,
        fee,
        refund_rate,
        from,
        remarks,
        isHalf: position === 'all' ? 0 : 1,
        isMember: user_type,
        type,
        date,
        batch_number,
        username,
        space_type_name,
        payment,
        coach_name,
        card_name,
        reservation_type,
        start_time_format,
        course_time,
        should_refund_amount,
        put_refund_amount,
        pt_schedule_id,
        sports_mark_order_id,
        ssl_status,
        isRefundNeedSQB,
      })
      this.search = username
      this.selectedDiscount = { discount_amount: coupon_amount || 0 }
      this.detailFlag = true
    },
    handleGridMove: throttle(function (id, index, site) {
      if (this.movingListenerFlag) {
        // 同一行
        if (this.selectedGrids.rowId === id && this.selectedGrids.site === site) {
          this.selectedGrids.end = index
        }
      }
    }, 69),
    async setPlanSet(sch) {
      this.planTimeSet = {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      }

      const strategy = { schedule_price: {} }
      for (const key in sch) {
        if (Object.hasOwnProperty.call(sch, key)) {
          const url = sch[key]
          strategy.schedule_price[key] = await getWeekJson(url)
        }
      }

      for (const key in strategy.schedule_price) {
        if (Object.hasOwnProperty.call(strategy.schedule_price, key)) {
          const day = strategy.schedule_price[key]
          day.list.forEach((item, index) => {
            this.planTimeSet[key].push([item.start_time, item.end_time])
          })
        }
      }
    },
    getPlanList(date) {
      let timeList = []
      switch (date.getDay()) {
        case 1:
          timeList = this.planTimeSet.monday
          break
        case 2:
          timeList = this.planTimeSet.tuesday
          break
        case 3:
          timeList = this.planTimeSet.wednesday
          break
        case 4:
          timeList = this.planTimeSet.thursday
          break
        case 5:
          timeList = this.planTimeSet.friday
          break
        case 6:
          timeList = this.planTimeSet.saturday
          break
        case 0:
          timeList = this.planTimeSet.sunday
          break
        default:
          timeList = this.planTimeSet.monday
          break
      }
      return timeList
    },
    handleRestClick(row) {
      this.spaceId = row.id + '_0';
      this.occupyDate = this.searchPost.date;
      this.occupyTime = [];
      this.showOccupyModal = true;

      // this.currentStadium.id = row.id
      // this.currentStadium.name = row.name
      // return this.$service.post('/Web/Space/restList', { space_id: row.id }).then(async (res) => {
      //   if (res.data.errorcode === 0) {
      //     await this.setPlanSet(res.data.data.schedules)
      //     const restList = res.data.data.rest_list
      //     this.restDateList = []
      //     if (Array.isArray(restList)) {
      //       // sort by date
      //       restList.sort((a, b) => new Date(a.start_time) - new Date(b.start_time))
      //       // package by date
      //       restList.forEach((rest) => {
      //         const date = new Date(rest.start_time.split(' ')[0])
      //         let restDate = this.restDateList.find((item) => item.date.getTime() === date.getTime())
      //         if (!restDate) {
      //           const planList = this.getPlanList(date)
      //           let timeList = []
      //           planList.forEach((plan) => {
      //             timeList.push({
      //               id: '',
      //               checked: false,
      //               start_time: plan[0],
      //               end_time: plan[1],
      //             })
      //           })
      //           restDate = {
      //             // id: "",
      //             date,
      //             timeList,
      //             saveFlag: false,
      //           }
      //           this.restDateList.push(restDate)
      //         }
      //         // add time from restList
      //         const start_time = rest.start_time.split(' ')[1]
      //         restDate.timeList.forEach((item) => {
      //           if (item.start_time === start_time) {
      //             item.id = rest.id
      //             item.checked = true
      //           }
      //         })
      //       })
      //     }
      //     this.restDialogFlag = true
      //   } else {
      //     this.$Message.error(res.data.errormsg)
      //   }
      // })
    },
    handleRestHide(flag) {
      if (!flag) {
        this.resetDetail()
        this.resetSelectedGrids()
        this.getList()
      }
    },
    handleRestAll(idx) {
      let rest = this.restDateList[idx]

      if (rest.indeterminate) {
        rest.checkAll = true
        rest.indeterminate = false
      } else {
        rest.checkAll = !rest.checkAll
      }

      if (rest.checkAll) {
        rest.saveFlag = true
        rest.timeList.forEach((item) => {
          item.checked = true
        })
      } else {
        rest.saveFlag = false
        rest.timeList.forEach((item) => {
          item.checked = false
        })
      }
    },
    handleRestAdd() {
      this.restDateList.push({
        // id: "",
        date: '',
        timeList: [],
        saveFlag: false,
      })
    },
    handleTimeClick($index, $timeIndex) {
      let time = this.restDateList[$index].timeList[$timeIndex]
      time.checked = !time.checked
      this.checkIndeterminate($index)
      this.checkSaveFlag($index)
    },
    checkSaveFlag(idx) {
      const rest = this.restDateList[idx]
      const count = rest.timeList.filter((item) => item.checked).length
      if (count > 0) {
        rest.saveFlag = true
      } else {
        rest.saveFlag = false
      }
    },
    checkIndeterminate(idx) {
      let rest = this.restDateList[idx]
      const allCount = rest.timeList.length
      const checkedCount = rest.timeList.filter((item) => item.checked).length
      if (allCount === checkedCount) {
        rest.checkAll = true
        rest.indeterminate = false
      } else if (checkedCount === 0) {
        rest.checkAll = false
        rest.indeterminate = false
      } else {
        rest.checkAll = false
        rest.indeterminate = true
      }
    },
    handleRestDateChange(val, idx) {
      const date = new Date(val)
      const planList = this.getPlanList(date)
      let timeList = []
      planList.forEach((plan) => {
        timeList.push({
          id: '',
          checked: false,
          start_time: plan[0],
          end_time: plan[1],
        })
      })
      this.restDateList[idx].timeList = timeList
    },
    handleRestDelete(idx) {
      const rest = this.restDateList[idx]
      if (!rest.date) {
        this.restDateList.splice(idx, 1)
        return false
      }
      const date = formatDate(rest.date, 'yyyy-MM-dd')
      return this.$service
        .post('/Web/Space/delRest', {
          space_id: this.currentStadium.id,
          date,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.restDateList.splice(idx, 1)
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleRestSubmit(idx) {
      const rest = this.restDateList[idx]
      const date = formatDate(rest.date, 'yyyy-MM-dd')
      const timeList = rest.timeList.filter((item) => item.checked)
      this.restType = 1
      this.restPostData = {
        space_id: this.currentStadium.id,
        id: rest.id,
        date,
        rest: timeList,
      }
      this.showRestModal = true
    },
    handleStadiumDelete(id) {
      this.$Modal.confirm({
        title: '删除场场地将把全部场次信息一并删除！',
        onOk: () => {
          return this.$service
            .post('/Web/Space/delSpace', {
              id, // 场地id
            })
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$Message.info({
                  content: res.data.errormsg,
                  duration: 10,
                })
                this.resetDetail()
                this.resetSelectedGrids()
                this.getList()
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        },
      })
    },
    getLightBulbList(space_id) {
      const categoryName = this.categoryList.find((item) => item.isChecked).name
      const spaceName = this.list.find((item) => item.id == space_id).name
      return this.$service
        .post('/web/light_control/getLineListsBySpaceId', {
          bus_id: this.busId,
          space_id,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.lightBulbList = []
            if (Array.isArray(res.data.data.lists)) {
              res.data.data.lists.forEach((item) => {
                let name = ''
                if (item.position == 0) {
                  name = '全场'
                } else if (item.position == 1) {
                  name = '上半场'
                } else if (item.position == 2) {
                  name = '下半场'
                }
                this.lightBulbList.push({
                  position: item.position,
                  space_id: item.space_id,
                  id: item.id,
                  name: `${categoryName}-${spaceName}-${name}-线路${item.index}`,
                  checked: item.is_on,
                })
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    turnLight(item, flag) {
      // 1:系统开灯 2:系统关灯 3:手动开灯 4:手动关灯 5:二维码开灯 6:二维码关灯
      return this.$service
        .post('/web/light_control/switchLightProduct', {
          admin_id: this.adminId,
          admin_name: this.adminName,
          action_id: flag ? 3 : 4,
          line_id: item.id,
          space_id: item.space_id,
          position: item.position,
          bus_id: this.busId,
        })
        .then((res) => {
          if (res.data.errorcode !== 0) {
            item.checked = item.last_checked
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleLightBulbSwitch(space_id) {
      this.lightFlag = true
      this.getLightBulbList(space_id)
    },
    handleOpenAllLight(val) {
      this.lightBulbList.forEach((item) => {
        item.last_checked = item.checked
        item.checked = val
        this.turnLight(item, val)
      })
    },
    handleOpenLight(line) {
      this.turnLight(line, line.checked)
    },
    setTimeList(start, end) {
      let timeList = []
      const startIndex = this.timeList.indexOf(start)
      const endIndex = this.timeList.indexOf(end)
      this.timeList.slice(startIndex, endIndex + 1).forEach((item) => {
        timeList.push(item)
      })
      if (timeList.length % 2 === 1) {
        timeList.push(this.timeList[endIndex + 1])
      }
      this.timeList = timeList

      const prefixWidth = '20px'
      const suffixWidth = '20px'
      this.gridHeaderStyle = `grid-template-columns: ${prefixWidth} repeat(${
        timeList.length / 2
      }, minmax(100px, 1fr)) ${suffixWidth};`
      // this.gridBuddyStyle = `grid-template-columns: ${prefixWidth/2} ${prefixWidth/2} repeat(${timeList.length}, minmax(50px, 1fr)) ${suffixWidth};`
    },
    setScrollbar(distance) {
      document.getElementById('scrollBar').scrollLeft = distance
    },
    validDate(end_time, start_time) {
      return new Date(end_time).getTime() + 24 * 3600 * 1000 < Date.now() || new Date(start_time).getTime() > Date.now()
    },
    // 1购卡购课', 2'续卡续课', 3'升级', 4'订场地', 5'购散场票'
    // 订场购买只需要判断是否包含4即可
    validActionType(use_limit) {
      let arr = []
      if (use_limit && use_limit.length > 0) {
        arr = use_limit.split(',')
      }
      if (arr.indexOf('5' != -1)) {
        return false
      } else {
        return true
      }
    },
    // 判断是否符合订场
    validCardType(type, lists) {
      let arr = []
      if (lists && lists.length > 0) {
        arr = lists.split(',')
      }
      // 当前场地id this.searchPost.type_id
      // type == 1 全部订场都可以使用
      // type == 2 指定场地可以使用
      let id = this.searchPost.type_id + ''
      if (type == 1) {
        return false
      } else if (type == 2 && arr.indexOf(id) != -1) {
        return false
      } else {
        return true
      }
    },
    // 将折扣券进行排序
    sortDiscount() {
      this.discountList.sort((a, b) => {
        // 排序权重: 1.可用的排前面 2.(不可用里)未用的排前面 3.(可用和不可用)优惠金额大的排前面 4.(可用和不可用优惠金额相同)快到期的排前面
        return (
          a.disabled - b.disabled ||
          a.status - b.status ||
          b.discount_amount - a.discount_amount ||
          new Date(a.end_time) - new Date(b.end_time)
        )
      })
    },
    handleDefaultDiscount() {
      if (this.discountList.length) {
        // 如果折扣券列表第一张是可用的 则进行赋值
        if (!this.discountList[0].disabled) {
          this.selectDiscountIndex = 0
          this.selectedDiscount = this.discountList[0]
          let price = this.sub(this.currentStadium.all_price, this.discountList[0].discount_amount)
          this.showPrice = price < 0 ? 0 : price
          this.allPrice = price < 0 ? 0 : price
          /* 如果折扣券列表第一张是不可用的 处理后续逻辑 */
        } else if (this.discountList[0].disabled) {
          this.selectDiscountIndex = -1
          this.selectedDiscount = ''
          this.discountText = '无可用折扣券'
          this.showPrice = this.currentStadium.all_price
          this.allPrice = this.currentStadium.all_price
        }
      } else {
        this.selectedDiscount = ''
        this.selectDiscountIndex = ''
        this.discountText = '无可用折扣券'
        this.showPrice = this.currentStadium.all_price
        this.allPrice = this.currentStadium.all_price
      }
    },
    dealDiscountList() {
      dealDiscountTimer && clearTimeout(dealDiscountTimer)
      dealDiscountTimer = setTimeout(() => {
        const { discountList, validDate, validActionType, validCardType, sortDiscount, handleDefaultDiscount } = this

        discountList.forEach((item) => {
          const { end_time, start_time, status, limit_amount, use_limit, limit_space_type, space_type_species } = item
          // 每次获取折扣券数据,需要判断折扣卷的适用范围是否包含该场地
          item.disabled =
            limit_amount - this.currentStadium.all_price > 0 ||
            validDate(end_time, start_time) ||
            status != 1 ||
            validActionType(use_limit) ||
            validCardType(limit_space_type, space_type_species)
        })

        sortDiscount()
        handleDefaultDiscount()
      }, 600)
    },
    /* 获取折扣券数据 */
    getDiscountList(userId) {
      const url = '/Web/Coupon/get_user_coupon'
      const params = {
        user_id: userId,
      }
      this.$service
        .post(url, params)
        .then((res) => {
          if (res.data.errorcode === 0) {
            const { data: list } = res.data
            if (list.length) {
              list.forEach((item) => {
                const { use_limit, limit_card } = item
                item.limit_card_text = DISCOUNT_CARD_LIMIT[limit_card - 1] // 卡种限制
                item.use_limit_text = (use_limit ? use_limit.split(',') : ['1'])
                  .map((v) => DISCOUNT_SCOPE_LIMIT[v - 1])
                  .join() // 使用限制 1 购卡 2续卡 3升卡 4订场地 5购散场票
              })
              this.discountList = list

              this.dealDiscountList()
            } else {
              this.discountText = '无可用折扣券'
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    handleSelectDiscount(index) {
      this.selectDiscountIndex = index
      if (index == -1) {
        this.selectedDiscount = ''
        this.discountText = '不使用折扣券'
        this.showPrice = this.currentStadium.all_price
        this.allPrice = this.currentStadium.all_price
      } else {
        this.selectedDiscount = this.discountList[index]
        let price = this.sub(this.currentStadium.all_price, this.discountList[index].discount_amount)
        this.showPrice = price < 0 ? 0 : price
        this.allPrice = price < 0 ? 0 : price
      }
    },
    // 全场半场切换
    changeIsHalf() {
      if (this.currentStadium.isDetail) {
        return
      }
      if (this.currentStadium.user_id) {
        this.userSelected(this.selectedUser)
      } else {
        this.getPrice()
      }
    },
    mul(a, b) {
      var c = 0,
        d = a.toString(),
        e = b.toString()
      try {
        c += d.split('.')[1].length
      } catch (f) {}
      try {
        c += e.split('.')[1].length
      } catch (f) {}
      return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c)
    },

    // 精确运算 减
    sub(a, b) {
      var c, d, e
      try {
        c = a.toString().split('.')[1].length
      } catch (f) {
        c = 0
      }
      try {
        d = b.toString().split('.')[1].length
      } catch (f) {
        d = 0
      }
      return (e = Math.pow(10, Math.max(c, d))), (this.mul(a, e) - this.mul(b, e)) / e
    },

    handleToLongTermRental() {
      this.$service.post('/Web/SpaceOrderLong/checkSpaceLongLimit').then((res) => {
        if (res.data.errorcode === 0) {
          const url = `${window.location.protocol}//${window.location.host}/booking/long-term-rental`
          window.open(url, '_self')
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // handleToLongTermRentalCancel() {
    //   const id = this.longTermRentalOrder?.long_order?.id
    //   const url = `${window.location.protocol}//${window.location.host}/booking/long-term-rental-cancel/${id}`
    //   window.open(url, '_self')
    // },
    handleTodayCancel() {
      this.showLongTermRental = false
      // 仅退定场也走MergeRefund 接口吧
      this.showSingleRefund(this.longTermRentalOrder, this.longTermRentalLabel, '/Web/SpaceOrder/MergeRefund')
      // this.handleBookingRefund({
      //   order: this.longTermRentalOrder,
      //   refundLabel: '取消预订',
      //   refundUrl: '/Web/SpaceOrder/MergeRefund'
      // } , false)
    },
    handleAllCancel() {
      this.showOneTimePay = false
      if (this.oneTimePayLabel === '取消预订') {
        this.showSingleRefund(this.oneTimePayOrder, this.oneTimePayLabel, '/Web/SpaceOrder/MergeRefund')
      } else {
        this.showSingleRefund(this.oneTimePayOrder, this.oneTimePayLabel, '/Web/SpaceOrder/postRefundMix')
      }
    },
    handleRestCancel(spaceId, position) {
      this.$Modal.confirm({
        title: '提示',
        content: '确定取消占用吗?',
        onOk: () => {
          this.handleCancelRest(spaceId, position)
        },
      })
    },
    
    // occupied expand to all of the available work time items
    disableExpand(disableList, workTimeList) {
      const date = formatDate(new Date(), 'yyyy-MM-dd') + ' ';
      let list = [];
      // [[08:00, 08:30], [10:00, 12:30], [22:00, 24:00]]
      disableList.forEach((disableItem) => {
        // find first work time item
        const firstTimestamp = new Date(date + disableItem.start_time).getTime();
        // find inside work time item
        const firstExist = workTimeList.find((workTimeItem) => {
          const startTimestamp = new Date(date + workTimeItem.start_time).getTime();
          const endTimestamp = new Date(date + workTimeItem.end_time).getTime();
          return startTimestamp < firstTimestamp && firstTimestamp < endTimestamp;
        })
        if (firstExist) {
          list.push({
            start_time: firstExist.start_time,
            end_time: firstExist.end_time,
            position: disableItem.position,
            expand: true,
          });
        }
        
        // find last work time item
        const lastTimestamp = new Date(date + disableItem.end_time).getTime();
        const lastExist = workTimeList.find((workTimeItem) => {
          const startTimestamp = new Date(date + workTimeItem.start_time).getTime();
          const endTimestamp = new Date(date + workTimeItem.end_time).getTime();
          return startTimestamp < lastTimestamp && lastTimestamp < endTimestamp;
        })
        if (lastExist) {
          list.push({
            start_time: lastExist.start_time,
            end_time: lastExist.end_time,
            position: disableItem.position,
            expand: true,
          });
        }
      })
      return list;
    },

    // show label 
    handleShowLabelChange() {
      localStorage.setItem('showLabelValue', this.showLabelValue.join(','))
    },

    // one time booking
    getMapCoachList() {
      const url = '/Web/Coach/map_coach_list';
      let postData = {
        date: this.searchPost.date,
      };
      return this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.filteredCoaches = res.data.data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleTypeChanged() {
      this.currentStadium.card_id = '';
      this.currentStadium.card_index = '';
      this.currentStadium.begin_date = '';
      this.currentStadium.card_user_id = '';
      this.radioTimeData = null;
      this.oneTimePayPrice = null
      this.oneTimePayType = []
      this.currentStadium.new_pay_type = []
    },
    async handleCardChange(params) {
      await this.getPrice();
      const course = this.oneTimePayCourseList[this.currentStadium.card_index];
      await this.getOneTimePayPrice({
        ...course,
        ...params,
      }, true);

      this.currentStadium.new_pay_type = [...this.oneTimePayType];
    },
    classChange(value) {
      this.designatedCoach = value;
    },
    coachChanged() {
      // this.userSearch = '';
      // this.selectedUser = null;
      this.radioTimeData = null;
      // this.currentStadium.user_id = '';
      this.currentStadium.card_id = '';
      this.currentStadium.card_index = '';
      this.currentStadium.reservation_type = 1;
      // this.$refs.userSearchRef.$refs.selectRef.values = [];

      // if select coach, fetch one time pay cpt course list
      this.oneTimePayPrice = null;
      this.oneTimePayType = [];
      this.getOneTimePayCourseList();

      // if select coach, fetch the free time of the coach
      this.getCoachFreeTime();
    },
    getCoachFreeTime() {
      if (!this.currentStadium.coach_id) return
      return this.$service.post('/web/Space/getCoachIdleTime', {
        bus_id: this.busId,
        coach_id: this.currentStadium.coach_id,
        date: this.searchPost.date
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.coachFreeTime = res.data.data.list
        }
      })
    },
    cardSelected(index = '') {
      if (index === '') return;

      this.currentStadium.begin_date = '';

      let selectedCard = this.selectedUser?.card_list[index];
      if (this.designatedCoach && this.currentStadium.reservation_type === 1) {
        if (Array.isArray(this.selectedUser.card_list)) {
          let findCoach = Array.isArray(selectedCard.class_coach_id) ? selectedCard.class_coach_id.indexOf(this.currentStadium.coach_id)!== -1 : selectedCard.class_coach_id == this.currentStadium.coach_id
          if (!findCoach) {
            this.$Message.error('该教练不是会员的上课教练');
            this.radioTimeData = false;
            return false;
          }
        }
      }

      if (this.currentStadium && this.currentStadium.reservation_type === 1) {
        this.currentStadium = Object.assign(this.currentStadium, {
          date: this.searchPost.date,
          card_user_id: selectedCard.card_user_id,
          card_id: selectedCard.card_id
        });
        this.getCoachTime(this.currentStadium);
      } else {
        const course = this.oneTimePayCourseList[index];

        this.currentStadium = Object.assign(this.currentStadium, {
          date: this.searchPost.date,
          card_id: course.id,
        }); 
        this.getCoachTime(this.currentStadium).then(() => {
          // default to get price
          if (this.radioTimeData) {
            this.getOneTimePayPrice(course, false, true);
          }
        });
      }
    },
    getCoachTime(data) {
      const { date, coach_id, card_id } = data;
      // const { courseActiveTime: hour_date } = this;
      // const url = '/Web/Coach/pt_schedule_time';
      const url ='/Web/Coach/PtScheduleHour';
      const [start_time, end_time] = this.currentStadium.between.split('~');
      let postData = {
        is_swim: 0,
        card_id,
        coach_id,
        id: '',
        action: 'add',
        date,
        // hour_date,
        start_time,
        end_time,
        appt_type: this.currentStadium.reservation_type - 1
      };
      return this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.radioTimeData = res.data.data.list;
            // this.courseActiveTime = ''
            this.currentStadium.begin_date = ''
          } else {
            this.$Message.error(res.data.errormsg);
          }
          return res
        })
        .catch(err => {
          this.radioTimeData = null;
          this.$Message.error(err);
        });
    },
    // classTimeSelected(info) {
    //   const { hour_date:val, pid_id } = info;
    //   if (this.currentStadium) {
    //     this.currentStadium.begin_date = `${this.searchPost.date} ${val}`;
    //     this.currentStadium.pid_id = pid_id;
    //   }
    //   this.courseActiveTime = val
    // },
    getOneTimePayCourseList() {
      if (this.currentStadium.coach_id === '') return;

      this.$service.post('/Web/PtChargePlan/get_coach_pt_card_list', {
        bus_id: this.$store.state.busId,
        coach_id: this.currentStadium.coach_id,
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.oneTimePayCourseList = res.data.data;
        }
      })
    },
    getOneTimePayPrice(course, hasValueCard = false, reload = false) {
      if (!hasValueCard && this.oneTimePayNoCardPrice && !reload) {
        this.oneTimePayPrice = _.cloneDeep(this.oneTimePayNoCardPrice);
        // this.cptPrice = Number(this.oneTimePayPrice.pay_amount);
        return new Promise((resolve) => resolve());
      }

      return this.$service.post('/Web/PtChargePlan/get_pt_card_low_price', {
        bus_id: this.$store.state.busId,
        user_id: this.selectedUser?.user_id,
        card_id: course.id,
        sort: course.sort,
        pt_charge_plan_id: course.pt_charge_plan_id,
        is_stored: hasValueCard ? 1 : 0,
        card_user_id: course.card_user_id,
        stored_card_id: course.stored_card_id,
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.oneTimePayPrice = res.data.data;
          this.cptPrice = Number(this.oneTimePayPrice.pay_amount);
          if (!hasValueCard) {
            this.oneTimePayNoCardPrice = _.cloneDeep(res.data.data);
          }

          let change = false;
          const newPayType = _.cloneDeep(this.oneTimePayType);
          newPayType.forEach(item => {
            if (item.pay_type === 8) {
              change = true;
              item.card_user_id = this.oneTimePayPrice.card_user_id;
              item.amount = this.oneTimePayPrice.pay_amount;
              item.autoSelect = true;

              this.$nextTick(() => {
                this.$refs?.payTypeListRef?.handleAmountChange(item.pay_type, this.finalPrice);
              })
            }
          })
          
          if (change) {
            this.oneTimePayCardUserId = this.oneTimePayPrice.card_user_id;
            this.oneTimePayType = newPayType;
          } else {
            this.oneTimePayCardUserId = '';
          }
        }
      })
    },
    handleToPrint() {
      window.open(`/stadiumPrint?user_id=${this.currentStadium.user_id}&order_sn=${this.currentStadium.order_sn}`, '_blank');

      const course = this.oneTimePayCourseList[this.currentStadium.card_index];
      setTimeout(() => {
        window.open(`/contractPrint?type=0&user_id=${this.currentStadium.user_id}&card_order_info_id=${this.card_order_info_id}&oper_type=buycard-coach&teamclass_type=2&curriculum_id=${course.id}&card_name=${course.name}`, '_blank');
      }, 1000);
    }
  },
  destroyed() {
    this.closeWebSocket()
  },
  created() {
    this.setTimeList('00:00', '24:00')
    this.getReserveAuth()
    this.getMapCoachList();

    const showLabelValue = localStorage.getItem('showLabelValue') || '0,1';
    this.showLabelValue = showLabelValue.split(',').map(item => Number(item))
  },
}
</script>

<style lang="less" scoped>
.search-box {
  display: flex;
  flex-direction: row;

  .tag-box-scroll {
    overflow-x: auto;
    white-space: nowrap;
    margin: 3px 0;
  }
}

.legend-box {
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;

  .legend {
    display: flex;
    flex-direction: row;
    align-items: center;

    .legend-color {
      width: 13px;
      height: 13px;
      overflow: hidden;
      border-radius: 50%;
      margin-right: 14px;
    }

    .legend-label {
      font-size: 14px;
      color: #999999;
      margin-right: 20px;
    }
  }
}

.button-box {
  height: 70px;

  .button-item {
    margin-top: 16px;
    margin-right: 16px;
    display: inline-block;
  }

  .button-height {
    height: 40px;
  }
}

@regionWidth: 105px;
@regionHeight: 60px;
@prefixWidth: 20px;
@suffixWidth: 20px;

.grid-box {
  overflow: hidden;

  .grid-header-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;

    .grid-header-one {
      font-size: 16px;
      color: #999999;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      width: @regionWidth;
      height: 50px;

      .region {
        width: @regionWidth;
        text-align: left;
        margin-left: 24px;
      }

      .line {
        width: 50px;
        height: 0;
        border: 1px solid #e1e1e1;
        transform: rotate(45deg);
      }

      .date {
        width: @regionWidth;
        text-align: right;
        margin-right: 24px;
      }
    }
  }

  .grid-buddy-row {
    display: flex;
    flex-direction: row;

    .grid-buddy-one-box {
      display: flex;
      flex-direction: column;
      height: calc(100vh - 460px);
      overflow: hidden;
      width: @regionWidth;
      min-width: @regionWidth;

      .ivu-poptip {
        position: relative;
        z-index: 999;
      }

      .grid-buddy-one {
        border-right: 1px solid #dddddd;
        border-bottom: 1px solid #dddddd;
        width: @regionWidth;
        height: @regionHeight;

        .label {
          width: @regionWidth;
          height: @regionHeight;
          font-size: 14px;
          color: #999999;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .grid-header {
    background-color: white;
    display: grid;
    grid-template-columns: @prefixWidth repeat(24, minmax(100px, 1fr)) @suffixWidth;
    grid-template-rows: 50px;
    text-align: center;
    user-select: none;
    overflow: hidden;

    .grid-header-other {
      .scratch {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .small-line {
          width: 0;
          height: 13px;
          border: 1px solid #dddddd;
        }

        .big-line {
          width: 0;
          height: 21px;
          border: 1px solid #dddddd;
        }
      }

      .hour {
        height: 50px;
        font-size: 14px;
        color: #999999;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time-box {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          height: 100%;
        }

        .short-line {
          width: 2px;
          height: 10px;
          background: #959595;
          border-radius: 1px;
          margin-top: 14px;
        }

        .long-line {
          width: 2px;
          height: 24px;
          background: #959595;
          border-radius: 1px;
        }
      }
    }
  }

  .grid-buddy {
    width: 100%;
    height: calc(100vh - 460px);
    overflow: auto;
    background-color: white;
    display: grid;
    // grid-template-columns: @prefixWidth/2 @prefixWidth/2 repeat(48, minmax(50px, 1fr)) @suffixWidth;
    // grid-template-rows: repeat(9, @regionHeight);
    text-align: center;

    .time-line {
      z-index: 888;
      margin-top: -@regionHeight;
      width: 0;

      .line {
        width: 0;
        height: 100%;
        border-left: 1px solid #17bf6a;
      }
    }

    .disabled-time {
      background-color: #eeeeee;
      // cursor: pointer;

      // /deep/ .ivu-poptip-rel {
      //   width: 100%;
      //   height: 100%;
      // }
      // /deep/ .ivu-tooltip {
      //   position: absolute;
      //   left: 2px;
      //   top: 0;
      // }
    }
    .rest-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;

      .rest-left-top {
        align-self: flex-start;
        margin-left: 4px;
      }
    }

    .row-one {
      border-right: 1px solid #dddddd;
      border-bottom: 1px solid #dddddd;
      width: @regionWidth;
      height: @regionHeight;
      .label {
        width: @regionWidth;
        height: @regionHeight;
        font-size: 14px;
        color: #999999;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.light-switch {
  width: @regionWidth;
  border: 1px solid #70b603;
  border-radius: 6px;
  background-color: #e0eecc;
  position: relative;
  top: -@regionHeight;
}

.light-switch-all {
  height: @regionHeight;
  .light-switch;
}

.light-switch-half {
  height: @regionHeight / 2;
  .light-switch;
}

.light-switch-half-down {
  .light-switch-half;
  top: -@regionHeight / 2;
}

.light-card {
  border: 1px solid #eeeeee;
  margin: 10px 0;
  padding: 4px 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.light-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.action {
  width: 100%;
  height: 66px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .row-ti-box {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 37px;
    height: 60px;
    cursor: pointer;

    .row-icon {
      width: 18px;
      height: 18px;
    }

    .row-tip {
      font-size: 12px;
      color: #1b1b1b;
      white-space: pre-wrap;
      text-align: center;
      margin-top: 7px;
    }
  }
}

.rest-time-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .rest-time-line {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .rest-tag-box {
      width: 470px;
    }
  }

  .rest-add-btn {
    margin-top: 10px;
    align-self: baseline;
  }
}

.header {
  width: 100%;
  color: #19be6b;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;

  h3 {
    padding-left: 5px;
    font-weight: normal;
  }
}

.form-no-bottom /deep/ .ivu-form-item {
  margin-bottom: 0;
}

.none-lightbulb {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #999999;
}
.remark-con {
  width: 110px;
  line-height: 30px;
  padding: 0 10px;
  white-space: normal;
  word-break: break-all;
}

.tip-box {
  display: flex;

  .tip-info {

    .info-label {
      font-size: 12px;
      line-height: 20px;
      height: 20px;
      color: #666666;
      display: flex;
      align-items: center;

      .info-label-tag {
        background-color: #86ca86; 
        color: white; 
        padding: 1px 4px; 
        border-radius: 4px; 
        font-size: 10px; 
        line-height: 12px;
        margin-left: 6px;
      }

      .info-label-text {
        margin-left: 6px;
      }
    }

    .info-title {
      font-size: 16px;
      font-weight: bold;
      line-height: 30px;
      color: #333333;
    }
  }
}

.tip-btn {
  width: 100%;
  border-top: 1px solid #dddddd;
  padding-top: 10px;
  text-align: center;
}

.modal-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;

  .modal-divider {
    width: 50%;

    .price-box {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
    }
  }
}
</style>
