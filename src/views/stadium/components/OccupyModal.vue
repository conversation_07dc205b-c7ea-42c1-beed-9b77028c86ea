<template>
  <Modal :title="spaceMode ? '场地占用' : '场次占用'" v-model="visible" v-bind="$attrs" width="600px" :mask-closable="false"
    :closable="false" v-on="$listeners">
    <Form ref="formRef" :model="formData" :rules="formRules" :label-width="80">
      <template v-if="spaceMode">
        <FormItem label="场地" prop="space_ids">
          <Select v-model="formData.space_ids" placeholder="请选择场地" filterable multiple>
            <Option v-for="option in spaceList" :value="option.id" :key="option.id">{{ option.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="日期" prop="date">
          <DatePicker type="date" v-model="formData.date" placeholder="请选择日期" format="yyyy-MM-dd"
            :options="datePickerOptions" style="width: 100%" :clearable="false" :editable="false" />
        </FormItem>
        <FormItem label="占用时间" prop="time">
          <TimePicker :steps="[1, 30]" v-model="formData.time[0]" format="HH:mm" placeholder="开始时间" style="width: 100px"
            :clearable="false" :editable="false" />
          <!-- <Tooltip content="至"> -->
          <!-- <Icon type="md-arrow-round-forward" size="20" style="margin: 0 10px" /> -->
          <span style="margin: 0 10px; font-size: 16px; font-weight: bold;">~</span>
          <!-- </Tooltip> -->
          <TimePicker :steps="[1, 30]" v-model="formData.time[1]" format="HH:mm" placeholder="结束时间" style="width: 100px"
            :clearable="false" :editable="false" />
        </FormItem>
      </template>
      <template v-else>
        <FormItem label="场地">
          {{ spaceName }}
        </FormItem>
        <FormItem label="场次时间">
          {{ restTime }}
        </FormItem>
      </template>
      <FormItem label="类型" :error="tagError">
        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 6px">
          <Button :icon="item.checked ? 'md-checkbox-outline' : 'md-square-outline'" size="small"
            v-for="(item, index) in tagList" :key="item.type" :type="item.checked ? 'success' : 'dashed'"
            @click="handleTagClick(index)">
            {{ item.type }}
          </Button>
          <Button v-show="!tagAddMode" icon="md-add" type="primary" ghost size="small" @click="handleTagAdd">
            添加
          </Button>
          <Input ref="tagAddInputRef" v-show="tagAddMode" v-model="tagAddInput" placeholder="请输入类型名称" size="small"
            style="width: 120px" />
          <div style="display: flex; align-items: center;">
            <Icon v-show="tagAddMode" type="ios-checkmark-circle-outline" style="color: #19be6b; font-size: 18px; cursor: pointer;"
              @click="handleTagAddSubmit">
            </Icon>
            <Icon v-show="tagAddMode" type="ios-close-circle-outline" style="color: #ed4014; font-size: 18px; margin-left: 4px; cursor: pointer;"
              @click="handleTagAddCancel">
            </Icon>
          </div>
          <Button v-show="selectedTagId && !tagAddMode" icon="md-remove" type="error" ghost size="small"
            @click="handleTagRemove">
            删除
          </Button>
        </div>
      </FormItem>
      <FormItem label="占用人信息" prop="information">
        <Input v-model="formData.information" placeholder="名称/电话" />
      </FormItem>
      <FormItem label="费用金额" prop="price">
        <InputNumber v-model="formData.price" placeholder="请输入" :precision="2" :min="0" :step="0.01"
          :active-change="false" style="width: 100%" />
      </FormItem>
      <FormItem label="备注" prop="remark">
        <Input v-model="formData.remark" placeholder="请输入" type="textarea" :rows="6" :maxlength="500" show-word-limit />
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleSubmit" :loading="loading">确定</Button>
      <Button @click="handleCancel">取消</Button>
    </div>
  </Modal>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, computed, nextTick, getCurrentInstance } from 'vue';
import { Message } from 'iview';
import service from '@/service';
import { formatDate } from 'utils';
import _ from 'lodash';

// instance
const ins = getCurrentInstance();
const bus_id = ins.proxy.$store.state.busId;

// props & emits
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  spaceId: {
    type: String,
    default: '',
  },
  spaceTypeId: {
    type: String,
    default: '',
  },
  date: {
    type: String,
    default: '',
  },
  time: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['update:show']);

// space list
const spaceList = ref([]);
const spaceName = computed(() => {
  return spaceList.value.find((item) => item.id === props.spaceId)?.name;
});

const getSpaceList = () => {
  service
    .post('/Web/SpaceOrderLong/getAllSpace', {
      bus_id,
      space_type_id: props.spaceTypeId,
    })
    .then((res) => {
      if (res.data.errorcode === 0) {
        spaceList.value = res.data.data;
      }
    });
};

// tag list
const tagList = ref([]);
const tagAddMode = ref(false);
const tagAddInput = ref('');
const tagAddInputRef = ref(null);

const selectedTagId = computed(() => {
  return tagList.value.find((item) => item.checked)?.id || '';
});

const tagChooseCount = ref(0);
const tagError = computed(() => {
  return (tagChooseCount.value && !selectedTagId.value) ? '请选择类型' : '';
})

const handleTagClick = (clickIndex) => {
  tagChooseCount.value++;
  tagList.value.forEach((item, index) => {
    if (clickIndex === index) {
      item.checked = !item.checked;
    } else {
      item.checked = false;
    }
  });
  formData.value.category = selectedTagId.value;
};
const handleTagAdd = () => {
  tagAddMode.value = true;
  nextTick(() => {
    tagAddInputRef.value.focus();
  });
};
const handleTagAddSubmit = () => {
  if (!tagAddInput.value) {
    Message.warning('请输入类型名称');
    tagAddInputRef.value.focus();
    return;
  }

  const existIndex = tagList.value.findIndex((item) => item.type === tagAddInput.value);
  if (existIndex !== -1) {
    Message.warning('类型已存在');
    tagAddInputRef.value.focus();
    return;
  }

  service
    .post('/Web/Space/saveRestType', {
      type: tagAddInput.value,
    })
    .then((res) => {
      if (res.data.errorcode === 0) {
        getTagList(tagAddInput.value).then(() => {
          tagAddInput.value = '';
          tagAddMode.value = false;
          formData.value.category = selectedTagId.value;
        })
      } else {
        Message.error(res.data.errormsg);
      }
    });
};
const handleTagAddCancel = () => {
  tagAddInput.value = '';
  tagAddMode.value = false;
};
const handleTagRemove = () => {
  const tag = tagList.value.find((item) => item.checked);
  if (!tag) {
    Message.warning('请选择类型');
    return;
  }
  if (tag.can_deleted === 0) {
    Message.warning('该类型无法删除');
    return;
  }

  service
    .post('/Web/Space/saveRestType', {
      id: tag.id,
    })
    .then((res) => {
      if (res.data.errorcode === 0) {
        getTagList();
        formData.value.category = selectedTagId.value;
      } else {
        Message.error(res.data.errormsg);
      }
    });
};
const getTagList = (type = '') => {
  return service.get('/Web/Space/getRestType').then((res) => {
    if (res.data.errorcode === 0) {
      const list = res.data.data;
      list.forEach((item) => {
        if (item.type === type) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      });
      tagList.value = list;
    } else {
      Message.error(res.data.errormsg);
    }
  });
};
getTagList();

// variables
const spaceMode = ref(true);
const loading = ref(false);
const visible = ref(false);
const datePickerOptions = ref({
  disabledDate(date) {
    return date && date.valueOf() < Date.now() - 86400000;
  },
});
const formRef = ref(null);
const NONE_FORM_DATA = {
  space_ids: [],
  date: '',
  time: [],
  category: '',
  information: '',
  price: null,
  remark: '',
}
const formData = ref(_.cloneDeep(NONE_FORM_DATA));
const formRules = ref({
  space_ids: [
    {
      required: true,
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value.length) {
          return callback(new Error('请选择场地'));
        } else {
          let occupyError = false;
          try {
            if (spaceMode.value) {
              const list = [];
              formData.value.space_ids.forEach((item) => {
                const [id, position] = item.split('_');
                const items = [];
                if (position === '0') {
                  items.push(id + '_up');
                  items.push(id + '_down');
                } else if (position === '1') {
                  items.push(id + '_up');
                } else if (position === '2') {
                  items.push(id + '_down');
                }

                // check for repeat item
                if (items.some((item) => list.includes(item))) {
                  occupyError = true;
                  throw new Error('场地重复哒!');
                }

                list.push(...items);
              })
            }
          } catch (error) {
            console.log(error);
          }

          if (occupyError) {
            return callback(new Error('场地重复占用, 请检查!'));
          }
        }
        callback();
      },
    },
  ],
  date: [
    {
      required: true,
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请选择日期'));
        }
        callback();
      },
    },
  ],
  time: [
    {
      required: true,
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!Array.isArray(value)) {
          return callback(new Error('请选择时间段'));
        } else if (Array.isArray(value) && value.length === 0) {
          return callback(new Error('请选择时间段'));
        } else if (Array.isArray(value) && value.length === 2) {
          if (!value[0] || !value[1]) {
            return callback(new Error('请选择时间段'));
          } else if (value[0] === value[1] && value[1] !== '00:00') {
            return callback(new Error('请选择不同的时间'));
          } else if (value[0] > value[1] && value[1] !== '00:00') {
            return callback(new Error('开始时间不能大于结束时间'));
          }
        }
        callback();
      },
    },
  ],
});

const restTime = computed(() => {
  if (spaceMode.value) {
    return '';
  } else {
    let start = '';
    let end = '';
    if (Array.isArray(props.time) && props.time.length) {
      start = props.time[0].start_time;

      const len = props.time.length;
      end = props.time[len - 1].end_time;
    }

    return `${start} ~ ${end}`;
  }
});

// methods
const handleCancel = () => {
  emit('update:show', false);
};
const handleSubmit = () => {
  tagChooseCount.value++;

  // tag require check
  if (tagError.value) {
    return;
  }

  loading.value = true;

  formRef.value.validate((valid) => {
    if (valid) {
      let url = '';
      let postData = {};
      if (spaceMode.value) {
        url = '/Web/Space/saveRest';
        postData = {
          space_id: formData.value.space_ids,
          date: formatDate(formData.value.date, 'yyyy-MM-dd'),
          rest: formData.value.time,
          rest_type: formData.value.category,
          userinfo: formData.value.information,
          amount: formData.value.price,
          remarks: formData.value.remark,
        };
      } else {
        url = '/Web/Space/addRest';
        postData = {
          space_id: props.spaceId,
          date: props.date,
          rest: props.time,
          rest_type: formData.value.category,
          userinfo: formData.value.information,
          amount: formData.value.price,
          remarks: formData.value.remark,
        };
      }

      service.post(url, postData).then((res) => {
        if (res.data.errorcode === 0) {
          Message.success(res.data.errormsg);
          emit('on-success');
          emit('update:show', false);
        } else {
          Message.error(res.data.errormsg);
        }
        loading.value = false;
      });
    } else {
      loading.value = false;
    }
  });
};

// watch
watch(
  () => props.show,
  (val) => {
    visible.value = val;
    if (val) {
      loading.value = false;
      tagChooseCount.value = 0;
      tagList.value.forEach((item) => {
        item.checked = false;
      });

      formData.value = _.cloneDeep(NONE_FORM_DATA);
      formData.value.space_ids = [props.spaceId];
      formData.value.date = props.date;

      nextTick(() => {
        if (props.time.length) {
          spaceMode.value = false;
        } else {
          spaceMode.value = true;
        }
      })
    }
  },
  { immediate: true }
);

watch(
  () => props.spaceTypeId,
  (val) => {
    if (val) {
      getSpaceList();
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped></style>
