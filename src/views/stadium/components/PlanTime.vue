<template>
  <div class="plan-time-box">
    <div class="plan-time-line" v-for="(between, $index) in planTimeList" :key="$index">
      <TimePicker class="plan-time-item" format="HH:mm" type="timerange" placement="bottom-end"
        :value="between" @on-change="handleTimeChange($event, $index)" placeholder="开始时间 ~ 结束时间" 
        :steps="[1, 30]" :disabled-hours="[0,1,2,3,4,5,6,7]" size="large" transfer></TimePicker>
      <Button icon="ios-trash" type="error" @click="handleTimeDelete($index)" ghost>删除</Button>
    </div>
    <Button icon="ios-add" type="primary" class="plan-add-btn" @click="handleTimeAdd('monday')">添加</Button>
  </div>
</template>

<script>
export default {
  props: {
    planTimeList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleTimeChange(val, idx) {
      this.planTimeList[idx] = val;
    },
    handleTimeDelete(idx) {
      this.planTimeList.splice(idx, 1);
    },
    handleTimeAdd() {
      this.planTimeList.push(["08:00", "08:00"]);
    }
  }
};
</script>

<style lang="less" scoped>
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan-time-box {
  flex-direction: column;
  .center;

  .plan-time-line {
    margin-top: 10px;

    .plan-time-item {
      width: 300px;
      margin-right: 10px;
    }
  }

  .plan-add-btn {
    margin-top: 10px;
    margin-left: 75px;
    align-self: baseline;
  }
}
</style>
