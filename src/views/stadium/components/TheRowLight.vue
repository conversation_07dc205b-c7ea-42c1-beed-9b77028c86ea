<template>
  <div :class="className" :style="order.grid" v-on="$listeners">
    <Tooltip theme="light" style="width: 100%; height: 100%" transfer>
      <div slot="content">
        <div class="tip">
          <div>
            <div class="tip-label" style="font-weight: bold">
              <span>{{ order.name }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/booking/date.png" />
              <span>{{ order.time }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/booking/user.png" />
              <span>{{ order.username }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/booking/phone.png" />
              <span>{{ order.phone }}</span>
            </div>
            <div class="tip-label" style="font-weight: bold">
              <img class="row-icon" src="@/assets/img/booking/money.png" />
              <span>{{ order.fee }}元</span>
            </div>
            <div class="tip-label" style="font-weight: bold; border: none">
              <img class="row-icon" src="@/assets/img/booking/emmo.png" />
              <span :style="{ color: order.color }">{{ order.statusLabel }}</span>
            </div>
          </div>
          <div v-if="order.ssl_status !== 1" style="margin-left: 8px">
            <div class="tip-label" style="font-weight: bold">
              <span>课程</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/stat_null_2.png" />
              <span>{{ order.card_name }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/admin1.png" />
              <span>{{ order.coach_name }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/booking/money.png" />
              <span>{{ order.reservation_type == 1 ? '用卡预约' : order.schedule_amount + '元' }}</span>
            </div>
            <div class="tip-label">
              <img class="row-icon" src="@/assets/img/booking/date.png" />
              <span>{{ order.schedule_time }}</span>
            </div>
          </div>
          <div class="remark" v-if="order.remarks">
            <div class="tip-label">
              <Icon class="icon-mr" type="ios-information-circle" />
              备注
            </div>
            <div class="remark-con">
              {{ order.remarks }}
            </div>
          </div>
        </div>
        <div class="tip-btn">
          <Button
            v-if="order.status === 1 && showLongTermRentalTask"
            type="text"
            @click="$emit('emitBookingRefund', { order, refundLabel, refundUrl })"
          >
            <img style="margin-right: 4px" src="@/assets/img/booking/refund.png" />
            <span style="color: #ff6600">{{ refundLabel }}</span>
          </Button>
        </div>
      </div>
      <div :class="consoleName">
        <div class="icon" v-if="order.remarks && order.type == 1" title="备注">
          <Icon type="ios-information-circle" />
        </div>
        <div class="icon" v-if="order.type == 2">
          <span class="icon-text">长租</span>
        </div>
        <div class="label" v-if="showName && showLabelValue.includes(0)">{{ order.username }}</div>
        <div class="phone" v-if="showPhone && showLabelValue.includes(1)">{{ order.phone }}</div>
        <div class="label" v-if="showName && showLabelValue.includes(2)">{{ order.coach_name }}</div>
        <div class="label" v-if="showName && showLabelValue.includes(3)">{{ order.card_name }}</div>
      </div>
    </Tooltip>
  </div>
</template>

<script>
export default {
  props: {
    order: {
      type: Object,
      required: true,
    },
    showLongTermRentalTask: {
      type: Boolean,
      default: false,
    },
    showLabelValue: {
      type: Array,
      default: () => {
        return []
      },
    }
  },
  computed: {
    className() {
      if (this.order.position === 'all') {
        return 'row-light'
      } else if (this.order.position === 'up') {
        return 'row-light-up'
      } else if (this.order.position === 'down') {
        return 'row-light-down'
      } else {
        return 'row-light'
      }
    },
    consoleName() {
      if (this.order.position === 'all') {
        return 'console-all'
      } else {
        return 'console-half'
      }
    },
    refundLabel() {
      if (this.order.endTime > Date.now()) {
        return '取消预订'
      } else {
        return '退款'
      }
    },
    refundUrl() {
      // 仅退定场也走MergeRefund 接口吧
      if (this.order.endTime > Date.now()) {
        return '/Web/SpaceOrder/MergeRefund'
      } else {
        return '/Web/SpaceOrder/postRefundMix'
      }
    },
    showName() {
      if (this.order.position === 'all') {
        return true;
      }
      
      const step = this.order.end - this.order.start;
      if (this.order.type == 2) {
        if (step === 1) {
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    showPhone() {
      if (this.order.position === 'all') {
        return true;
      }

      const step = this.order.end - this.order.start;
      if (this.order.type == 2) {
        if (step === 1) {
          return false;
        } else {
          return true;
        }
      } else {
        if (step === 1) {
          return false;
        } else {
          return true;
        }
      }
    }
  },
}
</script>

<style lang="less" scoped>
@regionHeight: 60px;

.row-light {
  position: relative;
  border-left: 3px solid #333333;
  overflow: hidden;
  cursor: pointer;
}

.row-light-up {
  .row-light;
  height: calc(@regionHeight / 2 - 1px);
}

.row-light-down {
  .row-light;
  height: calc(@regionHeight / 2 - 1px);
  margin-top: calc(@regionHeight / 2 + 1px);
}

.tip {
  display: flex;
  .remark {
    width: 110px;
    line-height: 30px;
    padding: 0 10px;
    white-space: normal;
    word-break: break-all;
  }
  .tip-label {
    font-size: 14px;
    color: #434343;
    border-bottom: 1px solid #eeeeee;
    height: 30px;
    line-height: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .row-icon {
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }
  }
}

.tip-btn {
  width: 100%;
  text-align: center;
  border-top: 1px solid #eeeeee;
}

.console-all {
  height: @regionHeight;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .icon {
    align-self: flex-start;
    margin: 0 0 0 2px;
    padding: 0;

    .icon-text {
      font-size: 10px;
      color: white;
      border-radius: 4px;
      padding: 1px 6px;
      background-color: rgb(91, 91, 221);
    }
  }
  
  .label {
    font-size: 12px;
    color: #000000;
  }

  .phone {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
  }
}

.console-half {
  height: calc(@regionHeight / 2 - 1px);
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;

  .icon {
    align-self: flex-start;
    margin: 0 0 0 2px;
    padding: 0;

    .icon-text {
      font-size: 10px;
      color: white;
      border-radius: 4px;
      padding: 1px 6px;
      background-color: rgb(91, 91, 221);
    }
  }

  .label {
    font-size: 12px;
    color: #000000;
    flex-grow: 1;
  }

  .phone {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    flex-grow: 2;
  }
}
</style>
