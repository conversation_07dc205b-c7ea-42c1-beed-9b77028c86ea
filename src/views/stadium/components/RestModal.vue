<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         title="休息"
         :width="750">
    <Form ref="postForm"
          :model="postForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item label="备注"
                 prop="remarks">
        <textarea rows="3"
                  maxlength="90"
                  v-model="postForm.remarks"></textarea>
      </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="handleRest">确定</Button>
      <Button
              @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'RestModal',
    props: {
      value: {
        type: Boolean
      },
      postData: {
        type: Object
      },
      type: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        postForm: {
          remarks: ''
        }
      }
    },
    computed: {
      showModal: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showModal(val) {
        if (!val) {
          this.$refs.postForm.resetFields()
        }
      }
    },
    created() {
    },
    methods: {
      handleRest() {
        const url = this.type===1?'/Web/Space/saveRest':'/Web/Space/addRest'
        this.$service
          .post(url, {
            ...this.postData,
            ...this.postForm,
          })
          .then((res) => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              this.$emit('on-success')
              this.showModal = false
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
  }
</script>

<style scoped>

</style>
