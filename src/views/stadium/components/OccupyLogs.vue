<template>
  <div class="long-term-rental-tasks">
    <Row class="box-head">
      <Col span="24" class="head-option">
      <DatePicker v-model="dateRange" placeholder="预订时间" type="daterange" :clearable="false" transfer
        class="option-item" />
      <Select v-model="searchParams.rest_type" placeholder="占用类型" clearable transfer class="option-item">
        <Option v-for="item in tagList" :value="item.id" :key="item.id">{{ item.type }}</Option>
      </Select>
      <Select v-model="searchParams.action" placeholder="操作" clearable transfer class="option-item">
        <Option v-for="(item, index) in actionList" :value="item.value" :key="index">{{ item.label }}</Option>
      </Select>
      <Button icon="ios-search" type="primary" ghost @click="handleSearch">搜索</Button>
      </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" class="height-table" :columns="columns" :data="list" disabled-hover stripe></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col span="6" class="foot-option" style="justify-content: flex-start">
      <ExportButton url="/Web/Space/getSpaceRestRecord" :data="searchParams" />
      </Col>
      <Col span="18" class="foot-option">
      <Page @on-change="handlePage" :total="total" :current="searchParams.page_no" @on-page-size-change="pageSizeChanged"
        show-total show-sizer></Page>
      </Col>
    </Row>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import service from '@/service';
import ExportButton from '@/components/form/ExportButton'
import { formatDate } from '@/utils'
import { Message } from 'iview';

const props = defineProps({
  clickCount: {
    type: Number,
    default: 0
  }
});

// variables
const dateRange = ref([]);
const searchParams = ref({
  rest_type: null,
  action: null,
  start_time: '',
  end_time: '',
  page_no: 1,
  page_size: 10
});
const tagList = ref([]);
const actionList = ref([
  {
    value: 1,
    label: '占用'
  },
  {
    value: 2,
    label: '取消占用'
  }
]);
const total = ref(0);
const list = ref([]);
const columns = ref([
  {
    title: '操作时间',
    key: 'create_time',
  },
  {
    title: '动作',
    key: 'action',
  },
  {
    title: '占用场次',
    key: 'space_name',
    render: (h, params) => {
      const text = params.row.space_name + ' | ' + params.row.rest_time
      let shortRemark = text
      if (shortRemark?.length > 30) {
        shortRemark = shortRemark.slice(0, 30) + '...'
        return h('Poptip', {
          props: {
            title: '备注',
            trigger: 'hover',
            width: '400',
            transfer: true
          }
        }, [
          h('span', shortRemark),
          h('div', {
            slot: 'content',
            style: 'white-space: normal;word-break: break-all;max-height: 300px;overflow: auto;'
          }, text)
        ])
      } else {
        return h('span', text)
      }
    }
  },
  {
    title: '占用类型',
    key: 'rest_type',
  },
  {
    title: '占用人',
    key: 'userinfo',
  },
  {
    title: '涉及金额',
    key: 'amount',
  },
  {
    title: '备注',
    key: 'remark',
    render: (h, params) => {
      let shortRemark = params.row.remark || '';
      if (shortRemark.length > 15) {
        shortRemark = params.row.remark.substr(0, 20) + '...';
        return h('Tooltip', {
          props: {
            content: params.row.remark,
            transfer: true
          }
        }, shortRemark)
      } else {
        return h('div', shortRemark)
      }
    }
  },
  {
    title: '操作账号',
    key: 'admin',
  },
]);

// methods
const getTagList = () => {
  return service.get('/Web/Space/getRestType').then((res) => {
    if (res.data.errorcode === 0) {
      tagList.value = res.data.data;
    } else {
      Message.error(res.data.errormsg);
    }
  });
};
const getList = () => {
  if (Array.isArray(dateRange.value) && dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    searchParams.value.start_time = formatDate(dateRange.value[0], 'yyyy-MM-dd')
    searchParams.value.end_time = formatDate(dateRange.value[1], 'yyyy-MM-dd')
  } else {
    searchParams.value.start_time = ''
    searchParams.value.end_time = ''
  }
  service.post('/Web/Space/getSpaceRestRecord', searchParams.value).then((res) => {
    if (res.data.errorcode === 0) {
      list.value = res.data.data.list
      total.value = Number(res.data.data.count)
    }
  })
}
const handleSearch = () => {
  searchParams.value.page_no = 1
  getList()
}
const handlePage = (page) => {
  searchParams.value.page_no = page
  getList()
}
const pageSizeChanged = (pageSize) => {
  searchParams.value.page_no = 1
  searchParams.value.page_size = pageSize
  getList()
}

// created
// set dateRange to recent 30 days
dateRange.value = [
  new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
  new Date()
]
getTagList();
getList();

// watch
watch(() => props.clickCount, () => {
  searchParams.value.page_no = 1
  getList();
}, { immediate: true })
</script>

<style lang="less" scoped>
.long-term-rental-tasks {

  .box-head {
    border: none;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-item {
        width: 200px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border: none;
    height: calc(100vh - 400px);
    overflow-y: auto;

    .height-table {
      max-height: calc(100vh - 400px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }

  .box-foot {
    border: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.height-table ::v-deep(.ivu-table-overflowX) {
  overflow-x: hidden !important;
}
</style>