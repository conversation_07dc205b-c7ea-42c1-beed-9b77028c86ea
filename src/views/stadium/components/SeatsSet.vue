<template>
  <div class="room-wrap" :class="!isShowSet ? 'no-left': ''">
          <div class="room-lef" v-if="isShowSet">
            <ul>
              <li class="room-lef-li" v-for="(item, index) in rowArr" :key="index">
                <label>第{{index+1}}排</label>
                <InputNumber
                  class="room-input"
                  v-model="rowArr[index]"
                  :max="99"
                  :min="1"
                  placeholder="请输入座位数"
                  :step="1"
                />
                <Icon @click="delRow(index)" class="del-ico" v-if="index === rowArr.length - 1" type="ios-trash" />
              </li>
              <li>
                <span class="room-lef-last" @click="addRow"><Icon class="add-ico" type="md-add-circle" />再添加一排</span>
              </li>
            </ul>
          </div>
          <div class="room-box" ref="roomBox">
            <div class="room-box-head" :style="{width: (rowArrMax*40)+'px'}">
              <div class="room-top-rig" v-if="!isShowSet">
                <div class="not-sit"></div> 可选
                <div class="is-sit">1</div> 已售
              </div>
              <div v-else>
                排布图
              </div>
              <Button disabled>讲台</Button>
            </div>
            <div class="room-box-con" :style="{height: (rowArr.length*40)+'px',width: (rowArrMax*40)+'px'}">
               <div class="seat-row" v-for="(row, x) in rowArr.length" :key="row" :style="{width:(rowArr[x]*40)+'px'}">
                 <template v-if="isShowSet" >
                  <span class="seat-num" v-for="(col, y) in rowArr[x]" :key="col">{{getSeatInfo(x, y, 'seat_number')}}</span>
                 </template>
                 <template v-else>
                   <span  @click="chooseNum(x, y)" class="seat-num" :class="{'is-choose': chooseIds.indexOf(getSeatInfo(x, y, 'id')) !== -1, 'is-used': getSeatInfo(x, y, 'is_used')}" v-for="(col, y) in rowArr[x]" :key="col">
                     <Icon type="md-checkmark" v-if="chooseIds.indexOf(getSeatInfo(x, y, 'id')) !== -1"/>
                     <template v-else>
                        {{getSeatInfo(x, y, 'seat_number')}}
                     </template>

                   </span>
                 </template>
              </div>
            </div>
          </div>
        </div>
</template>
<script>
 import { formatDate } from 'utils';
export default {
  name: "SeatsSet",
  data() {
    return {
      rowArr: [null],
      rowArrMax: 0,
      chooseIds: []
    };
  },
  props: {
    value: {
      type: Array,
      default: ()=> []
    },
    isShowSet: {
      type: Boolean,
      default: false
    },
    isCanChoose: {
      type: Boolean,
      default: true
    },
    chooseNumber: {
      type: Number,
      default: 1
    }
  },
  computed: {
    seatsList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    rowArr(val, oldVal) {

      let seats = [];
      this.rowArrMax = val[0] || 0;
      let total = 0;
      val.forEach((num, index) => {
        if (num > this.rowArrMax) {
          this.rowArrMax = num;
        }
        if (this.isShowSet) {
          for (let i = 0; i < num; i++) {
            total++;
            seats.push({
              position_x: index + 1,
              position_y: i + 1,
              seat_number: total
            });
          }
        }
      });
      if (this.isShowSet) {
        this.seatsList = seats
      }
    }
  },
  methods: {
    chooseNum(x, y) {
      if (this.isCanChoose) {
        let curId = this.getSeatInfo(x, y, 'id')
        let isUsed = this.getSeatInfo(x, y, 'is_used')
        if (isUsed) {
          return;
        }
        if (this.chooseIds.indexOf(curId) !== -1) {
          this.chooseIds.splice(this.chooseIds.indexOf(curId), 1)
          this.$emit('on-choose', this.chooseIds.join(','))
          return;
        }
        if (this.chooseNumber && (this.chooseIds.length >= this.chooseNumber)) {
          this.$Message.error('座位数不可超过预约人数！');
          return;
        }
        this.chooseIds.push(curId)
        this.$emit('on-choose', this.chooseIds.join(','))
      }
    },
    getSeatInfo(x, y, info) {
      for (const item of this.seatsList) {
        if ((x+1 === +item.position_x) &&  (y+1 ===  +item.position_y)) {
          return item[info]
        }
      }
    },
    addRow() {
      this.rowArr.push(null)
    },
    delRow(index) {
      if (index == 0) {
        this.rowArr = [null]
      } else {
        this.rowArr.splice(index, 1)
      }
    },
    getrowArr() {
      let arr = [];
      if (this.seatsList && this.seatsList.length) {
        let onlyOneRow = true
        let arrIndex = 0;
        this.seatsList.forEach((item, i) => {
          if (i > 0 && (this.seatsList[i - 1].position_x !== item.position_x)) {
            arr.push(i-arrIndex);
            arrIndex = i;
            onlyOneRow = false
          }

        });
        // 最后一排或者只有一排的时候
        arr.push(this.seatsList.length - arrIndex);
      } else {
        arr = [""];
      }
      this.rowArr = arr;
       this.$nextTick(()=>{
        // 600为通过css max-width: 652px;去掉边距后的最大宽度
        if (this.isShowSet) {
          this.$refs.roomBox.scrollLeft = (this.rowArrMax*40 -600)>0?(this.rowArrMax*40 -600)/2 + 'px' : 0
        }
      })
    },
  },
  created() {
    if (this.seatsList && this.seatsList.length>0) {
      this.getrowArr()
    }
  }
};
</script>
<style lang="less" scoped>
.room-wrap {
  position: relative;
  margin-bottom: 100px;
  .del-ico {
    color: #ea5252;
    font-size: 16px;
    cursor: pointer;
  }
  .room-lef-last {
    cursor: pointer;
  }
  .add-ico {
    color: #ffbc46;
    font-size: 16px;
    vertical-align: middle;
    margin-right: 4px;
  }
  .room-box {
    position: absolute;
    overflow-x: scroll;
    background: #fff;
    left: 300px;
    top: -80px;
    border: 1px solid #dedede;
    min-width: 450px;
    max-width: 652px;
    padding: 0 25px;
    text-align: center;
  }
  .room-box-head {
    margin-bottom: 13px;
    min-width: 100%;
  }
  .room-box-con {
    min-height: 80px;
    margin: 0 auto;
    .seat-row {
      height: 40px;
      text-align: center;
      margin: 0 auto;
    }
    .seat-num {
      display: inline-block;
      width: 32px;
      height: 32px;
      line-height: 32px;
      margin: 0 8px 8px 0;
      border-radius: 4px;
      border: 1px solid #dedede;
      vertical-align: middle;
      &.is-choose {
        color: #fff;
        font-size: 16px;
        line-height: 30px;
        background: #23c6d5;
        border-color: #23c6d5;
      }
      &.is-used {
        color: #fff;
        line-height: 30px;
        font-size: 16px;
        background: #ca2e53;
        border-color: #ca2e53;
      }
    }
  }
}
.room-lef-li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  .room-input {
    width: 100px !important;
    margin-left: 25px;
    margin-right: 10px;
  }
}
.no-left {
  margin-bottom: 0;
  .room-box {
    padding-top: 10px;
    position: relative;
    left: auto;
    top: auto;
  }
  .room-box-head {
    padding-top: 20px;
  }
}
 .room-top-rig {
  position: absolute;
  right: 15px;
  top: 5px;
  display: flex;
  justify-items: center;
  align-items: center;
  font-size: 12px;
  .not-sit {
    width: 19px;
    height: 19px;
    border-radius: 5px;
    border: 1px solid #bbb;
    margin-right: 10px;
  }
  .is-sit {
    margin-left: 15px;
    margin-right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background: #ca2e53;
    color: #fff;
    line-height: 20px;
    text-align: center;
  }
  .seat-num {
    cursor: pointer;
  }
}
</style>
