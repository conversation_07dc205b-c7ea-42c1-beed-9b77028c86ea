<template>
  <div class="plan-time-box">
    <div class="plan-time-line" v-for="(between, $index) in weekTimeList" :key="'wink_' + $index">
      <TimePicker @on-change="handleTimeChange(0, $event, $index)" :value="between[0]" format="HH:mm" placeholder="开始时间" :steps="[1, 30]" :editable="false" :clearable="false" style="width:180px" transfer></TimePicker>
      <TimePicker @on-change="handleTimeChange(1, $event, $index)" :value="between[1]" format="HH:mm" placeholder="结束时间" :steps="[1, 30]" :editable="false" :clearable="false" style="width:180px" transfer></TimePicker>
      <Button icon="ios-trash" type="error" @click="handleTimeDelete($index)" ghost>删除</Button>
    </div>
    <Button icon="ios-add" long size="large" class="plan-add-btn" @click="handleTimeAdd">添加</Button>
    <Button v-if="weekTimeList.length > 0" icon="ios-copy" long size="large" @click="handleTimeCopy">复制场次信息</Button>
  </div>
</template>

<script>
export default {
  props: {
    weekTimeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const timeList = [
      '00:00',
      '00:30',
      '01:00',
      '01:30',
      '02:00',
      '02:30',
      '03:00',
      '03:30',
      '04:00',
      '04:30',
      '05:00',
      '05:30',
      '06:00',
      '06:30',
      '07:00',
      '07:30',
      '08:00',
      '08:30',
      '09:00',
      '09:30',
      '10:00',
      '10:30',
      '11:00',
      '11:30',
      '12:00',
      '12:30',
      '13:00',
      '13:30',
      '14:00',
      '14:30',
      '15:00',
      '15:30',
      '16:00',
      '16:30',
      '17:00',
      '17:30',
      '18:00',
      '18:30',
      '19:00',
      '19:30',
      '20:00',
      '20:30',
      '21:00',
      '21:30',
      '22:00',
      '22:30',
      '23:00',
      '23:30'
    ]
    return {
      timeList
    }
  },
  methods: {
    getNewTime(last) {
      const lastStartIndex = this.timeList.findIndex((item) => item === last[0])
      const lastEndIndex = this.timeList.findIndex((item) => item === last[1])
      const step = lastEndIndex - lastStartIndex
      const newStart = last[1]
      const newEnd = this.timeList[lastEndIndex + step]
      if (newEnd) {
        return [newStart, newEnd]
      } else {
        return [newStart, '00:00']
      }
    },
    checkTimeList() {
      let flag = true
      this.weekTimeList.forEach(item => {
        const startIndex = this.timeList.findIndex(start => start === item[0])
        const endIndex = this.timeList.findIndex(end => end === item[1])
        if (endIndex !== 0 && startIndex === endIndex) {
          this.$Message.error('开始时间和结束时间不能相同！')
          flag = false
          return false
        } else if (endIndex !== 0 && startIndex > endIndex) {
          this.$Message.error('开始时间不能大于结束时间！')
          flag = false
          return false
        }
      })
      return flag
    },
    handleTimeChange(space, val, idx) {
      this.weekTimeList[idx][space] = val
      this.$emit('update:weekTimeList', [...this.weekTimeList])
    },
    handleTimeDelete(idx) {
      this.weekTimeList.splice(idx, 1)
      this.$emit('update:weekTimeList', [...this.weekTimeList])
    },
    handleTimeAdd() {
      if (this.weekTimeList.length === 0) {
        this.weekTimeList.push(['07:00', '08:00'])
      } else {
        let last = this.weekTimeList[this.weekTimeList.length - 1]
        if (last[1] === '00:00') {
          this.$Message.error('没有可用的时间！')
          return false
        } else {
          if (this.checkTimeList()) {
            this.weekTimeList.push(this.getNewTime(last))
          }
        }
      }
      this.$emit('update:weekTimeList', [...this.weekTimeList])
    },
    handleTimeCopy() {
      this.$emit('on-copy')
    },
  },
}
</script>

<style lang="less" scoped>
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan-time-box {
  flex-direction: column;
  .center;

  .plan-time-line {
    margin-top: 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .plan-add-btn {
    margin-top: 10px;
    margin-bottom: 15px;
    align-self: baseline;
    color: #3dabff;
  }
}
</style>
