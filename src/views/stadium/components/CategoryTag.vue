<template>
  <div class="category-tag" :class="{ checked: cat.isChecked }" v-on="$listeners">
    <span class="label">{{ cat.name }} ({{ cat.space_count }})</span>
    <img v-if="cat.isChecked" class="cat-icon" src="@/assets/img/booking/edit-white.png" @click.stop="handleSettingClick" />
    <img v-else class="cat-icon" src="@/assets/img/booking/edit-gray.png" @click.stop="handleSettingClick" />
    <img
      v-if="cat.isChecked"
      class="cat-icon"
      style="margin-left: 13px"
      src="@/assets/img/booking/delete-white.png"
      @click.stop="handleDeleteClick"
    />
    <img
      v-else
      class="cat-icon"
      style="margin-left: 13px"
      src="@/assets/img/booking/delete-gray.png"
      @click.stop="handleDeleteClick"
    />
  </div>
</template>

<script>
export default {
  props: {
    cat: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    handleSettingClick() {
      this.$router.push({
        path: `/stadium/categorySave/${this.cat.id}`,
      })
    },
    handleDeleteClick() {
      this.$Modal.confirm({
        title: '删除场地类型则该类型下的场地一并删除！',
        onOk: () => {
          this.$service.post('/Web/Space/delSpaceType', { id: this.cat.id }).then((res) => {
            if (res.data.errorcode == 0) {
              this.$Message.info(res.data.errormsg)
              this.$emit('fetchCategoryList', this.cat)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 5px;
  background-color: #f7f7f7;
  cursor: pointer;
  user-select: none;

  .label {
    font-size: 14px;
    line-height: 20px;
    margin-right: 20px;
  }
}

.cat-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.checked {
  border: 1px solid #e0e3e9;
  background-color: rgba(134, 202, 134, 1);
  color: white;
  font-weight: bold;
}
</style>
