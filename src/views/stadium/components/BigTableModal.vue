<template>
  <Modal v-model="visible" :title="title" footer-hide>
    <Table v-if="visible" :loading="loading" ref="table" :columns="props.columns" :data="showList" disabled-hover height="500"></Table>
    <div class="has-more">
      <Button v-if="hasMore" :loading="loading" @click="handleLoadMore">
        <span v-if="!loading">加载全部</span>
        <span v-else>Loading...</span>
      </Button>
    </div>
  </Modal>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits } from 'vue';

const emit = defineEmits(['update:bigVisible'])
const props = defineProps({
  bigVisible: {
    type: Boolean
  },
  title: {
    type: String,
  },
  columns: {
    type: Array
  },
  list: {
    type: Array
  },
})

const visible = ref(false)
const limit = 100
const hasMore = ref(false)
const loading = ref(false)
const showList = ref([])

const handleLoadMore = () => {
  loading.value = true

  setTimeout(() => {
    showList.value = [...showList.value, ...props.list.slice(limit)]
    hasMore.value = false
    loading.value = false
  }, 100)
}

watch(() => props.bigVisible, () => {
  visible.value = props.bigVisible
  if (props.bigVisible) {
    hasMore.value = false
    loading.value = false
    showList.value = []
    if (Array.isArray(props.list) && props.list.length > 0) {
      hasMore.value = props.list.length > limit
      showList.value = props.list.slice(0, limit)
    }
  } else {
    showList.value = []
  }
}, { immediate: true })
watch(() => visible.value, () => {
  emit('update:bigVisible', visible.value)
}, { immediate: true })
</script>

<style lang="less" scoped>
.has-more {
  text-align: center;
  margin-top: 10px;
}
</style>