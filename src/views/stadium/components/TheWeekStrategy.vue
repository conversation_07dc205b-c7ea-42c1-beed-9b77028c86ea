<template>
  <div class="week-strategy-box">
    <Tabs v-model="tabName" type="card">
      <Tab-pane :label="monday" name="monday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="tuesday" name="tuesday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="wednesday" name="wednesday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="thursday" name="thursday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="friday" name="friday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="saturday" name="saturday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
      <Tab-pane :label="sunday" name="sunday">
        <div class="strategy-box">
          <Table
            :columns="tableCols"
            :data="tableData"
            @on-selection-change="onSelectionChange"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
          ></Table>
        </div>
      </Tab-pane>
    </Tabs>

    <div class="button-box" :style="hasTemplate ? 'width:400px' : ''">
      <Button ghost type="info" style="background-color: white" @click="handleEditModalShow">编辑场次</Button>
      <Button
        ghost
        type="success"
        style="background-color: white"
        @click="handlePriceModalShow"
        :disabled="selection.length === 0"
      >
        设置价格
      </Button>
      <Button ghost type="error" style="background-color: white" @click="handleDelete" :disabled="selection.length === 0">
        删除场次
      </Button>
      <Button v-if="hasTemplate" :disabled="tableDataAll.length === 0" @click="handleTemplateShow">保存方案为模板</Button>
    </div>

    <Modal v-model="editModalShow" title="编辑场次" :mask-closable="false">
      <Tabs v-if="editModalShow" v-model="planDialogTab">
        <TabPane label="周一" name="monday">
          <the-week-time :weekTimeList.sync="planTimeSet.monday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周二" name="tuesday">
          <the-week-time :weekTimeList.sync="planTimeSet.tuesday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周三" name="wednesday">
          <the-week-time :weekTimeList.sync="planTimeSet.wednesday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周四" name="thursday">
          <the-week-time :weekTimeList.sync="planTimeSet.thursday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周五" name="friday">
          <the-week-time :weekTimeList.sync="planTimeSet.friday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周六" name="saturday">
          <the-week-time :weekTimeList.sync="planTimeSet.saturday" @on-copy="handleTimeCopy" />
        </TabPane>
        <TabPane label="周日" name="sunday">
          <the-week-time :weekTimeList.sync="planTimeSet.sunday" @on-copy="handleTimeCopy" />
        </TabPane>
      </Tabs>
      <week-copy-modal @on-confirm="handleWeekCopy" v-model="showCopyModal" :from="planDialogTab" />
      <div slot="footer">
        <Button type="primary" @click="handleEditModalOk">确定</Button>
      </div>
    </Modal>

    <Modal v-model="priceModalShow" title="设置价格" :mask-closable="false">
      <Form v-model="noneMemberPricePost" :label-width="100">
        <Form-item label="非会员价"></Form-item>
        <Row v-if="hasHalf === 1">
          <Col span="12">
            <Form-item label="半场价">
              <Input-number v-model="noneMemberPricePost.half.common" :min="0" size="small"></Input-number>
              <span class="unit">元</span>
            </Form-item>
          </Col>
          <Col span="12">
            <Form-item label="节假日-半场价">
              <Input-number v-model="noneMemberPricePost.half.holiday" :min="0" size="small"></Input-number>
              <span class="unit">元</span>
            </Form-item>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <Form-item label="全场价">
              <Input-number v-model="noneMemberPricePost.all.common" :min="0" size="small"></Input-number>
              <span class="unit">元</span>
            </Form-item>
          </Col>
          <Col span="12">
            <Form-item label="节假日-全场价">
              <Input-number v-model="noneMemberPricePost.all.holiday" :min="0" size="small"></Input-number>
              <span class="unit">元</span>
            </Form-item>
          </Col>
        </Row>
        <template v-for="(mp, mpIndex) in memberPriceList">
          <Form-item :label="'会员价' + (mpIndex + 1)" :key="'mp_' + mpIndex">
            <div style="display: flex; flex-direction: row; align-items: center">
              <treeselect
                :limit="10"
                :limitText="(count) => `还有 ${count} 个`"
                v-model="mp.card_ids"
                :options="treeCardsRespectively[mpIndex]"
                @input="resetAllTreeCards"
                placeholder="请选择..."
                no-results-text="无数据"
                no-options-text="无数据"
                no-children-text="无数据"
                loading-text="加载中..."
                style="width: 80%"
                valueConsistsOf="LEAF_PRIORITY"
                :default-expand-level="Infinity"
                multiple
              />
              <Icon
                type="ios-trash"
                :size="22"
                style="cursor: pointer; margin-left: 10px"
                @click="handleTreeRemove(mpIndex)"
              ></Icon>
            </div>
          </Form-item>
          <Row v-if="hasHalf === 1" :key="'row_half_' + mpIndex">
            <Col span="12">
              <Form-item label="半场价">
                <Input-number v-model="mp.half.common" :min="0" size="small"></Input-number>
                <span class="unit">元</span>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="节假日-半场价">
                <Input-number v-model="mp.half.holiday" :min="0" size="small"></Input-number>
                <span class="unit">元</span>
              </Form-item>
            </Col>
          </Row>
          <Row :key="'row_all_' + mpIndex">
            <Col span="12">
              <Form-item label="全场价">
                <Input-number v-model="mp.all.common" :min="0" size="small"></Input-number>
                <span class="unit">元</span>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="节假日-全场价">
                <Input-number v-model="mp.all.holiday" :min="0" size="small"></Input-number>
                <span class="unit">元</span>
              </Form-item>
            </Col>
          </Row>
        </template>
        <Form-item>
          <Button type="primary" shape="circle" @click="handleMemberPriceAdd">新增会员价</Button>
        </Form-item>
        <Alert type="warning" show-icon>
          注意：节假日价格需要设置对应节假日日期，价格才会生效；储值卡会员价仅限于使用储值卡支付，微信支付等方式不享受此价格
        </Alert>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handlePriceModalOk">确定</Button>
      </div>
    </Modal>

    <Modal
      v-model="copyModalShow"
      title="复制价格"
      @on-ok="handleCopyModalOk"
      @on-cancel="handleCopyModalCancel"
      :mask-closable="false"
    >
      <Alert type="warning" show-icon>{{ copyTitle }}</Alert>
      <Card v-for="(day, dayIndex) in copyDayList" :key="day.label" style="margin-bottom: 10px">
        <div slot="title">
          <Checkbox
            :indeterminate="copyDayList[dayIndex].indeterminate"
            :value="copyDayList[dayIndex].checkAll"
            @click.prevent.native="handleCheckAll(dayIndex)"
          >
            {{ day.label }}
          </Checkbox>
        </div>
        <Checkbox-group v-model="copyDayList[dayIndex].checkAllGroup" @on-change="checkAllGroupChange($event, dayIndex)">
          <Checkbox
            v-for="(time, timeIndex) in day.timeList"
            :key="'time_' + timeIndex"
            :label="timeIndex"
            :disabled="!copyModalRow || copyModalRow.id === day.tabName + '_' + timeIndex"
          >
            {{ time[0] }}~{{ time[1] }}
          </Checkbox>
        </Checkbox-group>
      </Card>
    </Modal>

    <Modal v-model="tempModalShow" title="保存模板" :mask-closable="false">
      <Form ref="tempRef" :model="tempPost" :rules="tempRule" :label-width="120">
        <Form-item label="方案名称" prop="name">
          <Input v-model="tempPost.name" placeholder="请输入..." style="width: 300px"></Input>
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleTempModalOk">确定</Button>
        <Button @click="handleTempModalCancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import TheWeekTime from './TheWeekTime'
import WeekCopyModal from './WeekCopyModal.vue'
import Selection from 'mixins/selection'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

const MEASURE_TIME_LIST = [
  '00:00',
  '00:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
  '04:00',
  '04:30',
  '05:00',
  '05:30',
  '06:00',
  '06:30',
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
]

const MEASURE_DAY_LIST = {
  monday: '星期一',
  tuesday: '星期二',
  wednesday: '星期三',
  thursday: '星期四',
  friday: '星期五',
  saturday: '星期六',
  sunday: '星期天',
}

export default {
  components: {
    TheWeekTime,
    WeekCopyModal,
    Treeselect,
  },
  mixins: [Selection],
  props: {
    planTimeSet: {
      type: Object,
      default: () => ({}),
    },
    tableDataAll: {
      type: Array,
      default: () => [],
    },
    hasHalf: {
      type: Number,
      default: 0,
    },
    hasTemplate: {
      type: Boolean,
      default: false,
    },
    maxHours: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      showCopyModal: false,
      allCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        {
          title: '场次时间',
          key: 'time',
        },
        {
          title: '半场价',
          key: 'half',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                    width: '100%',
                  },
                  props: {
                    content: params.row.half,
                    transfer: true,
                    maxWidth: 400,
                  },
                },
                params.row.half
              ),
            ])
          },
        },
        {
          title: '全场价',
          key: 'all',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                    width: '100%',
                  },
                  props: {
                    content: params.row.all,
                    transfer: true,
                    maxWidth: 400,
                  },
                },
                params.row.all
              ),
            ])
          },
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            return h('div', [
              h(
                'Icon',
                {
                  props: {
                    type: 'ios-copy',
                    size: '20',
                  },
                  attrs: {
                    title: '复制',
                  },
                  style: {
                    color: '#237DC1',
                    marginRight: '10px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      const dayZh = this.getDayZh(this.tabName)
                      this.copyTitle = `将${dayZh} ${params.row.time} 场次价格复制到`
                      this.copyDayList = []
                      for (const key in this.planTimeSet) {
                        if (Object.hasOwnProperty.call(this.planTimeSet, key)) {
                          const cpDayZh = this.getDayZh(key)
                          const cpTimeList = this.planTimeSet[key]
                          this.copyDayList.push({
                            label: cpDayZh,
                            tabName: key,
                            timeList: cpTimeList,
                            indeterminate: false,
                            checkAll: false,
                            checkAllGroup: [],
                          })
                        }
                      }
                      this.copyModalRow = params.row
                      this.copyModalShow = true
                    },
                  },
                },
                '复制'
              ),
              h(
                'Icon',
                {
                  props: {
                    type: 'ios-trash',
                    size: '20',
                  },
                  attrs: {
                    title: '删除',
                  },
                  style: {
                    color: '#237DC1',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.handleDelete(params.row.id)
                    },
                  },
                },
                '删除'
              ),
            ])
          },
        },
      ],
      noHalfCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        {
          title: '场次时间',
          key: 'time',
        },
        {
          title: '全场价',
          key: 'all',
          render: (h, params) => {
            return h('div', [
              h(
                'Tooltip',
                {
                  style: {
                    color: '#1890ff',
                    width: '100%',
                  },
                  props: {
                    content: params.row.all,
                    transfer: true,
                    maxWidth: 400,
                  },
                },
                params.row.all
              ),
            ])
          },
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            return h('div', [
              h(
                'Icon',
                {
                  props: {
                    type: 'ios-copy',
                    size: '20',
                  },
                  attrs: {
                    title: '复制',
                  },
                  style: {
                    color: '#237DC1',
                    marginRight: '10px',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      const dayZh = this.getDayZh(this.tabName)
                      this.copyTitle = `将${dayZh} ${params.row.time} 场次价格复制到`
                      this.copyDayList = []
                      for (const key in this.planTimeSet) {
                        if (Object.hasOwnProperty.call(this.planTimeSet, key)) {
                          const cpDayZh = this.getDayZh(key)
                          const cpTimeList = this.planTimeSet[key]
                          this.copyDayList.push({
                            label: cpDayZh,
                            tabName: key,
                            timeList: cpTimeList,
                            indeterminate: false,
                            checkAll: false,
                            checkAllGroup: [],
                          })
                        }
                      }
                      this.copyModalRow = params.row
                      this.copyModalShow = true
                    },
                  },
                },
                '复制'
              ),
              h(
                'Icon',
                {
                  props: {
                    type: 'ios-trash',
                    size: '20',
                  },
                  attrs: {
                    title: '删除',
                  },
                  style: {
                    color: '#237DC1',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.handleDelete(params.row.id)
                    },
                  },
                },
                '删除'
              ),
            ])
          },
        },
      ],
      // table
      tabName: 'monday',
      tableCols: [],
      // edit modal
      editModalShow: false,
      planDialogTab: 'monday',

      // price modal
      priceModalShow: false,
      noneMemberPricePost: {
        half: {
          common: 0,
          holiday: 0,
        },
        all: {
          common: 0,
          holiday: 0,
        },
      },
      memberPriceList: [],
      cardList: [],
      treeCardsRespectively: [],
      // copy modal
      copyModalShow: false,
      copyModalRow: null,
      copyTitle: '',
      copyDayList: [],
      indeterminate: false,
      checkAll: false,
      checkAllGroup: [],
      // temp modal
      tempModalShow: false,
      tempPost: {
        name: '',
      },
      tempRule: {
        name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
      },
    }
  },
  watch: {
    hasHalf(newValue, oldValue) {
      if (newValue === 0) {
        this.tableCols = this.noHalfCols
      } else if (newValue === 1) {
        this.tableCols = this.allCols
      }
    },
    maxHours(newValue, oldValue) {
      if (newValue) {
        this.handleEditModalOk()
      }
    },
  },
  computed: {
    monday() {
      return `周一 (${this.planTimeSet.monday.length})`
    },
    tuesday() {
      return `周二 (${this.planTimeSet.tuesday.length})`
    },
    wednesday() {
      return `周三 (${this.planTimeSet.wednesday.length})`
    },
    thursday() {
      return `周四 (${this.planTimeSet.thursday.length})`
    },
    friday() {
      return `周五 (${this.planTimeSet.friday.length})`
    },
    saturday() {
      return `周六 (${this.planTimeSet.saturday.length})`
    },
    sunday() {
      return `周日 (${this.planTimeSet.sunday.length})`
    },
    tableData() {
      return this.tableDataAll.filter((item) => {
        item._checked = this.selectionId.includes(item.id)
        return item.tabName === this.tabName
      })
    },
  },
  created() {
    this.getCards()
    if (this.hasHalf === 0) {
      this.tableCols = this.noHalfCols
    } else if (this.hasHalf === 1) {
      this.tableCols = this.allCols
    }
  },
  methods: {
    handleTimeCopy() {
      this.showCopyModal = true
    },
    handleWeekCopy(info) {
      if(info.from && Array.isArray(info.to) && info.to.length > 0) {
        const fromData = this.planTimeSet[info.from]
        info.to.forEach((item) => {
          this.planTimeSet[item] = [...fromData]
        })
        this.$Message.success('操作成功')
      }
      this.showCopyModal = false
    },
    getDayZh(tabName) {
      let dayZh = ''
      if (tabName === 'monday') {
        dayZh = '周一'
      } else if (tabName === 'tuesday') {
        dayZh = '周二'
      } else if (tabName === 'wednesday') {
        dayZh = '周三'
      } else if (tabName === 'thursday') {
        dayZh = '周四'
      } else if (tabName === 'friday') {
        dayZh = '周五'
      } else if (tabName === 'saturday') {
        dayZh = '周六'
      } else if (tabName === 'sunday') {
        dayZh = '周日'
      }
      return dayZh
    },
    // edit modal
    handleEditModalShow() {
      this.editModalShow = true
    },
    handleTimeCheck(day) {
      let flag = true
      const customList = this.planTimeSet[day]
      if (Array.isArray(customList)) {
        let checkList = []
        for (let i = 0; i < 48; i++) {
          checkList.push(false)
        }

        customList.forEach((customTime) => {
          const begin = MEASURE_TIME_LIST.findIndex((time) => time === customTime[0])
          let end = MEASURE_TIME_LIST.findIndex((time) => time === customTime[1])
          if (end === 0) {
            end = 48
          }
          const between = end - begin
          if (between / 2 > this.maxHours) {
            this.$Message.error({
              duration: 4,
              closable: true,
              content: `${MEASURE_DAY_LIST[day]} 单次订场时长不能大于 ${this.maxHours} 个小时！`,
            })
            flag = false
            return false
          }

          if (end !== 0 && begin >= end) {
            this.$Message.error({
              duration: 4,
              closable: true,
              content: `${MEASURE_DAY_LIST[day]} 开始时间 ${customTime[0]} 不能大于结束时间 ${customTime[1]}！`,
            })
            flag = false
            return false
          }

          // inside checkList
          for (let i = begin; i < end; i++) {
            if (checkList[i]) {
              this.$Message.error({
                duration: 4,
                closable: true,
                content: `${MEASURE_DAY_LIST[day]} ${MEASURE_TIME_LIST[i]} 重复排场！`,
              })
              flag = false
              return false
            } else {
              checkList[i] = true
            }
          }
        })
      }
      return flag
    },
    packagePrice() {
      let flag = true
      const all = []
      for (const key in this.planTimeSet) {
        if (Object.hasOwnProperty.call(this.planTimeSet, key)) {
          if (!this.handleTimeCheck(key)) {
            flag = false
            // return false
          }
          const day = this.planTimeSet[key]
          day.forEach((item, index) => {
            const row = this.tableDataAll.find((innerRow) => innerRow.id === key + '_' + index)
            if (row) {
              row.time = item[0] + '~' + item[1]
              all.push(row)
            } else {
              all.push({
                id: key + '_' + index,
                time: item[0] + '~' + item[1],
                half: '未设置',
                all: '未设置',
                tabName: key,
                noneMember: {
                  half: {
                    common: null,
                    holiday: null,
                  },
                  all: {
                    common: null,
                    holiday: null,
                  },
                },
                member: [],
              })
            }
          })
        }
      }
      return {
        all,
        flag,
      }
    },
    handleEditModalOk() {
      if (!this.maxHours) {
        this.$Message.error('请输入订场限制')
        this.editModalShow = false
        return false
      }

      const { all, flag } = this.packagePrice()

      if (flag) {
        this.editModalShow = false
        this.$emit('update:tableDataAll', all)
      }

      return flag
    },
    // price modal
    resetPriceModal() {
      this.noneMemberPricePost = {
        half: {
          common: null,
          holiday: null,
        },
        all: {
          common: null,
          holiday: null,
        },
      }
      this.memberPriceList = []
    },
    handlePriceModalShow() {
      this.resetPriceModal()
      if (this.selection.length === 1) {
        this.noneMemberPricePost = this.selection[0].noneMember
        this.memberPriceList = this.selection[0].member
      }

      this.treeCardsRespectively = []
      this.memberPriceList.forEach((member) => {
        this.treeCardsRespectively.push([])
      })
      this.resetAllTreeCards()

      this.priceModalShow = true
    },
    handlePriceModalOk() {
      let isZeroIds = false
      this.tableDataAll.forEach((item) => {
        if (item._checked) {
          const zeroIds = this.memberPriceList.filter((member) => member.card_ids.length === 0)
          if (zeroIds.length === 0) {
            let halfFlag = true
            let allFlag = true

            let half = `非会员:${this.noneMemberPricePost.half.common}元/${this.noneMemberPricePost.half.holiday}元 `
            this.memberPriceList.forEach((member, index) => {
              if (member.half.common === null || member.half.holiday === null) {
                halfFlag = false
              }
              half += `会员价${index + 1}:${member.half.common}元/${member.half.holiday}元; `
            })
            let all = `非会员:${this.noneMemberPricePost.all.common}元/${this.noneMemberPricePost.all.holiday}元 `
            this.memberPriceList.forEach((member, index) => {
              if (member.all.common === null || member.all.holiday === null) {
                allFlag = false
              }
              all += `会员价${index + 1}:${member.all.common}元/${member.all.holiday}元; `
            })

            if (this.noneMemberPricePost.half.common === null || this.noneMemberPricePost.half.holiday === null) {
              halfFlag = false
            }

            if (this.noneMemberPricePost.all.common === null || this.noneMemberPricePost.all.holiday === null) {
              allFlag = false
            }

            item.noneMember = this.noneMemberPricePost
            item.member = this.memberPriceList
            item.half = halfFlag ? half : '未设置'
            item.all = allFlag ? all : '未设置'
          } else {
            isZeroIds = true
          }
        }
      })
      this.$emit('update:tableDataAll', [...this.tableDataAll])
      if (isZeroIds) {
        this.$Message.error('请选择会员卡')
      } else {
        this.priceModalShow = false
      }
    },
    handleMemberPriceAdd() {
      this.memberPriceList.push({
        card_ids: [],
        half: {
          common: null,
          holiday: null,
        },
        all: {
          common: null,
          holiday: null,
        },
      })
      const newTreeCards = this.packageTreeCards()
      this.treeCardsRespectively.push(newTreeCards)
    },
    handleTreeRemove(treeIndex) {
      this.memberPriceList.splice(treeIndex, 1)
      this.resetAllTreeCards()
    },
    resetAllTreeCards() {
      this.treeCardsRespectively.forEach((tree, index) => {
        this.treeCardsRespectively[index] = this.packageTreeCards(index)
      })
    },
    packageTreeCards(treeIndex = -1) {
      let allCardIds = []
      this.memberPriceList.forEach((member, index) => {
        if (treeIndex !== index) {
          allCardIds = [...allCardIds, ...member.card_ids]
        }
      })
      const list = []
      this.cardList.forEach((item) => {
        let isDisabled = false
        if (allCardIds.includes(item.card_id)) {
          isDisabled = true
        }
        item.id = item.card_id
        item.label = item.card_name
        item.leaf = true
        item.children = undefined
        list.push({ ...item, isDisabled })
      })

      const termCard = list.filter((card) => card.card_type_id === '1')
      const timesCard = list.filter((card) => card.card_type_id === '2')
      const valueCard = list.filter((card) => card.card_type_id === '3')
      const privateCard = list.filter((card) => card.card_type_id === '4')
      const swimCard = list.filter((card) => card.card_type_id === '5')

      return [
        {
          id: 'all',
          label: '全部卡种',
          leaf: false,
          isDisabled: false,
          children: [
            {
              id: 'term',
              label: '期限卡',
              leaf: false,
              isDisabled: false,
              children: termCard,
            },
            {
              id: 'times',
              label: '次卡',
              leaf: false,
              isDisabled: false,
              children: timesCard,
            },
            {
              id: 'value',
              label: '储值卡',
              leaf: false,
              isDisabled: false,
              children: valueCard,
            },
            {
              id: 'private',
              label: '私教课',
              leaf: false,
              isDisabled: false,
              children: privateCard,
            },
            {
              id: 'swim',
              label: '泳教课',
              leaf: false,
              isDisabled: false,
              children: swimCard,
            },
          ].filter((item) => item.children.length > 0),
        },
      ]
    },
    // copy modal
    handleCheckAll(index) {
      const day = this.copyDayList[index]
      if (day.indeterminate) {
        day.checkAll = false
      } else {
        day.checkAll = !day.checkAll
      }
      day.indeterminate = false

      if (day.checkAll) {
        const checkedList = []
        for (let i = 0; i < day.timeList.length; i++) {
          checkedList.push(i)
        }
        day.checkAllGroup = checkedList
      } else {
        day.checkAllGroup = []
      }
    },
    checkAllGroupChange(data, index) {
      const day = this.copyDayList[index]
      if (data.length === day.timeList.length) {
        day.indeterminate = false
        day.checkAll = true
      } else if (data.length > 0) {
        day.indeterminate = true
        day.checkAll = false
      } else {
        day.indeterminate = false
        day.checkAll = false
      }
    },
    handleCopyModalOk() {
      const ids = []
      this.copyDayList.forEach((item) => {
        item.checkAllGroup.forEach((index) => {
          ids.push(item.tabName + '_' + index)
        })
      })
      const copyList = this.tableDataAll.filter((row) => ids.includes(row.id))
      if (copyList.length > 0) {
        copyList.forEach((item) => {
          item.noneMember = { ...this.copyModalRow.noneMember }
          item.member = [...this.copyModalRow.member]
          item.half = this.copyModalRow.half
          item.all = this.copyModalRow.all
        })
        this.copyModalRow = null
        this.copyModalShow = false
        this.$emit('update:tableDataAll', [...this.tableDataAll])
      } else {
        this.$Message.error('请选择要复制的时间段')
      }
    },
    handleCopyModalCancel() {
      this.copyModalRow = null
      this.copyModalShow = false
    },
    handleDelete(id) {
      if (!this.maxHours) {
        this.$Message.error('请输入订场限制')
        return false
      }

      let ids = []
      if (typeof id === 'string') {
        ids.push(id)
      } else {
        ids = [...this.selectionId]
      }
      if (ids.length === 0) {
        this.$Message.error('请选择要删除的记录')
        return
      }
      // this.tableDataAll = this.tableDataAll.filter(row => !ids.includes(row.id))
      const needToRemove = {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      }
      ids.forEach((item) => {
        const id_index = item.split('_')
        const day = id_index[0]
        const index = id_index[1]
        // this.planTimeSet[day].splice(index, 1)
        needToRemove[day].push(this.planTimeSet[day][index])
      })

      for (const key in needToRemove) {
        if (Object.hasOwnProperty.call(needToRemove, key)) {
          const oldList = this.planTimeSet[key]
          needToRemove[key].forEach((removeRange) => {
            const removeIndex = oldList.findIndex((range) => range[0] === removeRange[0] && range[1] === removeRange[1])
            if (removeIndex !== -1) {
              oldList.splice(removeIndex, 1)
            }
          })
        }
      }

      this.$emit('update:planTimeSet', this.planTimeSet)
      this.selection = []
      // this.handleEditModalOk()
      const { all } = this.packagePrice()
      this.$emit('update:tableDataAll', all)
    },
    getCards() {
      this.treeCardsRespectively = []
      this.$service.get('/Web/Activity/get_cards_for_all').then((res) => {
        if (res.data.errorcode === 0) {
          this.cardList = res.data.data
        } else {
          this.cardList = []
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    // template modal
    handleTemplateShow() {
      this.tempPost.name = ''
      this.tempModalShow = true
    },
    handleTempModalOk() {
      this.$refs.tempRef.validate((valid) => {
        if (valid) {
          this.$emit('emitSaveTemplate', this.tempPost.name)
          this.tempModalShow = false
        }
      })
    },
    handleTempModalCancel() {
      this.tempModalShow = false
    },
  },
}
</script>

<style lang="less" scoped>
.week-strategy-box {
  background-color: #f1f3f7;
  padding: 22px 13px;
}

.strategy-box {
  width: 100%;

  /deep/ .ivu-tooltip-rel {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.button-box {
  width: 300px;
  // height: 60px;
  margin-top: 17px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.unit {
  margin-left: 10px;
}
</style>
