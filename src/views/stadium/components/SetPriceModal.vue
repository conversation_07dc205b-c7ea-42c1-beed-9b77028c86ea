<template>
  <div class="set-price-modal">
    <Modal title="设置场地会员价" v-model="visible" v-bind="$attrs" width="740px" :mask-closable="false"
      @on-cancel="handleCancel" v-on="$listeners">
      <Alert type="warning">将卡种批量设置到场地的场次会员价中，若场次会员价已有对应卡种，新的会员价将覆盖原有价格</Alert>
      <Form ref="formRef" :model="formData" :rules="formRules" :label-width="80">
        <FormItem label="卡种名称" prop="card_ids">
          <treeselect 
            v-model="formData.card_ids" 
            :limit="5" 
            :limitText="(count) => `还有 ${count} 个`" 
            :options="cardTree"
            @input="handleCardChange" 
            placeholder="请选择..." 
            no-results-text="无数据" 
            no-options-text="无数据"
            no-children-text="无数据" 
            loading-text="加载中..." 
            valueConsistsOf="LEAF_PRIORITY"
            :default-expand-level="Infinity" 
            multiple />
        </FormItem>
        <FormItem label="">
          <Card v-for="(sp, i) in stadiumPriceList" :key="sp.uid" style="margin-bottom: 10px">
            <Row :style="!!stadiumPriceList[i].checkedError ? 'margin-bottom: 20px' : 'margin-bottom: 10px'">
              <Col span="3" style="text-align: right">
              <!-- <Tag style="margin-right: 10px">场次选择</Tag> -->
              <span style="font-size: 12px; margin-right: 10px; line-height: 32px">场次选择</span>
              </Col>
              <Col span="19">
              <FormItem :error="stadiumPriceList[i].checkedError">
                <treeselect 
                  v-model="stadiumPriceList[i].checked" 
                  :limit="2" 
                  :limitText="(count) => `还有 ${count} 个`"
                  :options="sp.tree" 
                  @select="handleSelectNode(i, $event)"
                  @deselect="handleDeselectNode"
                  @input="handleInputNode(i, $event)" 
                  placeholder="请选择..." 
                  no-results-text="无数据" 
                  no-options-text="无数据"
                  no-children-text="无数据" 
                  loading-text="加载中..." 
                  valueConsistsOf="LEAF_PRIORITY"
                  :load-options="loadOptions" 
                  multiple>
                  <div slot="value-label" slot-scope="{ node }">{{ node.raw.inputLabel }}</div>
                </treeselect>
              </FormItem>
              </Col>
              <Col span="2">
              <Button v-if="stadiumPriceList.length > 1" type="text" size="small" @click="handleDeleteStadiumPrice(i)">
                <Icon type="ios-trash-outline" size="20" />
              </Button>
              </Col>
            </Row>
            <Row>
              <Col span="3" style="text-align: right">
              <!-- <Tag style="margin-right: 10px">会员价</Tag> -->
              <span style="font-size: 12px; margin-right: 10px; line-height: 32px">会员价</span>
              </Col>
              <Col span="19">
              <Row v-if="stadiumPriceList[i].hasHalf"
                :style="(!!stadiumPriceList[i].priceError.half.common || !!stadiumPriceList[i].priceError.half.holiday) ? 'margin-bottom: 20px' : ''">
                <Col span="2" style="font-weight: bold">半场</Col>
                <Col span="3">平时价</Col>
                <Col span="8">
                <FormItem :error="stadiumPriceList[i].priceError.half.common">
                  <Input-number v-model="stadiumPriceList[i].price.half.common" :min="0" :step="0.01" :precision="2"
                    :active-change="false" size="small" style="width: 90px"
                    @on-change="priceValidator(stadiumPriceList[i], 'half.common')" />
                  <span class="unit">元</span>
                </FormItem>
                </Col>
                <Col span="3">节假日</Col>
                <Col span="8">
                <FormItem :error="stadiumPriceList[i].priceError.half.holiday">
                  <Input-number v-model="stadiumPriceList[i].price.half.holiday" :min="0" :step="0.01" :precision="2"
                    :active-change="false" size="small" style="width: 90px"
                    @on-change="priceValidator(stadiumPriceList[i], 'half.holiday')" />
                  <span class="unit">元</span>
                </FormItem>
                </Col>
              </Row>
              <Row
                :style="(!!stadiumPriceList[i].priceError.all.common || !!stadiumPriceList[i].priceError.all.holiday) ? 'margin-bottom: 20px' : ''">
                <Col span="2" style="font-weight: bold">全场</Col>
                <Col span="3">平时价</Col>
                <Col span="8">
                <FormItem :error="stadiumPriceList[i].priceError.all.common">
                  <Input-number v-model="stadiumPriceList[i].price.all.common" :min="0" :step="0.01" :precision="2"
                    :active-change="false" size="small" style="width: 90px"
                    @on-change="priceValidator(stadiumPriceList[i], 'all.common')" />
                  <span class="unit">元</span>
                </FormItem>
                </Col>
                <Col span="3">节假日</Col>
                <Col span="8">
                <FormItem :error="stadiumPriceList[i].priceError.all.holiday">
                  <Input-number v-model="stadiumPriceList[i].price.all.holiday" :min="0" :step="0.01" :precision="2"
                    :active-change="false" size="small" style="width: 90px"
                    @on-change="priceValidator(stadiumPriceList[i], 'all.holiday')" />
                  <span class="unit">元</span>
                </FormItem>
                </Col>
              </Row>
              </Col>
            </Row>
          </Card>
          <div v-if="stadiumPriceList.length === 0" style="text-align: center; margin: 40px">
            <img src="../../../assets/img/stat_null.png" alt="null" />
            <p>暂无数据</p>
          </div>
          <Button @click="handleAddStadiumPrice">
            <Icon type="ios-plus" /> 新增会员价
          </Button>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleSubmit" :loading="loading">确定</Button>
        <Button @click="handleCancel">取消</Button>
      </div>
    </Modal>
    <Modal v-model="loading" :mask-closable="false" class-name="vertical-center-modal" @on-cancel="loading = false"
      footer-hide>
      <Progress :percent="percent" />
      <h4 style="text-align: center; margin-top: 10px">正在设置会员价，请勿离开此页面...</h4>
    </Modal>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, computed, nextTick, getCurrentInstance } from 'vue';
import { Message } from 'iview';
import service from '@/service';
import _ from 'lodash';
import Treeselect, { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { getWeekJson } from '@/service/putOSS.js';

// instance
const ins = getCurrentInstance();
const bus_id = ins.proxy.$store.state.busId;

// props & emits
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:show']);

// constants variable
const SPACE_LIMIT = 20;

// card tree
const cardTree = ref([]);
const initCardTree = async () => {
  try {
    const res = await service.get('/Web/Activity/get_cards_for_activity')
    if (res.data.errorcode === 0) {
      const array = res.data.data.map((item) => {
        return {
          label: item.card_name,
          id: item.card_id,
          key: item.card_id,
          card_type_id: item.card_type_id,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
        };
      });
      const getCardByType = (type) => {
        return array.filter((item) => item.card_type_id === type && item.is_pt_time_limit_card !== '1');
      };
      cardTree.value = [
        {
          label: '全部卡种',
          id: 'all',
          key: 'all',
          children: [
            {
              label: '期限卡',
              id: 'term',
              key: 'term',
              children: getCardByType('1'),
            },
            {
              label: '次卡',
              id: 'times',
              key: 'times',
              children: getCardByType('2'),
            },
            {
              label: '储值卡',
              id: 'value',
              key: 'value',
              children: getCardByType('3'),
            },
            {
              label: '私教课',
              id: 'private',
              key: 'private',
              children: getCardByType('4'),
            },
            {
              label: '泳教课',
              id: 'swim',
              key: 'swim',
              children: getCardByType('5'),
            },
          ].filter((item) => item.children.length > 0),
        },
      ];
    }
  } catch (error) {
    console.error(error);
    cardTree.value = [];
  }
};
const handleCardChange = () => {
  formRef.value.validateField('card_ids');
};

// stadium list, it's the options of the select tag, packed in a tree
const stadiumList = ref([]);
const getStadiumList = async () => {
  const res = await service.post('/Web/Space/getSpaceNewNumber', {
    bus_id,
    type: 1,
  });

  if (res.data.errorcode === 0) {
    res.data.data.forEach((item) => {
      stadiumList.value.push({
        id: item.id,
        label: item.name,
        level: 1,
        isDisabled: false,
        isHalf: Number(item.is_half),
        children: [
          {
            label: '周一',
            id: `${item.id}_monday`,
            url: item.schedule_price?.monday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周二',
            id: `${item.id}_tuesday`,
            url: item.schedule_price?.tuesday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周三',
            id: `${item.id}_wednesday`,
            url: item.schedule_price?.wednesday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周四',
            id: `${item.id}_thursday`,
            url: item.schedule_price?.thursday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周五',
            id: `${item.id}_friday`,
            url: item.schedule_price?.friday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周六',
            id: `${item.id}_saturday`,
            url: item.schedule_price?.saturday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          },
          {
            label: '周日',
            id: `${item.id}_sunday`,
            url: item.schedule_price?.sunday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
          }
        ]
      })
    })
  }

  return res;
}

// stadium price list
const stadiumPriceList = ref([]);
const selectedIdList = computed(() => {
  return Array.from(new Set(stadiumPriceList.value.flatMap(item => item.checked)))
})
const setDisable = () => {
  if (stadiumPriceList.value.length === 0) return;
  
  for (let index = 0; index < stadiumPriceList.value.length; index++) {
    const own_ids = stadiumPriceList.value[index].checked;
    const disable_ids = selectedIdList.value.filter((id) => !own_ids.includes(id));

    for (let stadium of stadiumPriceList.value[index].tree) {
      let dayCount = 0;

      for (let day of stadium.children) {
        if (Array.isArray(day.children)) {
          let timeCount = 0;

          for (let time of day.children) {
            if (disable_ids.includes(time.id)) {
              time.isDisabled = true;
              timeCount += 1;
            } else {
              time.isDisabled = false;
            }
          }

          if (timeCount === day.children.length) {
            day.isDisabled = true;
            dayCount += 1;
          } else {
            day.isDisabled = false;
          }
        }
      }

      if (dayCount === stadium.children.length) {
        stadium.isDisabled = true;
      } else {
        stadium.isDisabled = false;
      }
    }

  }
};

let uid = 0;
const handleAddStadiumPrice = () => {
  const tree = _.cloneDeep(stadiumList.value)
  stadiumPriceList.value.push({
    uid,
    checked: [],
    tree,
    price: {
      all: {
        common: null,
        holiday: null,
      },
      half: {
        common: null,
        holiday: null,
      }
    },
    checkedError: '',
    priceError: {
      all: {
        common: '',
        holiday: '',
      },
      half: {
        common: '',
        holiday: '',
      }
    },
    hasHalf: false,
  })

  uid += 1;
}
const handleDeleteStadiumPrice = (index) => {
  stadiumPriceList.value.splice(index, 1)

  nextTick(() => {
    setDisable()
  })
}

// pull data from oss
const setTimeList = async (parentNode) => {
  if (!Array.isArray(parentNode.children) && parentNode.url) {
    const timeList = []
    const res = await getWeekJson(parentNode.url)
    res.list.forEach((item) => {
      const id = `${parentNode.id}_${item.start_time}_${item.end_time}`
      timeList.push({
        inputLabel: `${parentNode.parent} / ${parentNode.label} / ${item.start_time}~${item.end_time}`,
        label: `${item.start_time} ~ ${item.end_time}`,
        id,
        level: 3,
        isDisabled: selectedIdList.value.includes(id),
      })
    });
    parentNode.children = timeList
  }
}
const loadOptions = async ({ action, parentNode, callback }) => {
  if (action === LOAD_CHILDREN_OPTIONS) {
    if (parentNode.url) {
      await setTimeList(parentNode);
      callback();
    } else {
      parentNode.children = [];
      callback(new Error('未设置排场时间'));
    }
  }
}

// stadium price select
const removeChecked = (index, node) => {
  nextTick(() => {
    if (node.level === 1) {
      const removeIds = [node.id, ...node.children.map(item => item.id)]
      stadiumPriceList.value[index].checked = stadiumPriceList.value[index].checked.filter(item => !removeIds.includes(item))
    } else {
      stadiumPriceList.value[index].checked = stadiumPriceList.value[index].checked.filter(item => item !== node.id)
    }
  })
}

const handleSelectNode = async (index, node) => {
  // validate
  const spaceChecked = new Set();
  stadiumPriceList.value[index].checked.forEach(item => {
    const [id] = item.split('_');
    spaceChecked.add(id);
  })

  if (spaceChecked.size >= SPACE_LIMIT) {
    Message.error(`最多选择${SPACE_LIMIT}个场地`)
    removeChecked(index, node)
    return false;
  }

  if (node.level === 1) {
    // remove level 1 and 2
    removeChecked(index, node)

    // pull oss
    for (let item of node.children) {
      await setTimeList(item)
    }

    // get time checked
    const newChecked = node.children.map(item => item.children).flat().filter(item => !!item).filter(item => !item.isDisabled).map(item => item.id)
    if (newChecked.length === 0) {
      Message.error(`"${node.label}"无可用排场时间!`)
    } else {
      stadiumPriceList.value[index].checked = Array.from(new Set([...stadiumPriceList.value[index].checked.filter(item => item), ...newChecked]))
    }
  } else if (node.level === 2) {
    // remove current node
    removeChecked(index, node)

    // pull oss
    await setTimeList(node)

    // get time checked
    if (Array.isArray(node.children) && node.children.length) {
      const newChecked = node.children.filter(item => !item.isDisabled).map(item => item.id)
      stadiumPriceList.value[index].checked = Array.from(new Set([...stadiumPriceList.value[index].checked.filter(item => item), ...newChecked]))
    } else {
      Message.error(`"${node.parent} (${node.label})"无可用排场时间!`)
    }
  }

  nextTick(() => {
    checkedValidator(stadiumPriceList.value[index])
    setDisable()
  })
}
const handleDeselectNode = () => {
  nextTick(() => {
    setDisable()
  })
}

const handleInputNode = (index, value) => {
  // disable other stadium
  if (Array.isArray(value) && value.length === 0) {
    nextTick(() => {
      setDisable()
    })
  }
  // check the space half or all
  let hasHalf = false;
  const spaceIdList = stadiumPriceList.value[index].checked.map(item => item.split('_')[0])
  const spaceIdSet = new Set(spaceIdList)
  for (let id of spaceIdSet) {
    const space = stadiumList.value.find(item => item.id == id)
    if (space.isHalf === 1) {
      hasHalf = true;
      break;
    }
  }
  stadiumPriceList.value[index].hasHalf = hasHalf;
}

// form validator
const arrayValidator = (rule, value, callback) => {
  if (value.length === 0) {
    callback(new Error('请选择会员卡'))
  } else {
    callback()
  }
}
const priceValidator = (item, field = '') => {
  const message = '请输入会员价';
  let error = false;

  if (field !== '') {
    const [f1, f2] = field.split('.');
    if (item.price[f1][f2] === null || item.price[f1][f2] === undefined) {
      item.priceError[f1][f2] = message;
      error = true;
    } else {
      item.priceError[f1][f2] = '';
    }
    return error;
  }

  if (item.price.all.common === null || item.price.all.common === undefined) {
    item.priceError.all.common = message;
    error = true;
  } else {
    item.priceError.all.common = '';
  }

  if (item.price.all.holiday === null || item.price.all.holiday === undefined) {
    item.priceError.all.holiday = message;
    error = true;
  } else {
    item.priceError.all.holiday = '';
  }

  if (item.hasHalf) {
    if (item.price.half.common === null || item.price.half.common === undefined) {
      item.priceError.half.common = message;
      error = true;
    } else {
      item.priceError.half.common = '';
    }

    if (item.price.half.holiday === null || item.price.half.holiday === undefined) {
      item.priceError.half.holiday = message;
      error = true;
    } else {
      item.priceError.half.holiday = '';
    }
  }

  return error;
}
const checkedValidator = (item) => {
  if (item.checked.length === 0) {
    item.checkedError = '请选择场次';
    return true;
  } else {
    item.checkedError = '';
    return false;
  }
}

// variables
const percent = ref(0);
const loading = ref(false);
const visible = ref(false);
const formRef = ref(null);
const NONE_FORM_DATA = {
  card_ids: [],
  schedule_price_data: [],
  type: 1
}
const formData = ref(_.cloneDeep(NONE_FORM_DATA));
const formRules = ref({
  card_ids: [
    {
      required: true,
      validator: arrayValidator,
    },
  ],
});

// methods
const handleCancel = () => {
  formRef.value.resetFields();
  emit('update:show', false);
};
const handleSubmit = async () => {
  const valid = await formRef.value.validate();

  if (valid) {
    const list = [];

    // generate data
    let error = false;
    stadiumPriceList.value.forEach((item) => {
      const spaceMap = new Map();

      error = checkedValidator(item);
      item.checked.forEach((mix) => {
        const [id, day, start, end] = mix.split('_');

        if (spaceMap.has(id)) {
          const oldDayMap = spaceMap.get(id);

          if (oldDayMap.has(day)) {
            const dayValue = oldDayMap.get(day);
            dayValue.push({ start, end });

            const dayMap = new Map(oldDayMap);
            dayMap.set(day, dayValue);
            spaceMap.set(id, dayMap);
          } else {
            const dayMap = new Map(oldDayMap);
            dayMap.set(day, [{ start, end }]);
            spaceMap.set(id, dayMap);
          }
        } else {
          const dayMap = new Map();
          dayMap.set(day, [{ start, end }]);
          spaceMap.set(id, dayMap);
        }
      })

      const space = [];
      spaceMap.forEach((dayValue, id) => {
        const dayList = {};

        dayValue.forEach((timeValue, day) => {
          dayList[day] = timeValue
        })

        space.push({
          space_id: id,
          ...dayList
        })
      })

      error = priceValidator(item)
      const price = item.price;

      list.push({
        space,
        price
      })
    })

    // validate
    if (error) {
      loading.value = false;
      Message.error('请填写完整的会员价');
      return;
    } else {
      loading.value = true;
    }

    // split data and multiple request
    percent.value = 0;
    const chunk = 100 / list.length;
    const promises = [];

    let hasAuthority = true;
    for (let item of list) {
      formData.value.schedule_price_data = item;

      const promise = service.post('/web/Space/SpaceMemberPriceSave', formData.value, { loading: false }).then((res) => {
        if (res.data.errorcode === 0) {
          percent.value = Number((percent.value + chunk).toFixed(2));
        } else if (res.data.errorcode === 40014) {
          hasAuthority = false;
          Message.error(res.data.errormsg);
        } else {
          Message.error(res.data.errormsg);
        }

        return res;
      });

      promises.push(promise);
    }

    Promise.all(promises).then(() => {
      if (hasAuthority) {
        Message.success({
          content: '会员价设置完成',
          duration: 2,
          onClose: () => {
            handleCancel();
          },
        });
      }
      setTimeout(() => {
        loading.value = false; 
      }, 1000);
    });
  } else {
    loading.value = false;
  }
};

// created
initCardTree();
getStadiumList();

// watch
watch(
  () => props.show,
  (val) => {
    visible.value = val;
    if (val) {
      loading.value = false;
      formData.value = _.cloneDeep(NONE_FORM_DATA);
      formRef.value.resetFields();
      stadiumPriceList.value = [];
      handleAddStadiumPrice()
    } else {
      stadiumPriceList.value = [];
    }
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
::v-deep(.vertical-center-modal) {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}

.unit {
  margin: auto 8px;
}
</style>
