<template>
  <div class="box">
    <Row class="box-head">
        <Col offset="1" span="22" class="head-option">
            <Input style="width: 180px" v-model="searchTxt" class="option-select" placeholder="场地名称" />
            <Select v-model="stadiumCategoryId" style="width:200px;margin-right:20px;" placeholder="场地类型" clearable>
                <Option v-for="item in stadiumCategoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
            <Button type="success" @click="handleSearch">搜索</Button>
        </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <router-link style="color:white;" target="_self" to="/stadium/save/0">
          <Button type="success" style="margin-right: 30px;">添加场地</Button>
        </router-link>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" placement="top" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>
  </div>
</template>
<script>
export default {
  name: 'SportList',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      columns: [
          { title: '场地名称', key: 'space_name' },
          { title: '场地类型', key: 'space_type_name' },
          { title: '启用', key: 'status', render: (h, params) => {
              const change = val=>{
                this.$service.post('/Web/Space/open', {
                  space_id: params.row.space_id,
                  type: 1,
                  status: val
                }).then(res => {
                  if (res.data.errorcode == 0) {
                    this.$Message.info(res.data.errormsg);
                  } else {
                    params.row.status = (val==1)?0:1;
                    this.$Message.error(res.data.errormsg);
                  }
                });
              };
              return (
                  <i-switch v-model={params.row.status} true-value={1} false-value={0} onOn-change={change}></i-switch>
              );
            }
          },
          { title: '会员端微信支付', key: 'online_reserve', render: (h, params) => {
              const change = val=>{
                this.$service.post('/Web/Space/open', {
                  space_id: params.row.space_id,
                  type: 2,
                  status: val
                }).then(res => {
                  if (res.data.errorcode == 0) {
                    this.$Message.info(res.data.errormsg);
                  } else {
                    params.row.online_reserve = (val==1)?0:1;
                    this.$Message.error(res.data.errormsg);
                  }
                });
              };
              return (
                  <i-switch v-model={params.row.online_reserve} true-value={1} false-value={0} onOn-change={change}></i-switch>
              );
            }
          },
          {
          title: '操作',
          key: 'option',
          width: 150,
          render: (h, params) => {
              return (
                  <div style="display: flex;flex-direction: row;justify-content: space-around;">
                    <a onClick={() => {
                        this.$router.push(`/stadium/save/${params.row.space_id}`);
                      }}>编辑</a>
                    <a style="color:red;" onClick={() => {
                        this.deleteStadiumById(params.row.space_id);
                      }}>删除</a>
                  </div>
              );
            }
          }
      ],
      list: [],
      searchTxt: '',
      stadiumCategoryList: [],
      stadiumCategoryId: ''
    };
  },
  methods: {
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    },
    getList() {
      return this.$service.post('/Web/Space/getList', {
          space_name: this.searchTxt,
          space_type_id: this.stadiumCategoryId,
          page_no: this.currentPage,
          page_size: this.pageSize,
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.total = parseInt(res.data.data.count);
            this.list = [];
            if (Array.isArray(res.data.data.list)) {
              this.list = res.data.data.list;
            }
          } else {this.$Message.error(res.data.errormsg);}
        });
    },
    getStadiumCategoryList() {
      return this.$service.post('/Web/Space/getTypes').then(res => {
        if (res.data.errorcode == 0) {
          this.stadiumCategoryList = res.data.data;
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    deleteStadiumById(space_id) {
      return this.$service.post('/Web/Space/delSpace', {
        space_id
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.info(res.data.errormsg);
          this.getList();
        } else {this.$Message.error(res.data.errormsg);}
      });
    }
  },
  mounted() {
    this.getList();
    this.getStadiumCategoryList();
  }
};
</script>

<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
