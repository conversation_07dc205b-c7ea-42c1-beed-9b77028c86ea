<template>
  <div class="box">
    <div class="header">{{ title }}</div>
    <div class="buddy">
      <Form ref="formRef" :model="formPost" :rules="formRule" :label-width="120">
        <Form-item label="场地类型名称" prop="name">
          <Input v-model="formPost.name" placeholder="请输入..." style="width: 300px"></Input>
        </Form-item>
        <Form-item label="经营方式" prop="type">
          <CheckboxGroup v-model="formPost.type">
            <Checkbox label="1" v-show="false">订场</Checkbox>
            <Checkbox label="2">散场</Checkbox>
          </CheckboxGroup>
        </Form-item>
        <Form-item v-if="formPost.type.includes('1')" label="订场团队人数限制" prop="max_join_people" :rules="[{required:true, message:'订场团队人数不能为空！'}]">
          <span>订场时最多可支持团队进场人数</span>
          <InputNumber
            v-model="formPost.max_join_people"
            placeholder="请输入..."
            :min="1"
            size="small"
            style="margin: 0 10px"
          ></InputNumber>
          <span>人</span>
        </Form-item>
        <Form-item prop="pic_url">
          <div class="image-description image-description-required" slot="label">
            <p class="label">场馆环境</p>
            <p class="tip">图片最佳尺寸: 690X260</p>
            <p class="tip">推荐图片大小: &lt;100kb</p>
            <p class="tip">格式限制: jpg、png</p>
          </div>
          <div class="image-box" v-if="formPost.pic_url.length > 0">
            <div class="photo-box" v-for="(url, $index) in formPost.pic_url" :key="url">
              <img class="photo" :src="url" />
              <div class="delete-photo" @click="handlePhotoDelete($index)">
                <Icon type="ios-close" size="45" />
              </div>
            </div>
          </div>
          <image-crop-upload
            refName="cropEverything"
            v-model="imageTemporary"
            :options="{ aspectRatio: 69 / 26 }"
            multiple
            style="max-width: 690px"
          />
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit">提交</Button>
            <Button @click="handleCancel">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>

<script>
import ImageCropUpload from '@/components/form/cropper.vue'

export default {
  components: { ImageCropUpload },
  props: {
    stadiumId: {
      type: [String, Number],
      default: 0,
    },
  },
  data() {
    return {
      imageTemporary: '',
      formPost: {
        name: '',
        type: ['1'],
        max_join_people: 4,
        pic_url: [],
      },
      formRule: {
        name: [{ required: true, message: '请输入场地类型名称', trigger: 'blur' }],
        type: [
          {
            required: false,
            message: '请选择经营方式',
            validator: (rule, value, callback) => {
              if (value.length === 0) {
                callback(new Error('请选择经营方式'))
              } else {
                callback()
              }
            },
          },
        ],
      },
    }
  },
  watch: {
    imageTemporary(newValue, oldValue) {
      if (newValue) {
        this.formPost.pic_url.push(newValue)
      }
    },
  },
  computed: {
    title() {
      return this.stadiumId ? '编辑' : '新增'
    },
  },
  created() {
    if (this.stadiumId) {
      this.getInfo()
    }
  },
  methods: {
    getInfo() {
      return this.$service.get('/Web/Space/getTypeInfo?id=' + this.stadiumId).then((res) => {
        if (res.data.errorcode == 0) {
          this.formPost = res.data.data
        }
      })
    },
    handlePhotoDelete(index) {
      this.formPost.pic_url.splice(index, 1)
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.formPost.pic_url.length === 0) {
            this.formPost.pic_url = ''
          }
          this.$service.post('/Web/Space/saveSpaceType', this.formPost).then((res) => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg)
              this.$router.back()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCancel() {
      this.$router.back()
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;

  .header {
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    color: #333;
    background-color: #f7f7f7;
    width: 100%;
    background: #f7f7f7;
    height: 40px;
    padding: 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #e0e3e9;
  }

  .buddy {
    width: 100%;
    min-width: 721px;
    height: 100%;
    padding: 20px 40px;
    box-sizing: border-box;
  }
}

.image-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: #f7f7f7;
  padding: 20px;
  margin-bottom: 20px;

  .photo-box {
    display: flex;
    flex-direction: row;

    .photo {
      width: 570px;
      height: 240px;
      margin-top: 10px;
    }

    .delete-photo {
      width: 45px;
      height: 45px;
      position: relative;
      top: -10px;
      left: -25px;
      background-color: rgba(0, 0, 0, 0.4);
      color: red;
      border-radius: 50%;
      overflow: hidden;
      cursor: pointer;
    }
  }
}
</style>
