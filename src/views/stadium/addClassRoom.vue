<template>
  <div class="container rommmin-wrap">
    <header>
      <h3>{{!$route.query.id?'新增':'编辑'}}</h3>
    </header>
    <Form ref="stadium" class="form" :model="postData" :label-width="120">
      <Form-item label="教室名称" prop="classroom_name" :rules="{ required: true, message: '请输入教室名称'}">
        <Input v-model="postData.classroom_name" placeholder="请输入..." />
      </Form-item>
      <Form-item label="可容纳人数" prop="allow_number" :rules="{ required: true, message: '请输入可容纳人数'}">
        <InputNumber v-model="postData.allow_number" :max="99999" :min="0" :step="1" />
      </Form-item>
      <Form-item label="教室选座" prop="seat_status">
        <Radio-group v-model="postData.seat_status">
          <Radio label="0">不启用</Radio>
          <Radio label="1">启用</Radio>
        </Radio-group>
      </Form-item>
      <Form-item v-if="postData.seat_status == 1 && postData.seats">
        <SeatsSet v-model="postData.seats" :is-show-set="true" />
      </Form-item>
      <Form-item>
        <div class="buttons">
          <Button type="primary" @click="handleSaveForm">保存</Button>
          <Button @click="handleCancelForm">取消</Button>
        </div>
      </Form-item>
    </Form>
  </div>
</template>

<script>
import { getBaseUrl } from "src/utils/config";
import SeatsSet from "./components/SeatsSet";

export default {
  data() {
    return {
      postData: {
        classroom_id: "",
        classroom_name: "",
        allow_number: null,
        seat_status: "0",
        seats: ''
      }
    };
  },
  components: { SeatsSet },
  computed: {},
  watch: {
    'postData.seat_status'(val) {
      if(val) {
        this.postData.seats = this.postData.seats ? this.postData.seats : []
      } else {
        this.postData.seats = ''
      }
    }
  },
  methods: {
    // about form submit.
    handleSaveForm() {
      this.$refs["stadium"].validate(valid => {
        const url = this.postData.classroom_id
          ? "/Web/Classroom/post_update"
          : "/Web/Classroom/post_create";
        if (valid) {
          this.$service.post(url, this.postData).then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.$router.back();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    },
    handleCancelForm() {
      this.$router.back();
    },
    getInfo(classroom_id) {
      return this.$service
        .post("/Web/Classroom/get_info", { classroom_id })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.postData = res.data.data;
            this.postData.classroom_id = classroom_id;
            this.postData.allow_number = parseInt(this.postData.allow_number);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    }
  },
  mounted() {
    const roomId = this.$route.query.id;
    if (roomId) {
      this.postData.classroom_id = roomId;
      this.getInfo(roomId);
    }
  }
};
</script>
<style lang="less">
.rommmin-wrap {
  .ivu-input-wrapper,
  .ivu-input-number {
    width: 300px !important;
  }
}
</style>

