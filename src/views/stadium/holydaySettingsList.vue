<template>
  <div class="box">
    <div class="header">
      <Date-picker
        v-model="date"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        style="width: 200px; margin-right: 20px"
      ></Date-picker>
      <Button type="success" icon="ios-search" @click="handleSearch">搜索</Button>
    </div>
    <div class="buddy">
      <Table
        ref="table"
        :columns="tableCols"
        :data="tableData"
        @on-selection-change="onSelectionChange"
        @on-select="onSelect"
        @on-select-all="onSelectAll"
        @on-select-cancel="onSelectCancel"
      ></Table>
      <div class="page">
        <div class="page-btn">
          <Button type="success" @click="handleSave">新增临时调价</Button>
          <Button style="margin-left: 20px" :disabled="selection.length === 0" @click="dialogFlag = true">批量删除</Button>
          <Button style="margin-left: 20px" @click="handleExport">导出excel</Button>
        </div>
        <Page
          @on-change="handlePage"
          :total="total"
          :current="searchPost.page"
          @on-page-size-change="pageSizeChanged"
          show-total
          show-sizer
        ></Page>
      </div>
    </div>

    <Modal v-model="dialogFlag" width="360" @on-visible-change="handleModalChange">
      <p slot="header" style="color: #f60; text-align: center">
        <Icon type="ios-information-circle"></Icon>
        <span>提示</span>
      </p>
      <div style="text-align: center">
        <p>确认删除？</p>
      </div>
      <div slot="footer">
        <Button type="error" size="large" long @click="handleDelete(dialogId)">删除</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { formatDate } from 'utils'
import Selection from 'mixins/selection'
import editIcon from '@/assets/img/booking/edit-blue.png'
import deleteIcon from '@/assets/img/booking/delete-blue.png'

export default {
  mixins: [Selection],
  data() {
    return {
      dialogId: '',
      dialogFlag: false,
      total: 0,
      date: '',
      searchPost: {
        page: 1,
        per_page: 10,
        start_date: '',
      },
      tableCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        {
          title: '操作时间',
          key: 'update_time',
        },
        {
          title: '日期',
          key: 'start_time',
          render: (h, params) => {
            return h('div', params.row.start_time + ' ~ ' + params.row.end_time)
          },
        },
        {
          title: '场地',
          key: 'space_name',
        },
        {
          title: '操作人',
          key: 'operator_name',
        },
        {
          title: '状态', // 执行中，已完成
          key: 'status',
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            const action = []
            if (params.row.status === '执行中') {
              action.push(
                h(
                  'img',
                  {
                    attrs: {
                      src: editIcon,
                      title: '编辑',
                    },
                    style: {
                      width: '14px',
                      height: '14px',
                      marginRight: '24px',
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        this.handleSave(params.row.id)
                      },
                    },
                  },
                  '复制'
                )
              )
              action.push(
                h(
                  'img',
                  {
                    attrs: {
                      src: deleteIcon,
                      title: '删除',
                    },
                    style: {
                      width: '14px',
                      height: '14px',
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        // this.handleDelete(params.row.id)
                        this.dialogId = params.row.id
                        this.dialogFlag = true
                      },
                    },
                  },
                  '删除'
                )
              )
            }
            return h('div', action)
          },
        },
      ],
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (this.date === '') {
        this.searchPost.start_date = ''
      } else {
        this.searchPost.start_date = formatDate(this.date, 'yyyy-MM-dd')
      }
      return this.$service.post('/Web/Space/getHolidayList', this.searchPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.data.map((item) => {
            item._checked = this.selectionId.includes(item.id)
            return item
          })
          this.total = res.data.data.total
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleExport() {
      this.searchPost.page = 1
      this.searchPost.per_page = this.total
      this.$service.post('/Web/Space/excelHolidayList', this.searchPost).then((res) => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data.data)) {
            list = res.data.data.data
            list.forEach((row) => {
              row.start_time = row.start_time + ' ~ ' + row.end_time
            })
          }
          this.$refs.table.exportCsv({
            filename: `临时调价记录-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.tableCols.filter((col, index) => 0 < index && index + 1 < this.tableCols.length),
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSearch() {
      this.searchPost.page = 1
      this.getList()
    },
    handlePage(page) {
      this.searchPost.page = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.searchPost.page = 1
      this.searchPost.per_page = size
      this.getList()
    },
    handleSave(id) {
      if (typeof id !== 'number') {
        id = null
      }
      this.$router.push({
        name: '临时调价设置',
        params: { id },
      })
    },
    handleModalChange(flag) {
      if (!flag) {
        this.dialogId = ''
      }
    },
    handleDelete(id = '') {
      let ids = []
      if (typeof id === 'number') {
        ids.push(id)
      } else {
        // ids = [...this.selectionId]
        const list = this.selection.filter((row) => row.status === '执行中')
        ids = list.map((row) => row.id)
      }
      if (ids.length === 0) {
        this.$Message.error('请选择要删除的记录')
        return
      }
      return this.$service.post('/Web/Space/deleteHolidayList', { ids }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success('删除成功')
          this.dialogId = ''
          this.dialogFlag = false
          this.getList()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
  }

  .buddy {
    margin: 20px 0;

    .page {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;

      .page-btn {
      }
    }
  }
}
</style>
