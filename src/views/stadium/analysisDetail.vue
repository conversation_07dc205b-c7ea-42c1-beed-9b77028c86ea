<template>
  <div class="box">
    <div class="header">
      <Select
        filterable
        placeholder="请选择门店..."
        v-model="searchPost.bus_id"
        @on-change="getCategoryList"
        style="width: 180px; margin-right: 20px"
      >
        <Option v-for="item in storeList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Select
        clearable
        filterable
        placeholder="请选择类型"
        v-model="searchPost.space_type_id"
        style="width: 180px; margin-right: 20px"
      >
        <Option v-for="item in categoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Input v-model="searchPost.search" style="width: 180px; margin-right: 20px" clearable placeholder="请输入场地名称" />
      <Select
        clearable
        filterable
        placeholder="请选择会员类型"
        v-model="searchPost.user_type"
        style="width: 200px; margin-right: 20px"
      >
        <Option value="1">会员</Option>
        <Option value="2">散客</Option>
      </Select>
      <Date-picker
        v-model="daterange"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 200px; margin-right: 20px"
        :options="dateOptions"
        :clearable="false"
      ></Date-picker>
      <Button type="success" icon="ios-search" @click="doSearch">搜索</Button>
    </div>
    <div class="buddy">
      <Table ref="table" :columns="tableCols" :data="tableData"></Table>
    </div>
    <div class="footer">
      <Button @click="handleExport">导出excel</Button>
      <pager :post-data="searchPost" :total="count" :history="false" @on-change="handlePageChange"></pager>
    </div>
  </div>
</template>

<script>
import { formatDate } from 'utils'
import { mapState } from 'vuex'
import Pager from 'components/pager'

export default {
    name: 'analysisDetail',
  data() {
    return {
      daterange: [],
      searchPost: {
        bus_id: '',
        space_type_id: '',
        search: '',
        user_type: '',
        start_time: '',
        end_time: '',
        page_no: 1,
        page_size: 10,
        is_export: 0,
      },
      dateOptions: {
        shortcuts: [
          {
            text: '最近一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
          {
            text: '最近一月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            },
          },
          {
            text: '最近三月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              return [start, end]
            },
          },
        ],
      },
      storeList: [],
      categoryList: [],
      tableCols: [
        {
          title: '场地名称',
          key: 'space_name',
        },
        {
          title: '场地类型',
          key: 'space_type_name',
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            const item = params.row
            return (
              <a
                onClick={() => {
                  this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
                }}
              >
                {item.username}
              </a>
            )
          },
        },
        {
          title: '会员类型',
          key: 'user_type_name',
        },
        // {
        //   title: '预订时长(小时)',
        //   key: 'diff_hours',
        // },
        {
          title: '订场场次时间',
          key: 'date_desc',
          render: (h, params) => {
            const item = params.row
            return (
              <span>
                {item.date_desc} {item.time_desc}
              </span>
            )
          },
        },
        {
          title: '实付金额',
          key: 'amount',
        },
        {
          title: '付款方式',
          key: 'mix_pay_type_name',
          render: (h, params) => {
            const item = params.row
            return <span>{item.mix_pay_type_name ? item.mix_pay_type_name : '-'}</span>
          },
        },
        {
          title: '备注',
          key: 'remark',
          render: (h, params) => {
            let shortRemark = params.row.remark || '';
            if (shortRemark.length > 15) {
              shortRemark = params.row.remark.substr(0, 20) + '...';
              return h('Tooltip', {
                props: {
                  content: params.row.remark,
                  transfer: true
                }
              }, shortRemark)
            } else {
              return h('div', shortRemark)
            }
          }
        },
        {
          title: '付款时间',
          key: 'pay_time',
        },
        {
          title: '退款金额',
          key: 'refund_amount',
          render: (h, params) => {
            const item = params.row
            return <span>{item.refund_time ? item.refund_amount : '-'}</span>
          },
        },
        {
          title: '退款方式',
          key: 'mix_refund_type_name',
          render: (h, params) => {
            const item = params.row
            return <span>{item.mix_refund_type_name ? item.mix_refund_type_name : '-'}</span>
          },
        },
        {
          title: '退款时间',
          key: 'refund_time',
          render: (h, params) => {
            const item = params.row
            return <span>{item.refund_time ? item.refund_time : '-'}</span>
          },
        },
      ],
      tableData: [],
      count: 0,
    }
  },
  components: {
    Pager,
  },
  computed: {
    ...mapState(['busId']),
  },
  created() {
    const { bus_id, start, end } = window.history.state
    if (bus_id) {
      this.$route.params.bus_id = bus_id
      this.$route.params.start = start
      this.$route.params.end = end
    }

    if (this.$route.params && this.$route.params.space_type_id && this.$route.params.bus_id) {
      this.searchPost.bus_id = this.$route.params.bus_id
      this.searchPost.space_type_id = Number(this.$route.params.space_type_id)
    } else {
      this.searchPost.bus_id = bus_id || this.busId
    }
    if (this.$route.params && this.$route.params.user_type) {
      this.searchPost.user_type = this.$route.params.user_type + ''
    }
    if (this.$route.params && this.$route.params.start && this.$route.params.end) {
      let firstDay = new Date(this.$route.params.start)
      let lastDay = new Date(this.$route.params.end)
      this.daterange = [firstDay, lastDay]
    } else {
      let today = new Date()
      let firstDay = new Date()
      firstDay.setDate(1)
      this.daterange = [firstDay, today]
    }

    this.getStoreList()
    this.getCategoryList()
    this.getList()
  },
  methods: {
    getStoreList() {
      return this.$service.get('/Web/Business/get_bus_list').then((res) => {
        if (res.data.errorcode === 0) {
          this.storeList = res.data.data.bus_list
        }
      })
    },
    getCategoryList() {
      return this.$service
        .post('/Web/Space/getTypes', {
          bus_id: this.searchPost.bus_id,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.categoryList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost,
      }
      this.getList()
    },
    getList() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.start_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.start_time = ''
        this.searchPost.end_time = ''
      }
      return this.$service.post('/Web/Space/getIncomeList', this.searchPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list
          this.count = res.data.data.count
        }
      })
    },
    handleExport() {
      let post = JSON.parse(JSON.stringify(this.searchPost))
      post.is_export = 1
      this.$service.post('/Web/Space/getIncomeList', post).then((res) => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
          }
          Array.isArray(list) &&
            list.forEach((v) => {
              v.date_desc = v.date_desc + ' ' + v.time_desc
              v.refund_amount = v.refund_time ? v.refund_amount : '-'
              v.refund_time = v.refund_time ? v.refund_time : '-'
              v.mix_pay_type_name = v.mix_pay_type_name ? v.mix_pay_type_name.replaceAll(',', '、') : '-'
              v.mix_refund_type_name = v.mix_refund_type_name ? v.mix_refund_type_name.replaceAll(',', '、') : '-'
              // 替换逗号 替换换行符
              v.remark = v.remark ? v.remark.replaceAll(',', '、').replaceAll('\n', ' ') : '-'
            })
          this.$refs.table.exportCsv({
            filename: `场地分析-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.tableCols,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    doSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}
.box {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  .buddy {
    margin: 20px 0;
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }
}
</style>
