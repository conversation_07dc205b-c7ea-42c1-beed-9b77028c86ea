<template>
  <div class="box">
    <div class="header">
      <Select
        v-model="searchPost.bus_id"
        @on-change="getCategoryList"
        style="width: 200px; margin-right: 20px"
        placeholder="请选择门店..."
        filterable
      >
        <Option v-for="item in storeList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Date-picker
        v-model="daterange"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 200px; margin-right: 20px"
        :options="dateOptions"
        :clearable="false"
      ></Date-picker>
      <Select
        v-model="searchPost.type_id"
        style="width: 200px; margin-right: 20px"
        placeholder="请选择类型..."
        filterable
        clearable
      >
        <Option v-for="item in categoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Button type="success" icon="ios-search" @click="getList">搜索</Button>
    </div>
    <div class="buddy">
      <Table ref="table" :columns="tableCols" :data="tableData"></Table>
    </div>
    <div class="footer">
      <Button @click="handleExport">导出excel</Button>
    </div>
  </div>
</template>

<script>
import { formatDate } from 'utils'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      daterange: [],
      searchPost: {
        bus_id: '',
        type_id: '',
        start_time: '',
        end_time: '',
      },
      dateOptions: {
        shortcuts: [
          {
            text: '最近一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
          {
            text: '最近一月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            },
          },
          {
            text: '最近三月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              return [start, end]
            },
          },
        ],
      },
      storeList: [],
      categoryList: [],
      tableCols: [
        {
          title: '场地类型',
          key: 'space_type_name',
        },
        {
          title: '场地总数',
          key: 'space_count',
        },
        {
          title: '总收入',
          key: 'all_income',
          render: (h, params) => {
            let that = this
            const item = params.row
            return (
              <a
                onClick={() => {
                  this.$router.push({
                    name: '场地收益明细',
                    params: {
                      bus_id: this.searchPost.bus_id,
                      space_type_id: item.space_type_id,
                      start: formatDate(that.daterange[0], 'yyyy-MM-dd'),
                      end: formatDate(that.daterange[1], 'yyyy-MM-dd'),
                    },
                  })
                }}
              >
                {item.all_income}
              </a>
            )
          },
        },
        {
          title: '储值卡收入',
          key: 'value_card_income',
        },
        {
          title: '非储值卡收入',
          key: 'other_income',
        },
        {
          title: '排场场次',
          key: 'schedule_count',
          renderHeader(h, { column }) {
            return (
              <div style="display:flex;align-items:center;">
                <span>排场场次</span>
                <Tooltip content="仅指可供预约的场次，场地休息场次不算在内">
                  <Icon type="ios-help" size="25" color="rgb(255 153 0)" />
                </Tooltip>
              </div>
            )
          },
        },
        {
          title: '实际预约场次',
          key: 'real_booking_no',
        },
        {
          title: '预订总时长(小时)',
          key: 'real_booking_hour',
        },
        {
          title: '场地预约率',
          key: 'booking_rate',
          render(h, params) {
            return <span>{params.row.booking_rate}%</span>
          },
        },
        {
          title: '会员预订场次',
          key: 'member_booking_no',
          render: (h, params) => {
            let that = this
            const item = params.row
            return (
              <a
                onClick={() => {
                  this.$router.push({
                    name: '场地收益明细',
                    params: {
                      bus_id: this.searchPost.bus_id,
                      space_type_id: item.space_type_id,
                      user_type: 1,
                      start: formatDate(that.daterange[0], 'yyyy-MM-dd'),
                      end: formatDate(that.daterange[1], 'yyyy-MM-dd'),
                    },
                  })
                }}
              >
                {item.member_booking_no}
              </a>
            )
          },
        },
        {
          title: '非会员预订场次',
          key: 'no_member_booking_no',
          render: (h, params) => {
            let that = this
            const item = params.row
            return (
              <a
                onClick={() => {
                  this.$router.push({
                    name: '场地收益明细',
                    params: {
                      bus_id: this.searchPost.bus_id,
                      space_type_id: item.space_type_id,
                      user_type: 2,
                      start: formatDate(that.daterange[0], 'yyyy-MM-dd'),
                      end: formatDate(that.daterange[1], 'yyyy-MM-dd'),
                    },
                  })
                }}
              >
                {item.no_member_booking_no}
              </a>
            )
          },
        },
      ],
      tableData: [],
    }
  },
  computed: {
    ...mapState(['busId']),
  },
  created() {
    this.searchPost.bus_id = this.busId
    const today = new Date()
    const firstDay = new Date()
    firstDay.setDate(1)
    this.daterange = [firstDay, today]

    this.getStoreList()
    this.getCategoryList()
    this.getList()
  },
  methods: {
    getStoreList() {
      return this.$service.get('/Web/Business/get_bus_list').then((res) => {
        if (res.data.errorcode === 0) {
          this.storeList = res.data.data.bus_list
        }
      })
    },
    getCategoryList() {
      return this.$service
        .post('/Web/Space/getTypes', {
          bus_id: this.searchPost.bus_id,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.categoryList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getList() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.start_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.start_time = ''
        this.searchPost.end_time = ''
      }
      return this.$service.post('/Web/Space/getTotalList', this.searchPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data
        }
      })
    },
    handleExport() {
      this.$service.post('/Web/Space/getTotalList', this.searchPost).then((res) => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data)) {
            list = res.data.data
          }
          this.$refs.table.exportCsv({
            filename: `场地分析-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.tableCols,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  .buddy {
    margin: 20px 0;
  }

  .footer {
    width: 370px;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
