<template>
  <div class="container tab-table-wrap customized-tabs">
    <Tabs name="order" v-model="activeIndex" @on-click="clickTabs">
      <TabPane name="0" label="订场">
        <the-booking :categoryList.sync="stadiumCategoryList" :long-term-authorities="longTermAuthorities" :authority="authority" />
      </TabPane>
      <TabPane name="1" label="订场记录">
        <Row class="box-head">
          <Col span="24" class="head-option">
          <Input v-model="tab2Post.searchTxt" class="option-item" placeholder="会员姓名/电话" />
          <DatePicker class="option-item" v-model="tab2UpdateRange" @on-change="handleTab2UpdateChange" placeholder="操作时间"
            type="daterange" transfer :clearable="false" />
          <DatePicker class="option-item" v-model="tab2DateRange" @on-change="handleTab2DateChange" placeholder="预订时间"
            type="daterange" transfer />
          <Select v-model="tab2Post.categoryId" class="option-item" placeholder="场地类型" clearable transfer>
            <Option v-for="item in stadiumCategoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
          <Select v-model="tab2Post.type" class="option-item" placeholder="预订类型" clearable transfer>
            <Option value="2">长租预订</Option>
            <Option value="1">常规预订</Option>
          </Select>
          <Select v-model="tab2Post.statusId" class="option-item" placeholder="状态" clearable transfer>
            <Option v-for="(item, index) in stateList" :value="item.code" :key="index">{{ item.name }}</Option>
          </Select>
          <Select v-model="tab2Post.user_type" class="option-item" placeholder="用户类型" clearable transfer>
            <Option value="1">会员</Option>
            <Option value="2">散客</Option>
          </Select>
          <Button icon="ios-search" type="primary" @click="handleSearchOrder" ghost>搜索</Button>
          </Col>
        </Row>
        <Row class="box-body table-wrap">
          <Col span="24">
          <Table ref="table" :columns="columns" :data="logList" disabled-hover stripe></Table>
          </Col>
        </Row>
        <Row class="box-foot">
          <Col span="6" class="foot-option" style="justify-content: flex-start">
          <ExportButton url="/Web/SpaceOrder/getOrderList" :data="{
            search: this.tab2Post.searchTxt,
            update_start_time: this.tab2Post.update_start_time, // 操作开始时间 2018-01-01
            update_end_time: this.tab2Post.update_end_time,
            start_time: this.tab2Post.start_time, // 预订开始时间 2018-01-01
            end_time: this.tab2Post.end_time,
            space_type_id: this.tab2Post.categoryId, // 场地类型
            status: this.tab2Post.statusId, // 状态  0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款）
            type: this.tab2Post.type,
            user_type: this.tab2Post.user_type,
            page_no: this.currentPage,
            page_size: this.pageSize
          }" />
          </Col>
          <Col span="18" class="foot-option">
          <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged"
            show-total show-sizer></Page>
          </Col>
        </Row>

        <Modal :title="isNewRefund ? '退款' : '取消预订'" v-model="showOrderModal" width="1200">
          <!-- <div class="modal-header">
            <h3>{{ isNewRefund ? '退款' : '取消预订' }}</h3>
          </div> -->
          <div class="modal-buddy">
            <Form ref="orderModal" :model="orderModal" class="modal-form" :label-width="120">
              <div class="modal-box">
                <div class="modal-divider form-no-bottom">
                  <Form-item label="客户姓名" prop="username">{{ orderModal.username }}</Form-item>
                  <Form-item label="联系方式" prop="phone">{{ orderModal.phone }}</Form-item>
                  <Form-item label="订单编号" prop="orderCode">{{ orderModal.orderCode }}</Form-item>

                  <Divider></Divider>

                  <Form-item label="场地名称" prop="stadium">{{ orderModal.stadium }}</Form-item>
                  <Form-item label="场地类型" prop="stadiumCategory">{{ orderModal.stadiumCategory }}</Form-item>
                  <!-- <Form-item label="日期" prop="reservationDay">{{ orderModal.reservationDay }}</Form-item> -->
                  <Form-item label="时间" prop="reservationTime">{{ orderModal.reservationDay }} {{ orderModal.reservationTime }}</Form-item>
                  <Form-item label="实收金额" prop="price">{{ Number(orderModal.price).toFixed(2) }}</Form-item>
                  <Form-item v-if="orderModal.type == 1" label="应退比例" prop="refund_rate" style="color: red;">{{
                    (orderModal.refund_rate * 100).toFixed(2) }}%</Form-item>
                  <Form-item label="应退金额" style="color: red;">{{ Number(orderModal.should_refund_amount).toFixed(2) }}</Form-item> 

                  <template v-if="orderModal.ssl_status === 0">
                    <Divider></Divider>

                    <Form-item label="课程名称">{{ orderModal.card_name }}</Form-item>
                    <Form-item label="教练名称">{{ orderModal.coach_name }}</Form-item>
                    <Form-item label="上课时间">{{ orderModal.course_time }}</Form-item> 

                    <template v-if="orderModal.reservation_type === 2">
                      <Form-item label="实收金额">{{ Number(orderModal.pt_income_amount).toFixed(2) }}</Form-item>
                      <Form-item v-if="isRefund && orderModal.type == 1" label="应退比例" prop="refund_rate" style="color: red;">{{(orderModal.pt_refund_rate*100).toFixed(2)}}%</Form-item>
                      <Form-item label="应退金额" style="color: red;">{{ Number(orderModal.pt_refund_amount).toFixed(2) }}</Form-item> 
                    </template>
                  </template>

                </div>
                <div class="modal-divider">
                  <Form-item label="订场实退: ">
                    <Input-number
                      :precision="2"
                      :active-change="false"
                      :min="0"
                      style="width: 300px"
                      v-model="orderModal.put_refund_amount"
                    />
                  </Form-item>
                  <Form-item label="约课实退: " v-if="orderModal.ssl_status !== 1 && orderModal.reservation_type === 2">
                    <Input-number
                      :precision="2"
                      :active-change="false"
                      :min="0"
                      v-model="orderModal.put_pt_refund_amount"
                      style="width: 300px"
                    />
                  </Form-item>
                  <Form-item label="总计: ">
                    <span style="font-weight: bold; color: red">
                      {{ finalRefundPrice }}元
                    </span>
                  </Form-item>
                  <Form-item v-if="showOrderModal && orderModal.status != 2 && isGoPay"
                    :label="(orderModal.status == 0) ? '支付方式' : '退款方式'" prop="mix_pay_list"
                    :rules="{ required: !!Number(orderModal.price), message: '请选择支付方式!' }">
                    <pay-type-list v-model="orderModal.mix_pay_list" :amount="finalRefundPrice"
                      :describe="`订场[${currentStadium.name}]`" :userId="orderModal.userType == 1 ? orderModal.userId : -3"
                      :showCardPay="orderModal.userType == 1" :isRefundNeedSQB="orderModal.isRefundNeedSQB" isRefund
                      :isMaxAmount="orderModal.from == 2"></pay-type-list>
                  </Form-item>
                  <Form-item label="备注: ">
                    <Input v-model.trim="orderModal.remark" type="textarea" :maxlength="150" style="width: 300px"
                      :autosize="{ minRows: 4, maxRows: 8 }" />
                  </Form-item>
                  <Form-item>
                    <Button v-if="isNewRefund" type="success" @click="handleRefund">退款</Button>
                    <Button v-else type="success" @click="handleCancel">取消预订</Button>
                    <Button @click="showOrderModal = false;" style="margin-left: 40px">取消</Button>
                  </Form-item>
                </div>
              </div>
              <!-- <Form-item label="支付状态" prop="payment">{{ orderModal.payment }}</Form-item> -->
            </Form>
          </div>
          <div slot="footer"></div>
        </Modal>
      </TabPane>
      <TabPane name="2" label="占场记录">
        <occupy-logs :click-count="occupyClickCount" />
      </TabPane>
      <TabPane name="3" label="长租任务" v-if="longTermAuthorities.show">
        <LongTermRentalTasks :click-count="clickCount" />
      </TabPane>
    </Tabs>

    <Modal v-model="showReceipt" width="380">
      <div class="center" style="flex-direction: column;">
        <Icon type="ios-checkmark-circle-outline" size="32"></Icon>
        <h3>支付成功！</h3>
      </div>
      <div slot="footer" class="modal-buttons">
        <router-link style="margin-right:20px;color: #fff" target="_blank"
          :to="{ path: '/stadiumPrint', query: { user_id: this.currentStadium.user_id, order_sn: this.currentStadium.order_sn } }"><Button
            type="success">打印小票</Button></router-link>
        <Button @click="showReceipt = false">取消</Button>
      </div>
    </Modal>
    <mix-pay-show-modal v-model="showPayDetailModal" :list="curModalDetailPay" />

    <long-term-rental-modal :show.sync="showLongTermRental" :count="longTermRentalOrder?.long_order?.num || 0"
      :id="longTermRentalOrder?.long_order?.id || 0" @on-today-cancel="handleTodayCancel" :modify-one="longTermRentalLabel === '取消预订' ? authority.cancel === 1 : authority.refund === 1" :modify-all="longTermAuthorities.modifyAll" />

    <one-time-pay-modal :show.sync="showOneTimePay" :item="oneTimePayOrder" @on-all-cancel="handleAllCancel" />

    <Modal title="课程信息" v-model="showClassInfoModal" :mask-closable="false" width="380">
      <Form class="modal-form" :label-width="120">
        <FormItem v-for="(classInfo, index) in classInfoList" :key="index" :label="classInfo.label" >{{ classInfo.value }}</FormItem>
      </Form>
      <div slot="footer"></div>
    </Modal>

    <Modal
        v-model="noCancelVisible"
        title=""
        :mask-closable="false">
        <h2 style="text-align: center; margin-bottom: 40px;">场地关联的约课已签到或上课，无法取消订场</h2>
        <Form class="modal-form" :label-width="200">
          <Form-item label="会员名称">{{ noCancelOrder?.username }}</Form-item>
          <Form-item label="课程名称">{{ noCancelOrder?.card_name }}</Form-item>
          <Form-item label="教练名称">{{ noCancelOrder?.coach_name }}</Form-item>
          <Form-item label="上课时间">{{ noCancelOrder?.sign_date }}</Form-item>
        </Form>
        <div slot="footer" style="text-align: center; margin-bottom: 40px;">
            <Button @click="() => {
              this.noCancelVisible = false
              this.noCancelOrder = null
            }">关闭</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
import MixPayShowModal from "components/form/MixPayShowModal";
import PayTypeList from '@/components/form/PayTypeList.vue';
import { formatDate } from "utils";
import { throttle, cloneDeep } from 'lodash-es';
import TheBooking from "./components/TheBooking";
import ExportButton from 'components/form/ExportButton'
import LongTermRentalTasks from './components/LongTermRentalTasks'
import OccupyLogs from './components/OccupyLogs.vue'
import LongTermRentalModal from './components/LongTermRentalModal'
import OneTimePayModal from './components/OneTimePayModal'
import Big from 'big.js'

const MEASURE_TIME_LIST = [
  "08:00",
  "08:30",
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "12:00",
  "12:30",
  "13:00",
  "13:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30",
  "17:00",
  "17:30",
  "18:00",
  "18:30",
  "19:00",
  "19:30",
  "20:00",
  "20:30",
  "21:00",
  "21:30",
  "22:00",
  "22:30",
  "23:00"
];
const MEASURE_STEP_LIST = [
  0,
  "30分钟",
  "1个小时",
  "1.5个小时",
  "2个小时",
  "2.5个小时",
  "3个小时",
  "3.5个小时",
  "4个小时",
  "4.5个小时",
  "5个小时",
  "5.5个小时",
  "6个小时",
  "6.5个小时",
  "7个小时",
  "7.5个小时",
  "8个小时",
  "8.5个小时",
  "9个小时",
];
const MEASURE_DAY_LIST = {
  monday: "星期一",
  tuesday: "星期二",
  wednesday: "星期三",
  thursday: "星期四",
  friday: "星期五",
  saturday: "星期六",
  sunday: "星期天"
};

export default {
  name: "SpaceOrder",
  components: {
    TheBooking,
    MixPayShowModal,
    PayTypeList,
    ExportButton,
    LongTermRentalTasks,
    OccupyLogs,
    LongTermRentalModal,
    OneTimePayModal
  },
  data() {
    const resetOrder = async order => {
      let mix_pay_list = cloneDeep(order.mix_pay_list || []);
      if (order.type === 2) {
        const res = await this.$service.post('/Web/SpaceOrderLong/getLongOrderDetail', {
          id: order.long_order.id
        })
        mix_pay_list = cloneDeep(res?.data?.data?.new_pay_type || []);
      }

      // 退款类型中是否包收钱吧
      const isRefundNeedSQB = mix_pay_list.findIndex(item => item.pay_type === 20) > -1;

      this.isGoPay = false;
      this.orderModal = {
        orderId: order.id,
        orderCode: order.order_sn,
        stadium: order.space_name,
        stadiumCategory: order.space_type_name,
        reservationDay: order.date_desc,
        reservationTime: order.time_desc,
        refund_rate: order.refund_rate,
        refundable_amount: order.refundable_amount,
        payment: this.stateList[order.status].name,
        price: order.amount,
        userId: order.user_id,
        userType: order.user_type,
        username: order.username,
        phone: order.phone,
        mix_pay_list,
        from: order.from,
        sports_mark_order_id: order.sports_mark_order_id || '',
        status: order.status,
        type: Number(order.type || 1),
        batch_number: order.batch_number,
        number: order.number,
        remark: order.remark,
        // one time pay
        reservation_type: order.reservation_type,
        card_name: order.card_name,
        coach_name: order.coach_name,
        start_time_format: order.start_time_format,
        course_time: order.course_time,
        pt_refund_rate: 1,
        pt_refund_amount: 0,
        put_pt_refund_amount: 0,
        pt_income_amount: 0,
        should_refund_amount: order.should_refund_amount,
        put_refund_amount: order.should_refund_amount,
        pt_schedule_id: order.pt_schedule_id,
        ssl_status: order.ssl_status,
        isRefundNeedSQB: isRefundNeedSQB,
      };

    };
    const restDatePickerFlag = date => {
      const dateStr = formatDate(date, "yyyy-MM-dd");
      const flag = this.restDateList.find(item => formatDate(item.date, "yyyy-MM-dd") === dateStr);
      return flag;
    };
    return {
      activeIndex: '0',
      statusColorList: ["#ff6969", "#1bd4c9", "#3dabff"],
      statusNameList: ["未支付", "已支付", "已到场"],
      timeList: [
        "08:00",
        "09:00",
        "10:00",
        "11:00",
        "12:00",
        "13:00",
        "14:00",
        "15:00",
        "16:00",
        "17:00",
        "18:00",
        "19:00",
        "20:00",
        "21:00",
        "22:00",
        "23:00"
      ],
      timingBuddyStyle: "",
      timingLineStyle: "",
      // search
      stadiumCategoryList: [], // 场馆所有的场地类型
      tab1Post: {
        categoryId: "",
        searchDate: formatDate(new Date(), "yyyy-MM-dd")
      },
      tab2UpdateRange: [],
      tab2DateRange: [],
      tab2Post: {
        searchTxt: "",
        start_time: '',
        end_time: '',
        categoryId: "",
        statusId: "",
        type: '',
        user_type: ''
      },
      stateList: [
        { name: "未支付", code: 0 },
        { name: "已支付", code: 1 },
        { name: "已到场", code: 2 },
        { name: "已退款", code: 4 },
        { name: "已取消", code: 5 }
      ],
      // log
      logList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      columns: [
        {
          title: "订单编号",
          key: "order_sn",
          render: (h, params) => {
            let label = params.row.order_sn
            if (params.row.type == 2) {
              label += ' (长租)'
            }
            return h("div", label)
          }
        },
        {
          title: "操作时间",
          key: "create_time",
          // render: (h, params) => {
          //   return h("div", formatDate(new Date(Number(params.row.create_time) * 1000), 'yyyy-MM-dd HH:mm:ss'))
          // }
        },
        { title: "订场用户", key: "username" },
        {
          title: "预订时间",
          key: "start_time",
          render: (h, params) => {
            const dateString =
              params.row.date_desc + " " + params.row.time_desc;
            return h("div", dateString);
          }
        },
        { title: "场地", key: "space_name" },
        { title: "金额", key: "amount" },
        {
          title: '付款流水',
          key: 'mix_pay_type_name',
          width: 100,
          render: (h, params) => {
            return (
              <i-button
                type="text"
                onClick={() => {
                  this.curModalDetailPay = params.row.mix_pay_list.map(item => {
                    return {
                      ...item,
                      amount: item.max_amount
                    }
                  })
                  this.showPayDetailModal = true
                }}>
                <div class="pay-type-ellipsis">{params.row.mix_pay_type_name}</div>
              </i-button>
            )
          }
        },
        {
          title: "备注",
          key: "remark",
          render: (h, params) => {
            let shortRemark = params.row.remark || '';
            if (shortRemark.length > 15) {
              shortRemark = params.row.remark.substr(0, 20) + '...';
              return h('Tooltip', {
                props: {
                  content: params.row.remark,
                  maxWidth: 600,
                  transfer: true
                }
              }, shortRemark)
            } else {
              return h('div', shortRemark)
            }
          }
        },
        {
          title: "约课信息",
          key: "class_info",
          render: (h, params) => {
            if (params.row.ssl_status === 1) {
              return h("span", "-");
            } else {
              return h("a", {
                on: {
                  click: () => {
                    this.showClassInfoModal = true;
                    this.classInfoList = [];
                    this.$service.post('/Web/SpaceOrder/get_space_schedule_info', {
                      ssl_id: params.row.ssl_id
                    }).then(res => {
                      if (res.data.errorcode === 0) {
                        const resData = res.data.data;
                        if (resData) {
                          // 1.已约 2.已上课 3.未上课 4.已完成
                          // 1.排课 2.已上课 3.未上课 4.已完成 5.删除
                          const statusList = ['', '已约', '已上课', '未上课', '已完成', '已取消'];
                          const status = statusList[resData.pt_status] || '-';
  
                          this.classInfoList = [
                            {
                              label: '会员名称',
                              value: params.row.username || '-'
                            },
                            {
                              label: '课程名称',
                              value: resData.card_name || '-'
                            },
                            {
                              label: '教练名称',
                              value: resData.coach_name || '-'
                            },
                            {
                              label: '上课时间',
                              value: resData.start_date || '-'
                            },
                            {
                              label: '课程状态',
                              value: status
                            },
                          ]
  
                          if (resData.reservation_type == 2) {
                            this.classInfoList.push({
                              label: '支付金额',
                              value: resData.schedule_amount || '-'
                            })
                            this.classInfoList.push({
                              label: '业务单号',
                                value: resData.flow_sn || '-'
                            })
                          }
                        }
                      }
                    })
                  }
                }
              }, "详情");
            }
          }
        },
        {
          title: "操作人",
          key: "admin"
        },
        {
          title: "状态",
          key: "status",
          render: (h, params) => {
            //订单状态，0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款）
            const value = params.row.status;
            const stateItem = this.stateList.find(item => item.code == value);
            return h("div", stateItem.name);
          }
        },
        // {
        //   title: '付款方式',
        //   key: 'mix_pay_type_name',
        //   width: 100,
        //   render: (h, params) => {
        //     return (
        //       <i-button
        //         type="text"
        //         onClick={() =>{
        //         this.curModalDetailPay = params.row.mix_pay_list.map(item => {
        //           return {
        //             ...item,
        //             amount: item.max_amount
        //           }
        //         })
        //         this.showPayDetailModal = true
        //       }}>
        //         <div class="pay-type-ellipsis">{params.row.mix_pay_type_name}</div>
        //       </i-button>
        //     )
        //   }
        // },
        {
          title: "退款时间",
          key: "refund_time",
          render: (h, params) => {
            let dateString = "";
            if (params.row.refund_time) {
              const date = new Date(params.row.refund_time * 1000);
              dateString = formatDate(date, "yyyy-MM-dd HH:mm");
            }
            return <div>{dateString}</div>;
          }
        },
        {
          title: '退款流水',
          width: 100,
          key: 'mix_refund_type_name',
          render: (h, params) => {
            return (
              <i-button
                type="text"
                onClick={() => {
                  this.curModalDetailPay = params.row.mix_refund_list
                  this.showPayDetailModal = true
                }}>
                <div class="pay-type-ellipsis">{params.row.mix_refund_type_name}</div>
              </i-button>
            )
          }
        },
        {
          title: "退款金额",
          key: "refund_amount",
          render: (h, params) => {
            if (params.row.status === 4 || params.row.status === 5) {
              return <div>{params.row.refund_amount}</div>;
            } else {
              return <div>-</div>;
            }

          }
        },
        {
          title: "退款人",
          key: "refund_admin"
        },
        {
          title: "操作",
          key: "option",
          width: 150,
          render: (h, params) => {
            let rightLabel = "";
            let ctrl = false;
            if (params.row.status == 0) { // 只有会员端
              rightLabel = "";
            } else if (params.row.status == 1) {
              if (params.row.end_time * 1000 > Date.now()) {
                ctrl = this.authority.cancel === 1;
                rightLabel = "取消预订";
              } else {
                ctrl = this.authority.refund === 1;
                rightLabel = "退款";
              }
            }

            // const show = () => {
            //   resetOrder(params.row);
            //   if (rightLabel == "取消预订") {
            //     // this.handleCancel();
            //     this.isRefund = true;
            //     this.showOrderModal = true;
            //     this.isNewRefund = false;
            //     this.handlePayOrRefundClick();
            //   } else if (rightLabel == "退款") {
            //     this.isNewRefund = true;
            //     this.isRefund = true;
            //     this.showOrderModal = true;
            //     this.handlePayOrRefundClick();
            //   }
            // };

            const activeTag = (
              <div style="display: flex;flex-direction: row;justify-content: space-around;">
                <a
                  style="color:red;"
                  onClick={() => {
                    if (ctrl || this.longTermAuthorities.modifyAll) {
                      // show();
                      resetOrder(params.row);
                      this.handleBookingRefund(params.row, true, rightLabel)
                    }
                  }}
                >
                  {rightLabel}
                </a>
              </div>
            );
            const disabledTag = (
              <div style="display: flex;flex-direction: row;justify-content: space-around;">
                <a disabled style="color:gray;">
                  {rightLabel}
                </a>
              </div>
            );

            if (params.row.type === 2) {
              if (ctrl || this.longTermAuthorities.modifyAll) {
                return activeTag;
              } else {
                return disabledTag;
              }
            } else {
              if (ctrl) {
                return activeTag;
              } else {
                return disabledTag;
              }
            }

            // if (ctrl && showLink) {
            //   return (
            //     <div style="display: flex;flex-direction: row;justify-content: space-around;">
            //       <a
            //         style="color:red;"
            //         onClick={() => {
            //           if (ctrl) {
            //             // show();
            //             resetOrder(params.row);
            //             this.handleBookingRefund(params.row, true, rightLabel)
            //           }
            //         }}
            //       >
            //         {rightLabel}
            //       </a>
            //     </div>
            //   );
            // } else {
            //   return (
            //     <div style="display: flex;flex-direction: row;justify-content: space-around;">
            //       <a disabled style="color:gray;">
            //         {rightLabel}
            //       </a>
            //     </div>
            //   );
            // }
          }
        }
      ],
      // stadium
      stadiumList: [],
      // dialog
      showPayDetailModal: false,
      curModalDetailPay: [],
      planDialogFlag: false,
      planDialogTab: "monday",
      dateOptions: {
        disabledDate(date) {
          return (Date.now() - date) > 24 * 3600 * 1000 || restDatePickerFlag(date);
        }
      },
      planTimeSet: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: []
      },
      currentStadium: {
        id: "",
        name: "",
        minimum: 1,
        maximum: 8,
        between: "",
        isMember: 1,
        order_sn: '',
        phone: "",
        mix_pay_list: [],
        open_half: "",
        isHalf: "",
        hasWhole: true,
        amount: "",
        user_id: "",
        pay_order_ids: [],
        isDetail: false
      },
      selector: {
        id: '',
        start: -1,
        end: -1,
        half: 0
      },
      restDialogFlag: false,
      restDateList: [],
      detailFlag: false,
      search: "",
      showOrderModal: false,
      orderModal: {
        orderId: "",
        orderCode: "",
        stadium: "",
        stadiumCategory: "",
        reservationDay: "",
        reservationTime: "",
        payment: "",
        price: "",
        userId: "",
        userType: "",
        username: "",
        phone: "",
        mix_pay_list: [],
        sports_mark_order_id: '',
        status: "",
        type: "",
        // one time pay
        reservation_type: 1,
        card_name: '',
        coach_name: '',
        start_time_format: '',
        course_time: '',
        pt_refund_rate: 1,
        pt_refund_amount: 0,
        put_pt_refund_amount: 0,
        pt_income_amount: 0,
        should_refund_amount: 0,
        put_refund_amount: 0,
        pt_schedule_id: '',
        ssl_status: '',
        isRefundNeedSQB: false,
      },
      isGoPay: false,
      showReceipt: false,
      authority: {
        cancel: 0,
        refund: 0
      },
      isRefund: false,
      isNewRefund: false,
      occupyClickCount: 0,
      // long term rental
      showLongTermRental: false,
      longTermRentalOrder: null,
      longTermRentalLabel: '',
      clickCount: 0,
      // one time pay
      showClassInfoModal: false,
      classInfoList: [],
      showOneTimePay: false,
      oneTimePayOrder: null,
      oneTimePayLabel: '',
      noCancelVisible: false,
      noCancelOrder: null,
      longTermAuthorities: {
        show: false,
        limit: false,
        modifyAll: false
      }
    };
  },
  computed: {
    stadiumCategoryName() {
      let name = "";
      if (
        Array.isArray(this.stadiumCategoryList) &&
        this.stadiumCategoryList.length > 0 &&
        this.tab1Post.categoryId
      ) {
        name = this.stadiumCategoryList.find(
          cat => cat.id === this.tab1Post.categoryId
        ).name;
      }
      return name;
    },
    finalRefundPrice() {
      const stadium = new Big(this.orderModal.put_refund_amount || 0);
      const cpt = new Big(this.orderModal.put_pt_refund_amount || 0);
      return Number(stadium.add(cpt).toNumber().toFixed(2));
    }
  },
  methods: {
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
      if (index == 1) {
        this.currentPage = 1;
        this.getTab2List();
      } else if (index == 2) {
        this.occupyClickCount += 1;
      } else if (index == 3) {
        this.clickCount += 1;
      }
    },
    onDragonflyConfirm(info) {
      this.currentStadium.pay_order_ids = info.pay_order_ids
    },
    getOptionAuthority() {
      return this.$service.post("/Web/Space/checkRoleInfo").then(res => {
        if (res.data.errorcode == 0) {
          this.authority = res.data.data;
          // this.authority = {
          //   cancel: 1,
          //   refund: 0
          // }
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    handleSearch() {
      this.selector = {
        id: '',
        start: -1,
        end: -1
      };
      this.getTab1List()
    },
    handleSearchOrder() {
      this.currentPage = 1;
      this.getTab2List();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getTab2List();
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getTab2List();
    },

    handleRestAdd() {
      this.restDateList.push({
        // id: "",
        date: "",
        timeList: [],
        saveFlag: false
      });
    },
    handleRestDateChange(val, idx) {
      const date = new Date(val);
      const planList = this.getPlanList(date);
      let timeList = [];
      planList.forEach(plan => {
        timeList.push({
          id: '',
          checked: false,
          beg_time: plan[0],
          end_time: plan[1]
        });
      });
      this.restDateList[idx].timeList = timeList;
    },
    handleRestDelete(idx) {
      const rest = this.restDateList[idx];
      if (!rest.date) {
        this.restDateList.splice(idx, 1);
        return false;
      }
      const date = formatDate(rest.date, "yyyy-MM-dd");
      return this.$service.post("/Web/Space/delRest", {
        space_id: this.currentStadium.id,
        date
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.restDateList.splice(idx, 1);
          this.$Message.success(res.data.errormsg);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getPlanList(date) {
      let timeList = [];
      switch (date.getDay()) {
        case 1:
          timeList = this.planTimeSet.monday;
          break;
        case 2:
          timeList = this.planTimeSet.tuesday;
          break;
        case 3:
          timeList = this.planTimeSet.wednesday;
          break;
        case 4:
          timeList = this.planTimeSet.thursday;
          break;
        case 5:
          timeList = this.planTimeSet.friday;
          break;
        case 6:
          timeList = this.planTimeSet.saturday;
          break;
        case 0:
          timeList = this.planTimeSet.sunday;
          break;
        default:
          timeList = this.planTimeSet.monday;
          break;
      }
      return timeList;
    },
    setPlanSet(set) {
      if (set || Array.isArray(set)) {
        this.planTimeSet = set;
      } else {
        this.planTimeSet = {
          monday: [],
          tuesday: [],
          wednesday: [],
          thursday: [],
          friday: [],
          saturday: [],
          sunday: []
        };
      }
    },
    checkSaveFlag(idx) {
      const rest = this.restDateList[idx];
      const count = rest.timeList.filter(item => item.checked).length;
      if (count > 0) {
        rest.saveFlag = true;
      } else {
        rest.saveFlag = false;
      }
    },
    checkIndeterminate(idx) {
      let rest = this.restDateList[idx];
      const allCount = rest.timeList.length;
      const checkedCount = rest.timeList.filter(item => item.checked).length;
      if (allCount === checkedCount) {
        rest.checkAll = true;
        rest.indeterminate = false;
      } else if (checkedCount === 0) {
        rest.checkAll = false;
        rest.indeterminate = false;
      } else {
        rest.checkAll = false;
        rest.indeterminate = true;
      }
    },
    handleRestClick(space_id) {
      this.currentStadium.id = space_id;
      return this.$service
        .post("/Web/Space/restList", { space_id })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.setPlanSet(res.data.data.schedules);
            const restList = res.data.data.rest_list;
            this.restDateList = [];
            if (Array.isArray(restList) && restList.length > 0) {
              // sort by date
              restList.sort((a, b) => (new Date(a.beg_time) - new Date(b.beg_time)));
              // package by date
              restList.forEach(rest => {
                const date = new Date(rest.beg_time.split(" ")[0]);
                let restDate = this.restDateList.find(item => item.date.getTime() === date.getTime());
                if (!restDate) {
                  const planList = this.getPlanList(date);
                  let timeList = [];
                  planList.forEach(plan => {
                    timeList.push({
                      id: '',
                      checked: false,
                      beg_time: plan[0],
                      end_time: plan[1]
                    });
                  });
                  restDate = {
                    // id: "",
                    date,
                    timeList,
                    saveFlag: false
                  }
                  this.restDateList.push(restDate);
                }
                // add time from restList
                const beg_time = rest.beg_time.split(" ")[1];
                restDate.timeList.forEach(item => {
                  if (item.beg_time === beg_time) {
                    item.id = rest.id;
                    item.checked = true;
                  }
                });
              });
            }
            this.restDialogFlag = true;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleTagClick($index, $timeIndex) {
      let time = this.restDateList[$index].timeList[$timeIndex];
      time.checked = !time.checked;
      this.checkIndeterminate($index);
      this.checkSaveFlag($index);
    },
    handleRestSubmit(idx) {
      const rest = this.restDateList[idx];
      const date = formatDate(rest.date, "yyyy-MM-dd");
      const timeList = rest.timeList.filter(item => item.checked);
      return this.$service
        .post("/Web/Space/saveRest", {
          space_id: this.currentStadium.id,
          id: rest.id,
          date,
          rest: timeList
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.restDateList[idx].id = res.data.data.id;
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleRestHide(flag) {
      if (!flag) {
        this.getTab1List();
      }
    },
    handleRestAll(idx) {
      let rest = this.restDateList[idx];

      if (rest.indeterminate) {
        rest.checkAll = true;
        rest.indeterminate = false;
      } else {
        rest.checkAll = !rest.checkAll;
      }

      if (rest.checkAll) {
        rest.saveFlag = true;
        rest.timeList.forEach(item => {
          item.checked = true;
        });
      } else {
        rest.saveFlag = false;
        rest.timeList.forEach(item => {
          item.checked = false;
        });
      }
    },

    handleTab1DateChange(val) {
      this.tab1Post.searchDate = val;
      // this.getTab1List();
    },
    handleTab2UpdateChange(val) {
      this.tab2Post.update_start_time = val[0];
      this.tab2Post.update_end_time = val[1];
    },
    handleTab2DateChange(val) {
      this.tab2Post.start_time = val[0];
      this.tab2Post.end_time = val[1];
      // this.getTab1List();
    },
    // 获取场馆所有的场地类型
    getStadiumCategoryList() {
      return this.$service.post("/Web/Space/getTypes").then(res => {
        if (res.data.errorcode == 0) {
          this.stadiumCategoryList = res.data.data;
          if (
            Array.isArray(this.stadiumCategoryList) &&
            this.stadiumCategoryList.length > 0
          ) {
            this.tab1Post.categoryId = this.stadiumCategoryList[0].id;
          }
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getTab1List() {
      return this.$service
        .post("/Web/SpaceOrder/getList", {
          space_type_id: this.tab1Post.categoryId, // 场地类型id
          date_time: this.tab1Post.searchDate // 日期 如：2019-08-01
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            const list = res.data.data;
            list.forEach(stadium => {
              if (stadium.schedules.length > 0) {
                stadium.schedules = stadium.schedules.sort((arrA, arrB) => {
                  const targetA = arrA[0].split(':')
                  const targetB = arrB[0].split(':')
                  return targetA[0] === targetB[0] ? targetA[1] - targetB[1] : targetA[0] - targetB[0]
                })

                stadium.bussinessTimeStart =
                  MEASURE_TIME_LIST.findIndex(time => time === stadium.schedules[0][0]) +
                  4;
                stadium.bussinessTimeOver =
                  MEASURE_TIME_LIST.findIndex(time => time === stadium.schedules[0][1]) +
                  4;
              }

              stadium.processList = [];
              if (Array.isArray(stadium.space_list)) {
                stadium.space_list.forEach(reserve => {
                  let reserveStyle = { ...reserve };
                  reserveStyle.start =
                    MEASURE_TIME_LIST.findIndex(
                      time => time === reserve.start_time_format
                    ) + 4;
                  reserveStyle.over =
                    MEASURE_TIME_LIST.findIndex(
                      time => time === reserve.end_time_format
                    ) + 4;
                  reserveStyle.bgColor = this.statusColorList[reserve.status];
                  stadium.processList.push(reserveStyle);
                });
              }

              stadium.planList = [];
              if (Array.isArray(stadium.schedules)) {
                stadium.schedules.forEach(plan => {
                  let planStyle = {};
                  // 全场
                  planStyle.light = false;
                  planStyle.disableLight = false;
                  // 上半场
                  planStyle.light1 = false;
                  planStyle.disableLight1 = false;
                  // 下半场
                  planStyle.light2 = false;
                  planStyle.disableLight2 = false;

                  planStyle.start =
                    MEASURE_TIME_LIST.findIndex(time => time === plan[0]) + 4;
                  planStyle.over =
                    MEASURE_TIME_LIST.findIndex(time => time === plan[1]) + 4;

                  if (planStyle.start !== 3 && planStyle.over !== 3) {
                    stadium.planList.push(planStyle);
                  }
                });
              }

              stadium.disableList = [];
              if (Array.isArray(stadium.disable_space_list)) {
                stadium.disable_space_list.forEach(dis => {
                  let disStyle = {};
                  disStyle.start =
                    MEASURE_TIME_LIST.findIndex(time => time === dis[0]) + 4;
                  disStyle.over =
                    MEASURE_TIME_LIST.findIndex(time => time === dis[1]) + 4;
                  stadium.disableList.push(disStyle);
                });
              }
            });
            this.stadiumList = list;

            const count = list.length;
            const today = new Date();
            let currentTimeIdx = (today.getHours() - 8) * 2 + 4;
            if (today.getMinutes() > 30) {
              currentTimeIdx++;
            }
            this.timingLineStyle = `grid-area: 1 / ${currentTimeIdx} / span ${count} / span 1;`;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getTab2List() {
      return this.$service
        .post("/Web/SpaceOrder/getOrderList", {
          search: this.tab2Post.searchTxt,
          update_start_time: this.tab2Post.update_start_time, // 操作开始时间 2018-01-01
          update_end_time: this.tab2Post.update_end_time,
          start_time: this.tab2Post.start_time, // 开始时间 2018-01-01
          end_time: this.tab2Post.end_time,
          space_type_id: this.tab2Post.categoryId, // 场地类型
          status: this.tab2Post.statusId, // 状态  0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款）
          type: this.tab2Post.type,
          user_type: this.tab2Post.user_type,
          page_no: this.currentPage,
          page_size: this.pageSize
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.total = parseInt(res.data.data.count);
            this.logList = [];
            if (Array.isArray(res.data.data.list)) {
              this.logList = res.data.data.list;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getList() {
      this.getTab1List();
      this.getTab2List();
    },
    // 场地管理-删除场地
    handleStadiumDelete(space_id) {
      return this.$service
        .post("/Web/Space/delSpace", {
          space_id // 场地id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.info({
              content: res.data.errormsg,
              duration: 10
            });
            this.getTab1List();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handleTimeCheck(day) {
      let flag = true;
      const customList = this.planTimeSet[day];
      if (Array.isArray(customList) && customList.length > 0) {
        let checkList = [];
        for (let i = 0; i < 32; i++) {
          checkList.push(false);
        }

        customList.forEach(customTime => {
          // between minimum and maximum
          const begin = MEASURE_TIME_LIST.findIndex(
            time => time === customTime[0]
          );
          const end = MEASURE_TIME_LIST.findIndex(
            time => time === customTime[1]
          );
          const between = end - begin;
          if (between < this.currentStadium.minimum) {
            this.$Message.error(
              `${MEASURE_DAY_LIST[day]} 单次订场时长不能小于${MEASURE_STEP_LIST[this.currentStadium.minimum]
              }！`
            );
            flag = false;
            return false;
          } else if (between > this.currentStadium.maximum) {
            this.$Message.error(
              `${MEASURE_DAY_LIST[day]} 单次订场时长不能大于 ${MEASURE_STEP_LIST[this.currentStadium.maximum]
              } 个小时！`
            );
            flag = false;
            return false;
          }

          // inside checkList
          for (let i = begin; i < end; i++) {
            if (checkList[i]) {
              this.$Message.error(
                `${MEASURE_DAY_LIST[day]} ${MEASURE_TIME_LIST[i]} 重复排场！`
              );
              flag = false;
              return false;
            } else {
              checkList[i] = true;
            }
          }
        });
      } else {
        this.$Message.error("未排场场地无法提供用户预订！");
        flag = false;
      }
      return flag;
    },
    handlePlanClick(space_id) {
      this.planDialogTab = "monday";
      this.currentStadium.id = space_id;
      const stadium = this.stadiumList.find(item => item.space_id === space_id);
      this.currentStadium.minimum = parseFloat(stadium.booking_time_min) * 2;
      this.currentStadium.maximum = parseFloat(stadium.booking_time_max) * 2;

      return this.$service
        .post("/Web/Space/getSchedulesInfo", { space_id })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.setPlanSet(res.data.data);
            this.planDialogFlag = true;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handlePlanSubmit() {
      const { planTimeSet } = this;
      let flag = true;
      for (const key in planTimeSet) {
        if (!this.handleTimeCheck(key)) {
          flag = false;
          return false;
        }
      }

      if (flag) {
        const schedules = {};
        Object.keys(planTimeSet).forEach(k => {
          schedules[k] = this.planTimeSet[k].sort((arrA, arrB) => {
            const targetA = arrA[0].split(':')
            const targetB = arrB[0].split(':')
            return targetA[0] === targetB[0] ? targetA[1] - targetB[1] : targetA[0] - targetB[0]
          })
        })

        this.$service
          .post("/Web/Space/saveSchedules", {
            space_id: this.currentStadium.id,
            schedules,
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.planDialogFlag = false;
              this.getTab1List();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      }
    },
    getStadiumCategory(halfFlag) {
      // 0 全场，1上半场，2下半场
      let halfLightName = 'light'
      let halfDisableName = 'disableLight'
      if (halfFlag === 1) {
        halfLightName = 'light1'
        halfDisableName = 'disableLight1'
      } else if (halfFlag === 2) {
        halfLightName = 'light2'
        halfDisableName = 'disableLight2'
      }
      return { halfLightName, halfDisableName }
    },
    handleLightMove: throttle(function (id, idx, halfFlag) {
      const { halfLightName, halfDisableName } = this.getStadiumCategory(halfFlag);
      const { minimum, maximum } = this.currentStadium;
      const stadium = this.stadiumList.find(item => item.space_id === id);
      const start = this.selector.start;
      const end = idx;

      let flag = false;
      stadium.planList.forEach(plan => {
        if (plan[halfLightName]) {
          flag = true;
          return false;
        }
      });

      if (flag) { // 同一个馆子选择了开始
        let unavailableRest = 100;
        let unavailableReserve = 100;
        stadium.planList.forEach((plan, planListIndex) => {
          // const index = planListIndex;
          const index = planListIndex * minimum
          if (index === start) { // 起始位置一直亮
            // console.log(1, 'index === start', { planListIndex, index, start, end });
            plan[halfLightName] = true;
          } else if (start < index && index <= end * minimum) { // 点亮至移动到的位置
            // console.log(1, 'start < index && index <= end', { planListIndex, index, start, end });
            plan[halfLightName] = true;
          } else {
            // console.log(1, 'else', { planListIndex, index, start, end });
            plan[halfLightName] = false;
          }

          // 移动到不能预订的位置，给个灰灰
          plan[halfDisableName] = false;
          const step = planListIndex - start
          const maxStep = maximum / minimum;
          // console.log(step, maxStep);
          if (step >= maxStep) {
            // console.log(2, 'step >= maxStep', { planListIndex, index, step, maxStep, });
            plan[halfDisableName] = true;
          } else if (step < 0) {
            // console.log(2, 'step < 0', { planListIndex, index, step, maxStep, });
            plan[halfDisableName] = true;
          }

          // 遇到休息和已预订的，后面都给个灰灰
          stadium.disableList.forEach(item => {
            const itemIdx = item.start - stadium.bussinessTimeStart;
            if (itemIdx > start * minimum && itemIdx < end * minimum) {
              if (itemIdx < unavailableRest) {
                unavailableRest = itemIdx;
              }
            }
          });
          stadium.processList.forEach((item, processIndex) => {
            const itemIdx = item.start - stadium.bussinessTimeStart;
            // if (processIndex === 0) {
            //   console.log('processList', {
            //     processIndex,
            //     itemIdx,
            //     start,
            //     end,
            //     unavailableReserve
            //   });
            // }
            // position 0 全场 1上半场 2 下半场
            if (halfFlag == item.position && itemIdx > start * minimum && itemIdx < end * minimum) {
              if (unavailableReserve === 100) {
                /* console.log('processList---1', {
                  processIndex,
                  itemIdx,
                  start,
                  end,
                  unavailableReserve
                }); */
              }
              if (itemIdx < unavailableReserve) {
                unavailableReserve = itemIdx;
                // console.log('processList---2', { processIndex, unavailableReserve });
              }
            }
            /* if (itemIdx > start && itemIdx < end * this.currentStadium.minimum) {
              if (itemIdx < unavailableReserve) {
                unavailableReserve = itemIdx;
              }
            } */
          });

        });
        // console.log('-----');
        stadium.planList.forEach((plan, planListIndex) => {
          const index = planListIndex * minimum
          if (index > unavailableRest) {
            /* console.log('planList-unavailableRest', {
              index,
              unavailableRest,
            }); */
            plan[halfDisableName] = true;
          }
          if (index > unavailableReserve) {
            /* console.log('planList-unavailableReserve', {
              index,
              unavailableReserve,
            }); */
            plan[halfDisableName] = true;
          }
        });
      }
    }, 100),
    lightTheItem(id, idx, halfLightName) {
      const stadium = this.stadiumList.find(item => item.space_id === id);
      stadium.planList[idx][halfLightName] = true;
    },
    darkAllItem(id) {
      const stadium = this.stadiumList.find(item => item.space_id === id);
      stadium.planList.forEach(plan => {
        plan.light = false;
        plan.disableLight = false;
        plan.light1 = false;
        plan.disableLight1 = false;
        plan.light2 = false;
        plan.disableLight2 = false;
      });
    },
    // 开场收银-根据场地类型和时间和场地和场地类型计算金额
    getPrice() {
      return this.$service
        .post("/Web/SpaceOrder/getAmount", {
          date_time: this.tab1Post.searchDate, // 2019-08-1
          beg_time: this.currentStadium.between.split("~")[0], // 08:00
          end_time: this.currentStadium.between.split("~")[1],
          space_id: this.currentStadium.id, // 场地id
          is_half: this.currentStadium.isHalf, // 1-半场 0-全场
          pay_type: this.currentStadium.mix_pay_list
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.currentStadium.amount = res.data.data.price;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    handlePayOrRefundClick() {
      this.isGoPay = true;
    },
    longTermRentalCancel() {
      // the amount equal new_pay_type amount sum
      let amount = 0
      if (Array.isArray(this.orderModal.mix_pay_list) && this.orderModal.mix_pay_list.length > 0) {
        amount = this.orderModal.mix_pay_list.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber()
      }
      return this.$service
        .post("/Web/SpaceOrderLong/refundSpaceLongItems", {
          user_id: this.orderModal.userId,
          batch_number: this.orderModal.batch_number,
          ids: this.orderModal.orderId,
          // number: this.orderModal.number,
          number: 1,
          amount,
          new_pay_type: this.orderModal.mix_pay_list,
          scene: this.longTermRentalLabel === '取消预订' ? 2 : 3,
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.showOrderModal = false;
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    getContent() {
      let price = 0;
      if (Array.isArray(this.orderModal.mix_pay_list) && this.orderModal.mix_pay_list.length > 0) {
        price = this.orderModal.mix_pay_list.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber()
      }
      return `请确认退款金额: ${price} 元`;
    },
    handleCancel() {
      const overPrice = this.orderModal.mix_pay_list.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
      if (this.finalRefundPrice !== overPrice) {
        this.$Message.error('支付金额不等于总计金额!')
        return
      }
      const content = this.getContent();
      this.$refs.orderModal.validate(valid => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            // content: '请核对退款金额和退款方式，一旦确认退款无法再修改数据!',
            content,
            onOk: () => {
              if (this.orderModal.type === 2) {
                return this.longTermRentalCancel()
              } else {
                // 仅退定场也走 MergeRefund 接口吧
                let url = '/Web/SpaceOrder/MergeRefund';
                let schedule_amount = ''
                let pt_schedule_id = ''
                
                if (this.orderModal.ssl_status === 0) {
                  schedule_amount = this.orderModal.put_pt_refund_amount
                  pt_schedule_id = this.orderModal.pt_schedule_id
                }

                this.$service
                  .post(url, {
                    space_order_id: this.orderModal.orderId,
                    new_pay_type: this.orderModal.mix_pay_list,
                    sports_mark_order_id: this.orderModal.sports_mark_order_id, // 与会员端约球关联的
                    refund_amount: this.finalRefundPrice,
                    book_amount: this.orderModal.put_refund_amount,
                    schedule_amount,
                    pt_schedule_id,
                    remark: this.orderModal.remark,
                  })
                  .then(res => {
                    if (res.data.errorcode == 0) {
                      this.$Message.success(res.data.errormsg);
                      this.showOrderModal = false;
                      this.getList();
                    } else {
                      this.$Message.error(res.data.errormsg);
                    }
                  });
              }
            }
          })
        }
      })

    },
    handleInGym() {
      return this.$service
        .post("/Web/SpaceOrder/userSign", {
          space_order_id: this.orderModal.orderId
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.showOrderModal = false;
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    // 订场退款
    handleRefund() {
      const overPrice = this.orderModal.mix_pay_list.map(item => new Big(item.amount || 0)).reduce((a, b) => a.plus(b), new Big(0)).toNumber();
      if (this.finalRefundPrice !== overPrice) {
        this.$Message.error('支付金额不等于总计金额!')
        return
      }
      const content = this.getContent();
      this.$Modal.confirm({
        title: '提示',
        // content: '请核对退款金额和退款方式，一旦确认退款无法再修改数据',
        content,
        onOk: () => {
          this.$refs.orderModal.validate(valid => {
            if (valid) {
              if (this.orderModal.type === 2) {
                return this.longTermRentalCancel()
              } else {
                // 仅退定场也走 MergeRefund 接口吧
                let url = '/Web/SpaceOrder/postRefundMix';
                let schedule_amount = ''
                let pt_schedule_id = ''
                
                if (this.orderModal.ssl_status === 0) {
                  schedule_amount = this.orderModal.put_pt_refund_amount
                  pt_schedule_id = this.orderModal.pt_schedule_id
                }
                
                this.$service
                  .post(url, {
                    space_order_id: this.orderModal.orderId, // 订场记录id
                    new_pay_type: this.orderModal.mix_pay_list, // 退款类型
                    sports_mark_order_id: this.orderModal.sports_mark_order_id, // 与会员端约球关联的
                    refund_amount: this.finalRefundPrice,
                    book_amount: this.orderModal.put_refund_amount,
                    schedule_amount,
                    pt_schedule_id,
                  })
                  .then(res => {
                    if (res.data.errorcode == 0) {
                      this.$Message.success(res.data.errormsg);
                      this.showOrderModal = false;
                      this.getList();
                    } else {
                      this.$Message.error(res.data.errormsg);
                    }
                  });
              }
            }
          });
        }
      })
    },
    // 订场支付
    handlePayModal() {
      return this.$service
        .post("/Web/SpaceOrder/postPay", {
          space_order_id: this.orderModal.orderId,
          pay_type: this.orderModal.mix_pay_list
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.showOrderModal = false;
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },

    // print ticket
    eventFun(event) {
      if (event.key == 'receipt') {
        localStorage.removeItem('receipt');
        this.showReceipt = false;
      }
    },
    beforeDestroy() {
      window.removeEventListener('storage', this.eventFun);
    },
    activated() {
      window.addEventListener('storage', this.eventFun);
    },
    deactivated() {
      window.removeEventListener('storage', this.eventFun);
    },

    /**
     * 日期加上天数后的新日期.
     * @param  {Datetime} date 基准日期
     * @param  {number} days 天数
     */
    addDays(date, days) {
      let nd = new Date(date)
      nd = nd.valueOf()
      nd = nd + days * 24 * 60 * 60 * 1000
      nd = new Date(nd)
      let y = nd.getFullYear()
      let m = nd.getMonth() + 1
      let d = nd.getDate()
      if (m <= 9) m = '0' + m
      if (d <= 9) d = '0' + d
      let cdate = y + '-' + m + '-' + d
      return cdate
    },

    // long term rental
    handleBookingRefund(order, checkType = true, rightLabel) {
      // 单节付费, ssl_status = 0 约课且订场, ssl_status = 1 已经取消约课, ssl_status = 2 已到场上课
      if (order.ssl_status === 0 && checkType) {
        this.oneTimePayOrder = order
        this.oneTimePayLabel = rightLabel
        this.showOneTimePay = true
        return false
      } else if (order.ssl_status === 2 && checkType) {
        // this.$Message.error('场地关联的约课已签到或上课，无法取消订场')
        this.$service.post('/Web/SpaceOrder/get_space_schedule_info', {
          ssl_id: order.ssl_id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.noCancelOrder = res.data.data
            this.noCancelOrder.username = order.username
            this.noCancelVisible = true
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        return false
      }

      if (order.reservation_type === 2) {
        this.orderModal.mix_pay_list = []
        this.$service.post('/Web/SpaceOrder/scheduleAndBookRefundInfo', {
          space_order_id: this.orderModal.orderId,
          user_id: order.user_id,
        }).then(res => {
          if (res.data.errorcode == 0) {
            const info = res.data.data.pt_refund_info;
            this.orderModal.pt_schedule_id = info.id;
            this.orderModal.course_time = info.course_time;
            this.orderModal.pt_refund_rate = info.refund_rate;
            this.orderModal.pt_refund_amount = info.refund_amount || 0;
            this.orderModal.put_pt_refund_amount = info.refund_amount || 0;
            this.orderModal.pt_income_amount = info.income_amount;
            // this.orderModal.new_pay_type = info.pay_type;
            // this.orderModal.mix_pay_list = info.pay_type;
            this.orderModal.mix_pay_list = res.data.data?.new_pay_type;
            this.orderModal.card_name = info.class_name || info.card_name;
            this.orderModal.coach_name = info.coach_name;
            this.orderModal.should_refund_amount = res.data.data?.space_refund_info?.should_refund_amount || 0;
            this.orderModal.put_refund_amount = res.data.data?.space_refund_info?.should_refund_amount || 0;
            // this.orderModal.pt_refund_amount = res.data.data?.pt_refund_info?.refund_amount || 0;
            // this.orderModal.put_pt_refund_amount = res.data.data?.pt_refund_info?.refund_amount || 0;

            // 退款类型中是否包收钱吧
            this.orderModal.isRefundNeedSQB = res.data.data?.new_pay_type.findIndex(item => item.pay_type === 20) > -1;
          }
        })
      }

      if (order.type === 2 && checkType) {
        this.longTermRentalOrder = order
        this.longTermRentalLabel = rightLabel
        this.showLongTermRental = true
        return false
      }

      if (rightLabel == "取消预订") {
        // this.handleCancel();
        this.isRefund = true;
        this.showOrderModal = true;
        this.isNewRefund = false;
        this.handlePayOrRefundClick();
      } else if (rightLabel == "退款") {
        this.isNewRefund = true;
        this.isRefund = true;
        this.showOrderModal = true;
        this.handlePayOrRefundClick();
      }
    },
    handleTodayCancel() {
      this.showLongTermRental = false
      this.handleBookingRefund(this.longTermRentalOrder, false, this.longTermRentalLabel)
    },
    handleAllCancel() {
      this.showOneTimePay = false
      this.handleBookingRefund(this.oneTimePayOrder, false, this.oneTimePayLabel)
    }
  },
  async created() {
    // 长租功能是否可用检查
    const res = await this.$service.get('/Web/SpaceOrderLong/checkAuthLongOrderAndRefund')
    if (res.data.errorcode == 0) {
      const auth = res.data.data
      this.longTermAuthorities = {
        show: auth.long_order,
        limit: auth.long_limit,
        modifyAll: auth.long_refund
      }
      // this.longTermAuthorities = {
      //   show: true,
      //   limit: true,
      //   modifyAll: false
      // }
    } else {
      this.$Message.error(res.data.errormsg)
    }
    // 前往"长租任务"查看
    if (this.$route.query.active) {
      this.activeIndex = this.$route.query.active
      const url = `${window.location.origin}${window.location.pathname}`
      window.history.replaceState({}, '', url);
      this.clickCount += 1
    }
    // 新增默认订场记录-预订时间为当前时间往前一周
    let now = new Date()
    let beforeTime = this.addDays(now, -6)
    let nowTime = formatDate(now, 'yyyy-MM-dd')
    let before = new Date(beforeTime)
    this.tab2UpdateRange = [before, now]
    this.tab2Post.update_start_time = beforeTime
    this.tab2Post.update_end_time = nowTime

    this.getOptionAuthority();
    // this.getStadiumCategoryList().then(this.getList);
    this.getStadiumCategoryList()
    // this.getTab2List();
  },
  // mounted() {
  //   const height =
  //     document.getElementsByClassName("container")[0].clientHeight - 270;
  //   this.timingBuddyStyle = `height: ${height}px;`;
  // }
};
</script>
<style lang="less">
.pay-type-ellipsis {
  width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>

<style lang="less" scoped>
@regionWidth: 128px;
@regionHeight: 78px;
@prefixWidth: 28px;
@suffixWidth: 28px;

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.container {
  padding: 20px;
  height: 100%;

  .stadium-search {
    height: 60px;
    .center;

    .search-item {
      width: 200px;
      margin-right: 20px;
    }
  }

  .stadium-legend {
    margin-bottom: 10px;

    .legend-box {
      display: flex;
      flex-direction: row;

      .legend {
        display: flex;
        flex-direction: row;
        align-items: center;

        .legend-color {
          width: 13px;
          height: 13px;
          overflow: hidden;
          border-radius: 50%;
          margin-right: 14px;
        }

        .legend-label {
          font-size: 14px;
          color: #999999;
          margin-right: 20px;
        }
      }
    }
  }

  .stadium-button {
    height: 40px;
    .center;
    justify-content: flex-start;
  }

  .timing-header-16 {
    background-color: white;
    display: grid;
    grid-template-columns: @regionWidth @prefixWidth repeat(16, 1fr) @suffixWidth;
    grid-template-rows: @regionHeight;
    text-align: center;
    color: teal;
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;

    .header-top {
      font-size: 18px;
      color: #999999;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      width: @regionWidth;
      height: @regionHeight;
      border-right: 1px solid #dddddd;

      .region {
        width: 112px;
        text-align: left;
        margin-left: 14px;
      }

      .line {
        width: 64px;
        height: 0;
        border: 1px solid #e1e1e1;
        transform: rotate(45deg);
      }

      .date {
        width: 112px;
        text-align: right;
        margin-right: 14px;
      }
    }

    .timing {
      .scratch {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .small-line {
          width: 0;
          height: 14px;
          border: 1px solid #dddddd;
        }

        .big-line {
          width: 0;
          height: 20px;
          border: 1px solid #dddddd;
        }
      }

      .hour {
        height: 58px;
        font-size: 18px;
        color: #999999;
        .center;
      }
    }
  }

  .timing-buddy-32 {
    overflow-y: scroll;
    background-color: white;
    display: grid;
    grid-template-columns: @regionWidth @prefixWidth repeat(32, 1fr) @suffixWidth;
    grid-template-rows: repeat(6, 120px);
    text-align: center;
    color: teal;

    .timing-line {
      z-index: 887;
      margin-top: -@regionHeight;
      width: 0;

      .line {
        width: 0;
        height: 100%;
        border-left: 1px solid #17bf6a;
      }
    }

    .light-box {
      background-color: #e4f0fa;
      // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .dark-box {
      background-color: #eeeeee;
      // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      cursor: not-allowed !important;
    }

    .stadium-minimum {
      border: 1px solid #dddee1;
      margin: 16px 0;
      cursor: pointer;
    }

    .stadium-minimum1 {
      border: 1px solid #dddee1;
      margin: 16px 0 60px 0;
      cursor: pointer;
    }

    .stadium-minimum2 {
      border: 1px solid #dddee1;
      margin: 62px 0 16px 0;
      cursor: pointer;
    }

    .region {
      border-right: 1px solid #dddddd;
      border-bottom: 1px solid #dddddd;
      width: @regionWidth;
      height: 120px;

      .label {
        display: block;
        height: 100%;
        width: 100%;
        .center;
      }

      .panel {
        display: none;
        height: 100%;
        width: 100%;

        .panel-row {
          height: 50%;
          display: flex;
          justify-content: space-around;
          align-items: center;

          i {
            color: black;
            cursor: pointer;
          }
        }
      }
    }

    .region:hover {
      .label {
        display: none;
      }

      .panel {
        display: block;
        background-color: #fbfbfb;
      }
    }

    .disable-time {
      background-color: #eeeeee;
      margin: 16px 0 16px 0;
    }

    .disable-time1 {
      background-color: #eeeeee;
      margin: 16px 0 60px 0;
    }

    .disable-time2 {
      background-color: #eeeeee;
      margin: 62px 0 16px 0;
    }

    .process {
      border-left: 2px solid black;
      margin: 16px 0;
      cursor: pointer;
    }

    .process1 {
      border-left: 2px solid black;
      margin: 16px 0 62px 0;
      cursor: pointer;
    }

    .process2 {
      border-left: 2px solid black;
      margin: 62px 0 16px 0;
      cursor: pointer;
    }
  }

  .box-head {
    // border: 1px solid #dddee1;
    border: none;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-item {
        width: 200px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    // border-left: 1px solid #dddee1;
    border: none;
    height: calc(100vh - 400px);
    overflow-y: auto;
  }

  .box-foot {
    // border: 1px solid #dddee1;
    // border-top: none;
    border: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.rest-time-box {
  flex-direction: column;
  .center;

  .rest-time-line {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .rest-tag-box {
      width: 470px;
    }
  }

  .rest-add-btn {
    margin-top: 10px;
    align-self: baseline;
  }
}

/* Sliding entrances */
@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.animate__slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown;
  animation-duration: 0.5s;
}

// tabs inside dialog
.demo-tabs-style1>.ivu-tabs-card>.ivu-tabs-content {
  height: 120px;
  margin-top: -16px;
}

.demo-tabs-style1>.ivu-tabs-card>.ivu-tabs-content>.ivu-tabs-tabpane {
  background: #fff;
  padding: 16px;
}

.demo-tabs-style1>.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab {
  border-color: transparent;
}

.demo-tabs-style1>.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active {
  border-color: #fff;
}

.dialog-submit-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 0 0 35px 0;
  width: 100%;
}

.modal-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;

  .modal-divider {
    width: 50%;
  }
}

.form-no-bottom /deep/ .ivu-form-item {
  margin-bottom: 0;
}
</style>
