<template>
  <div class="box">
    <Row class="box-head">
        <Col offset="1" span="22" class="head-option">
            <Input style="width: 180px" v-model="username" class="option-select" placeholder="会员" />
            <Input style="width: 180px" v-model="stadium" class="option-select" placeholder="场地名称" />
            <Select v-model="stadiumCategoryId" style="width:200px;margin-right:20px;" placeholder="场地类型" clearable>
                <Option v-for="item in stadiumCategoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
            <Select v-model="whatDay" style="width:200px;margin-right:20px;" placeholder="星期" clearable>
                <Option v-for="(item, index) in dayOfWeek" :value="index+1" :key="index">{{ item }}</Option>
            </Select>
            <Button type="success" @click="handleSearch">搜索</Button>
        </Col>
    </Row>
    <Row class="box-body table-wrap">
      <Col span="24">
      <Table ref="table" stripe :columns="columns" :data="list" disabled-hover></Table>
      </Col>
    </Row>
    <Row class="box-foot">
      <Col offset="1" span="22" class="foot-option">
      <div class="option-ctrl">
        <Button type="success" @click="handleShowModal" style="margin-right: 30px;">添加固定场</Button>
      </div>
      <Page @on-change="handlePage" :total="total" :current="currentPage" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
      </Col>
    </Row>

    <Modal v-model="showSpecialDate" style="width:420px">
        <div class="modal-header">
            <h3>设置特殊时段价格</h3>
        </div>
        <div class="modal-buddy" style="margin-top:20px;">
            <Form ref="stadiumModal" :model="stadiumModal" class="modal-form" :label-width="80">
                <Form-item label="场地类型" prop="stadiumCategoryId">
                    <Select v-model="stadiumModal.stadiumCategoryId" style="width:200px;margin-right:20px;" placeholder="场地类型" clearable>
                        <Option v-for="item in stadiumCategoryList" :value="item.id" :key="item.id">{{ item.name }}</Option>
                    </Select>
                </Form-item>
                <Form-item label="" prop="dayInWeek">
                    <CheckboxGroup v-model="stadiumModal.dayInWeek">
                        <Checkbox :label="index + 1" v-for="(day, index) in dayOfWeek" :key="index">{{day}}</Checkbox>
                    </CheckboxGroup>
                </Form-item>
                <Form-item label="营业时间" prop="modalTime">
                    <TimePicker :value="modalTime" type="timerange" :steps="[1, 30]" :disabled-hours="[0,1,2,3,4,5,6,7]" :editable="false" format="HH:mm" @on-change="handleTimeChange" placement="bottom-start" placeholder="请选择时间段..." style="width: 200px;"></TimePicker>
                </Form-item>
                <Form-item label="周期" prop="lifecircle">
                    <Select v-model="stadiumModal.lifecircle" style="width:200px;margin-right:20px;" placeholder="周期" clearable>
                        <Option v-for="item in [1,2,3,4]" :value="item" :key="item">{{ item }}周</Option>
                    </Select>
                </Form-item>
            </Form>
        </div>
        <div slot="footer" class="modal-buttons">
            <Button type="success" @click="handleSaveModal">确定</Button>
            <Button @click="showSpecialDate = false">取消</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        columns: [
            { title: '场地名称', key: 'space_name' },
            { title: '场地类型', key: 'space_type_name' },
            { title: '会员', key: 'status' },
            { title: '星期', key: 'online_reserve' },
            { title: '时间', key: 'online_reserve' },
            { title: '费用', key: 'online_reserve' },
            {
            title: '操作',
            key: 'option',
            width: 150,
            render: (h, params) => {
                return (
                    <div style="display: flex;flex-direction: row;justify-content: space-around;">
                        <a onClick={() => {
                            this.deleteStadiumById(params.row.space_id);
                        }}>取消固定</a>
                    </div>
                );
                }
            }
        ],
        list: [],
        username: '',
        stadium: '',
        whatDay: '',
        stadiumCategoryList: [],
        stadiumCategoryId: '',
        dayOfWeek: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        showSpecialDate: false,
        stadiumModal: {
            stadiumCategoryId: '',
            dayInWeek: [],
            modalTime: [],
            lifecircle: ''
        },
        isHalf: 0,
        dayInWeek: [],
        startTime: '',
        overTime: '',
        price: 0,
        modalTime: [],
        modalId: ''
    };
  },
  methods: {
    handleSearch() {
      this.currentPage = 1;
      this.getList();
    },
    handlePage(val) {
      this.currentPage = val;
      this.getList();
    },
    pageSizeChanged(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.getList();
    },
    getList() {
      return this.$service.post('/Web/SpaceOrderLong/getList', {
            username: this.username,
            space_name: this.stadium,
            space_type_id: this.stadiumCategoryId,
            weeks: this.whatDay,
            page_no: this.currentPage,
            page_size: this.pageSize,
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.total = parseInt(res.data.data.count);
            this.list = [];
            if (Array.isArray(res.data.data.list)) {
              this.list = res.data.data.list;
            }
          } else {this.$Message.error(res.data.errormsg);}
        });
    },
    getStadiumCategoryList() {
      return this.$service.post('/Web/Space/getTypes').then(res => {
        if (res.data.errorcode == 0) {
          this.stadiumCategoryList = res.data.data;
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    deleteStadiumById(space_id) {
      return this.$service.post('/Web/Space/delSpace', {
        space_id
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.info(res.data.errormsg);
          this.getList();
        } else {this.$Message.error(res.data.errormsg);}
      });
    },
    handleShowModal() {
        this.showSpecialDate = true;
        this.isHalf = 0;
        this.dayInWeek = [];
        this.startTime = '';
        this.overTime = '';
        this.price = 0;
        this.modalTime = [];
        this.modalId = '';
    },
    handleTimeChange(arr) {
        if (Array.isArray(arr)) {
            this.startTime = arr[0];
            this.overTime = arr[1];
        }
    },
  },
  mounted() {
    this.getList();
    this.getStadiumCategoryList();
  }
};
</script>

<style lang="less">
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>
