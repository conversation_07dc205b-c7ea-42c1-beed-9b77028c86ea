<template>
  <div class="box">
    <div class="header">
      <Select
        v-model="searchPost.bus_id"
        @on-change="handleBusChange"
        style="width: 200px; margin-right: 20px"
        placeholder="请选择门店..."
        filterable
        :clearable="false"
      >
        <Option v-for="item in adminBusList" :value="item.id" :key="item.id">{{ item.name }}</Option>
      </Select>
      <Date-picker
        v-model="daterange"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 200px; margin-right: 20px"
        :options="dateOptions"
        :clearable="false"
      ></Date-picker>
      <Select
        v-model="searchPost.san_rule_id"
        style="width: 200px; margin-right: 20px"
        placeholder="请选择票名称..."
        filterable
        clearable
      >
        <Option v-for="item in categoryList" :value="item.san_rule_id" :key="item.san_rule_id">{{ item.card_name }}</Option>
      </Select>
      <Button type="success" icon="ios-search" @click="getList">搜索</Button>
    </div>
    <div class="buddy">
      <Table ref="table" :columns="tableCols" :data="tableData"></Table>
    </div>
    <div class="footer">
      <Button @click="handleExport">导出excel</Button>
      <Page
        @on-change="handlePage"
        :total="total"
        :current="searchPost.page_no"
        @on-page-size-change="pageSizeChanged"
        show-total
        show-sizer
      ></Page>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { formatDate } from 'utils'

export default {
  data() {
    return {
      daterange: [],
      searchPost: {
        bus_id: '',
        start_time: '',
        end_time: '',
        san_rule_id: '',
        is_export: 0,
        page_no: 1,
        page_size: 10,
      },
      dateOptions: {
        shortcuts: [
          {
            text: '最近一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
          {
            text: '最近一月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              return [start, end]
            },
          },
          {
            text: '最近三月',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              return [start, end]
            },
          },
        ],
      },
      storeList: [],
      categoryList: [],
      tableCols: [
        {
          title: '散场票名称',
          key: 'name',
        },
        {
          title: '票务收入',
          key: 'total_amount',
        },
        {
          title: '购票数量',
          key: 'num',
        },
        {
          title: '人均消费金额',
          key: 'avg_use_amount',
        },
      ],
      tableData: [],
      total: 0,
    }
  },
  computed: {
    ...mapGetters(['adminBusList']),
  },

  created() {
    const { busId, beginDate, endDate } = this.$route.query
    const start = beginDate ? new Date(beginDate) : new Date(new Date().setDate(1))
    const end = endDate ? new Date(endDate) : new Date()

    this.daterange = [start, end]
    this.searchPost.bus_id = busId || this.$store.state.busId

    this.getStoreList()
    this.getCategoryList()
    this.getList()
  },
  methods: {
    handleBusChange() {
      this.getCategoryList()
    },
    getStoreList() {
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      // return this.$service.get('/Web/Business/get_bus_list').then((res) => {
      //   if (res.data.errorcode === 0) {
      //     this.storeList = res.data.data.bus_list
      //   }
      // })
    },
    getCategoryList() {
      // return this.$service
      //   .post('/Web/Space/getTypes', {
      //     bus_id: this.searchPost.bus_id,
      //   })
      //   .then((res) => {
      this.$service.post('/Web/San/getSanCard', {
        bus_id: this.searchPost.bus_id
      }).then(res => {
          if (res.data.errorcode == 0) {
            this.categoryList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getList() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.start_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.start_time = ''
        this.searchPost.end_time = ''
      }
      return this.$service.post('/Web/san/getSanLogStatistics', this.searchPost).then((res) => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list
          this.total = Number(res.data.data.count)
        }
      })
    },
    handleExport() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.start_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.start_time = ''
        this.searchPost.end_time = ''
      }
      const copySearch = { ...this.searchPost, page_no: 1, page_size: this.total, is_export: 1 }

      this.$service.post('/Web/san/getSanLogStatistics', copySearch).then((res) => {
        if (res.data.errorcode == 0) {
          let list = []
          if (Array.isArray(res.data.data.list)) {
            list = res.data.data.list
          }
          this.$refs.table.exportCsv({
            filename: `散场票收入报表-${formatDate(new Date(), 'yyyy-MM-dd')}`,
            columns: this.tableCols,
            data: list,
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handlePage(page) {
      this.searchPost.page_no = page
      this.getList()
    },
    pageSizeChanged(size) {
      this.searchPost.page_no = 1
      this.searchPost.page_size = size
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  // height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;
  padding: 20px;

  .header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  .buddy {
    margin: 20px 0;
  }

  .footer {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
