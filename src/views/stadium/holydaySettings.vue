<template>
  <div class="box">
    <div class="header">临时调价设置</div>
    <div class="buddy">
      <Form :label-width="120">
        <Form-item>
          <Alert type="warning" show-icon>调价设置后在对应日期订场将执行调价日期的价格</Alert>
        </Form-item>
        <Form-item label="调价方式">
          <Radio-group v-model="type">
            <Radio :label="1">执行节假日价格</Radio>
            <Radio :label="2">自定义调价</Radio>
          </Radio-group>
        </Form-item>
        <Form-item v-if="type == 2" label="调价方式" required :error="priceError">
          <Input v-model="price" placeholder="调价价格" clearable>
          <Select v-model="priceType" slot="prepend" style="width: 80px">
            <Option :value="1">涨价</Option>
            <Option :value="2">降价</Option>
          </Select>
          <div slot="append">元</div>
          </Input>
        </Form-item>
        <Form-item label="调价场地" required :error="treeError">
          <treeselect :limit="10" :limitText="(count) => `还有 ${count} 个`" v-model="treeChecked" :options="treeOptions"
            placeholder="请选择..." :disabled="!!id" valueConsistsOf="LEAF_PRIORITY" :default-expand-level="Infinity"
            multiple />
        </Form-item>
        <Form-item :label="index ? '' : '调价时间'" v-for="(date, index) in dateList" :key="index" required
          :error="dateErrorList[index]">
          <Date-picker v-model="dateList[index]" format="yyyy-MM-dd" type="daterange" placeholder="选择日期"
            :editable="false" :style="index > 0 ? 'width:90%;margin-right:20px;' : 'width:100%'"></Date-picker>
          <Icon v-if="index > 0" type="ios-trash" :size="22" style="cursor: pointer" @click="dateList.splice(index, 1)">
          </Icon>
        </Form-item>
        <Form-item>
          <Button type="primary" shape="circle" @click="dateList.push(['', ''])" :disabled="!!id">添加时间</Button>
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit">提交</Button>
            <Button @click="handleReset">返回</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { formatDate } from 'utils'

const ERROR_STADIUM = '请选择调价场地'
const ERROR_DATE = '请选择调价时间'
const ERROR_X_DATE = '调价时间有冲突，请重新选择'
const ERROR_PRICE_NONE = '请输入调价价格'
const ERROR_PRICE_NUMBER = '调价价格必须为大于 0 的数字'
const ERROR_PRICE_TWO_DECIMAL = '调价价格最多两位小数'

export default {
  components: { Treeselect },
  props: {
    id: {
      type: [String, Number],
      default: null,
    },
    stadiumId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      treeOptions: [],
      treeChecked: [],
      allChecked: [],
      treeError: null,
      dateList: [['', '']],
      dateErrorList: [null],
      type: 1,
      priceType: 1,
      price: null,
      priceError: null,
    }
  },
  watch: {
    treeChecked(newValue) {
      if (newValue.length > 0) {
        this.treeError = null
      } else {
        this.treeError = ERROR_STADIUM
      }
    },
    dateList(newValue) {
      const errorList = []
      const timeList = []
      newValue.forEach((range, index) => {
        const start = range[0]
        const end = range[1]
        if (start === '' || end === '') {
          errorList.push(ERROR_DATE)
        } else {
          errorList.push(null)
        }
        const errorIndex = timeList.findIndex((time) => {
          const timeStart = time[0]
          const timeEnd = time[1]
          if (start >= timeStart && start <= timeEnd) {
            return true
          } else if (end >= timeStart && end <= timeEnd) {
            return true
          } else if (start <= timeStart && end >= timeEnd) {
            return true
          } else {
            return false
          }
        })
        if (errorIndex !== -1) {
          errorList[errorIndex] = ERROR_X_DATE
          errorList[index] = ERROR_X_DATE
        } else {
          if (start !== '' && end !== '') {
            timeList.push([start.getTime(), end.getTime()])
          }
        }
      })
      this.dateErrorList = errorList
    },
    price() {
      this.checkPrice()
    },
  },
  created() {
    this.getTree().then(() => {
      if (this.id) {
        this.getInfo()
      } else if (!this.id && !this.stadiumId) {
        this.treeChecked = this.allChecked
      }
    })
    if (this.stadiumId) {
      this.treeChecked = [this.stadiumId]
    }
  },
  methods: {
    checkPrice() {
      if (this.type === 1) {
        return true
      }
      // Is price a number? up to two decimal places
      if (!this.price) {
        this.priceError = ERROR_PRICE_NONE
      } else if (isNaN(this.price) || this.price <= 0) {
        this.priceError = ERROR_PRICE_NUMBER
      } else if (this.price.toString().split('.')[1] && this.price.toString().split('.')[1].length > 2) {
        this.priceError = ERROR_PRICE_TWO_DECIMAL
      } else {
        this.priceError = null
        return true
      }
      return false
    },
    packageTree(tree) {
      const result = []
      tree.forEach((node) => {
        const obj = {
          ...node,
          children: this.packageTree(node.children),
        }
        if (node.children.length === 0) {
          obj.leaf = true
          obj.children = undefined
          obj.isDisabled = node.is_space === 0
          if (!obj.isDisabled) {
            this.allChecked.push(node.id)
          }
        }
        result.push(obj)
      })
      return result
    },
    getInfo() {
      return this.$service.get('/Web/Space/getHolidayOne?holiday_id=' + this.id).then((res) => {
        if (res.data.errorcode === 0) {
          const info = res.data.data
          this.treeChecked = [info.space_id]
          this.dateList = [[new Date(info.start_time), new Date(info.end_time)]]
          this.priceType = Number(info.price_type || 1)
          this.price = info.price
          this.type = Number(info.type)
        }
      })
    },
    getTree() {
      return this.$service.get('/web/space/getSpaceTreeList').then((res) => {
        if (res.data.errorcode === 0) {
          this.treeOptions = this.packageTree(res.data.data)
        }
      })
    },
    handleSubmit() {
      if (this.treeChecked.length === 0) {
        this.$Message.error(ERROR_STADIUM)
        return
      }
      if (this.dateErrorList.includes(ERROR_DATE) || this.dateErrorList.includes(ERROR_X_DATE) || this.dateList[0][0] === '') {
        this.$Message.error(ERROR_DATE)
        return
      }

      if (!this.checkPrice()) {
        return
      }

      let url = '/Web/Space/addHoliday'
      const postData = {
        price_type: this.priceType,
        price: this.price,
        type: this.type,
      }
      if (this.id) {
        url = '/Web/Space/editHoliday'
        postData.id = this.id
        postData.start_date = formatDate(this.dateList[0][0], 'yyyy-MM-dd')
        postData.end_date = formatDate(this.dateList[0][1], 'yyyy-MM-dd')
      } else {
        postData.space_ids = this.treeChecked
        postData.holiday_arr = this.dateList.map((range) => {
          const start = formatDate(range[0], 'yyyy-MM-dd')
          const end = formatDate(range[1], 'yyyy-MM-dd')
          // return [start, end]
          return {
            start_date: start,
            end_date: end,
          }
        })
      }
      return this.$service.post(url, postData).then((res) => {
        if (res.data.errorcode === 0) {
          this.$Message.success('设置成功')
          this.$router.back()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleReset() {
      this.$router.back()
    },
  },
}
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  min-height: 100%;
  background-color: white;
  border: 1px solid #e0e3e9;
  box-sizing: border-box;

  .header {
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    color: #333;
    background-color: #f7f7f7;
    width: 100%;
    background: #f7f7f7;
    height: 40px;
    padding: 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #e0e3e9;
  }

  .buddy {
    width: 40%;
    min-width: 721px;
    height: 100%;
    padding: 20px 40px;
    box-sizing: border-box;
  }
}
</style>
