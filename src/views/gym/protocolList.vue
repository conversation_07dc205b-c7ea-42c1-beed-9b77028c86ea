<template>
      <div class="box tab-table-wrap customized-tabs">
                <div class="tab-head">
                     <Input style="margin-left:20px;width: 180px;" v-model="postData.name" placeholder="协议名称"/>
                     <Input style="margin-left:20px;width: 180px;" v-model="postData.version" placeholder="版本号"/>
                   <Button style="margin-left:20px;" type="success" @click="doSearch">搜索</Button>
                </div>
                <Table :columns="columns" :data="data" disabled-hover></Table>
                <footer>
                    <Button v-if="power.add" @click="addProtocol">添加协议</Button>
                    <Page :total="total" :history="false" @on-change="handlePageChange" @on-page-size-change="pageSizeChanged" show-total show-sizer></Page>
                </footer>
                <GymProtocolEdit ref="protocolEdit" @save-protocol="onSaveProtocol"  @edit-protocol="onEditProtocol"/>
    </div>
</template>
<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'

import Export from 'src/components/Export'
import { getNewHost } from 'utils/config'
import {
  queryBusAgreementList,
  queryBusProtocolDetail,
  addBusProtocolDetail,
  editBusProtocolDetail,
  delBusProtocolDetail,
  changeBusProtocolStatus
} from 'src/service/getData'
import GymProtocolEdit from './components/gymProtocolEdit'

export default {
  name: 'protocolList',
  components: {
    GymProtocolEdit
  },
  computed: {
    ...mapState(['busId'])
  },
  data() {
    return {
      columns: [
        // {
        //   title: '序号',
        //   key: 'id'
        // },
        {
          title: '序号',
          sortable: true,
          render: (h, params) => {
            return h('span', params.index + (this.temp_page_no - 1) * this.temp_page_size + 1) // currentPage 当前页 pageNum 每页显示条数
            }   
        },
        {
          title: '协议名称',
          key: 'name'
        },
        {
          title: '版本号',
          key: 'version'
        },
        {
          title: '创建时间',
          key: 'create_time'
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            const handleSwitch = (resolve) => {
              if (params.row.status === '1') {
                this.handleChangeStatus(params.row, '0')
              } else {
                if(this.usingProtocolName){
                this.$Modal.confirm({
                  title: '提示',
                  content: `${this.usingProtocolName}协议${this.usingProtocolVersion}版本正在使用中，是否进行替换？`,
                  onOk: () => {
                    this.handleChangeStatus(params.row, '1')
                    resolve()
                  }
                })}else{
                    this.handleChangeStatus(params.row, '1')
                }
              }
            }
            return (
            //   <div onClick={handleSwitch}>
                <i-switch
                  value={params.row.status}
                  key={params.row.id}
                //   loading
                  true-value="1"
                  false-value="0"
                  nativeOnMousedown={e => {
                      return new Promise((resolve)=> {
                          handleSwitch(resolve)
                      })
                    }}
                  //   disabled
                  //   onClick={handleSwitch}
                  //   on-on-change={e => {
                  //     this.handleChangeStatus(params.row, e)
                  //   }}
                />
            //   </div>
            )
          }
        },
        {
          title: '备注',
          key: 'remark',
          tooltip:true
        },
        {
          title: '操作',
          key: 'status',
          render: (h, params) => {
            const editProtocol = () => {
              this.queryProtocolDetail(params.row.id)
            }
            const delMe = () => {
              this.$Modal.confirm({
                title: '提示',
                content: `是否删除该协议？`,
                onOk: () => {
                  this.delProtocolDetail(params.row.id)
                }
              })
            }
            const deleteCard = parseInt(params.row.status) === 1 || !this.power.delete
            return (
              <div>
                <i-button
                  type="text"
                  shape="circle"
                    size="small"
                  style={{
                    color: '#52a4ea',
                    minWidth: '0',
                    marginRight: '20px'
                  }}
                  onClick={editProtocol}
                >
                  {this.power.edit?'编辑':'查看'}
                </i-button>
                <i-button
                  type="text"
                  shape="circle"
                    size="small"
                  disabled={deleteCard}
                  style={{ color: '#ff696a', minWidth: '0' }}
                  onClick={delMe}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      data: [],
      total: 0,
      postData: {
        page_no: 1,
        page_size: 10
      },
      temp_page_no:1,
      temp_page_size:10,
      power:{},
      usingProtocolName:'',
      usingProtocolVersion:'',
    }
  },
  mounted() {
    this.getList()
  },
  created() {
    // this.selectBusId2 = this.busId
    // this.queryAllPassengerList()
  },
  methods: {
    unescapeHTML(a) {
      a = '' + a
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
    },
    doSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      return queryBusAgreementList(this.postData).then(res => {
        if (res.data.errorcode == 0) {
            this.temp_page_no = this.postData.page_no
            this.temp_page_size = this.postData.page_size
          this.data = res.data.data.list
          this.power = res.data.data.power
          this.$refs.protocolEdit.onlyCheck = !this.power.edit
          if(!res.data.data.DetailByBusId){
            this.usingProtocolName = ''
            this.usingProtocolVersion = ''
          }else{
          this.usingProtocolName = res.data.data.DetailByBusId.name || ''
          this.usingProtocolVersion = res.data.data.DetailByBusId.version || ''}
          this.total = parseInt(res.data.data.count)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    addProtocol() {
      this.$refs.protocolEdit.addStart()
    },
    handlePageChange(pageNo) {
      this.postData.page_no = pageNo
      this.getList()
    },
    pageSizeChanged(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
    handleChangeStatus(item, value) {
      return changeBusProtocolStatus({
        id: item.id,
        status: value
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('更改成功')
        } else {
          this.$Message.error(res.data.errormsg)
        }
        this.getList()
      })
    },
    onSaveProtocol(protocolData) {
      addBusProtocolDetail(protocolData).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('添加成功')
          this.getList()
          this.$refs.protocolEdit.drawerFlag = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    onEditProtocol(protocolData) {
      editBusProtocolDetail(protocolData).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('编辑成功')
          this.getList()
          this.$refs.protocolEdit.drawerFlag = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    queryProtocolDetail(id) {
      queryBusProtocolDetail(id).then(res => {
        if (res.data.errorcode == 0) {
          this.$refs.protocolEdit.editStart(res.data.data)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    delProtocolDetail(id) {
      delBusProtocolDetail(id).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('删除成功')
        } else {
          this.$Message.error(res.data.errormsg)
        }
        this.getList()
      })
    }
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap {
    border-top: 0;
  }
}
.mgb-10 {
  margin-bottom: 10px;
}
.mgb-20 {
  margin-bottom: 20px;
}
.mgt-20 {
  margin-top: 20px;
}

.box {
  .tab-head {
    background-color: white;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 50px;
  }

  .total-stat {
    background-color: white;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    border-top: @border;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
}
</style>