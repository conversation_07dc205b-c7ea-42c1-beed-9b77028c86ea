<template>
    <Form label-position="right" ref="form" :model="formItem" :rules="formRules" class="form" :label-width="140">
      <FormItem label="购卡协议" prop="protocol_content">
        <FormEditor v-model="formItem.protocol_content" :options="options"/>
      </FormItem>
      <FormItem v-if="canEdit">
        <div class="form-bottom-buttons" style="marginTop:20px;">
          <Button type="success" @click="editBus">保存协议</Button>
          <!-- <Button @click="$router.back()">取消</Button> -->
        </div>
      </FormItem>
    </Form>
</template>

<script>
import { getBaseUrl } from 'utils/config'
import FormEditor from 'components/form/Editor'
export default {
  name: 'protocolEdit',
  components: {
    FormEditor
  },

  data() {
    return {
      formItem: {
        protocol_content: null
      },
      canEdit: false,
      options: {
        modules: {
          toolbar: [
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ color: [] }, { background: [] }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ align: [] }],
            [{ script: 'sub' }, { script: 'super' }]
          ]
        }
      },
      formRules: {
        bus_description: [{ required: true, message: '请填写协议内容' }]
      }
    }
  },
  created() {
    this.getBusDetail()
  },
  computed: {},
  watch: {},
  methods: {
    editBus() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$service
            .post('/Web/BuycardProtocol/update_protocol_content', this.formItem)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
        }
      })
    },
    getBusDetail() {
      this.$service
        .post('/Web/BuycardProtocol/get_protocol_content')
        .then(response => {
          if (response.data.errorcode === 0) {
            let dt = response.data.data
            this.canEdit = true
            this.formItem.protocol_content = this.unescapeHTML(
              dt.info.protocol_content || ''
            )
          } else {
            this.$Message.error(response.data.errormsg)
          }
        })
    },
    unescapeHTML(a) {
      a = '' + a
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
    }
  }
}
</script>
