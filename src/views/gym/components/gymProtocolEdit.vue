<template>
    <Drawer width='60' :title="status?'添加协议':'编辑协议'" :mask-closable="true" closable v-model="drawerFlag">
        <Form ref="form" :model="formItem" :rules="formRules" :label-width="80" class="form">
                <FormItem  label="协议名称" prop="name">
                    <Input :disabled="onlyCheck && !status" v-model="formItem.name" :maxlength="20" class="input" placeholder="请填写协议名称" />
                </FormItem>
                <FormItem  label="版本号" prop="version">
                    <Input :disabled="onlyCheck && !status"  v-model="formItem.version" :maxlength="12" class="input" placeholder="请填写协议号" />
                </FormItem>
                <FormItem  label="协议内容" prop="content">
                    <FormEditor :disabled="onlyCheck && !status" v-model="formItem.content" :options="options" />
                </FormItem>
                 <FormItem  label="状态" prop="status">
                    <i-switch :disabled="onlyCheck && !status" true-value="1" false-value="0" v-model="formItem.status" />
                </FormItem>
                 <FormItem  label="备注" prop="status">
                    <Input :disabled="onlyCheck && !status" type="textarea" v-model="formItem.remark" :autosize="{minRows: 2,maxRows: 5}" class="input" placeholder="请填写备注" />
                </FormItem>
                 <FormItem style="margin-top:20px;">
                    <Button  @click="handleCancel">取消</Button>
                    <Button :disabled="onlyCheck && !status" style="margin-left: 20px" type="primary" @click="handleSave">保存</Button>
                 </FormItem>
        </Form>
    </Drawer>
</template>
<script>
import FormEditor from 'components/form/Editor'
export default {
  name: 'gymProtocolEdit',
  components: {
    FormEditor
  },
  data() {
    return {
      status: true,
      formItem: {
        content:'',
        status: '1',
      },
      onlyCheck:false,
      drawerFlag: false,
      options: {
        modules: {
          toolbar: [
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ color: [] }, { background: [] }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ align: [] }],
            [{ script: 'sub' }, { script: 'super' }]
          ]
        }
      },
      formRules: {
        name: [{ required: true, message: '请填写协议名称' }],
        version: [{ required: true, message: '请填写协议号' }],
        content: [{ required: true, message: '请填写协议内容' }]
      }
    }
  },
  methods: {
    addStart() {
      this.formItem = {
        content:'',
         status: '1',
      }
      this.status = true
      this.drawerFlag = true
    },
    editStart(formItem) {
      this.formItem = {
        ...formItem,
        ...{
          content: this.unescapeHTML(formItem.content)
        }
      }
      this.status = false
      this.drawerFlag = true
    },
    unescapeHTML(a) {
      a = '' + a
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
    },
    handleCancel() {
      this.drawerFlag = false
    },
    handleSave() {
      this.$refs['form'].validate(valid => {
        if (valid) {
            const funcName = this.status ? 'save-protocol' : 'edit-protocol'
          this.$emit(funcName, this.formItem)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
</style>
