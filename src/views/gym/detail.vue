<template>
  <div class="venueDt">
    <div class="venueDtbox">
      <!-- 标题 -->
      <div class="venueDt-t">
        <h2>场馆详情</h2>
        <div class="venueDt-t_option">
          <router-link :to="{path:'/gym/detail/edit', query: {merName: busdata?.mer_name}}">
            <i class="add_venue"></i>
            <em>新增场馆</em>
          </router-link>
          <label @click="delBus">
            <i class="del_venue"></i>
            <em>删除场馆</em>
          </label>
          <span class="shu">|</span>
          <router-link v-if="!!busdata"
                       :to="{path:'/gym/detail/edit', query: {id: busdata.id}}">
            <i class="editor_venue"></i>
            <em>编辑</em>
          </router-link>
        </div>
      </div>
      <!-- 场馆介绍 -->
      <div class="venueDt-c"
           v-if="!!busdata">
        <img class="venueDt-c_img"
             :src="busdata.thumb">
        <div class="venueDt-c_test">
          <p class="venueDt-c_title">{{busdata.name}}</p>
          <span class="venueDt-c_name textfont">{{busdata.mer_name}}</span>
          <span class="venueDt-c_qu textfont">{{busdata.province_name.region_name}}-{{busdata.city_name.region_name}}-{{busdata.district_name.region_name}}</span>
          <span class="venueDt-c_dizhi textfont">{{busdata.address}}</span>
          <span class="venueDt-c_tel textfont">电话:{{busdata.phone}}</span>
        </div>
        <div class="venueDt-c_picbox">
          <div class="venueDt-c_pics"
               @click="showGymImg = true"
               v-if="busdata.images && !!busdata.images.length">
            <img :src="busdata.images[0]"
                 class="venueDt-c_pic">
            <p class="pics_text">共{{busdata.images && busdata.images.length}}张</p>
          </div>
        </div>
      </div>
      <div class="venueDt-class">
        <h2 class="class_title">场馆教室</h2>
        <template v-if="!!busdata &&!!busdata.room_info.length">
          <div class="class_test"
               v-for="room in busdata.room_info" :key="room.classroom_name">
            <p>{{room.classroom_name}}</p>
            <span>可容纳:{{room.allow_number}}人</span>
          </div>
        </template>
        <h2 class="class_present">场馆介绍</h2>
        <p class="present"
           v-html="bus_description"></p>
        <router-link  :to="{path:'/gym/gymProtocol'}">
             <h2 class="class_present">场馆协议 <Icon type="ios-create-outline" /></h2>
        </router-link>
        <div v-if="protocolDetail" >协议名称：{{protocolDetail.name}}    版本号：{{protocolDetail.version}}</div>
      </div>
    </div>
    <Modal title="场馆图片"
           width="750"
           v-model="showGymImg">
      <Carousel v-if="showGymImg" v-model="activeImage" autoplay loop :autoplay-speed="3000">
        <CarouselItem v-for="(image, index) in busImages" :key="index"><img style="width: 100%; max-height: 100%" :src="image"></CarouselItem>
      </Carousel>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>
<script >
import { getBaseUrl } from 'utils/config';
import {queryBusUsingProtocolDetail} from 'src/service/getData'
export default {
  name: 'GymDetail',
  created() {
    this.getBusDetail();
    this.queryUsingProtocolDetail();
  },
  data() {
    return {
      activeImage: 0,
      busdata: null,
      bus_description: '',
      busImages: [],
      showGymImg: false,
      protocolDetail:null,
    };
  },
  methods: {
    //删除场馆
    delBus() {
      let that = this;
      this.$Modal.confirm({
        title: '删除场馆',
        content: '确定删除场馆？',
        onOk: () => {
          this.delBuscf();
        }
      });
    },
    delBuscf() {
      let url = '/Web/Business/delete_bus';
      this.$service
        .post(url)
        .then(response => {
          if (response.status == 200) {
            if (response.data.errorcode == 0) {
              this.$Message.success(response.data.errormsg);
              window.location.href = getBaseUrl() + '/Web/Business/logout'+'?url='+encodeURIComponent(getNewHost());
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    getBusDetail() {
      let url = '/Web/Business/get_bus_info';
      return this.$service
        .post(url, { bus_id: this.$store.state.busId })
        .then(response => {
          if (response.status == 200) {
            if (response.data.errorcode == 0) {
              let dt = response.data.data;
              if (dt.length >= 1) {
                this.busdata = dt[0];
                this.bus_description = this.unescapeHTML(dt[0].bus_description);
                this.busImages = dt[0].images;
              } else {
                this.$Message.error(response.data.errormsg);
              }
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch(function(error) {
          console.log(error);
        });
    },
    queryUsingProtocolDetail(){
        queryBusUsingProtocolDetail(this.$store.state.busId).then(res=>{
           if (res.data.errorcode == 0) {
            this.protocolDetail=  res.data.data
            } else {
            this.$Message.error(res.data.errormsg)
            }
        })
    },
    unescapeHTML(a) {
      a = '' + a;
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    }
  }
};
</script>
<style lang="less" scoped>
.venueDt {
  /*overflow: hidden;*/
  width: 100%;
  height: 100%;
  background: #ececec;
  /* padding: 40px 48px; */
  position: relative;
}
.venueDtbox {
  background: #fff;
  width: 100%;
  overflow: hidden;
}
.venueDt-t {
  width: 100%;
  background: #f7f7f7;
  height: 37px;
  padding: 0 20px;
  overflow: hidden;
  border: 1px solid #dcdcdc;
}
.venueDt-t h2 {
  margin: 0;
  font-size: 14px;
  margin-top: 12px;
  font-weight: 600;
  display: block;
  float: left;
}
.venueDt-t_option {
  float: right;
}

.venueDt-t_option {
  display: flex;
  align-items: center;
}

.venueDt-t_option em {
  font-weight: normal;
  font-style: normal;
  font-size: 14px;
  color: #3598db;
  line-height: 35px;
  margin-left: 5px;
  cursor: pointer;
}
.add_venue {
  display: block;
  float: left;
  width: 17px;
  height: 17px;
  margin-top: 8px;
  background: url(../../assets/img/cg.png) no-repeat;
  background-position: -1px -1px;
}
.del_venue {
  display: block;
  float: left;
  width: 17px;
  height: 17px;
  margin-top: 8px;
  background: url(../../assets/img/cg.png) no-repeat;
  background-position: -27px -1px;
  margin-left: 20px;
}

.editor_venue {
  display: block;
  float: left;
  width: 17px;
  height: 17px;
  margin-top: 8px;
  background: url(../../assets/img/cg.png) no-repeat;
  background-position: -57px -1px;
}
.shu {
  display: inline-block;
  width: 1px;
  height: 20px;
  color: #dcdcdc;
  margin: 0 20px;
}
.venueDt-c {
  background: #fff;
  width: 100%;
  height: 240px;
  border-left: 1px solid #dcdcdc;
  border-right: 1px solid #dcdcdc;
}
.venueDt-c_img {
  display: block;
  float: left;
  width: 150px;
  height: 150px;
  border: 1px solid #dedede;
  margin-left: 40px;
  margin-top: 40px;
}
.venueDt-c_test {
  margin: 0;
  color: #333333;
  font-size: 16px;
  float: left;
  margin-left: 20px;
  margin-top: 44px;
}
.venueDt-c_title {
  font-weight: 600;
}
.textfont {
  clear: both;
  display: block;
  font-size: 14px;
  color: #666666;
  line-height: 30px;
}
.venueDt-c_picbox {
  float: right;
  width: 256px;
  height: 189px;
  margin-top: 34px;
  margin-right: 58px;
  position: relative;
  border: 1px solid #dcdcdc;
  background: #fff;
}
.venueDt-c_pics {
  position: absolute;
  top: 10px;
  left: -10px;
  z-index: 2;
  border: 1px solid #dcdcdc;
  padding-top: 10px;
  width: 255px;
  height: 192px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  background: #fff;
  cursor: pointer;
}
.venueDt-c_pic {
  display: block;
  width: 230px;
  height: 43/75 * 230px;
  margin: 0 auto;
}
.pics_text {
  display: block;
  float: right;
  margin-right: 10px;
  color: #999999;
  font-size: 14px;
  margin-top: 6px;
}
.venueDt-class {
  width: 100%;
  background: #fff;
  overflow: hidden;
  padding: 0 38px;
  /*min-height: 358px;*/
  padding-bottom: 45px;
  border-left: 1px solid #dcdcdc;
  border-right: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
}
.class_title {
  color: #333333;
  font-size: 14px;
  margin-bottom: 24px;
  margin-top: 0;
}
.class_test {
  width: 33.3%;
  float: left;
  height: 30px;
}
.class_test p {
  display: block;
  float: left;
  width: 50%;
  color: #666666;
  font-size: 14px;
  margin: 0;
  line-height: 30px;
}
.class_test span {
  display: block;
  float: left;
  width: 50%;
  color: #666666;
  font-size: 14px;
  margin: 0;
  line-height: 30px;
}
.class_present {
  margin: 0;
  clear: both;
  overflow: hidden;
  color: #333333;
  font-size: 14px;
  margin-bottom: 24px;
  padding-top: 40px;
}
.present {
  width: 90%;
  margin: 0;
  color: #666666;
  font-size: 14px;
}

/*弹窗*/
.busdels_box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 3;
  display: none;
  /*display: none;*/
}
.busdels {
  width: 380px;
  height: 400px;
  background: #fff;
  margin: 150px auto;
  padding: 0 21px;
  padding-bottom: 25px;
  border: 1px solid #dcdcdc;
}
.busdels-t {
  width: 100%;
  height: 49px;
}
.busdels h2 {
  font-size: 18px;
  line-height: 49px;
  display: block;
  float: left;
  margin: 0;
  font-weight: bold;
}
.busdels i {
  display: block;
  float: right;
  width: 20px;
  height: 20px;
  font-size: 20px;
  margin-top: 14px;
  background: url(../../assets/img/xclose.png) no-repeat;
  background-size: cover;
  cursor: pointer;
}
.busdels_seach {
  width: 320px;
  height: 37px;
  margin: 0 auto;
  margin-top: 20px;
}
.seachinput {
  display: block;
  width: 320px;
  height: 36px;
}
.buslist {
  width: 320px;
  height: 180px;
  clear: both;
  background: #f7f7f7;
  margin: 0 auto;
  margin-top: 25px;
}
.buslist ul {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  padding-top: 15px;
}
.buslist ul li {
  list-style: none;
  width: 100%;
  height: 30px;
}
.busCheckbox {
  clear: both;
  float: left;
  margin-left: 14px;
  margin-top: 5px;
}
.bus_poptest {
  margin: 0;
  display: block;
  float: left;
  color: #666666;
  font-size: 14px;
  line-height: 30px;
  margin-left: 10px;
}
.bus_on {
  display: block;
  width: 68px;
  height: 30px;
  overflow: hidden;
  clear: both;
  float: left;
  margin-top: 25px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  border: none;
  color: #fff;
  font-size: 14px;
  background: #5cb85c;
  border-radius: 4px;
  margin-left: 60px;
  box-shadow: 1px 1px 0px #e6e5e5;
}
.bus_on:hover {
  opacity: 0.7;
}
.bus_off {
  display: block;
  width: 68px;
  height: 30px;
  overflow: hidden;
  float: right;
  margin-top: 25px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  border: none;
  color: #5cb85c;
  font-size: 14px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #5cb85c;
  margin-right: 60px;
  box-shadow: inset 1px 1px 0px #e6e5e5;
}
.bus_off:hover {
  opacity: 0.7;
}
</style>
