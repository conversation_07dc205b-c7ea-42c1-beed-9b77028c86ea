<style lang="less">
@btnColor: #5fb75d;
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.gym-edit {
  .logo {
    .flex-center;
    border: 5px solid #dcdcdc;
    width: 150px;
    height: 150px;
    box-sizing: border-box;
    padding: 10px;
    margin-bottom: 20px;
    > img {
      width: 100%;
      height: 100%;
    }
  }

  .district {
    width: 30%;
  }

  .map-wrapper {
    width: 100%;
    height: 300px;
    margin-top: 20px;
  }

  .local-search {
    input {
      width: 100%;
      border: 1px solid #dddee1;
      border-radius: 4px;
      padding: 4px 7px;
      line-height: 1.5;
      height: 32px;
      color: #666;
    }
  }

  .el-vue-search-box-container {
    height: 0;
    width: 0;
    visibility: hidden;
    border: 1px solid #dddee1;
    border-radius: 4px;
    box-shadow: none;
    .search-tips {
      display: none;
    }
  }

  .images {
    background-color: #f7f7f7;
    padding: 10px;
    .flex-center;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 20px;
    > .img-box {
      width: 200px;
      margin: 1%;
      position: relative;
      height: 43/75 * 200px;
      > img {
        width: 100%;
        height: 100%;
        border: 1px solid #dcdcdc;
      }
    }
  }

  .class-room {
    .flex-center;
    justify-content: flex-start;
    margin-bottom: 10px;
    .text,
    .edit {
      .flex-center;
      justify-content: flex-start;
    }
    .text {
      p {
        width: 164px;
        font-size: 14px;
      }
    }
    .room-edit {
      margin-right: 10px;
      font-size: 14px;
      width: 154px;
    }
    .icons {
      font-size: 18px;
      .flex-center;
      i {
        cursor: pointer;
      }
      .fa-edit {
        color: @btnColor;
      }
      .fa-trash-o {
        color: #ccc;
      }
    }
  }
}
</style>

<template>
  <div class="container gym-edit">
    <header>
      <h3>{{ $route.query.id ? '编辑' : '新增' }}</h3>
    </header>
    <Form label-position="right"
          ref="form"
          :model="formItem"
          :rules="formRules"
          class="form"
          :label-width="180">
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">场馆 logo</p>
          <p class="tip">图片最佳尺寸: 120X120</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="logo">
          <img :src="formItem.thumb">
        </div>
        <LogoUploader refName="logoUploader" v-model="busLogo" :max-size="2" multiple />
      </FormItem>
      <FormItem label="场馆所属商家">{{ formItem.mer_name || $route.query.merName }}</FormItem>
      <!-- <FormItem label="场馆所属商家"
                prop="m_id"
                v-if="businessList.length">
        <Select v-model="formItem.m_id"
                @on-change="getGroupList"
                filterable>
          <Option v-for="bus in businessList"
                  :key="bus.id"
                  :value="bus.id">{{bus.mer_name}}</Option>
        </Select>
      </FormItem> -->
      <FormItem label="场馆名称"
                prop="name">
        <Input v-model="formItem.name"></Input>
      </FormItem>
      <FormItem prop="custom_order_no_pre">
        <div slot="label">
          <span>场馆编码</span>
          <Tooltip>
            <div slot="content"
                 style="width: 160px; white-space: normal">场馆编码和7位自增数字构成合同编号；如场馆编码为“QN”，则第一笔消费合同的编号为“QN0000001”，第二笔消费合同的编号为“QN0000002”，以此类推</div>
            <Icon size="16"
                  type="ios-help-circle"
                  color="#ffcf05"></Icon>
          </Tooltip>
        </div>
        <Input v-model="formItem.custom_order_no_pre"></Input>
      </FormItem>
      <FormItem label="场馆组">
        <CheckboxTagGroup style="width: 550px"
                          v-model="checkboxTag"
                          :id="formItem.m_id"
                          @tagAdded="getGroupList(formItem.m_id)"
                          :data="checkboxTagData"></CheckboxTagGroup>
      </FormItem>
      <FormItem label="场馆电话"
                prop="phone">
        <Input v-model="formItem.phone" />
      </FormItem>
      <FormItem label="所在地区"
                prop="district_id">
        <div style="display: flex; justify-content: space-between">
          <Select class="district"
                  @on-change="getRegion(2)"
                  filterable
                  v-model="formItem.province_id"
                  transfer>
            <Option v-for="(province) in provincesList"
                    :value="province.region_id"
                    :key="province.region_id">{{province.region_name}}</Option>
          </Select>
          <Select class="district"
                  @on-change="getRegion(3)"
                  filterable
                  v-model="formItem.city_id"
                  transfer>
            <Option v-for="(city) in citiesList"
                    :value="city.region_id"
                    :key="city.region_id">{{city.region_name}}</Option>
          </Select>
          <Select class="district"
                  filterable
                  v-model="formItem.district_id" 
                  @on-change="changeDistrict"
                  transfer>
            <Option v-for="(district) in districtsList"
                    :value="district.region_id"
                    :key="district.region_id">{{district.region_name}}</Option>
          </Select>
        </div>
      </FormItem>
      <FormItem label="详细地址"
                prop="address">
        <Input v-model="formItem.address"
               @on-enter="doAddressSearch"
               @on-blur="doAddressSearch">
        <Button slot="append"
                @click="doAddressSearch">定位</Button>
        </Input>
        <div style="margin-top: 20px;">
          经度: <Input v-model="formItem.lng" style="width: 130px;" />
          <span style="margin-left: 20px;">纬度:</span> <Input v-model="formItem.lat" style="width: 130px;" />
          <span style="margin-left: 20px;color: #999;font-size: 12px;">如需手动更改请使用 gcj02 国测局坐标系</span>
        </div>
        <!-- 获取上方下拉选择的经纬度 -->
        <!-- <el-amap-search-box :search-option="mapSearchOption" ref="searchBox" :on-search-result="onMapSearchResult"></el-amap-search-box> -->
        <!-- 地图实例 -->
        <div class="map-wrapper">
          <!-- <el-amap vid="amap"
                   :plugin="plugin"
                   :zoom="zoom"
                   :center="mapCenter">
            <el-amap-marker v-for="(marker, index) in markers"
                            :key="index"
                            :position="marker.position"
                            :events="marker.events"
                            :title="marker.title"
                            :draggable="marker.draggable"></el-amap-marker>
          </el-amap> -->
          <!-- <Tianditu :center="mapCenter" :zoom="zoom" :search="searchCityDistrict" @changeCenter="changeCenter" @changeCenterWord="changeCenterWord"></Tianditu> -->
          <TencentMap :center="mapCenter" :zoom="zoom" :search="searchCityDistrict" @emitCenter="changeCenter" @emitCenterWord="changeCenterWord" />
        </div>
      </FormItem>
      <FormItem>
        <div class="image-description" slot="label">
          <p class="label">场馆环境</p>
          <p class="tip">图片最佳尺寸: 750X430</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <div class="images"
             v-if="formItem.images.length">
          <div v-for="(image, index) in formItem.images"
               :key="index"
               class="img-box">
            <Icon style="cursor: pointer; position: absolute; right: -4px; top: -4px"
                  title="删除"
                  @click.native="formItem.images.splice(index, 1)"
                  type="md-close-circle"
                  color="red"
                  size="18"></Icon>
            <img :src="image">
          </div>
        </div>
        <BusImageUploader refName="busImageUploader"
                          v-model="uploadImg"
                          v-if="formItem.images && formItem.images.length<=30"
                          multiple
                          :options="{aspectRatio: 75/43}"></BusImageUploader>
        <div v-else>
          已达到环境图片上传的最大数量
        </div>
      </FormItem>
      <FormItem label="场馆介绍"
                prop="bus_description">
        <FormEditor v-model="formItem.bus_description" />
      </FormItem>
      <!-- <FormItem label="教室">
        <div class="class-room"
             v-for="(room, index) in formItem.room_info"
             :key="index">
          <div class="text"
               v-if="!room.edit">
            <p>{{room.classroom_name}}</p>
            <p>可容纳{{room.allow_number}}人</p>
          </div>
          <div class="edit"
               v-if="room.edit">
            <Input class="room-edit"
                   v-model="room.classroom_name"
                   placeholder="教室名称"></Input>
            <InputNumber class="room-edit"
                         :min="1"
                         v-model="room.allow_number"
                         placeholder="可容纳人数" />
          </div>
          <div class="icons">
            <template v-if="!room.edit">
              <FaIcon @click.native="editClassroom(index)"
                      name="edit"
                      color="#5fb75d"
                      style="margin-right: 10px"
                      title="编辑"></FaIcon>
              <FaIcon @click.native="formItem.room_info.splice(index, 1)"
                      name="trash-o"
                      color="#ccc"
                      title="删除"></FaIcon>
            </template>
            <FaIcon v-else
                    @click.native="confirmEditClassroom(index)"
                    name="check"
                    color="#5fb75d"
                    title="确认"></FaIcon>
          </div>
        </div>
        <Button @click="addClassroom">添加教室</Button>
      </FormItem> -->
      <FormItem>
        <div class="form-bottom-buttons">
          <Button type="success"
                  v-if="$route.query.id"
                  @click="editBus">保存</Button>
          <Button type="success"
                  v-else
                  @click="addBus">添加</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import ImgUploader from 'components/form/cropper';
import { getBaseUrl } from 'utils/config';
import FormEditor from 'components/form/Editor';
import CheckboxTagGroup from 'components/form/checkboxTagGroup';
// import Tianditu from 'components/maps/index';
// import gcoord from 'gcoord'
import TencentMap from 'components/maps/TencentMap';

export default {
  name: 'GymEdit',
  components: {
    LogoUploader: ImgUploader,
    BusImageUploader: ImgUploader,
    CheckboxTagGroup,
    FormEditor,
    // Tianditu,
    TencentMap
  },

  data () {
    return {
      formItem: {
        m_id: '',
        level_id: '',
        images: [],
        room_info: [],
        name: '',
        custom_order_no_pre: '',
        phone: '',
        province_id: '',
        lng: '',
        lat: '',
        city_id: '',
        district_id: ''
      },
      initCheckboxTagId: '',
      checkboxTag: '',
      checkboxTagData: [],
      formRules: {
        name: [{ required: true, message: '请填写场馆名称' }],
        phone: [
          { required: true, message: '请填写场馆电话' },
          { pattern: /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/, message: '电话格式不正确' }
        ],
        address: [{ required: true, message: '请填写场馆地址' }],
        district_id: [{ required: true, message: '请选择省市区县' }],
        m_id: [{ required: true, message: '请选择所属商家' }],
        bus_description: [{ required: true, message: '请填写场馆介绍' }],
        custom_order_no_pre: [{ pattern: /^([a-zA-Z]+)$/, message: '合同编号只能为字母' }]
      },
      businessList: [],
      provincesList: [],
      citiesList: [],
      districtsList: [],
      mapSearchOption: {
        city: '',
        citylimit: true
      },
      mapCenter: [0, 0],
      zoom: 16,
      uploadImg: '',
      busLogo: '',
      cityDistrict: '',
      // 高德地图标注
      markers: [
        {
          position: [0, 0],
          title: '可拖动选择精确定位',
          events: {
            click: () => { },
            dragend: e => {
              const { lng, lat } = e.lnglat;
              // console.log(lng, lat)
              this.markers[0].position = [lng, lat];
            }
          },
          draggable: true
        }
      ],
      plugin: [
        {
          pName: 'ToolBar',
          events: {
            init (instance) { }
          }
        }
      ],
      searchCityDistrict: ''
    };
  },
  created () {
    this.getRegion();
    this.getGroupList()
    const busId = this.$route.query.id;
    if (busId) {
      this.getBusDetail();
      this.$route.meta.breadText = '编辑场馆';
    } else {
      this.getBusinessList();
      this.$route.meta.breadText = '新增场馆';
    }
  },
  computed: {},
  watch: {
    uploadImg (newValue, oldValue) {
      if (newValue) {
        this.formItem.images.push(newValue);
      }
    },
    busLogo (val) {
      if (val) {
        this.formItem.thumb = val;
      }
    },
    async 'formItem.district_id' (val) {
      if (!val) return;
      if (!this.citiesList.length) {
        await this.getRegion(2);
      }
      if (!this.districtsList.length) {
        await this.getRegion(3);
      }
      this.setMapCity();
      const provinceName = this.provincesList.filter(item => {
        return item.region_id === this.formItem.province_id;
      })[0].region_name;
      const cityName = this.citiesList.filter(item => {
        return item.region_id === this.formItem.city_id;
      })[0].region_name;
      const districtName = this.districtsList.filter(item => {
        return item.region_id === this.formItem.district_id;
      })[0].region_name;
      const keyword = `${provinceName}${cityName}${districtName}`;
      this.cityDistrict = keyword;
      // console.log('城市定位: ' + this.cityDistrict)
      // if (this.formItem.address) {
      //   this.doAddressSearch();
      // } else {
      //   this.$refs.searchBox.keyword = keyword;
      //   this.$refs.searchBox.search();
      // }
    }
  },
  methods: {
    changeDistrict() {
      const provinceName = this.provincesList.filter(item => {
        return item.region_id === this.formItem.province_id;
      })[0]?.region_name;
      const cityName = this.citiesList.filter(item => {
        return item.region_id === this.formItem.city_id;
      })[0]?.region_name;
      const districtName = this.districtsList.filter(item => {
        return item.region_id === this.formItem.district_id;
      })[0]?.region_name;
      let keyword = `${provinceName}${cityName}${districtName}`;
      if(this.formItem.address != ''){
        keyword = `${provinceName || ''}${cityName || ''}${districtName || ''}${this.formItem.address || ''}`;
      }
      this.searchCityDistrict = keyword;
    },
    changeCenter(arr) {
      // let result = gcoord.transform(arr, gcoord.WGS84, gcoord.GCJ02)
      this.formItem.lat = arr[0]
      this.formItem.lng = arr[1]
      this.mapCenter = arr
    },
    changeCenterWord(result) {
      this.formItem.address = result.result
      this.formItem.lng = result.lnglat.lng
      this.formItem.lat = result.lnglat.lat
    },
    getGroupList (id) {
      return this.$service.post('/Web/business/get_level_list', { id: id || '' }).then(res => {
        if (res.data.errorcode === 0) {
          this.checkboxTagData = res.data.data.list
          if (!this.$route.query.id) { 
            //添加时可以切换商家
            this.checkboxTag = ''
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      });
    },
    doAddressSearch () {
      if (!this.cityDistrict) {
        const { province_name, city_name, district_name } = this.formItem;
        this.cityDistrict = `${province_name?.region_name}${city_name?.region_name}${district_name?.region_name}`;
      }
      // console.log('doAddressSearch:', this.cityDistrict + this.formItem.address)
      this.$nextTick(() => {
        this.searchCityDistrict = this.cityDistrict + this.formItem.address
        // this.$refs.searchBox.keyword = this.cityDistrict + this.formItem.address;
        // this.$refs.searchBox.search();
        // setTimeout(() => {
        //   // console.log(this.mapCenter)
        //   if (this.mapCenter[0] === 0) {
        //     this.doDistrictLocation(this.formItem);
        //   }
        // }, 1000);
      });
    },
    editClassroom (index) {
      this.formItem.room_info[index].edit = true;
      this.$forceUpdate();
    },
    confirmEditClassroom (index) {
      this.formItem.room_info[index].edit = false;
      this.$forceUpdate();
    },
    addClassroom () {
      this.formItem.room_info.push({
        classroom_name: '',
        allow_number: 1,
        edit: true,
        id: ''
      });
    },
    // 切换场馆
    changeBus (bus_id) {
      const url = '/Admin/Cutover/ajax_cutover';
      this.$service
        .post(url, { bus_id })
        .then(response => {
          if (response.status === 200) {
            if (response.data.status == 1) {
              window.location.href = getBaseUrl() + '/Admin/Index/index' + '/create_time/' + Date.now()+'?url='+encodeURIComponent(getNewHost());
            } else {
              this.$Message.error(response.data.info);
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    getBusinessList () {
      const url = '/Web/Business/is_admin';
      this.$service
        .post(url)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.businessList = data;
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    dealClassroom () {
      this.formItem.room_info.forEach(item => {
        delete item.edit;
      });
    },
    checkForm () {
      this.dealClassroom();
      let flag;
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.$Message.error('请完成信息填写');
        }
        flag = valid;
      });
      // const [lng, lat] = this.markers[0].position;
      // this.formItem = { ...this.formItem, ...{ lng, lat } };
      return flag;
    },
    addBus () {
      if (!this.checkForm()) return false;
      const url = '/Web/Business/add_bus';
      this.formItem.level_id = this.checkboxTag || ''
      this.$service
        .post(url, this.formItem)
        .then(res => {
          this.$Message.success(res.data.errormsg);
          if (res.data.errorcode === 0) {
            const data = res.data;
            this.$router.back();
            this.changeBus(data.bus_id);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    editBus () {
      if (!this.checkForm()) return false;
      this.formItem.level_id = this.checkboxTag || ''
      if(this.initCheckboxTagId != this.formItem.level_id) {
        this.$Modal.confirm({
          title: '提示',
          content: '请确认，编辑分组会变更卡课关联!',
          onOk: () => {
            this.updateBusInfo()
          }
        })
      } else {
        this.updateBusInfo()
      }
    },
    updateBusInfo() {
      this.$service
        .post('/Web/Business/update_bus', this.formItem)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.$router.back()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    doDistrictLocation () {
      const { province_name, city_name, district_name } = this.formItem;
      const keyword = `${province_name.region_name}${city_name.region_name}${district_name.region_name}`;
      this.$nextTick(() => {
        // this.$refs.searchBox.keyword = keyword;
        // this.$refs.searchBox.search();
      });
    },
    getBusDetail () {
      let url = '/Web/Business/get_bus_info';
      this.$service
        .post(url, { bus_id: this.$route.query.id })
        .then(response => {
          if (response.status === 200) {
            if (response.data.errorcode === 0) {
              let dt = response.data.data;
              if (dt.length >= 1) {
                this.formItem = dt[0];
                const { lng, lat } = this.formItem;
                if (lng && lng != 0) {
                  // let result = gcoord.transform([lng, lat], gcoord.GCJ02, gcoord.WGS84)
                  this.markers[0].position = [lng, lat];
                  // 接口拿到坐标系,进行转换后赋值。
                  this.mapCenter = [Number(lat), Number(lng)];
                }
                this.formItem.room_info.forEach(item => {
                  item.edit = false;
                  item.allow_number = +item.allow_number;
                });
                this.formItem.bus_description = this.unescapeHTML(dt[0].bus_description);
                this.initCheckboxTagId = this.checkboxTag = dt[0].level_id || ''
              } else {
                this.$Message.error(response.data.errormsg);
              }
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    unescapeHTML (a) {
      a = '' + a;
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    },
    // onMapSearchResult (position) {
    //   if (position.length) {
    //     console.log(position)
    //     const { lat, lng } = position[0];
    //     // console.log('地址定位: lng=' + lng + ', lat=' + lat)
    //     this.mapCenter = [lng, lat];
    //     this.markers[0].position = this.mapCenter;
    //   }
    // },
    setMapCity () {
      if (!this.formItem.city_id || !this.citiesList.length) return;
      const cityName = this.citiesList.filter(item => {
        return item.region_id === this.formItem.city_id;
      })[0].region_name;
      this.mapSearchOption.city = cityName;
      // console.log('搜索城市: ', this.mapSearchOption.city)
    },
    getRegion (type = 1) {
      if (type === 3) {
        this.setMapCity();
      }
      const url = '/Web/Business/get_region';
      const { province_id, city_id } = this.formItem;
      let postData = {
        province_id,
        city_id
      };
      if (type === 2) {
        postData = {
          province_id
        };
      }
      return this.$service
        .post(url, postData, { loading: false })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            if (type === 1) {
              this.provincesList = data;
            } else if (type === 2) {
              this.citiesList = data;
            } else if (type === 3) {
              this.districtsList = data;
            }
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
