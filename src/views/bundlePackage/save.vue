<template>
  <div class="box">
    <div class="header-ya">
      <div class="ya">{{ !bpPost.id ? '新增' : '编辑' }}</div>
    </div>
    <Form class="buddy-ya" ref="bpForm" :model="bpPost" :label-width="140">
      <FormItem
        label="套餐包名称"
        prop="name"
        :rules="{ required: true, message: '请选择', trigger: 'blur' }"
      >
        <Input
          v-model="bpPost.name"
          placeholder="请输入"
          class="input-item"
          :disabled="!!bpPost.id"
        ></Input>
      </FormItem>
      <FormItem label="售卖时间" prop="dateArr">
        <Date-picker
          v-model="dateArr"
          type="daterange"
          format="yyyy-MM-dd"
          placeholder="不填代表无限制"
          class="input-item"
          :editable="false"
        ></Date-picker>
      </FormItem>
      <FormItem label="套餐内容">
        <!-- 这里的actionType用于控制卡规则提示 -->
        <bundle-package-setting
          actionType="set"
          :bpPostId="bpPost.id"
          :belongBusId="bpPost.bus_id"
          :packageData="packageData"
          @updatePackage="handleUpdatePackage"
        ></bundle-package-setting>
      </FormItem>
      <FormItem label="发放/使用场馆" prop="deal_type">
        <RadioGroup v-model="bpPost.deal_type">
          <Radio :label="0" :disabled="!!bpPost.id">按场馆添加</Radio>
          <Radio :label="1" :disabled="!!bpPost.id">按场馆组添加</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="">
        <bus-card-set
          v-show="bpPost.deal_type === 0"
          :addType="0"
          :busList="busList"
          v-model="storeArr"
          isCustomize
        >
          <div slot="customize" class="package-info" slot-scope="{ storeData }">
            <bundle-package-store-slot
              :packageData="hipHopPackageData"
              :store="storeData"
            ></bundle-package-store-slot>
          </div>
        </bus-card-set>
        <bus-card-set
          v-show="bpPost.deal_type === 1"
          :addType="1"
          :groupList="groupList"
          v-model="groupArr"
          isCustomize
        >
          <div slot="customize" class="package-info" slot-scope="{ storeData }">
            <bundle-package-store-slot
              :packageData="hipHopPackageData"
              :store="storeData"
            ></bundle-package-store-slot>
          </div>
        </bus-card-set>
      </FormItem>
      <FormItem label="套餐描述" prop="description">
         <FormEditor class="description" v-model="bpPost.description" />
        <!-- <Input
          v-model="bpPost.description"
          type="textarea"
          placeholder="请输入"
        ></Input> -->
      </FormItem>
      <Form-item label="套餐封面" prop="thumb">
          <div class="image-description">
            <img v-if="bpPost.thumb" :src="bpPost.thumb" />
            <Button type="info" @click="uploadModal = true">
              选择图片
            </Button>
          </div>
      </Form-item>
      <Form-item>
        <div class="buttons">
          <Button type="primary" @click="handleSubmit">保存</Button>
          <Button @click="$router.back()">取消</Button>
        </div>
      </Form-item>
    </Form>
    <!-- 选择图片-弹窗 -->
    <ImgUpload
      v-model="uploadModal"
      @on-change="handleUpload"
      :options="{aspectRatio: 690/400}"
    />
  </div>
</template>

<script>
import { formatDate } from '@/utils/index.js'
import BundlePackageSetting from '@/components/bundlePackage/BundlePackageSetting.vue'
import BundlePackageStoreSlot from '@/components/bundlePackage/BundlePackageStoreSlot.vue'
import BusCardSet from '../member/components/BusCardSet.vue'
 import FormEditor from 'components/form/Editor';
import ImgUpload from 'components/form/ImgUpload';

export default {
  components: {
    BundlePackageSetting,
    BundlePackageStoreSlot,
    BusCardSet,
    FormEditor,
    ImgUpload
  },
  data() {
    return {
      dateArr: [],
      storeArr: [],
      groupArr: [],
      bpPost: {
        id: '',
        name: '',
        start_end_time: [],
        card_detail: [],
        belong: [],
        deal_type: 0,
        description: '',
        thumb:'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
      },
      packageData: [],
      hipHopPackageData: [],
      busList: [],
      groupList: [],
      uploadModal:false,
    }
  },
  methods: {
    // emit hip hop
    handleUpdatePackage(data) {
      this.hipHopPackageData = JSON.parse(JSON.stringify(data))
    },
    // form event
    async handleSubmit() {
      const flag = await this.$refs.bpForm.validate()
      if (!flag) {
        return false
      }

      // date output
      const arr = ['', '']
      if (
        Array.isArray(this.dateArr) &&
        this.dateArr.length === 2 &&
        this.dateArr[0] !== ''
      ) {
        arr[0] = formatDate(this.dateArr[0], 'yyyy-MM-dd')
        arr[1] = formatDate(this.dateArr[1], 'yyyy-MM-dd')
      }
      this.bpPost.start_end_time = arr
      // package data output
      this.bpPost.card_detail = this.hipHopPackageData
      // store choosen output
      if (this.bpPost.deal_type === 0) {
        this.bpPost.belong = this.storeArr
      } else if (this.bpPost.deal_type === 1) {
        this.bpPost.belong = this.groupArr
      }

      this.$service
        .post(
          `/Web/package/${
            this.bpPost.id ? 'editCardPackage' : 'addCardPackage'
          }`,
          this.bpPost
        )
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success('保存成功！')
            this.$router.back()
          } else if (res.data.errorcode === 50001) {
            let title = '可用或代售场馆不支持'
            if (this.bpPost.deal_type === 1) {
              title = '可用或代售场馆不支持组别'
            }
            const columns = [{title: '会员卡', key: 'card_name'},{title, key: 'bus_name'}]
            this.$Modal.error({
                title: '套餐包中部分卡种不支持设定场馆，请调整卡种可用场馆信息',
                width: 800,
                render() {
                  return (<div>
                    <Alert type="error">{res.data.errormsg}</Alert>
                    <i-table columns={columns} data={res.data.data}></i-table>
                  </div>)
                }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getBusGroupList() {
      return this.$service.get('/Web/business/get_level_list').then(res => {
        if (res.data.errorcode == 0) {
          const resList = res.data.data.list
          this.groupList = resList
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    unescapeHTML (a) {
      a = '' + a;
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    },
    getDetail() {
      return this.$service
        .post('/Web/package/getCardPackageDetail', {
          id: this.bpPost.id
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.bpPost = res.data.data
            this.bpPost.description = this.unescapeHTML(this.bpPost.description);
            // transpot date
            this.dateArr = this.bpPost.start_end_time

            // initialization bundle package template
            // this.packageData = res.data.data.card_detail
            const arr = res.data.data.card_detail
            arr.forEach(item => {
              if (!item._id) {
                item._id = Math.random()
              }
            })
            this.packageData = arr
            this.hipHopPackageData = this.packageData

            // initialization store
            if (this.bpPost.deal_type === 0) {
              this.storeArr = JSON.parse(JSON.stringify(this.bpPost.belong))
            } else if (this.bpPost.deal_type === 1) {
              this.groupArr = JSON.parse(JSON.stringify(this.bpPost.belong))
            }
          }
        })
    },
    handleUpload(path){
      this.bpPost.thumb = path;
    },
  },
  async created() {
    await this.$store.dispatch('getAdminBusList').then(res => {
      this.busList = res.data.data
    })
    await this.getBusGroupList()

    this.bpPost.id = this.$route.params.id
    if (this.bpPost.id) {
      this.getDetail()
    } else {
      this.storeArr.push({
        bus_id: this.$store.state.busId,
        bus_name: this.$store.state.busName
      })
      this.groupArr.push({
        level_id: this.groupList[0].level_id || '',
        level_name: this.groupList[0].level_name || ''
      })
    }
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: white;
  border: 1px solid #e0e3e9;

  .header-ya {
    width: 100%;
    background: #f7f7f7;
    height: 37px;
    padding: 0 20px;
    overflow: hidden;
    border-bottom: 1px solid #e0e3e9;

    .ya {
      font-size: 14px;
      line-height: 37px;
      font-weight: bold;
    }
  }

  /deep/ .buddy-ya {
    margin-top: 20px;
    width: 70%;

    .ivu-form-item-label {
      font-size: 14px;
      color: #333333;
    }

    .input-item {
      width: 100%;
    }
  }
  .description {
      width: 100%;
  }
  .image-description{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    img{
      width: 345px;
      height: 200px;
      margin-bottom: 10px;
    }
  }
}
</style>
