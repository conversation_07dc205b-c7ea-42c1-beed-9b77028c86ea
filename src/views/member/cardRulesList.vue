<template>
  <div class="table-wrap">
    <header>
      <Input
        v-model="postData.name"
        style="width: 180px"
        placeholder="规则名称"
        @on-enter="handleRefresh"
      />
      <Select
        v-model="postData.card_type_id"
        class="w120"
        placeholder="选择卡类型"
        clearable
      >
        <Option v-for="(item, key) in cardTypes" :key="key" :value="key">{{ item }}</Option>
      </Select>
      <!-- <AdminRegion v-model="postData.region_bus" style="width: 180px;max-width: none;" url="/Web/OpenClass/get_openclass_region_bus" :multiple="false" /> -->
      <Button type="success" @click.native="handleRefresh">搜索</Button>
    </header>

    <Table
      :columns="columns"
      :data="tableData"
      disabled-hover
      stripe />

    <footer>
      <Button type="success" style="margin-right: 30px" @click="handleShowModal()">添加规则</Button>
      <Page
        :total="totalCount"
        :current.sync="postData.page_no"
        placement="top"
        show-total
        show-sizer
        @on-change="getList"
        @on-page-size-change="pageSizeChanged"
      />
    </footer>

    <Modal
      v-model="showSetRules"
      title="添加规则"
      :mask-closable="false"
      :width="700"
    >
      <Form ref="rulesFormRef" :model="rulesForm" :label-width="110">
        <FormItem>
          <Alert type="warning" style="margin:0;width:310px;" show-icon>根据百分比方式计算得出小数时，会向下取整</Alert>
        </FormItem>
        <FormItem label="规则名称" prop="rule_name" :rules="{ required: true, message: '规则名称不能为空', trigger: 'change' }">
          <Input v-model="rulesForm.rule_name" :maxlength="30" placeholder="请输入规则名称" />
        </FormItem>
        <!-- :rules="{ required: true, message: '卡类型不能为空', trigger: 'change' }" -->
        <FormItem label="卡类型" prop="card_type_id" required>
          <div style="display:flex;">
            <Select v-model="rulesForm.card_type_id" :disabled="!!ruleItem" placeholder="请选择卡类型">
              <Option v-for="(item, key) in cardTypes" :key="key" :value="key">{{ item }}</Option>
            </Select>
            <Checkbox
              v-show="rulesForm.card_type_id === '4'"
              v-model="rulesForm.is_pt_time_limit_card"
              style="margin-left: 18px;width: 100px;"
              :disabled="!!ruleItem"
              true-value="1"
              false-value="0"
            >
              私教包月
            </Checkbox>
          </div>
        </FormItem>
        <!-- 私教/泳教类型 购买浮动隐藏 -->
        <template v-for="({ key }, index) in ruleSetData">
          <div v-if="index !== 1 || ['1', '2', '3'].includes(rulesForm.card_type_id)" :key="key">
            <FormItem
              :label="handleLabel(index)"
              :prop="key + '.0'"
            >
              <RadioGroup v-model="rulesForm[key][0]" @on-change="rulesForm[key][1] = null">
                <Radio label="0">无限制</Radio>
                <Radio label="1">按百分比</Radio>
                <Radio label="2">按{{ handleUnits(index) }}</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              v-show="rulesForm[key][0] !== '0'"
              label="浮动范围"
              :prop="key + '.1'"
              :rules="handleInputRules(index, key)">
              <div style="display:flex;flex-direction: row;align-items: center;">
                <!-- :formatter="value => rulesForm[key][0] === '1' ? `${value}%` : value"
                  :parser="value => rulesForm[key][0] === '1' ? value.replace('%', '') : value" -->
                <!-- :precision="handleInputNumberPrecision(key)" -->
                <InputNumber
                  v-model="rulesForm[key][1]"
                  :min="0"
                  :max="handleInputNumberMax(index, key)"
                  placeholder="请输入范围数字"
                />
                <!-- <Input
                  :value="rulesForm[key][1]"
                  placeholder="请输入范围数字"
                  @on-change="handleChangeInputNumber($event, key)"
                /> -->
                <Alert v-if="rulesForm[key][0] !== '0'" type="warning">
                  {{ handleExample(key, index) }}
                </Alert>
              </div>
            </FormItem>
          </div>
        </template>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleConfirmRules">确定</Button>
        <Button @click="showSetRules = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  // import AdminRegion from '../../components/form/adminRegion.vue';
  /* 用于添加编辑规则校验 */
  const validateIsInt = (rule, value, callback) => {
    if (!/^\d+$/.test(value + '')) {
      callback(new Error('该数值必须为正整数或0'));
    } else {
      callback();
    }
  };

  const validateIsFloat = (rule, value, callback) => {
    if (!/^[0-9]+(.[0-9]{1,2})?$/.test(value)) {
      callback(new Error('该数值必须大于等于0且只能保留两位小数'));
    } else {
      callback();
    }
  };
  const isRequired = { required: true, type: 'number', message: '浮动范围不能为空', trigger: 'change' };
  const isInt = { validator: validateIsInt, trigger: 'change' };
  const isFloat = { validator: validateIsFloat, trigger: 'change' };

  export default {
    name: 'CardRulesList',
    // components: { AdminRegion },

    data() {
      return {
        showSetRules: false,
        cardTypes: {
          "1": '期限卡',
          "2": '次卡',
          "3": '储值卡',
          "4": '私教课',
          "5": '泳教课',
        },
        postData: {
          name: '',
          card_type_id: null,
          is_pt_time_limit_card: 0, // 是否私教包月 0否1是
          page_no: 1,
          page_size: 10,
          // region_bus: []
        },
        totalCount: 0,
        tableData: [],
        columns: [
          /* {
            type: 'index',
            title: '序号',
            width: 60,
            align: 'center'
          }, */
          {
            title: '规则名称',
            key: 'rule_name'
          },
          {
            title: '卡类型',
            key: 'card_type_id',
            render: (h, param) => {
              const content = this.cardTypes[param.row.card_type_id] + (param.row.is_pt_time_limit_card == '1' ? '(私教包月)' : '');
              return h('span', content)
            },
          },
          {
            title: '售价浮动范围',
            key: 'sale_rule_value',
          },
          {
            title: '购买浮动上限',
            key: 'buy_num_rule_value',
            render: (h, param) => {
              const text = param.row.buy_num_rule_value
              return h('span', text || '无限制')
            }
          },
          {
            title: '赠送浮动上限',
            key: 'gift_num_rule_value'
          },
          {
            title: '操作',
            render: (h, param) => {
              return h('div', [
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                      shape: 'circle',
                      size: 'small'
                    },
                    style: {
                      color: '#52a4ea',
                      minWidth: '0'
                    },
                    on: {
                      click: () => { this.handleShowModal(param.row) }
                    }
                  },
                  '编辑'
                ),
                h(
                  'i-button',
                  {
                    props: {
                      type: 'text',
                      shape: 'circle',
                      size: 'small'
                    },
                    style: {
                      color: '#d9544f',
                      marginLeft: '10px',
                      minWidth: '0'
                    },
                    on: {
                      click: () => { this.handleIsDelete(param.row.id) }
                    }
                  },
                  '删除'
                ),
              ]);
            }
          }
        ],

        ruleItem: null,
        rulesForm: {
          // id: '',                  // 编辑必传规则id
          rule_name: '',
          card_type_id: '1',
          is_pt_time_limit_card: "0", // 是否私教包月 0否1是
          sale_rule: ['0', null],       // 规则类型（0不浮动，1百分比，2金额）, 浮动值（ ‘’/百分值/浮动金额 ）
          buy_num_rule: ['0', null],    // 购买量规则, 浮动值
          gift_num_rule: ['0', null],   // 赠送量规则, 浮动值
        },
        ruleSetData: [
          {
            key: 'sale_rule',
            prefix: ['售价', '售价', '售价', '单节售价', '单节售价'], // label
            units: ['金额', '金额', '金额', '金额', '金额'],
            example: [
              '例：标准$$100，浮动范围10%，$$范围90~110',
              '例：标准$$200，浮动范围20，$$范围180~220'
            ],
          },
          {
            key: 'buy_num_rule',
            prefix: ['购买', '购买', '价值', '购买', '购买'],
            units: ['天数', '次数', '金额', '节数', '节数'],
            example: [
              '例：标准$$360，浮动范围上限5%，则$$上限378',
              '例：标准$$360，浮动范围上限20，则$$上限380',
            ]
          },
          {
            key: 'gift_num_rule',
            prefix: ['赠送', '赠送', '赠送', '赠送', '赠送'],
            units: ['天数', '次数', '金额', '节数', '节数'],
            example: [
              '例：标准$$10，浮动范围上限20%，则$$上限12',
              '例：标准$$10，浮动范围上限5，则$$上限15'
            ]
          },
        ]

      }
    },

    watch: {
      showSetRules(show) {
        !show && this.handleInitRuleForm()
      },
      'rulesForm.card_type_id'(val, last) {
        if(this.ruleItem === null) {
          this.rulesForm.sale_rule[0] = '0';
          this.rulesForm.buy_num_rule[0] = '0';
          this.rulesForm.gift_num_rule[0] = '0';
        }
        // 如果上一次是私教 则重置私教包月控制参数
        if(last === '4' && this.ruleItem === null) {
          this.rulesForm.is_pt_time_limit_card = '0';
        }
      }
    },

    async created() {
      await this.getCardRuleSettingAuth()
      this.getList();
    },

    methods: {
      getList() {
        this.$service.post('/Web/CardSaleRule/getRuleList', this.postData).then(({ data: resData }) => {
          const { errorcode, errormsg, data } = resData;
          if (errorcode === 0) {
            this.tableData = data.list;
            this.totalCount = +data.count;
          } else {
            this.$Message.error(errormsg);
          }
        })
        .catch(err => { console.error(err) });
      },
      // 购卡规则设置权限校验
      getCardRuleSettingAuth() {
        return this.$service.post('/Web/CardSaleRule/set_rule').then(({data}) => {
          const { errorcode: code, errormsg } = data;
          if (code === 40014 || code !== 0) {
            this.$router.replace({ path: '/Web/Member/card' })
            this.$Message.error(errormsg);
          }
        })
      },
      // 添加/修改规则
      setRules() {
        const { ruleItem, rulesForm } = this;
        const params = { ...rulesForm };
        let url = '';

        if (['4', '5'].includes(params.card_type_id)) {
          delete params.buy_num_rule;
        }
        if (ruleItem) {
          url = '/Web/CardSaleRule/editSaleRule';
          params.id = ruleItem.id;
        } else {
          url = '/Web/CardSaleRule/addSaleRule';
        }

        this.$service.post(url, params).then(({ data: resData }) => {
          const { errorcode, errormsg, data } = resData;
          if (errorcode === 0) {
            this.handleRefresh();
            this.$Message.success(errormsg)
            this.showSetRules = false;
          } else {
            this.$Message.error(errormsg);
          }
        })
        .catch(err => { console.error(err) });
      },
      // 删除规则
      delRules(id) {
        this.$service.post('Web/CardSaleRule/delSaleRule', { id }).then(res => {
          if (res.data.errorcode === 0) {
            this.handleRefresh();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },

      // 打开添加规则弹窗
      handleShowModal(item) {
        this.handleInitRuleForm(item)
        this.showSetRules = true;
      },
      // 初始化规则表单数据
      handleInitRuleForm(item = null) {
        // 有值回显，否则重置
        if(item) {
          const { rulesForm: form } = this;
          let {
            // id,
            rule_name,
            card_type_id,
            sale_rule,
            buy_num_rule,
            gift_num_rule,
            is_pt_time_limit_card
          } = item;

          const ruleArr = [sale_rule, buy_num_rule, gift_num_rule];
          ruleArr.forEach((v, i, arr) => {
            if(v) {
              const newVal = JSON.parse(v);
              newVal[1] = newVal[1] === '' ? null : +newVal[1];
              arr[i] = newVal;
            }
          })

          form.rule_name = rule_name;
          form.card_type_id = card_type_id + '';
          form.sale_rule = ruleArr[0];
          if (ruleArr[1]) form.buy_num_rule = ruleArr[1];
          form.gift_num_rule = ruleArr[2];
          form.is_pt_time_limit_card = is_pt_time_limit_card + '';
          this.ruleItem = item;
        }else {
          this.ruleItem = null;
          this.$refs.rulesFormRef.resetFields()
        }
      },
      // 处理显示字段 随选择卡类型 变更
      handleLabel(labelIdx) {
        const { rulesForm, ruleSetData } = this;
        const { prefix, units } = ruleSetData[labelIdx]
        const isCardType_7 = rulesForm.is_pt_time_limit_card == '1'
        const typeIdx = isCardType_7 ? 0 : +rulesForm.card_type_id - 1

        return prefix[typeIdx] + (labelIdx ? units[typeIdx] : '') + '浮动方式'
      },
      handleUnits(labelIdx) {
        const { rulesForm, ruleSetData } = this;
        const { units } = ruleSetData[labelIdx]
        const isCardType_7 = rulesForm.is_pt_time_limit_card == '1'
        const typeIdx = isCardType_7 ? 0 : +rulesForm.card_type_id - 1

        return units[typeIdx]
      },
      handleExample(key, labelIdx) {
        const { rulesForm, ruleSetData } = this;
        const { prefix, units, example } = ruleSetData[labelIdx]
        const isCardType_7 = rulesForm.is_pt_time_limit_card == '1'
        const typeIdx = isCardType_7 ? 0 : +rulesForm.card_type_id - 1
        const idx = +rulesForm[key][0] - 1;
        // '例：标准$$100，浮动范围10%，$$范围90~110',
        return example[idx].replace(/\$\$/g, prefix[typeIdx] + (labelIdx ? units[typeIdx] : ''))
      },
      handleInputNumberMax(index, key) {
        // 0
        //   100 .   999    .
        //   100 .   999    .
        //   100 .   999    .
        //   100 .   999    .
        //   100 .   999    .
        // 1
        //   999  .  999    =
        //   999  .  999    =
        //   100  .  999    .


        // 2
        //   999  .  999    =
        //   999  .  999    =
        //   100  .  999    .
        //   999  .  999    =
        //   999  .  999    =
        const { rulesForm } = this;
        return rulesForm[key][0] === '1' && index === 0 ? 100 : 99999999
      },
      handleInputRules(index, key) {
        const { rulesForm } = this;
        const type_id = +rulesForm.card_type_id
        let rules = null;

          switch (rulesForm[key][0]) {
            case '1':
              if(index !== 1 || ![4, 5].includes(type_id)) {
                rules = [isRequired, isFloat]
              }
              break;
            case '2':
              rules = [isRequired, index === 0 || type_id === 3 ? isFloat : isInt]
              break;
          }

        return rules;
      },
      // 处理输入框的保留位数
      handleInputNumberPrecision(key) {
        const { rulesForm } = this;
        switch (key) {
          case 'sale_rule':
            return 2;
            break;
          default:
            return rulesForm[key][0] === '1' || rulesForm.card_type_id === '3' ? 2 : 0;
            break;
        }
      },
      // 确认提交购卡规则
      handleConfirmRules(e) {
        this.$refs.rulesFormRef.validate((valid) => {
          valid && this.setRules()
        })
      },

      handleIsDelete(id) {
        this.$Modal.confirm({
          title: '删除规则',
          content: '确认删除该规则吗？',
          onOk: () => {
            this.delRules(id)
          },
        });
      },

      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.postData.page_no = 1;
        this.getList();
      },

      handleRefresh() {
        this.postData.page_no = 1;
        this.getList();
      },


    }
  }
</script>

<style lang="less" scoped>
.ivu-input-number {
  width: 28%;
}
.ivu-alert-warning {
  margin-left: 20px;
  margin-bottom: 0;
  padding-right: 16px;
}
</style>
