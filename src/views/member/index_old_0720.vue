<template>
<div>
  <div v-if="$route.name !== '会员管理' && $route.meta.parentName == '会员'">
    <router-view></router-view>
  </div>
  <div class="table-wrap father-table" v-else>
    <Menu mode="horizontal" :active-name="curMenu" @on-select="showMenuContent" v-if="$route.query.curMenu !== 'search'">
        <MenuItem v-for="cur in curMenuObj.main" :key="cur" :name="cur">
          {{cur}}
        </MenuItem>
        <MenuItem name="体验卡会员" v-if="experienceCards">
          体验卡会员
        </MenuItem>
        <MenuItem name="其它门店会员" v-if="otherBusUser">
          其它门店会员
          <Tooltip>
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <p>归属门店是其他门店，但能在本店进行用卡</p>
              <p>训练的会员，例如通卡会员</p>
            </div>
          </Tooltip>
        </MenuItem>
    </Menu>
    <div class="badge-wrap" v-if="$route.query.curMenu !== 'search'">
      <Badge v-for="(cur,index) in curMenuObj.badge" :title="getTitle(index,listTitle[badgeKey[index]])"  :key="cur" :count="+listTitle[badgeKey[index]]" overflow-count="999" class="member-badge" :class="{'cur-badge': curMenu===cur}">
        <span class="badge-label" @click="showMenuContent(cur)">{{cur}}</span>
      </Badge>
    </div>
    <div>
      <!--所有会员-->
      <MemberTable v-if="curMenu==='search'" :curName="curMenu" />
      <div v-else>
        <keep-alive v-for="cur in curMenuObj.main" :key="cur">
          <MemberTable :curName="curMenu" v-if="curMenu === cur" />
        </keep-alive>
        <keep-alive v-if="curMenu === '体验卡会员' && !!experienceCards">
          <ExperienceCards />
        </keep-alive>
        <keep-alive  v-if="curMenu === '其它门店会员'">
          <MemberTable curName="其它门店会员" />
        </keep-alive>
        <keep-alive v-for="cur in curMenuObj.badge" :key="cur">
          <MemberTable :curName="curMenu" v-if="curMenu === cur" />
        </keep-alive>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import ExperienceCards from 'views/member/components/ExperienceCards';
import MemberTable from 'views/member/components/MemberTable';

export default {
  name: 'member',
  data() {
    return {
      experienceCards: true,//是否显示体验卡会员列表
      otherBusUser: true,//是否显示其它门店会员
      curMenu: this.$route.query.curMenu || '有效会员',
      curMenuObj:{
        main:['有效会员','潜在会员','过期会员','公海会员'],//隐藏未列出选项为searc模式 此时curMenu为search
        badge:['近期到期','次数耗尽','金额耗尽','生日提醒','长时间未到场','长时间未跟进','无会籍跟进的潜客','无教练跟进的会员']
      },
      badgeKey:['expire','not_num','not_amount','birthday_reminder','not_present','not_follow', 'not_membership_follow', 'not_coach_follow'],
      listTitle: {
        expire: 0,
        not_num: 0,
        not_amount: 0,
        birthday_reminder: 0,
        not_present: 0,
        not_follow: 0,
        not_membership_follow: 0,
        not_coach_follow: 0
      }
    };
  },
  watch: {
    $route(val,oldVal){
      if(val.query.curMenu!=oldVal.query.curMenu){
        this.curMenu = val.query.curMenu ? val.query.curMenu : sessionStorage.getItem('curMenu') ? sessionStorage.getItem('curMenu') : '有效会员'
      }
    },
    curMenu(val) {
      if(val!=='search') {
        sessionStorage.setItem('curMenu', val);
      }
    }
  },
  created() {
    this.curMenu = this.$route.query.curMenu ? this.$route.query.curMenu : sessionStorage.getItem('curMenu') ? sessionStorage.getItem('curMenu') : '有效会员'
    if(this.$route.name === '会员管理'){
      this.getListTitle();
      this.checkExpAndLockerent()
    }
  },
  components: {
    ExperienceCards,
    MemberTable,
  },
  methods: {
    getTitle(index,count){
      count = count || 0
      switch (index) {
        case 0:
          return `有${count}位会员在近期过期`
          break;
        case 1:
          return `有${count}位会员的会员卡次数或节数即将耗尽`
          break;
        case 2:
          return `有${count}位会员剩余金额即将用完`
          break;
        case 3:
          return `本月有${count}位会员即将过生`
          break;
        case 4:
          return `有${count}位会员长时间未到场训练`
          break;
        default:
          return `有${count}位会员长时间未进行跟进`
          break;
      }
    },
    showMenuContent(name) {
      this.curMenu = name;
    },
    checkExpAndLockerent() {
      this.$service.get('/Web/MemberList/check_member_list').then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            let data = res.data.data
            this.experienceCards = res.data.experienceCards
            this.otherBusUser = res.data.order_bus_user
          } else {
            this.$Message.error(res.data.errormsg)
          }
        } else {
          console.log('服务器连接失败！')
        }
      });
    },
    getListTitle() {
      this.$service.get('/Web/MemberList/member_list_title').then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.listTitle = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        } else {
          this.$Message.error('服务器连接失败！');
        }
      });
    }
  }
};
</script>
<style>
.badge-wrap .ivu-badge-count{
  transform: translateX(80%);
  top: -15px;
}
</style>
<style lang="less" scoped>
.father-table .table-wrap{
  border: none;
}
.badge-wrap{
  display: flex;
  align-items: center;
  height: 80px;
  padding: 0 35px;
  width: 100%;
  margin-bottom: -25px;
}
.member-badge{
  margin-right: 55px;
}
.badge-label{
  cursor: pointer;
}
.member-badge .badge-label:hover,.cur-badge .badge-label{
  color: #2d8cf0;
}
</style>
