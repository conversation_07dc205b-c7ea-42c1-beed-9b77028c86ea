<template>
    <div class="form-box">
        <div class="form-box-title">
            <h2>销卡</h2>
        </div>
        <div class="form-box-con">
            <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="140">
                <Form-item label="会员卡" prop="cardinfo">
                    {{cardinfo.card_name}}
                    <span v-if="cardinfo.card_sn">(卡号：{{cardinfo.card_sn}})</span>
                </Form-item>
                <Form-item label="当前次数" prop="cardinfo" v-if="cardinfo.is_pt_time_limit_card != 1 && (cardinfo.card_type==2||cardinfo.card_type==4||cardinfo.card_type==5)">
                    总次数{{cardinfo.all_num}}次，剩余次数{{cardinfo.last_num}}次
                </Form-item>
                <Form-item label="当前金额" prop="cardinfo" v-if="cardinfo.card_type==3">
                    总金额{{cardinfo.all_num}}元，剩余金额{{cardinfo.last_num}}元
                </Form-item>
                <Form-item label="当前有效期" prop="cardinfo">
                    {{cardinfo.active_time_date}}至{{cardinfo.end_time_date}}
                </Form-item>
                <Form-item label="剩余天数" prop="cardinfo">
                    <template v-if="cardinfo.last_day!='请假中'&&cardinfo.last_day!='未激活'">
                        {{cardinfo.last_day}}天
                    </template>
                    <template v-else>
                        {{cardinfo.last_day}}
                    </template>
                </Form-item>
                <left-value :value="cardinfo.card_last_value" :cardUserId="$route.params.card_user_id"/>
                <Form-item label="销卡服务费" prop="service_charge">
                  <Input v-model="formCustom.service_charge" type='text'></Input>
                </Form-item>
                <Form-item label="退款金额" prop="refund">
                    <Input v-model="formCustom.refund" type='text'></Input>
                </Form-item>
                <Form-item label="订单" prop="cardinfo" v-if="cardinfo.custom_order_sn">
                    编号{{ cardinfo.custom_order_sn }}，
                    {{ formCustom.service_charge==0 && formCustom.refund == 0 && cardinfo.is_buy_card == 1
                      ? `订单将一同被撤销，发放积分将回收最新一笔合同（发放积分 ${cardinfo.order_point}，已使用或过期 ${cardinfo.order_point - cardinfo.order_last_point}， 回收 ${cardinfo.order_last_point})`
                      : `原订单不被撤销，将生成新的订单，发放积分将一并回收（发放积分 ${cardinfo.card_order_point}，已使用或过期 ${cardinfo.card_order_point - cardinfo.card_order_last_point}， 回收 ${cardinfo.card_order_last_point})`
                    }}
                </Form-item>
                <Form-item v-if="formCustom.refund != 0 && formCustom.refund != '' && !isNaN(Math.abs(formCustom.refund))" label="退款方式" prop="refundtype" :required="formCustom.refund != 0 && formCustom.refund != ''"
                           :rules="{required: formCustom.refund == 0 || formCustom.refund == '' ? false : true,message: '金额不为0时,支付方式为必选项'}">
                    <pay-type-list v-model="formCustom.refundtype" :amount="Math.abs(formCustom.refund)" :showSQB="false" :showCardPay="false" :describe="`销卡[${cardinfo.card_name}]`"></pay-type-list>
                </Form-item>

                <!-- <FormItem label="储值卡退款方式" v-if="formCustom.refundtype===8"
                          prop="payCardUserId"
                          :required="formCustom.refundtype === 8">
                    <div>
                        <Select v-model="formCustom.payCardUserId">
                            <Option v-for="item in userDebtCard"
                                    :key="item.card_user_id"
                                    :value="item.card_user_id">{{item.card_name}}&nbsp;&nbsp;剩余￥{{item.amount}}
                            </Option>
                        </Select>
                    </div>
                </FormItem> -->

                <FormItem label="卡号注销" v-if="cardinfo.card_sn">
                    <RadioGroup v-model="formCustom.del_card_sn">
                        <Radio label="1">卡号后续可由其他会员进行使用</Radio>
                        <Radio label="0">卡号以后都不再使用</Radio>
                    </RadioGroup>
                </FormItem>
                <BelongTo
                  v-if="formCustom.refund != 0"
                  :cardTypeId="cardinfo.card_type"
                  :amount="formCustom.refund"
                  from="cancelCard"
                  v-model="belongList"
                  :hasStore="true"
                  :isStore="isStore"></BelongTo>
                <Form-item label="成交时间" prop="deal_time" :rules="{ required: true, message: '请选择日期'}">
                    <Date-picker v-model="dealTime" :editable="false" :options="disableDayAfter" type="date"
                                 placeholder="合同成交时间"></Date-picker>
                </Form-item>
                <Form-item label="销卡原因" prop="cancelremark">
                    <Input v-model="formCustom.cancelremark" placeholder="请填写销卡原因" :maxlength="500"
                           type="textarea"></Input>
                </Form-item>
                <Form-item>
                    <div class="buttons">
                        <Button type="primary" @click="handleSubmit('formCustom')">保存</Button>
                        <Button @click="$router.back()">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>
        <receipt-modal v-model="showPrint" :to-path='toPath' @on-receiptupdate="receiptUpdate"/>
    </div>
</template>
<script>
  import {getcardInfo, getsalesInfo} from '../../service/getData.js';
  import {formatDate} from 'utils';
  import BelongTo from 'components/member/belongTo';
  import LeftValue from './components/leftValue.vue';
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js';
  import PayTypeList from 'components/form/PayTypeList.vue'
  export default {
    name: 'CancelCard',
    components: {
      BelongTo,
      LeftValue,
      PayTypeList,
      receiptModal
    },
    mixins: [receipt],
    created() {
      this.user_id = this.$route.params.user_id;
      this.card_user_id = this.$route.params.card_user_id;
      this.getCardInfo();
    },
    data() {
      var validatePass = (rule, value, callback) => {
        // var checkval = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/.test(value);
        var checkval = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d{1,2}?)$/.test(value);
        if (checkval) {
          callback();
        } else {
          callback(new Error('请输入正确的金额（>=0)'));
        }
      };
      return {
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        styles: {
          alignCenter: {
            display: 'flex',
            alignItems: 'center',
            // width: '60%',
            maxWidth: '660px'
          },
          select: {
            // maxWidth: '400px',
            flex: 1
          },
        },
        belongList: [{marketers_id: '', amount: 0, percent: 100}],
        user_id: '',
        card_user_id: '',
        dealTime: formatDate(new Date(), 'yyyy-MM-dd'),
        formCustom: {
          refund: '0',
          cancelorder: 1,
          refundtype: [],
          deal_time: formatDate(new Date(), 'yyyy-MM-dd'),
          marketers_id: '',
          cancelremark: '',
          del_card_sn: '1',
          payCardUserId: 0,
          service_charge: '0'
        },
        coachslist: [],
        salecoach: [],
        cardinfo: {},
        ruleCustom: {
          service_charge: [
            {required: true, message: '请输入销卡服务费', trigger: 'blur'},
            {validator: validatePass, trigger: 'blur'}
          ],
          refund: [
            {required: true, message: '请输入退款金额', trigger: 'blur'},
            {validator: validatePass, trigger: 'blur'}
          ],
          cancelremark: [{required: true, message: '请填写销卡原因', trigger: 'blur'}]
        },
        isStore: false
      };
    },
    watch: {
      dealTime(val) {
        this.formCustom.deal_time = val ? formatDate(val, 'yyyy-MM-dd') : '';
      },
      'formCustom.refund'(val = 0, oldVal = 0) {
        if (val !== oldVal && oldVal) {
          this.belongList = this.belongList.map(item => {
            return {
              ...item,
              ...{
                amount: (val * item.percent / 100).toFixed(2)
              }
            };
          });
        }
      },
      // ! 退款金额=卡剩余价值-销卡服务费
      'formCustom.service_charge'(val = 0, oldVal = 0) {
        if (val > 0) {
          this.formCustom.refund = (parseFloat(this.cardinfo.card_last_value) - parseFloat(val)).toFixed(2)
        }
      }
    },
    methods: {
      receiptUpdate() {
        this.$router.back();
      },
      getCardInfo() {
        var postData = {
          card_user_id: this.card_user_id
        };
        getcardInfo(postData)
            .then(response => {
              if (response.status === 200) {
                if (response.data.errorcode === 0) {
                  const info = response.data.data.info;
                  this.cardinfo = response.data.data.info;
                  if (info.order_help_deal_list) {
                    this.belongList = info.order_help_deal_list
                        .map((item, index) => {
                          // * set store radio
                          // ! set market id and name
                          let dddid = item.marketers_id
                          if (item.marketer_category == 2) {
                            dddid = `c${item.marketers_id}`
                          }
                          if (index === 0 && item.marketer_category == 3) {
                            this.isStore = true
                          }
                          return {
                            ...item,
                            ...{
                              marketers_id: dddid,
                              percent: item.proportion
                            }
                          };
                        })
                        .sort((a, b) => b.is_main - a.is_main);
                  }
                  if(info.new_pay_type && info.new_pay_type.length) {
                    this.formCustom.refundtype = info.new_pay_type
                  }
                  this.formCustom.marketers_id = this.cardinfo.marketers_id;
                }
              } else {
                this.$Message.error(response.data.errormsg);
              }
            })
            .catch(function (response) {
              console.log(response);
            });
      },
      checkMultipleSelect(postData) {
        const percent = postData.help_deal.reduce((total, item) => {
          const num = total + +Number(item.percent).toFixed(3)
          return +Number(num).toFixed(3)
        }, 0);
        for (let item of postData.help_deal) {
          if (Number.isNaN(+item.percent)) {
            this.$Message.error('成单占比错误');
            return false;
          }
          if (item.marketers_id == '' && item.percent && postData.help_deal.length > 1) {
            this.$Message.error('请选择业绩归属');
            return false;
          }
        }
        if (percent != 100) {
          this.$Message.error('成单占比总和不等于100%');
          return false;
        }
        return true;
      },
      handleSubmit(name) {
        this.$refs[name].validate(valid => {
          if (valid) {
            let postData = {
              user_id: this.user_id,
              card_user_id: this.card_user_id,
              remark: this.formCustom.cancelremark,
              amount: this.formCustom.refund,
              deal_time: this.formCustom.deal_time,
              new_pay_type: this.formCustom.refundtype.filter(v => v.amount && +v.amount > 0), // fix 20075 此处将请求传参中金额为0的支付方式去掉了, 传了0会报：退款方式设置错误
              help_deal: this.belongList,
              del_card_sn: this.formCustom.del_card_sn,
              service_charge: this.formCustom.service_charge
            };
            if (!this.checkMultipleSelect(postData)) return false;

            // * 后端说外层也要放一个
            postData.marketers_id = postData.help_deal[0].marketers_id
            postData.marketer_category = postData.help_deal[0].marketer_category

            /* 12601 处理精度问题，如果有误差，将最后的 ±0.0X 误差算到第一个业绩归属里面 */
            const amount = postData.amount;
            const sum = postData.help_deal.map(v => v.amount).reduce((previous, current) => +previous + +current, 0)
            if (amount !== sum) {
              postData.help_deal = postData.help_deal.map(v => ({ ...v }))
              postData.help_deal[0].amount = (+postData.help_deal[0].amount + +( amount - sum ).toFixed(2)).toFixed(2);
            }

            let url = '/Web/Member/stopCard';
            let _this = this;
            this.$service
                .post(url, postData)
                .then(response => {
                    if (response.data.errorcode == 0) {
                      if (response.data.card_order_info_id) {
                        _this.contractComplete(postData.user_id, response.data.card_order_info_id, 'cancelcard');
                      } else {
                        _this.$Message.success(response.data.errormsg);
                        setTimeout(function () {
                          _this.$router.back();
                        }, 1000);
                      }
                    } else {
                      _this.$Message.error(response.data.errormsg);
                    }
                })
          }
        });
      }
    }
  };
</script>
<style scoped>
    .frame {
        width: 100%;
        background: #ececec;
    }

    .contentbox {
        /*width: 100%;*/
        margin: 40px 48px 89px 48px;
        border: 1px solid #dcdcdc;
        background: #fff;
        clear: both;
    }

    .contenthead {
        width: 100%;
        height: 37px;
        border-bottom: 1px solid #cccccc;
        background: #f7f7f7;
    }

    .contenthead p {
        color: #333;
        font-size: 14px;
        padding-left: 18px;
        line-height: 37px;
    }

    .contentbody {
        border-top: 1px solid #dcdcdc;
        width: 100%;
        background: #fff;
        clear: both;
        overflow: auto;
    }

    .oneline {
        width: 100%;
        clear: both;
        overflow: hidden;
    }

    .oneline .text-left {
        float: left;
        width: 110px;
        height: 60px;
        font-size: 14px;
        color: #333;
        text-align: right;
        line-height: 60px;
    }

    .oneline .text-right {
        float: left;
        margin-left: 28px;
        height: 60px;
        font-size: 14px;
        color: #333;
        text-align: right;
        line-height: 60px;
    }

    .cards-operation {
        width: 506px;
        height: 36px;
        float: left;
        margin-left: 28px;
        margin-top: 12px;
    }

    .remarkbox {
        float: left;
        width: 506px;
        height: 100px;
        margin-left: 28px;
        margin-top: 12px;
        margin-bottom: 26px;
    }

    .buttons {
        width: 100%;
        padding-bottom: 40px;
        clear: both;
        overflow: auto;
    }

    .confirmyes {
        float: left;
        margin-left: 351px;
    }

    .confirmno {
        float: left;
        margin-left: 71px;
    }
</style>
