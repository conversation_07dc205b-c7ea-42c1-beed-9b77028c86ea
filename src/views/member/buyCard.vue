<template>
    <div class="form-box">
        <div class="form-box-title">
            <h2>购卡</h2>
        </div>
        <div class="form-box-con">
            <Form ref="cardForm" :label-width="140">
                <Form-item label="所在门店">
                    <Select v-model="belongBusId"
                            placeholder="归属场馆"
                            :disabled="!isEditFlag"
                            filterable>
                        <Option v-for="item in merchantsBusList"
                                :disabled="busId == item.bus_id"
                                :value="item.bus_id"
                                :label="item.bus_name"
                                :key="item.bus_id">
                                {{ item.bus_name }}
                        </Option>
                    </Select>
                    <span @click="handleEdit" v-if="merchantsBusList && !isEditFlag" class="edit-bus">更改</span>
                    <span v-if="merchantsBusList && isEditFlag" class="edit-bus" @click="cancelChangeCard">取消更改</span>
                </Form-item>
                <card-Info :belongBusId="belongBusId" ref="cardComponent" :isPreOrder="isPreOrder" :cardData="cardData" :actionType="actionType" :selectedCard.sync="selectedCard" :coachList.sync="coachList" :coachCur.sync="coachCur" :overRuleAuth="overRuleAuth" @onPrivateNumChange="amount => privateTotalAmount = amount"/>

                <contract-Info ref="contractComponent" :isPreOrder="isPreOrder" :contractData='cardData'
                               :privateTotalAmount="privateTotalAmount" :selectedCard="selectedCard"
                               :coachList.sync="coachList"
                               :coachCur.sync="coachCur" :actionType="actionType" v-on:changePayType="payTypeChanged"/>
                <Form-item>
                    <div class="buttons">
                        <Button type="primary" @click="handleOnlinePay" v-if="onlinePay">在线支付并提交</Button>
                        <Button type="error" style="border:none;" v-if="isPreOrder" @click="showRejectModal=true">退单
                        </Button>
                        <Button type="primary" :loading="loadingFlag" @click="handleSubmit">提交</Button>
                        <Button @click="$router.back()">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>
        <Modal v-model="showModal" title="收款" :closable="closable" :mask-closable="false">
            <OnlinePay v-if="showModal" :data="formData" v-on:closeModal="showModal = false"
                       v-on:enterPaying="closable = false" v-on:leavePaying="closable = true"></OnlinePay>
            <span slot="footer"></span>
        </Modal>
        <reject-order v-if="isPreOrder" v-model="showRejectModal" :id="$route.query.preOrdId"/>
        <receipt-modal v-model="showPrint" :to-path='toPath' @on-receiptupdate="receiptUpdate"/>

        <Modal v-model="showVerify" title="请输入会员收到的短信验证码" :closable="false" :mask-closable="false">
            <Input v-model="verifyCode" placeholder="短信验证码">
                <Button slot="append" @click="sendVerifyCode">重发
                </Button>
            </Input>
            <div slot="footer" style="text-align:center;">
                <div>
                    <Button @click="handleShutDownOrder">关闭</Button>
                    <Button type="success" @click="handleConfirmSuccess">确认</Button>
                </div>
                <!--<div style="margin-top:16px;">-->
                <!--<span style="color:#AAA;">5分钟后未确认会取消该订单</span>-->
                <!--</div>-->
            </div>
        </Modal>

    </div>

</template>
<script>
  import cardInfo from 'components/member/cardInfo'
  import contractInfo from 'components/member/contractInfo'
  import OnlinePay from 'components/onlinePay/onlinePayAlert'
  import receiptModal from 'components/receipt/receipt.vue';
  import rejectOrder from 'components/member/RejectOrder.vue';
  import receipt from 'mixins/receipt.js'
  import {mapState, mapActions} from 'vuex'
  import {
    objectMerge
  } from "utils";

  export default {
    name: 'buyCard',
    mixins: [receipt],
    components: {
      cardInfo,
      rejectOrder,
      contractInfo,
      OnlinePay,
      receiptModal
    },
    created() {
      this.userId = this.$route.params.userId;
      this.belongBusId = this.$store.state.busId || '';
      this.getMerchantsBusList();
      if (this.$route.query.checkOrder) {
        this.isPreOrder = true;
        this.getPreOrder();
      }
      if (this.$route.query.coachClass === 'true') {
        this.isNormal = false
        this.isPt = true
        this.isSwim = true
      }
      this.getIsOpenDebtCardPay();
      this.getCanEditFields();
      this.getCardOverRuleAuth() // 规则范围可超出权限校验
    },
    computed: {
      ...mapState(['busId'])
    },
    data() {
      return {
        //验证码弹框
        isOpenDebtCardPay: false,
        showVerify: false,
        verifyCode: '',
        verifyId: null,
        finalData: {},

        belongBusId: '',
        merchantsBusList: [],
        isPreOrder: false, // 是否核单
        isNormal: true,
        isPt: false,
        isSwim: false,
        isEditFlag: false,
        loadingFlag: false,
        userId: '',
        formData: {},
        onlinePay: false,
        showModal: false,
        closable: true,
        selectedCard: {
          card_type_id: 1,
        },
        privateTotalAmount: 0, // 私教课通过单价和节数计算的合同金额
        coachList: '',
        coachCur: '',
        cardData: {},
        actionType: 'add', // actionType分为 add change edit reNew suspend switch,对应购卡 升卡  编辑 续卡 请假 转卡
        showRejectModal: false,
        overRuleAuth: false, // 规则范围可超出权限
      }
    },
    methods: {
      ...mapActions(['getCanEditFields']),
      // 规则范围可超出权限校验
      getCardOverRuleAuth() {
        this.$service.post('/Web/CardSaleRule/over_rule_auth').then(res => {
          if (res.data.errorcode === 0) {
            this.overRuleAuth = true;
          } else if (res.data.errorcode === 40014) {
            this.overRuleAuth = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      getIsOpenDebtCardPay() {
        this.$service.get('/Web/Commodity/get_setting').then(res => {
          if (res.data.errorcode == 0) {
            this.isOpenDebtCardPay = res.data.data.open_confirm == 1 ? true : false;
          } else {
            this.$Message.error(res.data.message)
          }
        })
      },
      sendVerifyCode() {
        if (this.verifyId) {
          this.$service.post('/Confirm/send_sms_code', {'id': this.verifyId}).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success("短信发送成功");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      },
      handleShutDownOrder() {
        this.showVerify = false;
        this.verifyCode = '';
        this.loadingFlag = false;
      },
      async handleConfirmSuccess() {
        //先验证验证码是否正确，在提交数据
        this.$service.post('/Confirm/post_confirm', {'id': this.verifyId, 'code': this.verifyCode}).then(res => {
          if (res.data.errorcode === 0) {
            this.postFinalData(this.finalData);
          } else {
            this.$Message.error('验证码错误');
          }
        });
      },
      cancelChangeCard() {
        this.belongBusId = this.busId;
        this.isEditFlag = false
      },
      handleEdit() {
        this.$Modal.confirm({
          title: '提示',
          content: '此操作可代售其他场馆的会员卡，是否继续?',
          onOk: () => {
            this.isEditFlag = true;
          },
          onCancel() {
          }
        });
      },
      getMerchantsBusList() {
        this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
          if (res.data.errorcode === 0) {
            this.merchantsBusList = res.data.data
          } else {
            this.$Message.success(res.data.errormsg);
          }
        });
      },
      receiptUpdate() {
        this.$router.back();
      },
      async handleOnlinePay() {
        let formReturn = await this.checkForm();
        if (formReturn.valid) {
          this.verifyCardSn(formReturn.postData).then(() => {
            // 显示在线支付弹窗
            this.showModal = true
          })
        }
      },

      async handleSubmit() {
        if (this.loadingFlag) {
          return false;
        }
        this.loadingFlag = true;
        let formReturn = await this.checkForm();
        if (formReturn.valid) {
          //是否选择了储值卡支付
          const hasPayTypeEight = formReturn.postData.new_pay_type.some(obj => obj.pay_type === 8 && obj.card_user_id);
          if (hasPayTypeEight) {
            this.finalData = formReturn.postData;
            if(this.isOpenDebtCardPay){
              this.showVerify = true;
              //请求验证码
              let sendData = {
                type: 1,
                data: this.finalData,
                confirm_type: 1,
                user_id: this.userId,
                amount: this.finalData.amount
              };
              this.$service.post('/Confirm/create_order', sendData).then(res => {
                if (res.data.errorcode === 0) {
                  this.verifyId = res.data.data.id;
                  this.sendVerifyCode();
                } else {
                  this.$Message.success(res.data.errormsg);
                  this.loadingFlag = false;
                }
              });
            }else{
              this.postFinalData(formReturn.postData);
            }
          } else {
            this.postFinalData(formReturn.postData);
          }
        } else {
          this.$Message.error( formReturn.errMsg || '请先正确填写数据！');
          this.loadingFlag = false;
        }
      },
      postFinalData(data) {
        if (this.belongBusId && this.isEditFlag) {
          data.help_sale_bus_id = this.belongBusId
        }
        data.over_rule_auth = this.overRuleAuth ? 1 : 0; // 范围可超出的权限,有权限传1，没得传0或者空

        this.$service.post(`/Web/Member/${this.$route.query.preOrdId?'check_pre_bill':'buyCard'}`, data)
            .then(response => {
              if (response.data.errorcode === 0) {
                // this.$Message.success(response.data.errormsg);
                let contractType = 'buycard'
                let teamclassType = 0 // 1泳教班 2私教班
                const cardId = data.card_id
                const cardName = this.selectedCard.card_name
                switch (+data.card_type_id) {
                  case 4:
                    contractType = 'buycard-coach';
                    teamclassType = 2
                    break;
                  case 5:
                    contractType = 'buycard-swim';
                    teamclassType = 1
                    break;
                  case 6:
                    contractType = 'buypackage';
                    break;
                }
                this.contractComplete(data.user_id, response.data.card_order_info_id, contractType, teamclassType, cardId, cardName);
              } else {
                this.$Message.error(response.data.errormsg);
              }
              this.loadingFlag = false;
            })
            .catch((response) => {
              this.loadingFlag = false;
            });
      },
      verifyCardSn(data) {
        let that = this;
        return new Promise((resolve, reject) => {
          let postData = {
            card_sn: data.card_sn
          };
          this.$service.post('/Web/Onlinepay/Verification_card_sn', postData)
              .then(function (response) {
                if (response.data.errorcode !== 0) {
                  that.$Message.error('实体卡号已存在');
                  reject();
                  return false
                }
                resolve()
              })
              .catch(function (error) {
                console.log(error);
              });
        })
      },
      async checkForm() {
        let cardInfoData = this.$refs.cardComponent.handleSubmitClick();
        // let purchase_volume = cardInfoData.purchase_volume;
        let contractInfoData = await this.$refs.contractComponent.handleSubmitClick()
        let valid = cardInfoData.valid && contractInfoData.valid;
        // 校验购卡范围权限及范围, false 规则范围不可超出
        // console.log('checkForm', cardInfoData, contractInfoData);
        if(this.overRuleAuth === false) {
          const { purchase_volume, gift_volume } = cardInfoData.postData;
          const { amount } = contractInfoData.postData;
          const isOK = this.checkCardRules(valid, purchase_volume, gift_volume, amount)
          if(isOK !== 'ok') return { valid: false, errMsg: isOK };
        }

        let postData = objectMerge(cardInfoData.postData, contractInfoData.postData);
        postData.user_id = this.userId;
        postData.pre_id = this.$route.query.preOrdId;
        valid && (this.formData = postData);

        return {valid, postData}
      },
      /* 校验购卡范围权限及范围 */
      checkCardRules(valid, purchase_volume, gift_volume, amount) {
        const { selectedCard: selected } = this;
        let errMsg = 'ok';
        if(valid && selected && selected.is_open_rule === '1' && selected.get_range && selected.card_type_id != '6') {
          const {
            card_type_id,
            is_pt_time_limit_card,
            get_range: { buy_max, gift_max, sale_range }
          } = selected;
          const index = is_pt_time_limit_card == '1' ? 5 : +card_type_id - 1;
          const tips = [
            ['购买天数', '购买次数','价值金额', '购买天数', '购买天数', '购买天数'],
            ['赠送天数', '赠送次数','赠送金额', '赠送节数', '赠送节数', '赠送天数'],
            ['售价', '售价', '售价', '单节售价', '单节售价', '售价'],
          ];

          if(buy_max !== '' && purchase_volume > +buy_max) { // '' 代表无限制
            errMsg = tips[0][index] + '不能大于上限！';
          }else if(gift_max !== '' && gift_volume > +gift_max) { // '' 代表无限制
            errMsg = tips[1][index] + '不能大于上限！';
          }else if(!Array.isArray(sale_range)) { // [] 代表无限制
            const { max, min } = sale_range;

            if((card_type_id == '4' || card_type_id == '5') && is_pt_time_limit_card != '1' ) {
              if((amount / purchase_volume).toFixed(2) > +max || (amount / purchase_volume).toFixed(2) < +min) {
                errMsg = tips[2][index] + '不在浮动范围内！';
              }
            }else {
              if(amount > +max || amount < +min) {
                errMsg = tips[2][index] + '不在浮动范围内！';
              }
            }
          }
        }
        return errMsg
      },
      payTypeChanged(val) {
        val = parseInt(val);
        // this.onlinePay = (val === 1 || val === 2)
      },
      handleReset() {
        this.$refs.cardComponent.handleReset();
        this.$refs.contractComponent.handleReset();
      },
      getPreOrder() {
        return this.$service.post('/Web/preBilling/orderInfo', {
          id: this.$route.query.preOrdId,
          user_id: this.$route.query.userId
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.cardData = res.data.data.info;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      }
    },
  }
</script>

<style lang="less" scoped>
    .buttons {
        display: flex;
        justify-content: space-around;
        margin-top: 35px;
    }

    .ivu-btn {
        width: auto;
        // margin: 0;
    }
</style>
