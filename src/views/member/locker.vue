<template>
    <div class="customized-tabs">
        <Tabs v-model="activeIndex" :animated="true">
            <TabPane label="储物柜出租">
                <rent></rent>
            </TabPane>
            <TabPane label="出租记录">
                <record></record>
            </TabPane>
        </Tabs>
    </div>
</template>

<script>
    import rent from './components/RentLocker'
    import record from './components/LockerRentRecord'

    export default {
        name: "locker",
        components: {
            rent,
            record
        },
        data() {
            return {
                activeIndex: ''
            }
        }
    }
</script>