<template>
<div>
  <div class="table-wrap father-table">
    <Menu mode="horizontal" :active-name="curMenu" @on-select="showMenuContent" v-if="curMenu !== 'search'">
        <MenuItem v-for="cur in curMenuObj" :key="cur" :name="cur">
          {{cur}}
          <Tooltip v-if="cur==='其它门店会员'">
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <p>归属门店是其他门店，但能在本店进行用卡</p>
              <p>训练的会员，例如通卡会员</p>
            </div>
          </Tooltip>
        </MenuItem>
    </Menu>
    <div class="badge-wrap" v-if="curMenu !== 'search' && fastMenuObj[curMenu]">
      <span class="badge-tit">快捷筛选</span>
      <span v-for="(cur,index) in fastMenuObj[curMenu].content"  :key="cur" class="badge-label" :class="{'cur-label': fastMenu === cur}" @click="showFastMenuContent(cur)">{{cur}}</span>
      <Dropdown class="badge-more" trigger="click" @on-click="showFastMenuContent">
        <a href="javascript:void(0)" :style="{ color: fastMenuObj[curMenu].more.indexOf(fastMenu) !== -1 ? '#2D8cF0' : '#515a6e' }">
            {{ fastMenuObj[curMenu].more.indexOf(fastMenu) !== -1 ? fastMenu : '更多快捷筛选'}}
            <Icon type="ios-arrow-down"></Icon>
        </a>
        <DropdownMenu slot="list">
            <DropdownItem v-for="(cur,index) in fastMenuObj[curMenu].more"  :key="cur" :name="cur" :style="{ color: cur === fastMenu ? '#2D8cF0' : '#515a6e' }">{{cur}}</DropdownItem>
        </DropdownMenu>
    </Dropdown>
    <Dropdown v-if="memberGroupList && memberGroupList.length" class="badge-more" style="marginLeft:15px" trigger="click" @on-click="showGroupMenuContent">
        <a href="javascript:void(0)" :style="{ color: memberGroupTitles.indexOf(fastMenu) !== -1 ? '#2D8cF0' : '#515a6e' }">
            {{ memberGroupTitles.indexOf(fastMenu) !== -1 ? fastMenu : '自定义人群'}}
            <Icon type="ios-arrow-down"></Icon>
        </a>
        <DropdownMenu slot="list">
            <DropdownItem v-for="cur in memberGroupList"  :key="cur.id" :name="JSON.stringify(cur)" :style="{ color: cur.title === fastMenu ? '#2D8cF0' : '#515a6e' }">{{cur.title}}</DropdownItem>
        </DropdownMenu>
    </Dropdown>
    </div>
    <MemberTable v-if="curMenu==='search'" :cur-menu="curMenu" :fast-menu="fastMenu" />
    <div v-else>
      <keep-alive v-for="cur in curMenuObj" :key="cur">
        <MemberTable :cur-menu="curMenu" :fast-menu="fastMenu" :group-params="groupParams" @on-fast-reset="fastMenuReset" @on-bus-change="onBusChange" @on-fast-change="onFastChange" v-if="curMenu === cur" />
      </keep-alive>
    </div>

    </div>
  </div>
</div>
</template>

<script>
import ExperienceCards from 'views/member/components/ExperienceCards';
import MemberTable from 'views/member/components/MemberTable';
import { mapState } from 'vuex'

export default {
  name: 'MemberIndex',
  computed: {
    ...mapState(['busId']),
  },
  data() {
    return {
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      memberGroupList: [],
      memberGroupTitles: [],
      groupParams: null,
      otherBusUser: true,//是否显示其它门店会员
      curMenu: '', // 当前选中的快捷筛选项
      fastMenu: '', // 当前选中的快捷筛选项
      curMenuObj:['会籍会员', '私教会员', '泳教会员', '其它门店会员'],//隐藏未列出选项为searc模式 此时curMenu为search
      fastMenuObj: {
        '会籍会员': {
          content: ['有效会籍会员','会籍潜客','会籍过期会员','次卡耗尽会员','无会籍跟进潜客'],
          more: ['即将会籍到期','会籍次数即将耗尽','储值卡即将耗尽','会员即将过生日','长期未到场会员','会籍长期未跟进']
        },
        '私教会员': {
          content: ['有效私教会员','私教潜客','过期私教会员','私教节数耗尽会员','无教练跟进私教潜客'],
          more: ['即将私教到期','私教节数即将耗尽','长期未到场私教会员','私教长期未跟进']
        },
        '泳教会员': {
          content: ['有效泳教会员','泳教潜客','过期泳教会员','泳教节数耗尽会员','无教练跟进泳教潜客'],
          more: ['即将泳教到期','泳教节数即将耗尽','长期未到场泳教会员','泳教长期未跟进']
        }
      }
    };
  },
  watch: {
    $route(val,oldVal){
      if(val.query.curMenu!=oldVal.query.curMenu){
        this.curMenu = val.query.curMenu ? val.query.curMenu : sessionStorage.getItem('curMenuNew') ? sessionStorage.getItem('curMenuNew') : '会籍会员'
        this.fastMenu = val.query.fastMenu ? val.query.fastMenu : sessionStorage.getItem('fastMenuNew') ? sessionStorage.getItem('fastMenuNew') : ''
      }
    },
    curMenu(val) {
      if(val!=='search') {
        sessionStorage.setItem('curMenuNew', val);
      }
    },
    fastMenu(val) {
      if(val!=='search') {
        sessionStorage.setItem('fastMenuNew', val);
      }
    }
  },
  async created() {
    if (this.IS_BRAND_SITE) {
      this.curMenuObj = ['会籍会员', '私教会员', '泳教会员']
    }
    const fast = this.$route.query.fastMenu ? this.$route.query.fastMenu : sessionStorage.getItem('fastMenuNew') ? sessionStorage.getItem('fastMenuNew') : ''
    if(this.$route.name === '会员管理'){
      // 参数中传入了会员分群的 默认为本地场馆id
      await this.getGroupList(fast?this.busId:'')
      this.checkExpAndLockerent()
    }
    this.onFastChange(fast)
    this.curMenu = this.$route.query.curMenu ? this.$route.query.curMenu : sessionStorage.getItem('curMenuNew') ? sessionStorage.getItem('curMenuNew') : '会籍会员'
    this.fastMenu = fast
  },
  beforeDestroy() {
    if(this.memberGroupTitles.indexOf(this.fastMenu) !== -1) {
      sessionStorage.setItem('fastMenuNew', '');
    }
  },
  components: {
    ExperienceCards,
    MemberTable,
  },
  methods: {
    onBusChange(busId) {
      this.getGroupList(busId)
    },
    onFastChange(fast) {
      if(this.memberGroupTitles.indexOf(fast) !== -1) {
        for (const item of this.memberGroupList) {
          if(item.title === fast) {
            this.showGroupMenuContent(JSON.stringify(item))
            break;
          }
        }
      }
    },
    showMenuContent(name) {
      this.curMenu = name;
      this.fastMenu = '';
    },
    showFastMenuContent(name) {
      this.fastMenu = name;
      this.groupParams = null
    },
    showGroupMenuContent(info) {
      info = JSON.parse(info)
      this.fastMenu = info.title;
      this.groupParams = info.params
    },
    fastMenuReset() {
      this.fastMenu =''
    },
    getGroupList(id) {
      if(!id) {
        this.memberGroupList = []
        this.memberGroupTitles = []
        return;
      }
      return this.$service.post('/Web/MemberGroup/list',{
        page_no: 1,
        page_size: 99,
        bus_id: id || '',
        type: 1
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.memberGroupList = res.data.data.list
          this.memberGroupTitles= res.data.data.list.map(item=>item.title)
        } else if(res.data.errormsg && res.data.errormsg !== '没有会员分群权限'){
          this.$Message.error(res.data.errormsg)
        }
      });
    },
    checkExpAndLockerent() {
      this.$service.get('/Web/MemberList/check_member_list').then(res => {
        if (res.data.errorcode == 0) {
          let data = res.data.data
          this.otherBusUser = res.data.order_bus_user
        } else {
          this.$Message.error(res.data.errormsg)
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.father-table .table-wrap{
  border: none;
}
.badge-wrap{
  display: flex;
  align-items: center;
  height: 80px;
  padding: 0 35px;
  width: 100%;
  margin-bottom: -15px;
}
.badge-tit {
  padding-left: 10px;
  border-left: 3px solid #52a4ea;
  margin-right: 25px;
}
.badge-label{
  margin-right: 35px;
  cursor: pointer;
}
.badge-wrap .badge-label:hover,.cur-label.badge-label{
  color: #2d8cf0;
}
.badge-more {

}
</style>
