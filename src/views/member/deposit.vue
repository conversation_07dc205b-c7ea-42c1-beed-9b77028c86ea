<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs" v-model="activeIndex">
      <TabPane label="定金" name="0">
       <DepositTable />
      </TabPane>
      <TabPane label="押金" name="1">
        <CashTable />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import DepositTable from './components/DepositTable.vue'
import CashTable from './components/CashTable.vue'
import receiptModal from 'components/receipt/receipt.vue';
import receipt from 'mixins/receipt.js';
  export default {
    name: 'deposit',
    mixins: [receipt],
    components: {DepositTable, CashTable, receiptModal},
    data() {
      return {
        activeIndex: 0
      }
    },
    methods: {
      clickTabs(index) {
        const active = document.querySelector('.ivu-tabs-ink-bar')
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`
        active.setAttribute('class', className)
      }
    }
  }
</script>

<style lang="less">

</style>
