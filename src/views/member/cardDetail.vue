<template>
  <Card class="container">
    <p slot="title">详情</p>
    <a slot="extra">
      <router-link v-if="showEdit" :to="{ path: `/member/card/save/${card.card_id}/${card.card_type}`}">编辑</router-link>
    </a>

    <Form class="form" :label-width="200">
      <FormItem label="会员卡名称">{{ card.name }}</FormItem>
      <FormItem label="会员卡类型">{{ card.card_type_name }}</FormItem>
      <template v-if="card.card_type_id != 4">
        <FormItem label="有效时间">{{ card.end_time == 0 ? '永久有效' : `${card.end_time}天` }}</FormItem>
        <FormItem label="售价">{{ card.current_price }}元</FormItem>
        <FormItem label="次数" v-if="card.card_type_id == 2">{{ card.number }}</FormItem>
        <FormItem label="储值卡金额" v-if="card.card_type_id == 3">{{ card.number }}</FormItem>
        <FormItem label="会员端在线购买">{{ card.phone_pay }}</FormItem>
      </template>
      <template v-else>
        <FormItem label="单节时长">{{ `${card.class_duration}分钟` }}</FormItem>
      </template>
      <FormItem label="时段限制" v-if="card.enable_time_limit == 1">
        <label v-for="(item, index) in card.weekTimes" :key="index" style="display: flex; align-items: center"><span>{{ item.weeksZh }} {{item.start_time}} ~ {{item.end_time}}</span></label>
      </FormItem>
      <FormItem label="使用场馆" v-if="card.universal_card == 1">{{ card.support_bus_name }}</FormItem>
      <FormItem label="会员卡描述">{{ card.description }}</FormItem>
    </Form>
  </Card>
</template>
<script>
  import { getResponseData } from '../../utils/index';
  import { mapState } from 'vuex';
  export default {
    data() {
      return {
        card: {},
        showEdit: false
      };
    },
    created() {
      this.getCardInfo().then(() => {
        if (!this.cardSettingAuth) {
          this.$router.push({ path: '/management/card' });
          return;
        }
        const { singleCard, multiCard, expCard, multiPtCard, swimCard } = this.cardSettingAuth;
        const { universal_card, experience_card, card_type_id } = this.card;
        this.showEdit =
          (multiCard && universal_card == 1 && card_type_id != 4) ||
          (multiCard && universal_card == 1 && card_type_id == 4 && multiPtCard) ||
          (expCard && experience_card == 1) ||
          (singleCard && experience_card == 0 && universal_card == 0);
      });
    },
    computed: {
      ...mapState(['cardSettingAuth'])
    },
    methods: {
      getCardInfo() {
        return this.$service.get(`/Web/Card/get_card_detail/id/${this.$route.params.card_id}`).then(res => {
          const data = getResponseData(res, {});

          let cardTypeName = '';
          switch (data.card_type_id) {
            case '1':
              cardTypeName = '期限卡';
              break;
            case '2':
              cardTypeName = '次卡';
              break;
            case '3':
              cardTypeName = '储值卡';
              break;
            case '4':
              cardTypeName = '私教卡';
              break;

            default:
              cardTypeName = '';
              break;
          }
          if (data.universal_card == 1) {
            data.card_type_name = `多店通用卡-${cardTypeName}`;
          } else {
            data.card_type_name = cardTypeName;
          }

          if (data.status == 1 && data.experience_card != 1) {
            data.phone_pay = '支持';
          } else {
            data.phone_pay = '不支持';
          }

          this.card = data;
        });
      }
    }
  };
</script>

<style lang="less">
  .row-line {
    margin: 10px 0;
  }

  .desc {
    text-indent: 2em;
    word-wrap: break-word;
  }

  .field {
    text-align: right;
  }

  .container .form {
    padding-bottom: 40px;
  }
</style>

<style scoped>
  .ivu-form-item {
    margin-bottom: 0;
  }
</style>
