<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>编辑卡</h2>
    </div>
    <div class="form-box-con">
      <Form ref="cardForm" :label-width="120">
        <Form-item label="修改记录" v-if="cardData.operating_record_count > 0">
          <span class="record-intr">有
            <em class="record-no" @click="openeditRecord">{{cardData.operating_record_count}}</em>条修改记录</span>
          <i class="record-icon" @click="openeditRecord"></i>
        </Form-item>
        <Modal v-model="ershow" title="操作记录" width="570" :loading="false">
          <edit-record v-if="this.cardorder_info_id" :ershow='ershow' :userid="userId" :orderSn="cardData.order_sn"></edit-record>
          <edit-record v-else :ershow='ershow' :userid="userId" :carduserid="cardUserId"></edit-record>
          <div slot="footer">
          </div>
        </Modal>
        <card-Info ref="cardComponent" actionType="edit" :cardData="cardData"  :cardTypeId="selectedCard.card_type_id" :selectedCard.sync="selectedCard" :coachList.sync="coachList" :coachCur.sync="coachCur" @onPrivateNumChange="amount => privateTotalAmount = amount" />
        <contract-Info
          ref="contractComponent"
          actionType="edit"
          :counterparts="counterparts"
          :contractData='cardData'
          :selectedCard="selectedCard"
          :coachList.sync="coachList"
          :coachCur.sync="coachCur"
          :privateTotalAmount="privateTotalAmount"
          :hasStore="hasStore"
          :isStore="isStore"/>
        <Form-item>
          <div class="buttons">
            <Button type="primary" :loading="loadingFlag" @click="handleSubmit">提交</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>
<script>
  import { computed } from 'vue';
  import cardInfo from 'components/member/cardInfo';
  import contractInfo from 'components/member/contractInfo';
  import editRecord from 'components/member/editRecord';
  import { objectMerge } from 'utils';
  import EventBus from 'components/EventBus.js'
  export default {
    name: 'editCard',
    components: {
      cardInfo,
      contractInfo,
      editRecord
    },
    created() {
      this.userId = this.$route.params.userId;
      this.cardUserId = this.$route.params.cardUserId;
      this.cardorder_info_id = this.$route.params.cardorder_info_id;
      this.getCardEditInfo();
    },
    provide() {
      return {
        sqbServInfo: computed(() => this.sqbServInfo)
      }
    },
    data() {
      return {
        privateTotalAmount: '',
        userId: '',
        cardUserId: '',
        cardorder_info_id: '',
        cardData: '',
        formData: '',
        ershow: false,
        loadingFlag: false,
        selectedCard: {
          card_type_id: 1
        },
        coachList: '',
        sqbServInfo: null,
        coachCur: '',
        hasStore: false,
        isStore: false,
        counterparts: false
      };
    },
    watch: {
      selectedCard(val) {
        this.counterparts =
          val.card_type_id == this.cardData.card_type_id && val.card_type_id == 4
      }
    },
    methods: {
      getCardEditInfo() {
        let postData = {
          card_user_id: this.cardUserId,
          user_id: this.userId,
          cardorder_info_id: this.cardorder_info_id
        };
        this.$service.post('/Web/Member/get_card_user_edit_info', postData).then(res => {
          if (res.data.errorcode === 0) {
            this.cardData = res.data.data.info;
            this.selectedCard.card_type_id = res.data.data.info.card_type_id
            this.sqbServInfo = {
              serv_type: 1,
              serv_id: this.cardData.cardorder_info_id
            }

            if (['销卡', '请假', '转卡（出）', '转卡（入）', '补卡', '拆分'].includes(this.cardData.order_name)) {
              this.hasStore = true
              if (this.cardData.marketer_category == 3) {
                this.isStore = true
              }
            }
          } else {
            this.$Message.success(res.data.errormsg);
          }
        });
      },
      async handleSubmit() {
        if (this.loadingFlag) {
          return false;
        }
        this.loadingFlag = true
        let cardInfoData = this.$refs.cardComponent.handleSubmitClick();
        let contractInfoData = await this.$refs.contractComponent.handleSubmitClick();
        if (!this.cardData.is_order_info) {
          contractInfoData = {
            valid: true,
            postData: {
              edit_reason: contractInfoData.postData.edit_reason,
              amount: contractInfoData.postData.amount
            }
          };
        }
        let postData = objectMerge(cardInfoData.postData, contractInfoData.postData);

        postData.user_id = this.userId;
        postData.card_user_id = this.cardUserId;
        postData.is_order_info = this.cardData.is_order_info;
        // fix: 12547 编辑期限卡时，如果剩余天数小于等于0时不允许提交编辑成功
        if (postData.card_type_id == 1) {
          if (postData.last_volume <= 0) {
            this.loadingFlag = false
            return this.$Message.error('剩余量必须大于0');
          }
        } else if (postData.last_volume < 0) {
          this.loadingFlag = false
          return this.$Message.error('剩余量必须大于等于0');
        }
        if (cardInfoData.valid && contractInfoData.valid) {
          this.$service.post('/Web/MemberCard/editCard', postData).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.$router.back();
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.loadingFlag = false
          });
        } else {
          this.$Message.error('请先正确填写数据！');
          this.loadingFlag = false
        }
      },
      openeditRecord() {
        this.ershow = true;
      }
    }
  };
</script>
