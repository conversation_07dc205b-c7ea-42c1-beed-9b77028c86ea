<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>请假</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="130">
        <Form-item label="会员" prop="cardinfo" v-if="!cardUserId">
          {{suspendInfo.username}}（{{suspendInfo.phone}}）
        </Form-item>

        <form-item label="请假类型">
          <RadioGroup v-model="formCustom.suspend_type" @on-change="changeSuspendType">
            <Radio :label="1">正常请假</Radio>
            <Radio :label="2">特殊请假</Radio>
          </RadioGroup>
        </form-item>
        <!-- <Form-item label="请假记录" v-if="cardinfo.user_stop_count && cardUserId">
          <span class="record-intr">
            该会员共请假
            <em class="record-no" @click="openeditRecord">{{cardinfo.user_stop_count}}</em>次
            该卡请假
            <em class="record-no" @click="openeditRecord">{{cardinfo.card_stop_count}}</em>次
        </span>
          <i class="record-icon" @click="openeditRecord"></i>
        </Form-item> -->
        <Form-item label="会员卡" prop="cardinfo" v-if="cardUserId">
          {{cardinfo.card_name}}
          <span v-if="cardinfo.card_sn">(卡号：{{cardinfo.card_sn}})</span>
        </Form-item>
        <Form-item label="请假">
          <span class="record-intr">
            该会员已请假
            <em class="record-no" @click="openUserSuspendLog">{{suspendInfo.used_suspend_number}}</em>次
            共计
            <em class="record-no" @click="openUserSuspendLog">{{suspendInfo.used_suspend_day}}</em>天
        </span>
          <span class="record-intr">
            剩余
            <em class="record-no">{{suspendInfo.suspend_number}}</em>次
            共计
            <em class="record-no">{{suspendInfo.suspend_day}}</em>天
        </span>
        </Form-item>
        <Modal v-model="ershow" title="请假记录" width="570" :loading="false">
          <edit-record :ershow='ershow' :userid="userId" type="请假"></edit-record>
          <div slot="footer">
          </div>
        </Modal>
        <Modal v-model="userSuspendLog" title="请假记录" width="570" :loading="false">
          <div class="contentbox">
            <recordDetail :data="userSuspendLogList"></recordDetail>
          </div>
          <div slot="footer">
          </div>
        </Modal>
        <Form-item label="请假周期" prop="offperiod">
          <Radio-group v-model="formCustom.offperiod" @on-change="changePeriod">
            <Radio label="15">15天</Radio>
            <Radio label="30">30天</Radio>
            <Radio label="45">45天</Radio>
            <Radio label="60">60天</Radio>
            <Radio label="90">90天</Radio>
            <Radio label="自定义周期">自定义周期</Radio>
            <Radio :disabled="suspendInfo.suspend_limit == 1 && formCustom.suspend_type != 2" label="不确定">不确定</Radio>
          </Radio-group>
        </Form-item>
        <Form-item label="请假开始时间" prop="start_date">
          <Date-picker type="date" :clearable="false" :editable="false" @on-change="handleStartDateChange" :value="formCustom.start_date" format="yyyy-MM-dd" placeholder="请选择请假开始日期"></Date-picker>
        </Form-item>
        <Form-item label="请假结束时间" prop="off_enddate" v-if="formCustom.offperiod!='不确定'">
          <template v-if="formCustom.offperiod!='自定义周期'">
            {{formCustom.off_enddate}}
          </template>
          <Date-picker type="date" @on-change="handledateChange" :value="formCustom.off_enddate" format="yyyy-MM-dd" placeholder="请选择请假结束日期" v-else></Date-picker>
        </Form-item>
        <form-item v-if="formCustom.suspend_type == 2" label="附件" required :show-message="false" prop="image_list">
          <div class="img-upload">
            <ImgData ref="imgData" suspend />
          </div>
        </form-item>
        <template v-if="formCustom.suspend_type == 2">
          <Form-item label="成交时间" prop="deal_time" :rules="{ required: true, message: '请选择成交时间'}">
            <Date-picker type="date" :value="formCustom.deal_time" :editable="false" placeholder="合同成交时间"></Date-picker>
          </Form-item>
          <form-item label="备注">
            <Input v-model="formCustom.remark" type="textarea" :autosize="{minRows: 3,maxRows: 5}" placeholder="请填写特殊请假原因" />
          </form-item>
        </template>
        <contract-Info
          v-if="formCustom.suspend_type != 2"
          ref="contractComponent"
          actionType="suspend"
          :contractData='cardinfo'
          :selectedCard="selectedCard"
          :labelWidth="130"
          :hasStore="true" />
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit('formCustom')">保存</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
    <receipt-modal v-model="showPrint" :to-path='toPath' @on-receiptupdate="receiptUpdate" />
  </div>
</template>
<script>
  import { getcardInfo } from '../../service/getData.js';
  import contractInfo from 'components/member/contractInfo';
  import { formatDate } from 'utils';
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js';
  import editRecord from 'components/member/editRecord';
  import recordDetail from 'components/member/recordDetail.vue';
  import  EventBus from 'components/EventBus.js';
  import ImgData from './detail/components/imageData.vue'

  export default {
    mixins: [receipt],
    created() {
      this.caldate('15');
      this.getCardInfo();
      this.getSuspendInfo(this.userId);
    },
    beforeDestroy () {
      EventBus.$off('on-suspend-change')
    },
    data() {
      return {
        userSuspendLog: false,
        userSuspendLogList: null,
        suspendInfo: {
          used_suspend_number: '0',
          used_suspend_day: '0',
          suspend_number: '0',
          suspend_day: '0',
          suspend_limit: 0
        },
        ershow: false,
        userId: this.$route.params.userId,
        cardUserId: this.$route.params.cardUserId || '',
        coachList: '',
        selectedCard: {
          card_type_id: ''
        },
        fromToday: {
          disabledDate(date) {
            return date && date.getTime() < Date.now() - 24 * 3600 * 1000;
          }
        },
        formCustom: {
          image_list: [],
          suspend_type: 1,
          charges: '0',
          paytype: '',
          remark: '',
          offperiod: '15',
          start_date: formatDate(new Date(), 'yyyy-MM-dd'),
          off_enddate: '',
          deal_time: formatDate(new Date(), 'yyyy-MM-dd'),
          manual: false
        },
        cardinfo: {},
        ruleCustom: {
          off_enddate: [
            {
              required: true,
              message: '请选择正确的请假结束日期',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    components: {
      contractInfo,
      editRecord,
      receiptModal,
      recordDetail,
      ImgData
    },
    watch: {
      userSuspendLog (val) {
        if (val) {
          this.getUserSuspendLog();
        }
      }
    },
    methods: {
      changeSuspendType(val) {
        if(val === 1 && this.formCustom.offperiod == '不确定') {
          this.formCustom.offperiod = '15'
          this.changePeriod('15')
        }
      },
      openUserSuspendLog () {
        this.userSuspendLog = true;
      },
      getUserSuspendLog () {
        const url = '/Web/OperatingRecord/user_suspend_operating_record_log';
        return this.$service.post(url, {
          user_id: this.userId
        }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.userSuspendLogList = res.data.data.list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            console.error("服务器扑街！");
          }
        }).catch(err => {
          console.error(err);
        })
      },
      getSuspendInfo (user_id) {
        let url = '/Web/Member/get_user_suspend_info';
        this.$service.post(url, { user_id }).then(res => {
          if(res.status === 200) {
            if(res.data.errorcode === 0) {
              this.suspendInfo = { ...res.data.data.info };
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error("服务器扑街！")
          }
        }).catch(err => {
          console.error(err)
        })
      },
      openeditRecord() {
        this.ershow = true;
      },
      receiptUpdate() {
        this.$router.back();
      },
      handleStartDateChange(e) {
        const day = Math.ceil(
          (new Date(e).getTime() - new Date(this.formCustom.start_date).getTime()) / (24 * 3600 * 1000)
        );
        this.formCustom.start_date = e;
        if (this.formCustom.off_enddate) {
          const utilDay = new Date(this.formCustom.off_enddate).getTime() + day * 24 * 3600 * 1000;
          this.formCustom.off_enddate = formatDate(new Date(utilDay), 'yyyy-MM-dd');
        }
      },
      // 请假周期改变时，会员请假要自动更新售价
      emitPeriod(value) {
        let days;
        if (value === '自定义周期') {
          let start = new Date(this.formCustom.start_date).getTime();
          let end = new Date(this.formCustom.off_enddate).getTime();
          days = parseInt((end - start) / 86400000);
        } else if (value === '不确定') {
          days = 0;
        } else {
          days = parseInt(value)
        }
        days = days || 0;
        EventBus.$emit("on-suspend-change", days)
      },
      changePeriod(value) {
        switch (value) {
          case '自定义周期':
            this.formCustom.off_enddate = '';
            this.formCustom.manual = false;
            break;
          case '不确定':
            this.formCustom.off_enddate = '';
            this.formCustom.manual = true;
            break;
          default:
            this.caldate(value);
            this.formCustom.manual = false;
            break;
        }
        this.emitPeriod(value);
      },
      handledateChange(date) {
        if(date <= this.formCustom.start_date) {
            this.$Message.error("结束时间必须大于开始时间！");
            this.formCustom.off_enddate = "";
            return;
        } else {
            this.formCustom.off_enddate = date;
        }
        this.emitPeriod('自定义周期');
      },
      caldate(gap) {
        var ds = new Date(this.formCustom.start_date);
        ds.setDate(ds.getDate() + parseInt(gap));
        var year = ds.getFullYear();
        var month = ds.getMonth() + 1;
        var date = ds.getDate();
        if (String(month).length == 1) {
          month = '0' + month;
        }
        if (String(date).length == 1) {
          date = '0' + date;
        }
        this.formCustom.off_enddate = year + '-' + month + '-' + date;
      },
      getCardInfo() {
        if(!this.cardUserId) {
          return;
        }
        getcardInfo({
          card_user_id: this.cardUserId,
          user_id: this.userId
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.cardinfo = res.data.data.info;
            EventBus.$emit('on-pay-des-change', `请假[${this.cardinfo.card_name}]`)
            this.selectedCard.card_type_id = res.data.data.info.card_type;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      specialSuspendVldt() {
        if (this.formCustom.suspend_type == 2) {
          if (this.$refs.imgData.uploadListComputed.length === 0) {
            this.$Message.error('请上传附件')
            return false
          } else {
            this.formCustom.image_list = this.$refs.imgData.uploadListComputed.map(item => item.url)
            return true
          }
        } else {
          return true
        }
      },
      handleSubmit(name) {
        let result = this.specialSuspendVldt()
        if (!result) return

        this.$refs[name].validate(async valid => {
          let contractInfoData = this.$refs.contractComponent && await this.$refs.contractComponent.handleSubmitClick();
          if (this.formCustom.suspend_type == 1) {
            valid = contractInfoData.valid && valid;
          }
          if (valid) {
            if (this.formCustom.start_date < formatDate(new Date(), 'yyyy-MM-dd')) {
              this.$Modal.confirm({
                title: '请假确认',
                content: '请假开始时间早于当前时间，是否请假?',
                onOk: () => {
                 this.postService(contractInfoData)
                }
              });
            } else {
              this.postService(contractInfoData)
            }
          }
        });
      },
      postService(contractInfoData) {
        // 会员请假
        if(!this.cardUserId) {
          let postData = Object.create(null);
          if (!this.formCustom.manual) {
            if (this.formCustom.offperiod == '自定义周期') {
              postData.suspend_cycle = this.formCustom.off_enddate;
            } else {
              postData.suspend_cycle = this.formCustom.offperiod;
            }
          }
          if (this.formCustom.suspend_type == 1) {
            postData = Object.assign(postData, {
              suspend_type: this.formCustom.suspend_type,
              image_list: this.formCustom.image_list,
              user_id: this.userId,
              action: 1,
              front_ids: contractInfoData.postData.front_ids,
              amount: contractInfoData.postData.amount,
              income_amount: contractInfoData.postData.income_amount || 0, //实收金额,
              active_date: this.formCustom.off_enddate || this.formCustom.off_enddate,
              stop_date: this.formCustom.start_date,
              deal_date: contractInfoData.postData.deal_time,
              remark: contractInfoData.postData.remark || this.formCustom.remark,
              pay_type: contractInfoData.postData.pay_type,
              new_pay_type: contractInfoData.postData.new_pay_type,
              marketers_id:  contractInfoData.postData.marketers_id,
              marketer_category: contractInfoData.postData.marketer_category
            })
          } else if (this.formCustom.suspend_type == 2) {
            postData = Object.assign(postData, {
              suspend_type: this.formCustom.suspend_type,
              image_list: this.formCustom.image_list,
              user_id: this.userId,
              action: 1,
              active_date: this.formCustom.off_enddate || this.formCustom.off_enddate,
              stop_date: this.formCustom.start_date,
              deal_date: this.formCustom.deal_time,
              remark: this.formCustom.remark
            })
          }
          let url = '/Web/Member/user_suspend';
          this.$service.post(url, postData).then(res => {
            if(res.status === 200) {
              if(res.data.errorcode === 0) {
                this.contractComplete(postData.user_id, res.data.card_order_info_id, 'suspendcard');
                this.showPrint = true;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            } else {
              console.error("服务器扑街！")
            }
          }).catch(err => {
            console.error(err)
          })
          return;
        }
        // 会员卡请假
        let postData = Object.create(null)
        if (this.formCustom.suspend_type == 1) {
          // 普通请假
          postData = {
            marketers_id:  contractInfoData.postData.marketers_id,
            marketer_category: contractInfoData.postData.marketer_category,
            user_id: this.userId,
            card_user_id: this.cardUserId,
            front_ids: contractInfoData.postData.front_ids,
            stop_time: this.formCustom.start_date,
            income_amount: contractInfoData.postData.income_amount, //实收金额,
            price: contractInfoData.postData.amount,
            pay_type: contractInfoData.postData.pay_type,
            new_pay_type: contractInfoData.postData.new_pay_type,
            deal_time: contractInfoData.postData.deal_time,
            remark: contractInfoData.postData.remark
          };
        } else {
          // 特殊请假
          postData = {
            suspend_type: this.formCustom.suspend_type,
            image_list: this.formCustom.image_list,
            user_id: this.userId,
            card_user_id: this.cardUserId,
            stop_time: this.formCustom.start_date,
            deal_time: this.formCustom.deal_time,
            remark: this.formCustom.remark
          };
        }
          let url = '/Web/MemberCard/suspendCard';
          if (!this.formCustom.manual) {
            let gap;
            if (this.formCustom.offperiod == '自定义周期') {
              gap = this.formCustom.off_enddate;
            } else {
              gap = this.formCustom.offperiod;
            }
            postData.suspend_cycle = gap;
            // url = '/Web/MemberCard/auto_suspendCard';
          }
          this.$service.post(url, postData).then(res => {
            if (res.data.errorcode == 0) {
              this.contractComplete(postData.user_id, res.data.card_order_info_id, 'suspendcard');
              this.showPrint = true;
              // this.$Message.success(res.data.errormsg);
              // setTimeout(() => {
              //   this.$router.back();
              // }, 1000);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      }
    }
  };
</script>
<style lang="less" scoped>
.contentbox {
  width: 100%;
  max-height: 630px;
  overflow: auto;
}
.img-upload {
  max-width: 50%;
  max-height: 400px;
  overflow: scroll;
}
</style>
