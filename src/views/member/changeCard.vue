<template>
    <div class="form-box">
        <div class="form-box-title">
            <h2>{{isOtherBus?'跨店':''}}升卡</h2>
        </div>
        <div class="form-box-con">
            <Form ref="otherBusCardForm"
                  :model="otherBusInfo"
                  :label-width="140"
                  v-if="isOtherBus">
                <Form-item>
                    <div style="color:red">
                        该功能可以将其他门店的会员升级到本场馆下
                    </div>
                </Form-item>
                <Form-item label="所在门店"
                           prop="bus_id">
                    <Select v-model="otherBusInfo.bus_id"
                            placeholder="归属场馆"
                            filterable>
                        <Option v-for="option in merchantsBusList"
                                :disabled="busId == option.bus_id"
                                :value="option.bus_id"
                                :key="option.bus_id">{{ option.bus_name }}
                        </Option>
                    </Select>
                </Form-item>
                <Form-item label="会员"
                           prop="user_id"
                           v-if="otherBusInfo.bus_id">
                    <OtherBusUserSearch v-model="otherBusInfo.user_id"
                                        :bus-id="otherBusInfo.bus_id"
                                        @on-change="otherBusUserChange"/>
                </Form-item>
                <Form-item label="需要升级的卡"
                           prop="card_user_id"
                           v-if="cardList">
                    <Select v-model="otherBusInfo.card_user_id"
                            v-if="cardList.length>0"
                            @on-change="otherBusCardChange">
                        <Option v-for="card in cardList"
                                :key="card.card_user_id"
                                :value="card.card_user_id">
                            {{card.cardname}}
                            <template v-if="card.card_sn">
                                （卡号：{{card.card_sn}}）
                            </template>
                        </Option>
                    </Select>
                    <div style="color:red"
                         v-else>
                        该会员暂无可升级的会员卡
                    </div>
                </Form-item>
            </Form>
            <Form ref="cardForm"
                  :label-width="140">
                <Form-item label="当前会员卡"
                           v-if="cardData && !isOtherBus">
                    {{cardData.card_name}}
                    <template v-if="cardData.card_sn">
                        （卡号：{{cardData.card_sn}}）
                    </template>
                </Form-item>
                <Form-item label="当前教练"
                           v-if="cardData && (cardData.card_type_id == 4 || cardData.card_type_id==5)">
                    {{Array.isArray(cardData.coach_name)?cardData.coach_name.join(',') : cardData.coach_name}}
                </Form-item>
                <Form-item :label="'当前' + typeText"
                           v-if="cardData && cardData.card_type_id != 1 && cardData.is_pt_time_limit_card != 1">
                    总{{typeText}} {{cardData.card_type_id == 3 ? cardData.total : cardData.all_num}}，剩余{{typeText}}
                    {{cardData.last_num}}
                </Form-item>
                <Form-item label="剩余天数"
                           v-if="cardData.last_day">
                    {{cardData.last_day === '永久' ? cardData.last_day : cardData.last_day +'天'}}
                </Form-item>
                <LeftValue v-if="cardData"
                           :value="cardData.card_last_value"
                           :cardUserId="$route.params.cardUserId != 0?$route.params.cardUserId:otherBusInfo.card_user_id"
                           :bus-id="otherBusInfo.bus_id"/>
                <div v-if="cardData"
                     class="line-cross">
                    <span>请填写新的会员卡信息</span>
                </div>
                <card-Info ref="cardComponent"
                           v-if="isPreOrder?true: cardData"
                           :isPreOrder="isPreOrder"
                           actionType="change"
                           :selectedCard.sync="selectedCard"
                           :coachList.sync="coachList"
                           :coachCur.sync="coachCur"
                           :cardData="orderinfo"
                           @onPrivateNumChange="amount => privateTotalAmount = amount"
                           :isNormal="cardData.card_type_id != 4 && cardData.card_type_id != 5"
                           :isPt="cardData.card_type_id == 4"
                           :isSwim="cardData.card_type_id == 5"
                           />
                <contract-Info ref="contractComponent"
                               v-if="isPreOrder?true:cardData"
                               :isPreOrder="isPreOrder"
                               actionType="change"
                               :privateTotalAmount="privateTotalAmount"
                               :contractData='orderinfo'
                               :otherBusInfo="isOtherBus?otherBusInfo:null"
                               :selectedCard="selectedCard"
                               :coachList.sync="coachList"
                               :coachCur.sync="coachCur" />
                <Form-item v-if="cardData">
                    <div class="buttons">
                        <Button type="error"
                                style="border:none;"
                                v-if="isPreOrder"
                                @click="showRejectModal=true">退单
                        </Button>
                        <Button type="primary"
                                @click="handleOnlinePay"
                                v-if="onlinePay">在线支付并提交
                        </Button>
                        <Button type="primary"
                                :loading="loadingFlag"
                                @click="handleSubmit">提交
                        </Button>
                        <Button @click="$router.back()">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>
        <Modal v-model="showModal"
               title="收款"
               :closable="closable"
               :mask-closable="false">
            <OnlinePay v-if="showModal"
                       :data="formData"
                       v-on:closeModal="showModal = false"
                       v-on:enterPaying="closable = false"
                       v-on:leavePaying="closable = true"></OnlinePay>
            <span slot="footer"></span>
        </Modal>
        <Modal v-model="showVerify" title="请输入会员收到的短信验证码" :closable="false" :mask-closable="false">
            <Input v-model="verifyCode" placeholder="短信验证码">
                <Button slot="append" @click="sendVerifyCode">重发
                </Button>
            </Input>
            <div slot="footer" style="text-align:center;">
                <div>
                    <Button @click="handleShutDownOrder">关闭</Button>
                    <Button type="success" @click="handleConfirmSuccess">确认</Button>
                </div>
                <!--<div style="margin-top:16px;">-->
                <!--<span style="color:#AAA;">5分钟后未确认会取消该订单</span>-->
                <!--</div>-->
            </div>
        </Modal>
        <reject-order v-if="isPreOrder"
                      v-model="showRejectModal"
                      :id="$route.query.preOrdId"/>
        <receipt-modal v-model="showPrint"
                       :to-path='toPath'
                       @on-receiptupdate="receiptUpdate"/>
    </div>
</template>
<script>
  import { computed } from 'vue'
  import cardInfo from 'components/member/cardInfo';
  import contractInfo from 'components/member/contractInfo';
  import OtherBusUserSearch from './components/OtherBusUserSearch';
  import OnlinePay from 'components/onlinePay/onlinePayAlert';
  import {objectMerge} from 'utils';
  import LeftValue from './components/leftValue.vue';
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js'
  import rejectOrder from 'components/member/RejectOrder.vue'
  import {mapState} from 'vuex'
  import EventBus from 'components/EventBus.js'
  export default {
    name: 'changeCard',
    mixins: [receipt],
    components: {
      cardInfo,
      contractInfo,
      rejectOrder,
      OtherBusUserSearch,
      OnlinePay,
      LeftValue,
      receiptModal
    },
    created() {
      if (this.$route.params.userId === this.$route.params.cardUserId && this.$route.params.userId == 0) {
        this.isOtherBus = true;
        this.getMerchantsBusList();
      } else {
        this.userId = this.$route.params.userId;
        this.cardUserId = this.$route.params.cardUserId;
        this.getCardEditInfo();
        if (this.$route.query.checkOrder) {
          this.isPreOrder = true
          this.getPreOrder()
        }
      }
      this.getIsOpenDebtCardPay();
    },
    provide() {
      return {
        sqbPhoneInfo: computed(() => this.sqbPhoneInfo)
      }
    },
    data() {
      return {
        sqbPhoneInfo: {
          phone: '',
          old_bus_id: '',
        },
        isOpenDebtCardPay: false,
        showVerify: false,
        verifyCode: '',
        verifyId: null,
        finalData: {},
        postUrl: '',
        loadingFlag: false,
        showRejectModal: false,
        isOtherBus: false,//是否为跨店升卡
        isPreOrder: false,//是否为核单
        otherBusInfo: {
          bus_id: '',
          user_id: '',
          card_user_id: ''
        },
        orderinfo: '',
        merchantsBusList: [],
        cardList: '',
        userId: '',
        cardUserId: '',
        formData: {},
        cardData: '',
        onlinePay: false,
        showModal: false,
        closable: true,
        selectedCard: {
          card_type_id: 1
        },
        privateTotalAmount: 0, // 私教课通过单价和节数计算的合同金额
        coachList: '',
        coachCur: ''
      };
    },
    watch: {},
    computed: {
      ...mapState(['busId']),
      typeText() {
        let text = '次数';
        if (this.cardData.card_type_id == 3) {
          text = '金额';
        } else if (this.cardData.card_type_id == 4 || this.cardData.card_type_id == 5) {
          text = '节数';
        }
        return text;
      }
    },
    methods: {
      getIsOpenDebtCardPay() {
        this.$service.get('/Web/Commodity/get_setting').then(res => {
          if (res.data.errorcode == 0) {
            this.isOpenDebtCardPay = res.data.data.open_confirm == 1 ? true : false;
          } else {
            this.$Message.error(res.data.message)
          }
        })
      },
      sendVerifyCode() {
        if (this.verifyId) {
          this.$service.post('/Confirm/send_sms_code', {'id': this.verifyId}).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success("短信发送成功");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      },
      handleShutDownOrder() {
        this.showVerify = false;
        this.verifyCode = '';
        this.loadingFlag = false;
      },
      async handleConfirmSuccess() {
        //先验证验证码是否正确，在提交数据
        this.$service.post('/Confirm/post_confirm', {'id': this.verifyId, 'code': this.verifyCode}).then(res => {
          console.log(res);
          if (res.data.errorcode === 0) {
            this.postFinalData(this.finalData);
          } else {
            this.$Message.error('验证码错误');
          }
        });
      },
      postFinalData() {
        this.$service
            .post(this.postUrl, this.finalData)
            .then(response => {
              if (response.data.errorcode === 0) {
                // this.$Message.success(response.data.errormsg);
                // this.$router.back();
                let userId = this.isOtherBus ? response.data.user_id : this.finalData.user_id;
                this.contractComplete(userId, response.data.card_order_info_id, 'changecard')
              } else {
                this.$Message.error(response.data.errormsg);
              }
            })
            .catch(function (response) {
              console.log(response);
            });
      },
      getPreOrder() {
        return this.$service
            .post('/Web/preBilling/orderInfo', {
              id: this.$route.query.preOrdId,
              user_id: this.$route.query.userId
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                const resInfo = res.data.data.info
                this.orderinfo = resInfo
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
      }
      ,
      receiptUpdate() {
        this.$router.back();
      }
      ,
      async handleOnlinePay() {
        let formReturn = await this.checkForm();
        if (formReturn.valid) {
          this.verifyCardSn(formReturn.postData).then(() => {
            // 显示在线支付弹窗
            this.showModal = true;
          });
        }
      }
      ,
      otherBusUserChange(info) {
        if (!info) {
          this.cardList = ''
          this.cardData = ''
          return false
        }
        this.sqbPhoneInfo = {
          phone: info.phone,
          old_bus_id: this.otherBusInfo.bus_id
        }
        this.$service
            .post('/Web/Member/carduserList', {
              user_id: this.otherBusInfo.user_id,
              bus_id: this.otherBusInfo.bus_id,
              isChangeCardAndBus: 1
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                let cardList = res.data.data.list
                console.log('all: ', cardList.length)
                this.cardList = cardList.filter(item => {
                  return !item.is_zhima_fit_pay_order
                })
                console.log('slice: ', this.cardList.length)
                if (cardList.length > 0) {
                  this.otherBusInfo.card_user_id = cardList[0].card_user_id
                  this.otherBusCardChange(cardList[0].card_user_id)
                }
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(function (err) {
              console.log(err)
            })
      }
      ,
      otherBusCardChange(cardUserId) {
        for (let item of this.cardList) {
          if (item.card_user_id == cardUserId) {
            //重置cardInfo,contractInfo组件的生命周期
            this.cardData = '';
            setTimeout(() => {
              this.cardData = item;
            }, 100);
            break;
          }
        }
      }
      ,
      getMerchantsBusList() {
        this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
          if (res.data.errorcode === 0) {
            this.merchantsBusList = res.data.data
          } else {
            this.$Message.success(res.data.errormsg);
          }
        });
      }
      ,
      getCardEditInfo() {
        let postData = {
          card_user_id: this.cardUserId,
          type: 'changeCard',
          user_id: this.userId
        };
        this.$service.post('/Web/Member/get_card_user_edit_info', postData).then(res => {
          if (res.data.errorcode === 0) {
            this.cardData = res.data.data.info;
          }
        });
      }
      ,
      async handleSubmit() {
        if (this.loadingFlag) {
          return false;
        }
        this.loadingFlag = true;
        let formReturn = await this.checkForm();
        if (formReturn.valid) {
          this.postUrl = `/Web/Member/${this.$route.query.preOrdId?'check_pre_bill_chang_card':'changeCard'}`
          if (this.isOtherBus) {
            this.postUrl = '/Web/MemberCard/changeCardAndBus'
          }
          formReturn.postData.pre_id = this.$route.query.preOrdId || ''
          this.finalData = formReturn.postData;
          //是否选择了储值卡支付
          const hasPayTypeEight = formReturn.postData.new_pay_type.some(obj => obj.pay_type === 8 && obj.card_user_id);
          if (hasPayTypeEight && this.isOpenDebtCardPay) {
            this.showVerify = true;
            //请求验证码
            let sendData = {
              type: 1,
              data: this.finalData,
              confirm_type: 1,
              user_id: this.userId,
              amount: this.finalData.amount
            };
            this.$service.post('/Confirm/create_order', sendData).then(res => {
              if (res.data.errorcode === 0) {
                this.verifyId = res.data.data.id;
                this.sendVerifyCode();
              } else {
                this.$Message.success(res.data.errormsg);
                this.loadingFlag = false;
              }
            });
          }else{
            this.postFinalData();
          }
        } else {
          this.$Message.error('请先正确填写数据！');
        }
        this.loadingFlag = false;
      }
      ,
      verifyCardSn(data) {
        return new Promise((resolve, reject) => {
          let url = '/Web/Onlinepay/Verification_card_sn';
          let postData = {
            card_sn: data.card_sn
          };
          this.$service
              .post(url, postData)
              .then(function (response) {
                if (response.status === 200) {
                  if (response.data.errorcode !== 0) {
                    this.$Message.error('实体卡号已存在');
                    reject();
                    return false;
                  }
                  resolve();
                }
              })
              .catch(function (error) {
                console.log(error);
              });
        });
      }
      ,
      async checkForm() {
        let cardInfoData = this.$refs.cardComponent.handleSubmitClick();
        let contractInfoData = await this.$refs.contractComponent.handleSubmitClick();
        let valid = cardInfoData.valid && contractInfoData.valid;
        let postData = objectMerge(cardInfoData.postData, contractInfoData.postData);
        postData.bus_id = this.busId;
        postData.user_id = this.userId || this.otherBusInfo.user_id;
        postData.card_user_id = this.cardUserId || this.otherBusInfo.card_user_id;
        valid && (this.formData = postData);
        return {valid, postData};
      }
    }
  }
  ;
</script>

<style lang="less" scoped>
    .buttons {
        display: flex;
        justify-content: space-around;
        margin-top: 35px;
    }

    .ivu-btn {
        width: auto;
        margin: 0;
    }

    .line-cross {
        height: 30px;
        margin: 15px 0;
        position: relative;
        width: 50%;

        &::before {
            content: '';
            display: block;
            width: 100%;
            height: 1px;
            border-top: 1px dashed #e5e5e5;
            position: absolute;
            top: 10px;
            left: 0;
        }

        span {
            display: inline-block;
            background: #fff;
            padding: 0 5px 0 18px;
            position: absolute;
            left: 50%;
            top: 0;
            margin-left: -90px;
            color: #999;
            font-size: 14px;
        }
    }
</style>
