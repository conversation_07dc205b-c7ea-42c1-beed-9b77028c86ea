<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{userId==0?'添加':'编辑'}}</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
        <Form-item label="名称" prop="username">
          <div class="field-sale">
            <Input v-model="formValidate.username" placeholder="请输入会员名称"></Input>
            <Alert v-if="needApprove" type="warning" class="field-tip" style="padding: 8px 10px;">审</Alert>
          </div>
        </Form-item>
        <Form-item label="性别" prop="sex">
          <RadioGroup v-model="formValidate.sex">
            <Radio label="1">
              <span>男</span>
            </Radio>
            <Radio label="2">
              <span>女</span>
            </Radio>
          </RadioGroup>
        </Form-item>
        <Form-item label="预留" prop="sex">
          <div class="field-sale">
            <div style="width: 53%;max-width: 550px;">
              <RadioGroup v-model="formValidate.phone_type" @on-change="handlePhoneTypeChange">
                <Radio :label="1" :disabled="bindWxPalmservice">
                  <span>本人电话</span>
                </Radio>
                <Radio :label="2" :disabled="bindWxPalmservice">
                  <span>
                      亲友电话
                      <Tooltip>
                        <div slot="content">
                          本人没有电话号码，可以填写紧急联系人电话
                        </div>
                        <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                      </Tooltip>
                    </span>
                </Radio>
              </RadioGroup>
            </div>
            <Alert v-if="needApprove" type="warning" class="field-tip" style="padding: 8px 10px;">审</Alert>
          </div>
        </Form-item>
        <Form-item :label="formValidate.phone_type==2?'亲友电话':'电话'" prop="phone" :error="phoneError">
          <div class="field-sale">
            <Input v-model="formValidate.phone" placeholder="请输入电话" :disabled="isWx || bindWxPalmservice" @on-change="getPalmUserList"></Input>
            <Alert v-if="isWx" type="warning" class="field-tip">请解绑电话号码相关绑定后再编辑</Alert>
            <Alert v-else-if="needApprove" type="warning" class="field-tip" style="padding: 8px 10px;">审</Alert>
          </div>
          <div v-if="formValidate.phone_type==2 && palmUserList.length">
            <div v-if="userId && formValidate.origin_phone" class="bus-user-tips">用户电话 {{formValidate.origin_phone}}(亲友电话)</div>
            <RadioGroup v-model="formValidate.k_user_id" @on-change="handleKUserChange" vertical>
              <Radio v-for="item in palmUserList" :key="item.k_user_id" :label="item.k_user_id">
                 <span v-if="item.k_user_id === '0'">
                  <span>{{userId?'修改为':'创建'}}新用户 {{item.phone}}</span>
                 </span>
                 <span v-else>
                  <span>选择用户 {{item.phone}} {{item.username}}</span>
                 </span>
              </Radio>
          </RadioGroup>
          <div class="tips">*同一位亲友（儿童/老人）请保持统一账户</div>
          <div v-if="palmBusUserList.length" class="bus-user-wrap">
            <div class="bus-user-tips">当前门店已有用户</div>
            <div class="bus-user-list" v-for="item in palmBusUserList" :key="item.k_user_id">
              <div class="bus-user-item">{{item.phone}} {{item.username}}</div>
            </div>
          </div>
          </div>
        </Form-item>
        <Form-item label="证件号" prop="id_code">
          <div class="field-sale">
            <Input v-model="formValidate.id_code" placeholder="请输入会员证件号码"></Input>
            <Alert v-if="needApprove" type="warning" class="field-tip" style="padding: 8px 10px;">审</Alert>
          </div>
        </Form-item>
        <Form-item label="出生日期" prop="birthday">
          <DatePicker :open="birthdayOpen" v-model="birthdayPicker" type="date" @on-change="birthdayChange">
            <Input v-model="formValidate.birthday" placeholder="请选择会员出生日期" @on-focus="birthdayFocus" @on-blur="birthdayBlur" readonly></Input>
          </DatePicker>
        </Form-item>
        <Form-item label="获客来源" prop="source_id">
          <Select v-model="formValidate.source_id" filterable clearable>
            <Option v-for="item in sourceList" :value="item.source_id" :key="item.source_id">{{ item.source_name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="归属会籍" prop="marketers_id">
          <div class="field-sale">
            <saleSelect v-model="formValidate.marketers_id" :disabled="!!userId"></saleSelect>
            <Alert v-if="userId!=0" type="warning" class="field-tip">会籍变更请使用指派会籍操作</Alert>
          </div>
        </Form-item>
        <FormItem label="标签" prop="tags">
          <CheckboxTag style="width: 400px;" v-model="tagsIds" @tagAdded="getTags" :data.sync="tags" isMemberDetail></CheckboxTag>
        </FormItem>
        <Form-item label="紧急联系人" prop="emerg_username">
          <Input v-model="formValidate.emerg_username"></Input>
        </Form-item>
        <Form-item label="紧急联系电话" prop="emerg_phone">
          <Input v-model="formValidate.emerg_phone"></Input>
        </Form-item>
        <FormItem label="备注" prop="remark">
          <Input v-model="formValidate.remark" type="textarea" :rows="4" placeholder="请输入..."></Input>
        </FormItem>

        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmitClick">提交</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>
<script>
  import CheckboxTag from 'components/form/checkboxTag';
  import saleSelect from 'components/membership/salesSelect';
  export default {
    name: 'EditMemberInfo',
    data() {
       const validatePhone = (rule, value, callback) => {
          if (this.phoneError && this.formValidate.phone_type === 2) {
            callback(new Error(this.phoneError));
          } else {
            callback();
          }
      };
      return {
        userId: this.$route.params.userId || '',
        initialData: {},
        tags: [],
        tagsIds: [],
        sourceList: [],
        birthdayOpen: false,
        birthdayPicker: '',
        isWx: false,
        bindWxPalmservice : false,
        isInfoApprove: false, // 会员信息是否审批中
        needApprove: false, // 会员信息是否需要审批
        needApproveData: { // 需要审批的会员信息，用于判断数据是否修改
          username: '',
          phone_type: '',
          phone: '',
          id_code: '',
        },
        formValidate: {
          username: '',
          sex: '1',
          phone: '',
          origin_phone: '',
          phone_type: 1,
          id_code: '',
          birthday: '',
          source_id: '',
          marketers_id: '',
          tags_ids: '',
          emerg_username: '',
          emerg_phone: '',
          k_user_id: '0',
          remark: ''
        },
        phoneError: '',
        ruleValidate: {
          username: [{ required: true, message: '会员名称不能为空' }],
          phone: [
            {
              required: true,
              pattern: /^1\d{10}$/,
              message: '请输入正确的手机号码'
            },
            {
              validator: validatePhone,
            }
          ],
          emerg_phone: [
            {
              pattern: /^1\d{10}$/,
              message: '请输入正确的手机号码'
            }
          ]
        },

        // 月付会员的手机号缓存
        cacheMemberPhone: '',
        isAlipayMember: false,
        isWeChatPayMember: false,
        palmUserList: [],
        palmBusUserList: [],
      };
    },
    watch: {
      'formValidate.id_code'(val) {
        if (!this.formValidate.birthday && /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/.test(val)) {
          this.formValidate.birthday = val.substring(6, 10) + '-' + val.substring(10, 12) + '-' + val.substring(12, 14);
        }
      },
      'formValidate.phone'(val) {
        if (!this.formValidate.emerg_phone && /^1\d{10}$/.test(val)) {
          this.formValidate.emerg_phone = val
        }
      }
    },
    components: {
      CheckboxTag,
      saleSelect
    },
    methods: {
      handleKUserChange() {
        if(!!this.formValidate.k_user_id && this.palmUserList.length) {
          const selectedUser = this.palmUserList.filter(item => item.k_user_id === this.formValidate.k_user_id)
          if (selectedUser && selectedUser[0].username) {
            this.formValidate.username = selectedUser[0].username;
          }
        }
      },
      handlePhoneTypeChange() {
        const type = this.formValidate.phone_type
        if(type === 1){
          this.resetPalmInfo()
          this.phoneError = ''
        } else {
          this.getPalmUserList()
        }
      },
      resetPalmInfo() {
        this.palmUserList = []
        this.palmBusUserList = []
        this.formValidate.k_user_id = '0'
      },
      getPalmUserList() {
        const phone = this.formValidate.phone
        // 编辑为初始电话时则不查询
        if(this.initialData.phone && phone === this.initialData.phone) {
          this.resetPalmInfo()
          this.phoneError = ''
          return;
        }
        if(/^1\d{10}$/.test(phone) && this.formValidate.phone_type == 2) {
           this.$service.get('/Web/Member/getPalmUserList', {
            params: {
              phone,
              user_id: this.userId || ''
            }
           }, { loading: true }).then(res => {
            if (res.data.errorcode === 0) {
              this.palmUserList = res.data.data.list || []
              this.palmBusUserList = res.data.data.bus_list || []
              if (this.palmUserList.length) {
                this.formValidate.k_user_id = this.palmUserList[0].k_user_id
              }
              this.phoneError = ''
            } else {
              this.resetPalmInfo()
              this.phoneError = res.data.errormsg
            }
          });
        } else {
          this.resetPalmInfo()
        }
      },

      birthdayChange(val) {
        this.formValidate.birthday = val;
        this.birthdayOpen = false;
      },
      handleReset(name) {
        if (this.userId == 0) {
          this.$refs[name].resetFields();
        } else {
          this.getUserInfo();
        }
      },
      birthdayFocus() {
        this.birthdayPicker = this.formValidate.birthday ? this.formValidate.birthday : '1990-06-01';
        this.birthdayOpen = true;
      },
      birthdayBlur() {
        this.birthdayOpen = false;
      },
      handleSubmitClick() {
        // check cache phone if change
        if (this.cacheMemberPhone !== this.formValidate.phone) {
          if (this.isAlipayMember) {
            this.$Modal.confirm({
              title: '是否确认提交',
              content: '当前会员已经签约了支付宝先享后付并在履约中!',
              onOk: () => {
               this.handleCheck('formValidate')
              },
              onCancel: () => {
                this.formValidate.phone = this.cacheMemberPhone
              },
              cancelText: '不修改',
            })
          } else if (this.isWeChatPayMember) {
            this.$Modal.confirm({
              title: '是否确认提交',
              content: '当前会员已经签约了微信先享后付并在履约中!',
              onOk: () => {
               this.handleCheck('formValidate')
              },
              onCancel: () => {
                this.formValidate.phone = this.cacheMemberPhone
              },
              cancelText: '不修改',
            })
          } else {
            this.handleCheck('formValidate')
          }
        } else {
          this.handleCheck('formValidate')
        }
      },
      handleCheck(name) {
        this.$refs[name].validate(valid => {
          if (valid) {
            /* 是否修改了需要审批数据 */
            const approveKeys = Object.keys(this.needApproveData)
            const isEditApproveData = approveKeys.some(k => {
              return this.needApproveData[k] !== this.formValidate[k]
            })
            if (this.userId && isEditApproveData && this.isInfoApprove) {
              this.$Modal.confirm({
                title: '是否确认提交',
                content: '会员信息正在审批中，上传成功后将会撤回原审批记录，重新提交本次审批',
                onOk: () => {
                  this.handleSubmit()
                },
              })
            } else {
              this.handleSubmit()
            }

          } else {
            this.$Message.error('请正确填写数据!');
          }
        });
      },
      handleSubmit() {
        let url = '/Web/Member/userAdd';
        this.formValidate.tags_ids = this.tagsIds.join(',');
        if (this.userId) {
          url = '/Web/Member/userUpdate';
          this.formValidate.user_id = this.userId;
        }

        this.$service.post(url, this.formValidate).then(res => {
          if (res.data.errorcode === 0) {
            let id = this.userId ? this.userId : res.data.user_id;
            this.$router.push(`/member/detail/${id}`);
          } else {
            if(!this.userId && res.data.errorcode === 40050) {
              this.$Modal.confirm({
                title: '提示',
                content: '手机号已经存在，是否跳转到对应会员?',
                onOk: () => {
                  console.log(res.data);
                  this.$router.push(`/member/detail/${res.data.data.user_id}`);
                },
              })
            } else {
              this.$Message.error(res.data.errormsg);
            }
          }
        });
      },
      getTags() {
        this.$service.post('/Web/MembershipTags/get_user_tags', { user_id: this.userId }).then(res => {
          if (res.data.errorcode === 0) {
            this.tags = res.data.data;
            if (this.userId) {
              this.tagsIds = [];
              this.tags.forEach(item => {
                if (item.is_select == 1) {
                  this.tagsIds.push(item.id);
                }
              });
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getUserInfo() {
        return this.$service
          .post('/Web/Member/getUser', {
            user_id: this.userId,
            source_type: 1
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              this.initialData = data;
              const approveKeys = Object.keys(this.needApproveData)
              Object.keys(this.formValidate).forEach(item => {
                this.formValidate[item] = data[item];
                /* 拿到需要审核的数据 */
                if(approveKeys.includes(item)) {
                  this.needApproveData[item] = data[item];
                }
              });
              this.isWx = res.data.data.is_wx ? true : false;
              this.bindWxPalmservice = res.data.data.bind_wx_palmservice;

              // cache phone
              this.cacheMemberPhone = data.phone;
              this.isAlipayMember = data.alipay_contract == 1;
              this.isWeChatPayMember = data.wx_contract == 1;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getSourceList() {
        return this.$service
          .post('/Web/Member/get_source', {
            source_type: 1
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              if (Array.isArray(res.data.data)) {
                this.sourceList = res.data.data;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      /* 获取会员信息是否审批 */
      getApproveSetting() {
        this.$service.get('Web/ApproveSet/get_approve_set').then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data;
            const has = Array.isArray(list) && list.find(v => v.approve_type == '12') // 是否有会员信息审批项
            this.needApprove = has && has.status == '1';
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      /* 获取会员信息是否审批中 */
      getUserInfoApproveStatus() {
        const params = {
          user_id: this.userId,
          type: 1 // 1会员信息 2会员头像
        }
        this.$service.post('/Web/Member/checkApproveUser', params).then(res => {
          if (res.data.errorcode === 0) {
            this.isInfoApprove = res.data.errormsg // false不存在 true存在
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      }
    },
    mounted() {
      this.getTags();
      this.getSourceList();
      if (this.userId) {
        this.getUserInfo();
        this.getApproveSetting()
        this.getUserInfoApproveStatus()
      }
    }
  };
</script>
<style lang="less" scoped>
  .field-sale {
    display: flex;
    flex-direction: row;

    .field-tip {
      margin-left: 22px;
      padding: 8px;
      padding-right: 16px;
      line-height: 14px;
    }
  }

  .tips {
    font-size: 12px;
    color: #888;
  }
  .bus-user-wrap {
    width: 53%;
    border-top: 1px dashed #ebebeb;
    font-size: 12px;
    .bus-user-item {
      margin-bottom: 8px;
    }
  }
  .bus-user-tips {
    font-size: 12px;
  }
</style>
