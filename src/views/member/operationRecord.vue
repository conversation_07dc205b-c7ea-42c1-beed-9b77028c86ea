<template>
  <div class="table-wrap">
    <div>
      <Menu mode="horizontal" :active-name="currentRecords" @on-select="switchRecords">
        <Menu-item :name="index" :key="index" v-for="(item, index) in allRecords">
          {{item}}
        </Menu-item>
      </Menu>
    </div>
    <header>
      <Input class="user-search"
            v-if="curTable !== 1 && curTable !== 8 && curTable !== 9"
             placeholder="姓名/电话/实体卡号"
             v-model="postData.search"
             @on-enter="doSearch" />
      <DatePicker format="yyyy-MM-dd" type="daterange" :value="dateRange" @on-change="dateChange" placeholder="时间段" style="width: 200px" clearable></DatePicker>
      <Select class="select" v-if="curTable == 1" v-model="postData.export_content" placeholder="导出内容" clearable>
        <Option v-for="item in exportTypes" :key="item.id" :value="item.id">{{item.type}}</Option>
      </Select>
      <Select class="select" v-if="curTable == 0 || curTable == 5" v-model="postData.approve_status" placeholder="审批状态" clearable>
        <Option value="">全部</Option>
        <Option value="1">待审</Option>
        <Option value="2">不通过</Option>
        <Option value="3">通过</Option>
      </Select>
      <Select class="select" v-if="curTable !== 1 && curTable !== 5" v-model="postData.operating_type" placeholder="操作类型" clearable filterable>
        <Option value="" v-if="curTable == 0">全部</Option>
        <Option v-for="item in typeList" :key="item.operating_type || item.id" :value="item.operating_type || item.id">{{item.operating_type || item.type}}</Option>
      </Select>
      <template>
      <Cascader
        class="cascader-short"
        v-if="curTable != 1 && curTable != 2 && curTable != 5 && curTable !== 8 && curTable !== 9"
        v-model="operator"
        filterable
        @on-change="operationIdChange"
        trigger='hover'
        :data="cascaderData"
        placeholder="操作人员" />
      <Select
        v-else
        class="select"
        v-model="postData.admin_id"
        placeholder="操作人员"
        clearable
        filterable>
        <Option value="">全部</Option>
        <Option
          v-for="item in adminList"
          :key="item.id || item.marketers_id || item.coach_id"
          :value="item.id || item.marketers_id || item.coach_id">{{item.username || item.sale_name || item.coach_name}}</Option>
      </Select>
      </template>
      <Button type="success"
              @click="doSearch">搜索</Button>
    </header>
    <main>
      <Table :columns="showColumns"
             :data="showTableData"
             stripe
             disabled-hover></Table>
      <Modal v-model="showRecord">
        <recordDetail :data="recordDetail"></recordDetail>
        <div slot="footer"></div>
      </Modal>
      <Modal title="支付方式" v-model="showPayTypeDetail">
        <Table :columns="payDetailColumns" :data="payTypeDetail" disabledHover></Table>
        <div slot="footer"></div>
      </Modal>
    </main>
    <footer>
      <ExportButton v-if="curTable == 0" url="/Web/OperatingRecord/get_operating_record_all" :data="postData" />
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
import { formatDate } from 'utils'
import recordDetail from 'src/components/member/recordDetail'
import Pager from 'components/pager'
import { getNewHost } from 'utils/config'
import ExportButton from 'components/form/ExportButton'

export default {
  name: 'operationRecord',
  components: {
    recordDetail,
    ExportButton,
    Pager
  },
  data() {
    return {
      operator: [],
      adminList: [],
      admin_usernames: [],
      exportTypes: [
        { type: '全部', id: 0 },
        { type: '定金会员', id: 1 },
        { type: '押金会员', id: 2 },
        { type: '会员签到数据', id: 16 },
        { type: '会员私教消课数据', id: 17 },
        { type: '会员到店体验数据', id: 18 },
        { type: '团课预约数据', id: 19 },
        { type: '报名营销活动用户', id: 20 },
        { type: '折扣券领取用户', id: 22 },
        { type: '领取红包用户', id: 23 },
        { type: '会员列表', id: 99 },
        { type: '会员签到', id: 100 },
        { type: '会员私教消课', id: 101 }
      ],
      deleteTypes: [
        { type: '全部', id: 0 },
        { type: '删除会员', id: 1 },
        { type: '删除用户活动记录', id: 2 }
      ],
      editTypes: [
        { type: '全部', id: 0 },
        { type: '编辑会员资料', id: 1 },
        { type: '上传图片', id: 2 },
        { type: '删除图片', id: 3 },
        { type: '编辑会员头像', id: 4 },
      ],
      clearTypes: [
        { type: '全部', id: 0 },
        { type: '积分', id: 1 },
        { type: '魅力值', id: 2 }
      ],
      invoiceTypes: [
        { type: '全部', id: 0 },
        { type: '人工开票', id: 18 },
        { type: '二维码开票', id: 19 },
        { type: '冲红', id: 20 },
        { type: '打印', id: 21 },
        { type: '作废', id: 22 },
        { type: '作废二维码', id: 23 },
        { type: '打印二维码', id: 24 },
        { type: '添加商品', id: 25 },
        { type: '编辑税务编码', id: 26 },
        { type: '重开', id: 27 },
        { type: '新增发票抬头', id: 29 },
        { type: '删除发票抬头', id: 30 },
        { type: '编辑发票抬头', id: 31 },
        { type: '新增场馆税号', id: 32 },
        { type: '编辑场馆税号', id: 33 },
        { type: '删除场馆税号', id: 34 },
        { type: '授权', id: 35 },
        { type: '禁用', id: 36 },
        { type: '启用', id: 37 },
        { type: '可用场馆设置', id: 38 }
      ],
      cashTypes: [
        { type: '全部', id: 0 },
        { type: '收取定金', id: 1 },
        { type: '退定金', id: 2 },
        { type: '收取押金', id: 3 },
        { type: '退押金', id: 4 }
      ],
      leaveTypes: [
        { type: '全部', id: 0 },
        { type: '请假', id: 1 },
        { type: '结束请假', id: 2 },
        { type: '启用会员卡', id: 3 }
      ],
      nonMemberTypes: [
        { type: '全部', id: 0 },
        { type: '购票', id: 1 },
        { type: '编辑', id: 2 },
        { type: '结算', id: 3 },
        { type: '补交', id: 4 },
        { type: '退票', id: 5 },
      ],
      otherTypes: [
        { type: '全部', id: 0 },
        { type: '会员签到', id: 1 },
        { type: '取消会员签到', id: 2 },
        { type: '私教消课', id: 3 },
        { type: '取消私教消课', id: 4 },
        { type: '预约团课', id: 5 },
        { type: '取消预约团课', id: 6 },
        { type: '预约私教', id: 7 },
        { type: '取消预约私教', id: 8 },
        { type: '取消爽约', id: 9 },
        { type: '取消爽约惩罚', id: 10 },
        { type: '商品兑换', id: 11 },
        { type: '租柜', id: 12 },
        { type: '退柜', id: 13 },
        { type: '续租柜', id: 16 },
        { type: '绑RFID', id: 14 },
        { type: '取消绑RFID', id: 15 },
        { type: '更改请假权益', id: 17 },
        { type: '黑名单-添加', id: 39 },
        { type: '黑名单-修改', id: 40 },
        { type: '资格证', id: 41 },
        { type: '编辑团课排课', id: 44 },
        { type: '删除团课排课', id: 45 },
        { type: '人脸采集告知', id: 50},
      ],
      assignTypes: [
        { type: '全部', id: 0 },
        { type: '指派会籍', id: 1 },
        { type: '指派跟进教练', id: 2 },
        { type: '指派上课教练', id: 3 }
      ],
      showTableData: [],
      showColumns: [],
      curTable: 0,
      currentRecords: this.$route.currentRecords || 0,
      allRecords: [
        '会员卡操作记录',
        '数据导出记录',
        '删除记录',
        '编辑记录',
        '定金/押金记录',
        '请假记录',
        '指派记录',
        '散场票操作记录',
        '积分清除记录',
        '发票操作记录',
        '其它记录'
      ],
      typeList: [],
      cascaderData: [
        {
          value: '1',
          label: '操作账号',
          children: [],
          loading: false
        },
        {
          value: '2',
          label: '操作会籍',
          children: [],
          loading: false
        },
        {
          value: '3',
          label: '操作教练',
          children: [],
          loading: false
        }
      ],
      postData: {
        approve_status: '',
        operating_group: '1',
        operating_type: '',
        export_content: '',
        search: '',
        beg_date: '',
        end_date: '',
        page_no: 1,
        page_size: 10,
        admin_id: ''
      },
      dateRange: [
        formatDate(new Date(), 'yyyy-MM-dd'),
        formatDate(new Date(), 'yyyy-MM-dd')
      ],
      userId: '',
      search: '',
      total: 0,
      showRecord: false,
      recordDetail: [],
      columns: [
        {
          title: '时间',
          key: 'creat_time',
          width: 200
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'operating_type'
        },
        {
          title: '描述',
          key: 'detail',
          ellipsis: true,
          align: 'left',
          render: (h, params) => {
            const record = params.row
            let order = record.order_sn ? `订单号：${record.order_sn}，` : ''
            let text = record.content.map(item => {
              return item && item.font
                ? `，${item.title}：${item.font}`
                : item && item.text
                  ? `，${item.title}：${item.text}`
                  : ''
            })
            let string = text.reduce((str, item) => str + item)
            let x = `${order}会员：${record.username}，操作场馆：${
              record.operating_bus_name
            }${string}`
            return (
              <a
                class="link"
                title={x}
                onClick={() => {
                  this.clickRecord(params.index)
                }}
              >
                {x}
              </a>
            )
          }
        },
        {
          title: '操作人员',
          key: 'admin_name'
        },
        {
          title: '审批状态',
          key: 'approve_status',
          render: (h, params) => {
            const item = params.row
            return item.approve_status == 1 ? (
              <div>待审</div>
            ) : item.approve_status == 2 ? (
              <div style="color: #d9534f">不通过</div>
            ) : (
              <div style="color: #19be6b">通过</div>
            )
          }
        }
      ],
      exportColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '导出内容',
          key: 'type',
          render: (h, params) => {
            let idx = Number(params.row.type)
            let type = this.exportTypes.filter(item => {
              return item.id == idx
            })
            return <span>{type[0].type}</span>
          }
        },
        {
          title: '导出数据',
          key: 'num'
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      deleteColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.deleteTypes.filter(item => {
              return item.id == idx
            })
            return <span>{type[0].type}</span>
          }
        },
        {
          title: '描述',
          key: 'desc',
          tooltip: true
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      editColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.editTypes.filter(item => {
              return item.id == idx
            })
            return <span>{type[0].type}</span>
          }
        },
        {
          title: '描述',
          key: 'desc',
          tooltip: true
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      cashColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.cashTypes.filter(item => {
              return item.id == idx
            })
            return <span>{type[0].type}</span>
          }
        },
        {
          title: '金额',
          key: 'amount'
        },
        {
          title: '收款人',
          key: 'collect_account_name'
        },
        {
          title: '支付方式',
          key: 'pay_type_name',
          render: (h, { row }) => {
            if (row.pay_type_name && row.pay_type_name.split(',').length > 1) {
              return (
                <i-button
                  onClick={() => {
                    this.handleShowPayDetail(row)
                  }}
                  type="text"
                >
                  {row.pay_type_name}
                </i-button>
              )
            } else {
              return <div>{row.pay_type_name}</div>
            }
          }
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      leaveColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'operating_type'
        },
        {
          title: '描述',
          key: 'detail',
          ellipsis: true,
          align: 'left',
          render: (h, params) => {
            const record = params.row
            let order = record.order_sn ? `订单号：${record.order_sn}，` : ''
            let text = record.content.map(item => {
              return item && item.font
                ? `，${item.title}：${item.font}`
                : item && item.text
                  ? `，${item.title}：${item.text}`
                  : ''
            })
            let string = text.reduce((str, item) => str + item)
            let x = `${order}会员：${record.username}，操作场馆：${
              record.operating_bus_name
            }${string}`
            return (
              <a
                class="link"
                title={x}
                onClick={() => {
                  this.clickRecord(params.index)
                }}
              >
                {x}
              </a>
            )
          }
        },
        {
          title: '操作人员',
          key: 'admin_name'
        },
        {
          title: '审批状态',
          key: 'approve_status',
          render: (h, params) => {
            const item = params.row
            return item.approve_status == 1 ? (
              <div>待审</div>
            ) : item.approve_status == 2 ? (
              <div style="color: #d9534f">不通过</div>
            ) : (
              <div style="color: #19be6b">通过</div>
            )
          }
        }
      ],
      assignColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '课程',
          key: 'course_name'
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.assignTypes.filter(item => {
              return item.id == idx
            })
            return <span>{type[0].type || '未知'}</span>
          }
        },
        {
          title: '会籍/教练',
          key: 'follow_name'
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      otherColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return (
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            )
          }
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.otherTypes.find(item => item.id == idx)
            return <span>{type.type}</span>
          }
        },
        {
          title: '描述',
          key: 'desc',
          tooltip: true
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      invoiceColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return params.row.username?(
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            ):(<div>-</div>)
          }
        },
        {
          title: '操作类型',
          key: 'type',
          render: (h, params) => {
            let idx = parseInt(params.row.type)
            let type = this.invoiceTypes.find(item => item.id == idx)
            return <span>{type.type}</span>
          }
        },
        {
          title: '描述',
          key: 'desc',
          tooltip: true
        },
        {
          title: '操作人员',
          key: 'admin_username'
        }
      ],
      nonMemberColumns: [
        {
          title: '时间',
          key: 'create_time',
          render: (h, params) => {
            return (
              <span>
                {formatDate(
                  new Date(params.row.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )}
              </span>
            )
          }
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            return params.row.username?(
              <a
                onClick={() => {
                  this.clickUser(params.row.user_id)
                }}
              >
                {params.row.username}
              </a>
            ):(<div>-</div>)
          }
        },
        {
          title: '操作类型',
          key: 'operating_type'
        },
        {
          title: '描述',
          key: 'detail',
          ellipsis: true,
          align: 'left',
          render: (h, params) => {
            const record = params.row
            let order = record.order_sn ? `订单号：${record.order_sn}，` : ''
            let text = record.content.map(item => {
              return item && item.font
                ? `，${item.title}：${item.font}`
                : item && item.text
                  ? `，${item.title}：${item.text}`
                  : ''
            })
            let string = text.reduce((str, item) => str + item)
            let x = `${order}会员：${record.username}，操作场馆：${
              record.operating_bus_name
            }${string}`
            return (
              <a
                class="link"
                title={x}
                onClick={() => {
                  this.clickRecord(params.index)
                }}
              >
                {x}
              </a>
            )
          }
        },
        {
          title: '操作人员',
          key: 'admin_name'
        }
      ],
      clearColumns: [
        {
          title: '时间',
          key: 'create_time'
        },
        {
          title: '操作类型',
          key: 'opt_type'
        },
        {
          title: '描述',
          key: 'desc'
        },
        {
          title: '操作人员',
          key: 'admin_name'
        }
      ],
      tableData: [],

      showPayTypeDetail: false,
      payTypeDetail: [],
      payDetailColumns: [
        {
          title: '支付方式',
          key: 'pay_type_name'
        },
        {
          title: '支付金额',
          key: 'amount'
        }
      ]
    }
  },
  async created() {
    this.postData.beg_date = this.dateRange[0]
    this.postData.end_date = this.dateRange[1]
    this.getOpRecord()
    await this.getOpType()
    await this.getAdminList()
    this.showColumns = this.columns
    this.showTableData = this.tableData
  },
  mounted() {
    this.cascaderData.map(async item => {
      item.children = await this.loadData(item)
    })
  },
  methods: {
    operationIdChange(value, selectedData) {
      let arr = [...value]
      this.postData.operating_group = arr[0]
      this.postData.admin_id = arr[1]
    },
    loadData(item, callback) {
      const busId = this.$store.state.busId
      item.loading = true
      switch (item.label) {
        case '操作教练':
          return this.$service
            .get(
              `/Web/Member/get_coach?belong_bus_id=${busId}&contain_deleted=1`,
              {
                loading: false
              }
            )
            .then(res => {
              if (res.status === 200) {
                if (res.data.errorcode == 0) {
                  let arr = res.data.data.map(item => {
                    return {
                      value: item.coach_id,
                      label: item.coach_name
                    }
                  })
                  // item.children = arr;
                  item.loading = false
                  return arr
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              } else {
                console.error('服务器扑街！')
              }
            })
            .catch(err => {
              console.error(err)
            })
        case '操作会籍':
          return this.$service
            .get(
              `/Web/Member/get_sale?belong_bus_id=${busId}&contain_deleted=1`,
              {
                loading: false
              }
            )
            .then(res => {
              if (res.status === 200) {
                if (res.data.errorcode == 0) {
                  let arr = res.data.data.map(item => {
                    return {
                      value: item.marketers_id,
                      label: item.sale_name
                    }
                  })
                  // item.children = arr;
                  item.loading = false
                  return arr
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              } else {
                console.error('服务器扑街！')
              }
            })
            .catch(err => {
              console.error(err)
            })
        case '操作账号':
          return this.$service
            .get('/Web/Admin/getBusAdminList', {
              loading: false
            })
            .then(res => {
              if (res.status === 200) {
                if (res.data.errorcode == 0) {
                  let arr = res.data.data.list.map(item => {
                    return {
                      value: item.id,
                      label: item.username
                    }
                  })
                  // item.children = arr;
                  item.loading = false
                  return arr
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              } else {
                console.error('服务器扑街！')
              }
            })
            .catch(err => {
              console.error(err)
            })
      }
    },
    switchRecords(name) {
      this.operator = []
      const tempData = {
        approve_status: '',
        operating_group: '1',
        operating_type: '',
        export_content: '',
        search: '',
        page_no: 1,
        page_size: 10,
        admin_id: ''
      }
      Object.assign(this.postData, tempData)
      this.typeList = []
      this.curTable = name
      this.showTableData = []
      this.getListNeeded()
    },
    getLogList(url, data = {}) {
      data.operating_group = this.postData.operating_group
      data.admin_id = this.postData.admin_id
      data.pageSize = this.postData.page_size
      data.pageNo = this.postData.page_no
      data.startTime = this.postData.beg_date
      data.endTime = this.postData.end_date
      data.type =
        this.curTable == 1
          ? this.postData.export_content
          : this.postData.operating_type
      return this.$service
        .get(url, {
          params: { ...data }
        })
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.total = res.data.data.count
              if (this.total == 0) {
                // this.$Message.error("未查询到数据！")
              } else {
                // this.$Message.success(`${this.allRecords[this.curTable]}${res.data.errormsg}`);
                this.showTableData = res.data.data.list
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error('网络有问题！')
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getAdminList() {
      let url = '/Web/Admin/getBusAdminList'
      this.$service
        .get(url)
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.adminList = res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error('服务器扑街！')
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getListNeeded() {
      switch (this.curTable) {
        case 0:
          this.showColumns = this.columns
          this.getOpType()
          this.getOpRecord()
          break
        case 1:
          this.showColumns = this.exportColumns
          this.typeList = this.exportTypes
          this.getLogList('/Web/UserLog/getUserLogExportList', {
            type: this.postData.export_content
          })
          break
        case 2:
          this.showColumns = this.deleteColumns
          this.typeList = this.deleteTypes
          this.getLogList('/Web/UserLog/getUserLogDeleteList', {
            search: this.postData.search
          })
          break
        case 3:
          this.showColumns = this.editColumns
          this.typeList = this.editTypes
          this.getLogList('/Web/UserLog/getUserLogUpdateList', {
            search: this.postData.search,
            operating_group: this.postData.operating_group
          })
          break
        case 4:
          this.showColumns = this.cashColumns
          this.typeList = this.cashTypes
          this.getLogList('/Web/UserLog/getUserLogDepositList', {
            search: this.postData.search,
            operating_group: this.postData.operating_group
          })
          break
        case 5:
          this.showColumns = this.leaveColumns
          this.typeList = this.leaveTypes
          this.getLogList('/Web/UserLog/getUserLogStopCardList', {
            search: this.postData.search,
            approve_status: this.postData.approve_status
          })
          break
        case 6:
          this.showColumns = this.assignColumns
          this.typeList = this.assignTypes
          this.getLogList('/Web/UserLog/getUserLogFollowUpList', {
            search: this.postData.search,
            operating_group: this.postData.operating_group
          })
          break
        case 7:
          this.showColumns = this.nonMemberColumns
          this.typeList = this.nonMemberTypes
          this.getLogList('/Web/UserLog/getUserLogSanList', {
            operating_group: this.postData.operating_group
          })
          break
        case 8:
          this.showColumns = this.clearColumns
          this.typeList = this.clearTypes
          this.getLogList('/Web/UserLog/getUserLogCleanIntegral')
          break
        case 9:
          this.showColumns = this.invoiceColumns
          this.typeList = this.invoiceTypes
          this.getLogList('/Web/UserLog/getUserLogOtherList', {
            invoice: 1
          })
          break
        case 10:
          this.showColumns = this.otherColumns
          this.typeList = this.otherTypes
          this.getLogList('/Web/UserLog/getUserLogOtherList', {
            search: this.postData.search,
            operating_group: this.postData.operating_group
          })
          break
        default:
          break
      }
    },
    dateChange([beg_date, end_date]) {
      this.postData.beg_date = beg_date
      this.postData.end_date = end_date
    },
    pageChange(postData) {
      const { beg_date, end_date } = postData
      this.dateRange = [beg_date, end_date]
      this.postData = { ...this.postData, ...postData }
      this.getListNeeded()
    },
    doSearch() {
      this.showTableData = []
      this.postData.page_no = 1
      if (this.curTable !== '0') {
        this.getListNeeded()
      }
      if (this.curTable === '0' || this.curTable === '') {
        this.getOpRecord()
      }
    },
    getOpType() {
      this.$service
        .post('/Web/OperatingRecord/get_operating_type')
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            let list = data.list
            if (!list) {
              this.typeList = []
              return
            } else {
              this.typeList = list
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getOpRecord() {
      const url = '/Web/OperatingRecord/get_operating_record_all'
      // this.postData.beg_date = this.dateRange[0];
      // this.postData.end_date = this.dateRange[1];
      this.$service
        .post(url, this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            let list = data.list
            if (!list) {
              this.$Message.error('未查询到数据')
              this.tableData = []
              return
            }
            this.showTableData = this.tableData = list.map(record => {
              return Object.assign(record, {
                creat_time: formatDate(
                  new Date(record.create_time * 1000),
                  'yyyy-MM-dd HH:mm'
                )
              })
            })
            this.total = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    clickRecord(index) {
      this.recordDetail = new Array(this.showTableData[index])
      this.showRecord = true
    },
    clickUser(userId) {
      // this.$router.push(`/member/detail/${userId}`);
      window.open(`/v2/member/detail/${userId}`, '_blank')
    },
    handleShowPayDetail(item) {
      this.payTypeDetail = Array.isArray(item.new_pay_type) ? item.new_pay_type : []
      this.showPayTypeDetail = true
    },
  }
}
</script>

<style>
/* .cascader-short .ivu-cascader-menu {
  height: 104px;
} */
</style>

<style lang="less" scoped>
.select {
  width: 140px;
}
.ivu-date-picker {
  width: 240px;
}

header {
  .user-search {
    width: 160px;
  }
}

footer {
  justify-content: flex-end;
}
</style>
