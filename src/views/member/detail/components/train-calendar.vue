<template>
  <div class="trainCalendar">
    <span class="title">签到日历</span>
    <div class="calendar">
      <div class="switch">
        <Icon class="switch-icon"
              @click.native="changeMonth(-1)"
              size="26"
              type="ios-arrow-dropleft-circle" />
        <DatePicker class="picker"
                    v-model="calendarDate"
                    @on-change="pickerDateChange"
                    format="yyyy年MM月"
                    type="month"></DatePicker>
        <Icon class="switch-icon"
              @click.native="changeMonth(1)"
              size="26"
              type="ios-arrow-dropright-circle" />
        <span class="calendar-instr">总共到场<i class="checkin-times">{{signCount}}天</i></span>
      </div>

      <div class="calendar-table">
        <ul>
          <li>周一</li>
          <li>周二</li>
          <li>周三</li>
          <li>周四</li>
          <li>周五</li>
          <li>周六</li>
          <li>周日</li>
        </ul>
        <div>
          <span v-for="(day, index) in calendarList" :title="privateSign.includes(day) && !(day - index >= 23 || index - day >= 28) ? '当天上过课' : ''" :key="index" :class="{'gray': day - index >= 23 || index - day >= 28}">
            <i :class="{'private-class': privateSign.includes(day), 'open-class': openClass.includes(day)}">{{day}}</i>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'trainCalendar',
    props: ['userId'],
    data() {
      return {
        calendarDate: '',
        date: '',
        year: '',
        month: '',
        signCount: 0,
        calendarList: [],
        privateSign: [3, 6, 8],
        openClass: [3, 5, 7, 8, 9]
      }
    },
    watch: {
      date(date) {
        this.calendarDate = this.getCalendarDate(date)
        this.calCalendar()
        this.getSignCalendar()
      }
    },
    created() {
      this.date = new Date()
    },
    methods: {
      pickerDateChange(date) {
        const year = date.slice(0, 4)
        const month = date.slice(-3, -1)
        this.date = new Date(year, month - 1)
      },
      getSignCalendar() {
        const url = '/Web/Sign/user_sign_calendar'
        const postData = { user_id: this.userId, date: `${this.year}-${this.month + 1}` }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.signCount = data.sign_count;
              this.openClass = data.normal.map(item => +item)
              this.privateSign = data.private.map(item => +item)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getCalendarDate(date) {
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        this.year = year
        this.month = month - 1
        return `${year}年${month}月`
      },
      changeMonth(count) {
        const month = this.date.getMonth()
        this.date = new Date(this.date.setMonth(month + count))
      },
      calCalendar() {
        const { year, month } = this
        let thisMonthFirstDayWeek = new Date(year, month, 1).getDay()
        const lastMonthLastDate = new Date(year, month, 0).getDate()
        const thisMonthLastDate = new Date(year, month + 1, 0).getDate()
        if (thisMonthFirstDayWeek === 0) {
          thisMonthFirstDayWeek = 7
        }
        let thisMonthDay = Array.from(new Array(thisMonthLastDate), (val, index) => index + 1)
        // 上一个月最后几天
        let prevMonthDay = Array.from(
          new Array(thisMonthFirstDayWeek - 1),
          (val, index) => lastMonthLastDate - (thisMonthFirstDayWeek - 1 - (index + 1))
        )
        let tableLength = prevMonthDay.concat(thisMonthDay).length > 35 ? 42 : 35
        let arr = Array.from(new Array(tableLength), (val, index) => index + 1)
        prevMonthDay
          .concat(thisMonthDay)
          .reverse()
          .forEach(day => {
            arr.unshift(day)
            arr.pop()
          })
        this.calendarList = arr
      }
    }
  }
</script>

<style lang="less">
  @border: 1px solid #ececec;
  .trainCalendar {
    position: relative;
    .title {
      position: absolute;
      top: -20px;
      left: -24px;
    }
    .flex-center {
      display: flex;
      justify-content: center;
      align-content: center;
    }
    .switch-icon {
      cursor: pointer;
      transition: all 0.5s;
      color: #ececec;
      &:hover {
        color: #bbdefb;
      }
    }
    .switch {
      .flex-center;
      align-items: center;
      margin-bottom: 10px;
      .picker {
        width: 146px;
        .ivu-input {
          font-size: 16px;
          color: #0068b7;
          font-weight: bold;
          padding: 0;
          border: 0;
          text-align: center;
          outline: 0;
          box-shadow: none;
          height: auto;
          line-height: inherit;
        }
        .ivu-input-wrapper {
          vertical-align: baseline;
        }
        .ivu-input-icon {
          display: none;
        }
      }
      .calendar-instr {
        font-size: 14px;
        position: absolute;
        right: 0;
      }
      .checkin-times {
        padding-left: 10px;
        font-style: normal;
        color: #0068b7;
      }
    }
    .calendar-table {
      border-top: @border;
      border-left: @border;
      ul {
        font-size: 14px;
        border-bottom: @border;
        border-right: @border;
        display: flex;
        justify-content: space-around;
        height: 36px;
        align-items: center;
        color: #313131;
        font-weight: bold;
      }
      > div {
        .flex-center;
        justify-content: space-around;
        flex-wrap: wrap;
        text-align: center;
        > span {
          .flex-center;
          width: 1/7 * 100%;
          height: 46px;
          font-size: 16px;
          color: #313131;
          border-bottom: @border;
          border-right: @border;
          i {
            .flex-center;
            border-radius: 50%;
            line-height: 27px;
            width: 30px;
            height: 30px;
            align-self: center;
            font-style: normal;
          }
          .open-class {
            background-color: #7fcbc2;
            color: #fff;
          }
          .private-class {
            background-color: #91cafa;
            color: #fff;
          }
        }
        .gray {
          background-color: #ececec;
          color: #959595;
          .open-class {
            background-color: #ececec;
            color: #959595;
          }
          .private-class {
            background-color: #ececec;
            color: #959595;
          }
        }
      }
    }
  }
</style>
