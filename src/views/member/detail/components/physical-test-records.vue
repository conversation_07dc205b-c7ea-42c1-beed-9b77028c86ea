<template>
  <div class="table-wrap" style="border: 0">
    <Table :columns="columns"
           :data="tableData"
           disabled-hover />
    <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import pager from 'mixins/pager'
  export default {
    name: 'physicalTestRecords',
    mixins: [pager],
    data() {
      return {
        columns: [
          {
            title: '体测时间',
            key: 'class_time'
          },
          {
            title: '身高',
            key: 'class_name'
          },
          {
            title: '体重',
            key: 'coach_name'
          },
          {
            title: '基础代谢',
            key: 'reservation_card'
          },
          {
            title: '体脂率',
            key: 'reservation_number'
          },
          {
            title: '骨骼肌含量',
            key: 'sign_type'
          },
          {
            title: 'BMI',
            key: 'sign_type'
          },
          {
            title: 'WHR',
            key: 'sign_type'
          },
          {
            title: ' ',
            width: 40,
            key: 'sign_type'
          },
          {
            title: '操作',
            key: 'sign_type'
          },
        ],
        tableData: []
      }
    },
    methods: {
      getList() {

      }
    },
  }
</script>

<style scoped>

</style>
