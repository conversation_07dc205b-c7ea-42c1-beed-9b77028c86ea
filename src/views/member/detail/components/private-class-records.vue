<template>
  <div class="table-wrap" style="border: 0">
    <header>
      <Select v-model="status" class="w120" @on-change="getList()" clearable filterable placeholder="课程状态">
        <Option value="1">预约</Option>
        <Option value="2">已上课</Option>
      </Select>
    </header>
    <Table :columns="columns"
           :data="tableData"
           :row-class-name="rowClassName"
           disabled-hover />
    <footer style="justify-content: space-between">
      <Button @click="exportCsv">导出</Button>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
    <FaceSignPhoto v-model="showPhotoModel" :data="curPhotoInfo" />
  </div>
</template>

<script>
  import pager from 'mixins/pager'
  import FaceSignPhoto from 'src/views/member/components/FaceSignPhoto.vue'
  export default {
    name: 'privateClassRecords',
    mixins: [pager],
    props: ['userId'],
    components: {
      FaceSignPhoto
    },
    data() {
      return {
        curPhotoInfo: null,
        status: '',
        showPhotoModel: false,
        columns: [
          {
            title: '课程名称',
            key: 'card_name'
          },
          {
            title: '上课教练',
            key: 'coach_name'
          },
          {
            title: '预约时间',
            key: 'appointment_date'
          },
          {
            title: '预约方式',
            key: 'appointment_type_copy'
          },
          {
            title: '扣除次数',
            key: 'sign_number',
            render: (h, params) => {
              const sign_number  = params.row.sign_number
              if (!!sign_number && params.row.is_miss==1) {
                return (<div>{sign_number}<span style="color:#e60012;">(爽约)</span></div>);
              } else {
                return (<div>{sign_number}</div>);
              }
            }
          },
          {
            title: '签到时间',
            key: 'create_date'
          },
          {
            title: '签退时间',
            key: 'end_date'
          },
          {
            title: '课程时长',
            key: 'duration'
          },
          {
            title: '确认方式',
            key: 'sign_type',
            render: (h, params) => {
              return params.row.is_action_close_appointment && params.row.type_copy == '取消预约'?(<i-button
                  type="text"
                  size="small"
                  onClick={() => {
                    this.cancelSign(params.row)
                  }}
                >
                  取消预约
                </i-button>):(
                <div>
                  <span title="签到方式">{params.row.sign_type}</span>
                  {params.row.sign_in_img || params.row.sign_out_img?<span style="cursor: pointer" class="icon-faceimg" title="签到照片" onClick={()=>{
                    this.curPhotoInfo = params.row
                    this.showPhotoModel = true
                  }}></span>:''}
                </div>
              );
             
            }
          }
        ],
        tableData: []
      }
    },
    created() {
      this.getList()
    },
    methods: {
      cancelSign(info) {
        if (info.appt_type == 1) {
          const id = info.pt_id
          let url = ''
          if (info.card_type_id == 4) {
            url = '/Web/PtSchedule/check_pt_schedule_del'
          } else {
            url = '/Web/PtSchedule/check_swim_schedule_del'
          }
          this.$service.post(url, {
            id
          }).then((res) => {
            if (res.data.errorcode == 0) {
              const url = `${window.location.protocol}//${window.location.host}/one-time-pay/cancel/${id}/mb`
              window.open(url, '_self')
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        } else {
          this.$Modal.confirm({
            title: '提示',
            content: '确定取消预约？',
            onOk: () => {
              this.$service
              .post('/Web/PtSchedule/pt_schedule_del_by_user', {
                id: info.pt_id
              })
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getList()
                  this.$Message.success(res.data.errormsg)
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              })
            }
          });
        }
      },
      rowClassName(row, index) {
        if (row.sign_status !== '0') {
          return 'disabled'
        }
      },
      // 设置取消签到 tr 的'已取消' title
      setDisabledTitle() {
        setTimeout(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled')
          let disabledTrArr = Array.prototype.slice.call(disabledTr)
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已取消')
          })
        }, 500)
      },
      getList() {
        const url = '/Web/Sign/private_sign_and_appointment_list'
        const postData = {
          status: this.status,
          user_id: this.userId,
          page_no: this.page,
          page_size: this.pageSize
        }
        this.$service
          .post(url, postData, {loading: false})
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.total = data.count
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    sign_type: item.end_type_copy ? `${item.type_copy}/${item.end_type_copy}` : item.type_copy,
                    duration: item.duration || item.duration === 0 ? `${item.duration}分钟` : item.duration,
                    sign_number: item.sign_number
                  }
                }
              })
              this.$nextTick(() => {
                this.setDisabledTitle()
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      async exportCsv() {
        const url = '/Web/Sign/private_sign_and_appointment_list'
        const postData = {
          status: this.status,
          user_id: this.userId,
          page_no: 1,
          page_size: this.total,
          is_export: 1,
          _export: 1,
          export_type: 1
        }
        const res = await this.$service.post(url, postData, { loading: false, isExport: true })
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content:'导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }
    }
  }
</script>

<style scoped>

</style>
