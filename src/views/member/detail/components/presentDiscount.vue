<template>
  <Modal v-model="showModal" :mask-closable="false" title="折扣券">
    <Form
      ref="form"
      :model="form"
      class="modal-form"
      style="padding: 0 30px"
      :label-width="100">
      <Form-item
        v-if="!userId"
        label="搜索会员"
        prop="user_id"
        :rules="{required: true, message: '请选择会员'}">
        <userSearch v-model="form.user_id" url="/Web/FrontMoney/search_all_user"></userSearch>
      </Form-item>
      <Form-item label="折扣券" prop="coupon_id" :rules="{required: true, message: '请选择折扣券'}">
        <busDiscount v-model="form.coupon_id" />
      </Form-item>
      <Form-item label="赠送原因" prop="reason" :rules="{required: true, message: '请填写赠送原因'}">
        <textarea v-model="form.reason" rows="3" maxlength="90"></textarea>
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="submit">确定</Button>
      <Button @click="showModal = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  import userSearch from 'src/components/user/userSearch';
  import busDiscount from 'components/form/busDiscount';
  export default {
    name: 'PresentDiscount',
    components: {
      userSearch,
      busDiscount
    },
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      }
    },
    data() {
      return {
        form: {
          user_id: '',
          coupon_id: '',
          reason: ''
        },
        discountList: []
      };
    },
    computed: {
      showModal: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      showModal(val) {
        if (!val) {
          this.$refs.form.resetFields();
        }
      }
    },
    methods: {
      async submit() {
        const valid = await this.$refs.form.validate();
        if (!valid) return false;
        const url = '/Web/Coupon/add_grant_coupon';
        this.form.user_id = this.userId;
        this.$service
          .post(url, this.form)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showModal = false;
              this.$Message.success('赠送成功');
              this.$emit('updateData')
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style scoped>
</style>
