<template>
  <div>
    <Card v-if="isLoginBus" dis-hover class="affix-box">
      <!-- <Affix :offset-top="100"> -->
      <div class="action-box">
        <div class="detail-top">
          <div class="tit-box bg-red" @click="editUser">
            <img class="tit-img" src="../../../../assets/img/icon-edit.png" alt="编辑" />
            <span class="top-tit">编辑</span>
          </div>
          <div class="tit-box">
            <Dropdown @on-click="assignCase">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-membership.png"
                alt="指派"
              />
              <span class="top-tit">指派</span>
              <DropdownMenu slot="list">
                <DropdownItem name="showAssignMembership">指派会籍</DropdownItem>
                <DropdownItem name="followCoachModal">指派跟进教练</DropdownItem>
                <DropdownItem name="followSwimCoachModal">指派跟进泳教</DropdownItem>
                <DropdownItem name="classCoachModal">指派上课教练</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="tit-box">
            <Dropdown @on-click="assignCase">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-money.png"
                alt="收定金"
              />
              <span class="top-tit">定金/押金</span>
              <DropdownMenu slot="list">
                <DropdownItem name="showAddDeposit">定金</DropdownItem>
                <DropdownItem name="showAddCash">押金</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="tit-box" @click="$router.push('/signin/locker')">
            <img
              class="tit-img"
              src="../../../../assets/img/icon-lockerent.png"
              alt="租柜"
            />
            <span class="top-tit">租柜</span>
          </div>
          <div
            v-if="!Boolean(Number(userData.suspend_status))"
            class="tit-box"
            @click="toSuspendPage"
          >
            <img
              class="tit-img"
              src="../../../../assets/img/suspend-start.png"
              alt="请假"
            />
            <span class="top-tit">请假</span>
          </div>
          <div
            v-if="Boolean(Number(userData.suspend_status))"
            class="tit-box"
            @click="endSuspend"
          >
            <img
              class="tit-img"
              src="../../../../assets/img/suspend-end.png"
              alt="结束请假"
            />
            <span class="top-tit end-suspend">结束请假</span>
          </div>
          <div class="tit-box">
            <Dropdown @on-click="otherCase">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-other.png"
                alt="其它操作"
              />
              <span class="top-tit">其它操作</span>
              <DropdownMenu slot="list">
                <DropdownItem name="绑RFID">绑RFID</DropdownItem>
                <DropdownItem v-if="hasAgreementPrintAuth" name="打合同"
                  >打合同</DropdownItem
                >
                <DropdownItem name="发短信">发短信</DropdownItem>
                <DropdownItem name="写提醒">写提醒</DropdownItem>
                <DropdownItem name="写跟进">写跟进</DropdownItem>
                <DropdownItem name="调积分">调积分</DropdownItem>
                <DropdownItem v-if="receiptAuth" name="打小票">打小票</DropdownItem>
                <DropdownItem name="请假权益">更改请假权益</DropdownItem>
                <DropdownItem name="资格证">资格证</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="line mr"></div>

          <div class="tit-box bg-blue" @click="createTips">
            <img class="tit-img" src="../../../../assets/img/icon-card.png" alt="购卡" />
            <span class="top-tit">购卡/购课</span>
          </div>

          <!-- <div class="tit-box" v-if="ptCard.length" @click="rewNewCard(ptCard)">
            <img class='tit-img' src="../../../../assets/img/icon-card.png" alt="购私教" />
            <span class="top-tit">续私教</span>
          </div> -->
          <!-- <div class="tit-box" @click="buyCard(true)">
            <img class='tit-img' src="../../../../assets/img/icon-card.png" alt="购私教" />
            <span class="top-tit">购私教</span>
          </div> -->
          <!-- <div class="tit-box" @click="showAddExperienceCards = true">
          <img class='tit-img' src="../../../../assets/img/icon-love.png" alt="赠体验卡" />
          <span class="top-tit">赠体验卡</span>
        </div> -->
          <div class="tit-box">
            <Dropdown @on-click="createTip">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-love.png"
                alt="赠体验卡"
              />
              <span class="top-tit">赠卡赠券</span>
              <DropdownMenu slot="list">
                <DropdownItem name="exp">赠体验卡</DropdownItem>
                <DropdownItem name="expClass">赠体验课</DropdownItem>
                <DropdownItem name="discount">赠折扣券</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="line mr2"></div>
          <div class="tit-box">
            <Dropdown @on-click="handleSignIn">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-signin.png"
                alt="签到"
              />
              <span class="top-tit">签到</span>
              <DropdownMenu slot="list">
                <DropdownItem name="1">到场签到</DropdownItem>
                <DropdownItem name="2">教练签到</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="tit-box">
            <Dropdown @on-click="orderClass">
              <img
                class="tit-img"
                src="../../../../assets/img/icon-time.png"
                alt="约课"
              />
              <span class="top-tit">约课</span>
              <DropdownMenu slot="list">
                <DropdownItem name="1">约团课</DropdownItem>
                <DropdownItem name="2">约私教</DropdownItem>
                <DropdownItem name="3">约泳教</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
      <!-- </Affix> -->
    </Card>
    <Card dis-hover title="基本信息">
      <Alert
        v-for="remind in infoRemind.remind_list"
        :key="remind.id"
        type="warning"
        show-icon="show-icon"
      >
        <span class="remind-time">{{ remind.create_time }}</span>
        <span class="remind-content" :title="remind.content">{{ remind.content }}</span>
        <span>操作账号 :{{ remind.remind_name }}</span>
        <a class="remind-close" @click="closeRemind(remind.id)">取消提醒</a>
      </Alert>
      <Alert type="error" v-show="is_blacklist">此会员在黑名单中</Alert>
      <div class="detail-info">
        <div class="avatar-wrap" @click="handleImgClick">
          <img class="avatar" :src="userData.avatar" alt="" />
          <img
            class="model"
            src="https://wx.rocketbird.cn/Public/Admin/img/upload.jpg"
            alt=""
          />
          <div
            v-if="userData.face_bind === '2' || userData.face_bind === '0'"
            class="face-tips"
          >
            {{ userData.face_bind == 0 ? "人脸信息同步中" : "人脸信息识别失败！" }}
          </div>
        </div>
        <div class="main-info">
          <div class="info-tit">
            <div class="name-wrap">
              <span
                v-if="Boolean(Number(userData.suspend_status)) && isLoginBus"
                class="suspend-tip"
                >请假中</span
              >
              <span class="name">{{ userData.username }}</span>
              <img
                v-if="isUserApprove"
                class="approval-waiting"
                src="../../../../assets/img/approval-waiting.svg"
                title="会员信息/头像审批中"
              />
              <template v-if="isLoginBus">
                <span
                  v-if="userData.bind_wx == 1"
                  class="icon-wx"
                  title="微信已绑定"
                  @click="handleConfirmBox('取消绑定', '要取消该用户的微信绑定吗？', 1)"
                ></span>
                <span
                  v-if="userData.bind_wx_palmservice"
                  @click="handlePalmsOpen()"
                  class="icon-palm"
                  title="刷掌已绑定"
                ></span>
                <span
                  v-if="userData.bind_fingerprint == 1"
                  class="icon-finger"
                  title="指纹已录入"
                  @click="handleConfirmBox('清除指纹', '要清除该用户的指纹信息吗？', 2)"
                ></span>
                <span
                  v-if="userData.is_bindSfinger == 1"
                  class="icon-s-finger"
                  title="指静脉已录入"
                  @click="
                    handleConfirmBox('清除指静脉', '要清除该用户的指静脉信息吗？', 3)
                  "
                ></span>
                <span
                  v-if="userData.bind_rfid == 1"
                  class="icon-rfid"
                  title="RFID已绑定"
                  @click="handleConfirmBox('解绑RFID', '要解除该用户的RFID绑定么？', 4)"
                ></span>
                <span
                  v-if="userData.face_bind !== false && userData.face_bind !== null"
                  class="icon-faceid"
                  title="人脸识别"
                  @click="
                    handleConfirmBox('清除人脸', '要清除该用户的人脸识别信息吗？', 5)
                  "
                ></span>
                <span
                  v-if="
                    userData.shuhua_face_bind !== false &&
                    userData.shuhua_face_bind !== null
                  "
                  class="icon-shufa-face"
                  title="舒华人脸"
                  @click="
                    handleConfirmBox('舒华人脸', '要清除该用户的舒华人脸识别信息吗？', 6)
                  "
                ></span>
                <span
                  v-if="
                    userData.yj_face.is_set_yj_face == 1 &&
                    userData.yj_face.is_bind_face == 1
                  "
                  class="icon-yj-face"
                  title="英杰人脸"
                  @click="
                    handleConfirmBox('英杰人脸', '要清除该用户的英杰人脸识别信息吗？', 'yj')
                  "
                ></span>
                <span
                  v-if="
                    userData.yj_face.is_set_yj_face == 1 &&
                    userData.yj_face.is_bind_rf_id == 1
                  "
                  class="icon-yj-rfid"
                  title="英杰RFID已绑定"
                  @click="
                    handleConfirmBox('解绑英杰RFID', '要解除该用户的英杰RFID绑定么？', 'yj')
                  "
                ></span>
                <span
                  v-if="userData.quick_new_face.is_bind_face"
                  class="icon-quick-face"
                  title="快诺优人脸"
                  @click="
                    handleConfirmBox('快诺优人脸', '要清除该用户的快诺优人脸识别信息吗？', 'quick')
                  "
                ></span>
                <!-- <span
                  v-if="userData.quick_new_face.is_bind_face"
                  class="icon-yj-rfid"
                  title="快诺优RFID已绑定"
                  @click="
                    handleConfirmBox('解绑快诺优RFID', '要解除该用户的快诺优RFID绑定么？', 'yj')
                  "
                ></span> -->
              </template>
            </div>
            <div v-if="infoRemind.all_credit && isLoginBus" class="action-wrap">
              <span class="label">挂账</span>
              <span class="line-bottom" @click="showMemberCredit = true">{{
                infoRemind.all_credit
              }}</span>
            </div>
            <div v-if="isLoginBus" class="action-wrap">
              <span class="label">积分</span>
              <router-link
                v-if="pointAuth"
                :to="{
                  path: '/Web/Point/getPointList',
                  name: '积分管理',
                  params: { search: userData.phone || userData.username },
                }"
              >
                <span class="line-bottom">{{ userPoint }}</span>
              </router-link>
              <span v-else>{{ userPoint }}</span>
            </div>
            <div v-if="discountValidNum" class="action-wrap">
              <span class="label">折扣券</span>
              <!-- 会员可用折扣卷数量 /Web/Coupon/valid_coupon_count user_id -->
              <span class="line-bottom" @click="showDiscount = true"
                >{{ discountValidNum }}张</span
              >
            </div>
            <div v-if="infoRemind.all_front_money && isLoginBus" class="action-wrap">
              <span class="label">定金</span>
              <span class="line-bottom" @click="showMemberDeposit = true">{{
                infoRemind.all_front_money
              }}</span>
            </div>
            <div v-if="infoRemind.all_cash_pledge && isLoginBus" class="action-wrap">
              <span class="label">押金</span>
              <span class="line-bottom" @click="showMemberCash = true">{{
                infoRemind.all_cash_pledge
              }}</span>
            </div>
            <div
              v-if="
                infoRemind.locker_rent_list &&
                infoRemind.locker_rent_list.length > 0 &&
                isLoginBus
              "
              class="action-wrap"
            >
              <span class="label">租柜</span>
              <template v-for="(lockerRent, index) in infoRemind.locker_rent_list">
                <span
                  :key="lockerRent.locker_id"
                  class="line-bottom"
                  @click="returnLocker(lockerRent.lockerrent_id)"
                  >{{ index >= 1 ? "," : "" }}{{ lockerRent.locker_id }}</span
                >
                <span v-if="lockerRent.status == 1" :key="index" class="color-red"
                  >(已过期)</span
                >
              </template>
            </div>
          </div>
          <div class="info-con">
            <ul class="info">
              <li class="item">
                <span class="label">性别</span>
                <span class="rig">{{ userData.sex }}</span>
              </li>
              <li class="item">
                <span class="label">出生日期</span>
                <span class="rig">{{ userData.birthday }}</span>
              </li>
              <li class="item">
                <span class="label">获客来源</span>
                <span class="rig">{{ userData.source_name }}</span>
              </li>
              <li class="item">
                <span class="label">手机号</span>
                <span class="rig"
                  >{{ userData.phone
                  }}{{ userData.phone_type == 2 ? "（亲友电话）" : "" }}</span
                >
              </li>
              <li class="item">
                <span class="label">证件号</span>
                <span class="rig">{{ userData.id_code }}</span>
              </li>
              <li class="item">
                <span class="label">会籍/私教/泳教</span>
                <span class="rig"
                  >{{ userData.sale_name || "无" }}/{{
                    userData.followup_coach_name || "无"
                  }}/{{ userData.followup_swim_coach_name || "无" }}</span
                >
              </li>
              <li class="item">
                <span class="label">紧急联系</span>
                <span class="rig"
                  >{{ userData.emerg_username || "佚名" }}/{{
                    userData.emerg_phone || "无"
                  }}</span
                >
              </li>
              <li class="item">
                <span class="label">备注</span>
                <span class="rig">{{
                  userData.remark ? userData.remark : "暂无备注"
                }}</span>
              </li>
              <li class="item">
                <span class="label">推荐人</span>
                <span class="rig">{{ userData.introducer_name }}</span>
              </li>
              <li class="max-item">
                <span class="label">标签</span>
                <div
                  v-if="userData.tag_list && userData.tag_list.length > 0"
                  class="tag-wrap"
                >
                  <span v-for="tag in userData.tag_list" :key="tag.tag_id">{{
                    tag.tag_name
                  }}</span>
                </div>
                <div v-else>暂无标签</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </Card>

    <MessageForAvatarDialog v-model="showMessageForAvatarDialog" :userId="userId" @on-success="(verifyCode) => {
      this.verifyCode = verifyCode;
      this.extraParams.from = 1;
      this.extraParams.code = verifyCode;
      this.showImgUpload = true;
      this.checkApproveUserAvatar().then(({ status, data }) => {
        if (status == 200 && data.errorcode == 0) {
          this.isAvatarApprove = data.errormsg;
        }
      });
    }" />
    <ProtocolForAvatarDialog v-model="showProtocolForAvatarDialog" :userId="userId" @on-success="() => {
      this.showImgUpload = true;
      this.checkApproveUserAvatar().then(({ status, data }) => {
        if (status == 200 && data.errorcode == 0) {
          this.isAvatarApprove = data.errormsg;
        }
      });
    }" />

    <template v-if="isLoginBus">
      <RewNewModal v-model="showRewNewModal" :list="rewNewModalList" />
      <AssignMembership
        v-model="showAssignMembership"
        :selected-members="[{ username: userData.username, user_id: userId }]"
        @on-success="getUserDetails"
      />
      <AssignFollowCoach
        v-model="followCoachModal"
        :selected-members="userDataArr"
        @on-success="getUserDetails"
      />
      <AssignFollowSwimCoach
        v-model="followSwimCoachModal"
        :selected-members="userDataArr"
        @on-success="getUserDetails"
      />
      <AssignClassCoach
        v-model="classCoachModal"
        :selected-members="[{ username: userData.username, user_id: userId }]"
        @on-success="assignClassCoachSuccess"
      />
      <AddDeposit
        v-model="showAddDeposit"
        :user-id="userId"
        @on-success="getInfoRemind"
        @on-printinfo="depositFinish"
      />
      <AddCash
        v-model="showAddCash"
        :user-id="userId"
        @on-success="getInfoRemind"
        @on-printinfo="depositFinish"
      />
      <AddFollow v-model="showAddFollow" :user-id="userId" />
      <AddRfid
        v-model="showAddRfid"
        :user-id="userId"
        :rfid-id="userData.RFID_id"
        @on-success="getUserDetails"
      />
      <AddRemind v-model="showAddRemind" :user-id="userId" @on-success="getInfoRemind" />
      <AddExperienceCards
        v-model="showAddExperienceCards"
        :user-id="userId"
        @on-success="getUserDetails"
      />
      <AddExperienceClass
        v-model="showAddExperienceClass"
        :user-id="userId"
        @on-success="getUserDetails"
      />
      <MemberLockerent
        v-if="infoRemind.locker_rent_list"
        v-model="showMemberLockerent"
        :data="infoRemind.locker_rent_list"
        :user-id="userId"
        :locker-id="lockerId"
        @on-success="getInfoRemind"
        @on-preprint="precashRefund"
      />
      <MemberDeposit
        v-if="infoRemind.front_money_list"
        v-model="showMemberDeposit"
        :data="infoRemind.front_money_list"
        :user-id="userId"
        @on-success="getInfoRemind"
        @on-printinfo="depositFinish"
      />
      <MemberCash
        v-if="infoRemind.cash_pledge_list"
        v-model="showMemberCash"
        :data="infoRemind.cash_pledge_list"
        :user-id="userId"
        :de-cash="deCash"
        @on-success="getInfoRemind"
        @on-printinfo="depositFinish"
        @on-preprint="cashRefund"
        @on-cleardecash="clearDeCash"
      />
      <deductCash
        v-if="infoRemind.cash_pledge_list"
        v-model="showDeductCash"
        :user-id="userId"
        :de-cash="deCash"
        @on-success="getInfoRemind"
        @on-printinfo="depositFinish"
        @on-cleardecash="clearDeCash"
      />
      <MemberPrint
        v-model="showMemberPrint"
        :data="infoRemind.front_money_list"
        :user-id="userId"
        @on-success="getInfoRemind"
      />
      <PointPutModal
        :show.sync="showAjustPoints"
        :user-id="userId"
        @updateData="getUserPoint"
      />
      <DiscountModal
        type="delete"
        :show.sync="showDiscount"
        :list="discountList"
        :isPreOrder="false"
        @updateData="getDiscountList"
      />
      <receiptDetail v-model="showReceiptDetail" :user-id="userId" />
      <MemberCredit
        v-if="infoRemind.credit_list"
        v-model="showMemberCredit"
        :data="infoRemind.credit_list"
        :user-id="userId"
        @on-success="getInfoRemind"
        @on-printinfo="commodityComplete"
      />
      <div v-if="showImgUpload">
        <ImgUploadWithMedia
          v-model="showImgUpload"
          :user-id="userId"
          :uploadTips="
            isAvatarApprove
              ? '会员头像正在审批中，上传成功后将会撤回原审批记录，重新提交本次审批'
              : ''
          "
          @on-success="upImgSuccess"
          :extra-params="extraParams"
        />
      </div>
      <div v-if="showPresentDiscount">
        <presentDiscount
          v-model="showPresentDiscount"
          :userId="userId"
          @updateData="getDiscountList"
        />
      </div>
      <receipt-modal v-model="showPrint" :to-path="toPath" />

      <Modal v-model="leaveModalFlag" title="更改请假权益">
        <Form
          ref="leaveForm"
          :model="leavePost"
          :rules="leaveRules"
          label-position="left"
          :label-width="100"
        >
          <FormItem>
            会员原允许请假{{
              leaveDesc.suspend_number + leaveDesc.used_suspend_number
            }}次， {{ leaveDesc.suspend_day + leaveDesc.used_suspend_day }}天； 已请假{{
              leaveDesc.used_suspend_number
            }}次， {{ leaveDesc.used_suspend_day }}天
          </FormItem>
          <FormItem>
            剩余请假权益{{ leaveDesc.suspend_number }}次，{{ leaveDesc.suspend_day }}天
          </FormItem>
          <FormItem label="增加请假次数" prop="num" :show-message="false" required>
            <InputNumber
              v-model="leavePost.num"
              :max="999"
              :min="0"
              style="width: 100%"
            ></InputNumber>
          </FormItem>
          <FormItem label="增加请假天数" prop="day" :show-message="false" required>
            <InputNumber
              v-model="leavePost.day"
              :max="999"
              :min="0"
              style="width: 100%"
            ></InputNumber>
          </FormItem>
          <FormItem label="更改原因" prop="remark">
            <Input
              v-model="leavePost.remark"
              :maxlength="100"
              show-word-limit
              type="textarea"
              placeholder="请输入..."
              style="width: 100%"
            />
          </FormItem>
        </Form>
        <div slot="footer" class="modal-buttons">
          <Button type="success" @click="handleLeaveSubmit">确 定</Button>
          <Button @click="leaveModalFlag = false">取 消</Button>
        </div>
      </Modal>
    </template>

    <DiveCertification v-model="showCertificationModal" :user-id="userId" />
  </div>
</template>

<script>
import RewNewModal from "../../components/RewNewModal";
import AssignMembership from "../../components/AssignMembership";
import AssignFollowCoach from "../../components/AssignFollowCoach.vue";
import AssignFollowSwimCoach from "../../components/AssignFollowSwimCoach.vue";
import AssignClassCoach from "../../components/AssignClassCoach.vue";
import AddDeposit from "../../components/AddDeposit.vue";
import AddCash from "../../components/AddCash.vue";
import AddFollow from "../../components/AddFollow.vue";
import AddRfid from "../../components/AddRfid.vue"; // 绑 RFID
import AddRemind from "../../components/AddRemind.vue";
import AddExperienceCards from "../../components/AddExperienceCards.vue";
import AddExperienceClass from "../../components/AddExperienceClass.vue";
import MemberLockerent from "../../components/MemberLockerent.vue";
import MemberDeposit from "../../components/MemberDeposit.vue";
import MemberCash from "../../components/MemberCash.vue";
import MemberCredit from "../../components/MemberCredit.vue";
import MemberPrint from "../../components/MemberPrint.vue"; // 合同打印
import DiscountModal from "components/form/discountModal"; // 折扣券弹窗
import PointPutModal from "src/components/member/pointPutModal.vue"; // 积分调整
import receiptDetail from "../../components/receiptDetail.vue"; // 小票
import ImgUploadWithMedia from "src/components/form/ImgUploadWithMedia.vue";
import receiptModal from "components/receipt/receipt.vue";
import receipt from "mixins/receipt.js";
import presentDiscount from "./presentDiscount.vue"; // 折扣券
import deductCash from "../../components/deductCash.vue"; // 费用退还

import { DISCOUNT_CARD_LIMIT, DISCOUNT_SCOPE_LIMIT } from "store/constants";
import EventBus from "components/EventBus.js";
// import { query } from 'quill';

import DiveCertification from "../../components/DiveCertification";
import MessageForAvatarDialog from './MessageForAvatarDialog';
import ProtocolForAvatarDialog from './ProtocolForAvatarDialog.vue';

export default {
  name: "DetailInfo",
  components: {
    RewNewModal,
    AssignMembership,
    AssignFollowCoach,
    AssignFollowSwimCoach,
    AssignClassCoach,
    AddExperienceCards,
    AddExperienceClass,
    AddDeposit,
    AddCash,
    AddFollow,
    AddRfid,
    AddRemind,
    MemberLockerent,
    MemberCredit,
    ImgUploadWithMedia,
    MemberPrint,
    MemberCash,
    DiscountModal,
    PointPutModal,
    MemberDeposit,
    receiptDetail,
    receiptModal,
    presentDiscount,
    deductCash,
    DiveCertification,
    MessageForAvatarDialog,
    ProtocolForAvatarDialog,
  },
  mixins: [receipt],
  props: {
    isLoginBus: {
      type: Boolean,
      default: true,
    },
    curUserId: {
      type: [String, Number],
      default: "",
    },
    curBusId: {
      type: [String, Number],
      default: "",
    },
    normalCard: {
      type: Array,
    },
    ptCard: {
      type: Array,
    },
    swimCard: {
      type: Array,
    },
  },
  data() {
    return {
      // 指派弹窗
      showAssignMembership: false,
      followCoachModal: false,
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      followSwimCoachModal: false,
      classCoachModal: false,
      showPresentDiscount: false, // 赠折扣券
      showRewNewModal: false, // 赠折扣券

      showCertificationModal: false, // 证书

      hasAgreementPrintAuth: false, // 合同打印权限
      userData: {
        yj_face: {is_set_yj_face: 0, is_bind_face: 0, is_bind_rf_id: 0},
        quick_new_face: { is_bind_face: 0 }
      },
      userDataArr: [],
      showAddFollow: false,
      showAddDeposit: false,
      showAddCash: false,
      showAddRfid: false,
      showAddExperienceCards: false,
      showAddExperienceClass: false,
      showMemberLockerent: false,
      showMemberDeposit: false,
      showMemberCash: false,
      showMemberCredit: false,
      showImgUpload: false,
      showAddRemind: false,
      showMemberPrint: false,
      showAjustPoints: false,
      isAvatarApprove: false, // 会员头像是否审批中
      isUserApprove: false, // 会员信息or头像是否审批中
      // selectedMembers: [],
      rewNewModalList: [],
      infoRemind: {},
      userPoint: 0,
      pointAuth: false, // 积分管理权限
      discountList: [], // 用户折扣券列表
      discountValidNum: 0, // 有效折扣券数
      showDiscount: false,

      showReceiptDetail: false,
      //退押金
      showDeductCash: false,
      deCash: {
        lockerId: "",
        fromtype: "",
      },
      lockerId: "",

      // 请假弹窗表单
      leaveModalFlag: false,
      leavePost: {
        user_id: "",
        num: 0,
        day: 0,
        remark: "",
      },
      leaveRules: {
        remark: [{ required: true, message: "请输入更改原因", trigger: "blur" }],
      },
      leaveDesc: {
        used_suspend_number: 0,
        used_suspend_day: 0,
        suspend_number: 0,
        suspend_day: 0,
      },
      userId: this.curUserId || this.$route.params.userId || "",
      is_blacklist: false,

      // 微信月付
      isWeChatPayMember: false,

      // 修改头像之前的弹窗短信确认
      showMessageForAvatarDialog: false,
      verifyCode: '',
      extraParams: {
        from: '', // 头像上传来源
        code: '', // 短信验证码
      },
      showProtocolForAvatarDialog: false, // 协议弹窗
    };
  },
  created() {
    this.getUserDetails();
    this.getUserPoint();
    this.getPointAuth();
    EventBus.$on("activateAll", () => {
      this.getUserDetails();
    });
    if (this.isLoginBus) {
      this.getInfoRemind();
      this.getAgreementPrintAuth();
    }
    this.getUserInfoApproveStatus();
    this.getDiscountList();
  },
  methods: {
    handleImgClick() {
      if (
        (this.userData.face_bind !== false && this.userData.face_bind !== null) ||
        (this.userData.shuhua_face_bind !== false &&
          this.userData.shuhua_face_bind !== null)
      ) {
        this.$Modal.confirm({
          title: "编辑头像",
          content: "请先解绑人脸再进行头像编辑。",
        });
      } else {
        // data. 1不弹。2要弹
        this.$service.post('/Web/Member/checkout_in_white_list').then(res => {
          if (res.data.errorcode === 0) {
            this.extraParams = {
              from: '',
              code: ''
            }
            if (res.data.data === 2) {
              // this.showMessageForAvatarDialog = true;
              this.showProtocolForAvatarDialog = true;
            } else {
              this.showImgUpload = true;
              this.checkApproveUserAvatar().then(({ status, data }) => {
                if (status == 200 && data.errorcode == 0) {
                  this.isAvatarApprove = data.errormsg;
                }
              });
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      }
    },
    rewNewCard(cardList) {
      this.rewNewModalList = cardList;
      this.showRewNewModal = true;
    },
    resetLeavePost() {
      this.leavePost = {
        user_id: "",
        num: 0,
        day: 0,
        remark: "",
      };
      this.$refs.leaveForm.resetFields();
    },
    getLeaveDesc() {
      return this.$service
        .post("/Web/Member/get_user_suspend_info", { user_id: this.userId })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.leaveDesc = res.data.data.info;
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    handleLeaveSubmit() {
      this.leavePost.user_id = this.userId;
      this.$refs.leaveForm.validate((flag) => {
        if (flag) {
          this.$service
            .post("/Web/Member/user_update_user_suspend", this.leavePost)
            .then((res) => {
              if (res.data.errorcode === 0) {
                this.leaveModalFlag = false;
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch((err) => {
              console.error(err);
            });
        }
      });
    },
    endSuspend() {
      let url = "/Web/Member/user_suspend";
      this.$service
        .post(url, {
          action: 2,
          user_id: this.userData.user_id,
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              this.getUserDetails();
              EventBus.$emit("endSuspend");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            console.error("服务器扑街！");
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    getMembershipCardList() {
      return this.$service
        .post("/Web/Member/carduserList", {
          user_id: this.userData.user_id,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            let list = res.data.data.list;
            let statusAll = list.some((item) => {
              return item.status === "正常";
            });
            if (!statusAll) {
              return 1;
            }
            let reviewAll = list.some((item) => {
              return item.under_review === 1;
            });
            if (reviewAll) {
              return 2;
            }
            return 3;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
    },
    async toSuspendPage() {
      let res = await this.getMembershipCardList();
      if (res === 1) {
        this.$Message.error("该会员无有效会员卡，无法请假!");
        return;
      }
      if (res === 2) {
        this.$Message.error("该会员有待审核的会员卡，无法请假!");
        return;
      }
      this.$router.push(`/member/suspendCard/${this.userData.user_id}`);
    },
    returnLocker(locker_id) {
      this.showMemberLockerent = true;
      this.lockerId = locker_id;
    },
    clearDeCash() {
      this.deCash.lockerId = "";
      this.deCash.fromtype = "";
    },
    precashRefund(lockerId, fromtype) {
      this.showMemberCash = true;
      this.deCash.lockerId = lockerId;
      this.deCash.fromtype = fromtype;
    },
    cashRefund(lockerId, fromtype) {
      this.showDeductCash = true;
      this.deCash.lockerId = lockerId;
      this.deCash.fromtype = fromtype;
    },
    assignClassCoachSuccess() {
      this.getUserDetails();
      this.$emit("assignClassCoachSuccess");
    },
    assignCase(val) {
      this[val] = true;
    },
    getAgreementPrintAuth() {
      const url = "Web/MemberList/print_order_auth";
      this.$service
        .get(url)
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.hasAgreementPrintAuth = true;
          } else {
            this.hasAgreementPrintAuth = false;
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    orderClass(val) {
      if (val == 1) {
        window.history.pushState({}, '', '/class/open-reser');
      } else if (val == 2) {
        this.$router.push(`/Web/PtSchedule/pt_schedule_list?userId=${this.userId}`);
      } else {
        this.$router.push(`/Web/PtSchedule/swim_schedule_list?userId=${this.userId}`);
      }
    },
    closeRemind(id) {
      this.$service
        .post("/Web/Remind/del_remind", {
          id: id,
          user_id: this.userId,
        })
        .then((res) => {
          if (res.status == 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success("取消成功");
              this.getInfoRemind();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    buyCard(coachClass) {
      if (Number(this.userData.suspend_status)) {
        this.$Message.error("该会员处于请假中，无法购卡！");
        return;
      }
      this.$router.push(`/member/buyCard/${this.userId}`);
    },
    editUser() {
      this.$router.push(`/member/edit/${this.userId}`);
    },
    upImgSuccess({ imgUrl, type }) {
      this.userData.avatar = imgUrl;
      /* 上传成功，检查更新是否显示审批标识 */
      this.checkApproveUserAvatar().then(({ data }) => {
        if (data.errorcode == 0) {
          this.isUserApprove = data.errormsg;
        }
      });
      /* 上传成功，更新编辑记录 */
      this.$service.post("/Web/ImageLib/uploadImg", { user_id: this.userId, type });
    },
    presentCase(name) {
      const event = {
        exp: () => {
          this.showAddExperienceCards = true;
        },
        expClass: () => {
          this.showAddExperienceClass = true;
        },
        discount: () => {
          this.showPresentDiscount = true;
        },
      };
      event[name]();
    },
    otherCase(val) {
      switch (val) {
        case "发短信":
          this.$router.push({
            // `/notice/list?userId=${this.userId}`
            name: "消息推送",
            params: {
              activeIndex: "2",
              // selectedMembers: this.selectedMembers,
              selectedUserId: this.userId,
            },
          });
          break;
        case "写跟进":
          this.showAddFollow = true;
          break;
        case "写提醒":
          this.showAddRemind = true;
          break;
        case "绑RFID":
          this.showAddRfid = true;
          break;
        case "打合同":
          this.showMemberPrint = true;
          break;
        case "调积分":
          this.showAjustPoints = true;
          break;
        case "打小票":
          this.showReceiptDetail = true;
          break;
        case "请假权益":
          this.resetLeavePost();
          this.getLeaveDesc().then(() => {
            this.leaveModalFlag = true;
          });
          break;
        case "资格证":
          this.showCertificationModal = true;
          break;
        default:
      }
    },
    handleSignIn(val) {
      if (val == 1) {
        this.$router.push(`/signin/member?userId=${this.userId}`);
      } else {
        this.$router.push(`/signin/ptSign?userId=${this.userId}`);
      }
    },
    //提醒信息
    getInfoRemind() {
      this.$service
        .post("/Web/Remind/user_info_remind", {
          user_id: this.userId,
        })
        .then((response) => {
          if (response.status == 200) {
            if (response.data.errorcode == 0) {
              this.infoRemind = response.data.data;
              let readStorage = ["showMemberCash", "showMemberLockerent"];
              readStorage.forEach((item) => {
                let storagetemp = localStorage.getItem(item);
                if (storagetemp) {
                  this[item] = true;
                  if (item == "showMemberCash") {
                    this.deCash.fromtype = "frompurecash";
                    this.deCash.lockerId = storagetemp;
                  } else {
                    this.deCash.fromtype = "";
                    this.lockerId = storagetemp;
                  }
                  localStorage.removeItem(item);
                }
              });
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 用户积分新 需要从这个接口获取，不从/Web/Remind/user_info_remind
    getUserPoint() {
      const params = {
        user_id: this.userId,
      };

      this.$service
        .post("/Web/Point/getUserPoint", params)
        .then((res) => {
          const { errorcode, errormsg, data } = res.data;

          if (errorcode === 0) {
            this.userPoint = data.point || 0;
          } else {
            this.$Message.error(errormsg);
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 获取是否有积分管理权限，直接从列表接口拿
    getPointAuth() {
      const formBody = {
        page: 1,
        limit: 1,
      };
      this.$service
        .post("/Web/Point/getPointList", formBody)
        .then((res) => {
          if (res.data.errorcode === 0) {
            // 40014 没有积分管理权限
            this.pointAuth = true;
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    //用户信息
    getUserDetails() {
      this.$service
        .post(this.IS_BRAND_SITE? '/Merchant/MemberList/userMsg' : "/Web/Member/userMsg", {
          user_id: this.userId,
          bus_id: this.curBusId,
        })
        .then((response) => {
          if (response.data.errorcode == 0) {
            this.userData = { ...this.userData, ...response.data.data };
            this.checkUser();

            // 散客退回
            if (this.userData.add_way == 5 || this.userData.add_way == 6) {
              this.$router.push({ path: "/signin/nonMember" }); //返回散客列表
              return false;
            }

            this.userDataArr.push(response.data.data);
            this.$store.commit("SET_USER_DETAIL_INFO", response.data.data);
            if (this.userDataArr.length > 1) {
              this.userDataArr.shift();
            }

            // 微信月付
            this.isWeChatPayMember = response.data.data.wx_contract == 1
          } else {
            this.$Message.error(response.data.errormsg);
            if (response.data.errorcode == 49007) {
              this.$router.push({ path: "/login", name: "登录" });
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handlePalmsOpen() {
      const { phone_type, palm_expire_time } = this.userData
      const typeName = phone_type === 1 ? '本人' : '亲友协助'
      const unbindName = phone_type === 1 ? '需要本人在会员端小程序' : '需要在协助人会员端小程序'
      this.$Modal.info({
        title: '微信刷掌',
        content: `<p>${typeName}开掌</p><p>刷掌有效期：${palm_expire_time}</p><p>${unbindName}，【我的-刷掌服务】页面解绑。</p>`
      });
    },
    handleConfirmBox(title, content, clearType) {
      let fakeContent = content
      if (clearType === 1 && this.isWeChatPayMember) {
        // this.$Message.warning({
        //   content: "当前会员已经签约了微信先享后付并在履约中!",
        //   duration: 5,
        //   closable: true,
        // })
        fakeContent += '当前会员已经签约了微信先享后付并在履约中!'
      }
      this.$Modal.confirm({
        title,
        content: fakeContent,
        onOk: () => {
          if(clearType === 'yj') {
            this.clearYjBind()
          } else if (clearType === 'quick') {
            this.clearQuickBind()
          } else {
            this.clearBind(clearType);
          }
        },
        onCancel() {},
      });
    },
    // 解除快诺优绑定
    clearQuickBind() {
      this.$service
        .post("/Web/QuickNew/untieFace", {
          user_id: this.userId
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.errorcode === 0) {
              this.userData.quick_new_face.is_bind_face = false
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          }
        });
    },
    // 解除英杰柜控绑定
    clearYjBind() {
      this.$service
        .post("/Web/YjFace/yj_user_face_del", {
          user_id: this.userId
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.errorcode === 0) {
              this.userData.yj_face.is_bind_rf_id = 0;
              this.userData.yj_face.is_bind_face = 0;
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          }
        });
    },
    // 解除微信绑定
    clearBind(clearType) {
      this.$service
        .post("/Web/Member/user_unbind", {
          user_id: this.userId,
          type: clearType,
        })
        .then((res) => {
          if (res.status === 200) {
            if (res.data.errorcode === 0) {
              if (clearType === 1) {
                this.userData.bind_wx = 0;
              } else if (clearType === 2) {
                this.userData.bind_fingerprint = 0;
              } else if (clearType === 3) {
                this.userData.is_bindSfinger = 0;
              } else if (clearType === 5) {
                this.userData.face_bind = false;
              } else if (clearType === 6) {
                this.userData.shuhua_face_bind = false;
              } else {
                this.userData.bind_rfid = 0;
              }
              this.$Message.success(res.data.errormsg);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          }
        });
    },
    /* 获取会员信息&头像是否审批中 */
    getUserInfoApproveStatus() {
      const p1 = () =>
        this.$service.post("/Web/Member/checkApproveUser", {
          user_id: this.userId,
          type: 1,
        });
      const p2 = this.checkApproveUserAvatar;

      Promise.all([p1(), p2()]).then((resArr) => {
        resArr.forEach((res) => {
          const { status, data } = res;
          if (status == 200 && data.errorcode == 0) {
            // "errormsg" false不存在 true存在
            data.errormsg === true && (this.isUserApprove = true);
          } else {
            this.$Message.error(data.errormsg);
          }
        });
      });
    },
    /* 获取会员头像是否审批中 */
    checkApproveUserAvatar() {
      return this.$service.post(
        "/Web/Member/checkApproveUser",
        { user_id: this.userId, type: 2 } /* type 1会员信息 2会员头像 */
      );
    },
    /* 获取折扣券数据 */
    getDiscountList() {
      this.$service
        .post("/Web/Coupon/get_user_coupon", { user_id: this.userId })
        .then((res) => {
          if (res.data.errorcode === 0) {
            if (res.data.data.length) {
              const { data: list } = res.data;
              let validNum = 0;

              list.forEach((item) => {
                // 原本是使用限制 现更改为使用范围
                item.limit_card_text = DISCOUNT_CARD_LIMIT[item.limit_card - 1];
                item.use_limit_text = (item.use_limit ? item.use_limit.split(",") : ["1"])
                  .map((v) => DISCOUNT_SCOPE_LIMIT[v - 1])
                  .join(); // 使用限制 1 购卡 2续卡 3升卡

                item.disabled = item.status != 1;

                item.status == 1 && validNum++;
              });

              this.discountValidNum = validNum;
              list.sort((a, b) => {
                // 排序权重: 1.可用的排前面 2.(不可用里)未用的排前面 3.(可用和不可用)优惠金额大的排前面 4.(可用和不可用优惠金额相同)快到期的排前面
                return (
                  a.disabled - b.disabled ||
                  a.status - b.status ||
                  b.discount_amount - a.discount_amount ||
                  new Date(a.end_time) - new Date(b.end_time)
                );
              });
              this.discountList = list;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    checkUser(){
      this.$service
        .post('/Web/UserBlacklist/checkUser', {
          phone: this.userData.phone,
          bus_id: this.curBusId
        }, { headers: { 'Content-Type': 'application/json' } })
        .then(response => {
          this.is_blacklist = response.data.data
        })
        .catch(err => {
          console.log(err);
        });
    },
    checkUserLimitByType(type) {
      return this.$service.post('/Web/UserBlacklist/checkUser', {
        phone: this.userData.phone,
        bus_id: this.curBusId,
        member_rule: type,
        loading: true
      }, { headers: { 'Content-Type': 'application/json' } }).then(res => {
        return res.data.data
      })
    },
    async createTips(){
      const flag = await this.checkUserLimitByType(2)
      if(flag) {
        this.$Modal.confirm({
          title: '确认购卡/购课?',
          content: '此会员已在门店黑名单中',
          okText: '仍要购买',
          onOk: () => {
            this.buyCard()
          },
        });
      } else {
        this.buyCard()
      }
    },
    async createTip(name){
      const flag = await this.checkUserLimitByType(2)
      if(flag) {
        this.$Modal.confirm({
          title: '确认赠卡赠券?',
          content: '此会员已在门店黑名单中',
          okText: '仍要购买',
          onOk: () => {
            this.presentCase(name)
          },
        });
      } else {
        this.presentCase(name)
      }
    },
  },
};
</script>

<style lang="less" scoped="scoped">
.remind-time {
  display: block;
  float: left;
  margin: 0;
  min-width: 120px;
  color: #666666;
}
.remind-content {
  display: block;
  float: left;
  margin-left: 7px;
  margin-right: 50px;
  color: #666666;
  max-width: 40%;
  min-width: 20%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.remind-close {
  float: right;
}
.box {
  background-color: #fff;
  width: 100%;
}
.action-box {
  .box;
}
.content-box {
  .box;
  /*padding: 25px 42px 15px;*/
}
.mb0 {
  margin-bottom: 0;
}
.affix-box {
  margin-bottom: 15px;
}
.radius-box {
  width: 104px;
  color: #fff;
  border-radius: 4px;
  margin-right: 48px;
}
.mr {
  margin-right: 60px;
}
.mr2 {
  margin-right: 62px;
}
.line {
  position: relative;
  width: 1px;
  height: 78px;
  background: #d1d1d1;
  margin-top: 16px;
}
.detail-top {
  width: 100%;
  display: flex;
  align-content: center;
  .tit-box {
    height: 110px;
    text-align: center;
    margin-right: 62px;
    cursor: pointer;
    .tit-img {
      display: inline-block;
      margin-top: 16px;
    }
    .top-tit {
      display: block;
      margin-top: 8px;
      font-size: 16px;
    }
    .end-suspend {
      color: #f39800;
      font-weight: 600;
    }
  }
  .bg-red {
    .radius-box;
    background: #f48fb1;
  }
  .bg-blue {
    .radius-box;
    background: #90caf9;
  }
}
@media (max-width: 1600px) {
  .detail-top .tit-box {
    margin-right: 40px;
  }
  .mr {
    margin-right: 30px;
  }
  .mr2 {
    margin-right: 32px;
  }
}
@media (max-width: 1366px) {
  .detail-top .tit-box {
    margin-right: 30px;
  }
  .mr {
    margin-right: 30px;
  }
  .mr2 {
    margin-right: 32px;
  }
}
.detail-info {
  display: flex;
  align-items: center;
  .avatar-wrap {
    cursor: pointer;
    margin-right: 18px;
    position: relative;
    .avatar,
    .model {
      width: 258px;
      height: 258px;
    }
    .model {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0.8;
    }
    .face-tips {
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      color: #fff;
      text-align: center;
      height: 22px;
      line-height: 22px;
      font-size: 12px;
    }
    &:hover {
      .model {
        display: block;
      }
    }
  }
  .main-info {
    width: 100%;
    color: #313131;
    .info-tit {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      font-size: 16px;
      align-items: center;
      margin-bottom: 18px;
    }
    .info-con {
      width: 100%;
      padding: 20px 24px 4px;
      border: 1px solid #e1e1e1;
      font-size: 16px;
      .info {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
        .label {
          color: #bbb;
        }
      }
      .item {
        display: inline-flex;
        width: 33.3%;
        margin-bottom: 15px;
      }
      .max-item {
        width: 100%;
        display: flex;
        margin-bottom: 20px;
        .rig {
          flex: 1;
        }
      }
      .label {
        display: block;
        width: 120px;
      }
      .tag-wrap {
        color: #fff;
        span {
          font-size: 14px;
          display: inline-block;
          height: 24px;
          line-height: 24px;
          background: #90caf9;
          border-radius: 15px;
          padding: 0 15px;
          margin-right: 15px;
          margin-bottom: 4px;
        }
      }
    }
    .name-wrap {
      margin-right: 50px;
      span {
        cursor: pointer;
      }
    }
    .name {
      display: inline-block;
      vertical-align: middle;
      font-size: 28px;
      font-weight: bold;
      color: #313131;
    }
    .approval-waiting {
      width: 42px;
      vertical-align: middle;
    }
    .suspend-tip {
      display: inline-block;
      text-align: center;
      font-family: SourceHanSansCN-Bold;
      vertical-align: middle;
      color: #f39800;
      font-weight: bold;
      margin-right: 6px;
      width: 80px;
      height: 26px;
      line-height: 26px;
      background-color: rgba(243, 152, 0, 0.15);
      border-radius: 40px;
    }
    .action-wrap {
      margin-right: 35px;
      .label {
        margin-right: 10px;
      }
      .color-red,
      .line-bottom {
        font-size: 16px;
        // font-weight: bold;
      }
      .line-bottom {
        cursor: pointer;
        color: #0068b7;
        text-decoration: underline;
      }
      .color-red {
        text-decoration: none;
        color: #e67371;
      }
    }
  }
}
</style>
