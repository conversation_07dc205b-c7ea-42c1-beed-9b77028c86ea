<template>
  <div class="table-wrap"
       style="border: 0">
    <Table :columns="columns"
           :data="tableData"
           :row-class-name="rowClassName"
           disabled-hover />
    <footer style="justify-content: space-between">
      <Button @click="exportCsv">导出</Button>
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import pager from 'mixins/pager'
  import { formatDate } from 'utils/index'
  export default {
    name: 'trainRecords',
    mixins: [pager],
    props: ['userId'],
    data() {
      return {
        columns: [
          {
            title: '签到时间',
            key: 'create_time',
            render: (h, params) => {
              return h('span',params.row.create_time == 0
                              ? ''
                              : formatDate(new Date(params.row.create_time * 1000), 'yyyy-MM-dd HH:mm'))
            }
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '签到用卡',
            key: 'card_name'
          },
          {
            title: '签到人数',
            key: 'sign_number'
          },
          {
            title: '扣费',
            key: 'consumption'
          },
          {
            title: '签到方式',
            key: 'type',
            render: (h, params) => {
              return h('span', this.signType(params.row.type))
            }
          },
          {
            title: '手环号',
            key: 'brand_number',
            render: (h, params) => {
              if (params.row.brand_number != '') {
                return h('span',params.row.brand_number)
              } else {
                return (<span style = "color: #ccc">{params.row.return_brand_number}</span>)
              }
            }
          },
          {
            title: '归还时间',
            key: 'return_time',
            render: (h, params) => {
              return h('span',params.row.return_time == 0 ? '' : formatDate(new Date(params.row.return_time * 1000), 'MM-d HH:mm'))
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              return (
                <div style="display: flex; justify-content: center">
                  <i-button
                    type="text"
                    style="min-width: 35px"
                    disabled={params.row.status == 1}
                    onClick={() => {
                      this.cancelSignin(params.row);
                    }}>
                    取消签到
                  </i-button>
                </div>
              );
            }
          }
        ],
        tableData: [],
        showCancelSig: false
      }
    },
    created() {
      this.getList()
    },
    methods: {
      rowClassName(row, index) {
        if (row.status !== '0') {
          return 'disabled'
        }
      },
      cancelSignin(row) {
        this.showCancelSig = true;
        this.$Modal.confirm({
          title: '提示',
          content: '您确定要取消签到吗？',
          onOk: () => {
            if (row.brand_number_arr.length > 0) {
              this.$Message.error('请先归还手环号!');
              return;
            }
            this.$service
              .post('/Web/Sign/cancel_user_sign', {
                sign_log_id: row.id
              })
              .then(res => {
                if (res.data.errorcode == 0) {
                  this.getList();
                  this.$Message.success(res.data.errormsg);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              });
            this.showCancelSig = false;
          },
          onCancel() {
            this.showCancelSig = false;
          }
        });
      },
      // 设置取消签到 tr 的'已取消' title
      setDisabledTitle() {
        setTimeout(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled')
          let disabledTrArr = Array.prototype.slice.call(disabledTr)
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已取消')
          })
        }, 500)
      },
      signType(type) {
        switch (+type) {
          case 1:
            return '扫码签到';
          case 2:
            return '系统代签';
          case 3:
            return '指静脉签到';
          case 4:
            return '智能手环';
          case 5:
            return '人脸识别';
          case 8:
            return '按时计费';
          case 10:
            return '蜻蜓机签到';
          case 12:
            return '微信刷掌';
          case 20:
            return '二维码签到';
          case 21:
            return '刷卡签到';
          default:
            return '手动代签';
        }
      },
      async exportCsv() {
        const url = '/Web/Sign/pc_sign_log'
        const postData = {
          user_id: this.userId,
          page_no: 1,
          page_size: this.total,
          is_export: 1,
          _export: 1,
          export_type: 1
        }
        const res = await this.$service.post(url, postData, { loading: false, isExport: true })
        if (res.data.errorcode === 0) {
          this.$Message.success({
            content:'导出任务运行中，请稍后到消息中心下载!',
            duration: 3
          })
        } else {
          this.$Message.error(res.data.errormsg)
        }
      },

      getList() {
        const url = '/Web/Sign/pc_sign_log'
        const postData = {
          user_id: this.userId,
          page_no: this.page,
          page_size: this.pageSize
        }
        this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.total = data.count
              this.tableData = data.sign_log_list.map(item => {
                return { ...item, ...{} }
              })
              this.$nextTick(() => {
                this.setDisabledTitle()
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>

<style scoped>

</style>
