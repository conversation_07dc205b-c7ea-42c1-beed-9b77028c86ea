<template>
  <div>
    <Row class="expand-row" v-for="(item, $index) in list" :key="$index">
      <Col span="5">
        <span>{{ item.order_sn }}</span>
      </Col>
      <Col span="3">
        <span>{{ item.edit_time }}</span>
      </Col>
      <Col span="3">
        <span>{{ item.amount }}</span>
      </Col>
      <Col span="4">
        <span>{{ item.name }}</span>
      </Col>
      <Col span="4">
        <span class="one-line" :title="item.description">{{ item.description }}</span>
      </Col>
      <Col span="4">
        <span>{{ item.marketer_name }}</span>
      </Col>
    </Row>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="less" scoped>
.one-line {
  width: 200px;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
