<template>
  <div class="box" v-if="!noData">
    <div v-if="tabOne" class="panel" style="width:70%;">
      <progress-box :bean="weight"></progress-box>
      <progress-box v-if="testType == 'ts'" :bean="heightBar"></progress-box>
      <progress-box v-if="testType != 'ts'" :bean="skeletal"></progress-box>
      <progress-box v-if="testType != 'ts'" :bean="fat"></progress-box>
      <progress-box v-if="testType != 'ts'" :bean="withoutFat"></progress-box>
      <progress-box :bean="bodyWater"></progress-box>
      <div class="dot-line"></div>
      <progress-box :bean="bodyQuality"></progress-box>
      <progress-box v-if="testType != 'ts'" :bean="basalMetabolism"></progress-box>
      <progress-box :bean="bodyFatRate"></progress-box>
      <progress-box v-if="testType == 'inbody'" :bean="goldRate"></progress-box>
      <!-- 泰山体测仪 -->
      <progress-box v-if="testType === 'ts'" :bean="bodyFatBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="proteinBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="saltBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="heartRateBar"></progress-box>
      <div class="dot-line"></div>
      <progress-box v-if="testType === 'ts'" :bean="reflectBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="gripBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="strengthBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="standOneBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="pushUpBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="jumpBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="breathBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="stairBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="inPressureBar"></progress-box>
      <progress-box v-if="testType === 'ts'" :bean="outPressureBar"></progress-box>
    </div>
    <div v-if="tabOne" class="panel large-margin" style="width:30%;">
      <div class="option">
        <Select v-model="testId" class="spicy-strips" @on-change="handleDateChange">
          <Option v-for="item in dateList" :value="item.id" :key="item.id">{{item.test_time}}</Option>
        </Select>
        <Button @click="tabOne=false" icon="arrow-graph-up-right" class="spicy-strips" style="margin-left: 6px;">体测变化走势</Button>
      </div>
      <div v-if="pageData.ivep_LA || pageData.ivep_CHEST ||  pageData.ivep_HIP ||  pageData.ivep_LT ||  pageData.ivep_RA ||  pageData.ivep_WAIST ||  pageData.ivep_RT " class="body-pic" :class="pageData.ivep_sex === 1 ? '':'pic-women'">
        <div v-if="pageData.ivep_LA" class="pic-part1">{{pageData.ivep_LA}}</div>
        <div v-if="pageData.ivep_CHEST" class="pic-part2">{{pageData.ivep_CHEST}}</div>
        <div v-if="pageData.ivep_HIP" class="pic-part3">{{pageData.ivep_HIP}}</div>
        <div v-if="pageData.ivep_LT" class="pic-part4">{{pageData.ivep_LT}}</div>
        <div v-if="pageData.ivep_RA" class="pic-part5">{{pageData.ivep_RA}}</div>
        <div v-if="pageData.ivep_WAIST" class="pic-part6">{{pageData.ivep_WAIST}}</div>
        <div v-if="pageData.ivep_RT" class="pic-part7">{{pageData.ivep_RT}}</div>
      </div>
      <square-box v-if="testType != 'vis'" :colors="colors1" :labels="labels1"></square-box>
      <square-box v-if="testType != 'vis'" :colors="colors2" :labels="labels2"></square-box>
      <div v-if="testType == 'vis'" class="body3d">
        <iframe :src="url3d" frameborder="0" height="620"></iframe>
      </div>
    </div>
    <div v-else class="trend">
      <Button @click="tabOne=true" icon="arrow-graph-up-right" style="width:100%">体测情况</Button>
      <trend-box beanId="id1" :bean="weightOption" ></trend-box>
      <trend-box beanId="id2" :bean="weightWithoutFatOption" ></trend-box>
      <trend-box beanId="id3" :bean="bodyFatRateOption" ></trend-box>
      <trend-box beanId="id4" :bean="BMIOption" ></trend-box>
      <trend-box beanId="id5" :bean="skeletalMuscleOption" ></trend-box>
      <trend-box beanId="id6" :bean="waistHipRatioOption" ></trend-box>
      <trend-box beanId="id7" :bean="bodyContainWaterOption" ></trend-box>
    </div>
  </div>
  <div v-else><Table></Table></div>
</template>

<script>
import SquareBox from './squareBox.vue'
import ProgressBox from './progressBox.vue'
import TrendBox from './trendBox.vue'

export default {
  data() {
    return {
      noData: false,
      pageData: '',
      url3d: '',
      userId: '',
      testId: '',
      testType: '',
      dateList: [],
      tabOne: true,
      weight: {},
      skeletal: {},
      fat: {},
      withoutFat: {},
      bodyWater: {},
      bodyQuality: {},
      basalMetabolism: {},
      bodyFatRate: {},
      goldRate: {},
      colors1: ['#D5E9EA', '#D5E9EA', '#E4EBE3', '#E4EBE3'],
      colors2: ['#F3E3D4', '#F3E3D4', '#F0DCDA', '#F0DCDA'],
      labels1: {
        leftUpArm: '',
        rightUpArm: '',
        leftDownLeg: '',
        rightDownLeg: '',
        body: ''
      },
      labels2: {
        leftUpArm: '',
        rightUpArm: '',
        leftDownLeg: '',
        rightDownLeg: '',
        body: ''
      },
      weightOption: {
        title: {
          text: '体重'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} kg'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      weightWithoutFatOption: {
        title: {
          text: '去脂体重'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} kg'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      bodyFatRateOption: {
        title: {
          text: '体脂百分比'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} %'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      BMIOption: {
        title: {
          text: 'BMI'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      skeletalMuscleOption: {
        title: {
          text: '骨骼肌'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} kg'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      waistHipRatioOption: {
        title: {
          text: '腰臀比'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      bodyContainWaterOption: {
        title: {
          text: '身体水分含量'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} kg'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            lineStyle: {
              color: '#58CFBA'
            },
            itemStyle: {
              color: '#58CFBA'
            }
          }
        ]
      },
      // 泰山体测仪
      heightBar: {},
      bodyFatBar: {},
      proteinBar: {},
      saltBar: {},
      heartRateBar: {},
      reflectBar: {},
      gripBar: {},
      strengthBar: {},
      standOneBar: {},
      pushUpBar: {},
      jumpBar: {},
      breathBar: {},
      stairBar: {},
      inPressureBar: {},
      outPressureBar: {}
    }
  },
  components: { SquareBox, ProgressBox, TrendBox },
  methods: {
    getDateList() {
      return this.$service.get('/web/Measurebody/getlist?uid=' + this.userId).then(res => {
        if (res.data.errorcode == 0) {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            this.dateList = res.data.data
            this.testId = res.data.data[0].id
            this.testType = res.data.data[0].device
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleDateChange(val) {
      const test = this.dateList.find(item => item.id == val)
      this.testType = test.device
      this.getBodyBuilding()
    },
    packageTrendData(list, wat) {
      let arr = []
      list.forEach(item => {
        arr.push(item[wat])
      })
      return arr
    },
    getTrendList() {
      return this.$service.get('/web/Measurebody/getStaminaTendency?uid=' + this.userId).then(res => {
        if (res.data.errorcode == 0) {
          if (Array.isArray(res.data.data.weight)) {
            this.weightOption.xAxis.data = this.packageTrendData(res.data.data.weight, 'test_time')
            this.weightOption.series[0].data = this.packageTrendData(res.data.data.weight, 'value')

            this.weightWithoutFatOption.xAxis.data = this.packageTrendData(res.data.data.weight_without_fat, 'test_time')
            this.weightWithoutFatOption.series[0].data = this.packageTrendData(res.data.data.weight_without_fat, 'value')

            this.bodyFatRateOption.xAxis.data = this.packageTrendData(res.data.data.body_fat_rate, 'test_time')
            this.bodyFatRateOption.series[0].data = this.packageTrendData(res.data.data.body_fat_rate, 'value')

            this.BMIOption.xAxis.data = this.packageTrendData(res.data.data.BMI, 'test_time')
            this.BMIOption.series[0].data = this.packageTrendData(res.data.data.BMI, 'value')

            this.skeletalMuscleOption.xAxis.data = this.packageTrendData(res.data.data.skeletal_muscle, 'test_time')
            this.skeletalMuscleOption.series[0].data = this.packageTrendData(res.data.data.skeletal_muscle, 'value')

            this.waistHipRatioOption.xAxis.data = this.packageTrendData(res.data.data.waist_hip_ratio, 'test_time')
            this.waistHipRatioOption.series[0].data = this.packageTrendData(res.data.data.waist_hip_ratio, 'value')

            this.bodyContainWaterOption.xAxis.data = this.packageTrendData(res.data.data.body_contain_water, 'test_time')
            this.bodyContainWaterOption.series[0].data = this.packageTrendData(res.data.data.body_contain_water, 'value')
          } else {
            this.noData = true
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    packageBodyItem(label, min, max, cur, lowest, highest) {
      return {
        name: label,
        rLowest: lowest,
        rHighest: highest,
        rLow: min,
        rHigh: max,
        low: (min - lowest) / (highest - lowest),
        normal: (max - min) / (highest - lowest),
        high: (highest - max) / (highest - lowest),
        current: cur
      }
    },
    packageTanInfo(info) {
      this.heightBar = this.packageBodyItem('身高', info.height_min, info.height_max, info.height, 60, 200)
      this.bodyFatBar = this.packageBodyItem('体脂肪', info.body_fat_min, info.body_fat_max, info.body_fat, 0, 35)
      this.proteinBar = this.packageBodyItem('蛋白质', info.protein_min, info.protein_max, info.protein, 0, 25)
      this.saltBar = this.packageBodyItem('无机盐', info.mineral_salt_min, info.mineral_salt_max, info.mineral_salt, 0, 6)
      this.heartRateBar = this.packageBodyItem('安静心率', info.peace_heart_min, info.peace_heart_max, info.peace_heart, 0, 200)
      this.reflectBar = this.packageBodyItem('反应时', info.reaction_time_min, info.reaction_time_max, info.reaction_time, 2, 0)
      this.gripBar = this.packageBodyItem('握力', info.grip_power_min, info.grip_power_max, info.grip_power, 0, 200)
      this.strengthBar = this.packageBodyItem('柔韧性', info.sit_and_reach_min, info.sit_and_reach_max, info.sit_and_reach, 0, 60)
      this.standOneBar = this.packageBodyItem(
        '闭眼单脚站立',
        info.single_stand_min,
        info.single_stand_max,
        info.single_stand,
        0,
        200
      )
      this.pushUpBar = this.packageBodyItem(
        '俯卧撑/仰卧起坐',
        info.up_and_down_min,
        info.up_and_down_max,
        info.up_and_down,
        0,
        150
      )
      this.jumpBar = this.packageBodyItem('纵跳', info.vertical_jump_min, info.vertical_jump_max, info.vertical_jump, 0, 100)
      this.breathBar = this.packageBodyItem(
        '肺活量',
        info.vital_capacity_min,
        info.vital_capacity_max,
        info.vital_capacity,
        0,
        15000
      )
      this.stairBar = this.packageBodyItem('台阶实验', info.step_min, info.step_max, info.step, 0, 200)
      this.inPressureBar = this.packageBodyItem('收缩压', info.systolic_min, info.systolic_max, info.systolic, 70, 200)
      this.outPressureBar = this.packageBodyItem('舒张压', info.diastolic_min, info.diastolic_max, info.diastolic, 40, 170)
    },
    getBodyBuilding() {
      if (this.testId === '' || this.testType === '') {
        return false
      } else {
        return this.$service
          .get(`/web/Measurebody/getdetail?id=${this.testId}&device=${this.testType}&uid=${this.userId}`)
          .then(res => {
            if (res.data.errorcode == 0) {
              const resData = res.data.data
              this.pageData = resData
              this.weight = this.packageBodyItem('体重', resData.weight_min, resData.weight_max, resData.weight, 30, 150)
              this.skeletal = this.packageBodyItem(
                '骨骼肌',
                resData.skeletal_muscle_min,
                resData.skeletal_muscle_max,
                resData.skeletal_muscle,
                5,
                80
              )
              this.fat = this.packageBodyItem('体脂', resData.body_fat_min, resData.body_fat_max, resData.body_fat, 0, 50)
              this.withoutFat = this.packageBodyItem(
                '去脂体重',
                resData.weight_without_fat_min,
                resData.weight_without_fat_max,
                resData.weight_without_fat,
                10,
                100
              )
              this.bodyWater = this.packageBodyItem(
                '身体水分',
                resData.body_contain_water_min,
                resData.body_contain_water_max,
                resData.body_contain_water,
                20,
                80
              )
              this.bodyQuality = this.packageBodyItem(
                '身体质量指数',
                resData.body_quality_min,
                resData.body_quality_max,
                resData.body_quality,
                5,
                50
              )
              this.basalMetabolism = this.packageBodyItem(
                '基础代谢',
                resData.basal_metabolism_min,
                resData.basal_metabolism_max,
                resData.basal_metabolism,
                500,
                3000
              )
              this.bodyFatRate = this.packageBodyItem(
                '体脂率',
                resData.body_fat_rate_min,
                resData.body_fat_rate_max,
                resData.body_fat_rate,
                0,
                50
              )
              this.goldRate = this.packageBodyItem(
                '腰臀比',
                resData.waist_hip_ratio_min,
                resData.waist_hip_ratio_max,
                resData.waist_hip_ratio,
                0,
                1
              )

              if (this.testType != 'vis') {
                this.labels1.name = '肌肉'
                this.labels1.leftUpArm = resData.lla
                this.labels1.rightUpArm = resData.lra
                this.labels1.leftDownLeg = resData.lll
                this.labels1.rightDownLeg = resData.lrl
                this.labels1.body = resData.lt

                this.labels2.name = '脂肪'
                this.labels2.leftUpArm = resData.fla
                this.labels2.rightUpArm = resData.fra
                this.labels2.leftDownLeg = resData.fll
                this.labels2.rightDownLeg = resData.frl
                this.labels2.body = resData.ft

                if (this.testType == 'inbody') {
                  this.labels2.leftUpArmRate = resData.pbfla
                  this.labels2.rightUpArmRate = resData.pbfra
                  this.labels2.leftDownLegRate = resData.pbfll
                  this.labels2.rightDownLegRate = resData.pbfrl
                  this.labels2.bodyRate = resData.pbft
                } else {
                  this.labels2.leftUpArmRate = ''
                  this.labels2.rightUpArmRate = ''
                  this.labels2.leftDownLegRate = ''
                  this.labels2.rightDownLegRate = ''
                  this.labels2.bodyRate = ''
                }

                // FIXME: 泰山体测仪
                if (this.testType === 'ts') {
                  this.packageTanInfo(resData)
                }
              } else {
                this.url3d = `/static/bodyThreeD_PC.html?body=${this.userId}&busId=&userId=${this.userId}&sence=${this.testId}`
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
    // 泰山体测仪
  },
  mounted() {
    this.userId = this.$route.params.userId
    // this.getDateList().then(this.getBodyBuilding).then(this.getTrendList);
    // this.getDateList().then(this.getTrendList);
    this.getDateList().then(() => {
      this.getBodyBuilding()
      this.getTrendList()
    })
  }
}
</script>

<style lang="less" scoped>
.body-pic {
  zoom: 0.385;
  width: 1042px;
  height: 865px;
  margin-top: 60px;
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 20px;
  background: url('~assets/img/man-pic.png') no-repeat center center rgba(242, 247, 252, 1);
  background-size: 795px 729px;
  position: relative;
  div {
    position: absolute;
    width: 170px;
    height: 50px;
    text-align: center;
    font-size: 40px;
    font-weight: bold;
    color: rgba(27, 27, 27, 1);
    line-height: 50px;
  }
}
@media screen and (min-width: 1300px) and (max-width: 1900px) {
  .body-pic {
    zoom: 0.3;
  }
}
@media screen and (max-width: 1300px) {
  .body-pic {
    zoom: 0.24;
  }
}
.pic-women {
  background: url('~assets/img/women-pic.png') no-repeat center center rgba(242, 247, 252, 1);
  background-size: 795px 729px;
}
.pic-part1 {
  left: 141px;
  top: 128px;
}
.pic-part2 {
  left: 141px;
  top: 277px;
}
.pic-part3 {
  left: 141px;
  top: 422px;
}
.pic-part4 {
  left: 141px;
  top: 568px;
}
.pic-part5 {
  left: 730px;
  top: 128px;
}
.pic-part6 {
  left: 730px;
  top: 344px;
}
.pic-part7 {
  left: 730px;
  top: 568px;
}
@media screen and (min-width: 1900px) {
  .spicy-strips {
    width: 200px;
  }
  .large-margin {
    padding-left: 60px;
  }
}

@media screen and (min-width: 1300px) and (max-width: 1900px) {
  .spicy-strips {
    width: 140px;
  }
  .large-margin {
    padding-left: 30px;
  }
}

@media screen and (max-width: 1300px) {
  .spicy-strips {
    width: 120px;
  }
  .large-margin {
    padding-left: 20px;
  }
}

.box {
  padding: 10px;
  display: flex;
  flex-direction: row;

  .panel {
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .dot-line {
      border: 1px dotted gray;
    }

    .body3d {
      height: 620px;
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }

  .trend {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
