<template>
  <div class="progress-box">
    <div class="name">{{bean.name}}</div>
    <div class="value" :style="{left: obean.current+'px'}">{{bean.current}}</div>
    <div class="progress-bar">
      <div v-if="bean.rLowest != bean.rLow" class="progress-low" :style="{width: obean.low+'px'}">
        <div class="stage">{{bean.rLowest}}</div>
        <div class="label">低标准</div>
        <div class="stage"></div>
      </div>
      <div class="progress-normal" :style="{width: obean.normal+'px'}">
        <div class="stage">{{bean.rLow}}</div>
        <div class="label">标准</div>
        <div class="stage">{{bean.rHigh}}</div>
      </div>
      <div v-if="bean.rHigh != bean.rHighest" class="progress-high" :style="{width: obean.high+'px'}">
        <div class="stage"></div>
        <div class="label">高标准</div>
        <div class="stage">{{bean.rHighest}}</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      bean: Object
    },
    computed: {
      obean() {
        return {
          low: this.bean.low * this.wade,
          normal: this.bean.normal * this.wade,
          high: this.bean.high * this.wade,
          current: (this.bean.current - this.bean.rLowest) / (this.bean.rHighest - this.bean.rLowest) * this.wade
        };
      }
    },
    data() {
      return {
        wade: 0
      };
    },
    mounted() {
      this.wade = document.querySelector(".progress-bar").offsetWidth;
    }
  };
</script>


<style lang="less" scoped>
  .progress-box {
    display: flex;
    flex-direction: row;

    .name {
      font-size: 16px;
      // font-weight: bold;
      width: 100px;
      min-width: 100px;
      text-overflow: ellipsis;
    }

    .value {
      height: 27px;
      width: 2px;
      border: 2px dotted black;
      position: relative;
      color: black;
      font-size: 16px;
      // font-weight: bold;
    }

    .progress-bar {
      height: 27px;
      padding: 5px 0;
      width: calc(100% - 130px);
      display: flex;
      flex-direction: row;
      color: white;

      .progress-low,
      .progress-normal,
      .progress-high {
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 6px;
      }

      .progress-low {
        background-color: #ff9596;
      }
      .progress-normal {
        background-color: #85bff0;
      }
      .progress-high {
        background-color: #8ef3c7;
      }
    }
  }
</style>
