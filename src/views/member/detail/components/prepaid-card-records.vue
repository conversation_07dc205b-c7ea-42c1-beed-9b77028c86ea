<template>
    <div class="table-wrap" style="border: 0">
        <header style="height: 60px; font-size: 16px">
            <p style="max-width: 500px">储值卡共计消费:
                <b>{{totalAmount}}</b>元</p>
            <p style="padding-left: 50px; max-width: 500px">储值卡共计剩余:
                <b>{{leftAmount}}</b>元</p>
        </header>
        <Table :columns="columns" :data="tableData" disabled-hover/>
        <footer style="justify-content: flex-end">
            <Page :total="+total" :current.sync="page" placement="top" show-total show-sizer @on-change="getList"
                  @on-page-size-change="pageSizeChanged"/>
        </footer>
    </div>
</template>

<script>
  import pager from 'mixins/pager';

  export default {
    name: 'prepaidCardRecords',
    props: ['userId'],
    mixins: [pager],
    data() {
      return {
        totalAmount: '',
        leftAmount: '',

        total: '',
        columns: [
          {
            title: '时间',
            key: 'consumption_time'
          },
          {
            title: '场馆',
            key: 'bus_name'
          },
          {
            title: '消费类型',
            key: 'consumption_type'
          },
          {
            title: '消费用卡',
            key: 'card_name'
          },
          {
            title: '消费金额',
            key: 'amount'
          }
        ],
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      getList() {
        const url = '/Web/Member/cash_card_consumption_list';
        this.$service
            .post(url, {user_id: this.userId, page_no: this.page, page_size: this.pageSize})
            .then(res => {
              if (res.data.errorcode === 0) {
                const data = res.data.data;
                this.totalAmount = data.all_consumption_amount;
                this.leftAmount = data.all_last_amount;
                this.total = data.count;
                this.tableData = data.list.map(item => {
                  return {
                    ...item,
                    ...{
                      consumption_type: item.ext_consumption_type ? item.ext_consumption_type :
                          item.consumption_type == 1 ? '签到扣费' :
                              item.consumption_type == 2 ? '预约课程' :
                                  item.consumption_type == 3 ? '商品消费' : '购卡消费'
                    }
                  };
                });
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
      }
    }
  };
</script>

<style scoped>
</style>
