<template>
  <div class="table-wrap"
       style="border: 0">
    <Table :columns="columns"
           disabled-hover
           :data="tableData" />
    <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import Pager from 'mixins/pager'
  import { mapState, mapActions } from 'vuex'
  export default {
    name: 'followRecords',
    props: ['userId'],
    mixins: [Pager],
    computed: {
      ...mapState(['addFollowSuccess'])
    },
    data() {
      return {
        showAdd: false,
        marketers_id: {},
        follow_up_mode: '',
        description: '',
        columns: [
          {
            title: '时间',
            key: 'create_time'
          },
          {
            title: '人员',
            key: 'marketers_name',
            render: (h, params) => {
              let labelDes = ''
              if (params.row.label) {
                labelDes = (<span class="gray">({params.row.label||''})</span>)
              }
              return (<div>{params.row.marketers_name}{labelDes}</div>)
            }
          },
          {
            title: '描述',
            tooltip: true,
            key: 'description'
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, params) => {
              let operation = params.row
              return (
                <i-button
                  type="text"
                  class="button-text-red"
                  onClick={() => {
                    this.delFollow(operation)
                  }}>
                  删除
                </i-button>
              )
            }
          }
        ],
        tableData: []
      }
    },
    created() {
      this.getList()
    },
    watch: {
      addFollowSuccess(val) {
        if(val){
          this.getList();
          this.$store.commit('SET_ADD_FollOW_SUCCESS', false)
        }
      }
    },
    methods: {
      ...mapActions(['getMembershipList']),
      delFollow(info) {
        const url = '/Web/Member/delFollowUpLog'
        const postData = {
          user_id: this.userId,
          id: info.id,
          type: info.type
        }
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getList()
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getList() {
        const url = '/Web/Member/getFollowUpLog'
        const postData = {
          user_id: this.userId,
          page_no: this.page,
          page_size: this.pageSize
        }
        this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.total = data.count
              this.tableData = data.followup_log
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>

<style scoped>

</style>
