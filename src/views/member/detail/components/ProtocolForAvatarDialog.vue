<template>
  <Modal v-model="show" title="会员人脸识别使用告知" width="680" :mask-closable="false" :closable="true" @on-cancel="handleClose">
    <div class="protocol-content">
      <!-- 隐私政策说明 -->
      <div class="privacy-notice">
        依据国家《个人隐私保护法》规定，采集人脸信息前须向用户说明用途，并取得用户同意。
      </div>

      <!-- 协议内容 -->
      <div class="protocol-sections" ref="protocolContainer" @scroll="handleScroll">
        <div v-html="protocol"></div>
      </div>

      <!-- 滚动提示 -->
      <div v-if="!hasScrolledToBottom" class="scroll-hint">
        <Icon type="ios-arrow-down" />
        请滚动阅读完整协议内容
      </div>

      <!-- 底部提示 -->
      <div class="bottom-notice">
        推荐用户在《会员小程序》自行上传头像。
      </div>
    </div>

    <div slot="footer" class="modal-footer">
      <Button @click="handleClose" class="cancel-btn">关闭</Button>
      <Button v-if="!hasScrolledToBottom || submitting" :loading="submitting" disabled>请先阅读全文</Button>
      <Button v-else type="success" @click="handleConfirm" class="confirm-btn">会员已知晓并同意采集其人脸信息</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ProtocolForAvatarDialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
      required: false,
    },
  },
  data() {
    return {
      submitting: false,
      hasScrolledToBottom: false,
      protocol: `<div style="text-align: start;color: rgb(0, 0, 0); line-height: 1.6;">
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">您正在使用人脸识别技术录入会员信息，请务必与会员确认如下信息：</span></strong></strong></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><span style="font-size:14px;font-family: 微软雅黑;">1、</span></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">明确告知</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">会员</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">采集目的：</span></strong></strong></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;我们将使用人脸识别设备进行身份核验和便捷入场管理。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">2、明确告知</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">会员</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">信息处理规则：</span></strong></strong></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;您的人脸信息仅用于本场馆的身份核验与服务管理。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;我们会采取安全措施存储您的人脸信息，原始图像会进行加密或特征值处理。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;在您会员服务到期不再续费后，或您主动要求时，我们会按法规删除您的人脸信息。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">3、明确告知会员权利与替代方式：</span></strong></strong></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;采集人脸信息需要您的明确同意，您有权拒绝。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;如果您不同意采集人脸信息，我们不会强制要求，并将为您提供其他入场方式（例如：刷会员卡、人工核验会员码等）。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">4、明确告知</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">会员有</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">删除权：</span></strong></strong></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&ldquo;在您的会员服务到期后，您有权要求我们删除您的个人信息（包括人脸信息）。&rdquo;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><span style="font-size:14px;font-family: 微软雅黑;">&nbsp;</span></p>
    <p style="text-align: justify;font-size:14px;font-family: Calibri;"><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">如您点击&ldquo;同意&rdquo;并确认提交会员人脸信息，即视为贵方已经获得信息提供方</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">（</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">会员</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">）</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">的授权，信息提供方</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">（</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">会员</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">）</span></strong></strong><strong><strong><span style="font-size:14px;font-family: 微软雅黑;">同意本平台按照生物识别协议中的规则收集、使用、保存和对外提供个人信息。</span></strong></strong></p>
</div>`,
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.initData()
        } else {
          this.resetData()
        }
      },
      immediate: false,
    },
  },
  mounted() {
    // 检查内容是否需要滚动
    this.$nextTick(() => {
      this.checkScrollStatus()
    })
  },
  methods: {
    initData() {
      this.submitting = false
      this.hasScrolledToBottom = false
      // 延迟检查滚动状态，确保DOM已渲染
      this.$nextTick(() => {
        // 重置滚动位置到顶部
        const container = this.$refs.protocolContainer
        if (container) {
          container.scrollTop = 0
        }
        this.checkScrollStatus()
      })
    },
    resetData() {
      this.submitting = false
      this.hasScrolledToBottom = false
    },
    // 检查滚动状态
    checkScrollStatus() {
      const container = this.$refs.protocolContainer
      if (!container) return

      // 如果内容高度小于等于容器高度，说明不需要滚动
      if (container.scrollHeight <= container.clientHeight) {
        this.hasScrolledToBottom = true
      }
    },
    // 处理滚动事件
    handleScroll() {
      const container = this.$refs.protocolContainer
      if (!container) return

      // 计算是否滚动到底部
      // 使用 Math.ceil 处理浮点数精度问题，允许1像素的误差
      const isAtBottom = Math.ceil(container.scrollTop + container.clientHeight) >= container.scrollHeight - 1

      if (isAtBottom && !this.hasScrolledToBottom) {
        this.hasScrolledToBottom = true
      }
    },
    // 确认
    async handleConfirm() {
      this.submitting = true

      try {
        // 这里可以添加API调用来记录用户同意
        const res = await this.$service.post('/Web/Member/sure_face_context', {
          user_id: this.userId,
        })

        if (res.data.errorcode !== 0) {
          this.$Message.error(res.data.errormsg || '操作失败，请重试')
          return
        }

        this.$Message.success('已记录用户同意')
        this.$emit('on-success')
        this.handleClose()
      } catch (error) {
        this.$Message.error('操作失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    // 关闭弹窗
    handleClose() {
      this.resetData()
      this.$emit('input', false)
    },
  },
}
</script>

<style lang="less" scoped>
// 居中显示模态框标题
:deep(.ivu-modal-header) {
  text-align: center;
}

.protocol-content {
  // padding: 20px 0;
  line-height: 1.6;

  .privacy-notice {
    font-size: 14px;
    color: #ff6b6b;
    line-height: 1.6;
    margin-bottom: 30px;
    padding: 0;
    background: none;
    border: none;
    font-weight: 400;
  }

  .protocol-sections {
    margin-bottom: 30px;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    // background-color: #fafafa;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .scroll-hint {
    text-align: center;
    color: #1890ff;
    font-size: 12px;
    margin: 10px 0;
    animation: bounce 2s infinite;

    i {
      margin-right: 4px;
    }
  }

  .bottom-notice {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    padding: 12px 0;
    background: none;
    border: none;
    text-align: left;
    font-style: italic;
  }
}

.modal-footer {
  text-align: right;
  padding-top: 20px;

  .cancel-btn {
    margin-right: 12px;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;
    min-width: 80px;
    height: 32px;
    font-weight: 400;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;

    &:hover {
      background-color: #e6e6e6;
      border-color: #bfbfbf;
      color: #666;
    }
  }

  .confirm-btn {
    background-color: #52c41a;
    border-color: #52c41a;
    color: white;
    min-width: 200px;
    height: 32px;
    font-weight: 400;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;

    &:hover {
      background-color: #73d13d;
      border-color: #73d13d;
    }

    &:disabled {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
    }
  }
}

// 动画定义
@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-5px);
  }

  60% {
    transform: translateY(-3px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .protocol-content {
    .protocol-sections {
      .protocol-section {
        .section-content {
          padding-left: 0;
        }
      }
    }
  }

  .modal-footer {
    .confirm-btn {
      min-width: 180px;
      font-size: 13px;
    }
  }
}
</style>