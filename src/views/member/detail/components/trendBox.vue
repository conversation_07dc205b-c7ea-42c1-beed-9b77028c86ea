<template>
  <div class="trend-box">
    <div :id="beanId" class="canvas"></div>
  </div>
</template>

<script>
  import * as echarts from "echarts";

  export default {
    props: {
      beanId: String,
      bean: Object
    },
    mounted() {
      let chart = echarts.init(document.querySelector("#"+this.beanId));
      chart.setOption(this.bean);
    }
  };
</script>

<style lang="less">

  @media screen and (min-width: 1900px) {
    .canvas {
      height: 400px;
      width: 800px;
    }
  }

  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .canvas {
      height: 400px;
      width: 530px;
    }
  }

  @media screen and (max-width: 1300px) {
    .canvas {
      height: 400px;
      width: 500px;
    }
  }

</style>
