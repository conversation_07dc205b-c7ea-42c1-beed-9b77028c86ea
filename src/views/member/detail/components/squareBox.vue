<template>
  <div class="square-box">
    <div class="square img1" :style="{backgroundColor: colors[0]}">
      <div class="square-label">
        <div class="percent" v-if="labels.leftUpArmRate">{{labels.leftUpArmRate}}%</div>
        <div class="value">{{labels.leftUpArm}}kg</div>
        <!-- <div class="name">低标准</div> -->
      </div>
      <div class="square-label">节段</div>
    </div>
    <div class="square img2" :style="{backgroundColor: colors[1]}">
      <div class="square-label">{{labels.name}}</div>
      <div class="square-label">
        <div class="percent" v-if="labels.rightUpArmRate">{{labels.rightUpArmRate}}%</div>
        <div class="value">{{labels.rightUpArm}}kg</div>
        <!-- <div class="name">正常</div> -->
      </div>
    </div>
    <div class="square img3" :style="{backgroundColor: colors[2], alignItems: 'flex-end'}">
      <div class="square-label">
        <div class="percent" v-if="labels.leftDownLegRate">{{labels.leftDownLegRate}}%</div>
        <div class="value">{{labels.leftDownLeg}}kg</div>
        <!-- <div class="name">低标准</div> -->
      </div>
      <div class="square-label"></div>
    </div>
    <div class="square img4" :style="{backgroundColor: colors[3], alignItems: 'flex-end'}">
      <div class="square-label"></div>
      <div class="square-label">
        <div class="percent" v-if="labels.rightDownLegRate">{{labels.rightDownLegRate}}%</div>
        <div class="value">{{labels.rightDownLeg}}kg</div>
        <!-- <div class="name">正常</div> -->
      </div>
    </div>
    <div class="square-center">
      <div class="name">躯干</div>
      <div class="percent" v-if="labels.bodyRate">{{labels.bodyRate}}%</div>
      <div class="value">{{labels.body}}kg</div>
      <!-- <div class="state">正常</div> -->
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      colors: Array,
      labels: Object
    }
  };
</script>

<style lang="less" scoped>

  @media screen and (min-width: 1900px) {
    .square-box {
      height: 300px;
      width: 400px;
    }

    .square-center {
      height: 300px;
      width: 400px;
    }

    .img1 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 214% -37%
    }
    .img2 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -115% -37%
    }
    .img3 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 214% 140%
    }
    .img4 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -115% 140%
    }
  }

  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .square-box {
      height: 300px;
      width: 310px;
    }

    .square-center {
      height: 300px;
      width: 310px;
    }

    .img1 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 537% -37%
    }
    .img2 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -437% -37%
    }
    .img3 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 537% 140%
    }
    .img4 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -437% 140%
    }
  }

  @media screen and (max-width: 1300px) {
    .square-box {
      height: 290px;
      width: 250px;
    }

    .square-center {
      height: 290px;
      width: 250px;
    }

    .img1 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -350% -37%
    }
    .img2 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 437% -37%
    }
    .img3 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: -350% 140%
    }
    .img4 {
      background-image: url("../../../../assets/img/white-people.png");
      background-repeat: no-repeat;
      background-position: 437% 140%
    }
  }

  .square-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 20px;

    .square {
      height: 50%;
      width: 50%;
      border: 1px solid gainsboro;
      opacity: .7;

      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .square-label {
        font-size: 18px;

        .value {
          font-size: 14px;
        }

        .name {
          font-size: 16px;
        }

        .percent {
          font-size: 14px;
        }
      }
    }

    .square-center {
      position: absolute;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      // font-weight: bold;
    }
  }
</style>
