<template>
  <div class="table-wrap"
       style="border: 0">
    <Table :columns="columns"
           disabled
           :row-class-name="rowClassName"
           :data="tableData"
           disabled-hover />
    <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import { formatDate } from 'utils'
  import pager from 'mixins/pager'
  export default {
    name: 'reservationRecords',
    mixins: [pager],
    props: ['userId'],
    data() {
      return {
        columns: [
          {
            title: '开课时间',
            key: 'class_time'
          },
          {
            title: '课程名称',
            key: 'class_name'
          },
          {
            title: '上课教练',
            key: 'coach_name'
          },
          {
            title: '预约用卡',
            key: 'card_name',
            render: (h, params) => {
               return (<div>{ params.row.card_name || '无/不使用会员卡'}</div>)
            }
          },
          {
            title: '预约人数',
            key: 'sign_number',
            render: (h, params) => {
              const num = this.getSignNum(params.row)
              if (!!params.row.sign_number && params.row.is_miss==1) {
                return (<div>{num}<span style="color:#e60012;">(爽约)</span></div>);
              } else {
                return (<div>{num}</div>);
              }
            }
          },
          {
            title: '预约时间',
            key: 'create_time',
            render: (h, params) => {
              return (<div>{formatDate(new Date(params.row.create_time * 1000), 'yyyy-MM-dd HH:mm')}</div>);
            }
          },
          {
            title: '签到方式',
            key: 'sign_type'
          }
        ],
        tableData: []
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getSignNum(obj) {
        return `${obj.sign_number}人${obj.consumption?`(${obj.consumption})`: ''}`
      },
      rowClassName(row, index) {
        if (row.status === '3') {
          return 'disabled'
        }
      },
      signType(type) {
        switch (+type) {
          case 1:
            return '扫码签到';
          case 2:
            return '系统代签';
          case 3:
            return '指静脉签到';
          case 4:
            return '智能手环';
          case 5:
            return '人脸识别';
          case 8:
            return '按时计费';
          case 10:
            return '蜻蜓机签到';
          default:
            return '手动代签';
        }
      },
      // 设置取消签到 tr 的'已取消' title
      setDisabledTitle() {
        setTimeout(() => {
          let disabledTr = document.querySelectorAll('tr.ivu-table-row.disabled')
          let disabledTrArr = Array.prototype.slice.call(disabledTr)
          disabledTr.forEach(tr => {
            tr.setAttribute('title', '已取消')
          })
        }, 500)
      },
      getList() {
        const url = '/Web/ClassMark/user_class_mark_list'
        const postData = {
          user_id: this.userId,
          page_no: this.page,
          page_size: this.pageSize
        }
        this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.total = data.count
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    class_time: formatDate(new Date(item.b_time * 1000), 'yyyy-MM-dd HH:mm'),
                    sign_type: item.type && this.signType(item.type)
                  }
                }
              })
              this.$nextTick(() => {
                this.setDisabledTitle()
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>

<style scoped>

</style>
