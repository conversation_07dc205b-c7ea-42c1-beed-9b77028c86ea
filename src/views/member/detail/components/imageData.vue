<template>
  <div style="display: flex; flex-direction: column">
    <div style="margin: 30px 40px 20px auto; display: flex; align-items: center">
      <Upload
        ref="upload"
        :show-upload-list="false"
        :default-file-list="list"
        :on-success="handleSuccess"
        :format="['jpg','jpeg','png']"
        :max-size="8192"
        :on-format-error="handleFormatError"
        :on-exceeded-size="handleMaxSize"
        multiple
        type="drag"
        :data="{savePath: './Uploads/'}"
        :action="getBaseUrl() + '/Admin/Public/upload'"
        style="display: inline-block">
        <Button type="success">电脑上传</Button>
      </Upload>
      <Button v-if="!suspend" style="margin-left: 30px;" @click="showUpload" type="primary">手机上传</Button>
    </div>
    <div class="image-data">
      <div class="img-item" v-for="(item, index) in uploadListComputed" :key="item.uid">
        <div class="img">
          <img v-if="item.status === 'finished'" :src="item.url" alt=""/>
          <Progress v-if="item.showProgress" :percent="item.percentage" hide-info></Progress>
          <div @click.stop="handleModal(item.uid)" class="modal" title="查看">
            <Icon @click.stop="handleDelete(item)" class="delete-icon" title="删除" color="#fff" size="22"
                  type="md-trash"/>
          </div>
        </div>
        <div v-if="item.status === 'finished'" class="name">
          <template v-if="!item.isEdit">
            <div class="text">{{item.name}}</div>
            <FaIcon @click="handleEditName(item.uid)" class="name-edit" title="编辑" color="#5cb85c" size="16"
                    name="pencil-square-o"/>
          </template>
          <template v-else>
            <Input v-model="item.name"/>
            <Icon title="保存" class="icon" @click.native="handleConfirmEdit(item)" color="#5cb85c" size="22"
                  type="md-checkmark-circle"/>
            <Icon title="取消" class="icon" @click.native="handleCancelEdit(item.uid)" color="red" size="22"
                  type="md-close-circle"/>
          </template>
        </div>
      </div>
    </div>

    <Modal v-model="showModal" :title="modalTitle" width="800px" style="text-align: center">
      <img ref="modalImg" style="width: 700px" :src="modalImg" alt="">
      <div slot="footer">
        tips：使用「alt + 鼠标滚轮」可以放大图片，使用「shift + 鼠标滚轮」可以左右滚动
      </div>
    </Modal>
    <Modal v-model="showUploadQr" :mask-closable="false" width="400px" title="图片上传">
      <div style="padding: 20px 10px">
        <img style="width: 100%" :src="codeImg" alt="">
      </div>
      <div style="text-align: center; font-size: 14px">
        <p>手机扫码二维码上传图片</p>
        <p>上传完成后点击刷新</p>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button @click="handleRefresh" type="success">刷新</Button>
        <Button @click="showUploadQr = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { getBaseUrl } from 'src/utils/config';

  export default {
    name: 'imageData',
    props: {
      userId: {
        type: [String, Number]
      },
      suspend: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        initialData: [],
        getBaseUrl,
        list: [],
        modalTitle: '',
        showModal: false,
        modalImg: '',
        showUploadQr: false,
        codeImg: '',
        uploadList: []
      };
    },
    computed: {
      uploadListComputed() {
        const arr = this.uploadList.filter(item => !this.initialData.includes(item.id)) || []
        if (this.suspend) {
          return arr
        } else {
          return this.uploadList
        }
      }
    },
    watch: {
      showModal(val) {
        if (!val) {
          this.$refs.modalImg.style.zoom = '100%';
        }
      }
    },
    async created() {
      await this.getList()
      this.initialData = this.uploadList.map(item => item.id)
    }, 
    mounted() {
      document.addEventListener('mousewheel', this.imageZoom);
    },
    destroyed() {
      document.removeEventListener('mousewheel', this.imageZoom);
    },
    methods: {
      imageZoom: function (ev) {
        if (this.$refs.modalImg && this.showModal && !ev.shiftKey && ev.altKey) {
          const delta = ev.wheelDelta;
          let zoom = parseInt(this.$refs.modalImg.style.zoom, 10) || 100;
          zoom += delta / 10;
          if (zoom > 0) {
            this.$refs.modalImg.style.zoom = zoom + '%';
          }
        }
      },
      updateList() {
        this.$nextTick(() => {
            this.uploadList = this.$refs.upload.fileList
        });
      },
      handleRemove(file) {
        const fileList = this.$refs.upload.fileList;
        this.$refs.upload.fileList.splice(fileList.indexOf(file), 1);
      },
      async handleSuccess(res, file, fileList) {
        const url = `${res.info}@70q_1pr`;
        if (url !== null && url.length > 0) {
          this.handleSaveUrl({ url, name: file.name }).catch(() => {
            this.handleRemove(file);
          });
        } else {
          this.$Message.error('上传图片失败！');
        }
      },
      handleFormatError(file) {
        this.$Notice.warning({
          title: '图片格式错误',
          desc: '文件 ' + file.name + ' 格式错误，请选择 jpg 或 png 格式。'
        });
      },
      handleMaxSize(file) {
        this.$Notice.warning({
          title: '图片体积过大',
          desc: '图片 ' + file.name + ' 体积过大，不能超过 8M。'
        });
      },

      handleSaveUrl(image) {
        const url = '/Web/ImageLib/upload';
        const postData = {
          bus_id: this.$store.state.busId,
          user_id: this.userId || this.$route.params.userId,
          from: 1,
          images: [image]
        };
        return this.$service.post(url, { data: JSON.stringify(postData) }).then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
          } else {
            this.$Message.error(res.data.errormsg);
            throw new Error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleRefresh() {
        this.showUploadQr = false;
        this.getList();
      },
      getList(loading = true) {
        const url = '/Web/ImageLib/get_list';
        return this.$service.post(url, { user_id: this.userId || this.$route.params.userId}, { loading }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.list;
            this.list = data.map(item => ({ ...item, isEdit: false }));
            this.updateList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleEditName(uid) {
        this.uploadList.find(item => item.uid === uid).isEdit = true;
        this.$forceUpdate();
      },
      handleConfirmEdit({ id, name, uid }) {
        const url = '/Web/ImageLib/update_image';
        this.$service.post(url, { id, name }).then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg);
            this.handleCancelEdit(uid);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleCancelEdit() {
        this.getList(false);
      },
      handleModal(uid) {
        this.modalTitle = this.uploadList.find(item => item.uid === uid).name;
        this.modalImg = this.uploadList.find(item => item.uid === uid).url;
        this.showModal = true;
      },
      handleDelete(file) {
        this.$Modal.confirm({
          title: '删除照片',
          width: 600,
          content: `<div style="padding: 20px 42px 20px 0"><img style="width: 100%" src="${file.url}" ></div>`,
          okText: '删除',
          onOk: () => this.deleteImage(file)
        });
      },
      deleteImage(file) {
        const url = '/Web/ImageLib/delete_image';
        this.$service.post(url, { id: file.id }).then(res => {
          if (res.data.errorcode === 0) {
            this.handleRemove(file);
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      showUpload() {
        const url = '/Web/ImageLib/qrcode';
        const { username } = this.$store.state.userDetailInfo;
        this.codeImg = `${getBaseUrl()}${url}?username=${username || '佚名'}&user_id=${this.userId || this.$route.params.userId}`;
        this.showUploadQr = true;
      }
    },
  };
</script>

<style>
  .ivu-upload-drag {
    border: 0;
  }

  .ivu-upload-drag:hover {
    border: 0;
  }
</style>

<style scoped lang="less">

  .demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    margin-right: 4px;
  }

  .demo-upload-list img {
    width: 100%;
    height: 100%;
  }

  .image-data {
    display: flex;
    flex-wrap: wrap;
    max-height: 600px;
    overflow-y: scroll;

    .img-item {
      width: 200px;
      margin: 16px;

      .name-edit {
        cursor: pointer;
        margin-left: auto;
      }

      .img {
        position: relative;
        height: 200px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f5f7;

        .modal {
          opacity: 0;
          z-index: 2;
          position: absolute;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, .5);

          .delete-icon {
            position: absolute;
            top: 5px;
            right: 5px;
          }
        }

        &:hover {
          cursor: pointer;

          .modal {
            opacity: 1;
          }
        }

        > img {
          max-width: 100%;
          max-height: 100%;
          position: relative;
          z-index: 1;
        }
      }

      .name {
        padding-top: 10px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .text {
          max-width: 60%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .icon {
          cursor: pointer;
        }

        .text {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
</style>
