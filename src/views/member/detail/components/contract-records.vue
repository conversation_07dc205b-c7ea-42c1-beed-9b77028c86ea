<template>
  <!-- 会员详情-合同记录 -->
  <div class="table-wrap" style="border: 0">
    <Table disabled-hover :columns="column" :row-class-name="rowClassName" :data="tableData" />
    <footer style="justify-content: flex-end">
      <Page
        :total="+total"
        :current.sync="page"
        placement="top"
        show-total
        show-sizer
        @on-change="getContractList"
        @on-page-size-change="pageSizeChanged"
      />
    </footer>
    <Modal title="撤销" v-model="showCancel">
      <span style="font-size: 14px; font-weight: bold; margin-bottom: 5px; display: block">撤销原因：</span>
      <Input
        type="textarea"
        v-model="cancelReason"
        :rows="5"
        :autosize="{ minRows: 4, maxRows: 6 }"
        placeholder="请输入撤销原因..."
      />
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="doCancel">确定</Button>
        <Button @click="showCancel = false">取消</Button>
      </div>
    </Modal>
    <Modal title="编辑合同" v-model="showEdit" :mask-closable="false" width="700">
      <Form class="modal-form" :model="modalEditRecord" style="padding: 0 30px" :label-width="80" label-position="left">
        <FormItem label="订单编号">
          <div style="font-size: 14px">{{ modalEditRecord.order_sn }}</div>
        </FormItem>

        <AmountItems
          ref="amountItems"
          v-if="showEdit"
          :from="from"
          actionType="edit"
          :cardTypeId="modalEditRecord.card_type_id"
          v-model="modalEditRecord"
          :hasStore="hasStore"
          :isStore="isStore"
        ></AmountItems>

        <FormItem label="编辑原因" prop="description">
          <Input type="textarea" v-model="modalEditRecord.description" :autosize="{ minRows: 4, maxRows: 8 }" />
        </FormItem>
      </Form>
      <footer slot="footer" class="modal-buttons">
        <Button type="success" @click="handleEditRecord">确定</Button>
        <Button @click="handleEditCancel">取消</Button>
      </footer>
    </Modal>
    <Modal :title="modalTitle" v-model="showModal">
      <Table ref="modalTable" :columns="modalColumns" :data="modalTableData" disabledHover></Table>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import AmountItems from 'components/member/amountItems'
import SubContractRecords from './SubContractRecords'
import { computed } from 'vue'

const MODAL_TITLE = ['支付金额', '支付方式', '业绩归属']
const COLUMNS = {
  0: [
    {
      title: '类型',
      key: 'name',
    },
    {
      title: '金额',
      key: 'value',
    },
  ],
  1: [
    {
      title: '支付方式',
      key: 'payType',
    },
    {
      title: '支付金额',
      key: 'amount',
    },
  ],
  2: [
    {
      title: '姓名',
      key: 'marketers_name',
    },
    {
      title: '归属/协助',
      key: 'isMain',
    },
    {
      title: '贡献占比',
      key: 'percent',
    },
    {
      title: '业绩金额',
      key: 'amount',
    },
  ],
}

export default {
  name: 'contractRecords',
  props: ['userId'],
  components: { AmountItems, SubContractRecords },
  provide() {
    return {
      sqbServInfo: computed(() => this.sqbServInfo),
    }
  },
  data() {
    return {
      sqbServInfo: null,
      from: '',
      modalColumns: [],
      modalTableData: [],
      modalTitle: '',
      showModal: false,
      pageSize: 10,
      page: 1,
      total: 0,
      showCancel: false,
      showEdit: false,
      modalEditRecord: {
        new_pay_type: [],
        help_deal: [{ marketers_id: '', marketers_name: '', amount: '', percent: 100 }],
        sale_amount: '',
        income_amount: '',
        front_ids: [],
        amount: '',
        is_front_money: 0,
        order_sn: '',
        description: '',
        marketers_id: '',
        pay_type: '0',
      },
      cancelReason: '',
      cardorder_info_id: '',
      disabledEditNames: ['体验卡', '在线购课', '转卡（出）', '升卡（跨店出）', '租柜', '请假', '补卡'],
      disabledCancelNames: ['体验卡', '转卡（出）', '升卡（跨店出）', '销卡'],
      column: [
        {
          type: 'expand',
          width: 50,
          render(h, params) {
            return h(SubContractRecords, {
              props: {
                list: params.row.sub_custom,
              },
            })
          },
        },
        {
          title: '合同编号',
          key: 'order_sn',
          render: (h, params) => {
            let order_sn = params.row.order_sn
             const orderSignTexts = {
                '1': '未签',
                '2': '线下签署',
                '3': '会员端签署',
              };
            return (
              <div>
                {order_sn.order_sn}
                <span style={{ color: params.row.order_sign_status === 1 ? '#d9544f' : '#5cb85c' }}>
                  {order_sn.status === '1' ? '(已撤销)' : params.row.order_sign_status === 1 || params.row.order_sign_status === 2 ||params.row.order_sign_status === 3 ? `(${orderSignTexts[params.row.order_sign_status]})` : ''}
                </span>
              </div>
            )
          },
        },
        {
          title: '最新编辑时间',
          key: 'edit_time',
        },
        {
          title: '金额',
          key: 'amount',
        },
        {
          title: '类型',
          key: 'name',
        },
        {
          title: '描述',
          key: 'description',
          ellipsis: true,
          render: (h, params) => {
            let description = params.row.description
            return (
              <div title={description} style="text-overflow: ellipsis; overflow: hidden">
                {description}
              </div>
            )
          },
        },
        {
          title: '业绩归属',
          key: 'marketer_name',
          width: '120px',
          render: (h, params) => {
            const item = params.row
            if (item.marketer_name.split(',').length > 1) {
              return (
                <i-button
                  onClick={() => {
                    this.handleDetail(2, item.cardorder_info_id)
                  }}
                  type="text"
                >
                  <div title={item.marketer_name} style="width: 100px; overflow: hidden; text-overflow: ellipsis">
                    {item.marketer_name}
                  </div>
                </i-button>
              )
            } else {
              return <div>{item.marketer_name}</div>
            }
          },
        },
        {
          title: '操作',
          key: 'operation',
          render: (h, params) => {
            let operation = params.row.operation
            const disabled = operation.status === '1'
            const disabledEdit = disabled || this.disabledEditNames.includes(operation.name)
            const disabledCancel = disabled || operation.is_new === 0 || this.disabledCancelNames.includes(operation.name)
            {/*order_sign_status 1: '未签',
            2: '线下签署',
            3: '会员端签署', */}
            const isNoSign = params.row.order_sign_status === 1
            const isSignFlag = params.row.order_sign_status === 2
            const isDownloadFlag = params.row.order_sign_status === 3
            const options = [
                !disabledEdit && { name: 'edit', label: '编辑' },
                !disabledCancel && { name: 'cancel', label: '撤销' },
                isDownloadFlag && {
                  name: 'download',
                  label: '下载合同',
                },
                !disabled && isNoSign && { name: 'signFlag', label: '标记签署' },
                !disabled && isSignFlag && { name: 'noSignFlag', label: '取消签署' },
              ].filter(Boolean);
            return options.length ? (
                <dropdown
                  class="z-controller"
                  style="max-height:260px"
                  on-on-click={(name) => {
                    this.handleControllerChange(name, params.row);
                  }}
                  transfer>
                  <span>
                    操作
                    <icon type="md-arrow-dropdown" />
                  </span>
                  <dropdownMenu slot="list">
                    {
                      options.map(({ name, label }) => (
                        <dropdownItem key={ name } name={ name }>
                          { label }
                        </dropdownItem>
                      ))
                    }
                  </dropdownMenu>
                </dropdown>
              ):(<span>-</span>);
          },
        },
      ],
      tableData: [],
      hasStore: false,
      isStore: false,
    }
  },
  created() {
    this.getContractList()
  },
  methods: {
    confirmSignFlag(orderId) {
      this.$Modal.confirm({
        title: '标记签署',
        okText: '确定签署',
        content: '确定将合同标记为线下签署?',
        onOk: () => {
          this.$service.post('/Web/CardOrderInfo/order_sign', {
            order_ids: [orderId],
          }).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getContractList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        },
      })
    },
    noSignFlag(orderId) {
      this.$Modal.confirm({
        title: '取消签署',
        okText: '取消签署',
        content: '确定取消合同的签署标记么?',
        onOk: () => {
          this.$service.post('/Web/CardOrderInfo/cancel_order_sign', {
            order_ids: [orderId],
          }).then((res) => {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.getContractList()
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
        }
      })
    },
    handleControllerChange(val, data) {
      switch (val) {
          case 'edit':
            this.editRecord(data)
            break
          case 'cancel':
            this.clickCancel(data.cardorder_info_id, data.revoke_locker_rent_confirm) 
            break
          case 'download':
            this.downloadContract(data)
            break
          case 'signFlag':
            this.confirmSignFlag(data.cardorder_info_id)
            break
          case 'noSignFlag':
            this.noSignFlag(data.cardorder_info_id)
            break
      }
    },
    downloadContract(data) {
      window.open(data.pdf_url)
    },
    handleDetail(index, id) {
      this.modalTitle = MODAL_TITLE[index]
      this.modalColumns = COLUMNS[index]
      this.getModalData(index, id)
      this.showModal = true
    },
    getModalData(type, card_order_id) {
      const url = '/Web/Statistics/statistics_card_order_info'
      this.$service
        .post(url, { type, card_order_id })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            // if (type == 0) {
            //   this.modalTableData = Object.keys(data)
            //     .map(key => {
            //       return {
            //         name: AMOUNT_TYPE[key],
            //         value: Number(data[key])
            //       };
            //     })
            //     .filter(item => item.value != 0);
            //   return false;
            // }
            this.modalTableData = data.map((item) => {
              if (type == 1) {
                return {
                  ...item,
                  ...{
                    payType: this.$store.getters['pay/getPayNameById'](item.pay_type),
                    amount: `￥${item.amount}`,
                  },
                }
              } else {
                return {
                  ...item,
                  ...{
                    isMain: item.is_main == 1 ? '主归属' : '协助',
                    percent: `${item.proportion}%`,
                    amount: `￥${item.amount}`,
                  },
                }
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    rowClassName(row, index) {
      if (row.status !== '0') {
        return 'disabled'
      }
    },
    editRecord(record) {
      if (record.custom_type == 1) {
        const card_user_id = record.sub_custom[0].card_user_id
        const cardorder_info_id = record.sub_custom[0].cardorder_info_id
        this.$router.push({
          name: '编辑卡',
          params: {
            userId: this.userId,
            cardUserId: card_user_id,
            cardorder_info_id,
          },
          query: {
            ttp: new Date().getTime(),
          },
        })
        // const url = `#/member/editCard/${this.userId}/${card_user_id}/${cardorder_info_id}?ttp=${new Date().getTime()}`;
        // window.location.href = url;
      } else if (record.is_new && record.is_active == 1 && record.is_fitness_stop == 0) {
        this.$router.push({
          name: '编辑卡',
          params: {
            userId: this.userId,
            cardUserId: record.card_user_id,
          },
          query: {
            ttp: new Date().getTime(),
          },
        })
        // const url = `#/member/editCard/${this.userId}/${record.card_user_id}?ttp=${new Date().getTime()}`;
        // window.location.href = url;
      } else {
        this.from = record.name
        this.hasStore = false
        this.isStore = false
        this.getOrderInfo(record.cardorder_info_id)
        this.sqbServInfo = {
          serv_type: 1,
          serv_id: record.cardorder_info_id,
        }
        // this.showEdit = true;
      }
    },
    checkEdit(postData) {
      if (postData.edit_reason === '') {
        this.$Message.error('请填写编辑原因')
        return false
      }
      return true
    },
    handleEditRecord() {
      if (!this.$refs.amountItems.checkSubmit()) return false
      const url = '/Web/CardOrderInfo/update_overdue_card_order_info'
      let {
        cardorder_info_id,
        sale_amount,
        is_front_money,
        income_amount,
        amount,
        new_pay_type,
        front_ids,
        help_deal,
        description: edit_reason,
      } = this.modalEditRecord

      const postData = {
        front_ids,
        cardorder_info_id,
        sale_amount,
        is_front_money,
        income_amount:
          this.modalEditRecord.order_name === '销卡' && income_amount != 0 ? -Number(income_amount) : income_amount,
        amount,
        new_pay_type,
        help_deal,
        edit_reason,
        marketers_id: help_deal[0].marketers_id,
        marketer_category: help_deal[0].marketer_category,
        marketers_name: help_deal[0].marketers_name,
        operating_user_id: this.userId,
      }
      if (!this.checkEdit(postData)) return false

      /* 12601 12683 处理精度问题，如果有误差，将最后的 ±0.0X 误差算到第一个业绩归属里面 */
      const targetAmount = postData.amount
      const sum = postData.help_deal.map((v) => +v.amount).reduce((previous, current) => previous + current, 0)
      if (targetAmount !== sum) {
        postData.help_deal = postData.help_deal.map((v) => ({ ...v }))
        postData.help_deal[0].amount = (+postData.help_deal[0].amount + +(amount - sum).toFixed(2)).toFixed(2)
      }

      // fix 19935 此处将请求传参中金额为0的支付方式去掉了
      postData.new_pay_type = postData.new_pay_type.filter((v) => v.amount && +v.amount > 0)
      // fix 20109 当编辑、核单等场景，如果实付income_amount=0（最后点击了定金），需要将支付方式置空传参
      if (+postData.income_amount === 0) {
        const needPayType = postData.new_pay_type.some((v) => [8, 20, 21].includes(+v.pay_type))
        if (needPayType) {
          this.$Message.error('实收金额不能小于储值卡、收钱吧、杉德支付方式的已收金额之和')
          return
        } else {
          postData.new_pay_type = []
        }
      }

      this.$service
        .post(url, postData)
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.$Message.success(res.data.errormsg)
            this.getContractList()
            this.showEdit = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    handleEditCancel() {
      this.showEdit = false
    },
    getOrderInfo(cardorder_info_id) {
      const url = '/Web/Member/get_card_user_edit_info'
      this.$service
        .post(url, { cardorder_info_id })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.info

            // data.income_amount = '';

            this.modalEditRecord = data
            this.modalEditRecord.selectedDiscount = data.coupon_receive_info

            // ? a、销卡  b、请假   c、转卡 d、补卡  e、拆卡
            if (
              ['销卡', '请假', '转卡（出）', '转卡（入）', '补卡', '拆分'].includes(this.modalEditRecord.order_name)
            ) {
              this.hasStore = true
            }

            if (this.modalEditRecord.marketer_category == 2) {
              this.modalEditRecord.marketers_id = `c${this.modalEditRecord.marketers_id}`
            } else if (this.modalEditRecord.marketer_category == 3) {
              // * set store radio
              // ! set market id and name
              this.isStore = true
              this.modalEditRecord.marketers_id = '0'
            }

            if (data.new_pay_type) {
              this.modalEditRecord.new_pay_type = data.new_pay_type.map((item) => {
                return {
                  ...item,
                  pay_type: +item.pay_type,
                  amount: Math.abs(item.amount),
                }
              })
            }
            this.modalEditRecord.income_amount = Math.abs(data.income_amount)

            if (data.order_help_deal_list) {
              this.modalEditRecord.help_deal = data.order_help_deal_list
                .map((item) => {
                  return {
                    ...item,
                    ...{
                      marketers_id: item.marketer_category == 2 ? 'c' + item.marketers_id : item.marketers_id,
                      percent: item.proportion,
                    },
                  }
                })
                .sort((a, b) => b.is_main - a.is_main)
            }

            this.showEdit = true
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    clickCancel(cardorder_info_id, revoke_locker_rent_confirm) {
      if (revoke_locker_rent_confirm === 1) {
        this.$Modal.confirm({
          title: '提示',
          content: '确认撤销租柜合同和押金，并撤销相关流水记录吗?',
          onOk: () => {
            this.cardorder_info_id = cardorder_info_id
            this.showCancel = true
          },
        })
      } else {
        this.cardorder_info_id = cardorder_info_id
        this.showCancel = true
      }
    },
    doCancel() {
      const url = '/Web/CardOrderInfo/revoke_card_order_info'
      const { cardorder_info_id, userId, cancelReason } = this
      if (!cancelReason) {
        return this.$Message.error('请输入撤销原因')
      }
      const postData = {
        cardorder_info_id,
        operating_user_id: userId,
        revoke_reason: cancelReason,
      }
      this.$service
        .post(url, postData)
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.getContractList()
            this.showCancel = false
            this.cancelReason = ''
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    getContractList() {
      const url = '/Web/CardOrderInfo/card_order_info_list'
      const postData = {
        user_id: this.userId,
        page_no: this.page,
        page_size: this.pageSize,
      }
      this.$service
        .post(url, postData, { loading: false })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.total = data.count
            this.tableData = data.list.map((item) => {
              return {
                _disableExpand: item.custom_type == 0,
                ...item,
                ...{
                  order_sn: {
                    order_sn: item.order_sn,
                    status: item.status,
                  },
                  operation: {
                    name: item.name,
                    status: item.status,
                    is_new: item.is_new,
                    cardorder_info_id: item.cardorder_info_id,
                    card_user_id: item.card_user_id,
                  },
                  marketer_name: item.marketer_name || '--',
                },
              }
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    pageSizeChanged(pageSize) {
      this.page = 1
      this.pageSize = pageSize
      this.getContractList()
    },
  },
}
</script>

<style lang="less" scoped>
  :deep(.ivu-dropdown-rel) {
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    width: 100%;
    height: 100%;
    line-height: 28px;
    position: relative;
    cursor: pointer;
    .ivu-icon {
      position: absolute;
      right: 10px;
      top: 8px;
      color: #b5b5b5;
      font-size: 10px;
    }
    span {
      display: block;
    }
  }
  .z-controller {
    height: 32px;
    width: 100px;
  }
</style>
