<template>
  <Modal v-model="show" title="头像上传" width="630" :mask-closable="false" :closable="true" @on-cancel="handleClose">
    <div class="avatar-upload-content">
      <!-- 隐私政策说明 -->
      <div class="privacy-notice">依据国家《个人隐私保护法》规定，采集人脸信息前须向用户说明用途，并取得用户同意。</div>

      <!-- 用户手机号 -->
      <div class="user-phone">
        <span class="label">用户手机号</span>
        <span class="phone-number">{{ phone }}</span>
      </div>
      <div class="user-phone" v-if="friendPhone">
        <span class="label">亲友手机号：</span>
        <span class="phone-number">{{ friendPhone }}</span>
      </div>

      <!-- 授权验证码 -->
      <div class="verify-section">
        <div class="verify-row">
          <span class="verify-label">授权验证码</span>
          <div class="input-with-icon">
            <Input
              :suffix="inputIcon"
              ref="verifyCodeInput"
              v-model="verifyCode"
              placeholder="请输入"
              class="verify-input"
              :maxlength="6"
              @on-enter="handleConfirm"
            ></Input>
          </div>
          <Button type="primary" style="background: rgba(24,190,108,0.1); color: #18BE6C; border: none;" :disabled="isSendDisabled" :loading="sendingCode" @click="sendVerifyCode">
            {{ sendButtonText }}
          </Button>
        </div>
      </div>

      <!-- 短信剩余条数提示 -->
      <!-- <div class="sms-info">
        <div class="sms-row" style="margin-bottom: 0;">
          <span class="sms-label">赠送短信剩余条数</span>
          <div>
            <span class="sms-count highlight">{{ freeSmsCount }}</span>
            <span class="sms-count">/{{ freeSmsLimit }} 条</span>
            <span class="sms-tip-line">勤鸟为场馆免费赠送 100 条短信，用完后需要场馆自行充值</span>
          </div>
        </div>
        <div class="sms-row">
          <span class="sms-label"></span>
          <div>
            <span class="sms-tip-line" style="margin-left: 81px;">推荐用户在《会员小程序》自行上传头像。</span>
          </div>
        </div>
        <div class="sms-row">
          <span class="sms-label">场馆短信剩余条数</span>
          <span class="sms-count">{{ venueSmsCount }} 条</span>
        </div>
      </div> -->

      <!-- 提示信息 -->
      <div class="tips">
        <div class="tip-line">推荐用户在《会员小程序》自行上传头像。</div>
      </div>
    </div>

    <div slot="footer" class="modal-footer">
      <Button @click="handleClose" class="cancel-btn">取消</Button>
      <Button type="success" :loading="submitting" :disabled="!verifyCode.trim()" @click="handleConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
// 常量定义
const COUNTDOWN_DURATION = 60
const VERIFY_CODE_LENGTH = 6

// 全局计时器变量
let countDownTimer = null

export default {
  name: 'MessageForAvatarDialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
      required: true,
      validator: (value) => value !== '' && value !== null && value !== undefined,
    },
  },
  data() {
    return {
      verifyCode: '',
      countDown: 0,
      submitting: false,
      sendingCode: false,
      freeSmsCount: 0,
      freeSmsLimit: 0,
      venueSmsCount: 0,
      phone: '',
      friendPhone: '',
      // 常量
      COUNTDOWN_DURATION,
    }
  },
  computed: {
    show: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
    // 脱敏手机号
    // maskedPhoneNumber() {
    //   const phone = this.phone || '123****0000'
    //   if (phone && phone.length === 11) {
    //     return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    //   }
    //   return phone || '123****0000'
    // },
    // 发送按钮文本
    sendButtonText() {
      if (this.sendingCode) return '发送中...'
      return this.countDown > 0 ? `${this.countDown}s` : '发送验证码'
    },
    // 发送按钮是否禁用
    isSendDisabled() {
      return this.countDown > 0 || this.sendingCode || !this.userId
    },
    inputIcon() {
      return this.verifyCode.length === VERIFY_CODE_LENGTH ? 'md-checkbox-outline' : 'md-square-outline'
    },
  },
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.initData()
          this.getSmsInfo()
          // 自动聚焦到验证码输入框
          this.$nextTick(() => {
            this.$refs.verifyCodeInput?.focus()
          })
        } else {
          this.resetData()
        }
      },
      immediate: false,
    },
    // 监听验证码输入，自动去除非数字字符
    verifyCode(newVal) {
      if (newVal && !/^\d*$/.test(newVal)) {
        this.verifyCode = newVal.replace(/\D/g, '')
      }
    },
  },
  methods: {
    initData() {
      this.verifyCode = ''
      this.countDown = 0
      this.submitting = false
      this.sendingCode = false
    },
    resetData() {
      this.verifyCode = ''
      this.countDown = 0
      this.submitting = false
      this.sendingCode = false
      this.clearTimer()
    },
    // 清除计时器
    clearTimer() {
      if (countDownTimer) {
        clearInterval(countDownTimer)
        countDownTimer = null
      }
    },
    // 获取短信信息
    async getSmsInfo() {
      if (!this.userId) return

      try {
        const res = await this.$service.post('/Web/Member/get_avatar_code_msg', {
          user_id: this.userId,
        })
        if (res.data.errorcode === 0) {
          const data = res.data.data || {}
          this.freeSmsCount = Number(data.last_free_avatar_code_no || 0)
          this.freeSmsLimit = Number(data.free_avatar_code_no || 0)
          this.venueSmsCount = Number(data.sms_number || 0)
          this.phone = data.phone || ''
          this.friendPhone = data.origin_phone || ''
        } else {
          // 静默处理错误，避免影响用户体验
          this.freeSmsCount = 0
          this.venueSmsCount = 0
        }
      } catch (error) {
        // 静默处理异常，使用默认值
        this.freeSmsCount = 0
        this.venueSmsCount = 0
      }
    },
    // 发送验证码
    async sendVerifyCode() {
      if (this.isSendDisabled) return

      this.sendingCode = true

      try {
        const params = {
          user_id: this.userId,
        }

        const res = await this.$service.post('/Web/Member/avatar_code', params)

        if (res.data.errorcode === 0) {
          this.$Message.success('验证码发送成功')
          this.startCountDown()
          // 更新短信剩余条数
          this.getSmsInfo()
        } else {
          this.$Message.error(res.data.errormsg || '发送失败')
        }
      } catch (error) {
        this.$Message.error('发送失败，请重试')
      } finally {
        this.sendingCode = false
      }
    },
    // 开始倒计时
    startCountDown() {
      this.countDown = COUNTDOWN_DURATION
      countDownTimer = setInterval(() => {
        this.countDown--
        if (this.countDown <= 0) {
          this.clearTimer()
        }
      }, 1000)
    },
    // 验证码校验
    validateVerifyCode() {
      const code = this.verifyCode.trim()
      if (!code) {
        this.$Message.error('请输入验证码')
        return false
      }
      if (code.length !== VERIFY_CODE_LENGTH) {
        this.$Message.error(`验证码应为${VERIFY_CODE_LENGTH}位数字`)
        return false
      }
      if (!/^\d+$/.test(code)) {
        this.$Message.error('验证码只能包含数字')
        return false
      }
      return true
    },
    // 确认
    async handleConfirm() {
      if (!this.validateVerifyCode()) return

      this.submitting = true

      try {
        const params = {
          user_id: this.userId,
          code: this.verifyCode.trim(),
        }

        const res = await this.$service.post('/Web/Member/check_avatar_code', params)

        if (res.data.errorcode === 0) {
          this.$Message.success('验证成功')
          this.$emit('on-success', this.verifyCode)
          this.handleClose()
        } else {
          this.$Message.error(res.data.errormsg || '验证失败')
        }
      } catch (error) {
        this.$Message.error('验证失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    // 关闭弹窗
    handleClose() {
      this.resetData()
      this.$emit('input', false)
    },
  },
  created() {
    // 组件创建时添加常量到实例
    this.VERIFY_CODE_LENGTH = VERIFY_CODE_LENGTH
  },
  beforeDestroy() {
    // 清理计时器，防止内存泄漏
    this.clearTimer()
  },
  // Vue 3 兼容性
  beforeUnmount() {
    this.clearTimer()
  },
}
</script>

<style lang="less" scoped>
.avatar-upload-content {
  padding: 20px 0;

  .privacy-notice {
    font-size: 14px;
    color: #ff6b6b;
    line-height: 1.6;
    margin-bottom: 30px;
    padding: 0;
    background: none;
    border: none;
    font-weight: 400;
  }

  .user-phone {
    margin-bottom: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;

    .label {
      color: #333;
      font-weight: 400;
      margin-right: 20px;
      min-width: 80px;
    }

    .phone-number {
      color: #333;
      font-weight: 400;
      font-family: inherit;
    }
  }

  .verify-section {
    margin-bottom: 30px;

    .verify-row {
      display: flex;
      align-items: center;
      gap: 12px;

      .verify-label {
        font-size: 14px;
        color: #333;
        min-width: 80px;
        font-weight: 400;
      }

      .input-with-icon {
        position: relative;
        display: flex;
        align-items: center;

        .verify-input {
          width: 180px;
          height: 32px;
        }
      }

      .ivu-btn {
        height: 32px;
        padding: 0 16px;
        font-size: 14px;
        border-radius: 4px;
        background-color: #52c41a;
        border-color: #52c41a;
        color: white;

        &:hover {
          background-color: #73d13d;
          border-color: #73d13d;
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #bfbfbf;
        }
      }
    }
  }

  .sms-info {
    margin-bottom: 30px;
    padding: 0;
    background: none;
    border: none;

    .sms-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }

      .sms-label {
        color: #333;
        min-width: 140px;
        font-weight: 400;
      }

      .sms-count {
        color: #333;
        font-weight: 400;

        &.highlight {
          color: #ff4d4f;
          font-weight: 400;
        }
      }
    }
  }

  .tips {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    padding: 0;
    background: none;
    border: none;

    .tip-line {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.modal-footer {
  text-align: right;
  padding-top: 20px;

  .cancel-btn {
    margin-right: 12px;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #666;

    &:hover {
      background-color: #e6e6e6;
      border-color: #bfbfbf;
      color: #666;
    }
  }

  .ivu-btn {
    min-width: 80px;
    height: 32px;
    font-weight: 400;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 16px;

    &[type="success"] {
      background-color: #52c41a;
      border-color: #52c41a;

      &:hover {
        background-color: #73d13d;
        border-color: #73d13d;
      }

      &:disabled {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .avatar-upload-content {
    .verify-section .verify-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .verify-label {
        min-width: auto;
      }

      .input-with-icon {
        width: 100%;

        .verify-input {
          width: 100%;
        }
      }
    }

    .sms-info .sms-row {
      flex-direction: column;
      align-items: flex-start;

      .sms-label {
        min-width: auto;
        margin-bottom: 4px;
      }
    }
  }
}

.sms-tip-line {
  font-weight: 400;
  font-size: 12px;
  color: #7F7F7F;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-left: 20px;
}
</style>
