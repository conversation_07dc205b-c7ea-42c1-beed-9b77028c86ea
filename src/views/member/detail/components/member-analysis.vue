<template>
  <div class="memberAnalysis">
    <div class="header">
      <div>
        <span>近30天训练频率</span>
        <p style="color: #63b5f7">
          <span>{{trainInfo.sign_count || '-- '}}</span>天/次</p>
      </div>
      <div class="center">
        <span>近30天上课频率</span>
        <p style="color: #7a86c9">
          <span>{{trainInfo.private_count || '-- '}}</span>天/节</p>
      </div>
      <div>
        <span>训练时长
          <Tooltip placement="bottom">
            <div slot="content"
                 style="white-space: normal; width: 200px">
              <div>根据会员领手环和退手环时间来计算</div>
              <div>以下情况不计算训练时长：</div>
              <div>1、未领取手环</div>
              <div>2、未归还手环</div>
              <div>3、归还时间超过3小时</div>
              <div>4、归还时间少于20分钟</div>
            </div>
            <Icon size="16"
                  type="ios-help-circle"
                  color="#F7DC6F" />
          </Tooltip>
        </span>
        <p style="color: #ba6ac8">
          <span>{{trainInfo.sign_duration || 0}}</span>分钟/次</p>
      </div>
    </div>
    <div class="bottom">
      <div class="calendar-wrap">
        <train-calendar :userId="userId" />
      </div>
      <div class="contract">
        <div class="total">
          <span>合同汇总</span>
          <h3>￥{{total || '0.00'}}</h3>
        </div>
        <div class="recent">
          <div class="record"
               v-for="(item, index) in orderList">
            <div class="left">
              <div class="type">
                <span :class="{'back-money': +item.amount < 0, 'renew-card': item.card_order_name == '续卡'}"></span>
                <p>{{item.card_order_name}}</p>
              </div>
              <div class="card">
                <h4>{{item.card_name}}</h4>
                <span>卡号：{{item.card_sn || '------'}}</span>
              </div>
            </div>
            <div class="info">
              <p>{{item.deal_time}}</p>
              <span>{{+item.amount >= 0 ? `+${item.amount}` : item.amount}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import TrainCalendar from './train-calendar'
  export default {
    name: 'memberAnalysis',
    props: ['userId'],
    components: {
      TrainCalendar
    },
    data() {
      return {
        trainInfo: {
          sign_count: 0,
          private_count: 0,
          sign_duration: 0
        },
        total: 0,
        orderList: []
      }
    },
    created() {
      this.getTrainInfo()
      this.getOrderList()
    },
    methods: {
      getTrainInfo() {
        const url = '/Web/Sign/user_sign_data'
        this.$service
          .post(url, { user_id: this.userId })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.trainInfo = data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getOrderList() {
        const url = '/Web/CardOrderInfo/all_order'
        this.$service
          .post(url, { user_id: this.userId, count: 3 })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.total = data.order_sum
              this.orderList = data.order_list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>

<style lang="less" scoped>
  @border: 1px solid #ececec;
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .memberAnalysis {
    font-size: 16px;
  }
  .header {
    .flex-center;
    border: @border;
    border-top: 0;
    .center {
      border-left: @border;
      border-right: @border;
    }
    > div {
      .flex-center;
      position: relative;
      flex: 1;
      height: 140px;
      > span {
        position: absolute;
        top: 20px;
        left: 36px;
      }
      p {
        font-size: 18px;
        > span {
          font-size: 48px;
          height: 58px;
          display: inline-block;
          margin-right: 10px;
          vertical-align: bottom;
        }
      }
    }
  }
  .bottom {
    .flex-center;
    .calendar-wrap {
      width: 66.7%;
      border-right: @border;
      padding: 40px 70px 50px;
    }
    .contract {
      .flex-center;
      font-size: 16px;
      align-self: flex-start;
      width: 33.3%;
      flex-direction: column;
      justify-content: flex-start;
      padding: 0 30px;
      .total {
        .flex-center;
        height: 140px;
        border-bottom: @border;
        width: 100%;
        position: relative;
        > span {
          position: absolute;
          top: 20px;
          left: 6px;
        }
        h3 {
          color: #e37573;
          font-weight: normal;
          font-size: 48px;
        }
      }
      .recent {
        width: 100%;
        .record {
          .flex-center;
          justify-content: space-between;
          padding: 0 6%;
          height: 78px;
          border-bottom: @border;
          .left {
            .flex-center;
            justify-content: flex-start;
            flex: 1;
            max-width: calc(~'100% - 80px');
            overflow: hidden;
          }
          .type {
            .flex-center;
            font-size: 16px;
            margin-right: 28px;
            span {
              width: 10px;
              height: 10px;
              border-radius: 1px;
              background-color: #7fcbc2;
              display: inline-block;
              margin-right: 8px;
            }
            .back-money {
              background-color: #e67373;
            }
            .renew-card {
              background-color: #8ecafc;
            }
          }
          .card {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            h4 {
              font-size: 16px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            span {
              font-size: 14px;
              color: #57a7ec;
            }
          }
          .info {
            .flex-center;
            white-space: nowrap;
            flex-direction: column;
            align-items: flex-end;
            width: 80px;
            span {
              color: #e37573;
            }
          }
        }
      }
    }
  }
</style>
