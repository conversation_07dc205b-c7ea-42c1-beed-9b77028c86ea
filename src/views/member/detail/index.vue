<template>
  <div>
   <Menu class="member-detail-menu" mode="horizontal" v-if="busList && busList.length>1" :active-name="curMenu" ref="memberMenu" @on-select="showMenuContent">
        <MenuItem v-for="bus in busList" :key="bus.bus_id" :name="bus.name">
          {{bus.name}}
        </MenuItem>
    </Menu>
    <div v-for="bus in busList" :key="bus.bus_id" :label="bus.name">
      <DetailInfo :user-data="userData" @assignClassCoachSuccess="getMembershipCardList" :normal-card='normalCard' :pt-card="ptCard" :swim-card="swimCard" :cur-user-id="bus.user_id" :cur-bus-id="bus.bus_id" :is-login-bus="bus.name==='本门店'" v-if="activatedMenu.includes(bus.name)" v-show="bus.name===curMenu" />
      <membershipCardList ref="cardList" @cardListChange='cardListChange' :cur-user-id="bus.user_id" :cur-bus-id="bus.bus_id" :is-login-bus="bus.name==='本门店'" v-if="activatedMenu.includes(bus.name)" v-show="bus.name===curMenu" />
      <Tables v-if="bus.name==='本门店' && activatedMenu.includes(bus.name)" :cur-user-id="bus.user_id" :cur-bus-id="bus.bus_id" v-show="bus.name===curMenu" />
    </div>
  </div>
</template>

<script>
import DetailInfo from './components/DetailInfo';
import membershipCardList from './../components/membershipCardList';
import Tables from './Tables';
export default {
  name: 'Detail',
  components: {
    DetailInfo,
    membershipCardList,
    Tables
  },
  data() {
    return {
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      curMenu: "本门店",
      activatedMenu: ['本门店'],
      userId: this.$route.params.userId,
      normalCard: [],
      busList: [],
      ptCard: [],
      swimCard: [],
      userData: {}
    };
  },
  created() {
    this.getUserBus()
  },
  methods: {
    showMenuContent(name) {
      this.curMenu = name;
      if (!this.activatedMenu.includes(name)) {
        this.activatedMenu.push(name);
      }
    },
    getUserBus() {
      this.$service.post( this.IS_BRAND_SITE ? '/Merchant/MemberList/getUserBus' : '/Web/Member/getUserBus', {
        user_id: this.$route.params.userId,
        from_merchant: this.IS_BRAND_SITE ? 1 : 0
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.busList = res.data.data
          if(this.$route.params.busId && this.busList && this.busList.length) {
            res.data.data.forEach((element, index) => {
              if(element.bus_id == this.$route.params.busId) {
                this.activatedMenu = []
                this.showMenuContent(element.name)
                this.$nextTick(()=>{
                  if(this.$refs.memberMenu) {
                    this.$refs.memberMenu.$el.scrollLeft = this.$refs.memberMenu.$children[index].$el.offsetLeft
                  }
                })
              }
            });
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      });
    },
    getMembershipCardList() {
      this.$refs.cardList.getMembershipCardList();
    },
    cardListChange(normalCard, ptCard, swimCad) {
      this.normalCard = normalCard.filter((item)=>{
        return item.under_review != 1  && item.experience_card != 1 && (item.status == '正常' || item.status == '已过期' || item.status == '已用完')
      })
      this.ptCard = ptCard.filter((item)=>{
        return item.under_review != 1 && item.experience_card != 1 && (item.status == '正常' || item.status == '已过期' || item.status == '已用完')
      })
      this.swimCad = swimCad.filter((item)=>{
        return item.under_review != 1 && item.experience_card != 1 && (item.status == '正常' || item.status == '已过期' || item.status == '已用完')
      })
    },
  },
  watch: {
    $route(val, oldVal) {
      // 添加name判断  防止从详情返回列表  造成整个页面的重新渲染
      if (val.name === oldVal.name && val.params.userId != oldVal.params.userId) {
        this.$router.go(0);
      }
    }
  }
};
</script>

<style lang="less">
.member-detail-menu {
  overflow-x: scroll;
  overflow-y: hidden;
  display: flex;
  white-space: nowrap;
  height: 65px;
  &::after {
    display: none;
  }
  .ivu-menu-item{
    height: 60px !important;
  }
}
</style>
