<template>
  <Card dis-hover class="member-detail-tables">
    <Tabs class="customized-tabs" @on-click="clickTabs">
      <TabPane label="会员分析">
        <MemberAnalysis v-if="activated.includes('0')" :user-id="userId" />
      </TabPane>
      <TabPane label="合同记录">
        <contract-records v-if="activated.includes('1')" :user-id="userId" />
      </TabPane>
      <TabPane label="训练记录">
        <train-records v-if="activated.includes('2')" :user-id="userId" />
      </TabPane>
      <TabPane label="团课预约">
        <reservation-records v-if="activated.includes('3')" :user-id="userId" />
      </TabPane>
      <TabPane label="上课记录">
        <private-class-records v-if="activated.includes('4')" :user-id="userId" />
      </TabPane>
      <TabPane label="跟进记录">
        <follow-records v-if="activated.includes('5')" :user-id="userId" />
      </TabPane>
      <TabPane label="储值卡消费记录">
        <prepaid-card-records v-if="activated.includes('6')" :user-id="userId" />
      </TabPane>
      <!-- <TabPane label="体测数据">
        <physical-test-records v-if="activated.includes('7')" />
      </TabPane> -->
      <TabPane label="体测情况">
        <body-info v-if="activated.includes('7')" :user-id="userId" />
      </TabPane>
      <TabPane label="图片资料库">
        <ImageData v-if="activated.includes('8')" :user-id="userId" />
      </TabPane>
    </Tabs>
  </Card>
</template>

<script>
  import MemberAnalysis from './components/member-analysis';
  import ContractRecords from './components/contract-records.vue';
  import FollowRecords from './components/follow-records.vue';
  import PrivateClassRecords from './components/private-class-records.vue';
  import TrainRecords from './components/train-records.vue';
  import ReservationRecords from './components/reservation-records.vue';
  import PrepaidCardRecords from './components/prepaid-card-records.vue';
  import PhysicalTestRecords from './components/physical-test-records.vue';
  import BodyInfo from './components/bodyInfo.vue';
  import ImageData from './components/imageData'

  import TabsMix from 'src/mixins/tabs';

  export default {
    name: 'Tables',
    components: {
      MemberAnalysis,
      ContractRecords,
      FollowRecords,
      PrivateClassRecords,
      TrainRecords,
      ReservationRecords,
      PrepaidCardRecords,
      PhysicalTestRecords,
      BodyInfo,
      ImageData
    },
    mixins: [TabsMix],
    props: {
      curUserId: {
        type: [String,Number],
        default: ''
      },
      curBusId: {
        type: [String,Number],
        default: ''
      }
    },
    data() {
      return {
        userId: this.curUserId || this.$route.params.userId
      };
    }
  };
</script>

<style lang="less">
  .member-detail-tables {
    background-color: #fff;
    /*padding: 12px 26px;*/
    .ivu-table {
      &::before {
        height: 0;
      }
      &::after {
        width: 0;
      }
      border: 1px solid #ddd;
      .ivu-table-body {
        overflow-x: hidden;
      }
      border-top: 0;
      th {
        background-color: #fff;
        font-weight: normal;
      }
    }
    .ivu-tabs-bar {
      margin-bottom: 0;
    }
    .ivu-tabs-ink-bar {
      width: 1/9 * 100% !important;
    }
  }
</style>
