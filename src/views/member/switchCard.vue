<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>转卡</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formCustom" :model="postData" :rules="ruleCustom" :label-width="130">
        <Form-item label="当前会员卡" prop="cardinfo">
          {{cardinfo.card_name}}
          <span v-if="cardinfo.card_sn">(卡号：{{cardinfo.card_sn}})</span>
        </Form-item>
        <Form-item label="当前次数" prop="cardinfo" v-if="cardinfo.is_pt_time_limit_card != 1 && (cardinfo.card_type_id==2||cardinfo.card_type_id==4||cardinfo.card_type_id==5)">
          总次数{{cardinfo.all_num}}次，剩余次数{{cardinfo.last_num}}次
        </Form-item>
        <Form-item label="当前金额" prop="cardinfo" v-if="cardinfo.card_type_id==3">
          总金额{{cardinfo.all_num}}元，剩余金额{{cardinfo.last_num}}元
        </Form-item>
        <Form-item label="当前有效期" prop="cardinfo">
          {{cardinfo.indate}}
        </Form-item>
        <Form-item label="剩余天数" prop="cardinfo">
          {{cardinfo.last_day}}{{cardinfo.last_day=='永久'?'':'天'}}
        </Form-item>
        <Form-item :label="cardinfo.card_type_id==2||cardinfo.card_type_id==4||cardinfo.card_type_id==5?'剩余次数':'剩余金额'" prop="last_num_after" v-if="cardinfo.card_type_id!=1">
          {{cardinfo.last_num}}
        </Form-item>
        <LeftValue :value="cardinfo.order_value" :cardUserId="$route.params.cardUserId" />
        <Form-item label="转出会员">
          {{cardinfo.username}}
        </Form-item>
        <div class="tips">
          会员卡一经转出,之前共享使用该卡的成员将不可再使用该会员卡
        </div>
        <Form-item label="转入会员" prop="to_user_id" :rules="{required: true,message: '请选择转入会员',trigger: 'change'}">
          <Select filterable clearable remote :remote-method="getSearchUserList" v-model="postData.to_user_id" @on-change="handleIntoMember" :loading="searching" placeholder="姓名/电话/实体卡号">
            <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">{{user.username}}</Option>
          </Select>
        </Form-item>
        <div class="tips-2" v-if="cardActiveFlag">
          转入会员还拥有其他会员卡，请选择是否需要将部分卡暂停使用
        </div>
        <Row v-if="cardActiveFlag">
          <Col offset="1">
            <RadioGroup class="card-active-group" v-model="cardActive">
              <Radio label="1">不暂停，转入卡和原卡一同使用</Radio>
              <Radio label="2">原卡优先使用，新转入卡暂停</Radio>
              <Radio label="3">转入卡优先使用，选择原卡进行暂停</Radio>
            </RadioGroup>
          </Col>
        </Row>
        <Row class="card-active-new" v-if="cardActiveFlag && cardActive=='3'">
          <Col offset="1" span="9">
            <h3>请选择需要暂停的卡</h3>
            <Table border :columns="cardActiveCols" :data="cardActiveDB" @on-selection-change="handleElectedCard"></Table>
          </Col>
        </Row>
        <contract-Info 
          ref="contractComponent" 
          actionType="switch" 
          :contractData='cardinfo' 
          :selectedCard="selectedCard" 
          :labelWidth="130" 
          :toUserId="postData.to_user_id"
          :hasStore="true"/>
        <Form-item>
          <div class="buttons">
            <Button type="primary" :loading="loadingFlag" @click="handleSubmit('formCustom')">确定</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
    <receipt-modal v-model="showPrint"
                   :to-path='toPath'
                   @on-receiptupdate="receiptUpdate" />
  </div>
</template>
<script>
  import { getcardInfo, getsalesInfo } from '../../service/getData.js';
  import contractInfo from 'components/member/contractInfo';
  import { formatDate, dateDiff } from 'utils';
  import receiptModal from 'components/receipt/receipt.vue'
  import receipt from 'mixins/receipt.js'
  import LeftValue from './components/leftValue.vue';
  import  EventBus from 'components/EventBus.js'
  import { isChinese } from '@/utils';

  export default {
    created() {
      this.getCardInfo();
    },
    components: {
      contractInfo,
      receiptModal,
      LeftValue
    },
    mixins: [receipt],
    data() {
      return {
        provideToUserId: '',
        cardActive: '1',
        cardActiveFlag: false,
        loadingFlag: false,
        cardUserIdArr: [],
        cardActiveCols: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '卡名称',
            key: 'card_name'
          },
          {
            title: '剩余/总共',
            key: 'last_all'
          },
          {
            title: '到期',
            key: 'end_date'
          }
        ],
        cardActiveDB: [],
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        selectedCard: {
          card_type_id: ''
        },
        postData: {
          user_id: this.$route.params.userId, //转出用户id
          card_user_id: this.$route.params.cardUserId, //转出会员卡id
          amount: '', //合同金额
          pay_type: '', //支付方式
          remark: '', //订单备注
          deal_time: '', //合同成交时间
          to_user_id: '' //转入用户id
        },
        searching: false,
        userList: null,
        cardinfo: {},
        ruleCustom: {}
      };
    },
    methods: {
      receiptUpdate () {
        this.$router.back()
      },
      // 被选中的卡
      handleElectedCard(sel) {
        let cuidArr = [];
        sel.forEach(item => {
          cuidArr.push(item.card_user_id);
        });
        this.cardUserIdArr = cuidArr;
      },
      // 选中会员
      handleIntoMember(val) {
        this.$service
          .post('/Web/FrontMoney/get_attorn_card_user_culist', {
            to_user_id: val,
            card_user_id: this.cardinfo.card_user_id
            }, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const list = res.data.data;
                if (Array.isArray(list) && list.length > 0) {
                  this.cardActiveFlag = true;
                  list.forEach(item=>{
                    item._checked = true;
                    this.cardUserIdArr.push(item.card_user_id);
                  });
                  this.cardActiveDB = list;
                } else {
                  this.cardActiveFlag = false;
                  this.cardActiveDB = [];
                }
            }
          })
      },
      // 搜索会员
      getSearchUserList(search) {
        if (search !== '') {

          // if numbers or letters must be more than 3 in length you can request
          if (!isChinese(search)) {
            return;
          }
          
          this.searching = true;
          this.$service
            .post('/Web/FrontMoney/search_all_user', { search: search.trim() }, { loading: false })
            .then(res => {
              if (res.data.errorcode === 0) {
                const list = res.data.data.list;
                if (Array.isArray(list)) {
                  this.userList = list.filter(item=>(item.user_id != this.cardinfo.user_id));
                } else {
                  this.userList = [];
                }
              }
              this.searching = false;
            })
            .catch(err => {
              this.searching = false;
              this.userList = [];
            });
        } else {
          this.userList = [];
        }
      },
      getCardInfo() {
        var postData = {
          card_user_id: this.postData.card_user_id,
          user_id: this.postData.user_id
        };
        this.$service.post('/Web/MemberCard/get_attorn_card_info', postData).then(res => {
          if (res.data.errorcode === 0) {
            this.cardinfo = res.data.data;
            EventBus.$emit('on-pay-des-change', `转卡[${this.cardinfo.card_name}]`)
            this.selectedCard.card_type_id = this.cardinfo.card_type_id;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleSubmit(name) {
        if (this.loadingFlag) {
          return false;
        }
        this.loadingFlag = true
        if (this.cardActive == 3 && this.cardUserIdArr.length === 0) {
          this.$Message.error('请选中至少一张卡！');
          this.loadingFlag = false
          return false;
        }

        this.$refs[name].validate(async valid => {
          let contractInfoData = await this.$refs.contractComponent.handleSubmitClick();
          valid = contractInfoData.valid && valid;
          if (valid) {
            const in2member = {attorn_type: this.cardActive, card_user_id_array: this.cardUserIdArr};
            delete contractInfoData.postData.help_deal; //转卡没有协助成单
            let postData = { ...this.postData, ...contractInfoData.postData, ...in2member };
            postData.is_package = this.$route.params.isPackage==1?1:0
            this.$service
              .post('/Web/MemberCard/attorn_card', postData)
              .then(res => {
                if (res.data.errorcode == 0) {
                  // this.$Message.success(res.data.errormsg);
                  this.contractComplete(
                    postData.to_user_id,
                    res.data.card_order_info_id,
                    'switchcard'
                  )
                } else {
                  this.$Message.error(res.data.errormsg);
                }
                this.loadingFlag = false;
              })
              .catch((err)=> {
                console.log(err);
                this.loadingFlag = false
              });
          } else {
            this.loadingFlag = false
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .tips {
    color: #ff0000;
    font-size: 14px;
    margin-bottom: 24px;
    margin-left: 45px;
  }
  .tips-2 {
    font-size: 14px;
    margin-bottom: 24px;
    margin-left: 45px;
  }
  .card-active-group {
    height: 31px;
  }
  .card-active-new {
    margin-bottom: 20px;

    h3 {
      margin-bottom: 6px;
    }
  }
</style>
