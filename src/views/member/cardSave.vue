<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{cardId?'编辑':'添加'}}</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formValidate"
            :model="formValidate"
            :label-width="170">
          <Form-item label="会员卡类型"
                     prop="cardType">
            <RadioGroup v-model="formValidate.cardType"
                        type="button"
                        @on-change="handleCardType" :disabled="disabledEdit">
              <Radio label="0"
                     v-if="hasAuth('singleCard')"
                     :disabled="cardId!==0">单店卡</Radio>
              <Radio label="1"
                     v-if="cardThreeType == 2?(hasAuth('multiCard')&&showMultiPtClass):hasAuth('multiCard')"
                     :disabled="cardId!==0">多店通用卡</Radio>
              <Radio label="2"
                     v-if="hasAuth('expCard') && formValidate.subCardType != 7"
                     :disabled="cardId!==0">体验卡</Radio>
            </RadioGroup>
          </Form-item>
          <Form-item label=""
                     prop="subCardType">
            <RadioGroup v-model="formValidate.subCardType"
                        type="button"
                        :disabled="disabledEdit"
                        @on-change="handleSubCardType">
              <Radio label="1"
                     v-if="cardThreeType == 1"
                     :disabled="cardId!==0">期限卡</Radio>
              <Radio label="2"
                     v-if="cardThreeType == 1"
                     :disabled="cardId!==0">次卡</Radio>
              <Radio label="3"
                     v-if="formValidate.cardType != 2 && cardThreeType == 1"
                     :disabled="cardId!==0">储值卡</Radio>
              <Radio label="4"
                     v-if="cardThreeType == 2"
                     :disabled="cardId!==0">私教课</Radio>
              <Radio label="7"
                     v-if="cardThreeType == 2 && formValidate.cardType != 2"
                     :disabled="cardId!==0">私教包月</Radio>
              <Radio label="5"
                     v-if="showSwimClass"
                     :disabled="cardId!==0">泳教课</Radio>
            </RadioGroup>
          </Form-item>
          <Form-item :label="cardThreeType == 1?'会员卡名称':'课程名称'"
                     :rules="{required: true, message: '名称不能为空', trigger: 'blur'}"
                     prop="name">
            <Input v-model="formValidate.name"
                   placeholder="请输入名称"
                   :disabled="disabledEdit"
                   style="width:550px" />
          </Form-item>
          <FormItem :label="cardThreeType == 1 ? '会籍卡标签': cardThreeType == 2 ? '私教课标签':'泳教课标签'">
            <CheckboxTag style="width: 550px"
                         radio
                         :disabled="disabledEdit"
                         v-model="checkboxTag"
                         :data="checkboxTagData"
                         :type="cardThreeType == 1 ? 2 : cardThreeType == 2 ? 1 : 3"
                         :before-tag-add="onTagAdd"
                         :before-tag-delete="onTagDelete"></CheckboxTag>
          </FormItem>
          <Form-item label="次数"
                     prop="number"
                     :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^[1-9]\d*$/, message: '次数为正整数' }"
                     v-if="formValidate.subCardType == 2">
            <Input-number v-model="formValidate.number"
                          :min="1"
                          :disabled="disabledEdit"
                          :step="1"
                          style="width:550px"></Input-number>
          </Form-item>
           <Form-item label="赠送次数" prop="gift_number" :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^\d+$/, message: '赠送次数为正整数或0' }" v-if="formValidate.subCardType == 2 && formValidate.cardType != 2">
            <Input-number v-model="formValidate.gift_number"
                          :min="0"
                          :disabled="disabledEdit"
                          :step="1"
                          style="width:550px"></Input-number>
          </Form-item>
          <Form-item label="节数"
                     prop="number"
                     :rules="{required: true,type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^[1-9]\d*$/, message: '节数只能为正整数'}"
                     v-if="(formValidate.subCardType == 4 || formValidate.subCardType == 5) && formValidate.cardType == 2">
            <Input-number v-model="formValidate.number"
                          :min="1"
                          :disabled="disabledEdit"
                          :step="1"
                          style="width:550px"></Input-number>
          </Form-item>
          <Form-item label="价值金额"
                     prop="number"
                     v-if="formValidate.subCardType == 3">
            <div class="across-row">
              <Input-number v-model="formValidate.number"
                            :min="0"
                            :disabled="disabledEdit"
                            :step="0.01"
                            style="width:550px"></Input-number>
              <Alert type="warning"
                     style="margin-left: 20px;">购卡时可根据实际情况进行修改</Alert>
            </div>
          </Form-item>
          <Form-item label="赠送金额"
                     prop="gift_number"
                     :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须大于等于0且最多只能保留两位小数' }"
                     v-if="formValidate.subCardType == 3 && formValidate.cardType != 2">
            <Input-number v-model="formValidate.gift_number"
                          :min="0"
                          :disabled="disabledEdit"
                          :step="0.01"
                          style="width:550px"></Input-number>
          </Form-item>
          
          <Form-item :label="(cardThreeType == 2 || cardThreeType == 3) && formValidate.subCardType != 7? '单节有效期（天）': '有效时限（天）'"
                    v-if="formValidate.cardType !=2 && formValidate.subCardType != 1 && formValidate.subCardType != 7"
                     prop="end_time" :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^\d+$/, message: '正整数或0' }">
            <div class="across-row">
              <Input-number v-model="formValidate.end_time"
                            :min="0"
                            :step="1"
                            :disabled="disabledEdit"
                            style="width:550px"></Input-number>
              <Alert type="warning"
                     style="margin-left: 20px;">为0表示永久有效
              </Alert>
            </div>
          </Form-item>
          <Form-item :label="(cardThreeType == 2 || cardThreeType == 3) && formValidate.subCardType != 7? '单节有效期（天）': '有效时限（天）'"
                    v-else
                     prop="end_time" :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^[1-9]\d*$/, message: '正整数' }">
            <div class="across-row">
              <Input-number v-model="formValidate.end_time"
                            :min="1"
                            :step="1"
                            :disabled="disabledEdit"
                            style="width:550px"></Input-number>

            </div>
          </Form-item>
          <Form-item label="赠送天数" prop="gift_number" :rules="{ type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^\d+$/, message: '赠送次数为正整数或0' }"
                     v-if="formValidate.subCardType == 1 && formValidate.cardType != 2">
            <Input-number v-model="formValidate.gift_number"
                          :min="0"
                          :disabled="disabledEdit"
                          :step="1"
                          style="width:550px"></Input-number>
          </Form-item>
          <Form-item label="每日最多上课节数" prop="per_day_pt_class_num" v-if="formValidate.subCardType == 7">
            <Input-number v-model="formValidate.per_day_pt_class_num"
                          :min="1"
                          :step="1"></Input-number>
          </Form-item>
          <Form-item label="有效期内最多上课节数" prop="per_all_pt_class_num" v-if="formValidate.subCardType == 7">
            <Input-number v-model="formValidate.per_all_pt_class_num" :min="1" :step="1" placeholder="不限"></Input-number>
          </Form-item>
          <Form-item prop="per_day_pt_class_num" v-if="formValidate.subCardType == 7">
            <div slot="label">
              <span>消课结算单价</span>
              <Tooltip>
                <div slot="content"
                    style="width: 160px; white-space: normal">举例：如果1~10节的结算单价是40.00/节，>10节的结算单价是20.00/节，表示的是10节以后每节单价是20.00/节</div>
                <Icon size="16"
                      type="ios-help-circle"
                      color="#ffcf05"></Icon>
              </Tooltip>
            </div>
            <Card class="bus-card bus-card-table" dis-hover>
              <p slot="title"></p>
              <i-button slot="extra" type="text" shape="circle" size="small" @click.prevent="showSaleAdd = true">
                编辑
              </i-button>
              <Table :columns="saleColumns" :data="formValidate.pt_settlement_unit_price" ref="table" disabled-hover></Table>
            </Card>
          </Form-item>
          <Form-item label="售卖时间" prop="start_end_time" v-if="formValidate.cardType != 2">
            <DatePicker v-model="duringDate"  @on-change="handleDateChange" type="daterange" placeholder="不填代表无限制"></DatePicker>
          </Form-item>
          <Form-item label="允许请假"
               class="pedro-input-center"
               v-if="formValidate.cardType!=2 ">
               <!-- && formValidate.subCardType!=4 && formValidate.subCardType!=5 && formValidate.subCardType!=7 -->
            <Input-number :min="0"
                :precision="0"
                :active-change="false"
                style="width:80px;"
                v-model="formValidate.enable_suspend_num" />&nbsp;次，共&nbsp;
            <Input-number :min="0"
                :precision="0"
                :active-change="false"
                style="width:80px;"
                v-model="formValidate.enable_suspend_day"  />&nbsp;天
          </Form-item>
          <Form-item label="会员分享" v-if="formValidate.subCardType == 2 && formValidate.cardType != 2">
            <RadioGroup v-model="formValidate.member_share">
              <Radio label="0">不允许</Radio>
              <Radio label="1">允许分享给朋友使用</Radio>
            </RadioGroup>
            <Alert style="display: inline-block" type="warning">会员自己将卡拆分出1次分享给朋友使用</Alert>
          </Form-item>
          <Form-item label="单节时长（分钟）"
                     prop="class_duration"
                     v-show="formValidate.subCardType == 4 || formValidate.subCardType == 5 || formValidate.subCardType == 7">
            <Input-number v-model="formValidate.class_duration"
                          :min="1"
                          :max="9999"
                          :disabled="disabledEdit"
                          style="width:550px" />
          </Form-item>
        <template v-if="formValidate.cardType === '2'">
          <Form-item v-if="this.cardThreeType === 1" label="会籍发放" required prop="membership_grant">
            <RadioGroup v-model="formValidate.membership_grant">
              <Radio label="1">允许</Radio>
              <Radio label="0">不允许</Radio>
            </RadioGroup>
          </Form-item>
           <Form-item label="转赠他人" required prop="give">
            <RadioGroup v-model="formValidate.give">
              <Radio label="1">允许</Radio>
              <Radio label="0">不允许</Radio>
            </RadioGroup>
          </Form-item>
          <Form-item
            label="领取有效期（天）"
            prop="activation_end"
            :rules="{ required: true, type: 'string',transform:value=>Number(value)?String(value):'0',pattern: /^[0-9]\d*$/, message: '0或正整数' }">
              <Input-number
                v-model="formValidate.activation_end"
                :min="0"
                :max="360"
                :step="1"
                :disabled="disabledEdit"
                placeholder="0~360天，0代表永久有效"
                style="width:550px" />
          </Form-item>
        </template>
        <Form-item label="发放/使用场馆">
          <div slot="label">
            <span>发放/使用场馆</span>
          </div>
          <RadioGroup v-model="formValidate.deal_type">
            <Radio :label="0" :disabled="cardId!==0">按场馆添加</Radio>
            <Radio :label="1" :disabled="cardId!==0" v-if="(cardId===0 && adminType == 1) || cardId!==0">按场馆组添加</Radio>
          </RadioGroup>
        </Form-item>
        <BusCardSet v-if="adminBusList && formValidate.deal_type === 0"
                  :busList="adminBusList"
                  v-model="busBelongData"
                  :addType="formValidate.deal_type"
                  :cardType="formValidate.cardType"
                  :cardId="cardId"
                  :subCardType="formValidate.subCardType"
                  :formValidate="formValidate"
                  :cardRuleList="cardRuleList"></BusCardSet>
        <BusCardSet v-if="busGroupList && formValidate.deal_type === 1 && busGroupList.length"
                  :groupList="busGroupList"
                   v-model="busGroupBelongData"
                  :addType="formValidate.deal_type"
                  :cardType="formValidate.cardType"
                  :cardId="cardId"
                  :subCardType="formValidate.subCardType"
                  :formValidate="formValidate"
                  :cardRuleList="cardRuleList"></BusCardSet>
        <Form-item  v-if="busGroupList && formValidate.deal_type === 1 && !busGroupList.length">
        暂无场馆组数据，去编辑场馆资料页面添加吧！
        </Form-item>
        <Form-item label="代售场馆"
                   v-if="formValidate.subCardType != 4 && formValidate.subCardType != 5  && formValidate.subCardType != 7 && formValidate.cardType != 2"
                   prop="help_sale_bus_id">
          <Select v-model="formValidate.help_sale_bus_list"
                  clearable
                  style="width:550px;"
                  multiple
                  :disabled="disabledEdit"
                  filterable>
             <Option v-for="bus in busList"
                    :key="bus.bus_id"
                    :value="bus.bus_id">{{bus.bus_name}}</Option>

          </Select>
        </Form-item>
        <ReChangeFormItem
          v-if="formValidate.subCardType == 3"
          :self-recharge.sync="formValidate.self_recharge"
          :list="formValidate.recharge_package"
        />
        <Form-item
            :label="cardThreeType == 1?'会员卡描述':'课程描述'"
            prop="description">
             <FormEditor class="description" v-model="formValidate.description" />
          <!-- <Input v-model="formValidate.description"
                 type="textarea"
                 :disabled="disabledEdit"
                 :autosize="{minRows: 4,maxRows: 5}"
                 placeholder="请输入..."
                 style="width:550px" /> -->
        </Form-item>
        <Form-item v-if="cardThreeType && formValidate.subCardType !== 3" label="课程封面" prop="thumb">
            <div class="image-description">
              <img v-show="formValidate.thumb" :src="formValidate.thumb" />
              <Button type="info" @click="uploadModal = true">
                选择图片
              </Button>
            </div>
        </Form-item>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit('formValidate')" :disabled="!authorization">提交</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>

    </div>
     <Modal v-model="supportMobileModal"
           title="提示">
      <div class="bodybuilding">
        <p class="w-tip" style="color:red;">在线支付功能未开通，请联系勤鸟工作人员</p>
      </div>
      <div slot="footer"
           class="modal-buttons">
        <Button @click="supportMobileModal = false">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="showSaleAdd" :mask-closable="false" title="消课结算单价">
      <Row>
        <Col span="9" class="saleadd-top">
          消课节数
        </Col>
        <Col span="9" class="saleadd-top">
        结算单价
        </Col>
      </Row>
      <Row v-for="(item, index) in saleModalData" class="saleadd-mb" :key="index">
        <Col span="12">
          <InputNumber :min="1" :active-change="false" v-model="saleModalData[index].min" disabled></InputNumber>
          <span>~</span>
          <InputNumber :min="saleModalData[index].min + 1" @on-change="maxNumChange($event, index)" :active-change="false" v-model="saleModalData[index].max"></InputNumber>
          <span>节</span>
        </Col>
        <Col span="12">
          <InputNumber v-model="saleModalData[index].price"></InputNumber><span>元/节</span>
          <Icon v-if="index!=0 && index === saleModalData.length-1" type="md-trash" size="18" title="删除" class="delete-sale" @click.native="handleDeleteItem(index)"></Icon>
        </Col>
      </Row>
      <div @click="addSaleRange" class="saleadd-btn">
        <Icon type="md-add" />消课节数区间
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="saveSaleModal">保存</Button>
        <Button @click="showSaleAdd = false">取消</Button>
      </div>
    </Modal>
    <!-- 选择图片-弹窗 -->
    <ImgUpload
      v-model="uploadModal"
      @on-change="handleUpload"
      :options="{aspectRatio: 690/400}"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex';
import CheckboxTag from 'components/form/checkboxTag';
import BusCardSet from './components/BusCardSet.vue';
import FormEditor from 'components/form/Editor';
import ImgUpload from 'components/form/ImgUpload';
import ReChangeFormItem from './components/rechargeFormItem';
import * as Types from '../../store/mutationTypes';

export default {
  name: 'cardSave',
  components: { CheckboxTag, BusCardSet,FormEditor,ImgUpload, ReChangeFormItem },
  data () {
    return {
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      dict: {
        0: '日',
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六'
      },
      saleColumns: [
        {
          title: '节数范围',
          render: (h, param) => {
            let item = param.row
            let priceDes;
            if (item.min && item.max) {
              priceDes = `${item.min}~${item.max}节`
            } else if (item.min) {
              priceDes = `>=${item.min}节`
            }  else if (item.max) {
              priceDes = `<=${item.max}节`
            }
            return <span>{priceDes}</span>
          }
        },
        {
          title: '结算价格',
          render: (h, param) => {
            return <span>{param.row.price}/节</span>
          }
        }
      ],
      saleModalData: [{
        min: 1,
        max: null,
        price: null
      }],
      busList: [],
      duringDate: [],
      busBelongData: [{
        bus_id: '',
        bus_name: '',
        currentPriceFlag: false,
        enable_time_limit: 0,
        weekTimes: [],
        current_price: 0,
        status: 0,
        activation_restriction: 0,
        alipay_sale_status: 0,
        gift_number: 0,
        sign_discount: 10,
        booking_discount: 10,
        buy_min_value: 1,
        single_price: 0,
        sale_rule_id: null, // 购卡规则id
        is_open_rule: '0'  // 是否开启规则 0否1是
      }],
      busGroupBelongData: [{
        level_id: '',
        level_name: '',
        gift_number: 0,
        currentPriceFlag: false,
        enable_time_limit: 0,
        weekTimes: [],
        current_price: 0,
        sign_discount: 10,
        booking_discount: 10,
        buy_min_value: 1,
        single_price: 0,
        sale_rule_id: null, // 购卡规则id
        is_open_rule: '0'  // 是否开启规则 0否1是
      }],
      busGroupList: [],
      cardRuleList: [],
      checkboxTag: [],
      checkboxTagData: [],
      checkboxTagMsData: [],
      cardId: 0,
      cardThreeType: 1, // 1 会籍卡 2 私教泳教
      supportMobileModal: false,
      currentPriceFlag: false,
      indeterminate: false,
      checkAll: false,
      showSaleAdd: false,
      formValidate: {
        is_pt_time_limit_card: 0,
        per_day_pt_class_num: 1,
        per_all_pt_class_num: '',
        pt_settlement_unit_price: [],
        enable_suspend_num: 0,
        enable_suspend_day: 0,
        member_share: '0',
        cardType: '0', // 会员卡类型
        subCardType: '1',
        name: '',
        group_id: '0',
        gift_number: 0,
        number: 1,
        end_time: 1,
        description: '',
        status: '0',
        commission: 0,
        sign_discount: 10,
        booking_discount: 10,
        class_duration: 60,
        buy_min_value: 1,
        help_sale_bus_list: [],
        help_sale_bus_id: '',
        belong: '',
        deal_type: 0,
        start_end_time: {},
        single_price: 0,
        membership_grant: '1', // 体验卡是否允许会籍发放 0不允许 1允许
        give: '1', // 体验卡是否允许转赠 0不允许 1允许
        activation_end: 0, // 体验卡有效期/天, 0 永久
        // 课程封面-选择上传图片路径
        thumb:'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
        self_recharge: '0', // 开启关闭储值卡自主续充
        recharge_package: [ // 储值卡专用，充值套餐设置
          {
            // id: ''
            key: Date.now(), // 没有id 则使用时间戳，有则为id
            isSame: false, // 用于显示隐藏校验提示
            amount: 0, // 售价
            number: 0, // 储值卡价值
            gift_number: 0, // 赠送价值
            delay_number: 0, // 延长到期天数
          },
        ]
      },
      // 课程封面-选择上传弹窗控制变量 默认false
      uploadModal:false,
      cardSettingAuths: {
        expCard: false,
        multiCard: false,
        multiPtCard: false,
        singleCard: false,
        packageCard: true,
        swimCard: true,
      },
    };
  },
  computed: {
    ...mapState(['cardSettingAuth', 'adminBusList', 'busId', 'busName', 'adminType' ]),
    showMultiPtClass () {
      return  this.cardSettingAuth && this.cardSettingAuth.multiPtCard && this.cardThreeType === 2;
    },
    showSwimClass () {
      return  this.cardSettingAuth && this.cardSettingAuth.swimCard && this.cardThreeType === 3;
    },
    disabledEdit() {
      return this.cardId !== 0 && this.formValidate.is_edt === 0
    },
    authorization() {
      const cardTypeName = this.getCardTypeName()
      if (this.cardId == 0) { // 新增
        return this.cardSettingAuth[cardTypeName]
      } else { // 编辑
        return this.formValidate.is_edt === 1 && this.cardSettingAuth[cardTypeName]
      }
    }
  },
  created () {
    if (Array.isArray(this.busBelongData) && this.busBelongData.length) {
      this.busBelongData[0].bus_id = this.busId
      this.busBelongData[0].bus_name = this.busName
    }
    this.cardId = parseInt(this.$route.params.card_id);
    this.cardThreeType = parseInt(this.$route.params.cardThreeType);
    this.formValidate.subCardType = this.cardThreeType == 1 ? '1' : this.cardThreeType == 2 ? '4' : '5'
     /* 如果不是会籍卡，不允许会籍发放 */
    if(this.cardThreeType !== 1) this.formValidate.membership_grant = '0';
    if (this.cardId === 0) {
      if (!this.cardSettingAuth) {
        this.$router.push({ path: '/management/card' });
        return;
      }
      const tabs = ['singleCard', 'multiCard', 'expCard'];
      for (let [index, value] of tabs.entries()) {
        if (this.cardSettingAuth[value]) {
          this.formValidate.cardType = `${index}`;
          break;
        }
      }
      // 当this.$route.params.card_id/this.cardId == 0时代表新增,刷新时,vuex会被重置,导致页面夜场
      // 调用Types.CARD_SETTING_AUTH并对cardSettingAuths进行赋值
      this.getCardSettingAuth();
    }
    this.getSortData();
    if (!this.adminBusList) {
      this.$store.dispatch('getAdminBusList').then((res)=>{
        if (res.data.errorcode === 0) {
          this.getCardInfo()
        }
      });
    } else {
      this.getCardInfo()
    }
    this.getBusList()
    this.getBusGroupList()
    this.cardId === 0 && this.getCardRuleList() // 获取购卡规则列表
  },
  methods: {
    getCardSettingAuth () {
      const prefix = this.IS_BRAND_SITE ? 'Merchant/CardClass/' : 'Web/Card/'
      const url = {
        singleCard: `${prefix}card_auth`,
        multiCard: `${prefix}universal_card_auth`,
        expCard: `${prefix}experience_card_auth`,
        swimCard: `${prefix}swim_card`,
        multiPtCard: `${prefix}multi_pt_card_auth`,
        packageCard: this.IS_BRAND_SITE ? 'Merchant/CardClass/checkAuthPackage' : 'Web/package/checkAuthPackage'
      };
      const requests = [];
      for (let [key, value] of Object.entries(url)) {
        requests.push(
          this.$service
              .get(value)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.cardSettingAuths[key] = true;
                } else {
                  this.cardSettingAuths[key] = false;
                }
              })
              .catch(err => {
                console.error(err);
              })
        );
      }
      Promise.all(requests).then(() => {
        console.log('触发')
        this.$store.commit(Types.CARD_SETTING_AUTH, this.cardSettingAuths);
      });
    },
    maxNumChange(num, index) {
      if(this.saleModalData[index+1] && this.saleModalData[index+1].min) {
        const max = this.saleModalData[index+1].max && this.saleModalData[index+1].max <= num + 1?num + 2:this.saleModalData[index+1].max
        this.$set(this.saleModalData, index + 1, {
          min: num + 1,
          max: max,
          price: this.saleModalData[index+1].price
        })
        this.maxNumChange(max, index+1)
      }
    },
    saveSaleModal() {
      const lastArr = this.saleModalData[this.saleModalData.length-1]
      if(lastArr.price === null) {
        this.$Message.error('结算单价不能为空');
        return;
      }
      if((lastArr.max && !/^[1-9]\d*$/.test(lastArr.max)) || !/^[1-9]\d*$/.test(lastArr.min)) {
        this.$Message.error('节数必须为正整数');
        return;
      }
      if(!/^[0-9]+(.[0-9]{1,2})?$/.test(lastArr.price)) {
        this.$Message.error('金额必须大于等于0且最只能保留两位小数');
        return;
      }
      this.formValidate.pt_settlement_unit_price = [...this.saleModalData]
      this.showSaleAdd = false
    },
    handleDeleteItem(index) {
      this.saleModalData.splice(index, 1)
    },
    addSaleRange() {
      const lastArr = this.saleModalData[this.saleModalData.length-1]
      let price = lastArr.price + ''
      if(!lastArr.max || price === 'null' || price === 'undefined') {
        this.$Message.error('请先填写完成上一阶段数据');
        return;
      }
      if(!/^[1-9]\d*$/.test(lastArr.max)) {
        this.$Message.error('节数必须为正整数');
        return;
      }
      if(!/^[0-9]+(.[0-9]{1,2})?$/.test(lastArr.price)) {
        this.$Message.error('金额必须大于等于0且最只能保留两位小数');
        return;
      }
      this.saleModalData.push({
        min: lastArr.max + 1,
        max: null,
        price: null
      })
    },
    handleDateChange(val) {
      this.formValidate.start_end_time.start = val[0];
      this.formValidate.start_end_time.end = val[1];
    },
    getBusList() {
      return this.$service.get('/Web/Card/get_merchants_bus_list').then(res => {
        if (res.data.errorcode == 0) {
          this.busList = res.data.data.list;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getBusGroupList() {
      return this.$service.get('/Web/business/get_level_list').then(res => {
        if (res.data.errorcode == 0) {
          const resList = res.data.data.list
          this.busGroupList = resList;
          this.busGroupBelongData[0].level_id = resList[0].level_id || ''
          this.busGroupBelongData[0].level_name = resList[0].level_name || ''
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    initBelongBusData () {
      const data = [{
        bus_id: this.busId,
        bus_name: this.busName,
        level_id: (this.busGroupList[0] && this.busGroupList[0].level_id) || '',
        level_name: (this.busGroupList[0] && this.busGroupList[0].level_name) || '',
        currentPriceFlag: false,
        enable_time_limit: 0,
        weekTimes: [],
        current_price: 0,
        alipay_sale_status: 0,
        sign_discount: 10,
        gift_number: 0,
        booking_discount: 10,
        buy_min_value: 1,
        single_price: 0,
        activation_restriction: 0,
        sale_rule_id: null, // 购卡规则id
        is_open_rule: '0'  // 是否开启规则 0否1是
      }]
      this.busGroupBelongData = data
      this.busBelongData = [{
        ...data[0],
        status: 0
      }]
    },
    dealWeek (week = []) {
      if (!week.length || !Array.isArray(week)) return '';
      const sorted = [...week].sort();
      return sorted.map(index => `周${this.dict[index]}`).join('、');
    },
    getSortData () {
      this.$service.post('/Web/CardGroup/getCardGroupList', { bus_id: this.$store.state.busId, type: this.cardThreeType == 1 ? 2 : this.cardThreeType == 2 ? 1 : 3 }).then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data;
          this.checkboxTagData = data.filter(item => item.id !== '0').map(item => ({ ...item, name: item.title }));
        } else {
          this.$Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e);
      });
    },
    onTagAdd (title, type) {
      return this.$service.post('/Web/CardGroup/addCardGroup', {
        bus_id: this.$store.state.busId,
        title, type
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.getSortData();
          return Promise.resolve();
        } else {
          this.$Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e);
      });
    },
    onTagDelete (id) {
      if (id === '0') return Promise.reject();
      const url = '/Web/CardGroup/deleteCardGroup';
      const postData = {
        bus_id: this.$store.state.busId,
        id
      };
      return this.$service.post(url, postData).then(res => {
        if (res.data.errorcode === 0) {
          return Promise.resolve();
        } else {
          this.$Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e);
      });
    },
    hasAuth (type) {
      return (this.cardSettingAuth && this.cardSettingAuth[type]) || this.cardId !== 0;
    },
    handleSubmit (name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          /**
           * @description
           * 01 普通期限卡 card_type_id=1
           * 02 普通次卡 card_type_id=2
           * 03 普通储值卡 card_type_id=3
           * 04 普通私教卡 card_type_id=4
           * 11 多店通用期限卡 universal_card
           * 12 多店通用次卡 universal_card
           * 13 多店通用储值卡 universal_card
           * 21 体验期限卡 experience_card
           * 22 体验次卡 experience_card
           * */
          this.formValidate.card_type_id = this.formValidate.subCardType == 7 ? 4 :  this.formValidate.subCardType;
          this.formValidate.group_id = this.checkboxTag[0] || '0';
          if (this.formValidate.cardType == 1) {
            this.formValidate.universal_card = 1;
          } else {
            this.formValidate.universal_card = 0;
          }
          if (this.formValidate.cardType == 2) {
            this.formValidate.experience_card = 1;
          } else {
            this.formValidate.experience_card = 0;
            // 不是体验卡就重置体验卡专用参数， 后台默认为0
            const { formValidate: form } = this;
            form.membership_grant = form.give = '0';
            form.activation_end = 0;
          }
          this.formValidate.is_pt_time_limit_card = this.formValidate.subCardType == 7 ? 1 : 0

          // if (this.formValidate.status == 1 && this.formValidate.card_type_id == 4) {
          //   if (this.formValidate.single_price == 0) {
          //     this.$Message.error('请填写单节售价');
          //     return false;
          //   }
          //   if (!this.formValidate.buy_min_value) {
          //     this.$Message.error('请填写起购节数');
          //     return false;
          //   }
          // }
          // 期限卡或者私教包月
          if (this.formValidate.subCardType == 1 || this.formValidate.subCardType == 7) {
            this.formValidate.number = this.formValidate.end_time;
          }
          this.formValidate.help_sale_bus_id = Array.isArray(this.formValidate.help_sale_bus_list) ? this.formValidate.help_sale_bus_list.join(',') : ''

          let postData = { ...this.formValidate }
          // 不是储值卡，删除自主续充相关数据
          if (postData.subCardType != 3) {
            delete postData.self_recharge
            delete postData.recharge_package
          } else {
            // 储值卡，未开启自主续充，
            if(postData.self_recharge != '1') {
              delete postData.recharge_package
            } else {
              // 开启后
              const rechargeList = postData.recharge_package.map((
                { id, amount, number, gift_number, delay_number, ...rest }
              ) => {
                return { id, amount, number, gift_number, delay_number }
              })
              postData.recharge_package = JSON.stringify(rechargeList)
            }
          }

          let belongData = JSON.parse(JSON.stringify(this.formValidate.deal_type ? this.busGroupBelongData : this.busBelongData))
          let errMsg = ''
          if (Array.isArray(belongData)) {
            belongData.forEach((item) => {
              if(item.is_open_rule === '1' && ['', null, undefined].includes(item.sale_rule_id)) {
                errMsg = '已开启购卡规则，请选择'
              }
              if (this.formValidate.cardType == 2) {
                if (this.formValidate.deal_type) {
                  item.level_id = Array.isArray(item.level_id) ? item.level_id.join(',') : item.level_id
                } else {
                  item.bus_id = Array.isArray(item.bus_id) ? item.bus_id.join(',') : item.bus_id
                }
              }
              if((this.formValidate.subCardType ==4 || this.formValidate.subCardType == 5) && !/^\d+$/.test(item.gift_number) && this.formValidate.cardType != 2) {
                errMsg = '赠送节数只能为正整数或0'
              }
              if(this.formValidate.cardType != 2 && Array.isArray(item.weekTimes)) {
                for (const time of item.weekTimes) {
                  let { weeks, start_time, end_time } = time
                  if (!weeks.length || !start_time || !end_time) {
                    errMsg = '请选择使用时间段'
                  }
                  const beginNum = +start_time.split(':').join('');
                  const endNum = +end_time.split(':').join('');
                  if (endNum - beginNum <= 0) {
                    errMsg = '时间段错误'
                  }
                  time.weeks = time.weeks.join(',')
                }
              }
            })
            if (errMsg) {
              this.$Message.error(errMsg);
              return false;
            }
          } else {
            belongData = ''
          }
          postData.belong = JSON.stringify(belongData)
          this.$service
            .post(`/Web/Card/${this.cardId == 0 ? 'add_card' : 'update_card'}`, postData)
            .then(res => {
              if (res.data.errorcode == 0) {
                const self = this;
                this.$Message.success({
                  content: '保存成功！',
                  onClose () {
                    self.$store.dispatch('getmemberCardList'); //更新会员卡列表的下拉卡列表信息
                    self.$router.push({ path: '/Web/Member/card', query: { ttp: Date.parse(new Date()) } });
                  }
                });
              } else if(res.data.errorcode == 50000) {
                this.supportMobileModal = true
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        } else {
          this.$Message.error('请正确填写数据!');
        }
      });
    },
    handleReset (name) {
      if (this.cardId === 0) {
        this.$refs[name].resetFields();
      } else {
        this.getCardInfo();
      }
    },
    handleCardType () {
      if (this.cardId === 0) {
        this.formValidate.subCardType =  this.cardThreeType == 1 ? '1' : this.cardThreeType == 2 ? '4' : '5';
        this.initBelongBusData()
        this.getCardRuleList()
      }
    },
    handleSubCardType (val) {
      if (this.cardId === 0) {
        if (val == 1) {
          // date of card
          this.formValidate.end_time = 1;
        } else if (val == 2) {
          // count of card
          if (this.formValidate.cardType == 2) {
            // experience of card
            this.formValidate.end_time = 1;
          } else {
            this.formValidate.end_time = 0;
          }
          this.formValidate.number = 1;
        } else if (val == 3) {
          // store of card
          this.formValidate.end_time = 0;
        } else if (val == 4) {
          this.formValidate.is_pt_time_limit_card = 0
          this.formValidate.end_time = 1
        } else if (val == 7) {
          this.formValidate.is_pt_time_limit_card = 1
          this.formValidate.end_time = 30
        }
        this.initBelongBusData()
        this.getCardRuleList()
      }
    },
     unescapeHTML (a) {
      a = '' + a;
      return a
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    },
    getCardInfo () {
      if (this.cardId !== 0) {
        return this.$service.get(`/Web/Card/get_card_detail/busid/${this.$route.query.bus}/id/${this.cardId}`).then(res => {
          if (res.data.errorcode == 0) {
            /**
             * @description
             * 01 普通期限卡 card_type_id=1
             * 02 普通次卡 card_type_id=2
             * 03 普通储值卡 card_type_id=3
             * 04 普通私教卡 card_type_id=4
             * 05 泳教卡 card_type_id=5
             * 11 多店通用期限卡 universal_card
             * 12 多店通用次卡 universal_card
             * 13 多店通用储值卡 universal_card
             * 21 体验期限卡 experience_card
             * 22 体验次卡 experience_card
             * */
            const card = res.data.data;

            this.formValidate = card;
            if(card.is_pt_time_limit_card==1) {
              this.saleModalData = card.pt_settlement_unit_price.map(obj=>{
                return {
                  max: obj.max === ''?null:Number(obj.max),
                  min: obj.min === ''?null:Number(obj.min),
                  price: obj.price === ''?null:Number(obj.price)
                }
              })
            }
            if (card.universal_card == 0 && card.experience_card == 0) {
              this.formValidate.cardType = '0';
            } else if (card.universal_card == 1) {
              this.formValidate.cardType = '1';
            } else if (card.experience_card == 1) {
              this.formValidate.cardType = '2';
            }

            this.formValidate.subCardType = card.is_pt_time_limit_card==1?'7':card.card_type_id;
            if(card.card_type_id != 3) {
              this.formValidate.number = parseInt(card.number);
              this.formValidate.gift_number = parseInt(card.gift_number);
            } else {
              this.formValidate.number = parseFloat(card.number);
              this.formValidate.gift_number = parseFloat(card.gift_number);
              this.$set(this.formValidate, 'self_recharge', card.self_recharge || '0') // 储值卡自主续充
              // 续充设置数据
              const resRechargeList = card.recharge_package && JSON.parse(card.recharge_package)
              if (Array.isArray(resRechargeList) && resRechargeList.length) {
                resRechargeList.forEach((v, i) => {
                  v.key = v.id || Date.now() + i
                  v.isSame = false // 用于显示隐藏校验提示
                })
                this.$set(this.formValidate, 'recharge_package', resRechargeList)
              } else {
                this.$set(this.formValidate, 'recharge_package', [{
                  // id: ''
                  key: Date.now(), // 有则为id，没有id则使用时间戳
                  isSame: false, // 用于显示隐藏校验提示
                  amount: 0, // 售价
                  number: 0, // 储值卡价值
                  gift_number: 0, // 赠送价值
                  delay_number: 0, // 延长到期天数
                }])
              }
            }

            this.formValidate.per_day_pt_class_num = parseInt(card.per_day_pt_class_num);
            this.formValidate.per_all_pt_class_num = card.per_all_pt_class_num && +card.per_all_pt_class_num > 0 ? parseInt(card.per_all_pt_class_num) : '';
            this.formValidate.end_time = parseInt(card.end_time);
            this.formValidate.enable_suspend_num = Number(card.enable_suspend_num);
            this.formValidate.enable_suspend_day = Number(card.enable_suspend_day);
            this.formValidate.class_duration = parseFloat(card.class_duration);
            this.formValidate.buy_min_value = parseFloat(card.buy_min_value);
            this.formValidate.activation_end = +card.activation_end;
            this.formValidate.help_sale_bus_list = card.help_sale_bus_list;
            this.formValidate.start_end_time = card.start_end_time;
             this.formValidate.description = this.unescapeHTML(card.description);
            this.duringDate = [card.start_end_time.start || '', card.start_end_time.end || ''];
            this.checkboxTag = [card.group_id];

            let belongBusInfo = card.belong
            if (Array.isArray(belongBusInfo)) {
              belongBusInfo.forEach((item, index) => {
                if (Array.isArray(item.weekTimes)) {
                  item.weekTimes.forEach((time, index) => {
                    time.weeks = time.weeks.split(',')
                  })
                }
                if (item.bus_id && (item.bus_id+ '').indexOf(',') !== -1) {
                  item.bus_id = item.bus_id.split(',')
                  item.bus_name = item.bus_name.split(',')
                }
                if (item.level_id && (item.level_id+ '').indexOf(',') !== -1) {
                  item.level_id = item.level_id.split(',')
                  item.level_name = item.level_name.split(',')
                }
              });
            } else {
              belongBusInfo = ''
            }
            if(this.formValidate.deal_type) {
              this.busGroupBelongData = belongBusInfo
            } else {
              this.busBelongData = belongBusInfo
            }

            this.shouldShowEdit = true
            if (!this.authorization) {
              this.$Message.warning('可查看,无编辑权限');
            }

            this.getCardRuleList() // 获取购卡规则列表
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      } else {
        return false;
      }
    },
    getCardTypeName() {
      if (!this.formValidate) {
        return ''
      //} else if (this.formValidate.experience_card == 1) {
      } else if (this.formValidate.cardType == 2) {
        return 'expCard'
      //} else if (this.formValidate.universal_card == 1) {
      } else if (this.formValidate.cardType == 1) {
        return 'multiCard'
      //} else if (this.formValidate.universal_card == 1
      //   && this.formValidate.card_type_id == 4) {
      } else if (this.formValidate.cardType == 1
        && this.formValidate.subCardType == 4) {
        return 'multiPtCard'
      //} else if (this.formValidate.universal_card == 0) {
      } else if (this.formValidate.cardType == 0) {
        return 'singleCard'
      } else if (this.formValidate.subCardType == 6) {
        return 'packageCard'
      } else if (this.formValidate.subCardType == 5) {
        return 'swimCard'
      }
    },
    getBusInfoById (id) {
      for (const bus of this.adminBusList) {
        if (id == bus.id) {
          return {
            bus_id: id,
            bus_name: bus.name
          }
        }
      }
      return {
        bus_id: '',
        bus_name: ''
      }
    },
    handleDownloadWord () {
      window.open(
        'https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf'
      );
    },
    getCardRuleList() {
      const { subCardType, is_pt_time_limit_card } = this.formValidate

      const params = {
        card_type_id: subCardType == 7 ? '4' : subCardType,
        is_pt_time_limit_card,
      };

      this.$service.post('/Web/CardSaleRule/getRuleList', params).then(({ data: resData }) => {
        const { errorcode, errormsg, data } = resData;
        if (errorcode === 0) {
          this.cardRuleList = data.list;
        } else {
          this.$Message.error(errormsg);
        }
      })
      .catch(err => { console.error(err) });
    },
    // 课程封面-选择图片-确定按钮事件
    handleUpload(path){
      this.formValidate.thumb = path;
    },
  },
};
</script>
<style>
.bus-card-table .ivu-card-body {
  padding: 0;
}
</style>
<style lang="less" scoped>
.delete-sale {
  cursor: pointer;
  margin-left: 10px;
  color: rgba(238, 61, 61, 0.87);
}
.saleadd-top {
  color: #333;
  font-weight: bold;
  padding: 10px 0 20px;
  text-align: center;
}
.saleadd-mb {
  margin-bottom: 15px;
}
.saleadd-btn {
  color: #2db7f5;
  cursor: pointer;
}
.bus-card {
  margin: 0 0 15px 0;
  width: 550px;
}
.across-row {
  display: flex;
  flex-direction: row;
}

.bodybuilding {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .w-red {
    font-size: 16px;
    color: red;
  }

  .w-blue {
    font-size: 16px;
    color: blue;
  }

  .word {
    height: 169px;
    width: 242px;
  }
}
.item-wrap {
  margin-bottom: 15px;
}
.info-wrap {
  width: 100%;
  height: auto;
  line-height: 30px;
  max-width: 550px;
  margin-bottom: 15px;
  .item-text {
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .time-wrap {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    vertical-align: middle;
    margin-right: 5px;
    width: 250px;
    .time-item {
      flex: 1;
    }
    .time-text {
      width: 130px;
      height: 30px;
      line-height: 30px;
      vertical-align: middle;
    }
  }
  .item-text {
    width: 100px;
  }
  .item-mar {
    display: inline-block;
    margin: 0 5px 0 0;
    vertical-align: middle;
  }
}
.button {
  width: 50px;
  min-width: 0;
  height: 30px;
}
  .description {
      width: 650px;
  }
.image-description{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  img{
    width: 345px;
    height: 200px;
    margin-bottom: 10px;
  }
}
.upload-modal-content{
  width:100%;
  height: 350px;
  background: #0f0;
}
</style>
