<template>
  <router-view v-if="$route.name !== '会员卡管理'"></router-view>
  <div class="tab-table-wrap customized-tabs" v-else>
    <div class="card-rule-setting-row">
      <div v-show="['0', '1', '2'].includes(activeIndex)">
        <router-link
          v-if="hasCardSettingAuth"
          v-show="activeIndex !== '2' || (cardSettingAuth && cardSettingAuth.swimCard)"
          :to="{ path: `/member/card/save/0/${+activeIndex + 1}`}">
          <Button type="success">
            新增{{activeIndex == '0' ? '会籍卡':activeIndex == '1' ? '私教课' : '泳教课'}}
          </Button>
        </router-link>
        <Button
          v-if="cardRuleAuth"
          class="top-btn"
          @click="handleToCardRules"
        >浮动售价规则设置</Button>
        <Button
          v-if="activeIndex == '0'"
          class="top-btn"
          @click="showRecharge=true"
        >批量修改储值卡续充规则</Button>
        <Button
          v-if="adminInfo.is_admin && ['1', '2'].includes(activeIndex)"
          class="top-btn"
          @click="otherCase('3')"
        >转化课程类型</Button>
      </div>

      <router-link
        v-if="cardSettingAuth && cardSettingAuth.packageCard"
        v-show="activeIndex === '3'"
        :to="{ path: '/bundlePackage/save'}">
        <Button class="top-btn" type="success">新增套餐包</Button>
      </router-link>

      <Button class="top-btn" @click="otherCase('2')">导出excel</Button>
    </div>

    <Tabs @on-click="clickTabs" :value="activeIndex">
      <TabPane label="会籍卡" name="0">
          <NewCardList :ref="(el) => cardListRef['0'] = el" :card-type="1" v-if="activated.includes('0')" :cardRuleAuth="cardRuleAuth" :showRecharge.sync="showRecharge" />
      </TabPane>
      <TabPane label="私教课" name="1">
          <NewCardList :ref="(el) => cardListRef['1'] = el" :card-type="2" v-if="activated.includes('1')" :cardRuleAuth="cardRuleAuth" />
      </TabPane>
      <TabPane label="游泳私教" name="2" v-if="cardSettingAuth && cardSettingAuth.swimCard">
          <NewCardList :ref="(el) => cardListRef['2'] = el" :card-type="3" v-if="activated.includes('2')" :cardRuleAuth="cardRuleAuth" />
      </TabPane>
      <TabPane label="套餐包" name="3" v-if="cardSettingAuth && cardSettingAuth.packageCard">
          <PackageCardList :ref="(el) => cardListRef['3'] = el" :card-type="4" v-if="activated.includes('3')"/>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import NewCardList from './components/NewCardList.vue'
import PackageCardList from './components/PackageCardList.vue'
import * as Types from '../../store/mutationTypes';

export default {
  name: 'cardList',
  components: {
    NewCardList,
    PackageCardList
  },
  data () {
    return {
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      cardListRef: [],
      activated: ['0'],
      cardSettingAuth: {
        expCard: false,
        multiCard: false,
        multiPtCard: false,
        singleCard: false,
        packageCard: true,
        swimCard: true,
      },
      cardRuleAuth: false, // 购卡规则设置权限
      hasCardSettingAuth: false, // 会员卡设置权限
      activeIndex: '0',

      showRecharge: false // 显示批量修改储值卡续充设置
    };
  },
  watch: {
    cardSettingAuth: {
      handler() {
        this.doseHasAuth()
      },
      deep: true
    },
    showChange(val) {
      if (!val) {
        this.changeCardForm.card_ids = ''
      }
    }
  },
  computed: {
    ...mapState(['busId', 'adminBusList', 'globalBelongBusId', 'adminInfo'])
  },
  methods: {
    doseHasAuth() {
      for (let value of Object.values(this.cardSettingAuth)) {
        if (value) {
          this.hasCardSettingAuth = true;
          break;
        }
        this.hasCardSettingAuth = false;
      }
    },
    getCardSettingAuth () {
      const prefix = this.IS_BRAND_SITE ? 'Merchant/CardClass/' : 'Web/Card/'
      const url = {
        singleCard: `${prefix}card_auth`,
        multiCard: `${prefix}universal_card_auth`,
        expCard: `${prefix}experience_card_auth`,
        swimCard: `${prefix}swim_card`,
        multiPtCard: `${prefix}multi_pt_card_auth`,
        packageCard: this.IS_BRAND_SITE ? 'Merchant/CardClass/checkAuthPackage' : 'Web/package/checkAuthPackage'
      };
      const requests = [];
      for (let [key, value] of Object.entries(url)) {
        requests.push(
          this.$service
              .get(value)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.cardSettingAuth[key] = true;
                } else {
                  this.cardSettingAuth[key] = false;
                }
              })
              .catch(err => {
                console.error(err);
              })
        );
      }
      Promise.all(requests).then(() => {
        this.$store.commit(Types.CARD_SETTING_AUTH, this.cardSettingAuth);
      });
    },
    // 购卡规则设置权限校验
    getCardRuleSettingAuth() {
      this.$service.post('/Web/CardSaleRule/set_rule').then(res => {
        if (res.data.errorcode === 0) {
          this.cardRuleAuth = true;
        } else if (res.data.errorcode === 40014) {
          this.cardRuleAuth = false;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    clickTabs(index) {
      this.activeIndex = index;
      const active = document.querySelector('.ivu-tabs-ink-bar');
      active.setAttribute('class', `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`)
      sessionStorage.setItem('cardListActive', index);
      if (!this.activated.includes(index)) {
        this.activated.push(index);
      }
    },
    handleToCardRules() {
      this.$router.push({ name: '购卡规则设置' })
    },

    otherCase (val) {
      const ref = this.cardListRef[this.activeIndex]
      ref && ref.otherCase(val)
    },
  },
  created() {
    this.getCardSettingAuth();
    this.getCardRuleSettingAuth();
  },
  mounted() {
    if (sessionStorage.getItem('cardListActive') !== null && sessionStorage.getItem('cardListActive') !== '0') {
      this.clickTabs(sessionStorage.getItem('cardListActive'))
      this.activated = [sessionStorage.getItem('cardListActive')]
    }
  }
};
</script>

<style lang="less">
  .tab-table-wrap .ivu-tabs {
    min-height: 0;
  }

</style>

<style lang="less" scoped>
  .card-rule-setting-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-bottom: 32px;
    // text-align: right;
    .top-btn {
      margin-left: 20px;
    }
  }
</style>
