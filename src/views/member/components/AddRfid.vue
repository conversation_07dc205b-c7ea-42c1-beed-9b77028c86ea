<template>
  <Modal v-model="showAdd" :mask-closable="false" title="RFID设备绑定">

    <Form ref="rfidForm" :model="postData" class="modal-form" :label-width="120">
      <input id="hiddenText" type="text" style="display:none"/>
      <Form-item label="RFID编号" prop="RFID_id" :rules="{required: true, message: '请输入'}">
        <Input @on-enter="doAdd" ref="rfid" placeholder="请将RFID设备放置在读取器上或者手动输入编号" v-model="postData.RFID_id" autofocus/>
        <cardReader v-if="showAdd" @on-change="onReaderChange" style="float: right"></cardReader>
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="doAdd">保存</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
  </Modal>
</template>
<script>
  import cardReader from 'components/card/cardReader.vue';

  export default {
    name: 'AddRfid',
    components: { cardReader },
    props: {
      userId: {
        type: [String, Number]
      },
      rfidId: '',
      value: {
        type: Boolean
      },
      beforeBind: Function
    },
    data() {
      return {
        followTypes: ['电话', '微信', 'QQ', '面谈', '其他'],
        postData: {
          user_id: this.userId,
          RFID_id: this.rfidId
        }
      };
    },
    computed: {
      showAdd: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      rfidVal() {
        return this.rfidId;
      }
    },
    watch: {
      showAdd(val) {
        if (!val && this.rfidId != this.postData.RFID_id) {
          if (this.rfidId) {
            this.postData.RFID_id = this.rfidId;
          } else {
            this.$refs.rfidForm.resetFields();
          }
        }
      },
      rfidVal(val) {
        this.postData.RFID_id = val;
      }
    },
    updated() {
      this.focus();
    },
    methods: {
      onReaderChange(id) {
        this.postData.RFID_id = id;
        this.doAdd();
      },
      focus() {
        this.$nextTick(() => {
          this.$refs.rfid && this.$refs.rfid.focus();
        });
      },
      doAdd() {
        this.$refs.rfidForm.validate(val => {
          if (!val) return false;

          if (this.beforeBind) {
            return this.beforeBind(this.postData.RFID_id).then(() => {
              this.showAdd = false;
              this.$emit('on-success');
            });
          }

          this.$service
            .post('/Web/member/add_RFID', this.postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showAdd = false;
                this.$emit('on-success');
                this.$Message.success(res.data.errormsg);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              this.$Message.error(err);
            });
        });
      }
    }
  };
</script>

<style scoped>
</style>
