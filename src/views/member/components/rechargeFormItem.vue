<template>
  <div>
    <Form-item label="会员自主续充" prop="self_recharge">
      <template slot="label">
        <span>会员自主续充</span>
        <Tooltip>
          <div slot="content">开启后会员可以在会员端自主充值续费</div>
          <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
        </Tooltip>
      </template>
      <i-switch
        :value="selfRecharge"
        true-value="1"
        false-value="0"
        @on-change="handleChangeOpenSetting" />
    </Form-item>
    <Form-item
      v-if="selfRecharge === '1'"
      :label="isModalUse ? '' : '充值套餐设置'"
      :label-width="isModalUse ? 25 : 160"
    >
      <div class="t-header">
        <span class="th"></span>
        <span class="th">售价</span>
        <span class="th">储值价值</span>
        <span class="th">赠送价值</span>
        <span class="th">
          储值卡延期天数
          <Tooltip placement="top">
            <div slot="content">充值后自动延长储值卡有效期的使用天数</div>
            <Icon size="16" type="ios-help-circle" color="#ffcf05"></Icon>
          </Tooltip>
        </span>
        <span class="th"></span>
      </div>
      <div v-for="(item, index) in list" :key="item.key" class="form-item-row">
        <FormItem
          class="item-num"
          :prop="'recharge_package.' + index + '.isSame'"
          :rules="rules.item">
          <span>方案{{ index + 1 }}</span>
        </FormItem>
        <FormItem :prop="'recharge_package.' + index + '.amount'" :rules="[rules.itemInput, { min: 0.01, type: 'number', message: '售价需要大于0' }]">
          <InputNumber
            v-model="item.amount"
            :min="0.01"
            :max="99999999"
            :precision="2"
            :active-change="false"
            placeholder="请填写"
            @on-change="handleChangeAmount($event, index)"
          />
        </FormItem>
        <FormItem :prop="'recharge_package.' + index + '.number'" :rules="rules.itemInput">
          <InputNumber
            v-model="item.number"
            :min="0"
            :max="99999999"
            :precision="2"
            :active-change="false"
            placeholder="请填写"
            @on-change="handleChangeInput"
          />
        </FormItem>
        <FormItem :prop="'recharge_package.' + index + '.gift_number'" :rules="rules.itemInput">
          <InputNumber
            v-model="item.gift_number"
            :min="0"
            :max="99999999"
            :precision="2"
            :active-change="false"
            placeholder="请填写"
            @on-change="handleChangeInput"
          />
        </FormItem>
        <FormItem :prop="'recharge_package.' + index + '.delay_number'" :rules="rules.itemInput">
          <InputNumber
            v-model="item.delay_number"
            :min="0"
            :max="9999"
            :precision="0"
            :active-change="false"
            placeholder="请填写"
            @on-change="handleChangeInput"
          />
        </FormItem>
        <FaIcon
          v-if="index > 0"
          class="del-btn"
          name="trash-o"
          title="删除"
          size="20"
          color="#d9544f"
          @click.native="handleDeleteItem(index)" />
        <span v-else class="del-btn"></span>
      </div>
      <Button type="info" @click="handleAddItem">新增方案</Button>
    </Form-item>
  </div>
</template>

<script>
export default {
  // 续充设置的表单组件
  name: 'RechargeFormItem',
  props: {
    selfRecharge: {
      type: String, // 1 开启 0 关闭
      required: true,
    },
    list: {
      type: Array,
      default: () => [],
    },
    isModalUse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        item: {
          validator: (rule, val, cb) => {
            if (val) {
              cb(new Error('存在相同方案'))
            } else {
              cb()
            }
          },
          trigger: 'change'
        },

        itemInput: { required: true, type: 'number', message: '请填写', trigger: 'change' },
      }
    }
  },

  methods: {
    // 伪清除指定表单的校验提示
    handleClearValidate(prop) {
      const field = this.$parent.fields.find(field => field.prop === prop);
      if (field && field.validateMessage === '存在相同的方案') {
        field.validateState = ''
        field.validateMessage = ''
      }
    },
    // 校验指定表单
    handleValidate(prop, trigger = 'change') {
      const field = this.$parent.fields.find(field => field.prop === prop);
      // console.log(prop, !!field, this.$parent.fields.map(v => v.prop))
      field && field.validate(trigger)
    },
    // 更新校验提示
    updateValidate() {
      this.list.forEach((_, i) => {
        const prop = `recharge_package.${i}.isSame`
        this.handleValidate(prop)
      })
    },
    handleChangeOpenSetting(value) {
      this.$emit('update:self-recharge', value)
      // 如果关闭后，不显示设置项，则不再需要这段代码
      // if (value === '0') {
      //   this.$nextTick(() => {
      //     const keys = ['amount', 'number', 'gift_number', 'delay_number']
      //     for (let index = 0; index < this.list.length; index++) {
      //       keys.forEach(key => {
      //         const prop = `recharge_package.${index}.${key}`
      //         this.handleClearValidate(prop)
      //       })
      //     }
      //   })
      // }
    },
    handleChangeAmount(val, index) {
      if (this.list[index].number === 0) {
        this.list[index].number = val
      }
      this.handleChangeInput()
    },
    handleChangeInput() {
      // 拿到填写完整数据的项
      const copyList = [...this.list].filter(v => {
        if ([v.amount, v.number, v.gift_number, v.delay_number].includes(null)) {
          v.isSame = false;
          return false
        } else {
          return true
        }
      })

      if (copyList.length === 1) {
        copyList[0].isSame = false;
        this.updateValidate()
        return;
      }
      // 检查是否有完全相同的数据
      first:
      for (let i = 0; i < copyList.length; i++) {
        const v = copyList[i];

        second:
        for (let j = 0; j < copyList.length; j++) {
          const k = copyList[j];
          // 自己和自己不比对
          if (v.key === k.key) {
            continue second;
          }
          // 有完全相同数据，则显示校验信息
          if (v.amount === k.amount && v.number === k.number && v.gift_number === k.gift_number && v.delay_number === k.delay_number) {
            v.isSame = true
            continue first;
          }
        }
        // 没有完全相同数据，则隐藏校验信息
        v.isSame = false
      }

      this.updateValidate()
    },

    handleDeleteItem(index) {
      this.list.splice(index, 1)
    },

    handleAddItem() {
      if (this.list.length >= 50) {
        this.$Message.error('已达最大可设置上限')
        return;
      }
      this.list.push({
        key: Date.now(), // 没有id 则使用时间戳，有则为id
        isSame: false, // 用于显示隐藏校验提示
        amount: 0, // 售价
        number: 0, // 储值卡价值
        gift_number: 0, // 赠送价值
        delay_number: 0, // 延长到期天数
      })
    }
  }
}
</script>

<style lang="less" scoped>
.t-header {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  max-width: 550px;

  .th {
    flex: 1;
    white-space: nowrap;
    &:first-child {
      margin-right: 10px;
      min-width: 55px;
      flex: initial;
    }
    &:last-child {
      margin-left: 12px;
      min-width: 18px;
      flex: initial;
    }
  }
}
.form-item-row {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  max-width: 550px;

  .ivu-form-item {
    margin-right: 10px;
    flex: 1;
    &:last-child {
      margin-right: 0;
    }
  }

  .item-num {
    margin-right: 10px;
    min-width: 55px;
    flex: initial;
  }
  .ivu-input-number {
    width: 100%;
  }

  /deep/.ivu-form-item-error-tip {
    white-space: nowrap;
  }

  .del-btn {
    margin-left: 12px;
    min-width: 18px;
    cursor: pointer
  }
}
</style>
