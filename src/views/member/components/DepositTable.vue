<template>
  <div class="table-wrap deposit">
    <header>
      <Select v-model="selectBusId" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input style="width: 180px"
             v-model="searchText"
             placeholder="姓名/电话/实体卡号" />
      <dateRange :clearable="true"
                 :days.sync="days"
                 @on-change="val => { dateRange = val }"></dateRange>
      <Select v-model="status"
              placeholder="定金状态"
              clearable>
        <Option value="0">未启用</Option>
        <Option value="1">已启用</Option>
        <Option value="2">已退款</Option>
      </Select>
      <saleSelect v-model="searchSaleId"
                  isCoach
                  :belongBusId="selectBusId"
                  placeholder="收款人"></saleSelect>
      <Select v-model="purpose"
              placeholder="定金用途"
              clearable>
        <Option value="0">会员卡</Option>
        <Option value="1">私教课</Option>
        <Option value="2">其它</Option>
      </Select>
      <Button type="success"
              @click="doSearch">搜索</Button>
    </header>
    <div class="total-stat">
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.all_told }}</h3>
        <p>总收定金</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.not_start_using }}</h3>
        <p>未启用定金</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.drawback }}</h3>
        <p>已退定金</p>
      </div>
      <b></b>
      <div class="stat">
        <div id="depositRing"
             style="width: 200px; height: 100%"></div>
      </div>
    </div>
    <Table :columns="columns"
           :data="tableData"
           ref="table"
           disabled-hover></Table>

    <ReturnPayModal
      :newPayType="newPayType"
      :amount="returnPayAmount"
      :createTime="createTime"
      :userId="reUserId"
      v-model="showBackMoney"
      @on-confirm="backConfirm" />

    <footer>

      <div style="display: flex">
        <Button type="success"
                style="margin-right: 30px"
                @click="takeDeposit = true">收定金</Button>
        <Dropdown @on-click="otherCase"
                  placement="top">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">导出excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page :total="depositData.count"
            :current.sync="currentPage"
            show-total
            show-sizer
            placement="top"
            @on-change="getDepositList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <AddDeposit v-model="takeDeposit" @on-success="getDepositList" @on-printinfo="depositFinish" />
    <receipt-modal v-model="showPrint" :to-path='toPath' />
    <Modal title="支付方式" v-model="showPayTypeDetail">
      <Table :columns="payDetailColumns" :data="payTypeDetail" disabledHover></Table>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
  import eCharts from 'echarts'
  import dateRange from 'src/components/picker/datePickerWithButton'
  import saleSelect from 'src/components/membership/salesSelect'
  import AddDeposit from './AddDeposit.vue'
  import { getNewHost } from 'utils/config'
  import { formatDate } from 'utils'
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js'
  import ReturnPayModal from 'components/form/ReturnPayModal'
  import { mapState, mapActions } from 'vuex'

  export default {
    name: 'depositTable',
    mixins: [receipt],
    data() {
      return {
        excelAuth: false,
        days: [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()],
        searchText: '',
        searchSaleId: '',
        dateRange: [],
        status: '',
        purpose: '',
        tableData: [],
        exportData: [],
        depositData: {},
        depositChart: null,

        userUrl: `${getNewHost()}/#/member/detail/`,
        showBackMoney: false,
        takeDeposit: false,

        sizer: 10,
        currentPage: 1,

        backDepositId: '',
        backDepositUserId: '',
        newPayType: [],
        returnPayAmount: 0,
        reUserId: '',
        createTime: '',
        refundTime: '',

        columns: [
          {
            title: '时间',
            key: 'create_time',
            width: 200
          },
          {
            title: '会员',
            key: 'user',
            render: (h, param) => {
              // let url = this.userUrl + param.row.user.id
              // return (
              //   <a target="_blank" href={url}>
              //     {param.row.user.name}
              //   </a>
              // )
              const item = param.row
              return <a onClick={() => {
                this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
              }}>{item.username}</a>
            }
          },
          {
            title: '定金金额',
            key: 'amount'
          },
          {
            title: '定金用途',
            key: 'purpose'
          },
          {
            title: '收款人',
            key: 'marketers_name'
          },
          {
            title: '支付方式',
            key: 'pay_type_name',
            render: (h, { row }) => {
              if (row.pay_type_name && row.pay_type_name.split(',').length > 1) {
                return (
                  <i-button
                    onClick={() => {
                      this.handleShowPayDetail(row)
                    }}
                    type="text"
                  >
                    {row.pay_type_name}
                  </i-button>
                )
              } else {
                return <div>{row.pay_type_name}</div>
              }
            }
          },
          {
            title: '收取时间',
            key: 'date'
          },
          {
            title: '状态',
            key: 'status',
            render: (h, param) => {
              let status = param.row.status
              return <span style={{ color: status === '已退款' ? '#d8321f' : '' }}>{status}</span>
            }
          },
          {
            title: '启用/退款时间',
            key: 'start_refund_date'
          },
          {
            title: '描述',
            ellipsis: true,
            key: 'description',
            render: (h, param) => {
              return <span title={param.row.description}>{param.row.description}</span>
            }
          },
          {
            title: '操作',
            key: 'operation',
            render: (h, param) => {
              let operation = param.row.operation
              return (
                <i-button type="text" disabled={operation.disabled || (this.jumpBusId!==this.busId)} onClick={() => this.clickBackDeposit(param.row)}>
                  退款
                </i-button>
              )
            }
          }
        ],
        depositOption: {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            top: 18,
            right: 25,
            formatter: '{name}',
            data: ['已启用', '未启用', '已退款'],
            itemHeight: 12,
            itemWidth: 12
          },
          color: ['#1abbde', '#a76de8', '#ff6969'],
          series: [
            {
              name: '定金详情',
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['30%', '50%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '14',
                    fontWeight: 'normal'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: [
                { value: 0, name: '已启用', code: 'start_using' },
                { value: 0, name: '未启用', code: 'not_start_using' },
                { value: 0, name: '已退款', code: 'drawback' }
              ]
            }
          ]
        },
        storeList: [],
        selectBusId: '',
        jumpBusId: '',

        showPayTypeDetail: false,
        payTypeDetail: [],
        payDetailColumns: [
          {
            title: '支付方式',
            key: 'pay_type_name'
          },
          {
            title: '支付金额',
            key: 'amount'
          }
        ]
      }
    },
    components: {
      dateRange,
      AddDeposit,
      saleSelect,
      ReturnPayModal,
      receiptModal
    },
    computed: {
      ...mapState(['busId'])
    },
    created() {
      let days = [this.timeToDate(this.days[0]), this.timeToDate(this.days[1])]
      this.dateRange = days
      this.selectBusId = this.busId
      this.jumpBusId = this.busId
      this.getStoreList()
      this.getDepositList()
      this.getExcelAuth()
    },
    methods: {
      ...mapActions(["getAdminInfo", "getReceiptAuth"]),
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      timeToDate(time) {
        return formatDate(new Date(time), 'yyyy-MM-dd')
      },
      doSearch() {
        this.sizer = 10
        this.currentPage = 1
        this.getDepositList()
      },
      backConfirm(info) {
        const item = this.newPayType.find(v => v.pay_type === 8)
        const reItem = info.new_pay_type.find(v => v.pay_type === 8)
        if (item && reItem && +reItem.amount > item.amount) {
          this.$Message.error({
            content: `储值卡退回金额，不能超过储值卡支付时的金额(￥${item.amount})`,
            duration: 2.5
          })
          return;
        }
        this.newPayType = info.new_pay_type
        this.refundTime = info.refund_time
        this.doBackDeposit()
      },
      /* 押金退还 */
      doBackDeposit() {
        this.$service
          .post('/Web/FrontMoney/refund', { id: this.backDepositId, new_pay_type: this.newPayType, refund_time: this.refundTime })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.newPayType = []
              this.refundTime = ''
              this.reUserId = ''
              this.returnPayAmount = 0
              this.getDepositList()
              this.depositFinish(5,this.backDepositUserId,this.backDepositId,'depositback');
              this.showBackMoney = false
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      },
      getDepositList() {
        const url = '/Web/FrontMoney/front_money_list'
        let postData = {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          state: this.status,
          purpose: this.purpose,
          marketers_id: this.searchSaleId,
          search: this.searchText,
          page_no: this.currentPage,
          page_size: this.sizer,
          bus_id: this.selectBusId
        }
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              let resData = res.data.data
              this.tableData = resData.list.map(item => {
                return Object.assign({}, item, {
                  status: item.status === '0' ? '未启用' : item.status === '1' ? '已启用' : '已退款',
                  purpose: item.purpose === '0' ? '会员卡' : item.purpose === '1' ? '私教课' : '其它',
                  user: {
                    id: item.user_id,
                    name: item.username
                  },
                  operation: {
                    disabled: item.status === '2' || item.status === '1',
                    id: item.id
                  }
                })
              })
              this.depositData = resData
              this.drawRing(resData)
              this.jumpBusId = this.selectBusId
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      },
      getExportList(isExport) {
        const url = '/Web/FrontMoney/front_money_list'
        let postData = {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          state: this.status,
          purpose: this.purpose,
          marketers_id: this.searchSaleId,
          search: this.searchText,
          page_no: 1,
          page_size: this.depositData.count,
          bus_id: this.selectBusId
        }
        return this.$service
          .post(url, postData, {isExport})
          .then(res => {
            if (res.data.errorcode === 0) {
              let resData = res.data.data
              this.exportData = resData.list.map(item => {
                return Object.assign({}, item, {
                  status: item.status === '0' ? '未启用' : item.status === '1' ? '已启用' : '已退款',
                  purpose: item.purpose === '0' ? '会员卡' : item.purpose === '1' ? '私教课' : '其它',
                  pay_type_name: item.pay_type_name.replace(/,/g, '，'),
                  user: item.username,
                  operation: {
                    disabled: item.status === '2' || item.status === '1',
                    id: item.id
                  }
                })
              })
            } else {
              this.$Message.error(res.data.errormsg)
              return false
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      },
      // 业绩环图
      drawRing(resData) {
        for (const key in resData) {
          if (resData.hasOwnProperty(key)) {
            this.depositOption.series[0].data.forEach(item => {
              if (item.code === key) {
                item.value = parseFloat(resData[key]).toFixed(2)
              }
            })
          }
        }
        this.$nextTick(() => {
          this.depositChart = eCharts.init(document.getElementById('depositRing'))
          this.depositChart.setOption(this.depositOption)
        })
      },
      clickBackDeposit(param) {
        this.backDepositId = param.id;
        this.backDepositUserId = param.user_id;
        this.newPayType = param.new_pay_type.map(v => {
          const item = {
            name: v.pay_type_name,
            pay_type: +v.pay_type,
            amount: v.amount
          };
          +v.card_user_id && (item.card_user_id = v.card_user_id)
          return item
        });
        this.reUserId = param.user_id
        this.returnPayAmount = param.amount
        this.createTime = param.date
        this.showBackMoney = true

      },
      pageSizeChanged(size) {
        this.currentPage = 1
        this.sizer = size
        this.getDepositList()
      },
      getExcelAuth() {
        this.$service.get('/Web/FrontMoney/front_money_list_excel').then(res => {
          if (res.data.errorcode === 40014) {
            this.excelAuth = false;
          } else {
            this.excelAuth = true;
          }
        });
      },
      handleShowPayDetail(item) {
        this.payTypeDetail = Array.isArray(item.new_pay_type) ? item.new_pay_type : []
        this.showPayTypeDetail = true
      },
      async otherCase(val) {
        if (val === '0') {
          if(!this.excelAuth) {
            this.$Message.error('没有导出权限');
            return false
          }
          await this.getExportList(true)
          if (!this.exportData.length) return false

          let columns = this.columns.filter((col, index) => index < 10);
          columns.splice(2,0,{ title: '电话号码', key: 'phone'});
          this.$refs.table.exportCsv({
            filename: '定金列表',
            columns: columns,
            data: this.exportData
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  @border: 1px solid #dcdcdc;

  .total-stat {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px;
    height: 150px;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }

  .back-money {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    padding: 50px 0;
  }

  footer {
    display: flex;
    justify-content: space-between;
  }
</style>
