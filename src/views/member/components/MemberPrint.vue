<template>
<Modal v-model="showAdd" :mask-closable="false" title="合同打印" width="600">
  <div v-if="printOrderList && printOrderList.length>0">
    <RadioGroup v-model="cardorderId" class="model-check-list" @on-change="change">
      <Radio v-for="item in printOrderList" :label="item.cardorder_info_id" :key="item.order_sn">
        <div class="check-right">
          <div class="top-con">
            <span class="item">[{{item.name}}] {{item.card_name}} </span>
            <span class="item">卡号: {{item.card_sn}}</span>
            <span class="item">{{item.create_time}}</span>
          </div>
          <div class="top-con">
            <span class="item">订单号：<span :class="item.status==1?'gray':''">{{item.order_sn}}</span><span v-if="item.status==1" :class="item.status==1?'red':''">（已撤销）</span></span>
            <span class="item">￥{{item.amount}}</span>
            <span class="item">业绩归属: {{item.marketer_name?item.marketer_name:'无'}}</span>
          </div>
        </div>
      </Radio>
    </RadioGroup>
    <Form ref="followForm" class="print-form" :label-width="120">
      <Form-item label="合同模板" v-if="templateList && templateList.length>0 && selectedOrder.order_sign_status!=1">
        <Select class="select w250" v-model="templateId" placeholder="合同模板" transfer placement="top">
          <Option v-for="item in templateList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
      </Form-item>
      <Alert v-show="selectedOrder.order_sign_status==1" type="warning" show-icon>合同已签字确认，将采用签字确认时合同模板打印</Alert>
       <!-- <Form-item label="打印机类型" v-if="templateList && templateList.length>0">
        <Select class="select w250" v-model="printerType" placeholder="打印机类型">
          <Option :value="1">喷墨打印机（网页直接打印）</Option>
          <Option :value="2">针式打印机（导出word打印）</Option>
        </Select>
      </Form-item> -->
    </Form>
  </div>
  <Alert type="warning" show-icon v-else>暂无相关数据</Alert>
  <div slot="footer" class="modal-buttons" v-if="printOrderList && printOrderList.length>0">
    <Button type="success" @click="print">打印</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
  <span slot="footer"  v-else></span>
</Modal>
</template>
<script>

export default {
  name: 'MemberPrint',
  data() {
    return {
      printOrderList: [],
      templateList: [],
      cardorderId: '',
      templateId: '',
      // printerType: localStorage.getItem('printerType') == 2 ? 2 : 1,
      name: '',
      selectedOrder: ''
    }
  },
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
      }
    }
  },
  components: {
  },
  created() {
    // this.getPrintOrderList();
  },
  watch: {
    name(val, oldVal) {
      if(val != oldVal){
        this.getOrderTemplate(val);
      }
    },
    value(val) {
      if (val) {
        this.getPrintOrderList()
      }
    }
  },
  methods: {
    change(id) {
      for (let item of this.printOrderList) {
        if (item.cardorder_info_id == id) {
          this.selectedOrder = item;
          this.name=item.name
          break;
        }
      }

    },
    getPrintOrderList() {
      this.$service.post('/Web/CardOrderInfo/can_print_card_order_list', {
          user_id: this.$route.params.userId
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            let resData = res.data.data;
            this.printOrderList = resData;
            if(resData.length>0) {
              this.selectedOrder = resData[0];
              this.cardorderId = resData[0].cardorder_info_id
              this.name=resData[0].name
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    getOrderTemplate(name) {
      this.$service.post('/Web/CardOrderInfo/get_print_template', {
          type: name
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            let resData = res.data.data;
            this.templateList = resData.list;
            this.templateId = resData.list && resData.list.length>0 ? resData.list[0].id : '';
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    print() {
      let templateId = this.templateId
      if(this.selectedOrder.order_sign_status == 1){
        templateId = ''
      }
      window.open(`/templatePrint?cardorder_info_id=${this.selectedOrder.cardorder_info_id}&template_id=${templateId}&printerType=1`);
    }
  }
}
</script>
<style lang="less">
.print-form {
  margin: 15px auto -25px;
  width: 420px;
}
.gray{
  color: #ccc;
}
</style>
