<template>
  <FormItem v-if="value >= 0 && show" label="会员卡剩余价值">
    {{value}}
    <Tooltip>
      <div slot="content" style="white-space: normal">会员卡剩余价值 = 会员卡价值 * 剩余次数 / 购买次数</div>
      <Icon size="16" type="ios-help-circle" style="padding-left: 5px" color="#ffcf05"></Icon>
    </Tooltip>
  </FormItem>
</template>

<script>
  export default {
    name: 'leftValue',
    props: {
      value: {},
      busId: {
        type: [String, Number],
        default: ''
      },
      cardUserId: {}
    },
    data() {
      return {
        show: false
      };
    },
    created() {
      this.getAuth();
    },
    methods: {
      getAuth() {
        const url = 'Web/MemberCard/check_order_value';
        this.$service
          .post(url, { card_user_id: this.cardUserId, bus_id: this.busId })
          .then(res => {
            if (res.data.errorcode === 0 && res.data.data.is_order_value === 1) {
              this.show = true;
            } else {
              // this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style scoped>
</style>
