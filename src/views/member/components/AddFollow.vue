<template>
<Modal v-model="showAdd" :mask-closable="false" title="添加跟进记录">
  <Form ref="followForm" :model="postData" class="modal-form" :label-width="80">
    <Form-item label="跟进人" prop="marketers_id" :rules="{required: true, message: '请选择跟进人'}">
      <saleSelect v-if="showAdd" :isCoach="true" v-model="postData.marketers_id"></saleSelect>
    </Form-item>
    <Form-item label="跟进方式" prop="follow_up_mode" :rules="{required: true, message: '请选择跟进方式'}">
      <Select v-model="postData.follow_up_mode" placeholder="请选择" clearable>
        <Option v-for="(item, index) in followTypes"
                :value="item"
                :key="index">{{item}}</Option>
      </Select>
    </Form-item>
    <Form-item label="跟进记录" prop="description" :rules="{required: true, message: '请填写跟进记录'}">
      <Input type="textarea" placeholder="请填写" v-model="postData.description" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
    </Form-item>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doAddFollow">保存</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
import saleSelect from 'src/components/membership/salesSelect'
export default {
  name: 'AddFollow',
  data() {
    return {
      followTypes: ['电话', '微信', 'QQ', '面谈', '其他'],
      postData: {
        user_id: this.userId,
        marketers_id: '',
        description: '',
        follow_up_mode: ''
      }
    }
  },
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.$refs.followForm.resetFields()
      }
    }
  },
  components: {
    saleSelect
  },
  created() {},
  methods: {
    doAddFollow() {
      this.$refs.followForm.validate(val => {
        if (!val) return false
        this.$service
          .post('/Web/Member/addFollowUpLog', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showAdd = false
              this.$store.commit('SET_ADD_FollOW_SUCCESS', true)
              this.$emit('on-success')
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      })
    }
  }
}
</script>

<style scoped>

</style>
