<template>
    <Card class="locker-num-wrapper">
            <div class="unit" @click.prevent="showAddLockerModal">
                <Icon type="md-add" size=40 /><span class="locker-menu-item">添加柜子</span>
            </div>
            <Modal v-model="showAddLocker" title="添加柜子" @on-visible-change="getFocus" :mask-closable="false">
                <div>
                    <Form ref="addLockerForm" :model="addLockerForm" :rules="ruleAddLocker" :label-width="80">
                        <FormItem label="储物柜号" prop="lockerNo">
                            <Input ref="setLockerNum" type="text" v-model="addLockerForm.lockerNo" />
                        </FormItem>
                        <FormItem label="区域">
                            <Select v-model="newAreaId">
                                <Option v-for="item in newAreaList" :value="item.id" :key="item.id">{{item.name}}</Option>
                            </Select>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer"
                        class="modal-buttons">
                    <Button type="success"
                        @click="addLocker">确定</Button>
                    <Button 
                        @click="showAddLocker = false">取消</Button>
                </div>
            </Modal>
            <Modal v-model="smartAlert">
                <h3>智能储物柜请到硬件设置中进行添加</h3>
            </Modal>
    </Card>
</template>

<script>
    import EventBus from "utils/eventBus.js";

    export default {
        name: "LockerUnitAdd",
        props: {
            areaList: {
                default: []
            },
            areaId: {
                default: ''
            },
            smart: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                showAddLocker: false,
                addLockerForm: {
                    lockerNo: ''
                },
                ruleAddLocker: {
                    lockerNo: [{ required: true, message: "请输入储物柜号！" }]
                },
                smartAlert: false,
                newAreaId: this.areaId,
            }
        },
        computed: {
            newAreaList: {
                get() {
                    return this.areaList.slice(1).filter(item => {
                        return item.typeName && item.typeName !== "智能柜";
                    })
                },
                set(v) {
                    return v
                }
            }
        },
        methods: {
            getFocus(val) {
                let _this = this;
                setTimeout(() => {
                    if(val){
                        _this.$refs.setLockerNum.focus()
                    }
                }, 100)
            },
            showAddLockerModal() {
                if(this.smart) {
                    this.smartAlert = true;
                    return;
                } else {
                    this.addLockerForm.lockerNo = "";
                    this.showAddLocker = true
                }
            },
            addLocker() {
                this.$refs.addLockerForm.validate(valid => {
                    if(valid) {
                        let postData = { lockerNo: this.addLockerForm.lockerNo, areaId: this.newAreaId };
                        this.$service.post('/Web/LockerRent/postAddLocker', postData).then(res => {
                            if(res.status === 200) {
                                if(res.data.errorcode == 0) {
                                    this.$Message.success(res.data.errormsg)
                                    EventBus.$emit("success")
                                    this.showAddLocker = false;
                                } else {
                                    this.$Message.error(res.data.errormsg)
                                }
                            } else {
                                console.error("服务器扑街！")
                            }
                        }).catch(err => {
                            console.log(err);
                        })
                    } else {
                        return;
                    }
                })
            }
        }
    }
</script>

<style scoped>
        .locker-num-wrapper .ivu-card-body {
            padding: 0 !important;
        }
        .locker-num-wrapper {
            height: 100%;
            cursor: pointer;
        }
        .locker-num-wrapper {
            width: 12.5%;
            height: 100px !important;
            float: left;
            font-size: 12px;
        }
        @media (max-width: 1400px) {
            .locker-num-wrapper {
                width: 20%;
            } 
        }
        .locker-menu {
            position: absolute;
            height: 100%;
            width: 100%;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .locker-menu-item {
            display: block;
            text-align: center;
        }
        .unit {
            flex: 1;
            text-align: center;
        }
        .locker-num {
            margin: 0;
            padding: 0;
            display: block;
            height: 60px;
            line-height: 60px;
            text-align: center;
        }
</style>