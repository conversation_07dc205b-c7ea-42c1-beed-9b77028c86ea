<template>
    <div class="table-wrap">
      <header>
        <Input class="user-search"
               placeholder="储物柜编号"
               v-model="postdata.lockerId"
               @on-enter="handleSearch"
               clearable />
        <Date-picker placeholder="选择查询时间段"
                     style="width:280px;"
                     @on-change="dateChanged"
                     :value="dateRange"
                     type="daterange"
                     :editable="false"
                     clearable
                     @on-clear="clearDate"
                     format="yyyy年MM月dd日"></Date-picker>
        <Select v-model="postdata.order"
                style="width:100px">
          <Option value="1">创建时间倒序</Option>
          <Option value="2">创建时间正序</Option>
          <Option value="3">到期时间倒序</Option>
          <Option value="4">到期时间正序</Option>
        </Select>
        <Button type="success"
                @click="handleSearch">搜索</Button>
      </header>
      <main>
        <Table ref="table"
               :columns="columns"
               :data="tableData"
               stripe
               disabled-hover></Table>
      </main>
      <footer>
          <Dropdown @on-click="otherCase"
                    placement="top" v-if="notExport">
            <Button>
              其他操作
              <Icon type="md-arrow-dropdown"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="0">导出excel</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        <Page :total="totalCount"
              :current.sync="currentPage"
              show-total
              show-sizer
              placement="top"
              class="page"
              @on-change="handlePageChange"
              @on-page-size-change="pageSizeChanged"></Page>
      </footer>
    </div>
  </template>

  <script>
    import NewLocker from './AddLocker.vue'
    import { getNewHost } from 'utils/config'
    import { formatDate } from 'utils'
    import EventBus from "utils/eventBus.js";
    import { isChinese } from '@/utils';

    export default {
      data() {
        return {
          postdata: {
            lockerId: '',
            startTime: '',
            endTime: '',
            pageNo: 1,
            pageSize: 10,
            order: '1'
          },
          searchUserList: [],
          memberList: [],
          isRemoteLoading: false,
          showUsingMember: false,
          searchUserId: "",
          curUsingLockerrenId: '',
          dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(Date.now() + (7 - (new Date()).getDay()) * 24 * 60 * 60 * 1000), 'yyyy-MM-dd') ],
          tableData: [],
          exportData: null,
          sizer: 10,
          currentPage: 1,
          totalCount: 0,
          // userUrl: `${getNewHost()}/#/member/detail/`,
          userUrl: '/member/detail/',
          columns: [
            {
              title: '时间',
              key: 'create_time',
              width: 200,
              render: (h, param) => {
                  return (
                      <span>
                        {
                            formatDate(new Date(param.row.create_time * 1000), "yyyy-M-d H:mm")
                        }
                      </span>
                  )
              }
            },
            {
              title: '会员',
              key: 'user_name',
              render: (h, param) => {
                let url = this.userUrl + param.row.user_id
                return (
                  // <a target="_blank" href={url}>
                  //   {param.row.user_name}
                  // </a>
                  <router-link target="_blank" to={ url }>{ param.row.user_name }</router-link>
                )
              }
            },
            {
              title: '柜号',
              key: 'locker_id'
            },
            {
              title: '动作',
              key: 'action',
              render: (h, param) => {
                  let action = param.row.action_type === '1' ? '租柜' : param.row.action_type === '2' ? '续租' : '退柜';
                  return (
                      <span>
                       {action}
                      </span>
                  )
              }
            },
            {
              title: '金额',
              key: 'amount',
              render: (h, param) => {
                  return (
                      <span>
                       {param.row.action_type == 3 ? '-' : param.row.amount}
                      </span>
                  )
              }
            },
            {
              title: '押金',
              key: 'cash_pledge_amount',
              render: (h, param) => {
                  return (
                      <span>
                       {param.row.action_type == 3 ? '-' : param.row.cash_pledge_amount}
                      </span>
                  )
              }
            },
            {
              title: '到期时间',
              key: 'end_time',
              width: 200,
              render: (h, param) => {
                  return (
                      <span>
                        {
                          param.row.action_type == 3 ? '-' : formatDate(new Date(param.row.end_time * 1000), "yyyy-M-d HH:mm")
                        }
                      </span>
                  )
              }
            },
            {
              title: '操作账号',
              key: 'admin_user_name'
            }
          ],
          backlockerId: '',
          backuserId: '',
          notExport: true,
        }
      },
      created() {
        this.getLockerList();
        //刷新数据
        EventBus.$on("success", () => {
            this.getLockerList()
        })
      },
      methods: {
        handleAddOldMemberRemote(query) {
          if (query.length === 0) {
            this.searchUserList = [];
            return false;
          }

          // if numbers or letters must be more than 3 in length you can request
          if (!isChinese(query)) {
            return;
          }

          this.isRemoteLoading = true;
          this.$service
            .post("/Web/FrontMoney/search_all_user", {
              search: query.trim()
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                if (Array.isArray(res.data.data.list)) {
                  this.searchUserList = res.data.data.list;
                  this.isRemoteLoading = false;
                }
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
        handleDeleteMember(id) {
          return this.$service
            .post("/Web/LockerRent/multi_delete_member", {
              user_id: id,
              lockerrent_id: this.curUsingLockerrenId
            })
            .then(res => {
              if(res.data.errorcode == 0){
                this.getMemberList();
                this.getLockerList();
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
        handleAddMemberSubmit() {
          return this.$service
            .post("/Web/LockerRent/multi_add_member", {
              user_id: this.searchUserId,
              lockerrent_id : this.curUsingLockerrenId
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.getMemberList();
                this.getLockerList();
                this.searchUserId = "";
                this.isAddOldMember = false;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
        getMemberList() {
          return this.$service
            .post("/Web/LockerRent/multimember_rent_list", {
              lockerrent_id : this.curUsingLockerrenId
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                let users = res.data.data
                if (Array.isArray(users)) {
                  this.memberList = users;
                }
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
        },
        dateChanged(val) {
          if (!val[0]) {
            return false
          }
          let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`
          let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`
          this.dateRange = [beginDate, endDate]
        },
        clearDate() {
          this.dateRange = []
        },
        handleSearch() {
          this.currentPage = 1
          this.getLockerList()
        },
        getLockerList() {
          this.postdata.startTime = this.dateRange[0] ? formatDate(this.dateRange[0], 'yyyy-MM-dd') : '';
          this.postdata.endTime = this.dateRange[1] ? formatDate(this.dateRange[1], 'yyyy-MM-dd') : '';
          this.postdata.pageNo = this.currentPage;
          this.postdata.pageSize = this.sizer;
          this.$service.get(`/Web/LockerRent/getLockerRentLogList`, {
              params: { ...this.postdata }
          }).then(res => {
            if (res.status === 200) {
              if (res.data.errorcode == 0) {
                let data = res.data
                this.totalCount = parseInt(data.count)
                this.tableData = data.list
              } else {
                this.$Message.error(res.data.errormsg)
              }
            } else {
              console.log('服务器扑街！')
            }
          })
        },
        getExportList(isExport) {
          this.postdata.startTime = this.dateRange[0] ? formatDate(this.dateRange[0], 'yyyy-MM-dd') : '';
          this.postdata.endTime = this.dateRange[1] ? formatDate(this.dateRange[1], 'yyyy-MM-dd') : '';
          this.postdata.pageNo = this.currentPage;
          this.postdata.pageSize = this.totalCount;
          return this.$service.get(`/Web/LockerRent/getLockerRentLogList`, {
            params: { ...this.postdata }
          }, { isExport }).then(res => {
            if (res.status === 200) {
              if (res.data.errorcode == 0) {
                let data = res.data
                this.exportData = data.list.map(item => {
                  return Object.assign({}, item, {
                    amount: item.action_type == 3 ? '-' : item.amount,
                    cash_pledge_amount: item.action_type == 3 ? '-' : item.cash_pledge_amount,
                    action: item.action_type === '1' ? '租柜' : item.action_type === '2' ? '续租' : '退柜',
                    create_time: formatDate(new Date(item.create_time * 1000), "yyyy-M-d H:m"),
                    end_time: item.action_type == 3 ? '-' : formatDate(new Date(item.end_time * 1000), "yyyy-M-d H:m")
                  })
                })
              } else {
                this.$Message.error(res.data.errormsg)
              }
              this.postdata.pageSize = this.sizer
            } else {
              console.log('服务器扑街！')
              this.postdata.pageSize = this.sizer
            }
          })
        },
        handlePageChange(pageno) {
          this.currentPage = pageno
          this.getLockerList()
        },
        pageSizeChanged(size) {
          this.currentPage = 1
          this.sizer = size
          this.getLockerList()
        },
        async otherCase(val) {
          if (val === '0') {
            if (this.totalCount == 0) {
              this.$Message.error("没有可导出的数据！");
              return false;
            }
            await this.getExportList(true)
            if (!this.exportData) return false

            this.notExport = false
            this.$refs.table.exportCsv({
              filename: '租柜记录',
              columns: this.columns.filter((col, index) => index < 8),
              data: this.exportData
            })
            setTimeout(() => {
              this.notExport = true
            }, 100)
          }
        },
      }
    }
  </script>
  <style lang="less" scoped>
    .ivu-date-picker {
      width: 240px;
    }

    .member-controller {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      margin-top: 22px;
    }

    .member-old-form {
      display: flex;
      flex-direction: row;
      margin-top: 22px;
    }

    .member-card {
      display: flex;
      align-items: center;
      width: 358px;
      margin-bottom: 15px;
    }

    .member-list{
      margin-bottom: 15px;
      &:last-child{
        margin-bottom: 0;
      }
      .member-delete{
        margin-left: 15px;
        color: #666;
        cursor: pointer;
      }
    }
    header {
      .user-search {
        width: 160px;
      }
    }

    footer {
      display: flex;
      justify-content: space-between;
    }

    .ivu-date-picker {
      width: 100%;
    }
  </style>
