<template>
  <Modal :mask-closable="false"
         v-model="showDeposit"
         title="收定金"
         :width="750">
    <Form ref="depositForm"
          :model="depositForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item label="会员"
                 prop="user_id"
                 v-if="!userId"
                 :rules="{required: true, message: '请选择会员'}">
        <userSearch url="/Web/FrontMoney/search_all_user" v-model="depositForm.user_id"></userSearch>
      </Form-item>
      <Form-item label="定金用途" prop="purpose">
        <RadioGroup v-model="depositForm.purpose">
          <Radio :label="0">
              <span>会员卡定金</span>
          </Radio>
          <Radio :label="1">
              <span>私教课定金</span>
          </Radio>
           <Radio :label="2">
              <span>其他定金</span>
          </Radio>
        </RadioGroup>
      </Form-item>
      <Form-item label="收取时间" prop="date" :rules="{ required: true, message: '请选择日期'}">
        <Date-picker
          type="date"
          format="yyyy-MM-dd"
          :value="depositForm.date"
          :options="disableDayAfter"
          @on-change="handleCollectDateChange"
          :editable="false"
          placeholder="定金收取时间"></Date-picker>
      </Form-item>
      <Form-item label="定金金额"
                 prop="amount"
                 :rules="{required: true, type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数'}">
        <Input v-model="depositForm.amount" />
      </Form-item>
      <Form-item
        v-if="depositForm.amount>0 && (userId || depositForm.user_id)"
        label="支付方式"
        prop="new_pay_type"
        :required="depositForm.amount != 0 && depositForm.amount != ''"
        :rules="{required: depositForm.amount != 0  && depositForm.amount != '' ? true : false, type: 'array', min: 1, message: '金额不为0时,支付方式为必选项'}">
        <pay-type-list
          v-model="depositForm.new_pay_type"
          :amount="parseFloat(depositForm.amount)"
          :showCardPay="!!(userId || depositForm.user_id)"
          :userId="userId || depositForm.user_id"
          :sqbOption="{ describe: '收定金', serviceType: 4, isEqual: false }" />
        </Form-item>
      <Form-item label="收款人" v-if="showDeposit" prop="marketers_id">
        <saleSelect v-model="depositForm.marketers_id" isCoach></saleSelect>
      </Form-item>
      <Form-item label="备注"
                 prop="remark">
        <textarea rows="3"
                  maxlength="90"
                  v-model="depositForm.remark"></textarea>
      </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="newDeposit">确定</Button>
      <Button
              @click="showDeposit = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  import userSearch from 'src/components/user/userSearch'
  import saleSelect from 'src/components/membership/salesSelect'
  import PayTypeList from 'components/form/PayTypeList'

  import {
    formatDate
  } from "utils"
  export default {
    name: 'AddDeposit',
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      }
    },
    components: {
      userSearch,
      PayTypeList,
      saleSelect
    },
    data() {
      return {
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        depositForm: {
          purpose: 0,
          pay_order_ids: [],
          date: formatDate(new Date(), "yyyy-MM-dd"),
          amount: '',
          user_id: '',
          marketers_id: '',
          new_pay_type: [],
          remark: ''
        }
      }
    },
    computed: {
      showDeposit: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showDeposit(val) {
        if (!val) {
          this.$refs.depositForm.resetFields()
          this.depositForm.new_pay_type = []
        }
      },
      // 'depositForm.date'(val) {
      //   if(typeof val=== 'object') {
      //     this.depositForm.date = formatDate(val, "yyyy-MM-dd")
      //   }
      // }
    },
    created() {
    },
    methods: {
      onDragonflyConfirm(info) {
        this.depositForm.pay_order_ids = info.pay_order_ids
      },
      handleCollectDateChange(val) {
        this.depositForm.date = val
      },
      newDeposit() {
        if (this.userId) {
          this.depositForm.user_id = this.userId
        }
        const params = {
          ...this.depositForm
        }
        if (+params.amount === 0) {
          params.new_pay_type = []
        }
        this.$refs.depositForm.validate(val => {
          if (!val) return false
          this.$service
            .post('/Web/FrontMoney/make_collections', params)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showDeposit = false
                this.$emit('on-success')
                this.$emit('on-printinfo',1,this.depositForm.user_id,res.data.front_id,'deposit')
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
        })
      }
    }
  }
</script>

<style scoped>

</style>
