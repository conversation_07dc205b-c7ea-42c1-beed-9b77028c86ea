<template>
<Modal v-model="showAdd" :mask-closable="false" title="押金" width="750">
  <Form ref="postForm"
          :model="postData"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
  <Form-item prop="id" :rules="{required: true,  message: '请选择' }" :label-width="0">
  <RadioGroup v-model="postData.id" class="model-check-list" @on-change="changeInfo">
      <Radio v-for="item in data" :label="item.id" :key="item.id" :disabled="(deCash.fromtype=='fromlockerreturn')&&(item.id!=postData.id)">
        <div class="check-right">
        <div class="top-con">
          <span class="item gray">{{item.create_time}}</span>
          <span class="item"> <span class="gray">收押金</span> <span class="red">￥{{item.amount}}</span></span>
          <span class="item"> <span class="gray">用途</span> <span>{{item.use_type == 1 ? '会员卡押金' : item.use_type == 2 ? '储物柜押金' : '其他押金'}}</span></span>
        </div>
        <div class="bottom-con" v-if="item.remark">
          备注：{{item.remark}}
        </div>
      </div>
      </Radio>
  </RadioGroup>
  </Form-item>
  <Form-item
    v-if="showAdd && !Number(curInfo.locker_rent_id)"
    label="退款方式"
    prop="new_pay_type"
    :rules="{required: parseFloat(curInfo.amount) != 0, type: 'array', min: 1, message: '退款方式为必选项'}">
      <pay-type-list
        v-model="postData.new_pay_type"
        :amount="parseFloat(curInfo.amount)"
        :userId="userId"
        :showSQB="false"
        :showCardPay="false"
        isRefund />
   </Form-item>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doBack">退款</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
import PayTypeList from 'components/form/PayTypeList'

export default {
  name: 'MemberCash',
  data() {
    return {
      curInfo: {},
      postData: {
        id: '',
        new_pay_type: [],
      }
    }
  },
  components: {
    PayTypeList
  },
  props: {
    data: {
      type: [Object,Array],
      required: true
    },
    userId: {
      type: [String, Number],
      required: true
    },
    deCash: {
      type: Object
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        if(this.value && this.deCash) {
          this.data.forEach(item => {
            if(item.locker_rent_id == this.deCash.lockerId) {
              this.postData.id = item.id;
              this.curInfo = item
            }
          })
        }
        return this.value
      },
      set(val) {
        if(!val) {
          this.$emit('on-cleardecash')
        }
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.postData.id = ''
        this.postData.new_pay_type = []
      }
    }
  },
  created() {},
  methods: {
    changeInfo(id) {
      for (const iterator of this.data) {
        if(iterator.id === this.postData.id) {
          this.curInfo = iterator
          this.postData.new_pay_type =
            Array.isArray(iterator.new_pay_type)
              ? iterator.new_pay_type.map(v => {
                  const item = {
                    name: v.pay_type_name,
                    pay_type: +v.pay_type,
                    amount: v.amount
                  };
                  +v.card_user_id && (item.card_user_id = v.card_user_id)
                  return item
                })
              : []
        }
      }
    },
    doBack() {
      let temptype = '';
      let lockertemp = '';
      this.data.forEach((item) => {
        if(item.id == this.postData.id) {
          temptype = item.locker_rent_id;
        }
      })
      if(Number(temptype)) {
        //关闭窗口时有清空decash拦截器，先存入temp
        let temp = this.deCash.fromtype;
        this.showAdd = false
        this.$emit('on-preprint',temptype,temp || 'frompurecash')
      } else {
        this.$refs.postForm.validate((valid) => {
          if (valid) {
            const new_pay_type = this.postData.new_pay_type.filter(v => {
              // 不能退款收钱吧，押金时不能储值卡退款
              return +v.amount && ![8, 20, 21].includes(v.pay_type)
            })

            this.$service
              .post('/Web/CashPledge/refundCashPledge', { cash_pledge_ids: this.postData.id, new_pay_type})
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.showAdd = false
                  this.$emit('on-printinfo',6,this.userId,this.postData.id,'cashrefund')
                  this.$emit('on-success')
                }else{
                  this.$Message.error(res.data.errormsg)
                }
              })
              .catch(err => {
                this.$Message.error(err)
              })
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.gray{
  color: #999;
}
</style>
