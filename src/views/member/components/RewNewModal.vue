<template>
  <Modal v-model="isShow" title="续卡" :mask-closable="false" @on-cancel="isShow=false">
  <div class="name-tips">
   请选择要续的卡种
  </div>
  <Form ref="rewForm" class="modal-form add-brand" style="padding: 0 10px" :label-width="80">
    <FormItem label="会员卡" v-if="list && list.length" >
      {{cardUserId}}
       <Select v-model="cardUserId" filterable>
          <Option v-for="item in list" :key="item.card_user_id" :value="item.card_user_id">{{item.cardname}} {{item.card_sn}}</Option>
        </Select>
        <div>
          未激活、请假中、待审状态请先激活或者过审后再操作
        </div>
    </FormItem>
    <div v-else>暂无相应卡种</div>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="rewNewCard">确定</Button>
    <Button @click="isShow=false">取消</Button>
  </div>
</Modal>
</template>

<script>
  export default {
    name: 'RewNewModal',
    data() {
      return {
        cardUserId: ''
      }
    },
    props: {
      list: Array,
      value: {
        type: Boolean
      },
    },
    watch: {
      list(val) {
        if (val.length) {
          this.cardUserId = val[0].card_user_id
        } else {
          this.cardUserId = ''
        }
        console.log(val, this.cardUserId);
      },
      isShow(val) {
        if (!val) {
           console.log(this.cardUserId);
        }
      }
    },
    computed: {
      isShow: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    created() {
    },
    methods: {
      rewNewCard(){
        if (!this.cardUserId) {
          return false
        }
        this.isShow = false
        this.$router.push(`/member/renewCard/${this.$route.params.userId}/${this.cardUserId}?ttp=${new Date().getTime()}`);
      }
    }
  }
</script>
<style lang="less" scoped>
.name-tips{
  text-align: center;
  margin-bottom: 15px;
  font-size: 14px;
  span{
    color: #d9534f;
  }
}
</style>
