<template>
  <Modal v-model="isShow" title="上课教练指派" :mask-closable="false" @on-cancel="isShow=false">
    <div v-if="(from == 'list' && isChangeAll === '')">
      <RadioGroup v-model="isChangeAll" vertical class="radio-lines">
          <Radio :label="1">
              <span>上课教练全部替换</span>
          </Radio>
          <Radio :label="0">
              <span>仅替换单个上课教练</span>
          </Radio>
        </RadioGroup>

    </div>
    <div v-if="(from == 'list' && isChangeAll === 1) || from != 'list'">
       <div class="name-tips">
          将
          <span>{{nameTips}}</span>的课程指派给
        </div>
        <Form ref="assignForm" class="modal-form add-brand" style="padding: 0 10px" :label-width="140">
          <FormItem v-for="item in classList" :label="item.name" :key="item.card_id">
            <saleSelect v-if="hasCoach && isShow" :belong-bus-id="belongBusId" v-model="item.coach_id" placeholder="请选择" :isPtCoach="item.card_type_id === '4'" :isSwimCoach="item.card_type_id === '5'" :isMembership="false"  multiple>
            </saleSelect>
          </FormItem>
        </Form>

    </div>
    <div v-if="from == 'list' && isChangeAll === 0">
      <Form ref="changeForm" class="modal-form add-brand" style="padding: 0 10px" :label-width="80">
      <FormItem label="上课教练">
        <Select v-model="oneData.old_coach_id" v-if="salesList"  placeholder="请选择" clearable transfer filterable class="sales-select" @on-change="oldCoachChange">
          <Option v-for="item in salesList" :key="item.coach_id" :label="item.coach_name" :value="item.coach_id">
          </Option>
        </Select>
      </FormItem>
      <FormItem label="替换为">
        <saleSelect v-if="hasCoach" :belong-bus-id="belongBusId" v-model="oneData.new_coach_id" placeholder="请选择" :isPtCoach="oldCoachInfo.is_swim !== 1" :isSwimCoach="oldCoachInfo.is_swim === 1" :isMembership="false" @on-change="newCoachChange" :labelInValue="true">
        </saleSelect>
      </FormItem>
      </Form>
    </div>
    <div slot="footer" class="modal-buttons"  v-if="(from == 'list' && isChangeAll === 1) || from != 'list'">
      <Button type="success" @click="addMembership">确定</Button>
      <Button @click="isShow=false">取消</Button>
    </div>
    <div slot="footer" class="modal-buttons"  v-if="from == 'list' && isChangeAll === 0">
      <Button type="success" @click="changeOne">确定</Button>
      <Button @click="isShow=false">取消</Button>
    </div>
    <div slot="footer" v-if="(from == 'list' && isChangeAll === '')"></div>
  </Modal>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  import saleSelect from 'src/components/membership/salesSelect';
  export default {
    name: 'assignClassCoach',
    components: {
      saleSelect
    },
    data() {
      return {
        salesList: [],
        oldCoachInfo: {
          is_swim: 0
        },
        classList: [],
        isChangeAll: '',
        membershipData: {
          coach_id: '',
          user_ids: ''
        },
        oneData: {
          userids: '',
          old_coach_id: '',
          new_coach_name: '',
          belong_bus_id: '',
          new_coach_id: ''
        }
      };
    },
    props: {
      selectedMembers: Array,
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      },
      from: {
        type: String
      },
      belongBusId: {
        // 会员的归属场馆
        type: [String, Number],
        default: ''
      }
    },
    watch: {
      isShow(val) {
        if (!val) {
          this.$refs.assignForm && this.$refs.assignForm.resetFields();
          this.isChangeAll = ''
        } else {
          this.from != 'list' && this.getClassList();
        }
      },
      isChangeAll(val) {
        if(val === 1) {
          this.getClassList();
        } else if(val === 0){
          this.getUsersAllClasscoach();
        }
      }
    },
    computed: {
      ...mapState(['ptCoachList', 'swimCoachList', 'globalBelongBusId']),
      isShow: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      nameTips() {
        let tips = '';
        let length = this.selectedMembers.length;
        if (length > 3) {
          tips =
            this.selectedMembers[0].username +
            '、' +
            this.selectedMembers[1].username +
            '、' +
            this.selectedMembers[2].username +
            '...等' +
            length +
            '位用户';
        } else {
          this.selectedMembers.forEach((user, index) => {
            if (index == length - 1) {
              tips = tips + this.selectedMembers[index].username;
            } else {
              tips = tips + this.selectedMembers[index].username + '、';
            }
          });
        }
        return tips;
      },
      userids() {
        return this.selectedMembers.map(item => item.user_id).join(',');
      },
      card_coach_arr() {
        if (this.from == 'list' && this.isChangeAll != 1) {
          return [];
        }
        return this.classList && this.classList.map(item => {
          return { card_id: item.card_id, coach_id: Array.isArray(item.coach_id) ? item.coach_id.join(',') : item.coach_id  };
        });
      },
      hasCoach() {
        const hasPt = Array.isArray(this.ptCoachList) && this.ptCoachList.length !== 0;
        const hasSwim = Array.isArray(this.swimCoachList) && this.swimCoachList.length !== 0;
        return hasPt || hasSwim;
      }
    },
    created() {
      //saleList组件外部请求  防止多下拉时多次请求接口
      this.getPtCoachList()
    },
    methods: {
      ...mapActions(['getPtCoachList']),
      shouldUpdateList(list) {
        if (this.belongBusId === this.globalBelongBusId &&  Array.isArray(list) && list.length) {
          return false;
        } else {
          return true;
        }
      },
      oldCoachChange(id) {
        let arr = this.salesList.filter(item => item.coach_id == id)
        arr && (this.oldCoachInfo = arr[0])
      },
      newCoachChange (info) {
        this.oneData.new_coach_id = info.value
        this.oneData.new_coach_name = info.label
      },
       getUsersAllClasscoach() {
        const { userids } = this;
        this.$service
          .post('/Web/Member/get_users_all_classcoach', { userids, belong_bus_id: this.belongBusId })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.salesList = data;
              if (!data.length) {
                this.$Message.error('无可替换的教练');
                setTimeout(() => {
                  this.isShow = false;
                }, 1000);
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getClassList() {
        const url = '/Web/Member/get_private_card_list';
        const { userids, card_coach_arr } = this;
        this.$service
          .post(url, { userids, belong_bus_id: this.belongBusId })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              let list = data.list
              Array.isArray(list) && list.forEach(item=>{
                item.coach_id = []
                item.coach_id_str = ''
              })
              this.classList = list;
              if (!list.length) {
                this.$Message.error('无可指派的课程');
                setTimeout(() => {
                  this.isShow = false;
                }, 1000);
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      addMembership() {
        const { userids, card_coach_arr } = this;
        this.$service
          .post('/Web/Member/add_private_card_coach', { userids, card_coach_arr, belong_bus_id: this.belongBusId })
          .then(response => {
            if (response.data.errorcode == 0) {
              this.$Message.success('指派成功!');
              this.isShow = false;
              this.$emit('on-success');
            } else {
              this.$Message.error(response.data.errormsg);
            }
          })
          .catch(function(err) {
            console.log(err);
          });
      },
      changeOne() {
        this.oneData.userids = this.userids
        this.oneData.belong_bus_id = this.belongBusId
        this.$service
          .post('/Web/Member/change_single_class_coach', this.oneData)
          .then(response => {
            if (response.data.errorcode == 0) {
              this.$Message.success('指派成功!');
              this.isShow = false;
              this.$emit('on-success');
            } else {
              this.$Message.error(response.data.errormsg);
            }
          })
          .catch(function(err) {
            console.log(err);
          });
      }
    }
  };
</script>
<style lang="less" scoped>
  .radio-lines {
    .ivu-radio-inner {
      border-radius: 0;
    }
    .ivu-radio-inner:after {
      border-radius: 0;
    }
  }
  .name-tips {
    text-align: center;
    margin-bottom: 35px;
    font-size: 14px;
    span {
      color: #d9534f;
    }
  }
</style>
