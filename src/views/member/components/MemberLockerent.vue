<template>
<Modal v-model="showAdd" :mask-closable="false" title="租柜">
  <RadioGroup v-model="checkedId" class="model-check-list">
      <Radio v-for="item in data" :label="item.lockerrent_id" :key="item.lockerrent_id">
        <div class="check-right">
        <div class="top-con">
          <span class="item">{{item.create_date}}</span>
          <span class="item"><span class="red">{{item.locker_id}}</span> 号柜 </span>
          <span class="item">有效期 <span class="red">{{item.end_date}}</span></span>
        </div>
        <div class="bottom-con" v-if="item.remark">
          备注：{{item.remark}}
        </div> 
      </div>
      </Radio>
  </RadioGroup>

  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="clickBackLocker">退柜</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
export default {
  name: 'MemberLockerent',
  data() {
    return {
      checkedId: ''
    }
  },
  props: {
    data: {
      type: [Object,Array],
      required: true
    },
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    },
    lockerId: {
      type: [String, Number]
    }
  },
  computed: {
    showAdd: {
      get() {
        if(this.value && this.lockerId) {
          this.checkedId = this.lockerId;
        }
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
      }
    }
  },
  components: {
  },
  created() {},
  methods: {
    clickBackLocker() {
      if(this.checkedId == ''){
        this.$Message.error('请选择柜号')
        return false;
      }
      let cashtemp = '';
      this.data.forEach((item) => {
        if(item.lockerrent_id == this.checkedId) {
          cashtemp = item.cash_pledge_id;
        }
      })
      if(Number(cashtemp)) {
        this.$emit('on-preprint',this.checkedId,'fromlockerreturn')
        this.showAdd = false
      } else {
        this.$Modal.confirm({
          title: '',
          content: '确定要退柜吗？',
          onOk: this.doBackLocker
        })
      }
    },
    doBackLocker() {
      this.$service
        .post('/Web/LockerRent/deleted_lockerrent', { lockerrent_id: this.checkedId, user_id: this.userId })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.showAdd = false
            this.$Message.success(res.data.errormsg)
            this.$emit('on-success')
          }else{
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    }
  }
}
</script>
