<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>{{membershipId==0?'添加':'编辑'}}</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
        <Form-item label="名称" prop="username">
          <Input v-model="formValidate.username" placeholder="请输入会员名称"></Input>
        </Form-item>
        <Form-item label="性别" prop="sex">
          <RadioGroup v-model="formValidate.sex">
            <Radio label="1">
              <Icon type="man"></Icon>
              <span>男</span>
            </Radio>
            <Radio label="2">
              <Icon type="woman"></Icon>
              <span>女</span>
            </Radio>
          </RadioGroup>
        </Form-item>
        <Form-item label="电话" prop="phone">
          <Input v-model="formValidate.phone" placeholder="请输入会员电话"></Input>
        </Form-item>
        <Form-item label="证件号" prop="id_code">
          <Input v-model="formValidate.id_code" placeholder="请输入会员证件号码"></Input>
        </Form-item>
        <Form-item label="出生日期" prop="birthday">
          <DatePicker v-model="formValidate.birthday" type="date" placeholder="请选择会员出生日期"></DatePicker>
        </Form-item>
        <Form-item label="获客来源" prop="source_id">
          <Select v-model="formValidate.source_id" filterable clearable>
            <Option v-for="item in formValidate.source" :value="item.source_id" :key="item.source_id">{{ item.source_name }}</Option>
          </Select>
        </Form-item>
        <Form-item label="跟进会籍" prop="marketers_id">
          <div class="field-sale">
            <Select v-model="formValidate.marketers_id" :disabled="membershipId != 0" filterable clearable>
              <Option v-for="item in formValidate.sale" :value="item.marketers_id" :key="item.sale_id">{{ item.sale_name }}</Option>
            </Select>
            <Alert v-if="membershipId!=0" type="warning" class="field-tip" show-icon>会籍变更请使用指派会籍操作</Alert>
          </div>
        </Form-item>
        <FormItem label="标签" prop="tags">
          <CheckboxGroup v-model="formValidate.checkedTags">
            <Checkbox v-for="item in formValidate.tags" :key="item.id" :label="item.id">{{item.name}}</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="备注" prop="remark">
          <Input v-model="formValidate.remark" type="textarea" :rows="4" placeholder="请输入..."></Input>
        </FormItem>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit('formValidate')">提交</Button>
            <Button @click="handleReset('formValidate')">重置</Button>
          </div>
        </Form-item>
      </Form>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        membershipId: null,
        formValidate: {
          sale: [],
          source: [],
          username: "",
          sex: "1",
          phone: "",
          id_code: "",
          birthday: "",
          source_id: "",
          marketers_id: "",
          tags: [
            { id: 1, name: "增肌", active: false },
            { id: 2, name: "脱脂", active: false }
          ],
          checkedTags: [],
          remark: ""
        },
        ruleValidate: {
          username: [{ required: true, message: "会员名称不能为空", trigger: "blur" }],
          phone: [
            {
              required: true,
              pattern: /^1\d{10}$/,
              message: "请输入正确的手机号码",
              trigger: "blur"
            }
          ]
        }
      };
    },
    methods: {
      handleReset(name) {
        if (this.membershipId == 0) {
          this.$refs[name].resetFields();
        } else {
          this.getUserInfo();
        }
      },
      handleSubmit(name) {
        this.$refs[name].validate(valid => {
          this.formValidate.bron = this.getDateString(this.formValidate.birthday);
          console.log(this.formValidate);
        });
      },
      getDateString(date) {
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? "0" + month : month;
        let day = date.getDate();
        day = day < 10 ? "0" + day : day;
        return `${year}-${month}-${day}`;
      },
      getUserInfo() {
        return this.$service
          .post("/Web/Member/getUser", {
            user_id: this.membershipId,
            source_type: 1
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.formValidate = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getSaleList() {
        return this.$service.get("/Web/Member/get_sale").then(res => {
          if (res.data.errorcode == 0) {
            if (Array.isArray(res.data.data)) {
              this.formValidate.sale = res.data.data;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getSourceList() {
        return this.$service
          .post("/Web/Member/get_source", {
            source_type: 1
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              if (Array.isArray(res.data.data)) {
                this.formValidate.source = res.data.data;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      }
    },
    mounted() {
      this.membershipId = this.$route.params.user_id;
      if (this.membershipId != 0) {
        this.getUserInfo();
      } else {
        this.getSaleList();
        this.getSourceList();
      }
    }
  };
</script>
<style lang="less" scoped>
  .field-sale {
    display: flex;
    flex-direction: row;

    .field-tip {
      margin-left: 22px;
    }
  }
</style>
