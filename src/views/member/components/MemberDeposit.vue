<template>
<Modal v-model="showAdd" :mask-closable="false" title="定金" width="750">
  <Form ref="postForm"
          :model="postData"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
   <Form-item prop="id" :rules="{required: true,  message: '请选择' }" :label-width="0" @on-change="changeInfo">
    <RadioGroup v-model="postData.id" class="model-check-list" @on-change="handleRadioChange">
      <Radio v-for="item in data" :label="item.front_money_id" :key="item.front_money_id">
        <div class="check-right">
        <div class="top-con">
          <span class="item">{{item.create_time}}</span>
          <span class="item"> 收定金({{item.purpose==0 ? '会员卡' : item.purpose==1 ? '私教课' : '其他'}}) <span class="red">{{item.amount}}</span>元</span>
        </div>
        <div class="bottom-con" v-if="item.remark">
          备注：{{item.remark}}
        </div>
      </div>
      </Radio>
  </RadioGroup>
   </Form-item>
   <Form-item
    v-if="showAdd"
    label="退款方式"
    prop="new_pay_type"
    :rules="{required: parseFloat(curInfo.amount) != 0, type: 'array', min: 1, message: '退款方式为必选项'}">
      <pay-type-list
        v-model="postData.new_pay_type"
        :amount="parseFloat(curInfo.amount)"
        :userId="userId"
        :showSQB="false"
        isRefund />
   </Form-item>
    <FormItem label="退款时间" prop="refund_time" :rules="{required: true,  message: '请填写退款时间' }">
      <Date-picker type="date" format="yyyy-MM-dd" :options="options" placeholder="选择日期" v-model="postData.refund_time"></Date-picker>
    </FormItem>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doBack">退款</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
import PayTypeList from 'components/form/PayTypeList'

import { formatDate } from "@/utils/index"
export default {
  name: 'MemberDeposit',
  data() {
    return {
      curInfo: {},
      postData: {
        id: '',
        new_pay_type: [],
        refund_time: ''
      },
      options: {
        disabledDate: date => {
          const item = this.data.find(item => item.front_money_id === this.postData.id)
          if (!item) {
            return false
          }
          const createTime = new Date(item.date)
          if (!createTime) {
            return false
          }
          return (date.valueOf() > Date.now()) || ((createTime.valueOf() - 24*60*60*1000) > date.valueOf())
        }
      }
    }
  },
  components: {
    PayTypeList
  },
  props: {
    data: {
      type: [Object,Array],
      required: true
    },
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.postData = {
          id: '',
          new_pay_type: [],
          refund_time: ''
        }
      }
    }
  },
  created() {},
  methods: {
    handleRadioChange() {
      this.postData.refund_time = ''

      for (const iterator of this.data) {
        if(iterator.front_money_id === this.postData.id) {
          this.curInfo = iterator
          this.postData.new_pay_type =
            Array.isArray(iterator.new_pay_type)
              ? iterator.new_pay_type.map(v => {
                  const item = {
                    name: v.pay_type_name,
                    pay_type: +v.pay_type,
                    amount: v.amount
                  };
                  +v.card_user_id && (item.card_user_id = v.card_user_id)
                  return item
                })
              : []
          return;
        }
      }
    },
    changeInfo(id) {

    },
    doBack() {
      this.$refs.postForm.validate((valid) => {
        if (valid) {
          const new_pay_type = this.postData.new_pay_type.filter(v => {
            // 不能退款收钱吧
            return +v.amount && ![20, 21].includes(v.pay_type)
          })
          const item = this.curInfo.new_pay_type.find(v => +v.pay_type === 8)
          const reItem = new_pay_type.find(v => v.pay_type === 8)
          if (item && reItem && +reItem.amount > +item.amount) {
            this.$Message.error({
              content: `储值卡退回金额，不能超过储值卡支付时的金额(￥${item.amount})`,
              duration: 2.5
            })
            return;
          }

          const params = {
            ...this.postData,
            refund_time: this.postData.refund_time ? formatDate(this.postData.refund_time, 'yyyy-MM-dd') : this.postData.refund_time,
            new_pay_type
          }

          this.$service
            .post('/Web/FrontMoney/refund', params)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.showAdd = false
                // this.$Message.success(res.data.errormsg)
                this.$emit('on-success')
                this.$emit('on-printinfo',5,this.userId,this.postData.id,'depositback')
              }else{
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
        }
      })
    }
  }
}
</script>
