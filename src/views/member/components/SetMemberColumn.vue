<template>
  <Modal v-model="isShow" :mask-closable="false" title="字段自定义">
    <div style="border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;">
      <Checkbox
        :indeterminate="indeterminate"
        :value="checkAll"
        @click.prevent.native="handleCheckAll">
        全选
      </Checkbox>
    </div>
    <Checkbox-group class="set-column-check" v-model="selectedKeys" @on-change="checkAllGroupChange">
      <Checkbox :label="column.key"  v-for="(column, key) in columns" :key="key" :disabled="column.disabled">{{column.title}}</Checkbox>
    </Checkbox-group>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="setColumns">确定</Button>
      <Button @click="isShow = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'SetMemberColumn',
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: <PERSON><PERSON><PERSON>
      }
    },
    data() {
      return {
        indeterminate: true,
        checkAll: false,
        selectedKeys: ['avatar', 'username', 'card_name', 'status', 'allplus', 'overplus', 'cu_deal_time', 'end_time', 'marketers_name', 'followup_coach_name', 'followup_swim_coach_name'],
        columns: [
        {
          title: "头像",
          key: "avatar",
          disabled: true
        },
        {
          title: "归属门店",
          key: "belong_bus_name"
        },
        {
          title: "姓名",
          key: "username",
          disabled: true
        },
        {
          title: "性别",
          key: "sex"
        },
        {
          title: "出生年月",
          key: "birthday"
        },
        {
          title: "身份证号",
          key: "id_code"
        },
        {
          title: "客户级别",
          key: "user_level"
        },
        {
          title: "客户标签",
          key: "tag_name"
        },
        {
          title: "注册时间",
          key: "ub_create_time"
        },
        {
          title: "客户来源",
          key: "source_name"
        },
        {
          title: "卡课名称",
          key: "card_name"
        },
        {
          title: "卡号",
          key: "card_sn"
        },
        {
          title: "状态",
          key: "status"
        },
        {
          title: "总计",
          key: "allplus"
        },
        {
          title: "剩余",
          key: "overplus"
        },
        {
          title: "使用量",
          key: "usedplus"
        },
        {
          title: "购卡时间",
          key: "cu_deal_time"
        },
        {
          title: "开卡时间",
          key: "active_time"
        },
        {
          title: "到期时间",
          key: "end_time"
        },
        {
          title: "成交方式",
          key: "source_name2"
        },
        {
          title: "上课教练",
          key: "coach_name"
        },
        {
          title: "跟进会籍",
          key: "marketers_name"
        },
        {
          title: "跟进教练",
          key: "followup_coach_name"
        },
        {
          title: "跟进泳教",
          key: "followup_swim_coach_name"
        },
        {
          title: "消费金额",
          key: "buy_card_amount"
        },
        {
          title: "购会籍卡次数",
          key: "buy_hj_times"
        },
        {
          title: "购私教卡次数",
          key: "buy_sj_times"
        },
        {
          title: "购泳教卡次数",
          key: "buy_yj_times"
        },
        {
          title: "本月会籍金额",
          key: "tm_hj_amount"
        },
        {
          title: "本月私教金额",
          key: "tm_sj_amount"
        },
        {
          title: "本月泳教金额",
          key: "tm_yj_amount"
        },
        // {
        //   title: "积分",
        //   key: "point"
        // }
      ]
      };
    },
    computed: {
      isShow: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
    },
    created() {
      let memberListColumn = localStorage.getItem('memberListColumnArr')
      this.selectedKeys = memberListColumn? JSON.parse(memberListColumn) : this.selectedKeys
    },
    methods: {
      setColumns() {
        localStorage.setItem('memberListColumnArr', JSON.stringify(this.selectedKeys))
        this.$emit('on-confirm', this.selectedKeys);
        this.isShow = false
      },
      handleCheckAll () {
        if (this.indeterminate) {
            this.checkAll = false;
        } else {
            this.checkAll = !this.checkAll;
        }
        this.indeterminate = false;

        if (this.checkAll) {
            let allSel =[]
            this.columns.forEach(item => {
              allSel.push(item.key)
            })
            this.selectedKeys = allSel
        } else {
            this.selectedKeys = ['avatar', 'username']
        }
      },
      checkAllGroupChange(data) {
        if (data.length === 23) {
            this.indeterminate = false;
            this.checkAll = true;
        } else if (data.length > 0) {
            this.indeterminate = true;
            this.checkAll = false;
        } else {
            this.indeterminate = false;
            this.checkAll = false;
        }
      }
    }
  };
</script>

<style scoped>
.set-column-check /deep/ .ivu-checkbox-wrapper {
  margin: 0 20px 20px 0;
}
</style>
