<template>
  <Modal :mask-closable="false" v-model="isShow" title="赠体验卡">
    <Form ref="giftcardForm" :model="giftcardForm" class="modal-form" style="padding: 0 30px" :label-width="100">
      <Form-item label="搜索会员" prop="user_id" v-if="!userId" :rules="{required: true, message: '请选择会员'}">
        <userSearch url="/Web/FrontMoney/search_all_user" v-model="giftcardForm.user_id"></userSearch>
      </Form-item>
      <Form-item label="赠送卡种" prop="card_id" :rules="{required: true, message: '请选择卡种'}">
        <Select v-model="giftcardForm.card_id" clearable filterable>
          <Option v-for="item in giftcardsList" :key="item.card_id" :value="item.card_id">{{item.card_name}}</Option>
        </Select>
      </Form-item>
      <Form-item label="实体卡号" prop="card_sn">
        <Input v-model="giftcardForm.card_sn" />
      </Form-item>
      <Form-item label="激活方式" prop="active_type">
        <Radio-group v-model="giftcardForm.active_type">
          <Radio label="1">立即激活</Radio>
          <Radio label="2">到场后激活</Radio>
        </Radio-group>
      </Form-item>
      <Form-item label="备注" prop="remark">
        <textarea rows="3" maxlength="90" v-model="giftcardForm.remark"></textarea>
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="newCard">确定</Button>
      <Button @click="isShow = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  import userSearch from 'src/components/user/userSearch';
  import EventBus from 'components/EventBus.js';
  export default {
    name: 'AddExperienceCards',
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      }
    },
    components: {
      userSearch
    },
    data() {
      return {
        giftcardForm: {
          user_id: '',
          card_id: '',
          card_type_id: '',
          charge_type: '',
          all_num: '',
          end_time: '',
          card_sn: '',
          active_type: '1',
          remark: '',
          experience_card: 1
        },
        giftcardsList: []
      };
    },
    computed: {
      isShow: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      isShow(val) {
        if (!val) {
          this.$refs.giftcardForm.resetFields();
        } else {
          this.getgiftcardsList();
        }
      }
    },
    created() {
    },
    methods: {
      newCard() {
        if (this.userId) {
          this.giftcardForm.user_id = this.userId;
        }
        this.$refs.giftcardForm.validate(valid => {
          if (valid) {
            for (let i = 0; i < this.giftcardsList.length; i++) {
              if (this.giftcardForm.card_id == this.giftcardsList[i].card_id) {
                this.giftcardForm.card_type_id = this.giftcardsList[i].card_type_id;
                this.giftcardForm.charge_type = this.giftcardsList[i].card_type_id == 1 ? 2 : 1;
                this.giftcardForm.end_time = this.giftcardsList[i].end_time;
                this.giftcardForm.all_num = this.giftcardsList[i].number;
              }
            }
            this.$service
              .post('/Web/Member/buy_experience_card', this.giftcardForm)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.isShow = false;
                  this.$emit('on-success');
                  this.$Message.success(res.data.errormsg);
                  EventBus.$emit('onExpCardSuccess');
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                this.$Message.error(err);
              });
          }
        });
      },
      getgiftcardsList() {
        this.$service.get('/Web/Member/get_experience_card').then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.giftcardsList = res.data.data.list;
            } else {
              this.giftcardsList = [];
              this.$Message.error(res.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      }
    }
  };
</script>

<style scoped>
</style>
