<template>
  <div class="table-wrap deposit">
    <header>
      <Select v-model="selectBusId" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Input style="width: 180px"
             v-model="searchText"
             placeholder="姓名/电话/实体卡号" />
      <dateRange :clearable="true"
                 :days.sync="days"
                 @on-change="val => { dateRange = val }"></dateRange>
      <Select v-model="status"
              style="width: 150px"
              placeholder="押金状态"
              clearable>
        <Option value="1">未退</Option>
        <Option value="2">已退</Option>
      </Select>
      <Button type="success"
              @click="doSearch">搜索</Button>
    </header>
    <div class="total-stat">
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.total }}</h3>
        <p>总收押金</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.not_refund }}</h3>
        <p>未退押金</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ depositData.refund }}</h3>
        <p>已退押金</p>
      </div>
      <b></b>
      <div class="stat">
        <div id="cashRing"
             style="width: 200px; height: 100%"></div>
      </div>
    </div>
    <Table :columns="columns"
           :data="tableData"
           ref="table"
           disabled-hover></Table>

    <ReturnPayModal
      v-model="showBackMoney"
      :newPayType="newPayType"
      :amount="returnPayAmount"
      :userId="reUserId"
      :notDate="true"
      @on-confirm="backConfirm" />

    <footer>

      <div style="display: flex">
        <Button type="success"
                style="margin-right: 30px"
                @click="takeDeposit = true">收押金</Button>
        <Dropdown @on-click="otherCase"
                  placement="top">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">导出excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <Page :total="depositData.count"
            :current.sync="currentPage"
            show-total
            show-sizer
            placement="top"
            @on-change="getDepositList"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <AddCash v-model="takeDeposit" @on-success="getDepositList" @on-printinfo="depositFinish" />
    <receipt-modal v-model="showPrint" :to-path='toPath' />
    <Modal title="支付方式" v-model="showPayTypeDetail">
      <Table :columns="payDetailColumns" :data="payTypeDetail" disabledHover></Table>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import eCharts from 'echarts'
import dateRange from 'src/components/picker/datePickerWithButton'
import AddCash from './AddCash.vue'
import { getNewHost } from 'utils/config'
import { formatDate } from 'utils'
import receiptModal from 'components/receipt/receipt.vue';
import receipt from 'mixins/receipt.js'
import ReturnPayModal from 'components/form/ReturnPayModal'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'cashTable',
  mixins: [receipt],
  data() {
    return {
      days: [Date.now() - 30 * 24 * 60 * 60 * 1000, Date.now()],
      searchText: '',
      searchSaleId: '',
      dateRange: [],
      status: '',
      tableData: [],
      exportData: [],
      depositData: {},
      depositChart: null,

      showBackMoney: false,
      takeDeposit: false,

      sizer: 10,
      currentPage: 1,

      backDepositId: '',
      backCashUserId: '',
      newPayType: [],
      returnPayAmount: 0,
      refundTime: '',
      reUserId: '',

      columns: [
        {
          title: '时间',
          key: 'create_time',
          width: 200
        },
        {
          title: '会员',
          key: 'username',
          render: (h, params) => {
            // return (
            //   <a
            //     href="javascript:void(0)"
            //     on-click={name => {
            //       this.$router.push(`/member/detail/${params.row.user_id}`)
            //     }}
            //   >
            //     {params.row.username}
            //   </a>
            const item = params.row
            return (<a onClick={() => {
              this.$router.push(`/member/detail/${item.user_id}/${item.bus_id}`)
            }}>{item.username}</a>)
          }
        },
        {
          title: '押金金额',
          key: 'amount'
        },
        {
          title: '押金用途',
          key: 'use_type'
        },
        {
          title: '收取时间',
          key: 'collect_time'
        },
        {
          title: '收取账号',
          key: 'collect_account'
        },
        {
          title: '支付方式',
          key: 'pay_type_name',
          render: (h, { row }) => {
              if (row.pay_type_name && row.pay_type_name.split(',').length > 1) {
                return (
                  <i-button
                    onClick={() => {
                      this.handleShowPayDetail(row)
                    }}
                    type="text"
                  >
                    {row.pay_type_name}
                  </i-button>
                )
              } else {
                return <div>{row.pay_type_name}</div>
              }
            }
        },
        {
          title: '状态',
          key: 'status'
        },
        {
          title: '实退金额',
          key: 'refund_amount'
        },
        {
          title: '备注',
          ellipsis: true,
          key: 'remark',
          render: (h, param) => {
            return (
              <span title={param.row.remark}>{param.row.remark}</span>
            )
          }
        },
        {
          title: '操作',
          render: (h, param) => {
            return (
              <i-button
                type="text"
                disabled={param.row.status === '已退' || this.jumpBusId!==this.busId}
                onClick={() => this.clickBackDeposit(param.row)}
              >
                退款
              </i-button>
            )
          }
        }
      ],
      depositOption: {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          top: 18,
          right: 25,
          data: ['已退', '未退' ],
          itemHeight: 12,
          itemWidth: 12
        },
        color: ['#1abbde', '#a76de8'],
        series: [
          {
            name: '押金详情',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '14',
                  fontWeight: 'normal'
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              { value: 0, name: '已退', code: 'refund' },
              { value: 0, name: '未退', code: 'not_refund' }
            ]
          }
        ]
      },
      storeList: [],
      selectBusId: '',
      jumpBusId: '',

      showPayTypeDetail: false,
      payTypeDetail: [],
      payDetailColumns: [
        {
          title: '支付方式',
          key: 'pay_type_name'
        },
        {
          title: '支付金额',
          key: 'amount'
        }
      ]
    }
  },
  components: {
    dateRange,
    AddCash,
    ReturnPayModal,
    receiptModal
  },
  computed: {
    ...mapState(['busId'])
  },
  created() {
    let days = [this.timeToDate(this.days[0]), this.timeToDate(this.days[1])]
    this.dateRange = days
    this.selectBusId = this.busId
    this.jumpBusId = this.busId
    this.getStoreList()
    this.getDepositList()
  },
  methods: {
    ...mapActions(["getAdminInfo", "getReceiptAuth"]),
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    timeToDate(time) {
      return formatDate(new Date(time), 'yyyy-MM-dd')
    },
    doSearch() {
      this.sizer = 10
      this.currentPage = 1
      this.getDepositList()
    },
    backConfirm(info) {
      this.newPayType = info.new_pay_type
      this.refundTime = info.refund_time
      this.doBackDeposit()
    },
    /* 押金退还 */
    doBackDeposit() {
      this.$service
        .post('/Web/CashPledge/refundCashPledge', { cash_pledge_ids: this.backDepositId, new_pay_type: this.newPayType, refund_time: this.refundTime })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.newPayType = []
            this.reUserId = ''
            this.getDepositList()
            this.depositFinish(6,this.backCashUserId,this.backDepositId,'cashrefund')
            this.showBackMoney = false
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    },
    getDepositList() {
      const url = '/Web/CashPledge/getList'
      let postData = {
        begin_date: this.dateRange[0],
        end_date: this.dateRange[1],
        status: this.status,
        marketers_id: this.searchSaleId,
        search: this.searchText,
        page_no: this.currentPage,
        page_size: this.sizer,
        bus_id: this.selectBusId
      }
      return this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.tableData = resData.list
            this.depositData = resData
            this.drawRing(resData)
            this.jumpBusId = this.selectBusId
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    },
    getExportList(isExport) {
      const url = '/Web/CashPledge/getList'
      let postData = {
        begin_date: this.dateRange[0],
        end_date: this.dateRange[1],
        status: this.status,
        marketers_id: this.searchSaleId,
        search: this.searchText,
        page_no: 1,
        page_size: this.depositData.count,
        bus_id: this.selectBusId
      }
      return this.$service
        .post(url, postData, { isExport })
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.exportData = resData.list.map(item => {
              return Object.assign({}, item, {
                pay_type_name: item.pay_type_name.replace(/,/g, '，'),
                mix_pay: item.mix_pay.replace(/,/g, '，'),
              })
            })
          } else {
            this.$Message.error(res.data.errormsg)
            return false
          }
        })
        .catch(err => {
          this.$Message.error(err)
        })
    },
    // 业绩环图
    drawRing(resData) {
      for (const key in resData) {
        if (resData.hasOwnProperty(key)) {
          this.depositOption.series[0].data.forEach(item => {
            if (item.code === key) {
              item.value = parseFloat(resData[key]).toFixed(2)
            }
          })
        }
      }
      this.$nextTick(() => {
        this.depositChart = eCharts.init(document.getElementById('cashRing'))
        this.depositChart.setOption(this.depositOption)
      })
    },
    handleShowPayDetail(item) {
      this.payTypeDetail = Array.isArray(item.new_pay_type) ? item.new_pay_type : []
      this.showPayTypeDetail = true
    },
    clickBackDeposit(param) {
      this.backDepositId = param.id;
      this.backCashUserId = param.user_id;
      this.newPayType = param.new_pay_type.map(v => {
        const item = {
          name: v.pay_type_name,
          pay_type: +v.pay_type,
          amount: v.amount
        };
        +v.card_user_id && (item.card_user_id = v.card_user_id)
        return item
      });
      this.returnPayAmount = param.amount
      this.reUserId = param.user_id
      if(Number(param.locker_rent_id)) {
        localStorage.setItem('showMemberCash',param.locker_rent_id);
        this.$router.push(`/member/detail/${param.user_id}`)
      } else {
        this.showBackMoney = true
      }

    },
    pageSizeChanged(size) {
      this.currentPage = 1
      this.sizer = size
      this.getDepositList()
    },
    async otherCase(val) {
      if (val === '0') {
        await this.getExportList(true)
        if (!this.exportData.length) return false
        this.$refs.table.exportCsv({
          filename: '押金列表',
          columns: this.columns.filter((col, index) => index < 10),
          data: this.exportData
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.total-stat {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 0 40px;
  height: 150px;
  border-bottom: @border;
  display: flex;
  justify-content: space-around;
  align-items: center;
  text-align: center;
  > span {
    position: absolute;
    left: 22px;
    top: 13px;
    color: #666;
    font-size: 14px;
  }
  .stat {
    h3 {
      font-size: 40px;
      color: #52a4ea;
      font-weight: normal;
      margin-top: 20px;
      span {
        font-size: 24px;
      }
    }
    p {
      color: #999;
      font-size: 14px;
    }
  }
  > b {
    width: 1px;
    height: 30px;
    background-color: #ccc;
  }
}

.back-money {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  padding: 50px 0;
}

footer {
  display: flex;
  justify-content: space-between;
}
</style>
