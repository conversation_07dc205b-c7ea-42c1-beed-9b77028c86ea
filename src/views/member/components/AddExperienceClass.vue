<template>
  <Modal :mask-closable="false" v-model="isShow" title="赠体验课">
    <Form
      ref="classCardForm"
      :model="giftcardForm"
      class="modal-form"
      style="padding: 0 30px"
      :label-width="100"
    >
      <Form-item
        label="搜索会员"
        prop="user_id"
        v-if="!userId"
        :rules="{required: true, message: '请选择会员'}"
      >
        <userSearch url="/Web/FrontMoney/search_all_user" v-model="giftcardForm.user_id"></userSearch>
      </Form-item>
      <Form-item label="赠送卡种" prop="card_id" :rules="{required: true, message: '请选择卡种'}">
        <ExpClassSelect v-model="giftcardForm.card_id" @on-change="classChage"/>
      </Form-item>
      <Form-item label="实体卡号" prop="card_sn">
        <Input v-model="giftcardForm.card_sn" />
      </Form-item>
      <Form-item label="激活方式" prop="active_type">
        <Radio-group v-model="giftcardForm.active_type">
          <Radio label="1">立即激活</Radio>
          <Radio label="2">到场后激活</Radio>
        </Radio-group>
      </Form-item>
      <Form-item label="备注" prop="remark">
        <textarea rows="3" maxlength="90" v-model="giftcardForm.remark"></textarea>
      </Form-item>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="newCard">确定</Button>
      <Button @click="isShow = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
import userSearch from "src/components/user/userSearch";
import EventBus from "components/EventBus.js";
import ExpClassSelect from "components/form/ExpClassSelect";
export default {
  name: "AddExperienceClass",
  props: {
    userId: {
      type: [String, Number]
    },
    value: {
      type: Boolean
    }
  },
  components: {
    userSearch,
    ExpClassSelect
  },
  data() {
    return {
      giftcardForm: {
        user_id: "",
        card_id: "",
        card_type_id: "",
        charge_type: "",
        all_num: "",
        end_time: "",
        card_sn: "",
        active_type: "1",
        remark: "",
        experience_card: 1
      }
    };
  },
  computed: {
    isShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  watch: {
    isShow(val) {
      if (!val) {
        this.$refs.classCardForm.resetFields();
      }
    }
  },
  created() {},
  methods: {
    classChage(item) {
      this.giftcardForm.card_type_id = item.card_type_id;
      this.giftcardForm.charge_type = item.card_type_id == 1 ? 2 : 1;
      this.giftcardForm.end_time = item.end_time;
      this.giftcardForm.all_num = item.number;
    },
    newCard() {
      if (this.userId) {
        this.giftcardForm.user_id = this.userId;
      }
      this.$refs.classCardForm.validate(valid => {
        if (valid) {
          this.$service
            .post("/Web/Member/buy_experience_card", this.giftcardForm)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.isShow = false;
                this.$emit("on-success");
                this.$Message.success(res.data.errormsg);
                EventBus.$emit("onExpCardSuccess");
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              this.$Message.error(err);
            });
        }
      });
    }
  }
};
</script>

<style scoped>
</style>
