<template>
    <div class="table-wrap">
        <header>
            <Select class="w120"
                    v-model="postData.bus_id"
                    placeholder="归属场馆"
                    filterable>
                <Option v-for="option in adminBusList"
                        :value="option.id"
                        :key="option.id">{{ option.name }}</Option>
            </Select>
            <Input class="w120" v-model="postData.name"
                   placeholder="套餐包名称"
                   @on-enter="handleSearch"></Input>

            <Select v-model="postData.status"
                    placeholder="在售状态">
                <Option value="">在售状态</Option>
                <Option :value="1">在售</Option>
                <Option :value="0">下架</Option>
            </Select>
            <Button type="success" @click="handleSearch">搜索</Button>
        </header>
        <main>
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="cardList"
                   ref="table"
                   @on-selection-change="handleCheckedArray"></Table>
        </main>
        <footer>
            <!-- <router-link :to="{ path: '/bundlePackage/save'}">
                <Button class="rb-btn"
                        type="success">新增套餐包</Button>
            </router-link>
            <Dropdown style="margin-left: 20px"
                      @on-click="otherCase" placement="top">
                <Button>
                    其他操作
                    <Icon type="md-arrow-dropdown"></Icon>
                </Button>
                <Dropdown-menu slot="list">
                    <DropdownItem name="2">导出excel</DropdownItem>
                </Dropdown-menu>
            </Dropdown> -->
            <Pager :name="'cardList'+cardType" :total="+cardTotal" :postData="postData" @on-change="handleWhichPage" />
        </footer>

        <Modal v-model="supportMobileModal"
               title="在线购卡">
            <div class="bodybuilding">
                <p class="w-red">启用在线支付购卡购课功能</p>
                <p class="w-red">需要先申请微信特约服务商</p>
                <img class="word"
                     src="../../../assets/img/word_support_mobile.png"
                     alt="word">
                <p class="w-blue">微信支付特约商户的申请和配置</p>
            </div>
            <div slot="footer"
                 class="modal-buttons">
                <Button type="info"
                        @click="handleDownloadWord">下载文档</Button>
                <Button @click="supportMobileModal = false">取消</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import { getResponseData } from '../../../utils/index'
import { mapState } from 'vuex'
import Pager from 'components/pager'
import { truncate } from 'lodash-es'
export default {
  name: 'PackageCardList',
  components: {
    Pager
  },
  props: {
    cardType: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      hasCardSettingAuth: false, // 会员卡设置权限
      totalCanChangeCard: [],
      changeCardForm: {
        card_ids: ''
      },
      postData: {
        bus_id: '',
        page_no: 1,
        page_size: 10,
        name: '',
        status: 1
      },
      cardColumns: [
        {
          title: '套餐包名称',
          key: 'name'
        },
        {
          title: '售价',
          key: 'amount',
          sortable: true
        },
        {
          title: '内容',
          key: 'contend',
          tooltip: true,
          width:  300
        },
        {
          title: '在售状态',
          key: 'sale_status_str',
          align: 'center',
          width: 150,
          render: (h, params) => {
            return (
              <i-switch
                value={params.row.sale_status}
                key={params.row.id + '1'}
                true-value="1"
                false-value="0"
                on-on-change={e => {
                  this.handleSaleStatus(params.row, e)
                }}
              />
            )
          }
        },
        {
          title: '会员端售卖',
          key: 'is_member_str',
          align: 'center',
          width: 150,
          render: (h, params) => {
            return (
              <i-switch
                key={params.row.id}
                value={params.row.is_member}
                true-value="1"
                false-value="0"
                on-on-change={e => {
                  this.handleSetStatus(params.row, e)
                }}
              />
            )
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            const goDetail = () => {
              this.$router.push(
                `/bundlePackage/save/${params.row.id}`
              )
            }
            const delMe = () => {
              if (params.row.delete_notice_bus_name) {
                this.$Modal.confirm({
                  title: '删除',
                  content: `删除操作将影响${
                    params.row.delete_notice_bus_name
                  }正常使用此卡，是否删除？`,
                  onOk: () => {
                    this.handleDelete(params.row, () => {
                      this.cardList.splice(params.index, 1)
                    })
                  }
                })
              } else {
                this.handleDelete(params.row, () => {
                  this.cardList.splice(params.index, 1)
                })
              }
            }
            return (
              <div>
                <i-button
                  type="text"
                  style={{
                    color: '#52a4ea',
                    minWidth: '0',
                    marginRight: '20px',
                    minHeight: '26px',
                    lineHeight: '26px'
                  }}
                  onClick={goDetail}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  style={{ color: '#ff696a', minWidth: '0',minHeight: '26px', lineHeight: '26px' }}
                  onClick={delMe}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      cardList: [],
      cardTotal: 0,
      checkedArray: [],
      supportMobileAuthority: false,
      showChange: false,
      supportMobileModal: false
    }
  },
  watch: {
    showChange(val) {
      if (!val) {
        this.changeCardForm.card_ids = ''
      }
    }
  },
  computed: {
    ...mapState([
      'busId',
      'adminBusList'
    ])
  },
  methods: {
    getCardList(allPageCount) {
      let postObj = Object.assign({}, this.postData)
      if (allPageCount) {
        postObj.page_size = allPageCount
        postObj.page_no = 1
      }
      return this.$service
        .post('/Web/package/getPackageList', postObj, { loading: true })
        .then(res => {
          let data = getResponseData(res, { count: 0, list: [] })
          data.list.forEach((item, index) => {
            item.sale_status_str = item.sale_status == 1 ? '是' : '否'
            item.is_member_str = item.is_member == 1 ? '是' : '否'
            item.amount = +item.amount
          })
          if (!allPageCount) {
            this.cardTotal = parseInt(data.count)
            this.cardList = data.list
          }
          return data.list
        })
    },
    handleWhichPage(postData) {
      // 加个$nextTick，否则被created的赋值覆盖
      this.$nextTick(() => {
        this.postData = {...this.postData, ...postData}
        this.getCardList();
      })
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getCardList()
    },
    // 修改在售状态
    handleSaleStatus(card, status) {
      card.sale_status = status
      if (status === '0') card.is_member = '0'
      this.$service
        .post(
          '/Web/package/swicthSaleStatus',
          {
            id: card.id,
            status: status
          },
          {
            loading: false
          }
        )
        .then(res => {
          if (res.data.errorcode !== 0) {
            this.$Message.error({ content: `设置失败，${res.data.errormsg}` })
            this.getCardList()
          }
        })
    },
    // 修改是否会员端售卖
    handleSetStatus(card, newStatus) {
      card.is_member = newStatus
      if (this.handleSupportMobile(card)) {
        if (newStatus == 1) card.sale_status = '1'
        this.$service
          .post(
            '/Web/package/swicthMemberStatus',
            {
              id: card.id,
              status: newStatus
            },
            {
              loading: false
            }
          )
          .then(res => {
            if (res.data.errorcode !== 0) {
              this.$Message.error({
                content: res.data.errormsg
              })
              this.getCardList()
            }
          })
      }
    },
    handleDelete(info, callback) {
      return this.$service
        .post('Web/package/delPackage', {
          id: info.id,
          usable_group_id: info.usable_group_id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success({ content: '删除成功' })
            callback()
          } else {
            this.$Message.error({ content: `删除失败，${res.data.errormsg}` })
          }
        })
    },
    otherCase(val) {
      const CASE = {
        '0': () => this.handleDeleteCheckedArray(),
        '1': () => this.$router.push('/gym/protocol'),
        '2': () => this.exportList()
      }
      CASE[val]();
    },
    async exportList() {
      let resData = await this.getCardList(this.cardTotal)
      resData = resData.map(item=> {
        return {
          ...item,
          contend: item.contend.replaceAll(',','，')
        }
      })
      this.$refs.table.exportCsv({
        filename: '套餐包',
        columns: this.cardColumns.filter((col, index) => {
          return col.key && col.key !== 'action'
        }),
        data: resData
      })
    },
    handleDeleteCheckedArray() {
      let idArr = []
      this.checkedArray.forEach((item, index) => {
        idArr.push(item.id)
      })
      if (idArr.length === 0) {
        this.$Message.error({ content: '请选择需要删除的卡！' })
        return false
      }
      this.handleDelete(idArr.toString(), () => {
        this.cardList = this.cardList.filter(
          card => !idArr.find(id => id === card.id)
        )
      })
    },
    handleCheckedArray(selection) {
      this.checkedArray = selection
    },
    getSupportMobile() {
      return this.$service.get('/Web/Card/check_surport_online').then(res => {
        if (res.data.errorcode == 0) {
          this.supportMobileAuthority = res.data.data.status == 1
        }
      })
    },
    handleSupportMobile(card) {
      if (!this.supportMobileAuthority && card.sale_status == 1) {
        this.supportMobileModal = true
        setTimeout(() => {
          card.sale_status = '0'
        }, 1000)
        return false
      } else {
        return true
      }
    },
    handleDownloadWord() {
      window.open(
        'https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf'
      )
    }
  },
  created() {
    this.postData.bus_id = this.busId
    !this.adminBusList && this.$store.dispatch('getAdminBusList')
    this.getSupportMobile()
    // this.checkWxAcount()
  }
}
</script>
