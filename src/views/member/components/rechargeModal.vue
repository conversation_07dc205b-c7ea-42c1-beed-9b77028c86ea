<template>
  <Modal
    class="rule-modal"
    title="储值卡续充设置"
    :value="show"
    :mask-closable="false"
    width="630"
    @on-visible-change="handleClose">
    <Form
      ref="addFormRef"
      class="rule-form"
      :model="formData"
      :label-width="120">
      <FormItem label="场馆" v-if="isBrant">
        <Select
          v-model="formData.bus_id"
          placeholder="请选择场馆"
          filterable
          @on-change="handleChangeBus">
          <Option
            v-for="option in adminBusList"
            :value="option.id"
            :key="option.id">{{ option.name }}</Option>
        </Select>
      </FormItem>
      <ReChangeFormItem
        :self-recharge.sync="formData.self_recharge"
        :list="formData.recharge_package"
        isModalUse
      />
    </Form>
    <div class="check-tree-form-item margin-left">
      <CheckPtClass
        v-if="show"
        :treeList="cardTreeList"
        :showCheckAll="false"
        placeholder="储值卡名称搜索"
      />
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" :disabled="!cardTreeList.length" @click="handleSummit">确定</Button>
      <Button @click="handleClose(false)">取消</Button>
      <!-- <Button @click="handleReset">重置</Button> -->
    </div>
  </Modal>
</template>

<script>
  import CheckPtClass from 'components/form/checkPtClass';
  import ReChangeFormItem from './rechargeFormItem';
  import { mapState } from 'vuex';

  export default {
    name: 'RechargeModal',
    components: {
      CheckPtClass,
      ReChangeFormItem,
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      initBusId: {
        type: [String, Number],
        default: ''
      },
      // ids: {
      //   type: Array,
      //   default: () => []
      // },
    },
    data() {
      return {
        isBrant: window.IS_BRAND_SITE,
        page_size: 9999,
        cardTreeList: [],
        formData: {
          bus_id: undefined,
          self_recharge: '1', // 开启关闭储值卡自主续充
          recharge_package: [ // 储值卡专用，充值套餐设置
            {
              id: Date.now(),
              isSame: false,
              amount: 0,
              number: 0,
              gift_number: 0,
              delay_number: 0,
            },
          ]
        },
      }
    },
    computed: {
      ...mapState(['busId','adminBusList']),
    },

    watch: {
      show(val) {
        if (val) {
          this.formData.bus_id = this.busId
          this.getCardList();
        }
      },
    },

    methods: {
      // 获取设置规则使用的储值卡列表
      getCardList() {
        const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
        return this.$service
        .post(url, {
          // bus_id: this.select_bus_id || this.busId, // 需要改其他场馆再说
          bus_id: this.formData.bus_id,
          page_size: this.page_size,
          card_type: 1, // 会籍卡
          card_class: 3 // 储值卡
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            const { list, count } = res.data.data
            const resList = Array.isArray(list) ? list : []
            const cardTreeList = [];

            if (resList.length) {
              // 转化为树组件需要的格式
              resList.forEach(v => {
                v.parentId = '-1'
                // v.name = v.name
                v.check = false // 控制选中状态
                v.show = true // 控制是否显示
                v.disabled = false // 控制是否可修改
              })
              cardTreeList.push({
                id: '-1', // parentId
                name: '储值卡',
                check: false,
                show: true,
                disabled: false,
                children: resList
              })
            }

            this.cardTreeList = cardTreeList
            // this.page_size = count && count < 9999 ? +count + 5: 9999 // 需要切换场馆后不再适用
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },

      handleChangeBus() {
        this.cardTreeList = []
        this.getCardList()
      },

      handleSummit() {
        this.$refs.addFormRef.validate((val) => {
          if (val) {
            let checkIds = [];
            checkIds = this.handleGetCheckIds(); // 拿到勾选的卡/课id
            if (checkIds.length) {
              const { self_recharge, recharge_package } = this.formData
              const params = {
                card_ids: checkIds.join(),
                self_recharge,
              };
              // 开启设置，才覆盖，关闭不覆盖
              if (self_recharge == '1') {
                const rechargeList = recharge_package.map((
                  { amount, number, gift_number, delay_number, ...rest }
                ) => {
                  return { amount, number, gift_number, delay_number }
                })
                params.recharge_package = JSON.stringify(rechargeList)
              }

              this.$service.post('Web/Card/allStoredValueCardSet', params).then(res => {
                if (res.data.errorcode === 0) {
                  this.$Message.success(res.data.errormsg)
                  this.handleClose()
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
            } else {
              this.$Message.error('至少勾选一个储值卡')
            }
          }
        })
      },

      handleClose(val) {
        this.$emit('update:show', !!val)
      },

      handleReset() {
        this.cardTreeList.length = 0;
        this.$refs.addFormRef.resetFields()
      },

      // 拿到勾选的卡，一维数组
      handleGetCheckIds() {
        const foo = v => v.children ?
          v.children.map(foo).filter(Boolean) :
          v.check ? v.id : false;

        return this.cardTreeList.map(foo).flat(2);
      }
    },
  }
</script>

<style lang="less" scoped>
.rule-modal {
  .rule-form {
    padding-right: 6px;
  }
}
/deep/.form-item-row {
  max-width: initial;
}
// .form-item-flex-row {
//   display: flex;
//   align-items: center;
// }
.check-tree-form-item {
  padding-right: 6px;
  &.margin-left {
    margin-left: 25px;
  }
  .copy-card-tips {
    margin-bottom: 24px;
    font-size: 14px;
    text-align: center;
    .card-name {
      color: #d9001b;
    }
  }

  // /deep/.filter-input-row {
  //   width: 510px;
  // }
}
/deep/.check-tree-form-item:not(.margin-left) .filter-input-row {
  width:100%;
}

</style>
