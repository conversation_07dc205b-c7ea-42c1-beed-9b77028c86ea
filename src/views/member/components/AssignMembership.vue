<template>
  <Modal v-model="isShow" title="会籍指派" :mask-closable="false" @on-cancel="isShow=false">
  <div class="name-tips">
    将<span>{{nameTips}}</span>指派给会籍人员
  </div>
  <Form ref="assignForm" class="modal-form add-brand" style="padding: 0 10px" :label-width="80">
    <FormItem label="归属会籍">
      <saleSelect v-if="isShow" :belong-bus-id="belongBusId" v-model="membershipData.marketers_id" placeholder="请选择">
        <Option value="0">无会籍跟进</Option>
      </saleSelect>
    </FormItem>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="addMembership">确定</Button>
    <Button @click="isShow=false">取消</Button>
  </div>
</Modal>
</template>

<script>
import saleSelect from 'src/components/membership/salesSelect';
  export default {
    name: 'assignMembership',
    data() {
      return {
        membershipData:{
          marketers_id:'',
          user_ids:''
        }
      }
    },
    props: {
      selectedMembers: Array,
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      },
      belongBusId: {
        // 会员的归属场馆
        type: [String, Number],
        default: ''
      }
    },
    watch: {
      isShow(val) {
        if (!val) {
          this.$refs.assignForm.resetFields()
        }
      }
    },
    components: {
      saleSelect
    },
    computed: {
      isShow: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      },
      nameTips() {
        let tips= '';
        let length=this.selectedMembers.length;
  			if(length>3){
  				tips=this.selectedMembers[0].username+'、'+this.selectedMembers[1].username+'、'+this.selectedMembers[2].username+'...等'+length+'位用户'
  			}else{
          this.selectedMembers.forEach((user,index)=>{
            if(index==length-1){
  						tips = tips + this.selectedMembers[index].username
  					}else{
  						tips = tips + this.selectedMembers[index].username+'、'
  					}
          });
  			}
        return tips;
      }
    },
    created() {
    },
    methods: {
      addMembership(){
        let userIds= [];
        this.selectedMembers.forEach(user=>{
          userIds.push(user.user_id);
        });
        this.membershipData.user_ids = userIds.join(',');
        this.membershipData.belong_bus_id = this.belongBusId;
        this.$service.post('/Web/Member/add_membership',this.membershipData)
        .then((response)=>{
          if(response.data.errorcode==0){
            this.$Message.success("指派成功!");
            this.isShow = false;
            this.$emit('on-success');
          }else{
            this.$Message.error(response.data.errormsg)
          }
        })
        .catch(function(err){
          console.log(err);
        })
      }
    }
  }
</script>
<style lang="less" scoped>
.name-tips{
  text-align: center;
  margin-bottom: 15px;
  font-size: 14px;
  span{
    color: #d9534f;
  }
}
</style>
