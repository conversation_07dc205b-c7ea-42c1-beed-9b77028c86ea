<template>
  <Modal v-model="isShow" :mask-closable="false" @on-cancel="isShow = false">
    <template name="header">
      <div class="header">
        <span>资格证</span>
      </div>
    </template>
    <div class="certification-box">
      <Card :padding="0" :bordered="false" style="width: 420px; margin: 24px auto">
        <div class="dive" :class="certification1 ? 'dive-light' : 'dive-dark'" @click="handleCardClick($event, 1, true)">
          <img class="dive-img" src="@/assets/img/dive.png" alt="dive" />
          <div class="select-box">
            <div class="select-label">有效期</div>
            <DatePicker
              v-model="validityDate1"
              type="date"
              placeholder="请选择有效期"
              style="width: 200px"
              :options="dateOptions"
              :disabled="!certification1"
              :editable="false"
            />
            <div v-if="certification1 && !validityDate1" class="error-msg">请选择有效期</div>
          </div>
          <div class="select-spot" @click="handleCardClick($event, 1, false)">
            <img v-if="certification1" class="nike" src="@/assets/img/nike.png" alt="nike" />
          </div>
        </div>
      </Card>
      <Card :padding="0" :bordered="false" style="width: 420px; margin: 0 auto">
        <div class="dive" :class="certification2 ? 'dive-light' : 'dive-dark'" @click="handleCardClick($event, 2, true)">
          <img class="dive-img" src="@/assets/img/health.png" alt="health" />
          <div class="select-box">
            <div class="select-label">有效期</div>
            <DatePicker
              v-model="validityDate2"
              type="date"
              placeholder="请选择有效期"
              style="width: 200px"
              :options="dateOptions"
              :disabled="!certification2"
              :editable="false"
            />
            <div v-if="certification2 && !validityDate2" class="error-msg">请选择有效期</div>
          </div>
          <div class="select-spot" @click="handleCardClick($event, 2, false)">
            <img v-if="certification2" class="nike" src="@/assets/img/nike.png" alt="nike" />
          </div>
        </div>
      </Card>
    </div>
    <div slot="footer" class="m-btn-box">
      <Button class="m-btn" style="margin-right: 24px; background-color: #b5b5b5" @click="isShow = false">关闭</Button>
      <Button class="m-btn" type="primary" @click="handleSave">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { formatDate } from 'utils'

export default {
  name: 'DiveCertification',
  data() {
    return {
      validityDate1: '',
      validityDate2: '',
      certification1: false,
      certification2: false,
      certificationList: [],
      dateOptions: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000
        },
      },
    }
  },
  props: {
    userId: {
      type: [String, Number],
    },
    value: {
      type: Boolean,
    },
  },
  watch: {
    isShow(val) {
      if (!val) {
        setTimeout(() => {
          this.validityDate1 = ''
          this.validityDate2 = ''
        }, 1000)
      } else {
        this.getInformation()
      }
    },
  },
  computed: {
    isShow: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    getValidityDate(name) {
      if (Array.isArray(this.certificationList)) {
        const item = this.certificationList.find((item) => item.name === name)
        return item ? item.valid_date : ''
      } else {
        return ''
      }
    },
    setValidityDate(name, date) {
      if (Array.isArray(this.certificationList)) {
        const item = this.certificationList.find((item) => item.name === name)
        if (date) {
          item.valid_date = formatDate(date, 'yyyy-MM-dd')
        } else {
          item.valid_date = ''
        }
      }
    },
    getInformation() {
      this.$service.post('/web/Document/getUserDocumentType', { user_id: this.userId }).then((res) => {
        if (res.data.errorcode === 0) {
          this.certificationList = res.data.data
          this.validityDate1 = this.getValidityDate('深水证')
          this.validityDate2 = this.getValidityDate('健康证')

          if (this.validityDate1) {
            this.certification1 = true
          } else {
            this.certification1 = false
          }
          if (this.validityDate2) {
            this.certification2 = true
          } else {
            this.certification2 = false
          }
        }
      })
    },
    handleSave() {
      if (this.certification1 && this.validityDate1) {
        this.setValidityDate('深水证', this.validityDate1)
      } else if (this.certification1) {
        this.$Message.error('请选择深水证有效期')
        return false
      } else {
        this.validityDate1 = ''
        this.setValidityDate('深水证', '')
      }
      if (this.certification2 && this.validityDate2) {
        this.setValidityDate('健康证', this.validityDate2)
      } else if (this.certification2) {
        this.$Message.error('请选择健康证有效期')
        return false
      } else {
        this.validityDate2 = ''
        this.setValidityDate('健康证', '')
      }

      this.$service
        .post('/web/Document/UpUserDocument', {
          user_id: this.userId,
          document_data: this.certificationList,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.$Message.success('保存成功')
            this.isShow = false
          }
        })
    },
    handleCardClick(event, type, isCard) {
      event.stopPropagation()
      if (type === 1) {
        if (isCard && !this.certification1) {
          this.certification1 = true
        } else if (!isCard) {
          this.certification1 = !this.certification1
        }
      } else {
        if (isCard && !this.certification2) {
          this.certification2 = true
        } else if (!isCard) {
          this.certification2 = !this.certification2
        }
      }
    },
  },
}
</script>
<style lang="less" scoped>
.ivu-card {
  border-radius: 10px;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #434343;
}

.m-btn-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;

  .m-btn {
    width: 180px;
    height: 40px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    line-height: 36px;
  }
}

.dive-light {
  opacity: 1;
  cursor: default;
}

.dive-dark {
  opacity: 0.4;
  cursor: pointer;
}

.dive {
  width: 420px;
  height: 190px;

  .dive-img {
    width: 100%;
    height: 100%;
  }

  .select-box {
    position: absolute;
    right: 30px;
    bottom: 34px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .select-label {
      font-size: 16px;
      font-weight: 400;
      color: #000000;
      margin-right: 7px;
    }

    .error-msg {
      position: absolute;
      top: 100%;
      left: 60px;
      line-height: 1;
      padding-top: 6px;
      color: #ed4014;
    }
  }

  .select-spot {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 36px;
    height: 36px;
    border: 2px solid #0b0d14;
    opacity: 0.7;
    border-radius: 50%;
    cursor: pointer;

    .nike {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
