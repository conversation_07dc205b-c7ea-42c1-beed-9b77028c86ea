<template>
  <Form-item label="收取押金">
    <a v-if="!data" @click="showAddCash = true">点击收取</a>
    <div v-else>
      <span class="gray">金额:</span> ￥{{data.amount}}
      <span class="gray">支付方式:</span>  {{ data.new_pay_type.map(v => v.name).join(',') }}
      <span class="gray">备注:</span> {{data.remark ? data.remark : '暂无'}}
      <i-button type="text" @click="editCash">编辑</i-button>
      <i-button type="text" style="color:red;min-width: 0" @click="delCash">删除</i-button>
    </div>
     <AddCash v-model="showAddCash" @on-success="addCashSuccess" from="card" :user-id ="$route.params.userId" :data='data'/>
  </Form-item>
</template>

<script>
import AddCash from './AddCash.vue';
  export default {
    name: 'cashCard',
    components: {
      AddCash
    },
    props: ['data'],
    data() {
      return {
        showAddCash: false
      }
    },
    created() {
    },
    methods: {
      editCash() {
        this.showAddCash = true
      },
      delCash() {
         this.$emit('update:data', '');
      },
      addCashSuccess(data) {
        this.$emit('update:data', data);
      }
    }
  }
</script>

<style scoped>
.gray{
  color: #999;
  margin-left: 15px;
}
</style>
