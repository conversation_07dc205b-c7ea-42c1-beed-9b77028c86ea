<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="800"
         title="费用退还">
    <Form ref="deductForm"
          :model="deductForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80"
          :rules="ruleValidate">
      <Form-item label="会员">
        <div>{{pageData.username}}</div>
      </Form-item>
      <Form-item label="储物柜">
        <div>{{pageData.locker_name}}</div>
      </Form-item>
      <Form-item label="租柜费用">
        <div>{{pageData.amount}}</div>
      </Form-item>
      <Form-item label="租柜时长">
        <div>{{pageData.duration}}</div>
      </Form-item>
      <Form-item label="租柜逾期">
        <div>{{pageData.overdue}}</div>
      </Form-item>
      <Form-item label="押金">
        <div>{{pageData.cash_pledge}}</div>
      </Form-item>
      <Form-item label="退还押金" prop="refund_amount">
        <Input v-model="deductForm.refund_amount" />
      </Form-item>
      <Form-item v-if="showModal && Number(deductForm.refund_amount) > 0" label="退款方式" prop="new_pay_type" :rules="{required: true,  message: '请填写退款方式' }">
        <pay-type-list
            v-model="deductForm.new_pay_type"
            :sqbOption="{ describe: '退还押金' }"
            :amount="Number(deductForm.refund_amount)"
            :userId="userId"
            isRefund />
      </Form-item>
    </Form>
    <div v-if="deCash.fromtype=='frompurecash'" class="warm">退款成功将自动退柜</div>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="submitDeduct">确定</Button>
      <Button
              @click="showModal = false">取消</Button>
    </div>

  </Modal>
</template>

<script>
import PayTypeList from '@/components/form/PayTypeList.vue'
  export default {
    name: 'deductCash',
    components: {
      PayTypeList
    },
    props: {
      userId: {
        type: [String, Number]
      },
      status: {
        type: [String, Number]
      },
      deCash: {
        type: Object,
        required: true
      },
      value: {
        type: Boolean
      }
    },
    data() {
      const validatePass = (rule, value, callback) => {
        if (value === '') {
            callback(new Error('退款金额必填'));
        } else {
          if(!/^[0-9]+(.[0-9]{1,2})?$/.test(value)) {
            callback(new Error('金额必须大于等于0且只能保留两位小数'));
          } else {
            if(Number(value) > Number(this.pageData.cash_pledge)){
              callback('退款金额不得高于押金金额');
            }else{
              callback();
            }
          }
        }
      };
      return {
        deductForm: {
          refund_amount: '',
          new_pay_type: [],
          lockerrent_id: ''
        },
        pageData: {},
        ruleValidate: {
          refund_amount: [{validator: validatePass, trigger: 'blur' }]
        }
      }
    },
    computed: {
      showModal: {
        get() {
          if(this.value) {
            this.deductForm.lockerrent_id = this.deCash.lockerId;
          }
          return this.value
        },
        set(val) {
          if(!val) {
            this.$emit('on-cleardecash')
          }
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showModal(val) {
        if (!val) {
          this.$refs.deductForm.resetFields()
        } else {
          this.getpageInfo();
        }
      },
      'deductForm.refund_amount'() {
        this.deductForm.new_pay_type = [];
      }
    },
    created() {
        if(this.deCash.flag) {
            this.getpageInfo();
        }
    },
    methods: {
      submitDeduct() {
        this.$refs.deductForm.validate((valid) => {
        if (!valid) {
          this.$Message.error('请正确填写信息')
          return
        }

        const reAmount = +this.deductForm.refund_amount
        if (reAmount !== 0) {
          const sum = this.deductForm.new_pay_type.map(v => +v.amount).reduce((previous, current) => previous + current, 0)
          if (sum !== reAmount) {
            this.$Message.error('退款方式金额之和不等于退还押金！')
            return
          }
        }

        this.$service
          .post('/Web/LockerRent/deleted_lockerrent',{user_id: this.userId, ...this.deductForm})
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$emit('on-printinfo',6,this.userId,this.pageData.cash_pledge_id,'cashrefund')
              this.$emit('on-success')
              this.showModal = false
            }else{
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
        })
      },
      getpageInfo() {
        this.$service
          .post('/Web/LockerRent/get_lockerrent_info', {lockerrent_id: this.deductForm.lockerrent_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.pageData = res.data.data;
              this.pageData.cash_pledge = res.data.data.cash_pledge.replace('元','');
              if (this.status != 1)  {
                this.deductForm.refund_amount = this.pageData.cash_pledge;
              }
              this.deductForm.new_pay_type = this.pageData.new_pay_type || []
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      }
    }
  }
</script>

<style scoped>
.warm {
  color: #e37573;
  text-align: center;
  font-size: 14px;
}
</style>
