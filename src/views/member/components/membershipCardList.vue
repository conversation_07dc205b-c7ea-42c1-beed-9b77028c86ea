<template>
  <!-- 会员详情-卡列表 -->
  <Card dis-hover title="卡列表" class="card-box">
    <div slot="extra" class="title">
      <div v-if="hasOperationRecordsAuth && isLoginBus" class="title-right" @click="recordModal=true">
        <img src="~assets/img/membership-record.png" alt="record" /> 会员卡操作记录
      </div>
    </div>

    <div class="card-tabs">
      <h3>会员卡（{{ cardList.length }}张）</h3>
      <span
        v-for="(item, index) in cardTabs"
        :key="index"
        class="tabs-item"
        :class="{ active: cardTabIndex === index }"
        @click="handleChangeCardTab('cardTabIndex', index)">
        {{ item }}卡
      </span>
    </div>
    <Table
      :columns="cardColumns"
      :data="filterCardList"
      no-data-text="未绑定会员卡"
      :show-header="false"></Table>

    <div class="card-tabs">
      <h3>私教卡（{{ ptCardList.length }}张）</h3>
      <span
        v-for="(item, index) in cardTabs"
        :key="index"
        class="tabs-item"
        :class="{ active: ptCardTabIndex === index }"
        @click="handleChangeCardTab('ptCardTabIndex', index)">
        {{ item }}课
      </span>
    </div>
    <Table
      :columns="cardColumns"
      :data="filterPtCardList"
      no-data-text="未绑定私教卡"
      :show-header="false"></Table>

    <div class="card-tabs">
      <h3>泳教卡（{{ swimCardList.length }}张）</h3>
      <span
        v-for="(item, index) in cardTabs"
        :key="index"
        class="tabs-item"
        :class="{ active: swimCardTabIndex === index }"
        @click="handleChangeCardTab('swimCardTabIndex', index)">
        {{ item }}课
      </span>
    </div>
    <Table
      :columns="cardColumns"
      :data="filterSwimCardList"
      no-data-text="未绑定泳教卡"
      :show-header="false"></Table>

    <!-- '请假中' '未激活' '暂停中' -->
    <Modal
      v-model="editModal"
      width="600"
      title="编辑"
      :loading="isLoading"
      :mask-closable="false">
      <Alert show-icon>其他信息请启用卡后再编辑</Alert>
      <Form
        v-if="editModal"
        :model="editForm"
        label-position="right"
        :label-width="100">
        <FormItem label="实体卡号">
          <Input v-model="editForm.cardSn" style="width: 100%" placeholder="请输入..."></Input>
        </FormItem>
        <FormItem v-if="showActiveTimeEdit" label="开卡时间">
          <DatePicker
            v-model="editForm.active_time"
            :options="cardActiveTime"
            style="width: 100%"
            placeholder="不填即随时可以开卡"
            :disabled="curDataStatus==='请假中'?true:false"
            transfer></DatePicker>
        </FormItem>
        <div v-if="editForm.hasOrder">
          <AmountItems
            v-if="editModal"
            ref="amountItems"
            :cardTypeId="editForm.card_type_id"
            actionType="edit"
            v-model="editForm"
            :hasStore="hasStore"
            :isStore="isStore"></AmountItems>
          <FormItem label="成交方式">
            <Select
              v-model="editForm.sourceId"
              style="width:100%;"
              transfer
              filterable
              clearable>
              <Option v-for="item in sourceList" :key="item.source_id" :value="item.source_id">{{ item.source_name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="成交时间">
            <DatePicker
              v-model="editForm.dealDate"
              :options="dealDateOptions"
              transfer
              type="date"
              placeholder="请选择成交时间"
              style="width: 100%"></DatePicker>
          </FormItem>
          <FormItem label="订单备注">
            <Input
              v-model="editForm.edit_remark "
              style="width: 100%"
              type="textarea"
              :rows="4"
              placeholder="请输入..."></Input>
          </FormItem>
        </div>
        <FormItem label="修改原因" required>
          <Input
            v-model="editForm.remark"
            style="width: 100%"
            type="textarea"
            :rows="4"
            placeholder="请输入..."></Input>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleEditOk">确定</Button>
        <Button @click="editModal=false">取消</Button>
      </div>
    </Modal>
    <Modal
      v-model="cancelModal"
      width="400"
      title="销卡"
      :loading="isLoading"
      :mask-closable="false">
      <Form :model="cancelForm" label-position="right" :label-width="80">
        <FormItem label="销卡备注">
          <Input
            v-model="cancelForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入..."></Input>
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handleCancelOk">确定</Button>
        <Button @click="cancelModal=false">取消</Button>
      </div>
    </Modal>
    <Modal
      v-model="memberModal"
      width="400"
      title="成员"
      :loading="isLoading"
      :mask-closable="false">
      <Card dis-hover>
        <p slot="title">该会员卡可使用成员</p>
        <div v-for="item in memberList" :key="item.user_id" class="member-card member-list">
          <Avatar icon="person" size="large" :src="item.avatar" />
          <label class="member-label" :title="item.username">{{ item.username }}</label>
          <label class="member-label">({{ item.phone }})</label>
          <Icon
            v-if="item.can_deleted==1"
            type="md-trash"
            size="18"
            class="member-delete"
            @click.native="handleDeleteMember(item.user_id)"></Icon>
        </div>
      </Card>
      <div v-if="isAddOldMember" class="member-old-form">
        <Select
          v-model="searchUserId"
          filterable
          remote
          :remote-method="handleAddOldMemberRemote"
          :loading="isRemoteLoading"
          placeholder="姓名/电话/卡号"
          style="width:300px"
          @on-change="handleAddMemberSubmit(false)">
          <Option v-for="item in searchUserList" :key="item.user_id" :value="item.user_id">{{ item.username }}</Option>
        </Select>
        <Button type="text" @click="isAddOldMember=false">取消添加</Button>
      </div>
      <div v-if="isAddNewMember" class="member-new-form">
        <Form
          ref="newMemberForm"
          :model="newMemberForm"
          label-position="right"
          :label-width="100">
          <FormItem label="姓名" prop="username" :rules="{required: true, message: '请输入会员名称'}">
            <Input v-model="newMemberForm.username" placeholder="请输入..."></Input>
          </FormItem>
          <FormItem label="性别">
            <RadioGroup v-model="newMemberForm.sex">
              <Radio label="1">
                <span>男</span>
              </Radio>
              <Radio label="2">
                <span>女</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="手机" prop="phone" :rules="{required: true, pattern: /^1\d{10}$/, message: '手机号码错误'}">
            <Input v-model="newMemberForm.phone" placeholder="请输入..."></Input>
          </FormItem>
          <FormItem label="出生年月">
            <DatePicker
              v-model="newMemberForm.birthday"
              type="date"
              placeholder="请选择成员出生日期"
              style="width: 222px"
              filterable
              clearable></DatePicker>
          </FormItem>
          <FormItem label="跟进会籍">
            <Select v-model="newMemberForm.marketers_id" filterable clearable>
              <Option v-for="item in saleList" :key="item.id" :value="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="获客来源">
            <Select v-model="newMemberForm.source_id" filterable clearable>
              <Option v-for="item in sourceList" :key="item.source_id" :value="item.source_id">{{ item.source_name }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Button type="success" @click="handleAddMemberSubmit(true)">提交</Button>
            <Button style="margin-left: 8px" @click="isAddNewMember=false">取消</Button>
          </FormItem>
        </Form>
      </div>
      <div v-if="!isAddOldMember && !isAddNewMember" class="member-controller">
        <Button @click="isAddOldMember=true">添加已有会员</Button>
        <Button @click="isAddNewMember=true">添加新会员</Button>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <Modal
      v-model="recordModal"
      title="操作记录"
      width="570"
      :loading="false"
      :mask-closable="false">
      <edit-record :ershow="recordModal" :userid="userId"></edit-record>
      <div slot="footer">
      </div>
    </Modal>
    <Modal v-model="detailModal" width="800" title="会员卡详情">
      <div class="detail-row">
        <p>会员卡类型：{{ userInfo.cardtype }}</p>
        <p>会员卡名称：{{ userInfo.cardname }}</p>
        <p>{{ userInfo.experience_card != 1 ? "购卡时间：" : "赠卡时间：" }} {{ userInfo.buy_time }}</p>
        <p>开卡时间：{{ userInfo.active_time }}</p>
        <p>有效期：{{ userInfo.validity }}</p>
        <p>剩余{{ userInfo.over_name }}：{{ userInfo.overplus }}</p>
        <p>实体卡号：{{ userInfo.card_sn }}</p>
        <p v-if="userInfo.experience_card != 1">购卡场馆：{{ userInfo.busname }}</p>
        <p v-if="userInfo.experience_card != 1">销售人员：{{ userInfo.sale }}</p>
        <p v-if="userInfo.experience_card != 1">成交方式：{{ userInfo.source_name }}</p>
        <p>使用场馆：{{ userInfo.support_bus_name }}</p>
        <p v-if="userInfo.experience_card != 1">使用人员：{{ userInfo.user_number }}人</p>
        <p v-if="userInfo.user_no && userInfo.experience_card != 1">授课方式：1对{{ userInfo.user_no }}</p>
        <p>备注：{{ userInfo.remark }}</p>
      </div>
      <div class="member-box">
        <div v-for="item in memberList" :key="item.user_id" class="member-card">
          <Avatar icon="person" size="large" :src="item.avatar" />
          <label
            v-if="isLoginBus"
            class="member-label member-link"
            :title="item.username"
            @click="handleUserClick(item.user_id)">{{ item.username }}</label>
          <label v-else class="member-label">{{ item.username }}</label>
          <label class="member-label">({{ item.phone }})</label>
        </div>
      </div>
      <div class="contract-box">
        <h3 style="margin:20px 0;font-size: 15px;">合同记录</h3>
        <Table
          :columns="contractColumn"
          :data="contractTableData"
          disabled-hover
        />
      </div>
      <div slot="footer">
      </div>
      <Modal v-model="showMarketerDetail" title="业绩归属">
        <Table
          ref="modalTable"
          :columns="marketerDetailColumn"
          :data="marketerDetailTableData"
          disabledHover></Table>
        <div slot="footer"></div>
      </Modal>
    </Modal>

    <Modal v-model="showLastCountEdit" :title="`编辑剩余${editUnit}`">
      <div style="padding: 0 30px">
        <p style="color: red; font-size: 14px; padding-bottom: 20px">剩余{{ editUnit }}只能减少不能增加，需要增加请编辑赠送{{ editUnit }}</p>
        <Form
          ref="lastCountForm"
          :model="editLastCountData"
          class="modal-form"
          label-position="right"
          :label-width="100">
          <FormItem :label="`剩余${editUnit}`" prop="last_volume" :rules="{required: true, message: '请填写'}">
            <Input-number
              v-model="editLastCountData.last_volume"
              style="width: 300px;"
              :max="initLastCount"
              :min="0"></Input-number>
          </FormItem>
          <FormItem label="编辑原因" prop="reason" :rules="{required: true, message: '请填写'}">
            <Input
              v-model="editLastCountData.reason"
              type="textarea"
              placeholder="请填写"
              style="width: 300px;"
              :rows="5"
              :autosize="{minRows: 4, maxRows: 6}" />
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="submitLastCount">确定</Button>
        <Button @click="showLastCountEdit = false">取消</Button>
      </div>
    </Modal>
  </Card>
</template>
<script>
  import { computed } from 'vue';
  import editRecord from 'components/member/editRecord';
  import AmountItems from 'components/member/amountItems';
  import EventBus from 'components/EventBus.js';
  import { mapGetters } from 'vuex';
  import { isChinese, SEARCH_HINT } from '@/utils';
  import { debounce } from 'lodash-es';

  const cardTab = 'MEMBER_SHIP_CARD_TAB'
  const cardStatusIndex = Object.freeze({
    '正常': 0,
    '未激活': 1,
    '请假中': 2,
    '已过期': 3,
    '已用完': 4
  });

  export default {
    components: { editRecord, AmountItems },

    props: {
      isLoginBus: {
        type: Boolean,
        default: true
      },
      curUserId: {
        type: [String, Number],
        default: ''
      },
      curBusId: {
        type: [String, Number],
        default: ''
      }
    },
    provide() {
      return {
        sqbServInfo: computed(() => this.sqbServInfo)
      }
    },
    data() {
      return {
        IS_BRAND_SITE: window.IS_BRAND_SITE || false,
        sqbServInfo: null,
        clickCard: 0,
        showActiveTimeEdit: false,
        cardActiveTime: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now();
          }
        },
        dealDateOptions: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        editUnit: '次数',
        showLastCountEdit: false,
        initLastCount: 0,
        editLastCountData: {
          last_volume: 0,
          card_user_id: '',
          reason: ''
        },
        hasCountEditAuth: false,
        selectedDiscount: '',
        hasOperationRecordsAuth: false, // 查看操作记录权限
        userId: this.curUserId || this.$route.params.userId,
        isLoading: false,
        // edit modal.
        editModal: false,
        editForm: {
          cardSn: '',
          active_time: '',
          new_pay_type: [{ pay_type: 0, amount: 0 }],
          help_deal: [{ marketers_id: '', amount: '', percent: 100 }],
          saleId: '',
          front_money_amount: '',
          is_front_money: 0,
          sale_amount: '',
          income_amount: 0,
          amount: 0,
          payId: '',
          sourceId: '',
          dealDate: '',
          remark: '',
          edit_remark: '',
          hasOrder: false,
          enableSubscription: false
        },
        saleList: [],
        source: {},
        sourceList: [],
        // cancel modal.
        cancelModal: false,
        cancelForm: {
          remark: ''
        },
        // member modal.
        memberModal: false,
        isRemoteLoading: false,
        searchUserId: '',
        searchUserList: [],
        memberForm: {},
        newMemberForm: {
          is_new_user: '1',
          username: '',
          sex: 1,
          phone: '',
          birthday: '',
          marketers_id: '',
          source_id: ''
        },
        memberList: [],
        isAddOldMember: false,
        isAddNewMember: false,
        // operation record modal.
        recordModal: false,
        // detail modal.
        detailModal: false,
        userInfo: {},
        // card list table.
        cardColumns: [
          {
            title: '会员卡名称',
            key: 'cardname',
            width: 230,
            render: (h, params) => {
              const packageIcon = (row) => {
                if (row.is_package === 1) {
                  // <Icon type="ios-cube" style="margin-right:6px" />
                  return (
                    <Tooltip content={row.package_name} transfer>
                      <span style="margin-right:6px;background-color:#8400ff;color:white;padding:4px">包</span>
                    </Tooltip>
                  )
                } else {
                  return (<span></span>)
                }
              }

              let teamClassName = ''
              if(params.row.teamclass_name && params.row.teamclass_name.length>0) {
                teamClassName = []
                params.row.teamclass_name.forEach(item => {
                  teamClassName.push(<p style="textAlign:left">{item}</p>)
                })
              }
              if (params.row.experience_card == 1) {
                let name = params.row.cardname;
                if (params.row.coachname) {
                  name += `(${params.row.coachname})`;
                }
                return (
                  <div>
                    <div class="hvr-curl-top-left" />
                    <span>{name}</span>
                  </div>
                );
              } else {
                return (
                  <div>
                    {packageIcon(params.row)}
                    <span>{params.row.cardname}</span>
                    { teamClassName?
                     <poptip trigger="hover" placement="right">
                        <icon type="ios-people" size="22"/>
                        <div slot="content">
                        {teamClassName}
                        </div>
                    </poptip>:''}
                  </div>
                );
              }
            }
          },
          {
            title: '会员卡号码',
            key: 'number'
          },
          {
            title: '先享后付',
            key: 'number',
            width: 80,
            render: (h, params) => {
              let dom = '';
              if (params.row.is_zhima_fit_pay_order) {
                dom = (
                  <span>
                    先享后付（<span style="color:#d9534f">{params.row.zhima_fit_pay_order.done_period}/{params.row.zhima_fit_pay_order.periods} </span>）
                  </span>
                );
              } else {
                dom = '';
              }
              return dom;
            }
          },
          {
            title: '会员卡次数',
            key: 'count',
            render: (h, params) => {
              let countDom = '';
              let item = params.row;
              if (item.is_pt_time_limit_card != 1 && (item.card_type_id == 2 || item.card_type_id == 4 || item.card_type_id == 5)) {
                countDom = (
                  <span>
                    总计
                    <span style="color:#d9534f">{item.all_num}次</span>，
                  </span>
                );
              } else if (item.is_pt_time_limit_card == 1 || item.card_type_id == 1) {
                countDom = (
                  <span>
                    总计
                    <span style="color:#d9534f">{item.all_days}天</span>，
                  </span>
                );
              } else if (item.card_type_id == 3) {
                countDom = (
                  <span>
                    总计
                    <span style="color:#d9534f">{item.all_num}元</span>，
                  </span>
                );
              }
              return (
                <div>
                  {countDom}
                  剩余
                  {this.hasCountEditAuth && item.is_pt_time_limit_card != 1 && item.card_type_id !== '1' && this.isLoginBus? (
                    <a onClick={() => this.editLastCount(item)}>{item.overplus}</a>
                  ) : (
                    <span style="color:#d9534f">{item.overplus}</span>
                  )}
                </div>
              );
            }
          },
          {
            title: '生命周期',
            key: 'lifecycle'
          },
          {
            title: '成员',
            key: 'member',
            width: 120
          },
          {
            title: '状态',
            key: 'status',
            width: 120,
            render: (h, params) => {
              let dom = '';
              if (params.row.status == '正常') {
                dom = (
                  <span style="color:#5cb85c">
                    {params.row.status}
                    {params.row.under_review == 1 ? ' (待审)' : ''}
                    <span title={params.row.custom_order_sn_str}>{params.row.order_sign_status === 0 ? ' (待签)' : ''}</span>
                  </span>
                );
              } else {
                dom = (
                  <span style="color:#d9534f">
                    {params.row.status}
                    {params.row.under_review == 1 ? ' (待审)' : ''}
                    <span title={params.row.custom_order_sn_str}>{params.row.order_sign_status === 0 ? ' (待签)' : ''}</span>
                  </span>
                );
              }
              return dom;
            }
          },
          {
            title: '详情',
            key: 'detail',
            width: 120,
            render: (h, params) => {
              return (
                <span
                  class="member-card-link"
                  onClick={() => {
                    this.handleDetailClick(params.row);
                  }}>
                  详情
                </span>
              );
            }
          },
          {
            title: '操作',
            key: 'controller',
            width: 120,
            render: (h, { row }) => {
              const {
                status,
                card_type_id,
                experience_card,
                is_pt_time_limit_card,
                is_zhima_fit_pay_order,
                under_review,
                can_stop,
                zhima_fit_pay_order
              } = row;

              const normalStatus = ['正常', '已过期', '已用完'].includes(status);
              const finalStatus = ['已过期', '已用完'].includes(status)
              const unReview = under_review != 1; // 0 已审核, 其他 待审核
              const isZhimaFitPay = is_zhima_fit_pay_order && ['SURRENDER', 'END'].includes(zhima_fit_pay_order.status);
              const notExCard = experience_card != 1; // 不是体验卡

              const options = [
                // 编辑
                notExCard && row.can_edit !== 0 && { name: 'edit', label: '编辑' },
                // 成员
                notExCard && is_pt_time_limit_card != 1 && { name: 'member', label: '成员' },
                // 续卡
                unReview && notExCard && normalStatus && { name: 'continue', label: card_type_id == 4 ? '续私教' : card_type_id == 5 ? '续泳教' : '续卡' },
                // 升卡
                unReview && notExCard && !is_zhima_fit_pay_order && normalStatus && { name: 'change', label: '升卡' },
                // 转卡
                unReview && notExCard && status == '正常' && !is_zhima_fit_pay_order && { name: 'transfer', label: '转卡' },
                // 补卡
                unReview && notExCard && status == '正常' && { name: 'supply', label: '补卡' },
                // 拆分
                under_review == 0 && notExCard && is_pt_time_limit_card != 1  && status == '正常' && [2, 4, 5].includes(+card_type_id) && { name: 'seperate', label: '拆分' },
                // 请假
                // unReview && notExCard && !is_zhima_fit_pay_order && status == '正常' && { name: 'leave', label: '请假' },
                unReview && notExCard && status == '正常' && can_stop === 1 && { name: 'leave', label: '请假' },
                // 启用
                unReview && ['未激活', '请假中', '暂停中'].includes(status) && { name: 'start', label: '启用' },
                // 销卡, 处理先享后付的销卡按钮  然后是正常逻辑
                unReview && (isZhimaFitPay || (!is_zhima_fit_pay_order && !finalStatus)) && { name: 'cancel', label: '销卡' },
                // 删除, 处理先享后付的删除按钮  然后是正常逻辑
                unReview && finalStatus && (isZhimaFitPay || !is_zhima_fit_pay_order) && { name: 'delete', label: '删除' },
              ].filter(Boolean);

              return (
                <dropdown
                  class="z-controller"
                  style="max-height:260px"
                  on-on-click={(name) => {
                    this.handleControllerChange(name, row);
                  }}
                  transfer>
                  <span>
                    操作
                    <icon type="md-arrow-dropdown" />
                  </span>
                  <dropdownMenu slot="list">
                    {
                      options.map(({ name, label }) => (
                        <dropdownItem key={ name } name={ name }>
                          { label }
                        </dropdownItem>
                      ))
                    }
                  </dropdownMenu>
                </dropdown>
              );
            }
          }
        ],
        cardTabs: ['全部', '有效', '失效'],
        cardTabIndex: 0, // 0 全部卡
        ptCardTabIndex: 0, // 0 全部卡
        swimCardTabIndex: 0, // 0 全部卡
        cardList: [],
        ptCardList: [],
        swimCardList: [],
        hasStore: false,
        isStore: false,
        curDataStatus: '',
        // 会员卡详情 - 合同记录table
        contractColumn: [
          // {
          //   type: 'expand',
          //   width: 50,
          //   render(h, params) {
          //     return h(SubContractRecords, {
          //       props: {
          //         list: params.row.sub_custom
          //       }
          //     })
          //   }
          // },
          {
            title: '合同编号',
            key: 'order_sn',
            render: (h, params) => {
              let order_sn = params.row.order_sn;
              const orderSignTexts = {
                '1': '未签',
                '2': '线下签署',
                '3': '会员端签署',
              };
              return (
                <div>
                  {order_sn.order_sn}
                  <span style={{ color: params.row.order_sign_status === 1 ? '#d9544f' : '#5cb85c' }}>
                  { order_sn.status === '1' ? '(已撤销)': params.row.order_sign_status === 1 || params.row.order_sign_status === 2 ||params.row.order_sign_status === 3 ? `(${orderSignTexts[params.row.order_sign_status]})` : ''}
                </span>
                </div>
              );
            }
          },
          {
            title: '最新编辑时间',
            width: 140,
            key: 'edit_time'
          },
          {
            title: '金额',
            key: 'amount'
          },
          {
            title: '类型',
            key: 'name'
          },
          {
            title: '描述',
            key: 'description',
            ellipsis: true,
            render: (h, params) => {
              let description = params.row.description;
              return (
                <div title={description} style="text-overflow: ellipsis; overflow: hidden">
                  {description}
                </div>
              );
            }
          },
          {
            title: '业绩归属',
            key: 'marketer_name',
            width: '120px',
            render: (h, params) => {
              const item = params.row;
              if (item.marketer_name.split(',').length > 1) {
                return (
                  <i-button
                    onClick={() => {
                      this.handleShowMarketerDetail(2, item.cardorder_info_id);
                    }}
                    type="text">
                    <div title={item.marketer_name} style="width: 100px; overflow: hidden; text-overflow: ellipsis">
                      {item.marketer_name}
                    </div>
                  </i-button>
                );
              } else {
                return <div>{item.marketer_name}</div>;
              }
            }
          },
        ],
        marketerDetailColumn: [
          {
            title: '姓名',
            key: 'marketers_name'
          },
          {
            title: '归属/协助',
            key: 'isMain'
          },
          {
            title: '贡献占比',
            key: 'percent'
          },
          {
            title: '业绩金额',
            key: 'amount'
          }
        ],
        contractTableData: [], // 会员卡详情 - 合同记录list
        marketerDetailTableData: [], // 会员卡详情 - 合同记录 - 业绩归属list
        showMarketerDetail: false,
      };
    },
    computed: {
      ...mapGetters(['busId']),
      filterCardList() {
        const { cardList, cardTabIndex } = this
        switch (cardTabIndex) {
          case 1:
            return cardList.filter(v => !['已过期', '已用完'].includes(v.status))
          case 2:
            return cardList.filter(v => ['已过期', '已用完'].includes(v.status))
        }
        return cardList
      },
      filterPtCardList() {
        const { ptCardList, ptCardTabIndex } = this
        switch (ptCardTabIndex) {
          case 1:
            return ptCardList.filter(v => !['已过期', '已用完'].includes(v.status))
          case 2:
            return ptCardList.filter(v => ['已过期', '已用完'].includes(v.status))
        }
        return ptCardList
      },
      filterSwimCardList() {
        const { swimCardList, swimCardTabIndex } = this
        switch (swimCardTabIndex) {
          case 1:
            return swimCardList.filter(v => !['已过期', '已用完'].includes(v.status))
          case 2:
            return swimCardList.filter(v => ['已过期', '已用完'].includes(v.status))
        }
        return swimCardList
      }
    },

    watch: {
      isAddNewMember(val) {
        if (val) {
          this.saleList.length == 0 && this.getSaleList();
          this.sourceList.length == 0 &&
            this.getSourceList().then(res => {
              this.sourceList = this.source.type1;
            });
        }
      }
    },
    created() {
      const cardTabIndex = localStorage.getItem(cardTab)
      this.cardTabIndex = +cardTabIndex // 0 全部卡 1 有效卡 2 失效卡 默认0
      this.ptCardTabIndex = +cardTabIndex
      this.swimCardTabIndex = +cardTabIndex

      if(this.isLoginBus) {
        this.getOperationRecordsAuth();
        this.getLastCountEditAuth();
      } else {
        this.cardColumns.splice(this.cardColumns.length-1, 1)
      }
    },
    mounted() {
      this.getMembershipCardList();
      EventBus.$on('onExpCardSuccess', () => {
        this.getMembershipCardList();
      });
      EventBus.$on('endSuspend', () => {
        this.getMembershipCardList();
      })
      EventBus.$on('deal-approach-change', () => {
        const marketer = this.editForm.help_deal[0]
        const hpId = marketer && marketer.marketers_id
        if (!!hpId&&hpId.startsWith('c')||['-2', '-3'].includes(hpId)) {
          this.sourceList = this.source.type3;
        } else {
          this.sourceList = this.source.type2;
        }
      })
    },
    beforeDestroy() {
      EventBus.$off('onExpCardSuccess')
      EventBus.$off('endSuspend')
      EventBus.$off('deal-approach-change')
    },
    methods: {
      submitLastCount() {
        this.$refs.lastCountForm.validate(val => {
          if(val) {
            this.$service
              .post('/Web/MemberCard/edit_last_volume', this.editLastCountData)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getMembershipCardList();
                  this.showLastCountEdit = false;
                  this.$Message.success(res.data.errormsg);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
          }
        });
      },
      editLastCount({ last_num: last_volume, card_user_id, card_type_id, universal_card, buy_bus_id }) {
        if (universal_card === '1' && buy_bus_id != this.busId) {
          return this.$Message.error('请在购卡场馆进行操作！');
        }
        this.editUnit = card_type_id === '2' ? '次数' : card_type_id === '3' ? '金额' : '节数';
        this.initLastCount = +last_volume;
        this.editLastCountData = { last_volume: +last_volume, card_user_id };
        this.showLastCountEdit = true;
      },
      getLastCountEditAuth() {
        const url = '/Web/MemberCard/edit_last_volume_auth';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.hasCountEditAuth = true;
            } else {
              // this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getMemberList() {
        return this.$service
          .post('/Web/Member/get_user_card', {
            user_id: this.curUserId || this.userId,
            bus_id: this.curBusId,
            card_user_id: this.memberForm.cardUserId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              let users = res.data.data.user;
              if (Array.isArray(users)) {
                this.memberList = users;
                users.forEach((item, index) => {
                  if (item.can_deleted == '0') {
                    this.newMemberForm.marketers_id = item.marketers_id;
                  }
                });
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getOperationRecordsAuth() {
        const url = 'Web/OperatingRecord/operating_record_auth';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.hasOperationRecordsAuth = true;
            } else {
              this.hasOperationRecordsAuth = false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getCardList() {
        return this.$service
          .post(this.IS_BRAND_SITE? '/Merchant/MemberList/cardUserList' : '/Web/Member/carduserList', {
          user_id: this.curUserId || this.userId,
          bus_id: this.curBusId
        })
      },
      getOrderDisableCardList() {
        return this.$service.post(this.IS_BRAND_SITE?'/Merchant/MemberList/order_disable_card_list':'/Web/Member/order_disable_card_list', {
          user_id: this.curUserId || this.userId,
          bus_id: this.curBusId
        })
      },
      getMembershipCardList() {
        return Promise.all([this.getCardList(), this.getOrderDisableCardList()]).then((res) => {
          const cardRes = res[0].data;
          const orderDisableCardRes = res[1].data;
          if (cardRes.errorcode == 0 && orderDisableCardRes.errorcode == 0) {
            if (Array.isArray(cardRes.data.list) && Array.isArray(orderDisableCardRes.data.list)) {
              const allList = orderDisableCardRes.data.list.concat(cardRes.data.list);
              const list = this.sortCard(allList),
                  cardList = [],
                  ptCardList = [],
                  swimCardList = [];
                list.forEach(v => {
                  const item = {
                    ...v,
                    name: v.cardname,
                    number: v.card_sn,
                    lifecycle: v.validity,
                    member: `成员${v.user_number}人`
                  }
                  switch (item.card_type_id) {
                    case '4':
                      ptCardList.push(item)
                      break;
                    case '5':
                      swimCardList.push(item)
                      break;
                    default:
                      cardList.push(item)
                      break;
                  }
                });
                this.cardList = cardList;
                this.ptCardList = ptCardList
                this.swimCardList = swimCardList
                this.$emit('cardListChange', cardList, ptCardList, swimCardList)

            }
          } else {
            this.$Message.error('获取会员卡列表失败！');
          }
        })
      },
      sortCard(list) {
        const STATUS = Object.keys(cardStatusIndex);
        list.forEach(item => {
          if (STATUS.includes(item.status)) {
            item.statusIndex = cardStatusIndex[item.status]
          } else {
            console.error(`异常会员卡状态: ${item.status}`);
          }
        });
        return list.sort((a, b) => a.statusIndex - b.statusIndex)
      },
      resetLoading() {
        const self = this;
        setTimeout(() => {
          self.isLoading = false;
          self.$nextTick(() => {
            self.isLoading = true;
          });
        }, 2222);
      },
      getDateString(date) {
        if (typeof date === 'string') date = new Date(date);
        const year = date.getFullYear();
        let month = date.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let day = date.getDate();
        day = day < 10 ? '0' + day : day;
        return `${year}-${month}-${day}`;
      },
      handleEditOk() {
        if (this.editForm.hasOrder && !this.$refs.amountItems.checkSubmit()) return false;
        if (this.editForm.help_deal&&!this.editForm.help_deal[0].marketers_id) {
          this.$Message.error('请选择归属会籍！');
          return false
        }
        if (!this.editForm.hasOwnProperty('remark') || this.editForm.remark.length === 0) {
          this.$Message.error('请输入修改原因！');
          this.resetLoading();
          return false;
        }

        let param = {};
        if (this.editForm.hasOrder) {
          param = {
            ...this.editForm,
            ...{
              card_user_id: this.editForm.cardUserId,
              edit_reason: this.editForm.remark,
              card_sn: this.editForm.cardSn,
              pay_type: this.editForm.payId,
              user_id: this.userId,
              is_order_info: this.editForm.hasOrder ? 1 : 0,
              deal_time:
                typeof this.editForm.dealDate === 'string'
                  ? this.editForm.dealDate
                  : this.getDateString(this.editForm.dealDate),
              active_time:
                typeof this.editForm.active_time === 'string'
                  ? this.editForm.active_time
                  : this.getDateString(this.editForm.active_time),
              marketers_id: this.editForm.help_deal[0].marketers_id,
              marketers_name: this.editForm.help_deal[0].marketers_name,
              marketer_category: this.editForm.help_deal[0].marketer_category,
              source_id: this.editForm.sourceId
            }
          };
          /* 12601 12683 处理精度问题，如果有误差，将最后的 ±0.0X 误差算到第一个业绩归属里面 */
          const amount = param.amount;
          const sum = param.help_deal.map(v => +v.amount).reduce((previous, current) => previous + current, 0)
          if (amount !== sum) {
            param.help_deal = param.help_deal.map(v => ({ ...v }));
            param.help_deal[0].amount = (+param.help_deal[0].amount + +( amount - sum ).toFixed(2)).toFixed(2);
          }

          // fix 19935 此处将请求传参中金额为0的支付方式去掉了
          param.new_pay_type = param.new_pay_type.filter(v => v.amount && +v.amount > 0)
          // fix 20109 当编辑、核单等场景，如果实付income_amount=0（最后点击了定金），需要将支付方式置空传参
          if (+param.income_amount === 0) {
            const needPayType = param.new_pay_type.some(v => [8, 20, 21].includes(+v.pay_type))
            if (needPayType) {
              this.$Message.error('实收金额不能小于储值卡、收钱吧、杉德支付方式的已收金额之和')
              return;
            } else {
              param.new_pay_type = []
            }
          }
        } else {
          param = {
            card_user_id: this.editForm.cardUserId,
            edit_reason: this.editForm.remark,
            card_sn: this.editForm.cardSn,
            card_type_id: this.editForm.card_type_id,
            user_id: this.userId,
            active_time:
              typeof this.editForm.active_time === 'string'
                ? this.editForm.active_time
                : this.getDateString(this.editForm.active_time),
          };
        }
        return this.$service.post('/Web/MemberCard/pop_edit', param).then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.editModal = false;
            this.editForm.saleId = '';
            this.getMembershipCardList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleCancelOk() {
        return this.$service
          .post('/Web/Member/stopCard', {
            ...this.cancelForm,
            user_id: this.userId,
            card_user_id: this.cancelForm.cardUserId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.cancelModal = false;
              this.getMembershipCardList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      handleDeleteMember(id) {
        return this.$service
          .post('/Web/Member/del_user_card', {
            user_id: id,
            card_user_id: this.memberForm.cardUserId,
            operating_user_id: this.userId
          })
          .then(res => {
            this.$Message.success(res.data.errormsg);
            this.getMemberList();
            this.getMembershipCardList();
          });
      },
      handleAddOldMemberRemote: debounce(function(query) {
        if (!isChinese(query)) {
          this.$Message.warning(SEARCH_HINT);
          return;
        }
        if (query.length === 0) {
          this.searchUserList = [];
          return false;
        }
        this.isRemoteLoading = true;
        this.$service
          .post('/Web/Member/user_search', {
            search: query,
            card_user_id: this.memberForm.cardUserId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              if (Array.isArray(res.data.data)) {
                this.searchUserList = res.data.data;
                this.isRemoteLoading = false;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      }, 500),
      handleAddMemberSubmit(isNew) {
        const cannotDeleteUser = this.memberList.find(item => item.can_deleted == 0);
        let postData = {
          user_id: this.searchUserId,
          card_user_id: this.memberForm.cardUserId,
          buycard_user_id: cannotDeleteUser.user_id,
          operating_user_id: this.userId,
          is_new_user: 0
        };
        let valid = true;
        if (isNew) {
          Object.assign(postData, this.newMemberForm);
          this.$refs.newMemberForm.validate(val => {
            valid = val;
          });
        }
        if (!valid) {
          return false;
        }
        return this.$service.post('/Web/Member/add_user_card', postData).then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg);
            this.getMemberList();
            this.getMembershipCardList();
            if (isNew) {
              this.$refs.newMemberForm.resetFields();
              this.isAddNewMember = false;
            } else {
              this.searchUserId = '';
              this.isAddOldMember = false;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleChangeCardTab(key, index) {
        this[key] = index
        localStorage.setItem(cardTab, index)
      },
      handleStartPrice() {
        if (this.editForm.enableSubscription) {
          let inAmount = parseFloat(this.editForm.amount) - parseFloat(this.editForm.front_money_amount);
          this.editForm.income_amount = inAmount > 0 ? inAmount : 0;
          this.editForm.amount = inAmount > 0 ? this.editForm.amount : this.editForm.front_money_amount;
        }
      },
      checkBusCtrl(data) {
        if (data.universal_card == 1) {
          if (data.buy_bus_id != this.busId) {
            this.$Message.error('请在购卡场馆进行操作！');
            return false;
          }
        }
        return true;
      },
      getSaleList() {
        return this.$service.get('/Web/Member/get_sale').then(res => {
          if (res.data.errorcode == 0) {
            if (Array.isArray(res.data.data)) {
              this.saleList = [];
              res.data.data.forEach(item => {
                this.saleList.push({
                  id: item.marketers_id,
                  name: item.sale_name
                });
              });
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getSourceList() {
        return this.$service.get('/Web/Member/get_source').then(res => {
          if (res.data.errorcode == 0) {
            this.source = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getCoachList() {
        return this.$service.get('/Web/Member/get_coach').then(res => {
          if (res.data.errorcode == 0) {
            if (Array.isArray(res.data.data)) {
              res.data.data.forEach(item => {
                this.saleList.push({
                  id: 'c' + item.coach_id,
                  name: item.coach_name + '[教练]'
                });
              });
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getEditInfo(data) {
        return this.$service
          .post('/Web/MemberCard/get_edit_info', {
            loading: true,
            card_user_id: data.card_user_id
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              const info = res.data.data.info;
              this.sqbServInfo = {
                serv_type: 1,
                serv_id: info.cardorder_info_id
              }
              this.editModal = true;
              this.editForm = {
                ...info,
                cardUserId: data.card_user_id,
                enableSubscription: info.is_front_money == 1,
                hasOrder: info.is_order_info == 1,
                cardSn: info.card_sn,
                saleId: info.marketers_id,
                payId: info.pay_type,
                sourceId: info.source_id,
                dealDate: info.deal_time,
                income_amount: parseFloat(info.income_amount),
                amount: parseFloat(info.amount)
              };
              this.editForm.selectedDiscount = info.coupon_receive_info;

              if (['销卡', '请假', '转卡（出）', '转卡（入）', '补卡', '拆分'].includes(this.editForm.order_name)) {
                this.hasStore = true
                if (this.editForm.marketer_category == 3) {
                  this.isStore = true
                }
              }

              if (info.new_pay_type) {
                this.editForm.new_pay_type = info.new_pay_type.map(item => {
                  return {
                    ...item,
                    ...{
                      pay_type: +item.pay_type
                    }
                  };
                });
              }

              if (info.order_help_deal_list) {
                this.editForm.help_deal = info.order_help_deal_list
                  .map(item => {
                    return {
                      ...item,
                      ...{
                        marketers_id: item.marketer_category == 2 ? 'c' + item.marketers_id : item.marketers_id,
                        percent: item.proportion
                      }
                    };
                  })
                  .sort((a, b) => b.is_main - a.is_main);
              }

              /* if (info.card_type_id == 4 || info.card_type_id == 5) {
                this.sourceList = this.source.type3;
              } else {
                this.sourceList = this.source.type2;
                this.getCoachList();
              } */
              // #13196 更换为通过主业绩归属决定成交方式列表
              const marketer = this.editForm.help_deal[0]
              const hpId = marketer && marketer.marketers_id
              if (!!hpId&&hpId.startsWith('c')||['-2', '-3'].includes(hpId)) {
                this.sourceList = this.source.type3;
              } else {
                this.sourceList = this.source.type2;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      activateAllCards() { //激活所有会员卡（激活请假的会员）
        let user_id = this.userId;
        let url = '/Web/Member/user_suspend';
        return this.$service.post(url, {
          user_id,
          action: 2
        }).then(res => {
          if(res.status === 200) {
            if(res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.getMembershipCardList();
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.error("服务器扑街！")
          }
        }).catch(err => {
          console.error(err)
        })
      },
      activateSingleCard() {
        this.$service
          .post('/Web/MemberCard/enableCard', {
            user_id: this.userId,
            card_user_id: this.clickCard
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.$Message.success(res.data.errormsg);
              this.getMembershipCardList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
        });
      },
      handleControllerChange(val, data) {
        this.curDataStatus = data.status
        switch (val) {
          case 'edit':
            if (this.checkBusCtrl(data)) {
              if (data.status === '请假中' || data.status === '未激活' || data.status === '暂停中') {
                if (data.status === '未激活' || data.status === '请假中') {
                  if (data.is_package === 1) {
                    this.$router.push(`/member/editCard/${this.userId}/${data.card_user_id}/${data.cardorder_info_id}?ttp=${new Date().getTime()}`);
                  } else {
                    this.hasStore = false
                    this.isStore = false
                    this.showActiveTimeEdit = true;
                  }
                } else {
                  this.showActiveTimeEdit = false;
                }
                this.isLoading = true;
                // this.getSaleList()
                // .then(this.getSourceList)
                this.getSourceList().then(() => {
                  this.getEditInfo(data);
                });
              } else {
                if (data.is_package === 1) {
                  this.$router.push(`/member/editCard/${this.userId}/${data.card_user_id}/${data.cardorder_info_id}?ttp=${new Date().getTime()}`);
                } else {
                  this.$router.push(`/member/editCard/${this.userId}/${data.card_user_id}?ttp=${new Date().getTime()}`);
                }
              }
            }
            break;
          case 'continue':
            if (this.checkBusCtrl(data)) {
              this.$router.push(`/member/renewCard/${this.userId}/${data.card_user_id}/${data.is_package}?ttp=${new Date().getTime()}`);
            }
            break;
            case 'seperate':
            if (this.checkBusCtrl(data)) {
              this.$router.push(`/member/seperateCard/${this.userId}/${data.card_user_id}/${data.cardorder_info_id}?ttp=${new Date().getTime()}`);
            }
            break;
          case 'change':
            if (this.checkBusCtrl(data)) {
              this.$router.push(`/member/changeCard/${this.userId}/${data.card_user_id}?ttp=${new Date().getTime()}`);
            }
            break;
          case 'transfer':
            if (this.checkBusCtrl(data)) {
              this.$router.push(`/member/switchCard/${this.userId}/${data.card_user_id}/${data.is_package}?ttp=${new Date().getTime()}`);
            }
            break;
          case 'supply':
            if (this.checkBusCtrl(data)) {
              this.$router.push({ name: '补卡', query: { userId: this.userId, cardUserId: data.card_user_id } });
            }
            break;
          case 'leave':
            if (this.checkBusCtrl(data)) {
              this.$router.push(`/member/suspendCard/${this.userId}/${data.card_user_id}?ttp=${new Date().getTime()}`);
            }
            break;
          case 'start':
            this.clickCard = data.card_user_id;
            if(data.status === '未激活' && this.$store.state.userDetailInfo.suspend_status == 1) {
              this.$Modal.confirm({
                title: "自动激活",
                content: "启用未激活的会员卡，该会员将自动结束请假，是否启用？",
                loading: true,
                onOk: async () => {
                  await this.activateSingleCard(); //激活单卡并结束请假
                  EventBus.$emit("activateAll")
                  this.$Modal.remove();
                }
              })
              return;
            } else if(data.status === '请假中' && this.$store.state.userDetailInfo.suspend_status == 1) {
              this.$Modal.confirm({
                title: "自动激活",
                content: "此操作将启用全部请假状态的会员卡，是否启用？",
                loading: true,
                onOk: async () => {
                  // await this.activateAllCards();
                  await this.activateSingleCard();
                  await this.getMembershipCardList();
                  EventBus.$emit("activateAll");
                  this.$Modal.remove();
                }
              })
            } else {
              this.activateSingleCard();
            }
            break;
          case 'cancel':
            if (data.experience_card == 0) {
              if (this.checkBusCtrl(data)) {
                this.$router.push(`/member/cancelCard/${this.userId}/${data.card_user_id}`);
              }
            } else if (data.experience_card == 1) {
              this.isLoading = true;
              this.cancelModal = true;
              this.cancelForm.cardUserId = data.card_user_id;
            }
            break;
          case 'member':
            this.isLoading = true;
            this.memberModal = true;
            this.memberForm.cardUserId = data.card_user_id;
            this.getMemberList();
            break;
          case 'delete':
            this.$Modal.confirm({
              title: '删除提示',
              content: '确认删除该会员卡吗？',
              onOk: () => {
                this.$service.post('/Web/Member/delCardUser', {
                  user_id: this.userId,
                  card_user_id: data.card_user_id
                })
                .then(res => {
                  if (res.data.errorcode == 0) {
                    this.$Message.success(res.data.errormsg);
                    this.getMembershipCardList();
                  } else {
                    this.$Message.error(res.data.errormsg);
                  }
                });
              },
            });

            break;

          default:
            break;
        }
      },
      getUserInfo() {
        return this.$service
          .post('/Web/Member/carduserMsg', {
            user_id: this.curUserId || this.userId,
            bus_id: this.curBusId,
            card_user_id: this.userInfo.cardUserId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.userInfo = res.data.data;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getContractList() {
        const url = '/Web/CardOrderInfo/card_order_info_list';
        const postData = {
          user_id: this.curUserId || this.userId,
          card_user_id: this.memberForm.cardUserId,
          page_no: 1,
          // page_size: this.pageSize,
        };
        this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.contractTableData = data.list.map(item => {
                return {
                  _disableExpand: item.custom_type == 0,
                  ...item,
                  ...{
                    order_sn: {
                      order_sn: item.order_sn,
                      status: item.status
                    },
                    operation: {
                      name: item.name,
                      status: item.status,
                      is_new: item.is_new,
                      cardorder_info_id: item.cardorder_info_id,
                      card_user_id: item.card_user_id
                    },
                    marketer_name: item.marketer_name || '--'
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      handleShowMarketerDetail(index, card_order_id) {
        const url = '/Web/Statistics/statistics_card_order_info';
        this.$service.post(url, { type: 2, card_order_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.marketerDetailTableData = data.map(item => ({
              ...item,
              ...{
                isMain: item.is_main == 1 ? '主归属' : '协助',
                percent: `${item.proportion}%`,
                amount: `￥${item.amount}`
              }
            }));
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        this.showMarketerDetail = true;
      },
      handleDetailClick(data) {
        this.detailModal = true;
        this.memberForm.cardUserId = data.card_user_id;
        this.userInfo.cardUserId = data.card_user_id;
        this.getContractList()
        this.getUserInfo()
          .then(this.getMemberList)
          .then(() => {
            this.memberForm.cardUserId = '';
          });
      },
      handleUserClick(id) {
        this.$router.push(`/member/detail/${id}`);
      }
    }
  };
</script>

<style lang="less" scoped>
  .sign-active-modal {
    font-size: 18px;

    label {
      font-weight: bold;
      color: red;
    }
  }
  .card-box {
    background-color: #fff;
    width: 100%;
    padding: 0 0 25px;
    margin-bottom: 15px;
    margin-top: 15px;
  }
  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    /*height: 50px;*/
    /*margin-bottom: 10px;*/

    .title-left {
      font-size: 20px;
      font-weight: bold;
      color: #313131;
      line-height: 20px;
      display: flex;
      flex-direction: row;

      .blue-line {
        height: 20px;
        width: 2px;
        border-left: 2px solid #52a4ea;
        margin-right: 18px;
      }
    }

    .title-right {
      font-size: 18px;
      color: #0068b7;
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;

      img {
        height: 20px;
        width: 20px;
        margin-right: 8px;
      }
    }
  }
  .card-tabs {
    display: flex;
    align-items: flex-end;
    padding: 15px 0;
    font-weight: bold;
    &:first-child {
      padding-top: 0;
    }

    .tabs-item {
      position: relative;
      margin: 0 5px;
      padding: 0 12px;
      text-align: center;
      color: #999;
      cursor: pointer;
      &::after {
        content: "";
        position: absolute;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        width: 55%;
        height: 2px;
        border-radius: 2px;
      }
      &:hover {
        color: #2B8DF2;
      }
      &.active {
        color: #2B8DF2;
        &:after {
          background-color: #2B8DF2;
        }
      }
    }
  }

  .member-label {
    margin-left: 13px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 120px;
    display: inline-block;
  }
  .member-link {
    color: #0068b7;
    cursor: pointer;
    &:hover {
      color: #2477b6;
    }
  }
  .member-controller {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: 22px;
  }

  .member-old-form {
    display: flex;
    flex-direction: row;
    margin-top: 22px;
  }

  .member-new-form {
    margin-top: 22px;
  }

  .member-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 27px;
  }
  .member-card {
    display: flex;
    align-items: center;
    width: 358px;
    margin-bottom: 15px;
  }
  .member-list {
    margin-bottom: 15px;
    &:last-child {
      margin-bottom: 0;
    }
    .member-delete {
      margin-left: 15px;
      color: #666;
      cursor: pointer;
    }
  }
  .detail-row {
    padding-left: 15px;
    font-size: 14px;
    overflow: hidden;
    p {
      width: 50%;
      float: left;
      margin-bottom: 15px;
      word-break: break-all;
    }
  }
</style>
<style lang="less">
  .ivu-dropdown-transfer {
    max-height: 260px;
    text-align: center;
  }
  .member-card-link {
    color: #0068b7;
    // font-size: 16px;
    text-decoration: underline;
    cursor: pointer;
  }
  .card-box {
    .ivu-table-body {
      border: 1px solid #dcdcdc;
      border-bottom: none;
      overflow-x: hidden;
    }
    // .ivu-table{
    //   font-size: 16px;
    // }
    .ivu-table td {
      position: relative;
      height: 52px;
      border-bottom: 1px solid #dcdcdc;
    }
    .ivu-dropdown-rel {
      border: 1px solid #dcdcdc;
      border-radius: 3px;
      width: 100%;
      height: 100%;
      line-height: 28px;
      position: relative;
      cursor: pointer;
      .ivu-icon {
        position: absolute;
        right: 10px;
        top: 8px;
        color: #b5b5b5;
        font-size: 10px;
      }
      span {
        display: block;
      }
    }
  }
  .z-controller {
    height: 32px;
    width: 100px;
  }
  .hvr-curl-top-left {
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    top: 0;
    left: 0;
    background: white;
    z-index: 9;
    width: 27px;
    height: 27px;
    background: url('../../../assets/img/gift_title.png') no-repeat;
  }
</style>
