<template>
  <div class="table-wrap">
    <header>
      <Select class="w120" v-model="postData.belong_bus_id" v-if="haveThisItem('belong_bus_id')" placeholder="归属场馆"
              :clearable="false" filterable>
        <Option v-for="option in adminBusList" :value="option.id" :key="option.id">{{ option.name }}</Option>
      </Select>
      <Input class="user-search" placeholder="会员名/电话号码/实体卡号" v-model="postData.search" @on-enter="handleSearch"
             v-if="haveThisItem('search')"></Input>
      <Select class="w120 group-select" v-model="postData.card_id" placeholder="会员卡"
              v-if="(haveThisItem('belong_bus_id') && postData.belong_bus_id && haveThisItem('card_id')) || (!haveThisItem('belong_bus_id') && haveThisItem('card_id'))"
              clearable filterable>
        <Option value="-1" class="group-title">普通卡</Option>
        <Option v-for="card in memberCardList.card_list" :value="card.card_id" :key="card.card_id">{{ card.card_name
          }}
        </Option>
        <Option value="-2" class="group-title">私教课</Option>
        <Option v-for="card in memberCardList.private_card_list" :value="card.card_id" :key="card.card_id">{{
          card.card_name }}
        </Option>
      </Select>
      <div class="input-group" v-if="searchItems[curName].other">
        <div class="input-before">
          <Select v-model="otherOptionVal" placeholder="过期时间" v-if="searchItems[curName].other.length>1"
                  @on-change="initOtherOption">
            <Option v-for="(option,index) in otherOption" :value="index+1" :key="index" v-if="haveThisOption(index+1)">
              {{ option }}
            </Option>
          </Select>
          <span class="before-label" v-else>{{otherOption[searchItems[curName].other[0]-1]}}</span>
        </div>
        <div class="input-after">
          <Date-picker placeholder="选择查询时间段" v-model="dateRange" @on-change="onDateChange" type="daterange" :editable="false" clearable v-if="otherOptionVal == '1'"></Date-picker>
          <div class="min-wrap" v-if="otherOptionVal == '2'">
            <Input class="min-input" v-model="postData.surplus_begin_num"></Input>
            <span>~</span>
            <Input class="min-input" v-model="postData.surplus_end_num"></Input>
            <span>次</span>
          </div>
          <div class="min-wrap" v-if="otherOptionVal == '3'">
            <Input class="min-input" v-model="postData.surplus_begin_sum"></Input>
            <span>~</span>
            <Input class="min-input" v-model="postData.surplus_end_sum"></Input>
            <span>元</span>
          </div>
          <Select v-model="postData.birthday_month" v-if="otherOptionVal == '4'">
            <Option v-for="(item,index) in monthList" :value="index" :key="index">{{ item }}</Option>
          </Select>
          <div class="min-wrap" v-if="otherOptionVal == '5'|| otherOptionVal == '6'">
            <Input class="min-input" v-model="postData.sign_follow_begin_num"></Input>
            <span>~</span>
            <Input class="min-input" v-model="postData.sign_follow_end_num"></Input>
            <span>天</span>
          </div>
        </div>
      </div>
      <Select class="w120" v-model="postData.status" v-if="haveThisItem('status')" placeholder="状态" clearable>
        <Option v-for="(option,index) in statusOption" :value="index+1" :key="index"
                v-if="haveThisOption(index+1,true)">{{ option }}
        </Option>
      </Select>
      <Select class="w120" v-model="postData.user_level" v-if="haveThisItem('user_level')" placeholder="客户级别">
        <Option value="">客户级别</Option>
        <Option value="A">A</Option>
        <Option value="B">B</Option>
        <Option value="C">C</Option>
      </Select>
      <Select class="w120" v-model="postData.user_status" v-if="haveThisItem('user_status')" placeholder="会员状态">
        <Option value="">会员状态</Option>
        <Option value="1">正常使用</Option>
        <Option value="2">未购卡</Option>
        <Option value="3">会员卡失效</Option>
      </Select>
      <sourceSelect v-model="postData.source_id" placeholder="获客来源" v-if="haveThisItem('source_id')"></sourceSelect>
      <saleSelect :belong-bus-id="postData.belong_bus_id" v-model="postData.marketers_id" placeholder="归属会籍"
                  v-if="(haveThisItem('belong_bus_id')&&postData.belong_bus_id && haveThisItem('marketers_id')) || (!haveThisItem('belong_bus_id') && haveThisItem('marketers_id'))"
                  class="w120">
        <Option label="未指派" value="0"></Option>
      </saleSelect>
      <saleSelect :belong-bus-id="postData.belong_bus_id" v-model="postData.class_coach_id" placeholder="上课教练"
                  v-if="(haveThisItem('belong_bus_id')&&postData.belong_bus_id && haveThisItem('class_coach_id')) || (!haveThisItem('belong_bus_id') && haveThisItem('class_coach_id'))"
                  class="w120" isCoach :isMembership="false" :showCoachText="false"/>
      <saleSelect :belong-bus-id="postData.belong_bus_id" v-model="postData.coach_id" placeholder="跟进教练"
                  v-if="(haveThisItem('belong_bus_id')&&postData.belong_bus_id && haveThisItem('coach_id')) || (!haveThisItem('belong_bus_id') && haveThisItem('coach_id'))"
                  class="w120" isCoach :isMembership="false" :showCoachText="false">
        <Option label="未指派" value="0"></Option>
      </saleSelect>
      <Select class="w120" v-model="postData.valid_type" v-if="haveThisItem('valid_type')" placeholder="会员分类" clearable>
        <Option value="0">全部会员</Option>
        <Option value="1">普通会员</Option>
        <Option value="2">私教会员</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>

    <main>
      <Table @on-select="selectMember" @on-selection-change="selectMemberChange" @on-select-all="selectMemberAll"
             @on-select-cancel="selectMemberCancel" ref="table" class="avatar-zoom" :columns="columns" :data="tableData"
             stripe disabled-hover></Table>
    </main>

    <footer>
      <Button type="success" @click="addMember" style="margin-right:30px" v-if="curName !== '其它门店会员'">潜客添加</Button>
      <Dropdown @on-click="otherCase" placement="top">
        <Button>
          其他操作
          <Icon type="md-arrow-dropdown"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem name="0"
                        v-if="(haveThisItem('belong_bus_id') && postData.belong_bus_id) || !haveThisItem('belong_bus_id')">
            指派会籍
          </DropdownItem>
          <DropdownItem name="3"
                        v-if="(haveThisItem('belong_bus_id') && postData.belong_bus_id) || !haveThisItem('belong_bus_id')">
            指派跟进教练
          </DropdownItem>
          <DropdownItem name="4"
                        v-if="curName !== '其它门店会员' && ((haveThisItem('belong_bus_id') && postData.belong_bus_id) || !haveThisItem('belong_bus_id'))">
            指派上课教练
          </DropdownItem>
          <DropdownItem name="5">短信发送</DropdownItem>
          <DropdownItem name="1" v-if="excelAuth">
            导出excel
            <Export ref="export"/>
          </DropdownItem>
          <DropdownItem name="6">跨店升卡</DropdownItem>
          <DropdownItem name="2" v-if="curName=='有效会员'||curName=='潜在会员'||curName=='过期会员'||curName=='公海会员'">批量删除
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Pager :total="+totalCount" :postData="postData" :name="curName" :history="!hasQuery"
             @on-change="handlePageChange"></Pager>
      <!-- <Page :total="totalCount"
            :page-size="postData.page_size"
            :current.sync="postData.page_no"
            show-total
            show-sizer
            placement="top"
            class="page"
            @on-change="handlePageChange"
            @on-page-size-change="pageSizeChanged"></Page> -->
    </footer>
    <AssignMembership :belong-bus-id="postData.belong_bus_id" v-model="isShowAdd" @on-success="actionSuccess"
                      :selected-members="selectedMembers"/>
    <AssignFollowCoach :belong-bus-id="postData.belong_bus_id" v-model="followCoachModal" @on-success="actionSuccess"
                       :selected-members="selectedMembers"/>
    <AssignClassCoach :belong-bus-id="postData.belong_bus_id" v-model="classCoachModal" @on-success="actionSuccess"
                      :selected-members="selectedMembers" from="list"/>
    <Modal :mask-closable="false"
         v-model="isShowExcelModal"
         title="导出">
        <div>
      <RadioGroup v-model="isExportMerge" vertical class="radio-lines">
          <Radio :label="1">
              <span>会员名称合并导出</span>
          </Radio>
          <Radio :label="0">
              <span>会员名称不合并导出</span>
          </Radio>
        </RadioGroup>

    </div>
      <div slot="footer">
      </div>
    </Modal>
  </div>

</template>

<script>
  import saleSelect from 'src/components/membership/salesSelect';
  import sourceSelect from 'src/components/member/sourceSelect';
  import AssignMembership from './AssignMembership';
  import AssignFollowCoach from './AssignFollowCoach.vue';
  import AssignClassCoach from './AssignClassCoach.vue';
  import Export from 'src/components/Export';
  import { formatDate } from 'utils/index.js';
  import { mapGetters, mapState, mapActions } from 'vuex';
  import Pager from 'components/pager';

  export default {
    components: {
      saleSelect,
      sourceSelect,
      AssignMembership,
      Export,
      AssignFollowCoach,
      AssignClassCoach,
      Pager
    },
    data() {
      return {
        isShowExcelModal: false,
        isExportMerge: '',
        postData: {
          user_type: 1, //0为所有会员 1为有效会员 2位过期会员 3为未购卡会员
          is_end_day: '',
          is_end_num: '',
          is_end_sum: '',
          is_top_search: 0, //是否顶部搜索 0为不是 1为是
          is_order_bus_user: 0, //是否是查询其他门店会员 0为不是 1为是
          is_public_sea: '', //是否查询公海会员0为不是 1为是
          is_birthday: '', //是否生日提醒
          valid_type: '', //0或空 所有  1 普通 2 私教
          search: '', //姓名/电话/实体卡号
          card_id: '', //会员卡id
          belong_bus_id: this.busId, //归属场馆
          expiration_begin_time: '', //过期时间开始时间节点时间戳
          expiration_end_time: '', //过期时间结束时间节点时间戳
          surplus_begin_num: '', //剩余次数区间开始次数
          surplus_end_num: '', //剩余次数区间结束次数
          surplus_begin_sum: '', //剩余金额区间开始金额
          surplus_end_sum: '', //剩余金额区间结束金额
          birthday_month: '', //生日月份：1.一月份，2.二月份
          marketers_id: '', //销售id,如果是未指派会籍选项，请填0
          coach_id: '', // 跟进教练
          class_coach_id: '', // 上课教练
          status: '', //会员状态:1.正常、2.暂停 3.未激活 4.时间过期、5、次数耗尽 6、金额耗尽
          user_level: '', //会员等级：A、B、C,
          source_id: '', //会员来源id
          sign_follow_begin_num: '',
          sign_follow_end_num: '',
          page_no: 1,
          page_size: 10
        },
        excelAuth: false,
        isShowAdd: false,
        followCoachModal: false,
        classCoachModal: false,
        selectedMembers: [],
        privateCardList: [],
        monthList: [
          '全部',
          '一月',
          '二月',
          '三月',
          '四月',
          '五月',
          '六月',
          '七月',
          '八月',
          '九月',
          '十月',
          '十一月',
          '十二月'
        ],
        searchItems: {
          search: {
            other: [1, 2, 3, 4],
            status: [1, 2, 3],
            items: [
              'search',
              'card_id',
              'status',
              'marketers_id',
              'class_coach_id',
              'coach_id',
              'belong_bus_id',
              'user_level'
            ],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name',
              'followup_coach_name'
            ]
          },
          有效会员: {
            other: [1, 2, 3],
            status: [1, 2, 3],
            items: [
              'search',
              'card_id',
              'status',
              'marketers_id',
              'class_coach_id',
              'valid_type',
              'belong_bus_id',
              'user_level'
            ],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name',
              'followup_coach_name'
            ]
          },
          潜在会员: {
            items: ['search', 'source_id', 'marketers_id', 'belong_bus_id', 'user_level'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'phone',
              'source_name',
              'ub_create_time',
              'marketers_name'
            ]
          },
          过期会员: {
            other: [1, 2, 3],
            status: [4, 5, 6],
            items: ['search', 'card_id', 'status', 'marketers_id', 'belong_bus_id', 'user_level'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'end_time',
              'status',
              'usage',
              'marketers_name'
            ]
          },
          公海会员: {
            items: ['search', 'user_level', 'user_status', 'belong_bus_id'],
            tableColumns: ['selection', 'avatar', 'username', 'belong_bus_name', 'status_name', 'source_name']
          },
          其它门店会员: {
            other: [1, 2, 3, 4],
            status: [1, 2, 3],
            items: ['search', 'card_id', 'status'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'card_name',
              'buy_bus_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name',
              'followup_coach_name'
            ]
          },
          近期到期: {
            other: [1],
            status: [1, 2, 3, 4],
            items: ['search', 'card_id', 'status', 'marketers_id', 'belong_bus_id'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name'
            ]
          },
          次数耗尽: {
            other: [2],
            status: [1, 2, 3, 5],
            items: ['search', 'card_id', 'status', 'marketers_id', 'belong_bus_id'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name'
            ]
          },
          金额耗尽: {
            other: [3],
            status: [1, 2, 3, 6],
            items: ['search', 'card_id', 'status', 'marketers_id', 'belong_bus_id'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'card_name',
              'overplus',
              'end_time',
              'status',
              'marketers_name'
            ]
          },
          生日提醒: {
            other: [4],
            items: ['search', 'marketers_id', 'belong_bus_id'],
            tableColumns: ['selection', 'avatar', 'username', 'belong_bus_name', 'birthday', 'marketers_name']
          },
          长时间未到场: {
            other: [5],
            items: ['search', 'marketers_id', 'belong_bus_id'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'last_sign_time',
              'last_sign_num',
              'marketers_name'
            ]
          },
          长时间未跟进: {
            other: [6],
            items: ['search', 'marketers_id', 'belong_bus_id'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'last_follow_time',
              'last_follow_num',
              'marketers_name'
            ]
          },
          无会籍跟进的潜客: {
            items: ['search', 'source_id', 'belong_bus_id', 'user_level'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'last_follow_num',
              'source_name',
              'ub_create_time'
            ]
          },
          无教练跟进的会员: {
            items: ['search', 'source_id', 'belong_bus_id', 'user_level'],
            tableColumns: [
              'selection',
              'avatar',
              'username',
              'belong_bus_name',
              'last_follow_num',
              'source_name',
              'ub_create_time'
            ]
          }
        },
        otherOption: ['过期时间', '剩余次数', '剩余金额', '出生月份', '未到场时间', '未跟进时间'],
        otherOptionVal: 1,
        statusOption: ['正常', '请假中', '未激活', '时间过期', '次数耗尽', '金额耗尽'],
        dateRange: ['', ''],
        tableData: [],
        exportData: null,
        sizer: 10,
        totalCount: 0,
        exportTableData: []
      };
    },
    props: {
      // 会员管理中当前选中项
      curName: {
        type: String,
        default: ''
      }
    },
    computed: {
      ...mapState(['busId', 'adminBusList', 'globalBelongBusId']),
      //来自外部搜索(此时curMenu为search)进入该页面，在这里加入搜索框的键值对
      searchData() {
        const { follow_coach_id: coach_id, class_coach_id } = this.$route.query;
        let obj = {
          search: this.$route.query.search || '',
          marketers_id: this.$route.query.marketers_id || '',
          coach_id: this.$route.query.coach_id || '',
          valid_type: this.$route.query.valid_type || '',
          card_id: this.$route.query.card_id || '',
          coach_id,
          class_coach_id
        };
        return obj;
      },
      hasQuery() {
        return Object.values(this.searchData).some(item => !!item);
      },
      ...mapGetters(['memberCardList']),
      columns() {
        const tableColumnList = {
          selection: {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          avatar: {
            title: '头像',
            className: 'avatar-wrap',
            render: (h, params) => {
              return (
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.goDetail(params);
                  }}>
                  <img class="avatar" src={params.row.avatar}/>
                </a>
              );
            }
          },
          username: {
            title: '姓名',
            key: 'username',
            render: (h, params) => {
              return (
                <div>
                  <a
                    href="javascript:void(0)"
                    on-click={name => {
                      this.goDetail(params);
                    }}>
                    {params.row.username}
                  </a>
                  {params.row.is_wx ? <span class="icon-wx"/> : ''}
                </div>
              );
            }
          },
          belong_bus_name: {
            title: '归属场馆',
            key: 'belong_bus_name'
          },
          phone: {
            title: '手机号',
            key: 'phone'
          },
          status_name: {
            title: '会员状态',
            key: 'status_name'
          },
          source_name: {
            title: '获客来源',
            key: 'source_name'
          },
          ub_create_time: {
            title: '注册时间',
            key: 'ub_create_time',
            render: (h, params) => {
              return <span>{formatDate(new Date(params.row.ub_create_time * 1000), 'yy-MM-dd HH:mm')}</span>;
            }
          },
          card_name: {
            title: '会员卡',
            key: 'card_name',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'card_name');
            }
          },
          buy_bus_name: {
            title: '售卡场馆',
            key: 'buy_bus_name',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'buy_bus_name');
            }
          },
          overplus: {
            title: '剩余(天数/次数/余额)',
            key: 'overplus',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'overplus');
            }
          },
          end_time: {
            title: '到期时间',
            key: 'end_time',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'end_time');
            }
          },
          status: {
            title: '状态',
            key: 'status',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'status');
            }
          },
          usage: {
            title: '会员卡使用率',
            key: 'usage',
            render: (h, params) => {
              return this.cardForColumn(params.row.card_list, 'usage');
            }
          },
          birthday: {
            title: '出生日期',
            key: 'birthday',
            render: (h, params) => {
              return <span>{formatDate(new Date(params.row.birthday * 1000), 'yyyy-MM-dd')}</span>;
            }
          },
          last_sign_time: {
            title: '最近一次到场时间',
            key: 'last_sign_time',
            render: (h, params) => {
              return <span>{params.row.last_sign_time}</span>;
            }
          },

          last_follow_time: {
            title: '最近一次跟进时间',
            key: 'last_follow_time',
            render: (h, params) => {
              return <span>{params.row.last_follow_time}</span>;
            }
          },
          last_sign_num: {
            title: '未到场时间',
            key: 'last_sign_num',
            render: (h, params) => {
              return <span>{`${params.row.last_sign_num}天`}</span>;
            }
          },
          last_follow_num: {
            title: '未跟进时间',
            key: 'last_follow_num',
            render: (h, params) => {
              return <span>{`${params.row.last_follow_num}天`}</span>;
            }
          },
          marketers_name: {
            title: '归属会籍',
            key: 'marketers_name'
          },
          followup_coach_name: {
            title: '跟进教练',
            key: 'followup_coach_name'
          }
        };
        let data = [];
        let tableColumns = this.searchItems[this.curName].tableColumns;
        tableColumns && tableColumns.forEach(col => data.push(tableColumnList[col]));
        return data;
      },
      excelColumns() {
        let cur = this.curName;
        if (
          cur === '潜在会员' ||
          cur === '生日提醒' ||
          cur === '长时间未到场' ||
          cur === '长时间未跟进' ||
          cur === '公海会员'
        ) {
          let columns = this.columns.filter((col, index) => index < this.columns.length && index > 1);
          if (cur !== '潜在会员') {
              columns.push({ title: '手机号', key: 'phone' });
          }
          if (cur === '潜在会员') {
            columns.push({ title: '身份证', key: 'id_code' });
          }
          return columns;
        } else {
          return [
            { title: '姓名', key: 'username' },
            { title: '归属场馆', key: 'belong_bus_name' },
            { title: '性别', key: 'sex' },
            { title: '手机号', key: 'phone' },
            { title: '身份证', key: 'id_code' },
            { title: '来源', key: 'source_name' },
            { title: '类型', key: 'card_type' },
            { title: '会员卡', key: 'card_name' },
            { title: '购卡场馆', key: 'buy_bus_name' },
            { title: '实体卡号', key: 'card_sn' },
            { title: '总计', key: 'allplus' },
            { title: '剩余', key: 'overplus' },
            { title: '开卡时间', key: 'active_time' },
            { title: '到期时间', key: 'end_time' },
            { title: '状态', key: 'status' },
            { title: '会员创建时间', key: 'ub_create_time' },
            { title: '会籍顾问', key: 'marketers_name' },
            { title: '上课教练', key: 'class_coach_name' },
            { title: '跟进教练', key: 'followup_coach_name' },
            { title: '备注', key: 'remark' }
          ];
        }
      },
      selectedUserIds() {
        let userIds = [];
        this.selectedMembers.forEach(user => {
          userIds.push(user.user_id);
        });
        return userIds;
      }
    },
    created() {
      this.getExcelAuth();
      this.initByCurname();
      if (this.hasQuery) {
        this.postData = { ...this.postData, ...this.searchData };
        this.getUserList();
      }
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
    },
    activated() {
    },
    methods: {
      ...mapActions(['getAdminInfo', 'getReceiptAuth']),
      onDateChange(val) {
        if (!val[0]) {
          this.postData.expiration_begin_time = '';
          this.postData.expiration_end_time = '';
          return false;
        }
        let beginDate = formatDate(new Date(val[0]), 'yyyy-MM-dd');
        let endDate = formatDate(new Date(val[1]), 'yyyy-MM-dd');
        this.postData.expiration_begin_time = beginDate;
        this.postData.expiration_end_time = endDate;
      },
      initPostData() {
        this.postData = {
          user_type: 0,
          is_public_sea: '',
          belong_bus_id: this.busId,
          is_top_search: 0, //是否是查询其他门店会员 0为不是 1为是
          is_order_bus_user: 0, //是否是查询其他门店会员 0为不是 1为是
          is_not_follow: 0,
          is_not_present: 0,
          is_birthday: 0,
          is_end_day: 0,
          is_end_num: 0,
          is_end_sum: 0,
          is_not_membership_follow: 0,
          is_not_coach_follow: 0,
          valid_type: '',
          search: '',
          card_id: '',
          expiration_begin_time: '',
          expiration_end_time: '',
          surplus_begin_num: '',
          surplus_end_num: '',
          surplus_begin_sum: '',
          surplus_end_sum: '',
          sign_follow_begin_num: '',
          sign_follow_end_num: '',
          birthday_month: '',
          marketers_id: '',
          status: '',
          user_level: '',
          source_id: '',
          page_no: 1,
          page_size: 10
        };
        this.selectedMembers = [];
        // this.dateRange = [];
      },
      //'过期时间', '剩余次数', '剩余金额', '出生月份', '未到场时间', '未跟进时间' 存在多项切换时
      initOtherOption(value) {
        let otherOptionData = {
          expiration_begin_time: '',
          expiration_end_time: '',
          surplus_begin_num: '',
          surplus_end_num: '',
          surplus_begin_sum: '',
          surplus_end_sum: '',
          birthday_month: '',
          sign_follow_begin_num: '',
          sign_follow_end_num: ''
        };
        this.dateRange = [];
        this.postData = Object.assign({}, this.postData, otherOptionData);
      },
      initByCurname() {
        let val = this.curName;
        this.otherOptionVal =
          this.searchItems[val].other && this.searchItems[val].other[0] ? this.searchItems[val].other[0] : 1;
        this.initPostData();
        switch (val) {
          case 'search':
            this.postData.is_top_search = 1;
            break;
          case '有效会员':
            this.postData.user_type = 1;
            break;
          case '潜在会员':
            this.postData.user_type = 2;
            break;
          case '过期会员':
            this.postData.user_type = 3;
            break;
          case '公海会员':
            this.postData.is_public_sea = 1;
            break;
          case '其它门店会员':
            this.postData.is_order_bus_user = 1;
            break;
          case '近期到期':
            this.postData.is_end_day = 1;
            this.dateRange = [new Date(Date.now() - 86400000 * 15), this.getCurMonthLastDay()];
            this.onDateChange(this.dateRange);
            break;
          case '次数耗尽':
            this.postData.is_end_num = 1;
            this.postData.surplus_begin_num = 0;
            this.postData.surplus_end_num = 7;
            break;
          case '金额耗尽':
            this.postData.is_end_sum = 1;
            this.postData.surplus_begin_sum = 0;
            this.postData.surplus_end_sum = 300;
            break;
          case '生日提醒':
            this.postData.is_birthday = 1;
            this.postData.user_type = 1;
            this.postData.birthday_month = new Date().getMonth() + 1;
            break;
          case '长时间未到场':
            this.postData.is_not_present = 1;
            this.postData.sign_follow_begin_num = 15;
            this.postData.sign_follow_end_num = 60;
            this.postData.user_type = 1;
            break;
          case '长时间未跟进':
            this.postData.is_not_follow = 1;
            this.postData.sign_follow_begin_num = 7;
            this.postData.sign_follow_end_num = 60;
            break;
          case '无会籍跟进的潜客':
            this.postData.is_not_membership_follow = 1;
            // this.postData.user_type = 2;
            break;
          case '无教练跟进的会员':
            this.postData.is_not_coach_follow = 1;
            // this.postData.user_type = 2;
            break;
          default:
            this.postData.is_order_bus_user = 0;
            this.postData.user_type = 0;
            this.postData.is_birthday = 0;
            this.postData.is_public_sea = 0;
            this.postData.is_end_day = 0;
            this.postData.is_end_num = 0;
            this.postData.is_end_sum = 0;
        }
        if (this.searchData) {
          this.postData = Object.assign({}, this.postData, this.searchData);
        }
        // this.$nextTick(() => this.getUserList());
      },
      selectMember(selection, member) {
        let selUserIds = this.selectedUserIds;
        if (selUserIds.indexOf(member.user_id) === -1) {
          this.selectedMembers.push(member);
        }
      },
      selectMemberCancel(selection, member) {
        this.selectedMembers.forEach((user, index) => {
          if (user.user_id == member.user_id) {
            this.selectedMembers.splice(index, 1);
          }
        });
      },
      selectMemberChange(selection) {
        if (selection.length == 0) {
          this.tableData.forEach(member => {
            this.selectMemberCancel(selection, member);
          });
        }
      },
      selectMemberAll(selection) {
        if (selection.length > 0) {
          selection.forEach(member => {
            this.selectMember(selection, member);
          });
        }
      },
      cardForColumn(cards, row) {
        if (cards) {
          return (
            <ul class="row-ul">
              {cards.map(item => {
                return (
                  <li
                    class={{
                      red: row === 'status' && item[row] != '正常',
                      green: row === 'status' && item[row] === '正常'
                    }}>
                    {item[row]}
                  </li>
                );
              })}
            </ul>
          );
        } else {
          return '';
        }
      },
      //判断搜索区域是否含有某项输入框
      haveThisItem(name) {
        return this.searchItems[this.curName].items.indexOf(name) >= 0;
      },
      //判断搜索区域下拉框
      haveThisOption(index, isStatus) {
        if (isStatus) {
          return this.searchItems[this.curName].status.indexOf(index) >= 0;
        } else {
          return this.searchItems[this.curName].other.indexOf(index) >= 0;
        }
      },

      handleSearchOption(name) {
        this.selectName = name;
      },
      clearDate() {
        this.dateRange = [];
      },
      handleSearch() {
        this.postData.page_no = 1;
        this.getUserList();
      },
      getCard(val, oldVal) {
        (!this.memberCardList || (this.memberCardList && oldVal != this.globalBelongBusId)) &&
        this.$store.dispatch('getmemberCardList', val);
      },
      getUserList(allPageCount) {
        let postObj = Object.assign({}, this.postData);
        if (allPageCount) {
          postObj.page_size = allPageCount;
          postObj.page_no = 1;
        }
        let url = this.curName === '其它门店会员' ? '/Web/MemberList/order_bus_user_list' : '/Web/MemberList/member_list';

        return this.$service.post(url, postObj, { isExport: !!allPageCount }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              if (!allPageCount) {
                this.totalCount = parseInt(data.count);
                this.tableData = data.list;
                //来自顶部搜素且搜索结果唯一
                let searchResultOne = this.curName === 'search' && this.tableData.length === 1;
                if (searchResultOne && this.tableData[0].bus_id && this.tableData[0].bus_id != this.busId) {
                  this.goOtherBus(this.tableData[0]);
                } else if (searchResultOne) {
                  this.$router.push(`/member/detail/${this.tableData[0].user_id}`);
                }
                this.tableData.forEach((user, index) => {
                  if (this.selectedUserIds.indexOf(user.user_id) >= 0) {
                    user._checked = true;
                  }
                });
              }
              return res.data.data.list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            this.$Message.error('服务器连接失败！');
          }
        });
      },
      handlePageChange(postData) {
        const { page_no, page_size, expiration_begin_time, expiration_end_time } = postData;
        if (this.curName !== 'search') {
          this.postData = postData;
        } else {
          this.postData = { ...this.postData, ...{ page_no, page_size } };
        }
        this.dateRange = [expiration_begin_time, expiration_end_time];
        this.$nextTick(() => this.getUserList());
      },
      pageSizeChanged(size) {
        this.postData.page_no = 1;
        this.postData.page_size = size;
        this.getUserList();
      },
      delMember() {
        if (this.selectedMembers.length < 1) {
          this.$Message.error('请先勾选需要删除的会员!');
          return;
        }
        let names = [];
        this.selectedMembers.forEach(item => {
          names.push(item.username);
        });
        this.$Modal.confirm({
          title: '提示',
          content: `您确定要删除已选中的会员"${names.join(',')}"吗？`,
          onOk: () => {
            this.$service
              .post('/Web/Member/deleteUser', {
                user_id: this.selectedUserIds.join(',')
              })
              .then(res => {
                if (res.data.errorcode == 0) {
                  this.actionSuccess();
                  this.$Message.success(res.data.errormsg);
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              });
          },
          onCancel() {
          }
        });
      },
      actionSuccess() {
        this.selectedMembers = [];
        this.getUserList();
      },
      addMember() {
        this.$router.push('/member/add');
      },
      membershipShow(type) {
        if (this.selectedUserIds.length > 0) {
          this[type] = true;
        } else {
          this.$Message.error('请先选择需要指派的会员');
        }
      },
      sendMsg() {
        if (this.selectedUserIds.length > 0) {
          this.$router.push({
            name: '消息推送',
            params: {
              activeIndex: '2',
              selectedMembers: this.selectedMembers
              }
          });
        } else {
          this.$Message.error('请先选择需要发送短信的会员');
        }
      },
      getCurMonthLastDay() {
        let myDate = new Date();
        let year = myDate.getFullYear();
        let month = myDate.getMonth() + 1;
        if (month < 10) {
          month = '0' + month;
        }
        return new Date(year, month, 0);
      },
      getExcelAuth() {
        this.$service.get('/Web/MemberList/member_list_excel').then(res => {
          if (res.data.errorcode === 40014) {
            this.excelAuth = false;
          } else {
            this.excelAuth = true;
          }
        });
      },
      formatKey(item, key, isCardListOut) {
        if (item.card_list) {
          return item.card_list.map(subItem => {
            if (isCardListOut) {
              return item[key] || '';
            } else if (key === 'card_type_id') {
              return subItem.card_type_id == 1
                    ? '期限卡'
                    : subItem.card_type_id == 2
                      ? '次卡'
                      : subItem.card_type_id == 3
                        ? '储值卡'
                        : '私教课';
            } else {
              return subItem[key] || '';
            }
          });
        } else {
          return '';
        }
      },
      otherCase(val) {
        const CASE = {
          '0': () => this.membershipShow('isShowAdd'),
          '1': () => {
            this.isShowExcelModal = true
          },
          '2': () => this.delMember(),
          '3': () => this.membershipShow('followCoachModal'),
          '4': () => this.membershipShow('classCoachModal'),
          '5': () => this.sendMsg(),
          '6': () => this.$router.push('/member/changeCard/0/0')
        };
        CASE[val]();
      },
      async exportTable(val) {
        this.isShowExcelModal = false
        const resData = await this.getUserList(this.totalCount);
        const data = resData.map(item => {
          let itemObj = {}
          Object.keys(item).forEach((kename)=>{
            if (item.card_list) {
              if (kename !== 'card_list') {
                itemObj[kename] = val === 1 ? item[kename] : new Array(item.card_list.length).fill(item[kename])
              }
            } else {
              itemObj[kename] = item[kename]
            }
          })
          itemObj = Object.assign({}, itemObj, {
            sex: item.sex == 1 ? '男' : item.sex == 2 ? '女' : '未知',
            card_type: this.formatKey(item, 'card_type_id'),
            ub_create_time: formatDate(new Date(item.ub_create_time * 1000), 'yy-MM-dd HH:mm'),
            birthday: formatDate(new Date(item.birthday * 1000), 'yy-MM-dd HH:mm'),
            card_name: this.formatKey(item, 'card_name'),
            buy_bus_name: this.formatKey(item, 'buy_bus_name'),
            overplus: this.formatKey(item, 'overplus'),
            allplus: this.formatKey(item, 'allplus'),
            active_time: this.formatKey(item, 'active_time'),
            card_sn: this.formatKey(item, 'card_sn'),
            end_time: this.formatKey(item, 'end_time'),
            status: this.formatKey(item, 'status'),
            usage: this.formatKey(item, 'usage'),
            class_coach_name: this.formatKey(item, 'coach_name')
          });
          return itemObj
        });
        //导出
        this.$refs.export.export({
          columns: this.excelColumns,
          data,
          filename: '会员列表'
        });
      },
      goDetail(params) {
        if (params.row.bus_id && params.row.bus_id != this.busId) {
          this.goOtherBus(params.row);
        } else {
          this.$router.push(`/member/detail/${params.row.user_id}`);
        }
      },
      goOtherBus(info) {
        this.$Modal.confirm({
          title: '提示',
          content: `查看会员信息场馆将切换到${info.belong_bus_name || '会员的归属场馆'}`,
          onOk: () => {
            this.$service
              .post('/Admin/Cutover/ajax_cutover', {
                bus_id: info.bus_id
              })
              .then(res => {
                if (res.status === 200) {
                  if (res.data.status == 1) {
                    this.$store.dispatch('getBusInfo').then(res => {
                      document.title = `${info.belong_bus_name}_${this.$route.name}`;
                    });
                    this.getAdminInfo();
                    this.getReceiptAuth();
                    this.$router.push(`/member/detail/${info.user_id}`);
                  } else {
                    this.$Message.error(res.data.info);
                  }
                } else {
                  this.$Notice.error({
                    title: '场馆切换失败！',
                    desc: res.data.info
                  });
                }
              });
          },
          onCancel() {
          }
        });
      }
    },
    watch: {
      isExportMerge(val) {
        this.exportTable(val)
      },
      searchData(val, oldVal) {
        if (val != oldVal && this.curName === 'search') {
          this.postData = Object.assign({}, this.postData, val);
          this.getUserList();
        }
      },
      'postData.belong_bus_id'(val, oldVal) {
        this.$store.commit('SET_GLOBAL_BELONG_BUS_ID', val);
        if (!val) {
          this.postData.marketers_id = '';
          this.postData.class_coach_id = '';
          this.postData.coach_id = '';
          this.postData.card_id = '';
        }
        this.getCard(val, oldVal);
      }
    }
  };
</script>
<style lang="less">
  .input-before {
    .ivu-select-selection,
    .before-label {
      border-radius: 4px 0 0 4px;
    }

    .before-label {
      display: inline-block;
      height: 32px;
      line-height: 30px;
      border: 1px solid #dddee1;
      vertical-align: middle;
      padding: 0 8px;
    }
  }

  .input-after {
    .ivu-select-selection:first-child,
    .min-input:first-child .ivu-input,
    .ivu-date-picker-editor .ivu-input {
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
  }

  //下拉分组
  .group-select {
    li {
      padding-left: 16px;
    }

    .group-title {
      color: #000;
      padding-left: 8px;
      font-weight: bold;
    }
  }
</style>
<style lang="less" scoped>
  .table-wrap > header > * {
    margin-right: 12px;
  }

  .row-ul {
    li {
      height: 35px;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
    }

    .red {
      color: #d9534f;
    }

    .green {
      color: #5cb85c;
    }
  }

  .input-group {
    display: table;
    border-collapse: separate;
    position: relative;
    font-size: 12px;
    vertical-align: middle;
    max-width: 350px;

    .ivu-select-item {
      padding: 7px;
    }

    .input-before,
    .input-after,
    .min-input {
      display: inline-block;
      vertical-align: middle;
    }

    .min-wrap {
      display: table;
      border-collapse: separate;
      margin-right: 3px;

      span {
        display: table-cell;
        vertical-align: middle;
        line-height: 32px;
        padding: 0 5px;
      }
    }

    .min-input {
      width: 60px;
    }
  }

  .ivu-date-picker {
    width: 240px;
  }

  header {
    .user-search {
      width: 160px;
    }
  }

  footer {
    display: flex;
    justify-content: space-between;
  }
</style>
