<template>
<Modal v-model="showAdd" :mask-closable="false" title="变更柜号">
  <Form ref="lockerForm" :model="changeInfo" class="modal-form" :label-width="80">
    <FormItem label="区域" :rules="{required: true, message: '请选择区域'}" prop="area_id">
        <Select v-if="newAreaList && newAreaList.length" v-model="changeInfo.area_id" @on-change="areaChange" :disabled="smart">
            <Option v-for="item in newAreaList" :value="item.id" :key="item.id">{{item.name}}</Option>
        </Select>
    </FormItem>
    <FormItem label="柜号" prop="locker_id" :rules="{required: true, message: '请选择柜号'}">
        <Select v-model="changeInfo.locker_id">
            <Option v-for="item in numList" :value="item.lockerNo" :key="item.lockerNo">{{item.lockerNo}}</Option>
        </Select>
    </FormItem>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="addLocker">保存</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>

<script>
import EventBus from "utils/eventBus.js";

export default {
  name: "ChangeLocker",
  props: {
    areaList: {
      default: []
    },
    areaId: {
      default: ""
    },
    lockerNo: {
        default: ''
    },
    curDetail: {
      type: Object,
      default: null
    },
    value: {
      type: Boolean
    },
    deviceName: {
      default: ''
    },
    smart: { //智能柜
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showAddLocker: false,
      areaListAndLockers: '',
      numList: [],
      beforeInfo: {
        rent_type: '',
        device_name: '',
        device_id: '',
        locker_id: '',
        area_id: '',
      },
      changeInfo: {
        rent_type: '',
        device_name: '',
        device_id: '',
        locker_id: '',
        area_id: this.areaId,
      }
    };
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    newAreaList: {
      get() {
        return this.areaList.slice(1).filter(item => {
          return this.smart || (item.typeName && item.typeName !== "智能柜");
        });
      },
      set(v) {
        return v;
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.$refs.lockerForm.resetFields()
      }
    }
  },
  created() {
    this.areaChange(this.areaId)
    this.beforeInfo.device_id = this.smart ? this.areaId : '' 
    this.beforeInfo.device_name = this.smart ? this.deviceName : '' 
    this.beforeInfo.rent_type = this.smart ? 1 : 0
    this.beforeInfo.area_id = this.areaId
    this.beforeInfo.locker_id = this.lockerNo
  },
  methods: {
    areaChange(areaId) {
      if (!areaId) {
        return false
      }
        this.$service.get('/Web/LockerRent/getNewLockerRentList', {
          params: {
            areaId: areaId,
            status: '1'
          }
        }).then(res => {
          if(res.data.errorcode == 0) {
            const resArea = res.data.data.area
              this.areaListAndLockers = resArea
              this.numList = resArea[0].items
              // 智能柜时device_id为区域id device_name为区域名称 area_id为空 非智能柜子 device_id为空device_name为空 area_id为区域id
              this.changeInfo.device_id = this.smart ? resArea[0].areaId : '' 
              this.changeInfo.device_name = this.smart ? resArea[0].name : '' 
              this.changeInfo.rent_type = this.smart ? 1 : 0
              this.changeInfo.area_id = resArea[0].areaId
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    addLocker() {
      const curInfo = this.curDetail
      this.$refs.lockerForm.validate(valid => {
        if (valid) {
          this.$service
            .post("/Web/LockerRent/change_locker_rent_num", {
               locker_rent_info: this.beforeInfo,
               change_locker_rent_info: this.changeInfo
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success(res.data.errormsg);
                this.$emit('on-success')
                this.showAdd = false;
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.log(err);
            });
        } else {
          return;
        }
      });
    }
  }
};
</script>

<style scoped>
.locker-num-wrapper .ivu-card-body {
  padding: 0 !important;
}
.locker-num-wrapper {
  height: 100%;
  cursor: pointer;
}
.locker-num-wrapper {
  width: 12.5%;
  height: 100px !important;
  float: left;
  font-size: 12px;
}
@media (max-width: 1400px) {
  .locker-num-wrapper {
    width: 20%;
  }
}
.locker-menu {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.locker-menu-item {
  display: block;
  text-align: center;
}
.unit {
  flex: 1;
  text-align: center;
}
.locker-num {
  margin: 0;
  padding: 0;
  display: block;
  height: 60px;
  line-height: 60px;
  text-align: center;
}
</style>
