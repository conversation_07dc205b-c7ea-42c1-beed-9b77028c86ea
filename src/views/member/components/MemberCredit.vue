<template>
  <div class="">
    <Modal v-model="showAdd" :mask-closable="false" :title="isBackShow?'销账':'挂账列表'">
      <CheckboxGroup v-model="checkedIds" class="model-check-list" v-show="!isBackShow">
          <Checkbox v-for="item in data" :label="item.id" :key="item.id">
            <div class="check-right">
            <div class="top-con">
              <span class="item">{{item.create_time}}</span>
              <span class="item">记录账号 {{item.remind_name}}</span>
            </div>
            <div class="bottom-con">
              {{item.content}}
            </div>
            <div class="bottom-con" v-if="item.remark">
              {{item.remark}}
            </div>
          </div>
          </Checkbox>
      </CheckboxGroup>
      <Form ref="followForm" :model="postData" class="modal-form" :label-width="80" v-if="isBackShow">
        <Form-item label="方式" prop="pay_type" :rules="{required: true, type: 'number', message: '请选择销账方式'}">
          <PayTypeSelect @on-change="payTypeChange"
                          v-model="postData.pay_type"
                          :amount="amount"
                          @on-dragonfly-confirm="onDragonflyConfirm"
                          :clearable="false"
                          :serviceType="1"
                          showCardPay />
        </Form-item>
        <Form-item label="会员卡" prop="card_user_id" :rules="{required: true, message: '请选择会员卡'}" v-if="postData.pay_type==8 && userCardList">
          <Select v-model="postData.card_user_id" placeholder="请选择" @on-change="cardChange">
            <Option :value="item.card_user_id" v-for="item in userCardList" :key="item.card_user_id">{{item.name}}</Option>
          </Select>
          <Checkbox v-if="isShowDiscount" :true-value="1" :false-value="0" v-model="postData.sign_discount" >启用储值卡折扣 {{curCard.sign_discount}}折</Checkbox>
        </Form-item>
          <Form-item v-if="postData.pay_type == 8 && userCardList">
            <Alert type="warning" v-if="userCardList.length == 0">会员暂无可用储值卡</Alert>
            <Alert type="warning" v-if="curCard && curCard.is_balance==0 && postData.sign_discount !== 1">会员卡余额不足</Alert>
            <Alert type="warning" v-if="postData.sign_discount === 1 && curCard && curCard.discount_balance==0">会员卡余额不足</Alert>
          </Form-item>
        <Form-item label="备注" prop="description">
          <Input type="textarea" placeholder="请填写" v-model="postData.remark" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
        </Form-item>
      </Form>
      <div slot="footer" class="modal-buttons" v-show="!isBackShow">
        <Button type="success" @click="clickBack">销账</Button>
        <Button @click="showAdd = false">取消</Button>
      </div>
      <div slot="footer" class="modal-buttons" v-show="isBackShow">
        <Button type="success" @click="doBack">确定</Button>
        <Button @click="cacelBackCredit">取消</Button>
      </div>

    </Modal>
  </div>

</template>
<script>
import PayTypeSelect from 'components/form/PayTypeSelect'
export default {
  name: 'MemberCredit',
  data() {
    return {
      checkedIds: [],
      userCardList: '',
      isBalance: false,
      isBackShow: false,
      curCard: '',
      postData: {
        id: '',
        user_id: '',
        sign_discount: 0,
        pay_order_ids: [],
        pay_type: '',
        remark: '',
        card_user_id: '',
        amount: ''
      }
    };
  },
  components: {
    PayTypeSelect
  },
  props: {
    data: {
      type: [Object, Array],
      required: true
    },
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    isShowDiscount() {
      return this.postData.pay_type == 8 && this.curCard && this.curCard.sign_discount && this.curCard.sign_discount!=='10.0'
    },
    amount() {
      let amount = 0;
      this.data.forEach((item, index) => {
        if (this.checkedIds.indexOf(item.id) >= 0) {
          amount += Number(item.amount);
        }
      });
      return amount.toFixed(2);
    }
  },
  watch: {
    isShowDiscount(val, oldVal) {
      if(!val && oldVal && this.postData.sign_discount) {
        this.postData.sign_discount = 0
      }
    },
    showAdd(val) {
      if (!val) {
        this.checkedIds = [];
      }
    },
    isBackShow(val) {
      if (!val) {
      }
    }
  },
  created() {},
  methods: {
    onDragonflyConfirm(info) {
      this.postData.pay_order_ids = info.pay_order_ids
    },
    cacelBackCredit() {
      this.isBackShow = false;
      this.userCardList = '';
      this.postData.pay_type = '';
    },
    clickBack() {
      if (this.checkedIds.length === 0) {
        return false;
      }
      this.isBackShow = true;
    },
    payTypeChange(val) {
      if (val == 8) {
        this.getUserCardList();
      }
    },
    cardChange(val) {
      if(!val) {
        this.curCard = null
      }
      for (const iterator of this.userCardList) {
        if(val === iterator.card_user_id) {
          this.curCard = iterator
        }
      }
    },
    getUserCardList() {
      this.$service
        .post('/Web/Remind/get_stored_card', {
          id: this.checkedIds.join(','),
          user_id: this.userId,
          amount: this.amount
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.userCardList = res.data.data;
            if (res.data.data.length > 0) {
              this.postData.card_user_id = res.data.data[0].card_user_id;
              this.cardChange(res.data.data[0].card_user_id)
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          this.$Message.error(err);
        });
    },
    doBack() {
      this.$refs.followForm.validate(valid => {
        if (valid) {
          this.postData.id = this.checkedIds.join(',');
          this.postData.user_id = this.userId;
          this.postData.amount = this.amount;
          this.$service
            .post('/Web/Remind/del_remind', this.postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.cacelBackCredit();
                this.showAdd = false;
                // this.$Message.success(res.data.errormsg);
                this.$emit('on-success')
                this.$emit('on-printinfo',this.postData.user_id,res.data.order_sn,'backpay');
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              this.$Message.error(err);
            });
        }
      })
    }
  }
};
</script>

<style lang="less" scoped>
.modal-form .ivu-form-item {
  margin-bottom: 24px;
}
</style>
