<template>
  <div>
    <Form-item v-if="cardType==2" :label-width="160">
      <Select v-model="postDataArr[0].bus_id"
              v-if="!addType"
              clearable
              multiple
              @change="getSelectedBusIds"
              filterable>
        <Option v-for="bus in busList"
                :key="bus.id"
                :disabled="selectedBusIds.indexOf(bus.id) !== -1"
                :value="bus.id">{{bus.name}}</Option>
      </Select>
      <Select v-model="postDataArr[0].level_id"
              clearable
              multiple
              @change="getSelectedBusIds"
              filterable
              v-else>
        <Option v-for="group in groupList"
                :key="group.level_id"
                :disabled="selectedBusIds.indexOf(group.level_id) !== -1"
                :value="group.level_id">{{group.level_name}}</Option>
      </Select>
      </Form-item>
      <Card :class="isCustomize?'mgb15':'bus-card'"
            dis-hover
            v-for="(postData, index) in postDataArr"
            :key="index"
            v-else>
        <p slot="title">{{addType?postDataArr[index].level_name:postDataArr[index].bus_name}}</p>
        <a href="#"
           slot="extra"
           @click.prevent="handleBusDel(index)">
          <Button type="text"
                  style="color: #ed3f14">删除场馆</Button>
        </a>
        <slot v-if="isCustomize" name="customize" :storeData="postData"></slot>
        <Form v-else :ref="'busSetForm'+index"
              :label-width="140">
          <Form-item label="会员手机端购买"
                     prop="status"
                     v-if="!addType"
                     v-show="cardType != 2">
            <Radio-group v-model="postDataArr[index].status">
              <Radio :label="1">支持</Radio>
              <Radio :label="0">不支持</Radio>
            </Radio-group>
          </Form-item>
          <Form-item label="到店激活限制" prop="activation_restriction" v-if="cardType != 2 && ((!addType && postDataArr[index].status == 1) || addType)">
            <Select class="w120" v-model="postDataArr[index].activation_restriction">
              <Option v-for="(item, index) in activationRestrictionList" :value="item.value" :key="index">{{ item.name }}</Option>
            </Select>
            <span class="rig-gray">该限制只适用于勤鸟+授权的会员端小程序</span>
          </Form-item>
          <!-- <Form-item label="支付宝小程序购买"
                     prop="alipay_sale_status"
                     v-if="!addType"
                     v-show="cardThreeType == 1 && subCardType == 2">
            <Radio-group v-model="postDataArr[index].alipay_sale_status" @on-change="alipaySaleChange(index)">
              <Radio :label="1">支持</Radio>
              <Radio :label="0">不支持</Radio>
            </Radio-group>
          </Form-item> -->
          <Form-item label="售价"
                     prop="current_price"
                     v-show="cardType != 2 && subCardType != 4 && subCardType != 5">
            <Input-number v-model="postDataArr[index].current_price"
                          :min="0"
                          :step="0.01"
                          :disabled="postDataArr[index].currentPriceFlag"
                          @on-change="() => updateCardRuleRange(false)"/>
            <Checkbox @on-change="handlePriceToFace(index)"
                      v-model="postDataArr[index].currentPriceFlag"
                      v-if="postDataArr[index].alipay_sale_status!=1"
                      style="margin-left: 20px;"
                      v-show="cardType != 2 && subCardType!=7">价格面议
            </Checkbox>
          </Form-item>
          <Form-item prop="sign_discount"
                     v-show="subCardType == 3">
            <div slot="label">
              签到扣费折扣
              <Tooltip content="用该储值卡种进行到馆签到可享受到的折扣">
                <Icon type="ios-help-circle"></Icon>
              </Tooltip>
            </div>
            <div class="across-row">
              <Input-number v-model="postDataArr[index].sign_discount"
                            :min="0.1"
                            :max="10.0"
                            :step="0.1" />
              <Alert type="warning"
                     style="margin-left: 20px;">请输入折扣值 0.1~10.0，10表示不打折</Alert>
            </div>
          </Form-item>
          <!-- <Form-item prop="reserve-booking_discount"
                     v-show="subCardType == 3">
            <div slot="label">
              订场扣费折扣
              <Tooltip content="用该储值卡种进行订场可享受的折扣">
                <Icon type="ios-help-circle"></Icon>
              </Tooltip>
            </div>
            <div class="across-row">
              <Input-number v-model="postDataArr[index].booking_discount"
                            :min="0.1"
                            :max="10.0"
                            :step="0.1" />
              <Alert type="warning"
                     style="margin-left: 20px;">请输入折扣值 0.1~10.0，10表示不打折</Alert>
            </div>
          </Form-item> -->
          <FormItem label="时段限制"
                    v-show="(subCardType == 1 || subCardType == 2) && cardType != 2">

            <div v-for="(item, i) in postDataArr[index].weekTimes"
                 :key="i"
                 class="flex-time">
              <CheckboxGroup v-model="item.weeks">
                <checkbox v-for="index in ['0','1','2','3','4','5','6']"
                          :key="index"
                          :label="index">周{{dict[index]}}</checkbox>
              </CheckboxGroup>
              开始时间
              <TimePicker v-model="item.start_time"
                          format="HH:mm"
                          style="width: 80px" /> ~ 截止时间
              <TimePicker v-model="item.end_time"
                          format="HH:mm"
                          style="width: 80px" />
              <Button type="text"
                      style="color: #ed3f14"
                      @click="postDataArr[index].weekTimes.splice(i, 1)">删除</Button>
            </div>
            <Button @click="confirmAddTimeLimit(index)"
                    type="text"
                    size="small">添加
            </Button>
          </FormItem>

          <FormItem label="单节售价（元）"
                    prop="single_price"
                    v-show="subCardType == 4 || subCardType == 5">
            <InputNumber v-model="postDataArr[index].single_price"
                         :min="0"
                         :step="0.01"
                         @on-change="() => updateCardRuleRange(false)"/>
          </FormItem>
          <FormItem :label="subCardType == 7?'赠送天数':'赠送节数（节）'"
                    prop="gift_number"
                    v-show="subCardType == 4 || subCardType == 5 || subCardType == 7">
            <InputNumber v-model="postDataArr[index].gift_number"
                         :min="0"
                         :step="1"
                         @on-change="() => updateCardRuleRange(false)"/>
          </FormItem>
          <FormItem label="起购节数（节）"
                    prop="buy_min_value"
                    v-show="(postDataArr[index].status == 1 || addType) && (subCardType == 4 || subCardType == 5)">
            <InputNumber v-model="postDataArr[index].buy_min_value"
                         :min="1"
                         :step="1" />
          </FormItem>
          <FormItem v-if="cardType != 2" label="购卡规则">
            <div class="card-rules-box">
              <i-switch
                v-model="postDataArr[index].is_open_rule"
                :disabled="cardRuleList.length === 0"
                true-value="1"
                false-value="0"
                @on-change="handleChangeOpenRule($event, index)"/>
              <div class="select-box">
                <Select
                  v-if="cardRuleList.length"
                  style="width:100%;"
                  v-model="postDataArr[index].sale_rule_id"
                  :disabled="postDataArr[index].is_open_rule === '0'"
                  @on-change="getCardRuleRange($event, index)">
                  <Option v-for="(item, index) in cardRuleList" :value="item.id" :key="index">{{ item.rule_name }}</Option>
                </Select>
                <Alert v-else type="warning" >该类型卡暂未添加规则</Alert>
              </div>
            </div>
            <div v-if="cardRuleList.length && range[index]" class="rule-info-box">
              <p>{{ rangelabel.sale + range[index].sale_range }}</p>
              <p v-show="subCardType ==1 || subCardType ==2 || subCardType ==3">{{ rangelabel.buy + range[index].buy_max }}</p>
              <p>{{ rangelabel.gift + range[index].gift_max }}</p>
            </div>
          </FormItem>
        </Form>
      </Card>
      <Form-item v-if="cardType != 2">
        <div>
          <Button type="info"
                  @click="handleBusShow">添加{{addType?'场馆组':'场馆'}}</Button>
        </div>
      </Form-item>

      <Modal v-model="showAddBusModal"
             :title="addType?'添加场馆组':'添加场馆'">
        <Form :labelWidth="10">
          <FormItem label=" ">
            <Select v-model="addBusModalData"
                    v-if="!addType"
                    clearable
                    multiple
                    filterable>
              <Option v-for="bus in busList"
                      :key="bus.id"
                      :disabled="selectedBusIds.indexOf(bus.id) !== -1"
                      :value="bus.id">{{bus.name}}</Option>
            </Select>
            <Select v-model="addBusModalData"
                    clearable
                    multiple
                    filterable
                    v-else>
              <Option v-for="group in groupList"
                      :key="group.level_id"
                      :disabled="selectedBusIds.indexOf(group.level_id) !== -1"
                      :value="group.level_id">{{group.level_name}}</Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer"
             class="modal-buttons">
          <Button type="success"
                  @click="confirmAddBus">确定</Button>
          <Button @click="cancelAddBus">取消</Button>
        </div>
      </Modal>
  </div>
</template>
<script>
import { mapState } from 'vuex';

let updateRangeTimer = null; // 用于节流更新购卡规则范围

export default {
  name: 'busCardSet',
  props: {
    addType: {
      type: Number,
      default: 0
    },
    cardType: {
      type: [String, Number],
      default: '0'
    },
    subCardType: {
      type: [String, Number],
      default: '1'
    },
    value: {
    },
    busList: {
      type: [Array],
      default: () => []
    },
    groupList: {
      type: [Array],
      default: () => []
    },
    isCustomize: {
      type: Boolean,
      defualt: false
    },
    cardRuleList: {
      type: Array,
      default: () => []
    },
    formValidate: {
      type: Object,
      default: () => ({})
    },
    cardId: {
      type: [String, Number],
      default: 0
    },
  },
  data () {
    return {
      cardThreeType: 1,
      activationRestrictionList: [{
        value: 0,
        name: '无限制'
      },{
        value: 7,
        name: '7天后自动激活'
      },{
        value: 15,
        name: '15天后自动激活'
      },{
        value: 30,
        name: '30天后自动激活'
      },{
        value: 45,
        name: '45天后自动激活'
      },{
        value: 60,
        name: '2个月(60)天后自动激活'
      },{
        value: 90,
        name: '3个月(90)天后自动激活'
      },{
        value: 120,
        name: '4个月(120)天后自动激活'
      },{
        value: 150,
        name: '5个月(150)天后自动激活'
      },{
        value: 180,
        name: '6个月(180)天后自动激活'
      },{
        value: 210,
        name: '7个月(210)天后自动激活'
      },{
        value: 240,
        name: '8个月(240)天后自动激活'
      },{
        value: 270,
        name: '9个月(270)天后自动激活'
      },{
        value: 300,
        name: '10个月(300)天后自动激活'
      },{
        value: 330,
        name: '11个月(330)天后自动激活'
      },{
        value: 360,
        name: '12个月(360)天后自动激活'
      }],
      dict: {
        0: '日',
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六'
      },
      enableTimeLimit: false,
      timeLimits: [],
      timeLimitIndex: 0,
      timeLimitModalData: {
        weeks: [],
        begin_time: '',
        end_time: ''
      },
      selectedBusIds: [],
      addBusModalData: [],
      showAddBusModal: false,
      range:[
        /* {
          sale_range: "",
          buy_max: "",
          gift_max: "",
        } */
      ]
    };
  },
  computed: {
    ...mapState(['busId', 'busName']),
    postDataArr: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val)
      }
    },
    // 获取购卡规则范围的部分传参
    number() {
      const { end_time, number, gift_number } = this.formValidate;
      return { end_time, number, gift_number }
    },
    // 购卡规则范围标签
    rangelabel() {
      const { subCardType } = this;
      let sale = '售价';
      let buy = '购买天数';
      let gift = '赠送天数';
      switch (+subCardType) {
        case 2:
          buy = '购买次数';
          gift = '赠送次数';
          break;
        case 3:
          buy = '价值金额';
          gift = '赠送金额';
          break;
        case 4:
        case 5:
          sale = '单节售价';
          gift = '赠送节数';
          break;
      }
      return {
        sale: sale + '可调整范围：',
        buy: buy + '可调整上限：',
        gift: gift + '可调整上限：'
       }
    },
  },

  created() {
    const unwatch = this.$watch('cardRuleList', function() {
      // 如果初始渲染时有购卡规则，则获取到对应规则范围并显示
      this.updateCardRuleRange()
      unwatch()
    });
    this.cardThreeType = parseInt(this.$route.params.cardThreeType);
  },

  watch: {
    // 修改获取购卡规则范围的部分传参后，重置选择的购卡规则
    number(val) {
      const { subCardType: type } = this;
      if([1, 2, 3].includes(+type)) {
        this.updateCardRuleRange(false /* loading */)
      }
    },
  },

  methods: {
    // 获取选择的购卡规则范围
    getCardRuleRange(id, index, loading = true) {
      if(!id) return this.range[index] = null;
      const { end_time, number } = this.formValidate;
      const { single_price, current_price, gift_number } = this.postDataArr[index];
      // 默认1 期限卡的传值
      const params = {
        rule_id:     id, // 规则id
        sale_amount: current_price, // 售价金额
        buy_num:     end_time, // 购买量
        gift_num:    this.formValidate.gift_number, // 赠送量
      };
      switch (+this.subCardType) {
        case 2:
        case 3:
          params.buy_num = number;
          break;
        case 4:
        case 5:
          params.sale_amount = single_price;
          params.gift_num = gift_number;
          delete params.buy_num;
          break;
        case 7:
          params.gift_num = gift_number;
          delete params.buy_num;
          break;
      }

      this.$service.post('/Web/CardSaleRule/getRange', params, { loading }).then(res => {
        if (res.data.errorcode == 0) {
          this.$set(this.range, index, res.data.data)
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    updateCardRuleRange(loading) {
      clearTimeout(updateRangeTimer)

      updateRangeTimer = setTimeout(() => {
        this.postDataArr.forEach((v, i) => {
          if(v.is_open_rule === '1' && v.sale_rule_id) {
            this.getCardRuleRange(v.sale_rule_id, i, loading)
          }
        })
      }, 1200);
    },

    getSelectedBusIds() {
      let ids = []
      for (const item of this.postDataArr) {
        ids.push(this.addType ? item.level_id : item.bus_id)
      }
      this.selectedBusIds = ids
    },
    getInfoById(id) {
      if (!this.addType) {
        for (const item of this.busList) {
          if (id == (item.id)) {
            return {
              bus_id: id,
              bus_name: item.name
            }
          }
        }
        return {
          bus_id: '',
          bus_name: ''
        }
      } else {
        for (const item of this.groupList) {
          if (id == (item.level_id)) {
            return {
              level_id: id,
              level_name: item.level_name
            }
          }
        }
        return {
          level_id: '',
          level_name: ''
        }
      }
    },
    confirmAddBus () {
      if (Array.isArray(this.addBusModalData) && this.addBusModalData.length) {
        let curIndexArr = { ...this.postDataArr[this.showAddBusIndex] };
        if(curIndexArr.sale_rule_id !== undefined) curIndexArr.sale_rule_id = null;
        if(curIndexArr.is_open_rule !== undefined) curIndexArr.is_open_rule = '0';
        this.addBusModalData.forEach(busId => {
          let data = JSON.parse(JSON.stringify({
            ...curIndexArr,
            ...this.getInfoById(busId)
          }))
          this.postDataArr.push(data)
        });
        this.showAddBusModal = false
      }
    },
    cancelAddBus () {
      this.addBusModalData = []
      this.showAddBusModal = false
    },
    handleBusShow () {
      this.showAddBusIndex = this.postDataArr.length - 1
      this.addBusModalData = []
      this.getSelectedBusIds();
      this.showAddBusModal = true
    },
    handleBusDel (index) {
      if (this.postDataArr && this.postDataArr.length === 1) {
        this.$Message.error('至少需要保留一个归属场馆');
        return false;
      }
      this.postDataArr.splice(index, 1);
    },
    dealWeek (week = []) {
      if (!week.length || !Array.isArray(week)) return '';
      const sorted = [...week].sort();
      return sorted.map(index => `周${this.dict[index]}`).join('、');
    },
    confirmAddTimeLimit (index) {
      this.postDataArr[index].weekTimes.push({
        weeks: [],
        begin_time: '',
        end_time: ''
      })
    },
    initTimeLimit () {
      return {
        weeks: [],
        start_time: '',
        end_time: ''
      };
    },
    alipaySaleChange(index) {
      if (this.postDataArr[index].alipay_sale_status == 1) {
        this.postDataArr[index].currentPriceFlag = false;
      }
    },
    handlePriceToFace (index) {
      if (this.postDataArr[index].currentPriceFlag) {
        this.postDataArr[index].current_price = 0;
      }
    },
    handleChangeOpenRule(val, index) {
      if(val === '0') {
        this.postDataArr[index].sale_rule_id = null;
      }
    }
  },
};
</script>
<style lang="less" scoped>
.across-row {
  display: flex;
  flex-direction: row;
}
.flex-time {
  display: flex;
  flex-wrap: wrap;
}
.bodybuilding {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .w-red {
    font-size: 16px;
    color: red;
  }

  .w-blue {
    font-size: 16px;
    color: blue;
  }

  .word {
    height: 169px;
    width: 242px;
  }
}
.bus-card {
  margin: 0 0 15px 140px;
  width: 60%;

  .card-rules-box {
    display: flex;
    align-items: center;
    width: 60%;
    height: 34px;
    .select-box {
      margin-left: 20px;
      flex: 1;
      .ivu-alert-warning {
        margin-bottom: 0;
        padding-right: 16px;
      }
    }
  }
  .rule-info-box {
    margin-top: 18px;
    padding: 2px 16px;
    width: 60%;
    border: 1px solid #dcdee2;
    border-color: #e8eaec;
  }
}

.rig-gray {
  color: gray;
  margin-left: 10px;
}
.mgb15 {
  margin-bottom: 15px;
}
@media screen and (max-width: 1440px) {
  width: 80%;
}
</style>
