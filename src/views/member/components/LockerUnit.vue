<template>
  <Card
    class="locker-num-wrapper"
    :class="menuStyle"
    @mouseenter.native="handleClickCard"
    @mouseleave.native="handleClickCard"
  >
    <div
      v-if="curDetail.status == 0 || curDetail.status == 1"
      v-show="!showMenu"
      class="title"
    >
      <span style="text-align:left;">{{ curDetail.userInfo.username || '无名' }}</span>
      <span style="text-align:right;">{{ curDetail.rentEndTime || '无日期' }}</span>
    </div>
    <div v-if="curDetail.deviceStatus == 1" class="title">
      <span style="text-align:left;color:rgba(255, 153, 51, 1)">损坏</span>
    </div>
    <div>
      <h3
        v-show="!showMenu"
        class="locker-num"
      >
        {{ curDetail.lockerNo }}
      </h3>
    </div>
    <div v-if="showMenu" class="locker-menu">
      <div
        v-if="curDetail.deviceStatus != 1 && curDetail.deviceStatus != 2"
        class="unit"
        style="opacity:1;"
        @click="showRentModal">
        <Icon type="md-exit" size="24" />
        <span class="locker-menu-item">{{ curDetail.status == 2 ? "出租" : "续租" }}</span>
      </div>
      <div
        v-if="curDetail.status == 2 && curDetail.deviceStatus == 0"
        class="unit"
        @click.prevent="showDamageModal"
      >
        <Icon type="ios-build" size="24" />
        <span class="locker-menu-item">损坏</span>
      </div>
      <div v-if="curDetail.status != 2" class="unit" @click.prevent="showMemberModal">
        <Icon type="ios-people" size="24" />
        <span class="locker-menu-item">成员</span>
      </div>
      <div
        v-if="curDetail.deviceStatus == 2"
        class="unit"
        @click="handleSureClean">
        <img src="https://imagecdn.rocketbird.cn/mainsite-fe/finish-clean.png" alt="完成清洁" />
        <span class="locker-menu-item">完成清洁</span>
      </div>
      <div
        v-if="curDetail.status == 2 && curDetail.deviceStatus == 1"
        class="unit"
        @click.prevent="showRepairModal"
      >
        <Icon type="md-checkmark-circle" size="24" />
        <span class="locker-menu-item">恢复正常</span>
      </div>
      <div
        v-if="curDetail.status == 2 && !smart"
        class="unit"
        @click.prevent="showDeleteModal"
      >
        <Icon type="ios-trash" size="24" />
        <span class="locker-menu-item">删除</span>
      </div>
      <div v-if="curDetail.status != 2" class="unit" @click.prevent="showReturnModal">
        <Icon type="md-exit" size="24" />
        <span class="locker-menu-item">退柜</span>
      </div>
      <div
        v-if="curDetail.status == 0"
        class="unit"
        @click.prevent="showChangeModal"
      >
        <Icon type="ios-swap" size="24" />
        <span class="locker-menu-item">变更</span>
      </div>
    </div>
    <Modal
      v-model="showMember"
      width="450"
      title="成员"
      :mask-closable="false">
      <Card>
        <p slot="title">可使用成员</p>
        <div v-for="item in memberList" :key="item.user_id" class="member-card member-list">
          <Avatar icon="md-person" size="large" :src="item.avatar" />
          <label class="member-label" :title="item.username">{{ item.user_name }}</label>
          <label class="member-label">({{ item.phone }})</label>
          <Icon
            v-if="!item.is_own"
            type="md-trash"
            size="18"
            title="删除"
            class="member-delete"
            @click.native="handleDeleteMember(item.user_id)"
          ></Icon>
        </div>
      </Card>
      <div v-if="isAddOldMember" class="member-old-form">
        <Select
          v-model="searchUserId"
          filterable
          remote
          :remote-method="handleAddOldMemberRemote"
          :loading="isRemoteLoading"
          placeholder="姓名/电话/卡号"
          style="width:300px"
          @on-change="handleAddMemberSubmit()"
        >
          <Option
            v-for="item in searchUserList"
            :key="item.user_id"
            :value="item.user_id"
          >
            {{ item.username }}
          </Option>
        </Select>
        <Button type="text" @click="isAddOldMember=false">取消添加</Button>
      </div>
      <div v-if="!isAddOldMember" class="member-controller">
        <Button @click="isAddOldMember=true">添加成员</Button>
      </div>
      <div slot="footer"></div>
    </Modal>
    <deduct
      v-if="renderReturnModal"
      v-model="showReturn"
      :user-id="detail.userId"
      :deCash="deCash"
      :status="curDetail.status"
      :value="true"
      @on-success="updateAfterOperate"
      @on-printinfo="depositFinish"
    />
    <AddLocker
      v-if="showAddLockerRender"
      v-model="showAddLocker"
      :user-id="curDetail.status == 2 ? $route.params.userId : curDetail.userId"
      :actionType="'newSys'"
      :lockerNo="curDetail.lockerNo"
      :isContinue="curDetail.status == 2 ? false : true"
      :smart="smart"
      :areaId="areaId"
      :deviceName="deviceName"
      :endDate="endTime"
      @on-success="updateAfterOperate"
      @on-printinfo="contractComplete"
    />
    <ChangeLocker
      v-if="showChangeLocerRender"
      v-model="showChangeLocer"
      :areaId="areaId"
      :lockerNo="curDetail.lockerNo"
      :areaList="areaList"
      :curDetail="curDetail"
      :smart="smart"
      :deviceName="deviceName"
      @on-success="updateAfterOperate"
    />
    <receipt-modal v-model="showPrint" :to-path="toPath" />
  </Card>
</template>

<script>
import receipt from "mixins/receipt.js";
import receiptModal from "components/receipt/receipt.vue";
import AddLocker from "./AddLocker";
import ChangeLocker from "./ChangeLocker";
import deduct from "./deductCash";
import EventBus from "utils/eventBus.js";
import { formatDate } from "utils/index";
import { isChinese } from "utils/index";

export default {
  name: "TheLocker",
  components: {
    AddLocker,
    ChangeLocker,
    receiptModal,
    deduct
  },
  mixins: [receipt],
  props: {
    areaList: {
      default: []
    },
    detail: {
      default: ""
    },
    areaId: {
      default: ""
    },
    smart: {
      default: false
    },
    deviceName: {
      default: ""
    }
  },
  data() {
    return {
      showReturn: false,
      showChangeLocer: false,
      showChangeLocerRender: false,
      renderReturnModal: false,
      showRepair: false,
      showMenu: false,
      showMember: false,
      memberList: [],
      isAddOldMember: false,
      searchUserList: [],
      showAddLocker: false,
      showAddLockerRender: false,
      isRemoteLoading: false,
      showDamage: false,
      showDelete: false,
      infoRemind: "",
      searchUserId: "" //添加成员
    };
  },
  computed: {
    endTime() {
      return this.curDetail.status == 1 || this.curDetail.status == 0
        ? this.curDetail.rentEndTime
        : formatDate(new Date(Date.now()), "yyyy-MM-dd");
    },
    menuStyle() {
      const { status, deviceStatus } = this.curDetail
      // deviceStatus 1 损坏 2待清洁
      if (this.showMenu) {
        if (deviceStatus == 2) {
          return "menu-clean";
        }
        if (deviceStatus == 1) {
          return "menu-damaged";
        }
        if (status == 0 && deviceStatus !== 1) {
          return "menu-rented";
        }
        if (status == 1 && deviceStatus != 1) {
          return "menu-expired";
        }
      } else {
        if (deviceStatus == 2) {
          return "locker-num-wrapper-clean";
        }
        if (deviceStatus == 1) {
          return "locker-num-wrapper-damaged";
        }
        if (status == 0 && deviceStatus !== 1) {
          return "locker-num-wrapper-rented";
        }
        if (status == 1 && deviceStatus != 1) {
          return "locker-num-wrapper-expired";
        }
      }
      return '';
    },
    curDetail: {
      set(v) {
        return v;
      },
      get() {
        return this.detail;
      }
    },
    deCash() {
      return { lockerId: this.curDetail.id, formtype: "", flag: "newSys" };
    }
  },
  methods: {
    updateAfterOperate() {
      EventBus.$emit("success");
    },
    showReturnModal() {
      this.showReturn = true;
      this.renderReturnModal = true;
    },
    showChangeModal() {
      this.showChangeLocer = true;
      this.showChangeLocerRender = true;
    },
    handleClickCard() {
      this.showMenu = !this.showMenu;
    },
    showRepairModal() {
      this.$Modal.confirm({
        title: "柜子已经修复？",
        onOk: () => {
          this.damage("repair");
        }
      });
    },
    showRentModal() {
      this.showAddLocker = true;
      this.showAddLockerRender = true;
    },
    showDamageModal() {
      this.$Modal.confirm({
        title: "柜子发生故障需要暂停使用么？",
        loading: true,
        onOk: async () => {
          await this.damage();
          this.$Modal.remove();
        }
      });
    },
    async showMemberModal() {
      await this.getMemberList();
      this.showMember = true;
    },
    showDeleteModal() {
      this.$Modal.confirm({
        title: "确定要删除柜子？",
        loading: true,
        onOk: async () => {
          await this.deleteLocker();
          this.$Modal.remove();
        }
      });
    },
    returnLocker() {
      this.$service
        .post("/Web/LockerRent/deleted_lockerrent", {
          lockerrent_id: this.curDetail.id,
          user_id: this.curDetail.userId
        })
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg);
              EventBus.$emit("success");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            this.$Message.error("服务器扑街！");
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    damage(flag) {
      let postData = Object.create(null);
      postData.deviceType = this.smart ? 1 : 2;
      postData.areaId = this.areaId;
      postData.lockerNo = this.curDetail.lockerNo;
      postData.changeType = flag == "repair" ? 0 : 1;
      this.$service
        .post("/Web/LockerRent/postSignFreeze", postData)
        .then(res => {
          if (res.status == 200) {
            if (res.data.errorcode == 0) {
              let tips =
                flag == "repair"
                  ? "这个柜子已经恢复！"
                  : "这个柜子损坏标记成功！";
              this.$Message.success(tips);
              EventBus.$emit("success");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            this.$Message.error("服务器扑街！");
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    deleteLocker() {
      this.$service
        .post("/Web/LockerRent/postDeleteLocker", {
          lockerRentId: this.curDetail.id
        })
        .then(res => {
          if (res.status == 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success("这个柜子删除成功！");
              EventBus.$emit("success");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            this.$Message.error("服务器扑街！");
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getMemberList() {
      return this.$service
        .post("/Web/LockerRent/multimember_rent_list", {
          lockerrent_id: this.curDetail.id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            let users = res.data.data;
            if (Array.isArray(users)) {
              this.memberList = users;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleDeleteMember(id) {
      return this.$service
        .post("/Web/LockerRent/multi_delete_member", {
          user_id: id,
          lockerrent_id: this.curDetail.id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.getMemberList();
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleAddOldMemberRemote(query) {
      if (query.length === 0) {
        this.searchUserList = [];
        return false;
      }

      // if numbers or letters must be more than 3 in length you can request
      if (!isChinese(query)) {
        return;
      }

      this.isRemoteLoading = true;
      this.$service
        .post("/Web/FrontMoney/search_all_user", {
          search: query.trim()
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (Array.isArray(res.data.data.list)) {
              this.searchUserList = res.data.data.list;
              this.isRemoteLoading = false;
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleAddMemberSubmit() {
      return this.$service
        .post("/Web/LockerRent/multi_add_member", {
          user_id: this.searchUserId,
          lockerrent_id: this.curDetail.id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.getMemberList();
            this.searchUserId = "";
            this.isAddOldMember = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    // 完成清洁-确认弹窗
    handleSureClean() {
      this.$Modal.confirm({
        title: "确认已完成清洁？",
        onOk: () => {
          this.handleCompleteClean();
        }
      });
    },
    // 变更清洁状态为空闲
    handleCompleteClean() {
      const url = "/Web/LockerRent/postCompleteClean"
      const params = {
        lockerNo: this.curDetail.lockerNo,
        deviceId: this.areaId // 设备id 在储物柜出租页面叫areaId
      }
      return this.$service.post(url, params).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg);
          EventBus.$emit("success");
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    }
  }
};
</script>

<style scoped lang="less">
.ivu-card-body {
  padding: 0px;
}
.member-controller {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin-top: 22px;
}
.question {
  color: whitesmoke;
  height: 40px;
  width: 40px;
  display: inline-block;
  border-radius: 100%;
  background: gold;
  text-align: center;
  line-height: 40px;
  margin-right: 10px;
}
.locker-num-wrapper {
  padding: 0 !important;
  overflow: hidden;
  .ivu-card-body {
    padding: 0 !important;
  }
}
.locker-num-wrapper > div {
  padding: 0 !important;
}
.smart-locker {
  display: inline-block;
  background: blue;
  color: whitesmoke;
}
.locker-num-wrapper {
  margin: 1px;
  box-sizing: border-box;
  width: 12.3%;
  height: 100px !important;
  float: left;
  height: 100%;
  cursor: pointer;
  background-color: #f8f9fb;
}
@media (max-width: 1500px) {
  .locker-num-wrapper {
    width: 20%;
  }
}
.locker-num-wrapper-rented {
  border: 2px solid rgba(0, 204, 0, 1);
  // background-color: rgba(0, 204, 0, 1);
  // opacity: .15;
}
.locker-num {
  margin: 0;
  padding: 0;
  font-size: 20px;
  display: block;
  height: 60px;
  line-height: 60px;
  text-align: center;
}
.locker-num-damaged {
  color: rgba(255, 153, 51, 1);
}
.locker-num-wrapper-damaged {
  border: 2px solid rgba(255, 153, 51, 1);
}
.locker-num-wrapper-expired {
  border: 2px solid #fe4242;
}
.locker-num-wrapper-clean {
  border: 2px solid #03B6FF;
}
.locker-menu {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.locker-menu-item {
  display: block;
  text-align: center;
}
.unit {
  flex: 1;
  text-align: center;
}
.member-card {
  display: flex;
  align-items: center;
  width: 358px;
  margin-bottom: 15px;
}
.title {
  width: 100%;
  display: flex;
  span {
    padding: 0;
    flex: 1;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.member-list {
  margin-bottom: 15px;
  &:last-child {
    margin-bottom: 0;
  }
  .member-delete {
    margin-left: 15px;
    color: #666;
    cursor: pointer;
  }
}
.locker-num-damaged {
  color: rgba(255, 153, 51, 1);
}
.locker-num-expired {
  color: red;
}
.locker-num-rented {
  color: rgba(0, 204, 0, 1);
}
.menu-damaged {
  background: #f9efe6;
}
.menu-expired {
  background-color: #f9e1e1;
}
.menu-rented {
  background-color: #dff4e1;
}
.menu-clean {
  background-color: #D3F2FF;
}
</style>
