<template>
        <div class="zone">
            <h3 class="zone-title">{{title}}<span class="smart" v-if="smart">智能</span>
                <span v-if="!smart" style="float:right;margin-right:20px;display: inline-block;height: 30px;">
                    <Button type="text" @click="showRename = true">区域更名</Button>
                    <Button type="text" @click="checkDeleteArea" style="margin-left:20px;color: red;">区域删除</Button>
                </span>
                 <span v-else style="float:right;margin-right:20px;display: inline-block;height: 30px;">
                    <Button type="text" @click="showMigration = true">租柜迁移</Button>
                </span>
            </h3>
            <locker
                :smart="smart"
                v-for="(item, index) in lockerList"
                :detail="item"
                :areaId="areaId"
                :areaList="areaList"
                :key="index"
                :deviceName="title"
             />
            <add-unit v-if="!smart" :smart="smart" :areaId="areaId" :areaList="areaList"></add-unit>
            <Modal title="区域更名" v-model="showRename" @on-visible-change="getFocus" :mask-closable="false">
                <Form>
                    <FormItem label="更名">
                        <Input
                            ref="renameInput"
                            placeholder="请输入新名称"
                            v-model="rename"
                            type="text"
                            style="width:90%;float:right;" />
                    </FormItem>
                </Form>
                <div slot="footer"
                        class="modal-buttons">
                    <Button type="success"
                        @click="renameArea">确定</Button>
                    <Button
                        @click="showRename = false">取消</Button>
                </div>
            </Modal>
            <Modal title="租柜迁移" v-model="showMigration" :mask-closable="false">
                <Form
                    ref="migrationRef"
                    :model="migrationForm"
                    :label-width="80"
                >
                    <FormItem label="迁移柜控" required>
                        <Input v-model="title" disabled />
                    </FormItem>
                    <FormItem label="目标柜控" prop="areaId" :rules="{required:true,message:'请选择目标柜控',trigger:'change'}">
                        <Select
                            v-model="migrationForm.areaId"
                            placeholder="请选择目标柜控">
                            <Option v-for="item in smartAreaOptions" :value="item.id" :key="item.id">{{item.name}}</Option>
                        </Select>
                    </FormItem>
                </Form>
                <div slot="footer" class="modal-buttons">
                    <Button type="success" @click="handleMigrationArea">确定</Button>
                    <Button @click="showMigration=false">取消</Button>
                </div>
            </Modal>
        </div>
</template>

<script>
import locker from './LockerUnit'
import addUnit from './LockerUnitAdd'
import EventBus from "utils/eventBus.js"; //有很多操作用了这个Bus

    export default {
        name: "lockerArea",
        components: {
            locker,
            addUnit
         },
        props: {
            "title": {
                default: ''
            },
            "areaId": {
                default: ''
            },
            "lockerList": {
                default: []
            },
            "areaList": {
                default: []
            },
            smart: { //智能柜
                type: Boolean,
                default: false
            },
        },
        data() {
            return {
                showRename: false,
                showMigration: false, // 租柜迁移弹窗
                rename: this.title,
                smartAreaOptions: [], // 智能租柜列表
                migrationForm: {
                  areaId: '', // 迁移目标柜控id
                },
            }
        },

        watch: {
            showMigration(val) {
                if(val) {
                    const fn = ({ id, typeName }) => ![this.areaId, '0'].includes(id) && typeName === '智能柜'
                    this.smartAreaOptions = this.areaList.filter(fn)
                }else {
                    this.migrationForm.areaId = ''
                    this.$refs['migrationRef'].resetFields()
                }
            }
        },

        methods: {
            getFocus(val) {
                setTimeout(() => {
                    if(val) {
                        this.$refs.renameInput.focus()
                    }
                })
            },
            checkDeleteArea() {
                let bool = this.lockerList.every(item => {
                  return item.status == 2
                })
                if(bool) {
                    this.$Modal.confirm({
                        title: "区域删除",
                        content: "您确定要删除这个区域吗？",
                        onOk: this.deleteArea
                    })
                } else {
                    this.$Message.error("请将全部柜子进行退柜操作后再删除！")
                }
            },
            renameArea() {
                this.$service.post('/Web/LockerRent/postUpdateArea', {
                    areaId: this.areaId,
                    name: this.rename
                }).then(res => {
                    if(res.status === 200) {
                        if(res.data.errorcode == 0) {
                            this.$Message.success("该区域更名成功！")
                            this.showRename = false;
                            EventBus.$emit("success")
                        }
                    } else {
                        console.error("服务器扑街！")
                    }
                }).catch(err => {
                    console.log(err);
                });
            },
            deleteArea() {
                this.$service.post('/Web/LockerRent/postDeleteArea', {
                    areaId: this.areaId
                }).then(res => {
                    if(res.status === 200) {
                        if(res.data.errorcode == 0) {
                            this.$Message.success("该区域删除成功！")
                            EventBus.$emit("success")
                        } else {
                            this.$Message.success(res.data.errormsg)
                        }
                    } else {
                        console.error("服务器扑街！")
                    }
                }).catch(err => {
                    console.log(err);
                })
            },
            handleMigrationArea() {
                this.$refs['migrationRef'].validate((valid) => {
                    if (valid) {
                        this.$service.post('/web/LockerRent/migration', {
                            from_device_id: this.areaId,
                            to_device_id: this.migrationForm.areaId
                        }).then(res => {
                            if(res.status === 200) {
                                if(res.data.errorcode == 0) {
                                    this.$Message.success("该区域迁移成功！")
                                    this.showMigration = false;
                                    EventBus.$emit("success")
                                }else {
                                  this.$Message.warning(res.data.errormsg)
                                }
                            } else {
                                console.error("服务器扑街！")
                            }
                        }).catch(err => {
                            console.log(err);
                        });
                    }
                })

            }
        },
    }
</script>

<style scoped>
    .zone {
        margin: 15px;
        display: block;
        overflow: hidden;
    }
    .zone-title {
        margin-left: 10px;
        width: 100%;
        text-align: left;
        display: block;
        height: 30px;
        line-height: 30px;
        margin-bottom: 10px;
    }
    .smart {
        vertical-align: baseline;
        display: inline-block;
        padding: 0 10px;
        color: #2B8DF2;
        border: 1px solid #2B8DF2;
        letter-spacing: 4px;
        height: 20px;
        line-height: 18px;
        margin-left: 8px;
        border-radius: 10px;
        text-align: center;
    }
</style>
