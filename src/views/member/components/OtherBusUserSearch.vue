<template>
  <Select filterable ref="otherBusUserSelect" :clearable="clearable" :remote="remote" not-found-text="未搜索到该会员" :remote-method="getSearchUserList" v-model="userId" :loading="searching" placeholder="11位电话号码">
    <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">
      <img :src="user.avatar" class="user-avatar"/>{{user.username}}({{user.phone}})
    </Option>
  </Select>
</template>

<script>
  let timer = null;
  export default {
    name: 'OtherBusUserSearch',
    data() {
      return {
        userId: '',
        searchType: '0', // 搜索方式: 姓名/电话/实体卡号 等
        searching: false,
        searchText: '',
        userList: [],
        selectedUser: null,
        support_fingerprint: '',
        remote: true
      };
    },
    props: {
      clearable: {
        type: Boolean,
        default: true
      },
      busId: {
        type: String,
        default: ''
      },
      value: {
        type: String
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {
      // 搜索会员
      getSearchUserList(search) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          search = search.trim();
          if ((search === this.searchText && this.userList) || !/^1\d{10}$/.test(search)) return;
          this.searchText = search;
          this.searching = true;
          this.userList = [];
          let postData = {
            search: search,
            bus_id: this.busId
          };
          return this.$service
            .post('/Web/Member/user_search_by_change', postData, { loading: false })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.userList= res.data.data
                this.searching = false;
                // if (this.userList.length === 1) {
                //   let user = this.userList[0];
                //   this.userId = user.user_id;
                //   let query = `${user.username}(${user.phone})`;
                //   this.$refs.signSelect.lastQuery = query;
                //   this.$refs.signSelect.setQuery(query);
                // }
              } else {
                this.$Message.error(res.data.errormsg)
                this.searching = false;
                this.userList= [];
              }
            })
            .catch(err => {
              console.error(err);
            });
        }, 80);
      }
    },
    watch: {
      busId(val, oldVal) {
        if (val != oldVal || !val) {
          this.userId = ''
        }
      },
      userId(val) {
        this.$emit('input', val)
        if (!val) {
          this.userList = null;
          this.$emit('on-change', null);
          return;
        }
        this.selectedUser = this.userList.filter(user => {
          return user.user_id === val;
        });
        this.$emit('on-change', this.selectedUser[0]);
      }
    }
  };
</script>

<style lang="less">
  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
  }
</style>
