<template>
  <Modal :mask-closable="false"
         v-model="detention"
         width="800"
         title="租柜">
    <Form ref="lockerForm"
          :model="lockerForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="100">
      <Form-item label="会员"
                 v-if="!userId"
                 prop="user_id"
                 :rules="{required: !userId, message: '请选择会员'}">
        <userSearch url="/Web/FrontMoney/search_all_user"
                    v-model="lockerForm.user_id"></userSearch>
      </Form-item>
      <Form-item label="租柜类型"
                 prop="rent_type">
        <Select v-model="lockerForm.rent_type"
                @on-change="lockertypeChange"
                :disabled="actionType == 'newSys'"
                filterable>
          <Option value="0">普通租柜</Option>
          <Option value="1">智能柜控</Option>
        </Select>
      </Form-item>
      <Form-item label="租柜选择"
                 prop="device_id"
                 v-if="lockerForm.rent_type==1"
                 :rules="{required: true, message: '请选择储物柜',trigger: 'change'}">
        <Select v-model="lockerForm.device_id"
                @on-change="deviceChange"
                :disabled="actionType == 'newSys' && smart"
                clearable
                filterable>
          <Option v-if="actionType == 'newSys'" :value="areaId">{{deviceName}}</Option>
          <Option
                v-for="item in rentList"
                v-if="actionType != 'newSys'"
                :key="item.deviceId"
                :value="item.deviceId">{{item.deviceName}}</Option>
        </Select>
      </Form-item>
      <Form-item label="柜号选择"
                 prop="rent_type"
                 v-if="lockerForm.rent_type==1&&lockerForm.device_id"
                 :rules="{required: true, message: '请选择柜号',trigger: 'change'}">
        <Select v-model="lockerForm.locker_id"
                :disabled="actionType == 'newSys' && smart"
                clearable
                filterable>
          <Option v-if="actionType == 'newSys'" :value="lockerNo">{{lockerNo}}</Option>
          <Option v-for="item in rentNumList"
                  v-else
                  :key="item"
                  :value="item">{{item}}</Option>
        </Select>
      </Form-item>
      <Form-item label="储物柜编号"
                 prop="locker_id"
                 v-if="lockerForm.rent_type==0"
                 :rules="{required: true, message: '请填写储物柜编号'}">
        <Input v-model="lockerForm.locker_id" :disabled="actionType == 'newSys'" />
      </Form-item>
      <Form-item label="截止时间"
                 prop="e_date"
                 :rules="{ required: true, message: '请选择日期'}">
        <Date-picker v-model="lockerForm.e_date"
                     :editable="false"
                     :options="disableDayBefore"
                     type="date"
                     placeholder="截止时间"></Date-picker>
      </Form-item>
      <Form-item label="租柜费用"
                 prop="amount"
                 :rules="{required: true, type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数'}">
        <Input v-model="lockerForm.amount" />
      </Form-item>
      <Form-item label="押金金额"
                 prop="cash_pledge"
                 :rules="{type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '金额必须为数字且只能保留两位小数'}">
        <Input v-model="lockerForm.cash_pledge" />
      </Form-item>
      <Form-item label="支付方式"
                 prop="new_pay_type"
                 v-if="curLockerTotalPay>0 && lockerForm.user_id"
                 :rules="{required: hasAmount, message: '请选择支付方式'}">
        <pay-type-list
          v-model="lockerForm.new_pay_type"
          :sqbOption="{ describe: `租柜[${lockerForm.locker_id}]`, serviceType: 3 }"
          :amount="Number(curLockerTotalPay)"
          :userId="lockerForm.user_id"
        />
      </Form-item>
      <FormItem label="销售人员">
        <Select placeholder="请选择销售人员" v-model="lockerForm.marketers_id" filterable>
            <!-- <Option label="无销售人员" value="0"></Option> -->
            <Option v-for="item in salersList" :key="item.marketers_id" :label="item.sale_name" :value="item.marketers_id">
                <span>{{item.sale_name}}</span>
                <span v-if="item.is_membership == 1" style="float:right;color:goldenrod">*会籍</span>
              </Option>
        </Select>
    </FormItem>
      <Form-item label="备注"
                 prop="remark">
        <textarea rows="3"
                  maxlength="90"
                  v-model="lockerForm.remark"></textarea>
      </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="newLocker">确定</Button>
      <Button
              @click="detention = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  import { formatDate } from 'utils/index'
  import userSearch from 'src/components/user/userSearch'
  import PayTypeList from '@/components/form/PayTypeList.vue'
  export default {
    name: 'AddLocker',
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      },
      actionType: { //新的需求标记，可视化储物柜操作
          type: String
      },
      lockerNo: {
          default: ''
      },
      deviceName: {
          default: ''
      },
      areaId: {
          default: ''
      },
      isContinue: {
          type: Boolean,
          default: true
      },
      endDate: {
          default: ''
      },
      smart: { //智能柜
          type: Boolean,
          default: false
      }
    },
    components: {
      userSearch,
      PayTypeList
    },
    data() {
      return {
        // detention: this.value,
        salersList: [],
        rentList: [],
        rentNumList: [],
        lockerForm: {
          marketers_id: '',
          user_id: '',
          new_pay_type: [],
          locker_id: '',
          rent_type: '0',
          device_id: '',
          cash_pledge: '',
          device_name: '',
          e_date: '',
          amount: '',
          remark: '',
        },
        eDate: '',
        disableDayBefore: {
          disabledDate: date => {
            if(formatDate(new Date(Date.now()), 'yyyy-MM-dd') === this.eDate) {
              return date && date.valueOf() < new Date(this.eDate) - 86400000
            } else {
              return date && date.valueOf() <= new Date(this.eDate)
            }
          }
        }
      }
    },
    computed: {
      curLockerTotalPay() {
        return ((Number(this.lockerForm.amount) || 0) + (Number(this.lockerForm.cash_pledge) || 0)).toFixed(2)
      },
      detention: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      },
      hasAmount() {
        return Number(this.lockerForm.amount) + Number(this.lockerForm.cash_pledge) > 0;
      }
    },
    watch: {
      curLockerTotalPay() {
        this.lockerForm.new_pay_type = []
      },
      detention(val) {
        if (!val) {
          this.$refs.lockerForm.resetFields()
        }
      },
      value: {
        handler(show) {
          if(show && this.userId) {
            this.lockerForm.user_id = this.userId;
          }
        },
        immediate: true,
      }
    },
    created() {
        if(this.actionType == 'newSys') {
            this.lockerForm.locker_id = this.lockerNo;
            this.eDate = this.endDate;
            if(this.smart) {
                this.lockerForm.locker_id = this.lockerNo;
                this.lockerForm.rent_type = '1';
                this.lockerForm.device_name = this.deviceName;
                this.lockerForm.device_id = this.areaId;
            }
        }
        this.getSalersList();
    },
    methods: {
        getSalersList() {
          let isIncludeMembership = {
            include_membership: 1
          }
          this.$service.post('Web/Marketers/get_marketers', isIncludeMembership).then((res) => {
              if(res.data.errorcode === 0) {
                this.salersList = res.data.data
              } else {
                  this.$Message.error(res.data.errormsg);
                }
          }).catch(err => console.error(err))
      },
      lockertypeChange() {
        if (this.lockerForm.rent_type == 1) {
          let url = '/Web/LockerRent/get_detention_device'
          this.$service
            .get(url)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.rentList = res.data.data
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
        }
      },
      deviceChange() {
        if (this.lockerForm.device_id) {
          for (let i = 0; i < this.rentList.length; i++) {
            if (this.rentList[i].deviceId == this.lockerForm.device_id) {
              this.lockerForm.device_name = this.rentList[i].deviceName
            }
          }
          let url = '/Web/LockerRent/get_free_cabinent'
          let postData = {
            device_id: this.lockerForm.device_id
          }
          this.$service
            .post(url, postData)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.rentNumList = res.data.data
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
        } else {
          this.lockerForm.device_name = ''
          this.rentNumList = []
        }
      },
      newLocker() {
        this.lockerForm.areaId = this.areaId;
        this.$refs.lockerForm.validate(valid => {
          if (valid) {
            let url = '/Web/LockerRent/add_lockerrent'
            if (!/^\d\d\d\d\-\d{1,2}\-\d{1,2}$/.test(this.lockerForm.e_date)) {
              this.lockerForm.e_date = formatDate(this.lockerForm.e_date, 'yyyy-MM-dd')
            }
            this.$service
              .post(url, this.lockerForm)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.detention = false
                  this.$emit('on-success')
                  this.$emit('on-printinfo',this.lockerForm.user_id,res.data.card_order_info_id,'addlocker')
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              })
              .catch(err => {
                this.$Message.error(err)
              })
          }
        })
      }
    }
  }
</script>

<style scoped>
</style>
