<template>
  <div class="user-search-main nopadding">
      <div class="input-group" :style="{marginLeft:busId?'0':'310px'}">
            <div class="input-before">
              <span class="before-label">入会时间</span>
            </div>
            <div class="input-after">
              <div class="min-wrap">
                <Date-picker placeholder="请选择日期区间" v-model="buyDate" @on-change="onDateChange($event, 'buy_card_begin_time', 'buy_card_end_time')" type="daterange" :editable="false" clearable transfer></Date-picker>
              </div>
            </div>
          </div>
          <SaleSelect :belong-bus-id="busId||postData.belong_bus_id" v-model="postData.marketers_id" placeholder="跟进会籍" v-if="busId||postData.belong_bus_id" class="w120" clearable>
            <Option label="无跟进" value="-1"></Option>
          </SaleSelect>
          <SaleSelect :belong-bus-id="busId||postData.belong_bus_id" v-model="postData.followup_coach_id" placeholder="跟进教练" v-if="busId||postData.belong_bus_id"
                      class="w120" isCoach :isMembership="false" :showCoachText="false">
            <Option label="无跟进私教" value="-1"></Option>
            <Option label="无跟进泳教" value="-2"></Option>
          </SaleSelect>
          <Select class="w120" v-model="postData.user_level"  placeholder="客户级别" clearable transfer>
            <Option value="A">A级</Option>
            <Option value="B">B级</Option>
            <Option value="C">C级</Option>
          </Select>
          <Select class="w120" v-model="postData.user_tag_id"  placeholder="客户标签" filterable clearable transfer>
            <Option v-for="tag in userTags" :value="tag.id" :key="tag.id">{{tag.name}}</Option>
          </Select>
          <Select v-model="postData.user_source_id" placeholder="客户来源" v-if="busSources" clearable transfer>
            <Option v-for="item in busSources.type1" :key="item.source_id" :value="item.source_id">{{item.source_name}}</Option>
          </Select>
          <Select class="w120" v-model="postData.sex"  placeholder="性别" clearable transfer>
            <Option value="1">男</Option>
            <Option value="2">女</Option>
          </Select>
          <div class="input-group" >
            <div class="input-before">
              <span class="before-label">年龄</span>
            </div>
            <div class="input-after">
              <div class="min-wrap">
                <InputNumber class="min-input" v-model="postData.age_begin_num"></InputNumber>
                <span>~</span>
                <InputNumber class="min-input" v-model="postData.age_end_num"></InputNumber>
                <span>岁</span>
              </div>
            </div>
          </div>
          <div class="input-group" >
            <div class="input-before">
              <span class="before-label">出生年月</span>
            </div>
            <div class="input-after">
              <div class="min-wrap">
                <Date-picker format="MM-dd" placeholder="请选择月份范围" v-model="birthDate" @on-change="onBirthDateChange" type="daterange" :editable="false" clearable transfer></Date-picker>
              </div>
            </div>
          </div>
          <div class="input-group" >
            <div class="input-before">
              <span class="before-label">注册时间</span>
            </div>
            <div class="input-after">
              <div class="min-wrap">
                <Date-picker placeholder="请选择日期区间" v-model="createDate" @on-change="onDateChange($event, 'ub_create_begin_time', 'ub_create_end_time')" type="daterange" :editable="false" clearable transfer></Date-picker>
              </div>
            </div>
          </div>
          <div class="input-group" >
          <div class="input-before">
            <span class="before-label">会籍未跟进天数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" v-model="postData.follow_begin_num"></InputNumber>
              <span>~</span>
              <InputNumber class="min-input" v-model="postData.follow_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>

        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">私教未跟进天数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" v-model="postData.pt_follow_begin_num"></InputNumber>
              <span>~</span>
              <InputNumber class="min-input" v-model="postData.pt_follow_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div> 

        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">泳教未跟进天数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" v-model="postData.swim_follow_begin_num"></InputNumber>
              <span>~</span>
              <InputNumber class="min-input" v-model="postData.swim_follow_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>

        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近有签到</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.sign_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近未签到</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.sign_begin_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近有上私教</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.signpt_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近未上私教</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.signpt_begin_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近有上泳教</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.signsw_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近未上泳教</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.signsw_begin_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近有购买</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.card_buy_end_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">最近无购买</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <InputNumber class="min-input" placeholder="1~720" v-model="postData.card_buy_begin_num"></InputNumber>
              <span>天</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">有效购买次数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <Select class="min-input" v-model="postData.card_buy_times_logic">
                <Option value="1">大于</Option>
                <Option value="2">小于</Option>
                <Option value="3">区间</Option>
              </Select>
              <InputNumber v-if="postData.card_buy_times_logic==1||postData.card_buy_times_logic==3" class="min-input" v-model="postData.card_buy_times_begin_num"></InputNumber>
              <span v-if="postData.card_buy_times_logic==3">~</span>
              <InputNumber v-if="postData.card_buy_times_logic==2||postData.card_buy_times_logic==3" class="min-input" v-model="postData.card_buy_times_end_num"></InputNumber>
              <span>次</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">消费金额</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <Select class="min-input" v-model="postData.card_buy_amount_logic">
                <Option value="1">大于</Option>
                <Option value="2">小于</Option>
                <Option value="3">区间</Option>
              </Select>
              <InputNumber v-if="postData.card_buy_amount_logic==1||postData.card_buy_amount_logic==3" class="min-input" v-model="postData.card_buy_amount_begin_sum"></InputNumber>
              <span v-if="postData.card_buy_amount_logic==3">~</span>
              <InputNumber v-if="postData.card_buy_amount_logic==2||postData.card_buy_amount_logic==3" class="min-input" v-model="postData.card_buy_amount_end_sum"></InputNumber>
              <span>元</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">购会籍卡次数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <Select class="min-input" v-model="postData.card_buy_hj_times_logic">
                <Option value="1">大于</Option>
                <Option value="2">小于</Option>
                <Option value="3">区间</Option>
              </Select>
              <InputNumber v-if="postData.card_buy_hj_times_logic==1||postData.card_buy_hj_times_logic==3" class="min-input" v-model="postData.card_buy_hj_times_begin_num"></InputNumber>
              <span v-if="postData.card_buy_hj_times_logic==3">~</span>
              <InputNumber v-if="postData.card_buy_hj_times_logic==2||postData.card_buy_hj_times_logic==3" class="min-input" v-model="postData.card_buy_hj_times_end_num"></InputNumber>
              <span>次</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">购私教课次数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <Select class="min-input" v-model="postData.card_buy_sj_times_logic">
                <Option value="1">大于</Option>
                <Option value="2">小于</Option>
                <Option value="3">区间</Option>
              </Select>
              <InputNumber v-if="postData.card_buy_sj_times_logic==1||postData.card_buy_sj_times_logic==3" class="min-input" v-model="postData.card_buy_sj_times_begin_num"></InputNumber>
              <span v-if="postData.card_buy_sj_times_logic==3">~</span>
              <InputNumber v-if="postData.card_buy_sj_times_logic==2||postData.card_buy_sj_times_logic==3" class="min-input" v-model="postData.card_buy_sj_times_end_num"></InputNumber>
              <span>次</span>
            </div>
          </div>
        </div>
        <div class="input-group" >
          <div class="input-before">
            <span class="before-label">购泳教课次数</span>
          </div>
          <div class="input-after">
            <div class="min-wrap">
              <Select class="min-input" v-model="postData.card_buy_yj_times_logic">
                <Option value="1">大于</Option>
                <Option value="2">小于</Option>
                <Option value="3">区间</Option>
              </Select>
              <InputNumber v-if="postData.card_buy_yj_times_logic==1||postData.card_buy_yj_times_logic==3" class="min-input" v-model="postData.card_buy_yj_times_begin_num"></InputNumber>
              <span v-if="postData.card_buy_yj_times_logic==3">~</span>
              <InputNumber v-if="postData.card_buy_yj_times_logic==2||postData.card_buy_yj_times_logic==3" class="min-input" v-model="postData.card_buy_yj_times_end_num"></InputNumber>
              <span>次</span>
            </div>
          </div>
        </div>
  </div>
</template>

<script>
  import { formatDate } from "utils"
  import SaleSelect from 'src/components/membership/salesSelect'
  import { getSources } from 'src/service/getData'
import { indexOf } from 'lodash-es'
  export default {
    name: 'MemberForm',
    components: {
      SaleSelect
    },
    props: {
      busId: { //来自新建分群
        type: [String, Number]
      },
      sources: {
        type: Object
      },
      value: {
        type: Object
      },
      birthDateRange: {
        type: Array
      },
      createDateRange: {
        type: Array
      },
      buyDateRange: {
        type: Array
      }
    },
    data() {
      return {
        userTags: [],
        busSources: [],
        birthDate: this.birthDateRange || [],
        createDate: this.createDateRange || [],
        buyDate:this.buyDateRange || []
      }
    },
    computed: {
      postData: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      //监控整体赋值行为
      postData: {
        handler(val, oldVal) {
          this.timeRangeSet(val)
        },
        immediate: true
      },
      sources: {
        handler(val, oldVal) {
          this.busSources = val
        },
        immediate: true
      },
      'postData.belong_bus_id'(val, oldVal) {
        if (val !== oldVal) {
          this.getTags()
        }
      }
    },
    created() {
      this.getTags()
      if(this.busId) {
        this.getSourcesList()
      }
    },
    methods: {
      getSourcesList() {
        getSources({bus_id: this.busId}).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.busSources = resData
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      timeRangeSet(info){
        //避免类型错误 空字符串转换
        for (const key in this.postData) {
          if(this.postData[key] === '' && key.indexOf('card_id')==-1) {
            this.postData[key] = null
          }
        }
        this.buyDate = info['buy_card_begin_time']?[new Date(info['buy_card_begin_time']), new Date(info['buy_card_end_time'])] : []
        this.createDate = info['ub_create_begin_time']?[new Date(info['ub_create_begin_time'])||'', new Date(info['ub_create_end_time'])||''] : []
        this.birthDate = info['birthday_begin_time']?[new Date(info['birthday_begin_time'])||'', new Date(info['birthday_end_time'])||''] : []
      },
      getTags() {
        this.$service.post('/Web/MembershipTags/get_user_tags',{bus_id: this.busId||this.postData.belong_bus_id}).then(res => {
          if (res.data.errorcode === 0) {
            this.userTags = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      onBirthDateChange(val) {
        if (!val[0]) {
          this.postData.birthday_begin_time = ''
          this.postData.birthday_end_time = ''
          return false
        }
        this.postData.birthday_begin_time = formatDate(new Date(val[0]), 'MM-dd')
        this.postData.birthday_end_time = formatDate(new Date(val[1]), 'MM-dd')
      },
      onDateChange(val, beginKey, endKey) {
        if (!val[0]) {
          this.postData[beginKey] = ''
          this.postData[endKey] = ''
          return false
        }
        this.postData[beginKey] = formatDate(new Date(val[0]), 'yyyy-MM-dd')
        this.postData[endKey] = formatDate(new Date(val[1]), 'yyyy-MM-dd')
      }
    }
  }
</script>

<style scoped>
.nopadding {
  padding: 0;
}
</style>
