<template>
<Modal v-model="showReceiptDetail" :mask-closable="true" width="523" title="小票" @on-cancel="showReceiptDetail = false">
  <div class='bodybox'>
    <div class="instruct center-row m-b-10">只能补打最近3天的小票记录</div>
    <RadioGroup v-model="type" type="button" @on-change="handleSwitch" class="space-bet-row m-b-10">
      <Radio label="1">合同订单</Radio>
      <Radio label="2">定金押金</Radio>
      <Radio label="3">教练消课</Radio>
      <Radio label="4">商品消费</Radio>
      <Radio label="5">场地预订</Radio>
    </RadioGroup>
    <RadioGroup v-model="band_type_id" class="bodycontent flex-start-col" @on-change="handleSelect" v-if='infoData.length>0'>
      <radio
        v-for="item in infoData"
        :key="type == 2 ? `${item.type}-${item.id}` : item.id"
        :label="type == 2 ? `${item.type}-${item.id}` : item.id"
        class="bodycard m-b-10 flex-start-row"
        >
        <div class="p-l-10">
          <div class="topline flex-start-row">
            <div class="topline-item-time">{{item.time}}</div>
            <div class="topline-item-type">{{item.title}}</div>
            <div class="topline-item-amount" v-if="type!=3">¥{{item.amount}}</div>
            <div class="topline-item-amount" v-else>{{item.amount}}</div>
          </div>
          <div class="line w100"></div>
          <div class="bottomline flex-start-row">
            <div class="topline-item-time" v-if="type==1&&!item.description.includes('租柜')">
              <span class="greycolor desc-label">描述：</span>
              <span class="desc-content">{{item.description}}</span>
            </div>
            <div class="topline-item-time prolong" v-else>
              <span class="greycolor desc-label">描述：</span>
              <span class="desc-content prolongcon">{{item.description}}</span>
            </div>
            <div v-if="type==1&&!item.description.includes('租柜')"><span class="greycolor">业绩归属：</span>{{item.marketer_name}}</div>
          </div>
        </div>
      </radio>
    </RadioGroup>
    <div class='nodata' v-else>
      暂无数据
    </div>
  </div>
  <div slot="footer" class="modal-buttons">
    <Button type="success">
      <router-link  style="color: #fff"
          v-if="type==1"
          target="_blank"
          :to="{path: '/contractPrint', query: {type: 0, user_id: userId, card_order_info_id: type_id, oper_type: 'contract'}}">
          打印小票
        </router-link>
      <router-link  style="color: #fff"
          v-if="type==2"
          target="_blank"
          :to="{path: '/depositPrint', query: {type: deposit_type, user_id: userId, charge_id: type_id, oper_type: 'deposit'}}">
          打印小票
        </router-link>
      <router-link  style="color: #fff"
          v-if="type==3"
          target="_blank"
          :to="{path: '/signInfoPrint', query: {type: 3, user_id: userId, small_ticket_id: type_id, oper_type: 'ptsign'}}">
          打印小票
      </router-link>
      <router-link  style="color: #fff"
          v-if="type==4"
          target="_blank"
          :to="{path: '/commodityPrint', query: {type: 4, user_id: userId, order_sn: type_id, oper_type: 'buygoods'}}">
          打印小票
      </router-link>
      <router-link  style="color: #fff"
          v-if="type==5"
          target="_blank"
          :to="{path: '/stadiumPrint', query: {type: 5, user_id: userId, order_sn: type_id, oper_type: 'stadium'}}">
          打印小票
      </router-link>
    </Button>
    <Button
            @click="showReceiptDetail = false">取消</Button>
  </div>

</Modal>
</template>
<script>
export default {
  name: 'receiptDetail',
  data() {
    return {
      type: '1',
      type_id: '',
      band_type_id: '',
      infoData: [],
      deposit_type: 1 // 定金/押金type
    }
  },
  props: {
    value: {
      type: Boolean
    },
    userId: {
      type: String
    }
  },
  computed: {
    showReceiptDetail: {
      get() {
        if(this.value) {
          this.getInfo();
        }
        return this.value
      },
      set(val) {
        this.infoData = [];
        this.type = '1';
        this.type_id = '';
        this.$emit('input', val)
      }
    }
  },
  methods: {
    getInfo () {
      const url = '/Web/SmallTicket/getTicketLists'
      this.$service.post(url, { type: this.type, user_id: this.userId}).then(res => {
        if (res.data.errorcode === 0) {
          this.infoData = res.data.data;
          this.type_id = this.infoData.length>0?this.infoData[0].id:'';
          this.band_type_id = this.infoData.length > 0
            ? this.type == 2 ? `${this.infoData[0].type}-${this.infoData[0].id}` : this.infoData[0].id
            : '';
          if(this.type==2) {
            this.deposit_type = this.infoData.length>0?this.infoData[0].type:1;
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        this.$Message.error(err);
      })
    },
    handleSwitch() {
      this.getInfo();
    },
    handleSelect(val) {
      this.deposit_type = 1;
      this.type_id = this.type == 2 && val ? val.split('-')[1] : val
      if(this.type == 2) {
        const item = this.infoData.find(ele => val === `${ele.type}-${ele.id}`);
        item && (this.deposit_type = item.type)
      }
    }
  }
}
</script>

<style scoped>
.bodybox {
  width: 100%;
}
.instruct {
  font-size: 14px;
  color: #ed3f14;
}
.topline {
  font-size: 14px;
  line-height: 40px;
  border-bottom: 1px dashed #ccc;
}
.topline-item-time {
  width: 220px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  /* align-items: center; */
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}
.topline-item-time .desc-label {
  width: 45px;
}
.topline-item-time .desc-content {
  width: 170px;
  padding-top: 12px;
  padding-bottom: 12px;
  line-height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.topline-item-type {
  width: 100px;
}
.topline-item-amount {
  width: 103px;
  padding-right: 12px;
  text-align: right;
  color:  #ed3f14;
  font-weight: bold;
}
.bottomline {
  font-size: 14px;
  line-height: 40px;
}
.bodycontent {
  display: block;
  overflow: scroll;
  max-height: calc(100vh - 450px);
}
.bodycard {
  width: 100%;
  border: 1px solid #ccc;
  padding: 0 10px;
}
.greycolor {
  color: #979faf;
}
.nodata {
  text-align: center;
  width: 100%;
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  border: 1px solid #ccc;
}
.prolong {
  width: 414px;
}
.prolong .prolongcon {
  width: 364px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

