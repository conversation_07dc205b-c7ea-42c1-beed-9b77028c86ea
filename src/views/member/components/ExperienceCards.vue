<template>
  <div class="table-wrap">
    <header>
      <Input class="user-search" placeholder="会员名/电话号码/实体卡号" v-model="postdata.search" @on-enter="handleSearch"></Input>
      <Date-picker placeholder="选择查询时间段" @on-change="dateChanged" style="max-width: 240px; width: 240px" :value="dateRange" type="daterange" :editable="false" clearable @on-clear="clearDate" format="yyyy年MM月dd日"></Date-picker>
      <Select v-model="postdata.status" clearable style="width:100px" placeholder="状态">
        <Option value="1">使用中</Option>
        <Option value="2">未激活</Option>
        <Option value="3">已过期</Option>
        <Option value="4">已耗尽</Option>
      </Select>
      <saleSelect v-model="postdata.marketers_id" placeholder="归属会籍"></saleSelect>
      <Select class="w120" v-model="postdata.user_level" placeholder="客户级别">
        <Option value="">客户级别</Option>
        <Option value="A">A</Option>
        <Option value="B">B</Option>
        <Option value="C">C</Option>
      </Select>
      <Button type="success" @click="handleSearch">搜索</Button>
    </header>

    <main>
      <Table @on-select="selectMember" @on-selection-change="selectMemberChange" @on-select-all="selectMemberAll" @on-select-cancel="selectMemberCancel" ref="table" :columns="columns" :data="tableData" stripe disabled-hover></Table>
    </main>

    <footer>
      <Button type="success" style="margin-right: 30px" @click="detention = true">赠体验卡</Button>
      <Dropdown @on-click="otherCase" placement="top" v-if="notExport">
        <Button>
          其他操作
          <Icon type="md-arrow-dropdown"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem name="0">导出excel</DropdownItem>
          <DropdownItem name="1">短信发送</DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Page :total="totalCount" :current.sync="currentPage" show-total show-sizer class="page" placement="top" @on-change="handlePageChange" @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <AddExperienceCards v-model="detention" @on-success="getexpcardsList" />
  </div>
</template>

<script>
  import saleSelect from 'src/components/membership/salesSelect';
  import AddExperienceCards from './AddExperienceCards.vue';
  import { getNewHost } from 'utils/config';

  export default {
    data() {
      return {
        selectedMembers: [],
        postdata: {
          user_level: '',
          search: '',
          s_date: '',
          e_date: '',
          status: '',
          page_no: 1,
          page_size: 10,
          marketers_id: ''
        },
        dateRange: [],
        tableData: [],
        exportData: null,
        sizer: 10,
        currentPage: 1,
        totalCount: 0,
        // userUrl: `${getNewHost()}/#/member/detail/`,
        userUrl: '/member/detail/',
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '时间',
            key: 'buy_date',
            width: 200
          },
          {
            title: '会员',
            key: 'username',
            render: (h, param) => {
              let url = this.userUrl + param.row.user_id;
              return (
                // <a target="_blank" href={url}>
                //   {param.row.username}
                // </a>
                <router-link target="_blank" to={ url }>{ param.row.username }</router-link>
              );
            }
          },
          {
            title: '赠卡方式',
            key: 'experience_from'
          },
          {
            title: '会员卡',
            key: 'card_name'
          },
          {
            title: '状态',
            key: 'status',
            render: (h, param) => {
              let status = param.row.status;
              return (
                <span>{status == '1' ? '使用中' : status == '2' ? '未激活' : status == '3' ? '已过期' : '已耗尽'}</span>
              );
            }
          },
          {
            title: '归属会籍',
            key: 'marketers_name'
          }
        ],
        detention: false,
        notExport: true
      };
    },
    components: {
      saleSelect,
      AddExperienceCards
    },
    computed: {
      selectedUserIds() {
        return this.selectedMembers.map(user => user.user_id);
      }
    },
    created() {
      this.getexpcardsList();
    },
    methods: {
      sendMsg() {
        if (this.selectedUserIds.length > 0) {
          this.$router.push({
            name: '消息推送',
            params: {
              activeIndex: '2',
              selectedMembers: this.selectedMembers,
            }
          });
        } else {
          this.$Message.error('请先选择需要发送短信的会员');
        }
      },
      selectMember(selection, member) {
        if (!this.selectedUserIds.includes(member.user_id)) {
          this.selectedMembers.push(member);
        }
      },
      selectMemberAll(selection) {
        if (selection.length > 0) {
          selection.forEach(member => {
            this.selectMember(selection, member);
          });
        }
      },
      selectMemberCancel(selection, member) {
        const index = this.selectedMembers.findIndex(user => user.user_id == member.user_id);
        this.selectedMembers.splice(index, 1);
      },
      selectMemberChange(selection) {
        if (selection.length == 0) {
          this.tableData.forEach(member => {
            this.selectMemberCancel(selection, member);
          });
        }
      },
      dateChanged(val) {
        if (!val[0]) {
          return false;
        }
        let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`;
        let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`;
        this.dateRange = [beginDate, endDate];
      },
      clearDate() {
        this.dateRange = [];
      },
      handleSearch() {
        this.currentPage = 1;
        this.getexpcardsList();
      },
      getexpcardsList() {
        this.postdata.s_date = this.dateRange[0];
        this.postdata.e_date = this.dateRange[1];
        this.postdata.page_no = this.currentPage;
        this.postdata.page_size = this.sizer;
        this.$service.post('/Web/Member/get_experience_list', this.postdata).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              this.totalCount = parseInt(data.count);
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    _checked: this.selectedUserIds.includes(item.user_id)
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      },
      getExportList(isExport) {
        this.postdata.s_date = this.dateRange[0];
        this.postdata.e_date = this.dateRange[1];
        this.postdata.page_no = this.currentPage;
        this.postdata.page_size = this.totalCount;
        return this.$service.post('/Web/Member/get_experience_list', { ...this.postdata }, { isExport }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data;
              this.exportData = data.list.map(item => {
                return Object.assign({}, item, {
                  status:
                    item.status == '1'
                      ? '使用中'
                      : item.status == '2'
                        ? '未激活'
                        : item.status == '3'
                          ? '已过期'
                          : '已耗尽'
                });
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
            this.postdata.page_size = this.sizer;
          } else {
            console.log('服务器扑街！');
            this.postdata.page_size = this.sizer;
          }
        });
      },
      handlePageChange(pageno) {
        this.currentPage = pageno;
        this.getexpcardsList();
      },
      pageSizeChanged(size) {
        this.currentPage = 1;
        this.sizer = size;
        this.getexpcardsList();
      },
      async exportTable() {
        await this.getExportList(true);
        if (!this.exportData) return false;

        let columns = this.columns.filter((col, index) => index < 6);
        columns.splice(2, 0, { title: '电话号码', key: 'phone' });
        this.notExport = false;
        this.$refs.table.exportCsv({
          filename: '体验卡管理',
          columns: columns,
          data: this.exportData
        });
        setTimeout(() => {
          this.notExport = true;
        }, 100);
      },
      otherCase(val) {
        ({
          '0': () => this.exportTable(),
          '1': () => this.sendMsg()
        }[val]());
      }
    }
  };
</script>
<style lang="less" scoped>
  .ivu-date-picker {
    width: 240px;
  }

  header {
    .user-search {
      width: 160px;
    }
  }

  footer {
    display: flex;
    justify-content: space-between;
  }

  .ivu-date-picker {
    width: 100%;
  }
</style>
