<template>
  <div class="container physical-test">
    <header>
      <h3>添加</h3>
    </header>
    <Form label-position="right"
          ref="form"
          :model="formItem"
          :rules="formRules"
          class="form"
          :label-width="160">
      <FormItem label="体测时间">
        <DatePicker v-model="formItem.date"
                    format="yyyy年MM月dd日" />
      </FormItem>
      <FormItem label="身高 (cm)"
                prop="height">
        <InputNumber v-model="formItem.height"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="体重 (kg)"
                prop="weight">
        <InputNumber v-model="formItem.weight"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="基础代谢 (Kcal)">
        <InputNumber v-model="formItem.kcal"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="骨骼肌含量 (kg)">
        <InputNumber v-model="formItem.muscle"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="胸围 (cm)">
        <InputNumber v-model="formItem.bust"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="腰围 (cm)">
        <InputNumber v-model="formItem.waistline"
                     style="width: 100%" />
      </FormItem>
      <FormItem label="臀围 (cm)">
        <InputNumber v-model="formItem.hip"
                     style="width: 100%" />
      </FormItem>
      <FormItem>
        {{pbf}}
        <div slot="label">
          <span>PBF (%)</span>
          <Tooltip placement="right">
            <Icon type="ios-help-circle"
                  color="#F7DC6F"></Icon>
            <div slot="content">体脂百分比：体脂肪(kg) / 体重(kg) * 100
              <p>正常范围：18.0 ~ 28.0</p>
            </div>
          </Tooltip>
        </div>
      </FormItem>
      <FormItem>
        {{bmi}}
        <div slot="label">
          <span>BMI (kg/m
            <sup style="margin-left: -0.4em">2</sup>)</span>
          <Tooltip placement="right">
            <Icon type="ios-help-circle"
                  color="#F7DC6F"></Icon>
            <div slot="content"
                 style="white-space: normal">身体质量指数：体重(kg) / 身高(m)
              <sup>2</sup>
              <p>正常范围：18.5 ~ 24.0</p>
            </div>
          </Tooltip>
        </div>
      </FormItem>
      <FormItem>
        {{whr}}
        <div slot="label">
          <span>WHR (%)</span>
          <Tooltip placement="right">
            <Icon type="ios-help-circle"
                  color="#F7DC6F"></Icon>
            <div slot="content">腰臀比：腰围(cm) / 臀围(cm)
              <p>正常范围：0.75 ~ 0.85</p>
            </div>
          </Tooltip>
        </div>
      </FormItem>
      <FormItem label="体测图片">
        <img-uploader v-model="formItem.img"
                      :options="{aspectRatio: 3/4}" />
      </FormItem>
      <FormItem>
        <div class="form-bottom-buttons">
          <Button type="success">保存</Button>
          <Button 
                  @click="$router.back()">取消</Button>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  import ImgUploader from 'components/form/cropper'
  export default {
    name: 'PhysicalTest',
    components: { ImgUploader },
    data() {
      return {
        formItem: {
          date: new Date(),
          height: '',
          weight: '',
          img: '',
          pbf: '-',
          bmi: '-',
          whr: '-',
          kcal: '',
          muscle: '',
          bust: '',
          waistline: '',
          hip: ''
        },
        formRules: {
          height: [{required: true, message: '身高为必填项', trigger: 'blur'}],
          weight: [{required: true, message: '体重为必填项', trigger: 'blur'}],
        }
      }
    },
    computed: {
      bmi() {
        if (this.formItem.height == 0) return '-'
        return (this.formItem.weight / (this.formItem.height / 100 * this.formItem.height / 100)).toFixed(2)
      },
      pbf() {
        if (this.formItem.weight == 0) return '-'
        return ((this.formItem.weight - this.formItem.muscle) / this.formItem.weight * 100).toFixed(2)
      },
      whr() {
        if (this.formItem.hip == 0) return '-'
        return (this.formItem.waistline / this.formItem.hip).toFixed(2)
      }
    }
  }
</script>

<style lang="less">
  .physical-test {
    .ivu-form .ivu-form-item-label {
      white-space: nowrap;
    }
  }
</style>
