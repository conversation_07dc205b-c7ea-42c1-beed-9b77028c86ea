<template>
    <div class="table-wrap">
        <header>
            <Select class="w120"
                    v-model="postData.bus_id"
                    placeholder="归属场馆"
                    filterable>
                <Option v-for="option in adminBusList"
                        :value="option.id"
                        :key="option.id">{{ option.name }}</Option>
            </Select>
            <Input class="w120" v-model="postData.card_name"
                   placeholder="会员卡名称"
                   @on-enter="handleSearch"></Input>
            <Select v-model="postData.is_universal_card"
                    placeholder="单店/通店">
                <Option value="">单店/通店</Option>
                <Option :value="0">单店</Option>
                <Option :value="1">通店</Option>
            </Select>
            <Select v-model="postData.card_class"
                    placeholder="卡类型">
                <Option v-for="item in cardTypeList"
                        :value="item.value"
                        :key="item.id">{{ item.name }}</Option>
            </Select>
            <Select v-model="postData.sale_status"
                    placeholder="在售状态">
                <Option value="">在售状态</Option>
                <Option :value="1">在售</Option>
                <Option :value="0">下架</Option>
            </Select>
            <Button type="success"
                    @click="handleSearch">搜索</Button>
        </header>
        <main v-if="cardSettingAuth && cardSettingAuth.singleCard !== undefined">
            <Table disabled-hover
                   :columns="cardColumns"
                   :data="cardList"
                   ref="table"
                   @on-selection-change="handleCheckedArray"></Table>
        </main>
        <footer>
            <!-- <router-link v-if="hasCardSettingAuth"
                         :to="{ path: `/member/card/save/0/${cardType}`}">
                <Button class="rb-btn"
                        type="success">新增{{cardType == 1 ? '会籍卡':cardType == 2 ? '私教课':'泳教课'}}</Button>
            </router-link>
            <Button
              v-if="cardRuleAuth"
              style="margin-left: 20px"
              type="success"
              @click="handleToCardRules"
            >浮动售价规则设置</Button>
            <Button
              v-if="cardType == 1"
              style="margin-left: 20px"
              @click="handleShowReChangeModal"
            >批量修改储值卡续充规则</Button>
            <Button
              style="margin-left: 20px"
              @click="otherCase('2')"
            >导出excel</Button>
            <Button
              v-if="adminInfo.is_admin && cardType !== 1"
              style="margin-left: 20px"
              @click="otherCase('3')"
            >转化课程类型</Button> -->
            <!-- <Dropdown style="margin-left: 20px"
                      @on-click="otherCase" placement="top">
                <Button>
                    其他操作
                    <Icon type="md-arrow-dropdown"></Icon>
                </Button>
                <Dropdown-menu slot="list"> -->
                    <!-- <Dropdown-item name="0" >批量删除</Dropdown-item> -->
                    <!-- <Dropdown-item name="1">购卡协议</Dropdown-item> -->
                    <!-- <DropdownItem name="2">导出excel</DropdownItem>
                    <DropdownItem v-if="adminInfo.is_admin && cardType !== 1" name="3">转化课程类型</DropdownItem>
                </Dropdown-menu>
            </Dropdown> -->
            <Pager :name="'cardList'+cardType" :total="+cardTotal" :postData="postData" @on-change="handleWhichPage" />
        </footer>

        <Modal v-model="supportMobileModal"
               title="在线购卡">
            <div class="bodybuilding">
                <p class="w-red">启用在线支付购卡购课功能</p>
                <p class="w-red">需要先申请微信特约服务商</p>
                <img class="word"
                     src="../../../assets/img/word_support_mobile.png"
                     alt="word">
                <p class="w-blue">微信支付特约商户的申请和配置</p>
            </div>
            <div slot="footer"
                 class="modal-buttons">
                <Button type="info"
                        @click="handleDownloadWord">下载文档</Button>
                <Button @click="supportMobileModal = false">取消</Button>
            </div>
        </Modal>
        <Modal v-model="showChange" :title="cardType ===2 ?'私教转泳教':'泳教转私教'">
          <Form ref="depositForm" :model="changeCardForm" class="modal-form" :label-width="120">
          <Form-item label="需要转换的卡种"  prop="remark">
              <Select v-model="changeCardForm.card_ids" filterable multiple>
                <Option v-for="item in totalCanChangeCard" :key="item.card_id" :value="item.card_id">{{item.name}}</Option>
              </Select>
            </Form-item>
          </Form>
            <div slot="footer"
                 class="modal-buttons">
                <Button type="info"
                        @click="handleChangeCard">转换</Button>
                <Button @click="showChange = false">取消</Button>
            </div>
        </Modal>
        <RechargeModal
          v-if="cardType === 1"
          :show.sync="showRechargeModal"
          :initBusId="postData.bus_id"
         />
    </div>
</template>
<script>
  import { getResponseData } from '../../../utils/index';
  import { mapState } from 'vuex';
  import Pager from "components/pager";
  import RechargeModal from './rechargeModal'
  export default {
    name: 'NewCardList',
    components: {
      Pager,
      RechargeModal
    },
    props: {
      cardType: {
        type: Number,
        required: true
      },
      cardRuleAuth: {
        type: Boolean,
        default: false
      },
      showRecharge: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        hasCardSettingAuth: false, // 会员卡设置权限
        cardTypeList: [],
        totalCanChangeCard: [],
        changeCardForm: {
          card_ids: ''
        },
        postData: {
          bus_id: '',
          page_no: 1,
          page_size: 10,
          card_name: '',
          card_type: '',
          card_class: '',
          is_universal_card: '',
          sale_status: 1
        },
        cardColumns: [
          {
            title: '会员卡名称',
            align: 'center',
            key: 'name'
          },
          {
            title: '卡类型',
            align: 'center',
            key: 'cardTypeName'
          },
          {
            title: '使用限制',
            align: 'center',
            key: 'numberStr'
          },
          {
            title: '售价',
            align: 'center',
            key: 'current_price',
            sortable: true,
            render: (h, params) => {
              if (params.row.is_pt_time_limit_card != 1 && (params.row.card_type_id == 4 || params.row.card_type_id == 5 || params.row.experience_card == 1)) {
                return (
                    <div>-</div>
                );
              } else if (params.row.current_price == 0) {
                return (
                  <div>价格面议</div>
                );
              } else {
                return (
                  <div>{params.row.current_price}</div>
                );
              }
            }
          },
          {
            title: '售卖时间',
            key: 'sale_time',
            align: 'center',
            width: 150
          },
          {
            title: '在售状态',
            key: 'sale_status_str',
            align: 'center',
            width: 150,
            render: (h, params) => {
                const deleteCard = this.hasAuth(params.row);
                return deleteCard ? (
                  <i-switch
                    value={params.row.sale_status}
                    key={params.row.id + '1'}
                    true-value="1"
                    false-value="0"
                    on-on-change={e => {
                      this.handleSaleStatus(params.row, e);
                    }}
                  />
              ) : (
                <div key={params.row.id + '1'}>-</div>
              );
            }
          },
          {
            title: '会员端售卖',
            key: 'phonePay',
            align: 'center',
            width: 150,
            render: (h, params) => {
              if (params.row.experience_card != 1) {
                const deleteCard = this.hasAuth(params.row);
                return deleteCard ? (
                    <i-switch
                    key={params.row.id + '2'}
                    value={params.row.status}
                    true-value="1"
                    false-value="0"
                    on-on-change={e => {
                    this.handleSetStatus(params.row, e);
                  }}
                />
              ) : (
                <div key={params.row.id + '2'}>{deleteCard}-</div>
              );
              } else {
                return <div key={params.row.id + '2'}>-</div>;
              }
            }
          },
          {
            title: '操作',
            key: 'action',
            width: 150,
            align: 'center',
            render: (h, params) => {
              const goDetail = () => {
                this.$router.push(`/member/card/save/${params.row.id}/${this.cardType}?bus=${params.row.bus_id}`);
              };
              const delMe = () => {
                if (params.row.delete_notice_bus_name) {
                  this.$Modal.confirm({
                    title: "删除",
                    content: `删除操作将影响${params.row.delete_notice_bus_name}正常使用此卡，是否删除？`,
                    onOk: () => {
                      this.handleDelete(params.row.id, () => {
                        this.cardList.splice(params.index, 1);
                      });
                    }
                  });
                } else {
                  this.handleDelete(params.row.id, () => {
                    this.cardList.splice(params.index, 1);
                  });
                }
              };
              const deleteCard = this.hasAuth(params.row);
              return (
                  <div>
                  <i-button
              type="text"
              style={{ color: '#52a4ea', minWidth: '0', marginRight: '20px', minHeight: '26px', lineHeight: '26px' }}
              onClick={goDetail}>
                  编辑
                  </i-button>
                  <i-button
              type="text"
              disabled={!deleteCard}
              style={{ color: '#ff696a', minWidth: '0', minHeight: '26px', lineHeight: '26px' }}
              onClick={delMe}>
                  删除
                  </i-button>
                  </div>
            );
            }
          }
        ],
        cardList: [],
        cardTotal: 0,
        checkedArray: [],
        supportMobileAuthority: false,
        showChange: false,
        supportMobileModal: false,
        // showRechargeModal: false
      };
    },
    watch: {
      cardSettingAuth() {
        this.doseHasAuth()
      },
      showChange(val) {
        if (!val) {
          this.changeCardForm.card_ids = ''
        }
      }
    },
    computed: {
      ...mapState(['busId', 'adminBusList', 'globalBelongBusId', 'cardSettingAuth', 'adminInfo']),
      showRechargeModal: {
        get() {
          return this.showRecharge
        },
        set(val) {
          this.$emit('update:showRecharge', val)
        }
      }
    },
    methods: {
      doseHasAuth() {
        for (let value of Object.values(this.cardSettingAuth)) {
          if (value) {
            this.hasCardSettingAuth = true;
            break;
          }
          this.hasCardSettingAuth = false;
        }
      },
      getCardTypeList () {
        return this.$service.post('/Web/Card/get_card_class', { card_type: this.cardType }).then(res => {
          this.cardTypeList = [{ value: '', name: '全部' }].concat(getResponseData(res));
        });
      },
      hasAuth (card) {
        const { experience_card, universal_card, card_type_id } = card;
        const { singleCard, multiCard, expCard, multiPtCard, swimCard } = this.cardSettingAuth;
        let hasAuth = false
        if (card_type_id == 5) {
          //泳教单独控制权限
          hasAuth = swimCard
        } else {
          hasAuth = (multiCard && universal_card == 1 && card_type_id != 4) || (multiCard && universal_card == 1 && card_type_id == 4 && multiPtCard) || (expCard && experience_card == 1) || (singleCard && experience_card == 0 && universal_card == 0);
        }
        return hasAuth;
      },
      getCardTypeList () {
        return this.$service.post('/Web/Card/get_card_class', { card_type: this.cardType }).then(res => {
          this.cardTypeList = [{ value: '', name: '全部' }].concat(getResponseData(res));
        });
      },
      calDisabledChecked (list) {
        this.cardList = list.map(item => {
          const hasAuth = this.hasAuth(item);
          return {
            ...item,
            ...{
              _disabled: !hasAuth
            }
          };
        });
      },
      getCardList (allPageCount) {
        const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
        let postObj = Object.assign({}, this.postData);
        if (allPageCount) {
          postObj.page_size = allPageCount;
          postObj.page_no = 1;
        }
        this.postData.card_type = this.cardType
        postObj.card_type = this.cardType
        return this.$service
            .post(url, postObj, { loading: true })
            .then(res => {
              let data = getResponseData(res, { count: 0, list: [] });
              data.list.forEach((item, index) => {
                item.numberStr = item.number + (item.card_type_id === '1' || item.is_pt_time_limit_card === '1'? '天' : item.card_type_id === '3' ? '元' : '次')
                item.sale_time = (!item.start_end_time || !item.start_end_time.start) ? '-' : (item.start_end_time.start+'~'+item.start_end_time.end)
                item.sale_status_str = item.sale_status == 1 ? '是' : '否'
                item.phonePay = item.status == 1 ? '是' : '否'
                item.cardTypeName = `${item.universal_card == 1 ? '多店通用' : ''}${item.experience_card == 1 ? '体验卡' : ''}${item.card_type_id == 1 ? '期限卡' : item.card_type_id == 2 ? '次卡' : item.card_type_id == 3 ? '储值卡' : item.is_pt_time_limit_card == 1? '私教包月' : item.card_type_id == 4 ?  '私教课':'泳教课'}`
                if (item.is_pt_time_limit_card != '1' && item.card_type_id == 4 && item.experience_card === '0') {
                  item.numberStr = '-';
                }
                item.current_price = +item.current_price
              });
              if (!allPageCount) {
               this.cardTotal = parseInt(data.count)
                this.calDisabledChecked(data.list);
              }
              return data.list;
            });
      },
      handleWhichPage (postData) {
        // 加个$nextTick，否则被created的赋值覆盖
        this.$nextTick(() => {
          this.postData = {...this.postData, ...postData}
          this.getCardList();
        })
      },
      handleSearch () {
        this.postData.page_no = 1;
        this.getCardList();
      },
      handleSaleStatus (card, status) {
        card.sale_status = status;
        if (status === '0') card.status = '0';
        this.$service
            .post(
                '/Web/Card/set_sale_status',
                {
                  id: card.id,
                  bus_id: card.bus_id,
                  sale_status: status
                },
                {
                  loading: false
                }
            )
            .then(res => {
              if (res.status == 200) {
                if (res.data.errorcode !== 0) {
                  this.$Message.error({ content: `设置失败，${res.data.errormsg}` });
                  this.getCardList();
                }
              } else {
                console.log('服务器扑街！');
              }
            });
      },
      handleSetStatus (card, newStatus) {
        card.status = newStatus;
        if (this.handleSupportMobile(card)) {
          if (newStatus == 1) card.sale_status = '1';
          this.$service
              .post(
                  '/Web/Card/set_card_status',
                  {
                    id: card.id,
                    bus_id: card.bus_id,
                    status: newStatus
                  },
                  {
                    loading: false
                  }
              )
              .then(res => {
                if (res.status == 200) {
                  if (res.data.errorcode !== 0) {
                    this.$Message.error({ content: `设置失败，${res.data.errormsg}` });
                    this.getCardList();
                  }
                } else {
                  console.log('服务器扑街！');
                }
              });
        }
      },
      handleDelete (idArr, callback) {
        return this.$service.get(`/Web/Card/delete_cards/ids/${idArr}`).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.$Message.success({ content: '删除成功' });
              callback();
            } else {
              this.$Message.error({ content: `删除失败，${res.data.errormsg}` });
            }
          } else {
            console.log('服务器扑街！');
          }
        });
      },
      otherCase (val) {
        const CASE = {
          '0': () => this.handleDeleteCheckedArray(),
          '1': () => this.$router.push('/gym/protocol'),
          '2': () => this.exportList(),
          '3': () => this.changeCard()
        };
        CASE[val]();
      },
      async changeCard() {
        if (!this.totalCanChangeCard || !this.totalCanChangeCard.length) {
          const allCard = await this.getCardList(this.cardTotal)
          this.totalCanChangeCard = allCard?allCard.filter(item=>item.is_pt_time_limit_card!=1):[]
        }
        this.showChange = true
      },
      handleChangeCard() {
        if (this.changeCardForm && this.changeCardForm.card_ids.length) {
          this.$service.post('/Web/MemberCard/change_card_to_swim', {
            card_ids: this.changeCardForm.card_ids.join(','),
            type: this.cardType === 2 ? 0 : 1 // 0私转泳 1泳转私
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.showChange = false;
              this.getCardList()
            }
          });
        }
      },
      async exportList() {
        let resData = await this.getCardList(this.cardTotal);

        const exportColumns = this.cardColumns.filter((col, index) => {
          return col.key && col.key !== 'action'
        }).map(({ title, key }) => ({ title, key }))
        exportColumns.unshift({
          title: '场馆',
          key: 'bus_name'
        })

        this.$refs.table.exportCsv({
          filename: this.cardType == 1 ? '会籍卡':this.cardType == 2 ? '私教课':'泳教课',
          columns: exportColumns,
          data: resData
        });
      },
      handleDeleteCheckedArray () {
        let idArr = [];
        this.checkedArray.forEach((item, index) => {
          idArr.push(item.id);
        });
        if (idArr.length === 0) {
          this.$Message.error({ content: '请选择需要删除的卡！' });
          return false;
        }
        this.handleDelete(idArr.toString(), () => {
          this.cardList = this.cardList.filter(card => !idArr.find(id => id === card.id));
        });
      },
      handleCheckedArray (selection) {
        this.checkedArray = selection;
      },
      getSupportMobile () {
        return this.$service.get('/Web/Card/check_surport_online').then(res => {
          if (res.data.errorcode == 0) {
            this.supportMobileAuthority = res.data.data.status == 1;
          }
        });
      },
      handleSupportMobile (card) {
        if (!this.supportMobileAuthority && card.status == 1) {
          this.supportMobileModal = true;
          setTimeout(() => {
            card.status = '0';
          }, 1000);
          return false;
        } else {
          return true;
        }
      },
      handleDownloadWord () {
        window.open("https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf");
      },
      handleToCardRules() {
        this.$router.push({ name: '购卡规则设置' })
      },
      // 显示批量修改储值卡续充设置弹窗
      handleShowReChangeModal() {
        this.showRechargeModal = true
      }
    },
    created () {
      this.postData.bus_id = this.busId
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      this.getCardTypeList()
      this.getSupportMobile();
      this.doseHasAuth()
    }
  };
</script>

