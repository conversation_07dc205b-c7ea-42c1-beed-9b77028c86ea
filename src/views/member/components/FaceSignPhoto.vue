<template>
  <Modal v-model="showModal" title="签到照片">
    <div class="sign-wrap">
      <div class="sign-item" v-if="curInfo.sign_in_img">
        <img :src="curInfo.sign_in_img" alt="">
        <div>上课签到</div>
      </div>
      <div class="sign-item" v-if="curInfo.sign_out_img">
        <img :src="curInfo.sign_out_img" alt="">
        <div>下课签到</div>
      </div>
    </div>
    <div slot="footer"></div>
  </Modal>
</template>

<script>
  export default {
    name: 'FaceSignPhoto',
    props: {
      data: {
        type: Object,
        default: ()=> {
          return {
            sign_in_img: '',
            sign_out_img: ''
          }
        }
      },
      value: {
        type: Boolean
      }
    },
    components: {},
    data() {
      return {
        curInfo: {
          sign_in_img: '',
          sign_out_img: ''
        }
      }
    },
    computed: {
      showModal: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showModal(val) {
        if (val && this.data) {
          Object.assign(this.curInfo, this.data)
        }
      }
    },
    created() {
    },
    methods: {
    }
  }
</script>

<style lang="less" scoped>
.sign-wrap {
  display: flex;
  justify-content: center;
}
.sign-item {
  margin-left: 50px;
  text-align: center;
  font-size: 16px;
  img {
    width: 150px;
  }
}
</style>
