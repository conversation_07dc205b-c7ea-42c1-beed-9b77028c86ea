<template>
  <div class="table-wrap">
    <div class="member-header">
      <template v-if="curMenu=='其它门店会员'">
        <div class="user-search-item">
          <div class="user-search-main">
            <Select
              v-model="postData.belong_bus_id"
              class="w120"
              placeholder="可用卡场馆"
              transfer
              clearable
              filterable>
              <Option v-for="option in adminBusList" :key="option.id" :value="option.id">{{ option.name }}</Option>
            </Select>
            <Input
              v-model="postData.search"
              class="user-search"
              placeholder="会员名/电话/卡号/身份证"
              @on-enter="handleSearch"></Input>
            <Select
              v-model="postData.status"
              placeholder="会员卡状态(多选)"
              clearable
              multiple>
              <Option v-for="(option,index) in statusOption" :key="index" :value="index+1">{{ option }} </Option>
            </Select>
            <SaleSelect
              v-if="postData.belong_bus_id"
              v-model="postData.marketers_id"
              :belong-bus-id="postData.belong_bus_id"
              placeholder="跟进会籍"
              class="w120"
              clearable>
              <Option label="无跟进" value="-1"></Option>
            </SaleSelect>
            <SaleSelect
              v-if="postData.belong_bus_id"
              v-model="postData.followup_coach_id"
              :belong-bus-id="postData.belong_bus_id"
              placeholder="跟进教练"
              class="w120"
              isCoach
              :isMembership="false"
              :showCoachText="false">
              <Option label="无跟进私教" value="-1"></Option>
              <Option label="无跟进泳教" value="-2"></Option>
            </SaleSelect>
            <Button type="success" @click="handleSearch">搜索</Button>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="user-search-item">
          <div class="user-search-left">会员</div>
          <div class="pos-form" :class="!showUser?'min-show-hidden':''">
            <div class="pos-left">
              <Select
                v-model="postData.belong_bus_id"
                class="w120"
                placeholder="归属场馆"
                transfer
                clearable
                filterable>
                <Option v-for="option in adminBusList" :key="option.id" :value="option.id">{{ option.name }}</Option>
              </Select>
              <Input
                v-model="postData.search"
                class="user-search"
                placeholder="会员名/电话/卡号/身份证"
                @on-enter="handleSearch"></Input>
            </div>
            <div class="pos-rig">
              <MemberForm v-model="postData" :sources="sources" />
            </div>
          </div>

          <div class="user-search-rig" @click="handleMore('showUser')">
            <Icon v-if="showUser" type="ios-arrow-up" />
            <Icon v-else type="ios-arrow-down" />
            {{ showUser?'收起':'展开' }}
          </div>
        </div>
        <div v-show="postData.belong_bus_id" class="user-search-item">
          <div class="user-search-left">会籍卡</div>
          <div class="user-search-main" :class="showMs?'':'min-show-hidden'" :style="{height: !showMs?(msSelectHeight+18) + 'px':'auto'}">
            <Select
              v-if="memberCardList.card_list"
              ref="msCardSelect"
              v-model="postData.card_id"
              class="group-select"
              placeholder="会籍卡种类(多选)"
              clearable
              filterable
              multiple
              @on-change="msCardChange">
              <Option value="-10086" style="padding:0;background:#fff;">
                <div class="option-tab-wrap">
                  <div :class="postData._card_id_search_type==='any'?'option-tab-item active':'option-tab-item'" @click.self.stop="handleOptTab('_card_id_search_type','any')">
                    满足选中任一条件
                  </div>
                  <div :class="postData._card_id_search_type==='all'?'option-tab-item active':'option-tab-item'" @click.self.stop="handleOptTab('_card_id_search_type','all')">
                    满足选中全部条件
                  </div>
                </div>
              </Option>
              <Option value="-1">无会籍卡</Option>
              <Option value="-2" class="group-title">普通会籍卡</Option>
              <Option value="-4" class="group-title pl16" :disabled="postData.card_id.indexOf('-2')!==-1">期限卡</Option>
              <Option
                v-for="card in memberCardList.card_list.filter(item=>item.card_type_id==1 &&item.experience_card !== '1')"
                :key="card.card_id"
                class="pl24"
                :value="card.card_id"
                :disabled="postData.card_id.indexOf('-2')!==-1 || postData.card_id.indexOf('-4')!==-1">
                {{ card.card_name
                }}
              </Option>
              <Option value="-5" class="group-title pl16" :disabled="postData.card_id.indexOf('-2')!==-1">次卡</Option>
              <Option
                v-for="card in memberCardList.card_list.filter(item=>item.card_type_id==2 &&item.experience_card !== '1')"
                :key="card.card_id"
                class="pl24"
                :disabled="postData.card_id.indexOf('-2')!==-1 || postData.card_id.indexOf('-5')!==-1"
                :value="card.card_id">
                {{ card.card_name
                }}
              </Option>
              <Option value="-6" class="group-title pl16" :disabled="postData.card_id && (postData.card_id.indexOf('-2')!==-1)">储值卡</Option>
              <Option
                v-for="card in memberCardList.card_list.filter(item=>item.card_type_id==3 &&item.experience_card !== '1')"
                :key="card.card_id"
                class="pl24"
                :disabled="postData.card_id.indexOf('-2')!==-1 || postData.card_id.indexOf('-6')!==-1"
                :value="card.card_id">
                {{ card.card_name
                }}
              </Option>
              <Option value="-3" class="group-title">体验卡</Option>
              <Option
                v-for="card in memberCardList.card_list"
                v-if="card.experience_card === '1'"
                :key="card.card_id"
                :value="card.card_id"
                :disabled="postData.card_id.indexOf('-3')!==-1">
                {{ card.card_name
                }}
              </Option>
            </Select>
            <Select
              ref="msStatusSelect"
              v-model="postData.ms_status"
              placeholder="会籍卡状态(多选)"
              clearable
              multiple
              @on-change="getSelectHeight">
              <Option v-for="(option,index) in statusOption" :key="index" :value="index+1">{{ option }} </Option>
            </Select>
            <div v-if="postData.card_id && postData.card_id.length<2 && (postData.card_id[0] == -5||firstMsCard.card_type_id == 2)" class="input-group">
              <div class="input-before">
                <span class="before-label">剩余总次数</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <InputNumber v-model="postData.surplus_begin_num" class="min-input"></InputNumber>
                  <span>~</span>
                  <InputNumber v-model="postData.surplus_end_num" class="min-input"></InputNumber>
                  <span>次</span>
                </div>
              </div>
            </div>
            <div v-if="postData.card_id && postData.card_id.length<2 && (postData.card_id[0] == -6||firstMsCard.card_type_id == 3)" class="input-group">
              <div class="input-before">
                <span class="before-label">剩余总金额</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <InputNumber v-model="postData.surplus_begin_sum" class="min-input"></InputNumber>
                  <span>~</span>
                  <InputNumber v-model="postData.surplus_end_sum" class="min-input"></InputNumber>
                  <span>元</span>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">购卡时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    v-model="cardCreateDateRange"
                    placeholder="请选择日期区间"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'card_create_begin_time', 'card_create_end_time')"></Date-picker>
                </div>
              </div>
            </div>

            <Select
              v-if="sources"
              v-model="postData.card_source_id"
              placeholder="会籍成交方式"
              clearable>
              <Option v-for="item in sources.type2" :key="item.source_id" :value="item.source_id">{{ item.source_name }}</Option>
            </Select>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">激活时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    v-model="activeDateRange"
                    placeholder="请选择日期区间"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'active_begin_time', 'active_end_time')"></Date-picker>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">卡过期时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    placeholder="请选择日期区间"
                    :value="cardExpirationDateRange"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'card_expiration_begin_time', 'card_expiration_end_time')"></Date-picker>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">会籍过期</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    placeholder="请选择日期区间"
                    :value="expirationDateRange"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'expiration_begin_time', 'expiration_end_time')"></Date-picker>
                </div>
              </div>
            </div>
          </div>
          <div class="user-search-rig" @click="handleMore('showMs')">
            <Icon v-if="showMs" type="ios-arrow-up" />
            <Icon v-else type="ios-arrow-down" />
            {{ showMs?'收起':'展开' }}
          </div>
        </div>
        <div v-show="postData.belong_bus_id" class="user-search-item">
          <div class="user-search-left">私教泳教</div>
          <div class="user-search-main" :class="!showPt?'min-show-hidden':''" :style="{height: !showPt?(ptSelectHeight+18) + 'px':'auto'}">
            <Select
              ref="ptCardSelect"
              v-model="postData.pt_card_id"
              class="group-select"
              placeholder="私教/泳教课(多选)"
              clearable
              filterable
              multiple
              @on-change="ptCardChange">
              <Option value="-10086" style="padding:0;background:#fff;">
                <div class="option-tab-wrap">
                  <div :class="postData._pt_card_id_search_type==='any'?'option-tab-item active':'option-tab-item'" @click.self.stop="handleOptTab('_pt_card_id_search_type','any')">
                    满足选中任一条件
                  </div>
                  <div :class="postData._pt_card_id_search_type==='all'?'option-tab-item active':'option-tab-item'" @click.self.stop="handleOptTab('_pt_card_id_search_type','all')">
                    满足选中全部条件
                  </div>
                </div>
              </Option>
              <Option class="pl16" value="-1">无私教课</Option>
              <Option class="pl16" value="-2">无泳教课</Option>
              <Option value="-3" class="group-title">私教课</Option>
              <Option
                v-for="card in memberCardList.private_card_list"
                v-if="card.experience_card !== '1'"
                :key="card.card_id"
                class="pl16"
                :value="card.card_id"
                :disabled="postData.pt_card_id.indexOf('-3')!==-1">
                {{
                  card.card_name }}
              </Option>
              <Option value="-4" class="group-title">私教体验课</Option>
              <Option
                v-for="card in memberCardList.private_card_list"
                v-if="card.experience_card === '1'"
                :key="card.card_id"
                class="pl16"
                :value="card.card_id"
                :disabled="postData.pt_card_id.indexOf('-4')!==-1">
                {{ card.card_name }}
              </Option>
              <Option value="-5" class="group-title">泳教课</Option>
              <Option
                v-for="card in memberCardList.swimming_card_list"
                v-if="card.experience_card !== '1'"
                :key="card.card_id"
                class="pl16"
                :disabled="postData.pt_card_id.indexOf('-5')!==-1"
                :value="card.card_id">
                {{
                  card.card_name }}
              </Option>
              <Option value="-6" class="group-title">泳教体验课</Option>
              <Option
                v-for="card in memberCardList.swimming_card_list"
                v-if="card.experience_card === '1'"
                :key="card.card_id"
                class="pl16"
                :disabled="postData.pt_card_id.indexOf('-6')!==-1"
                :value="card.card_id">
                {{ card.card_name }}
              </Option>
            </Select>
            <Select
              ref=""
              ref="ptStatusSelect"
              v-model="postData.pt_status"
              placeholder="课程状态(多选)"
              clearable
              multiple
              @on-change="getPtSelectHeight">
              <Option v-for="(option,index) in statusOption" :key="index" :value="index+1">{{ option }} </Option>
            </Select>
            <div v-if="!postData.pt_card_id || (postData.pt_card_id && postData.pt_card_id.length<2)" class="input-group">
              <div class="input-before">
                <span class="before-label">剩余总节数</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <InputNumber v-model="postData.pt_surplus_begin_num" class="min-input"></InputNumber>
                  <span>~</span>
                  <InputNumber v-model="postData.pt_surplus_end_num" class="min-input"></InputNumber>
                  <span>节</span>
                </div>
              </div>
            </div>
            <SaleSelect
              v-if="postData.belong_bus_id"
              v-model="postData.class_coach_id"
              :belong-bus-id="postData.belong_bus_id"
              placeholder="上课教练"
              class="w120"
              isCoach
              :isMembership="false"
              :showCoachText="false"
              clearable />
            <Select
              v-if="sources"
              v-model="postData.pt_source_id"
              placeholder="课程成交方式"
              clearable>
              <Option v-for="item in sources.type3" :key="item.source_id" :value="item.source_id">{{ item.source_name }}</Option>
            </Select>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">购课时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    v-model="ptCardDateRange"
                    placeholder="请选择日期区间"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'pt_card_create_begin_time', 'pt_card_create_end_time')"></Date-picker>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">激活时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    v-model="ptActiveDateRange"
                    placeholder="请选择日期区间"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'pt_active_begin_time', 'pt_active_end_time')"></Date-picker>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">课过期时间</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    placeholder="请选择日期区间"
                    :value="ptCardExpDateRange"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'pt_card_expiration_begin_time', 'pt_card_expiration_end_time')"></Date-picker>
                </div>
              </div>
            </div>
            <div class="input-group">
              <div class="input-before">
                <span class="before-label">私教泳教过期</span>
              </div>
              <div class="input-after">
                <div class="min-wrap">
                  <Date-picker
                    placeholder="请选择日期区间"
                    :value="ptExpDateRange"
                    type="daterange"
                    :editable="false"
                    clearable
                    transfer
                    @on-change="onDateChange($event, 'pt_expiration_begin_time', 'pt_expiration_end_time')"></Date-picker>
                </div>
              </div>
            </div>
          </div>
          <div class="user-search-rig" @click="handleMore('showPt')">
            <Icon v-if="showPt" type="ios-arrow-up" />
            <Icon v-else type="ios-arrow-down" />
            {{ showPt?'收起':'展开' }}
          </div>
        </div>
      </template>
      <div v-if="curMenu!=='其它门店会员'" class="user-button">
        <div class="user-lef">
          <Button @click="resetSearch">重置</Button>
          <Button type="success" @click="handleSearch">搜索</Button>
        </div>
        <!-- <Checkbox v-model="single">满足全部会员卡条件</Checkbox> -->
        <!-- <Checkbox v-model="single">隐藏无关的卡课数据</Checkbox> -->
        <div class="user-rig" @click="showSetColumn">
          <Icon type="ios-funnel" />
          自定义列
        </div>
      </div>
    </div>

    <main>
      <Table
        ref="table"
        :columns="columns"
        :data="tableData"
        stripe
        disabled-hover
        class="member-avatar-zoom"
        @on-select="selectMember"
        @on-selection-change="selectMemberChange"
        @on-select-all="selectMemberAll"
        @on-select-cancel="selectMemberCancel"></Table>
    </main>

    <footer>
      <template v-if="!IS_BRAND_SITE">
        <Button
          v-if="curMenu!=='其它门店会员'"
          type="success"
          style="margin-right:15px"
          @click="addMember">
          潜客添加
        </Button>
        <Button v-if="postData.belong_bus_id" style="margin-right:15px" @click="membershipShow('isShowAdd')">指派会籍</Button>
        <Button v-if="postData.belong_bus_id" style="margin-right:15px" @click="membershipShow('followCoachModal')">指派跟进私教</Button>
        <Button v-if="postData.belong_bus_id" style="margin-right:15px" @click="membershipShow('followSwimCoachModal')">指派跟进泳教</Button>
        <Button v-if="postData.belong_bus_id && curMenu!=='其它门店会员'" style="margin-right:15px" @click="membershipShow('classCoachModal')">指派上课教练</Button>
        <Dropdown placement="top" @on-click="otherCase">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem v-if="postData.belong_bus_id" name="0">
              发短信
            </DropdownItem>
            <DropdownItem name="1">跨店升卡</DropdownItem>
            <DropdownItem v-if="excelAuth" name="2">
              导出excel
              <!-- <Export ref="export"/> -->
            </DropdownItem>
            <DropdownItem v-if="curMenu!== '其它门店会员'" name="3">
              删除会员
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </template>
      <Page
        :total="+totalCount"
        :current.sync="postData.page_no"
        placement="top"
        show-total
        show-sizer
        @on-change="handlePageChange"
        @on-page-size-change="pageSizeChanged" />
    </footer>
    <AssignMembership
      v-model="isShowAdd"
      :belong-bus-id="postData.belong_bus_id"
      :selected-members="selectedMembers"
      @on-success="actionSuccess" />
    <AssignFollowCoach
      v-model="followCoachModal"
      :belong-bus-id="postData.belong_bus_id"
      :selected-members="selectedMembers"
      @on-success="actionSuccess" />
    <AssignFollowSwimCoach
      v-model="followSwimCoachModal"
      :belong-bus-id="postData.belong_bus_id"
      :selected-members="selectedMembers"
      @on-success="actionSuccess" />
    <AssignClassCoach
      v-model="classCoachModal"
      :belong-bus-id="postData.belong_bus_id"
      :selected-members="selectedMembers"
      from="list"
      @on-success="actionSuccess" />
    <SetMemberColumn v-model="isShowSetColumn" @on-confirm="refreshColumn" />
    <!-- <Modal :mask-closable="false"
         v-model="isShowExcelModal"
         title="导出">
        <div>
      <RadioGroup v-model="isExportMerge" vertical class="radio-lines" @on-change="exportTable">
          <Radio :label="1">
              <span>会员名称合并导出</span>
          </Radio>
          <Radio :label="0">
              <span>会员名称不合并导出</span>
          </Radio>
        </RadioGroup>

    </div>
      <div slot="footer">
      </div>
    </Modal> -->
    <Modal
        v-model="modalBool"
        title="提示"
        type="warning"
        @on-ok="handleDelete"
        @on-cancel="()=>{this.modalBool=false;this.modalContent='';}">
        <p>{{modalContent}}</p>
    </Modal>
  </div>
</template>

<script>
import SaleSelect from 'src/components/membership/salesSelect'
import AssignMembership from './AssignMembership'
import SetMemberColumn from './SetMemberColumn'
import AssignFollowCoach from './AssignFollowCoach.vue'
import AssignFollowSwimCoach from './AssignFollowSwimCoach.vue'
import AssignClassCoach from './AssignClassCoach.vue'
import MemberForm from './MemberForm.vue'
// import Export from 'src/components/Export'
import { formatDate } from 'utils/index.js'
import { mapGetters, mapState, mapActions } from 'vuex'
import { getSources } from 'src/service/getData'
const SEARCHOBJ = {
  belong_bus_id: '', //归属场馆
  is_order_bus_user: '', //是否是查询其他门店会员 0为不是 1为是
  is_top_search: '', //是否顶部搜索 0为不是 1为是
  user_level: '', // 客户级别
  user_tag_id: '', // 客户标签
  user_source_id: '', //获课来源id
  card_source_id: '', // 会籍成交方式id
  pt_source_id: '', // 私教成交方式id
  card_id: '', // 会籍卡id 0为所有会籍卡
  pt_card_id: '', //  会员卡id 0为所有私教泳教会员 -1为无私教卡 -2为无泳教卡 -3所有私教课 -4为所有私教体验课 -5 为所有泳教卡 -6为所有泳教体验卡
  status: [], //会员卡状态
  ms_status: [], //会籍卡状态
  pt_status: [], //私教卡状态
  search: '', //姓名/电话/实体卡号
  marketers_id: '', // 归属会籍id
  followup_coach_id: '', // 跟进教练ID
  class_coach_id: '', // 上课教练
  expiration_begin_time: '', // 过期时间开始时间
  expiration_end_time: '', // 过期时间结束时间
  card_expiration_begin_time: '',
  card_expiration_end_time: '',
  surplus_begin_num: null, //剩余次数区间开始次数
  surplus_end_num: null, //剩余次数区间结束次数
  pt_surplus_begin_num: null,
  pt_surplus_end_num: null,
  surplus_begin_sum: null, //剩余金额区间开始金额
  surplus_end_sum: null, //剩余金额区间结束金额
  sign_begin_num: null, // 长时间未到场区间开始天数
  sign_end_num: null, // 长时间未到场区间结束天数
  follow_begin_num: null, // 长时间未跟进区间开始天数
  follow_end_num: null, // 长时间未跟进区间结束天数
  pt_follow_begin_num: null, // 私教长时间未跟进区间开始天数
  pt_follow_end_num: null, // 私教长时间未跟进区间结束天数
  swim_follow_begin_num: null, // 泳教长时间未跟进区间开始天数
  swim_follow_end_num: null, // 泳教长时间未跟进区间开始天数
  sex: '', // 1男 2女
  birthday_begin_time: '', // 出生日期区间开始时间
  birthday_end_time: '', //出生日期区间结束时间
  card_create_begin_time: '',
  card_create_end_time: '',
  pt_card_create_begin_time: '',
  pt_card_create_end_time: '',
  pt_active_begin_time: '',
  pt_active_end_time: '',
  pt_card_expiration_begin_time: '',
  pt_card_expiration_end_time: '',
  pt_expiration_begin_time: '',
  pt_expiration_end_time: '',
  buy_card_begin_time: '', // 购卡入会区间开始时间
  buy_card_end_time: '', //购卡入会区间结束时间
  active_begin_time: '', //激活区间开始时间
  active_end_time: '', //激活区间结束时间
  ub_create_begin_time: '', //注册开始时间
  ub_create_end_time: '', //注册结束时间
  age_begin_num: null, // 年龄开始岁数
  age_end_num: null, // 年龄结束岁数
  signpt_begin_num: null, //最近未上私教|
  signpt_end_num: null, //最近有上私教|
  signsw_begin_num: null, //最近未上泳教|
  signsw_end_num: null, //最近有上泳教|
  card_buy_begin_num: null, //最近未购买|
  card_buy_end_num: null, //最近有购买|
  card_buy_times_logic: '1', //有效购买次数筛选方式：1--大于， 2--小于，3--区间|
  card_buy_times_begin_num: null, //有效购买次数（开始）|
  card_buy_times_end_num: null, // 有效购买次数（结束）|
  card_buy_amount_logic: '1', //消费金额筛选方式：1--大于， 2--小于，3--区间|
  card_buy_amount_begin_sum: null, //消费金额（开始）|
  card_buy_amount_end_sum: null, // 消费金额（结束）|
  card_buy_hj_times_logic: '1', // 购买会籍卡次数筛选方式：1--大于， 2--小于，3--区间|
  card_buy_hj_times_begin_num: null, // 购买会籍卡次数（开始）|
  card_buy_hj_times_end_num: null, // 购买会籍卡次数（结束）|
  card_buy_sj_times_logic: '1', // 购买私教卡次数筛选方式：1--大于， 2--小于，3--区间
  card_buy_sj_times_begin_num: null, // 购买私教卡次数（开始）
  card_buy_sj_times_end_num: null, // 购买私教卡次数（结束）
  card_buy_yj_times_logic: '1', // 购买泳教卡次数筛选方式：1--大于， 2--小于，3--区间
  card_buy_yj_times_begin_num: null, // 购买泳教卡次数（开始）
  card_buy_yj_times_end_num: null, // 购买泳教卡次数（结束）
  _card_id_search_type: 'any',
  _pt_card_id_search_type: 'any',
  page_no: 1,
  page_size: 10
}
const initSelectKeys = [
  'avatar',
  'username',
  'card_name',
  'status',
  'allplus',
  'overplus',
  'cu_deal_time',
  'end_time',
  'marketers_name',
  'followup_coach_name',
  'followup_swim_coach_name'
]
export default {
  name: 'MemberTable',
  components: {
    SaleSelect,
    AssignMembership,
    // Export,
    AssignFollowCoach,
    AssignFollowSwimCoach,
    AssignClassCoach,
    MemberForm,
    SetMemberColumn
  },
  props: {
    curMenu: {
      type: String,
      default: ''
    },
    fastMenu: {
      type: String,
      default: ''
    },
    groupParams: {
      type: Object
    }
  },
  data() {
    return {
      IS_BRAND_SITE: window.IS_BRAND_SITE || false,
      showUser: false,
      isRest: false,
      single: false,
      showMs: false,
      showPt: false,
      msSelectHeight: 32,
      ptSelectHeight: 32,
      sources: null,
      isShowSetColumn: false,
      isShowExcelModal: false,
      isExportMerge: '',
      postData: { ...SEARCHOBJ },
      excelAuth: false,
      isShowAdd: false,
      followCoachModal: false,
      followSwimCoachModal: false,
      classCoachModal: false,
      selectedMembers: [],
      userTags: [],
      selectedKeys: [...initSelectKeys],
      fastSearchObj: {
        会籍会员: {
          card_id: ['-2']
        },
        有效会籍会员: {
          card_id: [ '-2'],
          ms_status: [1, 2, 3]
        },
        会籍潜客: {
          card_id: ['-1']
        },
        次卡耗尽会员: {
          card_id: ['-5'],
          surplus_begin_num: 0,
          surplus_end_num: 0
        },
        会籍过期会员: {
          card_id: ['-2'],
          expiration_begin_time: formatDate(new Date(1970, 0, 1), 'yyyy-MM-dd'),
          expiration_end_time: formatDate(new Date(Date.now() - 86400000), 'yyyy-MM-dd'),
        },
        无会籍跟进潜客: {
          card_id: ['-1'],
          marketers_id: '-1'
        },
        即将会籍到期: {
          card_id: ['-2'],
          expiration_begin_time: formatDate(new Date(), 'yyyy-MM-dd'), // fix 12925 即将会籍到期、即将私教到期、即将泳教到期快捷筛选时，查询时间范围只是从当天往后30天
          expiration_end_time: formatDate(new Date(Date.now() + 86400000 * 30), 'yyyy-MM-dd')
        },
        会籍次数即将耗尽: {
          card_id: ['-5'],
          surplus_begin_num: 0,
          surplus_end_num: 10
        },
        储值卡即将耗尽: {
          card_id: ['-6'],
          surplus_begin_sum: 0,
          surplus_end_sum: 300
        },
        会员即将过生日: {
          card_id: ['-2'],
          // birthday_begin_time: formatDate(new Date(), 'yyyy-MM-dd'),
          // birthday_end_time: formatDate(new Date(Date.now() + 86400000 * 30), 'yyyy-MM-dd')
          // Fix: 12645
          birthday_begin_time: formatDate(new Date(), 'MM-dd'),
          birthday_end_time: formatDate(new Date(Date.now() + 86400000 * 30), 'MM-dd')
        },
        长期未到场会员: {
          card_id: ['-2'],
          sign_begin_num: 15,
          sign_end_num: null
        },
        会籍长期未跟进: {
          card_id: ['-2'],
          follow_begin_num: 15,
          follow_end_num: null
        },
        私教会员: {
          pt_card_id: ['-3']
        },
        有效私教会员: {
          pt_card_id: ['-3'],
          pt_status: [1, 2, 3]
        },
        私教潜客: {
          pt_card_id: ['-1'],
          card_id: ['-2'],
          ms_status: [1, 2, 3]
        },
        过期私教会员: {
          pt_card_id: ['-3'],
          pt_expiration_begin_time: formatDate(new Date(1970, 0, 1), 'yyyy-MM-dd'),
          pt_expiration_end_time: formatDate(new Date(Date.now() - 86400000), 'yyyy-MM-dd')
        },
        私教节数耗尽会员: {
          pt_card_id: ['-3'],
          pt_surplus_begin_num: 0,
          pt_surplus_end_num: 0
        },
        无教练跟进私教潜客: {
          pt_card_id: ['-1'],
          card_id: ['-2'],
          ms_status: [1, 2, 3],
          followup_coach_id: '-1'
        },
        即将私教到期: {
          pt_card_id: ['-3'],
          pt_expiration_begin_time: formatDate(new Date(), 'yyyy-MM-dd'), // fix 12925 即将会籍到期、即将私教到期、即将泳教到期快捷筛选时，查询时间范围只是从当天往后30天
          pt_expiration_end_time: formatDate(new Date(Date.now() + 86400000 * 30), 'yyyy-MM-dd')
        },
        私教节数即将耗尽: {
          pt_card_id: ['-3'],
          pt_surplus_begin_num: 0,
          pt_surplus_end_num: 10
        },
        长期未到场私教会员: {
          pt_card_id: ['-3'],
          signpt_begin_num: 15
        },
        私教长期未跟进: {
          pt_card_id: ['-3'],
          pt_follow_begin_num: 15,
          pt_follow_end_num: null
        },
        // 泳教课 -5  无泳教课 -2
        泳教会员: {
          pt_card_id: ['-5']
        },
        有效泳教会员: {
          pt_card_id: ['-5'],
          pt_status: [1, 2, 3]
        },
        泳教潜客: {
          pt_card_id: ['-2'],
          card_id: ['-2'],
          ms_status: [1, 2, 3]
        },
        过期泳教会员: {
          pt_card_id: ['-5'],
          pt_expiration_begin_time: formatDate(new Date(1970, 0, 1), 'yyyy-MM-dd'),
          pt_expiration_end_time: formatDate(new Date(Date.now() - 86400000), 'yyyy-MM-dd')
        },
        泳教节数耗尽会员: {
          pt_card_id: ['-5'],
          pt_surplus_begin_num: 0,
          pt_surplus_end_num: 0
        },
        无教练跟进泳教潜客: {
          pt_card_id: ['-2'],
          card_id: ['-2'],
          ms_status: [1, 2, 3],
          followup_coach_id: '-2'
        },
        即将泳教到期: {
          pt_card_id: ['-5'],
          pt_expiration_begin_time: formatDate(new Date(), 'yyyy-MM-dd'), // fix 12925 即将会籍到期、即将私教到期、即将泳教到期快捷筛选时，查询时间范围只是从当天往后30天
          pt_expiration_end_time: formatDate(new Date(Date.now() + 86400000 * 30), 'yyyy-MM-dd')
        },
        泳教节数即将耗尽: {
          pt_card_id: ['-5'],
          pt_surplus_begin_num: 0,
          pt_surplus_end_num: 10
        },
        长期未到场泳教会员: {
          pt_card_id: ['-5'],
          signsw_begin_num: 15
        },
        泳教长期未跟进: {
          pt_card_id: ['-5'],
          swim_follow_begin_num: 15,
          swim_follow_end_num: null
        }
      },
      statusOption: ['正常', '请假中', '未激活', '过期', '已用完'],
      birthDateRange: ['', ''],
      activeDateRange: ['', ''],
      cardCreateDateRange: ['', ''],
      expirationDateRange: ['', ''],
      cardExpirationDateRange: ['', ''],
      ptCardDateRange: ['', ''],
      ptActiveDateRange: ['', ''],
      ptCardExpDateRange: ['', ''],
      ptExpDateRange: ['', ''],
      createDateRange: ['', ''],
      buyDateRange: ['', ''],
      tableData: [],
      exportData: null,
      sizer: 10,
      totalCount: 0,
      exportTableData: [],
      allColums: [
        {
          type: 'selection',
          width: 60,
          key: 'selection',
          align: 'center'
        },
        {
          title: '头像',
          width: 60,
          key: 'avatar',
          className: 'avatar-wrap',
          render: (h, params) => {
            return (
              <a
                href="javascript:void(0)"
                on-click={name => {
                  this.goDetail(params)
                }}
              >
                <img class="avatar" src={params.row.avatar} />
              </a>
            )
          }
        },
        {
          title: '归属门店',
          minWidth: 120,
          key: 'belong_bus_name'
        },
        {
          title: '姓名',
          minWidth: 120,
          key: 'username',
          render: (h, params) => {
            return (
              <div>
                <a
                  href="javascript:void(0)"
                  on-click={name => {
                    this.goDetail(params)
                  }}
                >
                  {params.row.username || '未知'}
                </a>
                {params.row.is_wx ? <span class="icon-wx" /> : ''}
                {params.row.bind_wx_palmservice ? <span class="icon-palm" /> : ''}
              </div>
            )
          }
        },
        {
          title: '性别',
          minWidth: 50,
          key: 'sex',
          render: (h, params) => {
            return <span>{params.row.sex == 2 ? '女' : '男'}</span>
          }
        },
        {
          title: '出生年月',
          minWidth: 100,
          key: 'birthday'
        },
        {
          title: '消费金额',
          minWidth: 100,
          key: 'buy_card_amount'
        },
        {
          title: '本月会籍金额',
          minWidth: 100,
          key: 'tm_hj_amount'
        },
        {
          title: '本月私教金额',
          minWidth: 100,
          key: 'tm_sj_amount'
        },
        {
          title: '本月泳教金额',
          minWidth: 100,
          key: 'tm_yj_amount'
        },
        {
          title: '购会籍卡次数',
          minWidth: 100,
          key: 'buy_hj_times'
        },
        {
          title: '购私教卡次数',
          minWidth: 100,
          key: 'buy_sj_times'
        },
        {
          title: '购泳教卡次数',
          minWidth: 100,
          key: 'buy_yj_times'
        },
        {
          title: '身份证号',
          minWidth: 100,
          key: 'id_code'
        },
        {
          title: '客户级别',
          minWidth: 80,
          key: 'user_level'
        },
        {
          title: '客户标签',
          minWidth: 80,
          key: 'tag_name'
        },
        {
          title: '注册时间',
          minWidth: 100,
          key: 'ub_create_time'
        },
        {
          title: '客户来源',
          minWidth: 100,
          key: 'source_name'
        },
        {
          title: '卡课名称',
          minWidth: 120,
          key: 'card_name',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'card_name')
          }
        },
        {
          title: '卡号',
          minWidth: 120,
          key: 'card_sn',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'card_sn')
          }
        },
        {
          title: '状态',
          minWidth: 100,
          key: 'status',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'status')
          }
        },
        {
          title: '总计',
          minWidth: 100,
          key: 'allplus',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'allplus')
          }
        },
        {
          title: '使用量',
          minWidth: 100,
          key: 'usedplus',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'usedplus')
          }
        },
        {
          title: '剩余',
          minWidth: 100,
          key: 'overplus',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'overplus')
          }
        },
        {
          title: '购卡时间',
          minWidth: 120,
          key: 'cu_deal_time',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'cu_deal_time')
          }
        },
        {
          title: '开卡时间',
          minWidth: 120,
          key: 'active_time',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'active_time')
          }
        },
        {
          title: '到期时间',
          minWidth: 120,
          key: 'end_time',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'end_time')
          }
        },
        {
          title: '成交方式',
          minWidth: 120,
          key: 'source_name2',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'source_name')
          }
        },
        {
          title: '上课教练',
          minWidth: 100,
          tooltip: true,
          key: 'coach_name',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_list, 'coach_name')
          }
        },
        {
          title: '跟进会籍',
          minWidth: 100,
          key: 'marketers_name',
          render: (h, params) => {
            const rowInfo = params.row
            return <span>{rowInfo.marketers_name || '-'}</span>
          }
        },
        {
          title: '跟进教练',
          minWidth: 100,
          key: 'followup_coach_name',
          render: (h, params) => {
            const rowInfo = params.row
            return <span>{rowInfo.followup_coach_name || '-'}</span>
          }
        },
        {
          title: '跟进泳教',
          minWidth: 100,
          key: 'followup_swim_coach_name',
          render: (h, params) => {
            const rowInfo = params.row
            return <span>{rowInfo.followup_swim_coach_name || '-'}</span>
          }
        },

        // {
        //   title: '积分',
        //   minWidth: 100,
        //   key: 'point',
        //   render: (h, { row }) => {
        //     return <span>{ row.point || 0 }</span>
        //   }
        // }
      ],
      columns: [],
      firstMsCard: {},
      firstPtCard: {},
      // 确认删除弹窗控制变量
      modalBool: false,
      // 确认删除弹窗提示内容变量
      modalContent: '',
    }
  },
  computed: {
    ...mapState(['busId', 'adminBusList', 'globalBelongBusId']),
    //来自外部搜索(此时curMenu为search)进入该页面，在这里加入搜索框的键值对
    searchData() {
      let obj = {
        search: this.$route.query.search || '',
        marketers_id: this.$route.query.marketers_id || '',
        followup_coach_id: this.$route.query.followup_coach_id || '',
        class_coach_id: this.$route.query.class_coach_id || '',
        coach_id: this.$route.query.coach_id || '',
        valid_type: this.$route.query.valid_type || '',
        card_id: this.$route.query.card_id || ''
      }
      return obj
    },
    hasQuery() {
      return Object.values(this.searchData).some(item => !!item)
    },
    ...mapGetters(['memberCardList']),
    selectedUserIds() {
      let userIds = []
      this.selectedMembers.forEach(user => {
        userIds.push(user.user_id)
      })
      return userIds
    }
  },
  created() {
    this.getExcelAuth()
    this.getSourcesList()
    this.refreshColumn()
    if (this.hasQuery) {
      this.postData.belong_bus_id = this.busId
      this.postData = { ...this.postData, ...this.searchData }
      if (this.curMenu === 'search') {
        this.postData.is_top_search = 1
        this.postData.is_order_bus_user = ''
      }
      this.getUserList()
    } else {
      this.initByCurname()
    }
    !this.adminBusList && this.$store.dispatch('getAdminBusList')
  },
  methods: {
    ...mapActions(['getAdminInfo', 'getReceiptAuth']),
    handleOptTab(key, val) {
      this.postData[key] = val
    },
    resetSearch() {
      this.postData = {
        ...SEARCHOBJ,
        belong_bus_id: this.IS_BRAND_SITE ? '' : this.busId
      }
      if(this.fastMenu!='') {
        this.isRest = true
      }
      this.timeRangeReset()
      this.$emit('on-fast-reset')
    },
    timeRangeReset(){
      this.buyDateRange = []
      this.expirationDateRange = []
      this.cardExpirationDateRange = []
      this.createDateRange = []
      this.birthDateRange = []
      this.activeDateRange = []
      this.cardCreateDateRange = []
      this.ptCardDateRange = []
      this.ptActiveDateRange = []
      this.ptCardExpDateRange = []
      this.ptExpDateRange = []
    },
    timeRangeSet(info){
      this.buyDateRange = info['buy_card_begin_time']?[info['buy_card_begin_time'], info['buy_card_end_time']] : []
      this.expirationDateRange = info['expiration_begin_time']?[info['expiration_begin_time'], info['expiration_end_time']] : []
      this.cardExpirationDateRange = info['card_expiration_begin_time']?[info['card_expiration_begin_time'], info['card_expiration_end_time']] : []
      this.createDateRange = info['ub_create_begin_time']?[info['ub_create_begin_time'], info['ub_create_end_time']] : []
      this.birthDateRange = info['birthday_begin_time']?[info['birthday_begin_time'], info['birthday_end_time']] : []
      this.activeDateRange = info['active_begin_time']?[info['active_begin_time'], info['active_end_time']] : []
      this.cardCreateDateRange = info['expiratcard_create_begin_timeion_begin_time']?[info['card_create_begin_time'], info['card_create_end_time']] : []
      this.ptCardDateRange = info['pt_card_create_begin_time']?[info['pt_card_create_begin_time'], info['pt_card_create_end_time']] : []
      this.ptActiveDateRange = info['pt_active_begin_time']?[info['pt_active_begin_time'], info['pt_active_end_time']] : []
      this.ptCardExpDateRange = info['pt_card_expiration_begin_time']?[info['pt_card_expiration_begin_time'], info['pt_card_expiration_end_time']] : []
      this.ptExpDateRange =info['pt_expiration_begin_time']? [info['pt_expiration_begin_time'], info['pt_expiration_end_time']] : []
    },
    showSetColumn() {
      this.isShowSetColumn = true
    },
    refreshColumn(isOtherBus) {
      let curColumns = []
      let memberListColumn = localStorage.getItem('memberListColumnArr')
      let column = isOtherBus===1 ? [...initSelectKeys] : memberListColumn ? JSON.parse(memberListColumn) : [...initSelectKeys]
      this.allColums.forEach(item => {
        if (column.indexOf(item.key) !== -1 || item.key === 'selection') {
          curColumns.push(item)
        }
      })
      this.columns = curColumns
    },
    msCardChange(arr) {
      if (arr.length > 1) {
        this.postData.surplus_begin_num = null
        this.postData.surplus_end_num = null
        this.postData.surplus_begin_sum = null
        this.postData.surplus_end_sum = null
      } else if (arr[0] && arr[0] > 0) {
        for (const iterator of this.memberCardList.card_list) {
          if (iterator.card_id === arr[0]) {
            this.firstMsCard = iterator
            break
          }
        }
      } else {
        this.firstMsCard = {}
      }
      if(!(this.postData.card_id[0] == -5||this.firstMsCard.card_type_id == 2)) {
        this.postData.surplus_begin_num = null
        this.postData.surplus_end_num = null
      }
      if(!(this.postData.card_id[0] == -6||this.firstMsCard.card_type_id == 3)) {
        this.postData.surplus_begin_sum = null
        this.postData.surplus_end_sum = null
      }
      this.getSelectHeight()
    },
    ptCardChange(arr) {
      if (arr.length > 1) {
        this.postData.pt_surplus_begin_num = null
        this.postData.pt_surplus_end_num = null

      } else if (arr[0] && arr[0] > 0) {
        for (const iterator of this.memberCardList.private_card_list) {
          if (iterator.card_id === arr[0]) {
            this.firstPtCard = iterator
            break
          }
        }
      } else {
        this.firstPtCard = {}
      }
      this.getPtSelectHeight()
    },
    getSelectHeight() {
       this.$nextTick(() => {
        let height = this.$refs.msCardSelect?this.$refs.msCardSelect.$el.offsetHeight:''
        let statusHeight = this.$refs.msStatusSelect?this.$refs.msStatusSelect.$el.offsetHeight:''
        this.msSelectHeight = height > statusHeight ? height : statusHeight
      })
    },
    getPtSelectHeight() {
      this.$nextTick(() => {
        let height = this.$refs.ptCardSelect.$el.offsetHeight
        let statusHeight = this.$refs.ptStatusSelect.$el.offsetHeight
        this.ptSelectHeight = height > statusHeight ? height : statusHeight
      })
    },
    handleMore(key) {
      this[key] = !this[key]
    },
    initByCurname() {
      const cur = this.fastMenu || this.curMenu
      if(!cur) {
        return false;
      }
      let fastCur = this.fastSearchObj[cur]
      // 有场馆选中才会加载会员分群下拉
      if (this.fastMenu) {
        this.$emit('on-fast-change', this.fastMenu)
      }
      if (this.groupParams || fastCur) {
        fastCur = this.groupParams || fastCur
        for (const key in fastCur) {
          if(fastCur[key] === '') {
            delete fastCur[key]
          }
        }
        this.postData = {
          ...SEARCHOBJ,
          ...fastCur,
          belong_bus_id: this.postData.belong_bus_id ? this.postData.belong_bus_id : this.IS_BRAND_SITE ? '' : this.busId
        }
        this.timeRangeSet(fastCur)
        this.$nextTick(() => {
          this.getUserList()
        })
      }

      if (this.curMenu === '其它门店会员') {
        this.postData.is_order_bus_user = 1
        this.postData.belong_bus_id = this.busId
        this.refreshColumn(1)
        this.$nextTick(() => {
          this.getUserList()
        })
      } else {
        this.postData.is_order_bus_user = ''
      }
      this.selectedMembers = []
    },
    getSourcesList() {
      getSources({bus_id: this.postData.belong_bus_id}).then(res => {
        if (res.data.errorcode === 0) {
          let resData = res.data.data
          this.sources = resData
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    onDateChange(val, beginKey, endKey) {
      if (!val[0]) {
        this.postData[beginKey] = ''
        this.postData[endKey] = ''
        return false
      }
      this.postData[beginKey] = formatDate(new Date(val[0]), 'yyyy-MM-dd')
      this.postData[endKey] = formatDate(new Date(val[1]), 'yyyy-MM-dd')
    },
    onBirthDateChange(val) {
      if (!val[0]) {
        this.postData.birthday_begin_time = ''
        this.postData.birthday_end_time = ''
        return false
      }
      this.postData.birthday_begin_time = formatDate(new Date(val[0]), 'MM-dd')
      this.postData.birthday_end_time = formatDate(new Date(val[1]), 'MM-dd')
    },
    selectMember(selection, member) {
      let selUserIds = this.selectedUserIds
      if (selUserIds.indexOf(member.user_id) === -1) {
        this.selectedMembers.push(member)
      }
    },
    selectMemberCancel(selection, member) {
      this.selectedMembers.forEach((user, index) => {
        if (user.user_id == member.user_id) {
          this.selectedMembers.splice(index, 1)
        }
      })
    },
    selectMemberChange(selection) {
      if (selection.length == 0) {
        this.tableData.forEach(member => {
          this.selectMemberCancel(selection, member)
        })
      }
    },
    selectMemberAll(selection) {
      if (selection.length > 0) {
        selection.forEach(member => {
          this.selectMember(selection, member)
        })
      }
    },
    cardForColumn(cards, row) {
      if (cards) {
        return (
          <ul class="row-ul">
            {cards.map(item => {
              return (
                <li
                  title= {item[row] || '-'}
                  class={{
                    yellow:
                      row === 'status' &&
                      (item[row] == '请假中' || item[row] == '未激活'),
                    red:
                      row === 'status' &&
                      (item[row] == '已耗完' || item[row] == '过期' || item[row] == '已用完'),
                    green: row === 'status' && item[row] === '正常'
                  }}
                >
                  {item[row] || '-'}
                </li>
              )
            })}
          </ul>
        )
      } else {
        return ''
      }
    },
    handleSearch() {
      this.postData.page_no = 1
      this.getUserList()
    },
    getCard(val, oldVal) {
      this.$store.dispatch('getmemberCardList', val)
    },
    getUserList(allPageCount) {
      let postObj = Object.assign({}, this.postData)
      if (allPageCount) {
        postObj.page_size = allPageCount
        postObj.page_no = 1
      }
      let url = this.IS_BRAND_SITE ? '/Merchant/MemberList/busUserList' : '/Web/MemberList/new_member_list'
      if (this.curMenu === '其它门店会员') {
        url = '/Web/MemberList/order_bus_user_list'
      } else {
        for (const key in postObj) {
          if(key !== 'card_buy_amount_begin_sum' && key !== 'card_buy_amount_end_sum' &&  (/^([0-9]{1,}[.][0-9]*)$/.test(postObj[key]))) {
            this.$Message.error('除金额之外不可输入小数')
            return;
          }
        }
      }
      return this.$service
        .post(url, {
          ...postObj,
          _export: allPageCount ? 1 : 0
        })
        .then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data
              if (!allPageCount) {
                this.totalCount = parseInt(data.count)
                this.tableData = data.list
                //来自顶部搜素且搜索结果唯一
                let searchResultOne =
                  this.curMenu === 'search' && this.tableData.length === 1
                if (
                  searchResultOne &&
                  this.tableData[0].bus_id &&
                  this.tableData[0].bus_id != this.busId
                ) {
                  this.$router.push(`/member/detail/${this.tableData[0].user_id}/${this.tableData[0].bus_id}`)
                } else if (searchResultOne) {
                  this.$router.push(
                    `/member/detail/${this.tableData[0].user_id}`
                  )
                }
                this.tableData.forEach((user, index) => {
                  if (this.selectedUserIds.indexOf(user.user_id) >= 0) {
                    user._checked = true
                  }
                })
              } else {
                this.$Message.success({
                  content: '导出任务运行中，请稍后到消息中心下载!',
                  duration: 3
                })
              }
              return res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            this.$Message.error('服务器连接失败！')
          }
        })
    },
    pageSizeChanged(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getUserList()
    },
    handlePageChange(postData) {
      this.getUserList()
    },
    delMember() {
      if (this.selectedMembers.length < 1) {
        this.$Message.error('请先勾选需要删除的会员!')
        return
      }
      // let names = []
      let names=''
      this.selectedMembers.forEach(item => {
        // names.push(item.username)
        names+=item.username + ', '
      })
      names = names.slice(0,names.length-2)
      // 更改调用方式
      this.modalContent=`您确定要删除已选中的会员 "${names}" 吗？`
      this.modalBool = true
    },
    // 删除会员弹窗确认事件
    handleDelete(){
      this.$service
        .post('/Web/Member/deleteUser', {
          user_id: this.selectedUserIds.join(',')
        })
        .then(res => {
          this.modalBool = false;
          this.modalContent = '';
          if (res.data.errorcode == 0) {
            this.actionSuccess()
            this.$Message.success(res.data.errormsg)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    actionSuccess() {
      this.selectedMembers = []
      this.getUserList()
    },
    addMember() {
      this.$router.push('/member/add')
    },
    membershipShow(type) {
      if (this.selectedUserIds.length > 0) {
        this[type] = true
      } else {
        this.$Message.error('请先选择需要指派的会员')
      }
    },
    sendMsg() {
      if (this.selectedUserIds.length > 0) {
        this.$router.push({
          name: '消息推送',
          params: {
            activeIndex: '2',
            selectedMembers: this.selectedMembers,
          }
        })
      } else {
        this.$Message.error('请先选择需要发送短信的会员')
      }
    },
    getExcelAuth() {
      this.$service.get('/Web/MemberList/member_list_excel').then(res => {
        if (res.data.errorcode === 40014) {
          this.excelAuth = false
        } else {
          this.excelAuth = true
        }
      })
    },
    formatKey(item, key, isCardListOut) {
      if (item.card_list) {
        return item.card_list.map(subItem => {
          if (isCardListOut) {
            return item[key] || ''
          } else if (key === 'card_type_id') {
            return subItem.card_type_id == 1
              ? '期限卡'
              : subItem.card_type_id == 2
                ? '次卡'
                : subItem.card_type_id == 3
                  ? '储值卡'
                  : '私教课'
          } else {
            return subItem[key] || ''
          }
        })
      } else {
        return ''
      }
    },
    otherCase(val) {
      const CASE = {
        '0': () => this.sendMsg(),
        '1': () => this.$router.push('/member/changeCard/0/0'),
        '2': () => {
          // this.isShowExcelModal = true
          this.getUserList(this.totalCount)
        },
        '3': () => this.delMember()
      }
      CASE[val]()
    },
    goDetail(params) {
      if (this.IS_BRAND_SITE || (params.row.bus_id && params.row.bus_id != this.busId)) {
        this.$router.push(`/member/detail/${params.row.user_id}/${params.row.bus_id}`)
      } else {
        this.$router.push(`/member/detail/${params.row.user_id}`)
      }
    }
  },
  watch: {
    fastMenu(val) {
      if(!this.isRest) {
        this.initByCurname(true)
      } else {
        this.isRest = false
      }
    },
    searchData(val, oldVal) {
      if (val != oldVal && this.curMenu === 'search') {
        this.postData = Object.assign({}, this.postData, val)
        this.getUserList()
      }
    },
    'postData.belong_bus_id'(val, oldVal) {
      if (!val || (oldVal && val !== oldVal)) {
        this.postData.marketers_id = ''
        this.postData.class_coach_id = ''
        this.postData.followup_coach_id = ''
        this.postData.coach_id = ''
        this.postData.card_id = []
        this.postData.pt_card_id = []
        this.postData.card_source_id = ''
        this.postData.pt_source_id = ''
        this.selectedMembers = []
      }
      if (val !== oldVal) {
        this.getSourcesList()
        this.$emit('on-bus-change', val)
      }
      this.getCard(val, oldVal)
    }
  }
}
</script>
<style lang="less">
.member-avatar-zoom {
  overflow: visible;
  z-index: 1;
  .avatar {
    z-index: 9;
    transition: all 0.6s;
    transform-origin: 0 0;
    &:hover {
      transform: scale(5);
    }
  }
}

.min-show-hidden {
  height: 50px;
  overflow: hidden;
}
.search-more {
  border-bottom: 1px solid #e0e3e9;
  text-align: center;
  cursor: pointer;
  padding: 5px 0;
  position: relative;
  &:hover {
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.15);
    position: relative;
  }
  .ivu-btn > span {
    color: #515a6e;
    font-size: 12px;
    margin-left: 10px;
  }
}


//下拉分组
.group-select {
  .group-title {
    color: #000;
    padding-left: 8px;
    font-weight: bold;
  }
  .pl16 {
    padding-left: 16px;
  }
  .pl24 {
    padding-left: 24px;
  }
}
</style>
<style lang="less" scoped>
.option-tab-wrap {
  display: flex;
  width: 100%;
  height: 100%;
  border-bottom: 1px solid #eee;
  .option-tab-item {
    flex:1;
    text-align: center;
    height: 24px;
    min-width: 130px;
    &:first-child {
      border-right: 1px solid #eee;
    }
    &:hover,&.active {
      color:#2D8cF0;
    }
  }
}
.pos-form {
  position: relative;
  padding: 10px 35px 0;
  flex: 1;
}
.pos-left {
  position: absolute;
  left: 35px;
  top: 10px;
  width: 310px;
  height: 30px;
  display: flex;
  align-items: center;
  > * {
    min-width: 120px;
    max-width: 270px;
    margin-right: 15px;
  }
}
.user-search-rig {
  cursor: pointer;
}
.user-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e0e3e9;
  .user-lef {
    text-align: right;
    flex: 3;
  }
  button {
    margin-right: 15px;
  }
  .user-rig {
    flex: 2;
    text-align: right;
    cursor: pointer;
  }
}
.row-ul {
  li {
    height: 35px;
    line-height: 35px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
  }

  .red {
    color: #d9534f;
  }
  .yellow {
    color: gold;
  }

  .green {
    color: #5cb85c;
  }
}

footer {
  display: flex;
  justify-content: space-between;
}
</style>
