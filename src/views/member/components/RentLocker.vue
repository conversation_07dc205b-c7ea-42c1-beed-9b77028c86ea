<template>
        <div class="table-wrap">
          <header>
                    <Input style="width:140px;" placeholder="姓名/电话/卡号" v-model="postData.search" @on-enter="getAll" clearable />
                    <Input style="width:140px;" placeholder="柜号" v-model="postData.lockerId" @on-enter="getAll"/>
                    <DatePicker placeholder="到期时间段" type="daterange" v-model="postData.dateRange" :clearable="true"></DatePicker>
                    <Select style="width:140px;" v-model="postData.status" placeholder="状态">
                        <Option value="0">全部状态</Option>
                        <Option value="1">空闲</Option>
                        <Option value="2">损坏</Option>
                        <Option value="3">已租</Option>
                        <Option value="4">过期</Option>
                        <Option value="5">待清洁</Option>
                    </Select>
                    <Select clearable style="width:140px;" v-model="postData.areaId" placeholder="区域">
                        <Option v-for="item in areaList" :value="item.id" :key="item.id">{{item.name}}</Option>
                    </Select>
                    <Button type="success" @click="getAll">搜索</Button>
          </header>
          <div class="locker-info">
                <p>{{areaName}}</p>
                <p>柜子数：<b>{{lockerTotal}}</b></p>
                <p><span class="block"></span>空闲&nbsp;{{libre}}</p>
                <p><span class="block" style="border: 1px solid rgba(0, 204, 0, 1); background: rgba(0, 204, 0, 1);"></span>出租中&nbsp;{{rented}}</p>
                <p><span class="block" style="border: 1px solid red; background: #FE4242;"></span>过期&nbsp;{{expired}}</p>
                <p><span class="block" style="border: 1px solid rgba(255, 153, 51, 1); background: rgba(255, 153, 51, 1);"></span>损坏&nbsp;{{damaged}}</p>
                <p><span class="block" style="border: #03B6FF; background: #03B6FF;"></span>待清洁&nbsp;{{clean}}</p>
                <Button style="float:right;margin-top:4px;" type="primary" @click="showAddAreaModal">新增区域</Button>
            </div>
            <div>
                    <zone
                        v-for="(item, index) in areaListAndLockers"
                        :title="item.name"
                        :areaId="item.areaId"
                        :key="index"
                        :lockerList="item.items"
                        :areaList="areaList"
                        :smart="item.type == 1"
                    />
            </div>
          <Modal v-model="showAddArea" title="添加区域" @on-visible-change="getFocus" :mask-closable="false">
              <Form ref="formAddArea" :model="formAddArea" :rules="ruleAddArea">
                <FormItem prop="name" label="区域名称" >
                    <Input :maxlength="20" ref="setAreaName" type="text" placeholder="请输入区域名称" v-model="formAddArea.name" style="width:80%;" />
                </FormItem>
              </Form>
              <div slot="footer"
                        class="modal-buttons">
                    <Button type="success"
                        @click="addArea">确定</Button>
                    <Button
                        @click="showAddArea = false">取消</Button>
                </div>
          </Modal>
        </div>
</template>

      <script>
        import { isChinese, SEARCH_HINT, checkRangeLessThan30, SEARCH_DATE_HINT, formatDate } from '@/utils';
        import zone from "./LockerArea";
        import EventBus from "utils/eventBus.js"; //有很多操作用了这个Bus

        export default {
          name: 'storageList',
          components: { zone },
          data() {
            return {
                lockerTotal: 0,
                libre: 0,
                expired: 0,
                rented: 0,
                damaged: 0,
                clean: 0,
                postData: {
                    areaId: '',
                    search: '',
                    lockerId: '',
                    dateRange: [],
                    status: '0'
                },
                ruleAddArea: {
                    name: [
                        { required: true, message: "请输入正确的区域名称！", trigger: "blur" }
                    ]
                },
                formAddArea: {
                    name: ''
                },
                lockerAreaList: [],
                areaListAndLockers: [],
                showAddArea: false,
                areaList: [
                    {
                        id: '0',
                        name: "全部区域"
                    }
                ]
            };
          },
          computed: {
            areaName() {
                if(!this.postData.areaId) {
                    return this.areaList[0].name
                } else {
                    let target = this.areaList.filter((item) => {
                        return item.id == this.postData.areaId
                    })
                    return target[0].name
                }
            },
          },
          created() {
            // default dateRange equal to today
            // const today = new Date();
            // this.postData.dateRange = [formatDate(today, 'yyyy-MM-dd'), formatDate(today, 'yyyy-MM-dd')];

            this.updateAll();
            EventBus.$on("success", () => {
                this.updateAll();
            })
          },
          methods: {
            getFocus(val) {
                let _this = this;
                setTimeout(() => {
                    if(val) {
                        this.$refs.setAreaName.focus()
                    }
                })
            },
            showAddAreaModal() {
                this.showAddArea = true
            },
            addArea() {
                let  postData = { ...this.formAddArea };
                this.formAddArea.name = '';
                this.$service.post('/Web/LockerRent/postCreateArea', postData).then(res => {
                    if(res.status === 200) {
                        if(res.data.errorcode === 0) {
                            this.updateAll();
                            this.showAddArea = false;
                            this.$Message.success("新增区域成功！")
                        } else {
                            this.$Message.error(res.data.errormsg)
                        }
                    } else {
                        this.$Message.error("服务器扑街！")
                    }
                }).catch(err => {
                    console.error(err);
                })
            },
            updateAll() {
                this.getAll();
                this.getAreaList();
            },
            getAreaList() {
                this.areaList = [{
                        id: '0',
                        name: "全部区域"
                    }];
                let postData = this.postData
                this.$service.get('/Web/LockerRent/getAreaList', postData).then(res => {
                    if(res.status === 200) {
                        if(res.data.errorcode == 0) {
                            this.areaList = this.areaList.concat(res.data.data.list);
                        } else {
                            this.$Message.error(res.data.errormsg)
                        }
                    } else {
                        this.$Message.error("服务器扑街！");
                    }
                }).catch(err => {
                    console.error(err);
                })
            },
            getAll() {
                // if numbers or letters must be more than 3 in length you can request
                if (this.postData.search && !isChinese(this.postData.search)) {
                    this.$Message.warning(SEARCH_HINT);
                    return;
                }

                // date range must be less than 30 days
                // if (this.postData.dateRange && !checkRangeLessThan30(this.postData.dateRange)) {
                //     this.$Message.warning(SEARCH_DATE_HINT);
                //     return;
                // }

                let postData = this.postData;
                postData.startTime = postData.dateRange[0] ? formatDate(this.postData.dateRange[0], 'yyyy-MM-dd') : '';
                postData.endTime = postData.dateRange[1] ? formatDate(this.postData.dateRange[1], 'yyyy-MM-dd') : '';
                this.$service.get('/Web/LockerRent/getNewLockerRentList', {
                    params: { ...postData }
                }).then(res => {
                    if(res.status === 200) {
                        if(res.data.errorcode == 0) {
                            this.areaListAndLockers = res.data.data.area;
                            this.libre = res.data.data.totalFreeNum;
                            this.expired = res.data.data.totalOutTimeNum;
                            this.rented = res.data.data.totalUsingNum;
                            this.damaged = res.data.data.totalFreezeNum;
                            this.lockerTotal = res.data.data.totalNum;
                            this.clean = res.data.data.totalCleanNum;
                        } else {
                            this.$Message.error(res.data.errormsg)
                        }
                    } else {
                        this.$Message.error("服务器扑街！")
                    }
                }).catch(err => {
                    console.log(err);
                })
            },
          }
        };
      </script>

      <style scoped>
          .clear-float {
              width: 100%;
          }
          .locker-info {
            padding: 0 40px;
            border-bottom: 1px solid #E0E3E8;
          }
          .locker-info > p{
                display: inline-block;
                width: 120px;
                height: 40px;
                line-height: 40px;
          }
          .block {
              display: inline-block;
              width: 14px;
              height: 14px;
              border: 1px solid grey;
              vertical-align: middle;
              margin-right: 6px;
              margin-bottom: 2px;
          }
      </style>
