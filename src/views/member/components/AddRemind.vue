<template>
<Modal v-model="showAdd" :mask-closable="false" title="添加提醒">
  <Form ref="form" :model="postData" class="modal-form" :label-width="80">
    <Form-item label="内容" prop="content" :rules="{required: true, message: '请填写提醒内容'}">
      <Input type="textarea" placeholder="提醒内容会在签到等多个关键页面展示" v-model="postData.content" :rows="5" :autosize="{minRows: 4, maxRows: 6}" />
    </Form-item>
  </Form>
  <div slot="footer" class="modal-buttons">
    <Button type="success" @click="doAddRemind">保存</Button>
    <Button @click="showAdd = false">取消</Button>
  </div>
</Modal>
</template>
<script>
export default {
  name: 'AddRemind',
  data() {
    return {
      postData: {
        content: '',
        user_id: this.userId,
        remind_category: 1, //提醒类型，1提醒、2挂账
      }
    }
  },
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: Boolean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.$refs.form.resetFields()
      }
    }
  },
  created() {},
  methods: {
    doAddRemind() {
      this.$refs.form.validate(val => {
        if (!val) return false
        this.$service
          .post('/Web/Remind/add_remind', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showAdd = false
              this.$emit('on-success')
              this.$Message.success(res.data.errormsg)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      })
    }
  }
}
</script>

<style scoped>

</style>
