<template>
  <Modal :mask-closable="false"
         v-model="showDeposit"
         title="收押金"
         :width="750">
    <Form ref="depositForm"
          :model="depositForm"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item label="会员" prop="user_id" v-if="!userId && from!='card'" :rules="{required: true, message: '请选择会员'}">
        <userSearch url="/Web/FrontMoney/search_all_user" v-model="depositForm.user_id"></userSearch>
      </Form-item>
      <Form-item label="押金用途" prop="use_type">
        <Radio-group v-model="depositForm.use_type">
          <Radio label="1">会员卡押金</Radio>
          <Radio label="2">储物柜押金</Radio>
          <Radio label="3">其他押金</Radio>
        </Radio-group>
      </Form-item>
      <Form-item label="储物柜" prop="locker_rent_id" v-if="depositForm.use_type == 2" :rules="{required: true,message: '请选择柜号',trigger: 'change'}">
        <Select v-model="depositForm.locker_rent_id"  placeholder="请选择">
          <Option v-for="item in lockerList" :value="item.locker_rent_id" :label="item.locker_rent_name" :key="item.locker_rent_id">{{item.locker_rent_name}}</Option>
        </Select>
      </Form-item>
      <Form-item label="收取时间" prop="collect_date" :rules="{ required: true, message: '请选择日期'}">
        <Date-picker
          :value="depositForm.collect_date"
          :options="disableDayAfter"
          format="yyyy-MM-dd"
          :editable="false"
          type="date"
          @on-change="handleClctDateChange"
          placeholder="定金收取时间"></Date-picker>
      </Form-item>
      <Form-item label="押金金额"
                 prop="amount"
                 :rules="{required: true, type: 'string', pattern: /^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d*(\.\d{0,2})?)$/, message: '金额必须大于0且只能保留两位小数'}">
        <Input v-model="depositForm.amount" />
      </Form-item>
      <Form-item
        v-if="depositForm.amount>0 && (userId || depositForm.user_id)"
        label="支付方式"
        prop="new_pay_type"
        :required="depositForm.amount != 0 && depositForm.amount != ''"
        :rules="{required: depositForm.amount != 0  && depositForm.amount != '' ? true : false, type: 'array', min: 1, message: '金额不为0时,支付方式为必选项'}">
        <pay-type-list
          v-model="depositForm.new_pay_type"
          :amount="parseFloat(depositForm.amount)"
          :showCardPay="!!(userId || depositForm.user_id)"
          :userId="userId || depositForm.user_id"
          :sqbOption="{ describe: '收押金', serviceType: '3', isEqual: false }" />
        </Form-item>
      <Form-item label="备注"
                 prop="remark">
        <textarea rows="3"
                  maxlength="90"
                  v-model="depositForm.remark"></textarea>
      </Form-item>
    </Form>
    <div slot="footer"
         class="modal-buttons">
      <Button type="success"
              @click="newDeposit">确定</Button>
      <Button
              @click="showDeposit = false">取消</Button>
    </div>
  </Modal>
</template>

<script>
  import PayTypeList from 'components/form/PayTypeList'
  import userSearch from 'src/components/user/userSearch'
  import {
    formatDate
  } from "utils"
  export default {
    name: 'AddCash',
    props: {
      userId: {
        type: [String, Number]
      },
      from: {
        type: String
      },
      value: {
        type: Boolean
      },
      data: ''
    },
    components: {
      userSearch,
      PayTypeList
    },
    data() {
      return {
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        depositForm: {
          collect_date: formatDate(new Date(), "yyyy-MM-dd"),
          use_type: '1',
          locker_rent_id: '',
          pay_order_ids: [],
          amount: '',
          user_id: '',
          new_pay_type: [],
          remark: ''
        },
        lockerList: []
      }
    },
    computed: {
      showDeposit: {
        get() {
          if(this.value && this.userId) {
            this.getLocker();
          }
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showDeposit(val) {
        if (!val && this.from!='card') {
          this.$refs.depositForm.resetFields()
          this.depositForm.new_pay_type = []
        } else {
          if (this.data && typeof this.data === 'object'){
            Object.assign(this.depositForm, this.data)
          }
        }
      },
      // 'depositForm.collect_date'(val) {
      //   if(typeof val === 'object') {
      //     this.depositForm.collect_date = formatDate(val, "yyyy-MM-dd")
      //   }
      // },
      'depositForm.user_id'(val) {
        if(val) {
          this.getLocker();
        }
      }
    },
    created() {
    },
    methods: {
      onDragonflyConfirm(info) {
        this.depositForm.pay_order_ids = info.pay_order_ids
      },
      handleClctDateChange(val) {
        this.depositForm.collect_date = val
      },
      getLocker() {
        this.$service
          .post('/Web/CashPledge/getNoCplockerRents', { user_id: this.userId || this.depositForm.user_id})
          .then(res => {
            if (res.data.errorcode === 0) {
              this.lockerList = res.data.data;
            }else{
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            this.$Message.error(err)
          })
      },
      newDeposit() {
        if (this.userId && this.from!='card') {
          this.depositForm.user_id = this.userId
        }
        this.$refs.depositForm.validate(val => {
          if (!val) {
            return false;
          }
          const params = {
            ...this.depositForm
          }

          if (+params.amount === 0) {
            params.new_pay_type = []
          } else if (params.new_pay_type.reduce((sum, v) => sum + Number(v.amount), 0) !== +params.amount) {
            this.$Message.error('支付金额需等于押金金额')
            return;
          }
          if (this.from === 'card') {
            this.$emit('on-success', params);
            this.showDeposit = false;
            return;
          }

          if (params.use_type !== '2') {
            delete params.locker_rent_id
          }

          this.$service
            .post('/Web/CashPledge/collectCashPledge', params)
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$emit('on-success')
                this.$emit('on-printinfo',2,this.depositForm.user_id,res.data.data.cash_pledge_id,'cash')
                this.showDeposit = false
                // this.$Message.success(res.data.errormsg)
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
        })
      }
    }
  }
</script>

<style scoped>

</style>
