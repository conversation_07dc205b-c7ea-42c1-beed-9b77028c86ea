<template>
  <div class="table-wrap">
    <header>
      <Input class="user-search"
             placeholder="储物柜编号"
             v-model="postdata.locker_id"
             @on-enter="handleSearch"
             clearable />
      <Date-picker placeholder="选择查询时间段"
                   @on-change="dateChanged"
                   :value="dateRange"
                   type="daterange"
                   :editable="false"
                   clearable
                   @on-clear="clearDate"
                   format="yyyy年MM月dd日"></Date-picker>
      <Select v-model="postdata.status"
              clearable
              style="width:100px"
              placeholder="状态">
        <Option value="0">使用中</Option>
        <Option value="1">已过期</Option>
        <Option value="2">已退柜</Option>
      </Select>
      <Select v-model="postdata.order"
              style="width:100px">
        <Option value="0">创建时间倒序</Option>
        <Option value="1">创建时间正序</Option>
        <Option value="2">到期时间倒序</Option>
        <Option value="3">到期时间正序</Option>
      </Select>
      <Button type="success"
              @click="handleSearch">搜索</Button>
    </header>

    <main>
      <Table ref="table"
             :columns="columns"
             :data="tableData"
             stripe
             disabled-hover></Table>
    </main>

    <footer>
      <Button type="success"
              style="margin-right: 30px"
              @click="detention = true">租柜</Button>
        <Dropdown @on-click="otherCase"
                  placement="top" v-if="notExport">
          <Button>
            其他操作
            <Icon type="md-arrow-dropdown"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem name="0">导出excel</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      <Page :total="totalCount"
            :current.sync="currentPage"
            show-total
            show-sizer
            placement="top"
            class="page"
            @on-change="handlePageChange"
            @on-page-size-change="pageSizeChanged"></Page>
    </footer>
    <Modal v-model="showUsingMember" width="400" title="成员" :mask-closable="false">
      <Card dis-hover>
        <p slot="title">可使用成员</p>
        <div v-for="item in memberList" :key="item.user_id" class="member-card member-list">
          <Avatar icon="md-person" size="large" :src="item.avatar" />
          <label class="member-label" :title="item.username">{{item.user_name}}</label>
          <label class="member-label">({{item.phone}})</label>
          <Icon type="md-trash" size="18" title="删除" v-if="!item.is_own" class="member-delete" @click.native="handleDeleteMember(item.user_id)"></Icon>
        </div>
      </Card>
      <div class="member-old-form" v-if="isAddOldMember">
        <Select v-model="searchUserId" @on-change="handleAddMemberSubmit()" filterable remote :remote-method="handleAddOldMemberRemote" :loading="isRemoteLoading" placeholder="姓名/电话/卡号" style="width:300px">
          <Option v-for="item in searchUserList" :value="item.user_id" :key="item.user_id">{{ item.username }}</Option>
        </Select>
        <Button type="text" @click="isAddOldMember=false">取消添加</Button>
      </div>
      <div class="member-controller" v-if="!isAddOldMember">
        <Button @click="isAddOldMember=true">添加成员</Button>
      </div>
      <div slot="footer">
      </div>
    </Modal>
    <NewLocker v-model="detention" @on-success="getLockerList" @on-printinfo="contractComplete" />
    <receipt-modal v-model="showPrint" :to-path='toPath' />
  </div>
</template>

<script>
  import NewLocker from './AddLocker.vue'
  import { getNewHost } from 'utils/config'
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js'
  import { isChinese } from '@/utils';

  export default {
    mixins: [receipt],
    data() {
      return {
        detention: false,
        postdata: {
          locker_id: '',
          s_date: '',
          e_date: '',
          status: '',
          page_no: 1,
          page_size: 10,
          order: '0'
        },
        searchUserList: [],
        memberList: [],
        isRemoteLoading: false,
        showUsingMember: false,
        isAddOldMember: false,
        searchUserId: "",
        curUsingLockerrenId: '',
        dateRange: [],
        tableData: [],
        exportData: null,
        sizer: 10,
        currentPage: 1,
        totalCount: 0,
        // userUrl: `${getNewHost()}/#/member/detail/`,
        userUrl: '/member/detail/',
        columns: [
          {
            title: '时间',
            key: 'create_date',
            width: 200
          },
          {
            title: '会员',
            key: 'username',
            render: (h, param) => {
              let url = this.userUrl + param.row.user_id
              return (
                // <a target="_blank" href={url}>
                //   {param.row.username}
                // </a>
                <router-link target="_blank" to={ url }>{ param.row.username }</router-link>
              )
            }
          },
          {
            title: '金额',
            key: 'amount'
          },
          {
            title: '有效期',
            key: 'end_date',
            width: 100
          },
          {
            title: '储物柜编号',
            key: 'locker_id'
          },
          {
            title: '状态',
            key: 'status',
            render: (h, param) => {
              const status = param.row.status
              const color = status === '1' ? '#d9544f' : ''
              const label = status == '1' ? '已过期' : status == '2' ? '已退柜' : '使用中'
              return h('span', {
                style: {
                  color
                },
              }, label)
            }
          },
          {
            title: '成员',
            key: 'using_member',
            render: (h, param) => {
              let numDom = (<span style='color: #bbbec4'> {param.row.using_member}人 </span>)
              if (param.row.status==0) {
                numDom= (<a href="javascript:void(0)" on-click={() => {
                      this.showUsingMember = true;
                      this.curUsingLockerrenId = param.row.lockerrent_id;
                      this.getMemberList()
                    }}>
                    {param.row.using_member}人
                  </a>)
              }
              return numDom
            }
          },
          {
            title: '退柜时间',
            key: 'return_date',
            width: 180,
            render: (h, param) => {
              if (param.row.return_date) {
                return <span>{param.row.return_date}</span>
              } else {
                return <span>-</span>
              }
            }
          },
          {
            title: '操作',
            key: 'operate',
            width: '150',
            align: 'center',
            render: (h, param) => {
              let row = param.row
              return (
                <i-button
                  type="text"
                  disabled={row.status == '2'}
                  onClick={() => this.clickBackLocker(row)}>
                  退柜
                </i-button>
              )
            }
          }
        ],
        backlockerId: '',
        backuserId: '',
        notExport: true,
      }
    },
    components: {
      NewLocker,
      receiptModal
    },
    created() {
      this.getLockerList()
    },
    methods: {
      handleAddOldMemberRemote(query) {
        if (query.length === 0) {
          this.searchUserList = [];
          return false;
        }

        // if numbers or letters must be more than 3 in length you can request
        if (!isChinese(query)) {
          return;
        }

        this.isRemoteLoading = true;
        this.$service
          .post("/Web/FrontMoney/search_all_user", {
            search: query.trim()
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              if (Array.isArray(res.data.data.list)) {
                this.searchUserList = res.data.data.list;
                this.isRemoteLoading = false;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      handleDeleteMember(id) {
        return this.$service
          .post("/Web/LockerRent/multi_delete_member", {
            user_id: id,
            lockerrent_id: this.curUsingLockerrenId
          })
          .then(res => {
            if(res.data.errorcode == 0){
              this.getMemberList();
              this.getLockerList();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      handleAddMemberSubmit() {
        return this.$service
          .post("/Web/LockerRent/multi_add_member", {
            user_id: this.searchUserId,
            lockerrent_id : this.curUsingLockerrenId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.getMemberList();
              this.getLockerList();
              this.searchUserId = "";
              this.isAddOldMember = false;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      getMemberList() {
        return this.$service
          .post("/Web/LockerRent/multimember_rent_list", {
            lockerrent_id : this.curUsingLockerrenId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              let users = res.data.data
              if (Array.isArray(users)) {
                this.memberList = users;
              }
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      dateChanged(val) {
        if (!val[0]) {
          return false
        }
        let beginDate = `${val[0].slice(0, 4)}-${val[0].slice(5, 7)}-${val[0].slice(8, 10)}`
        let endDate = `${val[1].slice(0, 4)}-${val[1].slice(5, 7)}-${val[1].slice(8, 10)}`
        this.dateRange = [beginDate, endDate]
      },
      clearDate() {
        this.dateRange = []
      },
      handleSearch() {
        this.currentPage = 1
        this.getLockerList()
      },
      getLockerList() {
        this.postdata.s_date = this.dateRange[0]
        this.postdata.e_date = this.dateRange[1]
        this.postdata.page_no = this.currentPage
        this.postdata.page_size = this.sizer
        this.$service.post('/Web/LockerRent/get_lockerrent_list', this.postdata).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data
              this.totalCount = parseInt(data.count)
              this.tableData = data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          } else {
            console.log('服务器扑街！')
          }
        })
      },
      getExportList(isExport) {
        this.postdata.s_date = this.dateRange[0]
        this.postdata.e_date = this.dateRange[1]
        this.postdata.page_no = this.currentPage
        this.postdata.page_size = this.totalCount
        return this.$service.post('/Web/LockerRent/get_lockerrent_list', { ...this.postdata }, {isExport}).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              let data = res.data.data
              this.exportData = data.list.map(item => {
                return Object.assign({}, item, {
                  status: item.status === '0' ? '使用中' : item.status == '1' ? '已过期' : '已退柜'
                })
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
            this.postdata.page_size = this.sizer
          } else {
            console.log('服务器扑街！')
            this.postdata.page_size = this.sizer
          }
        })
      },
      handlePageChange(pageno) {
        this.currentPage = pageno
        this.getLockerList()
      },
      pageSizeChanged(size) {
        this.currentPage = 1
        this.sizer = size
        this.getLockerList()
      },
      async otherCase(val) {
        if (val === '0') {
          await this.getExportList(true)
          if (!this.exportData) return false

          this.notExport = false
          this.$refs.table.exportCsv({
            filename: '租柜',
            columns: this.columns.filter((col, index) => index < 8),
            data: this.exportData
          })
          setTimeout(() => {
            this.notExport = true
          }, 100)
        }
      },
      clickBackLocker(row) {
        this.backlockerId = row.lockerrent_id
        this.backuserId = row.user_id
        if(Number(row.cash_pledge_id)){
          localStorage.setItem('showMemberLockerent',row.lockerrent_id);
          this.$router.push(`/member/detail/${row.user_id}`)
        } else {
          this.$Modal.confirm({
            title: '',
            content: '确定要退柜吗？',
            onOk: this.doBackLocker
          })
        }
      },
      doBackLocker() {
        let url = '/Web/LockerRent/deleted_lockerrent'
        this.$service
          .post(url, { lockerrent_id: this.backlockerId, user_id: this.backuserId })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.getLockerList()
            }
            this.$Message.success(res.data.errormsg)
          })
          .catch(err => {
            this.$Message.error(err)
          })
      }
    }
  }
</script>
<style lang="less" scoped>
  .ivu-date-picker {
    width: 240px;
  }

  .member-controller {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: 22px;
  }

  .member-old-form {
    display: flex;
    flex-direction: row;
    margin-top: 22px;
  }

  .member-card {
    display: flex;
    align-items: center;
    width: 358px;
    margin-bottom: 15px;
  }

  .member-list{
    margin-bottom: 15px;
    &:last-child{
      margin-bottom: 0;
    }
    .member-delete{
      margin-left: 15px;
      color: #666;
      cursor: pointer;
    }
  }
  header {
    .user-search {
      width: 160px;
    }
  }

  footer {
    display: flex;
    justify-content: space-between;
  }

  .ivu-date-picker {
    width: 100%;
  }

  /*  .buttonFontSize {
        font-size: 14px;
        padding: 3px 14px;
        border-radius: 3px;
      }
      .lineHeight-32 {
        line-height: 32px;
      }
      .buttonFooter {
        padding: 20px 13px;
        border: 1px solid #ccc;

      }*/
</style>
