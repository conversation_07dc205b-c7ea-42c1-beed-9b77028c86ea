<template>
    <div class="form-box">
        <div class="form-box-title">
            <h2>拆分</h2>
        </div>
        <div class="form-box-con">
            <Form ref="cardForm" :label-width="140">
                <Form-item label="当前会员卡">
                    {{cardData.card_name}}
                    <template v-if="cardData.card_sn">（卡号：{{cardData.card_sn}}）</template>
                </Form-item>
                <Form-item
                    label="当前教练"
                    v-if="cardData && cardData.card_type_id == 4"
                >{{Array.isArray(cardData.coach_name)?cardData.coach_name.join(',') : cardData.coach_name}}</Form-item>
                <Form-item
                    :label="'当前' + typeText"
                    v-if="cardData && cardData.card_type_id != 1"
                >总{{typeText}} {{cardData.card_type_id == 3 ? cardData.total : cardData.all_num}}，剩余{{typeText}} {{cardData.last_num}}</Form-item>
                <Form-item
                    label="剩余天数"
                    v-if="cardData.last_day"
                >{{cardData.last_day === '永久' ? cardData.last_day : cardData.last_day +'天'}}</Form-item>
                <LeftValue
                    v-if="cardData"
                    :value="cardData.card_last_value"
                    :cardUserId="$route.params.cardUserId != 0?$route.params.cardUserId:otherBusInfo.card_user_id"
                />
                <div v-if="cardData" class="line-cross">
                    <span></span>
                </div>
                <card-Info
                    ref="cardComponent"
                    :cardData="cardData"
                    :actionType="actionType"
                    :selectedCard.sync="selectedCard"
                    :coachList.sync="coachList"
                    :coachCur.sync="coachCur"
                    @onPrivateNumChange="amount => privateTotalAmount = amount"
                    :isNormal="false"
                    @forSeperateCard="obj => seperateObj = obj"
                />
                <contract-Info
                    ref="contractComponent"
                    :contractData="cardData"
                    :privateTotalAmount="privateTotalAmount"
                    :selectedCard="selectedCard"
                    :coachList.sync="coachList"
                    :seperateObj="seperateObj"
                    :coachCur.sync="coachCur"
                    :actionType="actionType"
                    v-on:changePayType="payTypeChanged"
                />
                <Form-item>
                    <div class="buttons">
                        <Button type="primary" @click="handleOnlinePay" v-if="onlinePay">在线支付并提交</Button>
                        <Button
                            type="error"
                            style="border:none;"
                            v-if="checkNow"
                            @click="withdrawModal=true"
                        >退单</Button>
                        <Button type="primary" :loading="loadingFlag" @click="handleSubmit">提交</Button>
                        <Button @click="$router.back()">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>
        <Modal v-model="showModal" title="收款" :closable="closable" :mask-closable="false">
            <OnlinePay
                v-if="showModal"
                :data="formData"
                v-on:closeModal="showModal = false"
                v-on:enterPaying="closable = false"
                v-on:leavePaying="closable = true"
            ></OnlinePay>
            <span slot="footer"></span>
        </Modal>
        <Modal v-model="withdrawModal" title="退单原因">
            <Form
                ref="formValidate"
                :model="formValidate"
                :rules="withdrawValidate"
                :label-width="80"
            >
                <FormItem label="备注" prop="withdrawRemark">
                    <Input
                        v-model="formValidate.withdrawRemark"
                        type="textarea"
                        :autosize="{minRows: 2,maxRows: 5}"
                        placeholder="请输入..."
                    />
                </FormItem>
            </Form>
            <div slot="footer" class="modal-buttons">
                <Button type="success" @click="withdrawOk">确定</Button>
                <Button @click="withdrawCancel">取消</Button>
            </div>
        </Modal>
        <receipt-modal v-model="showPrint" :to-path="toPath" :seperate="seperate" @on-receiptupdate="receiptUpdate"/>
    </div>
</template>
<script>
  import cardInfo from 'components/member/cardInfo'
  import contractInfo from 'components/member/contractInfo'
  import OnlinePay from 'components/onlinePay/onlinePayAlert'
  import receiptModal from 'components/receipt/receipt.vue';
  import LeftValue from './components/leftValue'
  import receipt from 'mixins/receipt.js'
  import { mapState } from 'vuex'
  import {
    objectMerge
  } from "utils";

  export default {
    name: 'seperateCard',
    mixins: [receipt],
    components: {
      cardInfo,
      contractInfo,
      OnlinePay,
      receiptModal,
      LeftValue
    },
    created() {
      this.userId = this.$route.params.userId;

      if (this.$route.query.checkOrder) {
        this.checkNow = true;
        this.actionType = 'pre-order';
        this.formValidate.withdrawId = this.$route.query.preOrdId;
        this.getPreOrder();
      }

      if (this.$route.params.userId === this.$route.params.cardUserId &&  this.$route.params.userId == 0) {
        this.isOtherBus = true;
        this.getMerchantsBusList();
        } else {
        this.userId = this.$route.params.userId;
        this.cardUserId = this.$route.params.cardUserId;
        this.getCardEditInfo();
        }
    },
    computed: {
    ...mapState(['busId']),
    typeText() {
      let text = '次数';
      if (this.cardData.card_type_id == 3) {
        text = '金额';
      } else if (this.cardData.card_type_id == 4) {
        text = '节数';
      }
      return text;
    },
    cardTable() {
        let type = '-', leftBefore = '-', leftAfter = '-', expireTimeBefore = '-', expireTimeAfter = '-';

    }
  },
    data() {
      return {
        seperate: true,
        seperateObj: {}, //给拆卡使用
        dataForSeperate: {},
        loadingFlag: false,
        userId: '',
        cardUserId: '',
        formData: {},
        onlinePay: false,
        showModal: false,
        closable: true,
        selectedCard: {
          card_type_id: 1,
        },
        privateTotalAmount: 0, // 私教课通过单价和节数计算的合同金额
        coachList: '',
        coachCur: '',
        checkNow: false,
        cardData: {},
        actionType: 'seperate',
        withdrawModal: false,
        formValidate: {
          withdrawId: '',
          withdrawRemark: ''
        },
        withdrawValidate: {
          withdrawRemark: [{ required: true, message: '请添加退单理由！', trigger: 'blur' }]
        }
      }
    },
    watch: {
        seperateObj(val) {
            // this.card[1].type = val.name != undefined? val.name : '';
        }
    },
    methods: {
        getCardEditInfo() {
      let postData = {
        card_user_id: this.cardUserId,
        user_id: this.userId
      };
      this.$service.post('/Web/Member/get_card_user_edit_info', postData).then(res => {
        if (res.data.errorcode === 0) {
          this.cardData = res.data.data.info;
          console.log(this.cardData)
        //   this.card[0].type = this.cardData.card_name + '(原卡)';
        //   this.card[0].leftBefore = this.cardData.last_num;
        //   this.card[0].expireTimeBefore = this.cardData.end_time_date;
        //   this.card[0].expireTimeAfter = this.card[1].expireTimeAfter = this.cardData.end_time_date;    
        }
      });  
    },
      receiptUpdate() {
        this.$router.back();
      },
      handleOnlinePay() {
        let formReturn = this.checkForm();
        if (formReturn.valid) {
          this.verifyCardSn(formReturn.postData).then(() => {
            // 显示在线支付弹窗
            this.showModal = true
          })
        }
      },
      handleSubmit() {
        if (this.loadingFlag) {
          return false;
        }
        this.loadingFlag = true;
        let formReturn = this.checkForm();

        // formReturn.postData.card_user_id = this.cardData.carduser_id;
        formReturn.postData.purchase_volume = formReturn.postData.order_all_num;
        formReturn.postData.card_user_id = this.cardData.carduser_id;
        console.log(formReturn.postData);

        if(!formReturn.valid) {
            this.$Message.error("请先正确输入！");
            this.loadingFlag = false;
            return false;
        }
        if(this.actionType == 'seperate' && this.cardData.card_type_id == 2) {
            delete formReturn.postData.support_bus;

            this.$service.post("/Web/MemberCard/splitcard", formReturn.postData).then((res) => {
                if(res.data.errorcode === 0) {
                    this.$Message.success(res.data.errormsg)
                    this.contractComplete(formReturn.postData.user_id, res.data.card_order_info_id, 'buycard');
                    //此处应需要跳转路由
                } else {
                    this.$Message.error(res.data.errormsg)
                }
            })
            this.loadingFlag = false;
            return;
        }
        this.$service.post("/Web/MemberCard/splitcard", formReturn.postData).then((res) => {
           if(res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg)
                this.showPrint = true;
                console.log("私教卡拆分好了");
                this.contractComplete(formReturn.postData.user_id, res.data.card_order_info_id, 'buycard');
               //此处应需要跳转路由
           } else {
               this.$Message.error(res.data.errormsg)
           }
        })
        this.loadingFlag = false;
      },
      verifyCardSn(data) {
        let that = this;
        return new Promise((resolve, reject) => {
          let postData = {
            card_sn: data.card_sn
          };
          this.$service.post('/Web/Onlinepay/Verification_card_sn', postData)
            .then(function (response) {
              if (response.data.errorcode !== 0) {
                that.$Message.error('实体卡号已存在');
                reject();
                return false
              }
              resolve()
            })
            .catch(function (error) {
              console.log(error);
            });
        })
      },
      checkForm() {
        let cardInfoData = this.$refs.cardComponent.handleSubmitClick();
        let contractInfoData = this.$refs.contractComponent.handleSubmitClick();
        let valid = cardInfoData.valid && contractInfoData.valid;
        let postData = objectMerge(cardInfoData.postData, contractInfoData.postData);
        postData.user_id = this.userId;
        postData.pre_id = this.formValidate.withdrawId;
        valid && (this.formData = postData);
        return {valid, postData}
      },
      payTypeChanged(val) {
        val = parseInt(val);
        // this.onlinePay = (val === 1 || val === 2)
      },
      handleReset() {
        this.$refs.cardComponent.handleReset();
        this.$refs.contractComponent.handleReset();
      },
      getPreOrder() {
        return this.$service.post('/Web/preBilling/orderInfo', {
          id: this.$route.query.preOrdId,
          user_id: this.$route.query.userId
        }).then(res => {
          if (res.data.errorcode == 0) {
            this.cardData = res.data.data.info;
          } else {this.$Message.error(res.data.errormsg);}
        });
      },
      withdrawOk() {
        this.$refs['formValidate'].validate((valid) => {
          if (valid) {
            return this.$service.post('/Web/preBilling/reject', {
              id: this.formValidate.withdrawId,
              remark: this.formValidate.withdrawRemark.trim()
            }).then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success(res.data.errormsg);
                setTimeout(() => {
                  this.$router.back();
                }, 1000);
              } else {this.$Message.error(res.data.errormsg);}
            });
          } else {
              this.$Message.error('请添加退单理由！');
          }
        })
      },
      withdrawCancel() {
        this.withdrawModal = false;
        // this.formValidate.withdrawId = "";
        this.formValidate.withdrawRemark = "";
      }
    },
  }
</script>

<style lang="less" scoped>
  .buttons {
    display: flex;
    justify-content: space-around;
    margin-top: 35px;
  }

  .ivu-btn {
    width: auto;
    // margin: 0;
  }
  .line-cross {
  height: 30px;
  margin: 15px 0;
  position: relative;
  width: 50%;

  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    border-top: 1px dashed #e5e5e5;
    position: absolute;
    top: 10px;
    left: 0;
  }

  span {
    display: inline-block;
    background: #fff;
    padding: 0 5px 0 18px;
    position: absolute;
    left: 50%;
    top: 0;
    margin-left: -90px;
    color: #999;
    font-size: 14px;
  }
}
</style>