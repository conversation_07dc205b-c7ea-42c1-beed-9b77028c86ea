<template>
  <div class="form-box">
    <div class="form-box-title">
      <h2>补卡</h2>
    </div>
    <div class="form-box-con">
      <Form ref="formCustom" :model="postData" :rules="ruleCustom" :label-width="130" @submit.native.prevent>
        <Form-item label="当前会员卡" prop="cardinfo">
          {{cardinfo.card_name}}
          <span v-if="cardinfo.card_sn">(卡号：{{cardinfo.card_sn}})</span>
        </Form-item>
        <Form-item label="当前有效期" prop="cardinfo">
          {{cardinfo.use_time}}
        </Form-item>
        <Form-item label="剩余天数" prop="cardinfo">
          {{cardinfo.use_days}}
        </Form-item>
        <Form-item label="剩余金额" prop="cardinfo" v-if="cardinfo.card_type_id==3">
          {{cardinfo.balance}}元
        </Form-item>
        <Form-item label="剩余次数" prop="last_num_after" v-if="cardinfo.is_pt_time_limit_card != 1 && cardinfo.card_type_id != 1 && cardinfo.card_type_id != 3">
          {{cardinfo.last_num}}
        </Form-item>
        <LeftValue :value="cardinfo.card_last_value" :cardUserId="$route.query.cardUserId" />
        <!-- <Form-item label="转出会员">
          {{cardinfo.username}}
        </Form-item>
        <div class="tips">
          会员卡一经转出,之前共享使用该卡的成员将不可再使用该会员卡
        </div> -->
        <Form-item label="实体卡号">
          <Input placeholder="请输入实体卡号" v-model="postData.card_sn"></Input>
        </Form-item>
        <contract-Info 
          ref="contractComponent" 
          actionType="switch" 
          :contractData='cardinfo' 
          :selectedCard="selectedCard" 
          :labelWidth="130"
          :hasStore="true"/>
        <Form-item>
          <div class="buttons">
            <Button type="primary" @click="handleSubmit('formCustom')">确定</Button>
            <Button @click="$router.back()">取消</Button>
          </div>
        </Form-item>
      </Form>
    </div>
    <receipt-modal v-model="showPrint" :to-path='toPath' @on-receiptupdate="receiptUpdate" />
  </div>
</template>
<script>
  import { getcardInfo } from 'src/service/getData.js';
  import contractInfo from 'components/member/contractInfo';
  import LeftValue from './components/leftValue.vue';
  import receipt from 'mixins/receipt.js';
  import receiptModal from 'components/receipt/receipt.vue';
  import EventBus from 'components/EventBus.js'
  import { isChinese } from '@/utils';

  export default {
    name: 'supplyCard',
    mixins: [receipt],
    created() {
      this.getCardInfo();
    },
    components: {
      contractInfo,
      LeftValue,
      receiptModal
    },
    data() {
      return {
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        selectedCard: {
          card_type_id: ''
        },
        postData: {
          user_id: this.$route.query.userId, //转出用户id
          card_user_id: this.$route.query.cardUserId, //转出会员卡id
          card_sn: '',
          amount: '', //合同金额
          pay_type: '', //支付方式
          remark: '', //订单备注
          deal_time: '', //合同成交时间
          to_user_id: '' //转入用户id
        },
        searching: false,
        userList: null,
        cardinfo: {},
        ruleCustom: {}
      };
    },
    methods: {
      receiptUpdate() {
        this.$router.back();
      },
      // 搜索会员
      getSearchUserList(search) {
        if (search !== '') {

          // if numbers or letters must be more than 3 in length you can request
          if (!isChinese(search)) {
            return;
          }

          this.searching = true;
          let postData = {
            search: search.trim()
          };
          this.$service
            .post('/Web/FrontMoney/search_all_user', postData, { loading: false })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.userList = res.data.data.list;
              }
              this.searching = false;
            })
            .catch(err => {
              this.searching = false;
              this.userList = [];
            });
        } else {
          this.userList = [];
        }
      },
      getCardInfo() {
        const postData = {
          card_user_id: this.postData.card_user_id,
          user_id: this.postData.user_id
        };
        this.$service.post('/Web/MemberCard/get_make_up_card_info', postData).then(res => {
          if (res.data.errorcode === 0) {
            this.cardinfo = res.data.data;
            EventBus.$emit('on-pay-des-change', `补卡[${this.cardinfo.card_name}]`)
            this.selectedCard.card_type_id = this.cardinfo.card_type_id;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleSubmit(name) {
        this.$refs[name].validate(async valid => {
          let contractInfoData = await this.$refs.contractComponent.handleSubmitClick();
          valid = contractInfoData.valid && valid;
          if (valid) {
            let postData = { ...this.postData, ...contractInfoData.postData };
            delete postData.help_deal; //转卡没有协助成单
            this.$service
              .post('/Web/MemberCard/makeUpCard', postData)
              .then(res => {
                if (res.data.errorcode == 0) {
                  this.contractComplete(this.postData.user_id, res.data.data.cardorder_id, 'contract');
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(function(err) {
                console.log(err);
              });
          }
        });
      }
    }
  };
</script>
<style lang="less" scoped>
  .tips {
    color: #ff0000;
    font-size: 14px;
    margin-bottom: 24px;
    margin-left: 45px;
  }
</style>
