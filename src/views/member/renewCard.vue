<template>
    <div class="form-box">
        <div class="form-box-title">
            <h2>{{title}}</h2>
        </div>
        <div class="form-box-con">
            <Form ref="formCustom"
                  :model="formCustom"
                  :rules="ruleCustom"
                  :label-width="140">
                <Form-item label="会员卡"
                           prop="cardinfo">
                    {{cardinfo.card_name}}
                    <span v-if="cardinfo.card_sn">(卡号：{{cardinfo.card_sn}})</span>
                </Form-item>
                <Form-item label="当前次数"
                           prop="cardinfo"
                           v-if="cardinfo.is_pt_time_limit_card!=1 && (cardinfo.card_type==2||cardinfo.card_type==4||cardinfo.card_type==5)">
                    总次数{{cardinfo.all_num}}次，剩余次数{{cardinfo.last_num}}次
                </Form-item>
                <Form-item label="当前金额"
                           prop="cardinfo"
                           v-if="cardinfo.card_type==3">
                    总金额{{cardinfo.all_num}}元，剩余金额{{cardinfo.last_num}}元
                </Form-item>
                <Form-item label="当前有效期"
                           prop="cardinfo">
                    {{cardinfo.active_time_date}}至{{cardinfo.end_time_date}}
                </Form-item>
                <Form-item label="剩余天数"
                           prop="cardinfo">
                    {{cardinfo.last_day}}{{cardinfo.last_day=='永久'?'':'天'}}
                </Form-item>
                <!-- <Form-item :label="cardinfo.card_type==2||cardinfo.card_type==4?'剩余次数':'剩余金额'" prop="last_num_after" v-if="cardinfo.card_type!=1">
                  <Input v-model="formCustom.last_num_after" type='text'></Input>
                </Form-item> -->
                <LeftValue :value="formCustom.card_last_value"
                           :cardUserId="$route.params.cardUserId"/>
                <Form-item label="续卡方式"
                           key="renew_card_type">
                    <Radio-group v-model="formCustom.renew_card_type"
                                 @on-change="renewTypeChange">
                        <Radio label="0"
                               v-show="canEditFields && formCustom.is_pt_time_limit_card != 1"
                               :disabled="isPreOrder">沿用旧卡
                            <Tooltip>
                                <div slot="content"
                                     style="white-space: normal; width: 200px">使用原有的会员卡，沿用之前的卡号等信息，直接延长使用时间、金额、次数等信息
                                </div>
                                <Icon size="16"
                                      type="ios-help-circle"
                                      style="padding-left: 5px"
                                      color="#ffcf05"></Icon>
                            </Tooltip>
                        </Radio>
                        <Radio label="1"
                               :disabled="isPreOrder">采用新卡
                            <Tooltip>
                                <div slot="content"
                                     style="white-space: normal; width: 200px">可替换成新的卡种，可采用新的卡号并且可以指定时间进行卡激活
                                </div>
                                <Icon size="16"
                                      type="ios-help-circle"
                                      style="padding-left: 5px"
                                      color="#ffcf05"></Icon>
                            </Tooltip>
                        </Radio>
                    </Radio-group>
                </Form-item>
                <!-- 续旧卡 -->
                <template v-if="cardData.renew_card_type == 0">
                    <Form-item :label="`续卡${unit}`"
                               prop="purchase_volume"
                               key="purchase_volume">
                        <InputNumber v-model="formCustom.purchase_volume"
                                     @on-change="handleVolumeChange"
                                     :min="0"
                                     :step="1"/>
                    </Form-item>
                    <Form-item :label="`赠送${unit}`"
                               prop="gift_volume">
                        <InputNumber :min="0"
                                     v-model="formCustom.gift_volume"
                                     @on-change="handleVolumeChange"/>
                    </Form-item>
                    <Form-item v-if="cardinfo.card_type != 1"
                               label="开始时间"
                               prop="active_time_after">
                        <Date-picker type="date"
                                     :disabled="true"
                                     :value="formCustom.active_time_after"
                                     format="yyyy-MM-dd"
                                     placeholder="请选择开始日期"></Date-picker>
                    </Form-item>
                    <Form-item v-if="cardinfo.card_type != 1"
                               label="结束时间"
                               prop="end_time_after"
                               :required="cardinfo.card_type==1">
                        <Date-picker type="date"
                                     @on-change="handleendChange"
                                     @on-clear="handleendChange"
                                     :value="formCustom.end_time_after"
                                     format="yyyy-MM-dd"
                                     :placeholder="cardinfo.card_type==1?'请选择结束日期':'不填写表示永久有效'"
                                     :editable="false"></Date-picker>
                    </Form-item>
                    <Form-item label="续卡后次数" v-if="cardinfo.card_type==2||cardinfo.card_type==4||cardinfo.card_type==5">
                        总次数{{all_num_after}}次，剩余次数{{formCustom.last_num_after}}次
                    </Form-item>
                    <Form-item label="续卡后金额"
                               v-if="cardinfo.card_type==3">
                        总金额{{all_num_after}}元，剩余金额{{formCustom.last_num_after}}元
                    </Form-item>
                    <Form-item label="续卡后有效期">
                        {{formCustom.active_time_after}}至{{(cardinfo.card_type!=1&&!formCustom.end_time_after)?'永久':formCustom.end_time_after}}
                    </Form-item>
                    <Form-item label="续卡后剩余天数"
                               prop="formCustom">
                        {{(cardinfo.card_type!=1&&!formCustom.end_time_after)?'永久':formCustom.last_day_after+'天'}}
                    </Form-item>
                </template>
                <!-- 续新卡 -->
                <card-Info v-else-if="cardData.renew_card_type == 1"
                           :isPreOrder="isPreOrder"
                           ref="cardComponent"
                           actionType="reNew"
                           :selectedCard.sync="selectedCard"
                           :coachList.sync="coachList"
                           :coachCur.sync="coachCur"
                           :isNormal="cardData.card_type != 4 && cardData.card_type != 5"
                           :isPt="cardData.card_type == 4"
                           :isSwim="cardData.card_type == 5"
                           :cardSn="cardData.card_sn"
                           :cardData="cardData"
                           :overRuleAuth="overRuleAuth"
                           @onPrivateNumChange="amount => privateTotalAmount = amount"
                          />
                <contract-Info :isPreOrder="isPreOrder"
                               ref="contractComponent"
                               actionType="reNew"
                               :contractData='cardData'
                               :selectedCard="selectedCard"
                               :coachList="coachList"
                               :labelWidth="140"
                               :privateTotalAmount="privateTotalAmount"
                               :isNormal="cardData.card_type != 4 && cardData.card_type != 5"
                               :isPt="cardData.card_type == 4"
                               :isSwim="cardData.card_type == 5"
                               :renewType="cardData.renew_card_type"/>

                <Modal v-model="showVerify" title="请输入会员收到的短信验证码" :closable="false" :mask-closable="false">
                    <Input v-model="verifyCode" placeholder="短信验证码">
                        <Button slot="append" @click="sendVerifyCode">重发
                        </Button>
                    </Input>
                    <div slot="footer" style="text-align:center;">
                        <div>
                            <Button @click="handleShutDownOrder">关闭</Button>
                            <Button type="success" @click="handleConfirmSuccess">确认</Button>
                        </div>
                        <!--<div style="margin-top:16px;">-->
                        <!--<span style="color:#AAA;">5分钟后未确认会取消该订单</span>-->
                        <!--</div>-->
                    </div>
                </Modal>

                <Form-item>
                    <div class="buttons">
                        <Button type="error"
                                style="border:none;"
                                v-if="isPreOrder"
                                @click="showRejectModal=true">退单
                        </Button>
                        <Button type="primary"
                                :loading="loadingFlag"
                                @click="handleSubmit('formCustom')">确定
                        </Button>
                        <Button @click="$router.back()">取消</Button>
                    </div>
                </Form-item>
            </Form>
        </div>
        <reject-order v-if="isPreOrder"
                      v-model="showRejectModal"
                      :id="$route.query.preOrdId"/>
        <receipt-modal v-model="showPrint"
                       :to-path='toPath'
                       @on-receiptupdate="receiptUpdate"/>
    </div>
</template>
<script>
  import {
    getcardInfo,
    getsalesInfo,
    getcoachsInfo
  } from '../../service/getData.js'
  import cardInfo from 'components/member/cardInfo'
  import contractInfo from 'components/member/contractInfo'
  import {formatDate, dateDiff, objectMerge} from 'utils'
  import LeftValue from './components/leftValue.vue'
  import receiptModal from 'components/receipt/receipt.vue'
  import receipt from 'mixins/receipt.js'
  import rejectOrder from 'components/member/RejectOrder.vue'
  import  EventBus from 'components/EventBus.js'
  import { mapActions, mapGetters } from 'vuex'
  export default {
    components: {
      contractInfo,
      rejectOrder,
      cardInfo,
      LeftValue,
      receiptModal
    },
    mixins: [receipt],
    created() {
      // this.finalData.is_package = this.$route.params.is_package==1?1:0
      if (this.$route.query.checkOrder) {
        this.isPreOrder = true
      }
      this.getCardInfo()
      this.getCoachList()
      this.getIsOpenDebtCardPay();
      this.getCanEditFields()

      this.getCardOverRuleAuth() // 规则范围可超出权限校验
    },
    data() {
      const purchaseValidator = (rule, value, cb) => {
        if (this.cardinfo.card_type == 3) {
          const checked = /^\d+(\.\d{0,2})?$/.test(value)
          if (checked && value >= 0) {
            cb()
          } else {
            cb(new Error('为数字且只能保留两位小数'))
          }
        } else {
          const checked = /^[0-9]\d*$/.test(value)
          if (checked) {
            cb()
          } else {
            cb(new Error('正整数或0'))
          }
        }
      }
      const giftValidator = (rule, value, cb) => {
        if (this.cardinfo.card_type == 3) {
          const checked = /^\d+(\.\d{0,2})?$/.test(value)
          if (checked && value >= 0) {
            cb()
          } else {
            cb(new Error('为数字且只能保留两位小数'))
          }
        } else {
          const checked = /^\d+$/.test(value)
          if (checked) {
            cb()
          } else {
            cb(new Error('正整数或0'))
          }
        }
      }
      var numPass = (rule, value, callback) => {
        if (this.cardinfo.card_type == 2 || this.cardinfo.card_type == 4 || this.cardinfo.card_type == 5) {
          var checkval = /^\d+$/.test(value)
          if (checkval) {
            callback()
          } else {
            callback(new Error('请输入正确（>=0)'))
          }
        } else {
          checkval = /^-?[0-9]+(.[0-9]{1,2})?$/.test(value)
          if (checkval) {
            callback()
          } else {
            callback(new Error('请输入正确的金额（为数字且只能保留两位小数)'))
          }
        }
      }
      var enddatePass = (rule, value, callback) => {
        if (value) {
          var checkval = /^(\d{4})-(\d{2})-(\d{2})$/.test(value)
          if (checkval) {
            let checkdiff = dateDiff(value, this.formCustom.active_time_after)
            //选择的结束时间与开始时间比较，不得早于开始时间
            if (checkdiff >= 0) {
              //结束时间的初始值不为永久
              if (this.cardinfo.last_day != '永久') {
                let today = formatDate(new Date(), 'yyyy-MM-dd')
                let checktoday = dateDiff(value, today)
                //选择日期早于今天
                if (checktoday < 0) {
                  this.formCustom.last_day_after = 0
                } else {
                  //选择的结束时间晚于今天
                  let checktoday_3 = dateDiff(this.cardinfo.end_time_date, today)
                  //卡已过期，初始的结束时间早于今天，用结束时间减今天
                  if (checktoday_3 < 0) {
                    let datediff_end = dateDiff(value, today)
                    this.formCustom.last_day_after = datediff_end + 1
                  } else {
                    //卡未过期，用初始的剩余天数加差值天数。（常规状态）
                    let datediff_end = dateDiff(
                        value,
                        this.cardinfo.end_time_date
                    )
                    this.formCustom.last_day_after =
                        this.cardinfo.last_day + datediff_end
                    let datediff_beg = dateDiff(
                        this.formCustom.active_time_after,
                        this.cardinfo.active_time_date
                    )
                    this.formCustom.last_day_after =
                        this.formCustom.last_day_after - datediff_beg
                  }
                }
              } else {
                //结束时间的初始值为永久
                let today = formatDate(new Date(), 'yyyy-MM-dd')
                let checktoday = dateDiff(this.formCustom.end_time_after, today)
                //选取的结束时间早于今天
                if (checktoday < 0) {
                  this.formCustom.last_day_after = 0
                } else {
                  //选取的结束时间晚于今天
                  this.formCustom.last_day_after = dateDiff(value, today) + 1
                }
              }
              callback()
            } else {
              callback(new Error('结束日期不得早于开始日期'))
            }
          } else {
            callback(new Error('请输入正确的日期格式(YYYY-MM-DD)'))
          }
        }
        if (!value && this.cardinfo.card_type != 1) {
          callback()
        }
        if (!value && this.cardinfo.card_type == 1) {
          callback(new Error('请选择结束日期'))
        }
      }
      return {
        isOpenDebtCardPay: false,
        showVerify: false,
        verifyCode: '',
        verifyId: null,
        finalData: {},

        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now()
          }
        },
        showRejectModal: false,
        isPreOrder: false,
        loadingFlag: false,
        overRuleAuth: false, // 规则范围可超出权限
        cardData: '',
        title: '',
        selectedCard: {
          card_type_id: ''
        },
        coachCur: '',
        user_id: this.$route.params.userId,
        card_user_id: this.$route.params.cardUserId,
        coachList: '',
        formCustom: {
          last_num_after: '',
          active_time_after: '',
          end_time_after: '',
          last_day_after: '',
          renew_card_type: '0',
          purchase_volume: 0,
          is_pt_time_limit_card: 0,
          gift_volume: 0
        },
        initLeftDay: 0,//初始剩余天数
        initLeftNum: 0,//初始剩余次数 金额
        cardinfo: {}, //旧卡信息
        privateTotalAmount: 0, // 私教课通过单价和节数计算的合同金额
        preOrderData: {},
        ruleCustom: {
          purchase_volume: [
            {
              validator: purchaseValidator,
              required: true,
              trigger: 'change'
            }
          ],
          gift_volume: [
            {
              validator: giftValidator,
              required: true,
              trigger: 'change'
            }
          ],
          last_num_after: [
            {
              required: true,
              message: '请正确填写剩余次数/金额'
            },
            {
              validator: numPass,
              trigger: 'change'
            }
          ],
          end_time_after: [
            {
              validator: enddatePass,
              trigger: 'change'
            }
          ]
        },
      }
    },
     watch: {
      canEditFields: {
        handler(val) {
          if(!val) {
            this.formCustom.renew_card_type = '1'
          }
        },
        immediate: true
      }
     },
    computed: {
      ...mapGetters(['canEditFields']),
      all_num_after() {
        let after =
            Number(this.cardinfo.all_num) +
            Number(this.formCustom.purchase_volume) +
            this.formCustom.gift_volume
        if (this.cardinfo.card_type == 3) {
          after = after.toFixed(2)
        }
        return after
      },
      unit() {
        return this.cardinfo.card_type == 1
            ? '天数'
            : this.cardinfo.card_type == 2 || this.cardinfo.card_type == 4 || this.cardinfo.card_type == 5 ? '次数'
                : this.cardinfo.card_type == 3 ? '金额' : ''
      },
      isExpired() {
        return (
            new Date(this.initEndDate).getTime() + 24 * 3600 * 1000 - Date.now() < 0
        )
      }
    },
    methods: {
      ...mapActions(['getCanEditFields']),
      getIsOpenDebtCardPay() {
        this.$service.get('/Web/Commodity/get_setting').then(res => {
          if (res.data.errorcode == 0) {
            this.isOpenDebtCardPay = res.data.data.open_confirm == 1 ? true : false;
          } else {
            this.$Message.error(res.data.message)
          }
        })
      },
      // 规则范围可超出权限校验
      getCardOverRuleAuth() {
        this.$service.post('/Web/CardSaleRule/over_rule_auth').then(res => {
          if (res.data.errorcode === 0) {
            this.overRuleAuth = true;
          } else if (res.data.errorcode === 40014) {
            this.overRuleAuth = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      sendVerifyCode() {
        if (this.verifyId) {
          this.$service.post('/Confirm/send_sms_code', {'id': this.verifyId}).then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success("短信发送成功");
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      },
      handleShutDownOrder() {
        this.showVerify = false;
        this.verifyCode = '';
        this.loadingFlag = false;
      },
      async handleConfirmSuccess() {
        //先验证验证码是否正确，在提交数据
        this.$service.post('/Confirm/post_confirm', {'id': this.verifyId, 'code': this.verifyCode}).then(res => {
          if (res.data.errorcode === 0) {
            this.postFinalData(this.finalData);
          } else {
            this.$Message.error('验证码错误');
          }
        });
      },

      receiptUpdate() {
        this.$router.back()
      },
      renewTypeChange(val) {
        this.cardData.renew_card_type = val
      },
      getPreOrder() {
        return this.$service
            .post('/Web/preBilling/orderInfo', {
              id: this.$route.query.preOrdId,
              user_id: this.$route.query.userId
            })
            .then(res => {
              if (res.data.errorcode == 0) {
                let resInfo = res.data.data.info
                resInfo.purchase_volume = Number(resInfo.purchase_volume)
                resInfo.gift_volume = Number(resInfo.gift_volume)
                this.formCustom = Object.assign(this.formCustom, resInfo)
                this.initCardInfo(resInfo)
                this.cardData = resInfo.renew_card_type ? this.formCustom : resInfo
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
      },
      handleVolumeChange() {
        if (this.cardinfo.card_type == 1) {
          this.formCustom.last_day_after = +this.initLeftDay + this.formCustom.gift_volume + this.formCustom.purchase_volume
          if (this.isExpired) {
            this.formCustom.end_time_after = formatDate(new Date(Date.now() + (this.formCustom.gift_volume + this.formCustom.purchase_volume - 1) * 24 * 3600 * 1000),
                'yyyy-MM-dd')
          } else {
            this.formCustom.end_time_after = formatDate(new Date(new Date().getTime() + (this.formCustom.last_day_after - 1) * 24 * 3600 * 1000), 'yyyy-MM-dd')
          }
        } else {
          this.formCustom.last_num_after = Number(this.initLeftNum) + this.formCustom.gift_volume + this.formCustom.purchase_volume
          if(this.cardinfo.card_type == 3) {
            this.formCustom.last_num_after = this.formCustom.last_num_after.toFixed(2)
          }
        }
      },
      //获取教练列表
      getCoachList(cardId) {
        getcoachsInfo(cardId).then(res => {
          if (res.data.errorcode == 0) {
            this.coachList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      },
      handleendChange(date) {
        this.formCustom.end_time_after = date
      },
      initCardInfo(info) {//核单卡初始卡信息通过orderinfo获取  续卡通过cardinfo
        this.formCustom.is_pt_time_limit_card = info.is_pt_time_limit_card || 0
        if(this.formCustom.is_pt_time_limit_card == 1) {
          this.formCustom.renew_card_type = '1'
        }
        this.selectedCard.card_type_id = info.card_type || info.card_type_id
        this.selectedCard.card_id = info.card_id // fix: 14109 当折扣券类型限制指定卡种时，续旧卡符合条件时无法选中折扣券,因为amountItems组件里面没有拿到旧卡的card_id
        this.formCustom.last_num_after = String(info.last_num)
        this.formCustom.active_time_after = info.active_time_date || info.active_time
        this.formCustom.card_last_value = info.card_last_value
        if (info.end_time == '2000000001') {
          this.formCustom.end_time_after = ''
        } else {
          this.formCustom.end_time_after = info.end_time_date
          this.initEndDate = info.end_time_date
        }
        this.formCustom.last_day_after = info.last_day
        this.initLeftDay = this.isPreOrder ? info.last_day - info.purchase_volume - info.gift_volume : info.last_day
        this.initLeftNum = this.isPreOrder ? info.last_num - info.gift_volume - info.purchase_volume : info.last_num
        this.formCustom.marketers_id = info.marketers_id
      },
      getCardInfo() {
        var postData = {
          card_user_id: this.card_user_id,
          type: 'renewCard'
        }
        getcardInfo(postData)
            .then(response => {
              if (response.status === 200) {
                if (response.data.errorcode === 0) {
                  const {info} = response.data.data
                  this.cardinfo = response.data.data.info
                  EventBus.$emit('on-pay-des-change', `续卡[${info.card_name}]`)
                  if (info.card_type == 4) {
                    this.title = '续私教'
                  } else if (info.card_type == 5) {
                    this.title = '续泳教'
                  } else {
                    this.title = '续卡'
                  }
                  if (this.isPreOrder) {
                    this.getPreOrder()
                  } else {
                    this.initCardInfo(info)
                    this.cardData = {
                      ...this.cardinfo,
                      ...this.formCustom
                    }
                  }
                }
              } else {
                this.$Message.error(response.data.errormsg)
              }
            })
            .catch(function (response) {
              console.log(response)
            })
      },
      postFinalData() {
        // this.finalData.is_package = this.$route.params.isPackage==1?1:0
        const params = { ...this.finalData };
        params.is_package = this.$route.params.isPackage==1 ? 1 : 0;
        params.over_rule_auth = this.overRuleAuth ? 1 : 0; // 范围可超出的权限,有权限传1，没得传0或者空
        this.$service
            .post(`/Web/MemberCard/${this.$route.query.preOrdId?'check_pre_bill_renew_card':'renewCard'}`, params)
            .then(response => {
              if (response.status == 200) {
                if (response.data.errorcode == 0) {
                  // this.$Message.success(response.data.errormsg);
                  // setTimeout(() => {
                  //   this.$router.back();
                  // }, 1000);
                  let contractType = 'renewcard'
                  // if(this.cardinfo.card_type == 4 || this.cardinfo.card_type == 5) {
                  switch (+this.selectedCard.card_type_id) {
                    case 4:
                      contractType = 'renewcard-coach';
                      break;
                    case 5:
                      contractType = 'renewcard-swim';
                      break;
                    case 6:
                      contractType = 'buypackage';
                      break;
                  }
                  this.contractComplete(
                      this.finalData.user_id,
                      response.data.card_order_info_id,
                      contractType
                  )
                } else {
                  this.$Message.error(response.data.errormsg)
                }
              }
              this.loadingFlag = false
            })
            .catch(function (response) {
              console.log(response)
              this.loadingFlag = false
            })
      },
      handleSubmit(name) {
        if (this.loadingFlag) {
          return false
        }
        this.loadingFlag = true
        this.$refs[name].validate(async valid => {
          let cardInfoData = {}
          let contractInfoData = await this.$refs.contractComponent.handleSubmitClick()
          let errMsg = '';
          if (this.cardData.renew_card_type != 0) {
            cardInfoData = this.$refs.cardComponent.handleSubmitClick()
            valid = contractInfoData.valid && cardInfoData.valid && valid
            // 校验购卡范围权限及范围, false 规则范围不可超出
            if(this.overRuleAuth === false) {
              const { purchase_volume, gift_volume } = cardInfoData.postData;
              const { amount } = contractInfoData.postData;
              const isOK = this.checkCardRules(valid, purchase_volume, gift_volume, amount)
              if(isOK !== 'ok') {
                valid = false;
                errMsg = isOK;
              }
            }
          } else {
            valid = contractInfoData.valid && valid
          }

          if (valid) {
            if (this.cardData.card_type == 1) {
              var chargetype = 2
            } else {
              chargetype = 1
            }
            let postData = {
              card_user_id: this.card_user_id,
              card_type_id: this.cardinfo.card_type,
              cardname: this.cardinfo.card_name,
              charge_type: chargetype,
              last_num: this.formCustom.last_num_after,
              active_time: this.formCustom.active_time_after,
              renew_card_type: this.cardData.renew_card_type,
              purchase_volume: this.formCustom.purchase_volume,
              gift_volume: this.formCustom.gift_volume,
              pre_id: this.$route.query.preOrdId || ''
            }
            // console.log(postData, cardInfoData.postData, contractInfoData.postData);
            this.finalData = Object.assign(
                postData,
                cardInfoData.postData,
                contractInfoData.postData,
                {
                  end_time: cardInfoData.postData
                      ? cardInfoData.postData.getEndDay
                      : this.formCustom.end_time_after || '永久有效',
                  last_day: cardInfoData.postData && cardInfoData.postData.end_time,
                  user_id: this.user_id
                }
            )
            const hasPayTypeEight = this.finalData.new_pay_type.some(obj => obj.pay_type === 8 && obj.card_user_id);
            if (hasPayTypeEight && this.isOpenDebtCardPay) {
              this.showVerify = true;
              //请求验证码
              let sendData = {
                type: 1,
                data: this.finalData,
                confirm_type: 1,
                user_id: this.finalData.user_id,
                amount: this.finalData.amount
              };
              this.$service.post('/Confirm/create_order', sendData).then(res => {
                if (res.data.errorcode === 0) {
                  this.verifyId = res.data.data.id;
                  this.sendVerifyCode();
                } else {
                  this.$Message.success(res.data.errormsg);
                  this.loadingFlag = false;
                }
              });
            } else {
              this.postFinalData();
            }
          } else {
            this.$Message.error(errMsg || '表单验证失败')
          }
          this.loadingFlag = false
        })
      },
      /* 校验购卡范围权限及范围 */
      checkCardRules(valid, purchase_volume, gift_volume, amount) {
        const { selectedCard: selected } = this;
        let errMsg = 'ok';
        if(valid && selected && selected.is_open_rule === '1' && selected.get_range && selected.card_type_id != '6') {
          const {
            card_type_id,
            is_pt_time_limit_card,
            get_range: { buy_max, gift_max, sale_range }
          } = selected;
          const index = is_pt_time_limit_card == '1' ? 5 : +card_type_id - 1;
          const tips = [
            ['购买天数', '购买次数','价值金额', '购买天数', '购买天数', '购买天数'],
            ['赠送天数', '赠送次数','赠送金额', '赠送节数', '赠送节数', '赠送天数'],
            ['售价', '售价', '售价', '单节售价', '单节售价', '售价'],
          ];

          if(buy_max !== '' && purchase_volume > +buy_max) { // '' 代表无限制
            errMsg = tips[0][index] + '不能大于上限！';
          }else if(gift_max !== '' && gift_volume > +gift_max) { // '' 代表无限制
            errMsg = tips[1][index] + '不能大于上限！';
          }else if(!Array.isArray(sale_range)) { // [] 代表无限制
            const { max, min } = sale_range;

            if((card_type_id == '4' || card_type_id == '5') && is_pt_time_limit_card != '1' ) {
              if((amount / purchase_volume).toFixed(2) > +max || (amount / purchase_volume).toFixed(2) < +min) {
                errMsg = tips[2][index] + '不在浮动范围内！';
              }
            }else {
              if(amount > +max || amount < +min) {
                errMsg = tips[2][index] + '不在浮动范围内！';
              }
            }
          }
        }
        return errMsg
      },
    }
  }
</script>
<style scoped>
</style>
