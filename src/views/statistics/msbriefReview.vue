<template>
  <div class="tab-table-wrap customized-tabs">
    <Tabs @on-click="clickTabs">
      <TabPane label="场馆/组别汇总统计" name="0">
       <MsGroup :type="1" />
      </TabPane>
      <TabPane label="会籍人员明细统计" name="1">
       <MsGroup :type="2" v-if="show" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import MsGroup from './components/MsGroup.vue'
  export default {
    name: 'MsGroupAndDetail',
    components: {MsGroup},
    data() {
      return {
        activeIndex: 0,
        show: false
      }
    },
    methods: {
      clickTabs(index) {
        this.activeIndex = index
        if (this.activeIndex==1) {
          this.show = true
        }
        const active = document.querySelector('.ivu-tabs-ink-bar')
        const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${index}`
        active.setAttribute('class', className)
      }
    }
  }
</script>

<style lang="less" scoped>
.ivu-tabs-tabpane {
  .table-wrap{
    border-top: 0;
  }
}

</style>
