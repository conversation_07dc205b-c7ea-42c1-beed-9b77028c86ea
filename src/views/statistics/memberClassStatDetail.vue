<template>
<div>
  <Alert type="warning" show-icon style="marginBottom:10px">此报表仅从2022.11.08日开始统计</Alert>
  <div class="table-wrap">
    <header>
      <DatePicker
        type="month"
        :value="postData.month"
        :options="dateOptions"
        placeholder="请选择查询月份"
        @on-change="dateChange"></DatePicker>
      <UserSearch
        v-model="postData.user_id"
        url="/Web/Member/search_all_member"
        placeholder="输入会员名称/电话"
        @on-change="doSearch"
      />
    </header>
    <Table
      ref="table"
      :data="tableData"
      :columns="columns"
      disabledHover
      stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :history="false"
        :total="total"
        :postData="postData"
        @on-change="pageChange"></Pager>
    </footer>
  </div>
</div>
</template>

<script>
  import { formatDate } from 'utils';
  import UserSearch from "components/user/userSearch";
  import Export from 'src/components/Export';
  // import ExportButton from 'components/form/ExportButton'
  import Pager from 'components/pager';

  export default {
    name: 'MemberClassStatDetail', // 会员上课统计明细
    components: {
      UserSearch,
      Export,
      Pager,
    },

    data() {
      const startDate = new Date('2022-10-01');
      return {
        dateOptions: {
          disabledDate(date) {
            return date.valueOf() > Date.now() || date <= startDate;
          }
        },
        postData: {
          month: this.$route.params.month || formatDate(new Date(), 'yyyy-MM'),
          user_id: '',
          page_no: 1,
          page_size: 10,
          // _export: 0 // 是否导出 0否
        },
        tableData: [],
        total: 0,
        columns: [
          {
            key: 'username',
            title: '会员姓名',
          },
          {
            key: 'class_mark_count',
            title: '预约次数'
          },
          {
            key: 'sign_count',
            title: '签到次数'
          },
          {
            key: 'miss_count',
            title: '爽约次数'
          },
          {
            key: 'sign_day',
            title: '上课天数'
          },
          {
            key: 'class_name',
            title: '哪种课程上的最多'
          },
          {
            key: 'coach_name',
            title: '哪个教练的课上的最多'
          },
          {
            key: 'total_value',
            title: '成交价值',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  成交价值
                  <tooltip max-width="220" placement="left-start" transfer>
                    <div slot="content">
                      会员在时间段内总共支付团课的金额(包含使用储值卡、次卡、私教卡、泳教卡、现金支付)
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
        ],
      };
    },
    // computed: {
    //   exportPostData() {
    //     const { postData, total } = this
    //     return {
    //       ...postData,
    //       page_no: 1,
    //       page_size: +total || 999
    //     }
    //   }
    // },

    created() {
      this.getList()
    },

    methods: {
      getList() {
        this.$service
          .get('/web/Statistics/openClassUserMonthDetail', { params: this.postData })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      exportCsv() {
        this.$service.get('/web/Statistics/openClassUserMonthDetail', {
          params: {
            ...this.postData,
            page_no: 1,
            page_size: +this.total || 99999,
            _export: 1
          }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$Message.success({
              content: '导出任务运行中，请稍后到消息中心下载!',
              duration: 3
            })
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        if (this.postData.month !== info) {
          this.postData.month = info
          this.doSearch()
        }
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
    }
  };
</script>

<style scoped lang="less">
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .icon-tips {
    padding-left: 5px;
    vertical-align:text-bottom;
  }
</style>
