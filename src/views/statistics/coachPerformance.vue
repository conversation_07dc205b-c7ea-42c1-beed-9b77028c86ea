<template>
  <div class="merge-table-box">
    <div class="box-head">
      <div class="head-lef">
      <Select v-model="selectBusId" style="margin-left:20px;width:200px" @on-change="handleStoreChange" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <div class="datepicker">
        <DatePickerWithButton @on-change="handleDateRangeChanged" :days.sync="timeSlot"></DatePickerWithButton>
      </div>
      <AdminRegion v-model="choseGroup" :busId="selectBusId" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" url="/Web/CoachGroup/getCoachGroupList" :multiple="false" />
      <!--<div class="gym-select">
        <Select v-model="selectedGroup" @on-change="handleGymChanged" v-for="group in groupList" :key="group.group_id" filterable v-if="groupList">
          <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
          <Option v-for="coach in group.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}
          </Option>
          <template v-if="group.son">
            <Option-group v-for="group in group.son" :key="group.group_id">
              <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
              <Option style="padding-left: 30px" v-for="coach in group.coach_list" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name }}
              </Option>
              <template v-if="group.son">
                <Option-group v-for="group in group.son" :key="group.group_id">
                  <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
                  <Option style="padding-left: 30px" v-for="coach in group.coach_list" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name }}
                  </Option>
                </Option-group>
              </template>
            </Option-group>
          </template>
        </Select>
      </div>-->
      </div>
      <Button class="head-rig" @click="handleExport" type="success">导出</Button>
    </div>
    <div class="total-stat" v-if="totalStat">
      <!-- <span>私教统计</span> -->
      <div class="stat">
        <h3>{{ totalStat.order_count }}</h3>
        <p>订单总数</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.added_class_count }}</h3>
        <p>新售课时</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.consume_class_count }}</h3>
        <p>消耗课时</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.help_deal_amount }}</h3>
        <p>协助业绩
          <Tooltip content="作为成单协助人分得的业绩金额">
            <Icon type="ios-help-circle" style="color: rgb(247, 220, 111)"></Icon>
          </Tooltip></p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ totalStat.total_amount }}</h3>
        <p>业绩金额</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ totalStat.once_amount }}</h3>
        <p>课程单价
          <Tooltip content="课程单价=业绩金额/新售课时">
            <Icon type="ios-help-circle" style="color: rgb(247, 220, 111)"></Icon>
          </Tooltip>
        </p>
      </div>
    </div>
    <Row>
      <Col span="22" offset="1">
      <div class="checked-box">
        <Checkbox :indeterminate="checkedOrder.indeterminate" :value="checkedOrder.allChecked" @click.prevent.native="handleOrderCheckAll">订单类型</Checkbox>
        <Checkbox-group v-model="checkedOrder.columnsChecked" @on-change="handleOrderChangeTableColumns">
          <Checkbox v-for="item in checkedOrderList" :key="item.key" :label="item.key" >{{item.name}}</Checkbox>
        </Checkbox-group>
      </div>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1">
      <div class="checked-box">
        <Checkbox :indeterminate="checkedOrderWeight.indeterminate" :value="checkedOrderWeight.allChecked" @click.prevent.native="handleOrderWeightCheckAll">订单维度</Checkbox>
        <Checkbox-group v-model="checkedOrderWeight.columnsChecked" @on-change="handleOrderWeightChangeTableColumns">
          <Checkbox v-for="item in checkedOrderWeightList" :key="item.key" :label="item.key" >{{item.name}}</Checkbox>
        </Checkbox-group>
      </div>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1">
      <div class="checked-box">
        <Checkbox :indeterminate="checkedShow.indeterminate" :value="checkedShow.allChecked" @click.prevent.native="handleShowCheckAll">展示内容</Checkbox>
        <Checkbox-group v-model="checkedShow.columnsChecked" @on-change="handleShowChangeTableColumns">
          <Checkbox v-for="item in checkedShowList" :key="item.key" :label="item.key" >{{item.name}}</Checkbox>
        </Checkbox-group>
      </div>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1" style="border: 1px solid #dddee1;">
      <Table class="table-2b" disabled-hover v-if="outTableShow" :row-class-name="rowClassName" :height="tableHeight" :columns="outCol" :data="outData"></Table>
      </Col>
    </Row>
    <Row style="height: 22px;"></Row>
    <Modal :mask-closable="false"
         v-model="isShowExcelModal"
         title="导出">
        <div>
      <RadioGroup v-model="exportType" vertical class="radio-lines" @on-change="staExport">
          <Radio :label="0">
              <span>成交方式导出</span>
          </Radio>
          <Radio :label="1">
              <span>课程种类导出</span>
          </Radio>
          <Radio :label="2">
              <span>教练详情导出</span>
          </Radio>
        </RadioGroup>

    </div>
      <div slot="footer">
      </div>
    </Modal>
     <Export ref="export" :string-export="true" />
  </div>
</template>
<script>
  import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';
  import AdminRegion from 'components/form/adminRegion.vue';
  import Export from 'src/components/Export';
  import { mapState } from 'vuex';
  import { getBaseUrl, getHost } from 'utils/config';

  export default {
    data () {
      return {
        checkedOrderList: [{
          key: 'total',
          apiKey: 'col_total',
          name: '总计'
        },{
          key: 'new',
          apiKey: 'newopen_card',
          name: '新开卡'
        },{
          key: 'renewal',
          apiKey: 'renew_card',
          name: '续卡'
        },{
          key: 'cancle',
          apiKey: 'stop_card',
          name: '销卡'
        },{
          key: 'change',
          apiKey: 'change_card',
          name: '升卡'
        },{
          key: 'split',
          apiKey: 'split_card',
          name: '拆分'
        },{
          key: 'other',
          apiKey: 'other',
          name: '请假/转卡/补卡'
        }],
        checkedOrderWeightList: [{
          key: 'order',
          apiKey: 'order_count',
          name: '订单数'
        },{
          key: 'course',
          apiKey: 'class_count',
          name: '课时数'
        },{
          key: 'help',
          apiKey: 'help_deal',
          name: '协助业绩'
        },{
          key: 'price',
          apiKey: 'amount',
          name: '总业绩'
        },{
          key: 'proportion',
          apiKey: 'order_percent',
          name: '订单数占比'
        }],
        checkedShowList: [{
          key: 'deal',
          name: '成交方式'
        },{
          key: 'type',
          name: '课程类型'
        },{
          key: 'coach',
          name: '教练'
        }],
        choseGroup: "",
        choseGroupId: "",
        tableHeight: '',
        isShowExcelModal: false,
        exportType: '',
        outTableShow: true,
        inTableShow: true,
        checkedOrder: {
          columnsChecked: ['total', 'new', 'renewal', 'cancle', 'change', 'split', 'other'],
          allChecked: true,
          indeterminate: false
        },
        checkedOrderWeight: {
          columnsChecked: ['order', 'course', 'help', 'price', 'proportion'],
          allChecked: true,
          indeterminate: false
        },
        checkedShow: {
          columnsChecked: ['deal', 'type', 'coach'],
          allChecked: true,
          indeterminate: false
        },
        finalSubHeadCol: [
          {
            key: 'order_count',
            code: 'order',
            width: 99,
            render: (h, params) => {
              const rowInfo = params.row
              if (rowInfo.shouldLink && +rowInfo.id) {
              return ( <a
                    href="javascript:void(0)"
                    on-click={() => {
                      // id c${xx}代表教练
                      // this.$router.push(`/Web/Statistics/cardOrderList?busId=${this.selectBusId}&id=c${rowInfo.id}&name=${rowInfo.name}&s_date=${this.timeSlot[0]}&e_date=${this.timeSlot[1]}`)
                      // this.$router.push(`/finance/order-log?busId=${this.selectBusId}&id=c${rowInfo.id}&name=${rowInfo.name}&s_date=${this.timeSlot[0]}&e_date=${this.timeSlot[1]}`)
                      const url = `/finance/order-log?busId=${this.selectBusId}&id=c${rowInfo.id}&name=${rowInfo.name}&s_date=${this.timeSlot[0]}&e_date=${this.timeSlot[1]}`
                      const url2b = window.location.protocol + '//' + window.location.host + url
                      // this.$router.push({
                      //  base: '/',
                      //  path: url,
                      // })
                      window.open(url2b, '_self')
                    }}>
                    {rowInfo.order_count}
                  </a>)
              } else {
                return (<span>{rowInfo.order_count}</span>)
              }

            }
          },
          {
            key: 'class_count',
            code: 'course',
            width: 99
          },
          {
            key: 'help_deal',
            code: 'help',
            width: 99
          },
          {
            key: 'amount',
            code: 'price',
            width: 99
          },
          {
            key: 'order_percent',
            code: 'proportion',
            width: 99
          }
        ],
        subHeadCol: [],
        subHeadData: [{
          order_count: '订单',
          class_count: '课时数',
          help_deal: '协助业绩',
          amount: '总业绩',
          order_percent: '订单数占比',
        }],
        coachCol: [{
          title: '教练名字',
          key: 'coachName',
          width: 150,
          render: (h, params) => {
            return (<span class="ten-word">{params.row.coachName}</span>)
          }
        }],
        coachData: [],
        coachTotalDB: [],
        coachNewCardDB: [],
        coachRenewalCardDB: [],
        coachCancleCardDB: [],
        coachChangeCardDB: [],
        coachSplitCardDB: [],
        coachOtherCardDB: [],
        // coachHelpDealCardDB: [],
        dealMethodCol: [{
          title: '成交方式',
          key: 'dealMethod',
          width: 150,
          render: (h, params) => {
            return (<span class="ten-word">{params.row.dealMethod}</span>)
          }
        }],
        dealMethodData: [],
        dealMethodTotalDB: [],
        dealMethodNewCardDB: [],
        dealMethodRenewalCardDB: [],
        dealMethodCancleCardDB: [],
        dealMethodChangeCardDB: [],
        dealMethodSplitCardDB: [],
        dealMethodOtherCardDB: [],
        // dealHelpDealCardDB: [],
        courseTypeCol: [{
          title: '课程类别',
          key: 'courseType',
          width: 150,
          render: (h, params) => {
            return (<span class="ten-word">{params.row.courseType}</span>)
          }
        }],
        courseTypeData: [],
        courseTypeTotalDB: [],
        courseTypeNewCardDB: [],
        courseTypeRenewalCardDB: [],
        courseTypeCancleCardDB: [],
        courseTypeChangeCardDB: [],
        courseTypeSplitCardDB: [],
        courseTypeOtherCardDB: [],
        // courseHelpDealCardDB: [],
        allTotalDB: [],
        allNewCardDB: [],
        allRenewalCardDB: [],
        allCancleCardDB: [],
        allChangeCardDB: [],
        allSplitCardDB: [],
        allOtherCardDB: [],
        // allHelpDealCardDB: [],
        finalOutCol: [
          {
            title: ' ',
            code: 'god',
            key: 'rowName',
            width: 99,
            fixed: 'left',
            className: 'left-head'
          },
          {
            title: ' ',
            code: 'god',
            key: 'rowSubName',
            width: 150,
            fixed: 'left',
            className: 'left-head',
            render: (h, params) => {
              if (params.row.rowSubName === '') {
                return ' ';
              } else if (params.row.rowSubName === '总计') {
                return '总计';
              } else if (params.row.rowName === '成交方式') {
                return (<i-table class="no-scroll" stripe disabled-hover show-header={false} columns={this.dealMethodCol} data={this.dealMethodData}></i-table>);
              } else if (params.row.rowName === '课程种类') {
                return (<i-table class="no-scroll" stripe disabled-hover show-header={false} columns={this.courseTypeCol} data={this.courseTypeData}></i-table>);
              } else if (params.row.rowName === '教练') {
                return (<i-table class="no-scroll" stripe disabled-hover show-header={false} columns={this.coachCol} data={this.coachData}></i-table>);
              }
            }
          },
          {
            title: '总计',
            code: 'total',
            key: 'total',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachTotalDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeTotalDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodTotalDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allTotalDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '新开卡',
            code: 'new',
            key: 'newCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachNewCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeNewCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodNewCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allNewCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '续卡',
            code: 'renewal',
            key: 'renewalCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachRenewalCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeRenewalCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodRenewalCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allRenewalCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '销卡',
            code: 'cancle',
            key: 'cancleCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachCancleCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeCancleCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodCancleCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allCancleCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '升卡',
            code: 'change',
            key: 'changeCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachChangeCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeChangeCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodChangeCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allChangeCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '拆分',
            code: 'split',
            key: 'splitCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachSplitCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeSplitCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodSplitCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allSplitCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          },
          {
            title: '请假/转卡/补卡',
            code: 'other',
            key: 'otherCard',
            width: 500,
            render: (h, params) => {
              if (params.row.rowName === '教练') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.coachOtherCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '课程种类') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.courseTypeOtherCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '成交方式') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.dealMethodOtherCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '总计') {
                if (this.inTableShow) {
                  return (<i-table stripe disabled-hover show-header={false} columns={this.subHeadCol} data={this.allOtherCardDB}></i-table>);
                } else {
                  return '';
                }
              } else if (params.row.rowName === '' && params.row.rowSubName === '') {
                if (this.inTableShow) {
                  const subHead = (item) => {
                    const sbData = this.subHeadData[0][item.key];
                    return (<div class="sub-head-item">{sbData}</div>);
                  };
                  return (<div class="sub-head">{this.subHeadCol.map(subHead)}</div>);
                } else {
                  return '';
                }
              }
            }
          }
        ],
        outCol: [],
        infoList: null,
        finalOutData: [
          {
            rowName: '',
            rowSubName: '',
            code: 'god'
          },
          {
            rowName: '',
            rowSubName: '总计',
            code: 'god'
          },
          {
            rowName: '成交方式',
            code: 'deal'
          },
          {
            rowName: '课程种类',
            code: 'type'
          },
          {
            rowName: '教练',
            code: 'coach'
          },
        ],
        outData: [],
        timeSlot: null,
        groupList: null,
        totalStat: null,
        groupId: '',
        coachId: '',
        selectedGroup: '',
        storeList: [],
        selectBusId: ""
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    created () {
      this.getStoreList()
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      staExport(val) {
        let curShowName = this.checkedShowList[val].name
        let line1 = `<th rowspan="2">${curShowName}</th>`
        let line2 = ''
        this.checkedOrderList.forEach((item)=>{
          line1 += `<th colspan="5">${item.name}</th>`
          this.checkedOrderWeightList.forEach(item=> {
            line2 += `<th>${item.name}</th>`
          })
        })
        const curListData = this.infoList[val===0?'source_list':val===1? 'card_type_list':'coach_list']
        let bodyLine = ''
        curListData.forEach((item)=>{
          let bodyLineCon = `<td>${item.name}</td>`
          this.checkedOrderList.forEach((sitem)=>{
            this.checkedOrderWeightList.forEach(subitem=> {
              bodyLineCon += `<td>${item[sitem.apiKey][subitem.apiKey]}</td>`
            })
          })
          bodyLine += `<tr>${bodyLineCon}</tr>`
        })
        let headString= `<thead><tr>${line1}</tr><tr>${line2}</tr></thead>`
        let bodyString= `<tbody>${bodyLine}</tbody>`
        let data = `${headString}${bodyString}`
        this.isShowExcelModal = false
        this.$refs.export.export({
          data,
          filename: `教练业绩统计-${curShowName}`
        });
      },
      handleExport() {
        this.isShowExcelModal = true
      },
      rowClassName (row, index) {
        if (row.rowName === '' && row.rowSubName === '总计') {
          return 'total-row';
        } else {
          return '';
        }
      },
      handleOrderCheckAll () {
        if (this.checkedOrder.indeterminate) {
          this.checkedOrder.allChecked = false;
        } else {
          this.checkedOrder.allChecked = !this.checkedOrder.allChecked;
        }
        this.checkedOrder.indeterminate = false;

        if (this.checkedOrder.allChecked) {
          this.checkedOrder.columnsChecked = ['total', 'new', 'renewal', 'cancle', 'change'];
        } else {
          this.checkedOrder.columnsChecked = [];
        }
        this.hitOrderTableColumns();
      },
      handleOrderWeightCheckAll () {
        if (this.checkedOrderWeight.indeterminate) {
          this.checkedOrderWeight.allChecked = false;
        } else {
          this.checkedOrderWeight.allChecked = !this.checkedOrderWeight.allChecked;
        }
        this.checkedOrderWeight.indeterminate = false;

        if (this.checkedOrderWeight.allChecked) {
          this.checkedOrderWeight.columnsChecked = ['order', 'course', 'help', 'price', 'proportion'];
        } else {
          this.checkedOrderWeight.columnsChecked = [];
        }
        this.hitOrderWeightTableColumns();
      },
      handleShowCheckAll () {
        if (this.checkedShow.indeterminate) {
          this.checkedShow.allChecked = false;
        } else {
          this.checkedShow.allChecked = !this.checkedShow.allChecked;
        }
        this.checkedShow.indeterminate = false;

        if (this.checkedShow.allChecked) {
          this.checkedShow.columnsChecked = ['deal', 'type', 'coach'];
        } else {
          this.checkedShow.columnsChecked = [];
        }
        this.hitShowTableColumns();
      },
      handleOrderChangeTableColumns (data) {
        if (data.length === 5) {
          this.checkedOrder.indeterminate = false;
          this.checkedOrder.allChecked = true;
        } else if (data.length > 0) {
          this.checkedOrder.indeterminate = true;
          this.checkedOrder.allChecked = false;
        } else {
          this.checkedOrder.indeterminate = false;
          this.checkedOrder.allChecked = false;
        }
        this.hitOrderTableColumns();
      },
      handleOrderWeightChangeTableColumns (data) {
        if (data.length === 4) {
          this.checkedOrderWeight.indeterminate = false;
          this.checkedOrderWeight.allChecked = true;
        } else if (data.length > 0) {
          this.checkedOrderWeight.indeterminate = true;
          this.checkedOrderWeight.allChecked = false;
        } else {
          this.checkedOrderWeight.indeterminate = false;
          this.checkedOrderWeight.allChecked = false;
        }
        this.hitOrderWeightTableColumns();
      },
      handleShowChangeTableColumns (data) {
        if (data.length === 3) {
          this.checkedShow.indeterminate = false;
          this.checkedShow.allChecked = true;
        } else if (data.length > 0) {
          this.checkedShow.indeterminate = true;
          this.checkedShow.allChecked = false;
        } else {
          this.checkedShow.indeterminate = false;
          this.checkedShow.allChecked = false;
        }
        this.hitShowTableColumns();
      },
      hitOrderTableColumns () {
        let newOutCol = [];
        this.finalOutCol.forEach(col => {
          if (this.checkedOrder.columnsChecked.findIndex(name => (name === col.code || col.code === 'god')) > -1) {
            newOutCol.push(col);
          }
        });
        if (newOutCol.length === 0) {
          // this.outTableShow = false;
          this.outCol = this.finalOutCol;
        } else {
          // this.outTableShow = true;
          this.outCol = newOutCol;
        }
      },
      hitOrderWeightTableColumns () {
        let newSubHeadCol = [];
        this.finalSubHeadCol.forEach(col => {
          if (this.checkedOrderWeight.columnsChecked.findIndex(name => (name === col.code)) > -1) {
            newSubHeadCol.push(col);
          }
        });
        if (newSubHeadCol.length === 0) {
          // this.inTableShow = false;
          this.subHeadCol = this.finalSubHeadCol;
          this.outCol.forEach((item, index) => {
            if (index > 1) {
              item.width = 500;
            }
          });
        } else {
          // this.inTableShow = true;
          this.subHeadCol = newSubHeadCol;
          this.outCol.forEach((item, index) => {
            if (index > 1) {
              item.width = newSubHeadCol.length * 100;
            }
          });
        }
      },
      hitShowTableColumns () {
        let newOutData = [];
        if (this.checkedShow.columnsChecked.length === 0) {
          // newOutData = this.finalOutData.filter(col => (col.code === 'god'));
          newOutData = this.finalOutData;
        } else {
          this.finalOutData.forEach(col => {
            if (this.checkedShow.columnsChecked.findIndex(name => (name === col.code || col.code === 'god')) > -1) {
              newOutData.push(col);
            }
          });
        }
        this.outData = newOutData;
      },
      handleDateRangeChanged (val) {
        // this.timeSlot = val;
        if (this.groupId !== '' || this.coachId !== '') {
          this.getCoachStatisticsDetail().then(() => {
            if (document.querySelector('.merge-table-box').scrollHeight > 1162) {
              this.tableHeight = document.body.scrollHeight - 60 - 80 - 22;
            } else {
              this.tableHeight = '';
            }
          });
        }
      },
      handleGymChanged (val) {
        if (val.indexOf('group') === -1) {
          this.groupId = '';
          this.coachId = val;
        } else {
          this.groupId = val.substring(5);
          this.coachId = '';
        }
          this.getCoachStatisticsDetail;
      },
      getCoachStatisticsDetail(){
          return this.$service.post('/Web/Statistics/coach_statistics_detail', {
              s_date: this.timeSlot[0],
              e_date: this.timeSlot[1],
              // group_id: this.groupId,
              group_id: this.choseGroupId,
              coach_id: this.coachId,
              bus_id: this.selectBusId
          }).then(res => {
              if (res.status === 200) {
                  if (res.data.errorcode == 0) {
                      this.infoList = res.data.data.list
                      const dealMethodList = res.data.data.list.source_list;
                      const courseTypeList = res.data.data.list.card_type_list;
                      const all = res.data.data.list.total;
                      this.dealMethodData = [];
                      this.dealMethodTotalDB = [];
                      this.dealMethodNewCardDB = [];
                      this.dealMethodRenewalCardDB = [];
                      this.dealMethodCancleCardDB = [];
                      this.dealMethodChangeCardDB = [];
                      this.dealMethodSplitCardDB = [];
                      this.dealMethodOtherCardDB = [];
                      // this.dealHelpDealCardDB = [];
                      this.courseTypeData = [];
                      this.courseTypeTotalDB = [];
                      this.courseTypeNewCardDB = [];
                      this.courseTypeRenewalCardDB = [];
                      this.courseTypeCancleCardDB = [];
                      this.courseTypeChangeCardDB = [];
                      this.courseTypeSplitCardDB = [];
                      this.courseTypeOtherCardDB = [];
                      // this.courseHelpDealCardDB = [];
                      this.allTotalDB = [];
                      this.allNewCardDB = [];
                      this.allRenewalCardDB = [];
                      this.allCancleCardDB = [];
                      this.allChangeCardDB = [];
                      this.allSplitCardDB = [];
                      this.allOtherCardDB = [];
                      // this.allHelpDealCardDB = [];
                      dealMethodList.forEach(item => {
                          this.dealMethodData.push({
                              dealMethod: item.name
                          });
                          this.dealMethodTotalDB.push(item.col_total);
                          this.dealMethodNewCardDB.push(item.newopen_card);
                          this.dealMethodRenewalCardDB.push(item.renew_card);
                          this.dealMethodCancleCardDB.push(item.stop_card);
                          this.dealMethodChangeCardDB.push(item.change_card);
                          this.dealMethodSplitCardDB.push(item.split_card);
                          this.dealMethodOtherCardDB.push(item.other);
                          // this.dealHelpDealCardDB.push(item.help_deal);
                      });
                      courseTypeList.forEach(item => {
                          this.courseTypeData.push({
                              courseType: item.name
                          });
                          this.courseTypeTotalDB.push(item.col_total);
                          this.courseTypeNewCardDB.push(item.newopen_card);
                          this.courseTypeRenewalCardDB.push(item.renew_card);
                          this.courseTypeCancleCardDB.push(item.stop_card);
                          this.courseTypeChangeCardDB.push(item.change_card);
                          this.courseTypeSplitCardDB.push(item.split_card);
                          this.courseTypeOtherCardDB.push(item.other);
                          // this.courseHelpDealCardDB.push(item.help_deal);
                      });
                      this.allTotalDB = new Array(all.row_total);
                      this.allNewCardDB = new Array(all.newopen_card);
                      this.allRenewalCardDB = new Array(all.renew_card);
                      this.allCancleCardDB = new Array(all.stop_card);
                      this.allChangeCardDB = new Array(all.change_card);
                      this.allSplitCardDB = new Array(all.split_card);
                      this.allOtherCardDB = new Array(all.other);
                      // this.allHelpDealCardDB = new Array(all.help_deal);
                      const coachList = res.data.data.list.coach_list;
                      this.coachData = [];
                      this.coachTotalDB = [];
                      this.coachNewCardDB = [];
                      this.coachRenewalCardDB = [];
                      this.coachCancleCardDB = [];
                      this.coachChangeCardDB = [];
                      this.coachOtherCardDB = [];
                      this.coachSplitCardDB = [];
                      // this.coachHelpDealCardDB = [];
                      coachList.forEach(item => {
                          this.coachData.push({
                              coachName: item.name
                          });
                          this.coachTotalDB.push({
                            ...item.col_total,
                            shouldLink: true,
                            id: item.id,
                            name: item.name
                          });
                          this.coachNewCardDB.push(item.newopen_card);
                          this.coachRenewalCardDB.push(item.renew_card);
                          this.coachCancleCardDB.push(item.stop_card);
                          this.coachChangeCardDB.push(item.change_card);
                          this.coachOtherCardDB.push(item.other);
                          this.coachSplitCardDB.push(item.split_card);
                          // this.coachHelpDealCardDB.push(item.help_deal);
                      });
                      this.totalStat = res.data.data.list.info;
                  } else {
                      let msg = res.data.errormsg;
                      msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
                      this.$Notice.error({
                          title: msg
                      });
                  }
              } else {
                  this.$Notice.error({
                      title: '网络不稳定，请摇一摇显示器再重试！',
                      desc: `服务器返回代码：${res.status}`
                  });
              }
          });
      },
      getGroupDB () {
        this.$service.post('/Web/Statistics/coach_group_list').then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.groupList = res.data.data.list;
              if (this.coachId) {
                this.selectedGroup = this.coachId;

                // 未设置处理
                if (this.coachId == 0) {
                  this.groupId = this.groupList[0].group_id;
                }

              } else {
                this.groupId = this.groupList[0].group_id;
                this.selectedGroup = 'group' + this.groupId;
              }

              this.getCoachStatisticsDetail().then(() => {
                if (document.querySelector('.merge-table-box').scrollHeight > 1162) {
                  this.tableHeight = document.body.scrollHeight - 60 - 80 - 22;
                } else {
                  this.tableHeight = '';
                }
              });
            } else {
              let msg = res.data.errormsg;
              msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
              this.$Notice.error({
                title: msg
              });
            }
          } else {
            this.$Notice.error({
              title: '网络不稳定，请摇一摇显示器再重试！',
              desc: `服务器返回代码：${res.status}`
            });
          }
        });
      },
      handleStoreChange() {
        this.coachId = ''
        this.getGroupDB()
      }
    },
    watch: {
      choseGroup: function(val, old) {
        if (val) {
          const groupIdArr = val.split("_");
          if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
            this.choseGroupId = groupIdArr[0];
          }
        } else {
          this.choseGroupId = ""
        }
        this.getGroupDB();
      }
    },
    mounted () {
      this.outCol = this.finalOutCol;
      this.outData = this.finalOutData;
      this.subHeadCol = this.finalSubHeadCol;
      this.getGroupDB();
    },
    beforeMount () {
      if (this.$route.params.beginTime && this.$route.params.endTime) {
        let timeArr = [];
        timeArr.push(this.$route.params.beginTime);
        timeArr.push(this.$route.params.endTime);
        this.timeSlot = timeArr;
      } else {
        this.timeSlot = null
      }
      if (this.$route.params.busId) {
        this.selectBusId = this.$route.params.busId
      } else {
        this.selectBusId = this.busId
      }
      if (this.$route.params.coachId) {
        this.coachId = this.$route.params.coachId
      }
    },
    components: {
      DatePickerWithButton,
      Export,
      AdminRegion
    },
  }
</script>
<style lang="less">
  @border: 1px solid #dcdcdc;

  .merge-table-box {
    background-color: #fff;

    .box-head {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      height: 76px;
      .head-lef {
        display: flex;
        flex: 1;
      }
      .head-rig {
        margin-right: 50px;
      }
      .datepicker {
        margin-left: 20px;
      }

      .gym-select {
        width: 200px;
        margin-left: 30px;

        .group {
          font-size: 14px !important;
          font-weight: bold;
        }

        .ivu-select-item-selected,
        .ivu-select-item-selected:hover {
          color: #fff;
          background: rgba(45, 140, 240, 0.9);
        }

        .ivu-select-group-title {
          display: none;
        }
      }
    }

    .total-stat {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      padding: 0 40px;
      height: 120px;
      border-top: @border;
      border-bottom: @border;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      >span {
        position: absolute;
        left: 22px;
        top: 13px;
        color: #666;
        font-size: 14px;
      }
      .stat {
        h3 {
          font-size: 40px;
          color: #52a4ea;
          font-weight: normal; // margin-top: 20px;
          span {
            font-size: 24px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
        }
      }
      >b {
        width: 1px;
        height: 30px;
        background-color: #ccc;
      }
    }

    .checked-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40px; // margin-left: 40px;
      label {
        width: 150px;
      }
    }

    .sub-head {
      display: flex;
      flex-direction: row;
      border-right: 1px solid #dddee1;

      .sub-head-item {
        width: 100px;
        font-weight: bold;
      }
    }

    .total-row {
      background-color: #2db7f5;
      color: #fff;

      td {
        background-color: #2db7f5;
        color: #fff;
      }

      .ivu-table-body {
        background-color: #2db7f5;
        color: #fff;
      }
    }

    .left-head {
      font-weight: bold;
      border: none;
    }
  }

  // .table-2b {
  //   letter-spacing: 10px;

  //   :deep(div.ivu-table-cell) {
  //     letter-spacing: 0;
  //     background-color: red !important;
  //     word-break: keep-all;
  //   }

  //   ::v-deep {
  //     .ivu-table-cell {
  //       letter-spacing: 0;
  //       background-color: red !important;
  //       word-break: keep-all;
  //     }
  //   }

  //   /deep/ .ivu-table-cell {
  //     letter-spacing: 0;
  //     background-color: red !important;
  //     word-break: keep-all;
  //   }
  // }

</style>

<style lang="less">
.table-2b {
  .ivu-table-cell {
    word-break: keep-all;
    text-wrap: nowrap;
    white-space: nowrap;
  }

  .no-scroll .ivu-table-overflowX {
   overflow: hidden;
  }

  .ten-word {
    display: inline-block;
    width: 150px;
    overflow-x: auto;
  }
}
</style>