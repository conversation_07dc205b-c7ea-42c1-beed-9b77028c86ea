<style lang="less" scoped>

</style>

<template>
  <div class="table-wrap">
    <header>
      <Select v-model="selectBusId" style="margin-right:20px;width:200px" @on-change="getList" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <DatePickerWithButton @on-change="dateChanged"/>
    </header>
    <Total v-model="totalInfo"/>
    <Table :columns="columns" style="border-top: 1px solid #dddee1"
           :data="tableData"/>
    <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged"/>
    </footer>
  </div>
</template>

<script>
  import Total from 'components/form/Total.vue';
  import DatePickerWithButton from 'components/picker/datePickerWithButton.vue';
  import Pager from 'mixins/pager';
  import { formatDate } from 'utils';
  import { mapState } from "vuex";

  let weekNow = new Date().getDay();
  weekNow = weekNow === 0 ? 6 : weekNow - 1;

  const TOTAL_ITEMS = {
    global_total: { name: '总分配次数' },
    global_success: { name: '成功出单次数' },
    global_success_radio: { name: '完成率' },
    global_average_success: { name: '平均出单时间', unit: '天' }
  };

  export default {
    name: 'missionStat',
    mixins: [Pager],
    components: {
      Total,
      DatePickerWithButton
    },
    data() {
      return {
        isMembership: false,
        dateRange: [
          formatDate(new Date(Date.now() - weekNow * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
          formatDate(new Date(), 'yyyy-MM-dd')
        ],
        columns: [
          {
            key: 'name',
            title: '会籍'
          },
          {
            key: 'employee_assign_total',
            title: '分配人次'
          },
          {
            key: 'employee_success_total',
            title: '截至当前成功出单次数'
          },
          {
            key: 'success_percent',
            title: '完成率'
          },
          {
            key: 'employee_average_success',
            title: '平均出单时间'
          },
          {
            key: '3days_total',
            title: '3天内出单'
          },
          {
            key: '3days_radio',
            title: '3天内转化率'
          },
          {
            key: '7days_total',
            title: '7天内出单'
          },
          {
            key: '7days_radio',
            title: '7天内转化率'
          },
          {
            key: '15days_total',
            title: '15天内出单'
          },
          {
            key: '15days_radio',
            title: '15天内转化率'
          }
        ],
        tableData: [],
        totalInfo: [],
        storeList: [],
        selectBusId: ''
      };
    },
    watch: {
      $route(val) {
        this.getList();
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    created() {
      this.selectBusId = this.busId
      this.getStoreList()
    },
    beforeRouteEnter(to, from, next) {
      if (to.query.type === 'membership') {
        to.meta.breadText = '会籍分配任务完成情况';
      } else {
        to.meta.breadText = '教练分配任务完成情况';
      }
      next();
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      dateChanged(value) {
        this.dateRange = value;
        this.getList();
      },
      getList() {
        let url;
        if (this.$route.query.type === 'membership') {
          url = '/Web/Statistics/membership_assigning_task';
          this.columns[0].title = '会籍';
        } else {
          url = '/Web/Statistics/coach_assigning_task';
          this.columns[0].title = '教练';
        }
        const postData = {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          page_no: this.page,
          page_size: this.pageSize,
          bus_id: this.selectBusId
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.employee_list_total;
              let arr = [];
              for (let key in TOTAL_ITEMS) {
                arr.push({
                  value: data[key],
                  name: TOTAL_ITEMS[key].name,
                  unit: TOTAL_ITEMS[key].unit
                });
                this.totalInfo = arr;
              }
              this.tableData = data.employee_list.map(item => {
                let success_percent = item.employee_success_total / item.employee_assign_total;
                success_percent = Object.is(NaN, success_percent) ? 0 : (success_percent * 100).toFixed(2);
                return {
                  ...item,
                  ...{
                    success_percent: success_percent + '%'
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
