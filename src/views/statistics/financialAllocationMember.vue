<template>
  <div class="table-wrap">
    <header>
      <!-- <DatePicker type="daterange" :value="dateRange" @on-change="handleDateRangeChanged" :options="options" placeholder="请选择时间区间"></DatePicker> -->
      <Input class="w120"
             placeholder="会员名/电话号码"
             v-model="postData.search" />
      <Select v-model="postData.bus_id"
              placeholder="选择场馆"
              @on-change="handleBusChanged"
              filterable>
        <Option v-for="option in merchantsBusList"
                :value="option.bus_id"
                :key="option.bus_id">{{ option.bus_name }}</Option>
      </Select>
      <Select v-model="postData.card_id"
              placeholder="会员卡"
              clearable
              filterable>
        <Option v-if="cardList.card_list && card.experience_card === '0'"
                v-for="card in cardList.card_list"
                :value="card.card_id"
                :key="card.card_id">{{ card.card_name }}</Option>
        <Option v-if="cardList.private_card_list && card.experience_card === '0'"
                v-for="card in cardList.private_card_list"
                :value="card.card_id"
                :key="card.card_id">{{ card.card_name }}</Option>
      </Select>
      <Button type="success"
              @click="handleSearch">搜索</Button>
    </header>
    <Table width="100%"
           height="600"
           ref="table"
           :columns="columns"
           :data="tableData"
           v-if="tableData"
           stripe></Table>
    <footer v-if="tableData && tableData.length>0">
      <!-- <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export> -->
      <Page :total="total"
            :current.sync="postData.page_no"
            :page-size="postData.page_size"
            show-total
            show-sizer
            placement="top"
            @on-change="handleWhichPage"
            @on-page-size-change="handleHowMuch"></Page>
    </footer>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Export from 'src/components/Export';
import { formatDate } from 'utils';
export default {
  name: 'financialAllocationMember',
  props: {
    merchantsBusList: {
      type: [Array]
    }
  },
  data () {
    return {
      dataInfo: '',
      cardList: [],
      // dateRange:this.getInitDay(),
      // options: {
      //   disabledDate (date) {
      //     return date && date.valueOf() > Date.now() - 60*1000*60*24;
      //   }
      // },
      total: 0,
      postData: {
        // s_date: this.getInitDay()[0],
        // e_date: this.getInitDay()[1],
        search: '',
        bus_id: '',
        page_no: 1,
        page_size: 10,
        card_id: ''
      },
      columns: [
        {
          title: '会员',
          key: 'username'
        },
        {
          title: '会员卡',
          key: 'card_name',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'card_name');
          }
        },
        {
          title: '卡类型',
          key: 'card_type',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'card_type');
          }
        },
        {
          title: '激活时间',
          key: 'active_time',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'active_time');
          }
        },
        {
          title: '到期时间',
          key: 'end_time',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'end_time');
          }
        },
        {
          title: '总天数/次数/金额',
          key: 'all_num',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'all_num');
          }
        },
        {
          title: '消费价值',
          key: 'cost',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'cost');
          }
        },
        {
          title: '剩余天数/次数/金额',
          key: 'last_num',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'last_num');
          }
        },
        {
          title: '剩余价值',
          key: 'last_val',
          render: (h, params) => {
            return this.cardForColumn(params.row.card_user_info, 'last_val');
          }
        }
      ],
      resData: '',
      tableData: ''
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  methods: {
    exportCsv () {
      this.getInfo(true);
      const data = this.tableData.map(item => {
        return Object.assign({}, item, {
          card_name: this.formatKey(item, 'card_name'),
          active_time: this.formatKey(item, 'active_time'),
          end_time: this.formatKey(item, 'end_time'),
          card_type: this.formatKey(item, 'card_type'),
          cost: this.formatKey(item, 'cost'),
          all_num: this.formatKey(item, 'all_num'),
          last_num: this.formatKey(item, 'last_num'),
          last_val: this.formatKey(item, 'last_val')
        });
      });
      this.$refs.export.export({
        filename: `会员收入分摊明细(${this.postData.s_date}~${this.postData.e_date})`,
        columns: this.columns,
        data
      });
    },
    handleBusChanged (val) {
      this.getCardList(val);
    },
    getCardList (busId) {
      this.$service.get('/Web/Member/get_card?member_list=1&belong_bus_id=' + busId || '').then(res => {
        if (res.data.errorcode == 0) {
          this.cardList = res.data.data;
          this.postData.card_id = ''
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getInitDay () {
      let first, last;
      let date = new Date();
      if (date.getDate() === 1) {
        first = new Date(date.getFullYear(), date.getMonth() - 1, 1)
        let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
        last = new Date(new Date().getFullYear(), new Date().getMonth() - 1, day);
      } else {
        first = new Date(new Date().setDate(1))
        last = new Date(new Date().getTime() - 24 * 60 * 60 * 1000)
      }
      return [formatDate(first, 'yyyy-MM-dd'), formatDate(last, 'yyyy-MM-dd')]
    },
    cardForColumn (cards, row) {
      if (cards) {
        return (
          <ul class="row-ul">
            {cards.map(item => {
              return (
                <li>
                  {item[row]}
                </li>
              );
            })}
          </ul>
        );
      } else {
        return '';
      }
    },
    // handleDateRangeChanged (val) {
    //   val = val || ['','']
    //   this.postData.s_date = val[0]
    //   this.postData.e_date = val[1]
    // },
    handleSearch() {
      this.postData.page_no = 1;
      this.getInfo();
    },
    handleWhichPage (val) {
      this.postData.page_no = val;
      this.getInfo();
    },
    handleHowMuch (pageSize) {
      this.postData.page_no = 1;
      this.postData.page_size = pageSize;
      this.getInfo();
    },
    getInfo () {
      this.$service.post('/Web/Statistics/financial_allocation_detail', { ...this.postData }).then(res => {
        if (res.data.errorcode === 0) {
          const resData = res.data.data
          this.tableData = resData.list
          this.total = +resData.count
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    formatKey (item, key) {
      if (item.card_user_info) {
        return item.card_user_info.map(item => {
          return item[key] || 0;
        });
      } else {
        return '';
      }
    }
  },
  created () {
    this.postData.bus_id = this.busId || ''
    this.getCardList(this.busId)
    this.getInfo();
  },
  components: {
    Export
  }
}
</script>
<style lang="less" scoped>
.row-ul {
  li {
    height: 35px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }

  .red {
    color: #d9534f;
  }

  .green {
    color: #5cb85c;
  }
}
.footer-errors {
  margin-left: 15px;
  color: red;
}
.cen-text {
  padding: 10px 50px;
  line-height: 35px;
  font-size: 14px;
  border-bottom: 1px solid #e9eaec;
}
</style>

