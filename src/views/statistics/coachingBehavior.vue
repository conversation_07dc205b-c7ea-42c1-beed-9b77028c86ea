<template>
  <Card style="padding:14px;">

    <Row style="height:50px;">
      <Col span="24">
        <Select v-model="selectBusId" @on-change="getAllDB" style="width:200px;margin-right:20px" filterable>
          <Option
            v-for="item in storeList"
            :value="item.id"
            :key="item.id"
            >{{ item.name }}</Option>
        </Select>
        <DatePicker type="daterange" v-model="datedate" @on-change="handleDatedateChange" :options="datedateOptions" placeholder="请选择时间区间" style="width: 200px" :clearable='false'></DatePicker>
      </Col>
    </Row>

    <Row class="checkbox-controller-box" style="margin-top:20px;">
      <Col span="2">
      <Checkbox :indeterminate="ageFlag.isPart" v-model="ageFlag.isAll" @on-change="handleAgeCheckAll">年龄</Checkbox>
      </Col>
      <Col span="22">
      <Checkbox-group v-model="ageFlag.group" @on-change="handleAgeCheckGroup">
        <Checkbox label="14岁以下会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="15-20岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="21-30岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="31-40岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="41-50岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="51-60岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="61-70岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="71岁以上会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="未设置年龄会员" class="checkbox-controller-age"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
    <Row class="checkbox-controller-box">
      <Col span="2">
      <Checkbox :indeterminate="genderFlag.isPart" v-model="genderFlag.isAll" @on-change="handleGenderCheckAll">性别</Checkbox>
      </Col>
      <Col span="22">
      <Checkbox-group v-model="genderFlag.group" @on-change="handleGenderCheckGroup">
        <Checkbox label="男" class="checkbox-controller-gender"></Checkbox>
        <Checkbox label="女" class="checkbox-controller-gender"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>

    <!-- <Row style="border-top: 1px solid #dcdcdc;">
      <Col span="22" offset="1">
      <div class="tip-box">
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.arrive_total}}</span>
            <span class="k-unit">人</span>
          </div>
          <div class="tip-name">到场训练会员</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.arrive_ratio}}</span>
          </div>
          <div class="tip-name">会员到场率</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.train_total}}</span>
            <span class="k-unit">人次</span>
          </div>
          <div class="tip-name">总训练人次</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">
            <span class="k-val">{{membershipTip.aver_train_ratio}}</span>
            <span class="k-unit">天/人次</span>
          </div>
          <div class="tip-name">平均训练频率</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">
            <span class="k-val">{{membershipTip.aver_trainTimetotal}}</span>
            <span class="k-unit">分钟</span>
          </div>
          <div class="tip-name">平均训练时长
            <Tooltip placement="top">
              <Icon type="ios-information" style="color: orange;"></Icon>
              <div slot="content">
                <p>训练时长是根据会员领手环时和退手环</p>
                <p>时间或离场时间来计算，没领取手环或</p>
                <p>没归还手环或者没离场都无法统计出训</p>
                <p>练时间，还有归还时间超过3小时或低</p>
                <p>于20分钟也不计入统计</p>
              </div>
            </Tooltip>
          </div>
        </div>
      </div>
      </Col>
    </Row> -->

    <Row>
      <Col span="24" style="border-top:1px solid #dcdcdc;border-bottom:1px solid #dcdcdc;">
      <Total v-model="totalItem" />
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="24">
      <div class="age-ratio"></div>
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="12">
      <div class="customer-original"></div>
      </Col>
      <Col span="12">
      <div class="coach-method"></div>
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="24">
      <div class="increased"></div>
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="24">
      <div class="age-ratio-hell"></div>
      </Col>
    </Row>

  </Card>
</template>
<script>
  import echarts from "echarts";
  import Total from 'components/form/Total';
  import { mapState } from "vuex";

  const AGE_GROUP = [
    "14岁以下会员",
    "15-20岁会员",
    "21-30岁会员",
    "31-40岁会员",
    "41-50岁会员",
    "51-60岁会员",
    "61-70岁会员",
    "71岁以上会员",
    "未设置年龄会员"
  ];
  const GENDER_GROUP = ["男", "女"];
  const TRAINING_TIMES = [
    "0-3天/次",
    "4-7天/次",
    "8-10天/次",
    "11-15天/次",
    "16-20天/次",
    "21-30天/次",
    "31~60天/次",
    "61天以上/次"
  ];
  const TRAINING_DURATION = [
    "30分钟以下",
    "31-45分钟",
    "45-60分钟",
    "61-75分钟",
    "76-90分钟",
    "91-105分钟",
    "106-120分钟",
    "120分钟以上"
  ];
  const ALARM = ["09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "01", "02", "03", "04", "05", "06", "07", "08"];

  export default {
    data() {
      return {
        totalItem: [],
        datedate: [],
        datedateOptions: {
          shortcuts: [
            {
              text: "一周",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                return [start, end];
              }
            },
            {
              text: "一个月",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                return [start, end];
              }
            },
            {
              text: "三个月",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                return [start, end];
              }
            }
          ]
        },
        $ageChart: null,
        $ageChartHell: null,
        $customerChart: null,
        $coachChart: null,
        $increasedChart: null,
        ageFlag: {
          isAll: true,
          isPart: false,
          group: AGE_GROUP
        },
        genderFlag: {
          isAll: true,
          isPart: false,
          group: GENDER_GROUP
        },
        ageOption: {
          title: {
            text: '训练频率分布',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          legend: {
              data: ['男', '女']
          },
          grid: {
            left: "0",
            right: "0",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: TRAINING_TIMES,
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                }
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                },
                formatter: "{value}"
              },
              minInterval: 1
            }
          ],
          series: [
            {
              name: "男",
              type: "bar",
              barGap: 0,
              data: [0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#81cfef"]
            },
            {
              name: "女",
              type: "bar",
              data: [0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#f88397"]
            }
          ]
        },
        ageOptionHell: {
          title: {
            text: '训练时长分布',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          legend: {
              data: ['男', '女']
          },
          grid: {
            left: "0",
            right: "0",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: TRAINING_DURATION,
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                }
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                },
                formatter: "{value}"
              },
              minInterval: 1
            }
          ],
          series: [
            {
              name: "男",
              type: "bar",
              barGap: 0,
              data: [0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#81cfef"]
            },
            {
              name: "女",
              type: "bar",
              data: [0, 0, 0, 0, 0, 0, 0, 0],
              color: ["#f88397"]
            }
          ]
        },
        // membershipTip: {
        //   arrive_total: 0,
        //   arrive_ratio: "0%",
        //   train_total: 0,
        //   aver_train_ratio: 0,
        //   aver_trainTimetotal: 0
        // },
        customerOption: {
          title: {
            text: "训练人数占比",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: ["到场训练人员", "未到场会员"],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>人数：{c} <br/> 占比：{d}%"
          },
          series: [
            {
              name: "训练人数占比",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: "到场训练人员" },
                { value: 0, name: "未到场会员" }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        coachOption: {
          title: {
            text: "训练次数占比",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: [],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name + "次训练";
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{a}：{b}次<br/>人数：{c} <br/> 占比：{d}%"
          },
          series: [
            {
              name: "训练次数",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        increasedOption: {
          dataZoom: [{type: 'slider', show: true, start: 0, end: 70}],
          title: {
            text: '流量趋势',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          grid: {
            left: "0",
            right: "0",
            bottom: "3%",
            containLabel: true
          },
          xAxis: {
            type: "category",
            data: ALARM,
            nameLocation: 'middle',
            axisLabel: {
              formatter: function (value, index) {
                return   value + '点';
              }
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: "{value} 人"
            },
            minInterval: 1
          },
          series: [
            {
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
              type: "line",
              color: ['#ADD8E6']
            }
          ]
        },
        storeList: [],
        selectBusId: ''
      };
    },
    components: {Total},
    computed: {
      ...mapState(['busId'])
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      handleAgeCheckAll() {
        this.ageFlag.isPart = false;
        if (this.ageFlag.isAll) {
          this.ageFlag.group = AGE_GROUP;
        } else {
          this.ageFlag.group = [];
        }
        this.getAllDB();
      },
      handleGenderCheckAll() {
        this.genderFlag.isPart = false;
        if (this.genderFlag.isAll) {
          this.genderFlag.group = GENDER_GROUP;
        } else {
          this.genderFlag.group = [];
        }
        this.getAllDB();
      },
      handleAgeCheckGroup(data) {
        if (data.length === 9) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = true;
        } else if (data.length > 0) {
          this.ageFlag.isPart = true;
          this.ageFlag.isAll = false;
        } else if (data.length === 0) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = false;
        }
        this.getAllDB();
      },
      handleGenderCheckGroup(data) {
        if (data.length === 2) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = true;
        } else if (data.length > 0) {
          this.genderFlag.isPart = true;
          this.genderFlag.isAll = false;
        } else if (data.length === 0) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = false;
        }
        this.getAllDB();
      },
      handleDatedateChange(val) {
        this.datedate = val;
        this.getAllDB();
      },
      getAgeVal() {
        let arr = [];
        this.ageFlag.group.forEach(item => {
          const idx = AGE_GROUP.findIndex(val => item === val);
          if (idx !== -1) {
            arr.push(idx + 1);
          }
        });
        if (arr.length === 0) {
          arr = [1,2,3,4,5,6,7,8,9];
        }
        return arr.sort().join(",");
      },
      getSexVal() {
        if (
          this.genderFlag.group.length === 2 ||
          this.genderFlag.group.length === 0
        ) {
          return "1,2";
        } else if (this.genderFlag.group[0] === "男") {
          return 1;
        } else {
          return 2;
        }
      },
      getDateString(date) {
        if (typeof date === "object") {
          const year = date.getFullYear();
          let month = date.getMonth() + 1;
          month = month < 10 ? "0" + month : month;
          let day = date.getDate();
          day = day < 10 ? "0" + day : day;
          return `${year}-${month}-${day}`;
        } else {
          return date;
        }
      },
      getAllDB() {
        return this.$service
          .post("/Web/Statistics/customer_behave_analyze", {
            s_date: this.getDateString(this.datedate[0]),
            e_date: this.getDateString(this.datedate[1]),
            age_str: this.getAgeVal(),
            sex_str: this.getSexVal(),
            bus_id: this.selectBusId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              // this is membership base information.
              this.setMembershipTip(res.data.data);

              // it's training times map.
              this.setTrainingTimes(res.data.data.Trainfrequency);
              this.$ageChart.setOption(this.ageOption);

              // there are traning ratio.
              this.setComeStore(res.data.data);
              this.setTrainingRatio(res.data.data.trainNumratio);
              this.$customerChart.setOption(this.customerOption);
              this.$coachChart.setOption(this.coachOption);

              // here is flow data.
              this.setFlowData(res.data.data.Timepoint);
              this.$increasedChart.setOption(this.increasedOption);

              // how long training map.
              this.setTrainingDuration(res.data.data.Trimtime);
              this.$ageChartHell.setOption(this.ageOptionHell);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      setMembershipTip(rockstar) {
        // this.membershipTip.arrive_total = rockstar.arrive_total;
        // this.membershipTip.arrive_ratio = rockstar.arrive_ratio;
        // this.membershipTip.train_total = rockstar.train_total;
        // this.membershipTip.aver_train_ratio = rockstar.aver_train_ratio;
        // this.membershipTip.aver_trainTimetotal = rockstar.aver_trainTimetotal;

        const arr = [
          {value: rockstar.arrive_total, name: '到场训练会员', unit: '人'},
          {value: rockstar.arrive_ratio, name: '会员到场率'},
          {value: rockstar.train_total, name: '总训练人次', unit: '人次'},
          {value: rockstar.aver_train_ratio, name: '平均训练频率', unit: '天/人次'},
          {value: rockstar.aver_trainTimetotal, name: '平均训练时长', unit: '分钟', tips: '训练时长是根据会员领手环时和退手环时间或离场时间来计算，没领取手环或没归还手环或者没离场都无法统计出训练时间，还有归还时间超过3小时或低于20分钟也不计入统计'}
        ];

        this.totalItem = arr;
      },
      setTrainingTimes(meant) {
        let maleArr = [0, 0, 0, 0, 0, 0, 0, 0];
        let femaleArr = [0, 0, 0, 0, 0, 0, 0, 0];

        if (Array.isArray(meant.male)) {
          meant.male.forEach(item => {
            maleArr[parseInt(item.Key) - 1] = item.Peoplenum;
          });
        }

        if (Array.isArray(meant.female)) {
          meant.female.forEach(item => {
            femaleArr[parseInt(item.Key) - 1] = item.Peoplenum;
          });
        }

        this.ageOption.series[0].data = maleArr;
        this.ageOption.series[1].data = femaleArr;
      },
      setComeStore(mars) {
        this.customerOption.series[0].data = [
          { value: mars.arrive_total, name: "到场训练人员" },
          { value: mars.noArrivenum, name: "未到场会员" }
        ];
      },
      setTrainingRatio(barking) {
        barking.sort((a, b)=>(a.trainNum - b.trainNum));

        let coachData = [];
        let coachLegend = [];
        barking.forEach(item => {
          coachData.push({
            name: item.trainNum,
            value: item.trainPeople
          });
          coachLegend.push(item.trainNum);
        });

        if (coachData.length === 0) {
          coachData.push({name: 0, value: 0});
          coachLegend.push(0);
        }

        this.coachOption.series[0].data = coachData;
        this.coachOption.legend.data = coachLegend;
      },
      setFlowData(dance) {
        this.increasedOption.series[0].data = new Array(24).fill(0)
        if (Array.isArray(dance)) {
          dance.forEach(item => {
            const idx = ALARM.findIndex(it=>(it==item.Hour));
            if (typeof idx === 'number') {
              this.increasedOption.series[0].data[idx] = parseInt(item.Peoplenum);
            }
          });
        }
      },
      setTrainingDuration(river) {
        let maleArr = [0, 0, 0, 0, 0, 0, 0, 0];
        let femaleArr = [0, 0, 0, 0, 0, 0, 0, 0];

        if (Array.isArray(river.male)) {
          river.male.forEach(item => {
            maleArr[parseInt(item.Key) - 1] = item.Peoplenum;
          });
        }

        if (Array.isArray(river.female)) {
          river.female.forEach(item => {
            femaleArr[parseInt(item.Key) - 1] = item.Peoplenum;
          });
        }

        this.ageOptionHell.series[0].data = maleArr;
        this.ageOptionHell.series[1].data = femaleArr;
      }
    },
    created() {
      this.selectBusId = this.busId
      this.getStoreList()
    },
    mounted() {
      this.$ageChart = echarts.init(document.querySelector(".age-ratio"));
      this.$ageChartHell = echarts.init(
        document.querySelector(".age-ratio-hell")
      );
      this.$customerChart = echarts.init(
        document.querySelector(".customer-original")
      );
      this.$coachChart = echarts.init(document.querySelector(".coach-method"));
      this.$increasedChart = echarts.init(document.querySelector(".increased"));

      const today = new Date();
      const offset = today.getDay();
      this.datedate = [
        // new Date(
        //   today.getTime() - ((!!offset ? offset : 7) - 1) * 24 * 60 * 60 * 1000
        // ),
        new Date(
          today.getTime() - 30 * 24 * 60 * 60 * 1000
        ),
        today
      ];
      this.getAllDB();
    }
  };
</script>
<style lang="less">
  @media screen and (min-width: 1900px) {
    .age-ratio,
    .age-ratio-hell,
    .increased {
      height: 350px;
    }

    .customer-original,
    .coach-method {
      height: 400px;
    }
  }

  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .age-ratio,
    .age-ratio-hell,
    .increased {
      height: 320px;
    }

    .customer-original,
    .coach-method {
      height: 366px;
    }
  }

  @media screen and (max-width: 1300px) {
    .age-ratio,
    .age-ratio-hell,
    .increased {
      height: 300px;
    }

    .customer-original,
    .coach-method {
      height: 343px;
    }
  }

  .checkbox-controller-box {
    height: 50px;

    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }
  }

  .age-ratio,
  .age-ratio-hell,
  .customer-original,
  .coach-method,
  .increased {
    width: 100%;
  }

  .tip-box {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    // border: 1px dotted #dcdcdc;

    .tip {
      height: 165px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .tip-val {
        .k-val {
          font-size: 36px;
          color: #52a4ea;
        }

        .k-unit {
          font-size: 14px;
          color: #666666;
        }
      }

      .tip-name {
        font-size: 18px;
        color: #666666;
      }
    }

    .tip-line {
      height: 30px;
      border-left: 1px solid #dcdcdc;
    }
  }
</style>
