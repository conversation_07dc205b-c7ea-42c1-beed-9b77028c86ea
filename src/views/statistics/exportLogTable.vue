<template>
  <div class="box">
    <header class="header">
      <Alert type="warning" show-icon>数据量越大导出时间越长，导出过程中该页面可关闭，导出完成后将有系统消息通知您下载</Alert>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData"></Table>
    </main>
    <footer class="footer">
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from '@/utils/index.js';

export default {
  components: {
    Pager
  },
  data() {
    return {
      // header
      searchPost: {
        page_no: 1,
        page_size: 10
      },
      // main
      tableColumn: [
        {
          key: 'create_time',
          title: '创建时间',
          width: 300,
          render(h, params) {
            const timestamp = parseInt(params.row.create_time)*1000
            const dateString = formatDate(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')
            return (
              <span>{dateString}</span>
            )
          }
        },
        {
          key: 'name',
          title: '导出文件名',
          render(h, params) {
            return (
              <div style="text-align:left"><Icon type="md-filing" size="30" /> {params.row.name}</div>
            )
          }
        },
        {
          title: '操作',
          width: 300,
          render(h, params) {
            // 状态，1计算中，2计算完成，3计算数据已过期
            if (params.row.status == 1) {
              return (
                <div style="text-align:right;padding-right:100px;cursor:not-allowed">导出中请等待... <Icon type="md-download" color="gray" size="30" /></div>
              )
            } else if (params.row.status == 2) {
              return (
                <div style="text-align:right;padding-right:100px;cursor:pointer" onClick={()=>{
                  window.open(params.row.address)
                }}>已完成，请下载 <Icon type="md-download" color="blue" size="30" /></div>
              )
            } else if (params.row.status == 3) {
              return (
                <div style="text-align:right;padding-right:100px;cursor:not-allowed;color:gainsboro">已过期，请重新导出 <Icon type="md-download" color="gray" size="30" /></div>
              )
            }
          }
        }
      ],
      tableData: [],
      // footer
      count: 100
    }
  },
  methods: {
    getTableData() {
      return this.$service.post('/Web/CostAllocation/excel_export_log', this.searchPost).then(res => {
        if (res.data.errorcode === 0) {
          this.tableData = res.data.data.list
          this.count = res.data.data.count
        }
      })
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
  height: 80px;
}

.box {
  background-color: white;

  .footer {
    .white-panel;
    justify-content: flex-end;
  }
}
</style>
