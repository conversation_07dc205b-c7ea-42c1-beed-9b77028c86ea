<template>
      <div class="box tab-table-wrap customized-tabs">
        <Tabs value="tabNameOne" @on-click="handleTabClick" v-model="tabName">
            <TabPane label="团课评价统计" name="tabNameOne">
                <div class="tab-head">
                    <Select v-model="selectBusId1" style="margin-right:20px;width:200px" @on-change="handleStoreChange(1)" filterable>
                      <Option
                        v-for="item in storeList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option>
                    </Select>
                    <Select v-model="selectedClass1" placeholder="全部团课" style="margin-right:20px;width:200px" @on-change="handleStoreChange(2)" filterable clearable>
                      <Option
                        v-for="item in classList1"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.class_name }}</Option>
                    </Select>
                    <DatePickerWithButton select="本月" :options="options1" @on-change="dateChange1"></DatePickerWithButton>
                    <!-- <AdminRegion v-model="choseGroup1" url="/Web/CoachGroup/getCoachGroupList" :busId="selectBusId1" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" /> -->
                    <!-- <Select class="group-select" style="margin-left: 20px; width: 240px" v-model="selectedGroup"
                          @on-change="handleGymChanged" filterable v-if="groupList">
                    <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
                    <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
                    <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

                      <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
                        class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
                      <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

                      <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
                        <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
                          class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
                        <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
                      </template>

                    </template>
                  </Select> -->
                  <div style='margin-left:auto;'>
                   <Tooltip placement="bottom-end" >
                    好评/差评计算规则
                     <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                     <div slot='content' >
                        <p>当设置打分星级为1～10星时</p>
                        <p>大于7星为好评,小于4星为差评</p>
                        <p>当设置打分星级为1～5星时</p>
                        <p>大于3星为好评,小于3星为差评</p>
                        <p>当设置打分星级为1～3星时</p>
                        <p>大于2星为好评,小于2星为差评</p>
                     </div>
                    </Tooltip>
                    </div>
                </div>
                <div class="total-stat" v-if="commentInformation.length">
                    <div class="stat" v-for="(tip, index) in TAB_TEXT" :key="index">
                        <h3>{{ commentInformation[index] }}</h3>
                        <p>{{tip.name}}
                            <Tooltip v-if="tip.tip" :content="tip.tip" placement="bottom-end">
                            <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                            </Tooltip>
                         </p>
                    </div>
                </div>
                <Table :columns="columns1" :data="data1"></Table>
                <footer>
                    <!-- <Button @click="exportCsv1">导出Excel</Button>
                    <Export ref="export"></Export> -->
                    <Page :total="total1" :history="false" @on-change="handlePageChange1" @on-page-size-change="pageSizeChanged1" show-total show-sizer></Page>
                </footer>
            </TabPane>
            <TabPane label="会员评价明细" name="tabNameTwo">
                <div class="tab-head">
                  <Select v-model="selectBusId2" style="margin-right:20px;width:200px" @on-change="handleStoreChange(3)" filterable>
                    <Option
                      v-for="item in storeList"
                      :value="item.id"
                      :key="item.id"
                      >{{ item.name }}</Option>
                  </Select>
                  <Select v-model="selectedClass2" placeholder="全部团课" style="margin-right:20px;width:200px" @on-change="handleStoreChange(4)" filterable clearable>
                      <Option
                        v-for="item in classList2"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.class_name }}</Option>
                    </Select>
                    <DatePickerWithButton select="今天" :options="options2" @on-change="dateChange2"></DatePickerWithButton>
                    <!-- <AdminRegion v-model="choseGroup2" url="/Web/CoachGroup/getCoachGroupList" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" /> -->
                    <!-- <Select class="group-select" style="margin-left: 20px; width: 240px" v-model="selectedGroup" @on-change="handleGymChanged" filterable v-if="groupList">
                        <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
                        <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
                        <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

                            <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
                                class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
                            <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

                            <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
                                <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
                                class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
                                <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
                            </template>

                        </template>
                    </Select> -->
                  <Select v-model="replyValue" style="width:200px;margin-left:20px;" @on-change="handleStoreChange(5)" clearable>
                    <Option :value="0">待回复</Option>
                    <Option :value="1">已回复</Option>
                  </Select>
                </div>
                <Table :columns="columns2" :data="data2"></Table>
                <footer>
                    <!-- <Button @click="exportCsv2">导出Excel</Button>
                    <Export ref="export"></Export> -->
                    <Page :total="total2" :history="false" @on-change="handlePageChange2" @on-page-size-change="pageSizeChanged2" show-total show-sizer></Page>
                </footer>
            </TabPane>
        </Tabs>
        <Drawer width='30' :title='replayItem.userName' :closable="false" v-model="drawerflag">
            <div>
                <p>{{replayItem.adviceTime}}</p>
                <div  v-for="typeItem in replayItem.types" :value="typeItem.value" :key="typeItem.id">{{typeItem.value}}
                    <Rate v-model="typeItem.star" :count="replayItem.maxScore" show-text disabled/>
                </div>
                <div class="mgt-20 mgb-20">意见反馈:{{replayItem.advice}}</div>
                <div class="mgb-20">
                    <Tag v-for="tagItem in replayItem.labels" :value="tagItem.value" :key="tagItem.id" color="primary" disabled>{{tagItem.value}}</Tag>
                </div>
                <p v-if="replayItem.replyTime">{{replayItem.replyTime}}</p>
                <div class="mgb-20" v-if="replayItem.replyTime">回复内容:{{replayItem.reply}}</div>
                <p v-if="replayItem.addAdviceTime">{{replayItem.addAdviceTime}}</p>
                <div class="mgb-20" v-if="replayItem.addAdviceTime">追评内容:{{replayItem.addAdvice}}</div>
                <p v-if="replayItem.addReplyTime">{{replayItem.addReplyTime}}</p>
                <div class="mgb-20" v-if="replayItem.addReplyTime">追评回复内容:{{replayItem.addReply}}</div>
                <Input class="mgb-20" v-if="!(replayItem.isReply)" v-model="replayContent" type="textarea" placeholder="请输入回复内容"/>
                <Button class="mgb-20" v-if="!(replayItem.isReply)" type="primary" @click="handleSumbitReplay">提交</Button>
            </div>
        </Drawer>
    </div>
</template>
<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'

import DatePickerWithButton from 'components/picker/datePickerWithButton'
import AdminRegion from 'components/form/adminRegion.vue'
import Export from 'src/components/Export'
import { getNewHost } from 'utils/config'
import { formatDate, dateDiff } from 'utils';

const TAB_TEXT = [
  { name: '开课次数' },
  { name: '评价次数' },
  { name: '上课人数' },
  { name: '评价率', tip: '评价次数【除以】上课人数【乘以】100%' },
  {
    name: '好评率',
    tip: '针对课后综合评价，满足好评的次数和【除以】评价次数【乘以】100%'
  },
  {
    name: '差评率',
    tip: '针对课后综合评价，满足差评的次数和【除以】评价次数【乘以】100%'
  },
  { name: '追评次数' },
  { name: '追评率', tip: '追评次数【除以】评价次数【乘以】100%' }
]

export default {
  name: 'commentNewClassTable',
  components: { DatePickerWithButton, AdminRegion, Export },
  // computed: {
  //   ...mapGetters(['coachGroupList'])
  // },
  computed: {
    ...mapState(['busId'])
  },
  data() {
    const formatDate = (source, format) => {
      const o = {
        'M+': source.getMonth() + 1, // 月份
        'd+': source.getDate(), // 日
        'H+': source.getHours(), // 小时
        'm+': source.getMinutes(), // 分
        's+': source.getSeconds(), // 秒
        'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
        'f+': source.getMilliseconds() // 毫秒
      }
      if (/(y+)/.test(format)) {
        format = format.replace(
          RegExp.$1,
          (source.getFullYear() + '').substr(4 - RegExp.$1.length)
        )
      }
      for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
          format = format.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ('00' + o[k]).substr(('' + o[k]).length)
          )
        }
      }
      return format
    }
    return {
      formatDate,
      tabName: '',
      dateRange1: '',
      dateRange2: '',
      options1: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      options2: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      choseClass1: '',
      choseClassId1: '',
      choseClass2: '',
      choseClassId2: '',
      commentInformation: [0, 0, 0, 0, 0, 0, 0, 0],
      // userUrl: `${getNewHost()}/#/member/detail/`,
      userUrl: '/member/detail/',
      columns1: [
        {
          title: '课程名称',
          key: 'class_name',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  let classId = this.selectedClass1 || param.row.class_id
                  this.$router.push({
                    path: `/stat/menus/commentNewClassDetailTable`,
                    query: {
                      selectBusId1: this.selectBusId1,
                      selectedClass1: classId,
                      dateRange1: this.dateRange1
                    }
                  })
                }}
              >
                {param.row.class_name}
              </a>
            )
          }
        },
        {
          title: '开课次数',
          key: 'open_class_nums'
        },
        {
          title: '上课人数',
          key: 'sign_number_nums'
        },
        {
          title: '评价次数/率',
          key: 'comment_nums',
          render: (h, param) => {
            return (
              <span>
                {param.row.comment_nums}/{param.row.evaluation_rate + '%'}
              </span>
            )
          }
        },
        {
          title: '好评次数/率',
          key: 'good_nums',
          render: (h, param) => {
            return (
              <span>
                {param.row.good_nums}/{param.row.good_rate + '%'}
              </span>
            )
          }
        },
        {
          title: '差评次数/率',
          key: 'bad_nums',
          render: (h, param) => {
            return (
              <span>
                {param.row.bad_nums}/{param.row.bad_rate + '%'}
              </span>
            )
          }
        },
        {
          title: '追评次数/率',
          key: 'add_comment_nums',
          render: (h, param) => {
            return (
              <span>
                {param.row.add_comment_nums}/{param.row.add_comment_rate + '%'}
              </span>
            )
          }
        }
      ],
      columns2: [
        {
          title: '上课时间',
          key: 'date_start_time_copy',
          width: 200
        },
        {
          title: '会员姓名',
          key: 'username',
          width: 120,
          render: (h, param) => {
            let url = this.userUrl + param.row.user_id
            return (
              // <a target="_blank" href={url}>
              //   {param.row.username}
              // </a>
              <router-link target="_blank" to={ url }>{ param.row.username }</router-link>
            )
          }
        },
        {
          title: '课程名称',
          key: 'class_name',
          width: 120
        },
        {
          title: '上课教练',
          key: 'coach_name',
          width: 120
        },
        {
          title: '课后综合评价',
          key: 'base_max_score',
          width: 300,
          render: (h, param) => {
            const level = param.row.set_type_json[0].star
            return (
              <Rate v-model={level} count={param.row.base_max_score} disabled />
            )
          }
        },
        {
          title: '评价时间',
          key: 'create_time_copy',
          width: 200
        },
        {
          title: '追评',
          key: 'add_advice_time_copy',
          width: 200,
          render: (h, param) => {
            return <span>{param.row.add_advice_time_copy || '未追评'}</span>
          }
        },
        {
          title: '状态',
          key: 'is_reply',
          width: 100,
          render: (h, param) => {
            return <span>{param.row.is_reply ? '已回复' : '待回复'}</span>
          }
        },
        {
          title: '操作',
          key: 'is_reply',
          width: 80,
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  this.drawerflag = true
                  this.replayItem = {
                    markId: param.row.mark_id,
                    userName: param.row.username,
                    types: param.row.set_type_json, //评分类型
                    labels: param.row.tag_json, //评分标签
                    maxScore: param.row.base_max_score, //最大评分星级
                    advice: param.row.advice, //反馈
                    adviceTime: param.row.create_time_copy, //反馈时间
                    reply: param.row.reply, //回复
                    replyTime: param.row.reply_time_copy, //回复时间
                    addAdvice: param.row.add_advice, //追评
                    addAdviceTime: param.row.add_advice_time_copy, //追评时间
                    addReply: param.row.add_reply, //追评回复
                    addReplyTime: param.row.add_reply_time_copy, //追评回复时间
                    isReply: param.row.is_reply
                  }
                }}
              >
                {param.row.is_reply ? '查看' : '回复'}
              </a>
            )
          }
        }
      ],
      data1: [],
      data2: [],
      total1: 0,
      total2: 0,
      pageSize1: 10,
      pageSize2: 10,
      pageNo1: 1,
      pageNo2: 1,

      selectedClass1: '',
      classList1: null,
      selectedClass2: '',
      classList2: null,
      groupId: '',
      coachId: '',
      storeList: [],
      selectBusId1: '',
      selectBusId2: '',
      replyValue: '',
      TAB_TEXT,
      drawerflag: false,
      replayItem: {},
      replayContent: ''
    }
  },
  watch: {
    choseGroup1: function(val, old) {
      if (val) {
        const groupIdArr = val.split('_')
        if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
          this.pageNo1 = 1
          this.choseGroup1Id = groupIdArr[0]
        }
      } else {
        this.choseGroup1Id = ''
      }
      this.getOverall(1)
      this.getTab1List()
    }
    //   choseGroup2: function(val, old) {
    //     console.log(val);
    //     const groupIdArr = val.split("_");
    //     if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
    //       this.choseGroup2CoachId = groupIdArr[0];
    //       this.getOverall();
    //       this.getTab2List();
    //     }
    //   }
  },
  // async mounted() {
  //     if (!this.coachGroupList || !this.coachGroupList.length) {
  //         this.groupList = await this.getGroupDB();
  //     } else {
  //         this.groupList = this.coachGroupList;
  //     }
  // },
  created() {
    this.selectBusId1 = this.busId
    this.selectBusId2 = this.busId
    //初始化起始时间 getClassList使用
    //本月
    // let dayNow = new Date().getDate();
    // this.calBeginDate(dayNow - 1);
    //今天
    // this.calBeginDate(0, 1)

    this.getStoreList()
  },
  methods: {
    // ...mapActions(['getGroupDB']),
    timeToDate(time) {
      return formatDate(new Date(time), 'yyyy-MM-dd')
    },
    calBeginDate(selectVal, tab) {
      let beginDate = this.timeToDate(
        Date.now() - selectVal * 24 * 60 * 60 * 1000
      )
      let endDate = this.timeToDate(Date.now())
      if (tab === 1) {
        this.dateRange2 = [beginDate, endDate]
      } else {
        this.dateRange1 = [beginDate, endDate]
      }
    },
    getClassList(tab) {
      return this.$service
        .post('/Web/open_class/getListsByBusId', {
          start_time: tab === 1 ? this.dateRange2[0] : this.dateRange1[0],
          end_time: tab === 1 ? this.dateRange2[1] : this.dateRange1[1],
          bus_id: tab === 1 ? this.selectBusId2 : this.selectBusId1
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            if (tab === 1) {
              this.classList2 = res.data.data
            } else {
              this.classList1 = res.data.data
            }
          }
        })
    },
    getStoreList() {
      return this.$service.get('/Web/Business/get_bus_list').then(res => {
        if (res.data.errorcode === 0) {
          this.storeList = res.data.data.bus_list
        }
      })
    },
    handleStoreChange(sequence) {
      if (sequence === 1) {
        this.getClassList().then(() => {
          this.pageNo1 = 1
          this.selectedClass1 = ''
          this.getTab1List()
        })
      } else if (sequence === 2) {
        this.pageNo1 = 1
        this.getTab1List()
      } else if (sequence === 3) {
        this.getClassList(1).then(() => {
          this.pageNo2 = 1
          this.selectedClass2 = ''
          this.getTab2List()
        })
      } else if (sequence === 4) {
        this.pageNo2 = 1
        this.getTab2List()
      } else {
        this.pageNo2 = 1
        this.getTab2List()
      }
    },
    dateChange1(dateRange) {
          this.pageNo1 = 1
      this.dateRange1 = dateRange
        this.getClassList().then(() => {
          this.getTab1List()
        })
    },
    dateChange2(dateRange) {
      this.pageNo2 = 1
      this.dateRange2 = dateRange
       this.getClassList(1).then(() => {
          this.getTab2List()
        })
    },
    handlePageChange1(pageNo) {
      this.pageNo1 = pageNo
      this.getTab1List()
    },
    handlePageChange2(pageNo) {
      this.pageNo2 = pageNo
      this.getTab2List()
    },
    handleTabClick(tabName) {
      if (tabName == 'tabNameOne') {
        this.getTab1List()
      } else if (tabName == 'tabNameTwo') {
        this.getTab2List()
      }
    },
    handleGymChanged(val) {
      if (!val) {
        return false
      }
      if (val.indexOf('group') === -1) {
        this.groupId = ''
        this.coachId = val
      } else {
        this.groupId = val.substring(5)
        this.coachId = ''
      }
      this.pageNo2 = 1
      // this.getOverall(2);
      this.getTab2List()
    },
    handleSumbitReplay() {
      if (this.replayItem.addAdviceTime) {
        this.addReply()
      } else {
        this.reply()
      }
    },
    reply() {
      return this.$service
        .post('/web/course_comment/addFirstReply', {
          bus_id: this.selectBusId2,
          mark_id: this.replayItem.markId,
          reply: this.replayContent
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.drawerflag = false
            this.getTab2List()
          }
        })
    },
    addReply() {
      return this.$service
        .post('/web/course_comment/addSecondReply', {
          bus_id: this.selectBusId2,
          mark_id: this.replayItem.markId,
          add_reply: this.replayContent
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.drawerflag = false
            this.getTab2List()
          }
        })
    },
    getOverall(way) {
      // let groupId = '';
      // let coachId = '';
      // let begin = '';
      // let end = '';
      // let bus_id = ''
      // if (way == 1) {
      //     groupId = this.choseGroup1Id;
      //     begin = this.dateRange1[0];
      //     end = this.dateRange1[1];
      //     bus_id = this.selectBusId1
      // } else if (way == 2) {
      //     groupId = this.groupId;
      //     coachId = this.coachId;
      //     begin = this.dateRange2[0];
      //     end = this.dateRange2[1];
      //     bus_id = this.selectBusId2
      // }
      // return this.$service.post('/Web/CourseComment/summary', {
      //     start_time: begin,
      //     end_time: end,
      //     group_id: groupId,
      //     coach_id: coachId,
      //     bus_id
      // }).then(res => {
      //     if (res.data.errorcode == 0) {
      //         // this.commentInformation = res.data.data;
      //     } else {this.$Message.error(res.data.errormsg);}
      // });
    },
    getTab1List(extract = 0) {
      return this.$service
        .post('/web/statistics/open_class_comment_statistics', {
          start_time: this.dateRange1[0],
          end_time: this.dateRange1[1],
          class_id: this.selectedClass1,
          is_export: extract,
          page_no: !!extract ? '' : this.pageNo1,
          page_size: !!extract ? '' : this.pageSize1,
          bus_id: this.selectBusId1
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (extract === 0) {
              this.data1 = res.data.data.list
              this.total1 = res.data.data.count
              this.commentInformation[0] = res.data.data.total.open_class_nums
              this.commentInformation[1] = res.data.data.total.comment_nums
              this.commentInformation[2] = res.data.data.total.sign_number_nums
              this.commentInformation[3] =
                res.data.data.total.evaluation_rate + '%'
              this.commentInformation[4] = res.data.data.total.good_rate + '%'
              this.commentInformation[5] = res.data.data.total.bad_rate + '%'
              this.commentInformation[6] = res.data.data.total.add_comment_nums
              this.commentInformation[7] =
                res.data.data.total.add_comment_rate + '%'
            } else {
              let list = res.data.data.list
              if (Array.isArray(list)) {
                list.forEach(item => {
                  if (Array.isArray(item.tag_arr)) {
                    let tagStr = ''
                    const len = item.tag_arr.length
                    item.tag_arr.forEach((tag, index) => {
                      tagStr += '(' + tag.name + ' X ' + tag.num + ')'
                      if (index !== len - 1) {
                        tagStr += ', '
                      }
                    })
                    item.mark = tagStr
                  }
                })
              }
              this.$refs.export.export({
                filename: `私教评价汇总统计(${this.dateRange1[0]}~${
                  this.dateRange1[1]
                })`,
                columns: this.columns1,
                data: list
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTab2List(extract = 0) {
      return this.$service
        .post('/Web/course_comment/getLists', {
          start_time: this.dateRange2[0],
          end_time: this.dateRange2[1],
          class_id: this.selectedClass2,
          is_export: extract,
          page_no: !!extract ? '' : this.pageNo2,
          page_size: !!extract ? '' : this.pageSize2,
          bus_id: this.selectBusId2,
          isReply: this.replyValue
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (extract === 0) {
              this.data2 = res.data.data.list
              this.total2 = res.data.data.count
            } else {
              let list = res.data.data.list
              if (Array.isArray(list)) {
                list.forEach(item => {
                  if (!!item.class_time) {
                    item.class_time = this.formatDate(
                      new Date(item.class_time * 1000),
                      'yyyy-MM-dd HH:mm:ss'
                    )
                  }
                  if (!!item.comment_time) {
                    item.comment_time = this.formatDate(
                      new Date(item.comment_time * 1000),
                      'yyyy-MM-dd HH:mm:ss'
                    )
                  }
                  if (Array.isArray(item.set_type_json)) {
                    let tagStr = ''
                    const len = item.set_type_json.length
                    item.set_type_json.forEach((tag, index) => {
                      tagStr += tag.value + ': ' + tag.star + '分'
                      if (index !== len - 1) {
                        tagStr += ', '
                      }
                    })
                    item.assessDetail = tagStr
                  }
                  if (Array.isArray(item.tag_json)) {
                    let tagStr = ''
                    const len = item.tag_json.length
                    item.tag_json.forEach((tag, index) => {
                      tagStr += tag.value
                      if (index !== len - 1) {
                        tagStr += ', '
                      }
                    })
                    item.mark = tagStr
                  }
                })
              }
              this.$refs.export.export({
                filename: `私教评价明细统计(${this.dateRange2[0]}~${
                  this.dateRange2[1]
                })`,
                columns: this.columns2,
                data: list
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    exportCsv1() {
      this.getTab1List(1)
    },
    exportCsv2() {
      this.getTab2List(1)
    },
    pageSizeChanged1(size) {
      this.pageNo1 = 1
      this.pageSize1 = size
      this.getTab1List()
    },
    pageSizeChanged2(size) {
      this.pageNo2 = 1
      this.pageSize2 = size
      this.getTab2List()
    }
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap {
    border-top: 0;
  }
}
.mgb-20 {
  margin-bottom: 20px;
}
.mgt-20 {
  margin-top: 20px;
}

.box {
  .tab-head {
    background-color: white;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 50px;
  }

  .total-stat {
    background-color: white;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    border-top: @border;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
}
</style>
