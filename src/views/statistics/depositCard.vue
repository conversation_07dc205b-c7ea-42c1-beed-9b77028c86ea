<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" placeholder="选择场馆" @on-change="handleBusChanged" filterable>
        <Option v-for="option in adminBusList" :value="option.id" :key="option.id">{{ option.name }}</Option>
      </Select>
      <DatePicker type="daterange" :value="dateRange" @on-change="handleDateRangeChanged" :options="options" placeholder="请选择时间区间" :clearable="false"></DatePicker>
      <Select v-model="postData.statistics_type" placeholder="统计维度" @on-change="typeChange">
        <Option :value="1">按卡种统计</Option>
        <Option :value="2">按会员统计</Option>
      </Select>
      <Select v-model="postData.card_id" placeholder="储值卡种" clearable filterable>
        <Option v-for="card in cardList" :value="card.id" :key="card.id">{{ card.name }}</Option>
      </Select>

      <Input v-show="postData.statistics_type===2" style="width: 180px" class="notdo-enter" v-model="postData.search" placeholder="会员名称"
               @on-enter="searchInfo"/>
      <Button type="success" @click="searchInfo">搜索</Button>
    </header>
    <Table width="100%" ref="table" :columns="columns" :data="tableData" v-if="tableData" stripe></Table>
     <footer v-if="tableData && tableData.length>0">
      <div>
        <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      </div>
      <Page :total="+totalCount" :current.sync="postData.page_no" placement="top" show-total show-sizer @on-change="getInfo()" @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>
<script>
const cardColumns = [
  {
    title: '储值卡',
    key: 'card_name'
  },
  {
    title: '购买量',
    key: 'purchase_volume'
  },
  {
    title: '赠送量',
    key: 'gift_volume'
  },
  {
    title: '期间消耗购买量',
    key: 'current_period_purchase_volume'
  },
  {
    title: '期间消耗赠送量',
    key: 'current_period_gift_volume'
  },
  {
    title: '累积消耗购买量',
    key: 'cumulative_purchase_volume'
  },
  {
    title: '累积消耗赠送量',
    key: 'cumulative_gift_volume'
  },
  {
    title: '累积剩余购买',
    key: 'cumulative_last_purchase_volume'
  },
  {
    title: '累积剩余赠送',
    key: 'cumulative_last_gift_volume'
  }
]
import { mapState, mapGetters } from 'vuex'
import Export from 'src/components/Export'
import { formatDate } from 'utils'
export default {
  name: 'DepositCard',
  data() {
    return {
      dataInfo: '',
      totalCount: 0,
      merchantsBusList: [],
      cardList: [],
      dateRange: this.getInitDay(),
      options: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now()
        }
      },
      postData: {
        statistics_type: 1,
        start_time: this.getInitDay()[0],
        end_time: this.getInitDay()[1],
        bus_id: '',
        card_id: '',
        search: '',
        page_no: 1,
        page_size: 10,
        user_id: ''
      },
      columns: cardColumns.slice(),
      tableData: ''
    }
  },
  computed: {
    ...mapState(['busId']),
    ...mapGetters(['adminBusList']),
  },
  methods: {
    getMerchantsBusList() {
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      // this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
      //   if (res.data.errorcode === 0) {
      //     this.merchantsBusList = res.data.data
      //   } else {
      //     this.$Message.success(res.data.errormsg)
      //   }
      // })
    },
    pageSizeChanged(size) {
      this.postData.page_size = size;
      this.searchInfo();
    },
    typeChange(val) {
      if(val === 2) {
        this.columns.unshift({
          title: '会员',
          key: 'user_name'
        })
      } else {
        this.columns = cardColumns.slice()
      }
      this.searchInfo()
    },
    handleBusChanged(val) {
      this.getCardList()
      this.searchInfo()
    },
    searchInfo() {
      this.postData.page_no = 1
      this.getInfo()
    },
    getCardList() {
      const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
      // 获取所有储值卡
      return this.$service
        .post(url, {
          bus_id: this.postData.bus_id,
          page_size: 9999,
          card_type: 1
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.cardList = res.data.data.list.filter((item)=>item.card_type_id === '3')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getInitDay() {
      let first, last
      let date = new Date()
      if (date.getDate() === 1) {
        first = new Date(date.getFullYear(), date.getMonth() - 1, 1)
        let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate()
        last = new Date(
          new Date().getFullYear(),
          new Date().getMonth() - 1,
          day
        )
      } else {
        first = new Date(new Date().setDate(1))
        last = new Date()
      }
      return [formatDate(first, 'yyyy-MM-dd'), formatDate(last, 'yyyy-MM-dd')]
    },
    handleDateRangeChanged(val) {
      val = val || ['', '']
      this.postData.start_time = val[0]
      this.postData.end_time = val[1]
    },
    getInfo(allPageCount) {
      let postObj = Object.assign({}, this.postData);
      if (allPageCount) {
        postObj.page_size = allPageCount;
        postObj.page_no = 1;
      }
      return this.$service
        .post(
          '/Web/DepositCardStatistics/getDepositCardStatistics',
          postObj
        )
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            if(!allPageCount) {
               this.totalCount = resData.count
              this.tableData = resData.list
            }
            return res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    async exportCsv() {
      const resData = await this.getInfo(this.totalCount)
      this.$refs.export.export({
        filename: `储值卡使用统计(${this.postData.start_time}~${
          this.postData.end_time
        })`,
        columns: this.columns,
        data: resData
      })
    }
  },
  created() {
    this.getMerchantsBusList()
    this.postData.bus_id = this.busId || ''
    this.getInfo()
    this.getCardList(this.busId)
  },
  components: {
    Export
  }
}
</script>
