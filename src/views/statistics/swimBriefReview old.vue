<template>
  <div class="private-lessons">
    <div class="header">
      <DatePickerWithButton @on-change="dateRangeChanged"></DatePickerWithButton>
      <AdminRegion v-model="choseGroup" url="/Web/CoachGroup/getCoachGroupList" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" />
      <!-- <Select class="dropmenu" v-model="selectedGroup" v-for="group in groupList" :key="group.group_id" filterable v-if="groupList">
        <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
        <Option v-for="coach in group.coach_list"
                style="padding-left: 30px"
                :key="coach.coach_id"
                :value="coach.coach_id">{{ coach.coach_name}}
        </Option>
        <template v-if="group.son">
          <Option-group v-for="group in group.son" :key="group.group_id">
            <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
            <Option style="padding-left: 30px"
                    v-for="coach in group.coach_list"
                    :key="coach.coach_id"
                    :value="coach.coach_id">{{ coach.coach_name }}
            </Option>
            <template v-if="group.son">
              <Option-group v-for="group in group.son" :key="group.group_id">
                <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
                <Option style="padding-left: 30px"
                        v-for="coach in group.coach_list"
                        :key="coach.coach_id"
                        :value="coach.coach_id">{{ coach.coach_name }}
                </Option>
              </Option-group>
            </template>
          </Option-group>
        </template>
      </Select> -->
    </div>
    <div class="total-stat" v-if="totalStat">
      <!-- <span>教练业绩统计</span> -->
      <div class="stat">
        <h3>{{ totalStat.class_count }}</h3>
        <p>上课课时</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.new_class_count }}</h3>
        <p>新售课时</p>
      </div>
      <b></b>
      <!-- <div class="stat">
        <h3>{{ totalStat.new_member }}</h3>
        <p>新增私教会员
          <Tooltip>
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <div>首次购买某教练的私教课会员总人数，同一</div>
              <div>会员购买多个教练私教课总人数只计为1</div>
            </div>
          </Tooltip>
        </p>
      </div>
      <b></b> -->
      <div class="stat">
        <h3>{{ totalStat.card_order_count }}</h3>
        <p>订单总数
          <Tooltip>
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <div>购卡、续卡、销卡、升卡订单的总数</div>
            </div>
          </Tooltip>
        </p>
      </div>
      <b></b>
      <div class="stat">
        <h3>
          <span>￥</span>{{ totalStat.achievement }}</h3>
        <p>业绩
          <Tooltip>
            <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
            <div slot="content">
              <div>购卡、续卡、销卡、升卡产生的业绩总和</div>
            </div>
          </Tooltip>
        </p>
      </div>
    </div>
    <Table ref="table" border stripe :columns="columnstitle" :data="databriefs">
    </Table>
    <footer v-if="totalStat" class='footerbar'>
      <Button style="margin-right: 30px" @click="otherCase">导出excel</Button>
      <Page :total="totalStat.count" :current.sync="currentPage" show-total show-sizer placement="top" @on-change="pageChange" @on-page-size-change="pageSizeChanged"></Page>
    </footer>
  </div>
</template>
<script>
  import AdminRegion from 'components/form/adminRegion.vue';
  import Stat from '../../mixins/stat';
  export default {
    mixins: [Stat],
    components: { AdminRegion },
    data() {
      return {
        coachId: '',
        groupUrl: '/Web/Statistics/coach_group_list',
        // briefUrl: '/Web/Statistics/private_coach_statistics',
        briefUrl: '/Web/Statistics/swimming_coach_statistics',
        pagetitle: 'briefstat',
        databriefs: [],
        databriefsExport: [],
        sizer: 10,
        currentPage: 1,
        notExport: true,
        columnstitle: [
          {
            title: '教练',
            key: 'name'
          },
          {
            title: '上课课时',
            key: 'class_count'
          },
          {
            title: '新售课时',
            key: 'new_class_count'
          },
          // {
          //   title: '新增私教会员',
          //   key: 'new_member'
          // },
          {
            title: '新开订单',
            key: 'buy_card_order_count'
          },
          {
            title: '续课订单',
            key: 'renewal_of_insurance_order_count'
          },
          // {
          //   title: '升卡订单',
          //   key: 'change_card_count'
          // },
          // {
          //   title: '销卡订单',
          //   key: 'cancel_card_count'
          // },
          // {
          //   title: '拆分订单',
          //   key: 'split_order_count'
          // },
          {
            title: '其它订单',
            key: 'other_order_count',
            renderHeader: (h, params) => {
              return (
                <div>
                  其它订单
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      升级、转卡、销卡、拆分等其它合同订单
                    </div>
                    <icon size="16" type="ios-help-circle" style="padding-left: 5px" color="#F7DC6F" />
                  </tooltip>
                </div>
              );
            }
          },
          {
            title: '协助业绩',
            key: 'help_achievement',
            renderHeader: (h, params) => {
              return (
                <div>
                  协助业绩
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      作为成单协助人分得的业绩金额
                    </div>
                    <icon size="16" type="ios-help-circle" style="padding-left: 5px" color="#F7DC6F" />
                  </tooltip>
                </div>
              );
            }
          },
          {
            title: '总业绩',
            // width: '11%',
            key: 'achievement'
          },
          {
            title: '操作',
            key: 'action',
            // width: '12%',
            align: 'center',
            render: (h, params) => {
              const goDetail = () => {
                let postData = this.postData();
                this.$router.push(
                  `/Web/Statistics/coach_statistics_detail/${params.row.id}/${postData.s_date}/${postData.e_date}`
                );
              };
              return (
                <div>
                  <i-button
                    type="dashed"
                    shape="circle"
                    size="small"
                    style={{ color: '#52a4ea', border: 0 }}
                    onClick={goDetail}>
                    详情
                  </i-button>
                </div>
              );
            }
          }
        ],
        choseGroup: "",
        choseGroupId: ""
      };
    },
    watch: {
      choseGroup: function(val, old) {
        if (val) {
          const groupIdArr = val.split("_");
          if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
            this.choseGroupId = groupIdArr[0];
          }
        } else {
          this.choseGroupId = ""
        }
        this.getbriefData();
      }
    },
    methods: {
      getbriefExportList(isExport) {
        let url = this.briefUrl;
        let postData = this.postData();
        postData.page_no = 1;
        postData.page_size = this.totalStat.count;
        return this.$service
          .post(url, postData, { isExport })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.databriefsExport = res.data.data.list;
            } else {
              this.$Message.error(res.data.errormsg);
              return false;
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async otherCase() {
        await this.getbriefExportList(true);
        if (!this.databriefsExport) return false;

        this.notExport = false;
        this.$refs.table.exportCsv({
          filename: '教练业绩概况',
          columns: this.columnstitle.filter((col, index) => index < 11),
          data: this.databriefsExport
        });
        setTimeout(() => {
          this.notExport = true;
        }, 100);
      },
      pageChange(pageno) {
        this.currentPage = pageno;
        this.getbriefData();
      },
      pageSizeChanged(size) {
        this.sizer = size;
        this.getbriefData();
      },
      postData() {
        return {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          // group_id: this.groupId,
          group_id: this.choseGroupId,
          coach_id: this.coachId,
          page_no: this.currentPage,
          page_size: this.sizer
        };
      }
    },
    // watch: {
    //   selectedGroup(val) {
    //     let flag = val.slice(0, 5);
    //     if (flag === 'group') {
    //       this.groupId = val.replace(val.slice(0, 5), '');
    //       this.coachId = ''
    //     } else {
    //       this.coachId = val;
    //       this.groupId = ''
    //     }
    //     this.currentPage = 1;
    //     this.getbriefData()
    //   },
    // }
    activated() {
      this.getbriefData();
    }
  };
</script>
<style lang="less">
  @border: 1px solid #dcdcdc;
  .private-lessons {
    background-color: #fff;
    border: @border;
    .header {
      display: flex;
      margin: 22px 19px;
      .group {
        font-size: 14px !important;
        font-weight: bold;
      }
      .ivu-select-item-selected,
      .ivu-select-item-selected:hover {
        color: #fff;
        background: rgba(45, 140, 240, 0.9);
      }
    }
    .total-stat {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      padding: 0 40px 15px 40px;
      height: 135px;
      border-top: @border;
      border-bottom: @border;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      > span {
        position: absolute;
        left: 22px;
        top: 13px;
        color: #666;
        font-size: 14px;
      }
      .stat {
        h3 {
          font-size: 40px;
          color: #52a4ea;
          font-weight: normal;
          margin-top: 20px;
          span {
            font-size: 24px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
        }
      }
      > b {
        width: 1px;
        height: 30px;
        background-color: #ccc;
      }
    }

    .ivu-table-wrapper {
      border: 0;
      position: static;
      border-bottom: 1px solid #e9eaec;
    }
    .ivu-table-body {
      max-height: 480px;
      overflow: auto;
    }
    .ivu-table th {
      padding: 0;
      padding-right: 5px;
    }
    .ivu-table-border th,
    .ivu-table-border td {
      border-right: 0;
    }
    .ivu-table-cell {
      padding: 0;
    }
    .dropmenu.ivu-select {
      width: 200px;
      margin-left: 30px;
    }
    .ivu-select-group-title {
      display: none;
    }
    .footerbar {
      padding: 27px 17px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
</style>



