<template>
<div>
  <Alert type="warning" show-icon style="marginBottom:10px">此报表仅从2022.11.08日开始统计</Alert>
  <div class="table-wrap">
    <header>
      <DatePicker
        type="month"
        :value="postData.month"
        :options="dateOptions"
        placeholder="请选择查询月份"
        @on-change="dateChange"></DatePicker>
      <!-- <Button type="success" @click="doSearch">搜索</Button> -->
    </header>
    <Table
      ref="table"
      :data="tableData"
      :columns="columns"
      disabledHover
      stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :total="total"
        :postData="postData"
        @on-change="pageChange"></Pager>
    </footer>
  </div>
</div>
</template>

<script>
  import { formatDate } from 'utils';
  import Export from 'src/components/Export';
  import Pager from 'components/pager';

  export default {
    name: 'MemberClassStat', // 会员上课统计
    components: {
      Export,
      Pager,
    },

    data() {
      const startDate = new Date('2022-10-01');
      return {
        dateOptions: {
          disabledDate(date) {
            return date.valueOf() > Date.now() || date <= startDate;
          }
        },
        postData: {
          month: formatDate(new Date(), 'yyyy-MM'),
          page_no: 1,
          page_size: 10,
        },
        tableData: [],
        total: 0,
        columns: [
          {
            key: 'month',
            title: '月份',
            render: (_, { row }) => <router-link to={{ name: '会员上课统计明细', params: { month: row.month } }}>
              { row.month }
            </router-link>
          },
          {
            key: 'class_mark_count',
            title: '预约次数'
          },
          {
            key: 'sign_count',
            title: '签到次数'
          },
          {
            key: 'miss_count',
            title: '爽约次数'
          },
          {
            key: 'class_name',
            title: '哪种课程上的最多'
          },
          {
            key: 'coach_name',
            title: '哪个教练的课上的最多'
          },
          // {
          //   key: 'class_average_sign_count',
          //   title: '课均签到次数'
          // },
          {
            key: 'total_value',
            title: '成交价值',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  成交价值
                  <tooltip max-width="220" placement="left-start" transfer>
                    <div slot="content">
                      会员在时间段内总共支付团课的金额(包含使用储值卡、次卡、私教卡、泳教卡、现金支付)
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
        ],
      };
    },

    // created() {

    // },

    methods: {
      getList() {
        this.$service
          .get('/web/Statistics/openClassUserMonthAll', { params: this.postData })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        return this.$service.get('/web/Statistics/openClassUserMonthAll', {
          params: {
            ...this.postData,
            page_no: 1,
            page_size: +this.total || 999
          }
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            return res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        if (this.postData.month !== info) {
          this.postData.month = info
          this.doSearch()
        }
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      async exportCsv() {
        const exportData = await this.getExportData();
        this.$refs.export.export({
          filename: `会员上课统计${this.postData.month ? `(${this.postData.month})` : ''}`,
          columns: this.columns,
          data: exportData
        });
      }
    }
  };
</script>

<style scoped lang="less">
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .icon-tips {
    padding-left: 5px;
    vertical-align:text-bottom;
  }
</style>
