
<style lang="less">
  @border: 1px solid #dcdcdc;
  .private-class-info {
    padding: 0 30px;
    .title {
      line-height: 40px;
      font-size: 14px;
      color: #666;
    }
    .class-num,
    .class-count,
    .class-type {
      height: 400px;
    }

    .checkbox-controller-box {
      margin-bottom: 10px;
    }
    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }

    .pies {
      border-top: @border;
      // border-bottom: @border;
      padding: 30px 0 40px;
    }
  }
</style>

<template>
  <div class="table-wrap private-class-info">
    <header style="border-bottom: 0; padding: 0">
      <Select v-model="selectBusId" @on-change="getList" style="width:200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <DatePicker type="daterange" :value="dateRange" :options="dateOptions" @on-change="dateChanged" format="yyyy-MM-dd" style="width: 200px" :clearable="false" />
    </header>
    <AgeGender @on-change="ageGenderChanged" />
    <Total v-model="totalItem" />
    <Row class="pies">
      <Col span="8">
      <div class="class-num"></div>
      </Col>
      <Col span="8">
      <div class="class-count"></div>
      </Col>
      <Col span="8">
      <div class="class-type"></div>
      </Col>
    </Row>
    <h3 class="title">教练课程消课情况</h3>
    <Table :columns="columns" :data="tableData" style="border-left: 1px solid #dddee1; border-top: 1px solid #dddee1" />
    <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  import Total from 'components/form/Total';
  import quickPicker from 'components/picker/quickPicker';
  import Pager from 'mixins/pager';
  import AgeGender from 'components/stat/ageGender';
  import { formatDate } from 'utils';
  import echarts from 'echarts';
  import { mapState } from "vuex";

  const TOTAL_ITEM_NAME = {
    ptPeoplenum: { name: '来上私教课的会员', unit: '人' },
    ptClassratio: { name: '私教课上课率' },
    ptClassnum: { name: '消课节数', unit: '节' },
    ptincdecratio: { name: '增消比例', tips: '新增私教课程数 : 消课课程数' },
    ptFrequency: { name: '平均训练频率', unit: '天/人节' }
  };
  const INIT_COLUMNS = [
    {
      title: '教练',
      key: 'coach_name'
    },
    {
      title: '总消课节数',
      key: 'class_total'
    },
    {
      title: '消课频率（节/天）',
      key: 'decr_class_per_day'
    }
  ];

  export default {
    name: 'privateClassInfo',
    components: { Total, AgeGender },
    mixins: [Pager],
    data() {
      return {
        $classNum: null,
        $classCount: null,
        $classType: null,
        dateOptions: quickPicker.dateOptions,
        totalItem: [],
        dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
        privateClassInfo: {},
        columns: [],
        tableData: [],
        classNumOption: {
          title: {
            text: '上课人数占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['未来上课的私教会员', '来上课的私教会员'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}<br/>人数：{c} <br/> 占比：({d}%)'
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [{ value: 0, name: '未来上课的私教会员' }, { value: 0, name: '来上课的私教会员' }],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        classCountOption: {
          title: {
            text: '上课次数占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['无'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '上课节数：{b}<br/>会员：{c} <br/> 占比：({d}%)'
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [{ value: 0, name: '无' }],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        classTypeOption: {
          title: {
            text: '课程消费种类占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['无'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}：{c}节 <br/> 占比：({d}%)'
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [{ value: 0, name: '无' }],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        storeList: [],
        selectBusId: ''
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    created () {
      this.selectBusId = this.busId
      this.getStoreList()
    },
    mounted() {
      this.$classNum = echarts.init(document.querySelector('.class-num'));
      this.$classCount = echarts.init(document.querySelector('.class-count'));
      this.$classType = echarts.init(document.querySelector('.class-type'));
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      dateChanged(val) {
        this.dateRange = val
        this.$nextTick(() => this.getList());
      },
      ageGenderChanged(ages, genders) {
        this.ages = ages;
        this.genders = genders;
        this.getList();
      },
      dealTotal() {
        const info = this.privateClassInfo;
        let arr = [];
        for (let key in TOTAL_ITEM_NAME) {
          const { name, unit, tips } = TOTAL_ITEM_NAME[key];
          arr.push({
            value: info[key] || 0,
            name,
            unit,
            tips
          });
        }
        this.totalItem = arr;
      },
      dealPie() {
        const info = this.privateClassInfo;
        const { ptPeoplenum, ptClassratio, notPtclassnum } = info;
        if (ptPeoplenum || notPtclassnum) {
          this.classNumOption.series[0].data = [
            { name: '未来上课的私教会员', value: notPtclassnum },
            { name: '来上课的私教会员', value: ptPeoplenum }
          ];
        } else {
          this.classNumOption.series[0].data = [
            { name: '未来上课的私教会员', value: 0 },
            { name: '来上课的私教会员', value: 0 }
          ];
        }
        if (info.ptClassNamestatistics && info.ptClassNamestatistics.length) {
          this.classTypeOption.series[0].data = info.ptClassNamestatistics.map(item => {
            return {
              name: item.className,
              value: item.classNum
            };
          });
          this.classTypeOption.legend.data = info.ptClassNamestatistics.map(item => item.className);
        } else {
          this.classTypeOption.series[0].data = [{value: 0, name: '无'}]
          this.classTypeOption.legend.data = ['无']
        }
        if (info.ptNumratio && info.ptNumratio.length) {
          this.classCountOption.series[0].data = info.ptNumratio.map(item => {
            return {
              name: `${item.ptNum}节`,
              value: item.ptPeople
            };
          });
          this.classCountOption.legend.data = info.ptNumratio.map(item => `${item.ptNum}节`);
        } else {
          this.classCountOption.series[0].data = [{value: 0, name: '无'}]
          this.classCountOption.legend.data = ['无']
        }
        this.$classNum.setOption(this.classNumOption);
        this.$classCount.setOption(this.classCountOption);
        this.$classType.setOption(this.classTypeOption);
      },
      dealTableData() {
        const info = this.privateClassInfo;
        const tableTitle = info.ptTitleClassName || [];
        const tableList = info.coach_list;
        this.columns = INIT_COLUMNS.concat(
          tableTitle.map(item => {
            return {
              key: item,
              title: item
            };
          })
        );
        this.total = tableList.total;
        if (tableList.list) {
          this.tableData = tableList.list.map(item => {
            let obj = {};
            for (let key of tableTitle) {
              for (let course of item.class_list) {
                if (course.class_name === key) {
                  obj[key] = course.class_num;
                  break;
                }
              }
              obj[key] = obj[key] || 0;
            }
            return {
              ...item,
              ...obj
            };
          });
        } else {
          this.tableData = [];
        }
      },
      getList() {
        if (!this.ages.length || !this.genders.length) return false;

        const url = '/Web/Statistics/pt_class_analyze';
        const postData = {
          s_date: formatDate(new Date(this.dateRange[0]), 'yyyy-MM-dd'),
          e_date: formatDate(new Date(this.dateRange[1]), 'yyyy-MM-dd'),
          age_str: this.ages.join(','),
          sex_str: this.genders.join(','),
          page_no: this.page,
          page_size: this.pageSize,
          bus_id: this.selectBusId
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.privateClassInfo = data;
              this.dealTotal();
              this.dealPie();
              this.dealTableData();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
