<template>
  <div style="background: #fff;padding:20px;border:1px solid #dcdcdc;font-size:14px;">
    <div style="margin-left:20px;font-size:14px;">
      <Row :gutter='16'>
        <Col span="3">
        <Select v-model="memberList.coach_id"  filterable placeholder="销售人员" @on-change="select" >
          <Option v-for="item in memberList" :value="item.coach_id" :key="item.coach_id">{{ item.coach_name }}</Option>
        </Select>
        </Col>
        <Col span="10">
        <Date-picker :value="value1" @on-change='dateChange' format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期" style="width: 260px"></Date-picker>
        </Col>
        <Col span="2">
        <Button type="success" @click="searchBtn">搜索</Button>
        </Col>
      </Row>
    </div>
    <div style="border:1px solid #dcdcdc;margin-top:20px;">
      <div style="padding-top:20px;padding-bottom:20px;border-bottom:1px solid #dcdcdc">
        <Row type="flex" justify="center" align="middle" class="code-row-bg">
          <Col span="3">
          <p style="height: 86px;text-align:center;"><img :src=imgUrl alt="" style="width: 86px; height: 86px;"></p>
          </Col>
          <Col span="21">
          <Row>
            <Col span="20">
            <p style="font-size:18px;margin-bottom:10px;">{{peopleName}}</p>
            </Col>
          </Row>
          <Row>
            <Col span="8">
            <p style="font-size:16px;">时间 <span style="color:#d9534f">{{start_time}}</span> 至 <span style="color:#d9534f">{{end_time}}</span> </p>
            </Col>
            <Col span="12">
            <p style="font-size:16px;">销售业绩 <span style="color:#d9534f">{{amount}}</span> 元</p>
            </Col>
          </Row>
          <Row>
            <Col span="4">
            <p style="font-size:16px;">私教课 <span style="color:#d9534f">{{sClass}}</span> 节</p>
            </Col>
            <Col span="4">
            <p style="font-size:16px;">团课 <span style="color:#d9534f">{{tClass}}</span> 节</p>
            </Col>
            <Col span="12">
            <p style="font-size:16px;">操课 <span style="color:#d9534f">{{cClass}}</span> 节</p>
            </Col>
          </Row>
          </Col>
        </Row>
      </div>
      <Table :columns="columns1" :data="dataHeader" :row-class-name="rowClassName" ref="table"></Table>
      <Table :columns="columns2" :data="data2"></Table>
      <Table :columns="columns3" :data="data3"></Table>
      <Table :columns="columns4" :data="data2" height="38" no-data-text=' '></Table>
    </div>
    <div >
      <div class="otheroptions">
        <a :href="exportUrl">导出Excel</a>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate } from '../../utils/index.js';
export default {
  data() {
    return {
      memberList: [{
        coach_id: 259,
        coach_name: '翠花'
      }],
      imgUrl: '',
      peopleName: '',
      start_time: '',
      end_time: '',
      sClass: '',//私课
      tClass: '',//团课
      cClass: '',//操课
      amount: '',//业绩
      value1: ['2017-05-01', '2017-08-01'],
      columnsRightHeader:[
        {
          title: '名称',
          key: 'username'
        },
        {
          title: '课程',
          key: 'card_name'
        },
        {
          title: '购卡金额',
          key: 'amount'
        },
        {
          title: '总节数',
          key: 'all_num'
        },
        {
          title: '购卡单价',
          key: 'ave_price'
        },
        {
          title: '上课节数',
          key: 'class_count'
        }
      ],
      columnsRight:[],
      dataRightHeader: [],
      dataHeader:[
         {
          username: '名称',
          card_name: '课程',
          amount: '购卡金额',
          all_num: '总节数',
          ave_price: '购卡单价',
          class_count: '上课节数',
        }
      ],
      columns1: [
        {
          title: '课程种类',
          key: 'kind',
          width: 200,
          className: 'headerClass',
          render: (h, params, index) => {
             return <div>私教课</div>
          }
        },
        {
          className: 'headerClass',
          renderHeader: (h, params, index)=>{
              return <i-table show-header={false} columns={this.columnsRightHeader} data={this.dataHeader}></i-table>
          },
          render: (h, params, index)=>{
              return <i-table show-header={false} columns={this.columnsRightHeader} data={this.data1}></i-table>//会报width undefined
          }
        },
      ],
      columns2: [
        {
          title: ' ',
          key: 'kind'
        },
        {
          title: ' ',
          key: 'name'
        },
        {
          title: ' ',
          key: 'class'
        },
        {
          title: ' ',
          key: 'money'
        },
        {
          title: ' ',
          key: 'all'
        },
        {
          title: '总计',
          key: 'price'
        },
        {
          title: '节',
          key: 'classNum'
        }
      ],
      columns3: [
        {
          title: ' ',
          key: 'kind'
        },
        {
          title: ' ',
          key: 'name'
        },
        {
          title: ' ',
          key: 'class'
        },
        {
          title: ' ',
          key: 'money'
        },
        {
          title: ' ',
          key: 'all'
        },
        {
          title: '总计',
          key: 'price'
        },
        {
          title: '节',
          key: 'classNum'
        }
      ],
      columns4: [
        {
          title: ' ',
          key: 'kind'
        },
        {
          title: ' ',
          key: 'name'
        },
        {
          title: ' ',
          key: 'class'
        },
        {
          title: ' ',
          key: 'money'
        },
        {
          title: ' ',
          key: 'all'
        },
        {
          title: '总计',
          key: 'price'
        },
        {
          title: '节',
          key: 'classNum'
        }
      ],
      data1: [
        {
          username: '',
          card_name: '',
          amount: '',
          all_num: '',
          ave_price: '',
          class_count: '',
        }
      ],
      data2: [
        {
          kind: '操课',
          username: '',
          card_name: '',
          amount: '',
          all_num: '',
          ave_price: '',
          class_count: ''
        }
      ],
      data3: [
        {
          kind: '团课',
          username: '',
          card_name: '',
          amount: '',
          all_num: '',
          ave_price: '',
          class_count: ''
        }
      ]
    }
  },
  created() {
    this.getList(259)
    this.getCoachList()

  },
  computed: {
      exportUrl: function () {
        var host = window.location.host;
        if (host == 'i-test.rocketbird.cn') {
          var hostname = 'https://beta.rocketbird.cn';
        } else if (host == 'i-sim.rocketbird.cn') {
          var hostname = 'https://sim.rocketbird.cn';
        } else if (host == 'i.rocketbird.cn') {
          var hostname = 'https://wx.rocketbird.cn';
        } else if (host == 'localhost:8089') {
          var hostname = 'https://beta.rocketbird.cn';
        }
        else {
          return '';
        }
        var url = hostname + '/Web/Class/class_statistics_excel' + '?' + '&s_date=' + this.start_time + '&e_date=' + this.end_time;
        return (url);
      }
    },
  methods: {
    rowClassName(row, index){
      // console.log(row)
      if(row.username == '名称'){
        return 'firstRow';
      }
    },
    searchBtn() { //搜索方法
      this.getList()
    },
    select(value) {//切换销售人员
      this.memberList.coach_id = value
    },
    dateChange(value) {
      // console.log(this.value1)
      this.value1 = value;
      // console.log(this.value1[0])
      // console.log(this.value1[1])
    },
    getList(coach_id) {
      let url = '/Web/Class/get_class_statistics';
      let postData = {
        coach_id: this.memberList.coach_id ? this.memberList.coach_id : coach_id,
        s_date: this.value1[0],
        //s_date: '2017-5-1',
        e_date: this.value1[1],
        //e_date: '2017-8-1',
      };
      this.$service.post(url, postData).then(res => {
        if (res.data.errorcode === 0) {
          let tag = res.data.data.private_class_statistics
          delete tag.private_class_sign_count //删掉多余的一个属性
          this.data1 = Object.values(tag)//将对象转化为一个数组
          // console.log(Object.values(tag))
          // this.data1.username ? this.data1.username : this.data1.username = Object.values(tag).nickname
          let tagArr = Object.keys(tag)

          this.imgUrl = res.data.data.coach_info.avatar//图片
          this.sClass = res.data.data.coach_info.private_class_sign_count //私课
          this.tClass = res.data.data.coach_info.group_class_sign_count //团课
          this.cClass = res.data.data.coach_info.open_class_sign_count //操课
          this.amount = res.data.data.coach_info.amount //总节数
          this.peopleName = res.data.data.coach_info.coach_name //教练名
          this.start_time = res.data.data.coach_info.s_date //开始时间
          this.end_time = res.data.data.coach_info.e_date  //结束时间
          let title = res.data.data.coach_info.private_class_sign_count
          this.columns2[6].title = (title ? title : 0) + '节'
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getCoachList: function() {
      var url = '/Web/Sign/get_coach_list';
      this.$service.post(url).then(res => {
        this.memberList = res.data.data.list
        this.memberList.coach_id = this.memberList[0].coach_id
      })
    },
  }
}
</script>
<style>
  .firstRow td:last-child{
    padding: 0;
  }
  .headerClass{
    padding: 0 !important;
    background-color: #fff !important;
  }
  .ivu-table:after, .ivu-table:before{
    height: 0;
  }
  .otheroptions {
    height: 82px;
    /* padding-left: 17px; */
    padding-top: 25px;
  }

  .otheroptions a {
    display: block;
    text-decoration: none;
    line-height: 30px;
    width: 102px;
    height: 30px;
    border-radius: 4px;
    border: none;
    box-shadow: 0px 2px 2px #dad9d9;
    background: #f0f0f0;
    font-family: 微软雅黑;
    font-size: 14px;
    color: #666;
    text-align: center;
  }
</style>
