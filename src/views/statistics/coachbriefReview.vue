<template>
  <div class="box">
    <header class="header">
      <Select v-model="searchPost.bus_id" @on-change="getCoachList" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <!-- <Select v-model="searchPost.membership_id" class="search-item">
        <Option
          v-for="item in membershipList"
          :value="item.marketers_id"
          :key="item.marketers_id"
          >{{ item.sale_name }}</Option
        >
      </Select> -->
      <Select v-model="searchPost.coach_id" class="search-item" clearable filterable>
        <Option
          v-for="item in coachList"
          :value="item.coach_id"
          :key="item.coach_id"
          >{{ item.coach_name }}{{item.deleted==1?'--（已删除）':''}}</Option
        >
      </Select>
      <Date-picker
        v-model="dateValue"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        class="search-item"
      ></Date-picker>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData"></Table>
      <Table :columns="totalColumn" :data="totalData" :show-header="false" no-data-text="" :row-class-name="() => 'overall-table'"></Table>
    </main>
    <footer class="footer">
      <ExportButton url="/Web/Statistics/private_coach_statistics" :data="searchPost" />
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from '@/utils/index'
import { mapState } from 'vuex'
import { getcoachsInfo  } from '@/service/getData'
import ExportButton from 'components/form/ExportButton'
export default {
  components: {
    Pager,
    ExportButton
  },
  data() {
    const tableColumn = [
      {
        key: 'name',
        title: '教练'
      },
      {
        key: 'achievement',
        title: '总业绩'
      },
      {
        key: 'card_order_count',
        title: '总单数'
      },
      {
        key: 'buy_card_amount',
        title: '新开业绩'
      },
      {
        key: 'buy_card_order_count',
        title: '新开单数'
      },
      {
        key: 'renewal_of_insurance_amount',
        title: '续单业绩'
      },
      {
        key: 'renewal_of_insurance_order_count',
        title: '续单单数'
      },
      {
        key: 'change_card_amount',
        title: '升级业绩'
      },
      {
        key: 'change_card_count',
        title: '升级单数'
      },
      {
        key: 'delete_card_order_amount',
        title: '本期销卡'
      },
      {
        key: 'before_delete_card_order_amount',
        title: '往期销卡'
      },
      {
        key: 'other_order_amount',
        renderHeader(h, params) {
          return (
            <Tooltip content="拆分、请假、补卡、转卡">
              其他业绩
              <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
            </Tooltip>
          )
        }
      },
      {
        key: 'other_order_count',
        title: '其他单数'
      },
      {
        key: 'help_achievement',
        renderHeader(h, params) {
          return (
            <Tooltip content="协助他人成单的业绩">
              协助业绩
              <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
            </Tooltip>
          )
        }
      },
      {
        key: 'help_order_count',
        title: '协助单数'
      },
      {
        key: 'action',
        title: '操作',
        render: (h, params) => {
          return (
            <a onClick={() => {
              this.$service.post('/Web/Statistics/coach_statistics_detail', {
                s_date: this.searchPost.s_date,
                e_date: this.searchPost.e_date,
                coach_id: params.row.id,
                bus_id: this.jumpBusId
              }).then(res => {
                if (res.data.errorcode === 0) {
                  this.$router.push({
                    name: '教练业绩统计',
                    params: {
                      beginTime: this.searchPost.s_date,
                      endTime: this.searchPost.e_date,
                      busId: this.jumpBusId,
                      coachId: params.row.id
                    }
                  })
                } else {
                  this.$Message.error(res.data.errormsg)
                }
              })
            }}>
              详情
            </a>
          )
        }
      }
    ]
    const totalColumn = [
      {
        key: 'name',
        render(h, params) {
          return (<strong>总计</strong>)
        }
      },
      ...tableColumn.slice(1, tableColumn.length - 1),
      {
        key: 'action'
      }
    ]
    return {
      // header
      dateValue: [],
      searchPost: {
        bus_id: '',
        coach_id: '',
        s_date: '',
        e_date: '',
        page_no: 1,
        page_size: 10
      },
      storeList: [],
      jumpBusId: '',
      // membershipList: [],
      coachList: [],
      // main
      tableColumn,
      tableData: [],
      totalColumn,
      totalData: [],
      // footer
      count: 100
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  methods: {
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getMembershipList() {
      this.$service.get('/Web/Statistics/get_marketer').then(res => {
        if (res.data.errorcode === 0) {
          this.membershipList = res.data.data
        }
      })
    },
    getCoachList() {
      getcoachsInfo(
        this.searchPost.bus_id,
        0, 
        1
      ).then(res => {
        if (res.data.errorcode === 0) {
          this.coachList = res.data.data
        }
      })
    },
    getTableData() {
      if (!this.searchPost.bus_id) {
        this.$Message.error('请选择门店!')
        return false
      }
      if (!this.searchPost.s_date || !this.searchPost.e_date) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .post('/Web/Statistics/private_coach_statistics', this.searchPost)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.count = res.data.data.statistics.count
            this.jumpBusId = this.searchPost.bus_id
            const total = res.data.data.statistics
            this.totalData = []
            this.totalData.push({
              achievement: total.achievement_total,
              card_order_count: total.card_order_total,
              buy_card_amount: total.buy_card_amount_total,
              buy_card_order_count: total.buy_card_order_total,
              renewal_of_insurance_amount: total.renewal_of_insurance_amount_total,
              renewal_of_insurance_order_count: total.renewal_of_insurance_order_total,
              change_card_amount: total.change_card_amount_total,
              change_card_count: total.change_card_total,
              other_order_amount: total.other_order_amount_total,
              other_order_count: total.other_order_total,
              help_achievement: total.help_achievement_total,
              delete_card_order_amount: total.delete_card_order_amount,
              before_delete_card_order_amount: total.before_delete_card_order_amount,
              help_order_count: total.help_order_total
            })
          }
        })
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.searchPost.s_date = ''
      this.searchPost.e_date = ''
      if (this.dateValue[0]) {
        this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      }
      if (this.dateValue[1]) {
        this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      }
      this.getTableData()
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    const { busId, beginTime, endTime } = this.$route.params
    if (busId) {
      this.searchPost.bus_id = busId
    } else {
      this.searchPost.bus_id = this.busId
    }
    if (beginTime && endTime) {
      this.searchPost.s_date = beginTime
      this.searchPost.e_date = endTime
      this.dateValue = [beginTime, endTime]
    } else {
      const dayNow = new Date().getDate() - 1
      const beginDate = formatDate(
        new Date(Date.now() - dayNow * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM-dd')
      this.dateValue = [beginDate, endDate]
      this.searchPost.s_date = beginDate
      this.searchPost.e_date = endDate
    }
    // this.getMembershipList()
    this.getCoachList()
    this.getStoreList().then(this.getTableData)
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}

.box {
  .header {
    .white-panel;
    justify-content: flex-start;
    height: 90px;
    padding-top: 10px;

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }

  /deep/ .overall-table {
    height: 60px;

    td {
      border: none;
    }
  }
}
</style>
