<template>
  <Card style="padding:14px;">

    <Row style="height:50px;">
      <Col span="24">
      <Select v-model="selectBusId" @on-change="getAllDB" style="width:200px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <DatePicker type="daterange" v-model="datedate" @on-change="handleDatedateChange" :options="datedateOptions" placeholder="请选择时间区间" style="width: 200px" :clearable="false"></DatePicker>
      </Col>
    </Row>

    <Row class="checkbox-controller-box" style="margin-top:20px;">
      <Col span="2">
      <Checkbox :indeterminate="ageFlag.isPart" v-model="ageFlag.isAll" @on-change="handleAgeCheckAll">年龄</Checkbox>
      </Col>
      <Col span="22">
      <Checkbox-group v-model="ageFlag.group" @on-change="handleAgeCheckGroup">
        <Checkbox label="14岁以下会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="15-20岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="21-30岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="31-40岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="41-50岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="51-60岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="61-70岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="71岁以上会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="未设置年龄会员" class="checkbox-controller-age"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
    <Row class="checkbox-controller-box">
      <Col span="2">
      <Checkbox :indeterminate="genderFlag.isPart" v-model="genderFlag.isAll" @on-change="handleGenderCheckAll">性别</Checkbox>
      </Col>
      <Col span="22">
      <Checkbox-group v-model="genderFlag.group" @on-change="handleGenderCheckGroup">
        <Checkbox label="男" class="checkbox-controller-gender"></Checkbox>
        <Checkbox label="女" class="checkbox-controller-gender"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>

    <!-- <Row style="border-top: 1px solid #dcdcdc;">
      <Col span="22" offset="1">
      <div class="tip-box">
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.add_member}}</span>
            <span class="k-unit">人</span>
          </div>
          <div class="tip-name">新增潜在会员</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.add_member_purchase}}</span>
            <span class="k-unit">人</span>
          </div>
          <div class="tip-name">
            成功转化会员
            <Tooltip placement="top">
            <Icon type="ios-information" style="color: orange;"></Icon>
            <div slot="content">
              <p>转化为会员：指首次购卡成为会员</p>
            </div>
            </Tooltip>
          </div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val">
            <span class="k-val">{{membershipTip.add_member_purchase_ratio}}</span>
          </div>
          <div class="tip-name">会员转化率</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">
            <span class="k-val">{{membershipTip.out_member}}</span>
            <span class="k-unit">人</span>
          </div>
          <div class="tip-name">
            过期会员
            <Tooltip placement="top">
            <Icon type="ios-information" style="color: orange;"></Icon>
            <div slot="content">
              <p>次数、金额用完或者时间到期</p>
            </div>
            </Tooltip>
          </div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">
            <span class="k-val">{{membershipTip.out_member_continue}}</span>
            <span class="k-unit">人</span>
          </div>
          <div class="tip-name">续卡会员</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">
            <span class="k-val">{{membershipTip.out_member_continue_ratio}}</span>
          </div>
          <div class="tip-name">会员卡续卡率</div>
        </div>
      </div>
      </Col>
    </Row> -->

    <Row>
      <Col span="24" style="border-top:1px solid #dcdcdc;border-bottom:1px solid #dcdcdc;">
      <Total v-model="totalItem" />
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="12">
      <div class="customer-original"></div>
      </Col>
      <Col span="12">
      <div class="coach-method"></div>
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="12">
      <div class="customer-original-hell"></div>
      </Col>
      <Col span="12">
      <div class="coach-method-hell"></div>
      </Col>
    </Row>

    <Row style="margin-top:20px;">
      <Col span="24">
      <div class="age-ratio"></div>
      </Col>
    </Row>

    <!-- <Row>
      <Col span="22" offset="1">
      <h2>新开卡、续卡分布</h2>
      <div class="gai-head">
        <div class="kong" :style="kong"></div>
        <div class="once-buy" :style="onceBuy">新购</div>
        <div class="twice-buy" :style="twiceBuy">续卡</div>
      </div>
      <Table :columns="columns" :data="newAndRenewal"></Table>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1" style="display: flex; justify-content: flex-end; margin-top: 10px;">
        <Page :total="pageTotal" @on-change="handlePageNoChange"></Page>
      </Col>
    </Row> -->

  </Card>
</template>
<script>
  import echarts from "echarts";
  import Total from 'components/form/Total';
  import { mapState } from "vuex";

  const AGE_GROUP = [
    "14岁以下会员",
    "15-20岁会员",
    "21-30岁会员",
    "31-40岁会员",
    "41-50岁会员",
    "51-60岁会员",
    "61-70岁会员",
    "71岁以上会员",
    "未设置年龄会员"
  ];
  const GENDER_GROUP = ["男", "女"];

  export default {
    data() {
      return {
        totalItem: [],
        kong: {width: '33.33%'},
        onceBuy: {width: '33.33%'},
        twiceBuy: {width: '33.33%'},
        pageTotal: 0,
        pageNo: 1,
        pageSize: 10,
        datedate: [],
        datedateOptions: {
          shortcuts: [
            {
              text: "一周",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                return [start, end];
              }
            },
            {
              text: "一个月",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                return [start, end];
              }
            },
            {
              text: "三个月",
              value() {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                return [start, end];
              }
            }
          ]
        },
        $ageChart: null,
        $customerChart: null,
        $coachChart: null,
        $customerChartHell: null,
        $coachChartHell: null,
        ageFlag: {
          isAll: true,
          isPart: false,
          group: AGE_GROUP
        },
        genderFlag: {
          isAll: true,
          isPart: false,
          group: GENDER_GROUP
        },
        // membershipTip: {
        //   add_member: 0,
        //   add_member_purchase: 0,
        //   add_member_purchase_ratio: "0%",
        //   out_member: 0,
        //   out_member_continue: 0,
        //   out_member_continue_ratio: "0%"
        // },
        customerOption: {
          title: {
            text: "新增会员转比例",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: ["未购卡潜在会员", "已购卡会员"],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>人数：{c} <br/> 占比：{d}%"
          },
          series: [
            {
              name: "获客来源",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: "未购卡潜在会员" },
                { value: 0, name: "已购卡会员" }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        coachOption: {
          title: {
            text: "卡种种类占比",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: ["卡种"],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}：{c} 张<br/> 占比：{d}%"
          },
          series: [
            {
              name: "卡种种类占比",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [{ value: 0, name: "卡种" }],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        customerOptionHell: {
          title: {
            text: "续卡比例",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: ["近期过期未续卡会员", "续卡会员"],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>人数：{c} <br/> 占比：{d}%"
          },
          series: [
            {
              name: "续卡比例",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: "近期过期未续卡会员" },
                { value: 0, name: "续卡会员" }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        coachOptionHell: {
          title: {
            text: "续卡种类占比",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: "center",
            y: "bottom",
            data: ["卡种"],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function(name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}：{c} <br/> 占比：{d}%"
          },
          series: [
            {
              name: "续卡种类占比",
              type: "pie",
              radius: "57%",
              center: ["50%", "50%"],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [{ value: 0, name: "卡种" }],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              }
            }
          ]
        },
        ageOption: {
          title: {
            text: "会籍新增会员分布",
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            }
          },
          dataZoom: [{type: 'slider'}],
          legend: {
              data: ['未转化潜在会员', '已成功转化会员']
          },
          grid: {
            left: "0",
            right: "0",
            bottom: "3%",
            containLabel: true
          },
          xAxis: [
            {
              type: "category",
              data: [],
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                }
              }
            }
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#666666",
                  fontSize: 12
                },
                formatter: "{value}"
              },
              minInterval: 1
            }
          ],
          series: [
            {
              name: "未转化潜在会员",
              type: "bar",
              stack: "勤鸟会员",
              data: [],
              color: ["#334b5c"]
            },
            {
              name: "已成功转化会员",
              type: "bar",
              stack: "勤鸟会员",
              data: [],
              color: ["#6ab0b8"]
            }
          ]
        },
        columns: [],
        newAndRenewal: [],
        storeList: [],
        selectBusId: ''
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    components: {Total},
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      handleAgeCheckAll() {
        this.ageFlag.isPart = false;
        if (this.ageFlag.isAll) {
          this.ageFlag.group = AGE_GROUP;
        } else {
          this.ageFlag.group = [];
        }
        this.getAllDB();
      },
      handleGenderCheckAll() {
        this.genderFlag.isPart = false;
        if (this.genderFlag.isAll) {
          this.genderFlag.group = GENDER_GROUP;
        } else {
          this.genderFlag.group = [];
        }
        this.getAllDB();
      },
      handleAgeCheckGroup(data) {
        if (data.length === 9) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = true;
        } else if (data.length > 0) {
          this.ageFlag.isPart = true;
          this.ageFlag.isAll = false;
        } else if (data.length === 0) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = false;
        }
        this.getAllDB();
      },
      handleGenderCheckGroup(data) {
        if (data.length === 2) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = true;
        } else if (data.length > 0) {
          this.genderFlag.isPart = true;
          this.genderFlag.isAll = false;
        } else if (data.length === 0) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = false;
        }
        this.getAllDB();
      },
      handleDatedateChange(val) {
        this.datedate = val;
        this.getAllDB();
      },
      handlePageNoChange(val) {
        this.pageNo = val;
        this.getAllDB();
      },
      getAgeVal() {
        let arr = [];
        this.ageFlag.group.forEach(item => {
          const idx = AGE_GROUP.findIndex(val => item === val);
          if (idx !== -1) {
            arr.push(idx + 1);
          }
        });
        if (arr.length === 0) {
          arr = [1,2,3,4,5,6,7,8,9];
        }
        return arr.sort().join(",");
      },
      getSexVal() {
        if (
          this.genderFlag.group.length === 2 ||
          this.genderFlag.group.length === 0
        ) {
          return "1,2";
        } else if (this.genderFlag.group[0] === "男") {
          return 1;
        } else {
          return 2;
        }
      },
      getDateString(date) {
        if (typeof date === "object") {
          const year = date.getFullYear();
          let month = date.getMonth() + 1;
          month = month < 10 ? "0" + month : month;
          let day = date.getDate();
          day = day < 10 ? "0" + day : day;
          return `${year}-${month}-${day}`;
        } else {
          return date;
        }
      },
      getAllDB() {
        return this.$service
          .post("/Web/Statistics/bus_daily_purchase_card_statistics", {
            s_date: this.getDateString(this.datedate[0]),
            e_date: this.getDateString(this.datedate[1]),
            age_str: this.getAgeVal(),
            sex_str: this.getSexVal(),
            page_no: this.pageNo,
            page_size: this.pageSize,
            bus_id: this.selectBusId
          })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.pageTotal = res.data.data.employ_total;
              // this is membership base information.
              this.setMembershipTip(res.data.data);

              // there are new membership ratio.
              this.setNewMembership(res.data.data);
              this.$customerChart.setOption(this.customerOption);

              // the kinds of card type.
              this.setCardType(res.data.data.add_card_type);
              this.$coachChart.setOption(this.coachOption);

              // the lately radio of renewal card.
              this.setRenewalCard(res.data.data);
              this.$customerChartHell.setOption(this.customerOptionHell);

              // the kinds of renewal type.
              this.setRenewalType(res.data.data.con_card_type);
              this.$coachChartHell.setOption(this.coachOptionHell);

              // new membership map by staff.
              this.setNewMembershipMap(res.data.data.employee_num);
              this.$ageChart.setOption(this.ageOption);

              // new card and renewal card map.
              this.setTable(res.data.data);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
      },
      setMembershipTip(bahama) {
        // this.membershipTip.add_member = bahama.add_member;
        // this.membershipTip.add_member_purchase = bahama.add_member_purchase;
        // this.membershipTip.add_member_purchase_ratio =
        //   bahama.add_member_purchase_ratio;
        // this.membershipTip.out_member = bahama.out_member;
        // this.membershipTip.out_member_continue = bahama.out_member_continue;
        // this.membershipTip.out_member_continue_ratio =
        //   bahama.out_member_continue_ratio;

        const arr = [
          {value: bahama.add_member, name: '新增潜在会员', unit: '人'},
          {value: bahama.add_member_purchase, name: '成功转化会员', unit: '人', tips: '转化为会员：指首次购卡成为会员'},
          {value: bahama.add_member_purchase_ratio, name: '会员转化率'},
          {value: bahama.out_member, name: '过期会员', unit: '人', tips: '次数、金额用完或者时间到期'},
          {value: bahama.out_member_continue, name: '续卡会员', unit: '人'},
          {value: bahama.out_member_continue_ratio, name: '会员卡续卡率'}
        ];

        this.totalItem = arr;
      },
      setNewMembership(nostalgia) {
        this.customerOption.series[0].data = [
          {
            value:
              parseInt(nostalgia.add_member) -
              parseInt(nostalgia.add_member_purchase),
            name: "未购卡潜在会员"
          },
          { value: nostalgia.add_member_purchase, name: "已购卡会员" }
        ];
      },
      setCardType(panama) {
        if (Array.isArray(panama) && panama.length > 0) {
          let coachData = [];
          let coachLegend = [];
          panama.forEach(item => {
            coachData.push({
              name: item.card_name,
              value: item.card_num
            });
            coachLegend.push(item.card_name);
          });

          this.coachOption.series[0].data = coachData;
          this.coachOption.legend.data = coachLegend;
        } else {
          this.coachOption.series[0].data = [{name: '卡种', value: 0}];
          this.coachOption.legend.data = ['卡种'];
        }
      },
      setRenewalCard(bang) {
        this.customerOptionHell.series[0].data = [
          // { value: bang.out_member, name: "近期过期未续卡会员" },
          {
            value: parseInt(bang.out_member) - parseInt(bang.out_member_continue),
            name: "近期过期未续卡会员"
          },
          { value: bang.out_member_continue, name: "续卡会员" }
        ];
      },
      setRenewalType(anymore) {
        if (Array.isArray(anymore) && anymore.length > 0) {
          let coachData = [];
          let coachLegend = [];
          anymore.forEach(item => {
            coachData.push({
              name: item.card_name,
              value: item.card_num
            });
            coachLegend.push(item.card_name);
          });
          this.coachOptionHell.series[0].data = coachData;
          this.coachOptionHell.legend.data = coachLegend;
        } else {
          this.coachOptionHell.series[0].data = [{name: '卡种', value: 0}];
          this.coachOptionHell.legend.data = ['卡种'];
        }
      },
      setNewMembershipMap(sugar) {
        if (Array.isArray(sugar) && sugar.length > 0) {
          let sugarBaba = [],
            sugarMama = [],
            sugarLegend = [];
          sugar.forEach(item => {
            sugarBaba.push(item.no);
            sugarMama.push(item.yes);
            sugarLegend.push(item.name);
          });
          this.ageOption.series[0].data = sugarBaba;
          this.ageOption.series[1].data = sugarMama;
          this.ageOption.xAxis[0].data = sugarLegend;
        }
      },
      setTable(london) {
        let once = 0, twice = 0;
        this.columns = [{ title: "业绩归属", key: "name" }, { title: "购卡总数", key: "buyTotal" }];
        if (Array.isArray(london.new_class_sort) && london.new_class_sort.length > 0) {
          once = london.new_class_sort.length;
          london.new_class_sort.forEach(item => { this.columns.push({ title: item, key: item }); });
        }
        this.columns.push({ title: "续卡总数", key: "renewalTotal" });
        if ( Array.isArray(london.con_class_sort) && london.con_class_sort.length > 0 ) {
          twice = london.con_class_sort.length;
          london.con_class_sort.forEach(item => { this.columns.push({ title: item, key: item }); });
        }
        if ( Array.isArray(london.employee_list) && london.employee_list.length > 0) {
          this.newAndRenewal = [];
          london.employee_list.forEach(item => {
            let hellSon = {
              name: item.name
            }, newTotal = 0, renewalTotal = 0;
            if (Array.isArray(item.new) && item.new.length > 0) {
              item.new.forEach(jack => {
                hellSon[jack.class_name] = jack.class_num;
                newTotal += parseInt(jack.class_num);
              });
            }
            if (Array.isArray(item.con) && item.con.length > 0) {
              item.con.forEach(rose => {
                hellSon[rose.class_name] = rose.class_num;
                renewalTotal += parseInt(rose.class_num);
              });
            }
            hellSon.buyTotal = newTotal;
            hellSon.renewalTotal = renewalTotal;
            this.newAndRenewal.push(hellSon);
          });
        }

        once++;
        twice++;
        const all = once + twice + 1;
        this.kong = {width: `${100/all}%`};
        this.onceBuy = {width: `${once*100/all}%`};
        this.twiceBuy = {width: `${twice*100/all}%`};
      }
    },
    created() {
      this.selectBusId = this.busId
      this.getStoreList()
    },
    mounted() {
      this.$customerChart = echarts.init(
        document.querySelector(".customer-original")
      );
      this.$coachChart = echarts.init(document.querySelector(".coach-method"));
      this.$customerChartHell = echarts.init(
        document.querySelector(".customer-original-hell")
      );
      this.$coachChartHell = echarts.init(
        document.querySelector(".coach-method-hell")
      );
      this.$ageChart = echarts.init(document.querySelector(".age-ratio"));

      const today = new Date();
      const offset = today.getDay();
      this.datedate = [
        // new Date(
        //   today.getTime() - ((!!offset ? offset : 7) - 1) * 24 * 60 * 60 * 1000
        // ),
        new Date(
          today.getTime() - 30 * 24 * 60 * 60 * 1000
        ),
        today
      ];
      this.getAllDB();
    }
  };
</script>
<style lang="less">
  @media screen and (min-width: 1900px) {
    .age-ratio {
      height: 350px;
    }

    .customer-original,
    .customer-original-hell,
    .coach-method,
    .coach-method-hell {
      height: 400px;
    }
  }

  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .age-ratio {
      height: 320px;
    }

    .customer-original,
    .customer-original-hell,
    .coach-method,
    .coach-method-hell {
      height: 366px;
    }
  }

  @media screen and (max-width: 1300px) {
    .age-ratio {
      height: 300px;
    }

    .customer-original,
    .customer-original-hell,
    .coach-method,
    .coach-method-hell {
      height: 343px;
    }
  }

  .gai-head {
    display: flex;
    flex-direction: row;
    height: 37px;

    .kong {
      background-color: #fff;
    }

    .once-buy {
      background-color: #66FF66;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .twice-buy {
      background-color: #77DDFF;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .checkbox-controller-box {
    height: 50px;

    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }
  }

  .age-ratio,
  .customer-original,
  .customer-original-hell,
  .coach-method,
  .coach-method-hell {
    width: 100%;
  }

  .tip-box {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    // border: 1px solid #dcdcdc;

    .tip {
      height: 165px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .tip-val {
        .k-val {
          font-size: 36px;
          color: #52a4ea;
        }

        .k-unit {
          font-size: 14px;
          color: #666666;
        }
      }

      .tip-name {
        font-size: 18px;
        color: #666666;
      }
    }

    .tip-line {
      height: 30px;
      border-left: 1px solid #dcdcdc;
    }
  }
</style>
