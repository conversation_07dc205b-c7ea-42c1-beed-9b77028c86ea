<template>
 <div>
    <Alert type="warning" show-icon>当前功能已经升级为新的收入分摊功能，原有功能将于2022-03-31正式关闭，之后请前往新功能查看。<router-link :to="{ path: '/stat/menus/financialAllocation2' }">立即前往</router-link></Alert>
    <div class="customized-tabs">
    <Tabs @on-click="clickTabs">
      <TabPane label="收入分摊汇总"
               name="0">
        <FinancialAllocationList :merchantsBusList="merchantsBusList" />
      </TabPane>
      <TabPane label="卡种收入分摊明细"
               name="1">
        <FinancialAllocationDetail :merchantsBusList="merchantsBusList" />
      </TabPane>
      <TabPane label="会员收入分摊明细"
               name="2">
        <FinancialAllocationMember :merchantsBusList="merchantsBusList" />
      </TabPane>
    </Tabs>
  </div>
 </div>
</template>

<script>
import FinancialAllocationList from './financialAllocationList.vue'
import FinancialAllocationDetail from './financialAllocationDetail.vue'
import FinancialAllocationMember from './financialAllocationMember.vue'
export default {
  name: 'financialAllocation',
  components: { FinancialAllocationList, FinancialAllocationDetail, FinancialAllocationMember },
  data () {
    return {
      activeIndex: 0,
      merchantsBusList: [],
      activated: [0]
    }
  },
  created () {
    this.getMerchantsBusList();
  },
  methods: {
    clickTabs (index) {
      this.activeIndex = index
    },
    getMerchantsBusList() {
      this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
        if (res.data.errorcode === 0) {
          this.merchantsBusList = res.data.data
        } else {
          this.$Message.success(res.data.errormsg);
        }
      });
    }
  }
}
</script>

