<template>
    <Modal
    v-model="showFlag"
    title="评价详情"
    :footer-hide="detail.comment_status!==1 && detail.courseCommenDetail.reply"
    @on-ok="ok">
        <div v-if="detail.Coach" class="v-coach-info">
                <img :src="detail.Coach.avatar" class="i-coach" />
                <div class="v-info-column">
                    <div>
                        {{detail.Coach.coach_name}}
                        <span>{{detail.courseCommenDetail.base_level_copy}}</span>
                        <span>(综合得分:{{detail.courseCommenDetail.average}}分)</span>
                    </div>
                    <div class="v-info-content-time">
                        <div class="mgr-20">
                        上课内容:{{detail.card_name}}
                        </div>
                        <div>
                        上课时间:{{detail.class_time}}
                        </div>
                    </div>
                </div>
        </div>
        <Divider v-if="detail.Coach" dashed />
       <div class="v-reply-container">
            <div class="v-reply-top mgb-20">
                <img :src="detail.UserInfo.avatar" class="i-user" />
                <div>
                    <div>{{detail.UserInfo.name}}</div>
                    <div>{{detail.courseCommenDetail.create_time_copy}}</div>
                </div>
            </div>
            <div  v-for="typeItem in detail.courseCommenDetail.set_type_json" :value="typeItem.value" :key="typeItem.id">{{typeItem.value}}
                <Rate v-model="typeItem.star" :count="detail.courseCommenDetail.base_max_score" show-text disabled>
                    <span >{{ typeItem.star }}分</span>
                    </Rate>
            </div>
            <div class="mgb-20 mgt-20">
                 <Tag v-for="tagItem in detail.courseCommenDetail.tag_json" :value="tagItem.value" :key="tagItem.id" color="primary" disabled>{{tagItem.value}}</Tag>
            </div>
            <div v-if="detail.courseCommenDetail.advice">意见反馈</div>
            <div class="mgb-20">{{detail.courseCommenDetail.advice}}</div>
            <div v-if="detail.courseCommenDetail.reply" class="v-reply-con mgb-20">
                <div>场馆回复({{detail.courseCommenDetail.reply_time_copy}})</div>
                <div>{{detail.courseCommenDetail.reply}}</div>
            </div>
             <div v-if="detail.courseCommenDetail.add_advice">{{detail.courseCommenDetail.add_advice_time_copy}} 追评</div>
            <div class="mgb-20">{{detail.courseCommenDetail.add_advice}}</div>
            <div v-if="detail.comment_status===1 || !detail.courseCommenDetail.reply" class="v-reply-input-row">
                <div class="v-reply-tip">商家回复</div>
                <Input type="textarea" v-model="reply" maxlength="500"/>
            </div>
       </div>
    </Modal>
</template>
<script>
export default {
    name:'EvaluationModel',
    data () {
        return {
            showFlag:false,
            reply:'',
            detail:{
                courseCommenDetail:{},
                UserInfo:{}
            }
        }
    },
    methods:{
        beginReply(detail){
            console.log(detail)
            this.detail = detail
            this.reply = ''
            this.showFlag = true
        },
        handleCancel() {
            this.drawerFlag = false
         },
        ok(){
            let reply = this.detail.courseCommenDetail.reply
            if(this.detail.comment_status!==1 && reply){
                this.handleCancel()
                return
            }
            if(!this.reply){
                this.$Message.error('回复不能为空')
                return
            }
            const replyData={sign_log_id:this.detail.courseCommenDetail.sign_log_id,reply:this.reply}
            this.$emit('on-reply', replyData)
        }
    }
}
</script>
<style lang="less" scoped>
    .mgr-20{
        margin-right: 20px;
    }
    .mgt-20 {
        margin-top: 20px;
    }
    .mgb-20 {
        margin-bottom: 20px;
    }
    .v-coach-info{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .i-coach{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 20px;
        }
        .v-info-content-time{
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 5px;
        }
    }
    .v-reply-top{
            display: flex;
            justify-content: flex-start;
            align-items: center;
        .i-user{
             width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 20px;
         }
    }
    
    .v-reply-input-row{
         display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        .v-reply-tip{
            width: 60px;
        }
    }
</style>
