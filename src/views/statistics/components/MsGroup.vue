<template>
  <div class="merge-table-box table-wrap">
    <header>
        <!-- <DatePickerWithButton style="max-width:370px" select="本月" @on-change="handleDateRangeChanged"></DatePickerWithButton> -->
      <DatePicker type="month" placeholder="选择月份" v-model="month" :options="options"></DatePicker>
      <Select v-model="postData.bus_id" placeholder="选择场馆" @on-change="getGroupMs" filterable v-if="type==1">
        <Option v-for="option in merchantsBusList" :value="option.bus_id" :key="option.bus_id">{{ option.bus_name }}</Option>
      </Select>
      <Select v-model="postData.group_id" placeholder="选择组别" v-if="groupList && type==1" clearable filterable>
        <Option v-for="item in groupList" :key="item.group_id" :value="item.group_id" :label="item.name" :style="{'padding-left': (+item.group_level)*15+'px'}">{{item.name}}</Option>
      </Select>
      <Select v-model="postData.marketers_ids" placeholder="选择会籍" filterable v-if="type==2 && marketerList && marketerList.length">
        <Option v-for="option in marketerList" :value="option.marketers_id" :key="option.marketers_id">{{ option.sale_name }}</Option>
      </Select>
      <Button type="success" @click="getInfo(false)">搜索</Button>
    </header>
    
    <div class="total-stat" v-if="dataInfo && dataInfo.new_source!=undefined">
      <div class="stat" v-if="dataInfo.new_source!=undefined">
        <h3>{{ dataInfo.new_source }}</h3>
        <p>新增资源</p>
      </div>
      <b></b>
      <div class="stat" v-if="dataInfo.plan_visit!=undefined">
        <h3>{{ dataInfo.plan_visit }}</h3>
        <p>计划到店</p>
      </div>
      <b></b>
      <div class="stat" v-if="dataInfo.actual_visit!=undefined">
        <h3>{{ dataInfo.actual_visit }}</h3>
        <p>实际到店</p>
      </div>
      <b></b>
      <div class="stat" v-if="dataInfo.ms_card_order_total!=undefined">
        <h3>{{ dataInfo.ms_card_order_total }}</h3>
        <p>订单数</p>
      </div>
      <b></b>
       <div class="stat" v-if="dataInfo.achievement!=undefined">
        <h3>{{ dataInfo.achievement }}</h3>
        <p>总业绩</p>
      </div>
      
    </div>
    <main>
    <Table width="100%" height="500"  ref="table" :columns="columns" :data="tableData" v-if="tableData"></Table>
    </main>
    <footer v-if="tableData && tableData.length>0">
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
    </footer>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import { formatDate, treeToList } from 'utils';
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue'
import Export from 'src/components/Export';
  export default {
    name: 'MsGroupl',
    data () {
      return {
        timeSlot: ['',''],
        dataInfo: '',
        columns: [],
        marketerList: [],
        tableData: '',
        selectedGroup: '',
        groupList: '',
        month: new Date(),
        merchantsBusList: [],
        options: {
          disabledDate (date) {
              return date && date.valueOf() > Date.now() - 86400000;
          }
        },
        postData: {
          s_date: '',
          e_date: '',
          marketers_ids: '',
          group_id: '',
          bus_id: ''
        }
      }
    },
    props: {
      type: {
        type: Number,
        default: 1
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    methods: {
      handleDateRangeChanged (val) {
        this.timeSlot = val;
      },
      getMerchantsBusList() {
        this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
          if (res.data.errorcode === 0) {
            this.merchantsBusList = res.data.data
          } else {
            this.$Message.success(res.data.errormsg);
          }
        });
      },
      getMarketer() {
        return this.$service.post('/Web/Statistics/get_marketer').then(res => {
          if (res.data.errorcode === 0) {
            this.marketerList = res.data.data
            if (res.data.data && res.data.data[0]) {
              this.postData.marketers_ids = res.data.data[0].marketers_id || ''
            }
          } else {
            this.$Message.success(res.data.errormsg);
          }
        });
      },
      getInfo() {
        let postObj = this.postData
        let date = new Date(this.month)
        let y = date.getFullYear(), m = date.getMonth()
        let firstDay = new Date(y, m, 1);
        let lastDay = m===new Date().getMonth()?new Date() : new Date(y, m+1, 0);
        postObj.s_date = formatDate(firstDay, 'yyyy-MM-dd')
        postObj.e_date = formatDate(lastDay, 'yyyy-MM-dd')
        postObj.type = this.type
        this.$service.post('/Web/Statistics/membership_work_statistics', postObj).then(res => {
          if (res.data.errorcode === 0) {
            const dataInfo =  res.data.data
            this.dataInfo = dataInfo
            this.columns = []
            this.tableData = []
            let dataList= []
            if (dataInfo.list) {
              const keys = [''].concat(Object.keys(dataInfo.list));
              keys.forEach((item, index) => {
                this.columns.push({
                  title: item,
                  key: item,
                  width: item===''?150:100,
                  fixed: index===0?'left':''
                })
              });
              for (let index = 0; index < dataInfo.titles.length; index++) {
                let curIndexInfo = {}
                keys.forEach((item, i) => {
                    if(i===0){
                      curIndexInfo[item] = dataInfo.titles[index]
                    } else {
                      curIndexInfo[item] = dataInfo.list[item][index]
                    }
                });
                dataList.push(curIndexInfo)
              }
            }
            this.tableData = dataList
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getGroupMs (busId) {
        this.$service.post('/Web/MarketersGroup/get_marketers_group',{'bus_id':busId||this.busId||''}).then(res => {
            if (res.data.errorcode == 0) {
              this.groupList = treeToList(res.data.data, 'son')
              this.postData.group_id = ''
            } 
        });
      },
      async exportCsv() {
        await this.getInfo();
        this.$refs.export.export({
          filename: `会籍概况统计表(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns,
          data: this.tableData
        });
      }
    },
   async created () {
      this.postData.bus_id = this.busId || ''
      if(this.type == 2) {
        await this.getMarketer()
      } else if (this.type==1) {
        this.getMerchantsBusList();
      }
      this.getGroupMs();
      this.getInfo(false);
    },
    components: {
      DatePickerWithButton,
      Export
    }
  }
</script>
<style lang="less" scoped>
  @border: 1px solid #dcdcdc;

  .merge-table-box {
    background-color: #fff;

    

    .total-stat {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      padding: 0 40px;
      // height: 150px;
      height: 120px;
      border-top: @border;
      border-bottom: @border;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      >span {
        position: absolute;
        left: 22px;
        top: 13px;
        color: #666;
        font-size: 14px;
      }
      .stat {
        h3 {
          font-size: 40px;
          color: #52a4ea;
          font-weight: normal;
          // margin-top: 20px;
          span {
            font-size: 24px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
        }
      }
      >b {
        width: 1px;
        height: 30px;
        background-color: #ccc;
      }
    }

    .checked-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 40px;
      // margin-left: 40px;

      label {
        width: 150px;
      }
    }
    .sub-head {
      display: flex;
      flex-direction: row;

      .sub-head-item {
        width: 100px;
        font-weight: bold;
      }
    }
    .left-head {
      font-weight: bold;
      border: none;
    }
  }
</style>
