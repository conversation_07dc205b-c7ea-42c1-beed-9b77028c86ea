<template>
  <Modal
    v-model="showAdd"
    title="详情"
    :mask-closable="false"
    width="850px"
  >
    <div class="table-wrap">
      <main>
        <Table
          v-if="tableData"
          :columns="columns"
          :data="tableData"
          ref="table"
          stripe
          disabled-hover
        ></Table>
      </main>
      <footer>
        <Page
          :total="+pageTotal"
          :current.sync="searchData.page_no"
          placement="top"
          show-total
          show-sizer
          @on-change="getList"
          @on-page-size-change="pageSizeChanged"
        />
      </footer>
    </div>
    <div
      slot="footer"
    >
     
    </div>
  </Modal>
</template>
<script>
export default {
  name: 'TeamDetailTable',
  data() {
    return {
      pageTotal: 0,
      searchData: {
        page_no: 1,
        page_size: 10,
      },
      tableData: [],
      columns: [
        {
          title: '班级',
          key: 'team_class_name',
        },
        {
          title: '课程',
          key: 'curriculum_name',
        },
        {
          title: '上课时间',
          key: 'start_time',
        },
        {
          title: '班级人数',
          key: 'class_num',
        },
        {
          title: '实到人数',
          key: 'class_real_num',
        },
      ],
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    post: {
      type: Object,
    },
    isSwim: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
  },
  components: {},
  created() {
    this.searchData = {
      ...this.searchData,
      ...this.post
    }
    this.getList()
  },
  methods: {
    doSearch() {
      this.searchData.page_no = 1
      this.getList()
    },
    pageSizeChanged(val) {
      this.searchData.page_size = val
      this.getList()
    },
    getList() {
      this.$service
        .post(`/Web/Statistics/team_class_detail_${this.isSwim?'swim':'private'}_statistics`, this.searchData)
        .then((res) => {
          if (res.data.errorcode === 0) {
            let data = res.data.data
            this.tableData = data.list
            this.pageTotal = data.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
  },
}
</script>

