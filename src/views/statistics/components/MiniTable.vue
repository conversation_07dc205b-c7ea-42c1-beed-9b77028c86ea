<template>
  <div class="mini-table">
    <div class="mini-table-row">
      <div class="mini-table-field" v-for="(item, index) in showList" :key="index">
        <span class="mini-table-label">{{item.name}}</span>
        <span class="mini-table-value">￥{{item.value}}， {{item.percent}}%</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      required: true,
      default: () => {}
    },
    showSwimming: {
      type: Boolean,
      default: true
    },
    showBooking: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      list: [
        {
          key: 'buycard',
          name: '购卡',
          percent: 0
        },
        {
          key: 'renewcard',
          name: '续卡',
          percent: 0
        },
        {
          key: 'pt_buycard',
          name: '购私教',
          percent: 0
        },
        {
          key: 'pt_renewcard',
          name: '续私教',
          percent: 0
        },
        {
          key: 'swim_buycard',
          name: '购泳教',
          percent: 0
        },
        {
          key: 'swim_renewcard',
          name: '续泳教',
          percent: 0
        },
        // {
        //   key: 'package_buycard',
        //   name: '套餐包',
        //   percent: 0
        // },
        {
          key: 'changecard',
          name: '升级',
          percent: 0
        },
        {
          key: 'attorncard',
          name: '转卡',
          percent: 0
        },
        {
          key: 'commodity',
          name: '商品销售',
          percent: 0
        },
        {
          key: 'lockerrent',
          name: '租柜',
          percent: 0
        },
        {
          key: 'stopCard',
          name: '销卡',
          percent: 0
        },
        {
          key: 'front_money',
          name: '定金',
          percent: 0
        },
        {
          key: 'cash_pledge',
          name: '押金',
          percent: 0
        },
        {
          key: 'cross_buy_card',
          name: '跨店购卡',
          percent: 0
        },
        {
          // 订场展示 space_amount字段
          // key: 'space_reserve',
          key: 'space_amount',
          name: '订场',
          percent: 0
        },
        {
          key: 'other',
          name: '其他',
          percent: 0
        },
        {
          key: 'san_account',
          name: '票务',
          percent: 0
        }
      ],
      total: 0,
      showList: []
    }
  },
  watch: {
    detail(newValue, oldValue) {
      this.reload()
    }
  },
  methods: {
    getPercent(key) {
      const percent = (Number(this.detail[key]) * 100) / Number(this.total)
      if (percent > 0) {
        return percent.toFixed(2)
      } else if (percent > 100) {
        return 100
      } else {
        return 0
      }
    },
    reload() {
      this.total = this.detail.general_income

      this.showList = [...this.list]
      if (!this.showSwimming) {
        this.showList = this.showList.filter(item => {
          return item.key !== 'swim_buycard' && item.key !== 'swim_renewcard'
        })
      }
      if (!this.showBooking) {
        this.showList = this.showList.filter(item => {
          return item.key !== 'space_amount' && item.key !== 'san_account'
        })
      }

      this.showList = this.showList.map(item => {
        return {
          ...item,
          percent: this.getPercent(item.key),
          value: this.detail[item.key]
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.mini-table {
  width: 100%;
  margin-top: 10px;
}
.mini-table-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;

  .mini-table-field {
    width: 50%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    line-height: 2;

    .mini-table-label {
      font-size: 14px;
      color: #808890;
      margin-left: 20px;
    }

    .mini-table-value {
      font-size: 14px;
      color: #b4b5ba;
      margin: 0 20px;
    }
  }
}
</style>
