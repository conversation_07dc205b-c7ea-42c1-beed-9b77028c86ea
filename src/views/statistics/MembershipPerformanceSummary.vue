<template>
  <div class="box">
    <header class="header">
      <Select v-model="searchPost.bus_id" class="search-item" clearable filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Select v-model="searchPost.level_id" class="search-item" clearable filterable>
        <Option
          v-for="item in levelList"
          :value="item.level_id"
          :key="item.level_name"
          >{{ item.level_name }}</Option
        >
      </Select>
      <Date-picker
        v-model="dateValue"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        class="search-item"
        :clearable="false"
      ></Date-picker>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData"></Table>
    </main>
    <footer class="footer">
      <ExportButton url="/Web/Statistics/bus_statistics_detail_ms" :data="searchPost" />
      <div></div>
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from '@/utils/index'
import ExportButton from 'components/form/ExportButton'

export default {
  components: {
    Pager,
    ExportButton
  },
  data() {
    return {
      // header
      dateValue: [],
      searchPost: {
        s_date: '',
        e_date: '',
        level_id: '',
        bus_id: '',
        page_no: 1,
        page_size: 10,
        type: 1
      },
      storeList: [],
      levelList: [],
      // main
      tableColumn: [
        {
          key: 'name',
          title: '门店'
        },
        {
          key: 'level_name',
          title: '等级'
        },
        {
          key: 'staff_num',
          title: '会籍人数'
        },
        {
          key: 'total_amount',
          title: '总业绩'
        },
        {
          key: 'total_count',
          title: '总单数'
        },
        {
          key: 'new_amount',
          title: '新开业绩'
        },
        {
          key: 'new_count',
          title: '新开单数'
        },
        {
          key: 'renew_amount',
          title: '续单业绩'
        },
        {
          key: 'renew_count',
          title: '续单单数'
        },
        {
          key: 'change_amount',
          title: '升级业绩'
        },
        {
          key: 'change_count',
          title: '升级单数'
        },
        {
          key: 'other_amount',
          renderHeader(h, params) {
            return (
              <Tooltip content="拆分、请假、补卡、销卡、转卡、租柜">
                其他业绩
                <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
              </Tooltip>
            )
          }
        },
        {
          key: 'other_count',
          title: '其他单数'
        },
        {
          key: 'help_amount',
          renderHeader(h, params) {
            return (
              <Tooltip content="协助他人成单的业绩">
                协助业绩
                <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
              </Tooltip>
            )
          }
        },
        {
          key: 'help_count',
          title: '协助单数'
        },
        {
          title: '操作',
          render: (h, params) => {
            return (
              <a onClick={() => {
                this.$service.post('/Web/Statistics/membership_statistics', {
                  s_date: this.searchPost.s_date,
                  e_date: this.searchPost.e_date,
                  bus_id: params.row.id
                }).then(res => {
                  if (res.data.errorcode === 0) {
                    this.$router.push({
                      name: '会籍业绩概况',
                      params: {
                        beginTime: this.searchPost.s_date,
                        endTime: this.searchPost.e_date,
                        busId: params.row.id
                      }
                    })
                  } else {
                    this.$Message.error(res.data.errormsg)
                  }
                })
              }}>
                详情
              </a>
            )
          }
        }
      ],
      tableData: [],
      // footer
      count: 100
    }
  },
  methods: {
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getLevelList() {
      this.$service.get('/Web/Statistics/get_bus_level_list').then(res => {
        if (res.data.errorcode === 0) {
          this.levelList = res.data.data
        }
      })
    },
    getTableData() {
      if (!this.searchPost.s_date || !this.searchPost.e_date) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .post('/Web/Statistics/bus_statistics_detail_ms', this.searchPost)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.count = res.data.data.count
          }
        })
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.searchPost.s_date = ''
      this.searchPost.e_date = ''
      if (this.dateValue[0]) {
        this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      }
      if (this.dateValue[1]) {
        this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      }
      this.getTableData()
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    this.searchPost = { ...this.searchPost, ...this.$route.params }
    if (this.searchPost.s_date && this.searchPost.e_date) {
      this.dateValue = [this.searchPost.s_date, this.searchPost.e_date]
    } else {
      const dayNow = new Date().getDate() - 1
      const beginDate = formatDate(
        new Date(Date.now() - dayNow * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM-dd')
      this.dateValue = [beginDate, endDate]
      this.searchPost.s_date = beginDate
      this.searchPost.e_date = endDate
    }
    this.getStoreList()
    this.getLevelList()
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}

.box {
  .header {
    .white-panel;
    justify-content: flex-start;
    height: 90px;
    padding-top: 10px;

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }
}
</style>
