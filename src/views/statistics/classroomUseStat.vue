<template>
  <div>
    <Alert type="warning" show-icon style="marginBottom:10px">此报表仅从2022.11.08日开始统计</Alert>
    <div class="table-wrap">
      <header>
        <DatePicker
          type="month"
          :value="postData.month"
          :clearable="false"
          :options="dateOptions"
          placeholder="选择查询月份"
          @on-change="dateChange"></DatePicker>
        <Input
          v-model="postData.classroom_name"
          class="w200"
          clearable
          placeholder="输入教室名称查询"
          @on-enter="doSearch" />
        <Button type="success" @click="doSearch">搜索</Button>
      </header>
      <Table
        ref="table"
        :data="tableData"
        :columns="columns"
        disabledHover
        stripe></Table>
      <footer>
        <!-- <Button @click="exportCsv">导出Excel</Button>
        <Export ref="export">导出Excel</Export> -->
        <ExportButton url="/Web/Statistics/ClassroomUseStatistics" :data="exportPostData" />

        <Pager
          :total="total"
          :postData="postData"
          @on-change="pageChange"></Pager>
      </footer>
    </div>
  </div>
</template>

<script>
  import { formatDate } from 'utils';
  // import Export from 'src/components/Export';
  import ExportButton from 'components/form/ExportButton'
  import Pager from 'components/pager';

  export default {
    name: 'ClassroomUseStat', // 教室使用统计
    components: {
      // Export,
      ExportButton,
      Pager,
    },

    data() {
      const startDate = new Date('2022-10-01');
      return {
        dateOptions: {
          disabledDate(date) {
            return date.valueOf() > Date.now() || date <= startDate;
          }
        },
        postData: {
          month: formatDate(new Date(), 'yyyy-MM'),
          classroom_name: '',
          page_no: 1,
          page_size: 10,
        },
        tableData: [],
        total: 0,
        columns: [
          {
            key: 'classroom_name',
            title: '教室名称',
          },
          {
            key: 'use_times',
            title: '使用次数'
          },
          {
            key: 'use_days',
            title: '使用天数'
          },
          {
            key: 'use_average_day',
            title: '日均使用次数'
          },
          {
            key: 'most_class_name',
            title: '使用最多的课种'
          },
          {
            key: 'most_coach_name',
            title: '使用最多的教练'
          },
          {
            key: 'total_value',
            title: '成交价值',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  成交价值
                  <tooltip max-width="210">
                    <div slot="content">
                      使用该教室的课程的总计成交金额(包含使用储值卡、次卡、私教卡、泳教卡、现金)
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
          {
            key: 'day_average_value',
            title: '日均成交价值'
          },
        ],
      };
    },
    computed: {
      exportPostData() {
        const { postData, total } = this
        return {
          ...postData,
          page_no: 1,
          page_size: +total || 999
        }
      }
    },

    methods: {
      getList() {
        this.$service
          .post('/Web/Statistics/ClassroomUseStatistics', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // getExportData() {
      //   return this.$service
      //     .post('/Web/Statistics/ClassroomUseStatistics', {
      //       ...this.postData,
      //       page_no: 1,
      //       page_size: +this.total || 999
      //     })
      //     .then(res => {
      //       if (res.data.errorcode === 0) {
      //         return res.data.data.list
      //       } else {
      //         this.$Message.error(res.data.errormsg);
      //       }
      //     })
      //     .catch(err => {
      //       console.error(err);
      //     });
      // },

      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        if (this.postData.month !== info) {
          this.postData.month = info
          this.doSearch()
        }
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      // async exportCsv() {
      //   const exportData = await this.getExportData();
      //   this.$refs.export.export({
      //     filename: `教室使用统计${this.postData.month ? `(${this.postData.month})` : ''}`,
      //     columns: this.columns,
      //     data: exportData
      //   });
      // }
    }
  };
</script>

<style scoped lang="less">
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .icon-tips {
    padding-left: 5px;
    vertical-align:text-bottom;
  }
</style>
