<template>
  <div>
    <Alert type="warning" show-icon style="marginBottom:10px">此报表仅从2022.11.08日开始统计</Alert>
    <div class="table-wrap">
      <header>
        <DatePicker
          type="month"
          :value="postData.month"
          :options="dateOptions"
          placeholder="选择查询月份"
          @on-change="dateChange"></DatePicker>
        <!-- <Button type="success" @click="doSearch">搜索</Button> -->
      </header>
      <Table
        ref="table"
        :data="tableData"
        :columns="columns"
        disabledHover
        stripe></Table>
      <footer>
        <!-- <Button @click="exportCsv">导出Excel</Button>
        <Export ref="export">导出Excel</Export> -->
        <ExportButton url="/Web/Statistics/CourseEffectStatistics" :data="exportPostData" />
        <Pager
          :total="total"
          :postData="postData"
          @on-change="pageChange"></Pager>
      </footer>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { formatDate } from '@/utils/index'
  // import Export from 'src/components/Export';
  import ExportButton from 'components/form/ExportButton'
  import Pager from 'components/pager';

  export default {
    name: 'ClassEffectStat', // 课程效果统计
    components: {
      // Export,
      ExportButton,
      Pager,
    },

    data() {
      const startDate = new Date('2022-10-01');
      return {
        dateOptions: {
          disabledDate(date) {
            return date.valueOf() > Date.now() || date <= startDate;
          }
        },
        postData: {
          bus_id: '', // 可不传，默认当前登录场馆
          month: formatDate(new Date(), 'yyyy-MM'), // 月份，可不传
          page_no: 1,
          page_size: 10,
        },
        tableData: [],
        total: 0,
        columns: [
          {
            key: 'month',
            title: '月份',
            render: (_, { row }) => <router-link to={{ name: '课程效果统计明细', params: { month: row.month } }}>
              { row.month }
            </router-link>
          },
          {
            key: 'schedule_count',
            title: '排课节数'
          },
          {
            key: 'open_schedule_count',
            title: '开课节数'
          },
          {
            key: 'open_schedule_ratio',
            title: '开课率'
          },
          {
            key: 'class_mark_count',
            title: '预约次数'
          },
          {
            key: 'sign_count',
            title: '签到次数'
          },
          {
            key: 'sign_ratio',
            title: '签到率'
          },
          // {
          //   key: 'class_average_sign_count',
          //   title: '课均签到次数',
          //   minWidth: 12,
          // },
          {
            key: 'user_unique_count',
            title: '签到人数',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  签到人数
                  <tooltip max-width="200" transfer>
                    <div slot="content">
                      统计所有的签到人数，某会员参与签到多节课程只算一人
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
          {
            key: 'user_repeat_count',
            title: '复购人数',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  复购人数
                  <tooltip>
                    <div slot="content">
                      参与签到二次及以上的人数
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
          {
            key: 'user_repeat_ratio',
            title: '复购率'
          },
          {
            key: 'total_value',
            title: '成交价值',
            minWidth: 5,
            renderHeader: (_, { row }) => {
              return (
                <div>
                  成交价值
                  <tooltip max-width="210" placement="left-start" transfer>
                    <div slot="content">
                      会员使用储值卡、次卡、私教卡、泳教卡、现金支付的金额合计
                    </div>
                    <icon size="16" type="ios-help-circle" class="icon-tips" color="#f4a627" />
                  </tooltip>
                </div>
              );
            }
          },
        ],
      };
    },
    computed: {
      ...mapGetters(['busId']),
      exportPostData() {
        const { postData, total } = this
        return {
          ...postData,
          page_no: 1,
          page_size: +total || 999
        }
      }
    },

    created() {
      this.postData.bus_id = this.busId
    },

    methods: {
      getList() {
        this.$service
          .post('/Web/Statistics/CourseEffectStatistics', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      // getExportData() {
      //   return this.$service
      //     .post('/Web/Statistics/CourseEffectStatistics', {
      //       ...this.postData,
      //       page_no: 1,
      //       page_size: +this.total || 999
      //     })
      //     .then(res => {
      //       if (res.data.errorcode === 0) {
      //         return res.data.data.list
      //       } else {
      //         this.$Message.error(res.data.errormsg);
      //       }
      //     })
      //     .catch(err => {
      //       console.error(err);
      //     });
      // },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        if (this.postData.month !== info) {
          this.postData.month = info
          this.doSearch()
        }
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      // async exportCsv() {
      //   const exportData = await this.getExportData();
      //   this.$refs.export.export({
      //     filename: `课程效果统计${this.postData.month ? `(${this.postData.month})` : ''}`,
      //     columns: this.columns,
      //     data: exportData
      //   });
      // }
    }
  };
</script>

<style scoped lang="less">
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .icon-tips {
    padding-left: 5px;
    vertical-align:text-bottom;
  }
</style>
