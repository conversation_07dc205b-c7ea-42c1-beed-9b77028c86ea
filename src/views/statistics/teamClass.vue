<template>
  <div class="box">
    <header class="header">
      <Select v-model="searchPost.bus_id" @on-change="busChange" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <Date-picker
        v-model="dateValue"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        class="search-item"
      ></Date-picker>
      <Select v-model="searchPost.coach_id" placeholder="教练" class="search-item" clearable filterable>
        <Option
          v-for="item in coachList"
          :value="item.coach_id"
          :key="item.coach_id"
          >{{ item.coach_name }}{{item.deleted==1?'--（已删除）':''}}</Option
        >
      </Select>
      <Select v-model="searchPost.team_class_id" placeholder="班级" class="search-item" clearable filterable>
        <Option
          v-for="item in teamList"
          :value="item.id"
          :key="item.id"
          >{{ item.teamclass_name }}</Option
        >
      </Select>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData" border></Table>
    </main>
    <footer class="footer">
      <ExportButton :url="isSwim?'/Web/Statistics/team_class_swim_export':'/Web/Statistics/team_class_private_export'" :data="searchPost" />
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
    <div v-if="showDetailModal">
      <TeamDetailTable v-model="showDetailModal" :post="detailPost" :is-swim="isSwim"></TeamDetailTable>
    </div>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from '@/utils/index'
import { mapState } from 'vuex'
import { getcoachsInfo  } from '@/service/getData'
import ExportButton from 'components/form/ExportButton'
import TeamDetailTable from './components/TeamDetailTable'
export default {
  name: 'TeamClass',
  components: {
    Pager,
    TeamDetailTable,
    ExportButton
  },
  data() {
    return {
      isSwim: false,
      showDetailModal: false,
      dateValue: [],
      detailPost: {},
      searchPost: {
        begin_date: '',
        end_date: '',
        coach_id: '',
        team_class_id: '',
        bus_id: '',
        page_no: 1,
        page_size: 10
      },
      teamList: [],
      storeList: [],
      coachList: [],
      // main
      tableColumn: [
        {
          title: '头像',
          render(h, params) {
            return (<img src={params.row.coach_avatar} style="width:30px;height:30px"></img>)
          }
        },
        {
          key: 'coach_name',
          title: '教练'
        },
        {
          title: '班级',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.team_class_list.map(item => (<li>{item.team_class_name}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '课程名称',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.team_class_list.map(item => (<li>{item.curriculum_name}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '课程数',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.team_class_list.map(item => (<li>{item.teamclass_schedule_num}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '操作',
          render: (h, params) => {
            return (
              <ul class="inner-tbl">
                { params.row.team_class_list.map(item => 
                  (<li><a onClick={() => {
                    this.handleShowDetail(item.curriculum_id, params.row.coach_id)
                  }}>详情</a></li>)) 
                }
              </ul>
            )
          }
        }
      ],
      tableData: [],
      // footer
      count: 0,
    }
  },
  watch: {
    $route() {
      this.init()
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  methods: {
    init() {
      this.isSwim = this.$route.name === '泳教班课时统计' ? true : false
    },
    busChange() {
      this.getTeamClassList()
      this.getCoachList()
      this.handleSearchClick()
    },
    getTeamClassList() {
      this.searchPost.team_class_id = ''
      return this.$service
        .post('/Web/TeamclassPrivate/get_team_class', {
          bus_id: this.searchPost.bus_id,
          teamclass_type: this.isSwim ? 1 : 2
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.teamList = res.data.data
          }
        })
    },
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getCoachList() {
      this.searchPost.coach_id = ''
      getcoachsInfo(
        this.searchPost.bus_id,
        0, 
        this.isSwim ? 2 : 1
      ).then(res => {
        if (res.data.errorcode === 0) {
          this.coachList = res.data.data
        }
      })
    },
    getTableData() {
      if (!this.searchPost.bus_id) {
        this.$Message.error('请选择门店!')
        return false
      }
      if (!this.searchPost.begin_date || !this.searchPost.end_date) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .post(`/Web/Statistics/${this.isSwim?'team_class_swim_statistics':'team_class_private_statistics'}`, this.searchPost)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.count = res.data.data.count
          }
        })
    },
    handleShowDetail(id, coachId) {
      this.detailPost = {
        ...this.searchPost,
        coach_id: coachId,
        curriculum_id: id,
        page_no: 1,
        page_size: 10,
      }
      this.showDetailModal = true
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.searchPost.begin_date = ''
      this.searchPost.end_date = ''
      if (this.dateValue[0]) {
        this.searchPost.begin_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      }
      if (this.dateValue[1]) {
        this.searchPost.end_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      }
      this.getTableData()
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    this.searchPost.bus_id = this.busId
    this.searchPost = { ...this.searchPost, ...this.$route.params }
    if (this.searchPost.begin_date && this.searchPost.end_date) {
      this.dateValue = [this.searchPost.begin_date, this.searchPost.end_date]
    } else {
      const dayNow = new Date().getDate() - 1
      const beginDate = formatDate(
        new Date(Date.now() - dayNow * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM-dd')
      this.dateValue = [beginDate, endDate]
      this.searchPost.begin_date = beginDate
      this.searchPost.end_date = endDate
    }
    this.init()
    this.getStoreList().then(() => {
      this.getTeamClassList()
      this.getCoachList()
      this.getTableData()
    })
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}

.box {
  .header {
    .white-panel;
    justify-content: flex-start;
    height: 90px;
    padding-top: 10px;

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  /deep/ .buddy {
    .ivu-table td {
      padding: 0;

      .inner-tbl {
        li:first-child {
          border: none;
        }

        li {
          border-top: 1px solid #eeeeee;
          min-height: 30px;
          line-height: 30px;
        }
      }
    }
  }

  .overall {
    background-color: white;
    padding-top: 20px;

    .overall-box {
      display: flex;
      flex-direction: row;
    }

    .label {
      font-size: 14px;
      font-weight: bold;
      text-align: center;
    }

    .value {
      font-size: 16px;
      font-weight: bold;
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-left: 10px;
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }
}
</style>
