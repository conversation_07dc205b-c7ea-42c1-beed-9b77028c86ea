<template>
  <div class="private-lessons">
    <div class="header">
      <DatePickerWithButton @on-change="dateRangeChanged"></DatePickerWithButton>
      <Select v-model="selectedGroup" v-for="group in groupList" :key="group.group_id" filterable v-if="groupList">
        <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
        <Option v-for="coach in group.coach_list"
                style="padding-left: 30px"
                :key="coach.coach_id"
                :value="coach.coach_id">{{ coach.coach_name}}
        </Option>
        <template v-if="group.son">
          <Option-group v-for="group in group.son" :key="group.group_id">
            <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
            <Option style="padding-left: 30px"
                    v-for="coach in group.coach_list"
                    :key="coach.coach_id"
                    :value="coach.coach_id">{{ coach.coach_name }}
            </Option>
            <template v-if="group.son">
              <Option-group v-for="group in group.son" :key="group.group_id">
                <Option class="group" :value="'group' + group.group_id">{{ group.name }}</Option>
                <Option style="padding-left: 30px"
                        v-for="coach in group.coach_list"
                        :key="coach.coach_id"
                        :value="coach.coach_id">{{ coach.coach_name }}
                </Option>
              </Option-group>
            </template>
          </Option-group>
        </template>
      </Select>
    </div>
    <div class="total-stat" v-if="totalStat">
      <span>私教统计</span>
      <div class="stat">
        <h3>{{ totalStat.order_count }}</h3>
        <p>订单总数</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.added_class_count }}</h3>
        <p>新售课时</p>
      </div>
      <b></b>
      <div class="stat">
        <h3>{{ totalStat.consume_class_count }}</h3>
        <p>消耗课时</p>
      </div>
      <b></b>
      <div class="stat">
        <h3><span>￥</span>{{ totalStat.total_amount }}</h3>
        <p>业绩金额</p>
      </div>
      <b></b>
      <div class="stat">
        <h3><span>￥</span>{{ totalStat.once_amount }}</h3>
        <p>客单价</p>
      </div>
    </div>
    <table style="border-bottom: 1px solid #dcdcdc" border="1">
      <colgroup>
        <col class="vertical-text">
        <col style="width: auto">
        <col class="new-open-card">
        <col class="new-open-card">
        <col class="new-open-card">
        <col class="new-open-card">
        <col class="reopen-card">
        <col class="reopen-card">
        <col class="reopen-card">
        <col class="reopen-card">
      </colgroup>
      <thead>
      <tr>
        <th style="border: none"></th>
        <th style="border: none"></th>
        <th colspan="4">新开</th>
        <th colspan="4">续课</th>
      </tr>
      <tr class="sub-head">
        <td style="border: none"></td>
        <td style="border: none"></td>
        <td>订单数</td>
        <td>金额</td>
        <td>订单占比</td>
        <td>课时数</td>
        <td>订单数</td>
        <td>金额</td>
        <td>订单占比</td>
        <td>课时数</td>
      </tr>
      <tr class="total" v-if="listData.total">
        <td style="background-color: #fff; border-bottom: 1px solid #dcdcdc"></td>
        <td>总计</td>
        <td>{{ listData.total.newopen_card.order_count }}</td>
        <td>{{ listData.total.newopen_card.amount }}</td>
        <td>{{ listData.total.newopen_card.order_percent }}</td>
        <td>{{ listData.total.newopen_card.class_count }}</td>
        <td>{{ listData.total.renew_card.order_count }}</td>
        <td>{{ listData.total.renew_card.amount }}</td>
        <td>{{ listData.total.renew_card.order_percent }}</td>
        <td>{{ listData.total.renew_card.class_count }}</td>
      </tr>
      </thead>
    </table>
    <div class="table-container" v-if="listData.source_list || listData.coach_list || listData.card_type_list || listData.total">
      <table border="1">
        <colgroup>
          <col class="vertical-text">
          <col style="width: auto">
          <col class="new-open-card">
          <col class="new-open-card">
          <col class="new-open-card">
          <col class="new-open-card">
          <col class="reopen-card">
          <col class="reopen-card">
          <col class="reopen-card">
          <col class="reopen-card">
        </colgroup>
        <tr v-for="(item, index) in listData.source_list" v-if="listData.source_list" :key="item.id">
          <td v-if="index == 0" :rowspan="listData.source_list ? listData.source_list.length : 1">成交方式</td>
          <td>{{ item.name }}</td>
          <td>{{ item.newopen_card.order_count }}</td>
          <td>{{ item.newopen_card.amount }}</td>
          <td>{{ item.newopen_card.order_percent }}</td>
          <td>{{ item.newopen_card.class_count }}</td>
          <td>{{ item.renew_card.order_count }}</td>
          <td>{{ item.renew_card.amount }}</td>
          <td>{{ item.renew_card.order_percent }}</td>
          <td>{{ item.renew_card.class_count }}</td>
        </tr>
        <tr class="inter-laced"></tr>
        <tr v-for="(item, index) in listData.card_type_list" v-if="listData.card_type_list" :key="item.id">
          <td v-if="index == 0" :rowspan="listData.card_type_list ? listData.card_type_list.length : 1">课程类型</td>
          <td>{{ item.name }}</td>
          <td>{{ item.newopen_card.order_count }}</td>
          <td>{{ item.newopen_card.amount }}</td>
          <td>{{ item.newopen_card.order_percent }}</td>
          <td>{{ item.newopen_card.class_count }}</td>
          <td>{{ item.renew_card.order_count }}</td>
          <td>{{ item.renew_card.amount }}</td>
          <td>{{ item.renew_card.order_percent }}</td>
          <td>{{ item.renew_card.class_count }}</td>
        </tr>
        <tr class="inter-laced" v-if="listData.coach_list && listData.coach_list.length > 0"></tr>
        <tr v-for="(item, index) in listData.coach_list" :key="item.id" v-if="listData.coach_list && listData.coach_list.length > 0">
          <td v-if="index == 0" :rowspan="listData.coach_list.length">教练</td>
          <td>{{ item.name }}</td>
          <td>{{ item.newopen_card.order_count }}</td>
          <td>{{ item.newopen_card.amount }}</td>
          <td>{{ item.newopen_card.order_percent }}</td>
          <td>{{ item.newopen_card.class_count }}</td>
          <td>{{ item.renew_card.order_count }}</td>
          <td>{{ item.renew_card.amount }}</td>
          <td>{{ item.renew_card.order_percent }}</td>
          <td>{{ item.renew_card.class_count }}</td>
        </tr>
        <tr v-if="listData.coach_list && listData.coach_list.length === 0">
          <td colspan="10">无数据</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
  import Stat from '../../mixins/stat'
  
  export default {
    name: 'coachStat',
    mixins: [ Stat ],
    data() {
      return {
        coachId: '',
        groupUrl: '/Web/Statistics/coach_group_list',
        totalStatUrl: '/Web/Statistics/coach_statistics_data',
        dealTypeUrl: '/Web/Statistics/coach_source_card_type',
        marketerUrl: '/Web/Statistics/coach_marketer',
        listData: {
          total: null,
          source_list: null, // 按成交方式分
          card_type_list: null,
          coach_list: null
        },
      }
    },
    methods: {
      postData() {
        return {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          group_id: this.groupId,
          coach_id: this.coachId
        }
      },
      getMarketerStat() {
        let url = this.marketerUrl;
        let postData = this.postData();
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            let list = res.data.data.list;
            this.listData.coach_list = list.coach_list;
          }
        }).catch(err => {
          console.error(err)
        })
      }
    },
    watch: {
      selectedGroup(val) {
        let flag = val.slice(0, 5);
        if (flag === 'group') {
          this.groupId = val.replace(val.slice(0, 5), '');
          this.coachId = ''
        } else {
          this.coachId = val;
          this.groupId = ''
        }
        this.getData()
      },
    }
  }
</script>

<style lang="less">
  @border: 1px solid #dcdcdc;
  .private-lessons {
    background-color: #fff;
    border: @border;
    .header {
      display: flex;
      margin: 22px 19px;
      .group {
        font-size: 14px !important;
        font-weight: bold;
      }
      .ivu-select-item-selected, .ivu-select-item-selected:hover {
        color: #fff;
        background: rgba(45, 140, 240, 0.9);
      }
    }
    .total-stat {
      position: relative;
      width: 100%;
      box-sizing: border-box;
      padding: 0 40px;
      height: 150px;
      border-top: @border;
      border-bottom: @border;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      > span {
        position: absolute;
        left: 22px;
        top: 13px;
        color: #666;
        font-size: 14px;
      }
      .stat {
        h3 {
          font-size: 40px;
          color: #52a4ea;
          font-weight: normal;
          margin-top: 20px;
          span {
            font-size: 24px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
        }
      }
      > b {
        width: 1px;
        height: 30px;
        background-color: #ccc;
      }
    }
    .table-container {
      max-height: calc(~"100vh - 250px");
      overflow-y: scroll;
      position: relative;
    }
    .table-container::-webkit-scrollbar {
      display: none;
    }
    table {
      font-family: 'Microsoft YaHei', 'sans-serif';
      width: 100%;
      text-align: center;
      border-collapse: collapse;
      border-color: transparent;
      color: #333;
      font-size: 14px;
      colgroup {
        col {
          width: 10.9%;
        }
        .new-open-card {
          background-color: #f7fbfb;
        }
        .reopen-card {
          background-color: #dff3ff;
        }
      }
      tr {
        height: 35px;
        border-color: #dcdcdc;
      }
      .vertical-text {
        width: 28px;
      }
      tr.total {
        border: none;
        background-color: #52a4ea;
        color: #fff;
        td {
          border-color: #dcdcdc;
        }
      }
      thead {
        th {
          font-size: 17px;
          font-weight: normal;
        }
      }
      .fixed-table {
        position: fixed;
      }
      .sub-head {
        font-size: 16px;
      }
      .inter-laced {
        height: 30px;
      }
    }
    
    .ivu-select {
      width: 200px;
      margin-left: 30px;
    }
    .ivu-select-group-title {
      display: none;
    }
  }
</style>
