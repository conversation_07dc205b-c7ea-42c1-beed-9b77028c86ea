<template>
  <div class="box">
    <Alert type="warning" show-icon style="marginBottom:10px">可查看90天内数据，数据需要实时计算，需要时间5-10分钟，请耐心等待....</Alert>
    <header class="header">
      <div class="header-options">
        <Select v-model="searchPost.bus_id" class="search-item" filterable>
          <Option
            v-for="item in adminBusList"
            :value="item.id"
            :key="item.id"
            >{{ item.name }}</Option
          >
        </Select>
        <Date-picker
          v-model="dateValue"
          type="daterange"
          format="yyyy-MM-dd"
          placeholder="选择日期"
          class="search-item"
          :options="options"
          :editable="false"
        ></Date-picker>
        <Button type="success" @click="handleSearchClick">搜索</Button>
      </div>
    </header>
    <main class="buddy" v-show="tableData.length>0">
      <Table :columns="tableColumn" :data="tableData"></Table>
      <Row v-show="income.length === 3" class="row-line">
        <Col span="2" class="emphasize-value">
          会员卡收入
        </Col>
        <Col span="22">
          按权责发生制: <span class="emphasize-value">￥{{ income[0] }}</span><em>(过期卡算为收入)</em>, <span class="emphasize-value">￥{{ income[1] }}</span><em>(过期卡不算收入)</em>; 按收付实现制: <span class="emphasize-value">￥{{ income[2] }}</span>
        </Col>
      </Row>
      <Row v-show="debt.length === 2" class="row-line">
        <Col span="2" class="emphasize-value">
          会员卡负债
        </Col>
        <Col span="22">
          按权责发生制: <span class="emphasize-value">￥{{ debt[0] }}</span><em>(过期卡算为收入)</em>, <span class="emphasize-value">￥{{ debt[1] }}</span><em>(过期卡不算收入)</em>
        </Col>
      </Row>
    </main>
    <footer class="footer" v-show="tableData.length>0">
      <Button @click="dialogFlag=true">导出 Excel</Button>
    </footer>
    <Spin size="large" fix v-if="showLoading">
      <Icon type="ios-loading" size="50" class="demo-spin-icon-load"></Icon>
      <div class="loading-label">系统计算中，请耐心等待, 预计5-10分钟</div>
      <Button type="dashed" @click="showLoading=false">关闭</Button>
    </Spin>
    <Modal
      v-model="dialogFlag"
      title="导出">
      <Card class="card-box">
        <p class="card-label" @click="handleGoExportLog(1)">分摊总览导出</p>
      </Card>
      <Card class="card-box">
        <p class="card-label" @click="handleGoExportLog(2)">分摊卡种详情导出</p>
      </Card>
      <Card class="card-box">
        <p class="card-label" @click="handleGoExportLog(3)">分摊会员详情导出</p>
      </Card>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index.js';
import { mapState, mapGetters } from "vuex";

export default {
  data() {
    return {
      // header
      dateValue: [],
      options: {
        disabledDate(date) {
          const small = date && date.valueOf() < Date.now() - 86400000*91
          const big = date && date.valueOf() > Date.now() - 86400000
          return small || big
        }
      },
      searchPost: {
        bus_id: '',
        s_date: '',
        e_date: ''
      },
      storeList: [],
      // main
      tableColumn: [
        {
          key: 'card_type',
          title: '类型'
        },
        {
          key: 'start_value',
          title: '期初价值'
        },
        {
          key: 'add_value',
          title: '新增'
        },
        {
          key: 'consumption_value',
          title: '消耗'
        },
        {
          key: 'overdue_value',
          title: '过期'
        },
        {
          key: 'service_charge',
          title: '销卡服务费'
        },
        {
          key: 'end_value',
          renderHeader(h, params) {
            return (
              <Tooltip content="则期末结余=期初价值+新增价值-消费价值-过期价值-销卡服务费" placement="top-end" transfer>
                <strong>期末结余</strong>
                <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
              </Tooltip>
            )
          }
        }
      ],
      tableData: [],
      income: [],
      debt: [],
      timer: null,
      // other
      showLoading: false,
      dialogFlag: false
    }
  },
  computed: {
    ...mapState(['busId', 'adminId']),
    ...mapGetters(['adminBusList']),
  },
  methods: {
    handleGoExportLog(type) {
      this.$service.post('/Web/CostAllocation/export', {
        ...this.searchPost,
        type
      }).then(res => {
        if (res.data.errorcode === 0) {
          // this.$router.push({path: 'exportLogTable'})
          this.$Message.success({
            content:'导出任务运行中，请稍后到下载中心下载!',
            duration: 3
          })
          this.dialogFlag = false
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getStoreList() {
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      // return this.$service
      //   .get('/Web/Business/get_bus_list')
      //   .then(res => {
      //     if (res.data.errorcode === 0) {
      //       this.storeList = res.data.data.bus_list
      //     }
      //   })
    },
    getTableData() {
      return this.$service.post('/Web/CostAllocation/generate_report', this.searchPost).then(res => {
        if (res.data.errorcode === 0) {
          const { list, income, expend } = res.data.data
          if (Array.isArray(list)) {
            this.tableData = list
            this.income = income
            this.debt = expend
            this.showLoading = false
          } else if (list) {
            this.showLoading = true
            this.timer = setTimeout(() => {
              this.getTableData()
            }, 120000);
          }
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleSearchClick() {
      if (this.searchPost.bus_id === '') {
        this.$Message.error('请选择门店！')
        return false
      }
      if (this.dateValue[0] === '' || this.dateValue[1] === '') {
        this.$Message.error('请选择时间段！')
        return false
      }
      this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      this.tableData = []
      this.getTableData()

      // cache
      localStorage.setItem('remaining_value', JSON.stringify(this.searchPost))
      localStorage.setItem('remaining_value_expire', formatDate(new Date(), 'yyyy-MM-dd'))
      localStorage.setItem('remaining_value_administrator', this.adminId)
      // localStorage.setItem('remaining_value_store', this.busId)
    }
  },
  created() {
    this.searchPost.bus_id = this.busId
    this.getStoreList()

    const todayString = formatDate(new Date(), 'yyyy-MM-dd')
    const remainingValueExpire = localStorage.getItem('remaining_value_expire')
    const remainingValueAdministrator = localStorage.getItem('remaining_value_administrator')
    // const remainingValueStore = localStorage.getItem('remaining_value_store')
    if (todayString === remainingValueExpire
      && this.adminId === remainingValueAdministrator) {
      // && this.busId === remainingValueStore) {
      this.searchPost = JSON.parse(localStorage.getItem('remaining_value'))
      this.searchPost.bus_id = this.busId
      this.dateValue = [new Date(this.searchPost.s_date), new Date(this.searchPost.e_date)]
      this.handleSearchClick()
    } else {
      let today = new Date()
      const day = today.getDate()
      today.setDate(day - 1)
      let lastMonth = new Date()
      const month = lastMonth.getMonth()
      lastMonth.setMonth(month - 1)
      this.dateValue = [lastMonth, today]
      this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      localStorage.removeItem('remaining_value')
      localStorage.removeItem('remaining_value_expire')
      localStorage.removeItem('remaining_value_administrator')
      // localStorage.removeItem('remaining_value_store')
    }
  },
  destroyed () {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  }
}
</script>

<style lang="less" scoped>
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg);}
  50%  { transform: rotate(180deg);}
  to   { transform: rotate(360deg);}
}

.loading-label {
  font-size: 20px;
  margin: 10px;
}

.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
  height: 80px;
}

.box {

  .header {
    background-color: white;

    .header-options {
      .white-panel;
      justify-content: flex-start;
    }

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  .buddy {
    border-top: 1px solid #eeeeee;

    .row-line {
      margin: 20px 0;

      .emphasize-value {
        text-align: right;
        font-size: 16px;
        font-weight: bold;
        padding-right: 6px;
      }
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    border-top: 1px solid #eeeeee;
  }

}

.card-box {
  margin: 20px 0;
  cursor: pointer;

  .card-label {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
}
</style>
