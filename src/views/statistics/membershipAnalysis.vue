<template>
  <Card>
    <Row class="checkbox-controller-box">
      <Col span="23" offset="1">
        <Select v-model="selectBusId" @on-change="getAllDB" style="width:300px" filterable>
          <Option
            v-for="item in storeList"
            :value="item.id"
            :key="item.id"
            >{{ item.name }}</Option>
        </Select>
      </Col>
    </Row>
    <Row class="checkbox-controller-box">
      <Col span="2" offset="1">
      <Checkbox :indeterminate="ageFlag.isPart" v-model="ageFlag.isAll" @on-change="handleAgeCheckAll">年龄</Checkbox>
      </Col>
      <Col span="21">
      <Checkbox-group v-model="ageFlag.group" @on-change="handleAgeCheckGroup">
        <Checkbox label="14岁以下会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="15-20岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="21-30岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="31-40岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="41-50岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="51-60岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="61-70岁会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="71岁以上会员" class="checkbox-controller-age"></Checkbox>
        <Checkbox label="未设置年龄会员" class="checkbox-controller-age"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
    <Row class="checkbox-controller-box">
      <Col span="2" offset="1">
      <Checkbox :indeterminate="genderFlag.isPart" v-model="genderFlag.isAll" @on-change="handleGenderCheckAll">性别</Checkbox>
      </Col>
      <Col span="21">
      <Checkbox-group v-model="genderFlag.group" @on-change="handleGenderCheckGroup">
        <Checkbox label="男" class="checkbox-controller-gender"></Checkbox>
        <Checkbox label="女" class="checkbox-controller-gender"></Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
    <Row>
      <Col span="8">
      <div class="gender-ratio"></div>
      </Col>
      <Col span="16">
      <div class="age-ratio"></div>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1">
      <div class="tip-box">
        <div class="tip">
          <div class="tip-val">{{membershipTip.valid_count}}</div>
          <div class="tip-name">有效会员</div>
          <div class="tip-ratio">{{membershipTip.valid_proportion}}%</div>
        </div>
        <div class="tip">
          <div class="tip-val">{{membershipTip.overdue_count}}</div>
          <div class="tip-name">过期会员</div>
          <div class="tip-ratio">{{membershipTip.overdue_proportion}}%</div>
        </div>
        <div class="tip">
          <div class="tip-val">{{membershipTip.invalid_count}}</div>
          <div class="tip-name">未购卡会员</div>
          <div class="tip-ratio">{{membershipTip.invalid_proportion}}%</div>
        </div>
        <div class="tip-line"></div>
        <div class="tip">
          <div class="tip-val tip-orange">{{membershipTip.ordinary_count}}</div>
          <div class="tip-name">普通会员</div>
          <div class="tip-ratio tip-orange">{{membershipTip.ordinary_proportion}}%</div>
        </div>
        <div class="tip">
          <div class="tip-val tip-orange">{{membershipTip.pt_count}}</div>
          <div class="tip-name">私教会员</div>
          <div class="tip-ratio tip-orange">{{membershipTip.pt_proportion}}%</div>
        </div>
      </div>
      </Col>
    </Row>
    <Row>
      <Col span="7" offset="1">
      <div class="customer-original"></div>
      </Col>
      <Col span="8">
      <div class="pay-method"></div>
      </Col>
      <Col span="7">
      <div class="coach-method"></div>
      </Col>
    </Row>
    <Row>
      <Col span="22" offset="1">
      <div class="increased"></div>
      </Col>
    </Row>
  </Card>
</template>
<script>
  import echarts from 'echarts';
  import { mapState } from "vuex";

  const AGE_GROUP = ['14岁以下会员', '15-20岁会员', '21-30岁会员', '31-40岁会员', '41-50岁会员', '51-60岁会员', '61-70岁会员', '71岁以上会员', '未设置年龄会员'];
  const GENDER_GROUP = ['男', '女'];

  export default {
    data () {
      return {
        $ageChart: null,
        $genderChart: null,
        $customerChart: null,
        $payChart: null,
        $coachChart: null,
        $increasedChart: null,
        ageFlag: {
          isAll: true,
          isPart: false,
          group: AGE_GROUP
        },
        genderFlag: {
          isAll: true,
          isPart: false,
          group: GENDER_GROUP
        },
        ageOption: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '0',
            right: '6%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: AGE_GROUP,
              axisLabel: {
                interval: 0,
                rotate: -10,
                textStyle: {
                  color: '#666666',
                  fontSize: 12
                }
              },
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: {
                  color: '#666666',
                  fontSize: 12
                },
                formatter: '{value}'
              },
              minInterval: 1
            }
          ],
          series: [
            {
              name: '男',
              type: 'bar',
              stack: '勤鸟会员',
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
              color: ['#81cfef'],
            },
            {
              name: '女',
              type: 'bar',
              stack: '勤鸟会员',
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
              color: ['#f88397'],
            }
          ]
        },
        genderOption: {
          title: {
            text: '男女会员比例',
            x: 'center',
            y: 'bottom',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b}<br/>总数：{c} <br/> 占比：({d}%)"
          },
          series: [
            {
              name: '男女比列',
              type: 'pie',
              radius: '77%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: true
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: true
                }
              },
              color: ['#81cfef', '#f88397'],
              data: [
                { value: 0, name: '男会员' },
                { value: 0, name: '女会员' }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        membershipTip: {
          valid_count: 0,//有效会员数
          valid_proportion: 0,//有效会员百分比
          overdue_count: 0,//过期会员数
          overdue_proportion: 0,//过期会员百分比
          invalid_count: 0,//未购卡会员数
          invalid_proportion: 0,//未购卡会员百分比
          ordinary_count: 0,//普通会员数
          ordinary_proportion: 0,//普通会员占比
          pt_count: 0,//私教会员数
          pt_proportion: 0//私教会员占比
        },
        customerOption: {
          title: {
            text: '会员获客来源占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['朋友介绍'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function (name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b}<br/>人数：{c} <br/> 占比：({d}%)"
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: '朋友介绍' }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        payOption: {
          title: {
            text: '会员会籍成交方式占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['预约到店'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function (name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b}<br/>会员：{c} <br/> 占比：({d}%)"
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: '预约到店' }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        coachOption: {
          title: {
            text: '会员私教成交方式占比',
            x: 'center',
            y: 'top',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          legend: {
            x: 'center',
            y: 'bottom',
            data: ['POS'],
            itemWidth: 10,
            itemHeight: 10,
            tooltip: {
              show: true
            },
            formatter: function (name) {
              return name.substr(0, 10);
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: "{b}<br/>会员：{c} <br/> 占比：({d}%)"
          },
          series: [
            {
              name: '获客来源',
              type: 'pie',
              radius: '57%',
              center: ['50%', '50%'],
              label: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              lableLine: {
                normal: {
                  show: false
                },
                emphasis: {
                  show: false
                }
              },
              data: [
                { value: 0, name: 'POS' }
              ],
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        },
        increasedOption: {
          title: {
            text: '会员近30天新增情况',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          color: ['#2abfe1', '#fbe274'],
          legend: {
            data: ['新增资源', '已转化购卡'],
            x: 'right',
            textStyle: {
              color: '#666666',
              fontSize: 14
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} 人'
            },
            minInterval: 1
          },
          series: [
            {
              name: '新增资源',
              type: 'line',
              data: [0, 0, 0, 0, 0, 0, 0]
            },
            {
              name: '已转化购卡',
              type: 'line',
              data: [0, 0, 0, 0, 0, 0, 0]
            }
          ]
        },
        storeList: [],
        selectBusId: ''
      };
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      handleAgeCheckAll () {
        this.ageFlag.isPart = false;
        if (this.ageFlag.isAll) {
          this.ageFlag.group = AGE_GROUP;
        } else {
          this.ageFlag.group = [];
        }
        this.getAllDB();
      },
      handleGenderCheckAll () {
        this.genderFlag.isPart = false;
        if (this.genderFlag.isAll) {
          this.genderFlag.group = GENDER_GROUP;
        } else {
          this.genderFlag.group = [];
        }
        this.getAllDB();
      },
      handleAgeCheckGroup (data) {
        if (data.length === 9) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = true;
        } else if (data.length > 0) {
          this.ageFlag.isPart = true;
          this.ageFlag.isAll = false;
        } else if (data.length === 0) {
          this.ageFlag.isPart = false;
          this.ageFlag.isAll = false;
        }
        this.getAllDB();
      },
      handleGenderCheckGroup (data) {
        if (data.length === 2) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = true;
        } else if (data.length > 0) {
          this.genderFlag.isPart = true;
          this.genderFlag.isAll = false;
        } else if (data.length === 0) {
          this.genderFlag.isPart = false;
          this.genderFlag.isAll = false;
        }
        this.getAllDB();
      },
      getAgeVal () {
        let arr = [];
        this.ageFlag.group.forEach((item) => {
          const idx = AGE_GROUP.findIndex(val => (item === val));
          if (idx !== -1) {
            arr.push(idx + 1);
          }
        });
        return arr.sort();
      },
      getSexVal () {
        if (this.genderFlag.group.length === 2 || this.genderFlag.group.length === 0) {
          return 3;
        } else if (this.genderFlag.group[0] === '男') {
          return 1;
        } else {
          return 2;
        }
      },
      getAgeAndGenderRatioDB () {
        this.$service.post('/Web/Statistics/user_sex_age_info', {
          age: this.getAgeVal(),
          sex: this.getSexVal(),
          bus_id: this.selectBusId
        }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              const originOption = res.data.data;

              // 画男女饼图
              this.genderOption.series[0].data[0].value = originOption.sex1;
              this.genderOption.series[0].data[1].value = originOption.sex2;

              // 画年龄柱状图
              let maleArr = [];
              let femaleArr = [];
              let xField = [];
              let ageVal = this.getAgeVal();
              ageVal = ageVal.length === 0 ? [1, 2, 3, 4, 5, 6, 7, 8, 9] : ageVal;
              ageVal.forEach((item) => {
                if (item === 1) {
                  maleArr.push(originOption.age014sex1);
                  femaleArr.push(originOption.age014sex2);
                  xField.push(AGE_GROUP[0]);
                } else if (item === 2) {
                  maleArr.push(originOption.age1520sex1);
                  femaleArr.push(originOption.age1520sex2);
                  xField.push(AGE_GROUP[1]);
                } else if (item === 3) {
                  maleArr.push(originOption.age2130sex1);
                  femaleArr.push(originOption.age2130sex2);
                  xField.push(AGE_GROUP[2]);
                } else if (item === 4) {
                  maleArr.push(originOption.age3140sex1);
                  femaleArr.push(originOption.age3140sex2);
                  xField.push(AGE_GROUP[3]);
                } else if (item === 5) {
                  maleArr.push(originOption.age4150sex1);
                  femaleArr.push(originOption.age4150sex2);
                  xField.push(AGE_GROUP[4]);
                } else if (item === 6) {
                  maleArr.push(originOption.age5160sex1);
                  femaleArr.push(originOption.age5160sex2);
                  xField.push(AGE_GROUP[5]);
                } else if (item === 7) {
                  maleArr.push(originOption.age6170sex1);
                  femaleArr.push(originOption.age6170sex2);
                  xField.push(AGE_GROUP[6]);
                } else if (item === 8) {
                  maleArr.push(originOption.age71sex1);
                  femaleArr.push(originOption.age71sex2);
                  xField.push(AGE_GROUP[7]);
                } else if (item === 9) {
                  maleArr.push(originOption.not_set_age_sex1);
                  femaleArr.push(originOption.not_set_age_sex2);
                  xField.push(AGE_GROUP[8]);
                }
              });

              this.ageOption.series[0].data = maleArr;
              this.ageOption.series[1].data = femaleArr;
              this.ageOption.xAxis[0].data = xField;

              // const sexVal = this.getSexVal();
              // if (sexVal === 1 || sexVal === 2) {
              //   this.ageOption.tooltip.formatter = "{b0}<br/>{a0}: {c0}";
              // } else {
              //   this.ageOption.tooltip.formatter = "{b0}<br/>{a0}: {c0}<br/>{a1}: {c1}";
              // }
            } else {
              let msg = res.data.errormsg;
              msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
              this.$Notice.error({
                title: msg
              });
            }
          } else {
            this.$Notice.error({
              title: '网络不稳定，请摇一摇显示器再重试！',
              desc: `服务器返回代码：${res.status}`
            });
          }
        }).then(() => {
          this.$ageChart.setOption(this.ageOption);
          this.$genderChart.setOption(this.genderOption);
        });
      },
      getMembershipTipDB () {
        this.$service.post('/Web/Statistics/user_classify', {
          age: this.getAgeVal(),
          sex: this.getSexVal(),
          bus_id: this.selectBusId
        }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              this.membershipTip = res.data.data;
            } else {
              let msg = res.data.errormsg;
              msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
              this.$Notice.error({
                title: msg
              });
            }
          } else {
            this.$Notice.error({
              title: '网络不稳定，请摇一摇显示器再重试！',
              desc: `服务器返回代码：${res.status}`
            });
          }
        });
      },
      getCustomerDB () {
        this.$service.post('/Web/Statistics/source_info', {
          age: this.getAgeVal(),
          sex: this.getSexVal(),
          bus_id: this.selectBusId
        }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              const originOption = res.data.data;

              let custData = [];
              let custLegend = [];
              let payData = [];
              let payLegend = [];
              let coachData = [];
              let coachLegend = [];
              originOption.user.forEach((item, index) => {
                custData.push({
                  name: item.name,
                  value: item.count
                });
                custLegend.push(item.name);
              });
              originOption.membership.forEach((item, index) => {
                payData.push({
                  name: item.name,
                  value: item.count
                });
                payLegend.push(item.name);
              });
              originOption.pt.forEach((item, index) => {
                coachData.push({
                  name: item.name,
                  value: item.count
                });
                coachLegend.push(item.name);
              });

              this.customerOption.series[0].data = custData;
              this.customerOption.legend.data = custLegend;
              this.payOption.series[0].data = payData;
              this.payOption.legend.data = payLegend;
              this.coachOption.series[0].data = coachData;
              this.coachOption.legend.data = coachLegend;
            } else {
              let msg = res.data.errormsg;
              msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
              this.$Notice.error({
                title: msg
              });
            }
          } else {
            this.$Notice.error({
              title: '网络不稳定，请摇一摇显示器再重试！',
              desc: `服务器返回代码：${res.status}`
            });
          }
        }).then(() => {
          this.$customerChart.setOption(this.customerOption);
          this.$payChart.setOption(this.payOption);
          this.$coachChart.setOption(this.coachOption);
        });
      },
      getIncreasedDB () {
        this.$service.post('/Web/Statistics/get_bus_add_user', {
          time: 30,
          age: this.getAgeVal(),
          sex: this.getSexVal(),
          bus_id: this.selectBusId
        }).then(res => {
          if (res.status === 200) {
            if (res.data.errorcode == 0) {
              const originOption = res.data.data;

              let dateArr = [];
              let potentialArr = [];
              let membershipArr = [];
              originOption.forEach((item, index) => {
                dateArr.push(item.date);
                potentialArr.push(item.add_user_count);
                membershipArr.push(item.add_user_buycard_count);
              });

              this.increasedOption.xAxis.data = dateArr;
              this.increasedOption.series[0].data = potentialArr;
              this.increasedOption.series[1].data = membershipArr;
            } else {
              let msg = res.data.errormsg;
              msg = msg ? msg : '卧槽，谁把代码删了！(╯▔皿▔)╯';
              this.$Notice.error({
                title: msg
              });
            }
          } else {
            this.$Notice.error({
              title: '网络不稳定，请摇一摇显示器再重试！',
              desc: `服务器返回代码：${res.status}`
            });
          }
        }).then(() => {
          this.$increasedChart.setOption(this.increasedOption);
        });
      },
      getAllDB () {
        this.getAgeAndGenderRatioDB();
        this.getMembershipTipDB();
        this.getCustomerDB();
        this.getIncreasedDB();
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    created () {
      this.selectBusId = this.busId
      this.getStoreList()
    },
    mounted () {
      this.$ageChart = echarts.init(document.querySelector('.age-ratio'));
      this.$genderChart = echarts.init(document.querySelector('.gender-ratio'));
      this.$customerChart = echarts.init(document.querySelector('.customer-original'));
      this.$payChart = echarts.init(document.querySelector('.pay-method'));
      this.$coachChart = echarts.init(document.querySelector('.coach-method'));
      this.$increasedChart = echarts.init(document.querySelector('.increased'));
      this.getAllDB();
    }
  }
</script>
<style lang="less">
  @media screen and (min-width: 1900px) {
    .age-ratio,
    .gender-ratio,
    .increased {
      height: 350px;
    }
  
    .customer-original,
    .pay-method,
    .coach-method {
      height: 400px;
    }
  }
  
  @media screen and (min-width: 1300px) and (max-width: 1900px) {
    .age-ratio,
    .gender-ratio,
    .increased {
      height: 320px;
    }
  
    .customer-original,
    .pay-method,
    .coach-method {
      height: 366px;
    }
  }
  
  @media screen and (max-width: 1300px) {
    .age-ratio,
    .gender-ratio,
    .increased {
      height: 300px;
    }
  
    .customer-original,
    .pay-method,
    .coach-method {
      height: 343px;
    }
  }
  
  .checkbox-controller-box {
    height: 50px;
  
    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }
  }
  
  .age-ratio,
  .gender-ratio,
  .customer-original,
  .pay-method,
  .coach-method,
  .increased {
    width: 100%;
  }
  
  .tip-box {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    border: 1px dotted #dcdcdc;
    margin: 84px 0 24px 0;
  
    .tip {
      height: 165px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #52a4ea;
  
      .tip-val {
        font-size: 36px;
      }
  
      .tip-name {
        font-size: 18px;
        color: #666666;
      }
  
      .tip-ratio {
        font-size: 14px;
      }
  
      .tip-orange {
        color: #ff9c27;
      }
    }
  
    .tip-line {
      height: 92px;
      border-left: 1px dotted #dcdcdc;
    }
  }
  
  .increased {
    margin-top: 50px;
  }
</style>
