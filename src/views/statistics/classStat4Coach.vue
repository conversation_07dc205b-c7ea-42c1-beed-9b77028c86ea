<style lang="less">
  .class-stat-modal .coach-info {
    display: flex;
    align-items: center;
    padding-bottom: 24px;
    font-size: 14px;
    font-weight: bold;

    img {
      width: 70px;
      height: 70px;
      margin-right: 10px;
      border-radius: 50%;
      overflow: hidden;
    }
  }

  .class-stat {
    .no-padding {
      padding: 0;
    }
  }

  .group-select {
    width: 200px;
    margin-left: 30px;

    .group {
      font-size: 14px !important;
      font-weight: bold;
    }

    .ivu-select-item-selected,
    .ivu-select-item-selected:hover {
      color: #fff;
      background: rgba(45, 140, 240, 0.9);
    }

    .ivu-select-group-title {
      display: none;
    }
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>

<template>
  <div class="table-wrap class-stat">
    <header>
      <Select v-model="postData.bus_id" style="margin-right:20px;width:200px" @on-change="getCoachGroupList().then(getList)" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <DatePickerWithButton select="本月" :options="options" :days="initDateRange" @on-change="dateChange"></DatePickerWithButton>
      <Select class="group-select" style="margin-left: 100px; width: 240px" v-model="selectedGroup"
              @on-change="handleGymChanged" filterable v-if="groupList">
        <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
        <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
        <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

          <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
            class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
          <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

          <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
            <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
              class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
            <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
          </template>

        </template>
      </Select>
    </header>
    <Table ref="table" :data="tableData" class="avatar-zoom" :columns="columns" border></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export"></Export>
      <Pager :post-data="postData" :total="total" :history="false" @on-change="pageChange"></Pager>
    </footer>
    <Modal :title="modalTitle" v-model="showModal" width="800" class="class-stat-modal">
      <div class="coach-info"><img :src="modalCoachAvatar" alt="">
        <p>{{modalCoachName}}</p>
      </div>
      <Table :data="modalTableData" :columns="modalTableColumns"></Table>
      <div slot="footer">
        <Pager :post-data="modalPostData" :total="modalTotal" :history="false" @on-change="modalPageChange"></Pager>
      </div>
    </Modal>
  </div>
</template>

<script>
  // import { mapActions, mapGetters } from 'vuex';
  import { mapState } from "vuex";
  import DatePickerWithButton from 'components/picker/datePickerWithButton';
  import Pager from 'components/pager';
  import Export from 'src/components/Export';
  import COLS from './classStatColumns';

  const TYPE_TEXT = ['操课', '团课', '私教'];
  const TYPE_COLS = ['modalOpenClassCols', 'modalTeamClassCols', 'modalPrivateClassCols'];
  const TYPE_EXPORT_COLS = ['exportOpen', 'exportTeam', 'exportPrivate'];
  const LI_HEIGHT = 40;

  export default {
    name: 'classStat4Coach',
    components: {
      DatePickerWithButton,
      Pager,
      Export
    },
    data() {
      return {
        options: {
          disabledDate(date) {
            return date && date.getTime() > Date.now();
          }
        },
        initDateRange: [],
        groupList: null,
        groupId: '',
        coachId: '',
        selectedGroup: '',
        type: 0,
        modalTitle: '',
        modalCoachAvatar: '',
        modalCoachName: '',
        dateRange: [],
        postData: {
          page_no: 1,
          page_size: 10,
          s_date: '',
          e_date: '',
          type: 0,
          bus_id: '',
          coach_id: ''
        },
        modalPostData: {
          page_no: 1,
          page_size: 10,
          s_date: '',
          e_date: '',
          type: 0
        },
        total: 0,
        modalTotal: 0,
        showModal: false,
        modalTableData: [],
        exportColumns: [],
        exportTableData: [],
        modalTableColumns: [],
        tableData: [],
        columns: [
          {
            width: 100,
            title: '头像',
            className: 'avatar-wrap',
            render: (h, params) => {
              const item = params.row;
              return <img class="avatar" src={item.avatar}/>;
            }
          },
          {
            key: 'coach_name',
            width: 130,
            title: '教练'
          },
          {
            width: 80,
            title: '课程总数',
            render: (h, params) => {
              return (<div>{params.row.class_count}节</div>);
            }
          },
          {
            width: 220,
            title: '课程名称',
            className: 'no-padding',
            render: (h, params) => {
              const item = params.row;
              const rows = item.class_list.map((course, index, array) => {
                let liNum = 0;
                course.user_list.forEach(user => {
                  liNum += user.card_user_list.length;
                });
                const heightStyle = {
                  height: LI_HEIGHT*liNum + 'px',
                  borderBottom: array.length === (index+1) ? '' : '1px solid #e8eaec'
                };
                return (
                  <li style={heightStyle} class="center">{course.class_name || course.card_name} ({course.class_count}节)</li>
                );
              });
              return <ul>{rows}</ul>;
            }
          },
          {
            width: 150,
            title: '会员',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const liNum = user.card_user_list.length;
                  const heightStyle = {
                    height: LI_HEIGHT*liNum + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  return (<li style={heightStyle} class="center">{user.username}</li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            width: 60,
            title: '上课节数',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{parseInt(price.sign_num)}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            width: 100,
            title: '课程结算单价',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{price.price}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            title: '购买总节数',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{price.all_num}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            title: '购买总金额',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{price.amount}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            title: '当前剩余节数',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{price.last_num}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          },
          {
            title: '当前剩余金额',
            className: 'no-padding',
            render: (h, params) => {
              const rows = params.row.class_list.map((course, outIdx, courseArr) => {
                const userList = course.user_list.map((user, inIdx, userArr) => {
                  const heightStyle = {
                    height: LI_HEIGHT + 'px',
                    borderBottom: (courseArr.length === (outIdx+1) && userArr.length === (inIdx+1)) ? '' : '1px solid #e8eaec'
                  };
                  const priceList = user.card_user_list.map((price, smIdx, priceArr) => {
                    return (<li style={heightStyle} class="center">{price.last_amount}</li>);
                  });
                  return (<li><ul>{priceList}</ul></li>);
                });
                return (<ul>{userList}</ul>);
              });
              return (<ul><li>{rows}</li></ul>);
            }
          }
          // {
          //   title: '操作',
          //   render: (h, params) => {
          //     const item = params.row;
          //     return (
          //       <i-button
          //         type="text"
          //         onClick={() => {
          //           this.clickDetail(item.coach_id);
          //         }}>
          //         详情
          //       </i-button>
          //     );
          //   }
          // }
        ],
        storeList: []
      };
    },
    /* mounted() {
      const type = this.$route.query.type;
      // if (type != 0 && !type) {
      //   this.$router.replace({ path: '/stat/menus' });
      //   return;
      // }
      this.type = type || 0;
      this.$route.meta.breadText = `${TYPE_TEXT[this.type]}课时统计`;
      this.modalTitle = `${TYPE_TEXT[this.type]}上课详情`;
      this.modalTableColumns = COLS[TYPE_COLS[this.type]];
      this.postData.type = this.modalPostData.type = this.type;
      // if (!this.coachGroupList || !this.coachGroupList.length) {
      //   this.groupList = await this.getGroupDB();
      // } else {
      //   this.groupList = this.coachGroupList;
      // }
    }, */
    created () {
      const type = this.$route.query.type;
      // if (type != 0 && !type) {
      //   this.$router.replace({ path: '/stat/menus' });
      //   return;
      // }
      this.type = type || 0;
      this.$route.meta.breadText = `${TYPE_TEXT[this.type]}课时统计`;
      this.modalTitle = `${TYPE_TEXT[this.type]}上课详情`;
      this.modalTableColumns = COLS[TYPE_COLS[this.type]];
      this.postData.type = this.modalPostData.type = this.type;
      // if (!this.coachGroupList || !this.coachGroupList.length) {
      //   this.groupList = await this.getGroupDB();
      // } else {
      //   this.groupList = this.coachGroupList;
      // }

      const { beginTime, endTime, busId, coachId } = this.$route.params
      if (beginTime && endTime) {
        this.initDateRange = [beginTime, endTime]
      } else {
        this.initDateRange = null
      }
      if (busId) {
        this.postData.bus_id = busId
      } else {
        this.postData.bus_id = this.busId
      }
      this.postData.coach_id = coachId
      this.selectedGroup = coachId
      this.coachId = coachId
      this.getStoreList()
      this.getCoachGroupList()
    },
    computed: {
      // ...mapGetters(['coachGroupList'])
      ...mapState(['busId'])
    },
    methods: {
      // ...mapActions(['getGroupDB']),
      getCoachGroupList() {
        return this.$service.post('/Web/Statistics/coach_group_list', {
          bus_id: this.postData.bus_id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.groupList = res.data.data.list[0]
          }
        })
      },
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      handleGymChanged(val) {
        if (!val) {
          return false
        }
        if (val.indexOf('group') === -1) {
          this.groupId = '';
          this.coachId = val;
        } else {
          this.groupId = val.substring(5);
          this.coachId = '';
        }
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        const url = '/Web/Class/new_class_statistics';
        const [s_date, e_date] = this.dateRange;
        const { groupId: coach_group_id, coachId: coach_id } = this;
        this.postData = {
          ...this.postData,
          s_date,
          e_date,
          coach_group_id,
          coach_id
        };
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              let list = data.list;

              // set init data.
              const INIT_CARD_USER_LIST = [{sign_num: '', price: '', all_num: '', amount: '', last_num: '', last_amount: ''}];
              const INIT_USER_LIST = [{username: '未知会员', card_user_list: INIT_CARD_USER_LIST}];
              const INIT_CLASS_LIST = [{class_name: '未知课程', user_list: INIT_USER_LIST}];
              list.forEach(coach => {
                if (Array.isArray(coach.class_list) && coach.class_list.length > 0) {
                  coach.class_list.forEach(course => {
                    if (Array.isArray(course.user_list) && course.user_list.length > 0) {
                      course.user_list.forEach(user => {
                        if (Array.isArray(user.card_user_list) && user.card_user_list.length > 0) {

                        } else {
                          user.card_user_list = INIT_CARD_USER_LIST;
                        }
                      });
                    } else {
                      course.user_list = INIT_USER_LIST;
                    }
                  });
                } else {
                  coach.class_list = INIT_CLASS_LIST;
                }
              });

              this.tableData = list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      dateChange(dateRange) {
        this.dateRange = dateRange;
        this.getList();
      },
      getModalList() {
        const url = '/Web/Class/new_class_statistics_info';
        const [s_date, e_date] = this.dateRange;
        this.modalPostData.s_date = s_date;
        this.modalPostData.e_date = e_date;
        this.$service
          .post(url, this.modalPostData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.modalTotal = data.count;
              this.modalTableData = data.list;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      clickDetail(coach_id) {
        const coach = this.tableData.find(item => item.coach_id == coach_id);
        this.modalCoachAvatar = coach.avatar;
        this.modalCoachName = coach.coach_name;
        this.modalPostData.coach_id = coach_id;
        this.getModalList();
        this.showModal = true;
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      modalPageChange(postData) {
        this.modalPostData = { ...this.modalPostData, ...postData };
        this.getModalList();
      },
      getExportData() {
        const url = '/Web/Class/new_class_statistics_excel';
        const { groupId: coach_group_id, coachId: coach_id } = this;
        const postData = {
          s_date: this.dateRange[0],
          e_date: this.dateRange[1],
          type: this.type,
          coach_group_id,
          coach_id,
          bus_id: this.postData.bus_id
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.map(item => {
                return {
                  ...item,
                  class_name: item.card_name,
                  price: item.price,
                  sign_num: ~~item.sign_num,
                  last_num: ~~item.last_num
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const resData = await this.getExportData();
        this.exportColumns = COLS[TYPE_EXPORT_COLS[this.type]];
        const data = [];
        for (let coach of resData) {
          if (this.type == 2) {
            data.push(coach);
          } else {
            for (let course of coach.class_list) {
              data.push({
                coach_name: coach.coach_name,
                class_name: course.class_name,
                class_count: course.class_count,
                sign_time: course.class_info && course.class_info.map(time => time.sign_time),
                sign_number: course.class_info && course.class_info.map(time => time.sign_number),
                class_mark_number: course.class_info && course.class_info.map(time => time.class_mark_number)
              });
            }
          }
        }
        this.exportTableData = data;
        //导出
        this.$refs.export.export({
          columns: this.exportColumns,
          data: this.exportTableData,
          filename: `${this.$route.meta.breadText}(${this.dateRange[0]}~${this.dateRange[1]})`
        });
      }
    }
  };
</script>
