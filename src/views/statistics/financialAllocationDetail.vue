<template>
  <div class="table-wrap">
    <header>
      <DatePicker type="daterange" :value="dateRange" @on-change="handleDateRangeChanged" :options="options" placeholder="请选择时间区间"></DatePicker>
      <Select v-model="postData.bus_id" placeholder="选择场馆" @on-change="handleBusChanged" filterable>
        <Option v-for="option in merchantsBusList" :value="option.bus_id" :key="option.bus_id">{{ option.bus_name }}</Option>
      </Select>
      <Select v-model="postData.card_ids" placeholder="会员卡" clearable filterable multiple>
        <Option v-if="cardList.card_list" v-for="card in cardList.card_list" :value="card.card_id" :key="card.card_id">{{ card.card_name }}</Option>
        <Option v-if="cardList.private_card_list" v-for="card in cardList.private_card_list" :value="card.card_id" :key="card.card_id">{{ card.card_name }}</Option>
      </Select>
      <Button type="success" @click="getInfo">搜索</Button>
    </header>
    <Table width="100%" height="600" ref="table" :columns="columns" :data="tableData" v-if="tableData" stripe></Table>
    <footer v-if="tableData && tableData.length>0">
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
    </footer>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Export from 'src/components/Export';
import { formatDate } from 'utils';
  export default {
    name: 'financialAllocationDetail',
    props: {
      merchantsBusList: {
        type: [Array]
      }
    },
    data () {
      return {
        dataInfo: '',
        cardList: [],
        dateRange:this.getInitDay(),
        options: {
          disabledDate (date) {
            return date && date.valueOf() > Date.now() - 60*1000*60*24;
          }
        },
        postData: {
          detail: 1,
          s_date: this.getInitDay()[0],
          e_date: this.getInitDay()[1],
          bus_id: '',
          card_ids: []
        },
        columns: [
          {
            title: '类型',
            key: 'name'
          },
          {
            title: '会员卡',
            key: 'card_name',
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'card_name');
            }
          },
          {
            title: '期初价值',
            key: 'init',
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'init');
            }
          },
          {
            title: '新增价值',
            key: 'increase',
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'increase');
            }
          },
          {
            title: '消费价值',
            key: 'consumption',
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'consumption');
            }
          },
          {
            title: '过期价值',
            key: 'new_exp',
            renderHeader: (h, params) => {
              return (
                <div>
                  过期价值
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      从过期变为有效的会员卡的价值为负数
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            },
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'new_exp');
            }
          },
          // {
          //   title: '期末结余（过期算收入）',
          //   key: 'final',
          //   renderHeader: (h, params) => {
          //     return (
          //       <div>
          //         期末结余（过期算收入）
          //         <tooltip>
          //           <div slot="content" style={{ whiteSpace: 'normal' }}>
          //             期末结余=期初价值+新增价值-消费价值-过期价值
          //           </div>
          //           <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
          //         </tooltip>
          //       </div>
          //     );
          //   },
          //   render: (h, params) => {
          //     return this.cardForColumn(params.row.list, 'final');
          //   }
          // },
          {
            title: '期末结余（过期不算收入）',
            key: 'final2',
            renderHeader: (h, params) => {
              return (
                <div>
                  期末结余（过期不算收入）
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      若过期价值为正数，则期末结余=期初价值+新增价值-消费价值；若过期价值为负数，则期末结余=期初价值+新增价值-消费价值-过期价值
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            },
            render: (h, params) => {
              return this.cardForColumn(params.row.list, 'final2');
            }
          }
        ],
        resData: '',
        tableData: ''
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    methods: {
      handleBusChanged(val) {
        this.getCardList(val);
      },
      getCardList(busId) {
        this.$service.get('/Web/Member/get_card?member_list=1&belong_bus_id=' + busId || '').then(res => {
          if (res.data.errorcode == 0) {
            this.cardList = res.data.data;
            this.postData.card_ids = []
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getInitDay() {
        let first, last;
         let date = new Date();
        if (date.getDate() === 1) {
          first = new Date(date.getFullYear(), date.getMonth()-1, 1)
          let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
          last = new Date(new Date().getFullYear(), new Date().getMonth()-1, day);
        } else {
          first = new Date(new Date().setDate(1))
          last = new Date(new Date().getTime()- 24 * 60 * 60 * 1000)
        }
        return [ formatDate(first, 'yyyy-MM-dd'), formatDate(last, 'yyyy-MM-dd')]
      },
      cardForColumn(cards, row) {
        if (cards) {
          return (
            <ul class="row-ul">
              {cards.map(item => {
                return (
                  <li>
                    {item[row]}
                  </li>
                );
              })}
            </ul>
          );
        } else {
          return '';
        }
      },
      handleDateRangeChanged (val) {
        val = val || ['','']
        this.postData.s_date = val[0]
        this.postData.e_date = val[1]
      },
      getInfo() {
        let postData = JSON.parse(JSON.stringify(this.postData))
        postData.card_ids = postData.card_ids.join(',')
        this.$service.post('/Web/Statistics/financial_allocation', postData).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.resData = resData
            const cardGroup = resData.card_group
            this.tableData = [{
                name:'期限卡',
                list: cardGroup[0]
              },{
                name:'次卡',
                list: cardGroup[1]
              },{
                name:'储值卡',
                list: cardGroup[2]
              },{
                name:'私教课',
                list: cardGroup[3]
              }
            ]
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      formatKey(item, key) {
        if (item.list) {
          return item.list.map(item => {
             return item[key] || 0;
          });
        } else {
          return '';
        }
      },
      async exportCsv() {
        await this.getInfo();
        const data = this.tableData.map(item => {
              return Object.assign({}, item, {
                card_name: this.formatKey(item, 'card_name'),
                init: this.formatKey(item, 'init'),
                increase: this.formatKey(item, 'increase'),
                consumption: this.formatKey(item, 'consumption'),
                new_exp: this.formatKey(item, 'new_exp'),
                // final: this.formatKey(item, 'final'),
                final2: this.formatKey(item, 'final2')
              });
            });
        this.$refs.export.export({
          filename: `收入分摊明细(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns,
          data
        });
      }
    },
    created () {
      this.postData.bus_id = this.busId || ''
      this.getInfo();
      this.getCardList(this.busId)
    },
    components: {
      Export
    }
  }
</script>
<style lang="less" scoped>
.row-ul {
  li {
    height: 35px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }

  .red {
    color: #d9534f;
  }

  .green {
    color: #5cb85c;
  }
}
.footer-errors {
  margin-left: 15px;
  color: red;
}
.cen-text {
  padding: 10px 50px;
  line-height: 35px;
  font-size: 14px;
  border-bottom: 1px solid #e9eaec;
}
</style>

