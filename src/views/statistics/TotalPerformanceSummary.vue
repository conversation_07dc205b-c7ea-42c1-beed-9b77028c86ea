<template>
  <div class="box">
    <header class="header">
      <Select v-model="searchPost.bus_id" class="search-item" clearable filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Select v-model="searchPost.level_id" class="search-item" clearable filterable>
        <Option
          v-for="item in levelList"
          :value="item.level_id"
          :key="item.level_name"
          >{{ item.level_name }}</Option
        >
      </Select>
      <Date-picker
        v-model="dateValue"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        class="search-item"
        :clearable="false"
      ></Date-picker>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData"></Table>
    </main>
    <footer class="footer">
      <ExportButton url="/Web/Statistics/bus_statistics" :data="searchPost" />
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import ExportButton from 'components/form/ExportButton'
import { formatDate } from '@/utils/index'

export default {
  components: {
    Pager,
    ExportButton
  },
  data() {
    const gogogoFireTheHome = (url, postBuddy, callback) => {
      this.$service.post(url, postBuddy).then(res => {
        if (res.data.errorcode === 0) {
          callback(postBuddy)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
    return {
      // header
      dateValue: [],
      searchPost: {
        s_date: '',
        e_date: '',
        level_id: '',
        bus_id: '',
        page_no: 1,
        page_size: 10
      },
      storeList: [],
      levelList: [],
      // main
      tableColumn: [
        {
          key: 'name',
          title: '门店'
        },
        {
          key: 'level_name',
          title: '门店等级'
        },
        {
          title: '会籍业绩',
          render: (h, params) => {
            return (
              <a onClick={() => {
                gogogoFireTheHome(
                  '/Web/Statistics/bus_statistics_detail_ms', 
                  { ...this.searchPost, bus_id: params.row.id, type: 1},
                  paramsData => {
                    this.$router.push({
                      name: '会籍业绩汇总',
                      params: paramsData
                    })
                  } 
                )
              }}>
                {params.row.ms_total}
              </a>
            )
          }
        },
        {
          title: '私教业绩',
          render: (h, params) => {
            return (
              <a onClick={() => {
                gogogoFireTheHome(
                  '/Web/Statistics/bus_statistics_detail_pt', 
                  { ...this.searchPost, bus_id: params.row.id, type: 2},
                  paramsData => {
                    this.$router.push({
                      name: '私教业绩汇总',
                      params: paramsData
                    })
                  } 
                )
              }}>
                {params.row.pt_total}
              </a>
            )
          }
        },
        {
          title: '泳教业绩',
          render: (h, params) => {
            return (
              <a onClick={() => {
                gogogoFireTheHome(
                  '/Web/Statistics/bus_statistics_detail_swim', 
                  { ...this.searchPost, bus_id: params.row.id, type: 3},
                  paramsData => {
                    this.$router.push({
                      name: '泳教业绩汇总',
                      params: paramsData
                    })
                  } 
                )
              }}>
                {params.row.swim_total}
              </a>
            )
          }
        },
        {
          key: 'bus_total',
          title: '门店总流水',
          render: (h, params) => {
            return (
              <a onClick={() => {
                gogogoFireTheHome(
                  '/Web/Statistics/bus_fincl_flow', 
                  { ...this.searchPost, bus_id: params.row.id },
                  paramsData => {
                    this.$router.push({
                      name: '业务流水汇总',
                      params: paramsData
                    })
                  } 
                )
              }}>
                {params.row.bus_total}
              </a>
            )
          }
        }
      ],
      tableData: [],
      // footer
      count: 100
    }
  },
  methods: {
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getLevelList() {
      this.$service.get('/Web/Statistics/get_bus_level_list').then(res => {
        if (res.data.errorcode === 0) {
          this.levelList = res.data.data
        }
      })
    },
    getTableData() {
      if (!this.searchPost.s_date || !this.searchPost.e_date) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .post('/Web/Statistics/bus_statistics', this.searchPost)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.count = res.data.data.count
          }
        })
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.searchPost.s_date = ''
      this.searchPost.e_date = ''
      if (this.dateValue[0]) {
        this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      }
      if (this.dateValue[1]) {
        this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      }
      this.getTableData()
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    this.searchPost = { ...this.searchPost, ...this.$route.params }
    if (this.searchPost.s_date && this.searchPost.e_date) {
      this.dateValue = [this.searchPost.s_date, this.searchPost.e_date]
    } else {
      const dayNow = new Date().getDate() - 1
      const beginDate = formatDate(
        new Date(Date.now() - dayNow * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM-dd')
      this.dateValue = [beginDate, endDate]
      this.searchPost.s_date = beginDate
      this.searchPost.e_date = endDate
    }
    this.getStoreList()
    this.getLevelList()
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}

.box {
  .header {
    .white-panel;
    justify-content: flex-start;
    height: 90px;
    padding-top: 10px;

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }
}
</style>
