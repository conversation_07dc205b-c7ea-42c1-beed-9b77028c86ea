<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" @on-change="$store.dispatch('getmemberCardList', postData.bus_id)" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <Input class="w120" v-model="postData.search" placeholder="会员名称" />
      <Select v-model="postData.type" abelInValue placeholder="统计方式" @on-change="onTypeChange">
        <Option :value="1">按会员统计</Option>
        <Option :value="2">按会员卡统计</Option>
      </Select>
      <Date-picker placeholder="请选择日期区间" :value="createDateRange" @on-change="onCreateDateChange" type="daterange" :editable="false" :clearable="false"></Date-picker>
      <Select v-model="postData.card_id" class="w120" filterable placeholder="会员卡" clearable>
        <Option v-for="item in memberCardList.card_list" :value="item.card_id" :key="item.card_id">{{ item.card_name }}</Option>
      </Select>
      <div class="input-group">
        <div class="input-before">
          <span class="before-label">单次在场时长>=</span>
        </div>
        <div class="input-after">
          <div class="min-wrap">
            <InputNumber class="min-input" :min="0" v-model="postData.duration"></InputNumber>
            <span>分钟</span>
          </div>
        </div>
      </div>
      <div class="input-group">
        <div class="input-before">
          <Select v-model="postData.search_type" class="w100">
            <Option :value="1">训练次数</Option>
            <Option :value="2">训练天数</Option>
          </Select>
        </div>
        <div class="input-after">
          <div class="min-wrap">
            <InputNumber class="min-input" :min="0" v-model="postData.s_num"></InputNumber>
            <span>~</span>
            <InputNumber class="min-input" :min="1" v-model="postData.e_num"></InputNumber>
            <span>{{ postData.search_type === 1 ? '次' : '天' }}</span>
          </div>
        </div>
      </div>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { formatDate } from 'utils';
  import Export from 'src/components/Export';
  import { mapGetters } from "vuex";
  let signListColumns = [
    {
      key: 'username',
      title: '会员'
    },
    {
      key: 'num',
      title: '训练次数'
    },
    {
      key: 'day',
      title: '训练天数'
    },
    {
      key: 'ave_duration',
      title: '平均时长',
      renderHeader: (h, params) => {
        return (
          <div>
            平均时长
            <tooltip>
              <div slot="content" style={{ whiteSpace: 'normal' }}>
                根据会员签到时间和离场时间计算，无离场时间计为60分钟。
              </div>
              <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
            </tooltip>
          </div>
        );
      }
    },
     {
      key: 'name',
      title: '跟进会籍'
    },
  ]
  export default {
    name: 'signList',
    components: {
      Pager,
      Export
    },
    data() {
      return {
        createDateRange: [new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), new Date()],
        postData: {
          bus_id: '',
          search: '',
          duration: null,
          s_date: formatDate(new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), "yyyy-MM-dd"),
          e_date: formatDate(new Date(), "yyyy-MM-dd"),
          s_num: null,
          e_num: null,
          type: 1,
          search_type: 1,
          page_no: 1,
          page_size: 10
        },
        tableData: [],
        total: '',
        columns: signListColumns.slice(),
        storeList: []
      };
    },
    computed: {
      ...mapGetters(["memberCardList", 'busId'])
    },
    created() {
      !this.memberCardList && this.$store.dispatch("getmemberCardList")
      this.postData.bus_id = this.busId
      this.getStoreList()
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      onTypeChange(val) {
        if (val === 2) {
          this.columns.splice(1, 0, {
            key: 'card_name',
            title: '训练用卡'
          });
        } else {
          this.columns = signListColumns.slice()
        }
      },
      onCreateDateChange(val) {
        if (!val[0]) {
          this.postData.s_date = "";
          this.postData.e_date = "";
          return false;
        }
        this.postData.s_date = formatDate(
          new Date(val[0]),
          "yyyy-MM-dd"
        );
        this.postData.e_date = formatDate(
          new Date(val[1]),
          "yyyy-MM-dd"
        );
      },
      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      dateChange(info) {
        console.log(info)
        this.postData.m_date = info
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        this.$service
          .post('/Web/Statistics/user_sign_statistics', this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        return this.$service
          .post('/Web/Statistics/user_sign_statistics', { ...this.postData, ...{ page_size: this.total, page_no: 1 } })
          .then(res => {
            if (res.data.errorcode === 0) {
              if(Array.isArray(res.data.data.list)) {
                res.data.data.list.forEach(v => {
                  v.name = v.name || ''
                });
              }
              return res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const exportData = await this.getExportData();
        this.$refs.export.export({
          filename: `训练次数统计(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns,
          data: exportData
        });
      }
    }
  };
</script>

<style scoped lang="less">
  .select {
    width: 120px;
  }
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
</style>
