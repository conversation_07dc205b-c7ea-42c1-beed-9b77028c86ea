<template>
  <div class="table-wrap">
    <header>
      <DatePicker type="daterange" :value="dateRange" @on-change="handleDateRangeChanged" :options="options" placeholder="请选择时间区间"></DatePicker>
      <Select v-model="postData.bus_id" placeholder="选择场馆" filterable>
        <Option v-for="option in merchantsBusList" :value="option.bus_id" :key="option.bus_id">{{ option.bus_name }}</Option>
      </Select>
      <Button type="success" @click="getInfo">搜索</Button>
    </header>
    <Table width="100%" ref="table1" :columns="columns1" :data="tableData1" v-if="tableData1" disabledHover stripe ></Table>
    <div class="cen-text" v-if="resData">
<strong>会员卡收入</strong>  按权责发生制：￥{{resData.a}}（过期卡算为收入)  ，￥{{resData.b}}（过期卡不算收入）按收付实现制：￥{{resData.c}} <br />
<strong>会员卡负债</strong>       按权责发生制：￥{{resData.d}}(过期卡算为收入)  ，￥{{resData.e}}（过期卡不算收入）
    </div>
    <Table width="100%" ref="table2" :columns="columns2" :data="tableData2" v-if="tableData2" disabledHover stripe></Table>
    <footer v-if="tableData1 && tableData1.length>0">
      <Button @click="exportCsv">导出Excel</Button>
      <div class="footer-errors">只支持查询2019-04-26日及之后的数据 <br />每日统计更新一次数据</div>
      <Export ref="export">导出Excel</Export>
    </footer>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import Export from 'src/components/Export';
import { formatDate } from 'utils';
  export default {
    name: 'financialAllocationList',
    props: {
      merchantsBusList: {
        type: [Array]
      }
    },
    data () {
      return {
        dataInfo: '',
        dateRange: this.getInitDay(),
        options: {
          disabledDate (date) {
              return date && date.valueOf() > Date.now() - 60*1000*60*24;
          }
        },
        postData: {
          s_date: this.getInitDay()[0],
          e_date: this.getInitDay()[1],
          bus_id: ''
        },
        columns1: [
          {
            title: '类型',
            key: 'name'
          },
          {
            title: '期初价值',
            key: 'initial'
          },
          {
            title: '新增价值',
            key: 'increase'
          },
          {
            title: '消费价值',
            key: 'consumption'
          },
          {
            title: '过期价值',
            renderHeader: (h, params) => {
              return (
                <div>
                  过期价值
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      从过期变为有效的会员卡的价值为负数
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            },
            key: 'exp_val'
          },
          // {
          //   title: '期末结余（过期算收入）',
          //   renderHeader: (h, params) => {
          //     return (
          //       <div>
          //         期末结余（过期算收入）
          //         <tooltip>
          //           <div slot="content" style={{ whiteSpace: 'normal' }}>
          //             期末结余=期初价值+新增价值-消费价值-过期价值
          //           </div>
          //           <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
          //         </tooltip>
          //       </div>
          //     );
          //   },
          //   key: 'final'
          // },
          {
            title: '期末结余（过期不算收入）',
            renderHeader: (h, params) => {
              return (
                <div>
                  期末结余（过期不算收入）
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      若过期价值为正数，则期末结余=期初价值+新增价值-消费价值；若过期价值为负数，则期末结余=期初价值+新增价值-消费价值-过期价值
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            },
            key: 'final2'
          }
        ],
        columns2: [
          {
            title: '过期价值统计',
            key: 'name'
          },
          {
            title: '期初价值',
            key: 'initial'
          },
          {
            title: '新增过期',
            key: 'exp_val',
            renderHeader: (h, params) => {
              return (
                <div>
                  新增过期
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      从过期变为有效的会员卡的价值为负数
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            }
          },
          {
            title: '期末结余',
            key: 'final',
            renderHeader: (h, params) => {
              return (
                <div>
                  期末结余
                  <tooltip>
                    <div slot="content" style={{ whiteSpace: 'normal' }}>
                      期末结余=期初价值+新增过期
                    </div>
                    <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                  </tooltip>
                </div>
              );
            }
          }
        ],
        resData: '',
        tableData1: '',
        tableData2: ''
      }
    },
    computed: {
      ...mapState(['busId'])
    },
    methods: {
      getInitDay() {
        let first, last;
         let date = new Date();
        if (date.getDate() === 1) {
          first = new Date(date.getFullYear(), date.getMonth()-1, 1)
          let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate();
          last = new Date(new Date().getFullYear(), new Date().getMonth()-1, day);
        } else {
          first = new Date(new Date().setDate(1))
          last = new Date(new Date().getTime()- 24 * 60 * 60 * 1000)
        }
        return [ formatDate(first, 'yyyy-MM-dd'), formatDate(last, 'yyyy-MM-dd')]
      },
      timeToDate(time) {
        return formatDate(new Date(time), 'yyyy-MM-dd');
      },
      handleDateRangeChanged (val) {
        val = val || ['','']
        this.postData.s_date = val[0]
        this.postData.e_date = val[1]
      },
      getInfo() {
        this.$service.post('/Web/Statistics/financial_allocation', { ...this.postData }).then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            this.resData = resData
            this.tableData1 = resData.table1
            this.tableData2 = resData.table2
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      async exportCsv() {
        await this.getInfo();
        this.$refs.export.export({
          filename: `收入分摊汇总1(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns1,
          data: this.tableData1
        });
        this.$refs.export.export({
          filename: `收入分摊汇总2(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns2,
          data: this.tableData2
        });
      }
    },
    created () {
      this.postData.bus_id = this.busId || ''
      this.getInfo();
    },
    components: {
      Export
    }
  }
</script>
<style scoped>
.footer-errors {
  margin-left: 15px;
  color: red;
}
.cen-text {
  padding: 10px 50px;
  line-height: 35px;
  font-size: 14px;
  border-bottom: 1px solid #e9eaec;
}
</style>

