<template>
  <div class="table-wrap">
    <header>
      <Select v-model="postData.bus_id" placeholder="选择场馆" @on-change="handleBusChanged" filterable>
        <Option v-for="option in adminBusList" :value="option.id" :key="option.id">{{ option.name }}</Option>
      </Select>
      <DatePicker type="daterange" :value="dateRange" @on-change="handleDateRangeChanged" :options="options" placeholder="请选择时间区间" :clearable="false"></DatePicker>
      <Select v-model="postData.statistics_type" placeholder="统计维度" @on-change="typeChange">
        <Option :value="1">按卡种统计</Option>
        <Option :value="2">按会员统计</Option>
      </Select>
      <Select v-model="postData.card_id" placeholder="储值卡种" clearable filterable>
        <Option v-for="card in cardList" :value="card.id" :key="card.id">{{ card.name }}</Option>
      </Select>

      <Input  v-show="postData.statistics_type===2" style="width: 180px" class="notdo-enter" v-model="postData.search" placeholder="会员名称"
               @on-enter="searchInfo"/>
      <Button type="success" @click="searchInfo">搜索</Button>
    </header>
    <Table width="100%" ref="table" :columns="columns" :data="tableData" v-if="tableData" stripe></Table>
     <footer v-if="tableData && tableData.length>0">
      <div>
        <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Button @click="editTableFlag = true" style="margin-left: 20px;">更改表格样式</Button>
      </div>
      <Page :total="+totalCount" :current.sync="postData.page_no" placement="top" show-total show-sizer @on-change="getInfo()" @on-page-size-change="pageSizeChanged" />
    </footer>
    <Modal v-model="editTableFlag" title="更改表格样式" width="800" @on-ok="handleSaveClick">
        <Table :columns="defaultRowColumn" :data="showRowData"></Table>
        <Button type="primary" style="margin: 20px 0;" @click="handleAddRowClick">添加</Button>
    </Modal>
  </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex'
import Export from 'src/components/Export'
import { formatDate } from 'utils'
export default {
  name: 'DepositCardDetail',
  data() {
    return {
      dataInfo: '',
      editTableFlag: false,
      totalCount: 0,
      merchantsBusList: [],
      cardList: [],
      groupList: [],
      cardColumns: [],
      dateRange: this.getInitDay(),
      options: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now()
        }
      },
      showRowData: [],
      defaultRowColumn: [
        {
          title: '类型名称',
          key: 'name',
          render: (h, params) => {
            const handleUpdateTypeName = event => {
              params.row.group_name = event.target.value
            }
            if (params.row.editFlag) {
              return (
                <div>
                  <Input
                    value={params.row.group_name}
                    onOn-change={handleUpdateTypeName}
                  />
                </div>
              )
            } else {
              return (
                <div>
                  <label>{params.row.group_name}</label>
                </div>
              )
            }
          }
        },
        {
          title: '统计项',
          key: 'category',
          render: (h, params) => {
            if (params.row.editFlag) {
              const options = this.groupList.map(item => {
                return (
                  <Option
                    value={item.id}
                    key={item.id}
                    disabled={this.selectedIds.indexOf(item.id) !== -1}
                  >
                    {item.name}
                  </Option>
                )
              })
              const selectedCategoryIds = params.row.ids
              const handleUpdateCategory = idxArr => {
                params.row.ids = []
                let categoryArr = []
                idxArr.forEach(id => {
                  const item = this.groupList.find(row => row.id == id)
                  params.row.ids.push(item.id)
                  categoryArr.push(item.name)
                })
                params.row.description = categoryArr.join('、')
                this.showRowData[params.index] = params.row
              }
              return (
                <div>
                  <Select
                    value={selectedCategoryIds}
                    onOn-change={handleUpdateCategory}
                    multiple
                    transfer
                  >
                    {options}
                  </Select>
                </div>
              )
            } else {
              return (
                <div>
                  <label>{params.row.description}</label>
                </div>
              )
            }
          }
        },
        {
          title: '操作',
          key: 'option',
          width: 150,
          align: 'center',
          render: (h, params) => {
            const btnName = !!params.row.editFlag ? '完成' : '编辑'
            const handleEditClick = () => {
              if (params.row.editFlag) {
                if (!params.row.group_name) {
                  this.$Message.error('请输入类型名称！')
                  return false
                }
                if (
                  Array.isArray(params.row.ids) &&
                  params.row.ids.length === 0
                ) {
                  this.$Message.error('请选择统计项！')
                  return false
                }
              }
              params.row.editFlag = !params.row.editFlag
              this.showRowData[params.index] = params.row
            }
            const handleDeleteRowClick = () => {
              this.showRowData.splice(params.index, 1)
            }
            return (
              <div>
                <Button type="primary" size="small" onClick={handleEditClick}>
                  {btnName}
                </Button>
                <Button
                  type="error"
                  size="small"
                  style="margin-left: 5px;"
                  onClick={handleDeleteRowClick}
                >
                  删除
                </Button>
              </div>
            )
          }
        }
      ],
      postData: {
        statistics_type: 1,
        start_time: this.getInitDay()[0],
        end_time: this.getInitDay()[1],
        bus_id: '',
        card_id: '',
        search: '',
        page_no: 1,
        page_size: 10,
        user_id: ''
      },
      columns: [],
      tableData: []
    }
  },
  computed: {
    ...mapState(['busId']),
    ...mapGetters(['adminBusList']),
    selectedIds() {
      let ids = []
      this.showRowData.forEach(item => {
        ids = ids.concat(item.ids)
      })
      return ids
    }
  },
  methods: {
    getRowData() {
      return this.$service
        .post('/Web/DepositCardStatistics/getSummaryGroupList', {bus_id: this.postData.bus_id})
        .then(res => {
          if (res.data.errorcode == 0) {
            let cuCardColumns = []
            let groupList = res.data.data
            groupList.forEach(item => {
              item.editFlag = false
              cuCardColumns.push({
                title: item.group_name,
                key: item.group_id
              })
            })
            this.cardColumns = cuCardColumns
            this.showRowData = groupList
            this.typeChange(this.postData.type)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleAddRowClick() {
      this.showRowData.push({
        group_id: '',
        group_name: '',
        ids: [],
        editFlag: true
      })
    },
    handleSaveClick() {
      return this.$service
        .post('/Web/DepositCardStatistics/setGroup', {
          bus_id: this.postData.bus_id,
          group_list: this.showRowData
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.info('表格样式编辑成功！')
            this.getRowData()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getMerchantsBusList() {
      !this.adminBusList && this.$store.dispatch('getAdminBusList');
      // this.$service.post('/Web/MemberList/getMerchantsBusList').then(res => {
      //   if (res.data.errorcode === 0) {
      //     this.merchantsBusList = res.data.data
      //   } else {
      //     this.$Message.success(res.data.errormsg)
      //   }
      // })
    },
    pageSizeChanged(size) {
      this.postData.page_size = size
      this.searchInfo()
    },
    typeChange(val) {
      this.columns = this.cardColumns.slice()
      if (val === 2) {
        this.columns.unshift(
          {
            title: '会员',
            key: 'user_name'
          },
          {
            title: '储值卡',
            key: 'card_name'
          }
        )
      } else {
        this.columns.unshift({
          title: '储值卡',
          key: 'card_name'
        })
      }
      this.searchInfo()
    },
    handleBusChanged(val) {
      this.getCardList()
      this.searchInfo()
    },
    searchInfo() {
      this.postData.page_no = 1
      this.getInfo()
    },
    getCardList() {
      const url = window.IS_BRAND_SITE ? '/Merchant/CardClass/get_card_list' : '/Web/Card/get_card_list'
      // 获取所有储值卡
      return this.$service
        .post(url, {
          bus_id: this.postData.bus_id,
          page_size: 9999,
          card_type: 1
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.cardList = res.data.data.list.filter((item)=>item.card_type_id === '3')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getGroupList() {
      return this.$service
        .post('/Web/DepositCardStatistics/getGroupList', {bus_id: this.postData.bus_id})
        .then(res => {
          if (res.data.errorcode == 0) {
            this.groupList = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    getInitDay() {
      let first, last
      let date = new Date()
      if (date.getDate() === 1) {
        first = new Date(date.getFullYear(), date.getMonth() - 1, 1)
        let day = new Date(date.getFullYear(), date.getMonth(), 0).getDate()
        last = new Date(
          new Date().getFullYear(),
          new Date().getMonth() - 1,
          day
        )
      } else {
        first = new Date(new Date().setDate(1))
        last = new Date()
      }
      return [formatDate(first, 'yyyy-MM-dd'), formatDate(last, 'yyyy-MM-dd')]
    },
    handleDateRangeChanged(val) {
      val = val || ['', '']
      this.postData.start_time = val[0]
      this.postData.end_time = val[1]
    },
    getInfo(allPageCount) {
      let postObj = Object.assign({}, this.postData);
      if (allPageCount) {
        postObj.page_size = allPageCount;
        postObj.page_no = 1;
      }
      return this.$service
        .post(
          '/Web/DepositCardStatistics/getDepositCardStatisticsByGroupID',
          postObj
        )
        .then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data
            if(!allPageCount) {
               this.totalCount = resData.count
              this.tableData = resData.list
            }
            return resData.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    async exportCsv() {
       const resData = await this.getInfo(this.totalCount)
      this.$refs.export.export({
        filename: `储值卡使用统计(${this.postData.start_time}~${
          this.postData.end_time
        })`,
        columns: this.columns,
        data: resData
      })
    }
  },
  created() {
    this.postData.bus_id = this.busId || ''
    this.getMerchantsBusList()
    this.getGroupList()
    this.getRowData()
    this.getCardList(this.busId)
  },
  components: {
    Export
  }
}
</script>
