
<style lang="less">
  @border: 1px solid #dcdcdc;
  .private-sold {
    .title {
      line-height: 40px;
      font-size: 14px;
      color: #666;
    }
    padding: 0 30px;
    .ivu-table th {
      height: 48px;
      padding: 0;
      overflow: hidden;
    }
    .new-to-private,
    .buy-count,
    .buy-type,
    .renew-percent,
    .renew-count,
    .renew-type {
      height: 400px;
    }

    .sold-chart {
      height: 400px;
      margin: 30px 0;
      // width: calc(~'100% - 60px');
    }

    .checkbox-controller-box {
      margin-bottom: 10px;
    }
    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }

    .new-total {
      border-bottom: @border;
      border-top: @border;
      margin-top: 30px;
    }

    .pies {
      padding: 20px 0;
      border-bottom: @border;
    }
    .table-group {
      border-top: @border;
      .table {
        border: 0;
      }
      .table-title {
        text-align: center;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-weight: bold;
        border-right: @border;
      }
    }
  }
</style>

<template>
  <div class="table-wrap private-sold">
    <header style="border-bottom: 0; padding: 0">
      <Select v-model="selectBusId" @on-change="getList" style="width:240px;margin-right:20px" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option>
      </Select>
      <DatePicker type="daterange" v-model="dateRange" :options="dateOptions" format="yyyy年MM月dd日" @on-change="dateChanged" style="width: 240px" :clearable="false" />
    </header>
    <AgeGender @on-change="ageGenderChanged" />
    <Total class="new-total" v-model="totalItemNew" />
    <Total class="renew-total" v-model="totalItemReNew" />
    <Row class="pies">
      <Col span="8">
      <div class="new-to-private"></div>
      </Col>
      <Col span="8">
      <div class="buy-count"></div>
      </Col>
      <Col span="8">
      <div class="buy-type"></div>
      </Col>
    </Row>
    <Row class="pies" style="border: 0">
      <Col span="8">
      <div class="renew-percent"></div>
      </Col>
      <Col span="8">
      <div class="renew-count"></div>
      </Col>
      <Col span="8">
      <div class="renew-type"></div>
      </Col>
    </Row>
    <div class="sold-chart"></div>
    <!-- <h3 class="title">新增、续私教课分布</h3>
    <Row class="table-group">
      <Col span="2">
        <p class="table-title" style="border-left: 1px solid #dddee1"></p>
        <Table stripe :columns="nameColumns" :data="nameTableData" disabled-hover class="table" style="border-left: 1px solid #dddee1; border-top: 1px solid #dddee1; border-right: 0" />
      </Col>
      <Col span="11">
        <p class="table-title">新购</p>
        <Table stripe style="border-top: 1px solid #dddee1" :columns="soldColumns" :data="soldTableData" disabled-hover class="table" />
      </Col>
      <Col span="11">
        <p class="table-title">续课</p>
        <Table stripe style="border-top: 1px solid #dddee1" :columns="renewColumns" :data="renewTableData" disabled-hover class="table" />
      </Col>
    </Row> -->
    <!-- <footer style="justify-content: flex-end">
      <Page :total="+total"
            :current.sync="page"
            placement="top"
            show-total
            show-sizer
            @on-change="getList"
            @on-page-size-change="pageSizeChanged" />
    </footer> -->
  </div>
</template>

<script>
  import Total from 'components/form/Total';
  import quickPicker from 'components/picker/quickPicker';
  import Pager from 'mixins/pager';
  import AgeGender from 'components/stat/ageGender';
  import { formatDate } from 'utils';
  import echarts from 'echarts';
  import options from './privateSoldOption';
  import { mapState } from "vuex";

  const TOTAL_ITEM_NAME_NEW = {
    all_purchase_member: { name: '新增购卡会员', unit: '人' },
    new_purchase_member: {
      name: '新增购私教会员',
      unit: '人',
      tips: '新增购卡会员中转化为私教的部分，之前成交的会员在此时间段购私教的，不在该统计数据内'
    },
    new_purchase_member_transfer_ratio: { name: '新增会员私教转化率' },
    new_purchase_member_class_num: { name: '购课总节数', unit: '节' },
    new_purchase_member_avg_num: { name: '人均购课节数', unit: '节' }
  };
  const TOTAL_ITEM_NAME_RENEW = {
    out_pt_member: { name: '私教到期会员', unit: '人', tips: '所有私教课程节数都使用完或者有效期到期' },
    con_purchase_member: { name: '续私教会员', unit: '人', tips: '私教到期会员中，续课的会员数量' },
    con_purchase_member_transfer_ratio: { name: '私教续课率' },
    con_purchase_member_class_num: { name: '续课节数', unit: '节' },
    con_purchase_member_avg_num: { name: '人均续课节数', unit: '节' }
  };
  const INIT_SOLD_COL = [{ title: '新购总数', key: 'coach_new' }];
  const INIT_RENEW_COL = [{ title: '续课总数', key: 'coach_con' }];

  export default {
    name: 'privateSold',
    components: { Total, AgeGender },
    mixins: [Pager],
    data() {
      return {
        $soldChart: null,
        $newToPrivate: null,
        $buyCount: null,
        $buyType: null,
        $renewPercent: null,
        $renewCount: null,
        $renewType: null,
        dateRange: [new Date(Date.now() - 30 * 24 * 3600 * 1000), new Date()],
        dateOptions: quickPicker.dateOptions,
        ages: [],
        genders: [],
        totalItemNew: [],
        totalItemReNew: [],
        columns: [],
        tableData: [],
        nameColumns: [{ title: '业绩归属', key: 'coach_name' }],
        nameTableData: [],
        soldColumns: [],
        soldTableData: [{ title: '新购总数', key: 'coach_new' }],
        renewColumns: [{ title: '续课总数', key: 'coach_con' }],
        renewTableData: [{ coach_con: 0 }],
        soldChartOption: options.soldChartOption,
        newToPrivateOption: options.newToPrivateOption,
        buyCountOption: options.buyCountOption,
        buyTypeOption: options.buyTypeOption,
        renewPercentOption: options.renewPercentOption,
        renewCountOption: options.renewCountOption,
        renewTypeOption: options.renewTypeOption,
        storeList: [],
        selectBusId: ''
      };
    },
    computed: {
      ...mapState(['busId'])
    },
    created() {
      this.selectBusId = this.busId
      this.getStoreList()
      this.getList();
    },
    mounted() {
      this.$soldChart = echarts.init(document.querySelector('.sold-chart'));
      this.$newToPrivate = echarts.init(document.querySelector('.new-to-private'));
      this.$buyCount = echarts.init(document.querySelector('.buy-count'));
      this.$buyType = echarts.init(document.querySelector('.buy-type'));
      this.$renewPercent = echarts.init(document.querySelector('.renew-percent'));
      this.$renewCount = echarts.init(document.querySelector('.renew-count'));
      this.$renewType = echarts.init(document.querySelector('.renew-type'));
    },
    methods: {
      getStoreList() {
        return this.$service
          .get('/Web/Business/get_bus_list')
          .then(res => {
            if (res.data.errorcode === 0) {
              this.storeList = res.data.data.bus_list
            }
          })
      },
      dateChanged(val) {
        this.$nextTick(() => this.getList());
      },
      ageGenderChanged(ages, genders) {
        this.ages = ages;
        this.genders = genders;
        this.getList();
      },
      dealTotal() {
        const info = this.privateSoldInfo;
        let arrNew = [];
        let arrReNew = [];
        for (let key in TOTAL_ITEM_NAME_NEW) {
          const { name, unit, tips } = TOTAL_ITEM_NAME_NEW[key];
          arrNew.push({
            value: info[key] || 0,
            name,
            unit,
            tips
          });
        }
        for (let key in TOTAL_ITEM_NAME_RENEW) {
          const { name, unit, tips } = TOTAL_ITEM_NAME_RENEW[key];
          arrReNew.push({
            value: info[key] || 0,
            name,
            unit,
            tips
          });
        }
        this.totalItemNew = arrNew;
        this.totalItemReNew = arrReNew;
      },
      dealPie() {
        const info = this.privateSoldInfo;
        const { all_purchase_member, new_purchase_member, out_pt_member, con_purchase_member } = info;
        // 新增会员转化私教比例
        if (all_purchase_member) {
          this.newToPrivateOption.series[0].data = [
            { name: '转化的私教会员', value: new_purchase_member },
            { name: '未转化的私教会员', value: all_purchase_member - new_purchase_member }
          ];
        } else {
          this.newToPrivateOption.series[0].data = [{ value: 0, name: '未转化的私教会员' }, { value: 0, name: '转化的私教会员' }];
        }
        this.$newToPrivate.setOption(this.newToPrivateOption);
        // 续私教比例
        if (out_pt_member) {
          this.renewPercentOption.series[0].data = [
            { name: '续私教会员', value: con_purchase_member },
            { name: '未续私教会员', value: out_pt_member - con_purchase_member }
          ];
        } else {
          this.renewPercentOption.series[0].data = [{ value: 0, name: '未续私教会员' }, { value: 0, name: '续私教会员' }];
        }
        this.$renewPercent.setOption(this.renewPercentOption);
        // 购课（含赠送）节数占比
        if (info.new_purchase_member_class_num_ratio) {
          this.buyCountOption.series[0].data = info.new_purchase_member_class_num_ratio.map(item => {
            return {
              name: `${item.class_num}节`,
              value: item.people_num
            };
          });
          this.buyCountOption.legend.data = info.new_purchase_member_class_num_ratio.map(item => `${item.class_num}节`);
        } else {
          this.buyCountOption.series[0].data = [{ value: 0, name: '无' }];
          this.buyCountOption.legend.data = ['无']
        }
        this.$buyCount.setOption(this.buyCountOption);
        // 课程种类购课占比
        if (info.new_purchase_member_class_name_ratio) {
          this.buyTypeOption.series[0].data = info.new_purchase_member_class_name_ratio.map(item => {
            return {
              name: item.class_name,
              value: item.people_num
            };
          });
          this.buyTypeOption.legend.data = info.new_purchase_member_class_name_ratio.map(item => item.class_name);
        } else {
          this.buyTypeOption.series[0].data = [{ value: 0, name: '无' }];
          this.buyTypeOption.legend.data = ['无']
        }
        this.$buyType.setOption(this.buyTypeOption);
        // 续课节数占比
        if (info.con_purchase_member_class_num_ratio) {
          this.renewCountOption.series[0].data = info.con_purchase_member_class_num_ratio.map(item => {
            return {
              name: `${item.class_num}节`,
              value: item.people_num
            };
          });
          this.renewCountOption.legend.data = info.con_purchase_member_class_num_ratio.map(item => `${item.class_num}节`);
        } else {
          this.renewCountOption.series[0].data = [{ value: 0, name: '无' }];
          this.renewCountOption.legend.data = ['无']
        }
        this.$renewCount.setOption(this.renewCountOption);
        // 续私教课种类占比
        if (info.con_purchase_member_class_name_ratio) {
          this.renewTypeOption.series[0].data = info.con_purchase_member_class_name_ratio.map(item => {
            return {
              name: item.class_name,
              value: item.people_num
            };
          });
          this.renewTypeOption.legend.data = info.con_purchase_member_class_name_ratio.map(item => item.class_name);
        } else {
          this.renewTypeOption.series[0].data = [{ value: 0, name: '无' }];
          this.renewTypeOption.legend.data = ['无']
        }
        this.$renewType.setOption(this.renewTypeOption);
      },
      dealBar() {
        const barData = this.privateSoldInfo.coach_discuss;
        if (barData && barData.length) {
          this.soldChartOption.xAxis.data = barData.map(item => item.coach_name);
          this.soldChartOption.series[0].data = barData.map(item => item.coach_new);
          this.soldChartOption.series[1].data = barData.map(item => item.coach_con);
          this.$soldChart.setOption(this.soldChartOption);
        }
      },
      dealTableData() {
        const info = this.privateSoldInfo;
        this.total = info.coach_total;
        const { coach_list } = info;
        if (coach_list && coach_list.length) {
          this.nameTableData = coach_list;
          this.soldColumns = INIT_SOLD_COL.concat(
            info.new_class_sort.map(item => {
              return { key: item, title: item };
            })
          );
          this.renewColumns = INIT_RENEW_COL.concat(
            info.con_class_sort.map(item => {
              return { key: item, title: item };
            })
          );
          this.soldTableData = coach_list.map(item => {
            let obj = {};
            if (!item.coach_new_list) {
              item.coach_new_list = [];
            }
            for (let key of info.new_class_sort) {
              for (let course of item.coach_new_list) {
                if (course.class_name === key) {
                  obj[key] = course.class_num;
                  break;
                }
              }
              obj[key] = obj[key] || 0;
            }

            return { ...item, ...obj };
          });
          this.renewTableData = coach_list.map(item => {
            let obj = {};
            if (!item.coach_con_list) {
              item.coach_con_list = [];
            }
            for (let key of info.con_class_sort) {
              for (let course of item.coach_con_list) {
                if (course.class_name === key) {
                  obj[key] = course.class_num;
                  break;
                }
              }
              obj[key] = obj[key] || 0;
            }

            return { ...item, ...obj };
          });
        }
      },
      getList() {
        if (!this.ages.length || !this.genders.length) return false;

        const url = '/Web/Statistics/bus_daily_purchase_pt_statistics';
        const postData = {
          s_date: formatDate(this.dateRange[0], 'yyyy-MM-dd'),
          e_date: formatDate(this.dateRange[1], 'yyyy-MM-dd'),
          age_str: this.ages.join(','),
          sex_str: this.genders.join(','),
          page_no: this.page,
          page_size: this.pageSize,
          bus_id: this.selectBusId
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.privateSoldInfo = data;
              this.dealTotal();
              this.dealPie();
              this.dealBar();
              this.dealTableData();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
