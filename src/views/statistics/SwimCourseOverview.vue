<template>
  <div class="box">
    <header class="header">
      <Select v-model="searchPost.bus_id" @on-change="getCoachList" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <Date-picker
        v-model="dateValue"
        type="daterange"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        class="search-item"
      ></Date-picker>
      <Select v-model="searchPost.coach_id" class="search-item" clearable filterable>
        <Option
          v-for="item in coachList"
          :value="item.coach_id"
          :key="item.coach_id"
          >{{ item.coach_name }}{{item.deleted==1?'--（已删除）':''}}</Option
        >
      </Select>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
    <main class="buddy">
      <Table :columns="tableColumn" :data="tableData" border></Table>
      <Row class="overall">
        <Col span="2">
          <div class="label">总计</div>
        </Col>
        <Col span="4" offset="14" class="overall-box">
          <div class="label">上课节数</div>
          <div class="value">{{ courseCount }}节</div>
        </Col>
        <Col span="4" class="overall-box">
          <div class="label">上课价值</div>
          <div class="value">{{ courseAmount }}</div>
        </Col>
      </Row>
    </main>
    <footer class="footer">
      <ExportButton url="/Web/Statistics/swimming_class_statistics" :data="searchPost" />
      <pager
        :post-data="searchPost"
        :total="count"
        :history="false"
        @on-change="handlePageChange"
      ></pager>
    </footer>
  </div>
</template>

<script>
import Pager from 'components/pager'
import { formatDate } from '@/utils/index'
import { mapState } from 'vuex'
import { getcoachsInfo  } from '@/service/getData'
import ExportButton from 'components/form/ExportButton'
export default {
  components: {
    Pager,
    ExportButton
  },
  data() {
    return {
      // header
      dateValue: [],
      searchPost: {
        s_date: '',
        e_date: '',
        coach_id: '',
        bus_id: '',
        page_no: 1,
        page_size: 10
      },
      storeList: [],
      jumpBusId: '',
      coachList: [],
      // main
      tableColumn: [
        {
          title: '头像',
          render(h, params) {
            return (<img src={params.row.avatar} style="width:30px;height:30px"></img>)
          }
        },
        {
          key: 'coach_name',
          title: '教练'
        },
        {
          key: 'class_count',
          title: '上课总数'
        },
        {
          key: 'sign_price',
          title: '上课总价值'
        },
        {
          title: '课程名称',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.class_list.map(item => (<li>{item.card_name}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '节数',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.class_list.map(item => (<li>{item.class_count}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '消课价值',
          render(h, params) {
            return (
              <ul class="inner-tbl">
                { params.row.class_list.map(item => (<li>{item.sign_price}</li>)) }
              </ul>
            )
          }
        },
        {
          title: '操作',
          render: (h, params) => {
            return (
              <a onClick={() => {
                const url = `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Statistics/swimming_class_period_statistics`
                this.$service.get(url).then(res => {
                  if (res.data.errorcode === 0) {
                    this.$router.push({
                      name: '泳教课时',
                      params: {
                        type: 2,
                        beginTime: this.searchPost.s_date,
                        endTime: this.searchPost.e_date,
                        busId: this.jumpBusId,
                        coachId: params.row.coach_id
                      },
                      query: {
                        type: 2
                      }
                    })
                  } else {
                    this.$Message.error(res.data.errormsg)
                    // this.$Message.error('您的账号操作权限不足，请联系管理员操作。')
                  }
                })
              }}>
                详情
              </a>
            )
          }
        }
      ],
      tableData: [],
      // footer
      count: 0,
      courseCount: 0,
      courseAmount: 0
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  methods: {
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getCoachList() {
      getcoachsInfo(
        this.searchPost.bus_id,
        0,
        2
      ).then(res => {
        if (res.data.errorcode === 0) {
          this.coachList = res.data.data
        }
      })
    },
    getTableData() {
      if (!this.searchPost.bus_id) {
        this.$Message.error('请选择门店!')
        return false
      }
      if (!this.searchPost.s_date || !this.searchPost.e_date) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .post('/Web/Statistics/swimming_class_statistics', this.searchPost)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.tableData = res.data.data.list
            this.count = res.data.data.count.coach_count
            this.courseCount = res.data.data.count.class_count
            this.courseAmount = res.data.data.count.class_price
            this.jumpBusId = this.searchPost.bus_id
          }
        })
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.searchPost.s_date = ''
      this.searchPost.e_date = ''
      if (this.dateValue[0]) {
        this.searchPost.s_date = formatDate(this.dateValue[0], 'yyyy-MM-dd')
      }
      if (this.dateValue[1]) {
        this.searchPost.e_date = formatDate(this.dateValue[1], 'yyyy-MM-dd')
      }
      this.getTableData()
    },
    handlePageChange(pagePost) {
      this.searchPost = {
        ...this.searchPost,
        ...pagePost
      }
      this.getTableData()
    }
  },
  created() {
    this.searchPost.bus_id = this.busId
    this.searchPost = { ...this.searchPost, ...this.$route.params }
    if (this.searchPost.s_date && this.searchPost.e_date) {
      this.dateValue = [this.searchPost.s_date, this.searchPost.e_date]
    } else {
      const dayNow = new Date().getDate() - 1
      const beginDate = formatDate(
        new Date(Date.now() - dayNow * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM-dd')
      this.dateValue = [beginDate, endDate]
      this.searchPost.s_date = beginDate
      this.searchPost.e_date = endDate
    }
    this.getStoreList().then(() => {
      this.getCoachList()
      this.getTableData()
    })
  }
}
</script>

<style lang="less" scoped>
.white-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  padding: 0 35px;
}

.box {
  .header {
    .white-panel;
    justify-content: flex-start;
    height: 90px;
    padding-top: 10px;

    .search-item {
      width: 200px;
      margin-right: 15px;
    }
  }

  /deep/ .buddy {
    .ivu-table td {
      padding: 0;

      .inner-tbl {
        li:first-child {
          border: none;
        }

        li {
          border-top: 1px solid #eeeeee;
          min-height: 30px;
          line-height: 30px;
        }
      }
    }
  }

  .overall {
    background-color: white;
    padding-top: 20px;

    .overall-box {
      display: flex;
      flex-direction: row;
    }

    .label {
      font-size: 14px;
      font-weight: bold;
      text-align: center;
    }

    .value {
      font-size: 16px;
      font-weight: bold;
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-left: 10px;
    }
  }

  .footer {
    .white-panel;
    justify-content: space-between;
    height: 80px;
  }
}
</style>
