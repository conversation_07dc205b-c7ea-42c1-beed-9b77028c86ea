<template>
  <div class="table-wrap">
    <header class="header">
      <Select v-model="searchPost.bus_id" class="search-item" filterable>
        <Option
          v-for="item in storeList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
     
      <Date-picker
        :value="searchPost.start_total_time"
        @on-change="changeStart"
        type="month"
        format="yyyy-MM"
        placeholder="开始月份"
        :clearable="false"
      ></Date-picker>
      <span style="minWidth:10px">~</span>
      <Date-picker
        :value="searchPost.end_total_time"
        @on-change="changeEnd"
        type="month"
        format="yyyy-MM"
        placeholder="截止月份"
        :clearable="false"
      ></Date-picker>
      <Button type="success" @click="handleSearchClick">搜索</Button>
    </header>
      <Table ref="table" :columns="tableColumn" :data="tableData"></Table>
    <footer class="footer">
      <Button @click="exportCsv">导出Excel</Button>
    </footer>
  </div>
</template>

<script>
import { formatDate } from '@/utils/index'
import { mapState } from 'vuex'
import ExportButton from 'components/form/ExportButton'
export default {
  name: 'monthTotal',
  components: {
    // ExportButton
  },
  data() {
    const tableColumn = [
      {
        key: 'total_time',
        title: '日期'
      },
      {
        key: 'bus_name',
        title: '门店'
      },
      {
        key: 'achievement_total',
        title: '当月总业绩'
      },
      {
        key: 'card_order_total',
        title: '总单数'
      },
      {
        key: 'buy_card_amount_total',
        title: '新开业绩'
      },
      {
        key: 'renewal_of_insurance_amount_total',
        title: '续费业绩'
      },
      {
        key: 'change_card_amount_total',
        title: '升级业绩'
      },
      {
        key: 'other_order_amount_total',
        title: '其他业绩'
      },
      {
        key: 'delete_card_order_amount',
        title: '当月销卡'
      },
      {
        key: 'before_delete_card_order_amount',
        title: '往期销卡'
      },
      {
        key: 'action',
        title: '操作',
        render: (h, params) => {
          return (
            params.row.total_time=='总计'?'':(<a onClick={() => {
              let endDate = new Date(params.row.total_time)
              let fullYear = endDate.getFullYear()
              let month = endDate.getMonth()
              console.log(fullYear, month);
                this.$router.push({
                  name: this.$route.name==='会籍业绩月报'?'会籍业绩概况':this.$route.name==='私教业绩月报'?'教练业绩概况':'泳教业绩概况',
                  params: {
                    beginTime: formatDate(new Date(fullYear, month), 'yyyy-MM-dd'),
                    endTime: formatDate(new Date(fullYear, month+1, 0), 'yyyy-MM-dd'),
                    busId: this.searchPost.bus_id
                  }
                })
            }}>
              详情
            </a>)
          )
        }
      }
    ]
    return {
      // header
      dateValue: [],
      searchPost: {
        bus_id: '',
        start_total_time: '',
        end_total_time: ''
      },
      storeList: [],
      // main
      tableColumn,
      tableData: [],
      // footer
      count: 0
    }
  },
  computed: {
    ...mapState(['busId'])
  },
  methods: {
    changeStart(time) {
      this.searchPost.start_total_time = formatDate(new Date(time), 'yyyy-MM');
    },
    changeEnd(time) {
      this.searchPost.end_total_time = formatDate(new Date(time), 'yyyy-MM');
    },
    getStoreList() {
      return this.$service
        .get('/Web/Business/get_bus_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.storeList = res.data.data.bus_list
          }
        })
    },
    getTableData() {
      if (!this.searchPost.bus_id) {
        this.$Message.error('请选择门店!')
        return false
      }
      if (!this.searchPost.start_total_time || !this.searchPost.end_total_time) {
        this.$Message.error('请选择查询日期!')
        return false
      }
      this.$service
        .get('/Web/Statistics/'+(this.$route.name==='会籍业绩月报'?'getMemberShipAchievementMonthTotal':this.$route.name==='私教业绩月报'?'getPrivateCoachAchievementMonthTotal':'getSwimCoachAchievementMonthTotal'), {params: this.searchPost})
        .then(res => {
          if (res.data.errorcode === 0) {
            let list=res.data.data.list
            if(list && list.length) {
              list.push({
                total_time: '总计',
                ...res.data.data.statistics
              })
            }
            this.tableData = list
            this.count = res.data.data.statistics.count
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    handleSearchClick() {
      this.searchPost.page_no = 1
      this.getTableData()
    },
    exportCsv() {
      const columns = this.tableColumn.filter((item, index) => index < this.tableColumn.length - 1);
      if (this.tableData && this.tableData.length == 0) return this.$Message.error('请先搜索出有结果的数据');
      this.$refs.table.exportCsv({
        columns,
        data:this.tableData,
        filename: this.$route.name
      });
    }
  },
  created() {
    const { busId, beginTime, endTime} = this.$route.query
    this.searchPost.bus_id = busId || this.busId
    if (beginTime && endTime) {
      this.searchPost.start_total_time = beginTime
      this.searchPost.end_total_time = endTime
    } else {
      const beginDate = formatDate(
        new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
        'yyyy-MM'
      )
      const endDate = formatDate(Date.now(), 'yyyy-MM')
      this.searchPost.start_total_time = beginDate
      this.searchPost.end_total_time = endDate
    }
    this.getStoreList().then(this.getTableData)
  }
}
</script>

<style lang="less" scoped>

</style>
