<style lang="less" scoped>
  .common-box {
    width: 100%;
    background: #fff;
    padding: 23px 20px;
    border: 1px solid #dcdcdc;
  }

  .common-box-head {
    padding-left: 7px;
    display: flex;
    flex-direction: row;
    .ivu-date-picker {
      margin-left: -2px;
      width: 240px;

      .ivu-input {
        border-radius: 0;
      }
    }
  }

  .revedetail {
    width: 100%;
    padding-top: 20px;
    padding-bottom: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }
  .revedetail-item {
    margin-right: 50px;
  }
  .revedetail p {
    display: inline-block;
    font-size: 14px;
    color: #999;
    font-weight: normal;
  }
  .revedetail span {
    font-size: 14px;
    color: #52a4ea;
    font-weight: normal;
  }
  .revedetail em {
    font-size: 24px;
    font-style: normal;
  }
  .graphbox {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: flex-start;
  }
  .instrsframe:nth-child(odd) {
    margin-right: 20px;
  }
  .instrsframe {
    border: 1px solid #dcdcdc;
    width: 775px;
    height: 458px;
    margin-bottom: 20px;
  }
  .graphframe {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
  }
  .fullwidthframe {
    border: 1px solid #dcdcdc;
    width: 100%;
    margin-bottom: 20px;
  }
  .graphframe .statnum {
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

  }
  .statnum ul li {
    // line-height: 44px;
    // font-size: 14px;
    line-height: 12px;
    padding: 5px 0;
    font-size: 12px;
    color: #a3b7bf;
    max-width: 85px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .graphtitle {
    padding-left: 10px;
    padding-bottom: 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;
    color: #666;
  }
  .graphtitle p {
    margin-right: 15px;
    padding-top: 15px;
    padding-bottom: 6px;
    cursor: pointer;
  }
  .graphtitle .titlenotselected {
    color: #999;
  }
  .titleselected {
    border-bottom: 3px solid #5db75d;
  }
  .cardslist {
    width: 100%;
  }
  .cardslist li {
    width: 100%;
    height: 47px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
  }
  .cardslist li p:first-child {
    margin-left: 40px;
    margin-right: 40px;
    text-align: center;
  }
  .cardslist li p:nth-child(2) {
    width: 40%;
    text-align: left;
    max-width: 135px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .cardslist li p:nth-child(3) {
    width: 15%;
    text-align: left;
    max-width: 50px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .cardslist li p:nth-child(4) {
    width: 29%;
    text-align: left;
    max-width: 98px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .cardslist li p:nth-child(5) {
    width: 28%;
    text-align: left;
    max-width: 95px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .cardslist li p {
    font-size: 14px;
    color: #666;
  }
  .cardslist li p:last-child {
    color: #d9544f;
  }
  .cardslist li p:first-child {
    color: #dcdcdc;
    font-size: 24px;
    border-bottom: 2px solid #dcdcdc;
  }
  .cardslist li p.topthree {
    color: #1bbbdf;
    border-bottom: 2px solid #1bbbdf;
  }
  .daterange {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-left: 48px;
    padding-top: 20px;
    span {
      font-size: 14px;
      padding-left: 10px;
      padding-right: 10px;
    }
    .ivu-date-picker {
      margin-left: -2px;
      width: 240px;

      .ivu-input {
        border-radius: 0;
      }
    }
  }
  .compdata {
    width: 100%;
    padding: 53px 48px 13px 48px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    .compdata-item {
      flex-basis: 33.3%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 40px;
    }
  }
  .compdata-item .instrs {
    min-width: 50px;
    text-align: right;
  }
  .compdata-item .division {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    padding-left: 18px;
    padding-right: 18px;
    p {
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .numerator {
      border-bottom: 1px solid #666;
    }
  }
  .greendown {
    border: 1px dashed #5cb85c;
    padding: 5px 3px 5px 23px;
    color: #5cb85c;
    position: relative;
  }
  .greendown:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 11px;
    background: url(../../assets/img/greendown.gif) no-repeat;
    left: 4px;
    top: 9px;
  }
  .black {
    border: 1px dashed #666;
    padding: 5px 13px 5px 13px;
    color: #666;
    position: relative;
  }
  .redup {
    border: 1px dashed #d9544f;
    padding: 5px 3px 5px 23px;
    color: #d9544f;
    position: relative;
  }
  .redup:before {
    content: '';
    position: absolute;
    width: 16px;
    height: 11px;
    background: url(../../assets/img/redup.gif) no-repeat;
    left: 4px;
    top: 9px;
  }
  .daterange .ivu-date-picker {
    position: relative;
  }
  .stat-null {
    width: 100%;
    height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 20px;
    color: #d7d8d7;
    cursor: pointer;
  }
  @higher-box: 1616px;
  @higherWidth-outter: 775px;
  @higherWidth-inner: 580px;
  @higherHeight-outter: 488px;
  @higherHeight-inner: 430px;
  @main-box: 1076px;
  @mainWidth-outter: 505px;
  @mainWidth-inner: 385px;
  @mainHeight-outter: 455px;
  @mainHeight-inner: 355px;
  @lower-box: 995px;
  @lowerWidth-outter: 466px;
  @lowerWidth-inner: 368px;
  @lowerHeight-outter: 440px;
  @lowerHeight-inner: 352px;
  @media screen and (min-width: 1900px) {
    .common-box {
      width: @higher-box;
      margin: 0 calc(~'(100% - 1616px)/2');
    }
    .instrsframe {
      width: @higherWidth-outter;
      height: @higherHeight-outter;
    }
    #revedivis,
    #ptcoursestat {
      width: @higherWidth-inner;
      height: @higherHeight-inner;
    }
    #revetrend {
      width: @higherWidth-outter;
      height: @higherHeight-inner;
    }
    .cardslist li {
      height: 45px;
      p {
        font-size: 14px;
      }
      p:first-child {
        margin-left: 40px;
        margin-right: 40px;
        height: 62%;
        line-height: 28px;
        font-size: 26px;
        font-weight: bold;
      }
    }
    .compdata {
      padding: 53px 48px 13px 48px;
      .compdata-item {
        margin-bottom: 40px;
      }
    }
  }
  @media screen and (min-width: 1400px) and (max-width: 1900px) {
    .common-box {
      width: @main-box;
      margin: 0 calc(~'(100% - 1076px)/2');
    }
    .instrsframe {
      width: @mainWidth-outter;
      height: @mainHeight-outter;
    }
    #revedivis,
    #ptcoursestat {
      width: @mainWidth-inner;
      height: @mainHeight-inner;
    }
    #revetrend {
      width: @mainWidth-outter;
      height: @mainHeight-inner;
    }
    .cardslist li {
      height: 34px;
      p {
        font-size: 12px;
      }
      p:first-child {
        margin-left: 33px;
        margin-right: 33px;
        height: 65%;
        line-height: 20px;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .compdata {
      padding: 38px 48px 10px 48px;
      .compdata-item {
        margin-bottom: 35px;
      }
    }
    .graphtitle {
      padding-bottom: 0;
    }
  }
  @media screen and (max-width: 1400px) {
    .common-box {
      width: @lower-box;
      margin: 0 calc(~'(100% - 995px)/2');
    }
    .instrsframe {
      width: @lowerWidth-outter;
      height: @lowerHeight-outter;
    }
    #revedivis,
    #ptcoursestat {
      width: @lowerWidth-inner;
      height: @lowerHeight-inner;
    }
    #revetrend {
      width: @lowerWidth-outter;
      height: @lowerHeight-inner;
    }
    .cardslist li {
      height: 32px;
      p {
        font-size: 12px;
      }
      p:first-child {
        margin-left: 30px;
        margin-right: 30px;
        height: 65%;
        line-height: 20px;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .compdata {
      padding: 35px 48px 8px 48px;
      .compdata-item {
        margin-bottom: 35px;
      }
    }
    .graphtitle {
      padding-bottom: 0;
    }
  }
</style>
<template>
  <div class="common-box">
    <div class='common-box-head'>
      <Select v-model="selectBusId" style="margin-right:20px;width:200px" @on-change="dateRangeChanged(date_range)" filterable>
        <Option
          v-for="item in adminBusList"
          :value="item.id"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
      <DatePickerWithButton @on-change="dateRangeChanged"></DatePickerWithButton>
    </div>
    <div class='revedetail'>
      <div class='revedetail-item'>
        <p>总流水</p><span>￥<em>{{revedetail.general_income==''?'--':Number(revedetail.general_income).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>新购会员卡</p><span>￥<em>{{Number(Number(revedetail.buycard)+ Number(revedetail.cross_buy_card)).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>续卡</p><span>￥<em>{{revedetail.renewcard==''?'--':Number(revedetail.renewcard).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>私教新开</p><span>￥<em>{{revedetail.pt_buycard==''?'--':Number(revedetail.pt_buycard).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>私教续课</p><span>￥<em>{{revedetail.pt_renewcard==''?'--':Number(revedetail.pt_renewcard).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>泳教新开</p><span>￥<em>{{!revedetail.swim_buycard?'--':Number(revedetail.swim_buycard).toFixed(2)}}</em></span>
      </div>
      <div class='revedetail-item'>
        <p>泳教续课</p><span>￥<em>{{!revedetail.swim_renewcard?'--':Number(revedetail.swim_renewcard).toFixed(2)}}</em></span>
      </div>
      <!-- <div class='revedetail-item'>
        <p>套餐包</p><span>￥<em>{{!revedetail.package_buycard?'--':Number(revedetail.package_buycard).toFixed(2)}}</em></span>
      </div> -->
    </div>
    <div class='graphbox'>
      <div class="instrsframe">
        <div class="graphtitle">
          <p>收入占比</p>
        </div>
        <!-- <div class="graphframe">
          <div id="revedivis"></div>
          <div class="statnum">
            <ul>
              <li class="ajust" v-for="item in divisdatavalue" :title="'￥'+item">￥{{item}}</li>
            </ul>
          </div>
        </div> -->
        <mini-table :detail="this.revedetail" />
      </div>
      <div class="instrsframe">
        <div class="graphtitle">
          <p>收入走势</p>
        </div>
        <div id="revetrend"></div>
      </div>
      <div class="instrsframe">
        <div class="graphtitle">
          <p :class="{'titleselected':buyCadIndex==0,'titlenotselected':buyCadIndex!=0}" @click="buycardPost(0)">新购会员卡详情</p>
          <p :class="{'titleselected':buyCadIndex==1,'titlenotselected':buyCadIndex!=1}" @click="buycardPost(1)">续卡详情</p>
          <!-- <p :class="{'titleselected': buyCadIndex==2,'titlenotselected':buyCadIndex!=2}" @click="buycardPost(2)">套餐包详情</p> -->
        </div>
        <ul class="cardslist" v-if='cardstat.length>0'>
          <li v-for="(item,index) in cardstat">
            <p :class="{'topthree':index<3}">{{index+1}}</p>
            <p :title="item.card_name">{{item.card_name}}</p>
            <p>{{item.card_count}}张</p>
            <p>金额占比{{item.proportion}}%</p>
            <p :title="item.amount">￥{{item.amount}}</p>
          </li>
        </ul>
        <div class="stat-null" v-else>
          <img src="../../assets/img/stat_null.png" alt="null" />
          <p>暂无数据</p>
        </div>
      </div>
      <div class="instrsframe">
        <div class="graphtitle">
          <p :class="{'titleselected': pscIdx==3,'titlenotselected':pscIdx!=3}" @click="buySwitch(3)">私教新开详情</p>
          <p :class="{'titleselected': pscIdx==4,'titlenotselected':pscIdx!=4}" @click="buySwitch(4)">私教续课详情</p>
          <p :class="{'titleselected': pscIdx==5,'titlenotselected':pscIdx!=5}" @click="buySwitch(5)">泳教新开详情</p>
          <p :class="{'titleselected': pscIdx==6,'titlenotselected':pscIdx!=6}" @click="buySwitch(6)">泳教续课详情</p>
        </div>
        <div class="graphframe" v-show='!nodata'>
          <div id="ptcoursestat"></div>
          <div class="statnum">
            <ul>
              <li v-for="item in ptbuyvalue" :title="'￥'+item">￥{{item}}</li>
            </ul>
          </div>
        </div>
        <div class="stat-null" v-show='nodata'>
          <img src="../../assets/img/stat_null.png" alt="null" />
          <p>暂无数据</p>
        </div>
      </div>
      <div class="fullwidthframe">
        <div class="graphtitle">
          <p>对比增长</p>
        </div>
        <div class="daterange">
          <Date-picker @on-change="dateChanged" :value="date_range" type="daterange" :editable="false" format="yyyy年MM月dd日"></Date-picker>
          <span>对比</span>
          <Date-picker @on-change="dateChangedbef" :value="compdate_range" type="daterange" :editable="false" format="yyyy年MM月dd日"></Date-picker>
        </div>
        <div class="compdata">
          <div class="compdata-item part1">
            <p class="instrs">总收入</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.general_income).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.general_income).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.general)<0,'redup':Number(compdata.general)>0,'black':Number(compdata.general)==0||(Number(compreve_detail.general_income)==0&&Number(reve_detail.general_income)==0)}">{{compreve_detail.general_income==0?Number(reve_detail.general_income).toFixed(2):Math.abs(compdata.general)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">新购卡</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.buycard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.buycard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.buycard)<0,'redup':Number(compdata.buycard)>0,'black':Number(compdata.buycard)==0||(Number(compreve_detail.buycard)==0&&Number(reve_detail.buycard)==0)}">{{compreve_detail.buycard==0?Number(reve_detail.buycard).toFixed(2):Math.abs(compdata.buycard)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">续卡</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.renewcard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.renewcard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.renewcard)<0,'redup':Number(compdata.renewcard)>0,'black':Number(compdata.renewcard)==0||(Number(compreve_detail.renewcard)==0&&Number(reve_detail.renewcard)==0)}">{{compreve_detail.renewcard==0?Number(reve_detail.renewcard).toFixed(2):Math.abs(compdata.renewcard)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">私教新开</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.pt_buycard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.pt_buycard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.ptbuy)<0,'redup':Number(compdata.ptbuy)>0,'black':Number(compdata.ptbuy)==0||(Number(compreve_detail.pt_buycard)==0&&Number(reve_detail.pt_buycard)==0)}">{{compreve_detail.pt_buycard==0?Number(reve_detail.pt_buycard).toFixed(2):Math.abs(compdata.ptbuy)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">私教续课</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.pt_renewcard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.pt_renewcard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.ptrenew)<0,'redup':Number(compdata.ptrenew)>0,'black':Number(compdata.ptrenew)==0||(Number(compreve_detail.pt_renewcard)==0&&Number(reve_detail.pt_renewcard)==0)}">{{compreve_detail.pt_renewcard==0?Number(reve_detail.pt_renewcard).toFixed(2):Math.abs(compdata.ptrenew)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">泳教新开</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.swim_buycard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.swim_buycard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.swbuy)<0,'redup':Number(compdata.swbuy)>0,'black':Number(compdata.swbuy)==0||(Number(compreve_detail.swim_buycard)==0&&Number(reve_detail.swim_buycard)==0)}">{{compreve_detail.swim_buycard==0?Number(reve_detail.swim_buycard).toFixed(2):Math.abs(compdata.swbuy)+'%'}}</p>
          </div>
          <div class="compdata-item part1">
            <p class="instrs">泳教续课</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.swim_renewcard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.swim_renewcard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.swrenew)<0,'redup':Number(compdata.swrenew)>0,'black':Number(compdata.swrenew)==0||(Number(compreve_detail.swim_renewcard)==0&&Number(reve_detail.swim_renewcard)==0)}">{{compreve_detail.swim_renewcard==0?Number(reve_detail.swim_renewcard).toFixed(2):Math.abs(compdata.swrenew)+'%'}}</p>
          </div>
          <!-- <div class="compdata-item part1">
            <p class="instrs">套餐包</p>
            <div class="division">
              <p class="numerator">¥{{Number(reve_detail.package_buycard).toFixed(2)}}</p>
              <p class="denominator">¥{{Number(compreve_detail.package_buycard).toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.packagebuy)<0,'redup':Number(compdata.packagebuy)>0,'black':Number(compdata.packagebuy)==0||(Number(compreve_detail.package_buycard)==0&&Number(reve_detail.package_buycard)==0)}">{{compreve_detail.package_buycard==0?Number(reve_detail.package_buycard).toFixed(2):Math.abs(compdata.packagebuy)+'%'}}</p>
          </div> -->
          <div class="compdata-item part1">
            <p class="instrs">其他</p>
            <div class="division">
              <p class="numerator">¥{{othertotal.toFixed(2)}}</p>
              <p class="denominator">¥{{comp_othertotal.toFixed(2)}}</p>
            </div>
            <p class="colorwords" :class="{'greendown':Number(compdata.others)<0,'redup':Number(compdata.others)>0,'black':Number(compdata.others)==0||(Number(comp_othertotal)==0&&Number(othertotal)==0)}">{{comp_othertotal==0?othertotal.toFixed(2):Math.abs(compdata.others)+'%'}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import DatePickerWithButton from '../../components/picker/datePickerWithButton';
  import { getcardInfo } from '../../service/getData.js';
  import echarts from 'echarts';
  import { addDays, dateDiff } from '../../utils/index.js';
  // import Stat from '../../mixins/stat';
  import { mapState, mapGetters } from 'vuex'


  import MiniTable from './components/MiniTable'

  export default {
    name: 'revenueStatis',
    components: {
      DatePickerWithButton,
      MiniTable
    },
    data() {
      return {
        beginDate: '',
        endDate: '',
        date_range: [],
        compdate_range: [],
        reve_detail: {},
        compreve_detail: {},
        compdata: {
          general: 0,
          buycard: 0,
          renewcard: 0,
          ptbuy: 0,
          ptrenew: 0,
          packagebuy: 0,
          swbuy: 0,
          swrenew: 0,
          others: 0
        },
        dateRange: [],
        dateRangebef: [],
        revedetail: {
            general_income: 0,
            buycard: 0,
            renewcard: 0,
            pt_buycard: 0,
            pt_renewcard: 0,
            package_buycard: 0,
            swim_buycard: 0,
            package_buycard: 0,
            swim_renewcard: 0
        },
        divisdatakey: [],
        divisdatavalue: [],

        reveDivisOption: { //饼状图
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            // formatter: '{name}',
            formatter: function(name) {
              return echarts.format.truncateText(name, 80, '…');
            },
            data: [],
            orient: 'vertical',
            left: '70%',
            top: 'center',
            textStyle: {
              color: '#a3b7bf'
            },
            tooltip: {
              show: true
            },
            itemHeight: 12,
            itemWidth: 12
          },
          color: [
            '#1abbde',
            '#a76de8',
            '#ff6969',
            '#1bd4c9',
            '#b5db4f',
            '#80df89',
            '#f4ed28',
            '#ff85b3',
            '#ff7b44',
            '#808080',
            '#e49db6',
            '#221317',
            '#611817',
            '#812317'
          ],
          series: [
            {
              name: '收入占比',
              type: 'pie',
              // roseType: 'radius',
              center: ['35%', '50%'],
              radius: ['0%', '70%'],
              label: {
                normal: {
                  show: false
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },

              data: []
            }
          ]
        },
        trendOption: {
          grid: {
            left: '0',
            right: '7%',
            bottom: '7%',
            top: '7%',
            containLabel: true
          },
          xAxis: {
            show: true,
            position: 'bottom',
            type: 'time',
            nameLocation: 'end'
          },
          yAxis: {
            show: true,
            position: 'left',
            type: 'value',
            // name: '亿',
            nameLocation: 'end'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          series: [
            {
              name: '场馆总收入',
              type: 'line',
              // center:['55%','50%'],
              label: {
                normal: {
                  show: false
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              coordinateSystem: 'cartesian2d',
              smooth: true,
              lineStyle: {
                normal: {
                  width: 1,
                  color: '#268ed6'
                }
              },
              itemStyle: {
                normal: {
                  color: '#268ed6'
                }
              },
              areaStyle: {
                normal: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#268ed6' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#fff' // 100% 处的颜色
                      }
                    ],
                    globalCoord: false // 缺省为 false
                  }
                }
              },
              symbolSize: '6',
              data: []
            }
          ]
        },
        cardstat: {},
        buyCadIndex: 0, // private or swimming coach index
        pscIdx: 3, // private or swimming coach index
        ptnewselected: true,
        ptbuydetail: [],
        ptbuyvalue: [],
        ptbuykey: [],
        nodata: false,
        ptcourseOption: {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            data: [],
            formatter: function(name) {
              return echarts.format.truncateText(name, 80, '…');
            },
            orient: 'vertical',
            left: '70%',
            top: 'center',
            textStyle: {
              color: '#a3b7bf'
            },
            tooltip: {
              show: true
            },
            itemHeight: 12,
            itemWidth: 12
          },
          color: ['#1abbde', '#a76de8', '#ff6969', '#1bd4c9', '#b5db4f', '#80df89', '#f4ed28', '#ff85b3'],
          series: [
            {
              name: '私教新开详情',
              type: 'pie',
              // roseType: 'radius',
              center: ['35%', '50%'],
              radius: ['0%', '74%'],
              label: {
                normal: {
                  show: false
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              data: []
            }
          ]
        },
        buyselected: true,
        othertotal: 0,
        comp_othertotal: 0,
        storeList: [],
        selectBusId: ''
      };
    },
    computed: {
      ...mapState(['busId']),
    ...mapGetters(['adminBusList']),
    },
    methods: {
      dateRangeChanged(val) {
        this.beginDate = val[0];
        this.endDate = val[1];
        this.date_range = val;

        var postData = {
          beg_date: this.beginDate,
          end_date: this.endDate
        };
        this.nodata = false;
        this.getreveDetail(postData, 1);
        this.getreveTrend();
        this.buyselected = true;
        this.buycardPost();
        this.ptnewselected = true;
        this.getbuyptcourseStat();
      },
      dateChanged(val) {
        this.date_range[0] = val[0];
        this.date_range[1] = val[1];
        this.getDates(1);
      },
      dateChangedbef(val) {
        this.compdate_range[0] = val[0];
        this.compdate_range[1] = val[1];
        this.getComparison(3);
      },
      getDates(index) {
        if (index == 1) {
          let startdate = this.date_range[0].replace(/[^\d]/g, '-').substr(0, 10);
          let enddate = this.date_range[1].replace(/[^\d]/g, '-').substr(0, 10);
          let datediff = dateDiff(startdate, enddate) - 1;
          this.compdate_range = [addDays(startdate, datediff), addDays(enddate, datediff)];
          if (this.date_range[0] != this.beginDate || this.date_range[1] != this.endDate) {
            this.getComparison(2);
          } else {
            this.getComparison(3);
          }
        }
      },
      buySwitch(index) {
        if (index == '3') {
          this.pscIdx = 3
          if (!this.ptnewselected) {
            this.nodata = false;
            this.ptnewselected = true;
            this.getbuyptcourseStat();
          }
        } else if (index == '4') {
          this.pscIdx = 4
          if (this.ptnewselected) {
            this.nodata = false;
            this.ptnewselected = false;
            this.getrenewptcourseStat();
          }
        } else if (index == '5') {
          this.pscIdx = 5
          this.nodata = false;
          const postData = {
            beg_date: this.beginDate,
            end_date: this.endDate,
            is_renewcard: 0,
            is_swim: 1
          };
          this.ptbuyPost(postData);
        } else if (index == '6') {
          this.pscIdx = 6
          this.nodata = false;
          const postData = {
            beg_date: this.beginDate,
            end_date: this.endDate,
            is_renewcard: 1,
            is_swim: 1
          };
          this.ptbuyPost(postData);
        }
      },
      getreveDetail(postData, index) {
        // let url = '/Web/Statistics/get_bus_income_details';
        let url = '/Web/Index/achievement';
        postData.bus_id = this.selectBusId
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                if (index == '1') {
                  this.revedetail = response.data.data;
                  // this.getPiedata();
                  this.reve_detail = this.revedetail;
                  this.getDates(1);
                } else if (index == '2') {
                  this.reve_detail = response.data.data;
                  this.getComparison(3);
                } else {
                  this.compreve_detail = response.data.data;
                  this.compCal();
                }
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.error(response);
          });
      },
      getPiedata() {
        this.divisdatavalue = [];
        this.divisdatakey = [];

        const keyValue = {
          cross_buy_card: '跨店购卡',
          buycard: '购卡',
          changecard: '升卡',
          renewcard: '续卡',
          suspendcard: '请假',
          pt_buycard: '私教新开',
          pt_renewcard: '私教续课',
          pt_splitcard: '私教拆分',
          commodity: '商品销售',
          lockerrent: '租柜',
          stopCard: '销卡',
          attorncard: '转卡',
          makeUpCard: '补卡',
          activity_sign: '付费活动',
          space_reserve: '场地预订',
          space_return: '场地退订'
        };
        let divisdata = [];

        for (let [key, value] of Object.entries(this.revedetail)) {
          if (key === 'general_income') continue;
          const name = keyValue[key];
          divisdata.push({
            value,
            name
          });
          this.divisdatakey.push(name);
          this.divisdatavalue.push(value);
        }
        this.getreveDivis(divisdata);
      },
      getreveDivis(divisdata) {
        const reveDivisChart = echarts.init(document.querySelector('#revedivis')); //初始化echarts实例
        this.reveDivisOption.series[0].data = divisdata;
        this.reveDivisOption.legend.data = this.divisdatakey;
        reveDivisChart.setOption(this.reveDivisOption);
      },
      getreveTrend() {
        var postData = {
          beg_date: this.beginDate,
          end_date: this.endDate,
          bus_id: this.selectBusId
        };
        let url = '/Web/Statistics/get_bus_general_income';
        let _this = this;
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                let resdata = response.data.data;
                var trenddata = [];
                for (let i = 0; i < resdata.length; i++) {
                  let trenditem = [resdata[i].date, resdata[i].general_income];
                  trenddata.push(trenditem);
                }
                const trendChart = echarts.init(document.querySelector('#revetrend'));
                _this.trendOption.series[0].data = trenddata;
                trendChart.setOption(_this.trendOption);
              } else {
                _this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.log(response);
          });
      },
      buycardPost(index) {
        if(index === undefined) {
          index = this.buyCadIndex
        } else {
          this.buyCadIndex = index
        }
        this.$service
          .post('/Web/Statistics/buy_renew_ranking_list', {
          beg_date: this.beginDate,
          end_date: this.endDate,
          is_package: index==2?1:'',
          is_renewcard: index==2?0:index,
          number: 8,
          bus_id: this.selectBusId
        })
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                this.cardstat = response.data.data;
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.log(response);
          });
      },
      getbuyptcourseStat() {
        var postData = {
          beg_date: this.beginDate,
          end_date: this.endDate,
          is_renewcard: 0
        };
        this.ptbuyPost(postData);
      },
      getrenewptcourseStat() {
        var postData = {
          beg_date: this.beginDate,
          end_date: this.endDate,
          is_renewcard: 1
        };
        this.ptbuyPost(postData);
      },
      ptbuyPost(postData) {
        postData.bus_id = this.selectBusId
        let url = '/Web/Statistics/pt_buy_renew_info';
        let _this = this;
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                _this.ptbuyvalue = [];
                _this.ptbuykey = [];
                _this.ptbuydetail = [];
                let resdata = response.data.data;
                if (resdata.length == 0) {
                  _this.nodata = true;
                }
                for (let i = 0; i < resdata.length; i++) {
                  if (i < 8) {
                    let itemvalue = resdata[i].amount;
                    _this.ptbuyvalue.push(itemvalue);
                    let itemkey = resdata[i].card_name;
                    _this.ptbuykey.push(itemkey);
                  }
                  let item = {
                    name: resdata[i].card_name,
                    value: resdata[i].amount
                  };
                  _this.ptbuydetail.push(item);
                }
                const ptcourseChart = echarts.init(document.querySelector('#ptcoursestat'));
                _this.ptcourseOption.series[0].data = _this.ptbuydetail;
                _this.ptcourseOption.legend.data = _this.ptbuykey;
                if (postData.is_renewcard == 0) {
                  _this.ptcourseOption.series[0].name = '私教新开详情';
                } else {
                  _this.ptcourseOption.series[0].name = '私教续课详情';
                }
                ptcourseChart.setOption(this.ptcourseOption);
              } else {
                _this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(function(response) {
            console.log(response);
          });
      },
      getComparison(index) {
        if (index == 1) {
          let s1 = this.compdate_range[0].replace(/[^\d]/g, '-').substr(0, 10);
          let s2 = this.compdate_range[1].replace(/[^\d]/g, '-').substr(0, 10);
          var postD = {
            beg_date: s1,
            end_date: s2
          };
          this.getreveDetail(postD, 3);
        } else if (index == 2) {
          let s3 = this.date_range[0].replace(/[^\d]/g, '-').substr(0, 10);
          let s4 = this.date_range[1].replace(/[^\d]/g, '-').substr(0, 10);
          let postData = {
            beg_date: s3,
            end_date: s4
          };
          this.getreveDetail(postData, 2);
        } else {
          let s5 = this.compdate_range[0].replace(/[^\d]/g, '-').substr(0, 10);
          let s6 = this.compdate_range[1].replace(/[^\d]/g, '-').substr(0, 10);
          let postData = {
            beg_date: s5,
            end_date: s6
          };
          this.getreveDetail(postData, 3);
        }
      },
      compCal() {
        this.othertotal = 0;
        this.comp_othertotal = 0;
        this.othertotal = Number(this.reve_detail.other)
          // Number(this.reve_detail.changecard) +
          // Number(this.reve_detail.commodity) +
          // Number(this.reve_detail.suspendcard) +
          // Number(this.reve_detail.lockerrent) +
          // Number(this.reve_detail.stopCard) +
          // Number(this.reve_detail.attorncard);
        this.comp_othertotal = Number(this.compreve_detail.other)
          // Number(this.compreve_detail.changecard) +
          // Number(this.compreve_detail.commodity) +
          // Number(this.compreve_detail.suspendcard) +
          // Number(this.compreve_detail.lockerrent) +
          // Number(this.compreve_detail.stopCard) +
          // Number(this.compreve_detail.attorncard);
        this.compdata = {
          general: (
            (100 * (Number(this.reve_detail.general_income) - Number(this.compreve_detail.general_income))) /
            Math.abs(Number(this.compreve_detail.general_income))
          ).toFixed(2),
          buycard: (
            (100 * (Number(this.reve_detail.buycard) - Number(this.compreve_detail.buycard))) /
            Math.abs(Number(this.compreve_detail.buycard))
          ).toFixed(2),
          renewcard: (
            (100 * (Number(this.reve_detail.renewcard) - Number(this.compreve_detail.renewcard))) /
            Math.abs(Number(this.compreve_detail.renewcard))
          ).toFixed(2),
          ptbuy: (
            (100 * (Number(this.reve_detail.pt_buycard) - Number(this.compreve_detail.pt_buycard))) /
            Math.abs(Number(this.compreve_detail.pt_buycard))
          ).toFixed(2),
          ptrenew: (
            (100 * (Number(this.reve_detail.pt_renewcard) - Number(this.compreve_detail.pt_renewcard))) /
            Math.abs(Number(this.compreve_detail.pt_renewcard))
          ).toFixed(2),
          swbuy: (
            (100 * (Number(this.reve_detail.swim_buycard) - Number(this.compreve_detail.swim_buycard))) /
            Math.abs(Number(this.compreve_detail.swim_buycard))
          ).toFixed(2),
          swrenew: (
            (100 * (Number(this.reve_detail.swim_renewcard) - Number(this.compreve_detail.swim_renewcard))) /
            Math.abs(Number(this.compreve_detail.swim_renewcard))
          ).toFixed(2),
          packagebuy: (
            (100 * (Number(this.reve_detail.package_buycard) - Number(this.compreve_detail.package_buycard))) /
            Math.abs(Number(this.compreve_detail.package_buycard))
          ).toFixed(2),
          others: ((100 * (this.othertotal - this.comp_othertotal)) / Math.abs(this.comp_othertotal)).toFixed(2)
        };
      },
      getStoreList() {
        !this.adminBusList && this.$store.dispatch('getAdminBusList');
        // return this.$service
        //   .get('/Web/Business/get_bus_list')
        //   .then(res => {
        //     if (res.data.errorcode === 0) {
        //       this.storeList = res.data.data.bus_list
        //     }
        //   })
      },
    },
    created () {
      this.selectBusId = this.busId
      this.getStoreList()
    }
  };
</script>
