export default {
  soldChartOption: {
    title: {
      text: '教练新增、续课私教学员',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    toolbox: {
      show: true,
      feature: {
        saveAsImage: { show: true }
      },
      right: 20
    },
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: [0],
        filterMode: 'filter',
        top: '7%',
        startValue: 0,
        endValue: 10
      },
      {
        type: 'slider',
        yAxisIndex: [0],
        filterMode: 'empty',
        minSpan: 100
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        filterMode: 'filter',
        startValue: 0,
        endValue: 10
      },
      {
        type: 'inside',
        yAxisIndex: [0],
        filterMode: 'empty',
        minSpan: 100
      }
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '0',
      right: '6%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        interval: 0,
        textStyle: {
          color: '#666666',
          fontSize: 12
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          textStyle: {
            color: '#666666',
            fontSize: 12
          },
          formatter: '{value}人'
        },
        minInterval: 1
      }
    ],
    series: [
      {
        name: '新增',
        type: 'bar',
        data: [],
        color: ['#334b5c']
      },
      {
        name: '续课',
        type: 'bar',
        data: [],
        color: ['#6ab0b8']
      }
    ]
  },
  newToPrivateOption: {
    title: {
      text: '新增会员私教转比例',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['未转化的私教会员', '转化的私教会员'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>人数：{c} <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '获客来源',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '未转化的私教会员' }, { value: 0, name: '转化的私教会员' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  buyCountOption: {
    title: {
      text: '购课（含赠送）节数占比',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['0节'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '购课节数：{b}<br/>会员：{c} <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '获客来源',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '0节' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  buyTypeOption: {
    title: {
      text: '课程种类购课占比',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['无'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}：{c}节 <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '获客来源',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '无' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  renewPercentOption: {
    title: {
      text: '续私教比例',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['未续私教会员', '续私教会员'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>人数：{c} <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '续私教比例',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '未续私教会员' }, { value: 0, name: '续私教会员' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  renewCountOption: {
    title: {
      text: '续课节数占比',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['无'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '续课节数：{b}<br/>会员：{c} <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '获客来源',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '无' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  renewTypeOption: {
    title: {
      text: '续私教课种类占比',
      x: 'center',
      y: 'top',
      textStyle: {
        color: '#666666',
        fontSize: 14
      }
    },
    legend: {
      x: 'center',
      y: 'bottom',
      data: ['无'],
      itemWidth: 10,
      itemHeight: 10,
      tooltip: {
        show: true
      },
      formatter: function(name) {
        return name.substr(0, 10);
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}：{c}节 <br/> 占比：({d}%)'
    },
    series: [
      {
        name: '获客来源',
        type: 'pie',
        radius: '57%',
        center: ['50%', '50%'],
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        lableLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        data: [{ value: 0, name: '无' }],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
};
