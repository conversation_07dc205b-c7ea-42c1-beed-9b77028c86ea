<template>
  <div class="table-wrap">
    <header>
      <Select
        v-model="postData.bus_id"
        filterable
        @on-change="handleChangeBus">
        <Option
          v-for="item in busList"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        ></Option>
      </Select>
      <DatePickerWithButton
        select="本月"
        :option="{
          disabledDate(date) {
            return date && date.getTime() > Date.now()
          }
        }"
        style="max-width:unset;"
        @on-change="dateRangeChanged" />
      <UserSearch
        v-model="postData.user_id"
        :busId="postData.bus_id"
        url="/Web/Member/search_all_member"
        placeholder="输入会员名称/电话"
        @on-change="doSearch"
      />
      <Select
        v-model="postData.coach_id"
        clearable
        filterable
        placeholder="选择教练"
        @on-change="doSearch"
      >
        <Option
          v-for="item in coachList"
          :key="item.coach_id"
          :value="item.coach_id"
          :label="item.coach_name + (item.deleted==1 ?'--（已删除）':'')"
        ></Option>
      </Select>
      <!-- <Button type="success" @click="doSearch">搜索</Button> -->
    </header>
    <Table
      ref="table"
      :data="tableData"
      :columns="columns"
      disabledHover
      stripe></Table>
    <footer>
      <Button @click="exportCsv">导出Excel</Button>
      <Export ref="export">导出Excel</Export>
      <Pager
        :history="false"
        :total="total"
        :postData="postData"
        @on-change="pageChange"></Pager>
    </footer>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { getcoachsInfo } from '@/service/getData'
  import DatePickerWithButton from 'src/components/picker/datePickerWithButton';
  import UserSearch from "components/user/userSearch";
  import Export from 'src/components/Export';
  import Pager from 'components/pager';

  export default {
    name: 'MemberAttendClassDetail', // 会员上课明细
    components: {
      DatePickerWithButton,
      UserSearch,
      Export,
      Pager,
    },

    data() {
      return {
        coachList: [],
        postData: {
          bus_id: '',
          date_range: [],
          user_id: '',
          coach_id: '',
          page_no: 1,
          page_size: 10,
        },
        tableData: [],
        total: 0,
        columns: [
          {
            key: 'username',
            title: '会员姓名',
            // render: (_, { row }) =>
            // <router-link to={{ name: '', params: { user_id: row.user_id } }}>
            //   { row.username }
            // </router-link>
          },
          {
            key: 'course_time',
            title: '上课时间'
          },
          {
            key: 'class_name',
            title: '课程名称',
            render: (h, { row }) =>
              <div>
                { row.class_name }
                { row.is_miss == '1' ? <span style="color:#e60012;">(爽约)</span> : null }
              </div>
          },
          {
            key: 'bus_name',
            title: '上课门店',
          },
          {
            key: 'coach_name',
            title: '上课教练'
          },
          {
            key: 'type',
            title: '预约类型',
            render: (_, { row }) => <span>{ row.type == 1 ? '用卡预约' : '付费预约' }</span>
          },
          {
            key: 'type_value',
            title: '用卡名称/付费金额'
          },
          {
            key: 'buy_bus_name',
            title: '购卡门店'
          },
        ],
      };
    },
    computed: {
      ...mapGetters(['busId', 'busList'])
    },

    created() {
      this.postData.bus_id = this.busId
      this.getCoachList()
    },

    methods: {
      getList() {
        const { date_range, ...rest } = this.postData
        const params = {
          ...rest,
          s_date: date_range[0],
          e_date: date_range[1],
        }
        this.$service
          .get('/web/Statistics/openClassSignLists', { params })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData = res.data.data.list;
              this.total = res.data.data.count;
            } else {
              this.$Message.error(res.data.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const { date_range, ...rest } = this.postData
        const params = {
          ...rest,
          s_date: date_range[0],
          e_date: date_range[1],
          page_no: 1,
          page_size: +this.total || 999,
        }
        return this.$service
          .get('/web/Statistics/openClassSignLists', { params })
          .then(res => {
            if (res.data.errorcode === 0) {
              return res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getCoachList() {
        getcoachsInfo(this.postData.bus_id, 0, 0).then(res => {
          if (res.data.errorcode === 0) {
            this.coachList = res.data.data
          }
        })
      },
      handleChangeBus() {
        this.postData.user_id = ''
        this.postData.coach_id = ''
        this.getCoachList()
        this.doSearch()
      },
      dateRangeChanged(val) {
        this.postData.date_range = val
        this.doSearch()
      },

      pageChange(postData) {
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      async exportCsv() {
        const exportData = await this.getExportData();
        exportData.forEach(v => {
          v.custom_class_name = `${v.class_name}${v.is_miss == '1' ? '(爽约)' : ''}`;
          v.custom_type = v.type == 1 ? '用卡预约' : '付费预约'
        })
        this.$refs.export.export({
          filename: `会员上课明细${this.postData.m_date ? `(${this.postData.m_date})` : ''}`,
          columns: this.columns.map(v => v.render ? { ...v, key: 'custom_' + v.key } : v),
          data: exportData
        });
      }
    }
  };
</script>

<style scoped lang="less">
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .icon-tips {
    padding-left: 5px;
    vertical-align:text-bottom;
  }
</style>
