<template>
    <div class="box tab-table-wrap customized-tabs">
        <Tabs value="tabNameOne" @on-click="handleTabClick" v-model="tabName">
            <TabPane label="评价汇总统计" name="tabNameOne">
                <div class="tab-head">
                    <Select v-model="selectBusId1" style="margin-right:20px;width:200px" @on-change="handleStoreChange(1)" filterable>
                      <Option
                        v-for="item in storeList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option>
                    </Select>
                    <DatePickerWithButton select="本月" :options="options1" @on-change="dateChange1"></DatePickerWithButton>
                    <AdminRegion v-model="choseGroup1" url="/Web/CoachGroup/getCoachGroupList" :busId="selectBusId1" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" />
                    <!-- <Select class="group-select" style="margin-left: 20px; width: 240px" v-model="selectedGroup"
                            @on-change="handleGymChanged" filterable v-if="groupList">
                        <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
                        <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
                        <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

                        <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
                            class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
                        <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

                        <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
                            <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
                            class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
                            <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
                        </template>

                        </template>
                    </Select> -->
                    <div style='margin-left:auto;'>
                   <Tooltip placement="bottom-end" >
                    评分标准
                     <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                     <div slot='content' style="line-height: 30px">
                        <p>当总分为10分时，</p>
                        <p>8-10分为好评,3-7.9分为中评,1-2.9分为差评</p>
                        <p>当总分为5分时，</p>
                        <p>4-5分为好评,3-3.9分为中评,1-2.9分为差评,</p>
                        <p>当总分为3分时，</p>
                        <p>3分为好评,2-2.9分为中评,1-1.9分为差评</p>
                     </div>
                    </Tooltip>
                    </div>
                </div>
                <div class="total-stat" v-if="commentInformation">
                    <div class="stat">
                        <h3>{{ commentInformation.course_num }}</h3>
                        <p>泳教消课</p>
                    </div>
                    <b></b>
                    <div class="stat">
                        <h3>{{ commentInformation.comment_num }}</h3>
                        <p>评价人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.good_nums }}</h3>
                        <p>好评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.medium_nums }}</h3>
                        <p>中评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.bad_nums }}</h3>
                        <p>差评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.add_comment_nums }}</h3>
                        <p>追评人次</p>
                    </div>
                </div>
                <Table :columns="columns1" :data="data1"></Table>
                <footer>
                    <Button @click="exportCsv1">导出Excel</Button>
                    <Export ref="export"></Export>
                    <Page :total="total1" :history="false" @on-change="handlePageChange1" @on-page-size-change="pageSizeChanged1" show-total show-sizer></Page>
                </footer>
            </TabPane>
            <TabPane label="评价明细统计" name="tabNameTwo">
                <div class="tab-head">
                    <Select v-model="selectBusId2" style="margin-right:20px;width:200px" @on-change="handleStoreChange(2)" filterable>
                        <Option
                        v-for="item in storeList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option>
                    </Select>
                    <DatePickerWithButton :days.sync="days" select="本月"  :options="options2" @on-change="dateChange2"></DatePickerWithButton>
                    <!-- <AdminRegion v-model="choseGroup2" url="/Web/CoachGroup/getCoachGroupList" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" /> -->
                    <Select class="group-select" style="margin-left: 20px; width: 240px" v-model="selectedGroup" @on-change="handleGymChanged" filterable v-if="groupList">
                        <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
                        <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
                        <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

                            <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
                                class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
                            <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

                            <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
                                <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
                                class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
                                <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
                            </template>

                        </template>
                    </Select>
                    <Select v-model="scoreValue" style="width:200px;margin-left:20px;" @on-change="handleStoreChange(3)" clearable>
                    <Option :value="1">差评</Option>
                    <Option :value="2">中评</Option>
                    <Option :value="3">好评</Option>
                    </Select>
                    <div style='margin-left:auto;'>
                   <Tooltip placement="bottom-end" >
                    评分标准
                     <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                     <div slot='content' style="line-height: 30px">
                        <p>当总分为10分时，</p>
                        <p>8-10分为好评,3-7.9分为中评,1-2.9分为差评</p>
                        <p>当总分为5分时，</p>
                        <p>4-5分为好评,3-3.9分为中评,1-2.9分为差评,</p>
                        <p>当总分为3分时，</p>
                        <p>3分为好评,2-2.9分为中评,1-1.9分为差评</p>
                     </div>
                    </Tooltip>
                    </div>
                </div>
                <div class="total-stat" v-if="commentInformation">
                    <div class="stat">
                        <h3>{{ commentInformation.course_num }}</h3>
                        <p>泳教消课</p>
                    </div>
                    <b></b>
                    <div class="stat">
                        <h3>{{ commentInformation.comment_num }}</h3>
                        <p>评价人次</p>
                    </div>
                     <div class="stat">
                        <h3>{{ commentInformation.good_nums }}</h3>
                        <p>好评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.medium_nums }}</h3>
                        <p>中评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.bad_nums }}</h3>
                        <p>差评人次</p>
                    </div>
                    <div class="stat">
                        <h3>{{ commentInformation.add_comment_nums }}</h3>
                        <p>追评人次</p>
                    </div>
                </div>
                <Table :columns="columns2" :data="data2"></Table>
                <footer>
                    <Button @click="exportCsv2">导出Excel</Button>
                    <Export ref="export"></Export>
                    <Page :total="total2" :history="false" @on-change="handlePageChange2" @on-page-size-change="pageSizeChanged2" show-total show-sizer></Page>
                </footer>
            </TabPane>
        </Tabs>
        <EvaluationModel ref="evaluationModel" @on-reply="onReply"/>
    </div>
</template>

<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'
import { queryEvaluationDetail, addFirstReply } from 'src/service/getData'

import DatePickerWithButton from 'components/picker/datePickerWithButton'
import AdminRegion from 'components/form/adminRegion.vue'
import Export from 'src/components/Export'
import EvaluationModel from './components/EvaluationModel'

export default {
  name: 'comment4coachTable',
  components: { DatePickerWithButton, AdminRegion, Export,EvaluationModel },
  computed: {
    //   ...mapGetters(['swimGroupList'])
    ...mapState(['busId'])
  },
  data() {
    const formatDate = (source, format) => {
      const o = {
        'M+': source.getMonth() + 1, // 月份
        'd+': source.getDate(), // 日
        'H+': source.getHours(), // 小时
        'm+': source.getMinutes(), // 分
        's+': source.getSeconds(), // 秒
        'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
        'f+': source.getMilliseconds() // 毫秒
      }
      if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (source.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
          format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
        }
      }
      return format
    }
    return {
      formatDate,
      tabName: '',
      dateRange1: '',
      dateRange2: '',
      days: [],
      options1: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      options2: {
        disabledDate(date) {
          return date && date.getTime() > Date.now()
        }
      },
      choseGroup1: '',
      // choseGroup2: "",
      choseGroup1Id: '',
      // choseGroup2CoachId: "",
      commentInformation: {
        course_num: 0,
        comment_num: 0,
        five_num: 0,
        four_num: 0,
        three_num: 0,
        two_num: 0,
        one_num: 0
      },
      columns1: [
        {
          title: '教练',
          key: 'coach_name',
          render: (h, param) => {
            return (
              <a
                onClick={() => {
                  this.tabName = 'tabNameTwo'
                  this.coachId = param.row.coach_id
                  this.selectedGroup = param.row.coach_id
                  this.groupId = ''
                  this.pageNo2 = 1
                  this.days = [new Date(this.dateRange1[0]).getTime(), new Date(this.dateRange1[1]).getTime()]
                  this.getOverall(2)
                  this.getTab2List()
                }}
              >
                {param.row.coach_name}
              </a>
            )
          }
        },
        {
          title: '泳教消课',
          key: 'course_num'
        },
        {
          title: '评价人次',
          key: 'comment_num'
        },
        {
          title: '好评人次',
          key: 'good_nums'
        },
        {
          title: '中评人次',
          key: 'medium_nums'
        },
        {
          title: '差评人次',
          key: 'bad_nums'
        },
        {
          title: '追评人次',
          key: 'add_comment_nums'
        },
        {
          title: '用户评价标签',
          key: 'mark',
          width: 600,
          // renderHeader(h, param) {
          //     return (<div>
          //         <span style="margin-right:5px;">用户评价标签</span>
          //         <Tooltip>
          //             <Icon type="ios-help-circle" style="color: #f4a627"></Icon>
          //             <div slot="content">
          //                 <div>我还没想好！</div>
          //             </div>
          //         </Tooltip>
          //     </div>);
          // },
          render(h, param) {
            const tagBox = param.row.tag_arr.map(tag => {
              return (
                <span class="tag-item">
                  <Tag>{tag.name}</Tag>
                  <Icon type="ios-close" />
                  <strong style="margin-right:4px;">{tag.num}</strong>
                </span>
              )
            })
            return <span class="tag-box">{tagBox}</span>
          }
        }
      ],
      columns2: [
        {
          title: '上课时间',
          key: 'class_time',
          width: 150,
          render: (h, param) => {
            const temp = param.row.class_time * 1000
            const date = formatDate(new Date(temp), 'yyyy-MM-dd HH:mm:ss')
            return <div>{date}</div>
          }
        },
        {
          title: '评价会员',
          key: 'username',
          width: 120
        },
        {
          title: '泳教课',
          key: 'card_name',
          width: 120
        },
        {
          title: '教练',
          key: 'coach_name',
          width: 120
        },
        {
          title: '评价时间',
          key: 'comment_time',
          width: 150,
          render: (h, param) => {
            const temp = param.row.comment_time * 1000
            const date = formatDate(new Date(temp), 'yyyy-MM-dd HH:mm:ss')
            return <div>{date}</div>
          }
        },
        {
          title: '综合得分',
          key: 'average',
          width: 120
        },
        {
          title: '评分明细',
          key: 'assessDetail',
          width: 200,
          render: (h, param) => {
            const li = param.row.set_type_json.map(item => {
              return (
                <li>
                  {item.value}: {item.star}分
                </li>
              )
            })
            return <ul>{li}</ul>
          }
        },
        {
          title: '评价标准',
          key: 'base_level_copy',
          width: 120
        },
        {
          title: '评价标签',
          key: 'mark',
          width: 300,
          render(h, param) {
            const tagArr = param.row.tag_json
            const tagBox = tagArr.map(tag => {
              return <Tag>{tag.value}</Tag>
            })
            return <span class="tag-box">{tagBox}</span>
          }
        },
        {
          title: '是否存在追评',
          key: 'add_advice_time',
          width: 120,
          render(h, param) {
            const isHaveAddReplyStr = param.row.add_advice_time !== '0' ? '是' : '否'
            return <span>{isHaveAddReplyStr}</span>
          }
        },
        {
          title: '状态',
          key: 'is_reply',
          width: 120,
          render(h, param) {
            const isReplyStr = param.row.is_reply !== '0' ? '已回复' : '未回复'
            return <span>{isReplyStr}</span>
          }
        },
        {
          title: '操作',
          key: 'is_reply',
          width: 120,
          render: (h, param) => {
            const clickA = () => {
              this.replyItem(param.row)
            }
            const isReply = param.row.is_reply !== '0'
            return <a onClick={clickA}>{isReply ? '查看' : '回复'}</a>
          }
        }
      ],
      data1: [],
      data2: [],
      total1: 0,
      total2: 0,
      pageSize1: 10,
      pageSize2: 10,
      pageNo1: 1,
      pageNo2: 1,

      selectedGroup: '',
      groupList: null,
      groupId: '',
      coachId: '',
      storeList: [],
      selectBusId1: '',
      selectBusId2: '',
      scoreValue: ''
    }
  },
  watch: {
    choseGroup1: function(val, old) {
      if (val) {
        const groupIdArr = val.split('_')
        if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
          this.pageNo1 = 1
          this.choseGroup1Id = groupIdArr[0]
        }
      } else {
        this.choseGroup1Id = ''
      }
      this.getTab1List()
      this.getOverall(1)
    }
    //   choseGroup2: function(val, old) {
    //     console.log(val);
    //     const groupIdArr = val.split("_");
    //     if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
    //       this.choseGroup2CoachId = groupIdArr[0];
    //       this.getOverall();
    //       this.getTab2List();
    //     }
    //   }
  },
  // async mounted() {
  //     if (!this.coachGroupList || !this.coachGroupList.length) {
  //         this.groupList = await this.getSwimGroupDB();
  //     } else {
  //         this.groupList = this.coachGroupList;
  //     }
  // },
  created() {
    this.selectBusId1 = this.busId
    this.selectBusId2 = this.busId
    this.getStoreList()
    this.getCoachGroupList()
  },
  methods: {
    // ...mapActions(['getSwimGroupDB']),
    getCoachGroupList() {
      return this.$service
        .post('/Web/Statistics/coach_group_list', {
          bus_id: this.selectBusId,
          is_swimming_coach: true
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.groupList = res.data.data.list[0]
          }
        })
    },
    getStoreList() {
      return this.$service.get('/Web/Business/get_bus_list').then(res => {
        if (res.data.errorcode === 0) {
          this.storeList = res.data.data.bus_list
        }
      })
    },
    handleStoreChange(sequence) {
      if (sequence === 1) {
        this.getCoachGroupList().then(() => {
          this.pageNo1 = 1
          this.getOverall(1)
          this.getTab1List()
        })
      } else if (sequence === 2) {
         this.scoreValue = ''
        this.groupId = ''
        this.coachId = ''
        this.getCoachGroupList().then(() => {
          this.pageNo2 = 1
          this.getOverall(2)
          this.getTab2List()
        })
      } else {
        this.pageNo2 = 1
        this.getOverall(2)
        this.getTab2List()
      }
    },
    dateChange1(dateRange) {
      this.pageNo1 = 1
      this.dateRange1 = dateRange
      this.getOverall(1)
      this.getTab1List()
    },
    dateChange2(dateRange) {
      this.pageNo2 = 1
      this.dateRange2 = dateRange
      this.getOverall(2)
      this.getTab2List()
    },
    handlePageChange1(pageNo) {
      this.pageNo1 = pageNo
      this.getTab1List()
    },
    handlePageChange2(pageNo) {
      this.pageNo2 = pageNo
      this.getTab2List()
    },
    handleTabClick(tabName) {
      if (tabName == 'tabNameOne') {
        this.getOverall(1)
        this.getTab1List()
      } else if (tabName == 'tabNameTwo') {
        this.getOverall(2)
        this.getTab2List()
      }
    },
    handleGymChanged(val) {
      if (!val) {
        return false
      }
      if (val.indexOf('group') === -1) {
        this.groupId = ''
        this.coachId = val
      } else {
        this.groupId = val.substring(5)
        this.coachId = ''
      }
      this.pageNo2 = 1
      this.getOverall(2)
      this.getTab2List()
    },
    getOverall(way) {
      let groupId = ''
      let coachId = ''
      let begin = ''
      let end = ''
      let bus_id = ''
      let base_level = ''
      if (way == 1) {
        groupId = this.choseGroup1Id
        begin = this.dateRange1[0]
        end = this.dateRange1[1]
        bus_id = this.selectBusId1
      } else if (way == 2) {
        groupId = this.groupId
        coachId = this.coachId
        begin = this.dateRange2[0]
        end = this.dateRange2[1]
        bus_id = this.selectBusId2
         base_level= this.scoreValue
      }
      return this.$service
        .post('/Web/SwimmingCourseComment/summary', {
          start_time: begin,
          end_time: end,
          group_id: groupId,
          coach_id: coachId,
          bus_id
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.commentInformation = res.data.data
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTab1List(extract = 0) {
      return this.$service
        .post('/Web/SwimmingCourseComment/summaryList', {
          start_time: this.dateRange1[0],
          end_time: this.dateRange1[1],
          coach_group_id: this.choseGroup1Id,
          is_export: extract,
          page_no: extract ? '' : this.pageNo1,
          page_size: extract ? '' : this.pageSize1,
          bus_id: this.selectBusId1
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (extract === 0) {
              this.data1 = res.data.data.list
              this.total1 = res.data.data.count
            } else {
              let list = res.data.data.list
              if (Array.isArray(list)) {
                list.forEach(item => {
                  if (Array.isArray(item.tag_arr)) {
                    let tagStr = ''
                    const len = item.tag_arr.length
                    item.tag_arr.forEach((tag, index) => {
                      tagStr += '(' + tag.name + ' X ' + tag.num + ')'
                      if (index !== len - 1) {
                        tagStr += ', '
                      }
                    })
                    item.mark = tagStr
                  }
                })
              }
              let exColumns = [
                { title: '教练', key: 'coach_name' },
                { title: '泳教消课', key: 'course_num' },
                { title: '评价人次', key: 'comment_num' },
                { title: '好评人次', key: 'good_nums' },
                { title: '中评人次', key: 'medium_nums' },
                { title: '差评人次', key: 'bad_nums' },
                { title: '追评人次', key: 'add_comment_nums' },
                { title: '10分', key: 'ten_num' },
                { title: '9分-9.9分', key: 'nine_num' },
                { title: '8分-8.9分', key: 'eight_num' },
                { title: '7分-7.9分', key: 'seven_num' },
                { title: '6分-6.9分', key: 'six_num' },
                { title: '5分-5.9分', key: 'five_num' },
                { title: '4分-4.9分', key: 'four_num' },
                { title: '3分-3.9分', key: 'three_num' },
                { title: '2分-2.9分', key: 'two_num' },
                { title: '1分-1.9分', key: 'one_num' },
                { title: '用户评价标签', key: 'mark' }
              ]
              this.$refs.export.export({
                filename: `泳教评价汇总统计(${this.dateRange1[0]}~${this.dateRange1[1]})`,
                columns: exColumns,
                data: list
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getTab2List(extract = 0) {
      return this.$service
        .post('/Web/SwimmingCourseComment/summaryDetail', {
          start_time: this.dateRange2[0],
          end_time: this.dateRange2[1],
          coach_id: this.coachId,
          coach_group_id: this.groupId,
          is_export: extract,
          page_no: extract ? '' : this.pageNo2,
          page_size: extract ? '' : this.pageSize2,
          bus_id: this.selectBusId2,
          base_level: this.scoreValue
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            if (extract === 0) {
              this.data2 = res.data.data.list
              this.total2 = res.data.data.count
            } else {
              let list = res.data.data.list
              let arr = []
              if (Array.isArray(list)) {
                list.forEach(item => {
                  arr.push(this.packRow(item))
                })
              }
              let exColumns = [
                { title: '上课时间', key: 'class_time' },
                { title: '评价会员', key: 'username' },
                { title: '私教课', key: 'card_name' },
                { title: '教练', key: 'coach_name' },
                { title: '评价时间', key: 'comment_time' },
                { title: '综合得分', key: 'average' },
                { title: '评分明细', key: 'assessDetail' },
                { title: '评价标准', key: 'base_level_copy' },
                { title: '评价标签', key: 'mark' },
                { title: '评价内容', key: 'advice' },
                { title: '追评内容', key: 'add_advice' },
                { title: '追评时间', key: 'add_advice_time_date' },
                { title: '状态', key: 'is_reply' },
                { title: '商家回复', key: 'reply' },
                { title: '回复时间', key: 'reply_time_date' }
              ]
              this.$refs.export.export({
                filename: `泳教评价明细统计(${this.dateRange2[0]}~${this.dateRange2[1]})`,
                columns: exColumns,
                data: arr
              })
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    packRow(item) {
      if (item.class_time) {
        item.class_time = this.formatDate(new Date(item.class_time * 1000), 'yyyy-MM-dd HH:mm:ss')
      }
      if (item.comment_time) {
        item.comment_time = this.formatDate(new Date(item.comment_time * 1000), 'yyyy-MM-dd HH:mm:ss')
      }
      if (Array.isArray(item.set_type_json)) {
        let tagStr = ''
        const len = item.set_type_json.length
        item.set_type_json.forEach((tag, index) => {
          tagStr += tag.value + ': ' + tag.star + '分'
          if (index !== len - 1) {
            tagStr += ', '
          }
        })
        item.assessDetail = tagStr
      }
      if (Array.isArray(item.tag_json)) {
        let tagStr = ''
        const len = item.tag_json.length
        item.tag_json.forEach((tag, index) => {
          tagStr += tag.value
          if (index !== len - 1) {
            tagStr += ', '
          }
        })
        item.mark = tagStr
      }
      if (!item.add_advice) {
        item.add_advice = ''
      }
      if (!item.reply) {
        item.reply = ''
      }
      item.is_reply = item.is_reply !== '0' ? '已回复' : '未回复'
      return item
    },
    exportCsv1() {
      this.getTab1List(1)
    },
    exportCsv2() {
      this.getTab2List(1)
    },
    pageSizeChanged1(size) {
      this.pageNo1 = 1
      this.pageSize1 = size
      this.getTab1List()
    },
    pageSizeChanged2(size) {
      this.pageNo2 = 1
      this.pageSize2 = size
      this.getTab2List()
    },
    replyItem(item) {
      const { user_id, sign_log_id } = item
      queryEvaluationDetail({ bus_id: this.selectBusId2, user_id, sign_log_id }).then(res => {
        if (res.data.errorcode == 0) {
          this.$refs.evaluationModel.beginReply(res.data.data)
        }
      })
    },
    onReply(replyData) {
      addFirstReply({
        ...replyData,
        ...{
          bus_id: this.selectBusId2
        }
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$Message.success('回复成功')
          this.getTab2List()
          this.$refs.protocolEdit.handleCancel()
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap {
    border-top: 0;
  }
}

.box {
  .tab-head {
    background-color: white;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 50px;
  }

  .total-stat {
    background-color: white;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    border-top: @border;
    border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 24px;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
}
</style>
