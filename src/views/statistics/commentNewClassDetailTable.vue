<template>
      <div class="box tab-table-wrap customized-tabs">
                <div class="tab-head">
                    <Select v-model="selectBusId1" style="margin-right:20px;width:200px" @on-change="handleStoreChange(1)" filterable>
                      <Option
                        v-for="item in storeList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.name }}</Option>
                    </Select>
                    <Select v-model="selectedClass1" placeholder="全部团课" style="margin-right:20px;width:200px" @on-change="handleStoreChange(2)" filterable>
                      <Option
                        v-for="item in classList1"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.class_name }}</Option>
                    </Select>
                    <DatePickerWithButton v-if="days.length" :days.sync="days" :options="options1" @on-change="dateChange1"></DatePickerWithButton>
                    <!-- <AdminRegion v-model="choseGroup1" url="/Web/CoachGroup/getCoachGroupList" :busId="selectBusId1" :multiple="false" placeholder="请选择组" style="width: 180px;max-width: none; margin-left: 10px;" /> -->
                    <!-- <Select class="group-select" style="margin-left: 20px; width: 240px" v-model="selectedGroup"
                          @on-change="handleGymChanged" filterable v-if="groupList">
                    <Option class="group" :value="'group' + groupList.group_id">{{ groupList.name }}</Option>
                    <Option v-for="coach in groupList.coach_list" style="padding-left: 30px" :key="coach.coach_id" :value="coach.coach_id">{{ coach.coach_name}}</Option>
                    <template v-if="groupList.son" v-for="(secondGroup, secondIndex) in groupList.son">

                      <Option v-if="secondGroup.son && secondGroup.son.length || (secondGroup.coach_list && secondGroup.coach_list.length)"
                        class="group" :value="'group' + secondGroup.group_id">{{ secondGroup.name }}</Option>
                      <Option style="padding-left: 30px" v-for="coach in secondGroup.coach_list" :key="coach.coach_id + secondIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>

                      <template v-for="(thirdGroup, thirdIndex) in secondGroup.son">
                        <Option v-if="thirdGroup.son && thirdGroup.son.length || (thirdGroup.coach_list && thirdGroup.coach_list.length)"
                          class="group" :value="'group' + thirdGroup.group_id">{{ thirdGroup.name }}</Option>
                        <Option style="padding-left: 30px" v-for="coach in thirdGroup.coach_list" :key="coach.coach_id + thirdIndex" :value="coach.coach_id">{{ coach.coach_name }}</Option>
                      </template>

                    </template>
                  </Select> -->
                  <div style='margin-left:auto;'>
                   <Tooltip placement="bottom-end" >
                    好评/差评计算规则
                     <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                     <div slot='content' >
                        <p>当设置打分星级为1～10星时</p>
                        <p>大于7星为好评,小于4星为差评</p>
                        <p>当设置打分星级为1～5星时</p>
                        <p>大于3星为好评,小于3星为差评</p>
                        <p>当设置打分星级为1～3星时</p>
                        <p>大于2星为好评,小于2星为差评</p>
                     </div>
                    </Tooltip>
                    </div>
                </div>
                <div class="total-stat" v-if="commentInformation.length">
                    <div class="stat" v-for="(tip, index) in TAB_TEXT" :key="index">
                        <h3>{{ commentInformation[index] }}</h3>
                        <p>{{tip.name}}
                            <Tooltip v-if="tip.tip" :content="tip.tip" placement="bottom-end">
                            <icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
                            </Tooltip>
                         </p>
                    </div>
                </div>
                <Table :columns="columns1" :data="data1"></Table>
                <footer>
                    <!-- <Button @click="exportCsv1">导出Excel</Button>
                    <Export ref="export"></Export> -->
                    <Page :total="total1" :history="false" @on-change="handlePageChange1" @on-page-size-change="pageSizeChanged1" show-total show-sizer></Page>
                </footer>
    </div>
</template>
<script>
// import { mapActions, mapGetters } from 'vuex';
import { mapState } from 'vuex'

import DatePickerWithButton from 'components/picker/datePickerWithButton';
import AdminRegion from 'components/form/adminRegion.vue';
import Export from 'src/components/Export';

const TAB_TEXT = [{name:'开课次数'}, {name:'评价次数'}, {name:'上课人数'}, 
{name:'评价率',tip:'评价次数【除以】上课人数【乘以】100%'},
 {name:'好评率',tip:'针对课后综合评价，满足好评的次数和【除以】评价次数【乘以】100%'},
  {name:'差评率',tip:'针对课后综合评价，满足差评的次数和【除以】评价次数【乘以】100%'}, {name:'追评次数'}, 
  {name:'追评率',tip:'追评次数【除以】评价次数【乘以】100%'}];

export default {
    name: 'commentNewClassDetailTable',
    components: { DatePickerWithButton, AdminRegion, Export },
    // computed: {
    //   ...mapGetters(['coachGroupList'])
    // },
    computed: {
      ...mapState(['busId'])
    },
    data() {
        const formatDate = (source, format) => {
            const o = {
                'M+': source.getMonth() + 1, // 月份
                'd+': source.getDate(), // 日
                'H+': source.getHours(), // 小时
                'm+': source.getMinutes(), // 分
                's+': source.getSeconds(), // 秒
                'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
                'f+': source.getMilliseconds() // 毫秒
            };
            if (/(y+)/.test(format)) {
                format = format.replace(RegExp.$1, (source.getFullYear() + '').substr(4 - RegExp.$1.length));
            }
            for (let k in o) {
                if (new RegExp('(' + k + ')').test(format)) {
                format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
                }
            }
            return format;
        }
        return {
            formatDate,
            tabName: '',
            days:[],
            dateRange1: '',
            options1: {
                disabledDate(date) {
                    return date && date.getTime() > Date.now();
                }
            },
            commentInformation: [0,0,0,0,0,0,0,0],
            columns1: [
                {
                    title: '课程名称',
                    key: 'class_name',
                },
                {
                    title: '上课时间',
                    key: 'date_start_time_copy'
                },
                 {
                    title: '上课教练',
                    key: 'coach_name'
                },
                {
                    title: '上课人数',
                    key: 'sign_number_nums'
                },
                {
                    title: '评价次数/率',
                    key: 'comment_nums',
                    render: (h, param)=>{
                        return (<span>{param.row.comment_nums}/{(param.row.evaluation_rate)+'%'}</span>);
                    }
                },
                {
                    title: '好评次数/率',
                    key: 'good_nums',
                    render: (h, param)=>{
                        return (<span>{param.row.good_nums}/{(param.row.good_rate)+'%'}</span>);
                    }
                },
                {
                    title: '差评次数/率',
                    key: 'bad_nums',
                    render: (h, param)=>{
                        return (<span>{param.row.bad_nums}/{(param.row.bad_rate)+'%'}</span>);
                    }
                },
                {
                    title: '追评次数/率',
                    key: 'add_comment_nums',
                    render: (h, param)=>{
                        return (<span>{param.row.add_comment_nums}/{(param.row.add_comment_rate)+'%'}</span>);
                    }
                }
            ],
            data1: [],
            total1: 0,
            pageSize1: 10,
            pageNo1: 1,

            selectedClass1: '',
            classList1: null,
            groupId: '',
            coachId: '',
            storeList: [],
            selectBusId1: '',
            TAB_TEXT,
        }
    },
    watch: {
    //   choseGroup1: function(val, old) {
    //     if (val) {
    //       const groupIdArr = val.split("_");
    //       if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
    //         this.pageNo1 = 1;
    //         this.choseGroup1Id = groupIdArr[0];
    //       }
    //     } else {
    //       this.choseGroup1Id = ""
    //     }
    //     this.getOverall(1);
    //     this.getTab1List();
    //   },
    //   choseGroup2: function(val, old) {
    //     console.log(val);
    //     const groupIdArr = val.split("_");
    //     if (Array.isArray(groupIdArr) && groupIdArr.length === 3) {
    //       this.choseGroup2CoachId = groupIdArr[0];
    //       this.getOverall();
    //       this.getTab2List();
    //     }
    //   }
    },
    mounted() {
      this.selectBusId1 = this.$route.query.selectBusId1
      this.selectedClass1 = this.$route.query.selectedClass1
      this.dateRange1 = this.$route.query.dateRange1
      this.days = [new Date(this.dateRange1[0]).getTime(),new Date(this.dateRange1[1]).getTime()]
      this.getStoreList()
    },
    // created () {
    //   this.selectBusId1 = this.busId
    //   this.getStoreList()
    //   this.getClassList()
    // },
    methods: {
        getClassList() {
          return this.$service.post('/Web/open_class/getListsByBusId', {
              start_time: this.dateRange1[0],
                end_time: this.dateRange1[1],
            bus_id: this.selectBusId1
          }).then(res => {
            if (res.data.errorcode === 0) {
                this.classList1 = res.data.data
            }
          })
        },
        getStoreList() {
            return this.$service
            .get('/Web/Business/get_bus_list')
            .then(res => {
                if (res.data.errorcode === 0) {
                this.storeList = res.data.data.bus_list
                }
            })
        },
        handleStoreChange(sequence) {
          if (sequence === 1) {
            this.getClassList().then(() => {
              this.pageNo1 = 1;
              this.selectedClass1 = '';
              this.getTab1List();
            })
          } else if (sequence === 2) {
              this.pageNo1 = 1;
              this.getTab1List();
          }
        },
        dateChange1(dateRange) {
             this.pageNo1 = 1;
            this.dateRange1 = dateRange;
              this.getClassList().then(() => {
              this.getTab1List();
            })
        },
        handlePageChange1(pageNo) {
            this.pageNo1 = pageNo;
            this.getTab1List();
        },
        getTab1List(extract = 0) {
            return this.$service.post('/web/course_comment/getListsStatisticsByOpenClass', {
                start_time: this.dateRange1[0],
                end_time: this.dateRange1[1],
                class_id: this.selectedClass1,
                is_export: extract,
                page_no: !!extract?'':this.pageNo1,
                page_size: !!extract?'':this.pageSize1,
                bus_id: this.selectBusId1
            }).then(res => {
                if (res.data.errorcode == 0) {
                    if (extract === 0) {
                        this.data1 = res.data.data.list;
                        this.total1 = res.data.data.count;
                        this.commentInformation[0] = res.data.data.total.open_class_nums
                        this.commentInformation[1] = res.data.data.total.comment_nums
                        this.commentInformation[2] = res.data.data.total.sign_number_nums
                        this.commentInformation[3] = (res.data.data.total.evaluation_rate)+'%'
                        this.commentInformation[4] = (res.data.data.total.good_rate)+'%'
                        this.commentInformation[5] = (res.data.data.total.bad_rate)+'%'
                        this.commentInformation[6] = res.data.data.total.add_comment_nums
                        this.commentInformation[7] = (res.data.data.total.add_comment_rate)+'%'
                    } else {
                        let list = res.data.data.list;
                        if (Array.isArray(list)) {
                            list.forEach(item => {
                                if (Array.isArray(item.tag_arr)) {
                                    let tagStr = '';
                                    const len = item.tag_arr.length;
                                    item.tag_arr.forEach((tag, index) => {
                                        tagStr += "(" + tag.name + ' X ' + tag.num + ")";
                                        if (index !== (len-1)) {
                                            tagStr += ', '
                                        }
                                    });
                                    item.mark = tagStr;
                                }
                            });
                        }
                        this.$refs.export.export({
                            filename: `私教评价汇总统计(${this.dateRange1[0]}~${this.dateRange1[1]})`,
                            columns: this.columns1,
                            data: list
                        });
                    }
                } else {this.$Message.error(res.data.errormsg);}
            });
        },
        exportCsv1() {
            this.getTab1List(1);
        },
        pageSizeChanged1(size) {
            this.pageNo1 = 1;
            this.pageSize1 = size;
            this.getTab1List();
        },
    }
}
</script>

<style lang="less" scoped>
@border: 1px solid #dcdcdc;

.ivu-tabs-tabpane {
  .table-wrap{
    border-top: 0;
  }
}

.box {
    .tab-head {
        background-color: white;
        padding-left: 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 50px;
    }

    .total-stat {
      background-color: white;
      position: relative;
      width: 100%;
      box-sizing: border-box;
      padding: 0 40px 15px 40px;
      height: 135px;
      border-top: @border;
      border-bottom: @border;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      > span {
        position: absolute;
        left: 22px;
        top: 13px;
        color: #666;
        font-size: 14px;
      }
      .stat {
        h3 {
          font-size: 40px;
          color: #52a4ea;
          font-weight: normal;
          margin-top: 20px;
          span {
            font-size: 24px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
        }
      }
      > b {
        width: 1px;
        height: 30px;
        background-color: #ccc;
      }
    }
}
</style>