<style lang="less" scoped>
  .table-nav {
    padding: 30px 30px 1px;
    border: 0;

    .nav {
      padding: 24px 16px;
      margin-bottom: 30px;
      display: flex;
      border: 1px solid #e7e7e7;

      .class {
        display: flex;
        margin-right: 36px;

        .icon {
          height: 32px;
          display: flex;
          align-items: center;

          > img {
            height: 18px;
          }
        }

        h3 {
          font-size: 16px;
          margin-left: 8px;
          height: 32px;
          line-height: 32px;
          font-weight: normal;
        }
      }

      .menus {
        display: flex;
        flex-wrap: wrap;
        flex: 1;

        span {
          line-height: 32px;
          font-size: 14px;
          flex: 0 0 20%;
        }
      }
    }
  }
</style>

<template>
  <div>
    <router-view v-if="$route.name !== '统计报表'"></router-view>
    <div v-else class="table-wrap table-nav">
      <div v-for="(item, index) in menus" :key="index" class="nav">
        <div class="class">
          <div class="icon">
            <img :src="item.img" />
          </div>
          <h3>{{ item.className }}</h3>
        </div>
        <div class="menus">
          <span v-for="menu in item.menu" :key="menu.name">
            <router-link 
              v-if="menu.version !== 'base'"
              :to="{path: menu.path, name: menu.routeName, params: menu.params, query: menu.query}">
              {{ menu.name }}
            </router-link>
            <a v-else :href="getPathNew(menu)" @click.prevent="goPathNew(menu)"> {{ menu.name }}</a>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import statSold from 'src/assets/img/stat-sold.png';
  import statMember from 'src/assets/img/stat-member.png';
  import statMembership from 'src/assets/img/stat-membership.png';
  import statCoach from 'src/assets/img/stat-coach.png';
  import { getNewHost } from 'utils/config'
  const path = '/stat/menus/';

  const IMG = {
    业绩类报表: statSold,
    会员类报表: statMember,
    私教类报表: statCoach,
    会籍类报表: statMembership,
    泳教类报表: statCoach,
    门店汇总类报表: statSold,
    订场类报表: statCoach,
    团课类报表: statCoach,
  };

  const MENU_CLASS = ['1_achievements', '2_members', '3_coaches', '4_membership', '5_swimming', '6_bus_statistics', '7_booking_space', '8_open_class'];
  const MENU_CLASS_OBJ = {
    '1_achievements': '业绩类报表',
    '2_members': '会员类报表',
    '4_membership': '会籍类报表',
    '3_coaches': '私教类报表',
    '5_swimming': '泳教类报表',
    '6_bus_statistics': '门店汇总类报表',
    '7_booking_space': '订场类报表',
    '8_open_class': '团课类报表',
  };
  // 后端返回英文名和前端显示中文菜单名一一对应
  const MENU_CHART = {
    [MENU_CLASS[0]]: {
      get_bus_income_details: '收入分析',
      cardOrderList: '订单报表',
      get_salary_list: '薪资报表',
      financial_allocation2: '收入分摊',
      deposit_card_statistics: '储值卡使用统计',
      deposit_card_statistics_detail: '储值卡消费品类详情',
      new_card_analysis: '卡课分析',
    },
    [MENU_CLASS[1]]: {
      user_classify: '会员分析',
      customer_behave_analyze_data: '会员到场分析',
      pt_class_analyze_data: '私教会员分析',
      bus_daily_purchase_card_statistics_data: '购续会员卡情况',
      bus_daily_purchase_pt_statistics_data: '购续私教情况',
      user_sign_statistics_data: '训练次数统计',
      new_join_report: '新入会会员月报',
      new_transfer_report: '会员转化分析',
    },
    [MENU_CLASS[2]]: {
      // get_class_statistics: '上课统计',
      new_pt_statistics: '私教课时统计',
      coach_statistics_detail_data: '私教业绩统计',
      coach_brief_review: '私教业绩概况',
      getPrivateCoachAchievementMonthTotalData: '私教业绩月报',
      coach_assessment: '私教课评价报表',
      // coach_assigning_task: '私教分配任务完成情况',
      coach_class_statistics_data: '私教课时概况',
      team_class_private_statistics_data: '私教班课时统计',
    },
    [MENU_CLASS[3]]: {
      membership_statistics_data: '会籍业绩概况',
      getMemberShipAchievementMonthTotalData: '会籍业绩月报',
      ms_brief_review: '会籍工作概况',
      ms_statistics: '会籍业绩统计',
      membership_assigning_task: '会籍分配任务完成情况'
    },
    [MENU_CLASS[4]]: {
      swimming_class_period_statistics: '泳教课时统计',
      swimming_achievement_statistics: '泳教业绩统计',
      swimming_brief_review: '泳教业绩概况',
      getSwimCoachAchievementMonthTotalData: '泳教业绩月报',
      swimming_class_comment: '泳教课评价报表',
      swimming_class_statistics_data: '泳教课时概况',
      team_class_swim_statistics_data: '泳教班课时统计',
    },
    [MENU_CLASS[5]]: {
      bus_statistics_data: '总业绩汇总',
      bus_statistics_detail_ms_data: '会籍业绩汇总',
      bus_statistics_detail_pt_data: '私教业绩汇总',
      bus_statistics_detail_swim_data: '泳教业绩汇总',
      bus_fincl_flow_data: '业务流水汇总',
      bus_statistics_class_pt_data: '私教课时汇总',
      bus_statistics_class_swim_data: '泳教课时汇总'
    },
    [MENU_CLASS[6]]: {
      space_income_statistics: '场地收益概况',
      space_income_list: '场地收益明细',
      san_log_statistics: '散场票收入报表'
    },
    [MENU_CLASS[7]]: {
      open_class_comment_statistics_data: '团课评价报表',
      new_public_class_statistics: '操课课时统计',
      new_class_statistics: '团课课时统计',
      course_effect_statistics: '课程效果统计',
      classroom_statistics: '教室使用统计',
      openclass_user_month_all_statistics: '会员上课统计',
      openclass_sign_lists: '会员上课明细',
    }
  };
  // 最终要将后端返回的数据对应转化为类似如下格式 (menus.menu)
  const MENU = {
    业绩类报表: {
      收入分析: {
        name: '收入分析',
        path: `${path}revenueStatis`
      },
      卡课分析: {
        name: '卡课分析',
        version: 'base', 
        path: `/stat/table-nav/card-report`
      },
      订单报表: {
        name: '订单报表',
        path: `/finance/orderList`
      },
      薪资报表: {
        name: '薪资报表',
        path: `/finance/salaryList`
      },
      收入分摊: {
        name: '收入分摊',
        path: `${path}financialAllocation2`
      },
      储值卡使用统计: {
        name: '储值卡使用统计',
        path: `/stat/menus/depositCard`
      },
      储值卡消费品类详情: {
        name: '储值卡消费品类详情',
        path: `/stat/menus/depositCardDetail`
      }
    },
    会员类报表: {
      会员分析: {
        name: '会员分析',
        path: `${path}analysis`
      },
      会员到场分析: {
        name: '会员到场分析',
        path: `${path}coachingBehavior`
      },
      私教会员分析: {
        name: '私教会员分析',
        path: `${path}ptClassInfo`
      },
      购续会员卡情况: {
        name: '购续会员卡情况',
        path: `${path}buyAndRenewalBehavior`
      },
      新入会会员月报: {
        name: '新入会会员月报',
        version: 'base', 
        path: `/stat/table-nav/new-member`
      },
      会员转化分析: {
        name: '会员转化分析',
        version: 'base', 
        path: `/stat/table-nav/channel-report`
      },
      购续私教情况: {
        name: '购续私教情况',
        path: `${path}privateSold`
      },
      训练次数统计: {
        name: '训练次数统计',
        path: `${path}signList`
      },
    },
    私教类报表: {
      // 上课统计: {
      //   name: '上课统计',
      //   path: `${path}/#/web/coach/coachStatis`
      // },
      私教课时统计: {
        name: '私教课时统计',
        path: `${path}classStat4Coach`,
        routeName: '私教课时',
        query: {
          type: 2
        }
      },
      私教课时概况: {
        name: '私教课时概况',
        path: `${path}coachCourseOverview`
      },
      私教班课时统计: {
        name: '私教班课时统计',
        path: `${path}privateTeamClass`
      },
      // 私教课时统计: {
      //   name: '私教课时统计',
      //   path: `${path}classStat`,
      //   routeName: '上课统计',
      //   params: {
      //     type: 2
      //   }
      // },
      私教业绩统计: {
        name: '私教业绩统计',
        path: `${path}coach`
      },
      私教业绩概况: {
        name: '私教业绩概况',
        path: `${path}coachbriefReview`
      },
      私教业绩月报: {
        name: '私教业绩月报',
        path: `${path}ptMonthTotal`
      },
      私教课评价报表: {
        name: '私教课评价报表',
        path: `${path}comment4coachTable`
      },
      // 私教分配任务完成情况: {
      //   name: '私教分配任务完成情况',
      //   path: `${path}mission`,
      //   routeName: '分配任务完成情况',
      //   query: {
      //     type: 'coach'
      //   }
      // }
    },
    泳教类报表: {
      泳教课时统计: {
        name: '泳教课时统计',
        path: `${path}classStat4Swim`,
        routeName: '泳教课时',
        query: {
          type: 2
        }
      },
      泳教课时概况: {
        name: '泳教课时概况',
        path: `${path}swimCourseOverview`
      },
      泳教班课时统计: {
        name: '泳教班课时统计',
        path: `${path}swimTeamClass`
      },
      泳教业绩统计: {
        name: '泳教业绩统计',
        path: `${path}swim`
      },
      泳教业绩概况: {
        name: '泳教业绩概况',
        path: `${path}swimBriefReview`
      },
      泳教业绩月报: {
        name: '泳教业绩月报',
        path: `${path}swimMonthTotal`
      },
      泳教课评价报表: {
        name: '泳教课评价报表',
        path: `${path}comment4swimTable`
      }
    },
    会籍类报表: {
      会籍业绩概况: {
        name: '会籍业绩概况',
        path: `${path}msbriefReviewNew`
      },
      会籍工作概况: {
        name: '会籍工作概况',
        path: `${path}msbriefReview`
      },
      会籍业绩月报: {
        name: '会籍业绩月报',
        path: `${path}monthTotal`
      },
      会籍业绩统计: {
        name: '会籍业绩统计',
        path: `${path}membership`
      },
      会籍分配任务完成情况: {
        name: '会籍分配任务完成情况',
        path: `${path}mission`,
        routeName: '分配任务完成情况',
        query: {
          type: 'membership'
        }
      }
    },
    门店汇总类报表: {
      总业绩汇总: {
        name: '总业绩汇总',
        path: `${path}totalPerformanceSummary`
      },
      会籍业绩汇总: {
        name: '会籍业绩汇总',
        path: `${path}membershipPerformanceSummary`
      },
      私教业绩汇总: {
        name: '私教业绩汇总',
        path: `${path}coachPerformanceSummary`
      },
      泳教业绩汇总: {
        name: '泳教业绩汇总',
        path: `${path}swimPerformanceSummary`
      },
      业务流水汇总: {
        name: '业务流水汇总',
        path: `${path}businessFlowSummary`
        // version: 'base', // 需要打开base端页面 
        // path: `/finance/business-log-all`
      },
      私教课时汇总: {
        name: '私教课时汇总',
        path: `${path}coachCourseSummary`
      },
      泳教课时汇总: {
        name: '泳教课时汇总',
        path: `${path}swimCourseSummary`
      }
    },
    订场类报表: {
      场地收益概况: {
        name: '场地收益概况',
        path: '/stat/menus/spaceAnalysis'
      },
      场地收益明细: {
        name: '场地收益明细',
        path: '/stat/menus/spaceAnalysisDetail'
      },
      散场票收入报表: {
        name: '散场票收入报表',
        path: '/stat/menus/spaceIncome'
      }
    },
    团课类报表: {
      操课课时统计: {
        name: '操课课时统计',
        path: `${path}classStat`,
        routeName: '上课统计',
        query: {
          type: 0
        }
      },
      团课课时统计: {
        name: '团课课时统计',
        path: `${path}classStat`,
        routeName: '上课统计',
        query: {
          type: 1
        }
      },
      团课评价报表: {
        name: '团课评价报表',
        path: `${path}commentNewClassTable`
      },
      课程效果统计: {
        name: '课程效果统计',
        path: `${path}classEffectStat`
      },
      教室使用统计: {
        name: '教室使用统计',
        path: `${path}classroomUseStat`
      },
      会员上课统计: {
        name: '会员上课统计',
        path: `${path}memberClassStat`
      },
      会员上课明细: {
        name: '会员上课明细',
        path: `${path}memberAttendClassDetail`
      },
    },
  };
  export default {
    name: 'TableNav',
    data() {
      return {
        menus: {}
      };
    },
    created() {
      if (!window.IS_BRAND_SITE) {
        this.getMenu();
      }
    },
    methods: {
      getPathNew(info) {
        return `${window.location.origin}${info.path}`;
      },
      goPathNew(info) {
        const {params ,query, path } = info;
        window.history.pushState(params || query || {}, '', path);
      },
      getMenu() {
        const url = '/Web/Statistics/getStatisticsMenulist';
        this.$service
          .get(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.calStatMenus(data);
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      calStatMenus(data) {
        const obj = {};
        const menus = {};
        for (let key of Object.keys(data)) {
          obj[key] = data[key].map(menu => {
            return MENU_CHART[key]&&MENU_CHART[key][menu];
          });
        }
        for (let key of Object.keys(obj)) {
          menus[MENU_CLASS_OBJ[key]] = {};
          obj[key].forEach(menu => {
            if (menu) {
              menus[MENU_CLASS_OBJ[key]][menu] = MENU[MENU_CLASS_OBJ[key]][menu];
            }
          });
        }
        // console.log('menus: ', menus)
        this.menus = Object.keys(menus).map(key => {
          return {
            className: key,
            img: IMG[key],
            menu: Object.keys(menus[key]).map(item => menus[key][item])
          };
        });
        // console.log('this.menus: ', this.menus)
      }
    }
  };
</script>
