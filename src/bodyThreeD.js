import Vue from 'vue';
import iView from 'iview';
import axios from 'axios';
import Qs from 'qs';
import { getBaseUrl } from 'utils/config';
const service = axios.create({
  baseURL: getBaseUrl(), // api的base_url
  withCredentials: true,
  headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  transformRequest: [function (data) {
    // `transformRequest` allows changes to the request data before it is sent to the server
    // This is only applicable for request methods 'PUT', 'POST', and 'PATCH'
    return Qs.stringify(data);
  }],
  timeout: 10000        // 请求超时时间
});

Vue.config.productionTip = false
// Vue.use(iView)
Vue.prototype.$service = service;

//google统计
(function (i, s, o, g, r, a, m) {
  i['GoogleAnalyticsObject'] = r;
  i[r] = i[r] || function () {
    (i[r].q = i[r].q || []).push(arguments)
  }, i[r].l = 1 * new Date();
  a = s.createElement(o),
    m = s.getElementsByTagName(o)[0];
  a.async = 1;
  a.src = g;
  m.parentNode.insertBefore(a, m)
})(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

ga('create', 'UA-85778158-1', 'auto');
ga('send', 'pageview');

// 百度统计
var _hmt = _hmt || [];
(function () {
  var hm = document.createElement("script");
  hm.src = "//hm.baidu.com/hm.js?1e4a718ae3d994511ea70b7f6ea1ba75";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
// 百度转化统计
(function () {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?2ef54fb19f1dbb320a11cd949a28b233";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
