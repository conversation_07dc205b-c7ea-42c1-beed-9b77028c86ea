import layout from 'views/layout/';

export default {
  path: '/course',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '课程'
  },
  children: [
    {
      path: 'private',
      alias: '/Web/PtSchedule/pt_schedule_list',
      name: '私教排课',
      props: {
        isSwimFlag: false
      },
      component: resolve => require(['views/course/privateClassTab'], resolve),
      meta: {
        keepAlive: true,
        parentName: '课程'
      }
    },
    {
      path: 'swim',
      alias: '/Web/PtSchedule/swim_schedule_list',
      name: '泳教排课',
      props: {
        isSwimFlag: true
      },
      component: resolve => require(['views/course/privateClassTab'], resolve),
      meta: {
        keepAlive: true,
        parentName: '课程'
      }
      // 2023/03/01 代码与表现不同更正  name: '私教班' -> name: '私教班管理'
    },
    {
      path: 'privateTeam',
      alias: '/Web/TeamclassPrivate/teamclass_list',
      name: '私教班管理',
      component: resolve => require(['views/course/teamClass'], resolve),
      meta: {
        parentName: '课程'
      }
    },
    // 2023/03/01 代码与表现不同更正  name: '泳教班' -> name: '泳教班管理'
    {
      path: 'swimTeam',
      alias: '/Web/TeamclassSwim/teamclass_list',
      name: '泳教班管理',
      component: resolve => require(['views/course/teamClass'], resolve),
      meta: {
        parentName: '课程'
      }
    },
    {
      path: 'addTeamClass',
      name: '添加班级',
      component: resolve => require(['views/course/addTeamClass'], resolve),
      meta: {
        parentName: '团课管理'
      }
    },
    // 2023/03/01 注释(74行)原路由 代码与表现不同更正
    {
      path: 'scheduleWeek',
      name: '团课排课',
      alias: '/Web/CourseSchedule/get_class_list',
      component: resolve => require(['views/course/scheduleWeek'], resolve),
      meta: {
        parentName: '课程'
      }
    },
    {
      path: 'schedulePreview',
      alias: '/Web/CourseSchedule/schedulePreview',
      name: '课表预览',
      component: resolve => require(['views/course/schedulePreview'], resolve),
      meta: {
        parentName: '课程'
      }
    },
    // 注释
    // {
    //   path: 'scheduleWeek',
    //   alias: '/Web/CourseSchedule',
    //   component: resolve => require(['views/course/schedule'], resolve),
    //   children: [
    //     {
    //       path: 'scheduleWeek',
    //       name: '排课',
    //       alias: '/Web/CourseSchedule/get_class_list',
    //       component: resolve => require(['views/course/scheduleWeek'], resolve),
    //       meta: {
    //         parentName: '排课'
    //       }
    //     },
    //     {
    //       path: 'schedulePreview',
    //       alias: '/Web/CourseSchedule/schedulePreview',
    //       name: '课表预览',
    //       component: resolve => require(['views/course/schedulePreview'], resolve),
    //       meta: {
    //         parentName: '排课'
    //       }
    //     },
    //   ],
    //   meta: {
    //     breadText: '课程'
    //   },
    // },
    {
      path: 'openClass',
      alias: '/Web/OpenClass/get_open_class_list',
      name: '团课管理',
      component: resolve => require(['views/course/openClass'], resolve),
      meta: {
        parentName: '课程',
        breadText: '课种管理'
      },
      children: [
        {
          path: 'addClass',
          name: '添加团课',
          component: resolve => require(['views/course/addClass'], resolve),
          meta: {
            parentName: '团课管理'
          }
        },
        {
          path: 'editClass/:id',
          name: '团课编辑',
          component: resolve => require(['views/course/addClass'], resolve),
          meta: {
            parentName: '团课管理'
          }
        },
        {
          path: 'setConfig',
          name: '适合卡种配置',
          component: resolve => require(['views/course/setClassConfig'], resolve),
          meta: {
            parentName: '团课管理'
          }
        },
      ]
    }
  ]
}
