import layout from 'views/layout/';

export default {
  path: '/management',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '管理'
  },
  children: [
    {
      path: 'dynamic',
      name: '会员动态管理',
      alias: '/Web/UserDynamic/get_user_dynamic_list',
      component: () => import('views/system/dynamic'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'upData',
      name: '数据导入',
      alias: '/Web/Excel/index',
      component: () => import('views/system/upData'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'opRecord',
      name: '操作记录',
      alias: '/Web/OperatingRecord/get_operating_record_all',
      component: () => import('views/member/operationRecord'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'card',
      name: '会员卡管理',
      alias: '/Web/Member/card',
      component: () => import('views/member/cardList'),
      meta: {
        breadText: '会员卡设置',
        parentName: '管理'
      },
      children: [
        {
          path: 'cardRules',
          name: '购卡规则设置',
          alias: '/Web/CardSaleRule/getRuleList',
          component: () => import('views/member/cardRulesList'),
          meta: {
            breadText: '规则设置',
            parentName: '管理'
          }
        },
      ]
    },
    {
      path: 'nonMemberCard',
      alias: '/Web/SanRule/getList',
      name: '散场票设置',
      // component: resolve =>
      //   require(['views/reservation/nonMemberCard'], resolve),
      component: resolve => require(['views/stadium/invoice/list'], resolve),
      meta: {
        keepAlive: true,
        parentName: '管理'
      }
    },
    {
      path: 'punisher',
      alias: '/Web/MissPunitive/get_miss_punitive_list',
      name: '爽约惩罚',
      component: resolve => require(['views/reservation/punisher'], resolve),
      meta: {
        keepAlive: true,
        parentName: '管理'
      }
    },
    {
      path: 'feedBack',
      name: '意见反馈',
      alias: '/Web/Comment/index',
      component: resolve => require(['views/management/feedBack'], resolve),
      meta: {
        parentName: '管理',
        keepAlive: true
      }
    },
    {
      path: 'brandList',
      name: '手环管理',
      alias: '/Web/Sign/sign_card_number_list',
      component: resolve => require(['views/management/brandList'], resolve),
      meta: {
        parentName: '管理',
        keepAlive: true,
        breadText: '手环设置'
      }
    },
    {
      path: 'extendExpr',
      name: '批量延期',
      alias: '/Web/BatchDelayed/batch_delayed_list',
      component: () => import('views/management/extendExpr'),
      meta: {
        parentName: '管理',
        breadText: '批量延时'
      }
    },
    {
      path: 'setDelayRule',
      name: '设置批量延时',
      alias: '/Web/BatchDelayed/add_batch_delayed',
      component: () => import('views/management/setDelayRule'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'district',
      name: '门店区域设置',
      alias: '/web/BusRegion/get_bus_list',
      component: () => import('views/management/district/index'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'approval',
      name: '审批',
      alias: '/Web/Approve/pc_approve_list',
      component: () => import('views/management/approval/index'),
      meta: {
        parentName: '管理'
      }
    },
    {
      path: 'pointAndCoins',
      name: '积分管理',
      alias: '/Web/Point/getPointList',
      component: () => import('views/management/pointAndCoins'),
      meta: {
        parentName: '管理',
        breadText: '积分管理'
      },
      children: [
        {
          path: 'pointRules',
          name: '积分规则',
          // alias: '/Web/',
          component: () => import('views/management/pointAndCoins/pointRulesPage'),
          meta: {
            parentName: '管理',
            breadText: '积分发放规则',
          }
        },
        {
          path: 'originalPointList',
          name: '旧版积分列表',
          component: () => import('views/management/pointAndCoins/oldPointList'),
          meta: {
            parentName: '管理',
          }
        },
      ]
    }
  ]
}
