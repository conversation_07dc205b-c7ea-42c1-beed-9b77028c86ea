import layout from 'views/layout/';

export default [
  {
    path: '/activity',
    component: layout,
    meta: {
      breadNotLink: true,
      breadText: '营销',
    },
    children: [
      {
        path: 'memberGroup',
        name: '会员分群',
        alias: '/Web/MemberGroup/list',
        component: () => import('views/activity/memberGroup'),
        meta: {
          parentName: '会员分群',
        },
      },
      {
        path: 'memberGroupAdd',
        name: '添加/编辑分群',
        component: () => import('views/activity/memberGroupAdd'),
        meta: {
          parentName: '会员分群',
        },
      },
      {
        path: 'pageConfig',
        name: '会员端页面配置',
        alias: '/web/pageDiy/diyList',
        component: () => import('views/system/miniProgramPageConfig'),
        meta: {
          parentName: '营销',
          breadText: '会员端自定义',
        },
        children: [
          {
            path: 'config',
            name: '配置',
            component: () => import('views/system/miniProgramPageConfig/config'),
            meta: {
              parentName: '营销',
            },
          },
        ],
      },
      {
        path: 'diyNew',
        alias: '/Web/NewMemberTemplateSetting/get_bus_setting',
        name: '新版会员端自定义',
        component: () => import('views/system/diyNew'),
        meta: {
          parentName: '营销',
        },
      },
      {
        path: 'verifyExpCard',
        alias: '/Web/Active/check_serial_num',
        name: '体验卡验证',
        component: (resolve) => require(['views/activity/verifyExpCard'], resolve),
        meta: {
          parentName: '营销',
        },
      },
      {
        path: 'passengerList',
        alias: '/Web/Activity/pc_passenger_list',
        name: '客带客活动列表',
        component: (resolve) => require(['views/activity/passenger/passengerList.vue'], resolve),
        meta: {
          parentName: '营销',
          breadText: '客带客活动',
        },
      },
      {
        path: 'passengerStatisticsDetailList',
        name: '客带客明细统计',
        alias: '/Web/Activity/pc_passenger_statistics_detail_list',
        component: (resolve) => require(['views/activity/passenger/passengerStatisticsDetailList.vue'], resolve),
        meta: {
          parentName: '营销',
          breadText: '客带客明细统计',
        },
      },
      {
        path: 'list',
        alias: '/Web/Activity/pc_get_activity_list',
        name: '活动列表',
        component: (resolve) => require(['views/activity/activityList'], resolve),
        meta: {
          parentName: '营销',
          breadText: '营销活动',
        },
        children: [
          {
            path: 'number',
            name: '报名信息',
            alias: '/Web/Activity/pc_activity_sign',
            component: () => import('views/activity/signNumberDetail'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'sellCardDetail',
            name: '购卡信息',
            alias: '/Web/Activity/sellcard_list',
            component: () => import('views/activity/sellCardDetail'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'refundActivityDetail',
            name: '购卡信息',
            component: () => import('views/activity/refundActivityDetail'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'detail',
            name: '活动详情',
            alias: '/Web/Activity/get_activity_info',
            component: () => import('views/activity/add'),
            meta: {
              parentName: '营销',
              breadText: '活动编辑',
            },
          },
        ],
      },
      {
        path: 'bonusList',
        name: '营销红包',
        alias: '/Web/Bonus/get_bonus_list',
        component: () => import('views/activity/bonusList'),
        meta: {
          keepAlive: true,
          parentName: '营销',
        },
        children: [
          {
            path: 'bonusEdit/:bonusId',
            name: '红包编辑',
            component: () => import('views/activity/bonusEdit'),
            meta: {
              parentName: '营销',
              showMap: true,
            },
          },
          {
            path: 'bonusAdd',
            name: '红包添加',
            component: () => import('views/activity/bonusEdit'),
            meta: {
              parentName: '营销红包',
            },
          },
          {
            path: 'bonusRecord/:bonusId',
            name: '领奖记录',
            component: () => import('views/activity/bonusRecord'),
            meta: {
              parentName: '营销红包',
            },
          },
        ],
      },
      {
        path: 'discount',
        name: '折扣券',
        alias: '/Web/Coupon/coupon_list',
        component: () => import('views/activity/discount/list'),
        meta: {
          parentName: '营销',
        },
        children: [
          {
            path: 'edit',
            name: '折扣券设置',
            component: () => import('views/activity/discount/edit'),
            meta: {
              parentName: '营销',
              breadText: '编辑',
            },
          },
          {
            path: 'receive',
            name: '折扣券领取记录',
            component: () => import('views/activity/discount/receiveRecords'),
            meta: {
              parentName: '营销',
              breadText: '领取记录',
            },
          },
          {
            path: 'out',
            name: '折扣券发放记录',
            component: () => import('views/activity/discount/outRecords'),
            meta: {
              parentName: '营销',
              breadText: '发放记录',
            },
          },
        ],
      },
      {
        path: 'groupBuyList',
        name: '团购活动列表',
        alias: '/Web/Groupbuy/groupbuy_list',
        component: () => import('views/activity/groupBuy/index'),
        meta: {
          parentName: '营销',
        },
      },
      {
        path: 'groupBuyDetail',
        name: '拼团配置',
        component: () => import('views/activity/groupBuy/detail'),
        meta: {
          parentName: '营销',
        },
      },
      {
        path: 'groupBuyStep',
        name: '团购接龙',
        alias: '/Web/ChainsFission/lists',
        component: () => import('views/activity/groupBuy/stepTabs'),
        meta: {
          parentName: '营销',
        },
        children: [
          {
            path: 'clientDetail',
            name: '客户明细统计',
            component: () => import('views/activity/groupBuy/stepStatisticClient'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'startDetail',
            name: '开团明细统计',
            component: () => import('views/activity/groupBuy/stepStatisticStart'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'editActivity',
            name: '团购接龙活动详情',
            component: () => import('views/activity/groupBuy/stepActivityEdit'),
            meta: {
              parentName: '营销',
              breadText: '添加活动',
            },
          },
        ],
      },
      {
        path: 'plan',
        name: '运营计划',
        alias: '/Web/MemberGroup/cronList',
        component: () => import('views/activity/plan'),
        meta: {
          parentName: '营销',
        },
        children: [
          {
            path: 'edit',
            name: '运营计划设置',
            component: () => import('views/activity/plan/edit'),
            meta: {
              parentName: '营销',
              // breadText: '运营计划设置'
            },
          },
          {
            path: 'data',
            name: '运营计划数据',
            component: () => import('views/activity/plan/data'),
            meta: {
              parentName: '营销',
              breadText: '运营数据',
            },
          },
          {
            path: 'detail',
            name: '运营计划详情',
            component: () => import('views/activity/plan/detail'),
            meta: {
              parentName: '营销',
              breadText: '计划详情',
            },
          },
        ],
      },
      {
        path: 'reminder',
        alias: '/Web/SendMessage/getMsgList',
        component: () => import('views/activity/SMS'),
        meta: {
          parentName: '营销',
        },
        children: [
          {
            path: '/',
            name: '消息推送',
            component: () => import('views/activity/SMS/msg'),
            meta: {
              parentName: '营销',
            },
            children: [
              {
                path: 'detail',
                name: '短信通知',
                component: () => import('views/activity/SMS/detail'),
                meta: {
                  parentName: '营销',
                },
              },
              {
                path: 'wxDetail',
                name: '微信通知',
                component: () => import('views/activity/SMS/wxMsgDetail'),
                meta: {
                  parentName: '营销',
                },
              },
              {
                path: 'rules',
                name: '短信屏蔽规则',
                component: () => import('views/activity/SMS/rules'),
                meta: {
                  parentName: '营销',
                },
              },
            ],
          },
        ],
      },
      {
        path: '/goods',
        name: '会员端商城',
        alias: '/Web/Goods/goods_list',
        component: () => import('views/goods/index'),
        meta: {
          keepAlive: true,
          parentName: '营销',
        },
        children: [
          {
            path: 'add',
            name: '商品添加',
            component: () => import('views/goods/add'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'addPointGoods',
            name: '积分商品添加',
            component: () => import('views/goods/addPointGoods'),
            meta: {
              parentName: '营销',
              breadText: '商品添加',
            },
          },
          {
            path: 'exchange',
            name: '兑换记录',
            alias: '/Web/Exchange/exchange_list',
            component: () => import('views/goods/exchange'),
            meta: {
              parentName: '营销',
            },
          },
          {
            path: 'exchangePoint',
            name: '积分兑换记录',
            component: () => import('views/goods/exchangePoint'),
            meta: {
              parentName: '营销',
              breadText: '兑换记录',
            },
          },
        ],
      },
    ],
  },
]
