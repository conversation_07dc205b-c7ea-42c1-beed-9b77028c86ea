import layout from 'views/layout/';

export default {
  path: '/invoice',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '发票'
  },
  children: [
    {
      path: 'titleList',
      name: '发票抬头',
      alias: '/Web/invoice/invoiceInfoList',
      component: () => import('views/invoice/titleList'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'qrcodeList',
      name: '二维码管理',
      alias: '/Web/invoiceQr/qrInvoiceList',
      component: () => import('views/invoice/qrcodeList'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'taxCode',
      name: '税务编码',
      alias: '/Web/invoice/taxCodeList',
      component: () => import('views/invoice/taxCode'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'taxNum',
      name: '场馆税号',
      alias: '/Web/invoice/invoiceAuthList',
      component: () => import('views/invoice/taxNum'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'addTaxNum',
      name: '新增税号',
      component: () => import('views/invoice/addTaxNum'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'record',
      name: '发票填开',
      alias: '/Web/invoice/InvoiceList',
      component: () => import('views/invoice/record'),
      meta: {
        parentName: '发票'
      }
    },
    {
      path: 'add',
      name: '开发票',
      component: () => import('views/invoice/add'),
      meta: {
        parentName: '发票'
      }
    }
  ]
}
