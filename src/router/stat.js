import layout from 'views/layout/';

export default {
  path: '/stat',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '报表'
  },
  children: [
    {
      path: 'menus',
      name: '统计报表',
      alias: '/Web/Statistics/getStatisticsMenulist',
      component: () => import('views/statistics/tableNav'),
      meta: {
        parentName: '报表',
        brandPath: '/stat/table-nav'
      },
      children: [
        {
          path: 'classStat',
          name: '上课统计',
          component: () => import('views/statistics/classStat'),
          meta: {
            parentName: '报表',
            breadText: '操课课时统计'
          }
        },
        {
          path: 'classStat4Coach',
          name: '私教课时',
          component: () => import('views/statistics/classStat4Coach'),
          meta: {
            parentName: '报表',
            breadText: '私教课时统计'
          }
        },
        {
          path: 'classStat4Swim',
          name: '泳教课时',
          component: () => import('views/statistics/classStat4Swim'),
          meta: {
            parentName: '报表',
            breadText: '泳教课时统计'
          }
        },
        // {
        //   path: 'dayClear',
        //   name: '实收分摊',
        //   component: () => import('views/statistics/dayClear'),
        //   meta: {
        //     parentName: '报表'
        //   }
        // },
        {
          path: 'coach',
          name: '教练业绩统计',
          alias: '/Web/Statistics/coach_statistics_detail',
          component: resolve => require(['views/statistics/coachPerformance'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'swim',
          name: '泳教业绩统计',
          alias: '/Web/Statistics/swimming_coach_statistics_detail',
          component: resolve => require(['views/statistics/swimPerformance'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'mission',
          name: '分配任务完成情况',
          alias: '/Web/Statistics/coach_assigning_task',
          component: () => import('views/statistics/missionStat'),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'ptClassInfo',
          name: '私教课情况',
          alias: '/Web/Statistics/pt_class_analyze',
          component: () => import('views/statistics/privateClassInfo'),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'privateSold',
          name: '购续私教情况',
          alias: '/Web/Statistics/bus_daily_purchase_pt_statistics',
          component: () => import('views/statistics/privateSold'),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'coach/:coachId/:beginTime/:endTime',
          name: '教练业绩统计 ',
          alias: '/Web/Statistics/coach_statistics_detail/:coachId/:beginTime/:endTime',
          component: resolve => require(['views/statistics/coachPerformance'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'membership',
          name: '会籍业绩统计',
          alias: '/Web/Statistics/ms_statistics_data',
          component: resolve => require(['views/statistics/membershipPerformance'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'membership/:msId/:beginTime/:endTime',
          name: '会籍业绩统计 ',
          alias: '/Web/Statistics/ms_statistics_data/:msId/:beginTime/:endTime',
          component: resolve => require(['views/statistics/membershipPerformance'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'hourStatis',
          name: '课时统计',
          alias: '/Web/Statistics/hourStatis',
          component: resolve => require(['views/statistics/hourStatis'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'revenueStatis',
          name: '收入分析',
          alias: '/Web/Statistics/get_bus_income_details',
          component: resolve => require(['views/statistics/revenueStatis'], resolve),
          meta: {
            breadText: '场馆收入分析',
            parentName: '报表'
          }
        },
        {
          path: 'analysis',
          name: '会员分析',
          alias: '/Web/Statistics/user_classify',
          component: resolve => require(['views/statistics/membershipAnalysis'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'signList',
          name: '训练次数统计',
          alias: '/Web/Statistics/user_sign_statistics',
          component: resolve => require(['views/statistics/signList'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'coachingBehavior',
          name: '会员行为分析',
          alias: '/Web/Statistics/customer_behave_analyze',
          component: resolve => require(['views/statistics/coachingBehavior'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'buyAndRenewalBehavior',
          name: '购续卡情况',
          alias: '/Web/Statistics/bus_daily_purchase_card_statistics',
          component: resolve => require(['views/statistics/buyAndRenewalBehavior'], resolve),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'msbriefReview',
          name: '会籍工作概况',
          alias: '/Web/Statistics/ms_brief_review',
          component: resolve => require(['views/statistics/msbriefReview.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'coachbriefReview',
          name: '教练业绩概况',
          alias: '/Web/Statistics/coach_brief_review',
          component: resolve => require(['views/statistics/coachbriefReview.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimBriefReview',
          name: '泳教业绩概况',
          alias: '/Web/Statistics/swimming_coach_statistics',
          component: resolve => require(['views/statistics/swimBriefReview.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'monthTotal',
          name: '会籍业绩月报',
          component: resolve => require(['views/statistics/monthTotal.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'ptMonthTotal',
          name: '私教业绩月报',
          component: resolve => require(['views/statistics/monthTotal.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimMonthTotal',
          name: '泳教业绩月报',
          component: resolve => require(['views/statistics/monthTotal.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'comment4coachTable',
          name: '私教课评价报表',
          // alias: '/Web/Statistics/coach_brief_review',
          component: resolve => require(['views/statistics/comment4coachTable.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'commentNewClassTable',
          name: '团课评价报表',
          component: resolve => require(['views/statistics/commentNewClassTable.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'commentNewClassDetailTable',
          name: '团课明细统计',
          component: resolve => require(['views/statistics/commentNewClassDetailTable.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'classEffectStat',
          name: '课程效果统计',
          component: resolve => require(['views/statistics/classEffectStat.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'classEffectStatDetail',
          name: '课程效果统计明细',
          component: resolve => require(['views/statistics/classEffectStatDetail.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'classroomUseStat',
          name: '教室使用统计',
          component: resolve => require(['views/statistics/classroomUseStat.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'memberClassStat',
          name: '会员上课统计',
          component: resolve => require(['views/statistics/memberClassStat.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'memberClassStatDetail',
          name: '会员上课统计明细',
          component: resolve => require(['views/statistics/memberClassStatDetail.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'memberAttendClassDetail',
          name: '会员上课明细',
          component: resolve => require(['views/statistics/memberAttendClassDetail.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'comment4swimTable',
          name: '泳教课评价报表',
          // alias: '/Web/Statistics/coach_brief_review',
          component: resolve => require(['views/statistics/comment4swimTable.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        // {
        //   path: 'financialAllocation',
        //   name: '收入分摊',
        //   alias: '/Web/Statistics/financial_allocation',
        //   component: resolve =>
        //     require(['views/statistics/financialAllocation.vue'], resolve),
        //   meta: {
        //     keepAlive: true,
        //     parentName: '报表'
        //   }
        // },
        {
          path: 'financialAllocation2',
          name: '收入分摊',
          alias: '/Web/Statistics/financial_allocation2',
          component: resolve => require(['views/statistics/financialAllocation2.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'depositCard',
          name: '储值卡使用统计',
          alias: '/Web/DepositCardStatistics/getDepositCardStatistics',
          component: resolve => require(['views/statistics/depositCard.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'depositCardDetail',
          name: '储值卡消费品类详情',
          alias: '/Web/DepositCardStatistics/getDepositCardStatisticsByGroupID',
          component: resolve => require(['views/statistics/depositCardDetail.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'totalPerformanceSummary',
          name: '总业绩汇总',
          component: resolve => require(['views/statistics/TotalPerformanceSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'membershipPerformanceSummary',
          name: '会籍业绩汇总',
          component: resolve => require(['views/statistics/MembershipPerformanceSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'coachPerformanceSummary',
          name: '私教业绩汇总',
          component: resolve => require(['views/statistics/CoachPerformanceSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimPerformanceSummary',
          name: '泳教业绩汇总',
          component: resolve => require(['views/statistics/SwimPerformanceSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'businessFlowSummary',
          name: '业务流水汇总',
          component: resolve => require(['views/statistics/BusinessFlowSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'coachCourseSummary',
          name: '私教课时汇总',
          component: resolve => require(['views/statistics/CoachCourseSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'coachCourseOverview',
          name: '私教课时概况',
          component: resolve => require(['views/statistics/CoachCourseOverview.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimCourseSummary',
          name: '泳教课时汇总',
          component: resolve => require(['views/statistics/SwimCourseSummary.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'privateTeamClass',
          name: '私教班课时统计',
          component: resolve => require(['views/statistics/teamClass.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimTeamClass',
          name: '泳教班课时统计',
          component: resolve => require(['views/statistics/teamClass.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'swimCourseOverview',
          name: '泳教课时概况',
          component: resolve => require(['views/statistics/SwimCourseOverview.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'msbriefReviewNew',
          name: '会籍业绩概况',
          component: resolve => require(['views/statistics/msbriefReviewNew.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'exportLogTable',
          name: '下载中心',
          alias: '/Web/CostAllocation/excel_export_log',
          component: resolve => require(['views/statistics/exportLogTable.vue'], resolve),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'spaceIncome',
          name: '散场票收入报表',
          component: () => import('views/stadium/income'),
          meta: {
            parentName: '报表'
          }
        },
        {
          path: 'spaceAnalysis',
          name: '场地收益概况',
          component: () => import('views/stadium/analysis'),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
        {
          path: 'spaceAnalysisDetail',
          name: '场地收益明细',
          component: () => import('views/stadium/analysisDetail'),
          meta: {
            keepAlive: true,
            parentName: '报表'
          }
        },
      ]
    }
  ]
}
