import layout from 'views/layout/';

export default {
  path: '/paraset',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '设置'
  },
  children: [
    {
      path: 'youjiu',
      name: '体测设备管理',
      alias: '/Web/Youjiu/getList',
      component: () => import('views/paraset/youjiu'),
      meta: {
        breadText: '体测设备管理',
        parentName: '设置',
        keepAlive: true
      }
    },
    {
      path: 'graySet',
      name: '灰度发布',
      alias: '/Web/Business/get_gated_launch',
      component: () => import('views/paraset/graySet'),
      meta: {
        breadText: '灰度发布',
        parentName: '设置'
      }
    },
    {
      path: 'versionCtrl',
      name: '版本权限',
      alias: '/Web/Business/get_bus_setting',
      component: () => import('views/paraset/versionCtrl'),
      meta: {
        parentName: '设置'
      }
    },
    {
      path: 'bossSet',
      name: '账号数量设置',
      alias: '/Web/AccountNumber/get_account_number',
      component: () => import('views/paraset/bossSet'),
      meta: {
        parentName: '设置',
        breadText: '账号数量管理'
      }
    },
    {
      path: 'pointClear',
      name: '积分清零',
      alias: '/Web/AccountNumber/get_account_number',
      component: () => import('views/paraset/pointClear'),
      meta: {
        parentName: '设置',
        breadText: '积分清零'
      }
    },
    {
      path: 'padComment',
      name: '平板评论',
      alias: '',
      component: () => import('views/paraset/padComment'),
      meta: {
        parentName: '设置'
      }
    },
    {
      path: 'heartRateDevice',
      name: '心率带设备管理',
      alias: '/Web/GymsmartTerminal/get_bus_terminal',
      component: () => import('views/system/heartRateDevice'),
      meta: {
        parentName: '设置'
      }
    },
    {
      path: 'settings',
      name: '系统设置',
      alias: '/Web/Business/check_admin_bus_setting',
      component: resolve => require(['views/system/settings'], resolve),
      meta: {
        parentName: '设置'
      }
    },
    {
      path: 'electronic',
      name: '电子合同签署流程',
      component: resolve => require(['views/system/electronic'], resolve),
      meta: {
        parentName: '设置'
      }
    }
  ]
}
