import layout from '@/views/layout'

export default {
  path: '/bundlePackage',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '套餐包列表'
  },
  children: [
    {
      path: 'save',
      name: '新增套餐包',
      meta: {
        parentName: '套餐包列表',
        breadText: '新增套餐包'
      },
      component: () => import('@/views/bundlePackage/save')
    },
    {
      path: 'save/:id',
      name: '编辑套餐包',
      meta: {
        parentName: '套餐包列表',
        breadText: '编辑套餐包'
      },
      component: () => import('@/views/bundlePackage/save')
    }
  ]
}