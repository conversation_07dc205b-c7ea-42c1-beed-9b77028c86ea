import layout from 'views/layout'

export default {
  path: '/alipay-merchant',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '支付宝安心付',
  },
  children: [
    {
      // path: 'monthlyPayment/:refresh?',
      path: 'monthlyPayment/:busId?/:date?/:category?',
      name: '合约管理',
      alias: '/Web/AlipayMerchantCard/contracts',
      component: () => import('views/alipay_come_down/monthlyPayment'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: true,
      },
    },
    // {
    //   path: 'monthlyPayment/:busId/:subscriptionNo/:aliUserId/:category/:id?',
    //   props: true,
    //   name: '月付合约详情',
    //   alias: '/Web/AlipayMerchantCard/contractDetail',
    //   component: () => import('views/alipay_come_down/monthlyPaymentDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'tradeOrder/:refresh?',
    //   name: '组合月付首付订单交易查询',
    //   alias: '/Web/AlipayMerchantCard/fitAlipayOrderList',
    //   component: () => import('views/alipay_come_down/tradeOrder'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'tradeOrder/:category/:id',
    //   props: true,
    //   name: '组合月付首付订单交易详情',
    //   alias: '/Web/AlipayMerchantCard/fitAlipayOrderDetail',
    //   component: () => import('views/alipay_come_down/tradeOrderDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'deductionOrder/:refresh?',
    //   name: '单期扣款记录',
    //   alias: '/Web/AlipayMerchantCard/itemsPaidList',
    //   component: () => import('views/alipay_come_down/deductionOrder'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'deductionOrder/:category/:id',
    //   props: true,
    //   name: '单期扣款详情',
    //   alias: '/Web/AlipayMerchantCard/itemsPaidDetail',
    //   component: () => import('views/alipay_come_down/deductionOrderDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    {
      path: 'zhimaProducts',
      name: '产品管理',
      alias: '/Web/AlipayMerchantCard/products',
      component: () => import('views/alipay_come_down/zhimaProducts'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaProductAdd',
      name: '添加/编辑产品',
      component: () => import('views/alipay_come_down/zhimaProductAdd'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaSubscriptions',
      name: '签约会员',
      alias: '/Web/AlipayMerchantCard/subscriptions',
      component: () => import('views/alipay_come_down/zhimaSubscriptions'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaPlan',
      name: '查询扣款计划',
      component: () => import('views/alipay_come_down/zhimaPlan'),
      meta: {
        parentName: '支付宝月付',
        needBack: true,
      },
    },
    {
      path: 'dashboard',
      name: '业务走势',
      alias: '/Web/AlipayMerchantCard/getStatistics',
      component: () => import('views/alipay_come_down/dashboard'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'statistics',
      name: '签约数据统计',
      alias: '/Web/AlipayMerchantCard/getStatisticsListAll',
      component: () => import('views/alipay_come_down/statistics'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    // {
    //   path: 'protocol',
    //   name: '支付宝配置',
    //   alias: '/Web/AlipayMerchantCard/agreementInfo',
    //   component: () => import('views/alipay_come_down/protocol'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'memberStat',
    //   name: '签约会员统计',
    //   alias: '/Web/AlipayMerchantCard/memberByStore',
    //   component: () => import('views/alipay_come_down/memberStat'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'memberStatTabs/:date/:category',
    //   name: '会员明细统计',
    //   alias: '/Web/AlipayMerchantCard/memberByStoreDetail',
    //   component: () => import('views/alipay_come_down/memberStatTabs'),
    //   props: true,
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: false,
    //   },
    // },
    {
      path: 'predictiveValue',
      name: '月付经营测算',
      alias: '/Web/AlipayMerchantCard/businessCalc',
      component: () => import('views/alipay_come_down/predictiveValue'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'deductionRecord/:busId?/:subscriptionNo?/:date?/:status?',
      name: '履约扣款记录',
      alias: '/Web/AlipayMerchantCard/fitItemsList',
      component: () => import('views/alipay_come_down/deductionRecord'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'terminateUp',
      name: '解约申请',
      alias: '/Web/AlipayMerchantCard/memberUnSignList',
      component: () => import('views/alipay_come_down/terminateUp'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'leaveRecord',
      name: '请假记录',
      alias: '/Web/AlipayMerchantCard/cardStopList',
      component: () => import('views/alipay_come_down/leaveRecord'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'financeFlow',
      name: '财务流水',
      alias: '/Web/AlipayMerchantCard/financeContrastList',
      component: () => import('views/alipay_come_down/financeFlow'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'contractCommission/:busId/:saleId/:month',
      name: '回款提成明细',
      // alias: '/Web/AlipayMerchantCard/commissionList',
      component: () => import('views/alipay_come_down/contractCommission'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'memberCommission/:busId/:saleId/:month',
      name: '会员提成明细',
      // alias: '/Web/AlipayMerchantCard/commissionList',
      component: () => import('views/alipay_come_down/memberCommission'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
  ],
}
