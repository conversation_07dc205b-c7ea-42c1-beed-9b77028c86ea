import Vue from 'vue'
import Router from 'vue-router'
import layout from 'views/layout/'
import Login from 'views/login/login'
import Print from 'views/print/membershipPrint'
import SignPrint from 'views/print/signPrint'

import Stat from './stat'
import Invoice from './invoice'
import Management from './management'
import Settings from './settings'
import marketing from './marketing'
import bundlePackage from './bundlePackage'
import Course from './course'
import alipay_2024 from './alipay_2024'
// import alipay_come_down from './alipay_come_down'

Vue.use(Router)
/*
 *hideSidebar  是否显示面包屑,默认不配置为显示
 *hideHeader  是否顶部header,默认不配置为显示
 *hideBread  是否显示面包屑,默认不配置为显示
 *breadText     此层级面包屑显示的文字,没有设置则显示为路由的name值
 *breadNotLink  此层级对应面包屑是否添加链接,默认不配置为添加(处于面包屑最后一级的时候此值无效)
 *parentName  多级路由指定上级路由的name用以匹配左侧菜单栏选中
 *alias为了兼容后端接口菜单返回处理方式，需要配置为后端接口地址
 *version 面包屑中当前页面暂时需要跳到vue1.x版本的路径
 *keepAlive 页面状态缓存 当跳转到其它页面后返回该页面会继续保存上一次的状态（如页码等）
 */
const router = new Router({
  base: window.__POWERED_BY_QIANKUN__ ? '/v2/' : '/',
  mode: 'history',
  routes: [
    Stat,
    Invoice,
    Management,
    Settings,
    Course,
    ...marketing,
    bundlePackage,
    alipay_2024,
    // alipay_come_down,
    {
      path: 'wechat',
      component: layout,
      children: [
        {
          path: 'index',
          alias: '/Web/BusinessFit/wechat',
          name: '微信月付',
          beforeEnter: (to, from, next) => {
            if (window.location.hostname.indexOf('test') === -1) {
              window.open('https://paycontract.rocketbird.cn/', '_blank')
            } else {
              window.open('https://paycontract-dev.rocketbird.cn/', '_blank')
            }
            next()
          },
          component: () => import('views/index/wechat'),
          meta: {
            hideBread: true,
            breadText: '微信月付',
          },
        },
      ],
    },
    // {
    //   path: '/cashier/:orderId?',
    //   name: '收银台',
    //   component: resolve => require(['components/cashier'], resolve),
    //   meta: {
    //     hideBread: true,
    //   }
    // },
    {
      path: '/',
      component: layout,
      redirect: '/index',
      children: [
        {
          path: 'index',
          name: '概况',
          alias: '/Web/Index/index',
          component: (resolve) => require(['views/index/'], resolve),
          meta: {
            hideBread: true,
            breadText: '概况',
          },
        },
        {
          path: 'region',
          name: '区域概况',
          alias: '/web/BusRegion/getRegionLevel',
          component: () => import('views/management/district/overview'),
          meta: {
            hideBread: true,
            breadText: '区域概况',
          },
        },
        {
          path: 'help',
          name: '帮助中心',
          component: () => import('views/system/help'),
          meta: {
            keepAlive: true,
          },
        },
      ],
    },
    {
      path: '/putOnScreen/one',
      name: '投屏1',
      component: (resolve) => require(['views/putOnScreen/one'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/putOnScreen/two',
      name: '投屏2',
      component: (resolve) => require(['views/putOnScreen/two'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/putOnScreen/three',
      name: '投屏3',
      component: (resolve) => require(['views/putOnScreen/three'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/putOnScreen/four',
      name: '投屏4',
      component: (resolve) => require(['views/putOnScreen/four'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/login',
      component: Login,
      name: '登录',
    },
    {
      path: '/pay/',
      name: '支付',
      component: (resolve) => require(['views/login/pay.vue'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/membershipPrint/:card_order_info_id',
      name: '会员协议打印',
      component: Print,
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/signInfoPrint',
      name: '私教签到打印',
      component: SignPrint,
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/contractPrint',
      name: '合同小票打印',
      component: () => import('views/print/contractPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/commodityPrint',
      name: '商品购买打印',
      component: () => import('views/print/commodityPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/depositPrint',
      name: '费用缴纳打印',
      component: () => import('views/print/depositPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/nonMemberPrint',
      name: '合同签订',
      component: () => import('views/print/nonMemberPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/sanTicketPrint',
      name: '票凭证',
      component: () => import('views/print/sanTicketPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/signinPrint',
      name: '会员签到',
      component: () => import('views/print/signinPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/stadiumPrint',
      name: '场地预订',
      component: () => import('views/print/stadiumPrint.vue'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/signin',
      component: layout,
      meta: {
        breadText: '前台',
      },
      children: [
        // {
        //   path: 'refundCollection',
        //   name: '收退款明细',
        //   // alias: '/Web/Business/get_bind_link',
        //   component: () => import('views/finance/refundCollection'),
        //   meta: {
        //     keepAlive: true
        //   }
        // },
        {
          path: 'bracelet',
          name: '手环记录',
          alias: '/Web/QuickNew/braceletLogList',
          component: (resolve) => require(['views/signin/braceletLogs.vue'], resolve),
          meta: {
            parentName: '前台',
            keepAlive: true,
            breadText: '手环记录',
          },
        },
        {
          path: 'stadium',
          name: '订场',
          alias: '/Web/SpaceOrder/getList',
          component: (resolve) => require(['views/stadium/index'], resolve),
        },
        {
          path: 'manual',
          name: '使用帮助',
          alias: '/Web/Business/get_bind_link',
          component: () => import('views/signin/freedomSign'),
          meta: {},
        },
        {
          path: 'deposit',
          name: '定金/押金',
          alias: '/Web/FrontMoney/front_money_list',
          component: () => import('views/member/deposit'),
          meta: {
            parentName: '前台',
          },
        },
        {
          path: 'locker',
          name: '储物柜出租',
          alias: '/Web/LockerRent/getNewLockerRentList',
          component: () => import('views/member/locker'),
          meta: {
            keepAlive: true,
            parentName: '前台',
          },
          // children: [
          //   {
          //     path: 'collection',
          //     name:  '租柜收款',
          //     component: () => import('views/member/lockerCollection'),
          //     meta: {
          //       keepAlive: true,
          //       parentName: '前台',
          //     }
          //   },
          // ]
        },
        {
          path: 'ptSign',
          name: '教练消课',
          alias: '/Web/Sign/private_sign_log_list',
          component: () => import('views/signin/ptSign'),
          meta: {
            keepAlive: true,
            breadText: '教练消课',
            parentName: '前台',
          },
        },
        {
          path: 'member',
          name: '会员签到',
          alias: '/Web/Sign/pc_sign_log',
          component: (resolve) => require(['views/signin/member'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
          },
        },
        {
          path: 'coach',
          name: '教练签到',
          alias: '/Web/CoachSign/get_coach_sign_list',
          component: (resolve) => require(['views/signin/coach'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
            breadText: '团课教练签到',
          },
        },
        {
          path: 'checkOrder',
          alias: '/Web/preBilling/listData',
          name: '前台核单',
          component: (resolve) => require(['views/reservation/checkOrder'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
          },
        },
        {
          path: 'nonMember',
          alias: '/Web/San/getSanList',
          name: '票务核销',
          component: (resolve) => require(['views/stadium/invoice/logs'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
          },
        },
      ],
    },
    {
      path: '/remind',
      alias: '/Web/Sign/sign_reminder',
      name: '新签到提醒',
      component: (resolve) => require(['views/signin/remind'], resolve),
    },
    {
      path: '/rank',
      name: '签到投屏',
      component: (resolve) => require(['views/signin/rank'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/screen',
      name: '私教教练',
      component: (resolve) => require(['views/signin/screen'], resolve),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
    {
      path: '/member',
      component: layout,
      meta: {
        breadNotLink: true,
        breadText: '会员',
      },
      children: [
        {
          path: 'card/detail/:card_id',
          name: '会员卡详情',
          component: () => import('views/member/cardDetail'),
          meta: {
            parentName: '管理',
            keepAlive: true,
          },
          // children: [
          //   {
          //     path: 'detail/:card_id',
          //     name: '会员卡详情',
          //     component: () => import('views/member/cardDetail'),
          //     meta: { parentName: '管理' }
          //   },
          //   {
          //     path: 'save/:card_id',
          //     name: '会员卡编辑',
          //     component: () => import('views/member/cardSave'),
          //     meta: { parentName: '管理' }
          //   }
          // ]
        },
        {
          path: 'card/save/:card_id/:cardThreeType',
          name: '会员卡编辑',
          component: () => import('views/member/cardSave'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: '/',
          name: '会员管理',
          alias: '/Web/MemberList/member_list',
          component: (resolve) => require(['views/member/'], resolve),
          meta: {
            breadText: '会员管理',
            keepAlive: true,
            parentName: '会员',
          },
        },
        {
          path: 'detail/:userId/:busId?',
          name: '会员详情',
          component: (resolve) => require(['views/member/detail/index'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'add',
          name: '会员添加',
          component: (resolve) => require(['views/member/edit'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'edit/:userId',
          name: '会员编辑',
          component: (resolve) => require(['views/member/edit'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'buyCard/:userId',
          name: '购卡',
          component: (resolve) => require(['views/member/buyCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'editCard/:userId/:cardUserId',
          name: '编辑卡',
          component: (resolve) => require(['views/member/editCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'editCard/:userId/:cardUserId/:cardorder_info_id',
          name: '编辑卡',
          component: (resolve) => require(['views/member/editCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'seperateCard/:userId/:cardUserId/:cardorder_info_id?',
          name: '拆分',
          component: (resolve) => require(['views/member/seperateCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'changeCard/:userId/:cardUserId',
          name: '升卡',
          component: (resolve) => require(['views/member/changeCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'cancelCard/:user_id/:card_user_id',
          name: '销卡',
          component: (resolve) => require(['views/member/cancelCard.vue'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'renewCard/:userId/:cardUserId/:isPackage?',
          name: '续卡/续私教',
          component: (resolve) => require(['views/member/renewCard.vue'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'suspendCard/:userId/:cardUserId?',
          name: '请假',
          component: (resolve) => require(['views/member/suspendCard.vue'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'switchCard/:userId/:cardUserId/:isPackage?',
          name: '转卡',
          component: (resolve) => require(['views/member/switchCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
        {
          path: 'supplyCard',
          name: '补卡',
          component: (resolve) => require(['views/member/supplyCard'], resolve),
          meta: {
            parentName: '会员',
          },
        },
      ],
    },
    {
      path: '/employee',
      component: layout,
      meta: {
        breadNotLink: true,
        breadText: '人员',
      },
      children: [
        {
          path: 'coach',
          name: '教练',
          alias: '/Web/Coach/get_bus_coach_list',
          component: () => import('views/employee/coachList'),
          meta: {
            parentName: '人员',
          },
          children: [
            {
              path: 'add',
              name: '添加教练',
              component: () => import('views/employee/coachAddAndEdit'),
              meta: {
                parentName: '人员',
                breadText: '添加教练',
              },
            },
          ],
        },
        {
          path: 'membership',
          name: '工作人员',
          alias: '/Web/Marketers/get_marketers_list',
          component: () => import('views/employee/membershipList'),
          meta: {
            keepAlive: true,
            parentName: '人员',
          },
          children: [
            {
              path: 'add',
              name: '添加会籍',
              component: () => import('views/employee/membershipAdd'),
              meta: {
                parentName: '人员',
              },
            },
          ],
        },
        {
          path: 'attendance',
          name: '考勤记录',
          alias: '/Web/Staff/getVeinLogList',
          component: () => import('views/employee/attendance'),
          meta: {
            keepAlive: true,
            parentName: '出勤',
          },
          // children: [
          //   {
          //     path: 'add',
          //     name: '添加会籍',
          //     component: () => import('views/employee/membershipAdd'),
          //     meta: {
          //       parentName: '人员'
          //     }
          //   }
          // ]
        },
      ],
    },
    {
      path: '/notice',
      component: layout,
      meta: {
        breadNotLink: true,
        breadText: '管理',
      },
      children: [
        // {
        //   path: 'list',
        //   name: '短信群发',
        //   component: () => import('views/notice/list'),
        //   alias: '/Web/SendMessage/getMsgList',
        //   meta: {
        //     parentName: '管理'
        //   }
        // },
        {
          path: 'msgCenter',
          name: '消息中心',
          component: () => import('views/notice/msgCenter'),
          meta: {
            hideBread: true,
            hideSidebar: true,
            hidePadding: true,
            parentName: '管理',
          },
        },
        // {
        //   path: 'busConfiger',
        //   name: '场馆消息设置',
        //   component: () => import('views/notice/busConfig'),
        //   meta: {
        //     parentName: '管理'
        //   }
        // }
      ],
    },
    {
      path: '/gym',
      component: layout,
      meta: {
        breadNotLink: true,
        breadText: '管理',
      },
      children: [
        {
          path: 'detail',
          name: '场馆资料',
          component: () => import('views/gym/'),
          alias: '/Web/Business/get_bus_info',
          meta: {
            parentName: '管理',
          },
          children: [
            {
              path: 'edit',
              name: '编辑场馆',
              component: () => import('views/gym/edit'),
              meta: {
                parentName: '管理',
                breadText: '编辑场馆',
                showMap: true,
              },
            },
          ],
        },
        {
          path: 'protocol',
          name: '购卡协议',
          component: () => import('views/gym/protocolEdit.vue'),
          alias: '/Web/BuycardProtocol/get_protocol_content',
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'gymProtocol',
          name: '场馆协议',
          component: () => import('views/gym/protocolList.vue'),
          alias: '/Web/Business/protocolList',
          meta: {
            parentName: '管理',
          },
        },
      ],
    },
    {
      path: '/reservation',
      component: layout,
      name: '预约',
      meta: {
        breadNotLink: true,
        breadText: '前台',
      },
      children: [
        {
          path: 'freetry',
          alias: '/Web/Reservation/reservation_list',
          name: '体验预约',
          component: (resolve) => require(['views/reservation/freetry'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
            breadText: '到店体验',
          },
        },
        {
          path: 'class',
          alias: '/Web/ClassMark/pc_class_mark_list',
          name: '课程预约',
          component: (resolve) => require(['views/reservation/class'], resolve),
          meta: {
            keepAlive: true,
            parentName: '前台',
            breadText: '团课预约',
          },
          children: [
            {
              path: 'detail/:courseScheduleId',
              name: '预约详情',
              component: (resolve) => require(['views/reservation/detail'], resolve),
              meta: {
                parentName: '前台',
              },
            },
            {
              path: 'alternatesDetail/:courseScheduleId',
              name: '候补详情',
              component: (resolve) => require(['views/reservation/alternatesDetail'], resolve),
              meta: {
                parentName: '前台',
              },
            },
            {
              path: 'resProtocol',
              name: '团课预约协议',
              component: (resolve) => require(['views/reservation/components/resProtocol'], resolve),
              meta: {
                parentName: '前台',
              },
            },
          ],
        },
        {
          path: 'miss',
          alias: '/Web/Sign/get_miss_sign_list',
          name: '爽约列表',
          component: (resolve) => require(['views/reservation/miss'], resolve),
          meta: {
            keepAlive: true,
            breadText: '爽约记录',
            parentName: '前台',
          },
        },
        {
          path: 'miss/:search',
          alias: '/Web/Sign/get_miss_sign_list/:search',
          name: '爽约记录',
          component: (resolve) => require(['views/reservation/miss'], resolve),
          meta: {
            keepAlive: false,
            parentName: '前台',
          },
        },
      ],
    },
    {
      path: '/sjsConfig',
      component: layout,
      name: '硬件',
      children: [
        {
          path: 'addsControl',
          name: '中控柜广告',
          alias: '/Web/Business/getCabinetTemplate',
          component: (resolve) => require(['views/sjsConfig/addsControl'], resolve),
          meta: {
            parentName: '硬件',
          },
        },
        // {
        //   path: 'lockerSet',
        //   name: '固定租柜配置',
        //   alias: '/Web/Business/set_detention_list',
        //   component: resolve => require(['views/sjsConfig/lockerSet'], resolve),
        //   meta: {
        //     parentName: '硬件'
        //   }
        // },
        {
          path: 'hardwareConfig',
          name: '硬件设置',
          alias: '/Web/Business/Sjs_common_get_config',
          component: () => import('views/sjsConfig/hardwareConfig'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'usedLogs',
          name: '设备使用记录',
          alias: '/Web/LightControl/getLogTabPower',
          component: () => import('views/sjsConfig/usedLogs'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'boxRecord',
          name: '柜子记录',
          alias: '/web/Business/cabinetRecords',
          component: () => import('views/sjsConfig/boxRecord'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'enterRecord',
          name: '进出场记录',
          alias: '/Web/Business/getInAndOutRecords',
          component: () => import('views/sjsConfig/enter-record'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'dragonfly',
          name: '蜻蜓机',
          alias: '/Web/DragonflyMachine/get_list',
          component: () => import('views/sjsConfig/dragonfly'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'valueCardConfig',
          name: '储值卡签到',
          alias: '/Web/Business/Sjs_get_pay_per_time_config',
          component: () => import('views/sjsConfig/valueCardConfig'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'cabintDevice',
          name: '柜号管理',
          alias: '/Web/Business/get_cabint_device',
          component: () => import('views/sjsConfig/cabintDevice'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'bathDevice',
          name: '浴室管理',
          alias: '/Web/Business/getBathSetting',
          component: () => import('views/sjsConfig/bathDevice'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'bathRecord',
          name: '淋浴使用记录',
          alias: '/Web/Shower/getShowerList',
          component: () => import('views/sjsConfig/bathRecord'),
          meta: {
            parentName: '硬件',
            keepAlive: true,
          },
        },
        {
          path: 'cabinetList',
          name: '中控柜状态',
          alias: '/Web/Business/cabinetLists',
          component: () => import('views/sjsConfig/cabinetList'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'yjCabinet',
          name: '英杰柜控',
          alias: '/Web/YjFace/getDeviceList',
          component: () => import('views/sjsConfig/yjCabinet'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'cabinetName',
          name: '柜号别名',
          alias: '/Web/Cabinet/getList',
          component: () => import('views/sjsConfig/cabinetName'),
          meta: {
            parentName: '硬件',
          },
        },
        {
          path: 'assignRule',
          name: '柜号分配规则',
          alias: '/Web/Business/getCabinetRule',
          component: () => import('views/sjsConfig/assignRule'),
          meta: {
            parentName: '硬件',
          },
        },
      ],
    },
    {
      path: '/commodity',
      component: layout,
      name: '商品',
      meta: {
        breadText: '前台',
      },
      children: [
        {
          path: 'list',
          name: '商品及消费列表',
          alias: '/Web/Commodity/get_commodity_list',
          component: () => import('views/goods/commodity'),
          meta: {
            parentName: '前台',
            breadText: '商品管理',
          },
          children: [
            {
              path: 'check',
              name: '结算',
              component: () => import('views/goods/components/goodsCheck'),
              meta: {
                parentName: '前台',
                breadText: '结算',
              },
            },
            {
              path: 'detail',
              name: '商品详情',
              component: () => import('views/goods/components/goodsDetail'),
              meta: {
                breadText: '商品添加',
                parentName: '前台',
              },
            },
            {
              path: 'change',
              name: '商品入库',
              component: () => import('views/goods/components/goodsChange'),
              meta: {
                breadText: '商品入库',
                parentName: '前台',
              },
            },
            {
              path: 'turnover',
              name: '商品流水',
              component: () => import('views/goods/components/turnover'),
              meta: {
                breadText: '商品流水',
                parentName: '前台',
              },
            },
            {
              path: 'transferConfirm',
              name: '商品调入确认',
              component: () => import('views/goods/components/transferConfirm'),
              meta: {
                breadText: '商品调入确认',
                parentName: '前台',
              },
            },
            {
              path: 'confirmList',
              name: '调入确认列表',
              component: () => import('views/goods/components/confirmList'),
              meta: {
                breadText: '商品调入列表',
                parentName: '前台',
              },
            },
          ],
        },
      ],
    },
    {
      path: '/finance',
      component: layout,
      name: '财务',
      children: [
        // {
        //   path: 'financialAffair',
        //   name: '订单流水',
        //   // alias: '/Web/Statistics/cardOrderList',
        //   component: () => import('views/finance/financialAffair'),
        //   meta: {
        //     parentName: '财务',
        //     keepAlive: true
        //   }
        // },
        // {
        //   path: 'unmatchedOrder',
        //   name: ' 无匹配订单',
        //   // alias: '/Web/Statistics/cardOrderList',
        //   component: () => import('views/finance/unmatchedOrder'),
        //   meta: {
        //     parentName: '财务',
        //     keepAlive: true
        //   }
        // },
        {
          path: 'orderList',
          name: '合同订单',
          alias: '/Web/Statistics/cardOrderList',
          component: () => import('views/finance/orderList'),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'wxPayList',
          name: '线上支付明细',
          alias: '/Web/Finance/wxreceiptdetails',
          component: () => import('views/finance/wxPayList'),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'inventory',
          name: '流水明细表',
          alias: '/Web/statistics/getFinancialFlow',
          component: (resolve) => require(['views/finance/inventory'], resolve),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'inventoryNew',
          name: '业务流水',
          alias: '/Web/statistics/getFinancialFlowNew',
          component: (resolve) => require(['views/finance/inventoryNew'], resolve),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'receiveLog',
          name: '收银流水',
          alias: '/Web/statistics/ReceiveLogList',
          component: (resolve) => require(['views/finance/receiveLog'], resolve),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'totalOfFlowWater',
          name: '流水汇总表',
          alias: '/Web/Summary/getSummaryList',
          component: (resolve) => require(['views/finance/totalOfFlowWater'], resolve),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'salarySetList',
          name: '薪资设定',
          alias: '/Web/SalaryRule/get_salary_rule_list',
          component: (resolve) => require(['views/finance/salarySetList'], resolve),
          meta: {
            parentName: '财务',
          },
        },
        {
          path: 'salaryEdit',
          name: '薪资设置',
          component: (resolve) => require(['views/finance/salaryEdit'], resolve),
          meta: {
            parentName: '报表',
          },
        },
        {
          path: 'salaryList',
          name: '薪资报表',
          alias: '/Web/SalaryRule/get_salary_list',
          component: (resolve) => require(['views/finance/salaryList'], resolve),
          meta: {
            parentName: '报表',
          },
        },
        {
          path: 'rechargeRecord',
          name: '充值消费记录',
          alias: '/Web/ConsumptionRecord/get_recharge_record',
          component: (resolve) => require(['views/finance/rechargeRecord'], resolve),
          meta: {
            parentName: '财务',
          },
        },
      ],
    },
    {
      path: '/admin',
      component: layout,
      name: '管理',
      children: [
        {
          path: 'list',
          name: '账号管理',
          alias: '/Web/Admin/get_admin_list',
          component: () => import('views/account/list'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'add',
          name: '账号添加',
          component: () => import('views/account/add'),
          meta: {
            parentName: '管理',
          },
        },
      ],
    },
    {
      path: '/stadium',
      component: layout,
      name: '管理',
      children: [
        {
          path: 'list',
          name: '场地管理',
          alias: '/Web/Space/getList',
          component: () => import('views/stadium/list'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'fixedList',
          name: '固定场管理',
          alias: '/Web/SpaceOrderLong/getList',
          component: () => import('views/stadium/fixedList'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'addClassRoom',
          name: '新增教室',
          component: () => import('views/stadium/addClassRoom'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'save/:stadiumId',
          name: '新增场地',
          component: () => import('views/stadium/save'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'saveNew',
          name: '新增场地',
          props: true,
          component: () => import('views/stadium/saveNew'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'saveNew/:id',
          name: '编辑场地',
          props: true,
          component: () => import('views/stadium/saveNew'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'holydaySettings/:id?',
          name: '临时调价设置',
          props: true,
          component: () => import('views/stadium/holydaySettings'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'holydaySettings/fixed/:stadiumId',
          name: '临时调价设置',
          props: true,
          component: () => import('views/stadium/holydaySettings'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'holydaySettingsList',
          name: '临时调价记录',
          alias: '/Web/Space/getHolidayList',
          component: () => import('views/stadium/holydaySettingsList'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'strategy/:id?',
          name: '场次&定价',
          props: true,
          component: () => import('views/stadium/strategy'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'strategyList',
          name: '排场&定价方案记录',
          alias: '/Web/Space/getListSchedulePriceTemplate',
          component: () => import('views/stadium/strategyList'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'categorySave/:stadiumId?',
          props: true,
          name: '场地类型',
          component: () => import('views/stadium/categorySave'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'invoiceList',
          name: '票列表',
          alias: '/Web/Space/getList',
          component: () => import('views/stadium/invoice/list'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'invoiceSave/:id?',
          name: '票详情',
          props: true,
          component: () => import('views/stadium/invoice/save'),
          meta: {
            parentName: '管理',
          },
        },
        {
          path: 'invoiceLogs',
          name: '票记录',
          component: () => import('views/stadium/invoice/logs'),
          meta: {
            parentName: '管理',
          },
        },
      ],
    },
    {
      path: '/print',
      component: layout,
      name: '系统',
      children: [
        {
          path: 'template',
          name: '合同模板',
          alias: '/Web/contractTemplate/templateList',
          component: () => import('views/print/template'),
          meta: {
            parentName: '系统',
            hideBread: true,
            hidePadding: true,
          },
        },
      ],
    },
    {
      path: '/templatePrint',
      name: '合同打印',
      component: () => import('views/print/templatePrint'),
      meta: {
        hideBread: true,
        hideSidebar: true,
        hideHeader: true,
        hidePadding: true,
      },
    },
  ],
})

export default router
