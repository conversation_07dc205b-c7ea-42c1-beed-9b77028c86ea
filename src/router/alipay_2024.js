import layout from 'views/layout'

export default {
  path: '/alipay2',
  component: layout,
  meta: {
    breadNotLink: true,
    breadText: '支付宝月付',
  },
  children: [
    {
      // path: 'monthlyPayment/:refresh?',
      path: 'monthlyPayment/:busId?/:date?/:category?',
      name: '合约管理',
      alias: '/Web/BusinessFit/contracts',
      component: () => import('views/alipay_2024/monthlyPayment'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: true,
      },
    },
    // {
    //   path: 'monthlyPayment/:busId/:subscriptionNo/:aliUserId/:category/:id?',
    //   props: true,
    //   name: '月付合约详情',
    //   alias: '/Web/BusinessFit/contractDetail',
    //   component: () => import('views/alipay_2024/monthlyPaymentDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'tradeOrder/:refresh?',
    //   name: '组合月付首付订单交易查询',
    //   alias: '/Web/BusinessFit/fitAlipayOrderList',
    //   component: () => import('views/alipay_2024/tradeOrder'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'tradeOrder/:category/:id',
    //   props: true,
    //   name: '组合月付首付订单交易详情',
    //   alias: '/Web/BusinessFit/fitAlipayOrderDetail',
    //   component: () => import('views/alipay_2024/tradeOrderDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'deductionOrder/:refresh?',
    //   name: '单期扣款记录',
    //   alias: '/Web/BusinessFit/itemsPaidList',
    //   component: () => import('views/alipay_2024/deductionOrder'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'deductionOrder/:category/:id',
    //   props: true,
    //   name: '单期扣款详情',
    //   alias: '/Web/BusinessFit/itemsPaidDetail',
    //   component: () => import('views/alipay_2024/deductionOrderDetail'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    {
      path: 'zhimaProducts',
      name: '产品管理',
      alias: '/Web/BusinessFit/products',
      component: () => import('views/alipay_2024/zhimaProducts'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaProductAdd',
      name: '添加/编辑产品',
      component: () => import('views/alipay_2024/zhimaProductAdd'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaSubscriptions',
      name: '签约会员',
      alias: '/Web/BusinessFit/subscriptions',
      component: () => import('views/alipay_2024/zhimaSubscriptions'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'zhimaPlan',
      name: '查询扣款计划',
      component: () => import('views/alipay_2024/zhimaPlan'),
      meta: {
        parentName: '支付宝月付',
        needBack: true,
      },
    },
    {
      path: 'dashboard',
      name: '业务走势',
      alias: '/Web/BusinessFit/getStatistics',
      component: () => import('views/alipay_2024/dashboard'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'statistics',
      name: '签约数据统计',
      alias: '/Web/BusinessFit/getStatisticsListAll',
      component: () => import('views/alipay_2024/statistics'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    // {
    //   path: 'protocol',
    //   name: '支付宝配置',
    //   alias: '/Web/BusinessFit/agreementInfo',
    //   component: () => import('views/alipay_2024/protocol'),
    //   meta: {
    //     parentName: '支付宝月付',
    //   },
    // },
    // {
    //   path: 'memberStat',
    //   name: '签约会员统计',
    //   alias: '/Web/BusinessFit/memberByStore',
    //   component: () => import('views/alipay_2024/memberStat'),
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: true,
    //   },
    // },
    // {
    //   path: 'memberStatTabs/:date/:category',
    //   name: '会员明细统计',
    //   alias: '/Web/BusinessFit/memberByStoreDetail',
    //   component: () => import('views/alipay_2024/memberStatTabs'),
    //   props: true,
    //   meta: {
    //     parentName: '支付宝月付',
    //     keepAlive: false,
    //   },
    // },
    {
      path: 'predictiveValue',
      name: '月付经营测算',
      alias: '/Web/BusinessFit/businessCalc',
      component: () => import('views/alipay_2024/predictiveValue'),
      meta: {
        parentName: '支付宝月付',
      },
    },
    {
      path: 'deductionRecord/:busId?/:subscriptionNo?/:date?/:status?',
      name: '履约扣款记录',
      alias: '/Web/BusinessFit/fitItemsList',
      component: () => import('views/alipay_2024/deductionRecord'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'terminateUp',
      name: '解约申请',
      alias: '/Web/BusinessFit/memberUnSignList',
      component: () => import('views/alipay_2024/terminateCombination'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'leaveRecord',
      name: '请假记录',
      alias: '/Web/BusinessFit/cardStopList',
      component: () => import('views/alipay_2024/leaveRecord'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'financeFlow',
      name: '财务流水',
      alias: '/Web/BusinessFit/financeContrastList',
      component: () => import('views/alipay_2024/financeFlow'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'contractCommission/:busId/:saleId/:month',
      name: '回款提成明细',
      // alias: '/Web/BusinessFit/commissionList',
      component: () => import('views/alipay_2024/contractCommission'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
    {
      path: 'memberCommission/:busId/:saleId/:month',
      name: '会员提成明细',
      // alias: '/Web/BusinessFit/commissionList',
      component: () => import('views/alipay_2024/memberCommission'),
      meta: {
        parentName: '支付宝月付',
        keepAlive: false,
      },
    },
  ],
}
