<template>
    <div class="private_tabs">
        <div class="private_tabs_header">
            <div 
                v-for="(item,key) in list" 
                :key="`private_tabs_item${key}`" 
                class="private_tabs_items"
                @click="handleClick(key)"
            >
                <img :src="require(`../../assets/img/diy/${index === key ? item.iconSel : item.icon}`)" />
                <span :class="index === key ? '' : 'name'">{{item.name}}</span>
            </div>
        </div>
        <div class="private_tabs_slot">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
  name: 'tabs',
  props: {
    index: {
      type: Number,
      default: 1
    },
    list: {
      type: Array,
      default: []
    }
  },
  methods: {
    handleClick(key) {
      this.$emit('on-click', key)
    }
  }
}
</script>
<style lang="less">
.private_tabs {
  width: 100%;
  height: auto;
  background: #fff;
  .private_tabs_header {
    width: 100%;
    height: auto;
    padding: 8px 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(#515a6e, 0.5);
    .private_tabs_items {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      img {
        width: 30px;
        height: auto;
      }
      span {
        font-family: 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', Helvetica,
          sans-serif, Arial;
        font-size: 14px;
      }
      span.name {
        color: rgba(#000, 0.5);
      }
    }
    .private_tabs_items + .private_tabs_items {
      margin-left: 48px;
    }
  }
  .private_tabs_slot {
    width: 100%;
    height: 100%;
    padding: 16px;
    box-sizing: border-box;
  }
}
</style>
