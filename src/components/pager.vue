<template>
  <Page :total="+total"
        :current.sync="page"
        :page-size="pageSize"
        placement="top"
        show-total
        show-sizer
        @on-change="handleChange"
        @on-page-size-change="pageSizeChanged" />
</template>

<script>
import { mapGetters } from 'vuex';
import * as Types from 'types';
export default {
  name: 'pager',
  props: {
    total: {
      type: [String, Number],
      required: true,
      default: 0
    },
    history: {
      type: Boolean,
      default: true
    },
    name: {
      type: [String, Number]
    },
    postData: {
      type: Object,
      required: true,
      default: () => {
        return {
          page_no: 1,
          page_size: 10
        };
      }
    }
  },
  data() {
    return {
      pageSize: 10,
      id: '',
      stack: null
    };
  },
  created() {
    if (this.history) {
      this.id = this.name ? this.$route.name + this.name : this.$route.name;
      const history = this.pagerStack.find(item => item.id === this.id);
      if (history) {
        const { postData } = history;
        this.pageSize = postData.page_size;
        this._postData = postData;
      } else {
        this.handleChange(1);
      }
    }
  },
  beforeDestroy() {
    this.$store.commit(Types.SET_PAGER_STACK, {
      id: this.id,
      postData: this._postData
    });
  },
  computed: {
    ...mapGetters(['pagerStack']),
    page: {
      get() {
        return this._postData.page_no;
      },
      set() {}
    },
    _postData: {
      get() {
        return this.postData;
      },
      set(val) {
        this.$emit('on-change', val);
      }
    }
  },
  methods: {
    handleChange(e) {
      this._postData = { ...this._postData, ...{ page_no: e, page_size: this.pageSize } };
    },
    pageSizeChanged(pageSize) {
      this.pageSize = pageSize;
      this.handleChange(1);
    }
  }
};
</script>

<style scoped>
</style>
