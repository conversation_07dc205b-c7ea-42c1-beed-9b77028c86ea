<template>
  <div class="address-map">
    <FormItem label="所在地区" :label-width="100" prop="district_id">
      <Row>
        <Col span="5">
          <Select class="district" @on-change="getRegion(2)" filterable v-model="formItem.province_id" transfer>
            <Option v-for="(province) in provincesList" :value="province.region_id" :key="province.region_id"
                    :label="province.region_name">
            </Option>
          </Select>
        </Col>
        <Col span="5" offset="1">
          <Select class="district" @on-change="getRegion(3)" filterable v-model="formItem.city_id" transfer>
            <Option v-for="(city) in citiesList" :value="city.region_id" :key="city.region_id"
                    :label="city.region_name">
            </Option>
          </Select>
        </Col>
        <Col span="5" offset="1">
          <Select class="district" filterable v-model="formItem.district_id" @on-change="changeDistrict" transfer>
            <Option v-for="(district) in districtsList" :value="district.region_id" :key="district.region_id"
                    :label="district.region_name">
            </Option>
          </Select>
        </Col>
      </Row>
    </FormItem>
    <FormItem style="padding-top: 10px" label="详细地址" :label-width="100" prop="address">
      <Row>
        <Col span="17">
          <Input v-model="formItem.address" @on-enter="doAddressSearch" @on-blur="doAddressSearch">
            <Button slot="append" @click="doAddressSearch">定位</Button>
          </Input>
        </Col>
      </Row>
      <!-- <el-amap-search-box :search-option="mapSearchOption" ref="searchBox"
                          :on-search-result="onMapSearchResult"></el-amap-search-box> -->
    </FormItem>
  </div>
</template>

<script>
  export default {
    name: 'addressMap',
    props: {
      value: {}
    },
    data() {
      return {
        mapCenter: [0, 0],
        provincesList: [],
        citiesList: [],
        districtsList: [],
        mapSearchOption: {
          city: '',
          citylimit: true
        },
        cityDistrict: '',
        formItem: {
          province_id: '',
          city_id: '',
          district_id: ''
        }
      };
    },
    watch: {
      async 'formItem.district_id'(val) {
        if (!val) return;
        if (!this.citiesList.length) {
          await this.getRegion(2);
        }
        if (!this.districtsList.length) {
          await this.getRegion(3);
        }
        this.setMapCity();
        const provinceName = this.provincesList.find(item => item.region_id === this.formItem.province_id).region_name;
        const cityName = this.citiesList.find(item => item.region_id === this.formItem.city_id).region_name;
        const districtName = this.districtsList.find(item => item.region_id === this.formItem.district_id).region_name;
        const keyword = `${provinceName}${cityName}${districtName}`;
        this.cityDistrict = keyword;
        // console.log('城市定位: ' + this.cityDistrict)
        // if (this.formItem.address) {
        //   this.doAddressSearch();
        // } else {
        //   this.$refs.searchBox.keyword = keyword;
        //   this.$refs.searchBox.search();
        // }
      },
    },
    async created() {
      await this.getRegion();

      this.formItem = {
        ...{
          province_id: '',
          city_id: '',
          district_id: ''
        },
        ...this.value
      };
    },
    methods: {
      changeDistrict() {
        const provinceName = this.provincesList.filter(item => {
          return item.region_id === this.formItem.province_id;
        })[0].region_name;
        const cityName = this.citiesList.filter(item => {
          return item.region_id === this.formItem.city_id;
        })[0].region_name;
        const districtName = this.districtsList.filter(item => {
          return item.region_id === this.formItem.district_id;
        })[0].region_name;
        let keyword = `${provinceName}${cityName}${districtName}`;
        if(this.formItem.address != ''){
          keyword = `${provinceName}${cityName}${districtName}${this.formItem.address || ''}`;
        }
        this.$emit('changeSearchWord', {
          provinceName,
          province_id: this.formItem.province_id,
          cityName,
          city_id: this.formItem.city_id,
          districtName,
          district_id: this.formItem.district_id,
          keyword,
          address: this.formItem.address || ''
        })
      },
      doDistrictLocation() {
        const { province_name, city_name, district_name } = this.formItem;
        const keyword = `${province_name.region_name}${city_name.region_name}${district_name.region_name}`;
        this.$nextTick(() => {
          // this.$refs.searchBox.keyword = keyword;
          // this.$refs.searchBox.search();
        });
      },
      doAddressSearch() {
        if (!this.cityDistrict) {
          const { province_name, city_name, district_name } = this.formItem;
          this.cityDistrict = `${province_name?.region_name}${city_name?.region_name}${district_name?.region_name}`;
        }
        // console.log('doAddressSearch:', this.cityDistrict + this.formItem.address)
        this.$nextTick(() => {
          let word = this.cityDistrict + this.formItem.address
          this.$emit('changeSearchDetailWord', {
            word,
            address: this.formItem.address
          })
          // this.$refs.searchBox.keyword = this.cityDistrict + this.formItem.address;
          // this.$refs.searchBox.search();
        });
      },
      onMapSearchResult(position) {
        if (position.length) {
          const { lat, lng } = position[0];
          // console.log('地址定位: lng=' + lng + ', lat=' + lat)
          this.mapCenter = [lng, lat];
          const { province_id, city_id, district_id, address } = this.formItem;
          const province_name = this.provincesList.find(item => item.region_id === province_id).region_name;
          const city_name = this.citiesList.find(item => item.region_id === city_id).region_name;
          const district_name = this.districtsList.find(item => item.region_id === district_id).region_name;
          const detailAddress = `${province_name === city_name ? '' : province_name}${city_name}${district_name}${address || ''}`;
          this.$emit('on-location', {
            location: this.mapCenter, ...this.formItem, ...{
              province_name,
              city_name,
              district_name,
              detailAddress
            }
          });
        } else {
          setTimeout(() => {
            // console.log(this.mapCenter)
            if (this.mapCenter[0] === 0) {
              this.doDistrictLocation(this.formItem);
            }
          }, 1000);
        }
      },
      setMapCity() {
        if (!this.formItem.city_id || !this.citiesList.length) return;
        this.mapSearchOption.city = this.citiesList.find(item => item.region_id === this.formItem.city_id).region_name;
        // console.log('搜索城市: ', this.mapSearchOption.city)
      },
      getRegion(type = 1) {
        if (type === 3) {
          this.setMapCity();
        }
        const url = '/Web/Business/get_region';
        const { province_id, city_id } = this.formItem;
        let postData = {
          province_id,
          city_id
        };
        if (type === 2) {
          postData = {
            province_id
          };
        }
        return this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (type === 1) {
                this.provincesList = data;
              } else if (type === 2) {
                this.citiesList = data;
              } else if (type === 3) {
                this.districtsList = data;
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>

<style lang="less">
  .address-map {
    .el-vue-search-box-container {
      height: 0;
      width: 0;
      visibility: hidden;
      border: 1px solid #dddee1;
      border-radius: 4px;
      box-shadow: none;
      .search-tips {
        display: none;
      }
    }
  }
</style>
