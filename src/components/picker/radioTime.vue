<template>
  <Radio-group v-model="selectedTime" class="radio-time" type="button" size="large">
    <Radio
      v-for="time in data"
      :key="time.time"
      class="radio-time-item"
      :class="{ 'course-mark': time.name && time.status == 0 }"
      :label="time.hour_date"
      :disabled="!time.status">
      <div class="radio-time-item-text">
<div>{{ time.hour_date }}</div>
      <div v-if="+time.all_num>1 && +time.class_mark_num">已约{{ time.class_mark_num }}/{{ time.all_num }}</div>
      </div>
      
    </Radio>
  </Radio-group>
</template>

<script>
import { find } from "lodash-es"

export default {
  name: 'radioTime',
  props: ['data', 'courseActiveTime', 'date'],
  data() {
    return {
      selectedTime: '',
    }
  },
  created() {
  },
  watch: {
    courseActiveTime(val) {
      this.selectedTime = val;
    },
    selectedTime(val) {
      if (!val) {
        return
      }
      if (val !== this.courseActiveTime) {
        let selected = val.split(':');
        let date = new Date();
        let propYear = this.date.getFullYear(),
          propMonth = this.date.getMonth(),
          propDay = this.date.getDate();

        let yearNow = date.getFullYear(),
          monthNow = date.getMonth(),
          dayNow = date.getDate(),
          hourNow = date.getHours(),
          minNow = date.getMinutes();

        let isToday = propYear === yearNow && propMonth === monthNow && propDay === dayNow;
        if (isToday) {
          if (parseInt(selected[0]) < hourNow) {
            this.$Message.error(`所选时间已不可用，当前时间: ${hourNow}:${minNow}`);
            this.selectedTime = '';
            return
          } else if (parseInt(selected[0]) === hourNow) {
            if (parseInt(selected[1]) < minNow) {
              this.$Message.error(`所选时间已不可用，当前时间: ${hourNow}:${minNow}`);
              this.selectedTime = '';
              return
            }
          }
        }
      }
      const info = this.data.find(item => item.hour_date === val);
      if(+info.all_num > 1){
        this.$Modal.confirm({
          title: '提示',
          zIndex: 9999,
          content: `本节课为1对${info.all_num}课程，${info.begin_time}~${info.end_time}，时长${info.class_duration}分钟,是否确认参加?`,
          onOk: () => {
            this.$emit('selectedTime', info);
          },
          onCancel: () => {
            this.selectedTime = '';
          }
        })
      } else {
        this.$emit('selectedTime', info);
      }
      
    }
  }
}

</script>

<style lang="less" scoped>
@radio-color: #19be6b;
.radio-time {
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  max-height: 320px;
}

.radio-time-item {
  display: flex;
  flex: 1;
  position: relative;
  margin-right: 5px;
  color: #626262;
  font-weight: bold;
  border: 1px solid #bfbfbf;
  border-radius: 0 !important;
  margin-bottom: 5px;
  width: 76px;
  .radio-time-item-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    font-size: 12px;
    font-weight: 400;
  }
  &:hover {
    color: #fff;
    background-color: @radio-color;
    border: 1px solid @radio-color;
  }
  &.course-mark::before {
    position: absolute;
    top: 0;
    left: 0;
    content: '团操课';
    line-height: .82;
    font-size: 12px;
    font-weight: 400;
    color: #5db75d;
  }
}

.ivu-radio-group-button .ivu-radio-wrapper-checked {
  color: #fff;
  background-color: @radio-color;
  border: 1px solid @radio-color;
}

.ivu-radio-group-button .ivu-radio-wrapper-disabled {
  color: #b5b5b5;
  background-color: #eee;
  border: 1px solid #eee;
  &:hover {
    color: #b5b5b5;
    background-color: #eee;
    border: 1px solid #eee;
  }
}
</style>
