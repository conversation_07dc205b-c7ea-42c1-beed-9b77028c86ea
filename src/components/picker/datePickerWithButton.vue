<template>
  <div class="date-picker-container">
    <Radio-group v-if="showQuickBtn && showingBiu" v-model="quickSelect" type="button" style="margin-right: 10px">
      <Radio label="今天"></Radio>
      <Radio label="本周"></Radio>
      <Radio label="本月"></Radio>
    </Radio-group>
    <Date-picker @on-change="dateChanged" :value="dateRange" type="daterange" placeholder="请选择时间范围" :editable="false" :clearable="clearable" :options="options"></Date-picker>
  </div>
</template>

<script>
import { formatDate, dateDiff } from 'utils';

export default {
  name: 'datePickerWithButton',
  //days 数组  开始日期和结束日期 ex：[开始，结束]
  //clearable 是否可清空 默认不可清空
  props: {
    days: Array,
    clearable: {
      type: Boolean,
      default: false
    },
    select: {
      type: String,
      default: '本周'
    },
    options: {
      type: Object,
      default: () => {
        return {};
      }
    },
    showingBiu: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      quickSelect: '',
      showQuickBtn: !this.clearable,
      dateRange: []
    };
  },
  mounted() {
    if (this.days && this.days.length !== 0) {
      let days = [this.timeToDate(this.days[0]), this.timeToDate(this.days[1])];
      this.dateRange = days;
      this.quickSelect = ''
      this.$emit('update:days', days);
      this.$emit('on-change', days);
    } else {
      this.quickSelect = this.select;
    }
  },
  methods: {
    timeToDate(time) {
      // fix: 12925 time为空值时不处理
      return time ? formatDate(new Date(time), 'yyyy-MM-dd') : time;
    },
    calBeginDate(selectVal) {
      let beginDate = this.timeToDate(Date.now() - selectVal * 24 * 60 * 60 * 1000);
      let endDate = this.timeToDate(Date.now());
      this.$emit('update:days', [beginDate, endDate]);
      this.$emit('on-change', [beginDate, endDate]);
      this.dateRange = [beginDate, endDate];
    },
    dateChanged(val) {
      this.$emit('update:days', val);
      this.quickSelect = '';
      this.$emit('on-change', val);
      this.dateRange = val;
    }
  },
  watch: {
    days(val){
        if(val && val.length !== 0 ){
            let days = [this.timeToDate(val[0]), this.timeToDate(val[1])];
            this.dateRange = days;
            this.quickSelect = ''
            this.$emit('on-change', days);
        }
    },
    quickSelect(val) {
      switch (val) {
        case '今天':
          this.calBeginDate(0);
          break;
        case '本月':
          let dayNow = new Date().getDate();
          this.calBeginDate(dayNow - 1);
          break;
        case '本周':
          let weekNow = new Date().getDay();
          weekNow = weekNow === 0 ? 7 : weekNow;
          this.calBeginDate(weekNow - 1);
          break;
        default:
      }
    }
  }
};
</script>

<style lang="less">
.date-picker-container {
  @button-color: #52a4ea;
  display: flex;

  .ivu-radio-group-button .ivu-radio-wrapper {
    &:hover {
      color: @button-color;
    }
  }

  .ivu-radio-group-button {
    display: flex;
  }

  .ivu-radio-group-button .ivu-radio-wrapper-checked {
    color: #fff;
    border-color: @button-color;
    box-shadow: none;
    background-color: @button-color;

    &:hover {
      color: #fff;
    }
  }

  .ivu-date-picker-rel {
    width: 190px;
  }
}
</style>
