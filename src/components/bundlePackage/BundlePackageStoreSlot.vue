<template>
  <div>
    <div v-if="cardList.length > 0">
      <Row class="panel-title" :gutter="16">
        <Col span="12">名称</Col>
        <Col span="6">单节售价</Col>
        <Col span="6">售价</Col>
      </Row>
      <Row :gutter="16" v-for="(item, $index) in cardList" :key="item.card_id">
        <Col span="12">{{ item.name }}</Col>
        <Col span="6">
          <Input-number
            v-if="item.is_pt_time_limit_card != 1 && (item.card_type_id === 4 || item.card_type_id === 5)"
            :max="99999"
            :min="0"
            :step="0.01"
            :precision="2"
            :active-change="false"
            v-model.number="item.single_price"
            @on-change="handleChange($index)"
          ></Input-number>
          <span v-else>-</span>
        </Col>
        <Col span="6">
          <span v-if="item.is_pt_time_limit_card != 1 && (item.card_type_id === 4 || item.card_type_id === 5)">{{
            item.current_price
          }}</span>
          <Input-number
            v-else
            v-model.number="item.current_price"
            :max="99999"
            :min="0"
            :step="0.01"
            :precision="2"
            :active-change="false"
            @on-change="handleChange($index)"
          ></Input-number>
        </Col>
      </Row>
      <Row>
        <Col span="4">
          套餐包总售价
        </Col>
        <Col span="20">{{ this.storePrice }}</Col>
      </Row>
      <Row>
        <Col span="4">
          会员端购买
        </Col>
        <Col span="20">
          <RadioGroup v-model="radioValue" @on-change="handleChange(-1)">
            <Radio :label="1">支持</Radio>
            <Radio :label="0">不支持</Radio>
          </RadioGroup>
        </Col>
      </Row>
    </div>
    <div v-else>
      没有添加卡
    </div>
  </div>
</template>

<script>
export default {
  name: 'BundlePackageStoreSlot',
  props: {
    packageData: {
      type: Array,
      default: () => []
    },
    store: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      radioValue: 0,
      cardList: []
    }
  },
  computed: {
    storePrice() {
      let price = 0
      this.cardList.forEach(card => {
        price = card.current_price + price
      })
      return Number(price.toFixed(2))
    }
  },
  watch: {
    // packageData: {
    //   deep: true,
    //   handler: function() {
    //     console.log('wow..........')
    //   }
    // },
    // 监控增加和减少，单个变化看不到，靠，只好改变引用
    packageData() {
      this.changeAllData()
    },
    // 监控场馆还不是原来那个场馆不
    store(newval, oldval) {
      if (newval.bus_id !== oldval.bus_id) {
        this.cardList = JSON.parse(JSON.stringify(this.store.detail))
      }
    }
  },
  methods: {
    changeAllData() {
      this.cardList = JSON.parse(JSON.stringify(this.packageData))
      if (!Array.isArray(this.store.detail)) {
        this.store.detail = []
      }

      let newDetail = []
      for (let index = 0; index < this.cardList.length; index++) {
        const temp = this.cardList[index]
        // const item = this.store.detail.find(
        //   item => item.card_id === temp.card_id
        // )
        const item = this.store.detail[index]
        if (item && item._id === temp._id) {
          if (item.is_pt_time_limit_card!=1 && [4, 5].includes(item.card_type_id)) {
            // 次数变，单价不变，金额重算
            temp.single_price = item.single_price
            temp.current_price = Number(
              (temp.single_price * temp.all_num).toFixed(2)
            )
          } else {
            // 钱不动
            temp.single_price = item.single_price
            temp.current_price = item.current_price
          }
        }
        // 全部去模板 card_id
        // if (item.card_id) {
        //   temp.card_id = item.card_id
        // }
        newDetail.push(temp)
      }
      this.store.detail = newDetail
      this.store.total_price = this.storePrice
      this.store.status = this.radioValue
    },
    handleChange(cardIdx) {
      if (cardIdx !== -1) {
        if (Array.isArray(this.store.detail) && this.store.detail.length > 0) {
          const temp = this.cardList[cardIdx]
          const item = this.store.detail[cardIdx]
          if (item.is_pt_time_limit_card!=1 && [4, 5].includes(item.card_type_id)) {
            item.single_price = temp.single_price
            const new_price = Number(
              (item.single_price * item.all_num).toFixed(2)
            )
            item.current_price = new_price
            temp.current_price = new_price
          } else {
            item.current_price = temp.current_price
          }
        }
      }

      this.store.total_price = this.storePrice
      this.store.status = this.radioValue
    }
  },
  created() {
    this.radioValue = this.store.status === 1 ? 1 : 0
    this.changeAllData()
  }
}
</script>

<style lang="less" scoped></style>
