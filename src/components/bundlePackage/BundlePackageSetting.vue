<template>
  <div class="box">
    <div class="title-box" :style="$attrs.styleObject">
      <h6>套餐设置</h6>
      <Button type="info" @click="handleDialogShow" :disabled="disabled"
        >添加</Button
      >
    </div>
    <div style="width:100%" :style="$attrs.styleObject">
      <Table
        :columns="packageColumn"
        :data="packageData"
        disabled-hover
      ></Table>
    </div>
    <Modal v-model="packageFlag" :title="dialogTitle" width="700">
      <Form
        ref="packageForm"
        :model="packagePost"
        :rules="ruleValidate"
        :label-width="100"
      >
        <FormItem label="场馆">
          <Select
            v-model="packagePost.bus_id"
            class="dialog-item"
            placeholder="请选择"
            @on-change="handleBusChange"
            filterable
          >
            <Option
              v-for="item in adminBusList"
              :key="item.id"
              :value="item.id"
              >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem label="">
          <RadioGroup
            v-model="bpCardType"
            type="button"
            size="small"
            @on-change="handleCardTypeChange"
          >
            <Radio label="-1">全部</Radio>
            <Radio label="1">期限卡</Radio>
            <Radio label="2">次卡</Radio>
            <Radio label="3">储值卡</Radio>
            <Radio label="4">私教课</Radio>
            <Radio label="5">泳教课</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem :label="dialogLabel" prop="card_id">
          <Select
            v-model="packagePost.card_id"
            class="dialog-item"
            placeholder="请选择"
            @on-change="handleCardChange"
            filterable
          >
            <Option
              v-for="item in bpCardListFilter"
              :key="item.card_id"
              :value="item.card_id"
              >{{ item.card_name }}</Option
            >
          </Select>
        </FormItem>
        <template v-if="showDeadlineCard || selectedCard.is_pt_time_limit_card ==1">
          <FormItem label="有效期(天)" prop="all_days">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="1"
                :precision="0"
                :disabled="selectedCard.is_pt_time_limit_card ==1 || (!canEditFields && (actionType == 'add' || actionType == 'reNew'))"
                v-model.number="packagePost.all_days"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.buy_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.buy_max }}</Alert>
            </div>
          </FormItem>
          <FormItem v-if="selectedCard.is_pt_time_limit_card !=1 && canUpdatePrice" label="售价" prop="current_price">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="0.01"
                :precision="2"
                :active-change="false"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                v-model.number="packagePost.current_price"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.sale_range" type="warning" style="margin-left: 20px;">
                {{ open_rule.sale_range_text }}
              </Alert>
            </div>
          </FormItem>
        </template>
        <template v-else-if="showTimesCard">
          <FormItem label="次数" prop="all_num">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="1"
                :precision="0"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                v-model.number="packagePost.all_num"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.buy_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.buy_max }}</Alert>
            </div>
          </FormItem>
          <FormItem label="有效期(天)" prop="all_days">
            <Input-number
              class="dialog-item"
              :max="99999"
              :min="0"
              :precision="0"
              :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
              v-model.number="packagePost.all_days"
            ></Input-number>
          </FormItem>
          <FormItem v-if="canUpdatePrice" label="售价" prop="current_price">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="0.01"
                :step="0.01"
                :precision="2"
                :active-change="false"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                v-model.number="packagePost.current_price"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.sale_range" type="warning" style="margin-left: 20px;">
                {{ open_rule.sale_range_text }}
              </Alert>
            </div>
          </FormItem>
        </template>
        <template v-else-if="showValueCard">
          <FormItem label="价值金额" prop="total">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="0.01"
                :step="0.01"
                :precision="2"
                :active-change="false"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                v-model.number="packagePost.total"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.buy_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.buy_max }}</Alert>
            </div>
          </FormItem>
          <FormItem label="有效期(天)" prop="all_days">
            <Input-number
              class="dialog-item"
              :max="99999"
              :min="0"
              :precision="0"
              :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
              v-model.number="packagePost.all_days"
            ></Input-number>
          </FormItem>
          <FormItem v-if="canUpdatePrice" label="售价" prop="current_price">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="0.01"
                :step="0.01"
                :precision="2"
                :active-change="false"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                v-model.number="packagePost.current_price"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.sale_range" type="warning" style="margin-left: 20px;">
                {{ open_rule.sale_range_text }}
              </Alert>
            </div>
          </FormItem>
        </template>
        <template v-else-if="showCourseCard && selectedCard.is_pt_time_limit_card !=1">
          <FormItem label="总节数" prop="all_num">
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="1"
                :precision="0"
                v-model.number="packagePost.all_num"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.buy_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.buy_max }}</Alert>
            </div>
          </FormItem>
          <FormItem
            v-if="canUpdatePrice"
            label="单节价格(元)"
            prop="single_price"
          >
            <div class="input-number-box">
              <Input-number
                class="dialog-item"
                :max="99999"
                :min="0"
                :step="0.01"
                :precision="2"
                :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
                :active-change="false"
                v-model.number="packagePost.single_price"
              ></Input-number>
              <Alert class="rule-alert" v-if="open_rule.sale_range" type="warning" style="margin-left: 20px;">
                {{ open_rule.sale_range_text }}
              </Alert>
            </div>
          </FormItem>
          <FormItem v-if="canUpdatePrice" label="总价格">
            <span>{{ packageItemPrice }}</span>
          </FormItem>
          <FormItem label="单节有效期(天)" prop="single_days">
            <Input-number
              class="dialog-item"
              :max="99999"
              :min="0"
              :precision="0"
              :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')"
              v-model.number="packagePost.single_days"
            ></Input-number>
          </FormItem>
          <FormItem label="总有效期">
            <span>{{ packageItemDay }}</span>
          </FormItem>
        </template>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="handlePackageSave">确定</Button>
        <Button @click="handlePackageCancel">取消</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getCards } from 'src/service/getData';

const INIT_PACKAGE_POST = {
  _id: '',
  name: '',
  bus_id: '',
  bus_name: '',
  card_id: '',
  card_type_id: -1,
  is_pt_time_limit_card: 0,
  all_num: 10,
  single_price: 0,
  current_price: 0,
  single_days: 5,
  all_days: 0,
  total: 0
}

const bellona = (rule, value, callback) => {
  if (typeof value === 'number') {
    callback()
  } else {
    callback('不能为空')
  }
}

export default {
  name: 'BundlePackageSetting',
  inheritAttrs: true,
  props: {
    bpPostId: {
      type: [String, Number],
      default: ''
    },
    belongBusId: {
      type: [String, Number],
      default: ''
    },
    packageData: {
      type: Array,
      default: () => []
    },
    addColumn: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    actionType: { // set 卡课管理编辑
      type: String
    },
    overRuleAuth: { // 规则范围是否可超出 ture 可
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // table
      packageColumn: [
        {
          title: '名称',
          key: 'name'
        },
        {
          title: '场馆',
          key: 'bus_name'
        },
        {
          title: '总共',
          key: 'total',
          render(h, { row }) {
            if (row.card_type_id === 1 || row.is_pt_time_limit_card == 1) {
              return <span>{row.all_days}天</span>
            } else if (row.card_type_id === 2) {
              return <span>{row.all_num}次</span>
            } else if (row.card_type_id === 3) {
              return <span>{row.total}元</span>
            } else {
              return <span>{row.all_num}节</span>
            }
          }
        },
        {
          title: '单节有效期',
          key: 'single_days',
          render(h, { row }) {
            if (row.is_pt_time_limit_card != 1 && [4, 5].includes(row.card_type_id)) {
              if (row.single_days == 0) {
                return <span>永久</span>
              } else {
                return <span>{row.single_days}天/节</span>
              }
            } else {
              return <span>-</span>
            }
          }
        },
        {
          title: '总有效期',
          key: 'all_days',
          render: (h, { row }) => {
            if (row.all_days == 0) {
              return <span>永久</span>
            } else {
              return <span>{row.all_days}天</span>
            }
          }
        },
        {
          title: '操作',
          key: 'option',
          render: (h, params) => {
            return (
              <div class="btn-box">
                <i-button
                  type="text"
                  size="small"
                  style="margin-right:10px"
                  disabled={this.disabled}
                  onClick={$event => {
                    this.bpCardType = params.row.card_type_id + ''
                    this.selectedCard = { ...params.row }
                    this.selectedCard.card_type_id =
                      params.row.card_type_id + ''
                    this.selectedCard.is_pt_time_limit_card = params.row.is_pt_time_limit_card
                    this.packageFlag = true
                    this.$nextTick(() => {
                      this.packagePost = { ...params.row }

                      const list = this.bpCard[params.row.bus_id] || []
                      if (!list.length) {
                        this.getCardList()
                      }
                    })
                  }}
                >
                  编辑
                </i-button>
                <i-button
                  type="text"
                  size="small"
                  style={this.disabled ? '' : 'color:red'}
                  ghost
                  disabled={this.disabled}
                  onClick={$event => {
                    this.packageData.splice(params.index, 1)
                    this.$emit('updatePackage', this.packageData)
                  }}
                >
                  删除
                </i-button>
              </div>
            )
          }
        }
      ],
      // dialog
      packageFlag: false,
      packagePost: { ...INIT_PACKAGE_POST },
      ruleValidate: {
        card_id: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        all_days: [
          {
            required: true,
            trigger: 'blur',
            validator: bellona
          }
        ],
        all_num: [{ required: true, trigger: 'blur', validator: bellona }],
        current_price: [
          { required: true, trigger: 'blur', validator: bellona }
        ],
        single_price: [{ required: true, trigger: 'blur', validator: bellona }],
        single_days: [{ required: true, trigger: 'blur', validator: bellona }],
        total: [{ required: true, trigger: 'blur', validator: bellona }]
      },
      selectedCard: {},
      bpCardType: '-1',
      bpCard: {
        // '1': []
      }
    }
  },
  computed: {
    ...mapGetters(['canEditFields', 'adminBusList', 'busId', 'busName']),
    dialogTitle() {
      if (this.packagePost._id) {
        return '套餐编辑'
      } else {
        return '套餐添加'
      }
    },
    dialogLabel() {
      if (['-1', ''].includes(this.bpCardType)) {
        return '会员卡名称'
      } else if (['1', '2', '3'].includes(this.bpCardType)) {
        return '会籍卡名称'
      } else if (this.bpCardType === '4') {
        return '私教课名称'
      } else if (this.bpCardType === '5') {
        return '泳教课名称'
      }
    },
    bpCardListFilter() {
      const { packagePost, bpCard } = this
      const bpList = bpCard[packagePost.bus_id] || []
      if (Array.isArray(bpList) && bpList.length === 0) {
        return []
      }
      let list = bpList.filter(item => {
        return item.sale_status === '1'
      })
      if (this.bpCardType !== '-1') {
        list = list.filter(card => card.card_type_id === this.bpCardType)
      }
      return list
    },
    showDeadlineCard() {
      return this.bpCardType === '1' || (this.bpCardType === '-1' && this.selectedCard.card_type_id === '1')
    },
    showTimesCard() {
      return this.bpCardType === '2' || (this.bpCardType === '-1' && this.selectedCard.card_type_id === '2')
    },
    showValueCard() {
      return this.bpCardType === '3' || (this.bpCardType === '-1' && this.selectedCard.card_type_id === '3')
    },
    showCourseCard() {
      return (
        ['4', '5'].includes(this.bpCardType) ||
        ['4', '5'].includes(this.selectedCard.card_type_id)
      )
    },
    packageItemPrice() {
      return (this.packagePost.all_num * this.packagePost.single_price).toFixed(
        2
      )
    },
    packageItemDay() {
      return this.packagePost.all_num * this.packagePost.single_days
    },
    canUpdatePrice() {
      return !!this.addColumn
    },
    // 控制显示上限提示及文本
    open_rule() {
      const { actionType, selectedCard: card, canEditFields } = this;
      if(canEditFields &&['add', 'reNew', 'set'].includes(actionType) && card && card.is_open_rule === '1' && card.get_range) {
        const { buy_max, gift_max, sale_range } = card;
        const prefix = card.is_pt_time_limit_card == '1'
          ? '售价'
          : card.card_type_id =='4' || card.card_type_id =='5'
            ? '单节售价'
            : '售价'

        return {
          buy_max,
          gift_max,
          sale_range,
          sale_range_text: prefix + '浮动范围' + card.sale_range,
        }
      }else {
        return {};
      }
    },
  },
  methods: {
    ...mapActions(['getAdminBusList']),
    // dialog
    getCardList() {
      // getCards(this.packagePost.bus_id, undefined, this.packagePost.bus_id == this.busId ? undefined : 2).then(res => {
      getCards(this.packagePost.bus_id, undefined, window.IS_BRAND_SITE ? 2 : 0, this.bpPostId && this.belongBusId).then(res => {
        if(res.data.errorcode == 0) {
          this.$set(this.bpCard, this.packagePost.bus_id, res.data.data)
        } else {
            this.$Message.error(res.data.errormsg)
        }
      })

    },
    handleBusChange(val) {
      this.packagePost.card_id = ''

      const list = this.bpCard[val] || []
      if (!list.length) {
        this.getCardList()
      }

      if (Array.isArray(this.adminBusList)) {
        const item = this.adminBusList.find(v => v.id === val)
        if (item) {
          this.packagePost.bus_name = item.name
        }
      }
    },
    handleCardChange(card_id) {
      if (card_id) {
        this.selectedCard = this.bpCardListFilter.find(
          item => item.card_id === card_id
        )

        this.packagePost.name = this.selectedCard.card_name
        this.packagePost.card_type_id = Number(this.selectedCard.card_type_id)
        /* 增加购卡规则的参数 */
        const { is_open_rule, sale_rule_id, buy_max, gift_max, sale_range, get_range } = this.selectedCard;
        this.packagePost.is_open_rule = is_open_rule;
        this.packagePost.sale_rule_id = sale_rule_id;
        this.packagePost.buy_max = buy_max;
        this.packagePost.gift_max = gift_max;
        this.packagePost.sale_range = sale_range;
        this.packagePost.get_range = get_range;

        switch (this.selectedCard.is_pt_time_limit_card==1?'1':this.selectedCard.card_type_id) {
          case '1':
            this.packagePost.is_pt_time_limit_card = this.selectedCard.is_pt_time_limit_card
            this.packagePost.all_days = Number(this.selectedCard.end_time)
            this.packagePost.current_price = Number(
              this.selectedCard.current_price
            )
            break
          case '2':
            this.packagePost.all_num = Number(this.selectedCard.number)
            this.packagePost.all_days = Number(this.selectedCard.end_time)
            this.packagePost.current_price = Number(
              this.selectedCard.current_price
            )
            break
          case '3':
            this.packagePost.all_days = Number(this.selectedCard.end_time)
            this.packagePost.total = Number(this.selectedCard.number)
            this.packagePost.current_price = Number(
              this.selectedCard.current_price
            )
            break
          default:
            this.packagePost.all_num = Number(this.selectedCard.buy_min_value)
            this.packagePost.single_days = Number(this.selectedCard.end_time)
            this.packagePost.single_price = Number(
              this.selectedCard.single_price
            )
            const valString = (
              this.packagePost.all_num * this.packagePost.single_price
            ).toFixed(2)
            this.packagePost.current_price = Number(valString)
            break
        }
      }
    },
    handleCardTypeChange() {
      this.selectedCard = {}
      // this.resetDialog()
    },
    resetDialog() {
      const bus_id = this.belongBusId || this.busId
      let bus_name = this.busName
      if (Array.isArray(this.adminBusList)) {
        const item = this.adminBusList.find(v => v.id === bus_id)
        if (item) {
          bus_name = item.name
        }
      }
      this.packagePost = { ...INIT_PACKAGE_POST, bus_id, bus_name }
      this.$refs.packageForm.resetFields()
    },
    handlePackageSave() {
      this.$refs.packageForm.validate(flag => {
        if (flag) {
          const isOK = this.handleCheckCardRules()
          if(isOK !== 'ok') return this.$Message.error(isOK);

          if (this.packagePost.is_pt_time_limit_card != 1 && [4, 5].includes(this.packagePost.card_type_id)) {
            console.log(this.packagePost.is_pt_time_limit_card);
            if (this.canUpdatePrice) {
              this.packagePost.total = this.packageItemPrice
            }
            this.packagePost.all_days = this.packageItemDay
            this.packagePost.current_price = Number(
              (
                this.packagePost.single_price * this.packagePost.all_num
              ).toFixed(2)
            )
          }
          if (this.packagePost._id) {
            // eidt
            const index = this.packageData.findIndex(
              item => item._id === this.packagePost._id
            )
            // 这个不得引起 table render
            this.packageData[index] = { ...this.packagePost }
            this.packageData = JSON.parse(JSON.stringify(this.packageData))
          } else {
            // add
            this.packagePost._id = Math.random()
            this.packageData.push({ ...this.packagePost })
          }
          this.$emit('updatePackage', this.packageData)
          // this.resetDialog()
          this.packageFlag = false
        }
      })
    },
    handlePackageCancel() {
      // this.resetDialog()
      this.packageFlag = false
    },
    handleDialogShow() {
      this.bpCardType = '-1'
      this.packageFlag = true

      if (!Object.keys(this.bpCard).length) {
        this.handleBusChange(this.packagePost.bus_id)
      }

      this.$nextTick(() => {
        this.resetDialog()
      })
    },
    /* 校验编辑购卡范围权限及范围 */
    handleCheckCardRules() {
      const { packagePost: selected } = this;
      let errMsg = 'ok';
      if(this.overRuleAuth === false) {
        if(selected && selected.is_open_rule === '1' && selected.get_range && selected.is_pt_time_limit_card != '1') {
          const {
            card_type_id,
            is_pt_time_limit_card,
            get_range: { buy_max, gift_max, sale_range }
          } = selected;
          const index = +card_type_id - 1;
          const tips = [
            ['购买天数', '购买次数','价值金额', '购买天数', '购买天数'],
            ['赠送天数', '赠送次数','赠送金额', '赠送节数', '赠送节数'],
            ['售价', '售价', '售价', '单节售价', '单节售价'],
          ];
          const keys = {
            0: ['all_days', 'current_price'], // 期限
            1: ['all_num', 'current_price'],  // 次卡
            2: ['total', 'current_price'],    // 储值
            3: ['all_num', 'single_price'],   // 私教
            4: ['all_num', 'single_price'],   // 泳教
          }
          const buy_num = selected[keys[index][0]]
          const amount = selected[keys[index][1]]


          if(buy_max !== '' && buy_num > +buy_max) { // '' 代表无限制
            errMsg = tips[0][index] + '不能大于上限！';
          }else if(!Array.isArray(sale_range)) { // [] 代表无限制
            const { max, min } = sale_range;

            if(amount > +max || amount < +min) {
              errMsg = tips[2][index] + '不在浮动范围内！';
            }
          }
        }
      }
      return errMsg;
    }
  },
  created() {
    if (this.addColumn) {
      this.packageColumn.splice(this.addColumn.index, 0, this.addColumn.data)
    }

    this.packagePost.bus_id = this.belongBusId || this.busId
    // 获取场馆列表
    !this.adminBusList && this.getAdminBusList()
    // this.handleBusChange(this.packagePost.bus_id)
    // 编辑时需要等待套餐包详情数据更新后，重新赋值
    const unwatch = this.$watch('belongBusId', function() {
      this.packagePost.bus_id = this.belongBusId || this.busId
      this.handleBusChange(this.packagePost.bus_id)
      unwatch()
    });
  }
}
</script>

<style scoped lang="less">
.dialog-item {
  width: 61%;
}
.title-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 10px 30px;
}
.input-number-box {
  display: flex;
  align-items: center;
  .rule-alert {
    margin-bottom: 0;
    padding: 6px 14px 6px 14px;
  }
}
</style>

