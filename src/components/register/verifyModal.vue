<template>
  <Modal v-model="show"
         title="请输入验证码"
         :mask-closable="false"
         @on-cancel="$emit('on-cancel')"
         @on-ok="$emit('on-ok', verifyModalCode)">
    <img :src="verifyModalImg"
         @click="getVerifyImg">
    <a href="#" @click="getVerifyImg">换一张</a>
    <p>请输入验证码</p>
    <input type="text" style="height: 35px; line-height: 35px; margin-top: 5px; border: 1px solid #dcdcdc; font-size: 24px; padding: 5px" v-model="verifyModalCode">
  </Modal>
</template>

<script>
  import {getBaseUrl} from 'utils/config'
  export default {
    name: 'verifyModal',
    props: {
      value: {
        type: Boolean
      }
    },
    computed: {
    show: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    data() {
      return {
        verifyModalCode: '',
        verifyModalImg: '',
      }
    },
    created() {
      this.getVerifyImg()
    },
    methods: {
      getVerifyImg() {
        let timestamp = Date.parse(new Date());
        this.verifyModalImg = getBaseUrl() + '/Web/Tool/captchatype?type=register&' + timestamp;
      },
    },
    watch: {
      show(val) {
        val && this.getVerifyImg()
      }
    }
  }
</script>
