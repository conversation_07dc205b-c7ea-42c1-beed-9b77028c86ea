const maxLimit = {
  min: 1,
  max: 6,
  message: '最大值999999'
}
//年卡,次卡,储值卡,私教卡
export const cardTypeValidArr = [{
    itemRules: [{
        required: true,
        type: 'string',
        message: '期限卡必填'
      },
      {
        type: 'string',
        pattern: /^[1-9]\d*$/,
        message: '请输入天数(正整数)'
      },
      maxLimit
    ],
    giftRules: [{
        required: false,
        type: 'string',
        pattern: /^\d+$/,
        message: '赠送天数必须为正整数或0'
      },
      maxLimit
    ],
    allLabel: '购买天数',
    unitLabel: '天数',
    giftLabel: '赠送天数',
    allPla: '期限卡必填',
    giftPla: '请输入赠送天数'
  },
  {
    itemRules: [{
        required: true,
        type: 'string',
        pattern: /^[1-9]\d*$/,
        message: '请输入总次数(正整数)'
      },
      maxLimit
    ],
    giftRules: [{
      type: 'string',
      pattern: /^\d+$/,
      message: '赠送次数应为正整数或0'
    }, maxLimit],
    allLabel: '购买次数',
    unitLabel: '次数',
    giftLabel: '赠送次数',
    allPla: '请输入总次数',
    giftPla: '请输入赠送次数'
  },
  {
    itemRules: [{
      required: true,
      type: 'string',
      // pattern: /^[0-9]+(.[0-9]{1,2})?$/, // 大于等于0
      pattern:/^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/, // 大于0
      message: '金额必须大于0且最多只能保留两位小数'
    }],
    giftRules: [{
      type: 'string',
      pattern: /^[0-9]+(.[0-9]{1,2})?$/,
      message: '赠送金额必须大于等于0且只能保留两位小数'
    }],
    allLabel: '价值金额',
    unitLabel: '金额',
    giftLabel: '赠送金额',
    allPla: '请输入价值金额',
    giftPla: '请输入赠送金额'
  },
  {
    itemRules: [{
      required: true,
      type: 'string',
      pattern: /^[1-9]\d*$/,
      message: '请正确输入总节数'
    }, maxLimit],
    giftRules: [{
      type: 'string',
      pattern: /^\d+$/,
      message: '赠送节数应为正整数或0'
    }, maxLimit],
    allLabel: '购买节数',
    unitLabel: '节数',
    giftLabel: '赠送节数',
    allPla: '请输入总节数',
    giftPla: '请输入赠送节数'
  },
  {
    itemRules: [{
      required: true,
      type: 'string',
      pattern: /^[1-9]\d*$/,
      message: '请正确输入总节数'
    }, maxLimit],
    giftRules: [{
      type: 'string',
      pattern: /^\d+$/,
      message: '赠送节数应为正整数或0'
    }, maxLimit],
    allLabel: '购买节数',
    unitLabel: '节数',
    giftLabel: '赠送节数',
    allPla: '请输入总节数',
    giftPla: '请输入赠送节数'
  }
];
