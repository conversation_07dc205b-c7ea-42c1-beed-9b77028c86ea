
<style lang="less" scoped>
</style>

<template>
  <div v-if="Array.isArray(saleList) && Array.isArray(coachList) ">
    <FormItem :label="index == 0 ? '业绩归属' : `协助成单${index}`" :prop="index===0?'marketers_id':'anyway'" v-for="(belong, index) in list" :key="belong.id" :rules="[{required: index===0, trigger: 'change', validator: bellona}]">
      <RadioGroup v-if="hasStore&&index===0" v-model="belongType" @on-change="handleBelongTypeChange" style="margin-bottom:10px">
        <Radio label="个人"></Radio>
        <Radio label="门店"></Radio>
      </RadioGroup>
      <div :style="styles.alignCenter" v-if="belongType==='个人'">
        <Select @on-change="handleMarketerChange(index, belong.marketers_id)"
          v-if="saleList && coachList"
          placeholder="请选择" v-model="belong.marketers_id" :style="styles.select" transfer filterable>
          <Option value="-1">默认会籍</Option>
          <Option value="c-2">默认教练</Option>
          <Option value="c-3">默认泳教</Option>
            <Option v-for="item in saleList" :key="item.marketers_id" :value="item.marketers_id" :label="item.sale_name" v-show="!selectedId.includes(item.marketers_id)&&item.deleted!=1">
              {{item.sale_name}}
            </Option>
            <!-- 请假、转卡选择销售人员 -->
            <!-- 方便后端区分是教练还是销售教练id前加c -->
            <Option v-for="item in coachList" :key="'c' + item.coach_id" :value="'c'+item.coach_id" :label="item.coach_name" v-show="!selectedId.includes(item.marketers_id)&&item.deleted!=1">
              <span>{{item.coach_name}}</span>
              <span style="float:right;color:#ccc">{{item.is_swim?'泳教':'私教'}}</span>
            </Option>
        </Select>
        <div :style="styles.percent" v-show="list.length > 1">贡献占比
          <Input @on-change="handlePercentChange(index, $event)" :value="belong.percent" :style="styles.inputNumber" />%</div>
          <template v-if="list[0].marketers_id && (actionType !== 'switch' && actionType !== 'suspend')">
            <Button :style="styles.button" v-if="index == 0" type="text" @click="addPayType">添加协助人</Button>
            <Button :style="styles.button" v-else type="text" @click="deletePayType(index)" style="min-width: auto; color: #d8321f">删除</Button>
          </template>
      </div>
    </FormItem>
  </div>
</template>

<script>
  import { getSales, getcoachsInfo } from 'src/service/getData';
  import EventBus from '../EventBus';
  export default {
    name: 'belongTo',
    props: {
      actionType: '',
      value: {
        type: Array,
        required: true
      },
      cardTypeId: {},
      amount: {}, // 分配金额
      from: {},
      hasStore: {
        type: Boolean,
        default: false
      },
      isStore: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        bellona: (rule, value, callback) => {
          let val = ''
          if(Array.isArray(this.list)) {
            val = this.list[0]
          }
          if ((!!val && !!val.marketers_id) || this.belongType === '门店') {
            callback()
          } else {
            callback('请选择业绩归属!')
          }
        },
        styles: {
          alignCenter: {
            display: 'flex',
            alignItems: 'center',
            // width: '60%',
            maxWidth: '660px'
          },
          select: {
            // maxWidth: '400px',
            flex: 1
          },
          percent: {
            display: 'flex',
            alignItems: 'center',
            margin: '0 10px'
          },
          inputNumber: {
            margin: '0 10px',
            width: '60px'
          },
          button: {
            width: '100px'
          }
        },
        saleList: '',
        coachList: '',
        belongType: '个人'
      };
    },
    watch: {
      isStore(newValue) {
        console.log(newValue)
        if (newValue) {
          this.belongType = '门店'
        } else {
          this.belongType = '个人'
        }
      }
    },
    created() {
      this.getSalesList();
      this.getCoachList();
      if (this.isStore) {
        this.belongType = '门店'
      }
    },
    mounted() {
      EventBus.$on("on-check-marketer", toward => {
        this.checkMarketer(toward)
      })
    },
    beforeDestroy() {
      EventBus.$off('on-check-marketer')
    },
    computed: {
      list: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      },
      disabled() {
        return this.from == 'cancelCard';
      },
      selectedId() {
        return this.list.map(item => item.marketers_id);
      }
    },
    methods: {
      //获取教练列表
      getCoachList() {
        getcoachsInfo('', this.actionType === 'edit' ? 1 : 0).then(res => {
          if (res.data.errorcode == 0) {
            let arr = res.data.data
            arr.sort((a, b) => (a.is_swim -b.is_swim))
            this.coachList = arr.map(item => {
              return {
                ...item,
                ...{
                  marketers_id: 'c' + item.coach_id,
                  sale_name: item.coach_name
                }
              };
            });
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      getSalesList() {
        getSales({ contain_deleted: this.actionType === 'edit' ? 1 : 0}).then(res => {
          if (res.data.errorcode === 0) {
            this.saleList = res.data.data;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handlePercentChange(index, ev) {
        let percent = ev.target.value;
        if (Number.isNaN(+percent)) {
          this.$Message.error('请输入数字');
          return false;
        }
        const arr = [...this.list];
        // 只有两项的时候自动计算另一项的值
        if (this.list.length === 2) {
          const total = 100;
          if (index === 0) {
            arr[1].percent = Math.max(total - percent, 0).toFixed(2);
            arr[1].amount = (this.amount * arr[1].percent / 100).toFixed(2);
          } else {
            arr[0].percent = Math.max(total - percent, 0).toFixed(2);
            arr[0].amount = (this.amount * arr[0].percent / 100).toFixed(2);
          }
        }
        arr[index].percent = percent;
        arr[index].amount = (this.amount * arr[index].percent / 100).toFixed(2);
        this.list = arr;
      },
      checkMarketer(toward = true) {
        // const id = this.selectedId[0]
        const marketer = this.list[0]
        if (typeof marketer === 'undefined' || marketer.marketers_id === '' || !this.cardTypeId) {
          return false
        }
        if (!['-1', 'c-2', 'c-3'].includes(marketer.marketers_id) && this.from !== 'cancelCard' ) {
          this.$service.post('/Web/Member/checksale', {
            marketers_id: marketer.marketers_id,
            card_type_id: this.cardTypeId
          }).then(res => {
            if (res.data.errorcode === 0) {
              if (res.data.data.can_sale === 0) {
                if (toward) {
                  this.$Message.error('销售人员不允许售卖该卡种')
                }
                this.list[0].marketers_id = ''
                this.$forceUpdate()
              }
            }
          })
        }
      },
      handleMarketerChange(index, id) {
        // check authoriate
        if (index === 0) {
          this.checkMarketer(true)
        }

        if (!id && id !== 0) return false;
        const arr = [...this.list];
        const eqSale = this.saleList.concat(this.coachList).find(item => item.marketers_id == id)
        let name = eqSale ? eqSale.sale_name  : '';
        let is_swim = eqSale ? eqSale.is_swim  : '';
        arr[index].marketers_id = id;
        arr[index].marketers_name = name;
        if (id.indexOf('c') === 0) {
          arr[index].marketer_category = '2'
        } else {
          arr[index].marketer_category = '1';
        }
        if (id == -1) {
          arr[index].marketers_name = '默认会籍'
          arr[index].marketer_category = '1'
        } else if (id == -2) {
          arr[index].marketers_name = '默认教练'
          arr[index].marketer_category = '2'
        } else if (id == -3) {
          arr[index].marketers_name = '默认泳教'
          arr[index].marketer_category = '2'
        }
        arr[index].is_swim = is_swim
        this.list = arr;

        // change manner
        if (index === 0) {
          EventBus.$emit('deal-approach-change')
        }
      },
      addPayType() {
        this.list.push({ marketers_id: '', amount: '', percent: '' });
        this.list = this.value;
      },
      deletePayType(index) {
        this.list.splice(index, 1);
        if (this.list.length == 1) {
          this.list[0].percent = 100;
          this.list[0].amount = this.amount;
        }
        this.list = this.value;
      },
      handleBelongTypeChange() {
        if (this.belongType === '门店') {
          this.list[0].marketers_id = '0'
          this.list[0].marketers_name = ''
          this.list[0].marketer_category = '3'
          this.list[0].percent = '100.00'
          this.list[0].proportion = '100.00' // fix 12607
          this.list[0].amount = this.amount // fix 12607
          // this.list = this.list.filter((item, index) => index===0)
          for (let index = this.list.length - 1; index > 0; index--) {
            this.list.splice(index, 1)
          }
        } else if (this.belongType === '个人') {
          this.list[0].marketers_id = ''
          this.list[0].marketers_name = ''
          this.list[0].marketer_category = ''
          this.list[0].percent = '100.00'
        }
      }
    }
  };
</script>
