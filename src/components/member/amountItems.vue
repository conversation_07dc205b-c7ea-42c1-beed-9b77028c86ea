<!-- eslint-disable vue/no-side-effects-in-computed-properties -->
<template>
  <div>
    <Form-item
      :label="actionType == 'seperate'?'课程补差价':from === '销卡' ? '退款金额' : $route.name == '补卡'? '补卡费' : actionType == 'switch'? '转卡费' : actionType == 'change'? '升卡费' : actionType == 'suspend'? '请假费': '售价'"
      prop="sale_amount"
    >
      <div class="input-number-box">
        <Input
          v-model="computedPostData.sale_amount"
          placeholder="请填写金额"
          :disabled="cardTypeId==6||(actionType == 'suspend' && !timeZero) || (actionType == 'edit' && !hasEditAmountAuth) || (actionType === 'edit' && computedPostData.pay_type == 8) || (!canEditFields && (actionType == 'add' || actionType == 'reNew'))" />
        <Alert
          v-if="canEditFields && ['add', 'reNew'].includes(actionType) && selectedCard && selectedCard.sale_range"
          class="rule-alert"
          type="warning"
          style="margin-left: 20px;">
          {{ selectedCard.is_pt_time_limit_card == '1' ? '售价' : selectedCard.card_type_id =='4' || selectedCard.card_type_id =='5' ? '单节售价' : '售价' }}浮动范围{{ selectedCard.sale_range }}
        </Alert>
      </div>
    </Form-item>

    <Form-item v-if="userFrontMoneyList && userFrontMoneyList.length && (from?(from=='购卡'||from=='升卡'||from=='续私教'||from=='续卡'):(actionType != 'seperate'))" label="定金启用">
      <Checkbox
        v-for="(item, index) in userFrontMoneyList"
        :key="item.id"
        v-model="userFrontMoneyList[index].status"
        true-value="1"
        false-value="0"
        :disabled="(actionType == 'edit' && !hasEditAmountAuth) || (actionType === 'edit' && computedPostData.pay_type == 8)">
        定金抵扣({{ item.purpose==0 ? '会员卡' : item.purpose==1 ? '私教课' : '其他' }}) {{ item.amount }}元
      </Checkbox>
    </Form-item>

    <!-- 套餐包不显示折扣券 8277 跨店升卡暂不显示折扣券 14110 -->
    <Form-item
      v-if="cardTypeId!==6 && ['add', 'edit', 'change', 'reNew'].includes(actionType) && !otherBusInfo"
      label="折扣券">
      <div style="display: flex">
        <p v-if="selectedDiscount">{{ selectedDiscount.coupon_name }}</p>
        <p v-else-if="computedPostData.selectedDiscount">{{ computedPostData.selectedDiscount.coupon_name }}</p>
        <p v-else>{{ discountText }}</p>
        <Button
          v-if="['add', 'change', 'reNew'].includes(actionType)"
          type="text"
          @click="clickAllDiscount">
          全部折扣券
        </Button>
      </div>
    </Form-item>

    <Form-item v-if="(frontMoneyAmount || this.selectedDiscount) && actionType != 'seperate'" label="实收金额">
      <span>{{ Number(computedPostData.income_amount).toFixed(2) }}</span>
    </Form-item>
    <Form-item label="合同金额">
      <span>{{ computedPostData.amount }}</span>
    </Form-item>
    <Form-item label="支付方式" v-if="computedPostData.income_amount > 0">
      <pay-type-list
        v-model="computedPostData.new_pay_type"
        :amount="parseFloat(Number(computedPostData.income_amount).toFixed(2))"
        :showCardPay="from!=='销卡' && $route.params.userId !== '0' && !(actionType === 'edit' && computedPostData.order_name.indexOf('跨店') !== -1)"
        :showSQB="from!=='销卡'"
        :sqbOption="{ describe, serviceType: 2, isEqual: false }" />
    </Form-item>

    <!-- <CardPayType
      v-if="computedPostData.income_amount > 0"
      v-model="computedPostData.new_pay_type"
      :amount="computedPostData.income_amount"
      :from="from"
      :describe="describe"
      :pay_card_user="pay_card_user"
      :toUserId="toUserId"
      :phoneInfo="phoneInfo"
      :actionType="actionType"/> -->

    <BelongTo
      v-model="computedPostData.help_deal"
      :amount="computedPostData.amount"
      :cardTypeId="cardTypeId"
      :actionType="actionType"
      :hasStore="hasStore"
      :isStore="isStore" />

    <DiscountModal
      type="buy"
      :show.sync="showDiscount"
      :list="discountList"
      :selectIndex="selectDiscountIndex"
      :isPreOrder="isPreOrder"
      @radio="handleSelectDiscount"
    />
  </div>
</template>

<script>
import PayTypeList from 'components/form/PayTypeList.vue'
import BelongTo from './belongTo';

import DiscountModal from 'components/form/discountModal';
import { DISCOUNT_CARD_LIMIT, DISCOUNT_SCOPE_LIMIT } from 'store/constants';
import EventBus from 'components/EventBus.js';
import { mapGetters } from 'vuex'

let dealDiscountTimer = null;

export default {
  name: 'AmountItems',
  components: { BelongTo, DiscountModal, PayTypeList },
  props: {
    value: {
      type: Object
    },
    getDeposit: {
      type: Boolean,
      default: false
    },
    actionType: {
      type: String
    },
    toUserId: {
      type: [String, Number]
    },
    // eslint-disable-next-line vue/prop-name-casing
    pay_card_user: {
      type: Object
    },
    phoneInfo: {
      type: Object,
      default: () => {}
    },
    otherBusInfo: { // 如果传了则为跨店升卡
      type: Object,
      default: null
    },
    cardTypeId: {},
    cardId: {},
    selectedCard: {},
    isPreOrder: {
      type: Boolean,
      default: false
    },
    from: {
      type: String
    },
    hasStore: {
      type: Boolean,
      default: false
    },
    isStore: {
      type: Boolean,
      default: false
    },
    receiveId: { // 下单的折扣券Id
      type: [String, Number],
    },
  },
  data () {
    return {
      timeZero: false,
      describe: '',
      hasEditAmountAuth: false,
      perMonthSuspendPrice: 0, // 会员请假每月费用
      suspendRule: {}, // 请假付费规则
      suspendDays: 1,
      discountText: '未选择',
      userFrontMoneyList: [],
      selectDiscountIndex: '', // 选择的折扣数据索引
      selectedDiscount: '', // 选择的折扣数据对象
      showDiscount: false, // 显示全部折扣券弹窗
      discountList: [],
      deposit: 0 //定金金额
    };
  },
  async created () {
    // 会员维度请假时按照系统设置的收费规则来收费
    if (this.actionType == 'suspend') {
      this.suspendRule = await this.getSuspendRule();
      const rule = this.suspendRule.charging_rule;
      if ((this.suspendRule.suspend_number == 0 && this.suspendRule.suspend_limit == 1) || this.suspendRule.suspend_limit == 0) {
        this.timeZero = true;
        this.computedPostData.sale_amount = 0;
      } else {
        if (this.suspendRule.charging_type == 1) {
          this.computedPostData.sale_amount = rule.number_price;
        }
        if (this.suspendRule.charging_type == 2) {
          let sysMonth = Number(rule.time_price.month);
          let sysPrice = Number(rule.time_price.price);
          this.perMonthSuspendPrice = (sysPrice / sysMonth).toFixed(2);
          this.computedPostData.sale_amount = this.perMonthSuspendPrice * Math.ceil(this.suspendDays / 30) || 0;
        }
      }
    }
    if (this.actionType == 'add' && !this.isPreOrder) {
      this.getMsByUserId();
    }
    if (['add', 'change', 'reNew'].includes(this.actionType) && !this.otherBusInfo) {
      this.getDiscountList();
    }
    //编辑信息接口有定金相关返回  转卡不需要定金功能
    if (this.getDeposit && this.$route.params.userId != 0) {
      this.getFrontMoney();
    }
    if (this.actionType == 'edit') {
      this.getEditAmountAuth()
    }
    EventBus.$on("on-pay-des-change", value => {
      this.describe = value;
    })

  },
  mounted() {
    EventBus.$on("on-suspend-change", value => {
      this.suspendDays = value;
    })
    EventBus.$on("on-withdrap-discount", () => {
      this.selectDiscountIndex = ''
      this.selectedDiscount = null
    })
  },
  beforeDestroy () {
    this.computedPostData.sale_amount = 0;
    this.computedPostData.income_amount = '';
    EventBus.$off('on-phone-change')
    EventBus.$off('on-suspend-change')
    EventBus.$off('on-pay-des-change')
    EventBus.$off('on-withdrap-discount')
  },
  computed: {
    ...mapGetters(['canEditFields']),
    computedPostData: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    },
    frontMoneyAmount () { //定金总金额
      let frontIds = []
      let frontAmount = 0
      let userFrontMoneyList = this.userFrontMoneyList && this.userFrontMoneyList.length ? this.userFrontMoneyList : this.computedPostData.user_front_money_list
      Array.isArray(userFrontMoneyList) && userFrontMoneyList.forEach(item => {
        if (item.status == 1 || (this.computedPostData.fm_ids && this.computedPostData.fm_ids.indexOf(item.front_id + '') != -1)) {
          if (Array.isArray(this.computedPostData.fm_ids)) {
            let index = this.computedPostData.fm_ids.indexOf(item.front_id);
            if (index > -1) {
              this.computedPostData.fm_ids.splice(index, 1);
            }
          }
          item.status = '1'
          frontIds.push(item.front_id)
          frontAmount += +item.amount
        }
      });
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.computedPostData.front_ids = frontIds
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.userFrontMoneyList = userFrontMoneyList
      frontAmount = frontAmount ? frontAmount.toFixed(2) : 0
      return frontAmount;
    },

  },
  watch: {
    suspendDays (val) {
      if (this.actionType == 'suspend' && this.suspendRule.charging_type == 2) {
        this.computedPostData.sale_amount = this.perMonthSuspendPrice * Math.ceil(val / 30);
      }
    },
    'computedPostData.marketers_id' (val, oldVal) {
      if (val != oldVal) {
        this.initHelpDeal(val)
      }
    },
    'frontMoneyAmount' (val, oldVal) {
      const discount = this.selectedDiscount ? this.selectedDiscount.discount_amount : (this.computedPostData.selectedDiscount && this.computedPostData.selectedDiscount.discount_amount ) || 0
      if (val == 0 && oldVal > 0) {
        this.computedPostData.income_amount = this.computedPostData.amount = Math.max(this.computedPostData.sale_amount - discount, 0);
      } else {
        if (!val) return false;
        let inAmount = this.computedPostData.sale_amount - val - discount;
        this.computedPostData.income_amount = Math.max(inAmount, 0).toFixed(2);
        this.computedPostData.amount = Math.max(this.computedPostData.sale_amount - discount, val, 0).toFixed(2);
        if (this.cardTypeId == 6) {
          // 已和产品沟通，在勾选定金后给出定金不能大于售价的提示，其他卡种一样给出提示
          if (inAmount < 0) {
            this.$Message.error('定金不能大于售价!')
          }
        } else {
          this.computedPostData.sale_amount = inAmount >= 0 ? this.computedPostData.sale_amount : val; // 售价小于定金时, 收取全部定金
        }
      }
    },
    // 实收金额发生改变, 支付方式重置
    'computedPostData.income_amount' (val, oldVal) {
      // 编辑的时候需要锁定支付方式  不可在外部更改
      if(this.actionType === 'edit' || this.isPreOrder) {
        return
      }
      if (val !== 0 && +val != +oldVal) {
        if (this.computedPostData.new_pay_type.length == 1) {
          this.computedPostData.new_pay_type[0].amount = val;
        } else if (oldVal !== '' && oldVal !== undefined) {
          this.computedPostData.new_pay_type = [];
        }
      } else if (val === 0  && oldVal !== '') {
        this.computedPostData.new_pay_type = [];
      }
    },
    'computedPostData.amount' (val) {
      this.computedPostData.help_deal = this.computedPostData.help_deal.map(item => {
        return {
          ...item,
          ...{
            amount: (val * item.percent / 100).toFixed(2)
          }
        };
      });
    },
    // 售价改变时同步改变实收和合同金额, 并自动选中合适的折扣券
    'computedPostData.sale_amount' (val) {
      // 不同情况下的实收金额
      let income = val || 0;
      let amount = val || 0;

      if (val && isNaN(val)) return this.$Message.error('请输入数字');

      let { frontMoneyAmount, selectedDiscount } = this;
      if (!selectedDiscount) {
        selectedDiscount = this.computedPostData.selectedDiscount;
      }

      if (selectedDiscount) {
        income -= selectedDiscount.discount_amount;
        amount -= selectedDiscount.discount_amount;
      }
      if (frontMoneyAmount > 0) {
        if (!frontMoneyAmount) {
          return this.$Message.error('数据错误');
        }

        if (Number(val) > Number(frontMoneyAmount)) {
          income -= frontMoneyAmount;
        } else {
          income = 0;
        }
        amount = Math.max(frontMoneyAmount, amount);
      }
      this.computedPostData.income_amount = Math.max((+income).toFixed(2), 0);
      this.computedPostData.amount = Math.max((+amount).toFixed(2), 0);
      if (['add', 'change', 'reNew'].includes(this.actionType)) {
        // 如果用户没有选择不使用折扣券
        // 如果是套餐包，卧槽
        if (this.cardTypeId != 6) {
          this.dealDiscountList();
        }
      }
    },
    // 折扣券改变
    selectedDiscount (val) {
      if (this.actionType == 'edit') {
        return false
      }
      let { sale_amount } = this.computedPostData;
      sale_amount = sale_amount || 0;
      const front = this.frontMoneyAmount;
      if (val) {
        this.computedPostData.discount_id = val.receive_id || val.discount_id;
        const { discount_amount } = val;
        const income = sale_amount - discount_amount - front; // 实收金额
        const orderAmount = sale_amount - discount_amount; // 合同金额
        this.computedPostData.income_amount = Math.max((+income).toFixed(2), 0);
        this.computedPostData.amount = Math.max((+orderAmount).toFixed(2), front, 0);
      } else {
        const income = sale_amount - front; // 实收金额
        this.computedPostData.income_amount = Math.max(income, 0);
        this.computedPostData.amount = Math.max(sale_amount, 0);
        this.computedPostData.discount_id = '';
      }
    },
    cardId() {
      this.dealDiscountList();
    }
  },
  methods: {
    getEditAmountAuth() {
      this.$service.post('/Web/Member/edit_card_amount').then(res => {
        if (res.data.errorcode == 0) {
          this.hasEditAmountAuth = true
        } else {
          this.hasEditAmountAuth = false
        }
      })
    },
    getSuspendRule() {
      const user_id = this.$route.params.userId;
      const url = '/Web/Member/get_user_suspend_info';
      return this.$service.post(url, { user_id }).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            return res.data.data.info;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }
      })
    },
    /* 获取折扣券数据 */
    getDiscountList () {
      const { $route } = this
      const url = '/Web/Coupon/get_user_coupon';
      const params = {
        user_id: $route.params.userId,
        /*  如果需要跨店升卡，支持折扣券再放开测试， 目前是禁用跨店升卡使用折扣券
        user_id: actionType === 'change' && otherBusInfo ? otherBusInfo.user_id : $route.params.userId,
        ...actionType === 'change' && otherBusInfo ? {
          bus_id: otherBusInfo.bus_id
        } : {} */
      }
      this.$service
        .post(url, params)
        .then(res => {
          if (res.data.errorcode === 0) {
            const { data: list } = res.data;
            if (list.length) {
              list.forEach(item => {
                const { use_limit, limit_card } = item;
                item.limit_card_text = DISCOUNT_CARD_LIMIT[limit_card - 1]; // 卡种限制
                item.use_limit_text = (use_limit ? use_limit.split(',') : ['1']).map(v =>  DISCOUNT_SCOPE_LIMIT[v - 1]).join(); // 使用限制 1 购卡 2续卡 3升卡
              });
              this.discountList = list;

              this.dealDiscountList()
            } else {
              this.discountText = '无可用折扣券';
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
    },
    handleSelectDiscount (index) {
      this.selectDiscountIndex = index;

      if (index == -1) {
        this.selectedDiscount = '';
        this.discountText = '不使用';
      } else {
        this.selectedDiscount = this.discountList[index];
      }
    },
    clickAllDiscount () {
      this.showDiscount = true;
    },
    validDate(end_time, start_time) {
      return new Date(end_time + ' 00:00').getTime() + 24 * 3600 * 1000 < Date.now() ||
        new Date(start_time + ' 00:00').getTime() > Date.now();
    },
    validActionType(use_limit) {
      const limitArr = use_limit ? use_limit.split(',') : ['1']

      switch (this.actionType) {
        case 'add': //
          return !limitArr.includes('1');
        case 'reNew': //
          return !limitArr.includes('2');
        case 'change': //
          return !limitArr.includes('3');
        default:
          return true;
      }
    },
    validCardType(type, cardIds) {
      const { cardTypeId, cardId } = this;

      switch (+type) {
        case 1: // 除储值卡外所有卡种
          return cardTypeId == 3;
        case 2: // 除储值卡私教课外所有卡种
          return cardTypeId == 4 || cardTypeId == 3;
        case 3: // 限购私教课
          return cardTypeId != 4;
        case 4: // 限购特定卡种
          return !cardIds.split(',').includes(cardId);
        default:
          return false;
      }
    },
    sortDiscount() {
      this.discountList.sort((a, b) => {
        // 排序权重: 1.可用的排前面 2.(不可用里)未用的排前面 3.(可用和不可用)优惠金额大的排前面 4.(可用和不可用优惠金额相同)快到期的排前面
        return (
          a.disabled - b.disabled ||
          a.status - b.status ||
          b.discount_amount - a.discount_amount ||
          new Date(a.end_time) - new Date(b.end_time)
        );
      });
    },
    handleDefaultDiscount() {
      if(this.discountList.length) {
        /* 如果有使用折扣券 则选中使用的折扣券 '0'为未使用 */
        if(+this.receiveId) {
          const idx = this.discountList.findIndex(v => v.receive_id == this.receiveId)
          this.selectDiscountIndex = idx
          if(idx !== -1) {
            this.selectedDiscount = this.discountList[idx]
          } else { // 使用的折扣券被删除了
            this.selectedDiscount = {...this.computedPostData.selectedDiscount};
          }
        /* 核单时，如果手机开单未选择折扣券 '0'、''、0 */
        }else if(this.receiveId == 0 && this.receiveId == '') {
          this.selectDiscountIndex = -1;
          this.selectedDiscount = '';
          this.discountText = '未选择';
        /* 如果有可使用折扣券 receiveId == undefined */
        }else if(!this.discountList[0].disabled) {
          this.selectDiscountIndex = 0;
          this.selectedDiscount = this.discountList[0];
        /* 如果折扣券限制使用 */
        }else if(this.discountList[0].disabled) {
          this.selectDiscountIndex = -1;
          this.selectedDiscount = '';
          this.discountText = '未选择';
        }
      }else {
        this.selectDiscountIndex = '';
        this.discountText = '无可用折扣券';
        this.selectedDiscount = null;
      }
    },
    dealDiscountList() {
      clearTimeout(dealDiscountTimer)
      dealDiscountTimer = setTimeout(() => {
        const {
          computedPostData,
          discountList,
          validDate,
          validActionType,
          validCardType,
          sortDiscount,
          handleDefaultDiscount
        } = this;

        discountList.forEach(item => {
          const { end_time, start_time, status, limit_amount, use_limit, limit_card, card_species } = item;
          item.disabled =
            limit_amount - computedPostData.sale_amount > 0 ||
            (
              validDate(end_time, start_time) ||
              status != 1 ||
              validActionType(use_limit) ||
              validCardType(limit_card, card_species)
            )
        });

        sortDiscount()
        handleDefaultDiscount()
      }, 600);
    },
    //获取用户定金列表,
    getFrontMoney () {
      this.$service.post('/Web/FrontMoney/user_front_money_list', { user_id: this.$route.params.userId }).then(res => {
        let resData = res.data.data.list;
        if (res.data.errorcode == 0) {
          this.userFrontMoneyList = resData;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    getMsByUserId () {
      this.$service.post('/Web/member/get_ms_by_user_id', { user_id: this.$route.params.userId }).then(res => {
        let resData = res.data.data.info;
        if (res.data.errorcode != 0) {
          this.$Message.error(res.data.errormsg);
        } else {
          this.initHelpDeal(resData.marketers_id)
        }
      });
    },
    initHelpDeal (marketersId) {
      let helpDeal = [{
        marketers_id: marketersId || '',
        amount: this.computedPostData.amount,
        percent: 100
      }]
      this.computedPostData.marketers_id = marketersId || '';
      if (this.actionType != 'add') {
        helpDeal[0].marketers_name = this.computedPostData.marketer_name || ''
      }
      // this.computedPostData.help_deal = helpDeal;
      if (this.computedPostData.help_deal
        && Array.isArray(this.computedPostData.help_deal)
        && this.computedPostData.help_deal.length === 1) {
        this.computedPostData.help_deal = [{...this.computedPostData.help_deal[0], ...helpDeal[0]}]
      } else {
        this.computedPostData.help_deal = helpDeal;
      }
    },
    checkMultipleSelect () {
      for (let item of this.computedPostData.new_pay_type) {
        if (Number.isNaN(+item.amount)) {
          this.$Message.error('支付金额错误');
          return false;
        }
        if (item.amount > 0 && !item.pay_type) {
          this.$Message.error('请选择支付方式');
          return false;
        }
      }
      const payAmount = (this.computedPostData.new_pay_type.reduce((total, item) => total + Number(item.amount), 0)).toFixed(2);
      const percent = this.computedPostData.help_deal.reduce((total, item) => (Number(total) + Number(item.percent)).toFixed(2), 0);
      if (Number(this.computedPostData.income_amount) !== 0 && payAmount != Number(this.computedPostData.income_amount).toFixed(2)) {
        this.$Message.error('支付金额不等于实收金额!');
        return false;
      }
      for (let item of this.computedPostData.help_deal) {
        if (Number.isNaN(+item.percent)) {
          this.$Message.error('成单占比错误');
          return false;
        }
        if (item.marketers_id == 0 && item.percent && this.computedPostData.help_deal.length > 1) {
          this.$Message.error('请选择业绩归属');
          return false;
        }
      }
      if (percent != 100) {
        this.$Message.error('成单占比总和不等于100%');
        return false;
      }
      return true;
    },
    checkSaleAmount () {
      const { sale_amount } = this.computedPostData
      if (this.from === '销卡') return true;
      const { frontMoneyAmount } = this;
      const { discount_amount } = this.selectedDiscount || this.computedPostData.selectedDiscount || {};
      let amount = sale_amount;
      if (frontMoneyAmount > 0) {
        amount -= frontMoneyAmount;
      }
      if (discount_amount) {
        amount -= discount_amount;
      }
      const flag = amount >= 0;
      !flag && this.$Message.error({ content: '售价不能小于定金和折扣金额之和', duration: 3 });
      return flag;
    },
    checkSubmit () {
      return this.checkMultipleSelect() && this.checkSaleAmount();
    }
  }
};
</script>

<style lang="less" scoped>
.input-number-box {
  display: flex;
  align-items: center;
  .rule-alert {
    margin-bottom: 0;
    padding: 6px 14px 6px 14px;
  }
}
</style>
