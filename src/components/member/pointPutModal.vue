<template>
  <Modal
    title="积分调整"
    :value="show"
    @on-visible-change="handleClose">
    <Form
      ref="pointPutFormRef"
      :model="formData"
      :rules="formRules"
      :label-width="85">
      <FormItem prop="type">
        <RadioGroup v-model="formData.type" @on-change="handleChangeRadio">
          <Radio label="1">积分发放</Radio>
          <Radio label="2">积分扣除</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="!userId" label="会员" prop="user_id">
        <UserSearch
          v-model="formData.user_id"
          url="/Web/Point/SearchOperateUser"
          placeholder="姓名/电话"
          @on-change="handleChangeUser"
        />
      </FormItem>
      <FormItem label="当前积分">
        {{ currentPoint }}
      </FormItem>
      <FormItem :label="formData.type == '1' ? '本次发放' : '扣除数量'" prop="put_num">
        <InputNumber
          v-model="formData.put_num"
          style="width: 100%;"
          :max="formData.type == '2' && currentPoint !== 0 ? Math.floor(currentPoint) : 999999999"
          :min="1"
          :precision="0"
          :active-change="false"
        />
      </FormItem>
      <FormItem :label="formData.type == '1' ? '发放后' : '扣除后'">
        {{ result }}
      </FormItem>
      <FormItem
        v-if="formData.type == '1'"
        label="积分有效期"
        prop="end_time">
        <DatePicker
          v-model="formData.end_time"
          style="width:100%;"
          :editable="false"
          :options="datePickerOptions"
          type="date"
          format="yyyy-MM-dd"
          placeholder="有效期"
        />
      </FormItem>
      <FormItem label="原因" prop="remark">
        <Input
          v-model.trim="formData.remark"
          type="textarea"
          :maxlength="500"
          :autosize="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入" />
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleSummit">{{ typeName }}</Button>
      <Button @click="handleClose(false)">取消</Button>
      <!-- <Button @click="handleReset">重置</Button> -->
    </div>
  </Modal>
</template>

<script>
  import UserSearch from "components/user/userSearch";
  import { formatDate } from 'utils'

  const PUT_TYPES = {
    '1': '赠送',
    '2': '扣除'
  }

  export default {
    name: 'PointPutModal',
    components: {
      UserSearch
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      userId: {
         type: [Number, String],
         default: ''
      },
      isPointManagement: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        formData: {
          type: '1',
          user_id: '',
          put_num: null,
          end_time: new Date(new Date().setFullYear(new Date().getFullYear() + 1)), // 有效期默认一年,
          remark: ''
        },
        formRules: {
          user_id: [
            { required: true, message: '请搜索选择会员', trigger: 'change' }
          ],
          put_num: [
            {
              required: true,
              type: 'number',
              trigger: 'change',
              validator: (rule, val, cb) => {
                if (typeof val === 'number') {
                  +this.result < 0
                    ? cb(new Error('调整后剩余积分不能小于0'))
                    : cb()
                } else {
                  cb(new Error('请填写调整数量'))
                }
              }
            }
          ],
          end_time: [
            { required: true, type: 'date', message: '请选择有效期', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '请填写调整原因', trigger: 'blur' }
          ],
        },
        datePickerOptions: {
          disabledDate(date) {
            return date && date.valueOf() < Date.now() - 24 * 60 * 60 * 1000;
          }
        },
        currentPoint: 0,
      }
    },
    computed: {
      typeName() {
        return PUT_TYPES[this.formData.type]
      },
      result() {
        const { currentPoint, formData } = this;
        let result = 0;
        if (formData.put_num) {
          result = currentPoint + (formData.type == 1 ? 1 : -1) * formData.put_num;
        }
        return result.toFixed(0);
      },
    },

    watch: {
      show(val) {
        if (val && this.userId) {
          this.formData.user_id = this.userId;
          this.getUserPointNum()
        }
      },
    },
    methods: {
      getUserPointNum() {
        const params = {
          user_id: this.formData.user_id
        }

        this.$service.post('/Web/Point/getUserPoint', params).then(res => {
          const { errorcode, errormsg, data } = res.data;

          if (errorcode === 0) {
            this.currentPoint = data.point;

            const { formData, currentPoint } = this;
            if (formData.type == 2 && formData.put_num > currentPoint) {
              formData.put_num = currentPoint;
            }
          } else {
            this.$Message.error(errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      putPoint(url, params) {
        this.$service.post(url, params).then(res => {
          const { errorcode, errormsg } = res.data;
          this.$Message[errorcode === 0 ? 'success' : 'error'](errormsg);

          if (errorcode === 0) {
            this.$emit('updateData')
            this.$refs.pointPutFormRef.resetFields()
            this.handleClose()
          }
        })
        .catch(err => {
          console.error(err);
        });
      },

      handleChangeRadio() {
        this.formData.put_num = null;
      },

      handleChangeUser(userId) {
        if (userId) {
          this.getUserPointNum()
        } else {
          this.currentPoint = 0;
        }
      },

      handleSummit() {
        this.$refs.pointPutFormRef.validate((val) => {
          if (val) {
            const { type, ...rest } = this.formData;
            const params = {
              user_id: rest.user_id,
              num: rest.put_num,
              remark: rest.remark
            }
            // 积分管理有权限，所以和会员详情的调整接口分离了
            let url = this.isPointManagement ? 'PointDeduct' : 'MemberPointDeduct'; // 扣除

            if (type === '1') {
              url = this.isPointManagement ? 'PointGive' : 'MemberPointGive'; // 赠送
              params.end_time = formatDate(rest.end_time, 'yyyy-MM-dd')
            }
            this.putPoint('/Web/Point/' + url, params)
          }
        })
      },
      handleClose(val = false) {
        this.$emit('update:show', val)
        this.handleReset()
      },
      handleReset() {
        this.$refs.pointPutFormRef.resetFields()
      }
    },
  }
</script>

<style lang="less" scoped>

</style>
