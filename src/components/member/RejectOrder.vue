<template>
 <Modal v-model="showAdd" title="退单原因">
    <Form ref="formValidate" :model="postData" :rules="withdrawValidate" :label-width="80">
      <FormItem label="备注" prop="remark">
          <Input v-model="postData.remark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入..." />
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="doAddFollow">确定</Button>
      <Button @click="showAdd = false">取消</Button>
    </div>
</Modal>
</template>
<script>
export default {
  name: 'RejectOrder',
  data() {
    return {
      postData: {
        id: this.id,
        remark: ''
      },
      withdrawValidate: {
        remark: [{ required: true, message: '请添加退单理由！', trigger: 'blur' }]
      }
    }
  },
  props: {
    id: {
      type: [String, Number],
      required: true
    },
    value: {
      type: <PERSON>olean
    }
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    showAdd(val) {
      if (!val) {
        this.$refs.formValidate.resetFields()
      }
    }
  },
  created() {},
  methods: {
    doAddFollow() {
      this.$refs.formValidate.validate(val => {
        if (!val) return false
        return this.$service.post('/Web/preBilling/reject', this.postData).then(res => {
              if (res.data.errorcode == 0) {
                this.$Message.success(res.data.errormsg);
                setTimeout(() => {
                  this.$router.back();
                }, 1000);
              } else {this.$Message.error(res.data.errormsg);}
            })
          .catch(err => {
            this.$Message.error(err)
          })
      })
    }
  }
}
</script>

<style scoped>

</style>
