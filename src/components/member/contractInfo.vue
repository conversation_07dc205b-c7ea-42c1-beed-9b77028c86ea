<style lang="less" scoped>
  @border: 1px solid #dee0e2;
  .discount-list {
    width: 100%;
    padding: 0 16px;
    max-height: 60vh;
    overflow-y: scroll;
    .discount-card {
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;
      padding: 20px;
      margin-right: 0;
      border-radius: 6px;
      border: @border;
      border-top: 5px solid #ca2e53;
      box-shadow: 0 3px 5px rgba(74, 104, 164, 0.15);
      margin-bottom: 20px;
      position: relative;
      .tag {
        position: absolute;
        right: -2px;
        top: 10px;
        text-align: center;
        width: 80px;
        background-color: #e7e7e7;
        height: 26px;
        line-height: 26px;
        color: #898989;
      }
      .is-used {
        background-color: #4ed0c4;
        color: #fff;
      }
      .discount-wrap {
        color: #898989;
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: stretch;
        .top {
          display: flex;
          align-items: center;
        }
        .bottom {
          padding-top: 10px;
          margin-top: 10px;
          border-top: 1px dashed #eee;
          white-space: pre-wrap;
        }
      }
    }
    .no-choose {
      border-top: @border;
      line-height: 40px;
      font-size: 16px;
    }
    .disabled-card {
      border-top-color: #e7e7e7;
    }
    .discount-price {
      margin-right: 18px;
      padding-right: 18px;
      border-right: 1px dashed #eee;
      h4 {
        font-size: 24px;
        color: #ca2e53;
      }
    }
    .discount-info {
      h4 {
        font-size: 16px;
        color: #313131;
      }
    }
    .gray-text {
      h4 {
        color: #898989;
      }
    }
  }
</style>

<template>
  <Form ref="formData" :model="postData" :label-width="labelWidth?labelWidth:140">
    <div class="contract-info">
      <template v-if="isPackage||(actionType != 'edit' || contractData.is_order_info)">
        <AmountItems
          ref="amountItems"
          v-model="postData" :getDeposit="actionType != 'edit' && actionType != 'switch'"
          :actionType="actionType"
          :cardTypeId="selectedCard.card_type_id"
          :cardId="selectedCard.card_id"
          :selectedCard="selectedCard"
          :isPreOrder="isPreOrder"
          v-if="isPreOrder ? contractData.card_type_id : (actionType != 'seperate' || contractData.card_type_id != 2)"
          :toUserId="toUserId"
          :otherBusInfo="otherBusInfo"
          :hasStore="hasStore"
          :receiveId="contractData.receive_id"
          :isStore="isStore"/>
        <Form-item label="成交时间" prop="deal_time" :rules="{ required: true, message: '请选择日期'}">
            <Date-picker format="yyyy-MM-dd"
                         :value="postData.deal_time"
                         @on-change="handleDateChange"
                         :editable="false"
                         :disabled="!dealTimeAuth"
                         :options="disableDayAfter"
                         type="date"
                         placeholder="合同成交时间"></Date-picker>
        </Form-item>
        <Form-item v-if="actionType != 'reNew' && actionType != 'suspend' && actionType != 'switch' && actionType != 'seperate'" :label="dealLabel" prop="source_id">
          <Select v-model="postData.source_id" v-if="sourceList && sourceList.length>0" placeholder="请选择" clearable>
            <Option v-for="item in sourceList" :key="item.source_id" :value="item.source_id">{{item.source_name}}</Option>
          </Select>
        </Form-item>
        <Form-item key="remark" label="订单备注" prop="remark">
          <Input v-model="postData.remark" type="textarea" :autosize="{minRows: 3,maxRows: 5}" placeholder="请输入..." ></Input>
        </Form-item>
      </template>

      <!-- 无合同订单编辑购卡金额 -->
      <Form-item label="购卡金额" v-if="actionType == 'edit' && !contractData.is_order_info" prop="amount" :rules="{required: true, pattern: /^-?[0-9]+(.[0-9]{1,2})?$/, message: '购卡金额必须为数字且只能保留两位小数'}">
        <Input v-model="postData.amount" placeholder="请输入购卡金额"></Input>
      </Form-item>
      <Form-item key="edit_reason" v-if="actionType != 'add' && actionType != 'reNew' && actionType != 'suspend' && actionType != 'switch' && actionType != 'seperate'" label="修改原因" prop="edit_reason" :rules="{ required: true, validator: fuckValid}">
        <Input v-model="postData.edit_reason" type="textarea" :autosize="{minRows: 3,maxRows: 5}" placeholder="请填写会员卡修改的原因" />
      </Form-item>

      <CashCard v-if="(actionType === 'add' || (actionType === 'reNew' && renewType == 2)) && !isPreOrder" :data.sync="postData.cash_pledge" />
    </div>
  </Form>
</template>

<script>
  import { getSources } from 'src/service/getData';
  import { formatDate } from 'utils';
  import CashCard from 'src/views/member/components/CashCard.vue';
  import AmountItems from 'components/member/amountItems';
  import EventBus from 'components/EventBus.js'

  export default {
    name: 'contractInfo',
    components: { CashCard, AmountItems },
    props: {
      counterparts: {
        type: Boolean,
        default: false
      },
      seperateObj: {},
      contractData: {},
      actionType: {}, // actionType分为 add change edit reNew suspend switch,对应购卡 升卡  编辑 续卡 请假 转卡
      selectedCard: {},
      coachList: {},
      coachCur: {},
      labelWidth: {},
      privateTotalAmount: {},
      renewType: {},
      isPreOrder: {},
      userId: {},
      toUserId: {},
      otherBusInfo: {
        type: Object,
        default: null
      },
      hasStore: {
        type: Boolean,
        default: false
      },
      isStore: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        fuckValid: (f, s, c) => {
          if (this.postData.edit_reason) {
            c()
          } else {
            c('请填写修改原因！')
          }
        },
        disableDayAfter: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now();
          }
        },
        isPrivateSingleBuy: false,
        hasInitContractData: false,
        sourceList: '',
        sources: '',
        postData: {
          new_pay_type: [],
          help_deal: [{ marketers_id: '-1', amount: '', percent: 100 }],
          sale_amount: 0,
          income_amount: '', // 实收金额
          pay_order_ids: [], // 预收款id
          amount: '0', //合同金额,
          user_front_money_list: [], //定金列表
          fm_ids: [], //定金列表
          source_id: '', //获客来源ID,
          pay_type: '0', //支付类型，1微信2支付宝3现金4刷卡5其他6销卡, 0不选
          marketers_id: '-1', //销售人员ID,
          deal_time: formatDate(new Date(), 'yyyy-MM-dd'), // 合同成交时间
          remark: '', //订单备注
          edit_reason: '',
          cash_pledge: '', //押金信息
          amount_add: 0
        },
        dealTimeAuth: false, // 合同成交时间是否有修改权限
      };
    },
    computed: {
      isPackage() {
        return this.selectedCard && this.selectedCard.card_type_id === 6
      },
      dealLabel() {
        // selectedCard.card_type_id == 4 ? '私教成交方式' : selectedCard.card_type_id == 5 ? '泳教成交方式' : '会籍成交方式'
        const hpId = this.postData.help_deal[0].marketers_id
        if (!!hpId&&hpId.startsWith('c')||['-2','-3'].includes(hpId)) {
          let isSwim = this.postData.help_deal[0].is_swim
          if (typeof isSwim === 'undefined'&&hpId!=='c-2'&&hpId!=='c-3') {
            if (Array.isArray(this.coachList)) {
              const wr = this.coachList.find(co => 'c'+co.coach_id===hpId)
              if (wr) {
                isSwim = wr.is_swim
              }
            }
          }
          if (isSwim == 1 || hpId==='c-3') {
            return '泳教成交方式'
          } else if (isSwim == 0 || hpId === 'c-2') {
            return '私教成交方式'
          }
        } else {
          return '会籍成交方式'
        }
      }
    },
    async created() {
      if (this.actionType != 'reNew' && this.actionType != 'suspend' && this.actionType != 'switch') {
        await this.getSourcesList();
      }

      // 购续卡才校验权限
      if(['add', 'reNew'].includes(this.actionType)) {
        this.getDealTimeAuth()
      }else {
        this.dealTimeAuth = true;
      }
    },
    watch: {
      'postData.help_deal': function(val) {
        const hpId = val[0] && val[0].marketers_id
        if (!!hpId&&hpId.startsWith('c')||['-2','-3'].includes(hpId)) {
          this.sourceList = this.sources.type3;
        } else {
          this.sourceList = this.sources.type2;
        }
      },
      // 私教课通过单价计算的合同金额
      privateTotalAmount(val) {
        if (this.counterparts) {
          return ''
        }
        this.isPrivateSingleBuy = true;
        if (!this.isPreOrder) {
          if(this.actionType === 'seperate') {
            let money = (Number(this.seperateObj.total) || 0) - (this.contractData.card_last_value / this.contractData.last_num * this.seperateObj.times);
            money < 0? this.postData.sale_amount = 0 : this.postData.sale_amount = money.toFixed(2);
          } else if((this.actionType === 'edit' && this.hasInitContractData) || this.actionType !== 'edit'){
            this.postData.sale_amount = val;
          }
        }
      },
      //编辑卡赋值
       contractData(val) {
        const actType = this.actionType
        if (actType === 'edit' || this.isPreOrder) {
          const orderName = val.order_name
          const postData = {
            order_name: orderName,
            source_id: val.source_id,
            selectedDiscount: val.coupon_receive_info,
            front_money_amount: val.front_money_amount,
            user_front_money_list: val.user_front_money_list,
            fm_ids: val.fm_ids,
            income_amount: orderName === '转卡（入）' ? val.amount : val.income_amount,
            amount: val.amount,
            pay_type: val.pay_type,
            remark: val.remark,
            deal_time: val.deal_time_date,
            sale_amount: val.sale_amount || val.amount,
            new_pay_type: val.new_pay_type || []
          }
          if (actType === 'change') {
            postData.edit_reason = val.edit_reason
          }
          if (val.order_help_deal_list) {
            // 无业绩归属时默认设置默认金额
            if (val.order_help_deal_list.length === 1 && ['-1', 'c-2', 'c-3'].includes(val.order_help_deal_list[0].marketers_id)) {
              postData.help_deal = [{ marketers_id: '-1', amount: val.amount, percent: 100 }]
            } else {
              postData.help_deal = val.order_help_deal_list
                .map(item => {
                  return {
                    ...item,
                    ...{
                      marketers_id: item.marketer_category == 2 ? `c${item.marketers_id}` : item.marketers_id,
                      percent: item.proportion
                    }
                  }
                })
                .sort((a, b) => b.is_main - a.is_main)
            }
          }
          Object.assign(this.postData, postData)
        }
        this.hasInitContractData = true
      },
      coachCur(val) {
        if (val) {
          this.postData.marketers_id = 'c' + val;
          // this.belongList = [{ marketers_id: 'c' + val, amount: '', percent: 100 }];
        }
      },
      selectedCard(val) {
        if (!val) return false;
        const { card_type_id, current_price, single_price } = val;
        // here is bundle package
        if (card_type_id === 6) {
          let total = 0
          val.packageData?.forEach(item => {
            total = total + item.current_price
          })
          this.postData.sale_amount = Number(total.toFixed(2))
          return false
        }

        //私教课的来源不同
        // if (card_type_id == 4 || card_type_id == 5) {
        const hpId = this.postData.help_deal[0].marketers_id
        if (!!hpId&&hpId.startsWith('c')||['-2','-3'].includes(hpId)) {
          this.sourceList = this.sources.type3;
        } else {
          this.sourceList = this.sources.type2;
        }

        if (this.actionType !== 'edit' && card_type_id != 4 && card_type_id != 5 && !this.isPreOrder) {
          this.postData.sale_amount = current_price;
          this.postData.income_amount = current_price || 0;
        }
      }
    },
    methods: {
      handleDateChange(val) {
        this.postData.deal_time = val
      },
      getSourcesList() {
        getSources().then(res => {
          if (res.data.errorcode === 0) {
            let resData = res.data.data;
            this.sources = resData;
            // if (this.selectedCard.card_type_id == 4 || this.selectedCard.card_type_id == 5) {
            const hpId = this.postData.help_deal[0].marketers_id
            if (!!hpId&&hpId.startsWith('c')||['-2','-3'].includes(hpId)) {
              this.sourceList = resData.type3;
            } else {
              this.sourceList = resData.type2;
            }
            this.postData.source_id = this.contractData.source_id;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      // 获取成交时间可修改权限校验
      getDealTimeAuth() {
        this.$service.post('/Web/CardSaleRule/deal_time_update_auth').then(res => {
          if (res.data.errorcode === 0) {
            this.dealTimeAuth = true;
          } else if (res.data.errorcode === 40014) {
            this.dealTimeAuth = false;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
      },
      async handleSubmitClick() {
        if (this.actionType == 'seperate' && this.contractData.card_type_id == 2) {
          return {
            valid: true,
            postData: this.postData
          }
        }
        let valid;
        await this.$refs.formData.validate(val => {
          valid = val;
        });
        if (this.actionType != 'edit' || this.contractData.is_order_info) {
          valid = this.$refs.amountItems.checkSubmit();
        }
        let postData = { ...this.postData };
        postData.marketers_id = postData.help_deal[0] && postData.help_deal[0].marketers_id; // 第一个为业绩归属
        postData.marketer_category = postData.help_deal[0] && postData.help_deal[0].marketer_category
        // ? check marketer
        if (!postData.marketers_id) {
          this.$Message.error('业绩归属必填！')
          valid = false
        }
        EventBus.$emit('on-check-marketer')
        postData.marketers_name = postData.help_deal[0] && postData.help_deal[0].marketers_name; // 第一个为业绩归属
        if (this.actionType == 'add') {
          delete postData.edit_reason;
        }

        /* 处理精度问题，如果有误差，将最后的 ±0.0X 误差算到第一个业绩归属里面 */
        const amount = postData.amount;
        const sum = postData.help_deal.map(v => v.amount).reduce((previous, current) => +previous + +current, 0)
        if (amount !== sum) {
          postData.help_deal = postData.help_deal.map(v => ({ ...v }))
          postData.help_deal[0].amount = (+postData.help_deal[0].amount + +( amount - sum ).toFixed(2)).toFixed(2);
        }
        // fix 19935 此处将请求传参中金额为0的支付方式去掉了
        postData.new_pay_type = postData.new_pay_type.filter(v => v.amount && +v.amount > 0)
        // fix 20109 当编辑、核单等场景，如果实付income_amount=0（最后点击了定金！折扣券？），需要将支付方式置空传参
        if (+postData.income_amount === 0) {
          const needPayType = postData.new_pay_type.some(v => [8, 20, 21].includes(+v.pay_type))
          if (needPayType) {
            this.$Message.error('实收金额不能小于储值卡、收钱吧、杉德支付方式的已收金额之和')
            valid = false
          } else {
            postData.new_pay_type = []
          }
        }

        return { valid, postData };
      }
    }
  };
</script>
