<template>
  <Form ref="formData" :model="postData" :label-width="140">
    <div class="card-info">
      <Form-item label="会员卡类型" prop="cardType" v-show="actionType != 'seperate' || cardData.card_type_id != 2">
        <RadioGroup v-model="filterMan.cardType" type="button" @on-change="handleFilterCard" >
          <Radio label="-1" :disabled="selDis || filterMan.subCardType == 6">全部</Radio>
          <Radio label="0" :disabled="selDis || filterMan.subCardType == 6">单店卡</Radio>
          <Radio label="1" :disabled="selDis || filterMan.subCardType == 6">多店通用卡</Radio>
          <!-- <Radio label="2">体验卡</Radio> -->
        </RadioGroup>
      </Form-item>
      <Form-item label="" prop="subCardType">
        <RadioGroup v-model="filterMan.subCardType" type="button" @on-change="handleFilterCard">
          <template v-if="isNormal">
            <Radio label="-1" :disabled="selDis">全部</Radio>
            <Radio label="1" :disabled="selDis">期限卡</Radio>
            <Radio label="2" :disabled="selDis">次卡</Radio>
            <Radio label="3" v-if="filterMan.cardType != 2" :disabled="selDis">储值卡</Radio>
          </template>
          <Radio label="4" v-if="filterMan.cardType !=2 && isPt && (!belongBusId || (belongBusId && belongBusId === $store.state.busId))" :disabled="selDis" v-show="actionType != 'seperate' || cardData.card_type_id != 2">私教课</Radio>
          <Radio label="5" v-if="filterMan.cardType !=2 && isSwim && (!belongBusId || (belongBusId && belongBusId === $store.state.busId))" :disabled="selDis" v-show="actionType != 'seperate' || cardData.card_type_id != 2">泳教课</Radio>
          <Radio label="6" v-show="actionType != 'seperate'&&isTheSameStore" :disabled="(actionType!=='add'&&actionType!=='reNew')||selDis">套餐包</Radio>
        </RadioGroup>
      </Form-item>
      <FormItem
        v-show="isPackage&&isTheSameStore"
        label="套餐包名称"
        prop="packageId"
        :rules="{required: true, validator: packageValid, trigger: 'change'}">
        <Select
          v-model="packageId"
          class="dialog-item"
          @on-change="handlePackageChange"
          :disabled="actionType==='edit'||isPreOrder"
          filterable
        >
          <Option v-for="item in packageList" :key="item.id" :value="item.id">{{
            item.name
          }}</Option>
        </Select>
        <bundle-package-setting
          :disabled="actionType==='edit'||isPreOrder"
          :packageData="packageData"
          :actionType="actionType"
          :overRuleAuth="overRuleAuth"
          :addColumn="{index: 2, data: {title: '价格', key: 'current_price'}}"
          @updatePackage="handleUpdatePackage"
          :styleObject="{width:'80%',maxWidth:'800px'}"></bundle-package-setting>
      </FormItem>
      <template v-if="!isPackage">
      <Form-item :label="actionType == 'seperate'?'拆分出新卡种':(cardTypeId && (cardTypeId==4 || cardTypeId==5))||(filterMan.subCardType ==4 || filterMan.subCardType ==5)?'课程名称':'会员卡名称'" prop="card_id" :rules="{required: true,message: '请选择',trigger: 'change'}" v-show="actionType != 'seperate' || cardData.card_type_id != 2">
        <Select placeholder="请选择" v-if="filterCardList && filterCardList.length>0" v-model="postData.card_id" @on-change="cardChange" :disabled="selDis" filterable>
          <Option v-for="item in filterCardList" :key="item.card_id" :value="item.card_id">{{item.card_name}}</Option>

          <template v-if="filterMan.subCardType=='-1' && actionType !== 'change' && isTheSameStore">
            <Option v-for="item in packageList" :key="'package_'+item.id" :value="'package_'+item.id">[套餐包]--{{ item.name }}</Option>
          </template>

        </Select>
        <span v-if="filterCardList && filterCardList.length == 0">该场馆暂无该卡种会员卡</span>
        <span @click="handleEdit" v-if="filterCardList && selDis && actionType == 'edit' && cardData.is_order_info == 1 && cardData.is_pt_time_limit_card != 1 && !cardData.is_zhima_fit_pay_order" class="edit-bus">修改</span>
        <span v-if="filterCardList && !selDis && actionType == 'edit'" class="edit-bus" @click="cancelChangeCard">取消修改</span>
        <div style="color:red" v-if="curCard.is_pt_time_limit_card == 1">课程每天允许上 {{curCard.per_day_pt_class_num}} 节</div>
      </Form-item>
      <Form-item label="教练" prop="coach_id" :rules="{required: true,message: '请选择教练'}" v-if="curCard.card_type_id && (curCard.card_type_id == 4 || curCard.card_type_id == 5)">
        <Select placeholder="请选择" v-if="coachList && coachList.length>0" v-model="postData.coach_id" @on-change="coachChange" filterable multiple>
          <Option v-for="item in coachList" v-show="item.is_swim ? curCard.card_type_id == 5 : curCard.card_type_id == 4" :key="item.coach_id" :value="item.coach_id">{{item.coach_name}}</Option>
        </Select>
      </Form-item>
      <Form-item label="实体卡号" prop="card_sn" key="card_sn">
        <Input v-model="postData.card_sn" placeholder="请输入实体卡号"/>
      </Form-item>
      <Form-item label="可用场馆" prop="support_bus" v-if="curCard.universal_card == 1 && postData.support_bus" >
        <div class="form-other">
          <span style="margin-right:15px;" v-for="item in merchantsBusList" :key="item.bus_id" v-if="postData.support_bus.indexOf(item.bus_id)>=0">{{item.bus_name}}</span>
        </div>
      </Form-item>

      <!--1期限卡,2次卡,3储值卡,4私教卡,5泳教 -->
      <template v-if="cardTypeValid">
        <template v-if="actionType == 'edit' || actionType == 'seperate'">
          <Form-item v-if="selDis" :label="'累计' + cardTypeValid.allLabel">
            {{curCard.card_type_id == 1 || curCard.is_pt_time_limit_card== 1 ? postData.all_days : curCard.card_type_id == 2 || (curCard.card_type_id == 4 || curCard.card_type_id == 5) ? postData.all_num : postData.total}}
          </Form-item>
          <Form-item :label="(actionType == 'seperate'?'拆分次数':(cardData.is_order_info != 1 || !selDis ? '购买' : '最近合同购买') + cardTypeValid.unitLabel)">
            <Input v-model="postData.order_all_num" :disabled="actionType == 'edit' && curCard.is_pt_time_limit_card== 1"  @on-blur="handleBlur('order_all_num', $event)"/>
          </Form-item>
          <Form-item
            v-if="actionType != 'seperate'"
            :label="(cardData.is_order_info != 1  || !selDis ? '赠送' : '最近合同赠送') + cardTypeValid.unitLabel"
          >
            <Input v-model="postData.order_gift_num" @on-blur="handleBlur('order_gift_num', $event)"/>
          </Form-item>
        </template>
        <template v-else>
          <!-- 购买X数 -->
          <Form-item prop="purchase_volume" :required="cardTypeValid.itemRules[0].required" :label="cardTypeValid.allLabel" :rules="cardTypeValid.itemRules">
            <div class="input-number-box">
              <Input v-model="postData.purchase_volume" :disabled="curCard.is_pt_time_limit_card== 1 ||(!canEditFields && (actionType == 'add' || actionType == 'reNew') && curCard.card_type_id != 4 && curCard.card_type_id != 5)" :placeholder="cardTypeValid.allPla" />
              <Alert class="rule-alert" v-if="open_rule.buy_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.buy_max }}</Alert>
            </div>
          </Form-item>
          <!-- 赠送XX -->
          <Form-item prop="gift_volume" :label="cardTypeValid.giftLabel" :rules="cardTypeValid.giftRules">
            <div class="input-number-box">
              <Input v-model="postData.gift_volume" :disabled="!canEditFields && (actionType == 'add' || actionType == 'reNew')" :placeholder="cardTypeValid.giftPla" />
              <Alert class="rule-alert" v-if="open_rule.gift_max" type="warning" style="margin-left: 20px;">上限{{ open_rule.gift_max }}</Alert>
            </div>
          </Form-item>
        </template>
      </template>
      <Form-item label="有效期（天）" prop="end_time" v-if="curCard.card_type_id != undefined && curCard.card_type_id != 1 && actionType != 'seperate' && curCard.is_pt_time_limit_card != 1" :rules="{type: 'string',pattern: /^[1-9]\d*$/,message: '天数必须为正整数' }">
        <Input v-model="postData.end_time" placeholder="不填写表示无时间限制" />
      </Form-item>
      </template>
      <!-- update_active_time: 1 可以编辑新购买的期限卡到未激活状态 -->
      <Form-item label="开卡时间" v-if="(actionType != 'edit' || (actionType == 'edit' && !selDis && this.cardData.update_active_time == 1)) && actionType != 'seperate'">
        <Form-item prop="active_type">
          <Radio-group v-model="postData.active_type" @on-change="onActiveTypeChange">
            <Radio :label="1" :disabled="!activeTimeAuth">立即开卡</Radio>
            <Radio :label="2" :disabled="!activeTimeAuth">自定义开卡</Radio>
          </Radio-group>
        </Form-item>
        <Form-item>
          <Date-picker :editable="false" v-model="activeTime" v-if="postData.active_type == 2" :disabled="!activeTimeAuth" type="date" placeholder="开卡时间（不填即随时可以开卡）"></Date-picker>
        </Form-item>
      </Form-item>
      <Form-item v-if="actionType == 'edit' && selDis && numAuthor" :label="lastText" prop="last_volume" :rules="{required: true, type: 'string', pattern: /^\d+$/, message: '剩余量必须为正整数或0' }">
        <Input v-model="postData.last_volume" placeholder=""/>
      </Form-item>
      <Form-item v-if="!isPackage&&(actionType == 'edit' && !numAuthor)" :label="lastText">
        <span>{{postData.last_volume}}</span>
      </Form-item>
      <Form-item label="到期时间" v-show="!isPackage">
        {{ actionType == 'seperate'? cardData.end_time_date : getEndDay }}
      </Form-item>
      <Form-item v-if="actionType == 'seperate'" >
        <Card class="bonus-card bonus-card-table ivu-input-wrapper" dis-hover>
            <Table :columns="cardDateInfo" :data="card"></Table>
        </Card>
      </Form-item>
    </div>
  </Form>
</template>
<script>
import { mapGetters } from 'vuex'
import { getcoachsInfo, getSupportBus } from 'src/service/getData'
import { addDays, formatDate } from 'utils'
import { cardTypeValidArr } from './cardInfoValidRule.js'
import EventBus from 'components/EventBus.js'

import BundlePackageSetting from '@/components/bundlePackage/BundlePackageSetting'
import eventBus from '@/utils/eventBus.js'

export default {
  name: 'cardInfo',
  components: {
    BundlePackageSetting
  },
  //actionType分为 add change edit
  props: {
    cardData: {
      default: ''
    },
    cardSn: { default: '' },
    actionType: {},
    cardTypeId: {},
    //跨店购卡的id
    belongBusId: {},
    // 续卡的时候私教只能续私教 普通只能续普通
    isNormal: {
      type: Boolean,
      default: true
    },
    isPt: {
      type: Boolean,
      default: true
    },
    isSwim: {
      type: Boolean,
      default: true
    },
    isPreOrder: {
      type: Boolean,
      default: false
    },
    overRuleAuth: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // disableDayBefore: {
      //   disabledDate(date) {
      //     return date && date.valueOf() < Date.now() - 86400000;
      //   }
      // },
      packageValid: (rule, value, callback) => {
        if (this.filterMan.subCardType == 6 && this.packageId === '') {
          callback('请选择套餐包!')
        } else {
          callback()
        }
      },
      cardDateInfo: [
        { title: '卡种', key: 'type' },
        { title: '拆分前剩余', key: 'leftBefore' },
        { title: '拆分后剩余', key: 'leftAfter' },
        { title: '拆分后到期时间', key: 'expireTimeAfter' }
      ],
      card: [
        {
          type: '-',
          leftBefore: '-',
          leftAfter: '-',
          expireTimeAfter: '-'
        },
        {
          type: '-',
          leftBefore: '-',
          leftAfter: '-',
          expireTimeAfter: '-'
        }
      ],
      initLastVol: 0,
      activeTime: '',
      editSupportBus: false, //是否为编辑场馆状态
      isCardDataInit: false, //是否已经触发过卡数据初始化
      curCard: '',
      selDis: this.actionType == 'edit' || this.isPreOrder ? true : false,
      coachList: [], //教练列表
      //期限卡,次卡,储值卡,私教卡
      cardTypeValidArr: cardTypeValidArr,
      cardTypeValid:
        this.actionType == 'edit' || this.isPreOrder ? '' : cardTypeValidArr[0],
      editSupportBusList: '',
      numAuthor: false,
      postData: {
        is_pt_time_limit_card: 0, //是否私教包月卡
        user_id: '', //会员id,
        card_sn: '', //实体卡号
        card_type_id: '', //会员卡类型id(1年卡,2次卡,3储值卡,4私教卡),
        card_id: '', //会员卡id,
        coach_id: '', //教练id,
        coach_name: '', //教练名称,
        charge_type: 1, //收费方式(1按次数,2按时间),
        purchase_volume: '', //购买量，期卡为时间天，次卡为次数，储值卡为金额,
        gift_volume: '', //赠送量，期卡为时间天，次卡为次数，储值卡为金额,
        order_all_num: 0, //赠送量，期卡为时间天，次卡为次数，储值卡为金额,
        order_gift_num: 0, //赠送量，期卡为时间天，次卡为次数，储值卡为金额,
        active_type: 1, //激活类型时间,1立即激活，2到店激活
        active_time: formatDate(new Date(), 'yyyy-MM-dd'), //激活时间
        end_time: '', //有效时间,
        experience_card: 0, //是否是体验卡，1是0否
        universal_card: '', //是否是通卡，1是0否
        support_bus: '', //支持场馆数组 [1,2,3]
        last_volume: '',
        card: [],
        package_id: '',
        sale_amount: ''
      },
      filterMan: {
        cardType: '-1',
        subCardType: '-1'
      },
      filterCardList: [],
      // bundle package
      packageId: '',
      packageList: [],
      packageData: [],
      activeTimeAuth: false, // 是否可修改开卡时间
      once:true,
    }
  },
  computed: {
    ...mapGetters(['addCardList', 'merchantsBusList', 'canEditFields']),
    isPackage() {
      return this.filterMan && this.filterMan.subCardType === '6'
    },
    isTheSameStore() {
      if (this.actionType === 'add') {
        return this.$store.state.busId === this.belongBusId
      } else {
        return true
      }
    },
    // 到期时间计算
    getEndDay() {
      let curCardData = this.cardData
      let curFormData = this.postData
      //卡暂停状态
      if (curCardData && curCardData.end_time == '2000000000') {
        return '未知'
      }
      let activeDate = curFormData.active_time
      //立即激活的初始状态以及永久卡变有限时间的卡
      //此时的activeDate是今天
      if (curFormData.active_type == 1 && curFormData.active_time == '') {
        activeDate = formatDate(new Date(), 'yyyy-MM-dd')
      }
      let days =
        parseInt(curFormData.purchase_volume || 0) +
        parseInt(curFormData.gift_volume || 0)
      //不是期限卡与私教包月卡时
      if (
        curFormData.card_type_id != '' &&
        curFormData.card_type_id != 1 &&
        curFormData.is_pt_time_limit_card != 1
      ) {
        if (curFormData.end_time == '') return '永久有效'
        days = parseInt(curFormData.end_time)
      }
      //编辑
      const isTimeOut =
        curCardData.end_time < new Date().getTime() / 1000 && !this.isPreOrder && this.selDis
      if (this.actionType == 'edit') {
        if (this.postData.active_type === 1) {
          activeDate = formatDate(new Date(), 'yyyy-MM-dd')
        }
        if (
          curCardData.end_time &&
          curCardData.end_time == '2000000001' &&
          this.selDis
        ) {
          activeDate = formatDate(new Date(), 'yyyy-MM-dd')
        } else if (isTimeOut && this.selDis) {
          //已过期
          activeDate = curCardData.end_time_date
        }
        if (
          this.curCard.card_type_id != 1 &&
          this.curCard.is_pt_time_limit_card != 1
        ) {
          //不是期限卡
          days = parseInt(curFormData.end_time)
        } else {
          days = this.getLastNum
        }
      }
      //非过期卡 总天数一天  今天开始的话 今天就是到期时间了
      if (!isTimeOut || this.actionType == 'reNew') { // fix #11365 过期卡续新卡时，也修改为今天开始，就今天到期
        days = days - 1 > 0 ? days - 1 : 0
      }
      return addDays(activeDate, days).indexOf('NaN') != -1
        ? '暂无'
        : addDays(activeDate, days)
    },
    // 剩余量相关计算都在这里
    getLastNum() {
      let num = 0
      let change = 0
      if (this.actionType == 'edit') {
        if (
          !this.selDis &&
          this.postData.active_type == 2 &&
          (this.postData.card_type_id == 1 ||
            this.postData.is_pt_time_limit_card == 1) &&
          this.cardData.card_id == this.postData.card_id
        ) {
          let initLastOrderNum = +this.postData.purchase_volume
          let initLastGiftNum = 0
          let buyNUm = +this.postData.order_all_num || 0
          let giftNUm = +this.postData.order_gift_num || 0
          let allNum = buyNUm + giftNUm
          change = allNum - initLastOrderNum - initLastGiftNum
          num =
            +this.postData.order_all_num + Number(this.postData.order_gift_num)
        } else {
          let initLastOrderNum = +this.cardData.last_order_purchase_volume
          let initLastGiftNum = +this.cardData.last_order_gift_volume
          let buyNUm = +this.postData.order_all_num || 0
          let giftNUm = +this.postData.order_gift_num || 0
          let allNum = buyNUm + giftNUm
          if (
            this.cardData &&
            (this.cardData.last_day == '暂停' ||
              this.cardData.end_time == '2000000000')
          ) {
            return '暂停'
          }
          num = Number(
            this.cardData.last_num == '' ? 0 : this.cardData.last_num
          )
          if (
            this.cardData.card_type_id == 1 ||
            this.cardData.is_pt_time_limit_card == 1
          ) {
            num = Number(
              this.cardData.last_day == '' ? 0 : this.cardData.last_day
            )
          }
          if (this.cardData.card_id == this.postData.card_id || this.selDis) {
            change = allNum - initLastOrderNum - initLastGiftNum
            num = num + change
          } else {
            num = allNum
            change = allNum || 0
          }
        }
      }
      return num
    },
    lastText() {
      let text = '剩余天数'
      if (this.postData.card_type_id == 2) {
        text = '剩余次数'
      } else if (this.postData.card_type_id == 3) {
        text = '剩余金额'
      } else if (
        (this.postData.card_type_id == 4 &&
          this.postData.is_pt_time_limit_card != 1) ||
        this.postData.card_type_id == 5
      ) {
        text = '剩余节数'
      }
      return text
    },
    // 控制显示上限提示及文本
    open_rule() {
      const { actionType, curCard: card, canEditFields } = this;
      if(canEditFields && ['add', 'reNew'].includes(actionType) && card && card.is_open_rule === '1' && card.get_range) {
        const { buy_max, gift_max, sale_range } = card;
        return {
          buy_max,
          gift_max,
          sale_range,
        }
      }else {
        return {};
      }
    },
  },
  watch: {
    //跨店购卡
    belongBusId(val) {
      this.packageData = []
      this.packageId = ''
      this.changeCardList(val)
    },
    getLastNum(val) {
      if (this.actionType == 'edit' || this.isPreOrder) {
        this.postData.last_volume =
          this.lastText === '剩余金额' ? val.toFixed(2) : val
      }
    },
    'postData.active_type'(val) {
      let date = formatDate(new Date(), 'yyyy-MM-dd')
      if (val == 2) {
        date = this.activeTime ? formatDate(this.activeTime, 'yyyy-MM-dd') : ''
      }
      this.postData.active_time = date
    },
    //延迟开卡的开卡时间
    activeTime(val) {
      this.postData.active_time = val ? formatDate(val, 'yyyy-MM-dd') : ''
    },
    'postData.purchase_volume'(val) {
      if (
        this.curCard.is_pt_time_limit_card != 1 &&
        this.curCard.single_price &&
        (this.curCard.card_type_id == 4 || this.curCard.card_type_id == 5) &&
        !this.isPreOrder &&
        this.actionType !== 'seperate'
      ) {
        this.privateNumChange(val)
      }
      if (this.isPreOrder && !this.selDis) {
        this.postData.order_all_num = val || 0
        // this.postData.order_last_num = +this.postData.order_all_num + Number(this.postData.order_gift_num || 0);
      }
      this.setPrivateCardEndTime()
    },
    'postData.order_all_num'(val) {
      if (
        this.curCard.is_pt_time_limit_card != 1 &&
        this.curCard.single_price &&
        (this.curCard.card_type_id == 4 || this.curCard.card_type_id == 5) &&
        !this.isPreOrder &&
        this.actionType === 'edit'
      ) {
        this.privateNumChange(val)
      }
      if (this.actionType == 'seperate') {
        this.cardData.card_type_id == 2
          ? (this.card[0].type = this.card[1].type = this.cardData.card_name)
          : (this.card[0].type = this.cardData.card_name)
        this.cardData.card_type_id == 4 || this.cardData.card_type_id == 5
          ? (this.card[1].type = this.curCard.card_name)
          : this.cardData.card_name
        this.card[0].leftBefore = this.cardData.last_num
        this.card[0].leftAfter = this.cardData.last_num - val
        this.card[1].leftAfter = val
        this.card[0].expireTimeAfter = this.card[1].expireTimeAfter = this.cardData.end_time_date
        this.privateNumChange(val)
        let total = this.postData.order_all_num * this.curCard.single_price
        let times = val
        let price = this.curCard.single_price
        let name = this.curCard.card_name
        this.$emit('forSeperateCard', { total, times, price, name })
      }
    },
    'postData.gift_volume'(val) {
      if (this.isPreOrder && !this.selDis) {
        this.postData.order_gift_num = val || 0
        // this.postData.order_last_num =
        // Number(this.postData.order_all_num || 0) + Number(this.postData.order_gift_num || 0);
      }
      this.setPrivateCardEndTime()
    },
    //编辑卡赋值
    cardData: {
      handler(val) {
        if(this.actionType === 'reNew') {
          this.setReNewActiveTime()
        }
        if ((this.actionType == 'edit' || this.isPreOrder) && typeof val === 'object' && (val.card_id || val.package_id)) {
          if (val.support_bus_list && val.support_bus_list.length > 0) {
            this.postData.support_bus = val.support_bus_list
            this.editSupportBusList = val.support_bus_list
            if (val.bus_id != val.buy_bus_id) {
              this.$Modal.info({
                title: '提示',
                content: '通卡只能在购卡场馆进行编辑卡操作',
                onOk: () => {
                  this.$router.back()
                }
              })
            }
          }
          if (this.isPreOrder) {
            this.activeTime = val.active_time || ''
          }
          if (val.package_id) {
            this.setPackageInfo(val)
          } else {
            this.fildDataAndCardChange(val.card_id)
          }
          this.postData.user_id = val.user_id
          this.postData.active_time = val.active_time
            ? formatDate(new Date(val.active_time * 1000), 'yyyy-MM-dd')
            : ''
          //2000000001永久有效
          this.postData.end_time =
            val.end_time == '2000000001' ? '' : val.last_day.toString()
          this.postData.charge_type = val.charge_type
          this.postData.card_sn = val.card_sn

          if (val.purchase_volume === null) {
            val.purchase_volume = ''
          }

          this.postData.is_pt_time_limit_card = val.is_pt_time_limit_card
          this.postData.purchase_volume = val.purchase_volume.toString() || ''
          this.postData.gift_volume = val.gift_volume.toString() || ''
          this.postData.coach_id = val.coach_id
          this.postData.coach_name = val.coach_name
          this.postData.card_type_id = val.card_type_id
          this.postData.active_type = Number(val.active_type)
          // 编辑订单最近合同购买量/赠送量
          this.postData.order_all_num = +val.last_order_purchase_volume // 最近合同购买量
          this.postData.order_gift_num = +val.last_order_gift_volume // 最近合同赠送量
          this.postData.order_last_num = val.order_last_num

          this.postData.all_days = val.all_days
          this.postData.all_num = val.all_num
          this.postData.total = val.total

          //cardorder_info_id只有edit时需要
          this.postData.cardorder_info_id = val.cardorder_info_id
          this.$nextTick(() => {
            this.isCardDataInit = true
          })
        }
      },
      immediate: true
    },
  },
  created() {
    // !this.addCardList && this.$store.dispatch('getAddCardList');
    // !this.merchantsBusList && this.$store.dispatch('getMerchantsBusList');
    // 当以后卡片操作和场馆操作都写到新版后不用每次请求
    this.cardSn && (this.postData.card_sn = this.cardSn)
    this.changeCardList(this.$store.state.busId)
    this.$store.dispatch('getMerchantsBusList')
    this.getCoachList()

    if (['edit', 'seperate', 'change'].includes(this.actionType)) {
      const unwatch = this.$watch('cardData', function() {
        this.getPackageList(this.cardData.package_bus_id)
        unwatch()
      });
    } else {
      this.getPackageList()
    }
    
    // 购续卡才校验权限
    if(['add', 'reNew'].includes(this.actionType)) {
      this.getActiveTimeAuth()
    }else {
      this.activeTimeAuth = true;
    }
  },
  methods: {
    // 续新卡 初始开卡时间设置
    setReNewActiveTime() {
      this.postData.active_type = 2
      const time = this.cardData.end_time
      // 是否已经过期
      const isTimeOut = time < new Date().getTime() / 1000
      // 永久或者已过期  开卡时间默认为今天
      if (time == '2000000001' || isTimeOut) {
        this.activeTime = new Date()
      } else {
        this.activeTime = new Date(time * 1000 + 24 * 3600 * 1000)
      }
    },
    setPackageInfo(info) {
      this.selectedCard = { card_type_id: 6 }
      this.filterMan.subCardType = '6'
      this.packageId = info.package_id
      this.handleUpdatePackage(this.setStringToNumber(info.card_detail))
    },
    getPackageList(busId) {
      return this.$service
        .post('/Web/package/getPackageList', {
          // bus_id: this.belongBusId,
          bus_id: busId || this.$store.state.busId,
          status: 1
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.packageList = res.data.data.list
          }
        })
    },
    // 获取开卡时间可修改权限校验
    getActiveTimeAuth() {
      this.$service.post('/Web/CardSaleRule/create_time_update_auth').then(res => {
        if (res.data.errorcode === 0) {
          this.activeTimeAuth = true;
        } else if (res.data.errorcode === 40014) {
          this.activeTimeAuth = false;
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    setStringToNumber(arr) {
      const newArr = []
      if (Array.isArray(arr)) {
        arr.forEach(item => {
          newArr.push({
            ...item,
            all_days: Number(item.all_days),
            all_num: Number(item.all_num),
            current_price: Number(item.amount),
            single_price: Number(item.single_price || 0),
            single_days: Number(item.single_days || 0),
            card_type_id: Number(item.card_type_id),
            total: Number(item.total)
          })
        })
      }
      return newArr
    },
    handlePackageChange() {
      this.postData.card_type_id = 6
      EventBus.$emit('on-withdrap-discount')
      return this.$service
        .post('/Web/package/getCardPackageDetail', {
          id: this.packageId
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.handleUpdatePackage(res.data.data.card_detail)
          }
        })
    },
    handleUpdatePackage(packageData) {
      this.packageData = packageData
      this.postData.card_type_id = 6
      this.$emit('update:selectedCard', { card_type_id: 6, packageData })
      this.postData.package_id = this.packageId
      this.postData.card = []
      this.packageData.forEach(item => {
        if (!item._id) {
          item._id = Math.random()
        }
        let purchase_volume = ''
        if (item.card_type_id == 1 || item.is_pt_time_limit_card == 1) {
          purchase_volume = item.all_days
        } else if (item.card_type_id == 2) {
          purchase_volume = item.all_num
        } else if (item.card_type_id == 3) {
          purchase_volume = item.total
        } else {
          purchase_volume = item.all_num
        }
        this.postData.card.push({
          bus_id: item.bus_id,
          bus_name: item.bus_name,
          card_id: item.card_id,
          purchase_volume,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
          end_time: item.all_days,
          amount: item.current_price
        })
      })
    },
    privateNumChange(val) {
      let total = val * this.curCard.single_price
      this.$emit('onPrivateNumChange', total.toFixed(2))
    },
    changeCardList(busId) {
      this.$store.dispatch('getAddCardList', {belongBusId: busId}).then(res => {
        if (res.data.errorcode == 0) {
          //卡列表返回之前如果 card_id已经变化 无法检测到下拉change事件 主动触发
          if (this.actionType != 'edit' && !this.isPreOrder) {
            this.filterCardList = this.addCardList.filter(item => {
              return item.sale_status === '1'
            })
          }

          if (this.isPt && !this.isNormal) {
            this.filterMan.subCardType = '4'
            this.handleFilterCard()
          } else if (this.isSwim && !this.isNormal) {
            this.filterMan.subCardType = '5'
            this.handleFilterCard()
          } else if (!this.isPt && !this.isSwim && this.isNormal) {
            this.handleFilterCard()
          }
          this.fildDataAndCardChange(isNaN(+this.cardData.card_id) ? this.cardData.card_id : +this.cardData.card_id)
        }
      })
    },
    // 编辑修改卡类型, 期限卡修改开卡时间, 剩余天数恢复到初始值
    onActiveTypeChange(radio) {
      if (this.actionType == 'edit' && (this.postData.card_type_id == 1 || this.postData.is_pt_time_limit_card == 1)) {
        if (radio == 2) {
          this.postData.order_all_num = this.postData.purchase_volume
          this.postData.order_gift_num = 0
          this.$nextTick(() => {
            this.postData.last_volume = this.postData.purchase_volume
          })
        } else {
          this.postData.order_all_num = +this.cardData
            .last_order_purchase_volume
          this.postData.order_gift_num = +this.cardData.last_order_gift_volume
        }
      }
    },
    fildDataAndCardChange(cardId) {
      if (cardId && (this.actionType == 'edit' || this.isPreOrder)) {
        this.filterCardList =
          this.addCardList &&
          this.addCardList.length > 0 &&
          this.addCardList.filter(item => {
            if (cardId == item.card_id) {
              return true
            } else {
              return item.sale_status === '1'
            }
          })
        this.filterCardList &&
          this.filterCardList.length > 0 &&
          this.cardChange(cardId)
      }
    },
    handleBlur(key, ev) {
      if (this.actionType != 'seperate') {
        const { value } = ev.target
        if (!value) {
          this.postData[key] = 0
        }
      } else {
        // this.postData.purchase_volume = this.postData[key];
        if (!/^[1-9]\d*$/.test(this.postData.order_all_num)) {
          this.$Message.error('拆分次数需要为正整数！')
        } else if (
          Number(this.postData.order_all_num) >= Number(this.cardData.last_num)
        ) {
          this.$Message.error({
            content: '拆分次数必须小于原卡剩余次数！',
            duration: 2.5
          })
        }
      }
    },
    //次卡编辑状态是否可直接编辑剩余次数
    getEditCardNumAuthor() {
      this.$service.get('/Web/Member/editCardNum').then(res => {
        if (res.data.errorcode === 0) {
          this.numAuthor = true
        }
      })
    },
    //获取当前卡片支持的场馆列表
    getSupportBus(cardId) {
      getSupportBus(cardId).then(res => {
        if (res.data.errorcode == 0) {
          let supBus = res.data.data.support_bus
          this.postData.support_bus = supBus
          this.editSupportBusList = supBus
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    handleEdit() {
      this.$service.get('/Web/Member/checkAuthCardType').then(res => {
        if (res.data.errorcode === 0) {
          this.$Modal.confirm({
            title: '提示',
            content: '此操作将可能会变换会员卡类型,是否继续?',
            onOk: () => {
              this.selDisChange()
            },
            onCancel() {}
          })
        } else {
          this.$Message.error('没有权限');
        }
      });

    },
    //编辑状态改变 将激活类型和时间重置
    selDisChange() {
      this.selDis = false
      this.postData.active_type = 1
      this.postData.active_time = formatDate(new Date(), 'yyyy-MM-dd')
      // fix 13444 编辑会员卡--修改卡类型时，到期时间计算有误
      this.postData.order_all_num = +this.cardData.last_order_purchase_volume
      this.postData.order_gift_num = +this.cardData.last_order_gift_volume
    },
    //编辑状态取消修改会员卡
    cancelChangeCard() {
      this.$router.go(0)
    },
    //获取教练列表
    getCoachList() {
      getcoachsInfo().then(res => {
        if (res.data.errorcode == 0) {
          this.coachList = res.data.data
          this.$emit('update:coachList', this.coachList)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    //会员卡选择操作
    cardChange(cardId) {
      if (
        !this.isPreOrder &&
        (this.actionType != 'edit' ||
          (this.actionType == 'edit' && !this.selDis))
      ) {
        this.postData.coach_id = ''
      }
      if (!!cardId && String(cardId).startsWith('package_')) {
        this.filterMan.subCardType = '6'
        this.packageId = cardId.split('_')[1]
        this.handleFilterCard()
        this.handlePackageChange()
        return false
      }

      // let selectedCard = {}
      // selectedCard.card_type_id = ''
      let selectedCard = { ...this.selectedCard }
      for (let item of this.addCardList) {
        if (item.card_id == cardId) {
          selectedCard = item
          break
        }
      }
      this.curCard = selectedCard
      this.postData.card_type_id = selectedCard.card_type_id
      this.postData.is_pt_time_limit_card = selectedCard.is_pt_time_limit_card
      this.$emit('update:selectedCard', selectedCard)
      let payDes = ''
      const desObj = [
        {
          key: 'add',
          name: '购卡'
        },
        {
          key: 'change',
          name: '升卡'
        },
        {
          key: 'reNew',
          name: '续卡'
        },
        {
          key: 'seperate',
          name: '拆出'
        },
        {
          key: 'edit',
          name: '编辑'
        }
      ]
      for (const iterator of desObj) {
        if (iterator.key === this.actionType) {
          payDes = iterator.name
          break
        }
      }
      EventBus.$emit(
        'on-pay-des-change',
        `${payDes}[${selectedCard.card_name}]`
      )
      this.postData.universal_card = selectedCard.universal_card
      this.postData.card_id = selectedCard.card_id
      this.postData.charge_type = selectedCard.card_type_id == 1|| selectedCard.is_pt_time_limit_card == 1 ? 2 : 1
     
      //非编辑卡 或者编辑卡时的可编辑状态才需要执行
      if (
        this.actionType != 'edit' ||
        (this.actionType == 'edit' && !this.selDis)
      ) {
        if (!this.isPreOrder) {
          this.postData.purchase_volume = selectedCard.number
          this.postData.gift_volume = ''
          this.postData.end_time =
            selectedCard.end_time == 0 ? '' : selectedCard.end_time
        }
      }
      if (
        !this.isPreOrder &&
        (this.actionType == 'add' || this.actionType == 'reNew')
      ) {
        this.postData.gift_volume = selectedCard.gift_number || 0
      }
      
      // 部分表单校验
      this.cardTypeValid = this.cardTypeValidArr[
        selectedCard.is_pt_time_limit_card == 1
          ? 0
          : parseInt(selectedCard.card_type_id) - 1
      ]
      selectedCard.universal_card == 1 &&
        !(this.actionType == 'edit' && this.selDis) &&
        this.getSupportBus(selectedCard.card_id)
      if (
        this.curCard.is_pt_time_limit_card != 1 &&
        (selectedCard.card_type_id == 4 || selectedCard.card_type_id == 5) &&
        this.actionType != 'edit'
      ) {
        if (!this.isPreOrder) {
          this.postData.purchase_volume = selectedCard.buy_min_value
        }
      }

        const bool = this.curCard.single_price &&
        (this.curCard.card_type_id == 4 || this.curCard.card_type_id == 5) &&
        !this.isPreOrder &&
        this.actionType !== 'seperate' &&
        this.actionType !== 'edit'
      if (this.curCard.is_pt_time_limit_card != 1 && bool) {
        this.privateNumChange(this.postData.purchase_volume)
      // 处理售价未更新，导致折扣券限制使用的判断问题
      }else if(this.curCard.is_pt_time_limit_card == 1 && bool && this.curCard.current_price) {
        const num = typeof this.curCard.current_price === 'number'
          ? num.toFixed(2)
          : this.curCard.current_price
        this.$emit('onPrivateNumChange', num)
      }
      if (this.actionType == 'seperate') {
        this.card[0].type = this.cardData.card_name
        this.cardData.card_type_id == 4 || this.cardData.card_type_id == 5
          ? (this.card[1].type = this.curCard.card_name)
          : this.cardData.card_name
        this.card[0].leftBefore = this.cardData.last_num
        this.card[0].leftAfter =
          this.cardData.last_num - this.postData.order_all_num
        this.card[1].leftAfter = this.postData.order_all_num
        this.card[0].expireTimeAfter = this.card[1].expireTimeAfter = this.cardData.end_time_date
        // this.card[1].expireTimeAfter = addDays(formatDate(new Date(), 'yyyy-MM-dd'), this.cardData.last_day);
        let date = formatDate(new Date(), 'yyyy-MM-dd')
        // this.card[1].expireTimeAfter = addDays(date, this.cardData.last_day - 1);
        this.privateNumChange(this.postData.order_all_num)
        let total = this.postData.order_all_num * this.curCard.single_price
        let times = this.postData.order_all_num
        let price = this.curCard.single_price
        let name = this.curCard.card_name
        this.$emit('forSeperateCard', { total, times, price, name })
      }
      // check marketer
      setTimeout(() => {
        EventBus.$emit('on-check-marketer', false)
      }, 1000)
    },
    // 不为私教包月卡时 私教/泳教课的有效期计算 = 卡课设置单节有效期 * 节数 
    setPrivateCardEndTime() {
      if(!(this.curCard.card_type_id == 4 || this.curCard.card_type_id == 5)) {
        return;
      }
      // 购、开单、跨店购 、续新卡、升私/泳教、跨店升 情景下  
      if(this.curCard.is_pt_time_limit_card != 1 && (((this.actionType == 'add' || this.actionType == 'reNew' || this.actionType == 'change') && !this.isPreOrder) || (this.isPreOrder && this.isCardDataInit))) {
        const allNume = Number(this.postData.purchase_volume || 0) + Number(this.postData.gift_volume || 0)
        // 私教/泳教时 end_time为单节有效期
        this.postData.end_time = Number(this.curCard.end_time) !== 0 && allNume !== 0 ? (allNume * this.curCard.end_time).toString() : ''
      }
    },
    //私教教练选择操作--多教练
    coachChange(curCoachIds) {
      curCoachIds = curCoachIds || []
      let selectedCoach = {
        coachIds: [],
        coachNames: []
      }
      curCoachIds.forEach(item => {
        for (let coach of this.coachList) {
          if (item == coach.coach_id) {
            selectedCoach.coachIds.push(coach.coach_id)
            selectedCoach.coachNames.push(coach.coach_name)
          }
        }
      })
      this.postData.coach_id = selectedCoach.coachIds
      this.postData.coach_name = selectedCoach.coachNames
      if ((this.actionType == 'edit' || this.isPreOrder) && this.selDis) {
        //编辑的初始状态不触发教练更新
      } else {
        this.$emit(
          'update:coachCur',
          selectedCoach.coachIds && selectedCoach.coachIds[0]
        )
      }
    },
    //外部提交表单操作（如购卡时点击提交）
    handleSubmitClick() {
      let valid
      this.$refs.formData.validate(val => {
        valid = val
      })
      var postData = JSON.parse(JSON.stringify(this.postData))
      postData.getEndDay = this.getEndDay // 续卡选新卡需要到期日期
      postData.coach_id =
        (Array.isArray(postData.coach_id) && postData.coach_id.join(',')) ||
        postData.coach_id
      postData.coach_name =
        (Array.isArray(postData.coach_name) && postData.coach_name.join(',')) ||
        postData.coach_name
      if (this.actionType != 'edit') {
        delete postData.last_volume
      }

      if (this.actionType == 'seperate') {
        let seperate_valid = true
        if (this.cardData.card_type_id == 2) {
          postData.card_type_id = this.cardData.card_type_id
          postData.card_user_id = this.cardData.carduser_id
          postData.user_id = this.cardData.user_id
          postData.last_day = this.cardData.last_day
        } else {
          seperate_valid = valid
        }
        if (seperate_valid && !/^[1-9]\d*$/.test(postData.order_all_num)) {
          seperate_valid = false
          this.$Message.error('拆分次数需要为正整数！')
        }

        return {
          valid: seperate_valid,
          postData
        }
      } else {
        return {
          valid,
          postData
        }
      }
      // Object.freeze(postData)
    },
    //验证临时修改场馆权限
    getSupportBusAuthor() {
      this.$service
        .get('/Web/Card/update_support_bus')
        .then(res => {
          if (res.data.errorcode != 0) {
            this.$Message.error(res.data.errormsg)
          } else {
            this.editSupportBus = true
          }
        })
        .catch(function(error) {
          this.editSupportBus = false
        })
    },
    handleFilterCard() {
      // check marketer
      if (this.selectedCard) {
        this.selectedCard.card_type_id = this.filterMan.subCardType
      } else {
        this.selectedCard = { card_type_id: this.filterMan.subCardType }
      }
      this.curCard = { card_type_id: this.filterMan.subCardType }
      this.cardTypeValid = this.cardTypeValidArr[
        this.selectedCard.is_pt_time_limit_card == 1
          ? 0
          : parseInt(this.selectedCard.card_type_id) - 1
      ]
      this.$emit('update:selectedCard', this.selectedCard)
      setTimeout(() => {
        EventBus.$emit('on-check-marketer', false)
      }, 1000)

      if (this.filterMan.subCardType === '6') {
        this.postData.card_id = ''
        this.filterCardList = []
        this.selectDiscountIndex = ''
        this.selectedDiscount = null
        EventBus.$emit('on-withdrap-discount')
        return false
      } else {
        this.packageData = []
        this.packageId = ''
      }

      let arr = [].concat(this.addCardList)
      // single or multi.
      if (this.filterMan.cardType != '-1') {
        arr = arr.filter(item => item.universal_card == this.filterMan.cardType)
      }
      // date, count, store, coach of card.
      if (this.filterMan.subCardType != '-1') {
        arr = arr.filter(
          item => item.card_type_id == this.filterMan.subCardType
        )
      } else if (
        this.isNormal === true &&
        (this.isPt === false || this.isSwim === false)
      ) {
        arr = arr.filter(
          item => item.card_type_id != 4 && item.card_type_id != 5
        )
      }
      this.filterCardList = arr.filter(item => {
        if (
          (this.actionType == 'edit' || this.isPreOrder) &&
          this.cardData.card_id == item.card_id
        ) {
          return true
        } else {
          return item.sale_status === '1'
        }
      })
    }
  }
}
</script>
<style lang="less">
.bonus-card-table .ivu-card-body {
  padding: 0;
}
.bonus-card {
  width: 100%;
  padding: 0;
  /* margin: 0 0 24px 50px; */
}
.edit-bus {
  color: #3598db;
  cursor: pointer;
  margin-left: 10px;
}

.input-number-box {
  display: flex;
  align-items: center;
  .rule-alert {
    margin-bottom: 0;
    padding: 6px 14px 6px 14px;
  }
}
</style>
