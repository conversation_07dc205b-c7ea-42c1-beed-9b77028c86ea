<template>
<div>
  <Modal title="自主签到提醒" width="800" class="sign_modal" :mask-closable="false" v-model="showModal" @on-cancel="commit">
    <div class="voice-wrap">
      <span>语音提示</span>
      <i-switch v-model="isPlayVoice" @on-change="voiceStatusChange" size="small"></i-switch>
    </div>
    <div from="2" style="min-height: 100px;" v-if="commitItem.user_id">
      <RemindMessage :userId="commitItem.user_id"></RemindMessage>
      <UserInfo :data="noticeUserInfo"></UserInfo>
    </div>
    <div class="newsign-lang">
      <div class="newsign-lang-t">
        会员卡信息
      </div>
      <div class="newsign-lang-b">
        <table class="table" style="border-collapse: collapse" borderColor='#eeeeee' cellSpacing='0' align='center' border='1'>
          <thead>
            <tr>
              <th>卡种</th>
              <th width="80">卡号</th>
              <th>天数</th>
              <th>有效期</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="data in cardData" :key="data.card_id">
              <td>{{data.cardname}}</td>
              <td width="80">{{data.card_sn}}</td>
              <td>
                总计<span v-if="data.charge_type==1 && data.card_type_id!=3"><span class="red">{{data.all_num}}</span>次</span>
                <span v-if="data.card_type_id==3"><span class="red">{{data.all_num}}</span>元</span>
                <span v-if="data.charge_type==2"><span class="red">{{data.all_days}}</span>天</span>, 剩余
                <span class="red">{{data.overplus}}</span>
              </td>
              <td>{{data.validity}}</td>
              <td :class="{green:data.status!='正常'}">{{data.status}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <brandAdd v-if="noticeUserInfo" @on-enter="commit" :number.sync="commitItem.brand_number"></brandAdd>
    <Form v-if="allowPrint">
      <FormItem label="打印小票">
        <i-switch size="small" true-value="1" false-value="0" v-model="is_print_ticket" />
      </FormItem>
    </Form>
    <div slot="footer" class="modal-buttons" style="padding-bottom: 20px; padding-top: 10px" v-if="noticeUserInfo">
      <Button style="margin: 0 30px" type="success" @click="commit">确认，下一条</Button>
      <Button @click="cancel">撤销签到</Button>
    </div>
    <div slot="footer" v-else></div>
  </Modal>
   <!-- 音频播放 -->
    <audio controls="controls" id="audio" style="display:none">
      <source src="/static/media/welcome.mp3" type="audio/mpeg">
    </audio>
</div>

</template>

<script>
import socket from 'mixins/socket'
import brandAdd from 'src/views/signin/components/brandAdd.vue'
import UserInfo from 'src/views/signin/components/userInfo.vue'
import RemindMessage from 'components/user/remindMessage.vue'
import { mapState } from 'vuex'
import { EventBus } from 'components/EventBus.js'
export default {
  name: 'qrSignNotice',
  props: {},
  mixins: [socket],
  components: {
    UserInfo,
    brandAdd,
    RemindMessage
  },
  data() {
    return {
      maxTime: '0',
      index: '',
      msg: '有新签到消息提醒',
      noticeUserInfo: null,
      allowPrint: false,
      is_print_ticket: '1',
      cardData: [],
      isEntering: false,
      isFlash: false,
      oTitle: '',
      isPlayVoice: localStorage.getItem('isPlayVoice') !== 'false',
      commitItem: {
        user_id: '',
        symbol: '',
        sign_log_id: '',
        brand_number: []
      }
    }
  },
  computed: {
    ...mapState(['busId']),
    ...mapState('websocket', [
      'websocket',
      'socketNoticeType',
      'socketNoticeArray',
      'socketMsgCount',
      'socketModalShow'
    ]),
    showModal: {
      get() {
        return this.socketModalShow
      },
      set(value) {
        this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', value)
      }
    }
  },
  watch: {
    websocket(val, oldVal) {
      if (val && val !== oldVal) {
        this.websocket.onmessage = this.websocketonmessage
      }
    },
    showModal(val) {
      if (val) {
        this.commitItem.user_id = this.socketNoticeArray[0].user_id
        this.commitItem.sign_log_id = this.socketNoticeArray[0].sign_log_id
        this.commitItem.sign_log_ids = this.socketNoticeArray[0].sign_log_ids
        this.commitItem.symbol = this.socketNoticeArray[0].symbol
        this.getUser()
        this.getCardUser()
      } else {
        EventBus.$emit('qrSignAllRead')
      }
      this.clear()
    },
    socketNoticeArray(val, oldVal) {
      if (val && val.length === 0) {
        this.$Notice.close('socketNotice')
        this.showModal = false
        this.clear()
      }
    },
    'commitItem.sign_log_id'(val, oldVal) {
      if (val && val !== oldVal && this.showModal) {
        this.getUser()
        this.getCardUser()
      }
    }
  },
  created() {
    this.getUnreadCount()
    // EventBus.$on('outSocketNotice', id => {
    //   this.noticeOut(id)
    // })
    window.addEventListener('storage', (event)=> {
      if (event.key == 'socketMsgCount') {
        this.$store.commit('websocket/SET_SOCKET_MSG_COUNT',  localStorage.getItem('socketMsgCount'))
      }
    })
  },
  beforeDestroy() {
    this.destroyEvent()
  },
  methods: {
    websocketonmessage(e) {
      //{"user_id":"8289","create_time":1524730365,"sign_log_id":"14279","type":"sign","action":"push_info","symbol":"01|34"}
      //{"admin_id":"1","create_time":1524730191,"msg_count":null,"type":"","action":"push_info","symbol":"01|34"}
      const resdata = JSON.parse(e.data)
      if (
        resdata.action === 'push_info' &&
        resdata.type === 'sign' &&
        localStorage.getItem('noticeStatus' + this.busId) !== 'false' &&
        resdata.bus_id == this.busId
      ) {
        let noticeType = this.socketNoticeType
        this.$store.commit(
          'websocket/SET_SOCKET_NOTICE_ARRAY',
          this.socketNoticeArray.concat(resdata)
        )
        this.flash();
        if (localStorage.getItem('isPlayVoice') !== 'false') {
          this.playVoice()
        }
        if (noticeType === 0) {
          this.noticeModalShow(resdata)
        } else if (noticeType === 1) {
          this.$Notice.close('socketNotice')
          this.$Notice.config({
            top: 130
          });
          this.$Notice.open({
            title: '',
            name: 'socketNotice',
            render: (h, params) => {
              return (
                <div
                  style="cursor: pointer;"
                  on-click={name => {
                    this.noticeModalShow(resdata)
                  }}
                >
                  <h3>新签到提醒通知</h3>
                  <p>有{this.socketNoticeArray.length}条新签到通知待确认。</p>
                </div>
              )
            },
            onClose:()=> {
              this.$store.commit('websocket/SET_SOCKET_NOTICE_ARRAY', [])
            },
            duration: 0
          })
        }
      } else if (resdata.action == 'push_info' && resdata.type === 'message') {
        localStorage.setItem('socketMsgCount',  +resdata.msg_count)
        this.$store.commit('websocket/SET_SOCKET_MSG_COUNT',  +resdata.msg_count)
        EventBus.$emit('newSocketMsgIn', resdata)
      } else if (resdata.action == 'clean_sign') {
        this.noticeOut(resdata.sign_log_id)
        // EventBus.$emit('outSocketNotice', resdata.sign_log_id)
      }
    },
    getUnreadCount() {
      return this.$service
        .post('/Web/Msg/get_unread_count', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            localStorage.setItem('socketMsgCount',  +res.data.data.count)
            this.$store.commit('websocket/SET_SOCKET_MSG_COUNT',  +res.data.data.count)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },
    // 音频播放按钮
    playVoice() {
      let audio = document.getElementById('audio')
      audio.play()
    },
    noticeModalShow(resdata) {
      this.$Notice.close('socketNotice')
      this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', true)
    },
    destroyEvent() {
      this.noticeUserInfo = null
    },
    //用户信息
    getUser() {
      this.$service
        .post(
          '/Web/Member/userMsg',
          { user_id: this.commitItem.user_id, sign_log_id: this.commitItem.sign_log_id, sign_log_ids: this.commitItem.sign_log_ids },
          { loading: false }
        )
        .then(res => {
          if (res.data.errorcode == 0) {
            this.noticeUserInfo = res.data.data;

            this.is_print_ticket = this.noticeUserInfo.is_print_ticket;

            const ids = this.noticeUserInfo.card_type_ids;
            if (Array.isArray(ids) && ids.length > 0) {
              ids.forEach(item => {
                if (item == 2 || item == 3) {
                  this.allowPrint = true;
                }
              });
            } else {
              this.allowPrint = false;
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(function(err) {
          console.log(err)
        })
    },
    //用户会员卡列表
    getCardUser() {
      this.$service
        .post(
          '/Web/Member/carduserList',
          { user_id: this.commitItem.user_id },
          { loading: false }
        )
        .then(res => {
          if (res.data.errorcode == 0) {
            this.cardData = res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
        .catch(function(err) {
          console.log(err)
        })
    },

    voiceStatusChange(val) {
      localStorage.setItem('isPlayVoice', val)
    },

    //签到数据提交
    commit() {
      if (this.isEntering) {
        return false
      }
      this.isEntering = true
      let commitUrl = 'Web/Sign/sign_confirm_web'
      let postData = {
        sign_log_id: this.commitItem.sign_log_id,
        symbol: this.commitItem.symbol,
        is_print_ticket: this.is_print_ticket
      }
      if (
        this.commitItem.brand_number.length > 0 &&
        this.commitItem.brand_number[0] != ''
      ) {
        commitUrl = '/Web/Sign/add_brand_number'
        postData = this.commitItem
      }

      let goAhead = false;
      let signinIds = '';
      if (this.is_print_ticket == 1 && this.allowPrint) {
        goAhead = true;
        signinIds = this.commitItem.sign_log_ids;
      }

      this.$service
        .post(commitUrl, postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success(res.data.errormsg)
            this.next()

            if (goAhead) {
              if (Array.isArray(signinIds) && signinIds.length > 0) {
                // signinIds.forEach(signinId => {
                //   const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinId}});
                //   window.open(routeUrl.href, '_blank');
                // });
                signinIds = signinIds.join(',');
                const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinIds}});
                window.open(routeUrl.href, '_blank');
              }
            }
          } else {
            this.$Message.error(res.data.errormsg)
          }
          this.isEntering = false
        })
        .catch(err => {
          this.isEntering = false
        })
    },
    //当其它客户端或页面已经确认或取消了某条签到时
    noticeOut(signLogId) {
      //在队列中清除
      for (let index = 0; index < this.socketNoticeArray.length; index++) {
        if (this.socketNoticeArray[index].sign_log_id == signLogId) {
          this.socketNoticeArray.splice(index, 1)
          this.$store.commit(
            'websocket/SET_SOCKET_NOTICE_ARRAY',
            this.socketNoticeArray
          )
          break
        }
      }
      //如果已经展示在页面弹窗上
      if (this.commitItem.sign_log_id == signLogId) {
        this.next()
      }
    },
    //下一条
    next() {
      this.socketNoticeArray.splice(0, 1)
      this.$store.commit(
        'websocket/SET_SOCKET_NOTICE_ARRAY',
        this.socketNoticeArray
      )
      this.noticeUserInfo = null
      this.commitItem = {
        user_id: '',
        sign_log_id: '',
        symbol: '',
        brand_number: []
      }
      if (this.socketNoticeArray.length !== 0) {
        this.commitItem.user_id = this.socketNoticeArray[0].user_id
        this.commitItem.sign_log_id = this.socketNoticeArray[0].sign_log_id
        this.commitItem.sign_log_ids = this.socketNoticeArray[0].sign_log_ids
        this.commitItem.symbol = this.socketNoticeArray[0].symbol
      }
    },
    //撤销签到
    cancel() {
      this.$service
        .post('/Web/Sign/cancel_user_sign', {
          sign_log_id: this.socketNoticeArray[0].sign_log_id,
          symbol: this.socketNoticeArray[0].symbol
        })
        .then(res => {
          if (res.data.errorcode == 0) {
            this.$Message.success('取消成功')
            this.next()
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    //title闪烁
    flash(msg) {
      if (this.isFlash) {
        this.clear() //先停止
      } else {
        this.oTitle = document.title //保存原来的title
      }
      this.isFlash = true
      if (typeof msg == 'undefined') {
        msg = this.msg
      }
      this.message = [msg, this.getSpace(msg)]
      let th = this
      this.timer = setInterval(function() {
        th._flash(msg)
      }, 500)
    },
    _flash(msg) {
      this.index = !this.index ? 1 : 0
      document.title = '【' + this.message[this.index] + '】'
    },
    getSpace(msg) {
      let n = msg.length
      let s = ''
      let num = msg.match(/\w/g)
      let n2 = num != null ? num.length : 0 //半角字符的个数
      n = n - n2 //全角字符个数
      let t = parseInt(n2 / 2)
      if (t > 0) {
        //两个半角替换为一个全角
        n = n + t
      }
      s = n2 % 2 ? ' ' : '' //如果半角字符个数为奇数
      while (n > 0) {
        s = s + '　' //全角空格
        n--
      }
      return s
    },
    clear() {
      clearInterval(this.timer)
      if (this.isFlash) {
        // 如果正在闪
        document.title = this.oTitle //将title复原
      }
      this.isFlash = false
    }
  }
}
</script>

<style lang="less" scoped>
.newsign-lang {
  overflow: hidden;
  clear: both;
  /*padding: 0 30px;*/
  background: #fff;
  border: 1px solid #eee;
  border-top: none;
  background: #fff;
  margin-bottom: 40px;
  /*min-height: 100px;*/
}
.newsign-lang-b {
  table {
    width: 100%;
    margin: 0 auto;
    font-size: 14px;
    color: #313131;
    td,
    th {
      height: 30px;
      font-weight: normal;
      text-align: center;
      word-break: break-all;
      .red {
        color: #e60012;
      }
      .green {
        color: #5fb75d;
      }
    }
  }
}
.newsign-lang-t {
  width: 100%;
  background: #fff;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  border: 1px solid #eee;
  text-align: center;
  border-left: none;
  border-right: none;
  font-size: 14px;
  font-weight: bold;
}
.voice-wrap {
  position: absolute;
  top: 15px;
  left: 135px;
  span {
    vertical-align: middle;
  }
}
.ivu-switch-checked {
  border-color: #5fb75d;
  background-color: #5fb75d;
}
</style>
