<template>
  <Select placeholder="请选择储值卡" size="small" :disabled="disabled" v-model="selected" v-if="list.length" transfer filterable @on-change="handleChange">
    <Option v-for="item in list" :key="item.card_user_id" :label="item.card_name" :value="item.card_user_id">
      {{ item.card_name }} （余额: {{ item.amount }} 元）
    </Option>
  </Select>
</template>

<script>
  export default {
    name: 'moneyCardSelect',
    props: {
      userId: {},
      value: {},
      disabled: {
        type: <PERSON><PERSON>an,
        default: false
      }
    },
    data() {
      return {
        list: [],
      };
    },
    computed: {
      selected: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    created() {
      this.getList();
    },
    methods: {
      getList() {
        const user_id = this.userId || this.$route.params.userId || this.$route.query.userId
        if(!user_id) {
          this.$emit('user-no-card')
          this.$Message.error('无可用会员ID')
        }
        if (user_id === 'self_id') { // 散客购票，不调用接口
          this.$emit('user-no-card')
          this.$Message.error('无可用会员ID')
          return;
        }
        // is_disabled为1 可以展示已用完的卡
        this.$service.post('/Web/Commodity/get_user_debit_card', { user_id, type: 1, is_disabled: this.disabled ? 1 : 0 }).then(res => {
          if (res.data.errorcode === 0) {
            this.list = res.data.data.list || [];
            if (!this.list.length) {
              if (user_id === -1 && this.selected) { // 当批量购票时使用了储值卡。所以虚拟会员的购票记录会有储值卡数据需要显示，并且不提示错误
                return;
              }
              this.$emit('user-no-card');
              return this.$Message.error('该用户无储值卡');
            }
          } else {
            this.list = []
            this.$emit('user-no-card')
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleChange(val) {
        const item = this.list.find(item => item.card_user_id === val);
        this.$emit('on-card-change', {
          card_user_id: val,
          stored_card_id: item?.card_id || '',
        })
      }
    },
  };
</script>

<style scoped>

</style>
