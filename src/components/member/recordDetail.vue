<template>
  <div>
    <div class="expanditem"
         v-for="it in data"
         :key="it.create_time">
      <div class="leftpart">
        <img src="./../../assets/img/operator.png">
        <p class="opname">{{it.admin_name}}</p>
        <p class="optime">{{it.creat_time}}</p>
      </div>
      <div class="rightpart">
        <div class="stretch righthead">
          <p class="text_left operation"
             :class="{'one':it.operating_type=='购卡','two':it.operating_type=='编辑','three':it.operating_type=='续卡','four':it.operating_type=='升卡','five':it.operating_type=='请假','six':it.operating_type=='销卡','seven':it.operating_type=='启用会员卡','eight':it.operating_type=='会员卡激活','nine':it.operating_type=='成员变更','ten':it.operating_type=='会员卡删除','eleven':it.operating_type=='撤销合同',}">
            {{it.operating_type}}</p>
          <p class="text_right orderno"
             v-if='it.order_sn'>订单号：{{it.order_sn}}</p>
        </div>
        <div class="stretch">
          <p class="text_left">会员</p>
          <p class="text_right">{{it.username}}</p>
        </div>
        <div class="stretch">
          <p class="text_left">{{it.operating_type === '跨店购卡' ? '代售场馆': '操作场馆'}}</p>
          <p class="text_right">{{it.operating_type === '跨店购卡' ? it.operating_bus_name : it.operating_bus_name}}</p>
        </div>
        <div class="stretch" v-if="it.operating_type === '跨店购卡'">
          <p class="text_left">会员卡归属场馆</p>
          <p class="text_right">{{it.debt_bus_name}}</p>
        </div>
        <div class="stretch" v-if="it.custom_type === 1 || it.custom_type === 2">
          <p class="text_left">{{it.custom_type === 1?'套餐包类型':'归属套餐'}}</p>
          <p class="text_right">{{it.pc_name}}</p>
        </div>
        <div class="line-table" v-if="it.custom_type === 1 && it.sub_detail && it.sub_detail.length">
            <table>
              <thead>
                <tr>
                  <th>名称</th>
                  <th>归属场馆</th>
                  <th>总共</th>
                  <th>价值</th>
                  <th>到期时间</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(subItem, subIndex) in it.sub_detail" :key="subIndex">
                  <td>{{subItem.name}}</td>
                  <td>{{ subItem.bus_name }}</td>
                  <td>{{subItem.overplus}}</td>
                  <td>{{subItem.amount}}元</td>
                  <td>{{subItem.end_time}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        <template v-for='co in it.content'>
          <div class="stretch"
               v-if="co"
               :key="co.title">
            <p class="text_left"
               v-if='co.title!="订单备注"'>{{co.title}}</p>
            <p class="text_left"
               v-if='co.title=="订单备注"'>{{co.title}}：
              <span class="remark">{{co.text}}</span>
            </p>
            <div class="text_right"
                 v-if='co.title!="订单备注" && co.title != "合同金额" && co.title != "退款金额" && co.title != "业绩金额"'>
              <p class="rightone">{{co.text}}</p>
              <div class="rightmiddle"
                   v-if='co.text!==undefined&&co.font!==undefined'></div>
              <p class="righttwo"
                 v-if='co.font!==undefined'>{{co.font}}</p>
            </div>
            <div class="text_right"
                 v-if='co.title == "合同金额" || co.title == "退款金额" || co.title == "业绩金额"'>
              <p class="rightone" v-if="co.text || co.text === 0">{{co.text}}元</p>
              <div class="rightmiddle"
                   v-if='co.text&&(co.font || co.font === 0)'></div>
              <p class="righttwo"
                 v-if='co.font || co.font === 0'>{{co.font}}元</p>
            </div>
          </div>
        </template>
        <!-- <div class="fleft">
                                      <p>备注：</p>
                                      <p class="remark">此卡已送柜子</p>
                                    </div> -->
        <div class="lastline"
             v-if='it.edit_reason'>
          <p v-if='it.operating_type=="编辑"'>修改原因：</p>
          <p v-if='it.operating_type=="销卡"'>销卡原因：</p>
          <p v-if='it.operating_type=="升卡"||it.operating_type=="升卡（跨店）"'>升卡原因：</p>
          <p v-if='it.operating_type=="撤销合同"'>撤销原因：</p>
          <p class="cause" v-html="it.edit_reason"></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'recordDetail',
    data() {
      return {

      }
    },
    props: ['data'],
    methods: { }
  }
</script>

<style lang="less" scoped>
  .expanditem {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .expanditem:first-child {
    margin-top: 34px;
  }

  .expanditem p {
    margin: 0;
    padding: 0;
  }

  .leftpart {
    width: 118px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 21px;
  }


  /*.leftpart:first-child {
        margin-top: 55px;
      }*/

  .leftpart img {
    width: 30px;
    height: 30px;
  }

  .leftpart .opname {
    font-size: 14px;
    color: #333;
    line-height: 30px;
  }

  .leftpart .optime {
    font-size: 12px;
    color: #333;
    /*line-height: 18px;*/
  }

  .rightpart {
    width: 360px;
    margin-right: 29px;
    background: #fff;
    padding: 0 15px 0 15px;
    margin-bottom: 20px;
    box-sizing: border-box;
    border: 1px solid #f1f1f1;
    position: relative;
  }








  /*.rightpart:first-child {
        margin-top: 34px;
      }*/

  .rightpart:before {
    content: '';
    position: absolute;
    width: 50px;
    height: 24px;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-left: 25px solid transparent;
    border-right: 25px solid #f1f1f1;
    top: 30px;
    left: -50px;
  }

  .rightpart:after {
    content: '';
    position: absolute;
    width: 48px;
    height: 22px;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;
    border-left: 24px solid transparent;
    border-right: 24px solid #fff;
    top: 31px;
    left: -48px;
  }

  .stretch {
    width: 100%;
    min-height: 30px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    color: #333;
  }

  .stretch .text_left {
    font-size: 12px;
    min-width: 80px;
  }

  .stretch .text_right {
    font-size: 14px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
  }

  .text_right .righttwo {
    color: #d9544f;
  }

  .text_right .rightmiddle {
    width: 10px;
    height: 2px;
    border: 1px solid #d9544f;
    position: relative;
    margin-left: 5px;
    margin-right: 8px;
  }

  .rightmiddle:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    left: 9px;
    top: -3px;
    border: 3px solid transparent;
    border-left: 3px solid #d9544f;
  }

  .stretch.righthead {
    height: 46px;
  }

  .righthead .operation {
    font-size: 16px;
    padding-left: 8px;
    padding-right: 8px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    box-sizing: border-box;
    border: 1px dashed #3b9fff;
  }

  .operation.one {
    border: 1px dashed #5cb85c;
    color: #5cb85c;
  }

  .operation.two {
    border: 1px dashed #3b9fff;
    color: #3b9fff;
  }

  .operation.three {
    border: 1px dashed #da70fd;
    color: #da70fd;
  }

  .operation.four {
    border: 1px dashed #0a32ff;
    color: #0a32ff;
  }

  .operation.five {
    border: 1px dashed #f2c202;
    color: #f2c202;
  }

  .operation.six {
    border: 1px dashed #d9544f;
    color: #d9544f;
  }

  .operation.seven {
    border: 1px dashed #20d5be;
    color: #20d5be;
  }

  .operation.eight {
    border: 1px dashed #ff7214;
    color: #ff7214;
  }

  .operation.nine {
    border: 1px dashed #8dd018;
    color: #8dd018;
  }

  .operation.ten {
    border: 1px dashed #4c18d0;
    color: #4c18d0;
  }

  .operation.eleven {
    border: 1px dashed #aaaaaa;
    color: #aaaaaa;
  }

  .righthead .orderno {
    color: #3b9fff;
    font-size: 12px;
  }

  .fleft {
    width: 100%;
    height: 30px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    color: #333;
    font-size: 12px;
  }

  .fleft .remark {
    color: #d9544f;
  }

  .text_left .remark {
    color: #d9544f;
  }

  .lastline {
    width: 100%;
    min-height: 46px;
    padding-top: 8px;
    padding-bottom: 8px;
    box-sizing: border-box;
    border-top: 1px dashed #e5e5e5;
    color: #333;
    font-size: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
  }

  .lastline .cause {
    color: #666;
  }
  .lastline p{
    min-width: 80px;
  }
   .line-table {
    margin-bottom: 10px;
    font-size: 12px;
     table {
       width: 100%;
        border-collapse: collapse;
        margin: 0 auto;
        text-align: center;
      }
      table td, table th {
        border: 1px solid #79848b;
        font-weight: normal;
        color: #666;
        height: 30px;
      }
  }
</style>


