<template>
<div>
  <div class="contentbox" v-if="cardlist && cardlist.length>0 " >
    <template v-for="(item,index) in cardlist">
      <div class="itemtotal" v-if='orderSn?orderSn==item.order_sn:(!carduserid || carduserid == item.card_user_id)' :key="item.card_user_id">
        <div class="itembox" :class="{'blue':item.deleted!=1&&item.deleted!=2&&item.deleted!=3&&item.deleted!=5,'expandshow':selectindex==index}" @click="expandContent(index)">
          <div class="iteminstrs">
            <p class="cardtype" :title="item.card_name||item.package_name">{{item.card_name||item.package_name}}
              <em v-if="item.deleted==1||item.deleted==2||item.deleted==3||item.deleted==5">{{item.deleted == 1 ? '(已升卡)' : item.deleted == 2 ? '(已销卡)' : item.deleted == 3 ? `(${item.card_disable==1?'已禁用':'已删除'})` : item.deleted == 5 ? '(已转卡)':''}}</em>
            </p>
            <p class="cardid" v-if="item.card_sn">卡号：{{item.card_sn}}</p>
            <p class="recordinstr">条{{type?type:'操作'}}记录</p>
            <span class="recordno">{{item.operating_count}}</span>
          </div>
          <i :class="{'up':selectindex==index}"></i>
        </div>
        <div class="expand blue" :class="{'expandshow':selectindex==index}">
          <recordDetail :data="carddetaillist"></recordDetail>
        </div>
      </div>
    </template>
  </div>
  <Alert type="warning" show-icon v-else>暂无相关数据</Alert>
</div>

  <!-- </div> -->
  <!-- </div> -->
</template>
<script>
import recordDetail from './recordDetail.vue'
export default {
  name: 'EditRecord',
  data() {
    return {
      cardlist: [],
      selectedcard: '',
      curOrderSn: '',
      selectindex: -1,
      expanded: false,
      carddetaillist: [],
      // editrecordshow: true,
    }
  },
  components: {
    recordDetail
  },
  props: ['ershow', 'userid', 'carduserid', 'orderSn', 'type'],
  computed: {
    windowstatus: function () {
      // this.editrecordshow = this.ershow;
      return this.ershow;
    },
  },
  watch: {
    'windowstatus': function (val) {
      if (val) {
        this.selectedcard = '';
        this.selectindex = -1;
        this.expanded = false;
        this.carddetaillist = [];
        this.getcardList();
      }
    }
  },
  methods: {
    getcardList() {
      this.$service.post('/Web/OperatingRecord/get_all_card_user', {
        user_id: this.userid,
        type: this.type
      })
        .then((response) => {
          if (response.status == 200) {
            if (response.data.errorcode == 0) {
              this.cardlist = response.data.data.list;
              if (this.orderSn) {
                for (let i = 0; i < this.cardlist.length; i++) {
                  if (this.cardlist[i].order_sn == this.orderSn) {
                    this.selectindex = i;
                    this.expanded = true;
                  }
                }
                this.selectedcard = this.orderSn;
                this.getcardexpand();
              } else if (this.carduserid) {
                for (let i = 0; i < this.cardlist.length; i++) {
                  if (this.cardlist[i].card_user_id == this.carduserid) {
                    this.selectindex = i;
                    this.expanded = true;
                  }
                }
                this.selectedcard = this.carduserid;
                this.getcardexpand();
              }
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch(function (response) {
          console.log(response)
        })
    },
    expandContent(index) {
      if (!this.carduserid) {
        if (this.expanded && index == this.selectindex) {
          this.selectindex = -1;
          this.expanded = false;
        } else {
          this.selectedcard = this.cardlist[index].card_user_id 
          this.curOrderSn = this.cardlist[index].order_sn 
          this.cardlist[index].order_sn;
          if (this.cardlist[index].operating_count > 0) {
            this.selectindex = index;
            this.expanded = true;
            this.getcardexpand();
          }
        }
      }
    },
    getcardexpand() {
      this.$service.post('/Web/OperatingRecord/operating_record_list', {
        card_user_id: this.selectedcard,
        order_sn: this.curOrderSn || this.orderSn || '',
        type: this.type
      })
        .then((response) => {
          if (response.status == 200) {
            if (response.data.errorcode == 0) {
              this.carddetaillist = response.data.data.list;
            } else {
              this.$Message.error(response.data.errormsg);
            }
          }
        })
        .catch(function (response) {
          console.log(response)
        })
    },
    // cancelPage(){
    //   this.editrecordshow = false;
    //   this.$emit('update:ershow',this.editrecordshow)
    // }
  }
}
</script>
<style scoped>
.poppage {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: none;
}

.poppage.popdisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 9;
  overflow: scroll;
}

.popbox {
  width: 570px;
  /*min-height: 700px;*/
  background: #fff;
  margin: 100px auto;
  padding: 0 20px 35px 20px;
  padding-bottom: 20px;
  border: 1px solid #dcdcdc;
}

.headerbar {
  width: 100%;
  height: 49px;
}

.headerbar h2 {
  font-size: 18px;
  line-height: 49px;
  display: block;
  float: left;
  margin: 0;
  font-weight: bold;
}

.headerbar i {
  display: block;
  float: right;
  width: 20px;
  height: 20px;
  font-size: 20px;
  margin-top: 14px;
  background: url(../../assets/img/xclose.png) no-repeat;
  background-size: cover;
  cursor: pointer;
}

.contentbox {
  width: 100%;
  max-height: 630px;
  overflow: auto;
}

.contentbox .itemtotal {
  width: 100%;
  clear: both;
  position: relative;
  margin-bottom: 10px;
}

.itemtotal .itembox {
  width: 507px;
  height: 86px;
  border-radius: 10 rpx;
  background: url(../../assets/img/wave_grey.png) no-repeat;
  font-size: 18px;
  font-weight: normal;
  color: #666;
  padding: 0 28px 0 30px;
  position: relative;
  z-index: 10;
  cursor: pointer;
}

.itembox.expandshow {
  cursor: default;
}

.contentbox .itembox:last-child {
  margin-bottom: 0;
}

.blue.itembox {
  background: url(../../assets/img/wave_blue.png) no-repeat;
}

.itembox p {
  padding: 0;
  margin: 0;
}

.itembox .iteminstrs {
  width: 425px;
  height: 100%;
  float: left;
}

.itembox .cardtype {
  float: left;
  width: 185px;
  line-height: 86px;
  max-width: 185 rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.cardtype em {
  font-size: 14px;
  font-style: normal;
  line-height: 86px;
  color: #666;
}

.blue .cardtype em {
  color: #fff;
}

.itembox .cardid {
  float: left;
  font-size: 14px;
  width: 134px;
  line-height: 86px;
  max-width: 134 rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.itembox .recordinstr {
  float: right;
  font-size: 14px;
  width: 75px;
  line-height: 86px;
}

.itembox .recordno {
  color: #666;
  float: right;
  display: inline-block;
  width: 30px;
  font-size: 18px;
  line-height: 86px;
  max-width: 30 rpx;
  overflow: hidden;
  white-space: nowrap;
  text-align: right;
  /*text-overflow:ellipsis;*/
}

.blue .recordno {
  color: #fff;
}

.itembox.blue p {
  color: #fff;
}

.itembox i {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-top: 33px;
  background: url(../../assets/img/editrecord_greymore.png) no-repeat;
}

.blue.itembox i {
  background: url(../../assets/img/editrecord_bluemore.png) no-repeat;
}

.itembox i.up {
  transform: rotate(180deg);
}

.expand {
  width: 507px;
  clear: both;
  background: #f0f0f0;
  position: relative;
  top: -10px;
  z-index: 9;
  padding-top: 10px;
  display: none;
}

.expand.expandshow {
  display: block;
}

.blue.expand {
  background: #eef7ff;
}

.expanditem {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
}

.expanditem:first-child {
  margin-top: 34px;
}

.expanditem p {
  margin: 0;
  padding: 0;
}

.leftpart {
  width: 118px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  margin-top: 21px;
}


/*.leftpart:first-child {
    margin-top: 55px;
  }*/

.leftpart img {
  width: 30px;
  height: 30px;
}

.leftpart .opname {
  font-size: 14px;
  color: #333;
  line-height: 30px;
}

.leftpart .optime {
  font-size: 12px;
  color: #333;
  /*line-height: 18px;*/
}

.rightpart {
  width: 360px;
  margin-right: 29px;
  background: #fff;
  padding: 0 15px 0 15px;
  margin-bottom: 20px;
  box-sizing: border-box;
  border: 1px solid #f1f1f1;
  position: relative;
}


/*.rightpart:first-child {
    margin-top: 34px;
  }*/

.rightpart:before {
  content: '';
  position: absolute;
  width: 50px;
  height: 24px;
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  border-left: 25px solid transparent;
  border-right: 25px solid #f1f1f1;
  top: 30px;
  left: -50px;
}

.rightpart:after {
  content: '';
  position: absolute;
  width: 48px;
  height: 22px;
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  border-left: 24px solid transparent;
  border-right: 24px solid #fff;
  top: 31px;
  left: -48px;
}

.stretch {
  width: 100%;
  min-height: 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  color: #333;
}

.stretch .text_left {
  font-size: 12px;
  min-width: 80px;
}

.stretch .text_right {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
}

.text_right .righttwo {
  color: #d9544f;
}

.text_right .rightmiddle {
  width: 10px;
  height: 2px;
  border: 1px solid #d9544f;
  position: relative;
  margin-left: 5px;
  margin-right: 8px;
}

.rightmiddle:after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  left: 9px;
  top: -3px;
  border: 3px solid transparent;
  border-left: 3px solid #d9544f;
}

.stretch.righthead {
  height: 46px;
}

.righthead .operation {
  font-size: 16px;
  padding-left: 8px;
  padding-right: 8px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  box-sizing: border-box;
  border: 1px dashed #3b9fff;
}

.operation.one {
  border: 1px dashed #5cb85c;
  color: #5cb85c;
}

.operation.two {
  border: 1px dashed #3b9fff;
  color: #3b9fff;
}

.operation.three {
  border: 1px dashed #da70fd;
  color: #da70fd;
}

.operation.four {
  border: 1px dashed #0a32ff;
  color: #0a32ff;
}

.operation.five {
  border: 1px dashed #f2c202;
  color: #f2c202;
}

.operation.six {
  border: 1px dashed #d9544f;
  color: #d9544f;
}

.operation.seven {
  border: 1px dashed #20d5be;
  color: #20d5be;
}

.operation.eight {
  border: 1px dashed #ff7214;
  color: #ff7214;
}

.operation.nine {
  border: 1px dashed #8dd018;
  color: #8dd018;
}

.operation.ten {
  border: 1px dashed #4c18d0;
  color: #4c18d0;
}

.operation.eleven {
  border: 1px dashed #aaaaaa;
  color: #aaaaaa;
}

.righthead .orderno {
  color: #3b9fff;
  font-size: 12px;
}

.fleft {
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  color: #333;
  font-size: 12px;
}

.fleft .remark {
  color: #d9544f;
}

.text_left .remark {
  color: #d9544f;
}

.lastline {
  width: 100%;
  min-height: 46px;
  padding-top: 8px;
  padding-bottom: 8px;
  box-sizing: border-box;
  border-top: 1px dashed #e5e5e5;
  color: #333;
  font-size: 12px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
}

.lastline .cause {
  color: #666;
}

.lastline p{
  min-width: 80px;
}
</style>
