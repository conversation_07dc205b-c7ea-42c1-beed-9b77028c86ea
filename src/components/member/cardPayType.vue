<template>
    <div>
        <FormItem label="支付方式" prop="new_pay_type" v-for="(type, index) in typeList" :required="type.amount != 0" :rules="{required: type.amount != 0, message: '请选择支付方式'}" :key="index">
            <div :style="styles.alignCenter">
                <PayTypeSelect @on-change="handleTypeChange(index, type.pay_type)"
                               @on-dragonfly-confirm="onDragonflyConfirm(index, $event)"
                               class="paytype-box"
                               :describe="describe"
                               :payedList="payedList"
                               :amount="type.amount || shouldPayAmount"
                               :isEqual="false"
                               v-model="type.pay_type"
                               :userId="payedUserId"
                               :phone="phoneInfo && phoneInfo.phone"
                               :showSQB="from !== '销卡'" 
                               :show-card-pay="index == 0 && ((actionType != 'switch' && actionType != 'edit' && actionType != 'suspend' && actionType != 'seperate') || (actionType === 'edit' && type.pay_type === 8))"
                               :disabled="disabledSelect(type.pay_type) || (servInfo && servInfo.is_online === 1)"/>
                <div :style="styles.amount" v-show="typeList.length > 1">金额
                    <Input @on-change="handleAmountChange(index, $event)" :value="type.amount"
                           :style="styles.inputNumber"
                           :disabled="disabledSelect(type.pay_type) || type.pay_type == 20 || type.pay_type == 21"/>元
                </div>
                <template v-if="typeList[0] && typeList[0].pay_type && from !== '销卡' && typeList[0].pay_type!==8 && (servInfo?servInfo.is_online !== 1 : true)">
                    <Button :style="styles.button" v-if="index == 0" type="text" @click="addPayType">添加支付方式</Button>
                    <Button :style="styles.button" v-else type="text" @click="deletePayType(index)"
                            style="min-width: auto; color: #d8321f">删除
                    </Button>
                </template>
            </div>
        </FormItem>
        <!-- 编辑状态不可修改 -->
        <FormItem label="储值卡支付方式" v-if="typeList[0] && typeList[0].pay_type && typeList[0].pay_type===8 && actionType === 'edit'">
            <div :style="styles.alignCenter">
                {{ pay_card_user.name }}
            </div>
        </FormItem>
        <!-- 其他页面 -->
        <FormItem prop="pay_card_user_id" label="储值卡支付方式"
                  v-if="typeList[0] && typeList[0].pay_type && typeList[0].pay_type===8 && actionType !== 'edit'"
                  :rules="{required: typeList[0].pay_type == 8, message: '请选择支付储值卡'}">
            <div :style="styles.alignCenter">
                <Select :style="styles.select" v-model="payCardUserId" @on-change="updatePayCardUserId">
                    <Option v-for="item in userDebtCard"
                            :key="item.card_user_id"
                            :value="item.card_user_id">{{item.card_name}}&nbsp;&nbsp;剩余￥{{item.amount}}&nbsp;&nbsp;{{
                        item.is_active ? '' : '未激活' }}
                    </Option>
                </Select>
            </div>
        </FormItem>

    </div>
</template>

<script>
  import PayTypeSelect from 'components/form/PayTypeSelect'
  export default {
    name: 'CardPayType',
    props: {
      value: {
        type: Array,
        required: true
      },
      amount: {},
      describe: {
        type: String,
        default: ''
      },
      from: {
        type: String
      },
      actionType: String,
      pay_card_user: {
        type: Object,
      },
      servInfo: {
        type: Object,
        default: null
      },
      toUserId: {
        type: [String, Number]
      },
      phoneInfo: {
        type: Object,
        default:()=> {}
      }
    },
    components: {
      PayTypeSelect
    },
    data() {
      return {
        payedList: [],
        isOpenDebtCardPay: false,//是否开启储值卡支付
        phoneSearchUser: '',
        payCardUserId: 0,
        shouldPayAmount: this.amount || 0,
        payTypes: [],
        userId: this.$route.params.userId,
        userDebtCard: [],
        selectedType: [],
        styles: {
          alignCenter: {
            display: 'flex',
            alignItems: 'center',
            // width: '60%',
            maxWidth: '660px'
          },
          select: {
            // maxWidth: '400px',
            flex: 1
          },
          amount: {
            display: 'flex',
            alignItems: 'center',
            margin: '0 10px'
          },
          inputNumber: {
            margin: '0 10px',
            width: '80px'
          },
          button: {
            width: '100px'
          }
        }
      };
    },
    computed: {
      payedUserId() {
        let value = Number(this.toUserId ? this.toUserId : (this.$route.params.userId || this.$route.query.userId)) || ''
        if(this.phoneInfo && this.phoneInfo.phone) {
          value = this.phoneSearchUser
        } 
        return value
      },
      typeList: {
        get() {
          return this.value;
        },
        set(val) {
          this.changePayIdsShow()
          this.$emit('input', val);
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }
    },
    created() {
      this.getPayedList()
    },
    methods: {
      getPayedList() {
        if(!(this.phoneInfo && this.phoneInfo.phone) && !this.payedUserId) {
          return false
        }
        return this.$service
          .post('/Web/Pay/getPayedList', { user_id: this.payedUserId, serv_type: this.servInfo && this.servInfo.serv_type, serv_id: this.servInfo &&this.servInfo.serv_id,...this.phoneInfo })
          .then(res => {
            if (res.data.errorcode == 0) {
              this.phoneSearchUser = res.data.data.user_id
              this.payedList = res.data.data.list
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      },
      updatePayCardUserId(val) {
        this.$emit("updatePayCard", val)

        let debtAmount = 0;
        this.userDebtCard.forEach(function (item, index, arr) {
          if (val === item.card_user_id) {
            debtAmount = parseFloat(item.amount);
          }
        });

        if (debtAmount < this.amount) {
          this.$Message.error('此储值卡可用余额不足');
          this.payCardUserId = 0;
        }
      },
      async onDragonflyConfirm(index, infoObj) {
        await this.getPayedList()
        this.typeList[index].amount  = infoObj.amount;
        this.typeList[index].pay_order_ids  = infoObj.pay_order_ids;
        this.typeList[index].name = this.$store.getters['pay/getPayNameById'](infoObj.payType);
      },
      changePayIdsShow() {
        let selectedIds = []
        let hasPayAmount = 0
        this.typeList.forEach((item, index)=>{
          if(item.amount) {
            hasPayAmount = (Number(hasPayAmount)+Number(item.amount)).toFixed(2)
          }
          if(item.pay_order_ids && (item.pay_type === 20 || item.pay_type === 21)) {
            selectedIds = selectedIds.concat(item.pay_order_ids)
          } else {
            item.pay_order_ids=[]
          }
        })
        this.shouldPayAmount = (Number(this.amount)-Number(hasPayAmount)).toFixed(2)
        this.payedList.forEach((item, index) => {
          if(selectedIds.indexOf(item.id) !== -1) {
            this.payedList[index].show = false
          } else {
            this.payedList[index].show = true
          }
        });
      },
      disabledSelect(type) {
        return type == '8' && this.actionType === 'edit';
      },
      handleAmountChange(index, ev) {
        let amount = ev.target ? ev.target.value : ev;
        if (Number.isNaN(+amount)) {
          this.$Message.error('请输入数字');
        }
        const arr = [...this.typeList];
        if (this.typeList.length === 2) {
          const total = this.amount;
          if (index === 0 && arr[1].pay_type!=20 && arr[1].pay_type!=21) {
            arr[1].amount = Math.max(total - amount, 0).toFixed(2);
          } else if(index === 1 && arr[0].pay_type!=20 && arr[0].pay_type!=21){
            arr[0].amount = Math.max(total - amount, 0).toFixed(2);
          }
        }
        arr[index].amount = amount;
        this.typeList = arr;
      },
      handleTypeChange(index, type) {
        const arr = [...this.typeList];
        arr[index].pay_type = type;
        arr[index].name = this.$store.getters['pay/getPayNameById'](type);
        if (index === 0 && type === 8) {
          this.typeList.splice(1, this.typeList.length - 1);
          //拿用户储值卡
          this.getUserCard(this.userId)
        } else {
          this.$emit("updatePayCard", '')
          this.typeList = arr;
        }
      },
      getUserCard(user_id) {
        if (!user_id) return;
        const url = 'Web/Commodity/get_user_debit_card';
        this.$service
            .post(url, {user_id: user_id, type: 1})
            .then(res => {
              if (res.data.errorcode === 0) {
                const data = res.data.data;
                this.userDebtCard = data.list;
                if (!data.list.length) {
                  this.$Message.error('该会员没有储值卡');
                  return;
                }
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
      },
      addPayType() {
        this.typeList.push({pay_type: '', amount: 0});
        this.typeList = this.value;
      },
      deletePayType(index) {
        this.typeList.splice(index, 1);
        if (this.typeList.length == 1 && this.typeList[0].pay_type!=20 && this.typeList[0].pay_type!=21) {
          this.typeList[0].amount = this.amount;
        }
        this.typeList = this.value;
      }
    }
  };
</script>
<style lang="less" scoped>
.paytype-box {
  flex: 1;
}
.paytype-box  /deep/ .ivu-select {
  width: 100% !important;
}
</style>
