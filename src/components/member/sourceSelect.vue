<template>
  <div class="sales-select">
    <Select v-model="id"
            @on-change="sourceChanged"
            :placeholder="placeholder"
            clearable
            filterable>
            <slot></slot>
      <Option v-for="item in sourceList"
              :key="item.source_id"
              :label="item.source_name"
              :value="item.source_id">
      </Option>
    </Select>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex'
  export default {
    name: 'souceSelect',
    data() {
      return {
      }
    },
    props: {
      value: {},
      placeholder: {}
    },
    computed: {
      id: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      },
      ...mapState(['sourceList'])
    },
    created() {
      this.getSourceList()
    },
    methods: {
      ...mapActions(['getSourceList']),
      sourceChanged(id) {
        this.$emit('input', id)
      }
    }
  }
</script>
