<template>
  <a :id="id_name" style="color: inherit; width: 0; height: 0; opacity: 0" @click.stop="generate">
  </a>
</template>

<script>
  export default {
    name: 'ExportExcel',
    data() {
      return {
        animate: true,
        animation: ''
      };
    },
    props: {
      stringExport: {
        type: Boolean,
        default: false
      },
      type: {
        type: String,
        default: 'xls'
      },
      meta: {
        type: Array,
        default: function() {
          return [];
        }
      }
    },
    created() {},
    computed: {
      id_name: function() {
        let now = new Date().getTime();
        return 'export_' + now;
      }
    },
    methods: {
      export({ columns, data, filename }) {
        this.columns = columns;
        this.data = data;
        this.filename = filename || '表格导出';
        if (!this.data || (!this.stringExport  && !Array.isArray(this.data))) {
          console.error('export.vue: 没有导出数据');
          return;
        }
        this.$el.click();
      },
      generate() {
        if(this.stringExport) {
          return this.exportXLSByTableString(this.data, `${this.filename}.xls`);
        } else if (this.type == 'csv') {
          return this.exportCSV(this.data, `${this.filename}.csv`, this.columns);
        } else {
          return this.exportXLS(this.data, `${this.filename}.xls`, this.columns);
        }
      },
      generate_excel() {
        this.exportXLS(this.data, this.filename, this.columns);
      },
      jsonToXLSByString(jsonString) {
        // jsonString格式形如 <thead><tr><th rowspan="2">成交方式</th><th colspan="3">总计</th><th colspan="3">新开卡</th></tr><tr><th>订单</th><th>课时数</th><th>电话3</th><th>订单</th><th>课时数</th><th>电话3</th></tr></thead><tbody><tr><td></td><td>555 77 854</td><td>555 77 855</td><td>555 77 854</td><td>555 77 855</td> <td>555 77 854</td><td>555 77 855</td></tr></tbody>
        return `<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>${jsonString}</table></body></html>`;
      },
      jsonToXLS(data, columns) {
        let xlsTemp =
          '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>${table}</table></body></html>';
        let xlsData = '';
        let keys = [];
        xlsData += '<thead>';
        for (let column of columns) {
          keys.push(column.key);
          xlsData += '<th>' + column['title'] + '</th>';
        }
        xlsData += '</thead>';
        xlsData += '<tbody>';
        for (let item of data) {
          // 添加 rowspan
          for (let key of keys) {
            if (Array.isArray(item[key])) {
              item.rowspan = item[key].length;
              break;
            }
          }
          xlsData += '<tr>';
          if (item.rowspan) {
            let rowspan = +item.rowspan;
            const initRowspan = rowspan;
            let stringKeys = [];
            while (rowspan > 0) {
              for (let key of keys) {
                if (Array.isArray(item[key])) {
                  xlsData += `<td style="mso-number-format:'@';">${
                    item[key][initRowspan - rowspan] === undefined ? '' : item[key][initRowspan - rowspan]
                  }</td>`;
                } else if (!stringKeys.includes(key)) {
                  xlsData += `<td style="mso-number-format:'@';" rowspan="${initRowspan}">${
                    item[key] === undefined ? '' : item[key]
                  }</td>`;
                  stringKeys.push(key);
                }
              }
              if (rowspan > 1) {
                xlsData += '</tr><tr>';
              }
              rowspan--;
            }
          } else {
            for (let key of keys) {
              xlsData += `<td style="mso-number-format:'@';">${item[key] === undefined ? '' : item[key]}</td>`;
            }
          }
          xlsData += '</tr>';
        }
        xlsData += '</tbody>';
        return xlsTemp.replace('${table}', xlsData);
      },
      jsonToCSV(data, header) {
        let csvData = '';

        if (header) {
          for (let key in header) {
            csvData += header[key] + ',';
          }
          csvData = csvData.slice(0, csvData.length - 1);
          csvData += '\r\n';
        }
        data.map(function(item) {
          for (let k in item) {
            csvData += item[k] + ',';
          }
          csvData = csvData.slice(0, csvData.length - 1);
          csvData += '\r\n';
        });
        return csvData;
      },
      base64(s) {
        return window.btoa(window.unescape(encodeURIComponent(s)));
      },
      exportXLS(data, fileName, header) {
        let XLSData = 'data:application/vnd.ms-excel;base64,' + this.base64(this.jsonToXLS(data, header));
        this.download(XLSData, fileName);
      },
      exportXLSByTableString(data, fileName) {
        let XLSData = 'data:application/vnd.ms-excel;base64,' + this.base64(this.jsonToXLSByString(data));
        this.download(XLSData, fileName);
      },
      exportCSV(data, fileName, keys) {
        let CSVData = 'data:application/csv;base64,' + this.base64(this.jsonToXLSByString(data, keys));
        this.download(CSVData, fileName);
      },
      base64ToBlob(base64Data) {
        let arr = base64Data.split(',');
        let mime = arr[0].match(/:(.*?);/)[1];
        let bstr = atob(arr[1]);
        let n = bstr.length;
        let u8arr = new Uint8ClampedArray(n);

        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
      },
      download(base64data, fileName) {
        if (window.navigator.msSaveBlob) {
          let blob = this.base64ToBlob(base64data);
          window.navigator.msSaveBlob(blob, fileName);
          return false;
        }
        let a = document.getElementById(this.id_name);
        if (window.URL.createObjectURL) {
          let blob = this.base64ToBlob(base64data);
          let blobUrl = window.URL.createObjectURL(blob);

          a.href = blobUrl;
          a.download = fileName;
          return;
        }
        if (a.download === '') {
          a.href = base64data;
          a.download = fileName;
          return;
        }
      } //end download
    }
  };
</script>
