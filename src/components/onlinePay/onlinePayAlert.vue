<template>
  <div>
    <!--正在扫码-->
    <div v-show="isScanning">
      <input type="number" class="scan-code" @keyup.enter="sendScanCode" v-model="scanCode" id="scanCode">
      <div class="scan-box">
        <div>收款金额为 <span>￥{{ data.amount }}</span> 元</div>
        <img class="scan-img" src="../../assets/img/scan_img.png" alt="">
        <p v-show="isFocus">请将码枪对准条形码进行扫描</p>
        <p v-show="!isFocus">请用鼠标点击<span class="text-warning focus-text">此处</span>后再进行扫码</p>
      </div>
    </div>
    <!--正在支付-->
    <div class="scan-box" v-show="isPaying">
      <div>正在支付中</div>
      <img src="../../assets/img/paying.png" alt="">
      <p>请<span class="text-warning">不要关闭或刷新</span>页面</p>
    </div>
    <!--支付失败-->
    <div class="scan-box" v-show="payFalse">
      <div>{{ falseReason }}</div>
      <img src="../../assets/img/pay_false.png" alt="">
      <p class="pay-again">
        <button @click="isScanning = true" class="pay-again-btn pay-again-hover">重新支付</button>
        <button @click="$emit('closeModal')" class="pay-again-btn cancel-pay">取消</button>
      </p>
    </div>
    <!--支付成功-->
    <div class="scan-box" v-show="paySuccess">
      <img src="../../assets/img/pay_success.png" alt="">
      <div>
        <p class="text-color-666">成功收款 <b>￥</b><span class="text-warning">{{ data.amount }}</span>元
        </p>
        <p class="auto-close"><span>{{ closeAfterTime }}</span>s后消息将自动关闭</p>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        falseReason: '订单失效，用户确认超时',
        isFocus: true,
        closeAfterTime: 5,
        isScanning: false,
        isPaying: false,
        payFalse: false,
        paySuccess: false,
        scanCode: null, // 要提交的扫码枪的数字
        postData: null
      }
    },
    props: ['data', 'show'],
    created() {
      this.isScanning = true;
      this.closeAfterTime = 5;
      this.postData = this.data
    },
    watch: {
      isScanning(val) {
        this.scanCode = ''; // 每次窗口改变时清空扫码框内的码
        
        let _this = this;
        if (val) {
          this.isPaying = this.payFalse = this.paySuccess = false;
          let scanCodeInput = document.querySelector('#scanCode');
          scanCodeInput.focus();
          this.isPaying = this.payFalse = this.paySuccess = false;
          
          document.body.addEventListener('click', function () {
            scanCodeInput.focus();
            _this.isFocus = true
          });
          window.onblur = function () {
            _this.isFocus = false
          }
        }
      },
      isPaying(val) {
        if (val) {
          this.isScanning = this.payFalse = this.paySuccess = false;
          window.onblur = null;
          this.$emit('enterPaying')
        } else {
          this.$emit('leavePaying')
        }
      },
      paySuccess(val) {
        if (val) {
          this.isPaying = this.payFalse = this.isScanning = false;
          let successTimer = setInterval(() => {
            if (!this.show) {
              clearInterval(successTimer);
              if (!this.postData.day) {
                window.history.back();
              } else {
                window.location.reload();
              }
              return
            }
            
            this.closeAfterTime--;
            if (this.closeAfterTime <= 0) {
              clearInterval(successTimer);
              if (!this.postData.day) {
                window.history.back();
              } else {
                window.location.reload();
              }
            }
          }, 1000);
        }
      },
      payFalse(val) {
        if (val) {
          this.isPaying = this.isScanning = this.paySuccess = false;
        }
      }
    },
    methods: {
      // 扫码成功
      sendScanCode() {
        if (!this.scanCode) { // 防止用户空按回车键
          return
        }
        if (this.scanCode.length < 8) {
          this.$Message.error('无效条形码');
          this.scanCode = null;
          return
        }
        this.isPaying = true; // 跳到正在支付
        
        let postData = this.postData;
        postData.auth_code = this.scanCode;
        
        let url = '/Web/Onlinepay/buy_card';
        if (postData.day) {
          url = '/Web/Onlinepay/renew_card';
        }
        this.$service.post(url, postData, {loading: false})
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 47030) {
                this.paySuccess = true;
              } else {
                this.falseReason = response.data.errormsg;
                this.payFalse = true;
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      },
    }
  }
</script>

<style scoped>
  .focus-text {
    cursor: pointer;
    text-decoration: underline;
  }
  
  .scan-code {
    height: 0;
    width: 0;
    outline: none;
    margin: 0;
    padding: 0;
    border: none;
    display: block;
  }
  
  .auto-close {
    font-size: 14px;
  }
  
  .pay-again-btn {
    padding: 3px 10px;
    color: #fff;
    background-color: #5fb75d;
    border-radius: 3px;
    min-width: 60px;
    border: none;
  }
  
  .pay-again-hover:hover {
    opacity: 0.8;
  }
  
  .cancel-pay {
    background-color: #fff;
    color: #5fb75d;
    border: 1px solid #5fb75d;
  }
  
  .cancel-pay:hover {
    background-color: #5fb75d;
    color: #fff;
  }
  
  .pay-again {
    display: flex;
    width: 80%;
    justify-content: space-around;
  }
  
  .text-color-666 {
    color: #666;
  }
  
  .scan-box {
    text-align: center;
    font-size: 18px;
    display: flex;
    margin-top: 20px;
    padding-top: 10px;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 323px;
    color: #999;
  }
  
  .text-warning {
    color: #d9534f;
  }
  
  .scan-box .scan-img {
    width: 163px;
    height: 176px;
  }
  
  .scan-box span {
    font-family: 'Microsoft Yahei';
  }
  
  .scan-box span i {
    font-style: normal;
  }
</style>
