<template>
  <div>
    <div class="code">
      <img :src="payInfo.pay_image_url"
           alt="微信支付二维码">
      <div class="tips">
        <h2 class="money">{{payInfo.amount}}元</h2>
        <p style="padding-bottom: 5px">含微信支付手续费{{payInfo.service_charge}}元</p>
        <p style="padding-bottom: 5px">请用微信扫描二维码支付</p>
        <FaIcon name="weixin"
                size="18"
                color="#62b900"></FaIcon>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'BonusPay',
    data() {
      return {
        stateTimer: null
      }
    },
    props: {
      payInfo: {
        type: Object
      }
    },
    created() {
      this.getPayState();
    },
    beforeDestroy() {
      this.stateTimer && clearTimeout(this.stateTimer)
    },
    methods: {
      getPayState() {
        this.$service
          .post('/Web/Bonus/check_order_status', { order_sn: this.payInfo.order_sn }, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              if(res.data.data.order_status == 0){
                this.stateTimer = setTimeout(() => {
                  this.getPayState(this.payInfo.order_sn)
                }, 2000)
              } else if(res.data.data.order_status == 1) {
                this.stateTimer && clearTimeout(this.stateTimer)
                this.$emit('paySuccess')
              }
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>
<style lang="less" scoped>
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .code {
    display: flex;
    justify-content: center;
    > img {
      width: 200px;
      height: 200px;
    }
    .tips {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 30px;
      text-align: center;
      > h2 {
        font-size: 26px;
        font-weight: normal;
        line-height: 50px;
      }
    }
  }
  .money {
    color: 1px solid #ff6e1e;
  }
</style>

