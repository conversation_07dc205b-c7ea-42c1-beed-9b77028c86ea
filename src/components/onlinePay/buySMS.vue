
<style lang="less" scoped>
  @checkedColor: #ff6e1e;
  @grayBorder: 1px solid #e5e5e5;
  @checkedBorder: 1px solid @checkedColor;
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .bags {
    display: flex;
    width: 100%;
    justify-content: space-between;
    > div {
      width: 226px;
      height: 134px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      border: @grayBorder;
      cursor: pointer;
      user-select: none;
      > h3 {
        .flex-center;
        font-weight: normal;
        width: 100%;
        height: 84px;
        font-size: 32px;
        color: #434343;
      }
      > p {
        flex: 1;
        background-color: #eee;
        width: 100%;
        font-size: 16px;
        display: flex;
        align-items: center;
        color: #313131;
        justify-content: center;
      }
      .time {
        width: 100%;
        background: #eee;
      }
    }
    .checked {
      border: @checkedBorder;
      outline: @checkedBorder;
      h3 {
        color: @checkedColor;
      }
    }
  }
  .code {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    > img {
      width: 200px;
      height: 200px;
    }
    .tips {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 30px;
      text-align: center;
      > h2 {
        font-size: 26px;
        font-weight: normal;
        line-height: 50px;
      }
    }
  }
  .money {
    color: @checkedColor;
  }
</style>

<template>
  <div>
    <div class="bags">
      <div v-for="pack in package"
           @click="getQr(pack.id, pack.amount)"
           :key="pack.id"
           :class="{'checked': pack.id == chooseId}">
        <h3>￥{{pack.amount}}</h3>
        <p v-if="type==1">{{pack.number}}条短信包</p>
        <p v-else>
          {{pack.number}}份合同券
          <!-- <div class="time">有效期12个月</div> -->
        </p>
      </div>
    </div>
    <div class="code">
      <img :src="qrCode"
           alt="微信支付二维码">
      <div class="tips">
        <h2 class="money">{{money}}元</h2>
        <p style="padding-bottom: 5px">请用微信扫描二维码支付</p>
        <FaIcon name="weixin"
                size="18"
                color="#62b900"></FaIcon>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'buySMS',
    props: {
      type: {
        type: String,
        default: '1' //1 短信 2电子合同
      }
    },
    watch: {
      
    },
    data() {
      return {
        chooseId: '',
        money: '',
        package: [],
        qrCode: '',
        stateTimer: null
      }
    },
    created() {
      this.getSmsPackage()
    },
    beforeDestroy() {
      clearTimeout(this.stateTimer)
    },
    methods: {
      getSmsPackage() {
        this.$service
          .post('/MsgCenter/admin/getSmsPacketList', { type: this.type })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.getQr(data[0].id, data[0].amount)
              this.package = data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getQr(sms_package_id, amount) {
        this.chooseId = sms_package_id
        this.money = amount
        clearTimeout(this.stateTimer)
        const url = '/MsgCenter/admin/getBuySmsQrCode'
        this.$service
          .post(url, { sms_package_id, type: this.type })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              this.qrCode = data.image_string
              this.getPayState(data.order_sn)
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      },
      getPayState(order_sn) {
        const url = '/MsgCenter/Admin/getSmsPayStatus'
        this.$service
          .post(url, { order_sn }, { loading: false })
          .then(res => {
            if (res.data.errorcode === 47031) {
              this.stateTimer = setTimeout(() => {
                this.getPayState(order_sn)
              }, 2000)
            } else if (res.data.errorcode === 0) {
              this.$Message.success(res.data.errormsg)
              this.$emit('paySuccess')
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
          .catch(err => {
            console.error(err)
          })
      }
    }
  }
</script>
