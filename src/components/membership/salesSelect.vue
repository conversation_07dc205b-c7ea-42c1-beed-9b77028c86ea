<template>
  <Select v-model="saleId" v-if="showSelect" @on-change="saleChanged" :placeholder="placeholder"
          :label-in-value="labelInValue" clearable transfer filterable :disabled="this.disabled" :multiple="this.multiple" class="sales-select">
    <slot></slot>
    <Option v-for="item in salesList" :key="item.marketers_id" :label="item.sale_name" :value="item.marketers_id">
      <span>{{item.sale_name}}</span>
      <span v-if="item.isCoach && showCoachType" style="float:right;color:#ccc">{{item.isSwimCoach?'泳教':'私教'}}</span>
    </Option>
  </Select>
</template>

<script>
  import { mapState, mapActions } from 'vuex';

  export default {
    name: 'salesSelect',
    data() {
      return {
        salesList: [],
        showSelect: false,
        curBelongBusId: ''
      };
    },
    props: {
      value: {
        type: [String, Number, Array]
      },
      placeholder: {
        type: String,
        default: ''
      },
      showCoachType: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      multiple: {
        type: Boolean,
        default: false
      },
      labelInValue: {
        type: Boolean,
        default: false
      },
      isCoach: {
        type: Boolean,
        default: false
      },
      isPtCoach: {
        type: Boolean,
        default: false
      },
      isSwimCoach: {
        type: Boolean,
        default: false
      },
      isMembership: {
        type: Boolean,
        default: true
      },
      belongBusId: {
        // 会员的归属场馆
        type: [String, Number],
        default: ''
      }
    },
    computed: {
      saleId: {
        get() {
          return this.value;
        },
        set(val) {
          // this.$emit('input', val);
        }
      },
      ...mapState(['coachList', 'membershipList', 'ptCoachList', 'swimCoachList', 'globalBelongBusId', 'busId'])
    },
    watch: {
      belongBusId(val, oldVal) {
        if (val !== oldVal) {
          this.initData();
        }
      },
      isPtCoach() {
        this.initData();
      },
      isSwimCoach() {
        this.initData();
      },
      isMembership() {
        this.initData();
      },
      isCoach() {
        this.initData();
      }
    },
    created() {
      this.initData();
    },
    methods: {
      ...mapActions(['getCoachList', 'getMembershipList', 'getPtCoachList']),
      saleChanged(saleId) {
        if (Array.isArray(saleId) && saleId.length > 5) {
          this.$Message.error('最多只能选择5个跟进教练！');
          saleId.pop();
          return false;
        }
        if (saleId && this.labelInValue) {
          this.$emit('input', saleId.value);
          this.$emit('saleName', saleId.label);
          this.$emit('on-change', { value: saleId.value, label: saleId.label });
        } else {
          this.$emit('input', saleId);
          this.$emit('on-change', saleId);
        }
      },
      async initData() {
        const busId = this.belongBusId || this.busId
        this.salesList = [];
        if (this.isMembership) {
          this.shouldUpdateList(this.membershipList) && await this.getMembershipList(busId);
          this.salesList = this.membershipList.concat(this.salesList);
          this.$nextTick(() => {
            if (!this.isCoach && !this.isPtCoach && !this.isSwimCoach) {
              this.showSelect = true;
            }
          });
        }
        if (this.isCoach) {
          this.shouldUpdateList(this.coachList) && await this.getCoachList(busId);
        }
        //教练包括操课 泳教  私教
        if (this.isCoach && !this.isPtCoach && !this.isSwimCoach) {
          this.coachSet(this.coachList)
        } else if(this.isPtCoach || this.isSwimCoach){
          if ((this.isPtCoach && this.shouldUpdateList(this.ptCoachList)) || (this.isSwimCoach && this.shouldUpdateList(this.swimCoachList))) {
            await this.getPtCoachList(busId)
          }
          this.isPtCoach && this.coachSet(this.ptCoachList)
          this.isSwimCoach && this.coachSet(this.swimCoachList)
        }
        this.$store.commit('SET_GLOBAL_BELONG_BUS_ID', busId)
      },
      coachSet(list) {
        const curCoachList = list.map(coach => {
          return {
            marketers_id: this.isMembership
                          ? 'c' + coach.coach_id
                          : coach.coach_id,
            sale_name: coach.coach_name,
            isCoach: true,
            isSwimCoach: coach.is_swim == 1
          };
        });
        this.salesList = this.salesList.concat(curCoachList);
        this.$nextTick(() => {
          this.showSelect = true;
        });
      },
      shouldUpdateList(list) {
        return !(this.belongBusId === this.globalBelongBusId && Array.isArray(list) && list.length !== 0)
      }
    }
  };
</script>
