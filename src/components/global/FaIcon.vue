<template>
  <i :class="className" @click="$emit('click')"
     :style="{color: color, fontSize: fontSize}">
  </i>
</template>

<script>
  export default {
    name: 'faIcon',
    data() {
      return {}
    },
    props: {
      name: {},
      size: {
        default: 18
      },
      color: {}
    },
    computed: {
      className() {
        return `fa fa-${this.name}`
      },
      fontSize() {
        return `${this.size}px`
      }
    }
  }
</script>

<style scoped>

</style>
