<template>
  <div class="nfc-reader" v-if="useNFC">
    <div class="dot" :style="{ backgroundColor: isComOpen ? '#19be6b' : 'red' }"></div>
    NFC
  </div>
</template>

<script>
  import Reader, { READER_TYPE, FUNCIDS } from './reader.js';
  export default {
    name: 'cardReader',
    data() {
      return {
        cardReader: null,
        icdev: -1,
        isComOpen: false,
        gl_wantFunc: 0,
        hasCard: false,
        gl_fFailedShow: 0,
        useNFC: false
      };
    },
    created() {
      this.useNFC = this.$store.state.adminInfo.nfc_switch === '1';
      if (!this.useNFC) {
        return false;
      }

      Reader.createSocket();

      this.cardReader = Reader.getOBJ(READER_TYPE._reader_type_contactLess);

      this.cardReader.onResult(rData => {
        switch (rData.FunctionID) {
          case FUNCIDS._fid_adaptReader:
            // alert('Server is ready, you can start now!');
            this.Connect();
            break;
          case FUNCIDS._fid_initialcom:
            {
              var opst;
              var rel = rData.RePara_Int;
              if (0 == rel) {
                var hdev = parseInt(rData.RePara_Str);
                if (hdev != -1) {
                  this.icdev = hdev;

                  if (this.isComOpen == true) break;
                  this.isComOpen = true; //Set reader link status
                  this.gl_fFailedShow = 0; //reset the flag whether show fail message or not

                  // this.$Notice.success({ title: '读卡器连接成功', desc: '请将卡放置在读卡器中心区域' });
                  this.findCard();
                } else {
                  if (0 == this.gl_fFailedShow) {
                    this.gl_fFailedShow = 1; //set the flag whether show fail message or not
                    // this.$Notice.error({
                    //   title: '读卡器连接失败',
                    //   duration: 3
                    // });
                  }
                  this.isComOpen = false; //Set reader link failed status
                  this.reLink = setTimeout(this.Connect, 1000);
                }
              } else {
                // msg.value = 'Object load error\n';
              }
            }
            break;
          case FUNCIDS._fid_exit:
            // msg.value = msg.value + 'reader closed\n';
            break;
          case FUNCIDS._fid_beep:
            this.findCard();
            break;
          case FUNCIDS._fid_halt:
            this.cardReader.beep(this.icdev, 10);
            break;
          case FUNCIDS._fid_findCardStr:
            {
              var strcard = rData.RePara_Str;
              if (strcard != '') {
                this.hasCard = true;
                this.$emit('on-change', strcard);
                this.cardReader.halt(this.icdev);
              } else {
                if (0 == this.gl_fFailedShow) {
                  this.gl_fFailedShow = 1; //set the flag whether show fail message or not
                }

                this.hasCard = false; //Set no card status
                setTimeout(() => {
                  this.findCard();
                }, 500);
              }
            }
            break;
          case 'error':
            this.$Notice.error({
              title: '读卡器连接失败',
              desc: '请先运行 WebsocketServer, 或重新安装读卡器驱动',
              duration: 10
            });
        }
      });
    },
    beforeDestroy() {
      if (!this.useNFC) {
        return false;
      }
      this.Disconnect();
      this.cardReader.Disconnect();
      clearTimeout(this.reLink);
    },
    methods: {
      Connect() {
        try {
          if (this.isComOpen == false) {
            //if reader link failed
            // this.$Notice.info({ title: '读卡器连接中...' });
            this.cardReader.initialcom(100, 115200);
          }
        } catch (e) {
          alert(e.message);
        }
      },
      findCard() {
        this.cardReader.findcardStr(this.icdev, 0); //1);     //1: multy card mode
        this.gl_wantFunc = 0;
      },
      Disconnect() {
        this.cardReader.exit(this.icdev);
        this.isComOpen = false; //Set unlink status
      }
    }
  };
</script>

<style lang="less" scoped>
  .nfc-reader {
    display: flex;
    align-items: center;
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: red;
      margin-right: 5px;
    }
  }
</style>
