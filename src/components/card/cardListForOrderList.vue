<template>
  <Select v-if="cardList && cardList.length" v-model="cardId" v-bind="$attrs" :multiple="multiple" filterable>
    <Option v-for="card in cardList" :key="card.card_id" :value="card.card_id" :label="card.card_name">{{card.card_name}}</Option>
    <template v-if="showPackage">
      <Option v-for="pack in packageCardList" :key="pack.card_id" :value="pack.card_id" :label="pack.card_name">{{pack.card_name}}</Option>
    </template>
    
  </Select>
</template>

<script>
  export default {
    name: 'cardListForOrderList',
    data() {
      return {
        packageCardList: [],
        addCardList: []
      }
    },
    props: {
      value: {},
      // 只显示在售的卡
      onlySale: {
        type: Boolean,
        default: false
      },
      showPackage: {
        type: Boolean,
        default: false
      },
      // 排除的卡类型, 如: 不显示私教卡和储值卡 ['4', '3']
      exceptType: {
        type: Array,
        default: () => ([])
      },
      // 刷新卡列表
      refresh: {
        type: Boolean,
        default: false
      },
      multiple: {
        type: Boolean,
        default: false
      },
      // 将整个 card 返回
      returnCardInfo: {
        type: Boolean,
        default: false
      },
      busId: {
        type: String,
        default: ''
      }
    },
    watch: {
      busId(newValue, oldValue) {
        if (newValue !== '' && newValue !== oldValue) {
          this.cardId = ''
          this.getAddCardList(this.busId)
          if (this.showPackage) {
            this.getPackageLsit()
          }
        }
      }
    },
    created() {
      // if (!this.addCardList || this.refresh) {
      this.getAddCardList(this.busId)
      // }
      if(this.showPackage) {
         this.getPackageLsit()
      }
    },
    computed: {
      cardList() {
        if (this.addCardList && this.addCardList.length) {
          let list = this.addCardList
          if (this.onlySale) {
            list = list.filter(item => item.sale_status === '1');
          }
          if (this.exceptType.length) {
            list = list.filter(item => !this.exceptType.includes(item.card_type_id))
          }
          return list
        } else {
          return [];
        }
      },
      cardId: {
        get() {
          return this.value;
        },
        set(val) {
          if (this.returnCardInfo) {
            const card = []
            let allCard = this.addCardList
            if(this.showPackage) {
              allCard = allCard.concat(this.packageCardList)
            }
            allCard.forEach(item => {
              if (Array.isArray(val)?val.indexOf(item.card_id)!== -1 : val === item.card_id) {
                card.push(item)
              }
            });
            if (!card) {
              this.$emit('on-change', { card_id: '', card_name: '' });
            } else {
              this.$emit('on-change', card);
            }
          } else {
            this.$emit('on-change', val);
            this.$emit('input', val);
          }
        }
      }
    },
    methods: {
      getAddCardList() {
        this.$service.get('/Web/Member/get_card_all?bus_id=' + (this.busId || '')).then(res => {
          if (res.data.errorcode === 0) {
            this.addCardList = res.data.data
          }
        })
      },
      getPackageLsit() {
        this.$service
          .get('/Web/Member/get_package?belong_bus_id='+(this.busId||''))
          .then(res => {
            if (res.data.errorcode === 0) {
              this.packageCardList = res.data.data.map((item)=>{
                return {
                  card_id: item.id,
                  card_name: item.name
                }
              });
            } else {
              this.packageCardList = [];
            }
          })
      }
    }
  };
</script>

<style scoped>
</style>
