<template>
    <div class="home">
        <div id="mapContainer" class="map-container" ref="mapContainer"></div>
    </div>
</template>
<script>
import axios from 'axios'
export default {
  name: 'tian-di-tu',
  components: {},
  props: {
    // 经纬度
    center: {
      type: Array,
      required: true,
      default: function() {
        return [0, 0]
      }
    },
    // 缩放比例
    zoom: {
      type: Number,
      required: true,
      default: 18
    },
    // 搜索关键字
    search: {
      type: String,
      default: ''
    },
    // 是否需要添加圆 0 不添加 反之则为圆的半径
    circleNumber: {
      type: Number,
      default: 0
    }
  },
  watch: {
    center(val, oldVal) {
      if (val[0] != oldVal[0] && val[1] != oldVal[1] && (val[0] != val[1]) != 0) {
        this.setCenterAndZoom(val[0], val[1])
      }
    },
    search(val, oldVal) {
      if (val != oldVal) {
        this.getBy<PERSON>ey<PERSON>ords(val)
      }
    },
    circleNumber(val, oldVal) {
      if (val != oldVal && val != 0 && !this.first) {
        this.createCircle(this.center[0], this.center[1])
      }
    }
  },
  data() {
    return {
      tk: '6f7906581aad48c74344f0ab41566d51',
      map: '',
      control: '',
      marker: '',
      circle: '',
      geocode: '',
      first: true
    }
  },
  mounted() {
    this.drawMap(this.center[0] != 0 ? this.center : [0, 0])
  },
  methods: {
    loadTianditu() {
      return new Promise(resolve => {
        const script = document.createElement("script");
        script.type = "text/JavaScript";
        //   引入天地图JS文件 CDN
        //   v: 版本, 最新4.0
        //   tk: 秘钥， 目前是个人秘钥，配额较少
        //   需要提前设置白名单
        //   个人秘钥: 6f7906581aad48c74344f0ab41566d51
        //   GCJ-02坐标系转WGS84地理坐标系 高德地图是GCJ-02 天地图是WGS84
        //   安装对应module: npm install gcoord --save
        script.src = 'https://api.tianditu.gov.cn/api?v=4.0&tk=6f7906581aad48c74344f0ab41566d51';
        if (script.addEventListener) {
          script.addEventListener("load", () => resolve(true), false);
        }
        document.head.appendChild(script);
      })
    },
    waitWithTimeout(promise, timeout) {
        let timer;
        const timeoutPromise = new Promise((_, reject) => {
            timer = setTimeout(() => reject('地图加载超时，可刷新重新加载！'), timeout);
        }); 
        return Promise.race([timeoutPromise, promise])
            .finally(() => clearTimeout(timer));
    },
    // 初始化地图对象
    init() {
      // 30秒没加载完成则不再进行后续操作
      return this.waitWithTimeout(this.loadTianditu(), 1000 * 30);
    },
    // 绘制地图
    drawMap(center) {
      this.init().then(() => {
        this.map = new T.Map(this.$refs.mapContainer)
        this.map.centerAndZoom(new T.LngLat(center[0], center[1]), this.zoom)

        //创建缩放平移控件对象
        this.control = new T.Control.Zoom()
        let controlPosition = T_ANCHOR_BOTTOM_LEFT
        this.control.setPosition(controlPosition)
        //添加缩放平移控件
        this.map.addControl(this.control)

        // 更改地图中心点
        this.setCenterAndZoom(center[0], center[1])
      }).catch(err => {
        this.$Message.error(err)
      })
    },
    // 更改地图中心点
    setCenterAndZoom(lng, lat) {
      if(!this.map) {
        return false;
      }
      this.map.centerAndZoom(new T.LngLat(lng, lat), this.first ? this.zoom : this.map.getZoom())
      if (lng != 0) {
        this.first = false
      }
      this.addOverlay(lng, lat)
    },
    // 向地图实例添加标注
    addOverlay(lng, lat) {
      if(!this.map) {
        return false;
      }
        this.map.clearOverLays()
        //创建标注对象
        this.marker = new T.Marker(new T.LngLat(lng, lat))
        // 调用圆标注事件
        this.createCircle(lng, lat)
        //向地图上添加标注
        this.map.addOverLay(this.marker)
        // 开启拖拽
        this.marker.enableDragging()
        //创建逆地理编码对象
        this.geocode = new T.Geocoder()
        // 开启事件监听
        this.marker.addEventListener('dragend', (e)=> {
          let nextLng = e.lnglat.lng //经度
          let nextLat = e.lnglat.lat //纬度
          this.geocode.getLocation(e.lnglat, result => this.searchResult(result, e.lnglat))
          this.$emit('changeCenter', [nextLng, nextLat])
          this.createCircle(nextLng, nextLat)
        })
    },
    // 圆标注事件
    createCircle(lng, lat) {
      if(!this.map) {
        return false;
      }
      this.map.removeOverLay(this.circle)
      // 定义该矩形的显示区域
      this.circle = new T.Circle(new T.LngLat(lng, lat), Number(this.circleNumber), {
        color: '#A7CEEE',
        weight: 5,
        opacity: 0,
        fillColor: '#A7CEEE',
        fillOpacity: 0.5,
        lineStyle: 'solid'
      })
      //向地图上添加圆
      this.map.addOverLay(this.circle)
    },
    // 逆编码解析
    searchResult(result, lnglat) {
      if (result.getStatus() == 0) {
        let addressComponent = result.getAddressComponent()
        let word = result.getAddress()
        this.$emit('changeCenterWord', { result: addressComponent.poi, lnglat: lnglat })
      }
    },
    // 根据选择的地址查询经纬度
    getByKeyWords(search) {
      let url = `https://api.tianditu.gov.cn/geocoder?ds={"keyWord":"${search}"}&tk=${this.tk}`
      axios.get(url).then(res => {
        if (res.status == 200) {
          let lng = res.data.location.lon
          let lat = res.data.location.lat
          this.$emit('changeCenter', [lng, lat])
        } else {
          console.log(res)
        }
      })
    }
  }
}
</script>

<style lang="less">
.map-container {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
.home {
  width: 100%;
  height: 100%;
  .search-container {
    width: 100%;
    position: relative;
    margin-bottom: 20px;
    .list-container {
      width: 100%;
      position: absolute;
      top: 33px;
      left: 0;
      z-index: 999;
      background: rgba(#fff, 0.9);
      border: 1px solid #f5f7f9;
      div {
        padding: 0 8px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        align-items: center;
        cursor: pointer;
        img {
          width: 12px;
          margin-right: 8px;
        }
        span {
          font-size: 14px;
          color: #404040;
        }
        span + span {
          font-size: 12px;
          color: #999;
          margin-left: 8px;
        }
      }
      // & > div:nth-child(even) {
      //   background: #f5f7f9;
      // }
      div:hover {
        background: #eee;
      }
    }
  }
}
</style>
