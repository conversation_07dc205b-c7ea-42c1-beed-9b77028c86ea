<template>
  <div id="lbs-qq" class="map-container">

  </div>
</template>

<script setup>
import { onMounted, watch, defineProps } from 'vue';

const emit = defineEmits(['emitCenter', 'emitCenterWord'])

const props = defineProps({
  center: {
    type: Array,
    required: true,
  },
  zoom: {
    type: Number,
    required: true
  },
  search: {
    type: String,
    default: ''
  },
  circleNumber: {
    type: Number,
    default: 0
  }
})

// variables
let map = null
let geo = null
let marker = null
let geometries = null
let editor = null
let circle = null

// methods
const createMarker = (center) => {
  // 标注
  marker = new TMap.MultiMarker({
    map: map,
    geometries: [{ //点标注数据数组
      "position": center,
    }]
  });
  geometries = marker.getGeometries();

  // 初始化几何图形及编辑器
  editor = new TMap.tools.GeometryEditor({
    map, // 编辑器绑定的地图对象
    overlayList: [{
      overlay: marker, // 可编辑图层
      id: "marker",
      selectedStyleId: "highlight"  // 被选中的marker会变为高亮样式
    }],
    actionMode: TMap.tools.constants.EDITOR_ACTION.INTERACT, // 编辑器的工作模式
    activeOverlayId: "marker", // 激活图层
    selectable: true,
    snappable: true,
  });

  const { overlay } = editor.getOverlayList()[0];
  const ids = overlay.geometries.map(g => g.id)
  editor.select(ids);

  editor.on('adjust_complete', (event) => {
    const { lat, lng } = event.position;
    const center = new TMap.LatLng(lat, lng);
    map.panTo(center);
    updateRadar(center);
    geo.getAddress({
      location: center
    }).then(res => {
      const lat = res.result.location.lat;
      const lng = res.result.location.lng;
      emit('emitCenterWord', {
        result: res.result.address,
        lnglat: {
          lat,
          lng
        }
      })
      emit('emitCenter', [lat, lng])
    })
  })
}

const createRadar = (center) => {
  if (props.circleNumber === 0) {
    return
  }

  circle = new TMap.MultiCircle({
    map,
    styles: { // 设置圆形样式
      'circle': new TMap.CircleStyle({
        'color': 'rgba(41,91,255,0.16)',
        'showBorder': true,
        'borderColor': 'rgba(41,91,255,1)',
        'borderWidth': 2,
      }),
    },
    geometries: [{
      styleId: 'circle',
      center,
      radius: props.circleNumber,
    }],
  });
}

const updateRadar = (center, radius) => {
  circle && circle.setGeometries([{
    styleId: 'circle',
    center,
    radius: radius || props.circleNumber,
  }])
}

const initMap = () => {
  if (!window.TMap) {
    setTimeout(initMap, 1000)
    return false
  }

  console.log('初始化地图了噢~');
  //定义地图中心点坐标
  const center = new TMap.LatLng(...props.center);
  //定义map变量，调用 TMap.Map() 构造函数创建地图
  map = new TMap.Map(document.getElementById('lbs-qq'), {
    center,             //设置地图中心点坐标
    zoom: props.zoom,   //设置地图缩放级别
  });

  // 逆地址解析
  geo = new TMap.service.Geocoder()

  // 创建标记
  createMarker(center)

  // 绘制雷达扫描区域
  createRadar(center)
}

watch(() => props.center, (val, oldVal) => {
  if (map && val[0] != oldVal[0] && val[1] != oldVal[1] && (val[0] != val[1]) != 0) {
    const center = new TMap.LatLng(val[0], val[1]);
    map.setCenter(center);
    if (geometries.length > 0) {
      geometries[0].position = center;
    }
    marker.updateGeometries(geometries);

    // 选中标记
    const { overlay } = editor.getOverlayList()[0];
    const ids = overlay.geometries.map(g => g.id)
    editor.select(ids);
  }
})

watch(() => props.zoom, (val, oldVal) => {
  if (map && val != oldVal) {
    map.setZoom(val);
  }
})

watch(() => props.search, (val, oldVal) => {
  if (map && val != oldVal) {
    console.log('搜索关键字', val);
    geo.getLocation({
      address: val,
    }).then(res => {
      const { lat, lng } = res.result.location;
      const center = new TMap.LatLng(lat, lng);
      map.panTo(center);
      emit('emitCenter', [lat, lng])
    }).catch(err => {
      console.log('搜索失败: ', err)
    })
  }
})

watch(() => props.circleNumber, (val, oldVal) => {
  if (map && val != oldVal && val != 0) {
    const center = new TMap.LatLng(...props.center);
    updateRadar(center, val)
  }
})

onMounted(() => {
  initMap()
})
</script>

<style lang="less" scoped>
.map-container {
  /*地图(容器)显示大小*/
  width: 100%;
  height: 100%;
  user-select: none;
}
</style>