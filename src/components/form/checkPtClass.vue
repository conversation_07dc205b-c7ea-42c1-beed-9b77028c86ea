<template>
  <div class="check-pt-container">
    <template v-if="treeList.length">
      <!-- 筛选 -->
      <div class="filter-input-row">
        <Input
          v-model="filterText"
          clearable
          :placeholder="placeholder"
          @on-change="handleFilter"
        />
      </div>

      <!-- 全选 -->
      <div
        v-if="showCheckAll"
        class="check-tree-box"
        :class="{ 'disabled': filterText }"
        @click="handleCheckAll">
        <i class="check-tree-box-inner" :class="{ 'checked': isCheckAll }" />
        <span class="check-tree-box-content text_overflow">全选</span>
      </div>

      <div class="check-tree-wrapper" :style="{ 'max-height': height }">
        <!-- 多选树 -->
        <CheckPtClassTree
          :treeList="treeList"
          @node-check="handleNodeCheck"
        />
      </div>

      <div v-show="filterText && !isFound" class="not-data">没有找到匹配项</div>
    </template>
    <div v-else class="not-data">暂无可选数据</div>
  </div>
</template>

<script>
  import CheckPtClassTree from './checkPtClassTree';

  let searchFilterTimer = null;

  export default {
    name: 'CheckPtClass',
    components: {
      CheckPtClassTree
    },
    props: {
      treeList: { // 需处理成固定结构传入
        type: Array,
        default: () => [
          /* {
            name: '测试展示 多层分类',
            id: '1',
            show: true, // 用于筛选显示
            check: false, // 用于判断勾选
            disabled: false, // 用于禁用勾选
            children: [ // 没有则为最低子级
              {
                name: '次级 - 私教课',
                id: '2',
                parentId: '1', // 用于判断是否勾选父级，没有则为最高父级
                show: true,
                check: false,
                disabled: false,
                children: [
                  {
                    name: '私教课1',
                    id: '3',
                    parentId: '2',
                    show: true,
                    check: false,
                    disabled: false,
                  },
                  {
                    name: '私教课2',
                    id: '4',
                    parentId: '2',
                    show: true,
                    check: false,
                    disabled: false,
                  },
                ]
              },
              {
                name: '次级 - 泳教课',
                id: '5',
                show: true,
                check: false,
                disabled: false,
                children: [
                  {
                    name: '泳教课1',
                    id: '6',
                    parentId: '5',
                    show: true,
                    check: false,
                    disabled: false,
                  },
                  {
                    name: '泳教课2',
                    id: '7',
                    parentId: '5',
                    show: true,
                    check: false,
                    disabled: false,
                  },
                ]
              }
            ]
          }, */

          /* {
            name: '测试展示 单层分类',
            id: '8',
            show: true,
            check: false,
            disabled: false,
            children: [
              {
                name: '私教课1',
                id: '9',
                show: true,
                check: false,
                disabled: false,
              },
              {
                name: '泳教课1',
                id: '10',
                show: true,
                check: false,
                disabled: false,
              }
            ]
          }, */
        ]
      },
      height: {
        type: String,
        default: '400px'
      },
      placeholder: {
        type: String,
        default: '搜索名称'
      },
      showCheckAll: { // 是否显示全选
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        filterText: '',
        isCheckAll: false, // 是否全选
        isFound: true // 筛选是否有找到
      }
    },

    methods: {
      handleCheckAll() {
        if (this.filterText) return 'disabled';

        const isCheck = !this.isCheckAll;
        this.isCheckAll = isCheck;
        this.treeList.forEach(node => {
          this.handleCheckNode(node, isCheck)
        })
      },
      handleCheckNode(node, isCheck) {
        node.check = isCheck;
        node.children && node.children.forEach(child => {
          this.handleCheckNode(child, isCheck)
        });
      },
      handleNodeCheck(node) {
        const isCheckAll = this.treeList.every(v => v.check)
        this.isCheckAll = isCheckAll;
      },

      handleFilter() {
        clearTimeout(searchFilterTimer)

        searchFilterTimer = setTimeout(() => {
          const reg = RegExp(this.filterText, 'i');

          this.treeList.forEach(node => {
            // node.children && node.children.forEach(v => {
            //   v.show = reg.test(v.name);
            // })
            // node.show = node.children.some(v => v.show) || reg.test(node.name);
            // node.show = node.children.some(v => v.show);

            node.show = this.handleShowNode(node, reg)
          });

          this.isFound = this.treeList.some(v => v.show);
        }, 500);
      },

      handleShowNode(node, reg) {
        if (node.children) {
          node.disabled = this.filterText !== '';
          node.children.forEach(child => this.handleShowNode(child, reg))

          const isShow = node.children.some(v => v.show);
          node.show = isShow;
          return isShow;
        } else {
          const isShow = reg.test(node.name)
          node.show = isShow;
          return isShow;
        }
      },
    },
  }
</script>

<style lang="less" scoped>
.check-pt-container {
  width: 100%;

  .filter-input-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /deep/.ivu-btn {
      margin-left: 12px;
    }
  }

  .check-tree-wrapper {
    overflow-y: auto;
    // border-top: 1px solid #f2f2f2;
  }
}

/* check-tree组件样式 */
/deep/.check-tree-container {
  width: 100%;
}

/deep/.check-tree-node {
  // overflow: hidden;
  margin-bottom: 6px;
  // padding: 6px 0;
  // height: auto;
  // height: 47px;
  // transition: all 1s;
  border-top: 1px solid #f2f2f2;
}

/deep/.check-tree-item {
  display: inline-block;
  width: 45%;
  .check-tree-box {
    width: 100%;
  }
}

/deep/.check-tree-children {
  display: flex;
  flex-wrap: wrap;
  padding-left: 20px;
  // padding-bottom: 8px;
  width: 100%;
  // border-top: 1px solid #ccc;
  // border-bottom: 1px solid #ccc;
}

/deep/.check-tree-box {
  display: flex;
  align-items: center;
  margin: 6px 10px 6px 0;
  padding: 4px 0 4px 5px;
  width: 45%;
  line-height: 1.5;
  border-radius: 3px;
  transition: background-color .1s;
  cursor: pointer;

  .check-tree-box-inner {
    position: relative;
    display: inline-block;
    margin-right: 4px;
    min-width: 14px;
    min-height: 14px;
    border: 1px solid #dcdee2;
    border-radius: 2px;
    background-color: #fff;
    transition: border-color .2s ease-in-out, background-color .2s ease-in-out;
    &::after {
      content: "";
      display: table;
      width: 4px;
      height: 8px;
      position: absolute;
      top: 1px;
      left: 4px;
      border: 2px solid #fff;
      border-top: 0;
      border-left: 0;
      transform: rotate(45deg);
      transition: opacity .2s ease-in-out;
      opacity: 0;
      box-sizing: border-box;
    }

    &.checked {
      border-color: #2d8cf0;
      background-color: #2d8cf0;
      &::after {
        opacity: 1;
      }
    }
  }

  &:hover {
    background-color: #f8f8f9;
    .check-tree-box-inner {
      border-color: #bcbcbc;
    }
  }

  &.disabled {
    color: #ccc;
    cursor: not-allowed;
    .check-tree-box-inner {
      background-color: #f3f3f3;
      border-color: #dcdee2;
      &::after {
        animation-name: none;
        border-color: #ccc;
      }
    }
  }

  .check-tree-box-content {
    font-size: 14px;
  }
}

.not-data {
  text-align: center;
  font-size: 14px;
  color: #898989;
}

</style>
