<template>
  <div class="cropper">
    <slot name="result" :onCropping="!!imgSrc">
      <img :src="img" v-if="img && !imgSrc" class="upload-image">
    </slot>
      <div v-show="imgSrc && !cropImg" :style="{width, height, marginBottom: '10px'}">
        <VueCropper ref="cropper" :src="imgSrc" :aspectRatio="ratio" :view-mode="config.viewMode" :auto-crop-area="1" :min-container-width="100" :min-container-height="100" :container-style="{ width, height }" />
      </div>
      <div class="button">
        <slot name="picker" :onChange="setImage" :imgSelected="!!imgSrc">
          <label class="upload-btn" v-if="!imgSrc">选择本地图片
            <input ref="uploadInputRef" type="file" name="image" accept="image/*" style="position:absolute; clip:rect(0 0 0 0)" @change="setImage" />
        </label>
        </slot>
        <slot name="save" :save="doUpload" :cancel="cancelUpload" v-if="imgSrc && !cropImg">
          <Button type="success" @click="doUpload">保存图片</Button>
          <Button @click="cancelUpload" style="margin-left: 30px">取消</Button>
        </slot>
      </div>
  </div>
</template>
<script>
  import VueCropper from 'vue-cropperjs';
  export default {
    name: 'cropperPlus',
    components: {
      VueCropper
    },
    props: {
      value: {},
      width: {
        type: String,
        default: '500px'
      },
      height: {
        type: String,
        default: '300px'
      },
      outputWidth: { type: Number, default: 0 }, // 输出图片宽度, 默认 0 不限制
      outputHeight: { type: Number, default: 0 }, // 输出图片高度, 如果不传默认为: 输出宽度 * 比例
      outputType: { type: String, default: 'png' }, // 输出图片格式, 默认为 png, 可选: jpeg, webp 等, 注: 上传后服务器返回的都是 png 格式, 但是 jpeg 会比 png 小很多
      outputSize: { type: Number, default: 0.9 }, // 输出图片质量, 只有在 jpeg 和 webp 下生效
      compress: {
        type: Boolean,
        default: false
      },
      options: {
        default: () => {
          return {};
        }
      },
      // ratio: 比例 = 宽 / 高
      ratio: {
        type: Number,
        default: 0
      },
      // 限制图片体积大小, 单位 Mb
      maxSize: {
        type: Number,
        default: 10
      },
      beforeUpload: Function
    },
    data() {
      return {
        imgSrc: '', // 原图片
        cropImg: '', // 裁剪后的图片
        originImgSrc: '',
      };
    },
    computed: {
      img() {
        return this.cropImg || this.value;
      },
      config() {
        return {
          ...{
            viewMode: 2
          },
          ...this.options
        };
      }
    },
    methods: {
      cancelUpload() {
        this.imgSrc = '';

        // zj: set back to original image
        this.$emit('input', this.originImgSrc);
      },
      setImage(e) {
        const file = e.target.files[0];
        if (file && !file.type.includes('image/')) {
          this.$refs.uploadInputRef && (this.$refs.uploadInputRef.value = null);
          return this.$Message.error('请选择图片文件');
        }
        if (file.size > this.maxSize * 1024 * 1024) {
          this.$refs.uploadInputRef && (this.$refs.uploadInputRef.value = null);
          return this.$Message.error(`图片过大，超过${this.maxSize}MB`);
        }
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = event => {
            this.cropImg = '';
            this.imgSrc = event.target.result;
            this.$refs.cropper.replace(event.target.result);
          };

          // zj: set original image
          this.originImgSrc = this.value
        } else {
          this.$Message.error('您的浏览器版本过低，无法上传图片');
        }
      },
      cropImage() {
        return this.$refs.cropper
          .getCroppedCanvas({
            height: this.outputHeight || this.outputWidth * this.ratio,
            width: this.outputWidth,
            fillColor: '#fff'
          })
          .toDataURL(`image/${this.outputType}`, this.outputSize);
      },
      doUpload() {
        let cropImg = this.cropImage();
        if (this.beforeUpload && this.beforeUpload.then) {
          return this.beforeUpload(cropImg).then(res => this.onUploadSuccess(res));
        } else {
          return this.upload(cropImg);
        }
      },
      upload(cropImg) {
        const url = '/Admin/Public/upload_image';
        let postData = {
          image_data: cropImg,
          _type: 'platform'
        };
        return this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.status === 1) {
              return this.onUploadSuccess(res.data.path);
            } else {
              this.$Message.error(res.data.info);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      onUploadSuccess(res) {
        this.cropImg = res;
        this.imgSrc = '';
        let path = `${res}${this.compress ? '@70q_1pr' : ''}`;
        this.$emit('input', path);
        Promise.resolve();
      }
    }
  };
</script>

<style lang="less" scoped>
  @btn-color: #19be6b;
  .cropper {
    .button {
      display: flex;
      align-items: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .upload-image {
      max-height: 100%;
      max-width: 100%;
    }
  }
</style>
