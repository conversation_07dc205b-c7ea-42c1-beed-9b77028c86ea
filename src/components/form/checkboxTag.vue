<style lang="less" scoped>
  .checkbox-tag {
    width: 100%;

    .tags {
      display: flex;
      flex-wrap: wrap;

      .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;

        height: 30px;
        padding: 0 10px;
        margin-right: 10px;
        margin-bottom: 10px;

        cursor: pointer;
        background-color: #f1f1f1;
        color: #999;
      }

      .checked {
        background-color: #5cb85c;
        color: #fff;
      }

      .deleting {
        background-color: #fff;
        color: #d9534f;
        border: 1px solid #d9534f;
      }
    }
  }

  .button {
    width: 50px;
    min-width: 0;
    height: 30px;
  }
</style>

<template>
  <div class="checkbox-tag">
    <div class="tags">
      <div v-for="item in data"
           :key="item.id"
           @click="clickTag(item.id,item.can_deleted)"
           class="tag"
           :class="{'checked': value.includes(item.id) && !deleting, 'deleting': deleting && item.can_deleted != 1 }">
        <span style="padding-right: 5px">{{item.name}}</span>
        <template v-if="!deleting">
          <Icon v-if="value.findIndex(val => val == item.id) !== -1"
                type="ios-checkmark-circle"
                color="#fff"></Icon>
          <Icon v-else
                type="ios-checkmark-circle"
                color="#999"></Icon>
        </template>
        <Icon v-if="deleting && item.can_deleted!=1"
              type="ios-close-circle"
              color="#d9534f"></Icon>
      </div>
      <template v-if="!deleting && !disabled">
        <Button class="button"
                @click="adding = true"
                type="text">添加
        </Button>
        <Button class="button"
                @click="deleting = true"
                type="text">删除
        </Button>
      </template>
      <Button class="button"
              @click="deleting = false"
              v-if="deleting"
              type="text">完成
      </Button>
    </div>
    <Form :model="formData"
          ref="form"
          v-if="adding"
          :rules="formRules">
      <FormItem prop="name"
                style="display: flex;" required>
        <Input placeholder="请输入" :maxlength="10"
               style="width: 200px"
               v-model="formData.name" @on-enter="addTag"></Input>
        <Button type="text"
                @click="addTag"
                class="button">保存
        </Button>
        <Button type="text"
                @click="adding = false"
                class="button">取消
        </Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
  export default {
    name: 'CheckboxTag',
    data() {
      return {
        deleting: false,
        adding: false,
        formData: {
          name: ''
        },
        formRules: {
          name: [{ required: true, message: '请输入' }, {
            max: 10,
            message: '最多输入10个字符',
            trigger: 'change'
          }]
        }
      };
    },
    props: {
      value: {
        type: Array,
        default: () => []
      },
      data: {
        type: Array,
        default: () => []
      },
      isMemberDetail: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      radio: {
        type: Boolean,
        default: false
      },
      type: {
        type: [Number, String],
        default: ''
      },
      beforeTagAdd: Function,
      beforeTagDelete: Function
    },
    methods: {
      clickTag(id, canDeleted) {
        if (this.disabled) {
          return false
        }
        if (!this.deleting) {
          if (this.radio) {
            this.$emit('input', [id]);
          } else {
            let arr = this.value;
            let findIndex = this.value.findIndex(val => val == id);
            if (findIndex === -1) {
              arr.push(id);
            } else {
              arr.splice(findIndex, 1);
            }
            this.$emit('input', arr);
          }
        } else {
          if (canDeleted == 1) {
            this.$Message.error('该标签不可进行删除操作！');
          } else {
            this.deleteTag(id);
          }
        }
      },
      deleteSuccess(id) {
        let deletedIndex = this.data.findIndex(item => item.id === id);
        this.data.splice(deletedIndex, 1);
        let arr = this.value;
        let findIndex = this.value.findIndex(val => val === id);
        if (findIndex !== -1) {
          arr.splice(findIndex, 1);
        }
        this.$emit('input', arr);
      },
      addSuccess() {
        this.adding = false;
        this.formData.name = '';
        this.$emit('tagAdded');
      },
      setMemberTag(tagName, tagId) {
        let postData = {
          tags_name: tagName,
          tags_id: tagId
        };
        //如果存在tags_name 则为添加标签 如果存在tags_id则为删除标签
        this.$service.post('/Web/MembershipTags/set_bus_tag', postData).then(res => {
          if (res.data.errorcode === 0) {
            if (tagName) {
              this.addSuccess();
            } else {
              this.deleteSuccess(tagId);
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      deleteTag(id) {
        if (this.isMemberDetail) {
          this.setMemberTag('', id);
        } else {
          if (this.beforeTagDelete) {
            this.beforeTagDelete(id).then(() => this.deleteSuccess(id));
          } else {
            const url = '/Web/Coach/del_coach_spec';
            let postData = {
              ids: id
            };
            this.$service.post(url, postData).then(res => {
              if (res.data.errorcode === 0) {
                this.deleteSuccess(id);
              } else {
                this.$Message.error(res.data.errormsg);
              }
            });
          }
        }
      },
      addTag() {
        if (this.isMemberDetail) {
          this.setMemberTag(this.formData.name);
        } else {
          this.$refs.form.validate(valid => {
            if (valid) {
              const url = '/Web/Coach/add_coach_spec';
              const { name } = this.formData;
              if (this.beforeTagAdd) {
                this.beforeTagAdd(name, this.type).then(() => {
                  this.addSuccess();
                });
              } else {
                this.$service.post(url, { name, type: this.type || '' }).then(res => {
                  if (res.data.errorcode === 0) {
                    this.addSuccess();
                  } else {
                    this.$Message.error(res.data.errormsg);
                  }
                });
              }
            } else {
              // this.$Message.error('最多只能输入十个字符');
            }
          });
        }
      }
    },
  };
</script>
