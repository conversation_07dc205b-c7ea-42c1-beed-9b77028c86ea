<template>
  <Button @click="exportPost">导出 Excel</Button>
</template>

<script>
  export default {
    name: 'ExportButton',
    props: {
      url: {
        type: String
      },
      data: {
        type: Object
      }
    },
    data() {
      return {
        list: []
      };
    },
    methods: {
      exportPost() {
        this.$service
          .post(this.url, { ...this.data, _export: 1})
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$Message.success({
                content:'导出任务运行中，请稍后到消息中心下载!',
                duration: 3
              })
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
  };
</script>

<style scoped>
</style>
