<template>
  <Select v-model="selectedUnionids" @on-change="optionChange" @on-clear="optionChange" :multiple="multiple" :filterable="filterable" :clearable="!multiple" :placeholder="placeholder" :disabled="noway">
    <Option v-if="hasStore" value="m_m_m">门店收入</Option>
    <Option v-for="item in list" :key="item.unionid" :value="item.unionid" :disabled="item.disabled" :style="{'padding-left': (+item.level)*15+'px'}">{{item.name}}</Option>
  </Select>
</template>

<script>
import { treeToList } from 'utils';
  export default {
    name: 'adminRegion',
    props: {
      singularPrimaryKey: {
        type: Boolean,
        default: false
      },
      noway: {
        type: Boolean,
        default: false
      },
      value: {
        type: [Array, String]
      },
      multiple: {
        type: Boolean,
        default: true
      },
      placeholder: {
        type: String,
        default: "请选择区域/场馆"
      },
      filterable: {
        type: <PERSON>olean,
        default: true
      },
      id: {
        type: [String, Number],
        default: ''
      },
      shouldDefault: {//是否需要默认选中当前场馆 0不需要  1需要
        type: Number,
        default: 0
      },
      url: {
        type: String,
        default: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/admin/getMerchantsRegionBus`
      },
      busId: {
        type: String,
        default: ''
      },
      hasStore: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        list: []
      };
    },
    watch: {
      busId(newValue, oldValue) {
        if (newValue !== '' && newValue !== oldValue) {
          this.selectedUnionids = ''
          this.getRoleBusRegion()
        }
      }
    },
    computed: {
      selectedUnionids: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    created() {
      this.getRoleBusRegion()
    },
    methods: {
      optionChange(val) {
        setTimeout(() => {
          this.initResData(this.list);
        }, 0);
      },
      initResData(data) {
        let disabledUids = []
        //树Id和场馆id可能重合  组合唯一id
        data.forEach(item => {
          let unionid = '';
          if (this.singularPrimaryKey) {
            unionid = item.id;
          } else {
            unionid = `${item.id}_${item.type}_${item.level}`;
          }
          let isParentSel = false; //父节点已经选中

          item.unionid = unionid

          if (item.default) {
            this.multiple ? this.selectedUnionids.push(unionid) : this.selectedUnionids = unionid
          }
          item.default = 0
          if ( this.multiple && this.selectedUnionids ) {
            let parentUid = `${item.pid}_1_${+item.level-1}`
            isParentSel = this.selectedUnionids.indexOf(parentUid) !== -1 || disabledUids.indexOf(parentUid) !== -1
          }

          //is_disable： 后端返回的禁止选中  不可更改  shouldDisabled：前端判定的不可选状态（选中树形结构父级  子级不可选）
          let curDisabled = item.is_disable ? !!item.is_disable : isParentSel
          item.disabled = curDisabled
          if( isParentSel) {
            disabledUids.push(unionid)
            this.selectedUnionids.forEach((uid, index) => {
              if (uid == unionid) {
                this.selectedUnionids.splice(index, 1);
              }
            });
          }
        });
        this.list = data
      },
      getRoleBusRegion() {
        this.$service
          .post(this.url,{ admin_id: this.id, id: this.id, is_default: this.shouldDefault, bus_id: this.busId })
          .then(res => {
            if (res.data.errorcode === 0) {
              let resData = res.data.data
              this.initResData(treeToList(resData, 'son'));
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
  };
</script>

<style scoped>
</style>
