<template>
  <div class="quill-editor">
    <Upload id="uploader"
            :action="uploadUrl"
            :data="{savePath: './Uploads/'}"
            :default-file-list="defaultList"
            :show-upload-list="false"
            :format="['jpg','jpeg','png']"
            ref="upload"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
            :max-size="10000"
            :on-format-error="handleFormatError"
            :on-exceeded-size="handleMaxSize"
            style="width: 0; height: 0"
            multiple />
    <slot name="toolbar"></slot>
    <div ref="editor"
         :style="{ height: height + 'px' }"
         class="editor"></div>
  </div>
</template>
<script>
  import Quill from './QuillEditor'
  import defaultOptions from './defaultOptions'
  import { getBaseUrl } from 'utils/config'
  export default {
    name: 'QuillEditor',
    data() {
      return {
        _options: {},
        _content: '',

        uploadUrl: getBaseUrl() + '/Admin/Public/upload',
        defaultList: [],
        imgUrl: '',
        visible: false,
        uploadList: [],
        addImgRange: [],

        defaultOptions
      }
    },
    props: {
      content: String,
      value: String,
      height: { type: Number, default: 400 },
      options: {
        type: Object,
        required: false,
        default: () => ({})
      }
    },
    mounted() {
      this.initialize()
    },
    beforeDestroy() {
      this.quill = null
      delete this.quill
    },
    methods: {
      initialize() {
        this._options = Object.assign({}, this.defaultOptions, this.options)

        this.quill = new Quill(this.$refs.editor, this._options)

        this.quill.getModule('toolbar').addHandler('image', this.imageHandler)

        if (this.value || this.content) {
          this.quill.pasteHTML(this.value || this.content)
        }

        this.quill.on('text-change', (delta, oldDelta, source) => {
          let html = this.$refs.editor.children[0].innerHTML
          const quill = this.quill
          const text = this.quill.getText()
          if (html === '<p><br></p>') {
            html = ''
          }
          this._content = html
          this.$emit('input', this._content)
          this.$emit('change', { html, text, quill })
        })

        this.$emit('ready', this.quill)
      },
      imageHandler(state) {
        if (state) {
          this.$refs.upload.handleClick()
        }
      },
      handleSuccess(res, file) {
        const url = `${res.info}@70q_1pr`
        if (url != null && url.length > 0) {
          this.addImgRange = this.quill.getSelection()
          this.quill.insertEmbed(
            this.addImgRange != null ? this.addImgRange.index : 0,
            'image',
            url,
            Quill.sources.USER
          )
        } else {
          this.$Message.error('添加图片失败')
        }
      },
      handleFormatError() {
        this.$Notice.warning({
          title: '文件格式不正确',
          desc: '文件格式不正确，请上传 jpg 或 png 格式的图片。'
        })
      },
      handleMaxSize() {
        this.$Notice.warning({
          title: '超出文件大小限制',
          desc: '文件太大，不能超过 10M。'
        })
      },
      handleBeforeUpload() {
        this.uploadList.shift()
      }
    },
    watch: {
      // Watch content change
      content(newVal, oldVal) {
        if (this.quill) {
          if (newVal && newVal !== this._content) {
            this._content = newVal
            this.quill.pasteHTML(newVal)
          } else if (!newVal) {
            this.quill.setText('')
          }
        }
      },
      // Watch content change
      value(newVal, oldVal) {
        if (this.quill) {
          if (newVal && newVal !== this._content) {
            this._content = newVal
            this.quill.pasteHTML(newVal)
            this.quill.blur()
            document.getElementById('vipMainCon').scrollTop = -40
          } else if (!newVal) {
            this.quill.setText('')
          }
        }
      }
    }
  }
</script>

<style lang='less' scoped>
  .editor {
    height: 400px;
  }
</style>
