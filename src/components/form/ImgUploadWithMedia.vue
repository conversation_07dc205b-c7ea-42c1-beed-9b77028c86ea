<template>
  <Modal v-model="showAdd" :mask-closable="false" title="头像上传" @on-cancel="cancel" :width="600">
    <Tabs type="card" @on-click="tabChange">
      <TabPane label="拍照上传" name="拍照上传">
        <div class="video-box">
          <div v-if="videoShow==0" class="tips">请根据浏览器提示点击允许使用摄像头</div>
          <div v-if="videoShow==2" class="tips tips-error" :class="{'tips-errorallow':notAllowCamera}">
            {{errMessage}}

            <img v-if="notAllowCamera" src="../../assets/img/notAllowCamera.jpg" alt="">
          </div>
          <div class="video-wrap" id="videoWrap" v-if="videoShow==1">
            <video id="imgVideo" @loadedmetadata="videoPlay" autoplay="" :src="videoSrc"></video>
          </div>
          <canvas id="imgCanvas" width="900" height="900" class="dis"></canvas>
          <div class="buttons-wrap">
            <Button type="success" @click="handleConfirmUpload" v-if="videoShow==1">保存图片</Button>
          </div>
        </div>
      </TabPane>
      <TabPane label="本地上传" name="本地上传">
        <div class="image-description" style="padding:15px 5px">
          <p class="label">头像图片(可选)</p>
          <p class="tip">图片最佳尺寸: 900X900</p>
          <p class="tip">推荐图片大小: &lt;100kb</p>
          <p class="tip">格式限制: jpg、png</p>
        </div>
        <CroperImg
          v-if="showAdd"
          ref="croperImage"
          v-model="imgUrl"
          outputType="jpeg"
          :maxSize="10"
          :outputWidth="900"
          :outputHeight="900"
          :user-id="this.userId"
          :uploadTips="uploadTips"
          :extra-params="extraParams"
          @on-success="data => uploadType = data.type"/>
      </TabPane>
      <TabPane label="会员上传" name="会员上传">
          <Button class="face-btn" type="success" :disabled="hasUp" @click="setFaceAuth">{{hasUp?'已授权':'授权会员上传'}}</Button>
          <div class="face-tips">授权有效期为24小时，会员需要在有效期内在会员端小程序上传头像，超时后需要重新授权；</div>
      </TabPane>
    </Tabs>
    <span slot="footer"></span>
  </Modal>
</template>
<script>
  import CroperImg from './cropper.vue';

  export default {
    data() {
      return {
        videoShow: 0, //0提示点击允许，1显示摄像头，2错误提示
        boxShow: true,
        hasUp: false,
        videoSrc: '',
        videoStream: '',
        notAllowCamera: false,
        imgUrl: '',
        uploadType: null, // 上传成功后返回
        errMessage: '没有发现摄像设备或用户拒绝了连接'
      };
    },
    created() {
      this.showVideo();
    },
    props: {
      userId: {
        type: [String, Number]
      },
      value: {
        type: Boolean
      },
      uploadTips: { // 上传确认提示内容，控制上传前是否需要确认
        type: String,
        default: ''
      },
      extraParams: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      showAdd: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    watch: {
      showAdd(val) {},
      imgUrl(val, oldVal) {
        if (val && val != oldVal) {
          this.$emit('on-success', {
            imgUrl: val,
            type: this.uploadType,
          });
          this.cancel();
        }
      }
    },
    components: {
      CroperImg
    },
    methods: {
      tabChange(name) {
        if (name === '本地上传') {
          this.$refs.croperImage.$refs.uploadInput.click();
        }
        if (name === '会员上传') {
          this.getFaceAuth();
        }
      },
      cancel() {
        this.showAdd = false;
        if (this.videoShow == 2) {
          this.videoShow = 0;
        } else if (this.videoShow == 1) {
          this.closeCamera();
        }
      },
      //关闭摄像头
      closeCamera() {
        let mediaStream = this.videoStream;
        let track = mediaStream.getTracks()[0];
        if (mediaStream != null) {
          if (mediaStream.stop) {
            mediaStream.stop();
          }
          // this.videoSrc = "";
        }
        if (track != null) {
          if (track.stop) {
            track.stop();
          }
        }
      },
      showVideo() {
        let _this = this;
        let promisifiedOldGUM = function(constraints) {
          let getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia;
          if (!getUserMedia) {
            return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
          }
          return new Promise(function(resolve, reject) {
            getUserMedia.call(navigator, constraints, resolve, reject);
          });
        };
        if (navigator.mediaDevices === undefined) {
          navigator.mediaDevices = {};
        }
        if (navigator.mediaDevices.getUserMedia === undefined) {
          navigator.mediaDevices.getUserMedia = promisifiedOldGUM;
        }
        let constraints = {
          audio: false,
          video: {
            width: { min: 640, ideal: 1280, max: 1920 },
            height: { min: 480, ideal: 720, max: 1080 }
          }
        };
        navigator.mediaDevices
          .getUserMedia(constraints)
          .then(stream => {
            _this.videoShow = 1;
            _this.videoStream = stream;
            this.$nextTick(() => {
              const video = document.querySelector('#imgVideo');
              video.srcObject = stream;
            })
          })
          .catch(function(err) {
            _this.videoShow = 2;
            //用户点击了拒绝
            if (err.name == 'PermissionDeniedError' || err.name == 'NotFoundError') {
              _this.notAllowCamera = true;
            }
            throw new Error(err);
          });
      },
      videoPlay(e) {
        e.target.play();
        let ml = (e.target.offsetWidth - 500) / 2;
        let mt = (e.target.offsetHeight - 500) / 2;
        e.target.style.cssText = 'margin-left:' + '-' + ml + 'px;margin-top:-' + mt + 'px;';
      },
      // 是否确认拍照上传
      handleConfirmUpload() {
        if (this.uploadTips) {
          this.$Modal.confirm({
            title: '是否确认上传',
            content: this.uploadTips,
            onOk: () => {
              this.saveVideoImg()
            },
          })
        } else {
          this.saveVideoImg()
        }
      },
      saveVideoImg() {
        //使用canvas捕获viedeo图像并转化为base64。ps：动态设置width和height的时候截取图像会有bug，不要动态创建cavas
        let canvas = document.getElementById('imgCanvas');
        let imgVideo = document.getElementById('imgVideo');
        let ctx = canvas.getContext('2d');
        //ctx.drawImage(imgVideo,0,0,500,500);
        ctx.drawImage(
          imgVideo,
          (imgVideo.offsetWidth - 500) / 2,
          (imgVideo.offsetHeight - 500) / 2,
          500,
          500,
          0,
          0,
          900,
          900
        );
        let imgData = canvas.toDataURL('image/jpeg');

        let postData = {
          image_data: imgData,
          _type: 'platform',
          userid: this.userId,
          ...this.extraParams
        };
        this.$service
          .post('/Admin/Public/upload_image', postData)
          .then(response => {
            if (response.status == 200) {
              let imgpath = response.data.path + '?t=' + new Date().getTime();
              this.cancel();
              this.$emit('on-success', {
                imgUrl: imgpath,
                type: response.data.type,
              });
              if (response.data.status == 0) {
                this.$Message.error(response.data.info)
              } else if(response.data.status == 1) {
                this.$Message.success('上传成功！')
              }
            } else {
              this.$Message.error(response.data.statusText);
            }
          })
          .catch(function(response) {
            this.$Message.error('网络错误');
          });
      },
      getFaceAuth() {
        this.$service
          .post(`/Web/Member/get_upload_face_power?user_id=${this.userId}`)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.hasUp = res.data.data.upload_face_power==1 ? true : false
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      },
      setFaceAuth() {
        this.$service
          .post(`/Web/Member/set_upload_face_power?user_id=${this.userId}`)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.hasUp = res.data.data.upload_face_power==1 ? true : false
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
      }
    }
  };
</script>
<style scoped>
  .avatar-logo img {
    width: 500px;
    height: 500px;
  }
  .buttons-wrap {
    display: flex;
    justify-content: center;
  }
  .video-box {
    margin: 30px auto 0;
    /* min-height: 500px;
    min-width: 500px; */
  }

  .video-box .btn-container {
    width: 232px;
    margin: 0 auto;
  }

  .video-box .cropeer-containerimg {
    margin: 0 auto 30px;
  }

  .video-wrap {
    width: 500px;
    height: 500px;
    background: #000;
    margin: 0 auto 30px;
    overflow: hidden;
  }

  .video-wrap video {
    margin-left: -120px;
    margin-top: -120px;
  }

  .btn-wrap {
    width: 208px;
    height: 35px;
    margin: 0 auto;
  }

  .btn-wrap .nav {
    display: block;
    width: 50%;
    float: left;
    height: 100%;
    line-height: 33px;
    text-align: center;
    color: #aaa;
    font-size: 16px;
    border: 1px solid #ccc;
    background: #fff;
    cursor: pointer;
  }

  .btn-wrap label:first-child {
    border-radius: 4px 0 0 4px;
  }

  .btn-wrap label:last-child {
    border-radius: 0 4px 4px 0;
  }

  .btn-wrap .cur {
    background: #5db75d;
    color: #fff;
    border-color: #5db75d;
  }

  .dis {
    display: none !important;
  }

  .tips {
    height: 200px;
    padding: 80px 50px;
    text-align: center;
    line-height: 41px;
    color: #aaa;
  }

  .tips-error {
    color: #d95350;
  }

  .tips-errorallow {
    padding: 0;
    margin-top: -20px;
  }
  .face-btn {
    display: block;
    margin: 50px auto 20px;
  }
  .face-tips {
    margin: 0 auto;
    text-align: center;
  }
</style>
