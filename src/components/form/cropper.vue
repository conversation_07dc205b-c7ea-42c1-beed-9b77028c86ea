<template>
  <div class="cropper">
    <div class="image-wrapper" v-if="(imgSrc || cropImg) && !multiLoaded">
      <img :src="cropImg" v-if="cropImg" class="upload-image" alt="形象照">
      <div id="preview" v-if="!nopreview&&!cropImg"></div>
      <VueCropper :ref="refName" v-show="imgSrc && !cropImg" preview="#preview" :view-mode="2" :aspectRatio="aspectRatio" :auto-crop-area="1" :min-container-width="500" :min-container-height="300" :src="imgSrc" alt="原图片" :img-style="{ width: '400px', height: '300px' }">
      </VueCropper>
    </div>
    <div :class="[center?'centerButton':'button']">
      <label class="upload-btn" v-if="!imgSrc">选择本地图片
        <form id="myForm">
          <input type="file" :id="refName" name="image" :files="filedetail" style="font-size: 1.2em; padding: 10px 0; display: none" @change="setImage" ref="uploadInput" icon="ios-upload-outline" />
        </form>
      </label>
      <Button type="success" @click="handleConfirmUpload" v-if="imgSrc && !cropImg">保存图片</Button>
      <Button @click="cancelUpload" v-if="imgSrc && !cropImg" style="margin-left: 30px">取消</Button>
      <div class="specbutton" @click="showmodelPic=true" v-if="modelPicAddr&&modelPicAddr.length>0">
        <label class="upload-btn" v-if="!imgSrc">选择模板图片</label>
      </div>
    </div>
    <choosePic v-model="showmodelPic" v-on:selectPic="selectPic" :modelPicAddr="modelPicAddr" />
  </div>
</template>
<script>
  // readme: 取消编辑右方的预览，父组件中传入nopreview, 默认是false
  //取消保存图片后的图片展示，父组件中传入multiple，默认为false
  //需要加入模版图片选项，父组件中传入模板图片的地址数组modelPicAddr
  //编辑图片时需要隐藏父组件中的图片显示，父组件中添加监听emit事件v-on:noshowpic

  import VueCropper from 'vue-cropperjs';
  import choosePic from './choosePic.vue';
  export default {
    name: 'MyCropper',
    components: {
      VueCropper,
      choosePic
    },
    data() {
      //imgsrc为生成的图片数据，cropimg为上传后生成的图片地址
      //multiloaded为图片展示，showmodelpic为选择模版图片
      return {
        imgSrc: '',
        cropImg: '',
        multiLoaded: true,
        showmodelPic: false,
        filedetail: []
      };
    },
    props: {
      value: {},
      multiple: {
        type: Boolean,
        default: false
      },
      compress: {
        type: Boolean,
        default: false
      },
      userId: {
        type: [String, Number]
      },
      outputWidth: { type: Number, default: 0 }, // 输出图片宽度, 默认 0 不限制
      outputHeight: { type: Number, default: 0 }, // 输出图片高度, 如果不传默认为: 输出宽度 * 比例
      outputType: { type: String, default: 'png' }, // 输出图片格式, 默认为 png, 可选: jpeg, webp 等, 注: 上传后服务器返回的都是 png 格式, 但是 jpeg 会比 png 小很多
      options: {},
      refName: {
        default: 'cropper'
      },
      nopreview: {
        type: Boolean,
        default: false
      },
      modelPicAddr: {
        type: Array
      },
      maxSize: {
        type: Number,
        default: 5
      },
      uploadTips: { // 上传确认提示内容，控制上传前是否需要确认
        type: String,
        default: '',
      },
      center: {
        type: Boolean,
        default: false
      },
      url: {
        type: String,
        default: '/Admin/Public/upload_image'
      },
      extraParams: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      // 截取比例: 长 / 宽, 默认 1/1
      aspectRatio() {
        return (this.options && this.options.aspectRatio) || 1;
      }
    },
    watch: {
      value(val) {
        if (val) {
          this.cropImg = val;
        }
      }
    },
    methods: {
      cancelUpload() {
        this.cropImg = '';
        this.imgSrc = '';
        this.multiLoaded = true;
        this.$emit('noshowpic', false);
      },
      setImage(e) {
        this.cropImg = '';
        this.imgSrc = '';
        const file = e.target.files[0];
        if (this.maxSize && file.size > this.maxSize * 1024 * 1024) {
          this.$Message.error(`图片过大，超过${this.maxSize}M`);
          return;
        }
        if (file && !file.type.includes('image/')) {
          this.$Message.error('请选择图片文件');
          return;
        }
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = event => {
            this.multiLoaded = false;
            this.cropImg = this.uploadedImg = '';
            this.imgSrc = event.target.result;
            this.$nextTick(() => {
              this.$refs[this.refName].replace(event.target.result);
            });
            let imgform = document.getElementById('myForm');
            imgform.reset();
            if (this.imgSrc) {
              this.$emit('noshowpic', true);
            }
          };
        } else {
          this.$Message.error('您的浏览器版本过低，无法上传图片');
        }
      },
      cropImage() {
        return this.$refs[this.refName]
          .getCroppedCanvas({
            height: this.outputHeight,
            width: this.outputWidth,
            fillColor: '#fff'
          })
          .toDataURL(`image/${this.outputType}`);
      },
      // 是否确认上传
      handleConfirmUpload() {
        if (this.uploadTips) {
          this.$Modal.confirm({
            title: '是否确认上传',
            content: this.uploadTips,
            onOk: () => {
              this.doUpload()
            },
          })
        } else {
          this.doUpload()
        }
      },
      doUpload() {
        let cropImg = this.cropImage();
        let postData = {
          image_data: cropImg,
          _type: 'platform',
          ...this.extraParams
        };
        if (this.userId) {
          postData.userid = this.userId;
        }
        this.$service
          .post(this.url, postData)
          .then(res => {
            if (res.data.status === 1) {
              this.cropImg = cropImg;
              if (this.multiple) {
                this.multiLoaded = true;
                this.imgSrc = '';
              }
              let path = `${res.data.path}${this.compress ? '@70q_1pr' : ''}`;
              this.$emit('input', path);
              this.$emit('on-success', { type: res.data.type });
              this.$emit('noshowpic', false);
            } else {
              this.$Message.error(res.data.info);
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      selectPic(picaddr) {
        this.cropImg = picaddr;
        this.imgSrc = '';
        this.$emit('input', picaddr);
      }
    }
  };
</script>
<style lang="less" scoped>
  @btn-color: #19be6b;
  .cropper {
    #preview {
      width: 300px;
      height: 300px;
      overflow: hidden;
      position: absolute;
      right: -320px;
      border: 1px solid #dcdcdc;
      top: 0;
      background-color: #ccc;
    }
    .button {
      display: flex;
      align-items: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .centerButton {
      display: flex;
      align-items: center;
      justify-content: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .image-wrapper {
      display: flex;
      justify-content: center;
      // width: 500px;
      height: 300px;
      border: 2px solid #dcdcdc;
      margin-bottom: 30px;
      position: relative;
      .upload-image {
        max-height: 100%;
        max-width: 100%;
      }
    }
  }
  .specbutton {
    display: flex;
    align-items: center;
    .upload-btn {
      margin-left: 80px;
      border: 1px solid @btn-color;
      border-radius: 4px;
      height: 32px;
      line-height: 32px;
      padding: 0 10px;
      font-size: 14px;
      display: inline-block;
      cursor: pointer;
      color: @btn-color;
      &:hover {
        color: #fff;
        background-color: @btn-color;
      }
    }
  }
</style>
