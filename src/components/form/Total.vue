
<style lang="less" scoped>
  @border: 1px solid #dcdcdc;
  .total-stat {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 40px 15px 40px;
    height: 135px;
    // border-top: @border;
    // border-bottom: @border;
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    background-color: #fff;
    > span {
      position: absolute;
      left: 22px;
      top: 13px;
      color: #666;
      font-size: 14px;
    }
    .stat {
      h3 {
        font-size: 40px;
        color: #52a4ea;
        font-weight: normal;
        margin-top: 20px;
        span {
          font-size: 14px;
          color: #999;
        }
      }
      p {
        color: #999;
        font-size: 14px;
      }
    }
    > b {
      width: 1px;
      height: 30px;
      background-color: #ccc;
    }
  }
</style>

<template>
  <div class="total-stat">
    <template v-for="(item, index) in value">
      <div class="stat"
           :key="index">
        <h3>{{ item.value }}
          <span v-if="item.unit">{{item.unit}}</span>
        </h3>
        <p>{{item.name}}
          <Tooltip v-if="item.tips">
            <Icon type="ios-help-circle"
                  style="color: #f4a627"></Icon>
            <div slot="content">
              <div style="max-width: 200px; white-space: normal">{{item.tips}}</div>
            </div>
          </Tooltip>
        </p>
      </div>
      <b v-if="index < value.length - 1"></b>
    </template>
  </div>
</template>

<script>
  export default {
    name: 'statTotal',
    props: {
      value: {}
    }
  };
</script>
