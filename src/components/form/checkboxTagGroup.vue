<style lang="less" scoped>
.checkbox-tag {
  width: 100%;

  .tags {
    display: flex;
    flex-wrap: wrap;

    .tag {
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;

      height: 30px;
      padding: 0 10px;
      margin-right: 10px;
      margin-bottom: 10px;

      cursor: pointer;
      background-color: #f1f1f1;
      color: #999;
    }

    .checked {
      background-color: #5cb85c;
      color: #fff;
    }

    .deleting {
      background-color: #fff;
      color: #d9534f;
      border: 1px solid #d9534f;
    }
  }
}

.button {
  width: 50px;
  min-width: 0;
  height: 30px;
}
</style>

<template>
  <div class="checkbox-tag">
    <div class="tags">
      <div v-for="item in data"
           :key="item.level_id"
           @click="clickTag(item.level_id)"
           class="tag"
           :class="{'checked': value == item.level_id && !deleting, 'deleting': deleting }">
        <span style="padding-right: 5px">{{item.level_name}}</span>
        <template v-if="!deleting">
          <Icon v-if="value == item.level_id"
                type="ios-checkmark-circle"
                color="#ffffff"></Icon>
          <Icon v-else
                type="ios-checkmark-circle"
                color="#999"></Icon>
        </template>
        <Icon v-if="deleting"
              type="ios-close-circle"
              color="#d9534f"></Icon>
      </div>
      <template v-if="!deleting && !disabled">
        <Button class="button"
                @click="adding = true"
                type="text">添加
        </Button>
        <Button class="button"
                v-if="data.length"
                @click="deleting = true"
                type="text">删除
        </Button>
      </template>
      <Button class="button"
              @click="deleting = false"
              v-if="deleting"
              type="text">完成
      </Button>
    </div>
    <Form :model="formData"
          ref="form"
          v-if="adding"
          :rules="formRules">
      <FormItem prop="level_name"
                style="display: flex;"
                required>
        <Input placeholder="请输入"
               :maxlength="10"
               style="width: 200px"
               v-model="formData.level_name"
               @on-enter="addTag"></Input>
        <Button type="text"
                @click="addTag"
                class="button">保存
        </Button>
        <Button type="text"
                @click="adding = false"
                class="button">取消
        </Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'CheckboxTagGroup',
  data () {
    return {
      deleting: false,
      adding: false,
      formData: {
        id: this.id,
        level_name: ''
      },
      formRules: {
        level_name: [{ required: true, message: '请输入' }, {
          max: 10,
          message: '最多输入10个字符',
          trigger: 'change'
        }]
      }
    };
  },
  props: {
    value: {
      type: String,
      required: true
    },
    id: {
      type: String
    },
    data: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    clickTag (id, canDeleted) {
      if (this.disabled) {
        return false
      }
      if (!this.deleting) {
        if (id === this.value) {
          this.$emit('input', '');
        } else {
          this.$emit('input', id);
        }
      } else {
        if (canDeleted == 1) {
          this.$Message.error('该标签不可进行删除操作！');
        } else {
          this.deleteTag(id);
        }
      }
    },
    deleteSuccess (id) {
      let deletedIndex = this.data.findIndex(item => item.level_id === id);
      this.data.splice(deletedIndex, 1);
      if (id === this.value) {
        this.$emit('input', '');
      }
    },
    addSuccess () {
      this.adding = false;
      this.formData.level_name = '';
      this.$emit('tagAdded');
    },
    deleteTag (id) {
      this.$service.post('Web/Business/del_bus_level', {
        level_id: id,
        id: this.id
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.deleteSuccess(id);
        } else {
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    addTag () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.formData.id = this.id
          this.$service.post('/Web/Business/add_bus_level', this.formData).then(res => {
            if (res.data.errorcode === 0) {
              this.addSuccess();
            } else {
              this.$Message.error(res.data.errormsg);
            }
          });
        }
      });
    }
  },
};
</script>
