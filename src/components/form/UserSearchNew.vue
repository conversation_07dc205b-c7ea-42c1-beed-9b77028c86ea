<template>
  <div class="user-search-wrap">
    <Select
      filterable
      ref="selectRef"
      class="search-input"
      clearable
      :remote="remote"
      :not-found-text="notFoundText"
      :remote-method="getUsers"
      v-model="userId"
      :loading="searching"
      placeholder="姓名电话/卡号"
      transfer
      @on-query-change="onQueryChange"
      @on-change="onChange"
    >
      <Option v-if="query && showPrefix" class="pre-option" value="memberInfo" :label="query">
        <Icon type="ios-search" /> 点击搜【会员信息】包含 {{ query }}
      </Option>
      <Option v-if="query && showPrefix" class="pre-option" value="cardInfo" :label="query">
        <Icon type="ios-search" /> 点击搜【卡号信息】包含 {{ query }}
      </Option>
      <Option
        v-for="user in userList"
        :key="user.user_id"
        :value="user.user_id"
        :label="`${user.username}(${user.phone})`"
      ></Option>
    </Select>
  </div>
</template>

<script>
import { isChinese } from '@/utils'
import { debounce } from 'lodash-es'

export default {
  name: 'UserSearchNew',
  data() {
    return {
      query: '',
      userId: '',
      searching: false,
      showPrefix: true,
      accurateType: 1,
      userList: [],
      selectedUser: null,
      shouldOpenSelect: false,
      support_fingerprint: '',
      is_print_ticket: '1',
      remote: true,
      notFoundText: '未找到结果，请重试其他关键词',
    }
  },
  props: {
    isUserId: {
      type: Boolean,
      default: false,
    },
    isGetMoreOnChange: {
      type: Boolean,
      default: true,
    },
    search: {
      type: String,
      default: '',
    },
    // 1？(缺省值)，2普通签到，3私教签到 4黑名单添加 5 订场长租 6 团课预约
    from: {
      type: [Number, String],
    },
  },
  created() {},
  mounted() {
    if (!this.search && this.$refs.selectRef.$refs.reference) {
      this.$refs.selectRef.$refs.reference.focus()
    }
  },
  methods: {
    onChange(value) {
      if (value === 'memberInfo' || value === 'cardInfo') {
        this.accurateType = value === 'memberInfo' ? 2 : 3
        this.showPrefix = false
        this.shouldOpenSelect = true
        this.userList = []
        this.userId = ''
        this.$refs.selectRef.setQuery(this.query)
        this.getSearchUserList(this.query)
      } else {
        this.userChange(value)
      }
    },
    async userChange(id) {
      if (!id) {
        this.userList = []
        this.$emit('on-change', null)
        return
      }
      this.selectedUser = this.userList.find((user) => user.user_id === id)
      if(this.isGetMoreOnChange) {
        await this.getUserInfo(this.selectedUser);
      }
      this.selectedUser.support_fingerprint = this.support_fingerprint
      this.selectedUser.is_print_ticket = this.is_print_ticket
      this.$emit('on-change', this.selectedUser)
    },
    getUserInfo(user) {
      return this.$service
        .post('/Web/Sign/user_attribute', {
          user_id: user.user_id,
          from: this.from
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.selectedUser = {
              ...this.selectedUser,
              ...res.data.data
            }
            return res.data.data
          } else {
            this.$Message.error(res.data.errormsg);
          }
          return null
        })
    },
    onQueryChange(value) {
      if(this.selectedUser && value === `${this.selectedUser.username}(${this.selectedUser.phone})`) {
        this.showPrefix = false
        return
      }
      this.accurateType = 1
      this.showPrefix = true
      this.query = value
    },
    getUsers: debounce(function (search) {
      if (search === 'memberInfo' || search === 'cardInfo') {
        return
      }
      this.getSearchUserList(search)
    }, 400),
    // 搜索会员
    getSearchUserList(search) {
      if (
        (this.selectedUser && search === `${this.selectedUser.username}(${this.selectedUser.phone})`) ||
        (this.userList && this.userList[0] && search === this.userList[0].user_id)
      ) {
        return false
      }

      // if numbers or letters must be more than 3 in length you can request
      if (!isChinese(search)) {
        this.notFoundText = '至少输入3位数字'
        return
      }

      this.notFoundText = '未找到结果，请重试其他关键词'

      search = search.trim()
      if (search === '') return
      this.searching = true
      this.userList = []
      const url = '/Web/Sign/search_user'
      let postData = {
        search: search,
        accurate_type: this.accurateType,
        is_user_id: this.isUserId ? 1 : 0,
        from: this.from,
      }
      return this.$service.post(url, postData, { loading: false }).then((res) => {
        if (res.data.errorcode === 0) {
          this.userList = res.data.data.list
          this.support_fingerprint = res.data.data.support_fingerprint
          this.is_print_ticket = res.data.data.is_print_ticket
          this.searching = false
          if (
            this.userList &&
            this.userList.length === 1 &&
            (this.isUserId || (this.accurateType === 1 && this.userList[0].pg_accurate_uniq === 1) || this.accurateType !== 1)
          ) {
            if (this.isUserId) {
              this.$emit('isUserId', false)
            }
            let user = this.userList[0]
            this.userId = user.user_id
            this.userChange(user.user_id)
            this.shouldOpenSelect = false
            this.$refs.selectRef.hideMenu()
          } else {
            if (this.shouldOpenSelect) {
              this.shouldOpenSelect = false
              this.query = search
              const refSel = this.$refs.selectRef.$refs.reference
              refSel.click()
              refSel.focus()
            }
          }
        }
      })
    },
  },
  watch: {
    search: {
      handler(val) {
        this.query = val
        if (val) {
          this.shouldOpenSelect = true
          this.getSearchUserList(val)
        }
      },
      immediate: true,
    },
  },
}
</script>

<style lang="less">
.user-search-wrap {
  margin-bottom: 10px;

  .ivu-select-selection {
    border-radius: 0;
    outline: 0;
    border-color: #dddee1;
    box-shadow: none;
  }
}
.pre-option {
  color: #2d8cf0;
}
</style>
