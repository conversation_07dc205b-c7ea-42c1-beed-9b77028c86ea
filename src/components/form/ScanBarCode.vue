<template>
<div>
  <div class="scan-tit">
    请将扫描枪/ 二维码读取器对准收款二维码
  </div>
  <img class="scan-img" src="/static/img/scan-bar.png" alt="请将扫描枪/ 二维码读取器对准收款二维码" />
</div>
</template>

<script>
export default {
  name: 'ScanBarCode',
  data() {
    return {
      hasEnterNum: '',
      authCode: ''
    }
  },
  computed: {
  },
  created () {
    document.addEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    handleKeyDown(e) {
      let event = e || window.event;
      let key = event.key;
      if (event.target.localName != 'input' && /^[0-9]*$/.test(key)) {
        this.hasEnterNum += key
      }
      if (key === 'Enter') {
        this.authCode = this.hasEnterNum
        this.hasEnterNum = ''
        this.$emit('on-enter', this.authCode)
      }
    },
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown);
  },
};
</script>
<style lang="less" scoped>
.scan-tit {
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  margin-bottom: 20px;
}
.scan-img {
  display: block;
  margin: 0 auto;
}
</style>

