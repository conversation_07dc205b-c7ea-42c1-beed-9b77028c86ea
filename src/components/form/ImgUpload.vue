<template>
  <Modal v-model="showAdd" :mask-closable="false" :width="607">
    <p slot="header" class="upload-modal-header-content">
      <span>添加图片</span>
      <span> ( 只能添加jpg，jpeg，gif，png，svg，大小不超过1MB。)</span>
    </p>
    <Tabs type="card" v-model="tabs">
      <TabPane label="系统模板" name="系统模板">
        <div class="pic-box">
          <ul>
            <li
              v-for="(item, i) in defaultList"
              :key="i"
              @click="changeImg(item)"
              :style="{ width: width }"
            >
              <img
                :style="{ width: width, height: height }"
                :src="item"
                :class="imgUrl === item ? 'selected' : ''"
              />
            </li>
          </ul>
        </div>
      </TabPane>
      <TabPane label="自定义" name="自定义">
        <div class="self-wrap">
          <CroperImg
            v-if="showAdd"
            ref="croperImage"
            v-model="imgUrl"
            :options="options"
            :maxSize="1"
            :center="true"
          />
        </div>
      </TabPane>
    </Tabs>
    <div slot="footer" class="modal-buttons">
    </div>
  </Modal>
</template>
<script>
import CroperImg from './cropper.vue'
export default {
  components: {
    CroperImg,
  },
  data() {
    return {
      // 系统封面数组
      defaultList: [
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-2.png', 
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-3.png',
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-4.png',
      ],
      // 默认封面
      imgUrl: '',
      // 大小尺寸
      width: '276px',
      height: '160px',
      // 菜单切换
      tabs: '系统模板',
    }
  },
  props: {
    value: {
      type: Boolean,
    },
    defualt: {
      type: String,
      default: ''
    },
    options: {},
  },
  computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
		imgUrl(val,oldVal){
			if(val && val!=oldVal) {
				this.changeImg(this.imgUrl)
			}
		}
  },
  methods: {
    changeImg(path) {
      this.$emit('on-change',path)
      this.showAdd = false;
    }
  },
}
</script>
<style lang="less" scoped>
.upload-modal-header-content {
  font-size: 16px;
  color: #333;
  span + span {
    font-size: 12px;
    color: rgba(#333, 0.8);
  }
}
.pic-box {
  margin: 30px auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pic-box ul {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  margin-right: -15px;
}
.pic-box ul li {
  float: left;
  cursor: pointer;
  text-align: center;
  margin-bottom: 15px;
  margin-right: 15px;
}
.pic-box img {
  padding: 2px;
  margin-bottom: 5px;
  border: 1px solid #fff;
}
.pic-box img.selected {
  border-color: #0b78e3;
}
.self-wrap {
  margin-top: 40px;
}
</style>
