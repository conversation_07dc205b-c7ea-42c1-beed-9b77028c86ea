<template>
  <Select
    :value="couponId"
    :clearable="clearable"
    :not-found-text="busDiscount.length ? undefined : '暂无可用折扣券'"
    placeholder="折扣券"
    filterable
    labelInValue
    @on-change="handleChange"
  >
    <Option
      v-for="discount in busDiscount"
      :key="discount.coupon_id"
      :label="discount.coupon_name"
      :value="discount.coupon_id">
      {{ discount.coupon_name }}
    </Option>
  </Select>
</template>

<script>
  export default {
    name: 'BusDiscount',
    props: {
      value: {},
      clearable: {
        type: Boolean,
        default: true
      },
      showHistory: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        busDiscount: []
      };
    },
    computed: {
      couponId: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val.value);
          this.$emit('on-change', {
            value: val.value,
            label: val.label
          });
        }
      }
    },
    mounted() {
      this.getBusDiscount(this.showHistory);
    },
    methods: {
      getBusDiscount(showHistory) {
        this.$service.post('/Web/Coupon/get_bus_coupon', { is_show: showHistory ? 1 : '' }).then(res => {
          if (res.data.errorcode === 0) {
            const list = res.data.data;
            this.busDiscount = Array.isArray(list) ? list : [];
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },
      handleChange(e) {
        this.couponId = e;
      }
    }
  };
</script>

<style scoped>
</style>
