<template>
   <Modal v-model="showModal" title="支付方式">
      <Table ref="table" stripe :columns="payDetailCols" :data="list" disabled-hover></Table>
      <div slot="footer"></div>
    </Modal>
</template>

<script>
export default {
  name: 'ReturnPayModal',
  props: {
    value: {
      type: Boolean
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  data() {
    return {
      payDetailCols: [{ title: '支付方式', key: 'name' }, { title: '支付金额', key: 'amount' }],
    }
  },
  methods: {
  }
}
</script>

<style scoped>
</style>
