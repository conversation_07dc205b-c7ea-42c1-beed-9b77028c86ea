<template>
  <Select
    v-model="cardId"
    :disabled="disabled"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    @on-change="handleChange"
  >
    <Option
      v-for="item in expCardList"
      :key="item.card_id"
      :value="item.card_id"
      :label="item.card_name"
    >
      {{ item.card_name }}
    </Option>
  </Select>
</template>

<script>
export default {
  name: 'ExpCardSelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择体验卡'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    card_type_ids: {
      type: String,
      default: '1,2,3,4'
    }
  },
  data () {
    return {
      expCardList: [], // 体验卡选项
    }
  },
  computed: {
    cardId: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val);
      }
    }
  },

  created () {
    this.getExpCard()
  },
  methods: {
    getExpCard() {
      this.$service.post('/Web/Member/get_experience_card', {
        card_type_ids: this.card_type_ids, // 体验卡类型，时段卡1,2次数卡,3储值卡,4私教卡 ，多种类型以逗号分隔如：1,2,3,4
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.expCardList = res.data.data.list;
        } else {
          this.expCardList = [];
          this.$Message.error(res.data.errormsg);
        }
      });
    },
    handleChange (cardId) {
      const target = this.expCardList.find(v => v.card_id == cardId)
      if (target) {
        this.$emit('on-change', target)
      } else {
        this.$emit('on-change', { value: cardId })
      }
    },
  }
}
</script>
