<template>
  <Modal :mask-closable="false"
         v-model="showMdal"
         width="800px"
         title="添加教练可授课程">
    <div class="course-select">
        <ul class="sort">
          <li v-for="item in groupList" :key="item.id" :class="{ 'active': item.id === curType }"
              @click="handleTypeSelect(item.id)">{{item.title}}
          </li>
        </ul>
        <div class="course">
          <div v-if="filterData.length" class="course-tag" style="border-style: solid; margin-right: 20px;"
               :class="{'active': checkAll}"
               @click="handleCheckAll">全选
          </div>
          <div class="course-tag" :class="{'active': item._checked}"
               v-for="item in filterData" :key="item.card_id + checkAll"
               @click="item._checked = !item._checked">{{item.card_name}}
          </div>
        </div>
      </div>
      <div class="modal-buttons" slot="footer">
        <Button type="success" @click="handleConfirm">确定</Button>
        <Button @click="handleSelectCancel">取消</Button>
      </div>
  </Modal>
</template>

<script>
  export default {
    name: 'SelectGroupeData',
    props: {
      value: {
        type: Boolean
      },
      isSwimCoach: {
        type: Boolean,
        default: false
      },
      checked: {
        type: Array,
        default: []
      }
    },
    data() {
      return {
        checkAll: false,
        curType: '',
        groupList: [],
        allData: [],
        filterData: [],
        selectedList: []
      }
    },
    computed: {
      showMdal: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        }
      }
    },
    watch: {
      showMdal(val) {
        if(val) {
          this.allData.forEach(item => {
            item._checked = this.checked.includes(item.card_id);
          });
        }
      },
      checked(val) {
        this.allData.forEach(item => {
          item._checked = this.checked.includes(item.card_id);
        });
      },
      isSwimCoach(val, oldVaL) {
        if (val !== oldVaL) {
          Promise.all([this.getAllPrivate(),this.getSortData()]).then(res=> {
            this.handleTypeSelect(this.groupList[0].id);
          })
        }
      }
    },
    created() {
      Promise.all([this.getAllPrivate(),this.getSortData()]).then(res=> {
        this.handleTypeSelect(this.groupList[0].id);
      })
    },
    methods: {
      getAllPrivate() {
        return this.$service.get(`/Web/Coach/get_bus_card?type=${this.isSwimCoach ? 3 : '' }`).then(res => {
          if (res.data.errorcode === 0) {
            let checkedList = []
            this.allData = res.data.data.map(item => {
              const flag = this.checked.includes(item.card_id)
              flag && checkedList.push(item)
              return { ...item, _checked: this.checked.includes(item.card_id) };
            });
            if (checkedList && checkedList.length) {
              this.$emit('on-edit', this.checked, checkedList)
            }
          } else {
            this.$Message.create(res.data.errormsg);
          }
        }).catch(err => {
          throw new Error(err);
        });
      },
      getSortData() {
        return this.$service.post('/Web/Card/get_bus_card_group', { bus_id: this.$store.state.busId, type: this.isSwimCoach ? 3 : '' }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.groupList = data.list;
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      handleCheckAll() {
        this.checkAll = !this.checkAll;
        if (this.checkAll) {
          this.filterData.forEach(item => {
            item._checked = true;
          });
        } else {
          this.filterData.forEach(item => {
            item._checked = false;
          });
        }
      },
      handleTypeSelect(id) {
        this.curType = id;
        this.filterData = this.allData.filter(item => {
          if (!item.group_id) item.group_id = '0';
          return item.group_id === id;
        });
        this.checkAll = !this.filterData.some(item => !item._checked);
      },
      handleConfirm() {
        const allCheckedArr = this.allData.filter(item => item._checked)
        this.selectedList = allCheckedArr.map(item => item.card_id);
        this.$emit('on-confirm', this.selectedList, allCheckedArr)
        this.showMdal = false;
      },
      handleSelectCancel() {
        this.showMdal = false;
        this.allData.forEach(item => {
          item._checked = this.selectedList.includes(item.card_id);
        });
      }

    }
  }
</script>

<style scoped lang="less">
 .course-select {
    display: flex;
    max-height: 60vh;
    min-height: 50vh;

    .sort {
      width: 180px;
      overflow-y: scroll;
      background-color: #f1f3f5;

      li {
        text-align: center;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        cursor: pointer;

        &.active {
          background-color: #fff;
          border-left: 4px solid #19be6b;
          color: #19be6b;
          font-weight: bold;
        }
      }
    }

    .course {
      flex: 1;
      overflow-y: scroll;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;

      .course-tag {
        padding: 6px 25px;
        height: auto;
        font-size: 14px;
        margin: 10px 5px;
        border: 1px dashed #19be6b;
        background-color: #fff;
        cursor: pointer;
        user-select: none;

        &.active {
          background-color: #19be6b;
          color: #fff;
        }
      }
    }
  }
</style>
