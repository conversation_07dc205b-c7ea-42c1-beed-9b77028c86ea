<template>
  <div class="check-tree-container">
    <div
      v-for="(v, index) of treeList"
      v-show="v.show"
      :key="index"
      :class="v.children ? 'check-tree-node' : 'check-tree-item'">
      <div
        class="check-tree-box"
        :class="{ disabled: v.disabled }"
        @click="handleClickCheckBox(v)">
        <!-- <i v-if="v.children" class="">TODO 收起/展开？</i> -->
        <i class="check-tree-box-inner" :class="{ 'checked': v.check }" />
        <div
          v-once
          class="check-tree-box-content text_overflow"
          :title="v.name.length > 10 ? v.name : null">
          {{ v.name }}
        </div>
      </div>

      <div
        v-if="Array.isArray(v.children) && v.children.length"
        class="check-tree-children">
        <CheckPtClassTree
          :treeList="v.children"
          @node-check="handleCheckNodeParent"
        />
      </div>
    </div>
  </div>
</template>

<script>
  let checkTimer = null;

  export default {
    name: 'CheckPtClassTree',
    props: {
      treeList: {
        required: true,
        type: Array,
        default: () => []
      },
    },
    data() {
      return {

      }
    },

    methods: {
      handleClickCheckBox(node) {
        if (node.disabled) return 'disabled';

        /* clearTimeout(checkTimer)
        checkTimer = null;
        checkTimer = setTimeout(() => {

        }, 500); */
        const isCheck = !node.check; // 最新勾选状态
        this.handleCheckNode(node, isCheck) // 修改自己及子节点
        this.$emit('node-check', node) // 修改父节点
      },
      handleCheckNode(node, isCheck) {
        node.disabled || (node.check = isCheck);
        node.children && node.children.forEach(child => { this.handleCheckNode(child, isCheck) });
      },
      handleCheckNodeParent(child) {
        const parent = this.treeList.find((v) => v.id === child.parentId);
        if (parent) {
          if (!parent.disabled) {
            const isCheck = !parent.children.some(v => !v.check);
            parent.check = isCheck;
          }
          this.$emit('node-check', parent)
        }
      },
      // TODO 收起/展开？
      handleToggleNodeExtend(node) {
        node.collapse = !node.collapse;
      }
    },
  }
</script>

<style lang="less" scoped>
// 样式定义在checkPtClassTree组件
</style>
