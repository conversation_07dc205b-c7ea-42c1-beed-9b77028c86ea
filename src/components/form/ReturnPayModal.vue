<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="750"
         title="退款">
    <Form :model="modaldata"
      class="modal-form"
      ref="typeForm"
      :label-width="80">
      <template v-if="showModal">
        <Form-item v-if="payType" label="退款方式" prop="pay_type" :rules="{required: true,  message: '请填写退款方式' }">
          <PayTypeSelect
            v-if="payType"
            v-model="modaldata.pay_type"
            :disabled="showCardPay && initPayType === 8"
            :showSQB="false"
            :showCardPay="showCardPay && initPayType === 8"/>
        </Form-item>
        <Form-item
          v-if="Array.isArray(newPayType) && newPayType.length"
          label="退款方式"
          prop="new_pay_type"
          :rules="{required: parseFloat(amount) != 0, type: 'array', min: 1, message: '退款方式为必选项'}">
          <pay-type-list
            v-model="modaldata.new_pay_type"
            :amount="parseFloat(amount)"
            :userId="userId"
            :showSQB="false"
            :showCardPay="!notDate"
            isRefund />
        </Form-item>
      </template>
      <FormItem v-if="!notDate" label="退款时间" prop="refund_time" :rules="{required: true,  message: '请填写退款时间' }">
        <Date-picker type="date" format="yyyy-MM-dd" :options="options" placeholder="选择日期" v-model="modaldata.refund_time"></Date-picker>
      </FormItem>
      <FormItem v-if="showMark" label="备注">
        <Input v-model="modaldata.mark" />
      </FormItem>
    </Form>
      <div slot="footer"
           class="modal-buttons">
        <Button type="success"
                @click="handleConfirm">确定</Button>
        <Button
                @click="showModal = false">取消</Button>
      </div>
  </Modal>
</template>

<script>
import PayTypeSelect from 'components/form/PayTypeSelect'
import PayTypeList from 'components/form/PayTypeList'
import { formatDate } from "@/utils/index"
export default {
  name: 'ReturnPayModal',
  props: {
    value: {
      type: Boolean
    },
    userId: {
      type: [String, Number],
      default: ''
    },
    showCardPay: {
      type: Boolean,
      default: false
    },
    showMark: {
      type: Boolean,
      default: false
    },
    payType: { // 商品目前还是原方式
      type: [String, Number],
      default: ''
    },
    newPayType: { // 定金押金组合方式
      type: Array,
      default: () => []
    },
    amount: { // 定金押金总金额/最大可退款金额
      type: [String, Number],
      default: 0
    },
    createTime: {
      type: String,
      default: ''
    },
    notDate: {
      type: Boolean,
      default: false
    }
  },
  components: {
    PayTypeSelect,
    PayTypeList
  },
  data() {
    return {
      initPayType: '',
      modaldata: {
        pay_type: '',
        new_pay_type: [],
        refund_time: '',
        mark: ''
      },
      options: {
        disabledDate: date => {
          const createTime = new Date(this.createTime)
          if (!createTime) {
            return false
          }
          return (date.valueOf() > Date.now()) || (createTime.valueOf() > date.valueOf())
        }
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    payType(val) {
      this.initPayType = Number(val) || ''
      // 退款支付方式不包含收钱吧
      let name = this.$store.getters['pay/getPayNameById'](val, true)
      if (Number(val) === 20 || name === '暂无') {
        this.modaldata.pay_type = ''
      } else {
        this.modaldata.pay_type = Number(val) || ''
      }
    },
    showModal(val) {
      if (!val) {
        this.$refs.typeForm.resetFields()
      } else {
        if (this.newPayType && this.newPayType.length) {
          this.modaldata.new_pay_type = this.newPayType.map(v => ({ ...v }))
        }
      }
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.typeForm.validate(valid => {
        if (valid) {
          const data = {
            ...this.modaldata,
            refund_time:
              this.modaldata.refund_time ? formatDate(this.modaldata.refund_time, 'yyyy-MM-dd') : ''
          }
          // 只返回对应需要的pay_type数据
          if (this.newPayType && this.newPayType.length) {
            delete data.pay_type
            data.new_pay_type = data.new_pay_type.filter(v => {
              // 不能退款收钱吧，押金时不能储值卡退款
              return v.pay_type === 8
                ? !this.notDate || +v.amount
                : +v.amount && ![20, 21].includes(v.pay_type)
            })
          } else {
            delete data.new_pay_type
          }
          this.$emit('on-confirm', data)
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
