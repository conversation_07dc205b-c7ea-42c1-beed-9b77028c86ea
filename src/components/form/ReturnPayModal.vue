<template>
  <Modal :mask-closable="false"
         v-model="showModal"
         width="750"
         title="退款">
    <div v-if="showWarning" style="margin: 0 auto 20px auto; width: 60%">
      <Alert type="warning" show-icon>会员端自助购买, 退款原路返回</Alert>
    </div>
    <Form :model="modaldata"
      class="modal-form"
      ref="typeForm"
      :label-width="80">
      <template v-if="showModal">
        <Form-item v-if="payType" label="退款方式" prop="pay_type" :rules="{required: true,  message: '请填写退款方式' }">
          <PayTypeSelect
            v-if="payType"
            v-model="modaldata.pay_type"
            :disabled="(showCardPay && [8, 20].includes(initPayType)) || !change"
            :showSQB="true"
            :showCardPay="showCardPay && initPayType === 8"/>
        </Form-item>
        <Form-item
          v-if="Array.isArray(newPayType) && newPayType.length"
          label="退款方式"
          prop="new_pay_type"
          :rules="{required: parseFloat(amount) != 0, type: 'array', min: 1, message: '退款方式为必选项'}">
          <pay-type-list
            v-model="modaldata.new_pay_type"
            :amount="parseFloat(amount)"
            :userId="userId"
            :showSQB="true"
            :showCardPay="!notDate"
            isRefund />
        </Form-item>
        <Form-item v-if="payType2" label="" prop="pay_type2" :rules="{required: true,  message: '请填写退款方式' }">
          <PayTypeSelect
            v-if="payType2"
            v-model="modaldata.pay_type2"
            :disabled="(showCardPay && [8, 20].includes(initPayType2)) || !change2"
            :showSQB="true"
            :showCardPay="showCardPay && initPayType2 === 8"/>
        </Form-item>
        <Form-item
          v-if="Array.isArray(newPayType) && newPayType.length"
          label="退款方式"
          prop="new_pay_type2"
          :rules="{required: parseFloat(amount) != 0, type: 'array', min: 1, message: '退款方式为必选项'}">
          <pay-type-list
            v-model="modaldata.new_pay_type2"
            :amount="parseFloat(amount)"
            :userId="userId"
            :showSQB="true"
            :showCardPay="!notDate"
            isRefund />
        </Form-item>
      </template>
      <FormItem v-if="!notDate" label="退款时间" prop="refund_time" :rules="{required: true,  message: '请填写退款时间' }">
        <Date-picker type="date" format="yyyy-MM-dd" :options="options" placeholder="选择日期" v-model="modaldata.refund_time"></Date-picker>
      </FormItem>
      <FormItem v-if="showMark" label="备注">
        <Input v-model="modaldata.mark" />
      </FormItem>
    </Form>
      <div slot="footer"
           class="modal-buttons">
        <Button type="success"
                @click="handleConfirm">确定</Button>
        <Button
                @click="showModal = false">取消</Button>
      </div>
  </Modal>
</template>

<script>
import PayTypeSelect from 'components/form/PayTypeSelect'
import PayTypeList from 'components/form/PayTypeList'
import { formatDate } from "@/utils/index"
export default {
  name: 'ReturnPayModal',
  props: {
    value: {
      type: Boolean
    },
    userId: {
      type: [String, Number],
      default: ''
    },
    showCardPay: {
      type: Boolean,
      default: false
    },
    showMark: {
      type: Boolean,
      default: false
    },
    payType: { // 商品目前还是原方式
      type: [String, Number],
      default: ''
    },
    newPayType: { // 定金押金组合方式
      type: Array,
      default: () => []
    },
    amount: { // 定金押金总金额/最大可退款金额
      type: [String, Number],
      default: 0
    },
    createTime: {
      type: String,
      default: ''
    },
    notDate: {
      type: Boolean,
      default: false
    },
    showWarning: {
      type: Boolean,
      default: false
    },
    payType2: {
      type: [String, Number],
      default: ''
    },
    change: {
      type: Boolean,
      default: true
    },
    change2: {
      type: Boolean,
      default: true
    }
  },
  components: {
    PayTypeSelect,
    PayTypeList
  },
  data() {
    return {
      initPayType: '',
      initPayType2: '',
      modaldata: {
        pay_type: '',
        pay_type2: '',
        new_pay_type: [],
        new_pay_type2: [],
        refund_time: '',
        mark: ''
      },
      options: {
        disabledDate: date => {
          const createTime = new Date(this.createTime)
          if (!createTime) {
            return false
          }
          return (date.valueOf() > Date.now()) || (createTime.valueOf() > date.valueOf())
        }
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    payType(val) {
      this.initPayType = Number(val) || ''
      // 退款支付方式不包含收钱吧
      let name = this.$store.getters['pay/getPayNameById'](val, true)
      if (name === '暂无') {
        this.modaldata.pay_type = ''
      } else {
        this.modaldata.pay_type = Number(val) || ''
      }
    },
    payType2(val) {
      this.initPayType2 = Number(val) || ''
      // 退款支付方式不包含收钱吧
      let name = this.$store.getters['pay/getPayNameById'](val, true)
      if (name === '暂无') {
        this.modaldata.pay_type2 = ''
      } else {
        this.modaldata.pay_type2 = Number(val) || ''
      }
    },
    showModal(val) {
      if (!val) {
        this.$refs.typeForm.resetFields()
        this.modaldata.mark = ''
      } else {
        if (this.newPayType && this.newPayType.length) {
          this.modaldata.new_pay_type = this.newPayType.map(v => ({ ...v }))
          this.modaldata.new_pay_type2 = this.newPayType.map(v => ({ ...v }))
        }
      }
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.typeForm.validate(valid => {
        if (valid) {
          const data = {
            ...this.modaldata,
            refund_time:
              this.modaldata.refund_time ? formatDate(this.modaldata.refund_time, 'yyyy-MM-dd') : ''
          }
          // 只返回对应需要的pay_type数据
          if (this.newPayType && this.newPayType.length) {
            delete data.pay_type
            delete data.pay_type2
            data.new_pay_type = data.new_pay_type.filter(v => {
              // 不能退款收钱吧，押金时不能储值卡退款
              return v.pay_type === 8
                ? !this.notDate || +v.amount
                : +v.amount && ![21].includes(v.pay_type)
            })
            data.new_pay_type2 = data.new_pay_type2.filter(v => {
              // 不能退款收钱吧，押金时不能储值卡退款
              return v.pay_type === 8
                ? !this.notDate || +v.amount
                : +v.amount && ![21].includes(v.pay_type)
            })
          } else {
            delete data.new_pay_type
            delete data.new_pay_type2
          }
          this.$emit('on-confirm', data)
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
