<template>
  <Modal v-model="showPic" :mask-closable="false" title="模板图片选择" class="choosepic">
    <div class="cfmaskingbox-c">
				<ul>
					<li v-for="item in modelPicAddr" :key="item.addr">
						<div class="imgpicbox">
							<div class="imgpic">
								<img :src="item.addr">
								<div class="use" @click="tpl_checked(item.addr)">使用</div>
								<a :href="item.addr" target="_blank">
								  <div class="preview">预览</div>
                </a>
							</div>
						</div>
					</li>
				</ul>
			</div>
    <div slot="footer">
    </div>
  </Modal>
</template>
<script>

export default {
  data() {
    return {
    }
  },
  props: {
    value: {
      type: Boolean
    },
    modelPicAddr: {
      type: Array
    }
  },
  computed: {
    showPic: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    tpl_checked(picaddr) {
      this.showPic = false;
      this.$emit('selectPic',picaddr)
    }
  }
}
</script>
<style lang="less" scoped>
.cfmaskingbox-c{
	width: 100%;
	height: 390px;
	overflow-y: scroll;
	overflow-x:hidden;
}
.cfmaskingbox-c li{
	width: 50%;
	padding: 23px;
	float: left;
}
.imgpic{
	width: 180px;
	height: 180px;
	border: 1px solid #dedede;
}
.imgpic img{
	display: block;
	width: 178px;
	height: 113px;
	margin-top: 18px;
	border: none;
	cursor: pointer;
}
.use{
	clear: both;
	overflow: hidden;
	float: left;
	width: 50%;
	text-align: center;
	margin-top: 18px;
	border: 1px solid #dedede;
	cursor: pointer;
	border-left: none;
	color: #999999;
	height: 30px;
	line-height: 30px;
}
.use:hover{
	color: #d93220;
}
.preview{
	float: left;
	width: 50%;
	text-align: center;
	margin-top: 18px;
	border: 1px solid #dedede;
	border-left: none;
	cursor: pointer;
	color: #999999;
	height: 30px;
	line-height: 30px;
}
.preview:hover{
	color: #d93220;
}
.choosepic /deep/ .ivu-modal-body {
  padding-top: 0;
}

</style>
