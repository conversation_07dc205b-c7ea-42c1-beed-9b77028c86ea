<template>
<Modal v-model="showAdd" :mask-closable="false" title="更换图片" :width="450">
  <Tabs type="card">
    <TabPane label="模板库" name="模板库">
      <div class="pic-box">
       <ul>
         <li @click="changeImg('https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg1.jpg')">
            <img src="https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg1.jpg">
            <h3>模板1</h3>
         </li>
         <li @click="changeImg('https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg.jpg')">
            <img src="https://imagecdn.rocketbird.cn/minprogram/member/image/share-bg.jpg">
            <h3>模板2</h3>
         </li>
       </ul>
      </div>
    </TabPane>
    <TabPane label="自定义" name="自定义">
      <div class="self-wrap">
        <CroperImg v-if="showAdd" ref="croperImage" v-model="imgUrl" :options="options" :maxSize="1"/>
        <div class="tips">
          <p>
            <span class="red">Tips：</span>  请创建活动后，在活动列表处下载活动二维码并附在自行设计的分享图上，再上传设计图
          </p>
          <p>
             推荐分享图尺寸： <span class="red">750 * 1334 </span>像素
          </p>
          <p>
             推荐分享图大小： <span class="red">&lt;100 </span>KB
          </p>
        </div>
       
      </div>
			
    </TabPane>
  </Tabs>
	<span slot="footer"></span>
</Modal>
</template>
<script>
import CroperImg from './cropper.vue'
export default {
  data() {
    return {
      imgUrl: ''
    }
  },
  created() {
  },
  props: {
    value: {
      type: Boolean
    },
    options: {},
  },
	computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
		imgUrl(val,oldVal){
			if(val && val!=oldVal) {
				this.changeImg(this.imgUrl)
			}
		}
  },
	components: {
		CroperImg
	},
  methods: {
    changeImg(path){
      this.$emit('on-change',path)
      this.showAdd = false;
    }
  }
}
</script>
<style lang="less" scoped>
.pic-box {
  margin: 30px auto 0;
  /* min-height: 500px;
  min-width: 500px; */
}
.pic-box ul li {
  float: left;
  width: 188px;
  cursor: pointer;
  text-align: center;
  margin-bottom: 15px;
  margin-right: 15px;
}
.pic-box img {
  width: 188px;
  height: 334px;
  margin-bottom: 5px;
}
.self-wrap {
  margin-top: 40px;
  .tips {
    padding-top: 20px;
    line-height: 20px;
    .red {
      color: #ff0000;
    }
    p {
      margin-bottom: 10px;
    }
  }
}
</style>
