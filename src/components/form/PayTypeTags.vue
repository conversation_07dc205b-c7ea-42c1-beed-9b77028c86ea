<template>
<div class="paytype-tags">
  <RadioGroup v-model="payType"
          :filterable="filterable"
          :clearable="clearable"
          @on-change="payTypeChange"
          :disabled="disabled"
          type="button">
    <Radio v-for="item in payTypes"
            v-if="item.usable===1 && isShowOption(item.pay_type_id)"
            :key="item.id"
            :label="item.pay_type_id">{{item.pay_type_name}}</Radio>
  </RadioGroup>
  <div v-if="showDragonFly">
    <PayByDragonFly @on-dragonfly-confirm="dragonFlyConfirm" @on-dragonfly-cancel="dragonFlyCancel" :describe="describe" v-model="showDragonFly" :list="payedList" :payType="payType" :amount="amount" :userId="userId" :isEqual="isEqual" :phone="phone" :serviceType="1" />
  </div>
</div>
  
</template>

<script>
// import { mapState } from 'vuex'
import PayByDragonFly from './PayByDragonFly'
export default {
  name: 'PayTypeTags',
  props: {
    value: {},
    amount: {},
    // 储值卡支付
    showCardPay: {
      type: Boolean,
      default: false
    },
    // 收钱吧支付
    showSQB: {
      type: Boolean,
      default: true
    },
    isEqual: {
      type: Boolean,
      default: true
    },
    //是否拉起收钱吧支付弹窗
    needShowDragonFly: {
      type: Boolean,
      default: true
    },
    //描述附加信息
    describe: {
      type: String,
      default: '购卡'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    userId: {
      type: [String, Number],
      default: ''
    },
    phone: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    payedList: {
      type: Array,
      default: ()=> []
    },
    busId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDragonFly: false,
      payTypes: []
    }
  },
  components: {
    PayByDragonFly
  },
  computed: {
    // ...mapState('pay', ['payTypes']),
    payType: {
      get () {
        let val = Number(this.value) || ''
        // 支付方式赋值过滤
        if (val!=='' && (((val === 20 || val === 21) && !this.showSQB) || (val === 8 && !this.showCardPay))) {
          val = ''
          this.$emit('input', val)
        }
        return val
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    busId(newValue, oldValue) {
      if (newValue !== '' && newValue !== oldValue) {
        this.payType = ''
        this.getPayTypes()
      }
    }
  },
  created () {
    this.getPayTypes()
  },
  methods: {
    getPayTypes() {
      return this.$service.post('/Web/PaymentManage/getBusPayType', {
          bus_id: this.busId
        }, { 
          loading: false 
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.payTypes = res.data.data?.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    // 8 储值卡支付 20 收钱吧  21 杉德支付 
    isShowOption (id) {
      return (id != 8 && id != 20 && id != 21) || (id == 8 && this.showCardPay)|| ((id == 20 || id == 21) && this.showSQB)
    },
    payTypeChange (val) {
      if(this.showSQB && (val == 20 || val == 21) && this.needShowDragonFly) {
        this.$nextTick(()=> {
          this.showDragonFly = true
        })
      }
      this.$emit('on-change', val)
    },
    dragonFlyConfirm(infoObj) {
      this.$emit('on-dragonfly-confirm', infoObj)
    },
    dragonFlyCancel(infoObj) {
      this.payType = ''
      this.$emit('on-change', '')
      this.$emit('on-dragonfly-cancel')
    }
  },
};
</script>
<style lang="less">
.paytype-tags {
  width: 450px;
  .ivu-radio-group-button .ivu-radio-group-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 60px;
    text-align: center;
    margin: 0 10px 10px 0;
    white-space: normal;
    border-radius: 4px;
  }
  .ivu-radio-group-button .ivu-radio-wrapper-checked {
    border-color: #2d8cf0;
  }
}

</style>
