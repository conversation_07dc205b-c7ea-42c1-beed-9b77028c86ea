<template>
  <Select v-model="jobId" @on-change="jobChanged" :placeholder="placeholder" clearable filterable :disabled="this.disabled" class="job-select">
    <slot></slot>
    <Option v-for="item in jobList" :key="item.job_id+item.staff_type" :label="item.job_name" :value="item.job_id+'_'+item.staff_type">
    </Option>
  </Select>
</template>

<script>
export default {
  name: 'jobSelect',
  data() {
    return {
      jobList: [],
    }
  },
  props: {
    value: {
      type: [String, Number]
    },
    placeholder: {
      type: String,
      default: '职务'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    busId: {
      type: String,
      default: ''
    }
  },
  computed: {
    jobId: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    busId(newValue, oldValue) {
      if (newValue !== '' && newValue !== oldValue) {
        this.jobId = ''
        this.initData()
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    jobChanged(jobId) {
      let jobInfo = ''
      this.jobList.forEach((item, index) => {
        if (item.job_id === jobId) {
          jobInfo = item
        }
      })
      this.$emit('on-change', jobInfo)
    },
    initData() {
      this.jobList = []
          this.$service
            .post('/Web/SalaryRule/get_job', {
              bus_id: this.busId
            })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.jobList = res.data.data
              } else {
                this.$Message.error(res.data.errormsg)
              }
            })
            .catch(err => {
              this.$Message.error(err)
            })
    }
  }
}
</script>
