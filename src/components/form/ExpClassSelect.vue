<template>
  <Select v-model="expId"
          @on-change="expChanged"
          :placeholder="placeholder"
          :clearable="clearable"
          filterable
          :disabled="this.disabled">
    <slot></slot>
    <Option v-for="item in expList"
            :key="item.card_id"
            :label="item.card_name"
            :value="item.card_id">
            <span>{{item.card_name}}</span>
            <span v-if="showNumber" style="float:right;color:#ccc">{{item.number}}节</span>
    </Option>
  </Select>
</template>

<script>
export default {
  name: 'ExpClassSelect',
  data () {
    return {
      expList: [],
    }
  },
  props: {
    value: {
      type: [String, Number]
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    showNumber: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    expId: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
  },
  created () {
    this.initData()
  },
  methods: {
    expChanged (expId) {
      let expInfo = ''
      this.expList.forEach((item, index) => {
        if (item.card_id === expId) {
          expInfo = item
        }
      })
      this.$emit('on-change', expInfo)
    },
    initData () {
      this.expList = []
      this.$service
        .get('/Web/Member/get_experience_pt_card')
        .then(res => {
          if (res.data.errorcode === 0) {
            this.expList = res.data.data.list
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    }
  }
}
</script>
