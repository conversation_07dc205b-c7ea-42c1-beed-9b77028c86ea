<template>
<Modal v-model="showAdd" :mask-closable="false" title="更换图片" :width="600">
  <Tabs type="card" @on-click="tabChange">
    <TabPane label="图片库" name="图片库">
      <div class="pic-box">
       <ul>
         <li @click="changeImg('https://imagecdn.rocketbird.cn/frontend/image/card.png')">
            <img src="https://imagecdn.rocketbird.cn/frontend/image/card.png">
            <h4>体验卡</h4>
         </li>
         <li @click="changeImg('https://imagecdn.rocketbird.cn/minprogram/member/image/discount.png')">
            <img src="https://imagecdn.rocketbird.cn/minprogram/member/image/discount.png">
            <h4>折扣券</h4>
         </li>
         <li @click="changeImg('https://imagecdn.rocketbird.cn/frontend/image/goods.png')">
            <img src="https://imagecdn.rocketbird.cn/frontend/image/goods.png">
            <h4>其它</h4>
         </li>
       </ul>
      </div>
    </TabPane>
    <TabPane label="自定义" name="自定义">
      <div class="image-description" style="padding: 15px 5px">
        <p class="tip">图片最佳尺寸: 500X500</p>
        <p class="tip">推荐图片大小: &lt;100kb</p>
        <p class="tip">格式限制: jpg、png</p>
      </div>
			<CroperImg v-if="showAdd" ref="croperImage" v-model="imgUrl" />
    </TabPane>
  </Tabs>
	<span slot="footer"></span>
</Modal>
</template>
<script>
import CroperImg from './cropper.vue'
export default {
  data() {
    return {
      imgUrl: ''
    }
  },
  created() {
  },
  props: {
    value: {
      type: Boolean
    }
  },
	computed: {
    showAdd: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
		imgUrl(val,oldVal){
			if(val && val!=oldVal) {
				this.changeImg(this.imgUrl)
			}
		}
  },
	components: {
		CroperImg
	},
  methods: {
    changeImg(path){
      this.$emit('on-change',path)
      this.showAdd = false;
    },
		tabChange(name){
			if(name === '自定义'){
				this.$refs.croperImage.$refs.uploadInput.click();
			}
		}
  }
}
</script>
<style scoped>
.avatar-logo img{
	width: 500px;
	height: 500px;
}
.buttons-wrap{
  display: flex;
  justify-content: center;
}
.pic-box {
  margin: 30px auto 0;
  /* min-height: 500px;
  min-width: 500px; */
}
.pic-box ul li {
  float: left;
  width: 100px;
  cursor: pointer;
  text-align: center;
  margin-bottom: 15px;
  margin-right: 15px;
}
.pic-box img {
  width: 100px;
  height: 100px;
  margin-bottom: 5px;
}
</style>
