<template>
  <Modal :mask-closable="false" @on-cancel="onCancel" v-model="showModal" title="收款">
    <Form ref="payForm"
          v-if="status==='0'"
          :model="postData"
          class="modal-form"
          style="padding: 0 30px"
          :label-width="80">
      <Form-item>
        <RadioGroup v-model="type" v-if="showPrePay">
          <Radio :label="0">
              <span>从已收款中选择抵扣</span>
          </Radio>
          <Radio :label="1">
              <span>使用设备收款</span>
          </Radio>
        </RadioGroup>
      </Form-item>
      <Form-item v-if="type === 0" label="抵扣款项">
        <Checkbox-group v-if="payedList && payedList.length" v-model="payOrderIds">
          <Checkbox :label="payed.id"  v-for="payed in payedList" :key="payed.id">预收款抵扣 <span class="text-red">{{payed.amount}}</span></Checkbox>
        </Checkbox-group>
        <div v-else>暂无已收款</div>
      </Form-item>
      <div v-if="type === 1">
        <Form-item label="收款金额" prop="amount" :rules="{required: true, type: 'string', pattern: /^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d*(\.\d{0,2})?)$/, message: '金额必须大于0且只能保留两位小数'}">
          <Input v-model="postData.amount" />
        </Form-item>
        <Form-item label="收款设备" prop="remark">
          <RadioGroup class="radiobg-tags" :value="postData.device_type+'---'+postData.device_id" type="button" @on-change="handleConfirm">
            <Radio v-for="item in deviceList" :label="item.device_type+'---'+item.device_id" :key="item.device_type+'_'+item.device_id" :class="item.device_type==1?'dragonfly-bg':''">{{item.device_name}}</Radio>
          </RadioGroup>
        </Form-item>
      </div>
    </Form>
    <div v-else-if="!showScanBar">
      <div v-if="postData.device_type === '1'">
        收款信息已发送到蜻蜓机
      </div>
      <Spin v-if="status==='1'">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>付款状态查询中</div>
      </Spin>
      <div v-if="status==='2'">付款成功</div>
      <div v-if="status==='3'">付款失败</div>
      <div v-if="status==='4'">用户取消支付</div>
    </div>
    <div v-else>
      <ScanBarCode @on-enter="handleScanEnter" />
    </div>
    <div slot="footer"
         class="modal-buttons">
         <div v-if="status==='0' && type !== 1">
           <Button type="success" @click="handleConfirm">确定</Button>
           <Button @click="handleCancelPay">取消</Button>
         </div>
         <Button v-if="status=='1' && !showScanBar && postData.device_type === '1'" type="text" @click="handleCancel">取消收款</Button>
    </div>
  </Modal>
</template>

<script>
import ScanBarCode from './ScanBarCode';
export default {
  name: 'PayByDragonFly',
  props: {
    describe: {
      type: [String, Number]
    },
    amount: {
      type: [String, Number],
      default: 0
    },
    isEqual: {
      type: Boolean,
      default: true
    },
    value: {
      type: Boolean
    },
    userId: {
      type: [String, Number],
      default: ''
    },
    phone: {
      type: [String, Number],
      default: ''
    },
    payType: {
      type: [String, Number]
    },
    serviceType: { // 1为商品支付 2为购续升编辑等卡相关操作，3为收押金，其它场景暂时为空
      type: Number,
    },
    list: { // 购卡等页面使用多支付方式  预售列表从外部传入以便去重已选预收项
      type: Array,
      default: ()=> []
    }
  },
  components: { ScanBarCode },
  data() {
    return {
      payedList: [],
      deviceList: [],
      type: 0,
      status: '0',
      orderNo: '',
      orderId: '',
      showScanBar: false,
      showPrePay: true,
      payStatusTimer: '',
      timeOutCloseTimer: '',
      payOrderIds: [],
      totalAmount: 0,
      postData: {
        user_id: '',
        auth_code: '', // 扫码枪auth_code（必须存在device_id或auth_code）
        amount: '',
        describe: '',
        device_id: '' // 蜻蜓机设备id（必须存在device_id或auth_code）
      }
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    userId(val, oldVal) {
      this.postData.user_id = val
      if(val && oldVal !== val) {
        this.getPayedList()
        this.payOrderIds = []
      }
    },
    amount: {
      handler(val) {
        this.postData.amount = String(val)
      },
      immediate: true
    },
    showModal(val) {
      if (!val) {
        this.$refs.payForm.resetFields()
      }
    },
    describe: {
      handler(val) {
        this.postData.describe = val
      },
      immediate: true
    },
    payOrderIds(idsArr) {
      this.totalAmount = 0
      for (const iterator of this.payedList) {
        if(idsArr.indexOf(iterator.id) !== -1) {
          this.totalAmount = (Number(this.totalAmount) + Number(iterator.amount)).toFixed(2)
        }
      }
    }
  },
  created() {
    this.postData.user_id = this.userId?this.userId:this.$route.params.userId?this.$route.params.userId:this.$route.query.userId
    if (this.list && this.list.length) {
      this.payListInit(this.list)
    } else {
      this.getPayedList()
    }
    this.getDeviceList()
  },
  beforeDestroy() {
    this.payStatusTimer && clearTimeout(this.payStatusTimer)
    this.timeOutCloseTimer && clearTimeout(this.timeOutCloseTimer)
  },
  methods: {
    payListInit(list) {
      this.payedList = list.filter((item)=> item.show !== false && item.pay_type === this.payType)
      if (!this.payedList || !this.payedList.length) {
        this.type = 1
        this.showPrePay = false
      } else {
        this.showPrePay = true
      }
    },
    handleCancelPay() {
      this.showModal = false
      this.onCancel()
    },
    onCancel() {
      this.$emit('on-dragonfly-cancel',{
        payType: this.payType
      })
    },
    handleCancel() {
      this.$service
        .post('/Web/Pay/cancelPay', { order_no: this.orderNo })
        .then(res => {
          this.handleCancelPay()
          if (res.data.errorcode != 0) {
            this.$Message.error(res.data.errormsg)
          }
        }).catch(()=>{
          this.handleCancelPay()
        })
    },
    handleScanEnter(data) {
      this.postData.auth_code = data
      this.payByDragonFly()
    },
    handleConfirm(info) {
      let totalAmount = this.type === 1?this.postData.amount : this.totalAmount
      if(Number(this.amount)<totalAmount ) {
        this.$Message.error('金额超过剩余应收！')
        return false
      }
      if(this.isEqual && Number(this.amount)!=totalAmount ) {
        this.$Message.error('支付金额不等于应收金额')
        return false
      }
      if (this.type === 0) {
        this.emitConfirm()
      } else {
        this.$refs.payForm.validate(valid => {
          if (valid && info) {
            const [deviceType , deviceID] = info.split('---')
            this.postData.device_id = deviceID || ''
            this.postData.device_type = deviceType || ''
            if(deviceType == 2) {
              this.payByScan()
            } else {
              this.payByDragonFly()
            }
          }
        })
      }

    },
    emitConfirm() {
      this.showModal = false
      this.$emit('on-dragonfly-confirm', {
        type: this.type,
        pay_order_ids: this.payOrderIds,
        payType: this.payType,
        amount: this.totalAmount
      })
    },
    payByScan() {
      this.status = '1'
      this.showScanBar = true
    },
    payByDragonFly() {
      this.showScanBar = false
      this.$service.post('/Web/Pay/pay', { ...this.postData, pay_type: this.payType }).then(res => {
        if (res.data.errorcode == 0) {
          this.status = '1'
          this.orderNo = res.data.data.order_no
          this.orderId = res.data.data.id
          this.timeOutClose()
          this.getPayStatus()
        } else {
          this.status = '3'
          this.$Message.error(res.data.errormsg)
        }
      })
    },
    getPayStatus() {
      this.$service
        .post(
          '/Web/Pay/checkPayStat',
          { order_no: this.orderNo },
          { loading: false }
        )
        .then(res => {
          //status：1-正在支付，2-支付成功，3-支付失败，4-用户取消支付
          if (
            res.data.errorcode == 0 &&
            res.data.data &&
            (res.data.data.status === '2' ||
              res.data.data.status === '3' ||
              res.data.data.status === '4')
          ) {
            this.status = res.data.data.status
            if(res.data.data.status === '2') {
               this.$Message.success('支付成功！')
              this.totalAmount = this.postData.amount
              this.payOrderIds = [String(this.orderId)]
              this.emitConfirm()
            } else {
              this.onCancel()
            }
            setTimeout(() => {
              this.showModal = false
            }, 3000)
          } else {
            this.payStatusTimer = setTimeout(() => {
              this.getPayStatus()
            }, 3000)
          }
        })
    },
    timeOutClose(){
      if(this.timeOutCloseTimer) {
        return;
      }
      // 超时5分钟未支付取消订单
      this.timeOutCloseTimer = setTimeout(() => {
        if(this.showModal){
          this.$Message.error('支付超时，弹窗即将关闭！')
          this.handleCancel()
        }
      }, 5*60*1000)
    },
    getPayedList() {
      if(!Number(this.postData.user_id) && !this.phone) {
        return
      }
      this.$service
        .post('/Web/Pay/getPayedList', { user_id: this.postData.user_id, phone: this.phone})
        .then(res => {
          if (res.data.errorcode == 0) {
            this.postData.user_id = res.data.data.user_id
            this.payListInit(res.data.data.list)
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
    },
    getDeviceList() {
      if(!Number(this.postData.user_id) && !this.phone) {
        this.$Message.error('请先选择会员')
        setTimeout(() => {
          this.handleCancelPay()
        }, 0);
        return
      }
      this.$service.post('/Web/Pay/getDevice', {
        service_type: this.serviceType || '',
        pay_type_id: this.payType
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.deviceList = res.data.data
          this.deviceList.length &&
            (this.postData.device_id = this.deviceList[0].device_id)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.radiobg-tags {
  .ivu-radio-wrapper {
    display: inline-flex;
    align-items: flex-end;
    justify-content: center;
    width: 100px;
    height: 60px;
    border-radius: 4px;
    text-align: center;
    margin: 0 10px 10px 0;
    white-space: normal;
    background: url(../../assets/img/saomaqiang.png) #8dbef1 no-repeat top center;
    background-size: 50px 40px;
    border-color: #8dbef1;
    color: #73263b;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    &.dragonfly-bg {
      background: url(../../assets/img/dianshiji.png) #ef9780 no-repeat top center;
      background-size: 50px 40px;
      border-color: #ef9780;
    }
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
</style>
