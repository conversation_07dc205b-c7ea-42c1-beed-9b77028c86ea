<template>
  <Modal
    title="全部折扣券"
    :value="show"
    :width="type === 'delete' ? 600 : 520"
    @on-visible-change="$emit('update:show', $event)"
  >
    <div class="discount-list">
      <div
        v-if="type === 'buy'"
        :label="-1"
        class="discount-card no-choose"
        @click="handleRadio('', -1)">
        <div>不使用折扣券</div>
        <Icon
          type="ios-checkmark-circle"
          size="36"
          :color="selectIndex === -1 ? '#ca2e53' : '#e7e7e7'"
          style="margin-left: auto"></Icon>
      </div>
      <div
        v-for="(card, index) in list"
        :key="card.receive_id"
        class="discount-item"
        @click="handleClick(card, index)">
        <div v-if="type === 'delete'" class="discount-check-box">
          <Icon
            v-if="!card.disabled"
            v-show="selectedIds.includes(card.receive_id)"
            type="ios-checkmark-circle"
            size="36"
            color="#ca2e53"
          />
          <i v-show="!selectedIds.includes(card.receive_id)" class="icon-circle" />
        </div>
        <div class="discount-card" :class="{ 'disabled-card': card.disabled }">
          <div class="discount-wrap">
            <div class="top">
              <div
                class="discount-price"
                :class="{'gray-text': card.disabled}">
                <h4>￥{{ card.discount_amount }}</h4>
                <p>满{{ card.limit_amount }}元可用</p>
              </div>
              <div
                class="discount-info"
                :class="{'gray-text': card.disabled}">
                <h4>{{ card.coupon_name }}</h4>
                <p>{{ card.start_time }}~{{ card.end_time }}</p>
                <p>{{ card.use_limit_text }}</p>
              </div>
              <Icon
                v-if="!card.disabled && type === 'buy'"
                type="ios-checkmark-circle"
                size="36"
                :color="selectIndex === index ? '#ca2e53' : '#e7e7e7'"
                style="margin-left: auto" />
            </div>
            <div class="bottom">
              <div v-show="card.limit_card == 1 || card.limit_card == 2 || card.limit_card == 3 || card.limit_card == 4">
                <span>支持使用的卡种: {{ card.limit_card_text == '仅限购指定卡种' ? card.card_species_name && card.card_species_name.join('、') : card.limit_card_text }}</span>
              </div>
              <div v-show="card.limit_space_type == 1 || card.limit_space_type == 2">
                <span>支持使用的场地: {{ card.limit_space_type == 1 ? '全部场地' : card.space_type_name }}</span>
              </div>
              <div v-show="card.limit_san_rule == 1 || card.limit_san_rule == 2">
                <span>支持使用的散场票: {{ card.limit_san_rule == 1 ? '全部散场票' : card.san_rule_name }}</span>
              </div>
              <div v-if="card.limit_class == 2 || card.limit_class == 1">
                <span>支持使用的团课: {{ card.limit_class == 2 ? card.class_type_name : '全部团课' }}</span>
              </div>
            </div>
          </div>
          <div
            v-if="card.disabled && card.status != 1"
            class="tag"
            :class="{'is-used': card.status == 2}">
            {{ card.status == 2 ? '已使用' : '已过期' }}
          </div>
        </div>
      </div>
    </div>
    <div
      slot="footer"
      class="modal-buttons">
      <Button
        v-if="type === 'buy'"
        type="success"
        style="width: 200px; height: 40px; margin-top: 10px"
        @click="$emit('update:show', false)">
        确定
      </Button>
      <template v-if="type === 'delete'">
        <Button
          type="error"
          style="width: 120px; height: 40px; margin-top: 10px"
          :disabled="selectedIds.length === 0"
          @click="deleteDiscount">
          删除折扣券
        </Button>
        <Button
          style="width: 120px; height: 40px; margin-top: 10px"
          @click="$emit('update:show', false)">
          取消
        </Button>
      </template>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'DiscountModal',
    props: {
      type: { // buy 购续升使用, delete 会员详情使用
        type: String,
        required: true
      },
      show: {
        type: Boolean,
        required: true
      },
      isPreOrder: {
        type: Boolean,
        required: true
      },
      list: {
        type: Array,
        required: true
      },
      selectIndex: {
        type: [Number, String],
        default: -1
      }
    },
    data() {
      return {
        selectedIds: []
      }
    },

    watch: {
      show(val) {
        if (val === false) {
          this.selectedIds = []
        }
      }
    },

    methods: {
      deleteDiscount() {
        this.$Modal.confirm({
          title: '删除折扣券',
          content: '确定删除已选折扣券吗？',
          onOk: () => {
            const params = {
              receive_id: this.selectedIds.join()
            };

            this.$service.post('/Web/Coupon/del_coupon_receive', params).then(res => {
              if (res.data.errorcode === 0) {
                this.$Message.success(res.data.errormsg);
                this.$emit('updateData')
                this.$emit('update:show', false)
              } else {
                this.$Message.error(res.data.errormsg);
              }
            })
            .catch(err => {
              console.error(err);
            });
          }
        });
      },
      handleRadio(card, index) {
        // 如果是核单则不支持修改
        if(this.isPreOrder) return this.$Message.warning('核单不支持修改');

        this.$emit('radio', index)
      },
      handleCheck(card, index) {
        const { selectedIds: ids } = this;
        const { receive_id } = card;
        const selectedIndex = ids.indexOf(receive_id);

        selectedIndex === -1 ? ids.push(receive_id) : ids.splice(selectedIndex, 1);
      },
      handleClick(card, index) {
        if (card.disabled) return false;

        switch (this.type) {
          case 'buy':
            this.handleRadio(card, index)
            break;
          case 'delete':
            this.handleCheck(card, index)
            break;
        }
      },
    }
  }
</script>

<style lang="less" scoped>
@border: 1px solid #dee0e2;

.discount-list {
  width: 100%;
  padding: 0 16px;
  max-height: 60vh;
  overflow-y: scroll;
  .discount-item {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
  }
  .discount-card {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    margin-right: 0;
    padding: 20px;
    flex: 1;
    border-radius: 6px;
    border: @border;
    border-top: 5px solid #ca2e53;
    box-shadow: 0 3px 5px rgba(74, 104, 164, 0.15);
    user-select: none;

    .tag {
      position: absolute;
      right: -2px;
      top: 10px;
      text-align: center;
      width: 80px;
      background-color: #e7e7e7;
      height: 26px;
      line-height: 26px;
      color: #898989;
    }

    .is-used {
      background-color: #4ed0c4;
      color: #fff;
    }

    .discount-wrap {
      color: #898989;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      flex: 1;

      .top {
        display: flex;
        align-items: center;
      }

      .bottom {
        padding-top: 10px;
        margin-top: 10px;
        border-top: 1px dashed #eee;
        white-space: pre-wrap;
      }
    }
  }

  .no-choose {
    border-top: @border;
    line-height: 40px;
    font-size: 16px;
  }

  .discount-check-box {
    margin-right: 20px;
    width: 40px;
    .icon-circle {
      display: block;
      margin-left: 3px;
      width: 30px;
      height: 30px;
      border: #ccc 1px solid;
      border-radius: 50%;
    }
  }

  .disabled-card {
    border-top-color: #e7e7e7;
  }

  .discount-price {
    margin-right: 18px;
    padding-right: 18px;
    border-right: 1px dashed #eee;

    h4 {
      font-size: 24px;
      color: #ca2e53;
    }
  }

  .discount-info {
    h4 {
      font-size: 16px;
      color: #313131;
    }
  }

  .gray-text {
    h4 {
      color: #898989;
    }
  }
}
</style>
