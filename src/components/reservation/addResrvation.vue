<template>
  <Select filterable
          clearable
          remote
          not-found-text="未搜索到该会员"
          v-model="searchText"
          :remote-method="getSearchUserList"
          @on-change="userSelected"
          :loading="searching"
          placeholder="姓名/电话/实体卡号">
    <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">{{user.username}}
    </Option>
  </Select>
</template>

<script>
  export default {
    name: 'userSearch',
    data() {
      return {
        searchText: '',
        searching: false,
        userList: null,
      }
    },
    props:['url'],
    methods: {
      // 搜索会员
      getSearchUserList(search) {
        if (search === '') {
          return
        }
        this.searching = true;
        this.userList = null;
        let postData = {
          search: search.trim()
        };
        return this.$service.post(this.url, postData, {loading: false}).then(res => {
          if (res.data.errorcode === 0) {
            this.userList = res.data.data.list;
            this.searching = false;
          }
        }).catch(err => {
          console.error(err)
        })
      },
      userSelected(userId) {
        this.$emit('input', userId)
      }
    }
  }
</script>
