<template>
  <div style="height: 100%">
    <Modal v-model="isEdit" :title="editTitle">
      <Form class="modal-form">
        <FormItem label="组名称" style="padding-left: 50px; padding-top: 30px">
          <Input v-model="editData.name" style="width: 300px" />
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="editConfirm(editData)">确定</Button>
        <Button @click="isEdit = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="isDelete" title="组别删除">
      <div style="padding-left: 40px; font-size: 14px">
        <p>确认删除'
          <span style="color: red">{{ delData.name }}</span>'及其下属组别信息吗?</p>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="delConfirm(delData)">确定</Button>
        <Button @click="isDelete = false">取消</Button>
      </div>
    </Modal>
    <TreeMenu :data="groupList" :options="{titleKey: 'name', idKey: 'id', levelKey: 'group_level', level: 3, levelTop: 1, addTitle: '添加分组', editTitle: '编辑组名', deleteTitle: '删除分组'}" @on-edit="data => addGroup(data, true)" @on-add="data => addGroup(data)" @on-delete="data => delGroup(data)" @on-select="groupId => toggleTree(groupId)"></TreeMenu>
  </div>
</template>
<script>
  import { mapState, mapActions } from 'vuex';
  import TreeMenu from 'components/treeMenu';

  export default {
    name: 'CoachNav',
    components: { TreeMenu },
    data() {
      return {
        groupList: [],
        editData: {},
        editModal: {
          show: false,
          title: '组别编辑'
        },
        delData: {},
        delModal: {
          show: false,
          title: '组别删除'
        },
        currentId: '',
        delParentId: '',
        showTree: true,
        showListIndex: [],

        isEdit: false,
        editTitle: '',
        isDelete: false
      };
    },
    created() {
      this.getGroup();
    },
    computed: {
      ...mapState('employee', {
        resGroupList: 'coachGroup'
      })
    },
    methods: {
      ...mapActions('employee', ['getCoachGroup']),
      async getGroup() {
        await this.getCoachGroup();
        this.dealResGroupList();
        this.groupList = this.groupList.map(item => {
          return {
            ...item,
            ...{
              expand: true,
              children:
                item.children &&
                item.children.map(item => {
                  return {
                    ...item,
                    ...{
                      expand: true
                    }
                  };
                })
            }
          };
        });
      },
      dealChildren(data) {
        for (let item of data) {
          if (item.son && item.son.length) {
            item.children = item.son;
            item.title = item.name;
            this.dealChildren(item.son);
          }
        }
      },
      dealResGroupList() {
        let list = this.resGroupList;
        if (!list || !list.length) return false;

        let obj = {
          children: []
        };
        list.forEach((item, index) => {
          if (item.group_level === 1) {
            obj = { ...item, ...obj };
          } else if (item.group_level === 2) {
            obj.children.push(item);
          } else {
            let children = obj.children;
            let level3Child = children[children.length - 1].children;
            if (level3Child) {
              level3Child.push(item);
            } else {
              children[children.length - 1].children = [item];
            }
          }
        });
        this.groupList = [obj];
        // 默认全部展开分组
        // this.showListIndex = Array.from(new Array(this.groupList.children.length), (val, index) => index);
      },
      addGroup(data, isEdit) {
        let dataObj = {
          group_level: data.group_level,
          id: data.id,
          name: data.name,
          pid: data.pid
        };
        this.editTitle = isEdit ? '组别编辑' : '组别创建';
        this.editData = dataObj;
        this.editData.name = isEdit ? dataObj.name : '';
        this.editData.isEdit = isEdit;
        this.isEdit = true;
      },
      delGroup(data) {
        this.delData = {
          name: data.name,
          group_level: data.group_level,
          id: data.id
        };
        this.isDelete = true;
      },
      toggleTree(id, level, index) {
        this.currentId = id;
        this.$emit('groupChange', id);
      },
      toggleGroup(index) {
        let findIndex = this.showListIndex.findIndex(item => item === index);
        if (findIndex === -1) {
          this.showListIndex.push(index);
        } else {
          this.showListIndex.splice(findIndex, 1);
        }
      },
      editConfirm(data) {
        let postData = {
          pid: data.pid,
          group_level: data.group_level,
          name: data.name,
          id: data.id
        };
        let url = '/Web/CoachGroup/add_coach_group';
        if (data.isEdit === true) {
          url = '/Web/CoachGroup/update_coach_group';
          postData = {
            name: data.name,
            id: data.id
          };
        }
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.data.errorcode === 0) {
              this.getGroup();
              this.isEdit = false;
              this.$Message.success(response.data.errormsg);
            } else {
              this.$Message.error(response.data.errormsg);
            }
          })
          .catch(error => {
            console.error(error);
          });
      },
      delConfirm(data) {
        let postData = {
          id: data.id,
          group_level: data.group_level
        };
        let url = '/Web/CoachGroup/del_coach_group';
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status === 200) {
              if (response.data.errorcode === 0) {
                this.getGroup();
                this.$emit('groupChange', data.pid);
                this.isDelete = false;
                this.$Message.success(response.data.errormsg);
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(error => {
            console.error(error);
          });
      }
    }
  };
</script>
<style scoped>
  h2 {
    font-weight: normal;
    user-select: none;
  }

  .tree {
    width: 100%;
    height: 100%;
    background: #fff;
    border: 1px solid #dcdcdc;
    padding: 10px 0;
  }

  .tree_t {
    width: 100%;
    height: 30px;
  }

  .tree_t_l {
    width: 27px;
    height: 27px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: 4px -1px;
    margin-left: 10px;
  }

  .tree_t_c {
    float: left;
    color: #666666;
    font-size: 18px;
    margin: 0;
    line-height: 30px;
    margin-left: 6px;
    max-width: 60%;
  }

  .tree_t_c,
  .treeli_t_h2,
  .list_c {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .isfocus .tree_t_c,
  .isfocus .treeli_t_h2,
  .isfocus .list_c {
    color: #5cb85c;
  }

  .tree_t_r {
    width: 27px;
    height: 27px;
    float: right;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -287px 0px;
    margin-right: 16px;
    cursor: pointer;
  }

  .tree_t_r:hover {
    opacity: 0.7;
  }

  .treeli_t {
    height: 30px;
  }

  .treeli_t span,
  .list_t span {
    display: none;
  }

  .treeli_t:hover span,
  .list_t:hover span {
    display: block;
  }

  .treeli_t_l {
    width: 27px;
    height: 27px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -34px -4px;
    margin-left: 30px;
  }

  .no-children {
    background-position: -70px -4px;
  }

  .treeli_t_h2 {
    margin: 0;
    float: left;
    color: #666666;
    font-size: 16px;
    line-height: 30px;
    height: 30px;
    max-width: 45%;
  }

  @media screen and (max-width: 1280px) {
    .treeli_t_h2 {
      max-width: 35%;
    }
  }

  .list_t {
    height: 30px;
  }

  .list_l {
    clear: both;
    width: 27px;
    height: 27px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -70px -4px;
    margin-left: 50px;
  }

  .list_c {
    margin: 0;
    float: left;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    line-height: 30px;
    max-width: 40%;
  }

  .ullist li {
    clear: both;
    margin-left: 90px;
    color: #666666;
    font-size: 12px;
    line-height: 20px;
  }

  .dellist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -180px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .dellist:hover {
    opacity: 0.7;
  }

  .emitlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -109px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .emitlist:hover {
    opacity: 0.7;
  }

  .addlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -145px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .addlist:hover {
    opacity: 0.7;
  }

  .hjdpic {
    width: 30px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -248px 2px;
    margin: 0 auto;
  }

  .togglegreen {
    background-position: -212px 2px;
  }
</style>
