<template>
  <div style="height: 100%" v-if="groupList">
    <TreeMenu :data="groupList" :options="{titleKey: 'name', idKey: 'group_id', levelKey: 'group_level', level: 3, levelTop: 1, addTitle: '添加分组', editTitle: '编辑组名', deleteTitle: '删除分组'}" @on-edit="({name, group_id}) => addGroup(name, group_id, true)" @on-add="data => addGroup('', data.group_id)" @on-delete="({name, group_id, pid}) => delGroup(name, group_id, pid)" @on-select="groupId => toggleTree(groupId)"></TreeMenu>
    <Modal v-model="isEdit" :title="editTitle">
      <Form class="modal-form">
        <FormItem label="组名称" style="padding-left: 50px; padding-top: 30px">
          <Input v-model="editData.name" style="width: 300px" />
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="editConfirm(editData)">确定</Button>
        <Button @click="isEdit = false">取消</Button>
      </div>
    </Modal>
    <Modal v-model="isDelete" title="组别删除">
      <div style="padding-left: 40px; font-size: 14px">
        <p style="padding-bottom: 20px">确认删除'
          <span style="color: red">{{ delData.name }}</span>'及其下属组别信息吗?</p>
        <Checkbox v-model="delData.isDel">同时删除组内员工信息</Checkbox>
      </div>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="delConfirm(delData)">确定</Button>
        <Button @click="isDelete = false">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
  import TreeMenu from 'components/treeMenu';
  export default {
    name: 'membershipGroup',
    components: { TreeMenu },
    data() {
      return {
        groupList: [],
        editData: {},
        editModal: {
          show: false,
          title: '组别编辑'
        },
        delData: {
          isDel: false
        },
        delModal: {
          show: false,
          title: '组别删除'
        },
        currentId: '',
        delParentId: '',
        showTree: true,
        showListIndex: [],

        isEdit: false,
        editTitle: '',
        isDelete: false
      };
    },
    created() {
      this.getMarketersGroup();
    },
    watch: {
      isDelete(newValue, oldValue) {
        if (!newValue) {
          this.delData.isDel = false;
        }
      }
    },
    methods: {
      dealChildren(data) {
        for (let item of data) {
          if (item.son && item.son.length) {
            item.children = item.son;
            item.title = item.name;
            this.dealChildren(item.son);
          }
        }
      },
      //获取分组列表
      getMarketersGroup() {
        this.$service
          .get('/Web/MarketersGroup/get_marketers_group')
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                const data = response.data.data;
                // this.groupList = response.data.data;
                this.dealChildren(data);
                const tree = data.map(item => {
                  return {
                    ...item,
                    ...{
                      expand: true,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            ...{
                              expand: true
                            }
                          };
                        })
                    }
                  };
                });
                this.groupList = tree;
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      },
      toggleGroup(index) {
        let findIndex = this.showListIndex.findIndex(item => item === index);
        if (findIndex === -1) {
          this.showListIndex.push(index);
        } else {
          this.showListIndex.splice(findIndex, 1);
        }
      },
      addGroup(name, id, isEdit = false) {
        this.editTitle = isEdit ? '组别编辑' : '组别创建';
        this.editData.id = id;
        this.editData.name = name;
        this.editData.isEdit = isEdit;
        this.isEdit = true;
      },
      delGroup(name, id, parentId) {
        this.delData.id = id;
        this.delData.name = name;
        this.delParentId = parentId;
        this.isDelete = true;
      },
      toggleTree(id) {
        this.currentId = id;
        this.$emit('groupChange', id);
      },
      editConfirm(data) {
        let postData = {
          group_name: data.name,
          group_pid: data.id
        };
        let url = '/Web/MarketersGroup/add_marketers_group';
        if (data.isEdit == true) {
          url = '/Web/MarketersGroup/update_marketers_group';
          postData = {
            group_name: data.name,
            group_id: data.id
          };
        }
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                this.getMarketersGroup();
                this.isEdit = false;
                this.$Message.success(response.data.errormsg);
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      },
      delConfirm(data) {
        let postData = {
          is_delete_marketers: data.isDel == true ? 1 : 0,
          group_id: data.id
        };
        let url = '/Web/MarketersGroup/delete_marketers_group';
        this.$service
          .post(url, postData)
          .then(response => {
            if (response.status == 200) {
              if (response.data.errorcode == 0) {
                this.getMarketersGroup();
                this.$emit('groupChange', this.delParentId);
                this.isDelete = false;
                this.$Message.success(response.data.errormsg);
              } else {
                this.$Message.error(response.data.errormsg);
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    }
  };
</script>
<style scoped>
  h2 {
    font-weight: normal;
    user-select: none;
  }

  .tree {
    width: 100%;
    background: #fff;
    border: 1px solid #dcdcdc;
    padding: 10px 0;
  }

  .tree_t {
    width: 100%;
    height: 30px;
  }

  .tree_t_l {
    width: 30px;
    height: 30px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: 4px -1px;
    margin-left: 10px;
  }

  .tree_t_c {
    float: left;
    color: #666666;
    font-size: 18px;
    margin: 0;
    line-height: 30px;
    margin-left: 6px;
    max-width: 60%;
  }

  .tree_t_c,
  .treeli_t_h2,
  .list_c {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .isfocus .tree_t_c,
  .isfocus .treeli_t_h2,
  .isfocus .list_c {
    color: #5cb85c;
  }

  .tree_t_r {
    width: 27px;
    height: 27px;
    float: right;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -287px 0px;
    margin-right: 16px;
    cursor: pointer;
  }

  .tree_t_r:hover {
    opacity: 0.7;
  }

  .treeli_t {
    height: 30px;
  }

  .treeli_t span,
  .list_t span {
    display: none;
  }

  .treeli_t:hover span,
  .list_t:hover span {
    display: block;
  }

  .treeli_t_l {
    width: 27px;
    height: 27px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -34px -4px;
    margin-left: 30px;
  }

  .treeli_t_h2 {
    margin: 0;
    float: left;
    color: #666666;
    font-size: 16px;
    line-height: 30px;
    height: 30px;
    max-width: 45%;
  }

  @media screen and (max-width: 1280px) {
    .treeli_t_h2 {
      max-width: 35%;
    }
  }

  .list_t {
    height: 30px;
  }

  .list_l {
    clear: both;
    width: 27px;
    height: 27px;
    float: left;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -70px -4px;
    margin-left: 50px;
  }

  .list_c {
    margin: 0;
    float: left;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    line-height: 30px;
    max-width: 40%;
  }

  .ullist li {
    clear: both;
    margin-left: 90px;
    color: #666666;
    font-size: 12px;
    line-height: 20px;
  }

  .dellist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -180px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .dellist:hover {
    opacity: 0.7;
  }

  .emitlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -109px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .emitlist:hover {
    opacity: 0.7;
  }

  .addlist {
    float: right;
    width: 20px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -145px -4px;
    margin-right: 2px;
    cursor: pointer;
  }

  .addlist:hover {
    opacity: 0.7;
  }

  .hjdpic {
    width: 30px;
    height: 30px;
    background: url('../../assets/img/yg_pic.png') no-repeat;
    background-position: -248px 2px;
    margin: 0 auto;
  }

  .togglegreen {
    background-position: -212px 2px;
  }

  .no-children {
    background-position: -70px -4px;
  }
</style>
