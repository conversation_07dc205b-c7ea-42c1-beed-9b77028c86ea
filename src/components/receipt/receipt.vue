<template>
<div>
  <Modal v-model="showModal" width="500">
    <div class="header">
      <Icon type="ios-checkmark-circle-outline" size="28"></Icon>
      <h4>{{curTypeName}}成功！</h4>
    </div>
    <div class="print-icons">
      <div class="icons-wrap" @click.self="handleJoinClick" v-if="showCoursera">
        <Icon @click.self="handleJoinClick" type="ios-school-outline" size="80" />
        加入培训班
      </div>
      <div class="icons-wrap" @click.self="print" v-if="showPrintTemplate">
        <Icon @click.self="print" type="md-print" size="80" />
        <Select class="print-select" v-model="templateId" placeholder="合同模板" transfer placement="top">
          <Option v-for="item in templateList" :key="item.id" :value="item.id">{{item.name}}</Option>
        </Select>
        <div @click.self="print" style="margin-top:10px">打印合同</div>
      </div>
      <router-link v-if="!(toPath.query && toPath.query.oper_type==='cashrefund' && toPath.query.charge_id == 0)" class="icons-wrap" target="_blank" :to="toPath">
        <Icon type="ios-print-outline" size="80" />
        打印小票
      </router-link>
    </div>
    <div slot="footer"></div>
  </Modal>

  <Modal v-model="showJoin" title="加入培训班" width="500">
    <div class="buddy">
      <Form :model="joinForm" :label-width="80">
        <FormItem label="卡课名称">
          <!-- <Input :value="toPath.query?toPath.query.card_name:''" readonly /> -->
          <span>{{ toPath.query?toPath.query.card_name:'' }}</span>
        </FormItem>
        <FormItem label="培训班" prop="course">
          <Select v-model="joinForm.teamclass_id" placeholder="请选择培训班">
            <Option v-for="item in courseList" :value="item.id" :key="item.id">{{ item.teamclass_name }}</Option>
          </Select>
        </FormItem>
      </Form>
    </div>
    <div slot="footer" class="modal-buttons">
      <Button type="success" @click="handleJoinSubmit">确认加入</Button>
      <Button @click="showJoin = false">取消</Button>
    </div>
  </Modal>

</div>
</template>
<script>
export default {
  data() {
    return {
      templateList: [],
      curTypeName: '操作',
      showPrintTemplate: false,
      templateId: '',
      typeDes:{
        'buycard': '购卡',
        'renewcard': '续卡',
        'changecard': '升卡',
        'cancelcard': '销卡',
        'switchcard': '转卡',
        'contract': '补卡',
        'buycard-coach': '购私教',
        'renewcard-coach': '续私教',
        'buycard-swim': '购泳教',
        'renewcard-swim': '续泳教',
        'buypackage': '套餐包',
        'buygoods': '购买',
        'backpay': '销账',
        'addlocker': '租柜',
        'deposit': '定金收取',
        'cash': '押金收取',
        'suspendcard': '请假',
        'seperate': '拆分',
        'depositback': '定金退还',
        'cashrefund': '押金退还',
        'other': '操作',
      },
      showCoursera: false,
      showJoin: false,
      joinForm: {
        teamclass_id: '',
        user_id: ''
      },
      courseList: [],
    }
  },
  props: {
    value: {
      type: Boolean
    },
    toPath: {
      type: Object
    }
  },
  computed: {
    showModal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
        if(!val && this.toPath.query) {
          if(this.toPath.query.type=='4') {
            if(this.toPath.query.oper_type == 'buygoods' || this.toPath.query.oper_type == 'backpay') {
              this.$emit('on-receiptupdate');
            }
          }
          if(this.toPath.query.type=='0') {
            if(this.toPath.query.oper_type!='addlocker') {
              this.$emit('on-receiptupdate');
            }
          }
        }
      }
    }
  },
  watch: {
    'toPath.query.oper_type': {
      handler(val, oldVal) {
        if(val && val!==oldVal) {
          this.printInit()
          this.getCourseList()
        }
      },
      immediate: true
    }
  },
  created() {
    window.addEventListener('storage', this.eventFun)
  },
  beforeDestroy() {
    window.removeEventListener('storage', this.eventFun);
  },
  methods: {
    printInit() {
      const showPrintTemplateArr = ['购卡', '续卡', '升卡',  '销卡', '租柜', '请假','转卡', '补卡', '购私教', '续私教', '购泳教', '续泳教', '套餐包']
      this.curTypeName = this.typeDes[(this.toPath.query && this.toPath.query.oper_type) ? this.toPath.query.oper_type : 'other']
      if(showPrintTemplateArr.indexOf(this.curTypeName) != -1) {
        this.showPrintTemplate = true
        this.getOrderTemplate(this.curTypeName)
      } else {
        this.showPrintTemplate = false
      }
    },
    print() {
      const cardOrderInfoId = this.toPath.query && this.toPath.query.card_order_info_id
      window.open(`/templatePrint?cardorder_info_id=${cardOrderInfoId}&template_id=${this.templateId}&printerType=1`);
      this.showModal = false
    },
    getOrderTemplate(name) {
      this.$service.post('/Web/CardOrderInfo/get_print_template', {
          type: name
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            let resData = res.data.data;
            this.templateList = resData.list;
            this.templateId = resData.list && resData.list.length>0 ? resData.list[0].id : '';
          } else {
            this.$Message.error(res.data.errormsg);
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    eventFun(event) {
      if (event.key == 'receipt') {
        localStorage.removeItem('receipt')
        this.showModal = false;
      }
    },
    handleJoinClick() {
      this.showJoin = true
    },
    handleJoinSubmit() {
      this.$service.post('/Web/TeamclassPrivate/buy_card_add_team_class_student', this.joinForm).then((res) => {
        if (res.data.errorcode == 0) {
          this.$Message.success(res.data.errormsg);
          this.showJoin = false
          this.showCoursera = false
        } else {
          this.$Message.error(res.data.errormsg);
        }
      })
    },
    getCourseList() {
      this.$service.post('/Web/TeamclassPrivate/get_team_class', {
        teamclass_type: this.toPath.query.teamclass_type, // 1泳教班 2私教班
        curriculum_id: this.toPath.query.curriculum_id
      }).then((res) => {
        if (res.data.errorcode == 0) {
          const list = res.data.data
          if (Array.isArray(list) && list.length > 0 && (this.toPath.query.teamclass_type == 1 || this.toPath.query.teamclass_type == 2)) {
            this.joinForm.user_id = this.toPath.query.user_id
            this.courseList = list
            this.showCoursera = true
            if (this.courseList.length === 1) {
              this.joinForm.teamclass_id = this.courseList[0].id
            }
          } else {
            this.courseList = []
            this.showCoursera = false
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .header {
    width: 100%;
    color: #19be6b;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;

    h4 {
      padding-left: 5px;
      font-weight: normal;
    }
  }
  .modal-buttons .ivu-btn-ghost {
    margin-left: 60px;
  }
  .print-icons {
    margin-top: 30px;
    display: flex;
    // align-items: center;
    justify-content: center;
    text-align: center;
    .ivu-icon {
      display: block;
      margin-bottom: 10px;
    }
    .print-select {
      width: 100px;
      margin-right: 2px;
    }
    .icons-wrap {
      cursor: pointer;
      flex: 1;
      color: #333;
      font-size: 14px;
    }
  }
</style>
