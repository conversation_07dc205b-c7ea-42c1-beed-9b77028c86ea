<template>
  <div class="age-gender-checkbox">
    <Row class="checkbox-controller-box">
      <Col span="2">
      <Checkbox :indeterminate="partAgeChecked" :value="allAgeChecked" @on-change="handleAllAgeChecked">年龄</Checkbox>
      </Col>
      <Col span="21">
      <Checkbox-group v-model="ages" @on-change="ageChanged">
        <Checkbox v-for="(item, index) in ageGroup" :label="index + 1" :key="index" class="checkbox-controller-age">{{item}}</Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
    <Row class="checkbox-controller-box">
      <Col span="2">
      <Checkbox :indeterminate="partGenderChecked" :value="allGenderChecked" @on-change="handleAllGenderChecked">性别</Checkbox>
      </Col>
      <Col span="21">
      <Checkbox-group v-model="genders" @on-change="genderChanged">
        <Checkbox :label="1" class="checkbox-controller-gender">男</Checkbox>
        <Checkbox :label="2" class="checkbox-controller-gender">女</Checkbox>
      </Checkbox-group>
      </Col>
    </Row>
  </div>
</template>

<script>
  const AGE_GROUP = [
    '14岁以下会员',
    '15-20岁会员',
    '21-30岁会员',
    '31-40岁会员',
    '41-50岁会员',
    '51-60岁会员',
    '61-70岁会员',
    '71岁以上会员',
    '未设置年龄会员'
  ];
  const INIT_AGE = Array.from({ length: AGE_GROUP.length }, (item, index) => index + 1);
  const INIT_GENDER = [1, 2];

  export default {
    name: 'ageGender',
    data() {
      return {
        ages: INIT_AGE,
        genders: INIT_GENDER,
        ageGroup: AGE_GROUP
      };
    },
    computed: {
      allAgeChecked() {
        return this.ages.length === INIT_AGE.length;
      },
      partAgeChecked() {
        return this.ages.length > 0 && this.ages.length < INIT_AGE.length;
      },
      allGenderChecked() {
        return this.genders.length === INIT_GENDER.length;
      },
      partGenderChecked() {
        return this.genders.length > 0 && this.genders.length < INIT_GENDER.length;
      }
    },
    created() {
      this.$emit('on-change', this.ages, this.genders);
    },
    methods: {
      ageChanged(ages) {
        if (!ages.length) return this.$Message.error('请至少勾选一项');
        this.$emit('on-change', this.ages, this.genders);
      },
      genderChanged(genders) {
        if (!genders.length) return this.$Message.error('请至少勾选一项');
        this.$emit('on-change', this.ages, this.genders);
      },
      handleAllAgeChecked(checked) {
        this.ages = checked ? INIT_AGE : [];
        if (!this.ages.length) return this.$Message.error('请至少勾选一项');
        this.$emit('on-change', this.ages, this.genders);
      },
      handleAllGenderChecked(checked) {
        this.genders = checked ? INIT_GENDER : [];
        if (!this.genders.length) return this.$Message.error('请至少勾选一项');
        this.$emit('on-change', this.ages, this.genders);
      }
    }
  };
</script>

<style lang="less" scoped>
  .age-gender-checkbox {
    .checkbox-controller-box {
      margin-bottom: 10px;
    }
    .checkbox-controller-age,
    .checkbox-controller-gender {
      width: 140px;
    }
  }
</style>
