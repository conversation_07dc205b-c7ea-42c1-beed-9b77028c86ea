<template>
  <Select filterable
          clearable
          :disabled="disabled"
          remote
          ref="signSelect"
          :not-found-text="notFoundText"
          :remote-method="getUsers"
          v-model="userId"
          @on-change="userSelected"
          :loading="searching"
          :placeholder="holder">
    <!-- <Option v-for="user in userList" :key="user.user_id" :value="user.user_id">{{user.username}}<span v-if="user.phone" style="color: #999"> ({{user.phone}})</span></Option> -->
    <template v-for="user in userList">
      <Option v-if="user.user_id!=='self_id'" :value="user.user_id"  :key="user.user_id">{{user.username}} ({{user.phone}})</Option>
      <Option v-else :value="user.user_id"  :key="'phone_'+user.user_id">{{user.phone}}</Option>
    </template>
  </Select>
</template>

<script>
  import { debounce } from 'lodash-es';
  import { isChinese } from '@/utils'

  export default {
    name: 'userSearch',
    data() {
      return {
        userId: '',
        searching: false,
        userList: null,
        notFoundText: '未搜索到该会员'
      };
    },
    props: ['url', 'value', 'placeholder', 'focus', 'busId', 'hasSelfPhone', 'disabled'],
    watch: {
      value(val, oldVal) {
        if (val != oldVal) {
          this.userId = this.value;
        }
        if(!val) {
          this.getSearchUserList(val);
        }
      },
      focus: {
        handler(val, oldValue) {
          if (val) {
            setTimeout(() => {
              this.$refs.signSelect.toggleHeaderFocus({
                type: 'focus'
              })
            }, 300);
          }
        },
        immediate: true
      }
    },
    computed: {
      holder() {
        return this.placeholder || '姓名/电话/实体卡号';
      }
    },
    mounted() {
      // if (this.focus) {
      //   setTimeout(() => {
      //     this.$refs.signSelect.toggleHeaderFocus({
      //       type: 'focus'
      //     })
      //   }, 300);
      // }
    },
    methods: {
      getUsers: debounce(function (search) {
        this.getSearchUserList(search)
      }, 400),
      getSearchUserList(search) {
        if (search === '' || typeof search === "undefined") {
          this.userList = null;
          return;
        }

        // if numbers or letters must be more than 3 in length you can request
        if (!isChinese(search)) {
          this.notFoundText = '至少输入3位数字'
          return;
        }
        
        this.notFoundText = '未搜索到该会员'

        this.searching = true;
        this.userList = null;
        let postData = {
          search: search.trim()
        };
        if(this.busId) postData.bus_id = this.busId
        return this.$service
          .post(this.url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.searching = false;
              const data = res.data.data;
              if (!data) {
                this.$Message.error(res.data.errormsg);
                return;
              }
              if (Array.isArray(res.data.data)) {
                this.userList = res.data.data;
                this.addSelfPhone(search)
              } else if (Array.isArray(res.data.data.list)) {
                this.userList = res.data.data.list;
                this.addSelfPhone(search)
              } else {
                this.userList = [];
                this.$Message.error(res.data.errormsg);
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      addSelfPhone(phone) {
        if (this.hasSelfPhone && /^1\d{10}$/.test(phone)) {
          this.userList.push({
            user_id: 'self_id',
            username: '',
            phone
          })
        }
      },
      userSelected(userId) {
        this.$emit('input', userId);
        this.$emit('on-change', userId, this.userList && this.userList.filter((ele) => {
          return (ele.user_id == userId);
        }))
      }
    }
  };
</script>
