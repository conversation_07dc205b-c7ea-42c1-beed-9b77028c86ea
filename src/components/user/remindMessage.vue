<template>
  <div class="remind_message"
       v-if="remindList">
    <ul class="remind_list">
      <li v-for="(remind, index) in remindList"
          :key="index">

        <div class="remind"
             v-if="remind.remind_category == 1">
          <p class="remind_type">【提醒】</p>
          <p class="create_time">{{ remind.create_time }}</p>
          <p class="content">{{ remind.content }}</p>
        </div>

        <div class="remind"
             v-if="remind.remind_category == 2">
          <p class="remind_type">【挂账】</p>
          <p class="create_time">{{ remind.create_time }}</p>
          <p class="content">{{ remind.content }}</p>
          <p class="record">记录人：{{ remind.name }}</p>
        </div>

        <div class="remind"
             v-if="remind.remind_category == 3">
          <p class="remind_type">【租柜】</p>
          <p class="create_time">{{ remind.create_time }}</p>
          <p class="content">租
            <span class="red">{{ remind.locker_id }}</span>号柜</p>
          <p class="date">截至有效期
            <span class="red">{{ remind.end_date }}</span>
          </p>
          <p class="remark">备注：{{ remind.content }}</p>
        </div>

        <div class="remind"
             v-if="remind.remind_category == 4">
          <p class="remind_type">【定金】</p>
          <p class="create_time">{{ remind.create_time }}</p>
          <p class="content">收定金
            <span class="red">{{ remind.amount }}</span>
          </p>
          <p class="record">操作账号：{{ remind.remind_name }}</p>
          <p class="remark">备注：{{ remind.remark }}</p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  export default {
    name: 'remindMessage',
    data () {
      return {
        remindList: null
      }
    },
    props: {
      userId: {}
    },
    created () {
      this.getRemindList()
    },
    watch: {
      userId (val) {
        if (!val) {
          this.remindList = null
          return
        }
        this.getRemindList()
      }
    },
    methods: {
      getRemindList () {
        const url = '/Web/Remind/get_remind'
        let postData = {
          user_id: this.userId
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.remindList = res.data.data
          } else {
            throw new Error(res.data.errormsg)
          }
        }).catch(err => {
          this.$Message.error(err)
        })
      },
    }
  }
</script>

<style lang="less" scoped>
  .remind_list {
    // max-height: 100px;
    overflow: hidden;
    .remind {
      display: flex;
      background-color: #fef6e8;
      border-bottom: 1px solid #fff;
      height: 34px;
      line-height: 34px;
    }
    .content,
    .record,
    .date,
    .remark {
      padding-left: 10px;
    }
    .red {
      color: #d9544f;
    }
    .content,
    .remark {
      max-width: 30%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .content {
      max-width: 40%;
    }
  }
</style>
