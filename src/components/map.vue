<template>
  <div class="map-wrapper" style="width: 100%; height: 300px">
    <el-amap vid="amap" :plugin="plugin" :zoom="zoom" :center="value">
      <el-amap-marker v-for="(marker, index) in markers" :key="index" :position="value" :events="marker.events"
                      :title="marker.title" :draggable="marker.draggable"></el-amap-marker>
      <el-amap-circle :center="value" :radius="radius" :strokeWeight="0" :strokeOpacity="0" fillColor="#52a4ea"
                      :fill-opacity="0.5"></el-amap-circle>
    </el-amap>
  </div>
</template>

<script>
  export default {
    name: 'amap',
    props: {
      radius: { default: 0 },
      value: Array
    },
    data() {
      return {
        zoom: 15,
        // 高德地图标注
        markers: [
          {
            position: [0, 0],
            title: '可拖动选择精确定位',
            events: {
              click: () => {
              },
              dragend: e => {
                const { lng, lat } = e.lnglat;
                // console.log(lng, lat)
                // this.markers[0].position = [lng, lat];
                this.$emit('input', [lng, lat]);
              }
            },
            draggable: true
          }
        ],
        plugin: [
          {
            pName: 'ToolBar',
            events: {
              init(instance) {
              }
            }
          }
        ]
      };
    }
  };
</script>

<style scoped></style>
