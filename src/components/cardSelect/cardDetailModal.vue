<template>
  <Modal
    :value="show"
    :title="title"
    @on-visible-change="handleClose">
    <Table
      :data="cardList"
      :columns="columns"
      :max-height="600"
      disabled-hover
    />
    <div slot="footer"></div>
  </Modal>
</template>

<script>
  import { CARD_TYPE_DETAIL_DATA } from '@/components/cardSelect//constants';

  export default {
    name: '',

    props: {
      show: {
        type: Boolean,
        required: true
      },
      title: {
        type: String,
        default: '包含卡种'
      },
      showData: {
        type: Object,
        required: true
      },
    },
    data() {
      return {
        cardList: [],
        columns: [
          {
            title: '名称',
            key: 'name',
            tooltip: true
          },
          {
            title: '类型',
            key: 'card_type_id',
            render: (h, { row }) => {
              const card = [1, 2, 3].includes(+row.card_type_id) ? '会籍卡' : row.card_type_id == '4' ? '私教课' : '泳教课'
              let cardType = ''
              if (row.is_pt_time_limit_card == 1) {
                cardType = '私教包月'
              } else {
                cardType = [1, 2, 3].includes(+row.card_type_id)
                ? (row.experience_card == '1' ? '体验' : '') + CARD_TYPE_DETAIL_DATA[row.card_type_id].name
                : row.experience_card == '1' ? '体验课' : row.card_type_id == '4' ? '一对一私教' : '一对一泳教'
              }
              return <span>{ `${card} - ${cardType}` }</span>
            }
          },
        ],
      }
    },

    watch: {
      show(val) {
        val && this.getCardDetail()
      },
    },

    methods: {
      getCardDetail() {
        const { select_type, type_id, is_pt_time_limit_card, query_limit } = this.showData
        const params = {
          select_type,
          type_id,
          is_pt_time_limit_card,
        }
        if (query_limit) {
          params.query_limit = query_limit
        }
        this.$service.post('/Web/Card/cardTypeSelectorDetail', params).then(res => {
          if (res.data.errorcode === 0) {
            const { list } = res.data.data
            if (Array.isArray(list)) {
              list.forEach(v => {
                v.is_pt_time_limit_card = v.is_pt_time_limit_card == 1 ? 1 : 0
              })
              this.cardList = list
            }
          } else {
            this.$Message.error(res.data.errormsg);
          }
        });
      },

      handleClose(val = false) {
        if (val === false) {
          this.cardList = []
        }
        this.$emit('update:show', val)
      },
    }
  }
</script>

<style lang="less" scoped>

</style>
