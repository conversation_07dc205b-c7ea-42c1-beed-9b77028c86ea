import { debounce } from 'lodash-es';
import TableWrap from './tableWrap';
import { CONFIG_TYPE_KEYS } from './constants';

export default {
  components: {
    TableWrap
  },
  props: {
    tabValue: {
      type: Number,
      validator: (value) => [1, 2, 3].includes(value),
      required: true
    },
    show: {
      type: Boolean,
      required: true
    },
    tabActive: { // 当前显示的tab
      type: Number,
      validator: (value) => [1, 2, 3].includes(value),
      required: true
    },
    selected: { // 已选中的标签/卡种/卡类型
      type: Array,
      required: true
    },
    currentBusId: {
      type: [String, Number],
      default: ''
    },
    customDataFn: {
      type: Function,
      default: null
    },
    needPtLimit: { // 用于控制请求列表是否返回私教包月 true 返回 false 不返回
      type: Boolean,
      default: true
    },
    hidePrice: { // 隐藏列表售价的显示
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      idKey: 'id',
      total: 0,
      postData: {
        // select_type: '', // 弹窗选择选项：1标签 2卡种 3卡类型
        page_no: 1,
        page_size: 10,
      },
      tableData: [],
      groupOptions: { // 卡课类型group_type
        '2': { label: '会籍卡', key: 'ms', value: '2', },
        '1': { label: '私教课', key: 'pt', value: '1', },
        '3': { label: '泳教课', key: 'swim', value: '3', },
      },
      cardOptions: { // 卡类型card_type
        '1': { label: '会籍卡', key: 'ms', value: '1', },
        '2': { label: '私教课', key: 'pt', value: '2', },
        '3': { label: '泳教课', key: 'swim', value: '3', },
      },
      keys: CONFIG_TYPE_KEYS
    };
  },
  computed: {
    watchForGetList() {
      return {
        show: this.show,
        tabActive: this.tabActive
      }
    },
    watchForCheckList() {
      return {
        selectedLength: this.selected.length,
        tableData: this.tableData
      }
    }
  },

  watch: {
    // 打开和切换时触发
    watchForGetList({ show, tabActive }) {
      if(show && (tabActive === this.tabValue)) {
        this.getList(true, 1)
      }
    },
    // 获取数据后和父组件删除已选数据时触发
    watchForCheckList: {
      handler() {
        this.$nextTick(() => {
          const { tableData, idKey, selected } = this
          tableData.forEach(v => {
            // const selectedIndex = selected.findIndex(k => k[idKey] === v[idKey] || k.type_id == v[idKey] || k.config_type_id == v[idKey])
            const selectedIndex = selected.findIndex(k => k.myCustom.configTypeId === v[idKey])
            this.$set(v, '_checked', selectedIndex !== -1)
          })
        })
      }
    }
  },
  methods: {
    handleSearch: debounce(function() {
      this.getList(false, 1)
    }, 400),

    getList(loading = true, page) {
      if (page) {
        this.postData.page_no = page
      }
      const { tabValue, postData, needPtLimit, keys } = this
      const params = {
        ...postData,
        bus_id: this.currentBusId,
        select_type: tabValue, // 弹窗选择选项：1标签 2卡种 3卡类型
        query_limit: [needPtLimit ? '' : 'is_pt_time_limit_card,pt'], // 是否返回私教包月 true 返回 false 不返回
        loading: !!loading
      }
      // http://wiki.rocketbird.cn/web/#/120/3075
      this.$service.post('/Web/Card/cardTypeSelector', params).then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          const target = data[keys[tabValue].list]
          if (data && target && Array.isArray(target.data)) {
            const { customDataFn, groupOptions, cardOptions } = this
            target.data.forEach(v => {
              if (tabValue === 3) {
                v.is_pt_time_limit_card = v.is_pt_time_limit_card == 1 ? 1 : 0
              }

              if (typeof customDataFn === 'function') {
                // 自定义数据加工，统一内外共通数据
                customDataFn(v, this)
              } else {
                v.myCustom = {
                  name: v[keys[tabValue].name],
                  configType: tabValue,
                  configTypeId: v[keys[tabValue].id]
                }
                switch (tabValue) {
                  case 1:
                    v.myCustom.typeName = groupOptions[v.type].label
                    v.myCustom.typeKey = groupOptions[v.type].key
                    v.myCustom.typeValue = v.type === '2' ? '1' : v.type === '1' ? '2' : '3'
                    break;
                  case 2:
                  case 3: {
                    const type = [1, 2, 3].includes(+v.card_type_id) ? '1' : v.card_type_id == '4' ? '2' : '3'
                    v.myCustom.typeName = cardOptions[v.myCustom.typeValue].label
                    v.myCustom.typeKey = cardOptions[v.myCustom.typeValue].key
                    v.myCustom.typeValue = type
                    break;
                  }
                }
              }
            })
            this.tableData = target.data
            this.total = +target.count
          } else {
            this.tableData = []
            this.total = 0
          }
        }
      })
    },
  },
};
