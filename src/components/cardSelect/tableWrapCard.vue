<template>
  <TableWrap
    v-bind="{ selected, idKey, total, postData, tableData, columns }"
    v-on="$listeners">
    <template slot="header">
      <Input
        v-model="postData.card_name"
        class="w150"
        clearable
        placeholder="请输入名称搜索"
        @on-change="handleSearch"
      />
      <Select
        v-model="postData.is_universal_card"
        class="w120"
        clearable
        placeholder="单店/通店"
        @on-change="getList(true, 1)">
        <Option value="0">单店</Option>
        <Option value="1">多店通用</Option>
      </Select>
      <Select
        v-model="postData.card_type"
        class="w120"
        clearable
        placeholder="选择卡类型"
        @on-change="getList(true, 1)">
        <Option v-for="item in cardOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
      </Select>
      <Select
        v-model="postData.card_group_id"
        class="w120"
        clearable
        placeholder="选择标签"
        @on-change="getList(true, 1)">
        <Option v-for="item in tagGroupOptions" :key="item.id" :value="item.id">{{ item.title }}</Option>
      </Select>
      <Select
        v-model="postData.sale_status"
        class="w120"
        clearable
        placeholder="在售状态"
        @on-change="getList(true, 1)">
        <Option value="1">在售</Option>
        <Option value="0">已下架</Option>
      </Select>
    </template>
  </TableWrap>
</template>

<script>
  import mixins from "./mixins";
  import { CARD_TYPE_DETAIL_DATA } from '@/components/cardSelect//constants';


  export default {
    name: 'TableWrapCard',
    mixins: [mixins],

    data() {
      return {
        tagGroupOptions: [], // 场馆所有标签
        idKey: 'card_id',
        postData: {
          card_name: '', // 卡种名称
          is_universal_card: '', // 0单店 1多店
          card_type: '', // 卡类型：1为会籍卡 2为私教课 3为泳教课
          card_group_id: '', // 标签id
          sale_status: '', // 售卖状态 0下架 1在售
          page_no: 1,
          page_size: 10,
        },
        templateColumns: [
          {
            type: 'selection',
            width: 60,
          },
          {
            title: '会员卡名称',
            key: 'name',
            width: 150,
            tooltip: true
          },
          {
            title: '单店/通店',
            key: 'universal_card',
            render: (h, { row }) => {
              return <span>{ row.universal_card == 1 ? '多店通用' : '单店' }</span>;
            }
          },
          {
            title: '标签',
            key: 'card_group_id',
            tooltip: true,
            render: (h, { row }) => {
              const item = this.tagGroupOptions.find(v => v.id == row.card_group_id)
              return <span>{ item ? item.title : '-' }</span>;
            }
          },
          {
            title: '卡类型',
            key: 'card_type_id',
            render: (h, { row }) => {
              return <span>{ row.is_pt_time_limit_card == 1 ? '私教包月' : this.cardOptions[[1, 2, 3].includes(+row.card_type_id) ? '1' : row.card_type_id == '4' ? '2' : '3'].label }</span>;
            }
          },
          {
            title: '使用限制',
            key: 'number',
            render: (h, { row }) => {
              return <span>{ row.number + (row.is_pt_time_limit_card == 1 ? '天' : CARD_TYPE_DETAIL_DATA[row.card_type_id].unit) }</span>;
            }
          },
          {
            title: '售价',
            key: 'current_price',
            render: (h, { row }) => {
              return <span>{ '￥' + row.current_price }</span>;
            }
          },
          {
            title: '售卖状态',
            key: 'sale_status',
            render: (h, { row }) => {
              return <span>{  row.sale_status == 1 ? '在售' : '已下架' }</span>;
            }
          }
          // {
          //   title: '包含卡种数',
          //   render: (h, { row }) => {
          //     return <div>
          //       <i-Button type="text">({ 123 })查看</i-Button>
          //     </div>

          //   }
          // },
        ]
      }
    },
    computed: {
      columns() {
        const target = [...this.templateColumns]
        if (this.hidePrice) {
          const index = target.findIndex(v => v.title === '售价')
          index !== -1 && target.splice(index, 1)
        }

        return target
      }
    },

    watch: {
      show(val) {
        val && this.getTagGroupOptions()
      }
    },

    methods: {
      getTagGroupOptions () {
        this.$service.get('/Web/CardGroup/getCardGroupAll').then(res => {
          if (res.data.errorcode === 0) {
            const { data } = res.data
            if (Array.isArray(data)) {
              this.tagGroupOptions = data
            }
          }
        });
      },
    }
  }
</script>

<style lang="less" scoped>

</style>
