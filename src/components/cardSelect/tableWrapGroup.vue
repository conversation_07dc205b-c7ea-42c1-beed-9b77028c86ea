<template>
  <TableWrap
    v-bind="{ selected, total, postData, tableData, columns }"
    v-on="$listeners">
    <template slot="header">
      <Input
        v-model="postData.group_name"
        class="w150"
        clearable
        placeholder="请输入名称搜索"
        @on-change="handleSearch"
      />
      <Select
        v-model="postData.group_type"
        class="w120"
        placeholder="请选择卡种类型"
        clearable
        @on-change="getList(true, 1)">
        <!-- <Option v-for="item of groupOptions" :key="item.value" :value="item.value">{{ item.label }}</Option> -->
        <Option value="2">会籍卡</Option>
        <Option value="1">私教课</Option>
        <Option value="3">泳教课</Option>
      </Select>
    </template>
  </TableWrap>
</template>

<script>
  import mixins from "./mixins";

  export default {
    name: 'TableWrapGroup',
    mixins: [mixins],

    data() {
      return {
        postData: {
          group_name: '', // 标签名
          group_type: '', // 卡课类型：1私教，2会籍卡 3 泳教
          page_no: 1,
          page_size: 10,
        },
        columns: [
          {
            type: 'selection',
            width: 60,
          },
          {
            title: '标签名称',
            key: 'title',
            tooltip: true
          },
          {
            title: '卡课类型',
            render: (h, { row }) => {
              return <span>{ this.groupOptions[+row.type].label }</span>;
            }
          },
          {
            title: '包含卡种',
            render: (h, { row }) => {
              return <div>
                <i-Button
                  type="text"
                  style="line-height:1;min-height:unset;"
                  onClick={ () => { this.$emit('show-cards', row) } }
                >查看</i-Button>
              </div>

            }
          },
        ]
      }
    },

    methods: {

    }
  }
</script>

<style lang="less" scoped>

</style>
