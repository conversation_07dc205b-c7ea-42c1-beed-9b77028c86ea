<template>
  <Modal
    :value="show"
    :width="60"
    class="card-select-modal"
    :mask-closable="false"
    footer-hide
    @on-visible-change="handleClose">
    <template slot="header">
      <div class="tabs-bar">
        <span
          v-for="item in tabOptions"
          :key="item.value"
          class="tab-item"
          :class="{active: tabActive === item.value}"
          @click="handleChangeTab(item)"
        >{{ item.label }}</span>
      </div>
    </template>
    <div class="modal-content">
      <div class="left">
        <component
          :is="item.component"
          v-for="item in tabOptions"
          v-show="tabActive === item.value"
          :key="item.value"
          :tabValue="item.value"
          :tabActive="tabActive"
          :show="show"
          :currentBusId="currentBusId"
          :selected.sync="selectedList"
          :customDataFn="customDataFn"
          :needPtLimit="typeof needPtLimit === 'boolean' ? needPtLimit : !!needPtLimit[item.value]"
          :hidePrice="hidePrice"
          @show-cards="handleShowCardsModal($event, item.value)"
        ></component>
      </div>
      <div class="right">
        <div class="selected-num">已选择（{{ selectedList.length }}）：</div>
        <div class="tag-box">
          <Tag
            v-for="(item, index) in selectedList"
            :key="item.myCustom.configTypeId"
            closable
            color="primary"
            type="border"
            :fade="false"
            :title="item.myCustom.name"
            @on-close="handleDeleteItem(item, index)"
          >
            {{ item.myCustom.name }}
          </Tag>
        </div>
        <div class="footer-btn-row">
          <Button type="primary" @click="handleSummit">保存</Button>
        </div>
      </div>
    </div>
    <!-- <Button type="primary" @click="handleSummit">保存</Button>
      <Button type="primary" ghost @click="handleClose(false)">取消</Button> -->
    <!-- <Button @click="handleReset">重置</Button> -->

    <CardDetailModal
      :show.sync="showCardModal"
      :showData="showCardData"
    />
  </Modal>
</template>

<script>
  import GroupTable from './tableWrapGroup';
  import CardTable from './tableWrapCard';
  import TypeTable from './tableWrapType';
  import CardDetailModal from './cardDetailModal'

  const TAB_OPTIONS = {
    1: { label: '选标签', value: 1, component: GroupTable },
    2: { label: '选卡种', value: 2, component: CardTable },
    3: { label: '选卡类型', value: 3, component: TypeTable },
  }

  const CONFIG_SELECTED_KEYS = {
    1: 'selectedGroups',
    2: 'selectedCards',
    3: 'selectedTypes',
  }
  export default {
    name: 'CardSelect',
    components: {
      GroupTable,
      CardTable,
      TypeTable,
      CardDetailModal
    },

    props: {
      show: {
        type: Boolean,
        required: true
      },
      configType: { // 控制初始打开显示的tab 1标签 2卡种 3卡类型
        type: Number,
        validator: (value) => [1, 2, 3].includes(value),
        default: 1
      },
      tabs: { // 控制显示哪些tab [1标签, 2卡种, 3卡类型]
        type: Array,
        validator(tabs) {
          return [1, 2, 3].some(v => tabs.includes(v))
        },
        default: () => [1]
      },
      selected: {
        type: Array,
        default: () => []
      },
      currentBusId: {
        type: [String, Number],
        default: ''
      },
      customDataFn: {
        type: Function,
        default: null
      },
      // 用于外部控制请求列表是否返回私教包月 key对应tab, true返回
      needPtLimit: {
        type: [Object, Boolean],
        default: () => ({ 1: true, 2: true, 3: true })
      },
      // 隐藏列表售价的显示
      hidePrice: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        tabActive: this.configType,
        selectedGroups: [], // 已选标签
        selectedCards: [], // 已选卡种
        selectedTypes: [], // 已选卡类型
        showCardModal: false,
        showCardData: {
          // select_type: 1, // 1标签 3卡类型
          // type_id: '', // 具体想获取的数据id
          // is_pt_time_limit_card: '' // 是否为包月：1是 0否
          // query_limit: ['is_pt_time_limit_card,pt'] // 传这个值就不会返回私教包月
        }
      }
    },
    computed: {
      tabOptions() {
        return Object.values(TAB_OPTIONS).filter(v => this.tabs.includes(v.value))
      },
      selectedList: {
        get() {
          return this[CONFIG_SELECTED_KEYS[this.tabActive]]
        },
        set(val) {
          this[CONFIG_SELECTED_KEYS[this.tabActive]] = val
        }
      },
    },

    watch: {
      show(val) {
        if (val) {
          if (this.selected.length) {
            // this.selectedList = this.selected.map((v) => ({ ...v, _checked: true }))
            this.selectedList = JSON.parse(JSON.stringify(this.selected))
          }
        } else {
          this.selectedGroups = []
          this.selectedCards = []
          this.selectedTypes = []
          if ([1, 2, 3].includes(+this.configType) && this.tabActive !== +this.configType) {
            this.tabActive = +this.configType
          }
        }
      },
      configType(val) {
        if ([1, 2, 3].includes(+val) && this.tabActive !== +val) {
          this.tabActive = +val
        }
      }
    },

    methods: {
      handleChangeTab(item) {
        if (item.value === this.tabActive) {
          return false
        }
        if (this.selectedList.length) {
          this.$Modal.confirm({
            title: '确定切换吗？',
            content: '切换后，当前已选择的数据将被清空',
            onOk: () => {
              this.selectedList = [] // 此处可不清空，数据不冲突，关闭窗口时会清空数据
              this.tabActive = item.value
            },
          });
        } else {
          this.tabActive = item.value
        }
      },
      handleDeleteItem(item, index) {
        this.selectedList.splice(index, 1)
      },
      handleShowCardsModal(row, tabValue) {
        const data = {
          select_type: tabValue, // 1标签 3卡类型
          type_id: row.myCustom.configTypeId, //  具体想获取的数据id
        }
        if (tabValue == 1) {
          const isShowLimit = typeof this.needPtLimit === 'boolean' ? this.needPtLimit : !!this.needPtLimit[1]
          data.query_limit = [isShowLimit ? '' : 'is_pt_time_limit_card,pt'] // 控制是否返回私教包月
        } else if (tabValue == 3) {
          data.is_pt_time_limit_card = row.is_pt_time_limit_card == 1 ? 1 : 0
        }
        this.showCardData = data
        this.showCardModal = true
      },
      handleSummit() {
        const { tabActive, selectedList } = this
        /*
        const list = []
        selectedList.forEach(({_checked, ...rest}) => {
          if (!list.some(v => v.myCustom.configTypeId == rest.myCustom.configTypeId)) {
            list.push(rest)
          }
        }); */
        // const list = selectedList.map(({_checked, ...rest}) => rest)
        const list = JSON.parse(JSON.stringify(selectedList))
        this.$emit('on-confirm', {
          tabActive,
          list
        })
        this.$emit('update:show', false)
      },
      handleClose(val = false) {
        this.$emit('update:show', val)
      },
    },
  }
</script>

<style lang="less" scoped>
@text-color: #2C3945;
@tab-color: #2B8DF2;
.card-select-modal {
  /deep/.ivu-modal {
    .ivu-modal-close {
      top: 10px;
      right: 16px;
    }
    .ivu-modal-header {
      padding: 10px 20px 0;
    }
    .ivu-modal-body {
      padding: 0;
    }
  }
}
@media screen and (max-width: 1600px) {
  .card-select-modal {
    /deep/.ivu-modal {
      top: 70px;
      width:80%!important;
    }
  }
}

.tabs-bar {
  display: flex;
  align-items: center;
  padding-right: 20px;
  height: 40px;
  border-bottom: 1px solid #E1E3E9;
  .tab-item {
    width: 100px;
    height: 100%;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    &:hover {
      color: @tab-color;
    }
    &.active {
      color: @tab-color;
      border-bottom: 2px solid @tab-color;
    }
  }
}

.modal-content {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 20px;
  min-height: 615px;
  .left {
    position: relative;
    padding-right: 20px;
    width: 75%;
    // border-right: 1px solid #E1E3E9;
  }
  .right {
    position: relative;
    width: 25%;
    .selected-num {
      height: 72px;
      line-height: 62px;
      // font-size: 14px;
    }
    .tag-box {
      overflow-y: auto;
      height: 440px;
      min-height: 440px;
    }

    .ivu-tag {
      margin: 0 10px 10px 0;
      border-radius: 3px;
      line-height: 22px;
      width: 45%;
      cursor: auto;
      /deep/.ivu-tag-text {
        display: inline-block;
        max-width: 78%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      /deep/.ivu-icon-ios-close {
        position: absolute;
        top: 4px;
        right: 4px;
        left: unset;
        margin-left: 0!important;
        opacity: 1;
      }
    }

    .footer-btn-row {
      // position: absolute;
      // left: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 80px;
      padding: 0 35px;
      padding-top: 6px;
    }

  }
}



</style>
