<template>
  <div class="table-wrap">
    <header>
      <slot name="header"></slot>
    </header>
    <main>
      <Table
        :columns="columns"
        :data="tableData"
        :max-height="440"
        disabled-hover
        @on-selection-change="onSelectionChange"
        @on-select="onSelect"
        @on-select-all="onSelectAll"
        @on-select-cancel="onSelectCancel"
      />
    </main>
    <footer>
      <Page
        :total="total"
        :current.sync="postData.page_no"
        :page-size="postData.page_size"
        placement="top"
        show-total
        @on-change="pageChanged"
        @on-page-size-change="pageSizeChanged" />
    </footer>
  </div>
</template>

<script>
  export default {
    name: 'TableWrap',
    inheritAttrs: false,

    props: {
      selected: {
        type: Array,
        required: true
      },
      idKey: {
        type: String,
        default: 'id'
      },
      postData: {
        type: Object,
        default: () => ({})
      },
      total: {
        type: Number,
        default: 0
      },
      tableData: {
        type: Array,
        default: () => []
      },
      columns: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {

      }
    },

    computed: {
      selection: {
        get() {
          return this.selected;
        },
        set(val) {
          const fn = this.$listeners['update:selected']
          typeof fn === 'function' && fn([...val])
        }
      },
    },

    methods: {
      onSelectionChange(selection) {
        if (selection.length === 0) {
          const { tableData, onSelectCancel } = this
          tableData.forEach(item => onSelectCancel(selection, item));
        }
      },
      onSelectCancel(selection, row) {
        const { idKey } = this;
        // item.myCustom.configTypeId: tab对应类型的id,需要在获取列表时给列表自定义。 标签为id 卡种为card_id 卡类型为id
        const index = this.selection.findIndex(item => item.myCustom.configTypeId === row[idKey]);
        if (index !== -1) {
          this.selection.splice(index, 1);
        }
      },
      onSelect(selection, row) {
        const { idKey } = this;
        // item.myCustom.configTypeId: tab对应类型的id,需要在获取列表时给列表自定义。 标签为id 卡种为card_id 卡类型为id
        if (!this.selection.some(v => v.myCustom.configTypeId === row[idKey])) {
          this.selection.push(row);
        }
      },
      onSelectAll(selection) {
        const { onSelect } = this
        selection.forEach(item => onSelect(selection, item));
      },
      pageChanged(page) {
        this.postData.page_no = page
        this.$parent.getList()
      },
      pageSizeChanged(size) {
        this.postData.page_size = size;
        this.postData.page_no = 1;
        this.$parent.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
.table-wrap {
  border: 0;
  min-height: 602px;
  & > header {
    min-height: 72px;
    padding: 15px 0;
    border-bottom: 0;
  }

  & > main {
    border: 1px solid #dcdee2;
    border-bottom: 0;
  }

  /deep/.ivu-page-item-active {
    border-color: #2d8cf0;
    color: #2d8cf0;
    a {
      color: #2d8cf0;
    }
  }
}
</style>
