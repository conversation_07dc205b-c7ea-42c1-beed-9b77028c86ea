<template>
  <TableWrap
    v-bind="{ selected, total, postData, tableData, columns }"
    v-on="$listeners">
    <template slot="header">
      <!-- 已和产品沟通，没必要筛选 -->
      <!-- <Input
        v-model="postData.name"
        class="w150"
        clearable
        placeholder="请输入名称搜索"
        @on-change="handleSearch"
      />
      <Select
        v-model="postData.type"
        class="w120"
        placeholder="请选择卡种类型"
        clearable
        @on-change="getList(true, 1)">
        <Option v-for="item in cardOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
      </Select> -->
    </template>
  </TableWrap>
</template>

<script>
  import mixins from "./mixins";

  export default {
    name: 'TableWrapType',
    mixins: [mixins],

    data() {
      return {
        postData: {
          name: '', // 卡种名称
          type: '', // 卡类型
          page_no: 1,
          page_size: 10,
        },
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '卡类型',
            key: 'name',
          },
          {
            title: '卡种类型',
            key: 'card_type_id',
            render: (h, { row }) => {
              return <span>{ [1, 2, 3].includes(+row.card_type_id) ? '会籍卡' : row.card_type_id == '4' ? '私教课' : '泳教课' }</span>;
            }
          },
          {
            title: '包含卡种',
            render: (h, { row }) => {
              return <div>
                <i-Button
                  type="text"
                  style="line-height:1;min-height:unset;"
                  onClick={ () => { this.$emit('show-cards', row) } }
                >查看</i-Button>
              </div>
            }
          },
        ]
      }
    },

    methods: {

    }
  }
</script>

<style lang="less" scoped>

</style>
