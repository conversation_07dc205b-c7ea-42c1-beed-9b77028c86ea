<template>
  <div class="tree-component">
    <Tree :data="data" :render="renderComponent" :showCheckbox="showCheckbox" />
  </div>
</template>

<script>
  export default {
    name: 'treeMenu',
    props: {
      data: {
        type: Array,
        default() {
          return [];
        }
      },
      disabledEdit: {
        type: Boolean,
        default: false
      },
      showCheckbox: {
        type: Boolean,
        default: false
      },
      options: {
        default: () => {
          return {
            titleKey: 'title',
            levelKey: 'level',
            idKey: 'id',
            level: 4,
            levelTop: 0,
            addTitle: '添加分区',
            editTitle: '编辑名称',
            deleteTitle: '删除分区'
          };
        }
      }
    },
    data() {
      return {
        currentId: ''
      };
    },
    watch: {
      data(val, oldVal) {
        if (!oldVal.length && val.length) {
          this.currentId = val[0][this.options.idKey];
        }
      }
    },
    methods: {
      renderComponent(h, { root, node, data }) {
        const { titleKey, levelKey, level, levelTop, addTitle, editTitle, deleteTitle, idKey, disabled<PERSON><PERSON>s } = this.options;
        const styles = {
          cursor: 'pointer',
          color: this.currentId === data[idKey] ? '#5cb85c' : '',
          whiteSpace: 'Pre-line',
        };
        return (
          <span class="tree-menu">
            <span
              style={styles}
              onClick={() => {
                this.currentId = data[idKey];
                this.$emit('on-select', data[idKey]);
              }}>
              {data[titleKey].trim() || '?'}
            </span>
            <span style={{ display: disabledKeys && disabledKeys.includes(data[idKey]) || this.disabledEdit ? 'none': '' }} class="buttons">
              {data[levelKey] < level && (
                <fa-icon
                  onClick={() => {
                    this.$emit('on-button-click', 'add', data);
                    this.$emit('on-add', data);
                  }}
                  class="icon"
                  size="16"
                  color="#5cb85c"
                  name="plus-square-o"
                  title={addTitle}
                />
              )}
              {data[levelKey] != levelTop && (
                <fa-icon
                  onClick={() => {
                    this.$emit('on-button-click', 'edit', data);
                    this.$emit('on-edit', data);
                  }}
                  class="icon"
                  size="16"
                  color="#5cb85c"
                  name="edit"
                  title={editTitle}
                />
              )}
              {data[levelKey] != levelTop && (
                <fa-icon
                  onClick={() => {
                    this.$emit('on-button-click', 'delete', data);
                    this.$emit('on-delete', data);
                  }}
                  class="icon"
                  size="16"
                  color="#5cb85c"
                  name="trash-o"
                  title={deleteTitle}
                />
              )}
            </span>
          </span>
        );
      }
    }
  };
</script>

<style lang="less">
  .tree-component {
    min-height: 100%;
    // max-height: calc(~'100vh - 150px');
    border: 1px solid #e0e3e9;
    border-right: 0;
    overflow-y: scroll;
    user-select: none;
    padding: 10px 20px;
    background-color: #fff;
    min-width: 260px;
    .ivu-tree ul {
      font-size: 14px;
    }
  }
  .tree-menu {
    padding: 12px 0;
    margin: -12px 0;
    &:hover {
      color: #5cb85c;
      .buttons {
        display: inline;
      }
    }
    .buttons {
      display: none;
      margin-left: 5px;
      .icon {
        margin: 0 5px;
        cursor: pointer;
      }
    }
  }
</style>
