<template>
  <div v-if="userNoType === '1'">
    <div class="item">
      <p>1对{{classData.user_no}}课程</p>
    </div>
    <div class="item">
      <p>人员{{classData.class_mark_no}}/{{classData.user_no}}</p>
    </div>
  </div>
    <Poptip transfer
          v-else-if="showInfo"
          placement="right-start"
          class="class-item-pop"
          trigger="hover">
    <div class="pop-item" :class="prefixClass">
      <div class="item">
        <Icon type="ios-person-outline" :color="iconColor"></Icon>
        <p>{{ classData.username}}</p>
      </div>
      <div class="item">
        <Icon type="ios-compose-outline" :color="iconColor" size="13"></Icon>
        <p>{{ itemText }}</p>
      </div>
    </div>
    <div class="pop" slot="content">
      <SingleClassInfo :class-data="classData" :coach-status="classData.suspend_status" :type="classData.status"></SingleClassInfo>
    </div>
  </Poptip>
  <div class="pop-item" v-else>
    <div class="item">
      <Icon type="ios-person-outline" :color="iconColor"></Icon>
      <p>{{ classData.username}}</p>
    </div>
    <div class="item">
      <Icon type="ios-compose-outline" :color="iconColor" size="13"></Icon>
      <p>{{ itemText }}</p>
    </div>
  </div>

</template>

<script>
import SingleClassInfo from './singleClassInfo.vue';
export default {
  name: 'singleClassItem',
  components: {
    SingleClassInfo
  },
  data() {
    return {
      prefix: '',
      itemText: '',
      iconColor: ''
    };
  },
  created() {
    this.courseStatus();
  },
  props: {
    type: {},
    userNoType: {
      type: String,
      default: '0'
    },
    classData: {},
    showInfo: {
      type: Boolean,
      default: false
    },
  },
  methods: {
    courseStatus() {
      switch (parseInt(this.type)) {
        case 1:
          this.prefix = 'reserved';
          this.iconColor = '#a3a1ff';
          this.itemText = '已约';
          break;
        case 2:
          this.prefix = 'attended';
          this.iconColor = '#a4ddb4';
          this.itemText = '已上';
          break;
        case 3:
          this.prefix = 'not-in';
          this.iconColor = '#fc88c2';
          this.itemText = '未上';
          break;
        case 4:
          this.prefix = 'done';
          this.iconColor = '#f5b57d';
          this.itemText = '完成';
          break;
        default:
      }
    },
    toUserDetail(userId) {
      window.open(`/v2/member/detail/${userId}`);
    },
  },
  computed: {
    prefixClass() {
      return `${this.prefix}-class`;
    }
  }
};
</script>
<style scoped>

</style>
<style lang="less" scoped>
.class-item-pop {
  width: 100%;
  :deep(.ivu-poptip-rel) {
    width: 100%;
  }
}
.not-in-class {
  background-color: #fff1f8;
  border-left: 4px solid #fc88c2;
  .item {
    color: #fc88c2;
  }
}

.attended-class {
  background-color: #ebfaef;
  border-left: 4px solid #a4ddb4;
  .item {
    color: #a4ddb4;
  }
}

.reserved-class {
  background-color: #f6f6ff;
  border-left: 4px solid #a3a1ff;
  .item {
    color: #a3a1ff;
  }
}

.done-class {
  background-color: #fcf3eb;
  border-left: 4px solid #f5b57d;
  .item {
    color: #f5b57d;
  }
}

.item {
  display: flex;
  align-items: center;
  padding-left: 3px;
  p {
    padding-left: 3px;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
}

.icons {
  height: 42px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  img {
    cursor: pointer;
  }
}
</style>
