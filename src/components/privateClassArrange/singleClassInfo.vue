<template>
  <div>
    <ul>
      <li>
        <Icon style="width: 14px; text-align: center" type="ios-timer-outline" color="#434343" size="16"></Icon>
        <p>{{ classData.beg_time }}-{{ classData.endTime }}</p>
      </li>
      <li>
        <Icon style="width: 14px; text-align: center" type="ios-list-box-outline" color="#434343" size="16"></Icon>
        <p>{{ classData.class_name }}</p>
      </li>
      <li v-if="classData.class_user_no_type==='1'">
        <Icon style="width: 14px; text-align: center" type="ios-people-outline" color="#434343" size="16"></Icon>
        <p>1对{{ classData.user_no }}</p>
      </li>
      <li>
        <Icon style="width: 14px; text-align: center" type="ios-person-outline" color="#434343" size="16"></Icon>
        <p @click="toUserDetail(classData.user_id)">{{ classData.username }}</p>
      </li>
      <li>
        <Icon style="width: 14px; text-align: center" type="ios-information-circle-outline" color="#434343" size="16"></Icon>
        <p :style="{color: iconColor}">{{ itemText }}</p>
      </li>
    </ul>
    <div class="icons">
      <img v-if="coachStatus == 0" src="~assets/img/pop-copy.png" alt="" title="复制" @click="handleCopy">
      <img src="~assets/img/pop-delete.png" alt="" title="删除" @click="handleDel">
    </div>
  </div>
</template>

<script>
import EventBus from '@/utils/eventBus.js'

export default {
  name: 'singleClassItem',
  data() {
    return {
      prefix: '',
      itemText: '',
      iconColor: ''
    };
  },
  created() {
    this.courseStatus();
  },
  props: {
    type: {},
    coachStatus: {},
    classData: {},
  },
  methods: {
    courseStatus() {
      switch (parseInt(this.type)) {
        case 1:
          this.prefix = 'reserved';
          this.iconColor = '#a3a1ff';
          this.itemText = '已约';
          break;
        case 2:
          this.prefix = 'attended';
          this.iconColor = '#a4ddb4';
          this.itemText = '已上';
          break;
        case 3:
          this.prefix = 'not-in';
          this.iconColor = '#fc88c2';
          this.itemText = '未上';
          break;
        case 4:
          this.prefix = 'done';
          this.iconColor = '#f5b57d';
          this.itemText = '完成';
          break;
        default:
      }
    },
    toUserDetail(userId) {
      window.open(`/v2/member/detail/${userId}`);
    },
    handleCopy() {
      EventBus.$emit('courseCopy', this.classData)
    },
    handleDel() {
      if (this.classData.appt_type == 1) {
        const id = this.classData.pt_schedule_id
        let url = ''
        if (this.classData.card_type_id == 4) {
          url = '/Web/PtSchedule/check_pt_schedule_del'
        } else {
          url = '/Web/PtSchedule/check_swim_schedule_del'
        }
        this.$service.post(url, {
          id
        }).then((res) => {
          if (res.data.errorcode == 0) {
            const url = `${window.location.protocol}//${window.location.host}/one-time-pay/cancel/${id}/pt`
            window.open(url, '_self')
          } else {
            this.$Message.error(res.data.errormsg)
          }
        })
      } else {
        EventBus.$emit('courseDel', this.classData)
      }
    }
  },
  computed: {
    prefixClass() {
      return `${this.prefix}-class`;
    }
  }
};
</script>

<style lang="less" scoped>
#singleClass {
  height: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.not-in-class {
  background-color: #fff1f8;
  border-left: 4px solid #fc88c2;
  .item {
    color: #fc88c2;
  }
}

.attended-class {
  background-color: #ebfaef;
  border-left: 4px solid #a4ddb4;
  .item {
    color: #a4ddb4;
  }
}

.reserved-class {
  background-color: #f6f6ff;
  border-left: 4px solid #a3a1ff;
  .item {
    color: #a3a1ff;
  }
}

.done-class {
  background-color: #fcf3eb;
  border-left: 4px solid #f5b57d;
  .item {
    color: #f5b57d;
  }
}

.item {
  display: flex;
  align-items: center;
  padding-left: 3px;
  p {
    padding-left: 3px;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
}

.pop {
  li {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    height: 30px;
    p {
      padding-left: 10px;
      font-size: 14px;
    }
  }
}

.icons {
  height: 42px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  img {
    cursor: pointer;
  }
}
</style>
