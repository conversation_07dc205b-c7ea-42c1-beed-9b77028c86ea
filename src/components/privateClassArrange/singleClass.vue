<template>
  <Poptip transfer
          id="singleClass"
          :class="prefixClass"
          :style="{left: leftValue + '%', width: classWidth + '%'}"
          trigger="hover">
    <SingleClassItem :class-data="classData" :type="type" :user-no-type="classData.class_user_no_type"></SingleClassItem>
    <div class="pop" slot="content">
      <div class="pop-item-wrap" v-if="classData.class_user_no_type === '1'">
        <SingleClassItem style="margin-bottom: 10px;" v-for="(item, index) in classData.class_list" :key="index" :class-data="item" user-no-type="0" :type="item.status" :show-info="true"></SingleClassItem>
      </div>
      <SingleClassInfo v-else :class-data="classData" :coach-status="coachStatus" :type="type"></SingleClassInfo>
    </div>
  </Poptip>
</template>

<script>
import SingleClassItem from './singleClassItem.vue';
import SingleClassInfo from './singleClassInfo.vue';
export default {
  name: 'singleClass',
  components: {
    SingleClassItem,
    SingleClassInfo
  },
  data() {
    return {
      prefix: '',
      itemText: '',
      iconColor: ''
    };
  },
  created() {
    this.courseStatus();
  },
  props: {
    type: {},
    classData: {},
    lastTwo: {},
    coachStatus: {},
    timeArrayLength: {
      type: Number,
      default: 16
    }
  },
  methods: {
    courseStatus() {
      switch (parseInt(this.type)) {
        case 1:
          this.prefix = 'reserved';
          this.iconColor = '#a3a1ff';
          this.itemText = '已约';
          break;
        case 2:
          this.prefix = 'attended';
          this.iconColor = '#a4ddb4';
          this.itemText = '已上';
          break;
        case 3:
          this.prefix = 'not-in';
          this.iconColor = '#fc88c2';
          this.itemText = '未上';
          break;
        case 4:
          this.prefix = 'done';
          this.iconColor = '#f5b57d';
          this.itemText = '完成';
          break;
        default:
      }
    },
    toUserDetail(userId) {
      window.open(`/v2/member/detail/${userId}`);
    },
  },
  computed: {
    leftValue() {
      const length = this.timeArrayLength
      let beginTimeArr = this.classData.beg_time.split(':'),
          unit =  100 / length,
          hour = Number(beginTimeArr[0]) + beginTimeArr[1] / 60;
      return hour * unit;
    },
    classWidth() {
      const length = this.timeArrayLength
      let beginTimeArr = this.classData.beg_time.split(':');
      let endTimeArr = this.classData.endTime.split(':');
      let classTime = (endTimeArr[0] - beginTimeArr[0]) * 60 + parseInt(endTimeArr[1]) - beginTimeArr[1];
      let unit = 1 / length * 100;
      return classTime / 60 * unit;
    },
    prefixClass() {
      return `${this.prefix}-class`;
    }
  },
  watch: {
    lastTwo(val, old) {
      console.log('lastTwo', val, old);
    }
  }
};
</script>

<style lang="less" scoped>
#singleClass {
  height: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.not-in-class {
  background-color: #fff1f8;
  border-left: 4px solid #fc88c2;
  .item {
    color: #fc88c2;
  }
}

.attended-class {
  background-color: #ebfaef;
  border-left: 4px solid #a4ddb4;
  .item {
    color: #a4ddb4;
  }
}

.reserved-class {
  background-color: #f6f6ff;
  border-left: 4px solid #a3a1ff;
  .item {
    color: #a3a1ff;
  }
}

.done-class {
  background-color: #fcf3eb;
  border-left: 4px solid #f5b57d;
  .item {
    color: #f5b57d;
  }
}

.item {
  display: flex;
  align-items: center;
  padding-left: 3px;
  p {
    padding-left: 3px;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
}

.pop {
  li {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    height: 30px;
    p {
      padding-left: 10px;
      font-size: 14px;
    }
  }
}
.pop-item-wrap {
  display: flex;
  flex-direction: column;
  max-height: 330px;
}

.icons {
  height: 42px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  img {
    cursor: pointer;
  }
}
</style>
