<template>
  <div>
    <div class="logo"><img src="./../../assets/img/logo.jpg"></div>
    <div class="main">
      <div class="stadium">
        <h2>
          <span>{{stadiumName}}</span>
        </h2>
        <p class="address">
          <span><img src="./../../assets/img/address.jpg"></span>
          <span class="box ml-6">{{stadiumAddress}}</span>
        </p>
        <p class="phone">
          <span><img src="./../../assets/img/phone.png"></span>
          <span class="ml-6">{{stadiumPhone}}</span>
        </p>
      </div>
      <article>
        <div class="flow-path">
          <h2>
            <span>体验卡领取流程</span>
          </h2>
          <p class="flow-row">
            <span><img src="./../../assets/img/one.png"></span>
            <span class="ml-6">长按识别二维码，关注“勤鸟运动”公众号</span>
          </p>
          <p class="flow-row">
            <span><img src="./../../assets/img/two.png"></span>
            <span class="ml-6">点击公众号发送的领取的消息，并完成注册</span>
          </p>
          <p class="flow-row">
            <span><img src="./../../assets/img/three.png"></span>
            <span class="ml-6">到场馆去体验吧</span>
          </p>
        </div>
      </article>
      <div class="two-code">
        <div class="code-bg">
          <img :src="codeUrl">
          <p>长按二维码，免费领取健身体验卡</p>
        </div>
      </div>
    </div>
    <div class="bg">
      <div class="introduce">
        <h2>
          <span>场馆介绍</span>
        </h2>
        <p class="content"
           v-html="bus_description"></p>
      </div>
    </div>
    <footer>
      <img src="./../../assets/img/footer.jpg">
    </footer>
  </div>
</template>

<script>
  import { getBaseUrl } from './../../utils/config'
  // import verifyModal from './../../components/register/verifyModal.vue'

  export default {
    name: 'stadiumDetail',
    data() {
      return {
        stadiumName: '',
        stadiumAddress: '',
        stadiumPhone: '',
        bus_description: '',
        codeUrl: ''
      }
    },
    created() {
      let sence_id = this.GetQueryString('sence_id')
      // let sence_id = window.location.search.substring(window.location.search.indexOf("?sence_id=")+10)//获取地址栏场景id
      let windowWidth = document.documentElement.clientWidth
      document.documentElement.style.fontSize = `${windowWidth / 7.2}px`
      this.getStadium(sence_id)
      this.getCode(sence_id)
    },
    methods: {
      getStadium(sence_id) {
        let url = '/MembershipV2/Business/info'
        let postData = {
          sence_id: sence_id != '' ? sence_id : '10000113' //(场景值，必传);
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode == 0) {
            this.stadiumName = res.data.data.name
            this.stadiumAddress = res.data.data.address
            this.stadiumPhone = res.data.data.phone
            this.bus_description = res.data.data.bus_description
          }
        })
      },
      GetQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
      },
      getCode(sence_id) {
        let url = '/MembershipV2/Marketers/get_experience_card_qr'
        let postData = {
          sence_id: sence_id != '' ? sence_id : '10000113' //(场景值，必传);
        }
        this.$service.post(url, postData).then(res => {
          this.codeUrl = res.data
        })
      }
    }
  }
</script>

<style>
  body,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  form {
    margin: 0;
  }
  ol,
  ul {
    margin: 0;
    padding: 0;
  }
  /* html{font-size:62.5%;} */
  body {
    position: relative;
    font-family: 'PingFangSC-Regular', 'Microsoft YaHei', sans-serif;
  }

  .logo img {
    width: 100%;
    display: block;
  }

  .main {
    width: 100%;
    height: 7.88rem;
    background-size: contain;
    background-color: #5366fc;
    background-image: url(./../../assets/img/main.jpg);
    margin-top: -0.03rem;
    background-repeat: no-repeat;
  }
  /* .response img{width: 100%;height: 100%;} */
  .stadium {
    width: 6.4rem;
    height: 1.68rem;
    padding-top: 0.56rem;
    margin-left: auto;
    margin-right: auto;
    color: #fff;
  }
  article {
    width: 6.4rem;
    /* padding-top: 0.56rem; */
    margin-left: auto;
    margin-right: auto;
    color: #fff;
  }
  h2 {
    height: 0.416rem;
    font-size: 0.3rem;
    text-align: center;
    font-weight: bold;
    margin-bottom: 0.18rem;
  }
  h2 span {
    padding-bottom: 0.04rem;
    border-bottom: 0.04rem solid #ff2a48;
  }
  .address,
  .phone {
    width: 6rem;
    min-height: 0.352rem;
    max-height: 0.6rem;
    margin-left: 0.3rem;
    font-size: 0.26rem;
    margin-bottom: 0.1rem;
    /* height: 0.33rem; */
    /* overflow: hidden; */
  }
  .box {
    display: inline-block;
    width: 5rem;
    max-height: 0.7rem;
    overflow: hidden;
    vertical-align: top;
  }
  .ml-6 {
    margin-left: 0.06rem;
  }
  .address img,
  .phone img {
    width: 0.65rem;
    height: 0.28rem;
    vertical-align: middle;
  }

  /*2*/
  .flow-path {
    margin-top: 0.44rem;
    padding-top: 0.16rem;
  }
  .flow-row {
    width: 6rem;
    margin-left: 0.3rem;
    font-size: 0.26rem;
    margin-bottom: 0.08rem;
  }
  .flow-row img {
    width: 0.6rem;
    height: 0.28rem;
    vertical-align: middle;
  }
  .two-code {
    /* width: 2.18rem; */
    height: 2.48rem;
    margin: auto;
    /* background-size: contain; */
    /* box-shadow: 0.02rem 0.03rem 0.02rem #49dfff; */
    text-align: center;
    /* padding-top: 0.33rem; */
    /* background-image: url(./../../assets/img/code_bg.jpg); */
    margin-top: -0.16rem;
  }
  .code-bg {
    width: 3.71rem;
    height: 2.4rem;
    margin: 0.3rem auto;
    background-size: contain;
    background-image: url(./../../assets/img/code_bg.jpg);
    position: relative;
  }
  .code-bg p {
    position: absolute;
    bottom: -0.37rem;
    font-size: 0.24rem;
    color: #fff;
  }
  .two-code img {
    width: 2.18rem;
    height: 2.18rem;
  }
  /*3*/
  .bg {
    /* height: 2.69rem; */
    padding-top: 0.2rem;
    margin-top: -0.03rem;
    background-size: contain;
    background-image: url(./../../assets/img/bottom.jpg);
    background-position: 0 30%;
    background-repeat: no-repeat;
    background-color: #515dfc;
    padding-bottom: 0.3rem;
  }
  .introduce {
    width: 6.3rem;
    margin: auto;
    padding-top: 0.2rem;
    border: 0.04rem solid #4fa0fe;
    border-radius: 0.2rem;
    color: #fff;
    /* margin-top: 1.1rem; */
  }
  .content {
    width: 5.8rem;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
    margin-left: auto;
    margin-right: auto;
    padding-bottom: 0.5rem;
    font-size: 0.24rem;
  }

  /*footer*/
  footer {
    /* background-color: #5366fc; */
    position: absolute;
    bottom: -0.45rem;
    right: 0;
    width: 100%;
    /* height: 0.55rem; */
  }
  footer img {
    display: block;
    width: 100%;
  }
</style>


