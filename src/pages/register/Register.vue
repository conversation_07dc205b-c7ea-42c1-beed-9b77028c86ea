<template>
  <div class="wrap register">
    <div
      v-if="!registerSucceed"
      class="container"
    >
      <div class="top-title-wrapper">
        <div>
          <p class="name">
            ROCKET BIRD
          </p>
          <h1 class="h1">
            勤鸟运动健身产业服务平台
          </h1>
          <p class="text">
            智慧场馆系统集成商
          </p>
        </div>
      </div>
      <div class="form-content-wrapper">
        <Form
          ref="registerForm"
          :model="formData"
          :rules="ruleValidate"
        >
          <Form-item
            label="场馆类型"
            prop="bus_type"
          >
            <Select v-model="formData.bus_type" transfer>
              <!-- <Option value="1">
                健身房
              </Option>
              <Option value="6">
                健身工作室
              </Option>
              <Option value="2">
                瑜伽馆
              </Option>
              <Option value="3">
                跆拳道馆
              </Option>
              <Option value="4">
                武道馆
              </Option>
              <Option value="5">
                舞蹈馆
              </Option> -->
              <Option
                v-for="item in busTypeOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label">
                {{ item.label }}
              </Option>
            </Select>
          </Form-item>
          <Form-item
            label="场馆名称"
            prop="bus_name"
          >
            <Input
              v-model="formData.bus_name"
              autofocus
            />
          </Form-item>
          <div class="area">
            <Form-item
              label="省"
              prop="province"
            >
              <Select
                v-model="formData.province"
                transfer
                @on-change="provinceChanged"
              >
                <Option value="2">
                  北京
                </Option>
                <Option value="3">
                  安徽
                </Option>
                <Option value="4">
                  福建
                </Option>
                <Option value="5">
                  甘肃
                </Option>
                <Option value="6">
                  广东
                </Option>
                <Option value="7">
                  广西
                </Option>
                <Option value="8">
                  贵州
                </Option>
                <Option value="9">
                  海南
                </Option>
                <Option value="10">
                  河北
                </Option>
                <Option value="11">
                  河南
                </Option>
                <Option value="12">
                  黑龙江
                </Option>
                <Option value="13">
                  湖北
                </Option>
                <Option value="14">
                  湖南
                </Option>
                <Option value="15">
                  吉林
                </Option>
                <Option value="16">
                  江苏
                </Option>
                <Option value="17">
                  江西
                </Option>
                <Option value="18">
                  辽宁
                </Option>
                <Option value="19">
                  内蒙古
                </Option>
                <Option value="20">
                  宁夏
                </Option>
                <Option value="21">
                  青海
                </Option>
                <Option value="22">
                  山东
                </Option>
                <Option value="23">
                  山西
                </Option>
                <Option value="24">
                  陕西
                </Option>
                <Option value="25">
                  上海
                </Option>
                <Option value="26">
                  四川
                </Option>
                <Option value="27">
                  天津
                </Option>
                <Option value="28">
                  西藏
                </Option>
                <Option value="29">
                  新疆
                </Option>
                <Option value="30">
                  云南
                </Option>
                <Option value="31">
                  浙江
                </Option>
                <Option value="32">
                  重庆
                </Option>
                <Option value="33">
                  香港
                </Option>
                <Option value="34">
                  澳门
                </Option>
                <Option value="35">
                  台湾
                </Option>
              </Select>
            </Form-item>
            <Form-item
              label="市"
              prop="city"
            >
              <Select
                v-model="formData.city"
                transfer
                @on-change="cityChanged"
              >
                <Option
                  v-for="city in cities"
                  :key="city.region_id"
                  :value="city.region_id"
                >
                  {{ city.region_name }}
                </Option>
              </Select>
            </Form-item>
            <Form-item
              label="区县"
              prop="district"
            >
              <Select v-model="formData.district" transfer>
                <Option
                  v-for="item in districts"
                  :key="item.region_id"
                  :value="item.region_id"
                >
                  {{ item.region_name }}
                </Option>
              </Select>
            </Form-item>
          </div>
          <!-- <Form-item label="街道门牌号"
                    prop="address">
            <Input v-model="formData.address" />
          </Form-item> -->
          <Form-item
            label="申请人姓名"
            prop="apply_name"
          >
            <Input v-model="formData.apply_name" />
          </Form-item>
          <Form-item
            label="联系手机"
            prop="phone"
          >
            <Input v-model="formData.phone" />
          </Form-item>
          <verifyModal
            v-model="verifyModal"
            @on-ok="getVerifyModalCode"
          />
          <Form-item
            label="验证码"
            prop="code"
          >
            <Input v-model="formData.code" />
            <Button
              class="get-verify"
              :disabled="verifyBtnDisabled"
              @click="beforeVerifyModal"
            >
              {{ verifyBtnWord }}
            </Button>
          </Form-item>
          <Form-item
            label="推荐人"
            prop="recommender"
          >
            <Input v-model="formData.recommender" />
          </Form-item>
          <!-- <Form-item label="登录用户名"
                    prop="accounts">
            <Input v-model="formData.accounts" />
          </Form-item>
          <Form-item label="登录密码"
                    prop="password">
            <Input type="password"
                  v-model="formData.password" />
          </Form-item>
          <Form-item label="确认密码"
                    prop="check_password">
            <Input type="password"
                  v-model="formData.check_password" />
          </Form-item>
          -->
          <!-- <Form-item label="会员规模"
                    prop="member_scale">
            <Select v-model="formData.member_scale">
              <Option value="1">100人以下</Option>
              <Option value="2">100-500人</Option>
              <Option value="3">500人以上</Option>
            </Select>
          </Form-item>
          <Form-item label="营业执照上传"
                    prop="license">
            <Input v-model="formData.license"
                  :value="uploadList[0] ? uploadList[0].url : ''"
                  style="display: none" />
            <div class="upload-images">
              <div class="demo-upload-list"
                  v-for="(item, index) in uploadList"
                  :key="index">
                <template v-if="item.status === 'finished'">
                  <img :src="item.url">
                  <div class="demo-upload-list-cover">
                    <Icon type="ios-eye-outline"
                          @click.native="handleView(item.url)"></Icon>
                  </div>
                </template>
                <template v-else>
                  <Progress :percent="item.percentage"
                            hide-info></Progress>
                </template>
              </div>
            </div>
            <Upload :action="uploadUrl"
                    :data="{savePath: './../../Uploads/'}"
                    :default-file-list="defaultList"
                    :show-upload-list="false"
                    :format="['jpg','jpeg','png']"
                    ref="upload"
                    :before-upload="handleBeforeUpload"
                    :on-success="handleSuccess"
                    :max-size="10000"
                    :on-format-error="handleFormatError"
                    :on-exceeded-size="handleMaxSize"
                    multiple>
              <div class="upload-icon">
                <Icon type="camera"
                      size="20"></Icon>
              </div>
            </Upload>
            <Modal title="查看已上传"
                  v-model="visible">
              <img :src="imgUrl"
                  v-if="visible"
                  style="width: 100%">
            </Modal>
          </Form-item> -->
        </Form>
        <div class="agree-checkbox">
          <Checkbox v-model="agree">
            <span>同意服务协议</span>
          </Checkbox>
          <a
            class="read-agree"
            href="https://mp.weixin.qq.com/s?__biz=MzI5ODE1MjA4Mw==&mid=501949274&idx=1&sn=97841a06dfead62d889563b9cf5ac693#rd"
            target="_blank"
          >《勤鸟运动健身服务平台协议》</a>
        </div>
        <Button
          class="submit"
          @click="doRegister('registerForm')"
        >
          立即免费试用
        </Button>
      </div>
    </div>
    <div
      v-else
      class="success"
    >
      <h3>
        <img
          src="./../../assets/img/register-success.png"
          alt=""
        >
        <span>申请已提交</span>
      </h3>
      <p>恭喜您注册成功！</p>
      <p>即日起您可以免费试用勤鸟系统7天</p>
      <p>wx.rocketbird.cn</p>
    </div>
  </div>
</template>

<script>
  import { getBaseUrl } from './../../utils/config';
  import verifyModal from './../../components/register/verifyModal.vue';

  export default {
    name: 'Register',
    components: {
      verifyModal
    },
    data() {
      return {
        uploadUrl: getBaseUrl() + '/Admin/Public/upload',
        verifyModal: false,
        captcha: '',
        messageTimer: null,
        busTypeOptions: [
          { label: '健身房', value: '1' },
          { label: '健身工作室', value: '9' },
          { label: '体育馆', value: '7' },
          { label: '游泳馆', value: '8' },
          { label: '瑜伽馆', value: '2' },
          { label: '跆拳道馆', value: '3' },
          { label: '武道馆', value: '4' },
          { label: '舞蹈馆', value: '5' },
          { label: '其他', value: '6' },
        ],

        defaultList: [],
        imgUrl: '',
        visible: false,
        uploadList: [],

        agree: true,
        registerSucceed: false,
        verifyBtnWord: '获取验证码',
        verifyBtnDisabled: false,
        registerUrl: '/Admin/MerchantsRegister/ajax_apply',
        regionUrl: '/Admin/MerchantsRegister/ajax_getRegion',
        smsUrl: '/Admin/MerchantsRegister/ajax_send_sms',
        veriUrl: '/Admin/MerchantsRegister/ajax_check_username',
        cities: {},
        districts: {},

        formData: {
          bus_name: '',
          province: '2',
          city: '52',
          district: '500',
          // address: '',
          apply_name: '',
          phone: '',
          code: '',
          recommender: '',
          // accounts: '',
          // password: '',
          // check_password: '',
          bus_type: '1',
          // member_scale: '1',
          // license: '',
          id: ''
        },
        ruleValidate: {
          bus_name: [{ required: true, message: '请填写场馆名称' }],
          province: [{ required: true, message: ' ' }],
          city: [{ required: true, message: ' ' }],
          district: [{ required: true, message: ' ' }],
          // address: [{ required: true, message: '请填写地址' }],
          apply_name: [{ required: true, message: '请填写姓名' }],
          phone: [
            { required: true, message: '请填写电话号码' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号错误', trigger: 'blur' }
          ],
          code: [{ required: true, message: ' ' }]
          // accounts: [{ required: true, message: '请填写用户名(用于登录)' }],
          // password: [{ required: true, message: '请设置登录密码' }],
          // check_password: [{ required: true, message: '请再次输入登录密码' }],
          // license: [{ required: true, message: '请上传营业执照' }]
        }
      };
    },
    created() {
      if (this.getQueryString('from') === "baidusem" && window._hmt) {
        localStorage.setItem('isBaidusem', 1)
        window._hmt.push(['_setCustomVar', 1, 'baidusem', "手机注册页", 1])
      }
      let windowWidth = document.documentElement.clientWidth;
      document.documentElement.style.fontSize = `${windowWidth / 7.5}px`;
      this.provinceChanged(2);
      this.cityChanged(52);
      const search = location.search;
      const trimmedSearch = search.substring(1);

      const searchObj = trimmedSearch
        ? JSON.parse('{"' + trimmedSearch.replace(/&/g, '","').replace(/=/g, '":"') + '"}', function(key, value) {
            return key === '' ? value : decodeURIComponent(value);
          })
        : {};

      this.formData.phone = searchObj.phone;
      this.formData.recommender = searchObj.tj;
    },
    mounted() {
      // this.uploadList = this.$refs.upload.fileList;
    },
    methods: {
      getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
      },
      beforeVerifyModal() {
        let phone = this.formData.phone;
        let reg = /^1[3456789]\d{9}$/;
        if (!reg.test(phone)) {
          this.$Message.error('手机号错误');
          return false;
        }
        this.$service
          .post(this.veriUrl, { phone: phone })
          .then(res => {
            if (res.data.status === 1) {
              this.verifyModal = true;
            } else {
              this.$Message.error(res.data.msg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getVerifyModalCode(val) {
        this.captcha = val;
        this.verifyModal = false;
        this.getSms();
      },
      // handleView(url) {
      //   this.imgUrl = url;
      //   this.visible = true;
      // },
      // handleSuccess(res, file) {
      //   file.url = `${res.info}@60q_1pr`;
      //   this.formData.license = res.info;
      // },
      // handleFormatError() {
      //   this.$Notice.warning({
      //     title: '文件格式不正确',
      //     desc: '文件格式不正确，请上传 jpg 或 png 格式的图片。'
      //   });
      // },
      // handleMaxSize() {
      //   this.$Notice.warning({
      //     title: '超出文件大小限制',
      //     desc: '文件太大，不能超过 10M。'
      //   });
      // },
      // handleBeforeUpload() {
      //   this.uploadList.shift();
      // },
      checkPhoneNum() {
        let phone = this.formData.phone;
        let reg = /^1[3456789]\d{9}$/;
        if (!reg.test(phone)) {
          this.$Message.error('手机号错误');
          return false;
        }
        return true;
      },
      setTimer() {
        let second = 60;
        this.messageTimer = setInterval(() => {
          if (second > 1) {
            second--;
            this.verifyBtnWord = `${second}s后重新获取`;
          } else {
            clearInterval(this.messageTimer);
            this.verifyBtnDisabled = false;
            this.verifyBtnWord = `获取验证码`;
          }
        }, 1000);
      },
      // 获取验证码
      getSms() {
        if (!this.checkPhoneNum()) return false;

        let url = this.smsUrl;
        let postData = {
          phone: this.formData.phone,
          captcha: this.captcha
        };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.status === 1) {
              this.verifyBtnDisabled = true;
              this.setTimer();
              this.$Message.success(res.data.msg);
            } else {
              clearInterval(this.messageTimer);
              this.verifyBtnDisabled = false;
              this.verifyBtnWord = `获取验证码`;
              if (res.data.status === 0) {
                this.$Message.error(res.data.msg);
              } else {
                this.$Message.error('服务器发生错误');
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      doRegister(name) {
        this.$refs[name].validate(valid => {
          if (valid) {
            if (!this.agree) {
              this.$Message.error('请勾选服务协议!');
              return false;
            }
            let url = this.registerUrl;
            let postData = this.formData;
            this.$service.post(url, postData).then(res => {
              if (res.data.status === 0) {
                this.$Message.error(res.data.msg);
                return false;
              } else {
                if (localStorage.getItem('isBaidusem') === '1' && window._hmt) {
                  window._hmt.push(['_setCustomVar', 1, 'baidusem', (postData.phone|| "未识别")+'_mobile', 1])
                  window._hmt.push(['_trackEvent', 'baidusem', 'registerSuccess', (postData.phone|| "未识别")+'_mobile']);
                }
                this.registerSucceed = true;
              }
            });
          } else {
            this.$Message.error('申请单填写有误!');
          }
        });
      },
      provinceChanged(val) {
        this.cities = this.districts = {};
        let url = this.regionUrl;
        let postData = {
          region_id: val
        };
        this.$service.post(url, postData).then(res => {
          if (res.status === 200) {
            this.cities = res.data;
          } else {
            this.cities = {};
            this.$Message.error('网络错误');
          }
        });
      },
      cityChanged(val) {
        this.districts = {};
        let url = this.regionUrl;
        let postData = {
          region_id: val
        };
        this.$service.post(url, postData).then(res => {
          if (res.status === 200) {
            this.districts = res.data;
          } else {
            this.districts = {};
            this.$Message.error('网络错误');
          }
        });
      }
    }
  };
</script>

<style lang="less">
  @rem: 100rem;
  @wrapWidth: 608 / @rem;
  @baseColor: #aab4bf;
  html,body {
    background: #ffffff;
  }
  .register {
    width: 100%;
    height: 100%;
    // background: #070c0f url('./../../assets/img/register-bg.jpg') no-repeat center -144 / @rem / contain;
    font-size: 26 / @rem;

    .ivu-input {
      padding: 0 7 / @rem;
      height: 100%;
    }

    .ivu-input-wrapper {
      line-height: inherit;
    }

    .upload-images {
      margin-left: 20 / @rem;
    }

    .ivu-form-item-content {
      flex: 1;
    }

    .demo-upload-list {
      display: inline-block;
      width: 60 / @rem;
      height: 60 / @rem;
      text-align: center;
      line-height: 60 / @rem;
      border: 1px solid transparent;
      border-radius: 4 / @rem;
      overflow: hidden;
      background: #fff;
      position: relative;
      box-shadow: 0 1 / @rem 1 / @rem rgba(0, 0, 0, 0.2);
      margin-right: 4 / @rem;
    }

    .demo-upload-list img {
      width: 100%;
      height: 100%;
    }

    .demo-upload-list-cover {
      display: none;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
    }

    .demo-upload-list:hover .demo-upload-list-cover {
      display: block;
    }

    .demo-upload-list-cover i {
      color: #fff;
      font-size: 20 / @rem;
      cursor: pointer;
      margin: 0 2 / @rem;
    }
    // 申请成功
    .success {
      width: 100%;
      height: 100vh;
      overflow-y: hidden;
      // background-color: rgba(4, 18, 32, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      h3 {
        height: 60 / @rem;
        // font-size: 30 / @rem;
        // font-weight: normal;
        // color: #fff;
        font-size: 40 / @rem;
        display: flex;
        align-items: center;
        margin-bottom: 80 / @rem;
        span {
          white-space: nowrap;
        }
        img {
          height: 40 / @rem;
          margin-right: 20 / @rem;
        }
      }
      p {
        padding-bottom: 20 / @rem;
        font-size: 36 / @rem;
        // color: #a8b7c6;
      }
    }

    label {
      white-space: nowrap;
    }

    .ivu-checkbox-wrapper {
      display: flex;
      align-items: center;
      font-size: 26 / @rem;
      > span {
        // color: #fff;
      }
      .ivu-checkbox {
        display: flex;
        align-items: center;
      }
      .ivu-checkbox-input {
        margin: 5 / @rem 10 / @rem 0 0;
        width: 34 / @rem;
        height: 34 / @rem;
        border-radius: 4 / @rem;
        border: 0;
        outline: 0;
        transition: all 1s;
      }
    }



    .container {
      overflow-y: auto;
      width: 100%;
      // background: linear-gradient(to bottom, rgba(4, 18, 32, 0.1) 10%, rgba(4, 18, 32, 0.4) 10%, rgba(4, 18, 32, 1) 20%);
      display: flex;
      flex-direction: column;
      align-items: center;
      // padding-top: 80 / @rem;

      .top-title-wrapper {
        display: flex;
        align-items: center;
        padding-left: 40 / @rem;
        width: 100%;
        height: 360 / @rem;
        min-height: 360 / @rem;
        color: #ffffff;
        background: #cc0a27 url('./../../assets/img/register-bg-v2-top.png') no-repeat center / cover;

        .name {
          font-weight: bold;
          font-size: 40 / @rem;
        }
        .h1 {
          margin: 8 / @rem 0;
          font-weight: 400;
          font-size: 36 / @rem;
          letter-spacing: .05rem;
        }
        .text {
          font-size: 30 / @rem;
          font-weight: 300;
        }
      }

      // h1 {
      //   font-size: 34 / @rem;
      //   font-family: 'PingFang SC', 'Microsoft YaHei', serif;
      //   font-weight: normal;
      //   // color: #fff;
      //   border: 1px solid #000;
      //   letter-spacing: 0.7em;
      //   padding: 20 / @rem 25 / @rem;
      // }

      .form-content-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 1.24rem;
        width: 100%;
        flex: 1;
        background: #ffffff url('./../../assets/img/register-bg-v2-bottom.png') no-repeat center / cover;
        z-index: 1;
        &::before {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          background-color: rgba(255, 255, 255, .5);
          z-index: -1;
        }
      }

      .ivu-form {
        // padding-top: 130 / @rem;
        padding-top: 60 / @rem;
        width: @wrapWidth;
      }
      .ivu-form-item {
        display: flex;
        border-bottom: 1px solid #aab4bf;
        height: 75 / @rem;
        line-height: 75 / @rem;
        margin-bottom: 23 / @rem;
        flex: 1;
      }
    }

    .area {
      display: flex;
      .ivu-form-item-content {
        width: 60%;
      }
      input {
        width: 100%;
      }
    }

    .get-verify {
      background-color: transparent !important;
      color: #fc4545 !important;
      border: 1px solid #fc4545 !important;
      height: 60 / @rem;
      width: 220 / @rem;
      font-size: 26 / @rem;
      padding: 6 / @rem 18 / @rem;
      box-sizing: border-box;
      text-align: center;
      outline: 0;

      > span {
        white-space: nowrap;
      }
    }

    .submit {
      width: @wrapWidth;
      height: 80 / @rem;
      background-color: #fc4545 !important;
      color: #fff !important;
      font-size: 34 / @rem;
      letter-spacing: .2em;
      margin: 32 / @rem 0;
      border: 0;
      border-radius: 2 / @rem;
      outline: 0;
    }

    .agree-checkbox {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: @wrapWidth;
      margin: 5 / @rem 0;
    }

    .read-agree {
      text-decoration: none;
      color: #1fbcfd;
      display: flex;
      align-items: center;
    }

    .ivu-upload {
      display: flex;
      .ivu-upload-list {
        // color: #fff;
      }
      .ivu-btn {
        margin-left: 20 / @rem;
        // color: #fff;
      }
    }

    .upload-icon {
      width: 60 / @rem;
      height: 60 / @rem;
      line-height: 60 / @rem;
      border: 1px solid @baseColor;
      // color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5 / @rem;
    }
  }

  // 处理在ios下选择框滚动条不显示的问题
  .ivu-select-dropdown {
    // 滚动条整体部分
    &::-webkit-scrollbar {
        width: 3px;
        -webkit-border-radius: 1px;
        -moz-border-radius: 1px;
        border-radius: 1px;
    }
    // 滚动条的轨道
    &::-webkit-scrollbar-track-piece {
      //  background-color: rgba(0, 0, 0, 0);
      //  border-left: 1px solid rgba(0, 0, 0, 0);
    }

    // 滚动条里面的滑块
    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, .3);
        background-clip: padding-box;
        -webkit-border-radius: 1px;
        -moz-border-radius: 1px;
        border-radius: 1px;
        // min-height: 10px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, .5);
    }
  }
</style>

