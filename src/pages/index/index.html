<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="robots" content="noindex, nofollow">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
 
  <!--  -->
  <!-- <script src="https://unpkg.com/gcoord/dist/gcoord.js"></script> -->

  <title>勤鸟运动</title>
  <script>
    function indexLoadJS(beforeNode, url) {
      var scriptTag = document.createElement('script');
      scriptTag.setAttribute('src', url);
      scriptTag.setAttribute('type', 'text/javascript');
      var parentNode = beforeNode.parentNode;
      if (parentNode) {
        parentNode.insertBefore(scriptTag, beforeNode)
        parentNode.removeChild(beforeNode);
      }
    }

    function indexLoadCSS(beforeNode, url) {
      var style = document.createElement('link');
      style.href = url;
      style.rel = 'stylesheet';
      style.type = 'text/css';
      var parentNode = beforeNode.parentNode;
      if (parentNode) {
        parentNode.insertBefore(style, beforeNode)
        parentNode.removeChild(beforeNode);
      }
    }
    window.addEventListener('error', function (e) {
      var target = e.target || e.srcElement
      if (target.nodeName === "SCRIPT" && target.src.indexOf('vipcdn.rocketbird.cn/js') != -1) {
        var scriptSrc = target.src.replace('vipcdn.rocketbird.cn', 'vipcdn2.rocketbird.cn')
        indexLoadJS(target, scriptSrc)
      } else if (target.nodeName === "LINK" && target.href.indexOf('vipcdn.rocketbird.cn/css') != -1) {
        var linkHref = target.href.replace('vipcdn.rocketbird.cn', 'vipcdn2.rocketbird.cn')
        indexLoadCSS(target, linkHref)
      }
    }, true)

  </script>
</head>

<body>
  <noscript>
    <strong>We're sorry but code doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
  </noscript>
  <div id="v2App"></div>
  <!-- built files will be auto injected -->
</body>

</html>
