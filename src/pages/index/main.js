//阿里云ARMS性能监控
// import 'utils/aliARMS.js'
import './public-path'
import 'font-awesome/css/font-awesome.css';
import Vue from 'vue';
import App from './App';
import router from './../../router';
import iView from './iviewReset';
import components from 'components/global';
import store from './../../store';
import service from './../../service';
// import VueAMap from 'vue-amap';
// 定制ivew主题样式
import './../../styles/index.less';
import './../../styles/table.less';
import qiankunActions from "@/utils/qiankun-action";
Vue.config.productionTip = false;
Vue.prototype.$service = service;
Vue.use(iView);
Vue.use(components);
//防止button等再得到焦点状态的二次误触
window.addEventListener('keyup', function(e) {
  let event = e || window.event;
  let target = event.target || event.srcElement
  let code = event.keyCode || event.which || event.charCode;
  if (code == 13 && target && target.nodeName === "BUTTON") {
    target.blur()
  }
},true)
window.addEventListener('click', function (e) {
  let event = e || window.event;
  let target = event.target || event.srcElement
  if (target && target.nodeName === "BUTTON") {
    target.blur()
  }
},true)
// VueAMap.initAMapApiLoader({
//   key: '1001129a414cce091a331de7fc273df1',
//   plugin: [
//     'Autocomplete',
//     'PlaceSearch',
//     'Scale',
//     'OverView',
//     'ToolBar',
//     'MapType',
//     'PolyEditor',
//     'CircleEditor',
//     'Geolocation'
//   ]
// });
// Vue.use(VueAMap);

const errHandlerLog = function(errMsg) {
  let errInfo = { time: new Date().getTime(), info: window.location.href + errMsg };
  let errMsgList = store.state.errHandleList;
  let errFlag = false;
  let findFlag = false;
  for (let item of errMsgList) {
    if (item.info == errInfo.info) {
      findFlag = true;
      if (errInfo.time - item.time < 60 * 1000) {
        errFlag = true;
      } else {
        item.time = errInfo.time;
        store.commit('SET_ERR_HANDLE_LIST', errMsgList);
      }
      break;
    }
  }
  if (!findFlag) {
    errMsgList.push(errInfo);
    store.commit('SET_ERR_HANDLE_LIST', errMsgList);
  }
  if (!errFlag) {
    let errData = {
      url: window.location.href,
      error_msg: errMsg,
      user_agent: navigator.userAgent
    };
    service.post('/Web/Upload/jslog', errData);
  }
};
// 一般错误上报
window.onerror = function(msg, url, line, col, error) {
  console.error(error);
  //由于浏览器的同源策略往往无法获取错误的具体信息，需要手动上报。
  window.__bl && __bl.error(error, {
    filename: url,
    lineno: line,
    colno: col
  });
  errHandlerLog(msg);
};

// 组件渲染时出现运行错误上报
Vue.config.errorHandler = function (err, vm, info) {
  console.error(err);
  window.__bl && __bl.error(err, {
    filename: `${window.location.href}, ${vm.$vnode.tag}, ${info}`
  });
  errHandlerLog(err.message);
};

// 百度统计
// var _hmt = _hmt || [];
// (function() {
//   var hm = document.createElement('script');
//   hm.src = '//hm.baidu.com/hm.js?407e2bd7b2e251ed84a39976dd041cc6';
//   var s = document.getElementsByTagName('script')[0];
//   s.parentNode.insertBefore(hm, s);
// })();

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start();
  const { fullPath, hash, meta, name, params, path, query } = to;
  qiankunActions.setGlobalState({
    subRouteInfo: { fullPath, hash, meta, name, params, path, query },
  })
  next();
  // 第一次加载页面时无法在此处获取store.state.busName 需要在headerTop组件中重新设置
  if (qiankunActions.isMicroAppV2(to.path)) {
    document.title = `${
      window.IS_BRAND_SITE ? store.state.merchantName : store.state.busName ? store.state.busName : '勤鸟运动'
    }${to.name ? '—' + to.name : ''}`
  }
});

router.afterEach(() => {
  iView.LoadingBar.finish();
  // Cannot read property 'nodeName' of null
  if (document.readyState === "complete") {
    let vipMainCon = document.getElementById('vipMainCon');
    if (vipMainCon) {
      vipMainCon.scrollTop = -40;
    }
  }
  // 百度统计
  // _hmt.push(['_trackPageview', router.path]);
});

/* eslint-disable no-new */
let instance = null
function render(props = {}) {
  const { container } = props
  if (Object.keys(props).length) {
    qiankunActions.setActions(props)
  }
  instance = new Vue({
    router,
    store,
    render: (h) => h(App),
  }).$mount(container ? container.querySelector('#v2App') : '#v2App')
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  render()
}
/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log('[vue] vue app bootstraped')
}
/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  console.log('[vue] props from main framework mount', props)
  render(props)
}
/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount() {
  console.log('[vue] vue app unmount')
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
}
/**
 * 可选生命周期钩子，仅使用 loadMicroApp 方式加载微应用时生效
 */
export async function update(props) {
  console.log('update props', props);
}

/** 重写window.open 主应用中打开子应用链接需要加微应用activeRule**/
window.open = (function (_open) {
  const qiankunRouterBase = '/v2/'
  return function () {
    if (window.__POWERED_BY_QIANKUN__) {
      const url = arguments[0]
      if (url.indexOf(qiankunRouterBase) !== 0 && !(url.indexOf('http://') === 0 || url.indexOf('https://') === 0)) {
        const isLineStart = /^\/.*/.test(url)
        arguments[0] = qiankunRouterBase + (isLineStart ? url.slice(1) : url)
      }
    }
    _open.apply(this, arguments)
  }
}(window.open))