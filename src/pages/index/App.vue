<template>
  <div id="v2AppWrapper">
    <Alert type="warning" show-icon v-show="$store.state.timeOutAlert" class="alert-warning" closable @on-close="closeAlert">
        网络超时
        <template slot="desc">
            您的网络慢，建议刷新重试或呼叫您的网管.
        </template>
    </Alert>
    <router-view></router-view>
    <Spin fix v-show="$store.state.loading">
      <Icon type="ios-loading" size='48'  class="spin-load"></Icon>
      <div>{{$store.state.loadingText || 'Loading...'}}</div>
    </Spin>
  </div>
</template>

<script>
  export default {
    name: 'app',
    methods: {
      closeAlert() {
        this.$store.commit('SET_TIMEOUTALERT', false)
      }
    }
  }
</script>

<style>
  html, body, #v2AppWrapper{
    height: 100vh;
  }
  #v2container #v2AppWrapper {
    height: calc(100vh - 60px);
  }


  .spin-load {
    -webkit-animation: ani-spin 1s linear infinite;
    animation: ani-spin 1s linear infinite;
  }

  @keyframes ani-spin {
    from {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .ivu-spin {
    color: #52a4ea !important;
  }

  .ivu-spin-fix {
    background: rgba(255, 255, 255, 0) !important;
    z-index: 99999 !important;
  }

  .ivu-message {
    z-index: 2018;
  }
  .alert-warning {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    margin: 0 !important;
  }
</style>
