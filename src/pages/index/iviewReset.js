import iView from 'iview';

let iviewRest = iView
// 重写 this.$Message.error, 最多同时显示一个错误
let errorFlag = true;
iviewRest.Message.error = function (options = {
  duration: 1.5,
  content: ''
}) {
  if (!errorFlag) return false;
  errorFlag = false;
  setTimeout(() => {
    errorFlag = true;
  }, (options.duration || 1.5) * 1000);
  return this.message('error', options);
};
//fix iview 远程搜索bug
const optionRegexp = /^i-option$|^Option$/i;
const optionGroupRegexp = /option-?group/i;
const findChild = (instance, checkFn) => {
  let match = checkFn(instance);
  if (match) return instance;
  for (let i = 0, l = instance.$children.length; i < l; i++) {
    const child = instance.$children[i];
    match = findChild(child, checkFn);
    if (match) return match;
  }
};

const findOptionsInVNode = (node) => {
  const opts = node.componentOptions;
  if (opts && opts.tag.match(optionRegexp)) return [node];
  if (!node.children && (!opts || !opts.children)) {
    return [];
  }
  const children = [...(node.children || []), ...(opts && opts.children || [])];
  const options = children.reduce(
    (arr, el) => [...arr, ...findOptionsInVNode(el)], []
  ).filter(Boolean);
  return options.length > 0 ? options : [];
};

const extractOptions = (options) => options.reduce((options, slotEntry) => {
  return options.concat(findOptionsInVNode(slotEntry));
}, []);
const applyProp = (node, propName, value) => {
  return {
    ...node,
    componentOptions: {
      ...node.componentOptions,
      propsData: {
        ...node.componentOptions.propsData,
        [propName]: value,
      }
    }
  };
};

const getNestedProperty = (obj, path) => {
  const keys = path.split('.');
  return keys.reduce((o, key) => o && o[key] || null, obj);
};

iviewRest.Select.computed.selectOptions = function () {
  const selectOptions = [];
  const slotOptions = (this.slotOptions || []);
  let optionCounter = -1;
  const currentIndex = this.focusIndex;
  const selectedValues = this.values.filter(Boolean).map(({
    value
  }) => value);
  if (this.autoComplete) {
    const copyChildren = (node, fn) => {
      return {
        ...node,
        children: (node.children || []).map(fn).map(child => copyChildren(child, fn))
      };
    };
    const autoCompleteOptions = extractOptions(slotOptions);

    const selectedSlotOption = autoCompleteOptions[currentIndex];

    return slotOptions.map(node => {
      if (node === selectedSlotOption || getNestedProperty(node, 'componentOptions.propsData.value') === this.value) return applyProp(node, 'isFocused', true);
      return copyChildren(node, (child) => {
        if (child !== selectedSlotOption) return child;
        return applyProp(child, 'isFocused', true);
      });
    });
  }
  for (let option of slotOptions) {

    const cOptions = option.componentOptions;
    if (!cOptions) continue;
    if (cOptions.tag.match(optionGroupRegexp)) {
      let children = cOptions.children;

      // remove filtered children  //添加远程搜索不过滤
      if (this.filterable && !this.remote) {
        children = children.filter(
          ({
            componentOptions
          }) => this.validateOption(componentOptions)
        );
      }

      // fix #4371
      children = children.map(opt => {
        optionCounter = optionCounter + 1;
        return this.processOption(opt, selectedValues, optionCounter === currentIndex);
      });

      // keep the group if it still has children  // fix #4371
      if (children.length > 0) selectOptions.push({ ...option,
        componentOptions: { ...cOptions,
          children: children
        }
      });
    } else {
      // ignore option if not passing filter
      if (this.filterQueryChange) {
        //添加远程搜索不过滤
        const optionPassesFilter = this.filterable && !this.remote ? this.validateOption(cOptions) : option;
        if (!optionPassesFilter) continue;
      }

      optionCounter = optionCounter + 1;
      selectOptions.push(this.processOption(option, selectedValues, optionCounter === currentIndex));
    }
  }
  return selectOptions;
};

export default iviewRest;
