import service from './index'

/**
 * 左侧导航数据
 */
let getLeftNav = () => service.get('/Web/NodeGroup/get_menus');

/**
 * 当前权限所能查看到的所有场馆列表名
 */
let getBuseList = () => service.get('/Web/Business/get_bus_list');

let getAdminBusList = () => service.get('/Web/MemberList/getAdminBusList');
/**
 * 当前商家下的所有场馆列表
 */
let getMerchBuseList = () => service.get('/Web/Card/get_merchants_bus_list');

 /**
  获取商家下场馆列表
  @param { Object } query { m_id }
  @return { Promise }
*/
export const getMerBusList = (query)  => service.get('/Merchant/NewMemberApplet/getMerBusList', query);

// 会员详情里的会员卡操作，获取会员卡信息
let getcardInfo = (postData) => service.post('/Web/Member/get_card_user_general_info',postData);

// 会员卡选择 (is_help_sale 总部端套餐包2，门店套餐包0或者不传； packageBusId 套餐包编辑时，被编辑套餐包的bus_id)
// type 0或空 不包含体验卡  1 包含体验卡(注意：此时需要belongBusId有值)
let getCards = (belongBusId, type, is_help_sale, packageBusId) => service.get(`/Web/Member/get_card?belong_bus_id=${belongBusId || ''}&type=${type||''}&is_help_sale=${is_help_sale||''}${packageBusId ? '&bus_id=' + packageBusId : ''}` );

/**
*获取场馆获客来源
*请求参数：source_type(1:获客来源 2:会籍成交方式 3:私教课成交方式，不传都会返回) 编辑卡时传card_user_id,编辑会员时传user_id
*/
let getSources = (postData) => service.post('/Web/Member/get_source',postData);

/**
*销售人员选择
*/
let getSales = (postData) => service.post('/Web/Member/get_sale',postData);

/**
*获取当前卡片支持的场馆列表
*/
let getSupportBus = (cardId) => service.get('/Web/Card/get_support_bus/card_id/' + cardId)

/**
 * 教练列表
 * type: 0所有 1私教 2泳教
 */
let getcoachsInfo = (belongBusId, contain_deleted, type) =>
  service.get(
    "/Web/Member/get_coach?belong_bus_id=" +
      (belongBusId || "") +
      "&contain_deleted=" +
      (contain_deleted || "") +
      "&type=" +
      (type || "")
  );

/**
 * 私教教练列表
 */
export const getPtCoachList = (belongBusId) => service.get('/Web/Member/get_all_private_coach_list?belong_bus_id=' + (belongBusId || ''))

/**
 * 会籍列表
 */
let getsalesInfo = (belongBusId) => service.get('/Web/Member/get_sale?belong_bus_id=' + (belongBusId || ''));

/**
 * 员工-教练-教练职位
 */
export const coachPosition = (bus_id) => service.get('/Web/Coach/get_coach_posi', { params: { bus_id } })

/**
 * 员工-会籍-会籍职位
 */
export const membershipPosition = () => service.get('/Web/Marketers/get_all_marketers_type')

/**
 * 员工-教练-教练分组
 */
export const coachGroup = (bus_id) => service.get('/Web/CoachGroup/get_coach_group', { params: { bus_id } })

// 教练分组成员列表
export const coachGroupList = () => service.post('/Web/Statistics/coach_group_list')
// 泳教分组成员列表
export const swimGroupList = () => service.post('/Web/Statistics/coach_group_list', {is_swimming_coach: true})

/**
 * 员工-会籍-会籍分组
 */
export const membershipGroup = () => service.get('/Web/MarketersGroup/get_marketers_group')

/**
 * 场馆-场馆详情
 */
export const gymInfo = (bus_id) => service.post(
  window.IS_BRAND_SITE ? '/Merchant/NewMemberApplet/getBusInfo' : '/Web/Business/get_bus_info',
  { bus_id }
)

// 会员卡列表卡选择
export const getMemberListCards = (belongBusId) => service.get('Web/Member/get_card?member_list=1&belong_bus_id=' + (belongBusId || ''));
/**
 * 账号信息
 */
export const adminInfo = () => service.post(`${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/getAccountDetails`)

export const receiptAuthInfo = () => service.post('/Web/SmallTicket/check_admin_print')
/**
 * 导出日志
 */
export const postExportLog = (postData) => service.post('/Web/Public/excel_export_log', postData)

export const inventoryList = (postData) => service.post('/web/inventory/inventoryList', postData);

export const catList = (postData) => service.post('/web/comCate/cateList', postData);

export const getCanEditFields = postData => service.post('/Web/Member/getCanEditFields', postData);
/**
 * 客带客活动列表
 */
export const queryPassengerList = (postData)  => service.post('/web/ActivityBringGuests/get_activity_list', postData);
/**
 * 客带客删除活动
 */
 export const delPassengerActivity = (id)  => service.post('/web/ActivityBringGuests/activity_del', {id});
 /**
 * 客带客下架活动
 */
  export const offPassengerActivity = (id)  => service.post('/web/ActivityBringGuests/activity_off', {id});
   /**
 * 客带客活动详情
 */
    export const queryPassengerActivityDetail = (id)  => service.post('/web/ActivityBringGuests/get_activity_detail', {id});
  /**
   * 折扣劵
   */
  export const queryBusCoupon = ()  => service.post('/Web/Coupon/get_bus_coupon');
  /**
 * 添加客带客活动
 */
export const addActivity = (postData)  => service.post('/web/ActivityBringGuests/add_activity_guests', postData);
  /**
 * 编辑客带客活动
 */
   export const editActivity = (postData)  => service.post('/web/ActivityBringGuests/edit_activity_guests', postData);
/**
 * 客带客活动统计
 */
 export const queryPassengerStatisticsList = (postData)  => service.post('/web/ActivityBringGuests/get_activity_statistics', postData);
/**
 * 客带客明细统计
 */
 export const queryPassengerStatisticsDetailList = (postData)  => service.post('/web/ActivityBringGuests/get_activity_user_detail_list', postData);
 /**
 * 场馆协议列表
 */
export const queryBusAgreementList = (postData)  => service.post('/web/bus_agreement/getLists', postData);
/**
 * 场馆协议详情
 */
export const queryBusProtocolDetail = (id)  => service.post('/web/bus_agreement/getDetailById', {id});
/**
 * 根据场馆id获取使用的协议详情
 */
 export const queryBusUsingProtocolDetail = (bus_id)  => service.post('/web/bus_agreement/getDetailByBusId', {bus_id});
 /**
 * 删除协议详情
 */
export const delBusProtocolDetail = (id)  => service.post('/web/bus_agreement/delete', {id});
 /**
 * 添加场馆协议
 */
export const addBusProtocolDetail = (postData)  => service.post('/web/bus_agreement/add', postData);
/**
 * 编辑场馆协议
 */
export const editBusProtocolDetail = (postData)  => service.post('/web/bus_agreement/edit', postData);
/**
 * 变更场馆协议状态
 */
 export const changeBusProtocolStatus = (postData)  => service.post('/web/bus_agreement/changeStatus', postData);
/**
 * 获取私教课评价详情
 */
 export const queryEvaluationDetail = (postData)  => service.post('/web/course_comment/getDetailBySignLogId', postData);
/**
 * 评价回复
 */
 export const addFirstReply = (postData)  => service.post('/web/course_comment/addFirstReply', postData);
/**
 * 卡课标签列表
 */
 export const getCardGroupListByBusId = (postData)  => service.post('/Web/CardGroup/getCardGroupListByBusId', postData);

 export const getSpaceTypes = (postData)  => service.post('/Web/Space/getTypes', postData);


export { getLeftNav, getBuseList, getAdminBusList, getMerchBuseList, getcardInfo, getCards, getSources, getSales, getSupportBus, getcoachsInfo, getsalesInfo  }
