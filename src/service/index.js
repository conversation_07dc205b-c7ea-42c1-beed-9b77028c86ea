import axios from 'axios';
import store from '../store';
import Qs from 'qs';
import {
  getBaseUrl
} from 'utils/config';
import {
  _uuid
} from 'utils';
let needLoadingRequestCount = 0
let prevAjaxHref = window.location.href
let curAjaxHref = ''

function tryShowFullScreenLoading() {
  if (needLoadingRequestCount === 0) {
    store.commit('SET_LOADING', true)
  }
  needLoadingRequestCount++
}

function tryHideFullScreenLoading() {
  if (needLoadingRequestCount <= 0) return
  needLoadingRequestCount--
  if (needLoadingRequestCount === 0) {
    store.commit('SET_LOADING', false)
  }
}

// 创建axios实例
const service = axios.create({
  baseURL: getBaseUrl(), // api的base_url
  withCredentials: true,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  transformRequest: [function (data, headers) {
    // `transformRequest` allows changes to the request data before it is sent to the server
    // This is only applicable for request methods 'PUT', 'POST', and 'PATCH'
    return headers['Content-Type'] === 'application/json' ?  JSON.stringify(data) : Qs.stringify(data);
  }],
  timeout: 30 * 1000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(config => {
  if (prevAjaxHref !== curAjaxHref) {
    store.commit('SET_TIMEOUTALERT', false)
  }
  prevAjaxHref = curAjaxHref
  curAjaxHref = window.location.href
  let requestId = _uuid()
  config.headers.requestId = requestId
  let configData = config.method == 'get' ? config.params : config.data
  if (configData && +configData.page_size > 50) {
    config.timeout = 50 * 1000;
  }
  config.loading !== false && tryShowFullScreenLoading()
  return config;
}, error => {
  tryHideFullScreenLoading()
  Promise.reject(error);
})

// 响应拦截器
service.interceptors.response.use(
  response => {
    tryHideFullScreenLoading()
    if (response.status !== 200) {
      throw new Error(`网络错误${response.status}`)
    } else {
      if (response.config.isExport && response.data.errorcode === 0) {
        store.dispatch('exportLog', response.config)
      }
    }
    return response;
  },
  error => {
    if (!store.state.timeOutAlert && error.message.indexOf('timeout') !== -1) {
      store.commit('SET_TIMEOUTALERT', true)
      needLoadingRequestCount = 0
      store.commit('SET_LOADING', false)
    } else {
      tryHideFullScreenLoading()
    }
    return Promise.reject(error);
  }
)

export default service;
