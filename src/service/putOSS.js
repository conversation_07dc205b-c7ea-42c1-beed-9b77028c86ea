import OSS from 'ali-oss'
import service from './index.js'
import axios from 'axios'

let client = null

const init = async () => {
  if (!client) {
    const settings = await service
      .get('/Web/business/get_oss_token')
      .then(res => res.data)
    client = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
      region: 'oss-cn-shenzhen',
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: settings.AccessKeyId,
      accessKeySecret: settings.AccessKeySecret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: settings.SecurityToken,
      // 填写Bucket名称。
      bucket: 'rb-platform',
      secure: true
    })
  }
}

const putOneDay = async (filename, dayJSON) => {
  try {
    // object表示上传到OSS的文件名称。
    // file表示浏览器中需要上传的文件，支持HTML5 file和Blob类型。
    const bf = new OSS.Buffer(JSON.stringify(dayJSON))
    const cb = await client.put(filename, bf)
    return cb.url
  } catch (e) {
    console.error('error: %j', e)
    client = null
    await init()
    return await putOneDay(filename, dayJSON)
  }
}

export async function putWeek(weekUrl, schedule) {
  await init()
  const weekFullUrl = {}
  for (const key in schedule) {
    if (Object.hasOwnProperty.call(schedule, key)) {
      const url = weekUrl[key]
      const day = schedule[key]
      weekFullUrl[key] = await putOneDay(url, day)
    }
  }
  return weekFullUrl
}

export async function getWeekJson(url) {
  return await axios.get(url).then(res => res.data)
}
