export function formatTime(time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) { // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
  }
}

//合并对象
export function objectMerge(target, source) {
  /* Merges two  objects,giving the last one precedence */
  if (typeof target !== 'object') {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  for (const property in source) {
    if (source.hasOwnProperty(property)) {
      const sourceProperty = source[property];
      if (typeof sourceProperty === 'object') {
        target[property] = objectMerge(target[property], sourceProperty);
        continue;
      }
      target[property] = sourceProperty;
    }
  }
  return target;
}

// 存cookie
export function getCookie(name) {
  let doc = document,
    val = null,
    regVal;
  if (doc.cookie) {
    regVal = doc.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    if (regVal != null) {
      val = decodeURIComponent(regVal[2]);
    }
  }
  return val;
}

//设置cookie
export function setCookie(name, value, expire, path, domain, s) {
  let dt = new Date();
  dt.setTime(dt.getTime() + 1000 * expire);
  if (!!domain) {
    document.cookie = name + "=" + encodeURIComponent(value) +
      ((expire) ? "; expires=" + dt.toGMTString() : "") +
      "; path=" + (path || '/') +
      "; domain=" + (domain || "rocketbird.cn") +
      ((s) ? "; secure" : "");
  } else {
    document.cookie = name + "=" + encodeURIComponent(value) +
      ((expire) ? "; expires=" + dt.toGMTString() : "") +
      "; path=" + (path || '/') +
      "; domain=" + (".rocketbird.cn") +
      ((s) ? "; secure" : "");
  }
  return true;
}

export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += '' + className;
  } else {
    classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

// 月份日期时间等 小于 10 返回两位数
export function ten(num) {
  return num < 10 ? `0${num}` : num
}

/**
 * 格式化时间
 * @param  {Datetime} source 时间对象
 * @param  {String} format 格式
 * @return {String}        格式化过后的时间
 *月(M)、日(d)、小时(H)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
 * 年(y)可以用 1-4 个占位符，毫秒(f)只能用 1 个占位符(是 1-3 位的数字)
 * 例子：
 * formatDate(new Date(1494818037415),"yyyy-M-d H:m:s.f") ==>2017-5-15 11:13:57.415
 * formatDate(new Date(),"yyyy-MM-dd HH:mm:ss") ==> 2017-05-15 11:15:33
 */
export function formatDate(source, format) {
  source = new Date(source);
  const o = {
    'M+': source.getMonth() + 1, // 月份
    'd+': source.getDate(), // 日
    'H+': source.getHours(), // 小时
    'm+': source.getMinutes(), // 分
    's+': source.getSeconds(), // 秒
    'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
    'f+': source.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (source.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  return format
}
/**
 * 日期加上天数后的新日期.
 * @param  {Datetime} date 基准日期
 * @param  {number} days 天数
 */
export function addDays(date, days) {
  let nd = new Date(date);
  nd = nd.valueOf();
  nd = nd + days * 24 * 60 * 60 * 1000;
  nd = new Date(nd);
  let y = nd.getFullYear();
  let m = nd.getMonth() + 1;
  let d = nd.getDate();
  if (m <= 9) m = "0" + m;
  if (d <= 9) d = "0" + d;
  let cdate = y + "-" + m + "-" + d;
  return cdate;
}
/**
 * 两个日期的差值(天)
 * @param  {Datetime} d1
 * @param  {Datetime} d2
 */
export function dateDiff(d1, d2) {
  let day = 24 * 60 * 60 * 1000;
  try {
    let dateArr = d1.split("-");
    let checkDate = new Date();
    checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
    let checkTime = checkDate.getTime();

    let dateArr2 = d2.split("-");
    let checkDate2 = new Date();
    checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
    let checkTime2 = checkDate2.getTime();

    let cha = (checkTime - checkTime2) / day;
    return cha;
  } catch (e) {
    return false;
  }
}

/**
 * ajax 返回数据小处理一下
 * @param {*} response xmlresponse
 * @param {*} defaultVal 返回空数据类型
 */
export function getResponseData(response, defaultVal = []) {
  if (response.status === 200) {
    if (response.data.errorcode == 0) {
      return response.data.data;
    } else {
      console.log(response.data.errormsg);
    }
  } else {
    console.log('服务器扑街！');
  }
  return defaultVal;
}
/**
 * 全屏切换
 */
export function toggleFullScreen() {
  let doc = window.document;
  let docEl = doc.documentElement;
  let requestFullScreen = docEl.requestFullscreen || docEl.mozRequestFullScreen || docEl.webkitRequestFullScreen || docEl.msRequestFullscreen;
  let cancelFullScreen = doc.exitFullscreen || doc.mozCancelFullScreen || doc.webkitExitFullscreen || doc.msExitFullscreen;

  if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {
    requestFullScreen.call(docEl);
  } else {
    cancelFullScreen.call(doc);
  }
}
/**
 * var tree = [{"id":1,"pid":"-1","son":[{"id":11,"pid":"1","son":[]},{"id":12,"pid":"1","son":[]}]}];
treeToList(tree,'son') //[{"id":1,"pid":"-1"},{"id":11,"pid":"1"},{"id":12,"pid":"1"}]
 */
export function treeToList(treeNodes, key) {
  let NodeList = [];
  function appenChildren(nodes) {
    for (let i = 0; i < nodes.length; i++) {
      let node = nodes[i];
       NodeList.push(node);
      //判断是否有子节点
      if (node[key] && node[key].length > 0) {
        //所有子节点
        appenChildren(node[key]);
      }
      if (node.hasOwnProperty(key))
      delete node[key];
    }
  }
  appenChildren(treeNodes);
  return NodeList;
}

/**
 * VueHtml5Editor 处理返回的字符
 */
export function unescapeHTML(a) {
  a = '' + a
  return a
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
}

export function _uuid() {
  let d = Date.now();
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    d += performance.now(); //use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-8xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

/**
 * check a string contain Chinese characters
 */
export function countChineseCharacters(str) {
  const chineseReg = /[\u4e00-\u9fa5]/g;
  const chineseMatches = str.match(chineseReg);
  return chineseMatches ? chineseMatches.length : 0;
}
export function isChinese(val) {
  const str = val || ''
  if (str.length >= 3 || countChineseCharacters(str) >= 1) {
    return true;
  }
  return false;
}
export const SEARCH_HINT = '至少3位数字'
/**
 * check the date range less than 30 days
 */
export function checkRangeLessThan30(range) {
  if (Array.isArray(range) && range.length === 2) {
    return (new Date(range[1]).getTime() - new Date(range[0]).getTime()) < 31 * 24 * 60 * 60 * 1000
  } else {
    return false
  }
}
export const SEARCH_DATE_HINT = '日期搜索范围不能超过30天'

export function getQueryString(url) {
  let result = decodeURIComponent(url).match(new RegExp("[\?\&][^\?\&]+=[^\?\&]+","g"));
  if(result === null) {
      return [];
  }
  for (let i = 0; i < result.length; i++) {
      result[i] = result[i].substring(1);
  }
  return result;
}

// 根据QueryString参数名称获取值
export function getQueryStringByName(url, name) {
  let result = decodeURIComponent(url).match(new RegExp("[\?\&]" + name+ "=([^\&]+)","i"));
  if(result === null || result.length < 1) {
      return "";
  }
  return result[1];
}
