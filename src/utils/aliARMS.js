!(function (c, b, d, a, r, t) {
  if (c.location.host === 'vip.rocketbird.cn') {
    c[a] || (c[a] = {});
    c[a].config = {
      pid: "f2bmh4hag8@1019d88467f78a5",
      imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
      enableSPA: true,
      disableHook: true, //关闭API自动上报
      useFmp: true
    };
    r = b.createElement('script')
    t = b.getElementsByTagName('script')[0]
    r.crossorigin = ''
    r.src = d
    t.parentNode.insertBefore(r, t)
  }
})(window, document, "https://retcode.alicdn.com/retcode/bl.js", "__bl");
