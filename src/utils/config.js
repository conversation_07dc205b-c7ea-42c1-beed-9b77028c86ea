/**
 * 配置各个子域环境之间的切换
 *
 * getBaseUrl(): 得到当前域名地址对应的接口地址
 * getHost(): 得到当前域名对应的vue1.x版本的域名
 * imgBaseUrl: 图片所在CDN地址
 *
 */

const imgBaseUrl = 'https://imagecdn.rocketbird.cn/'
const apis = ['vip','pe','yoga', 'brand']//0正常api 1球馆 2瑜伽馆
const apisSim = ['vip-sim', 'brand-sim']; // sim
const apisFe = ['fe', 'brand-fe']; // fe

function getBaseUrl () {
  const { host } = window.location
  const subDomain = host.split('.')[0]

  if (apisFe.includes(subDomain)) {
    return '/api'
  }

  if (apisSim.includes(subDomain)) {
    return 'https://sim.rocketbird.cn'
  }

  if (apis.includes(subDomain)) {
    return 'https://wx.rocketbird.cn'
  }

  return 'https://beta.rocketbird.cn'
}
//websocket的基础域名地址
function getSocketUrl() {
  const { host } = window.location
  const subDomain = host.split('.')[0]

  if (apisSim.includes(subDomain)) {
    return 'wss://im-sim.rocketbird.cn:9500'
  }

  if (apis.includes(subDomain)) {
    return 'wss://im.rocketbird.cn:9500'
  }

  return 'wss://im-beta.rocketbird.cn'
}

function getHost() {
  const { host } = window.location
  const subDomain = host.split('.')[0]
  if (apisSim.includes(subDomain)) {
    return 'https://i-sim.rocketbird.cn'
  }
  if (apis.includes(subDomain)) {
    return 'https://i.rocketbird.cn'
  }
  return 'https://i-test.rocketbird.cn'
}

const getNewHost = function () {
  return window.location.host
}

// 得到当前环境的商家端域名
function getBrandHost() {
  const { host, port } = window.location;
  const subDomain = host.split('.')[0];

  if (apisFe.includes(subDomain)) {
    return `http://brand-fe.rocketbird.cn:${port}`;
  }

  if (apisSim.includes(subDomain)) {
    return 'https://brand-sim.rocketbird.cn';
  }

  if (apis.includes(subDomain)) {
    return 'https://brand.rocketbird.cn';
  }

  return 'https://brand-test.rocketbird.cn';
}
// 得到当前环境的门店端域名
function getBusHost() {
  const { host, port } = window.location;
  const subDomain = host.split('.')[0];

  if (apisFe.includes(subDomain)) {
    return `http://fe.rocketbird.cn:${port}`;
  }

  if (apisSim.includes(subDomain)) {
    return 'https://vip-sim.rocketbird.cn';
  }

  if (apis.includes(subDomain)) {
    return 'https://vip.rocketbird.cn';
  }

  return 'https://vip-test.rocketbird.cn';
}
const getOSSPath = () => {
  let path,
    host = window.location.host,
    hostname = window.location.hostname,
    subDomain = host.split('.')[0]
  if (apisSim.includes(subDomain)) {
    path = '/sim/json/'
  } else if (apis.includes(subDomain)) {
    path = '/online/json/'
  } else {
    path = '/test/json/'
  }
  if (hostname === 'localhost') {
    path = `http://${subDomain}`
  }

  return path
}

export { getBaseUrl, getSocketUrl, getHost, imgBaseUrl, getNewHost, getBrandHost, getBusHost, apis, getOSSPath }
