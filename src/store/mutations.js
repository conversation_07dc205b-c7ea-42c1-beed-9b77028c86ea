// 使用全大写常量形式区分mutations
import * as types from './mutationTypes';
export default {
  SET_MERCHANT_ID: (state, id) => {
    state.merchantId = id
  },
  SET_MERCHANT_NAME: (state, name) => {
    state.merchantName = name
  },
  SET_BUS_ID: (state, id) => {
    state.busId = id
  },
  SET_BUS_NAME: (state, busName) => {
    state.busName = busName
  },
  SET_ENCODE_BUS_ID: (state, busIdEncode) => {
    state.busIdEncode = busIdEncode
  },
  SET_BUS_LIST: (state, busList) => {
    state.busList = busList
  },
  SET_GATEDLAUNCH: (state, data) => {
    state.gatedLaunch = data
  },
  SET_ADMIN_BUSLIST: (state, busList) => {
    state.adminBusList = busList
  },
  SET_MERCHANTS_BUSLIST: (state, busList) => {
    state.merchantsBusList = busList
  },
  SET_ADMIN_NAME: (state, adminName) => {
    state.adminName = adminName
  },
  SET_ADMIN_ID: (state, adminId) => {
    state.adminId = adminId
  },
  SET_ADMIN_TYPE: (state, adminType) => {
    state.adminType = adminType
  },
  SET_GLOBAL_BELONG_BUS_ID: (state, globalBelongBusId) => {
    state.globalBelongBusId = globalBelongBusId
  },
  SET_SUPPORT_PAY: (state, supportPay) => {
    state.supportPay = supportPay
  },
  SET_BUS_LOGO: (state, data) => {
    state.busLogo = data
  },
  SET_SHOW_CUSTOM_LOGO: (state, supportPay) => {
    state.showCustomLogo = supportPay
  },
  SET_ADDCARD_LIST: (state, addCardList) => {
    state.addCardList = addCardList
  },
  SET_MEMBERCARD_LIST: (state, memberCardList) => {
    state.memberCardList = memberCardList
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading
  },
  SET_TIMEOUTALERT: (state, timeOutAlert) => {
    state.timeOutAlert = timeOutAlert
  },
  SET_LOADING_TEXT: (state, text) => {
    state.loadingText = text
  },
  SET_SOURCE_LIST: (state, sourceList) => {
    state.sourceList = sourceList
  },
  SET_ADD_FollOW_SUCCESS: (state, data) => {
    state.addFollowSuccess = data
  },
  SET_ERR_HANDLE_LIST: (state, data) => {
    state.errHandleList = data
  },
  SET_CAN_EDIT_FIELDS: (state, data) => {
    state.canEditFields = data
  },
  SET_FINACE_CHECK_DAYS: (state, data) => {
    state.financeCheckDays = data
  },
  SET_IS_QN_J: (state, data) => {
    state.is_qn_j = data
  },
  [types.SET_COACH_GROUP_LIST]: (state, list) => {
    state.coachGroupList = list
  },
  [types.SET_SWIM_GROUP_LIST]: (state, list) => {
    state.swimGroupList = list
  },
  [types.SET_ADMIN_INFO]: (state, info) => {
    state.adminInfo = Object.assign({}, info)
  },
  [types.SET_RECEIPT_AUTH]: (state, data) => {
    state.receiptAuth = data
  },
  [types.SET_MEMBERSHIP_LIST]: (state, list) => {
    state.membershipList = list
  },
  [types.SET_COACH_LIST]: (state, list) => {
    state.coachList = list
  },
  [types.CARD_SETTING_AUTH]: (state, data) => {
    state.cardSettingAuth = data
  },
  [types.SET_PT_COACH_LIST]: (state, list) => {
    state.ptCoachList = list
  },
  [types.SET_SWIM_COACH_LIST]: (state, list) => {
    state.swimCoachList = list
  },
  [types.SET_BUS_IMAGES]: (state, images) => {
    state.busImages = images
  },
  [types.SET_PAGER_STACK]: (state, newStack) => {
    let idx = ''
    const history = state.pagerStack.find((item, index) => {
      if (item.id == newStack.id) {
        idx = index
        return true
      }
      return false
    })
    if (!history) {
      state.pagerStack.push(newStack)
    } else {
      state.pagerStack[idx] = newStack
    }
  },
  [types.SET_USER_DETAIL_INFO]: (state, userInfo) => {
    state.userDetailInfo = userInfo
  }
}
