export default {
  namespaced: true,
  state: {
    websocket: '',
    socketNoticeType: 1, //0 直接弹窗提示新签到 1 右侧全局通知浮窗  2 右侧红色角标数字（会员签到页面）
    socketNoticeArray: [],
    socketMsgCount: '',
    socketModalShow: false
  },
  mutations: {
    SET_WEBSOCKET: (state, data) => {
      state.websocket = data
    },
    SET_SOCKET_NOTICE_ARRAY: (state, data) => {
      state.socketNoticeArray = data
    },
    SET_SOCKET_NOTICE_TYPE: (state, data) => {
      state.socketNoticeType = data
    },
    SET_SOCKET_MODAL_SHOW: (state, data) => {
      state.socketModalShow = data
    },
    SET_SOCKET_MSG_COUNT: (state, data) => {
      state.socketMsgCount = data
    }
  },
  actions: {}
}
