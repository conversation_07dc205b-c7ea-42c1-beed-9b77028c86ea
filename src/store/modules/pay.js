import service from 'src/service/index'
const state = {
  payTypes: [],
  // 是否支持储值卡混合支付
  isMoneyCardMix: false,
}
const getters = {
  getPayNameById: (state) => (id, notIncluedUnusable) => {
    let allPayTypes = state.payTypes
    if (notIncluedUnusable) {
      allPayTypes = allPayTypes.filter(item => item.usable === 1)
    } 
    for (const item of allPayTypes) {
      if (Number(id) === Number(item.pay_type_id)) {
        return item.pay_type_name
      }
    }
    return '暂无'
  }
}
const mutations = {
  SET_PAY_TYPES(state, payTypes) {
    state.payTypes = payTypes.list || []
    state.isMoneyCardMix = payTypes.stored_value_blend === 1
  }
}
const actions = {
  setPayTypesInit({ commit }) {
    return service
      .post('/Web/PaymentManage/getBusPayType', {}, { loading: false })
      .then(res => {
        if (res.data.errorcode === 0) {
          commit('SET_PAY_TYPES', res.data.data)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      })
  }
}

export default {
  namespaced: true,
  state,
  actions,
  mutations,
  getters
}
