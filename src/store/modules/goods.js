import * as Types from '../mutationTypes';
import { inventoryList, catList } from 'src/service/getData';

function dealChildren(data) {
  for (let item of data) {
    if (item.son && item.son.length) {
      item.children = item.son;
      item.title = item.name;
      dealChildren(item.son);
    }
  }
}

export default {
  namespaced: true,
  state: {
    list: [], // 购物车商品列表
    tableData: [], // 商品列表
    inventoryCode: '',
    inventoryList: [],
    catList: []
  },
  getters: {
    // 查找已存在于购物车中的商品, 同步数量与金额, 返回处理后的商品列表
    tableData({ tableData, list }) {
      const cartIds = list.map(item => item.id);
      const includesItems = tableData.filter(item => cartIds.includes(item.id));
      for (let item of includesItems) {
        const cartItem = list.find(goods => goods.id === item.id);
        item.count = cartItem.count;
        item.totalPrice = (cartItem.count * cartItem.commodity_price).toFixed(2);
      }
      return tableData;
    },
    // 购物车商品列表, 返回数量大于 0 的商品, 优先计算折扣价
    list: ({ list }) =>
      list.filter(item => item.count > 0).map(item => {
        return {
          ...item,
          discountPrice: typeof item.discountPrice === 'undefined' ? item.totalPrice : item.discountPrice
        };
      }),
    // 购买商品总数
    count: (state, { list }) => list.reduce((count, item) => count + Number(item.count), 0),
    // 总价
    total: (state, { list }) => list.reduce((total, item) => total + Number(item.discountPrice), 0).toFixed(2),
    catList: ({ catList }) => catList,
    inventoryName({ inventoryCode, inventoryList }) {
      const item = inventoryList.find(item => item.inventory_code === inventoryCode);
      return item && item.name;
    }
  },
  mutations: {
    // 仓库列表
    [Types.SET_INVENTORY_LIST](state, list) {
      state.inventoryList = list;
    },
    [Types.SET_CAT_LIST](state, list) {
      state.catList = list;
    },
    // 切换仓库
    [Types.SET_INVENTORY_CODE](state, id) {
      state.inventoryCode = id;
    },
    // 添加商品到购物车, 如果不存在则直接添加, 如果存在则更新购物车中对应的商品(数量与小计金额)
    [Types.ADD_GOODS_ITEM](state, addItem) {
      let item = state.list.find(item => item && item.id === addItem.id);
      let itemIndex = state.list.findIndex(item => item && item.id === addItem.id);
      if (!item) {
        state.list.push(addItem);
      } else {
        state.list.splice(itemIndex, 1, { ...item, ...addItem })
      }
    },
    // 删除购物车商品, 把对应商品数量归零即可
    [Types.DELETE_GOODS_ITEM](state, id) {
      state.list.find(item => item.id === id).count = 0;
    },
    // 清空购物车
    [Types.CLEAR_GOODS_CART](state) {
      state.list = [];
    },
    // 每次从服务器请求回商品列表, 进行数据处理
    [Types.SET_GOODS_LIST](state, data) {
      state.tableData = data.map(item => {
        return {
          ...item,
          stock: `${item.stock_balance}（${item.unit}）`,
          singlePrice: `￥ ${item.commodity_price}`,
          count: item.count || 0,
          totalPrice: item.totalPrice || '0.00'
        };
      });
    }
  },
  actions: {
    getInventoryList({ commit }, postData) {
      return inventoryList(postData).then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data;
          commit(Types.SET_INVENTORY_LIST, data);
        }
      }).catch(err => {
        throw new Error(err);
      });
    },
    getCatList({ commit }, postData) {
      return catList(postData).then(res => {
        if (res.data.errorcode === 0) {
          const data = [res.data.data];
          dealChildren(data);
          commit(Types.SET_CAT_LIST, data);
          return data;
        }
      });
    }
  }
};
