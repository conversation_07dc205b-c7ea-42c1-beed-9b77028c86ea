import { getCardGroupListByBusId, getSpaceTypes, getMerBusList } from 'src/service/getData'
import { treeToList } from 'utils'
export default {
  namespaced: true,
  state: {
    //1外观颜色   4我的页面装修 5底部导航
    theme1: {
      member_open: '1',
      background_color: '1',
      fashion_color: 'a1ea2b',
    },
    //2首页装修
    theme2: {
      list: [
        {
          name: '首页设置',
          temp_type: 0,
        },
        {
          name: '场馆介绍',
          temp_type: 1,
        },
        {
          name: '明星教练',
          temp_type: 2,
        },
        {
          name: '卡课推荐',
          temp_type: 3,
        },
        {
          name: '热门活动',
          temp_type: 4,
        },
        {
          name: '功能区',
          temp_type: 5,
        },
        {
          name: '团课推荐',
          temp_type: 6,
        },
      ],
      setting: {
        enter_voucher: '1',
        toggle_bus: '1',
      },
    },
    //3预约页面装修
    theme3: [
      {
        name: '团课',
        temp_type: 1,
      },
      {
        name: '私教课',
        temp_type: 2,
      },
      {
        name: '场地',
        temp_type: 3,
      },
    ],
    //4我的页面
    theme4: {
      background_type: '1',
      background_img: '',
      display: 1,
      my_interest: ['会员卡课', '入场凭证', '预约记录', '我的折扣券', '我的合同'],
      personal_data: ['运动记录', '历史体测'],
      service_item: ['我的发票', '留言咨询', '人脸识别', '请假记录', '租柜记录', '红包记录', '券码兑换', '帮助中心', '在线客服'],
    },
    //5底部导航
    theme5: [
      {
        name: '首页',
        temp_type: '1',
        display: 1,
      },
      {
        name: '预约',
        temp_type: '2',
        display: 1,
      },
      {
        name: '商城',
        temp_type: '3',
        display: 0,
      },
      {
        name: '我的',
        temp_type: '4',
        display: 1,
      },
    ],
    //6登录页
    theme6: {
      background_type: '1',
      background_img: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/login-bg.jpg',
    },
    //7教练称谓
    theme7: {
      appellation: '教练',
    },
    //卡课列表页面装修
    theme8: {
      setting: {
        module_style: '1',
      },
      list: [
        {
          name: '会员卡',
          temp_type: 1,
        },
        {
          name: '私教课',
          temp_type: 2,
        },
        {
          name: '泳教课',
          temp_type: 3,
        },
        {
          name: '套餐包',
          temp_type: 4,
        },
        {
          name: '微信月付',
          temp_type: 5,
        },
      ],
    },
    // 运营模式
    theme9: {
      mode: '0', // 1 综合体育场版 0 健身瑜伽版
    },
    // 积分商城
    theme10: {
      bus_id: '',
    },
    // 进店指引
    theme11: {
      image_url: '',
    },
    indexLeftSelected2: 0, // number | string
    indexLeftSelected3: 0,
    indexLeftSelected8: 0, // number | string
    otherSelectType: 6, // 其它设置的选中项save_type
    // 会员端的跳转链接列表
    links: {
      // encode_bus_id: {
      //   linkTree: [],
      //   linkList: [],
      // }
    },

    selectMerBusId: '', // 选择的商家/场馆id 如果是商家id则会有前缀m
    selectMerBusIdEncode: '',
    firstBusIdEncode: '', // 最早创建的门店加密id
    merBusList: [],
  },
  getters: {
    isMerchantMode(state) {
      // 当前是否综合体育场运营模式
      return state.theme9 && state.theme9.mode === '1'
    },
    isSelectMerchant(state) {
      // 当前选择的是否商家
      return String(state.selectMerBusId).startsWith('m')
    },
  },
  mutations: {
    UPDATE_DIY_THEME(state, data) {
      state['theme' + data.type] = data.value
    },
    UPDATE_DIY_THEME_BY_KEY(state, data) {
      state['theme' + data.type][data.key] = data.value
    },
    UPDATE_DIY_CUR_ITEM(state, data) {
      const typesWithKeyPath = [2, 8]
      const curIndex = data.curIndex || state['indexLeftSelected' + data.type]
      const theme = state['theme' + data.type]

      if (typesWithKeyPath.includes(data.type) || data.keyPath) {
        const listKey = data.keyPath || 'list'
        theme[listKey][curIndex] = {
          ...theme[listKey][curIndex],
          ...data.value,
        }
      } else {
        theme[curIndex] = {
          ...theme[curIndex],
          ...data.value,
        }
      }
    },
    UPDATE_INDEX_LEFT_SELECTED(state, data) {
      state['indexLeftSelected' + data.type] = data.value
    },
    UPDATE_OTHER_SELECT_TYPE(state, data) {
      state.otherSelectType = data
    },
    SET_LINKS(state, data) {
      state.links = {
        ...state.links,
        [data.busId]: {
          linkTree: data.list,
          linkList: treeToList(JSON.parse(JSON.stringify(data.list)), 'children'),
        },
      }
    },
    RESET_LINKS(state, data) {
      state.links = data || {}
    },
    SET_MER_BUS_LIST(state, data) {
      state.merBusList = data
    },
    SET_SELECT_MER_BUS_ID(state, data) {
      state.selectMerBusId = data.id
      state.selectMerBusIdEncode = data.encodeId
    },
    SET_IS_SELECT_MERCHANT(state, data) {
      state.isSelectMerchant = data
    },
    SET_FIRST_ENCODE_BUS_ID(state, data) {
      state.firstBusIdEncode = data
    },
  },
  actions: {
    // 首页装修-功能区 链接选项初始化
    async linkListInit({ commit, state }, busId) {
      commit('SET_LINKS', { busId, list: [] })
      // 为了兼容旧数据  value链接后面的参数不可随意更改 注意这里传递的value有&等特殊符号时必须要先encodeURIComponent
      // 以:分割的值将在会员端特殊处理 appid:path形式代表可以跳转到其它对应appid小程序下的路径
      const otherLinkArr = [
        {
          value: '/pages/coach/list',
          label: '教练介绍',
        },
        { value: '/pages/activity/index', label: '场内活动' },
        { value: '/pages/bus/detail', label: '场馆介绍' },
        { value: '/packageMy/agreement/guide', label: '进店指引' },
        { value: '/pages/class/class?type=1', label: '团课预约' },
        { value: '/pages/class/class?type=2', label: '私教预约' },
        { value: '/pages/class/class?type=4', label: '泳教预约' },
        { value: '/pages/stadium/aboutBall', label: '运动邀约' },
        {
          value: '/pages/my/index',
          label: '我的',
          children: [
            { value: '/pages/my/ticket', pValue: '/pages/my/index', label: '入场凭证', pLabel: '我的' },
            { value: '/pages/bus/feedback', pValue: '/pages/my/index', label: '留言咨询', pLabel: '我的' },
          ],
        },
        { value: '/pages/busSelect', label: '场馆切换' },
        { value: '/pages/my/ticket?from=index', label: '首页-入场凭证' },
        { value: '/packageMy/thirdParty/verify', label: '券码兑换' },
      ]
      // {
      //   value: 'wxd6af78b398c93f7b:/pages/stadium/stadiumCategory',
      //   label: '订场(运动生活管家)',
      // }
      let linkArr = [
        {
          value: '/pages/card/list?type=1',
          label: '私教购买',
          cardType: 1,
          children: [],
        },
        {
          value: '/pages/card/list',
          label: '购卡购课',
          cardType: 0,
          children: [],
        },
        {
          value: '/pages/card/list?type=2',
          label: '泳教购买',
          cardType: 2,
          children: [],
        },
        {
          value: '/pages/card/list?type=3',
          label: '套餐包',
          cardType: 3,
        },
        // {
        //   value: '/pages/payscore/list',
        //   label: '连续包月服务',
        // }
      ]

      getCardGroupListByBusId({ bus_id: busId }).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          data.forEach((item) => {
            const parentItem = linkArr[(+item.type || 2) - 1]
            const value = `/pages/card/list?type=${parentItem.cardType}&card_group_id=${item.id}`
            parentItem.children.push({
              value: encodeURIComponent(value),
              pValue: parentItem.value,
              label: item.title,
              pLabel: parentItem.label,
              decodeValue: value,
            })
          })
          linkArr = linkArr.concat(otherLinkArr)
          commit('SET_LINKS', { busId, list: [...linkArr, ...(state.links[busId] ? state.links[busId].linkTree : [])] })
        } else {
          linkArr = linkArr.concat(otherLinkArr)
          commit('SET_LINKS', { busId, list: [...linkArr, ...(state.links[busId] ? state.links[busId].linkTree : [])] })
        }
      })
      let spaceLinkArr = [
        {
          value: '/pages/class/class?type=3',
          label: '场地预订',
          children: [],
        },
        {
          value: '/pages/stadium/ticketList',
          label: '散场票',
          children: [],
        },
      ]

      const spaceData = await Promise.all(
        spaceLinkArr.map((item, index) => {
          // type 1 订场 2散场
          return getSpaceTypes({ type: index + 1, from: 2, bus_id: busId }).then((res) => {
            if (res.data.errorcode === 0) {
              const data = res.data.data
              data.forEach((resItem) => {
                const path =
                  index === 0
                    ? resItem.id === 0
                      ? `/pages/class/class?type=3`
                      : `/pages/stadium/choose?id=${resItem.id}`
                    : `/pages/stadium/ticketList?id=${resItem.id}`
                item.children.push({
                  value: encodeURIComponent(path),
                  pValue: item.value,
                  label: resItem.name,
                  pLabel: item.label,
                  decodeValue: path,
                })
              })
            }
            return item
          })
        })
      )

      const miniProgram = {
        value: 'miniProgram',
        label: '外部小程序',
        children: [
          {
            value: '',
            pValue: 'miniProgram',
            label: '小程序appID',
            pLabel: '外部小程序',
            example: '例如: wxd6af78b398c93f7b',
          },
          {
            value: '',
            pValue: 'miniProgram',
            label: '链接地址',
            pLabel: '外部小程序',
            example: '例如: /pages/index/index',
          },
        ],
      }

      const contact = {
        value: 'contact',
        label: '在线客服'
      }

      commit('SET_LINKS', {
        busId,
        list: [...(state.links[busId] ? state.links[busId].linkTree : []), ...spaceData, miniProgram, contact],
      })

      // 如果当前是商家,并且是综合馆模式
      // if (window.IS_BRAND_SITE && this.isMerchantMode) { 是否展示，放到弹窗选择框里面判断了
      commit('SET_LINKS', {
        busId,
        list: [
          ...(state.links[busId] ? state.links[busId].linkTree : []),
          {
            value: '/pages/index/bus?isShowMerchantMode=false',
            label: '链接其他门店',
            children: [],
          },
        ],
      })
      // }

      return Promise.resolve()
    },

    getMerchantsBusList({ commit }) {
      return getMerBusList().then((res) => {
        if (res.data.errorcode === 0) {
          if (res.data.data && res.data.data.list) {
            commit('SET_MER_BUS_LIST', res.data.data.list)
            // 默认选中最早创建的门店
            if (res.data.data.list.length) {
              commit('SET_FIRST_ENCODE_BUS_ID', res.data.data.list[0].encode_bus_id)
            }
          }
        }
        return res.data
      })
    },
  },
}
