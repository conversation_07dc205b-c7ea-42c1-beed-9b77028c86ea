import * as service from 'src/service/getData'
import * as types from '../mutationTypes'

export default {
  namespaced: true,
  state: {
    coachPosition: null,
    coachGroup: null,
    membershipPosition: null,
    membershipGroup: null
  },
  mutations: {
    [types.SET_COACH_POSITION](state, list) {
      state.coachPosition = list
    },
    [types.SET_COACH_GROUP](state, list) {
      state.coachGroup = list
    },
    [types.SET_MEMBERSHIP_POSITION](state, list) {
      state.membershipPosition = list
    },
    [types.SET_MEMBERSHIP_GROUP](state, list) {
      state.membershipGroup = list
    }
  },
  actions: {
    getCoachPosition({ commit }) {
      return service.coachPosition().then(res => {
        if (res.data.errorcode === 0) {
          commit(types.SET_COACH_POSITION, res.data.data)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getCoachGroup({ commit }) {
      return service.coachGroup().then(res => {
        if (res.data.errorcode === 0) {
          commit(types.SET_COACH_GROUP, res.data.data)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getMembershipGroup({ commit }) {
      return service.membershipGroup().then(res => {
        if (res.data.errorcode === 0) {
          commit(types.SET_MEMBERSHIP_GROUP, res.data.data[0])
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getMembershipPosition({ commit }) {
      return service.membershipPosition().then(res => {
        if (res.data.errorcode === 0) {
          commit(types.SET_MEMBERSHIP_POSITION, res.data.data.marketers_type_list)
        } else {
          this.$Message.error(res.data.errormsg)
        }
      }).catch(err => {
        console.error(err)
      })
    }
  }
}
