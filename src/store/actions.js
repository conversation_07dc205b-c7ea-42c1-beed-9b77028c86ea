import * as service from 'src/service/getData';
import * as types from './mutationTypes';

export default {
  // 获取场馆教练组成员列表
  getGroupDB({ commit }) {
    return service.coachGroupList().then(res => {
      if (res.status === 200) {
        if (res.data.errorcode == 0) {
          const data = res.data.data.list[0]
          commit(types.SET_COACH_GROUP_LIST, data)
          return data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      } else {
        console.error(res)
      }
    })
  },
  // 获取场馆 泳教 组成员列表
  getSwimGroupDB({ commit }) {
    return service.swimGroupList().then(res => {
      if (res.status === 200) {
        if (res.data.errorcode == 0) {
          const data = res.data.data.list[0]
          commit(types.SET_SWIM_GROUP_LIST, data)
          return data
        } else {
          this.$Message.error(res.data.errormsg)
        }
      } else {
        console.error(res)
      }
    })
  },
  // 上传表格导出日志
  exportLog({ commit }, config) {
    const postData = {
      url: config.url,
      where_str: config.data
    }
    return service.postExportLog(postData)
  },
  // 获取教练列表
  getCoachList({ commit }, belongBusId) {
    return service
      .getcoachsInfo(belongBusId)
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          commit(types.SET_COACH_LIST, data)
          return data
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(err => {
        console.error(err)
      })
  },
  // 获取私教教练列表
  getPtCoachList({ commit }, belongBusId) {
    return service
      .getPtCoachList(belongBusId)
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          const ptList = data.list.filter(item => item.is_swim != 1)
          const swimList = data.list.filter(item => item.is_swim == 1)
          commit(types.SET_PT_COACH_LIST, ptList)
          commit(types.SET_SWIM_COACH_LIST, swimList)
          return data.list
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(err => {
        console.error(err)
      })
  },
  // 获取会籍列表
  getMembershipList({ commit }, belongBusId) {
    return service
      .getsalesInfo(belongBusId)
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          commit(types.SET_MEMBERSHIP_LIST, data)
          return data
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(err => {
        console.error(err)
      })
  },
  // 获取账号信息: 版本, 短信条数等
  getAdminInfo({ commit }) {
    return service
      .adminInfo()
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          commit(types.SET_ADMIN_INFO, data)
          return data
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(err => {
        console.error(err)
      })
  },
  getReceiptAuth({ commit }) {
    return service
      .receiptAuthInfo()
      .then(res => {
        if (res.data.errorcode === 0) {
          const receauth = res.data.data.print_small_ticket == 1 ? true : false
          commit(types.SET_RECEIPT_AUTH, receauth)
          return receauth
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(err => {
        console.error(err)
      })
  },
  // 获取用户信息 场馆列表
  getBusInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      service
        .getBuseList()
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            commit('SET_MERCHANT_NAME', data.mer_name)
            commit('SET_MERCHANT_ID', data.m_id)
            commit('SET_BUS_NAME', data.bus_name)
            commit('SET_BUS_ID', data.bus_id)
            commit('SET_ENCODE_BUS_ID', data.encode_bus_id)
            commit('SET_GLOBAL_BELONG_BUS_ID', data.bus_id)
            commit('SET_BUS_LIST', data.bus_list)
            commit('SET_ADMIN_NAME', data.admin_name)
            commit('SET_ADMIN_ID', data.admin_id)
            commit('SET_ADMIN_TYPE', data.admin_type)
            commit('SET_SUPPORT_PAY', data.is_support_pay)
            commit('SET_BUS_LOGO', data.thumb)
            commit('SET_SHOW_CUSTOM_LOGO', data.is_show_custom_logo)
            commit('SET_GATEDLAUNCH', res.data.gatedlaunch === 'on')
            commit('SET_IS_QN_J', data.is_qn_j)
          } else {
            console.error(res.data.errormsg)
          }
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 获取当前商家下的场馆信息
  getMerchantsBusList({ commit, state }) {
    return new Promise((resolve, reject) => {
      service
        .getMerchBuseList()
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.list
            commit('SET_MERCHANTS_BUSLIST', data)
          } else {
            console.error(res.data.errormsg)
          }
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getAdminBusList({ commit, state }) {
    return new Promise((resolve, reject) => {
      service
        .getAdminBusList()
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            commit('SET_ADMIN_BUSLIST', data)
          } else {
            console.error(res.data.errormsg)
          }
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 获取购卡时候的卡信息列表
  getAddCardList({ commit }, {belongBusId, type} = {}) {
    return new Promise((resolve, reject) => {
      service
        .getCards(belongBusId || '', type || '')
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            commit('SET_ADDCARD_LIST', data)
          } else {
            console.error(res.data.errormsg)
          }
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 获取会员卡列表的卡信息列表
  getmemberCardList({ commit }, belongBusId) {
    return service
      .getMemberListCards(belongBusId)
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          commit('SET_MEMBERCARD_LIST', data)
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(error => {
        console.error(error)
      })
  },
  // 获取获客来源列表
  getSourceList({ commit }) {
    return service
      .getSources({ source_type: 1 })
      .then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          commit('SET_SOURCE_LIST', data)
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(error => {
        console.error(error)
      })
  },
  // 获取当前账号，购卡续卡能否编辑售价、赠送、有效天数等字段的权限
  getCanEditFields({ commit }) {
    return service
      .getCanEditFields()
      .then(res => {
        if (res.data.errorcode === 0) {
          commit('SET_CAN_EDIT_FIELDS', res.data.data.can_edit_field)
        } else {
          console.error(res.data.errormsg)
        }
      })
      .catch(error => {
        console.error(error)
      })
  }
}
