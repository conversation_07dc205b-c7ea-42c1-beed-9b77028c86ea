export default {
  busImages: state => state.busImages,
  busName: state => state.busName,
  gatedLaunch: state => state.gatedLaunch,
  busList: state => state.busList,
  adminBusList: state => state.adminBusList,
  merchantsBusList: state => state.merchantsBusList,
  financeCheckDays: state => state.financeCheckDays,
  adminName: state => state.adminName,
  adminId: state => state.adminId,
  adminType: state => state.adminType,
  globalBelongBusId: state => state.globalBelongBusId,
  receiptAuth: state => state.receiptAuth,
  supportPay: state => state.supportPay,
  busLogo: state => state.busLogo,
  showCustomLogo: state => state.showCustomLogo,
  addCardList: state => state.addCardList,
  memberCardList: state => state.memberCardList,
  loading: state => state.loading,
  timeOutAlert: state => state.timeOutAlert,
  busId: state => state.busId,
  coachGroupList: state => state.coachGroupList,
  swimGroupList: state => state.coachGroupList,
  errHandleList: state => state.errHandleList,
  canEditFields: state => state.canEditFields,
  is_qn_j: state => state.is_qn_j,
  pagerStack: state => {
    return state.pagerStack || []
  }
}
