import Vue from 'vue'
import Vuex from 'vuex'
import actions from './actions'
import mutations from './mutations'
import getters from './getters'

Vue.use(Vuex)
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})
const store = new Vuex.Store({
  state: {
    busId: '', // 当前场馆ID
    busIdEncode: '', // 当前场馆加密ID
    busName: '', // 当前场馆名称
    merchantId: '', // 当前场馆的商家ID
    merchantName: '', // 当前场馆的商家名称
    gatedLaunch: '', // 是否处于灰度环境
    busList: '', // 场馆列表
    adminBusList: '', // 账号权限下的场馆列表 当为admin账号的时候显示的是商家场馆
    merchantsBusList: '', // 当前商家下场馆列表
    adminName: '', // 管理员名称
    adminType: '', // 账号是否是商家管理员
    adminId: '', // 管理员id
    supportPay: '', // 是否支持在线支付
    busLogo: '', // 当前场馆logo
    showCustomLogo: '', // 是否在侧边栏展示自定义logo
    addCardList: '', // 购卡时的卡片信息
    memberCardList: '', // 会员列表的卡片信息
    cardCur: '', // 购卡时的卡片信息
    loading: false, // loading遮罩框（推荐点击按钮提交ajax时使用）
    timeOutAlert: false, // 当前路由下慢网络警告标识
    loadingText: '',
    adminInfo: {
      commodity_inventory: {} // 商品管理权限
    }, // 账号信息
    membershipList: [], // 会籍列表
    globalBelongBusId: '', //归属场馆id
    coachList: [], // 教练列表
    ptCoachList: [], // 私教教练列表package/getPackageList
    swimCoachList: [], // 泳教教练列表
    addFollowSuccess: false, // 会员详情添加跟进成功
    pagerStack: [],
    coachGroupList: null, // 教练组成员列表
    swimGroupList: null, // 泳教 组成员列表
    sourceList: [], // 获客来源
    financeCheckDays: '', // 对应角色在财务板块查询时，日期范围要根据角色权限中的设定来限制
    errHandleList: [],
    receiptAuth: false,
    cardSettingAuth: {
      expCard: false,
      multiCard: false,
      multiPtCard: false,
      singleCard: false,
      packageCard: false,
      swimCard: false
    },
    canEditFields: true, //当前账号，购卡续卡能否编辑售价、赠送、有效天数等字段的权限
    busImages: [],
    userDetailInfo: {},
    is_qn_j: 0, //当前账号是否开通了新版会员端
  },
  actions,
  mutations,
  getters,
  modules
})

export default store
