export default {
  methods: {
    nameRender(params, type, callBack) {
      let fingerIco = '';
      let rfidIco = ''
      if (params.row.is_bindSfinger == 1) {
        let postData = { user_id: params.row.id, type: params.row.coach_type ? 2 : 1 };
        fingerIco = (
          <span
            onClick={() => {
              this.handleConfirmBox('清除指静脉', '要清除该用户的指静脉信息吗？', postData);
            }}
            class="icon-s-finger"
            style="cursor:pointer"
            title="指静脉已录入"
          />
        );
      }
      if (params.row.rfid) {
        let postData = { id: params.row.id, type: 'rfid' };
        rfidIco = (
          <span
            onClick={() => {
              callBack(postData);
            }}
            class="icon-rfid"
            style="cursor:pointer"
            title="RFID已绑定"
          />
        );
      }
      return (
        <span>
          {params.row.name}
          {fingerIco}
          {rfidIco}
        </span>
      );
    },
    handleConfirmBox(title, content, postData) {
      this.$Modal.confirm({
        title: title,
        content: content,
        onOk: () => {
          this.clearBind(postData);
        },
        onCancel() {}
      });
    },
    // 解除释金石绑定
    clearBind(postData) {
      this.$service.post('/Web/Member/cancel_finger_relation', postData).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$Message.success(res.data.errormsg);
          } else {
            this.$Message.error(res.data.errormsg);
          }
        }
      });
    }
  }
};
