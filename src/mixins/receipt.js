import { mapGetters } from 'vuex';
import { EventBus } from 'components/EventBus.js';
export default {
  data() {
    return {
      showPrint: false,
      toPath: {}
    };
  },
  computed: {
    ...mapGetters(['receiptAuth'])
  },
  methods: {
    ptsignComplete(user_id, ticket_id, oper_type) {
      this.toPath = {
        path: '/signInfoPrint',
        query: {
          type: 3,
          user_id: user_id,
          small_ticket_id: ticket_id,
          oper_type: oper_type
        }
      };
      this.showPrint = true;
    },
    /* 押金 */
    depositFinish(deposit_type, user_id, charge_id, oper_type) {
      if (this.receiptAuth) {
        this.toPath = {
          path: '/depositPrint',
          query: {
            type: deposit_type,
            user_id: user_id,
            charge_id: charge_id,
            oper_type: oper_type
          }
        };
        this.showPrint = true;
      } else {
        this.$Message.success('操作成功');
      }
    },
    commodityComplete(user_id, order_sn, oper_type) {
      if (this.receiptAuth) {
        this.toPath = {
          path: '/commodityPrint',
          query: {
            type: 4,
            user_id: user_id,
            order_sn: order_sn,
            oper_type: oper_type
          }
        };
        this.showPrint = true;
      } else {
        this.$Message.success('操作成功');
        EventBus.$emit('clearCargo');
      }
    },
    /* 合同完成 */
    contractComplete(user_id, card_order_info_id, oper_type, teamclass_type, curriculum_id, card_name) {
      if (this.receiptAuth) {
        this.toPath = {
          path: '/contractPrint',
          query: {
            type: 0,
            user_id: user_id,
            card_order_info_id: card_order_info_id,
            oper_type: oper_type,
            teamclass_type,
            curriculum_id,
            card_name
          }
        };
        this.showPrint = true;
      } else {
        this.$Message.success('操作成功');
        if (oper_type != 'addlocker') {
          this.$router.back();
        }
      }
    }
  }
};
