const beta = 'wss://im-beta.rocketbird.cn/mainsite_light_control'
const sim = 'wss://im-sim.rocketbird.cn/mainsite_light_control'
const online = 'wss://light.rocketbird.cn/mainsite_light_control'
let PATH = online
const HEART_JUMPING_TIMER = 60000

export default {
  data() {
    return {
      socket: null,
      heartJumpingTimer: '',
    }
  },
  created() {
    if (window.location.hostname === 'fe.rocketbird.cn' || window.location.hostname === 'vip-test.rocketbird.cn') {
      PATH = beta
    } else if (window.location.hostname === 'vip-sim.rocketbird.cn') {
      PATH = sim
    }

    this.initWebSocket()
  },
  methods: {
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.error('您的浏览器不支持socket')
      } else {
        // 获取 socket
        if (this.socket) {
          if (this.socket.readyState === WebSocket.CONNECTING) {
            // console.log('socket正在连接')
          } else if (this.socket.readyState === WebSocket.OPEN) {
            // console.log('socket已经连接')
          } else {
            // console.log('socket重新连接')
            this.createWebSocket()
          }
        } else {
          this.createWebSocket()
        }
      }
    },
    createWebSocket() {
      // 实例化socket
      this.socket = new WebSocket(PATH)
      // 监听socket连接
      this.socket.onopen = () => {
        // 心跳检测
        this.heartJumpingTimer && clearInterval(this.heartJumpingTimer)
        this.heartJumpingTimer = setInterval(() => {
          this.socket.send('{"action":"pingMessage"}')
        }, HEART_JUMPING_TIMER)
      }
      // 监听socket错误信息
      this.socket.onerror = () => {
        console.log('🚀: lightbulb websocket disconnected!')
      }
      // 监听socket消息
      this.socket.onmessage = (event) => {
        const msg = event.data
        if (typeof msg === 'string') {
          const res = JSON.parse(msg)
          if (res.action === 'switchLight') {
            this.list.find(item => item.id == res.space_id).lightBulb = Number(res.light_status)
          }
        }
      }
      // 销毁监听
      this.socket.onclose = (event) => {
        // console.log("🚀 ~ ", event.code)
        if (event.code !== 4444) {
          this.resetWebSocket()
        }
      }
    },
    resetWebSocket() {
      this.heartJumpingTimer && clearInterval(this.heartJumpingTimer)
      setTimeout(() => {
        this.initWebSocket()
      }, HEART_JUMPING_TIMER)
    },
    closeWebSocket() {
      this.socket.close(4444)
    },
  },
}
