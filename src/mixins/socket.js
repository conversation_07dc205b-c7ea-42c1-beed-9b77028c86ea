import { getSocketUrl } from 'utils/config'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      socketCloseTimer: '',
      heartBeatTimer: ''
    }
  },
  watch: {
    adminId(val, oldVal) {
      if (val && oldVal !== val) {
        this.websocketAction()
      }
    }
  },
  computed: {
    ...mapState(['adminId']),
    ...mapState('websocket', ['websocket'])
  },
  methods: {
    //连接websocket
    websocketAction() {
      if (this.websocket && this.websocket.readyState === this.websocket.OPEN) {
        // this.websocketsend(agentData)
      } else if (
        this.websocket &&
        this.websocket.readyState === this.websocket.CONNECTING
      ) {
        // 若是正在开启状态
      } else {
        // 若未开启
        this.initWebSocket()
      }
    },
    initWebSocket() {
      const wsuri = getSocketUrl()
      this.$store.commit('websocket/SET_WEBSOCKET', new WebSocket(wsuri))
      //心跳验证 60秒一次
      this.heartBeatTimer = setInterval(this.websocketHeartbeat, 1000 * 60)
      this.websocket.onopen = this.websocketononopen
      // this.websocket.onmessage = this.websocketonmessage
      this.websocket.onerror = this.websocketonerror
      this.websocket.onclose = this.websocketonclose
    },
    websocketononopen() {
      //参数
      this.websocketsend(`{"action":"init","admin_id":${this.adminId}}`)
      if (this.socketCloseTimer) {
        clearInterval(this.socketCloseTimer)
        this.socketCloseTimer = ''
      }
    },
    //websocket心跳验证
    websocketHeartbeat() {
      this.websocketsend('{"action":"check_heart","msg":"@heart|end|"}')
    },

    websocketsend(agentData) {
      if(this.websocket && this.websocket.readyState === this.websocket.OPEN) {
        this.websocket.send(agentData)
      }
    },
    closeWebSocket() {
      this.websocket && this.websocket.close()
    },
    websocketonclose(e) {
      this.heartBeatTimer && clearInterval(this.heartBeatTimer)
      if (!e.wasClean && !this.socketCloseTimer) {
        this.socketCloseTimer = setInterval(this.websocketAction, 1000 * 30)
      }
    },
    websocketonerror() {
      console.log('websocketonerror')
    }
  }
}
