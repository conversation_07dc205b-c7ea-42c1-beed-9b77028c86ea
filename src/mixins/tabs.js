export default {
  data() {
    return {
      activeIndex: '0',
      activated: ['0']
    };
  },

  created() {
    const { activeIndex } = this.$route.params;
    // 处理从其他位置跳转的情况
    const { active } = this.$route.query;
    if (activeIndex !== undefined || active !== undefined) {
      this.activeIndex = activeIndex || active
      this.activated = [this.activeIndex]
    }
  },

  methods: {
    clickTabs(index, classIndex) {
      this.activeIndex = typeof index === 'number' ? index + '' : index;

      const active = document.querySelector('.ivu-tabs-ink-bar');
      const className = `ivu-tabs-ink-bar ivu-tabs-ink-bar-animated tabs-active-${classIndex !== undefined ? classIndex: index}`
      active.setAttribute('class', className);

      const { activeIndex, activated } = this;
      !activated.includes(activeIndex) && activated.push(activeIndex);
    }
  }
};
