import {formatDate} from 'utils'
import DatePickerWithButton from 'components/picker/datePickerWithButton.vue'

let weekNow = new Date().getDay();
weekNow = weekNow === 0 ? 6 : weekNow - 1;

export default {
  components: {
    DatePickerWithButton
  },
  data() {
    return {
      dateRange: [formatDate(new Date(Date.now() - weekNow * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      selectedGroup: '',
      groupId: '',
      totalStat: null,
      // 组别和教练下拉选矿数据
      groupList: null,
    }
  },
  async created() {
    await this.getGroupList();
    this.selectedGroup = 'group' + this.groupList[0].group_id;
  },
  watch: {
    dateRange(val, oldVal) {
      if (val[0] === oldVal[0] && val[1] === oldVal[1]) {
        return
      }
      if(this.pagetitle=='briefstat'){
        this.currentPage = 1;
        this.getbriefData();
      }else{
        this.getData();
      }
    }
  },
  methods: {
    dateRangeChanged(val) {
      this.dateRange = val
    },
    getData() {
      this.getTotalStat()
      this.getDealTypeStat()
      this.getMarketerStat()
      this.getDealType()
      this.getTotalStat()
    },
    getbriefData() {
      this.getbriefTotal();
    },
    getbriefTotal() {
      let url = this.briefUrl;
      let postData = this.postData();
      this.$service.post(url, postData).then(res => {
        if (res.data.errorcode === 0) {
           this.totalStat = res.data.data.statistics;
           this.databriefs = res.data.data.list;
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getGroupList() {
      let url = this.groupUrl;
      return this.$service.post(url).then(res => {
        if (res.data.errorcode === 0) {
          this.groupList = res.data.data.list;
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getTotalStat() {
      let url = this.totalStatUrl;
      let postData = this.postData();
      this.$service.post(url, postData).then(res => {
        if (res.data.errorcode === 0) {
          this.totalStat = res.data.data.info
        }
      }).catch(err => {
        console.error(err)
      })
    },
    getDealTypeStat() {
      let url = this.dealTypeUrl;
      let postData = this.postData();
      this.$service.post(url, postData).then(res => {
        if (res.data.errorcode === 0) {
          let list = res.data.data.list;
          this.listData.total = list.total;
          this.listData.source_list = list.source_list;
          this.listData.card_type_list = list.card_type_list;
        }
      }).catch(err => {
        console.error(err)
      })
    },
  }
}
