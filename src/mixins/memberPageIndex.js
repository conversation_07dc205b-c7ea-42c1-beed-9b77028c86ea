import { mapState, mapGetters } from 'vuex'


export default {
  data() {
    return {
      initList: []
    }
  },

  computed: {
    ...mapState(['merchantId', 'busId']),
    ...mapState('diy', ['selectMerBusId', 'merBusList']),
    ...mapGetters('diy', ['isMerchantMode', 'isSelectMerchant']),
  },

  methods: {
    arrIndexOf(arr, value) {
      if (!Array.isArray(arr)) {
        return false
      }
      if (arr.indexOf(value) < 0) {
        return false
      } else {
        return true
      }
    },
    getInitList(get_type, encodeBusId) {
      const item = this.merBusList.find(v => v.encode_bus_id === encodeBusId)
      if (item) {
        // '/Web/NewMemberTemplateSetting/get_home_recommend_default'
        const params = {
          get_type,
          // [get_type == 9 || this.isSelectMerchant ? 'm_id' : 'bus_id']: get_type == 9 ? this.merchantId : this.selectMerBusId.replace('m', '')
          bus_id: item.bus_id
        }
        this.$service
          .post('Merchant/NewMemberApplet/getHomeRecommendDefault', params)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.initList = res.data.data
            } else {
              this.$Message.error(res.data.errormsg)
            }
          })
      }
    }
  }
}
