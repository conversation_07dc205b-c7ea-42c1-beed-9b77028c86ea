export default {
  data() {
    return {
      selectionItemKey: 'id',
      selection: []
    };
  },
  computed: {
    selectionId() {
      const { selectionItemKey: key } = this;
      return this.selection.map(item => item[key]);
    }
  },
  methods: {
    onSelectionChange(selection) {
      if (selection.length === 0) {
        this.tableData.forEach(item => this.onSelectCancel(selection, item));
      }
    },
    onSelectCancel(selection, row) {
      const { selectionItemKey: key } = this;
      const index = this.selection.findIndex(item => item[key] === row[key]);
      if (index !== -1) {
        this.selection.splice(index, 1);
      }
    },
    onSelect(selection, row) {
      if (!this.selectionId.includes(row[this.selectionItemKey])) {
        this.selection.push(row);
      }
    },
    onSelectAll(selection) {
      selection.forEach(item => this.onSelect(selection, item));
    },
  },
};
