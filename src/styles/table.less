@border: 1px solid #e0e3e9;
@colorSuccess: #5db75d;
@colorDanger: #d9544f;

.flex-center {
  display: flex;
  align-items: center;
}

.ivu-btn-text:focus {
  box-shadow: none;
}

.ivu-input {
  border: @border;
}

.ivu-radio-group-button .ivu-radio-wrapper {
  border-color: #e0e3e9;
}

.ivu-page-item-active {
  border-color:@colorSuccess;
  color: @colorSuccess;
  a {
    color: @colorSuccess;
  }
}
.table-wrap {
  background-color: #fff;
  border: @border;
  width: 100%;

  .ivu-btn-text {
    min-width: auto;
    padding: 0;
    line-height: 32px;
  }

  .button-text-red {
    color: @colorDanger;
    margin-left: 10px;
    &[disabled] {
      color: #bbbec4;
    }
  }

  .color-success {
    color: @colorSuccess;
  }

  .color-danger {
    color: @colorDanger;
  }

  > header {
    .flex-center;
    flex-wrap: wrap;
    min-height: 90px;
    padding: 10px 35px 0;
    border-bottom: @border;
    width: 100%;

    > * {
      min-width: 120px;
      max-width: 270px;
      margin-right: 15px;
      margin-bottom: 10px;
    }
    > .ivu-select {
      max-width: 160px;
    }
    > .ivu-date-picker {
      width: 200px;
    }
    button {
      min-width: 80px;
    }
  }

  > footer {
    .flex-center;
    height: 80px;
    padding: 0 35px;

    .ivu-page {
      margin-left: auto;
    }

    .ivu-dropdown .ivu-btn {
      background: #fff;
      font-size: 13px;
      min-width: 100px;
      text-align: center;
      color: #999;
    }

    .ivu-btn-success {
      background-color: #5cb85c;
      border-color: #4cae4c;
    }

  }

  .avatar-wrap {
    &:hover {
      position: relative;
      z-index: 99;
    }
    .ivu-table-cell {
      overflow: visible;
    }
  }
  .avatar {
    display: block;
    width: 30px;
    height: 30px;
    margin: 0 auto;
  }
}

.disabled.ivu-table-row td {
  color: #ccc !important;
}

.ivu-table-wrapper {
  border: 0;
}

//当头像需要放大时
.avatar-zoom {
  overflow: visible;
  z-index: 1;
  .ivu-table {
    overflow: visible;
    &::before,
    &::after {
      z-index: 0;
    }
  }
  .ivu-table .ivu-table-body {
    overflow-x: visible !important;
    overflow-y: visible !important;
  }
  //当头像需要放大时
  .avatar {
    z-index: 9;
    transition: all 0.6s;
    transform-origin: 0 0;
    &:hover {
      transform: scale(5);
    }
  }
}

.ivu-table {
  line-height: 1.5;
  font-size: 14px;
  .ivu-table-cell {
    padding-left: 0;
    padding-right: 0;
  }
  .ivu-table th,
  .ivu-table td {
    // padding: 8px 0;
  }
  &::before {
    height: 0;
  }
  &::after {
    width: 0;
  }
  table {
    width: 100%;
  }
  tr {
    height: 40px;
  }
  th {
    height: auto;
    text-align: center;
    padding: 8px;
    background-color: #f7f7f7;
  }
  td {
    height: auto;
    padding: 8px;
    text-align: center;
    // border: 0;
  }
  td.ivu-table-column-left {
    text-align: left;
  }

  td.ivu-table-column-right {
    text-align: right;
  }
}

.ivu-tabs-bar {
  margin-bottom: 0;
}
.customized-tabs .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
  border: 1px solid #ddd !important;
  border-bottom-color: transparent !important;
}
.customized-tabs .ivu-tabs-nav {
  display: flex;
  width: 100%;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  border-left: 1px solid #ddd;
  .ivu-tabs-tab {
    width: 100%;
    text-align: center;
    color: inherit;
    margin-right: 0;
    border: 1px solid #ddd;
    border-left: 0;
    background-color: #f7f7f7;
    &:hover {
      color: inherit;
    }
  }
  .ivu-tabs-tab-active {
    color: inherit;
    font-weight: bold;
    background-color: #fff;
    border-bottom-color: transparent !important;
  }

  .ivu-tabs-ink-bar {
    top: 0;
    bottom: auto;
    background-color: #6cbf6c;
  }
}

.ivu-tabs .ivu-table {
  &::before {
    height: 0;
  }
  &::after {
    width: 0;
  }
  border-bottom: 1px solid #ddd;
  .ivu-table-body {
    overflow-x: hidden;
  }
  .ivu-table-overflowX{
    overflow-x:scroll;
  }
  border-top: 0;
  th {
    background-color: #fff;
    font-weight: normal;
  }
}
//通用宽度
.w100 {
  width: 100px !important;
}
.w120 {
  width: 120px !important;
}
.w150 {
  width: 150px !important;
}
.w200 {
  width: 200px !important;
}
.w250 {
  width: 250px !important;
}
.mr5 {
  margin-right: 5px !important;
}
//带tab的table样式
.ivu-tabs .ivu-tabs-nav {
  .ivu-tabs-tab {
    padding: 8px 0px;
  }
  .tabs-active-1 {
    transform: translate3d(100%, 0, 0) !important;
  }
  .tabs-active-2 {
    transform: translate3d(200%, 0, 0) !important;
  }
  .tabs-active-3 {
    transform: translate3d(300%, 0, 0) !important;
  }
  .tabs-active-4 {
    transform: translate3d(400%, 0, 0) !important;
  }
  .tabs-active-5 {
    transform: translate3d(500%, 0, 0) !important;
  }
  .tabs-active-6 {
    transform: translate3d(600%, 0, 0) !important;
  }
  .tabs-active-7 {
    transform: translate3d(700%, 0, 0) !important;
  }
  .tabs-active-8 {
    transform: translate3d(800%, 0, 0) !important;
  }
  .tabs-active-9 {
    transform: translate3d(900%, 0, 0) !important;
  }
}
// .tab-table-wrap {
//   border-left: 1px solid #ddd;
//   border-right: 1px solid #ddd;
//   border-bottom: 1px solid #ddd;
// }
.tab-table-wrap header>* {
    min-width: 120px;
    max-width: 300px;
    margin-right: 15px;
}
.tab-table-wrap header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 35px;
  border-bottom: 1px solid #e9eaec;
  background-color: #fff;
  width: 100%;
  height: 75px;
}
.tab-table-wrap .ivu-table th, .tab-table-wrap .ivu-table td {
  border-bottom: none;
  background-color: #fff;
}
.tab-table-wrap tr.ivu-table-row-hover td{
  background-color: #ebf7ff;
}
.tab-table-wrap .ivu-table th {
  border-bottom: 1px solid #e9eaec;
}
.tab-table-wrap .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.tab-table-wrap .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background-color: #fff;
}

.tab-table-wrap footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  padding: 0 35px;
  background-color: #fff;
}

.tab-table-wrap .ivu-tabs {
  min-height: 400px;
}
.table-wrap {
  .input-before {

    .ivu-select-selection,
    .before-label {
      border-radius: 4px 0 0 4px;
    }

    .before-label {
      display: inline-block;
      height: 32px;
      line-height: 30px;
      border: 1px solid #dddee1;
      vertical-align: middle;
      padding: 0 8px;
      border-right: 0 none;
    }
  }

  .input-after {

    .ivu-select-selection:first-child,
    .min-input:first-child,
    .min-input:first-child .ivu-input-number,
    .ivu-date-picker-editor .ivu-input-number,
    .ivu-date-picker-editor .ivu-input {
      border-radius: 0 4px 4px 0;
    }
  }

  .input-group {
    display: table;
    border-collapse: separate;
    position: relative;
    font-size: 12px;
    vertical-align: middle;
    max-width: 350px;

    .ivu-select-item {
      padding: 7px;
    }

    .input-before,
    .input-after,
    .min-input {
      display: inline-block;
      vertical-align: middle;
    }

    .min-wrap {
      display: table;
      border-collapse: separate;
      margin-right: 3px;
      >span {
        vertical-align: middle;
        line-height: 32px;
        padding: 0 5px;
      }
    }

    .min-input {
      width: 60px;
    }

    .ivu-date-picker {
      width: 200px;
    }
  }
}