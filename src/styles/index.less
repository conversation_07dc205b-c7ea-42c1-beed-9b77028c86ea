//定制iview样式
@import '~iview/src/styles/index.less';
@boxBorder: 1px solid #e0e3e9;
//scroll
::-webkit-scrollbar-track {
  border-radius: 3px;
  background-color: #fff;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #ccc;
}
//防止iview元素在body内时的抖动
body {
  overflow-y: hidden;
}

// 统计图合并表格中表格样式
.merge-table-box .ivu-table-cell,
.merge-table-box .ivu-table-row td {
  padding: 0;
}

.merge-table-box .ivu-table th {
  background-color: #fff;
  padding: 0;
}

.merge-table-box .ivu-table-fixed {
  box-shadow: 2px 3px 6px -2px rgba(0, 0, 0, 0.2);
}

// Base
@font-family: 'Microsoft YaHei', '微软雅黑', Helvetica, sans-serif, Arial;
// Button
@btn-border-radius: 0;
@btn-border-radius-small: 0;
@btn-confirm-color: #19be6b;

li,
ol {
  list-style: none;
}
// Menu
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
  color: #fff;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu):hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
  background: #0f1922 !important;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu):hover {
  color: rgb(31, 195, 236);
}

.ivu-menu-dark,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
  background: #1c2b36;
  color: #fff;
}

.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  background: #ff0000;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  color: rgb(31, 195, 236);
  border-right: 2px solid rgb(31, 195, 236);
  background: #0f1922 !important;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover,
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  background: #16222b;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  color: rgb(31, 195, 236);
}

.ivu-menu-item a {
  color: #fff;
}

.ivu-menu-submenu:hover .ivu-menu-submenu-title-icon {
  color: rgb(31, 195, 236);
}

.ivu-menu-vertical .ivu-menu-item,
.ivu-menu-vertical .ivu-menu-submenu-title {
  padding-top: 10px;
  padding-bottom: 10px;
}

//head search
@head-search-color: #d9544f;
.head-search .ivu-input-group-prepend,
.head-search .ivu-input-group-append,
.head-search .ivu-input {
  border-color: @head-search-color;
  background: #fff;
  border-radius: 0;
}

.head-search .ivu-input {
  border-left: 0;
  border-right: 0;
  box-shadow: none;
}

.head-search .ivu-select,
.head-search .ivu-select-arrow {
  color: @head-search-color;
}

.head-search .ivu-select-dropdown {
  box-shadow: none;
  border: 1px solid @head-search-color;
  border-top: 0;
  box-sizing: border-box;
}

.head-search .ivu-dropdown-menu {
  min-width: auto;
}

.head-search .ivu-input,
.head-search .ivu-select-selection,
.head-search .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.head-search .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  line-height: 38px;
  height: 39px;
  font-size: 14px;
}

.head-search .ivu-dropdown-item:hover {
  background-color: @head-search-color;
  color: #fff;
}
.brand-box .head-search .ivu-input {
  border-left: 1px solid #ff0000;
}
.wall .ivu-select-small .ivu-select-selection {
  border: none;
  box-shadow: none;
}

.head-search .ivu-select-dropdown {
  margin-top: 12px;
  left: 0 !important;
  border-radius: 0;
}

.head-search .ivu-select-item:hover,
.head-search .ivu-select-item-selected,
.head-search .ivu-select-item-selected:hover,
.head-search .ivu-input-group-append {
  background-color: @head-search-color;
  color: #fff;
}
.head-search .ivu-select-item-selected::after {
  color: #fff;
}
.head-search .ivu-input-group-append {
  font-size: 16px;
  cursor: pointer;
}

.phone-wrap .ivu-poptip-body {
  padding: 0;
}

.phone-wrap .ivu-poptip-popper[x-placement^='bottom'] .ivu-poptip-arrow,
.phone-wrap .ivu-poptip-popper[x-placement^='bottom'] .ivu-poptip-arrow:after {
  border-bottom-color: #1c2b36;
}

//表单
@form-item-font-size: 14px;
@input-color: #666;
@input-placeholder-color: #999;
@form-item-label-color: #333;
@form-input-width: 53%;
@form-input-maxwidth: 550px;
@btn-font-size: 14px;
@btn-border-radius: 4px;
.modal-form {
  .ivu-input {
    font-size: @form-item-font-size;
  }
  .ivu-date-picker {
    width: 100%;
  }
  .ivu-form-item {
    margin-bottom: 18px;
  }
  .ivu-form-item-label {
    font-size: @form-item-font-size;
  }
  .ivu-select-single .ivu-select-input {
    font-size: @form-item-font-size;
  }
  .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
  .ivu-select-input,
  .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    font-size: @form-item-font-size;
  }
  .ivu-select-item {
    font-size: @form-item-font-size !important;
  }
  textarea {
    width: 100%;
    max-width: 100%;
    resize: none;
    line-height: 1.2;
    font-size: 14px;
    padding: 10px;
    border: 1px solid #dddee1;
    color: #666;
  }
}

.ivu-switch-checked {
  border-color: #5fb75d;
  background-color: #5fb75d;
}

.ivu-switch-loading {
    opacity: 1 !important;
}

.form-box {
  width: 100%;
  background: #ececec;
  border: 1px solid #e0e3e9;
  .ivu-form-item-label {
    font-size: @form-item-font-size;
    color: @form-item-label-color;
    padding-right: 28px;
  }
  .ivu-input-wrapper,
  .ivu-select,
  .ivu-checkbox-group,
  .form-other {
    width: @form-input-width;
    max-width: @form-input-maxwidth;
  }
  .ivu-input {
    font-size: @form-item-font-size;
  }
  .ivu-input-number-input,
  .ivu-input-number-input-wrap,
  .ivu-input-number {
    width: 100%;
    max-width: 550px;
  }
  .flex-time{
    .ivu-input-wrapper,
    .ivu-select,
    .ivu-checkbox-group,
    .form-other{
      width: auto;
    }
  }
  .bus-card {
    .ivu-input-number-input,
    .ivu-input-number-input-wrap,
    .ivu-input-number {
      width: 60%;
    }
  }
  .ivu-select-input {
    font-size: @form-item-font-size;
    color: @input-placeholder-color;
  }
  .ivu-form-item-content {
    font-size: @form-item-font-size;
    color: @form-item-label-color;
    line-height: 34px;
  }
  .ivu-select-selection {
    .ivu-select-placeholder {
      font-size: @form-item-font-size;
      color: @input-placeholder-color;
    }
    .ivu-select-selected-value {
      font-size: @form-item-font-size;
      color: @input-color;
    }
  }
  .ivu-select-dropdown {
    .ivu-select-item {
      font-size: @form-item-font-size !important;
      color: @input-placeholder-color;
    }
    .ivu-select-item-selected {
      background: #2d8cf0;
      color: #fff;
    }
    .ivu-select-item-selected::after {
      color: #fff;
    }
    .ivu-select-item-disabled{
      color:#bbbec4;
    }
  }
  .ivu-select-multiple {
    .ivu-select-item-selected,.ivu-select-item-selected::after {
      color: #fff;
    }
  }
  .ivu-date-picker {
    width: 100%;
  }
}

.buttons {
  width: @form-input-width;
  max-width: @form-input-maxwidth;
  margin-top: 34px;
  .ivu-btn {
    min-width: 68px;
    height: 32px;
    border: 1px solid @btn-confirm-color;
  }
  .ivu-btn:hover {
    opacity: 0.8;
    border-color: @btn-confirm-color;
  }
  .ivu-btn-primary {
    background: @btn-confirm-color;
    border-color: @btn-confirm-color;
    margin-left: 22%;
  }
  .ivu-btn-success {
    background: @btn-confirm-color;
    color: #fff;
  }
  .ivu-btn-default {
    background: #fff;
    color: @btn-confirm-color;
    margin-left: 71px;
  }
}

.ivu-btn {
  min-width: 80px;
  min-height: 32px;
  font-size: 14px;
  padding: 0 10px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.ivu-btn-small {
  min-width: auto;
  min-height: auto;
  padding: 0px 7px;
  border-radius: 4px;
}

.ivu-select-item {
  white-space: normal;
}

.ivu-btn-text {
  color: dodgerblue;
}
.button-text-red {
  color: red;
  margin-left: 10px;
  &[disabled] {
    color: #bbbec4;
  }
  &:hover {
    color: red;
  }
}
.text-red {
  color: red;
}

.modal-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 30px;
  button {
    margin-right: 30px;
    &:last-child {
      margin-right: 0;
    }
  }
  .ivu-btn-default {
    &:hover {
      color: @btn-confirm-color;
      border-color: @btn-confirm-color;
    }
  }
}

.form-box-title {
  width: 100%;
  background: #f7f7f7;
  height: 37px;
  padding: 0 20px;
  overflow: hidden;
  border-bottom: 1px solid #e0e3e9;
}

.form-box-title h2 {
  font-size: 14px;
  line-height: 37px;
  font-weight: bold;
}

.form-box-con {
  width: 100%;
  // border: 1px solid #dcdcdc;
  background: #fff;
  padding: 17px 44px 70px 44px;
}

//Modal
.ivu-modal {
  margin-bottom: 30px;
}
.ivu-modal-header {
  border-bottom: 0;
  p,
  .ivu-modal-header-inner {
    font-size: 18px;
  }
}

.ivu-modal-footer {
  border-top: 0;
}

.ivu-modal-body {
  padding: 20px;
}

//信息弹窗
.ivu-message {
  padding-top: 200px;
}

.ivu-message-notice-content {
  max-width: 250px;
  span {
    line-height: 20px;
    font-size: 16px;
  }
  .ivu-icon {
    font-size: 18px;
  }
}

// Slider
.ivu-slider-button-wrap {
  top: -7px;
}

.clearfix:after {
  clear: both;
  content: '.';
  display: block;
  height: 0;
  overflow: hidden;
  font-size: 0;
}

.clearfix {
  zoom: 1;
}

// 链接颜色
.link {
  color: #333;
  cursor: pointer;
  &:hover {
    color: #d8321f;
  }
}

.container {
  background-color: #fff;
  border: @boxBorder;
  .form {
    padding-top: 20px;
    padding-bottom: 30px;
    padding-right: calc(~'100% - 800px');
    .ivu-input-number {
      width: 100%;
      font-size: @form-item-font-size;
    }
    .ivu-radio-wrapper {
      font-size: @form-item-font-size;
    }
    .ivu-date-picker {
      width: 100%;
    }
    .ivu-form-item-label,
    .ivu-input,
    .ive-select-input {
      font-size: @form-item-font-size;
      padding-right: 28px;
    }
    .ivu-form-item-content {
      font-size: @form-item-font-size;
    }
    .ivu-btn-default {
      // color: @btn-confirm-color;
      // border-color: @btn-confirm-color;
      &:hover {
        color: #fff;
        background-color: @btn-confirm-color;
      }
    }
    .form-bottom-buttons {
      padding: 50px 50px 100px;
      max-width: 100%;
      display: flex;
      button {
        min-width: 80px;
        margin-right: 72px;
      }
    }
  }
  > header {
    background-color: #f7f7f7;
    border-bottom: @boxBorder;
    height: 40px;
    h3 {
      font-size: 14px;
      line-height: 40px;
      padding-left: 14px;
    }
  }
}

.ivu-message-notice {
  display: flex;
  justify-content: center;
}
//提示
.ivu-tooltip-inner {
  word-break: break-all;
  max-width: 500px;
}

//Icon
.icon {
  display: inline-block;
  margin-left: 5px;
  vertical-align: middle;
}
.icon-wx {
  .icon;
  width: 18px;
  height: 14px;
  background: url(../assets/img/wx.png) 0px 1px no-repeat;
  line-height: 14px;
  background-size: cover;
}
.icon-faceid, .icon-shufa-face,.icon-yj-face, .icon-quick-face {
  .icon;
  width: 14px;
  height: 14px;
  background: url(../assets/img/user-smile-line-1.svg) 0 0 no-repeat;
  line-height: 14px;
  background-size: cover;
}
.icon-shufa-face {
  background: url(../assets/img/user-smile-line-2.svg) 0 0 no-repeat;
}
.icon-yj-face {
  background: url(../assets/img/user-smile-line-3.svg) 0 0 no-repeat;
}
.icon-quick-face {
  background: url(../assets/img/user-smile-line-4.svg) 0 0 no-repeat;
}
.icon-faceimg {
  .icon;
  width: 16px;
  height: 16px;
  background: url(../assets/img/avatar-modal.png) 0 0 no-repeat;
  line-height: 14px;
  background-size: cover;
}
.icon-finger {
  .icon;
  width: 16px;
  height: 15px;
  background: url(../assets/img/finger.png) 0px 0px no-repeat;
  line-height: 15px;
  background-size: cover;
}
.icon-s-finger {
  .icon;
  width: 14px;
  height: 14px;
  background: url(../assets/img/s_finger.png) 0px 0px no-repeat;
  line-height: 14px;
  background-size: cover;
}
.icon-rfid,.icon-yj-rfid {
  .icon;
  width: 14px;
  height: 14px;
  background: url(../assets/img/rfid.png) 0px 0px no-repeat;
  line-height: 14px;
  background-size: cover;
}
.icon-yj-rfid {
  background: url(../assets/img/yj-rfid.png) 0px 0px no-repeat;
}
//model-check-list
.model-check-list {
  width: 100%;
  max-height: 500px;
  overflow-y: scroll;
  .red {
    color: #e67371 !important;
  }
  .ivu-checkbox-group-item,
  .ivu-radio-group-item {
    display: flex;
    align-items: center;
    border-bottom: 1px dashed #d3d3d3;
    padding: 15px 0;
    margin-right: 0;
    &:last-child {
      border-bottom: none;
    }
    .top-con {
      display: flex;
      justify-content: space-between;
      .item {
        flex: 1;
      }
    }
  }
  .check-right {
    width: 100%;
    line-height: 30px;
    margin-left: 15px;
  }
}
.bg-phone-repeat {
  width: 30px;
  height: 30px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAATQAAAAaCAYAAAFOV5D7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NzQ3QzhEM0NGN0ZCMTFFNkE3NzU5MTc3M0U3NTM5NjMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NzQ3QzhEM0RGN0ZCMTFFNkE3NzU5MTc3M0U3NTM5NjMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo3NDYyRUE2NUY3RkIxMUU2QTc3NTkxNzczRTc1Mzk2MyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo3NDYyRUE2NkY3RkIxMUU2QTc3NTkxNzczRTc1Mzk2MyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PufTTJYAAAunSURBVHjaYvz//z8DPUHszthtQMpmsftiPhD/7Nmzn4DUf2NjY350tUzxu+L7gZhuLgQ6ygtIRSIJgRzJj+R4eyC+A3YcELOBGMgOBEpyA/E8NB//B2EkfhMQo4cKCHsg8R2BOI1E968BYmWQXUwL3RZmY1HwBYgTCRhSB8TCWMSPI7FPAvFedE8CqS1IQv+hGAaEYAyAAGKkZ5oDOgycvoBRC49GYJoDp0FgmuNDURy3M+4/yHEDhc+cOQPC3jB+zI6YuyCaCZbO0NLaPOS0hp7OkNJVLhL/IRBLoalJQ0+PRAAlkF0sMB4wvTEiScLSWBIeA/KAeBJybgfiZ2hqZmNJY6BcyoiWzsDJClktQAAx0rtcGygAKjWAAbIDXRyYxpG5HsC0voOQWSyg8hZIbwXiv0gxux+LpdhiAqsaIJABRQhQ7RMsakBUGxCDyl0DoBps5iQAqVogPgPEEUA1/3GY8xaI7YD4KjZz0AAzEWHLjMWe10BKBCXQgFgViHcjCwIDUgIYcC/R9DcCcT0xgQcET7Alayi4AAosIK4CYjEgfoVFDciuR0C8AK0YRgagEtEBiGcAsS2OSHRG4hpAEwc6IEYNCgAIoGGbPYEBtglICQLxfSAOAGIjYETfQcuaGGqA2fMOwew5jIsxX+TcAAzESdAKAkUNWm7ApoYBWxvyPz3bkEMwxd4D4rvYUpoTMOAcoewPwPLsPJrGS0BKl0AlsAtIuUK5siBzgOq/YCm8GaDZwRSI36AX4FA1MEeC2sV7caiBqVMmohKgBCiiVXLgQGMH4p/oZR0a/w0Qz0dquGkCHWqJpuYDEvsxnooABJqhZuICytCso4/eZkcCoADthFY6MnToZcEjCxRoNkToAdWwjkj8bxS6gZ0INaDUvhmP/CwgrgDid3hSvzOWhiq+2pOoYooJT0wiAwU0viwWNXeIDLAQIJ5OQL0ZUrMFF+CGBpwIHjV7gHgulOamQA1ykcAAEEAjpkcwzCqn/9DEzo1UzH6EskHDFCeAxUkmIXOATS6C5gCbYJnUcDPLaLQNTQBMSC44EqE3kHInwSgXHOKkmkNUd72AgLqFwJZbAp1zbQe0LgM1WjrQGjQMNG6Z4awC8DVIRmjp+h1IcaAJg1rsicgNTliJ1g7ElXjMiwcmyHi0kUBCXQJskcFIpONXAalQJKFybCMNJCSQU9A2DAxIArEVEK8lIYE8BGJ5tE7rI2jYeZGQYN8D8So0KS6Q9BBNrF/QE1qOTo7blCtTnqI3bDWACaiKCAMnE9lrECPQtqA3uA9NZDlAvB6aaEHjhGvJNM8HiKWgvRUpYhMZWichHYibgPgAEP+DdhWHPJhkNwlcsptLm4NpYIJDaaMtBpZWFwkZAkyMeUTYtQgaibhA+wD43xyIJYB4ChBPRepa3gJiNRLNmgPEoJHvV9BS8gkF7hKClpA6FHQIYKAPiIuQ+BNIMIpa5jDkHcqDlWgMwBINRQ4ggEZ7nUOvTQQbmAfVRqC5430MkJl+WMl6HViamBPR4yRoDrDHaU4td4/2OoceAA0ePgdiUBsINNeoCS2VTkHlnelszmhCG6YA1MvrB5ZasyhsB4PNYYAM4BKqUikGTKPxNiQB8yAzh3CJhmV6eA2w4R86GpejgMg2I6gjyQNr8wPxPGBp20JM1RmCnvgIjZ/RwPGgfrE0lPuHAbLWCgUMpgHbkTpYCwV6aPxmYHgtQKt638ASGiik5uMwiJGECPmPFgHkJlD0Mbt7DIh1c6Di/iGJCUQE2qvihfq1jowEAhqc/Y3EBy2XyALiaWQkWG0gBi3tEmCALqEeZon1MdYSDVhixeFbYQWUA1GtQHU1JJYEyGauAQYmuVXyPDT+MSC2JlJvMFQ9cs5DnykgBsih8X8wQCajMxggCzCJBaAUBVr+sgfKnwnEYQyQ9YRDHmjxazE8/faU4ePvj8jCn2Er4EuJMGM6kXaB1h0tgJaQyJiDjJ0XuMBbEtRKQrvxIABbOA9KZG0U2H+ZATLt8oXERAYCoPVWZ9Fw0HApypT5lRn4WPmwDm+ARvJbCBkALM2eEmEPqKQ4gkf+5AD4fQq0dw3KTBYMkHlFkBiomK4iM5HpAvFVIOZkwJwDJQRAC+gMgfgQFIPClX84JDJY9R8GLqBR27aghAbaorWGSnaBppjW45HvGQD/C0NLQNDS0kykkvkNGWbtZIBMxl+FtrPIBTrQBCoETWikAg20VZ3kAg0GKg7MghKWr5wvw4U3Fxgef3uMUaIRs1p0LpF2vSSQ4n8MQEID7U2EzWnCqjlxBjyrTPCAGAbIygvtAS488hgQ2xvmMeDfS0cPc+Dg7se7DJ9+f8JadfoRoZ/Y9ow/nQL6BQlqQYkMtATaHFqy5UFLEVJXbygA8QMg9kQTJ3XQE1TCBjJAlgu9h3YofpOgnw2N70NmGFLLHBRw7eM1bMK8AAHau5rQJoIoPApRUWI91IiI4KEXexAhih4KFg8qCB6LeKnWQ4VC8dBTFapVPPQUqbXiyYiliFaEgmChWG2EIOZQsRdRCiKiUlAacuhPWt+XvG22a7K72Z1Z1mYeLNs0uzNvZna+vP/VTnVNmhQTiRTQD/os/06x4vxYFNNQCxvSRm/oJ4mgUwY/mUxGCj/xeLwzbHOtfeqaNKknI/fnHIHSsMO1VyxgCIkvrYofOqrihyXQdFgnWsduaNIUHO3wcE99DfHjX0JrHWuFfwbJ9giqXykDePCx9CZPJKf186hJk6ZQA5ooxnlddLhuNwEf3FTlDJMIA8oT4H3Q06lJk6ZqiFRqeBJjbiRDUtcdA2kAaPMuGkOdO1vAQriazCBcGii8/gh9i4i15SqRa/tCloG0Cn6u0anH5pKUqFAP0EyyYhs91B5Vxst646fGg6WDJrdmr1la02VROV8K2DBjdgp8IUBqIGCKi2KBvGrdn5vpXgQ0w9WdoLbmfA70vKicQ95BgzvE4LbBMqgFAA89lK8lT3yjw/dNDnO2wJLwI0UPBmKIEQSFKDsYeo0YGsSooNo4MjBQCGJE8QOKwCdE6tmtPyqW3OJrUUjxspCc2GeifXQ08N8waDfzPAmejwlRipAELzMaY/5b8IuZAe0CARKqjLyX0DEKUwz6bGOJzy/5AdziEs0Bat9DuBiIxzmrANB20vGWjmOmjWss/goDW4IP2Ek/Mfj+UjjWOQcgeyBKaW1DLNm+UcQLQqGRIZ3jPpGkEefvjIQNfIbn77ko5QxqCgkhirZlf4vonuz+J5K2nMqJDQF1cZIAbVxC/1PU1qCEdup5Q55xqRaXUzmmSFI7GKK1WVbQJmrRofqy1b7QzmfzWvxg8MBGPhDguBFMifD+kyy9oUJzGwNsI4OaKkBDX5MuxguTyh4NH+GgukjdanLTtkgxyiQaiYq9W4sloBG9bcmwWwU0LPh9AjNIQMcl8HJT0piQPnndJ0+XamDtYyYwg3TawdLGUf7fdpZOBkwSWzQg3pBye88CZPCWd4m16RZNAfEDYEOu92n+jPeW3BUubJ+S6KeHe77VED++CYCG0hx4VcgNCe19JulMVsIeVIVxH3xNkHSWFuufkJcG+9gdBiyjwGIbA5w1bw0vOxsOgC+AxCkGtCeiGPNkBbJaoxEfzoq8Cn583JtXOVGQvgwJLLeYK5yzi1lXKmcP/6J3hUg6EwxkkCSaPd4vu0iujBRyFfYZVKmBQ+Adq+dG/qG1SAnUO1STgT2yX/HGPcL9fGQpaCBkar+h+i8F1Ocmuy+NCkgEdgCJjTZ7NRB+RMnRFgQ/TryI0a+jhcOVhEYS1R9SNw+z6O3H0zRNbSVlzTgt8ita4GcAZg+3p+j+MckPJcACRuxdonpbGB4KvISjT9UvPx+wO8LoDuM7SizAs/qbQeW2qL5qkhdC/0+5b8yVmzILVxXyg5/5h6wqzTOoG9RuUqMSovRKItmEfYGE+nLvADNX2sraqIZDQfFjwoEg+Onl9ZdRcXT2L+ks7jocu1S6AAAAAElFTkSuQmCC)
    no-repeat;
  margin: 0 auto;
}
.ico-phone-use {
  .bg-phone-repeat;
  background-position: -212px 2px;
}
.ico-phone-notuse {
  .bg-phone-repeat;
  background-position: -248px 2px;
}
//小票打印
.sign-print	{
  // border: 1px solid black;
  display: block;
  page-break-before: always;
  font-family: simhei,fangsong,simsun,serif;
  width: 58mm;
  overflow: hidden;
  margin: 0 auto;
  font-size:4mm !important;
  color: #000 !important;
}

.sign_print_item {
  box-sizing: border-box;
  margin-top: 15pt;

  .ivu-form-item {
    margin: 0 0 4pt !important;
    display: flex;
    align-items: center;
  }

  .line {
    height: 0;
    margin: 2mm 0;
    border-bottom: 0.3mm dotted #333;
  }

  header {
    text-align: center;
    padding-bottom: 2mm;
    font-weight: bold;
    font-size: 4.5mm;
  }

  footer {
    padding-top: 4mm;

    >div {
      display: flex;
      justify-content: space-between;

      h3 {
        padding-right: 15mm;
        display: block;
        margin-bottom: 7pt;
      }
    }

    .type {
      border: 0.1mm dotted #999;
    }

    .footer-line {
      padding-bottom: 4mm;
      border-bottom: 0.6mm dashed #000;
    }

    .signline {
      flex-direction: column;

      .type {
        width: 17mm;
      }
    }
  }
}
.sign-print /deep/ .ivu-form-item-content,.sign-print /deep/ .ivu-form-item-label{
  line-height: 1;
  font-size:4mm !important;
  color: #000 !important;
}
.sign-print .ivu-form-item-label {
  padding: 0 12px 0 0;
}
.space-bet-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.flex-start-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.flex-start-col {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.center-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.p-r-12 {
  padding-right: 12px;
}
.p-l-10 {
  padding-left: 10px;
}
.m-b-10 {
  margin-bottom: 10px;
}
.ql-editor {
  overflow-x: hidden;
}
.record-intr {
  display: block;
  float: left;
  padding-right: 8px;
}
.record-no {
  font-style: normal;
  color: #d9544f;
  cursor: pointer;
}
.record-icon {
  display: block;
  float: left;
  width: 20px;
  height: 20px;
  margin-top: 8px;
  cursor: pointer;
  background: url(../assets/img/orderrecord.png) no-repeat;
}

.radio-lines {
  width: 100%;

  .ivu-radio-wrapper {
    position: relative;
    width: 80%;
    height: 80px;
    border-radius: 5px;
    border: 1px solid #dcdcdc;
    margin: 0 auto 20px;
    line-height: 50px;
    padding: 15px;
    font-size: 14px;
    text-align: center;
  }

  .ivu-radio {
    display: none;
  }
}
// 图片上传规避责任文案
.image-description {
  display: inline-block;
  vertical-align: top;

  .label {
    margin-bottom: 10px;
  }

  .tip {
    color: red;
    margin-top: 4px;
    font-size: 10px;
  }
}

.image-description-required {
  width: 60px;

  .tip {
    position: relative;
    margin-left: -100px;
    float: right;
    clear: right;
  }
}
.user-search-item {
  width: 100%;
  box-sizing: border-box;
  padding: 0 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e3e9;
  .user-search-left {
    width: 50px;
    color: #000;
  }
}
.user-search-main {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  min-height: 50px;
  padding: 10px 35px 0;
  flex: 1;
  > * {
    min-width: 120px;
    max-width: 270px;
    margin-right: 15px;
    margin-bottom: 10px;
  }
  > .ivu-select {
    max-width: 160px;
  }
  > .ivu-date-picker {
    width: 200px;
  }
  .user-search {
    width: 160px;
  }
}

.text_overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon-palm {
  .icon;
  width: 18px;
  height: 18px;
  background: url(../assets/img/palm.png) 0px 1px no-repeat;
  line-height: 14px;
  background-size: cover;
}