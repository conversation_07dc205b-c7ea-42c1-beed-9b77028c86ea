@import '~iview/src/styles/index.less';

@rem: 100rem;

html, body {
  height: 100%;
}

.ivu-btn {
  text-align: center !important;
}

/*表单输入框*/
.ivu-input {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  height: 32/@rem;
  line-height: 32/@rem;
  padding: 0 0 0 20/@rem;
  font-size: 26/@rem;
  // color: #fff;
  border-radius: 4/@rem;
}

.ivu-input:hover {
  box-shadow: none !important;
  border-color: transparent !important;
}
.ivu-form .ivu-form-item-label {
  color: #aab4bf;
}

/*下拉选择输入框*/
.ivu-select-arrow {
  display: none;
}

.ivu-select {
  font-size: 26/@rem;
  display: flex;
  align-items: center;
}

.ivu-select-item {
  font-size: 26/@rem !important;
  padding: 14/@rem 32/@rem;
}

.ivu-select-selection {
  border: none !important;
  background-color: transparent !important;
  border: 0 !important;
  outline: 0 !important;
  display: flex !important;
  align-items: center !important;
  // height: 100% !important;
  width: 100%;
  box-shadow: none !important;
}

.ivu-select-dropdown {
  max-height: 400/@rem;
  margin: 5/@rem 0;
  padding: 5/@rem 0;
  border-radius: 4/@rem;
  box-shadow: 0 1/@rem 6/@rem rgba(0, 0, 0, 0.2);
  z-index: 900;
}

.ivu-select-input {
  // color: #fff !important;
  height: 32/@rem !important;
  line-height: 32/@rem !important;
  padding: 0 0 0 20/@rem !important;
  font-size: 26/@rem !important;
  // height: 100% !important;
  // line-height: 100% !important;
}

.ivu-select-input::placeholder {
  // color: #fff;
}

.ivu-select-single .ivu-select-selection .ivu-select-placeholder, .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  height: 30/@rem;
  line-height: 30/@rem;
  font-size: 26/@rem;
  padding-left: 30/@rem;
  padding-right: 24/@rem;
  // color: #fff;
}

/*checkbox*/
.ivu-checkbox {
  margin-right: 10/@rem;
}

/*表单*/
.ivu-form-item-content {
  font-size: 12/@rem;
  line-height: inherit;
  display: flex;
  width: 100%;
  position: relative;
  .ivu-form-item-error-tip {
    font-size: 26/@rem;
    white-space: nowrap;
    position: absolute;
    right: 0;
    top: 0;
    left: auto;
    line-height: inherit;
    padding: 0;
  }
}

/*上传*/
.ivu-upload-list-file {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4/@rem;
  // color: #fff;
  border-radius: 4/@rem;
  transition: background-color 0.2s ease-in-out;
  overflow: hidden;
  position: relative;
}

.ivu-form-item-label {
  white-space: nowrap !important;
  font-size: 26/@rem !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
}

#newBridge {
  #nb_icon_wrap.nb-icon-left-center {
    left: auto;
    right: 5px;
    top: 3.6rem;
    width: 56px !important;
    height: 70px !important;
    .nb-icon-wrap-base {
      height: 105%;
      background-size: 25px 50px;
    }
    .nb-icon-flag {
      margin-top: -1px;
      border-left-width: 27px!important;
      border-right-width: 27px!important;
      border-bottom-width: 12px!important;
    }
  }

  #nb_toolbar_wrap {
    display: none!important;
  }
}
