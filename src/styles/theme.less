
@import './themeVar.less';
[data-theme="default"] {
  background: #F6F6F8;
  color: #000;
  .theme-bg {
    background: #fff;
  }
}
[data-theme="dark"] {
  background: #000;
  color: #fff;
  .ivu-tabs {
    color: #fff;
  }
  .theme-bg {
    background: #0f0f0f;
  }
  input,button {
    color: #fff;
  }
  &.mobile-box .ivu-tabs-bar {
    background: #000;
    color: #fff;
  }
  &.mobile-box .ivu-tabs .ivu-tabs-nav .ivu-tabs-tab {
    color: #fff;
  }
  .arrow-right {
    border-color: #fff;
  }
}
.themeicon {
  display: inline-block;
}
.theme-color-other {
  color: @theme-text-color-other;
}

.cut-up {
  margin: 0 10px;
}
.icon-mr {
  margin-right: 10px;
}

// BUTTON
.normal-btn {
  width: 100%;
  height: 80px;
  line-height: 80px;
  background: var(--THEME-COLOR);
  font-size: 30px;
  font-weight: bold;
  color: #000;
  border: 0 none;
  text-align: center;
}
.normal-btn-min {
  width: 300px;
  border-radius: 40px;
}
.gray-btn {
  width: 195px;
  background: #f2f2f2;
}
.buttons {
  display: flex;
  width: 100%;
  z-index: 10;
  .outer-green {
    background-color: #fff;
    border: 1px solid var(--THEME-COLOR);
    color: #1b1b1b;
  }
  .outer-org {
    background-color: #fff;
    border: 1px solid @theme-text-color-other;
    color: #1b1b1b;
  }
  .disabled {
    background-color: #e7e7e7;
    color: #898989;
  }
  .transparent {
      background-color: transparent !important;
      color: #1b1b1b !important;
      font-size: 24px;
      font-weight: normal;
  }
}
[data-theme="dark"] .buttons {
  .transparent {
    color: #fff !important;
  }
  .outer-org {
    background: #000;
    color: #fff;
  }
}