---
type: "always_apply"
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

```bash
# Development
npm run dev    # Start dev server on fe.rocketbird.cn:8089

# Build
npm run build  # Production build
npm run cdn    # Production build with CDN resources

# Code Quality
npm run lint   # ESLint code checking
npm run test:unit  # Run Vue CLI unit tests
```

## Architecture Overview

This is a Vue 2.7 SaaS fitness/gym management system with microfrontend capabilities via qiankun. Key architectural patterns:

### Multi-Page Application Structure
- Pages auto-detected from `src/pages/*/main.js` pattern
- Each page can have its own HTML template (`index.html` or fallback to `_default.html`)
- Configured as UMD library for microfrontend integration
- Build output configured for qiankun with UMD format

### Route Organization
Routes are split across modules in `src/router/`:
- `index.js` - Main router with core routes including layout, print, and screen routes
- `stat.js`, `invoice.js`, `management.js`, `settings.js`, `course.js`, `marketing.js` - Feature-specific routes
- `alipay_2024.js` - Payment integration routes
- Router uses history mode with qiankun base path detection: `window.__POWERED_BY_QIANKUN__ ? '/v2/' : '/'`

### State Management Pattern
Vuex store organized in modules (`src/store/modules/`) with auto-loading:
- `user.js` - Authentication and user state  
- `employee.js` - Staff management
- `goods.js` - Inventory and products
- `pay.js` - Payment processing
- `websocket.js` - Real-time communication
- `diy.js` - UI customization
- Store modules are auto-imported using `require.context('./modules', true, /\.js$/)`

### Component Architecture
- **Global Components**: Auto-registered from `src/components/global/` (e.g., FaIcon for FontAwesome)
- **Form Components**: Rich form controls in `src/components/form/` (Editor, cropperPlus, payment selectors)
- **Business Components**: Domain-specific components (cardSelect, employeeNav, member, reservation)
- **Layout System**: Centralized layout in `src/views/layout/` with sidebar, header, and main content areas

### Key Development Patterns

**Route Metadata System**:
```javascript
{
  path: 'example',
  meta: {
    keepAlive: true,        // Enable Vue keep-alive caching
    hideBread: true,        // Hide breadcrumb
    hideSidebar: true,      // Hide sidebar
    hideHeader: true,       // Hide top header
    hidePadding: true,      // Remove content padding
    parentName: 'Parent',   // For menu highlighting
    breadText: 'Custom',    // Custom breadcrumb text
    breadNotLink: true,     // Disable breadcrumb linking
    version: 'path'         // For Vue 1.x compatibility redirects
  }
}
```

**AJAX Loading Control**:
```javascript
// Disable global loading indicator
this.$service.post(url, data, { loading: false })
// Loading state is managed via Vuex with request counting
```

**Page State Persistence**:
Use `keepAlive: true` in route meta, then use `activated()` hook instead of `created()` for data refresh.

**Service Layer Pattern**:
- All API calls go through `src/service/index.js` with axios interceptors
- Request/response interceptors handle loading states automatically
- 30-second timeout default, extended to 50s for large data requests
- Automatic request ID generation for tracking
- Error handling with timeout detection

### Build Configuration Highlights
- **Webpack Aliases**: `@` (src), `components`, `views`, `utils`, `mixins`, `store`, `router`
- **CDN Support**: Configurable via build command argument (`npm run cdn`)
- **Proxy Setup**: `/api` → `https://beta.rocketbird.cn` in development
- **Multi-entry Detection**: Automatic page discovery and configuration via glob patterns
- **UMD Output**: Configured for microfrontend with library name based on package.json

### UI Framework Stack
- **iView 3.4.2**: Primary component library
- **FontAwesome 4.7.0**: Icon system via global FaIcon component
- **Less**: CSS preprocessing with JavaScript enabled
- **Vue 2.7**: Core framework with Composition API support
- **qiankun**: Microfrontend framework for modular architecture

### Business Domain Structure
The application manages comprehensive gym/fitness operations:
- **Member Management**: Registration, cards, lockers, deposits, detailed member operations
- **Employee Management**: Coaches, membership staff, attendance tracking
- **Course Management**: Classes, schedules, reservations, private training
- **Finance**: Payments, salary, invoices, inventory, flow tracking
- **Marketing**: Activities, discounts, SMS campaigns, group buying
- **Hardware Integration**: RFID, fingerprint systems, locker management
- **Stadium/Venue**: Booking, pricing strategies, space management
- **Reporting**: Comprehensive analytics and business intelligence

### Service Layer
Centralized API handling in `src/service/getData.js` with request/response interceptors for loading states and error handling.

### Modal Guidelines
From README: Use `:mask-closable="false"` for iView modals with input operations for consistent UX.

### Z-Index Standards
- Modal: 1000
- Sidebar: 999  
- Header: 998