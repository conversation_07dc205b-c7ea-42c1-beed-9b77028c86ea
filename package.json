{"name": "web-fe-v2", "version": "2.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "cdn": "vue-cli-service build cdn", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "ali-oss": "^6.17.1", "axios": "^1.4.0", "big.js": "^6.2.1", "core-js": "^3.8.3", "crypto-js": "^3.1.9-1", "echarts": "^4.0.4", "font-awesome": "^4.7.0", "gcoord": "^0.3.2", "html2canvas": "^1.3.2", "iview": "3.4.2", "lodash-es": "^4.17.10", "qiankun": "^2.10.11", "qrcode.vue": "^1.7.0", "qs": "^6.5.1", "quill": "^1.3.6", "swiper": "^5.4.5", "vue": "^2.7.0", "vue-amap": "^0.5.4", "vue-awesome-swiper": "^4.1.1", "vue-cropperjs": "^2.2.0", "vue-router": "^3.0.1", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "vuex-composition-helpers": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "eslint": "^7.32.0", "eslint-plugin-vue": "^9.3.0", "less": "^4.1.3", "less-loader": "^11.1.0"}, "engines": {"node": ">= 8.9.0", "npm": ">= 5.2.0"}, "browserslist": ["last 3 Chrome versions", "last 3 Firefox versions", "Safari >= 10", "Explorer >= 11", "Edge >= 12", "iOS >= 10", "Android >= 6"]}