{"env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-this-before-super": "warn", "no-undef": "warn", "no-unreachable": "error", "no-unused-vars": "warn", "constructor-super": "warn", "valid-typeof": "warn", "no-console": "warn", "space-before-function-paren": "off", "no-unexpected-multiline": "error", "camelcase": "off", "eqeqeq": "off"}}